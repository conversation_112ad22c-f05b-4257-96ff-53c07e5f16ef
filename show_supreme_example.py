#!/usr/bin/env python3
"""
Mostra esempio completo SUPREME
===============================
"""

import sys
sys.path.append('.')

from neuroglyph_supreme_god_mode_dataset_generator import NeuroglyphSupremeGodModeGenerator

def show_supreme_example():
    """Mostra esempio completo SUPREME."""
    
    print("🧠 NEUROGLYPH SUPREME GOD MODE - Esempio Completo")
    print("=" * 60)
    
    generator = NeuroglyphSupremeGodModeGenerator()
    
    # Genera esempio SUPREME per logica simbolica
    example = generator.generate_supreme_god_mode_example('symbolic_logic', 'god_mode')
    
    print("📝 INSTRUCTION:")
    print(example['instruction'])
    print("\n" + "="*60)
    
    print("\n📄 OUTPUT COMPLETO:")
    print(example['output'])
    print("\n" + "="*60)
    
    print("\n📊 METADATA:")
    metadata = example['metadata']
    for key, value in metadata.items():
        print(f"   {key}: {value}")
    
    print(f"\n🎯 QUALITÀ SUPREME: {metadata['quality_score']}/10")
    print(f"🔣 SIMBOLI ATOMICI: {metadata['symbols_used']}")
    print(f"🔗 RAGIONAMENTO MULTI-HOP: {metadata['reasoning_steps']} step")
    print(f"✅ ZERO ALLUCINAZIONI: {metadata['zero_hallucination']}")
    print(f"🧠 INTELLIGENZA SIMBOLICA: {metadata['deterministic']}")

if __name__ == "__main__":
    show_supreme_example()
