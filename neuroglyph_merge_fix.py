# 🔧 NEUROGLYPH MERGE FIX - TOKENIZER MISMATCH SOLUTION
# Risolve il problema di size mismatch tra base model e LoRA checkpoint

import torch
from transformers import AutoModelForCausalLM, AutoTokenizer
from peft import PeftModel
from pathlib import Path
import json

print("🔧 NEUROGLYPH MERGE FIX - TOKENIZER MISMATCH SOLUTION")
print("=" * 80)

# 📁 PATHS CONFIGURATION
lora_checkpoint_path = "/content/drive/MyDrive/NEUROGLYPH_ULTIMATE/models/NEUROGLYPH_ULTIMATE_v1.0/checkpoint-525"
base_model_name = "Qwen/Qwen2.5-Coder-1.5B-Instruct"
output_path = "/content/drive/MyDrive/NEUROGLYPH_ULTIMATE/models/NEUROGLYPH_MERGED_v1.0"

print(f"📁 LoRA checkpoint: {lora_checkpoint_path}")
print(f"🏗️ Base model: {base_model_name}")
print(f"💾 Output path: {output_path}")

# 🔍 STEP 1: LOAD TOKENIZER FROM CHECKPOINT FIRST
print("\n🔍 Step 1: Caricamento tokenizer dal checkpoint...")

try:
    # Load tokenizer from LoRA checkpoint (has NEUROGLYPH symbols)
    checkpoint_tokenizer = AutoTokenizer.from_pretrained(lora_checkpoint_path)
    print(f"✅ Tokenizer checkpoint caricato!")
    print(f"📊 Vocab size checkpoint: {len(checkpoint_tokenizer.get_vocab()):,}")
    
    # Verify NEUROGLYPH symbols
    vocab_dict = checkpoint_tokenizer.get_vocab()
    neuroglyph_symbols = [token for token in vocab_dict.keys() if token.startswith('⚛') or token.startswith('🧠') or token.startswith('⚡')]
    print(f"⚛️ NEUROGLYPH symbols found: {len(neuroglyph_symbols)}")
    
except Exception as e:
    print(f"❌ Errore caricamento tokenizer checkpoint: {e}")
    raise

# 🏗️ STEP 2: LOAD BASE MODEL WITH CORRECT TOKENIZER
print("\n🏗️ Step 2: Caricamento base model con tokenizer corretto...")

try:
    # Load base model
    base_model = AutoModelForCausalLM.from_pretrained(
        base_model_name,
        torch_dtype=torch.float16,
        device_map="auto",
        trust_remote_code=True
    )
    print(f"✅ Base model caricato!")
    print(f"📊 Original vocab size: {base_model.config.vocab_size:,}")
    
    # CRITICAL: Resize token embeddings to match checkpoint
    target_vocab_size = len(checkpoint_tokenizer.get_vocab())
    print(f"\n🔧 Resizing embeddings: {base_model.config.vocab_size:,} → {target_vocab_size:,}")
    
    base_model.resize_token_embeddings(target_vocab_size)
    print(f"✅ Embeddings resized successfully!")
    print(f"📊 New vocab size: {base_model.config.vocab_size:,}")
    
except Exception as e:
    print(f"❌ Errore caricamento/resize base model: {e}")
    raise

# 🔗 STEP 3: LOAD LORA CHECKPOINT
print("\n🔗 Step 3: Caricamento LoRA checkpoint...")

try:
    # Now load LoRA - should work with matching vocab sizes
    model = PeftModel.from_pretrained(base_model, lora_checkpoint_path)
    print(f"✅ LoRA checkpoint caricato successfully!")
    
    # Verify model state
    print(f"📊 Model vocab size: {model.config.vocab_size:,}")
    print(f"📊 Tokenizer vocab size: {len(checkpoint_tokenizer.get_vocab()):,}")
    
    if model.config.vocab_size == len(checkpoint_tokenizer.get_vocab()):
        print("✅ Vocab sizes match perfectly!")
    else:
        print("⚠️ Vocab size mismatch still exists")
    
except Exception as e:
    print(f"❌ Errore caricamento LoRA: {e}")
    raise

# 🔄 STEP 4: MERGE LORA INTO BASE MODEL
print("\n🔄 Step 4: Merge LoRA into base model...")

try:
    # Merge LoRA weights into base model
    merged_model = model.merge_and_unload()
    print(f"✅ LoRA merged successfully!")
    
    # Verify merged model
    print(f"📊 Merged model vocab size: {merged_model.config.vocab_size:,}")
    
except Exception as e:
    print(f"❌ Errore durante merge: {e}")
    raise

# 💾 STEP 5: SAVE MERGED MODEL
print("\n💾 Step 5: Salvataggio merged model...")

try:
    # Create output directory
    Path(output_path).mkdir(parents=True, exist_ok=True)
    
    # Save merged model
    print("💾 Saving merged model...")
    merged_model.save_pretrained(
        output_path,
        safe_serialization=True,  # Use safetensors
        max_shard_size="5GB"
    )
    print(f"✅ Merged model saved!")
    
    # Save tokenizer (CRITICAL for GGUF)
    print("💾 Saving tokenizer...")
    checkpoint_tokenizer.save_pretrained(output_path)
    print(f"✅ Tokenizer saved!")
    
except Exception as e:
    print(f"❌ Errore salvataggio: {e}")
    raise

# 🔍 STEP 6: VERIFY MERGED MODEL
print("\n🔍 Step 6: Verifica merged model...")

try:
    # List generated files
    output_files = list(Path(output_path).glob("*"))
    print(f"📁 Files generated:")
    
    total_size = 0
    for file_path in sorted(output_files):
        if file_path.is_file():
            size_mb = file_path.stat().st_size / (1024 * 1024)
            total_size += size_mb
            print(f"  📄 {file_path.name} ({size_mb:.1f} MB)")
    
    print(f"\n📊 Total size: {total_size/1024:.2f} GB")
    
    # Verify GGUF-required files
    required_files = [
        "model.safetensors",  # or pytorch_model.bin
        "config.json",
        "tokenizer.json",
        "tokenizer_config.json",
        "vocab.json",
        "special_tokens_map.json"
    ]
    
    print(f"\n🔍 GGUF readiness check:")
    missing_files = []
    
    for required_file in required_files:
        file_path = Path(output_path) / required_file
        if file_path.exists():
            print(f"  ✅ {required_file}")
        else:
            print(f"  ❌ {required_file} (missing)")
            missing_files.append(required_file)
    
    # Check for pytorch_model.bin as alternative
    if "model.safetensors" in missing_files:
        pytorch_model = Path(output_path) / "pytorch_model.bin"
        if pytorch_model.exists():
            print(f"  ✅ pytorch_model.bin (alternative)")
            missing_files.remove("model.safetensors")
    
    if len(missing_files) == 0:
        print(f"\n🎉 ALL GGUF-REQUIRED FILES PRESENT!")
        print(f"🚀 Ready for GGUF conversion!")
        gguf_ready = True
    else:
        print(f"\n⚠️ Missing files for GGUF: {missing_files}")
        gguf_ready = False
    
except Exception as e:
    print(f"❌ Errore verifica: {e}")
    gguf_ready = False

print("\n🎉 NEUROGLYPH MERGE COMPLETED!")
print("🏆 LoRA successfully merged into base model!")
print("⚛️ NEUROGLYPH symbols preserved!")
print("📦 Ready for GGUF conversion!")
print(f"💾 Merged model location: {output_path}")

if gguf_ready:
    print("\n🚀 NEXT STEPS:")
    print("1. Convert to GGUF format")
    print("2. Test with Ollama")
    print("3. Deploy NEUROGLYPH!")
else:
    print("\n⚠️ NEXT STEPS:")
    print("1. Check missing files")
    print("2. Regenerate if needed")
    print("3. Then convert to GGUF")
