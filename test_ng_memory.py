#!/usr/bin/env python3
"""
Test NG_MEMORY v3.0 ULTRA
=========================

Test del modulo NG_MEMORY reale con tutti i sotto-moduli:
- SymbolStore (LMDB storage)
- EpisodeCache (FAISS similarity search)
- <PERSON>rrorLog (pattern analysis)

Autore: NEUROGLYPH NG-THINK Team
Data: 2025-01-27
"""

import sys
import os
import tempfile
import shutil
sys.path.append(os.path.join(os.path.dirname(__file__), 'neuroglyph'))

from neuroglyph.ng_think.v1_base.ng_memory import NGMemory
from neuroglyph.ng_think.v1_base.ng_parser import NGParser
from neuroglyph.ng_think.v1_base.ng_context_prioritizer import NGContextPrioritizer
from neuroglyph.ng_think.core.ng_types import (
    NGMessage, NGModuleType, NGProcessingStage
)

def test_symbol_store():
    """Test SymbolStore con fallback in-memory"""
    print("🗄️ TEST SYMBOL STORE")
    print("=" * 60)
    
    # Usa directory temporanea per test
    with tempfile.TemporaryDirectory() as temp_dir:
        config = {
            'symbol_store': {
                'db_path': os.path.join(temp_dir, 'test_symbols.lmdb')
            }
        }
        
        memory = NGMemory(config)
        
        # Test storage simboli
        test_symbols = [
            {'symbol': '⊢', 'meaning': 'deduzione logica', 'context': 'reasoning'},
            {'symbol': '≈', 'meaning': 'analogia', 'context': 'similarity'},
            {'symbol': '→', 'meaning': 'implicazione', 'context': 'logic'}
        ]
        
        print(f"\n📝 Test storage di {len(test_symbols)} simboli:")
        
        for symbol_data in test_symbols:
            result = memory.symbol_store.execute(symbol_data, operation='store')
            print(f"   ✅ Stored {symbol_data['symbol']}: {result.success}")
        
        # Test retrieval simboli
        print(f"\n🔍 Test retrieval simboli:")
        
        query_data = {
            'symbols': ['⊢', '≈', '→'],
            'domain': 'logic'
        }
        
        result = memory.symbol_store.execute(query_data, operation='retrieve')
        
        if result.success:
            symbols_found = result.output_data.get('symbols_found', 0)
            print(f"   ✅ Simboli trovati: {symbols_found}")
            
            symbols_data = result.output_data.get('symbols_data', [])
            for symbol_info in symbols_data:
                print(f"      {symbol_info.get('symbol', '')}: {symbol_info.get('meaning', '')}")
        else:
            print(f"   ❌ Retrieval fallito: {result.error_message}")

def test_episode_cache():
    """Test EpisodeCache con fallback lineare"""
    print("\n🔍 TEST EPISODE CACHE")
    print("=" * 60)
    
    # Usa directory temporanea per test
    with tempfile.TemporaryDirectory() as temp_dir:
        config = {
            'episode_cache': {
                'cache_path': os.path.join(temp_dir, 'test_episodes.faiss'),
                'embedding_dim': 16  # Dimensione ridotta per test
            }
        }
        
        memory = NGMemory(config)
        
        # Test aggiunta episodi
        test_episodes = [
            {
                'embedding': [0.1, 0.2, 0.3, 0.4] * 4,  # 16 dim
                'metadata': {'type': 'reasoning', 'domain': 'programming'}
            },
            {
                'embedding': [0.5, 0.6, 0.7, 0.8] * 4,  # 16 dim
                'metadata': {'type': 'problem_solving', 'domain': 'mathematics'}
            },
            {
                'embedding': [0.1, 0.3, 0.2, 0.4] * 4,  # 16 dim (simile al primo)
                'metadata': {'type': 'reasoning', 'domain': 'logic'}
            }
        ]
        
        print(f"\n📝 Test aggiunta di {len(test_episodes)} episodi:")
        
        for i, episode_data in enumerate(test_episodes):
            result = memory.episode_cache.execute(episode_data, operation='add')
            print(f"   ✅ Added episode {i}: {result.success}")
        
        # Test search episodi
        print(f"\n🔍 Test search episodi simili:")
        
        query_data = {
            'embedding': [0.1, 0.2, 0.3, 0.4] * 4,  # Simile al primo
            'k': 2,
            'threshold': 0.5
        }
        
        result = memory.episode_cache.execute(query_data, operation='search')
        
        if result.success:
            episodes_found = result.output_data.get('total_found', 0)
            print(f"   ✅ Episodi trovati: {episodes_found}")
            
            episodes = result.output_data.get('episodes', [])
            for episode in episodes:
                similarity = episode.get('similarity', 0.0)
                metadata = episode.get('metadata', {})
                print(f"      Similarity: {similarity:.3f}, Type: {metadata.get('type', 'unknown')}")
        else:
            print(f"   ❌ Search fallito: {result.error_message}")

def test_error_log():
    """Test ErrorLog con pattern detection"""
    print("\n📊 TEST ERROR LOG")
    print("=" * 60)
    
    # Usa directory temporanea per test
    with tempfile.TemporaryDirectory() as temp_dir:
        config = {
            'error_log': {
                'log_path': os.path.join(temp_dir, 'test_errors.jsonl')
            }
        }
        
        memory = NGMemory(config)
        
        # Test logging errori
        test_errors = [
            {
                'error_type': 'parsing_error',
                'error_message': 'Token non riconosciuto',
                'module': 'parser',
                'severity': 'medium'
            },
            {
                'error_type': 'memory_error',
                'error_message': 'Simbolo non trovato',
                'module': 'memory',
                'severity': 'low'
            },
            {
                'error_type': 'parsing_error',
                'error_message': 'Ambiguità non risolta',
                'module': 'parser',
                'severity': 'high'
            }
        ]
        
        print(f"\n📝 Test logging di {len(test_errors)} errori:")
        
        for error_data in test_errors:
            result = memory.error_log.execute(error_data, operation='log')
            print(f"   ✅ Logged {error_data['error_type']}: {result.success}")
        
        # Test retrieval errori
        print(f"\n🔍 Test retrieval errori per modulo:")
        
        query_data = {
            'module': 'parser',
            'limit': 5
        }
        
        result = memory.error_log.execute(query_data, operation='retrieve')
        
        if result.success:
            errors_found = result.output_data.get('total_found', 0)
            print(f"   ✅ Errori trovati per 'parser': {errors_found}")
            
            patterns = result.output_data.get('patterns', {})
            print(f"   📊 Pattern rilevati: {len(patterns)}")
            
            for pattern, count in patterns.items():
                print(f"      {pattern}: {count} occorrenze")
        else:
            print(f"   ❌ Retrieval fallito: {result.error_message}")

def test_memory_integration():
    """Test integrazione completa NG_MEMORY"""
    print("\n🔗 TEST INTEGRAZIONE MEMORY")
    print("=" * 60)
    
    # Usa directory temporanea per test
    with tempfile.TemporaryDirectory() as temp_dir:
        config = {
            'symbol_store': {
                'db_path': os.path.join(temp_dir, 'symbols.lmdb')
            },
            'episode_cache': {
                'cache_path': os.path.join(temp_dir, 'episodes.faiss'),
                'embedding_dim': 16
            },
            'error_log': {
                'log_path': os.path.join(temp_dir, 'errors.jsonl')
            }
        }
        
        # Inizializza pipeline completa
        parser = NGParser()
        prioritizer = NGContextPrioritizer()
        memory = NGMemory(config)
        
        test_prompt = "Usa ⊢ per deduzione e ≈ per analogia nel reasoning simbolico"
        
        print(f"📝 Test prompt: {test_prompt}")
        
        # 1. Parse
        parsed = parser.parse(test_prompt)
        print(f"\n🔤 Parser output:")
        print(f"   Tokens: {len(parsed.tokens)}")
        print(f"   Simboli NEUROGLYPH: {[t for t in parsed.tokens if any(ord(c) > 127 for c in t)]}")
        
        # 2. Prioritize
        priority = prioritizer.prioritize(parsed)
        print(f"\n⚖️ Prioritizer output:")
        print(f"   Domain: {priority.domain}")
        print(f"   Urgency: {priority.urgency:.3f}")
        
        # 3. Memory retrieval
        memory_context = memory.retrieve(priority, parsed)
        print(f"\n🗄️ Memory output:")
        print(f"   Examples: {len(memory_context.examples)}")
        print(f"   Symbols: {memory_context.symbols}")
        print(f"   Errors: {len(memory_context.errors)}")
        print(f"   Relevant cases: {len(memory_context.relevant_cases)}")
        print(f"   Confidence: {memory_context.confidence:.3f}")
        print(f"   Retrieval time: {memory_context.retrieval_time:.4f}s")

def test_memory_with_symbols():
    """Test memory con simboli NEUROGLYPH reali"""
    print("\n🔣 TEST MEMORY CON SIMBOLI NEUROGLYPH")
    print("=" * 60)
    
    with tempfile.TemporaryDirectory() as temp_dir:
        config = {
            'symbol_store': {
                'db_path': os.path.join(temp_dir, 'symbols.lmdb')
            }
        }
        
        memory = NGMemory(config)
        
        # Pre-popola con simboli NEUROGLYPH
        neuroglyph_symbols = [
            {'symbol': '⊢', 'meaning': 'deduzione logica', 'context': 'reasoning'},
            {'symbol': '≈', 'meaning': 'analogia semantica', 'context': 'similarity'},
            {'symbol': '→', 'meaning': 'implicazione causale', 'context': 'logic'},
            {'symbol': '∴', 'meaning': 'quindi/conclusione', 'context': 'reasoning'},
            {'symbol': '¬', 'meaning': 'negazione logica', 'context': 'logic'},
            {'symbol': '∧', 'meaning': 'congiunzione AND', 'context': 'logic'},
            {'symbol': '∨', 'meaning': 'disgiunzione OR', 'context': 'logic'}
        ]
        
        print(f"📝 Pre-popolamento con {len(neuroglyph_symbols)} simboli NEUROGLYPH:")
        
        for symbol_data in neuroglyph_symbols:
            result = memory.symbol_store.execute(symbol_data, operation='store')
            if result.success:
                print(f"   ✅ {symbol_data['symbol']}: {symbol_data['meaning']}")
        
        # Test retrieval con prompt simbolico
        parser = NGParser()
        prioritizer = NGContextPrioritizer()
        
        symbolic_prompt = "Se P ⊢ Q e Q ≈ R, allora P → R ∴ conclusione valida"
        
        print(f"\n🔍 Test retrieval con prompt simbolico:")
        print(f"   Input: {symbolic_prompt}")
        
        parsed = parser.parse(symbolic_prompt)
        priority = prioritizer.prioritize(parsed)
        memory_context = memory.retrieve(priority, parsed)
        
        print(f"\n📊 Risultati retrieval:")
        print(f"   Simboli trovati: {len(memory_context.symbols)}")
        print(f"   Simboli: {memory_context.symbols}")
        print(f"   Examples: {len(memory_context.examples)}")
        
        for example in memory_context.examples:
            print(f"      {example.symbols[0] if example.symbols else 'N/A'}: {example.context}")

def test_performance_benchmark():
    """Test performance del modulo memory"""
    print("\n⚡ TEST PERFORMANCE BENCHMARK")
    print("=" * 60)
    
    with tempfile.TemporaryDirectory() as temp_dir:
        config = {
            'symbol_store': {'db_path': os.path.join(temp_dir, 'symbols.lmdb')},
            'episode_cache': {'cache_path': os.path.join(temp_dir, 'episodes.faiss')},
            'error_log': {'log_path': os.path.join(temp_dir, 'errors.jsonl')}
        }
        
        memory = NGMemory(config)
        parser = NGParser()
        prioritizer = NGContextPrioritizer()
        
        test_prompts = [
            "Semplice test",
            "Crea algoritmo con ⊢ e ≈ simboli",
            "Analisi complessa con ragionamento simbolico ⊢ → ∴",
            "Test performance con molti simboli ⊢≈→∴¬∧∨ e reasoning complesso"
        ]
        
        for i, prompt in enumerate(test_prompts, 1):
            print(f"\n📝 Test {i}: {len(prompt)} chars")
            
            import time
            
            # Misura tempo parsing + prioritization
            start_time = time.time()
            parsed = parser.parse(prompt)
            priority = prioritizer.prioritize(parsed)
            prep_time = time.time() - start_time
            
            # Misura tempo memory retrieval
            start_time = time.time()
            memory_context = memory.retrieve(priority, parsed)
            memory_time = time.time() - start_time
            
            total_time = prep_time + memory_time
            
            print(f"   ⏱️ Prep time: {prep_time:.4f}s")
            print(f"   ⏱️ Memory time: {memory_time:.4f}s")
            print(f"   ⏱️ Total time: {total_time:.4f}s")
            print(f"   📊 Confidence: {memory_context.confidence:.3f}")
            print(f"   🗄️ Symbols: {len(memory_context.symbols)}")
            
            # Performance target: <50ms per memory retrieval
            performance_ok = memory_time < 0.05
            print(f"   ✅ Performance OK: {performance_ok}")

def main():
    """Esegue tutti i test del memory system"""
    try:
        print("🧠 NG-THINK v3.0 ULTRA - MEMORY SYSTEM TESTS")
        print("🎯 Modulo NG_MEMORY con SymbolStore + EpisodeCache + ErrorLog")
        print("=" * 80)
        
        test_symbol_store()
        test_episode_cache()
        test_error_log()
        test_memory_integration()
        test_memory_with_symbols()
        test_performance_benchmark()
        
        print("\n" + "=" * 80)
        print("✅ TUTTI I TEST MEMORY COMPLETATI CON SUCCESSO!")
        print("🏗️ NG_MEMORY reale implementato e funzionante")
        print("📋 SymbolStore, EpisodeCache, ErrorLog operativi")
        print("🚀 Pronto per integrazione in pipeline ibrida")
        print("🎯 Phase A Early Integration ready!")
        print("=" * 80)
        
    except Exception as e:
        print(f"\n❌ ERRORE NEI TEST: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
