{"evaluation_timestamp": "2025-06-01T23:19:36.452797", "dataset_path": "neuroglyph_adaptive_depth_20k.json", "total_examples": 16000, "aggregate_statistics": {"symbolic_completeness": {"mean": 0.884, "median": 0.863, "std": 0.053, "min": 0.66, "max": 1.0}, "logical_structure_score": {"mean": 0.835, "median": 0.82, "std": 0.135, "min": 0.59, "max": 1.0}, "multi_hop_depth": {"mean": 12, "median": 12.0, "std": 0.0, "min": 12, "max": 12, "target_range_3_8": 0.0}, "determinism_score": {"mean": 0.89, "median": 0.92, "std": 0.033, "min": 0.853, "max": 0.92}, "zero_hallucination": {"rate": 1.0, "count_true": 16000, "count_false": 0, "total": 16000}, "symbol_quality": {"mean": 0.899, "median": 0.9, "std": 0.035, "min": 0.775, "max": 0.95}, "cognitive_tags_presence": {"mean": 0.671, "median": 0.625, "std": 0.084, "min": 0.5, "max": 0.875}, "excellence_score": {"mean": 89.32, "median": 88.12, "std": 4.18, "min": 80.52, "max": 96.85}}, "god_mode_analysis": {"quality_distribution": {"supreme_95+": 2674, "excellent_85+": 11450, "good_75+": 1876, "acceptable_65+": 0, "poor_below_65": 0}, "god_mode_examples": 2674, "excellent_examples": 11450, "good_examples": 1876, "total_examples": 16000, "god_mode_rate": 0.167125, "threshold_compliance": {"excellence_score": {"compliant_count": 14124, "compliance_rate": 0.88275, "threshold": 85.0}, "symbolic_completeness": {"compliant_count": 15763, "compliance_rate": 0.9851875, "threshold": 0.8}, "logical_structure_score": {"compliant_count": 13106, "compliance_rate": 0.819125, "threshold": 0.7}, "determinism_score": {"compliant_count": 16000, "compliance_rate": 1.0, "threshold": 0.8}, "symbol_quality": {"compliant_count": 16000, "compliance_rate": 1.0, "threshold": 0.7}, "multi_hop_depth_min": {"compliant_count": 16000, "compliance_rate": 1.0, "threshold": 3}, "multi_hop_depth_max": {"compliant_count": 0, "compliance_rate": 0.0, "threshold": 8}}, "overall_god_mode_readiness": true}, "recommendations": ["🟡 WARNING: Solo 0.0% esempi nel range multi-hop 3-8", "   → Bilanciare profondità ragionamento", "✅ GOOD: Dataset raggiunge standard GOD MODE", "   → Pronto per training con qualità eccellente", "🔴 CRITICAL: Solo 16.7% esempi GOD MODE quality", "   → Rigenerare esempi con qualità superiore"], "detailed_results": [{"example_id": 0, "instruction": "Crea un sistema di inferenza formale utilizzando ⊢ ∴ ∧ per dimostrare proprietà metalogiche. Applica...", "metrics": {"symbolic_completeness": 0.8625, "logical_structure_score": 0.9500000000000001, "multi_hop_depth": 12, "determinism_score": 0.8533333333333334, "zero_hallucination": true, "symbol_quality": 0.95, "cognitive_tags_presence": 0.625, "excellence_score": 89.66}}, {"example_id": 1, "instruction": "Costruisci una dimostrazione formale usando ⊢ ∴ ∧ per dimostrare la decidibilità di sistemi logici. ...", "metrics": {"symbolic_completeness": 0.8625, "logical_structure_score": 0.9500000000000001, "multi_hop_depth": 12, "determinism_score": 0.8533333333333334, "zero_hallucination": true, "symbol_quality": 0.95, "cognitive_tags_presence": 0.625, "excellence_score": 89.66}}, {"example_id": 2, "instruction": "Costruisci una dimostrazione formale usando ⊢ ∴ ∧ per dimostrare la decidibilità di sistemi logici. ...", "metrics": {"symbolic_completeness": 0.8625, "logical_structure_score": 0.9999999999999999, "multi_hop_depth": 12, "determinism_score": 0.92, "zero_hallucination": true, "symbol_quality": 0.9375, "cognitive_tags_presence": 0.75, "excellence_score": 96.85}}, {"example_id": 3, "instruction": "S<PERSON>uppa una catena deduttiva rigorosa con simboli ⊢ ∴ ∧ per analizzare la consistenza logica. Usa d...", "metrics": {"symbolic_completeness": 0.8625, "logical_structure_score": 0.9999999999999999, "multi_hop_depth": 12, "determinism_score": 0.92, "zero_hallucination": true, "symbol_quality": 0.9375, "cognitive_tags_presence": 0.75, "excellence_score": 96.85}}, {"example_id": 4, "instruction": "S<PERSON>uppa una catena deduttiva rigorosa con simboli ⊢ ∴ ∧ per analizzare la consistenza logica. Usa d...", "metrics": {"symbolic_completeness": 0.8625, "logical_structure_score": 0.9999999999999999, "multi_hop_depth": 12, "determinism_score": 0.92, "zero_hallucination": true, "symbol_quality": 0.9375, "cognitive_tags_presence": 0.75, "excellence_score": 96.85}}, {"example_id": 5, "instruction": "Costruisci una dimostrazione formale usando ⊢ ∴ ∧ per dimostrare la decidibilità di sistemi logici. ...", "metrics": {"symbolic_completeness": 0.8625, "logical_structure_score": 0.9500000000000001, "multi_hop_depth": 12, "determinism_score": 0.8533333333333334, "zero_hallucination": true, "symbol_quality": 0.95, "cognitive_tags_presence": 0.625, "excellence_score": 89.66}}, {"example_id": 6, "instruction": "S<PERSON>uppa una catena deduttiva rigorosa con simboli ⊢ ∴ ∧ per analizzare la consistenza logica. Usa d...", "metrics": {"symbolic_completeness": 0.8625, "logical_structure_score": 0.9500000000000001, "multi_hop_depth": 12, "determinism_score": 0.8533333333333334, "zero_hallucination": true, "symbol_quality": 0.95, "cognitive_tags_presence": 0.625, "excellence_score": 89.66}}, {"example_id": 7, "instruction": "Costruisci una dimostrazione formale usando ⊢ ∴ ∧ per dimostrare la decidibilità di sistemi logici. ...", "metrics": {"symbolic_completeness": 0.8625, "logical_structure_score": 0.9500000000000001, "multi_hop_depth": 12, "determinism_score": 0.8533333333333334, "zero_hallucination": true, "symbol_quality": 0.95, "cognitive_tags_presence": 0.625, "excellence_score": 89.66}}, {"example_id": 8, "instruction": "Costruisci una dimostrazione formale usando ⊢ ∴ ∧ per dimostrare la decidibilità di sistemi logici. ...", "metrics": {"symbolic_completeness": 0.8625, "logical_structure_score": 0.9999999999999999, "multi_hop_depth": 12, "determinism_score": 0.92, "zero_hallucination": true, "symbol_quality": 0.9375, "cognitive_tags_presence": 0.75, "excellence_score": 96.85}}, {"example_id": 9, "instruction": "Costruisci una dimostrazione formale usando ⊢ ∴ ∧ per dimostrare la decidibilità di sistemi logici. ...", "metrics": {"symbolic_completeness": 0.8625, "logical_structure_score": 0.9999999999999999, "multi_hop_depth": 12, "determinism_score": 0.92, "zero_hallucination": true, "symbol_quality": 0.9375, "cognitive_tags_presence": 0.75, "excellence_score": 96.85}}], "neuroglyph_readiness": {"symbolic_intelligence_ready": true, "deterministic_reasoning_ready": true, "zero_hallucination_ready": true, "multi_hop_ready": true, "overall_god_mode_ready": true}}