{"evaluation_timestamp": "2025-06-01T22:37:44.675360", "dataset_path": "neuroglyph_supreme_god_mode_perfected.json", "total_examples": 800, "aggregate_statistics": {"symbolic_completeness": {"mean": 0.979, "median": 1.0, "std": 0.054, "min": 0.7, "max": 1.0}, "logical_structure_score": {"mean": 0.386, "median": 0.34, "std": 0.15, "min": 0.18, "max": 0.75}, "multi_hop_depth": {"mean": 4.8, "median": 5.0, "std": 2.4, "min": 1, "max": 12, "target_range_3_8": 0.7175}, "determinism_score": {"mean": 0.911, "median": 0.92, "std": 0.034, "min": 0.84, "max": 1.0}, "zero_hallucination": {"rate": 1.0, "count_true": 800, "count_false": 0, "total": 800}, "symbol_quality": {"mean": 0.891, "median": 0.9, "std": 0.058, "min": 0.7, "max": 0.967}, "cognitive_tags_presence": {"mean": 0.546, "median": 0.5, "std": 0.114, "min": 0.375, "max": 1.0}, "excellence_score": {"mean": 73.66, "median": 73.27, "std": 7.23, "min": 59.8, "max": 89.11}}, "god_mode_analysis": {"quality_distribution": {"supreme_95+": 0, "excellent_85+": 66, "good_75+": 252, "acceptable_65+": 426, "poor_below_65": 56}, "god_mode_examples": 0, "excellent_examples": 66, "good_examples": 252, "total_examples": 800, "god_mode_rate": 0.0, "threshold_compliance": {"excellence_score": {"compliant_count": 66, "compliance_rate": 0.0825, "threshold": 85.0}, "symbolic_completeness": {"compliant_count": 788, "compliance_rate": 0.985, "threshold": 0.8}, "logical_structure_score": {"compliant_count": 64, "compliance_rate": 0.08, "threshold": 0.7}, "determinism_score": {"compliant_count": 800, "compliance_rate": 1.0, "threshold": 0.8}, "symbol_quality": {"compliant_count": 789, "compliance_rate": 0.98625, "threshold": 0.7}, "multi_hop_depth_min": {"compliant_count": 626, "compliance_rate": 0.7825, "threshold": 3}, "multi_hop_depth_max": {"compliant_count": 748, "compliance_rate": 0.935, "threshold": 8}}, "overall_god_mode_readiness": false}, "recommendations": ["🔴 CRITICAL: Excellence score medio (73.66) sotto soglia GOD MODE (85)", "   → Migliorare qualità generale degli esempi", "🟡 WARNING: Logical structure score (0.39) sotto target (0.7)", "   → <PERSON><PERSON><PERSON><PERSON><PERSON> struttura ragionamento simbolico", "🟡 WARNING: Solo 71.8% esempi nel range multi-hop 3-8", "   → Bilanciare profondità ragionamento", "🔴 CRITICAL: Solo 0.0% esempi GOD MODE quality", "   → Rigenerare esempi con qualità superiore"], "detailed_results": [{"example_id": 0, "instruction": "Costruisci un'analogia strutturale tra sistema solare e modello atomico usando ≈ → ∴. Applica analog...", "metrics": {"symbolic_completeness": 1.0, "logical_structure_score": 0.21000000000000002, "multi_hop_depth": 4, "determinism_score": 0.92, "zero_hallucination": true, "symbol_quality": 0.9, "cognitive_tags_presence": 0.5, "excellence_score": 69.18}}, {"example_id": 1, "instruction": "Crea un sistema di inferenza formale utilizzando ⊢ ∴ ∧ per dimostrare proprietà metalogiche. Applica...", "metrics": {"symbolic_completeness": 1.0, "logical_structure_score": 0.26, "multi_hop_depth": 3, "determinism_score": 0.92, "zero_hallucination": true, "symbol_quality": 0.7999999999999999, "cognitive_tags_presence": 0.5, "excellence_score": 66.91}}, {"example_id": 2, "instruction": "Sviluppa una prova formale dell'identità ∑k = n(n+1)/2 con simboli ∀ ∃ ∈. Applica quantificatore_uni...", "metrics": {"symbolic_completeness": 1.0, "logical_structure_score": 0.43999999999999995, "multi_hop_depth": 2, "determinism_score": 0.92, "zero_hallucination": true, "symbol_quality": 0.9571428571428572, "cognitive_tags_presence": 0.625, "excellence_score": 69.91}}, {"example_id": 3, "instruction": "Costruisci una dimostrazione rigorosa per la formula della somma aritmetica usando ∀ ∃ ∈. Utilizza q...", "metrics": {"symbolic_completeness": 1.0, "logical_structure_score": 0.43999999999999995, "multi_hop_depth": 2, "determinism_score": 0.92, "zero_hallucination": true, "symbol_quality": 0.9571428571428572, "cognitive_tags_presence": 0.625, "excellence_score": 69.91}}, {"example_id": 4, "instruction": "Costruisci una dimostrazione rigorosa per la formula della somma aritmetica usando ∀ ∃ ∈. Utilizza q...", "metrics": {"symbolic_completeness": 1.0, "logical_structure_score": 0.43999999999999995, "multi_hop_depth": 2, "determinism_score": 0.92, "zero_hallucination": true, "symbol_quality": 0.9571428571428572, "cognitive_tags_presence": 0.625, "excellence_score": 69.91}}, {"example_id": 5, "instruction": "Sviluppa una prova formale dell'identità ∑k = n(n+1)/2 con simboli ∀ ∃ ∈. Applica quantificatore_uni...", "metrics": {"symbolic_completeness": 1.0, "logical_structure_score": 0.43999999999999995, "multi_hop_depth": 2, "determinism_score": 0.92, "zero_hallucination": true, "symbol_quality": 0.9571428571428572, "cognitive_tags_presence": 0.625, "excellence_score": 69.91}}, {"example_id": 6, "instruction": "Sviluppa soluzione creativa per sorting massivo usando ragionamento simbolico 🎯 🤔 💡. Utilizza focus_...", "metrics": {"symbolic_completeness": 1.0, "logical_structure_score": 0.21000000000000002, "multi_hop_depth": 3, "determinism_score": 0.84, "zero_hallucination": true, "symbol_quality": 0.7999999999999999, "cognitive_tags_presence": 0.625, "excellence_score": 65.11}}, {"example_id": 7, "instruction": "Crea una catena di inferenza analogica usando ≈ → ∴ per identificare pattern universali. Applica ana...", "metrics": {"symbolic_completeness": 1.0, "logical_structure_score": 0.31000000000000005, "multi_hop_depth": 5, "determinism_score": 0.92, "zero_hallucination": true, "symbol_quality": 0.925, "cognitive_tags_presence": 0.5, "excellence_score": 73.5}}, {"example_id": 8, "instruction": "Costruisci strategia di ottimizzazione con simboli 🎯 🤔 💡 per algoritmi paralleli. Applica focus_obie...", "metrics": {"symbolic_completeness": 1.0, "logical_structure_score": 0.41000000000000003, "multi_hop_depth": 2, "determinism_score": 0.84, "zero_hallucination": true, "symbol_quality": 0.9, "cognitive_tags_presence": 0.625, "excellence_score": 67.39}}, {"example_id": 9, "instruction": "Costruisci una dimostrazione rigorosa per la formula della somma aritmetica usando ∀ ∃ ∈. Utilizza q...", "metrics": {"symbolic_completeness": 1.0, "logical_structure_score": 0.43999999999999995, "multi_hop_depth": 2, "determinism_score": 0.92, "zero_hallucination": true, "symbol_quality": 0.9571428571428572, "cognitive_tags_presence": 0.625, "excellence_score": 69.91}}], "neuroglyph_readiness": {"symbolic_intelligence_ready": false, "deterministic_reasoning_ready": true, "zero_hallucination_ready": true, "multi_hop_ready": true, "overall_god_mode_ready": true}}