#!/usr/bin/env python3
"""
Fix dataclass field ordering in ng_types.py
"""

import re

def fix_dataclass_ordering():
    """Fix dataclass field ordering to put required fields first"""
    
    with open('neuroglyph/ng_think/core/ng_types.py', 'r') as f:
        content = f.read()
    
    # Pattern per trovare dataclass
    dataclass_pattern = r'@dataclass\nclass (\w+):\s*\n(.*?)(?=\n@dataclass|\n\nclass|\nclass|\Z)'
    
    def fix_single_dataclass(match):
        class_name = match.group(1)
        class_body = match.group(2)
        
        # Estrae i campi
        field_pattern = r'(\s+)(\w+):\s*([^=\n]+)(?:\s*=\s*([^\n]+))?'
        fields = []
        
        for field_match in re.finditer(field_pattern, class_body):
            indent = field_match.group(1)
            field_name = field_match.group(2)
            field_type = field_match.group(3).strip()
            default_value = field_match.group(4)
            
            has_default = default_value is not None
            
            fields.append({
                'indent': indent,
                'name': field_name,
                'type': field_type,
                'default': default_value,
                'has_default': has_default,
                'full_line': field_match.group(0)
            })
        
        if not fields:
            return match.group(0)
        
        # Separa campi con e senza default
        required_fields = [f for f in fields if not f['has_default']]
        optional_fields = [f for f in fields if f['has_default']]
        
        # Ricostruisce la classe
        docstring_pattern = r'(\s+""".*?"""\s*\n)'
        docstring_match = re.search(docstring_pattern, class_body, re.DOTALL)
        
        result = f"@dataclass\nclass {class_name}:\n"
        
        if docstring_match:
            result += docstring_match.group(1)
        
        # Aggiunge campi richiesti prima
        for field in required_fields:
            if field['name'] not in ['"""', 'pass']:  # Skip docstring e pass
                result += f"{field['indent']}{field['name']}: {field['type']}\n"
        
        # Aggiunge campi opzionali dopo
        for field in optional_fields:
            if field['name'] not in ['"""', 'pass']:  # Skip docstring e pass
                result += f"{field['indent']}{field['name']}: {field['type']} = {field['default']}\n"
        
        return result
    
    # Applica fix a tutti i dataclass
    fixed_content = re.sub(dataclass_pattern, fix_single_dataclass, content, flags=re.DOTALL)
    
    # Scrive il file corretto
    with open('neuroglyph/ng_think/core/ng_types.py', 'w') as f:
        f.write(fixed_content)
    
    print("✅ Dataclass field ordering fixed!")

if __name__ == "__main__":
    fix_dataclass_ordering()
