# extract_unicode_symbols.py (VERSIONE MIGLIORATA, RESILIENTE E COMPATIBILE CON SANDBOX)
import csv
import json
import hashlib
import unicodedata
import os
from datetime import datetime

# === CONFIG ===
UNICODE_DATA_FILE = os.environ.get("UNICODE_DATA_FILE", "neuroglyph/UCD/UnicodeData.txt")
OUTPUT_REGISTRY_JSON = "neuroglyph/core/unicode_official_symbols_UCD.json"
OUTPUT_CSV = "neuroglyph/logs/unicode_extraction_UCD_log.csv"

VALID_CATEGORIES = {"Sm", "So"}
BLOCKED_NAMES = {"", "NULL", "RESERVED"}

CATEGORY_TO_DOMAIN = {
    "Sm": "math",
    "So": "logic",
    "Sc": "currency",
    "Sk": "modifiers",
    "Nl": "number",
}

def slugify(text):
    return (
        text.lower()
        .replace(" ", "-")
        .replace("/", "-")
        .replace("--", "-")
        .replace("(", "")
        .replace(")", "")
        .replace(",", "")
    )

def score_symbol():
    return 95.0

def is_utf8_valid(char):
    try:
        encoded = char.encode("utf-8")
        return 1 <= len(encoded) <= 4
    except:
        return False

def normalize_name(name):
    return slugify(name.strip().lower())

# === CHECK FILE LOCALE ===
if not os.path.isfile(UNICODE_DATA_FILE):
    raise FileNotFoundError(f"❌ File '{UNICODE_DATA_FILE}' non trovato. Verifica che la directory UCD sia presente.")

# === CREA DIRECTORY SE NECESSARIE ===
os.makedirs("neuroglyph/core", exist_ok=True)
os.makedirs("neuroglyph/logs", exist_ok=True)

registry = []
symbol_set = set()
fallback_set = set()

with open(UNICODE_DATA_FILE, "r", encoding="utf-8") as infile, open(OUTPUT_CSV, "w", newline="") as csvfile:
    writer = csv.writer(csvfile)
    writer.writerow(["symbol", "name", "domain", "fallback", "score", "hash", "timestamp"])

    for line in infile:
        fields = line.strip().split(";")
        if len(fields) < 3:
            continue

        codepoint_hex, name, category = fields[0], fields[1], fields[2]
        if category not in VALID_CATEGORIES:
            continue
        if name.startswith("<") or name in BLOCKED_NAMES:
            continue

        try:
            symbol = chr(int(codepoint_hex, 16))
        except:
            continue

        if not is_utf8_valid(symbol) or unicodedata.category(symbol)[0] == "C":
            continue

        fallback_slug = normalize_name(name)
        fallback = f"ng:{CATEGORY_TO_DOMAIN.get(category, 'logic')}:{fallback_slug}"

        if symbol in symbol_set or fallback in fallback_set:
            continue

        score = score_symbol()
        hash_id = hashlib.sha256(fallback.encode()).hexdigest()[:8]

        registry.append({
            "symbol": symbol,
            "type": CATEGORY_TO_DOMAIN.get(category, "logic"),
            "tags": [name.lower()],
            "fallback": fallback,
            "score": score,
            "hash": hash_id,
            "gen_time": datetime.utcnow().isoformat(),
            "source": "unicode16",
            "verified_by": "SymbolValidator-v3",
            "unicode_name_original": name
        })

        writer.writerow([
            symbol, name, CATEGORY_TO_DOMAIN.get(category, "logic"), fallback, score, hash_id, datetime.utcnow().isoformat()
        ])

        symbol_set.add(symbol)
        fallback_set.add(fallback)

with open(OUTPUT_REGISTRY_JSON, "w", encoding="utf-8") as f:
    json.dump({"approved_symbols": registry, "locked": True}, f, ensure_ascii=False, indent=2)

print(f"✅ Estratti {len(registry)} simboli unici da {UNICODE_DATA_FILE} → Salvati in {OUTPUT_REGISTRY_JSON}")
