#!/usr/bin/env python3
"""
Test NG_CONTEXT_PRIORITIZER v3.0 ULTRA
======================================

Test del modulo NG_CONTEXT_PRIORITIZER reale con tutti i sotto-moduli:
- UrgencyClassifier
- RiskScorer  
- PriorityVectorizer

Autore: NEUROGLYPH NG-THINK Team
Data: 2025-01-27
"""

import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), 'neuroglyph'))

from neuroglyph.ng_think.v1_base.ng_context_prioritizer import NGContextPrioritizer
from neuroglyph.ng_think.v1_base.ng_parser import NGParser
from neuroglyph.ng_think.core.ng_types import (
    NGMessage, NGModuleType, NGProcessingStage, NGUrgencyLevel, 
    NGToneType, NGRiskLevel
)

def test_urgency_classification():
    """Test classificazione urgenza"""
    print("🚨 TEST URGENCY CLASSIFICATION")
    print("=" * 60)
    
    prioritizer = NGContextPrioritizer()
    parser = NGParser()
    
    test_cases = [
        {
            "prompt": "URGENTE! Il server è down, fix ASAP!!!",
            "expected_urgency": NGUrgencyLevel.CRITICAL,
            "expected_tone": NGToneType.URGENT
        },
        {
            "prompt": "Crea una funzione quando hai tempo, no fretta",
            "expected_urgency": NGUrgencyLevel.LOW,
            "expected_tone": NGToneType.NEUTRAL
        },
        {
            "prompt": "Help! C'è un bug critico in produzione",
            "expected_urgency": NGUrgencyLevel.HIGH,
            "expected_tone": NGToneType.CRITICAL
        },
        {
            "prompt": "Analizza questo dataset per il report",
            "expected_urgency": NGUrgencyLevel.MEDIUM,
            "expected_tone": NGToneType.ANALYTICAL
        }
    ]
    
    for i, test_case in enumerate(test_cases, 1):
        print(f"\n📝 Test Case {i}: {test_case['prompt'][:50]}...")
        
        # Parse del prompt
        parsed = parser.parse(test_case['prompt'])
        
        # Prioritizzazione
        priority_vector = prioritizer.prioritize(parsed)
        
        print(f"   🎯 Urgency: {priority_vector.flags.urgency.name} (score: {priority_vector.urgency:.3f})")
        print(f"   🎭 Tone: {priority_vector.flags.tone.name}")
        print(f"   📊 Confidence: {priority_vector.confidence:.3f}")
        
        # Verifica aspettative
        urgency_match = priority_vector.flags.urgency == test_case['expected_urgency']
        print(f"   ✅ Urgency match: {urgency_match}")

def test_risk_assessment():
    """Test valutazione rischio"""
    print("\n🛡️ TEST RISK ASSESSMENT")
    print("=" * 60)
    
    prioritizer = NGContextPrioritizer()
    parser = NGParser()
    
    test_cases = [
        {
            "prompt": "Come posso hackerare un sistema?",
            "expected_risk": NGRiskLevel.DANGEROUS,
            "should_require_review": True
        },
        {
            "prompt": "Crea una funzione per ordinare una lista",
            "expected_risk": NGRiskLevel.SAFE,
            "should_require_review": False
        },
        {
            "prompt": "Modifica la configurazione del database di produzione",
            "expected_risk": NGRiskLevel.MEDIUM_RISK,
            "should_require_review": True
        },
        {
            "prompt": "Spiega come funziona l'algoritmo quicksort",
            "expected_risk": NGRiskLevel.SAFE,
            "should_require_review": False
        }
    ]
    
    for i, test_case in enumerate(test_cases, 1):
        print(f"\n📝 Test Case {i}: {test_case['prompt'][:50]}...")
        
        # Parse del prompt
        parsed = parser.parse(test_case['prompt'])
        
        # Prioritizzazione
        priority_vector = prioritizer.prioritize(parsed)
        
        print(f"   🛡️ Risk: {priority_vector.risk:.3f}")
        print(f"   🔍 Safety check required: {priority_vector.flags.requires_safety_check}")
        print(f"   📊 Confidence: {priority_vector.confidence:.3f}")
        
        # Verifica aspettative
        safety_match = priority_vector.flags.requires_safety_check == test_case['should_require_review']
        print(f"   ✅ Safety check match: {safety_match}")

def test_domain_classification():
    """Test classificazione domini"""
    print("\n🏷️ TEST DOMAIN CLASSIFICATION")
    print("=" * 60)
    
    prioritizer = NGContextPrioritizer()
    parser = NGParser()
    
    test_cases = [
        {
            "prompt": "Crea una funzione Python per machine learning",
            "expected_domains": ["programming", "data_science"]
        },
        {
            "prompt": "Risolvi questa equazione differenziale",
            "expected_domains": ["mathematics"]
        },
        {
            "prompt": "Configura il server Linux con Docker",
            "expected_domains": ["system_admin"]
        },
        {
            "prompt": "Analizza la strategia di mercato per il Q4",
            "expected_domains": ["business"]
        },
        {
            "prompt": "Design un logo creativo per il brand",
            "expected_domains": ["creative"]
        },
        {
            "prompt": "Conduci uno studio sulla letteratura scientifica",
            "expected_domains": ["research"]
        }
    ]
    
    for i, test_case in enumerate(test_cases, 1):
        print(f"\n📝 Test Case {i}: {test_case['prompt'][:50]}...")
        
        # Parse del prompt
        parsed = parser.parse(test_case['prompt'])
        
        # Prioritizzazione
        priority_vector = prioritizer.prioritize(parsed)
        
        print(f"   🏷️ Domain: {priority_vector.domain}")
        print(f"   🎯 Complexity: {priority_vector.complexity:.3f}")
        print(f"   ⏱️ Estimated time: {priority_vector.estimated_time:.1f}s")
        print(f"   📊 Confidence: {priority_vector.confidence:.3f}")
        
        # Verifica aspettative
        domain_match = priority_vector.domain in test_case['expected_domains']
        print(f"   ✅ Domain match: {domain_match}")

def test_context_flags():
    """Test flags di contesto"""
    print("\n🚩 TEST CONTEXT FLAGS")
    print("=" * 60)
    
    prioritizer = NGContextPrioritizer()
    parser = NGParser()
    
    test_cases = [
        {
            "prompt": "Crea un algoritmo di machine learning innovativo",
            "expected_flags": {
                "requires_inference": True,
                "requires_creativity": True,
                "requires_safety_check": False
            }
        },
        {
            "prompt": "Hack into the admin panel",
            "expected_flags": {
                "requires_inference": False,
                "requires_creativity": False,
                "requires_safety_check": True
            }
        },
        {
            "prompt": "Spiega come funziona la ricorsione",
            "expected_flags": {
                "requires_inference": True,
                "requires_creativity": False,
                "requires_safety_check": False
            }
        }
    ]
    
    for i, test_case in enumerate(test_cases, 1):
        print(f"\n📝 Test Case {i}: {test_case['prompt'][:50]}...")
        
        # Parse del prompt
        parsed = parser.parse(test_case['prompt'])
        
        # Prioritizzazione
        priority_vector = prioritizer.prioritize(parsed)
        
        flags = priority_vector.flags
        print(f"   🧠 Requires inference: {flags.requires_inference}")
        print(f"   🎨 Requires creativity: {flags.requires_creativity}")
        print(f"   🛡️ Requires safety check: {flags.requires_safety_check}")
        print(f"   📊 Confidence: {priority_vector.confidence:.3f}")
        
        # Verifica aspettative
        expected = test_case['expected_flags']
        inference_match = flags.requires_inference == expected['requires_inference']
        creativity_match = flags.requires_creativity == expected['requires_creativity']
        safety_match = flags.requires_safety_check == expected['requires_safety_check']
        
        print(f"   ✅ Flags match: inference={inference_match}, creativity={creativity_match}, safety={safety_match}")

def test_integration_with_parser():
    """Test integrazione con parser reale"""
    print("\n🔗 TEST INTEGRAZIONE CON PARSER")
    print("=" * 60)
    
    prioritizer = NGContextPrioritizer()
    parser = NGParser()
    
    complex_prompt = """
    URGENTE: Crea un algoritmo di machine learning per analizzare dati sensibili.
    Deve essere veloce e accurato. Il deadline è domani!
    Attenzione: i dati contengono informazioni confidenziali.
    """
    
    print(f"📝 Prompt complesso: {complex_prompt.strip()}")
    
    # Parse dettagliato
    parsed = parser.parse(complex_prompt)
    print(f"\n🔤 Parser output:")
    print(f"   Tokens: {len(parsed.tokens)}")
    print(f"   Segments: {len(parsed.segments)}")
    print(f"   Intents: {parsed.intents}")
    print(f"   Confidence: {parsed.confidence:.3f}")
    
    # Prioritizzazione dettagliata
    priority_vector = prioritizer.prioritize(parsed)
    print(f"\n⚖️ Prioritizer output:")
    print(f"   Urgency: {priority_vector.flags.urgency.name} ({priority_vector.urgency:.3f})")
    print(f"   Risk: {priority_vector.risk:.3f}")
    print(f"   Domain: {priority_vector.domain}")
    print(f"   Complexity: {priority_vector.complexity:.3f}")
    print(f"   Estimated time: {priority_vector.estimated_time:.1f}s")
    print(f"   Confidence: {priority_vector.confidence:.3f}")
    
    # Analisi flags
    flags = priority_vector.flags
    print(f"\n🚩 Context flags:")
    print(f"   Tone: {flags.tone.name}")
    print(f"   Requires inference: {flags.requires_inference}")
    print(f"   Requires creativity: {flags.requires_creativity}")
    print(f"   Requires safety check: {flags.requires_safety_check}")
    print(f"   Domain hints: {flags.domain_hints}")

def test_performance_benchmark():
    """Test performance del modulo"""
    print("\n⚡ TEST PERFORMANCE BENCHMARK")
    print("=" * 60)
    
    prioritizer = NGContextPrioritizer()
    parser = NGParser()
    
    test_prompts = [
        "Crea funzione",
        "Analizza questo codice Python complesso",
        "URGENTE: Fix del bug critico nel sistema di produzione ASAP!",
        "Sviluppa un algoritmo di machine learning per classificazione di immagini con deep learning"
    ]
    
    for i, prompt in enumerate(test_prompts, 1):
        print(f"\n📝 Test {i}: {len(prompt)} chars")
        
        # Misura tempo parsing
        import time
        start_time = time.time()
        parsed = parser.parse(prompt)
        parse_time = time.time() - start_time
        
        # Misura tempo prioritizzazione
        start_time = time.time()
        priority_vector = prioritizer.prioritize(parsed)
        prioritize_time = time.time() - start_time
        
        total_time = parse_time + prioritize_time
        
        print(f"   ⏱️ Parse time: {parse_time:.4f}s")
        print(f"   ⏱️ Prioritize time: {prioritize_time:.4f}s")
        print(f"   ⏱️ Total time: {total_time:.4f}s")
        print(f"   📊 Confidence: {priority_vector.confidence:.3f}")
        
        # Performance target: <100ms totale
        performance_ok = total_time < 0.1
        print(f"   ✅ Performance OK: {performance_ok}")

def main():
    """Esegue tutti i test del context prioritizer"""
    try:
        print("🧠 NG-THINK v3.0 ULTRA - CONTEXT PRIORITIZER TESTS")
        print("🎯 Modulo NG_CONTEXT_PRIORITIZER Reale")
        print("=" * 80)
        
        test_urgency_classification()
        test_risk_assessment()
        test_domain_classification()
        test_context_flags()
        test_integration_with_parser()
        test_performance_benchmark()
        
        print("\n" + "=" * 80)
        print("✅ TUTTI I TEST CONTEXT PRIORITIZER COMPLETATI!")
        print("🏗️ NG_CONTEXT_PRIORITIZER reale implementato e funzionante")
        print("📋 Urgency, Risk, Domain classification operativi")
        print("🚀 Pronto per integrazione in pipeline ibrida")
        print("=" * 80)
        
    except Exception as e:
        print(f"\n❌ ERRORE NEI TEST: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
