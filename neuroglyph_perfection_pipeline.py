#!/usr/bin/env python3
"""
NEUROGLYPH Perfection Pipeline
==============================

Pipeline completa per portare il dataset SUPREME GOD MODE alla perfezione assoluta:

1. Multi-hop Balancing (12 → 3-8 range ottimale)
2. Symbol Quality Normalization (0.89 → >0.95)
3. Cognitive Tags Expansion (0.787 → >0.9)
4. Final Validation (Excellence Score → >95)

Obiettivo: Dataset perfetto per il primo LLM simbolico deterministico.

Autore: NEUROGLYPH Perfection Team
Data: 2025-06-01
"""

import json
import subprocess
import sys
from datetime import datetime
from typing import Dict, Any

class NeuroglyphPerfectionPipeline:
    """
    Pipeline completa per perfezionamento dataset NEUROGLYPH.
    
    Orchestrazione di tutti gli strumenti di ottimizzazione.
    """
    
    def __init__(self):
        self.pipeline_steps = [
            {
                'name': 'Multi-hop Balancing',
                'script': 'dataset_balancer.py',
                'description': 'Bilancia profondità ragionamento 3-8 step',
                'target_improvement': 'Multi-hop distribution ottimale'
            },
            {
                'name': 'Symbol Quality Normalization',
                'script': 'symbol_quality_normalizer.py',
                'description': 'Normalizza simboli per qualità >0.95',
                'target_improvement': 'Symbol Quality: 0.89 → >0.95'
            },
            {
                'name': 'Cognitive Tags Expansion',
                'script': 'cognitive_tags_expander.py',
                'description': 'Espande cognitive tags a >0.9',
                'target_improvement': 'Cognitive Tags: 0.787 → >0.9'
            }
        ]
        
        self.expected_improvements = {
            'excellence_score': 95.0,  # Da 93.66 a >95
            'symbol_quality': 0.95,   # Da 0.89 a >0.95
            'cognitive_tags': 0.9,    # Da 0.787 a >0.9
            'multi_hop_balance': True  # Distribuzione bilanciata
        }
    
    def run_perfection_pipeline(self, input_dataset: str) -> Dict[str, Any]:
        """Esegue pipeline completa di perfezionamento."""
        
        print("🎯 NEUROGLYPH PERFECTION PIPELINE")
        print("=" * 50)
        print("🎊 Portando il dataset alla perfezione assoluta...")
        
        pipeline_results = {
            'start_time': datetime.now().isoformat(),
            'input_dataset': input_dataset,
            'steps_completed': [],
            'final_dataset': None,
            'improvements_achieved': {},
            'success': False
        }
        
        current_dataset = input_dataset
        
        # Esegui ogni step della pipeline
        for i, step in enumerate(self.pipeline_steps, 1):
            print(f"\n🔧 STEP {i}/3: {step['name']}")
            print(f"   📋 {step['description']}")
            print(f"   🎯 Target: {step['target_improvement']}")
            
            try:
                # Determina file output per questo step
                if i == 1:
                    output_dataset = "neuroglyph_supreme_god_mode_balanced.json"
                elif i == 2:
                    output_dataset = "neuroglyph_supreme_god_mode_normalized.json"
                else:
                    output_dataset = "neuroglyph_supreme_god_mode_perfected.json"
                
                # Esegui script
                print(f"   ⚙️ Running {step['script']}...")
                result = subprocess.run([
                    sys.executable, step['script']
                ], capture_output=True, text=True, timeout=300)
                
                if result.returncode == 0:
                    print(f"   ✅ {step['name']} completed successfully")
                    
                    step_result = {
                        'name': step['name'],
                        'script': step['script'],
                        'success': True,
                        'output_dataset': output_dataset,
                        'stdout': result.stdout[-500:] if result.stdout else '',  # Last 500 chars
                        'execution_time': datetime.now().isoformat()
                    }
                    
                    pipeline_results['steps_completed'].append(step_result)
                    current_dataset = output_dataset
                    
                else:
                    print(f"   ❌ {step['name']} failed")
                    print(f"   Error: {result.stderr}")
                    
                    step_result = {
                        'name': step['name'],
                        'script': step['script'],
                        'success': False,
                        'error': result.stderr,
                        'execution_time': datetime.now().isoformat()
                    }
                    
                    pipeline_results['steps_completed'].append(step_result)
                    break
                    
            except subprocess.TimeoutExpired:
                print(f"   ⏰ {step['name']} timed out")
                step_result = {
                    'name': step['name'],
                    'script': step['script'],
                    'success': False,
                    'error': 'Timeout after 300 seconds',
                    'execution_time': datetime.now().isoformat()
                }
                pipeline_results['steps_completed'].append(step_result)
                break
                
            except Exception as e:
                print(f"   💥 {step['name']} crashed: {e}")
                step_result = {
                    'name': step['name'],
                    'script': step['script'],
                    'success': False,
                    'error': str(e),
                    'execution_time': datetime.now().isoformat()
                }
                pipeline_results['steps_completed'].append(step_result)
                break
        
        # Valida risultato finale
        if len(pipeline_results['steps_completed']) == 3 and all(step['success'] for step in pipeline_results['steps_completed']):
            pipeline_results['success'] = True
            pipeline_results['final_dataset'] = current_dataset
            
            # Esegui validazione finale
            print(f"\n🔍 FINAL VALIDATION")
            final_validation = self._run_final_validation(current_dataset)
            pipeline_results['final_validation'] = final_validation
            
        pipeline_results['end_time'] = datetime.now().isoformat()
        
        # Stampa summary finale
        self._print_pipeline_summary(pipeline_results)
        
        # Salva report pipeline
        report_filename = f"neuroglyph_perfection_pipeline_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        with open(report_filename, 'w', encoding='utf-8') as f:
            json.dump(pipeline_results, f, indent=2, ensure_ascii=False)
        
        print(f"\n💾 Pipeline report saved: {report_filename}")
        
        return pipeline_results
    
    def _run_final_validation(self, dataset_path: str) -> Dict[str, Any]:
        """Esegue validazione finale del dataset perfezionato."""
        
        try:
            print(f"   ⚙️ Running final GOD MODE validation...")
            result = subprocess.run([
                sys.executable, 'neuroglyph_god_mode_validator.py'
            ], capture_output=True, text=True, timeout=180)
            
            if result.returncode == 0:
                print(f"   ✅ Final validation completed")
                return {
                    'success': True,
                    'stdout': result.stdout[-1000:] if result.stdout else '',
                    'validation_time': datetime.now().isoformat()
                }
            else:
                print(f"   ❌ Final validation failed")
                return {
                    'success': False,
                    'error': result.stderr,
                    'validation_time': datetime.now().isoformat()
                }
                
        except Exception as e:
            print(f"   💥 Final validation crashed: {e}")
            return {
                'success': False,
                'error': str(e),
                'validation_time': datetime.now().isoformat()
            }
    
    def _print_pipeline_summary(self, results: Dict[str, Any]):
        """Stampa summary della pipeline."""
        
        print("\n" + "="*50)
        print("🎊 NEUROGLYPH PERFECTION PIPELINE SUMMARY")
        print("="*50)
        
        # Status generale
        success_count = sum(1 for step in results['steps_completed'] if step['success'])
        total_steps = len(self.pipeline_steps)
        
        overall_success = results['success']
        status_emoji = "🎊" if overall_success else "⚠️"
        status_text = "PERFECTION ACHIEVED" if overall_success else "PARTIAL COMPLETION"
        
        print(f"\n{status_emoji} OVERALL STATUS: {status_text}")
        print(f"📊 Steps completed: {success_count}/{total_steps}")
        
        # Dettagli step
        print(f"\n📋 PIPELINE STEPS:")
        for step in results['steps_completed']:
            emoji = "✅" if step['success'] else "❌"
            print(f"   {emoji} {step['name']}")
            if not step['success'] and 'error' in step:
                print(f"      Error: {step['error'][:100]}...")
        
        # Dataset finale
        if results['final_dataset']:
            print(f"\n📁 FINAL DATASET: {results['final_dataset']}")
            
            # Validazione finale
            if 'final_validation' in results:
                validation = results['final_validation']
                val_emoji = "✅" if validation['success'] else "❌"
                print(f"   {val_emoji} Final validation: {'PASSED' if validation['success'] else 'FAILED'}")
        
        # Miglioramenti attesi
        print(f"\n🎯 EXPECTED IMPROVEMENTS:")
        for metric, target in self.expected_improvements.items():
            print(f"   📈 {metric}: Target {target}")
        
        # Raccomandazioni
        if overall_success:
            print(f"\n🎊 CONGRATULATIONS!")
            print(f"   Dataset NEUROGLYPH SUPREME GOD MODE raggiunge perfezione assoluta!")
            print(f"   Pronto per training del primo LLM simbolico deterministico perfetto!")
        else:
            print(f"\n⚠️ PARTIAL SUCCESS")
            print(f"   Alcuni step necessitano attenzione")
            print(f"   Verificare errori e ri-eseguire se necessario")
        
        print("="*50)

def main():
    """Esegue pipeline di perfezionamento completa."""
    
    input_dataset = "neuroglyph_supreme_god_mode_1k_test.json"
    
    print("🎯 NEUROGLYPH PERFECTION PIPELINE")
    print("=" * 40)
    print("🎊 Portando il dataset alla perfezione assoluta")
    print("📊 3 step di ottimizzazione chirurgica")
    
    pipeline = NeuroglyphPerfectionPipeline()
    
    # Esegui pipeline completa
    results = pipeline.run_perfection_pipeline(input_dataset)
    
    if results['success']:
        print(f"\n🎊 PERFECTION ACHIEVED!")
        print(f"🚀 Dataset pronto per training LLM simbolico perfetto!")
    else:
        print(f"\n⚠️ Pipeline parzialmente completata")
        print(f"🔧 Verificare errori e ri-eseguire se necessario")

if __name__ == "__main__":
    main()
