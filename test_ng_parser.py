#!/usr/bin/env python3
"""
Test per NG_PARSER v3.0 ULTRA
=============================

Test completo del modulo di parsing simbolico con tutti i sottomoduli:
- UltraTokenizer
- IntentSegmenter  
- AmbiguityDetector

Autore: NEUROGLYPH NG-THINK Team
Data: 2025-01-27
"""

import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), 'neuroglyph'))

from neuroglyph.ng_think.v1_base.ng_parser import NGParser
from neuroglyph.ng_think.core.ng_types import NGMessage, NGModuleType, NGProcessingStage

def test_ng_parser_basic():
    """Test base del modulo NG_PARSER"""
    print("🧪 TEST NG_PARSER v3.0 ULTRA")
    print("=" * 60)
    
    # Inizializza parser
    parser = NGParser()
    
    # Test case 1: Prompt semplice
    print("\n📝 Test Case 1: Prompt semplice")
    test_input = "Crea una funzione per ordinare una lista in Python"
    
    message = NGMessage(
        source_module=NGModuleType.PARSER,
        stage=NGProcessingStage.PARSING,
        content=test_input
    )
    
    result = parser.process(message)
    
    print(f"   Input: {test_input}")
    print(f"   Success: {result.metadata.get('overall_success', False)}")
    print(f"   Confidence: {result.confidence:.3f}")
    print(f"   Processing time: {result.processing_time:.3f}s")
    
    # Analizza risultati sottomoduli
    if result.content:
        print("\n   📊 Risultati sottomoduli:")
        
        # Tokenizzazione
        if 'tokenization' in result.content:
            tok_result = result.content['tokenization']
            if tok_result.success and tok_result.output_data:
                tokens = tok_result.output_data.tokens
                print(f"      🔤 Tokenizzazione: {len(tokens)} tokens")
                print(f"         Tokens: {tokens[:10]}{'...' if len(tokens) > 10 else ''}")
                print(f"         Semantica preservata: {tok_result.output_data.semantic_preserved}")
                print(f"         OOV tokens: {len(tok_result.output_data.oov_tokens)}")
        
        # Segmentazione
        if 'segmentation' in result.content:
            seg_result = result.content['segmentation']
            if seg_result.success and seg_result.output_data:
                segments = seg_result.output_data
                print(f"      ✂️ Segmentazione: {len(segments)} segmenti")
                for i, seg in enumerate(segments):
                    print(f"         {i+1}. [{seg.segment_type}] {seg.content[:50]}{'...' if len(seg.content) > 50 else ''}")
        
        # Ambiguità
        if 'ambiguity' in result.content:
            amb_result = result.content['ambiguity']
            if amb_result.success and amb_result.output_data:
                amb_data = amb_result.output_data
                print(f"      🔍 Ambiguità: {len(amb_data.ambiguous_spans)} span ambigue")
                print(f"         Clarity score: {amb_data.overall_clarity:.3f}")
                if amb_data.suggestions:
                    print(f"         Suggerimenti: {amb_data.suggestions[:2]}")

def test_ng_parser_complex():
    """Test con prompt complesso"""
    print("\n📝 Test Case 2: Prompt complesso con ambiguità")
    
    parser = NGParser()
    
    complex_input = """
    Dato il contesto di machine learning, voglio che tu crei qualcosa per analizzare dati.
    Dovrebbe essere veloce e preciso. Usa quello che ritieni migliore.
    Non deve essere troppo complicato ma nemmeno troppo semplice.
    """
    
    message = NGMessage(
        source_module=NGModuleType.PARSER,
        stage=NGProcessingStage.PARSING,
        content=complex_input
    )
    
    result = parser.process(message)
    
    print(f"   Input: {complex_input[:100]}...")
    print(f"   Success: {result.metadata.get('overall_success', False)}")
    print(f"   Confidence: {result.confidence:.3f}")
    
    # Analizza ambiguità rilevate
    if result.content and 'ambiguity' in result.content:
        amb_result = result.content['ambiguity']
        if amb_result.success and amb_result.output_data:
            amb_data = amb_result.output_data
            print(f"\n   🔍 Ambiguità rilevate: {len(amb_data.ambiguous_spans)}")
            print(f"      Clarity score: {amb_data.overall_clarity:.3f}")
            
            if amb_data.suggestions:
                print("      💡 Suggerimenti:")
                for suggestion in amb_data.suggestions[:3]:
                    print(f"         - {suggestion}")

def test_ng_parser_technical():
    """Test con testo tecnico NEUROGLYPH"""
    print("\n📝 Test Case 3: Testo tecnico NEUROGLYPH")
    
    parser = NGParser()
    
    technical_input = """
    Implementa un symbolic validator che utilizzi i neuroglifi ⊢ e ≈ per 
    verificare la coerenza semantica del reasoning chain. Il tokenizer deve
    preservare l'atomicità dei simboli durante l'encoding.
    """
    
    message = NGMessage(
        source_module=NGModuleType.PARSER,
        stage=NGProcessingStage.PARSING,
        content=technical_input
    )
    
    result = parser.process(message)
    
    print(f"   Input: {technical_input}")
    print(f"   Success: {result.metadata.get('overall_success', False)}")
    print(f"   Confidence: {result.confidence:.3f}")
    
    # Verifica preservazione simboli
    if result.content and 'tokenization' in result.content:
        tok_result = result.content['tokenization']
        if tok_result.success and tok_result.output_data:
            tokens = tok_result.output_data.tokens
            symbols_found = [t for t in tokens if t in ['⊢', '≈', '→', '≡']]
            print(f"   🔤 Simboli NEUROGLYPH preservati: {symbols_found}")
            print(f"      Semantica preservata: {tok_result.output_data.semantic_preserved}")

def test_ng_parser_performance():
    """Test performance e metriche"""
    print("\n📊 Test Case 4: Performance e metriche")
    
    parser = NGParser()
    
    # Test multipli per raccogliere metriche
    test_cases = [
        "Semplice test",
        "Test più complesso con multiple frasi. Alcune potrebbero essere ambigue?",
        "Test tecnico con symbolic reasoning ⊢ e validation ≈ per NEUROGLYPH",
        "Test molto lungo " * 20 + "con ripetizioni per testare performance"
    ]
    
    total_time = 0
    total_confidence = 0
    
    for i, test_input in enumerate(test_cases):
        message = NGMessage(
            source_module=NGModuleType.PARSER,
            stage=NGProcessingStage.PARSING,
            content=test_input
        )
        
        result = parser.process(message)
        total_time += result.processing_time
        total_confidence += result.confidence
        
        print(f"   Test {i+1}: time={result.processing_time:.3f}s, confidence={result.confidence:.3f}")
    
    print(f"\n   📈 Metriche aggregate:")
    print(f"      Tempo medio: {total_time/len(test_cases):.3f}s")
    print(f"      Confidence media: {total_confidence/len(test_cases):.3f}")
    
    # Mostra metriche performance del parser
    metrics = parser.get_performance_metrics()
    print(f"      Success rate: {metrics.get('success_rate', 0):.3f}")
    print(f"      Operazioni totali: {metrics.get('total_operations', 0)}")

def test_ng_parser_summary():
    """Test summary delle capacità"""
    print("\n📋 Test Case 5: Summary capacità")
    
    parser = NGParser()
    summary = parser.get_parsing_summary()
    
    print(f"   Modulo: {summary['module_type']} {summary['version']}")
    print(f"   Sottomoduli attivi:")
    
    for name, info in summary['submodules'].items():
        print(f"      🔧 {name}: {'✅' if info['enabled'] else '❌'}")
        for capability in info['capabilities'][:2]:  # Prime 2 capacità
            print(f"         - {capability}")
    
    print(f"\n   🛠️ Gestione pitfall:")
    for module, pitfalls in summary['pitfall_handling'].items():
        print(f"      {module}: {len(pitfalls)} strategie")

def main():
    """Esegue tutti i test"""
    try:
        test_ng_parser_basic()
        test_ng_parser_complex()
        test_ng_parser_technical()
        test_ng_parser_performance()
        test_ng_parser_summary()
        
        print("\n✅ TUTTI I TEST COMPLETATI CON SUCCESSO!")
        print("🧠 NG_PARSER v3.0 ULTRA funziona correttamente")
        
    except Exception as e:
        print(f"\n❌ ERRORE NEI TEST: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
