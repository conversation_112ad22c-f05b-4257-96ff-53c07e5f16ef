#!/usr/bin/env python3
"""
Test Pipeline Stub NG-THINK v3.0 ULTRA
======================================

Test completo della pipeline end-to-end con tutti gli stub per verificare
che i contratti tra moduli funzionino correttamente.

Autore: NEUROGLYPH NG-THINK Team
Data: 2025-01-27
"""

import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), 'neuroglyph'))

from neuroglyph.ng_think.core.ng_stubs import NGThinkPipelineStub

def test_individual_stubs():
    """Test dei singoli stub moduli"""
    print("🧪 TEST SINGOLI STUB MODULI")
    print("=" * 60)
    
    pipeline = NGThinkPipelineStub()
    
    # Test NG_PARSER stub
    print("\n📝 Test NG_PARSER Stub")
    parsed = pipeline.parser.parse("Crea una funzione per ordinare")
    print(f"   Tokens: {parsed.tokens}")
    print(f"   Intents: {parsed.intents}")
    print(f"   Confidence: {parsed.confidence}")
    print(f"   Semantic preserved: {parsed.semantic_preserved}")
    
    # Test NG_CONTEXT_PRIORITIZER stub
    print("\n⚖️ Test NG_CONTEXT_PRIORITIZER Stub")
    priority = pipeline.prioritizer.prioritize(parsed)
    print(f"   Urgency: {priority.urgency}")
    print(f"   Risk: {priority.risk}")
    print(f"   Domain: {priority.domain}")
    print(f"   Confidence: {priority.confidence}")
    
    # Test NG_MEMORY stub
    print("\n🗄️ Test NG_MEMORY Stub")
    memory = pipeline.memory.retrieve(priority, parsed)
    print(f"   Examples: {len(memory.examples)}")
    print(f"   Symbols: {memory.symbols}")
    print(f"   Confidence: {memory.confidence}")
    print(f"   Retrieval time: {memory.retrieval_time}s")
    
    # Test NG_REASONER stub
    print("\n🧩 Test NG_REASONER Stub")
    graph = pipeline.reasoner.reason(memory, parsed)
    print(f"   Nodes: {len(graph.nodes)}")
    print(f"   Depth: {graph.depth}")
    print(f"   Confidence: {graph.confidence}")
    print(f"   Root nodes: {graph.root_nodes}")
    
    # Test NG_SELF_CHECK stub
    print("\n🔍 Test NG_SELF_CHECK Stub")
    check = pipeline.self_check.self_check(graph)
    print(f"   Score: {check.score}")
    print(f"   Passed: {check.passed}")
    print(f"   Issues: {len(check.issues)}")
    print(f"   Confidence: {check.confidence}")
    
    # Test NG_DECODER stub
    print("\n📤 Test NG_DECODER Stub")
    output = pipeline.decoder.decode(graph)
    print(f"   Text: {output.text[:50]}...")
    print(f"   Glyphs: {output.glyphs}")
    print(f"   Confidence: {output.confidence}")
    print(f"   Validation passed: {output.validation_passed}")

def test_end_to_end_pipeline():
    """Test pipeline completa end-to-end"""
    print("\n🚀 TEST PIPELINE END-TO-END")
    print("=" * 60)
    
    pipeline = NGThinkPipelineStub()
    
    test_cases = [
        "Crea una funzione per ordinare una lista",
        "Come posso implementare un algoritmo di ricerca?",
        "Spiega il concetto di ricorsione",
        "Genera codice Python per calcolare fibonacci",
        "Analizza questo errore: IndexError"
    ]
    
    for i, prompt in enumerate(test_cases, 1):
        print(f"\n📝 Test Case {i}: {prompt}")
        
        # Esegui pipeline completa
        result = pipeline.process_end_to_end(prompt)
        
        print(f"   ✅ Output: {result.text[:80]}...")
        print(f"   🎯 Confidence: {result.confidence:.3f}")
        print(f"   ⏱️ Time: {result.generation_time:.3f}s")
        print(f"   ✓ Validated: {result.validation_passed}")
        print(f"   🔗 Trace ID: {result.trace_id[:8]}...")
        
        # Verifica che tutti i campi siano popolati
        assert result.text is not None
        assert result.glyphs is not None
        assert result.trace_id is not None
        assert result.confidence > 0
        assert result.generation_time > 0
        
        print(f"   ✅ Tutti i contratti rispettati")

def test_pipeline_status():
    """Test stato pipeline"""
    print("\n📊 TEST STATO PIPELINE")
    print("=" * 60)
    
    pipeline = NGThinkPipelineStub()
    status = pipeline.get_pipeline_status()
    
    print(f"   Pipeline type: {status['pipeline_type']}")
    print(f"   Version: {status['version']}")
    print(f"   Ready: {status['ready']}")
    print(f"   Modules:")
    
    for module_name, module_class in status['modules'].items():
        print(f"      🔧 {module_name}: {module_class}")

def test_contract_validation():
    """Test validazione contratti tra moduli"""
    print("\n📋 TEST VALIDAZIONE CONTRATTI")
    print("=" * 60)
    
    pipeline = NGThinkPipelineStub()
    
    # Test che ogni output rispetti il contratto del modulo successivo
    prompt = "Test contract validation"
    
    # 1. Parser → Context Prioritizer
    parsed = pipeline.parser.parse(prompt)
    print(f"   ✅ ParsedPrompt: {type(parsed).__name__}")
    assert hasattr(parsed, 'tokens')
    assert hasattr(parsed, 'intents')
    assert hasattr(parsed, 'confidence')
    
    # 2. Context Prioritizer → Memory
    priority = pipeline.prioritizer.prioritize(parsed)
    print(f"   ✅ PriorityVector: {type(priority).__name__}")
    assert hasattr(priority, 'urgency')
    assert hasattr(priority, 'risk')
    assert hasattr(priority, 'domain')
    
    # 3. Memory → Reasoner
    memory = pipeline.memory.retrieve(priority, parsed)
    print(f"   ✅ MemoryContext: {type(memory).__name__}")
    assert hasattr(memory, 'examples')
    assert hasattr(memory, 'symbols')
    assert hasattr(memory, 'confidence')
    
    # 4. Reasoner → Self Check
    graph = pipeline.reasoner.reason(memory, parsed)
    print(f"   ✅ ReasoningGraph: {type(graph).__name__}")
    assert hasattr(graph, 'nodes')
    assert hasattr(graph, 'confidence')
    assert hasattr(graph, 'depth')
    
    # 5. Self Check → Decoder
    check = pipeline.self_check.self_check(graph)
    print(f"   ✅ CheckResult: {type(check).__name__}")
    assert hasattr(check, 'score')
    assert hasattr(check, 'passed')
    assert hasattr(check, 'confidence')
    
    # 6. Decoder → Final Output
    output = pipeline.decoder.decode(graph)
    print(f"   ✅ NGOutput: {type(output).__name__}")
    assert hasattr(output, 'text')
    assert hasattr(output, 'glyphs')
    assert hasattr(output, 'trace_id')
    
    print(f"   🎉 Tutti i contratti validati correttamente!")

def test_performance_baseline():
    """Test performance baseline degli stub"""
    print("\n⚡ TEST PERFORMANCE BASELINE")
    print("=" * 60)
    
    pipeline = NGThinkPipelineStub()
    
    # Test performance con diversi input
    test_inputs = [
        "short",
        "medium length input for testing",
        "very long input " * 20 + "to test performance with larger prompts"
    ]
    
    for i, prompt in enumerate(test_inputs, 1):
        print(f"\n   Test {i}: {len(prompt)} chars")
        
        # Misura tempo per 10 esecuzioni
        times = []
        for _ in range(10):
            result = pipeline.process_end_to_end(prompt)
            times.append(result.generation_time)
        
        avg_time = sum(times) / len(times)
        min_time = min(times)
        max_time = max(times)
        
        print(f"      Avg time: {avg_time:.4f}s")
        print(f"      Min time: {min_time:.4f}s") 
        print(f"      Max time: {max_time:.4f}s")
        
        # Gli stub dovrebbero essere molto veloci
        assert avg_time < 0.1, f"Stub troppo lento: {avg_time}s"

def main():
    """Esegue tutti i test della pipeline stub"""
    try:
        print("🧠 NG-THINK v3.0 ULTRA - PIPELINE STUB TESTS")
        print("🎯 Verifica contratti modulo-per-modulo")
        print("=" * 80)
        
        test_individual_stubs()
        test_end_to_end_pipeline()
        test_pipeline_status()
        test_contract_validation()
        test_performance_baseline()
        
        print("\n" + "=" * 80)
        print("✅ TUTTI I TEST STUB COMPLETATI CON SUCCESSO!")
        print("🏗️ Pipeline end-to-end funzionante con stub")
        print("📋 Contratti tra moduli validati")
        print("🚀 Pronto per sviluppo modulo-per-modulo")
        print("=" * 80)
        
    except Exception as e:
        print(f"\n❌ ERRORE NEI TEST: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
