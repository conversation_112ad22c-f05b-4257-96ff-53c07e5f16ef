{"neuroglyph": "⟨⟩α⟲ι⟦⟧◊ι≤ξ⤴ι⤴⟨⟩ι⊖ξ⊕⟨⟩ι⊖ξ", "code": "def fibonacci(n):\n    for i in range(n):\n        if i <= 1:\n            return i\n        return fibonacci(i-1) + fi<PERSON><PERSON><PERSON>(i-2)", "language": "python", "description": "Funzione ricorsiva per calcolo Fi<PERSON>ci", "complexity": "O(2^n)", "tags": ["recursion", "mathematics", "fibonacci"]}
{"neuroglyph": "⟪⟫α⟨⟩⟘ξ⟙⟨⟩β⤴α⊗β", "code": "class Calculator:\n    \"\"\"Simple calculator class\"\"\"\n    def multiply(self, a, b):\n        return a * b", "language": "python", "description": "Classe calcolatrice con metodo moltiplicazione", "complexity": "O(1)", "tags": ["class", "mathematics", "basic"]}
{"neuroglyph": "⟚μ⟛⟨⟩α⟦⟧⟲ι∈⟦⟧◊ι≡γ⤴δ⤴∅", "code": "import math\n\ndef find_element(arr):\n    for i in arr:\n        if i == target:\n            return found\n    return None", "language": "python", "description": "Ricerca elemento in array", "complexity": "O(n)", "tags": ["search", "array", "linear"]}
{"neuroglyph": "⟨⟩α⟦⟧⟲ι⟦⟧◊ι≥δ⟦⟧⊕ι", "code": "def filter_positive(numbers):\n    result = []\n    for num in numbers:\n        if num >= 0:\n            result.append(num)\n    return result", "language": "python", "description": "Filtra numeri positivi da lista", "complexity": "O(n)", "tags": ["filter", "array", "positive"]}
{"neuroglyph": "⟨⟩α𝕊◊α≡∅⤴ξ⤴α⟦ξ⟧⊕⟨⟩α⟦ξ⟧", "code": "def reverse_string(s):\n    if s == \"\":\n        return \"\"\n    return s[-1] + reverse_string(s[:-1])", "language": "python", "description": "Inversione stringa ricorsiva", "complexity": "O(n)", "tags": ["recursion", "string", "reverse"]}
{"neuroglyph": "⟪⟫α⟨⟩⟘ξ⟙⟨⟩β⟦⟧⟲ι∈β◊ι∈α⟦⟧⤵ι", "code": "class UniqueFilter:\n    \"\"\"Filter for unique elements\"\"\"\n    def __init__(self):\n        self.seen = set()\n    \n    def filter_unique(self, items):\n        for item in items:\n            if item not in self.seen:\n                self.seen.add(item)\n                yield item", "language": "python", "description": "Filtro per elementi unici con generator", "complexity": "O(n)", "tags": ["class", "generator", "unique", "filter"]}
{"neuroglyph": "⟨⟩α⟦⟧β⟦⟧⟲ι∈α⟲κ∈β◊ι≡κ⤴𝔹⤴𝔹", "code": "def arrays_equal(arr1, arr2):\n    if len(arr1) != len(arr2):\n        return False\n    for i in range(len(arr1)):\n        if arr1[i] != arr2[i]:\n            return False\n    return True", "language": "python", "description": "Confronto uguaglianza tra array", "complexity": "O(n)", "tags": ["comparison", "array", "equality"]}
{"neuroglyph": "⟨⟩α⟦⟧◊α≡⟦⟧⤴ξ◊α⟦ξ⟧≤α⟦ξ⟧⤴α⟦ξ⟧⊕⟨⟩α⟦ξ⟧", "code": "def quicksort(arr):\n    if len(arr) <= 1:\n        return arr\n    pivot = arr[0]\n    less = [x for x in arr[1:] if x <= pivot]\n    greater = [x for x in arr[1:] if x > pivot]\n    return quicksort(less) + [pivot] + quicksort(greater)", "language": "python", "description": "Algoritmo quicksort ricorsivo", "complexity": "O(n log n)", "tags": ["sorting", "recursion", "quicksort", "divide-conquer"]}
{"neuroglyph": "⟨⟩α𝕊⟪⟫⟲ι∈α◊ι∈⟪⟫⟪⟫⟦ι⟧⊕ξ◈⟪⟫⟦ι⟧←ξ⤴⟪⟫", "code": "def char_frequency(text):\n    freq = {}\n    for char in text:\n        if char in freq:\n            freq[char] += 1\n        else:\n            freq[char] = 1\n    return freq", "language": "python", "description": "Calcola frequenza caratteri in testo", "complexity": "O(n)", "tags": ["frequency", "dictionary", "string", "counting"]}
{"neuroglyph": "⟨⟩α⟦⟧β⟦⟧⟲ι∈α⟲κ∈β⟦⟧⊕⟦ι⊗κ⟧⤴⟦⟧", "code": "def matrix_multiply(A, B):\n    result = []\n    for i in range(len(A)):\n        row = []\n        for j in range(len(B[0])):\n            sum_val = 0\n            for k in range(len(B)):\n                sum_val += A[i][k] * B[k][j]\n            row.append(sum_val)\n        result.append(row)\n    return result", "language": "python", "description": "Moltiplicazione matrici", "complexity": "O(n^3)", "tags": ["matrix", "multiplication", "linear-algebra"]}
{"neuroglyph": "⟨⟩α⟦⟧◊α≡⟦⟧⤴ξ⟦⟧←α⟦ξ⟧⟲ι∈α⟦ξ⟧◊ι≤⟦⟧⟦⟧⊕ι◈⟦⟧⊕ι⤴⟦⟧", "code": "def merge_sort(arr):\n    if len(arr) <= 1:\n        return arr\n    \n    mid = len(arr) // 2\n    left = merge_sort(arr[:mid])\n    right = merge_sort(arr[mid:])\n    \n    merged = []\n    i = j = 0\n    \n    while i < len(left) and j < len(right):\n        if left[i] <= right[j]:\n            merged.append(left[i])\n            i += 1\n        else:\n            merged.append(right[j])\n            j += 1\n    \n    merged.extend(left[i:])\n    merged.extend(right[j:])\n    return merged", "language": "python", "description": "Algoritmo merge sort", "complexity": "O(n log n)", "tags": ["sorting", "recursion", "merge-sort", "divide-conquer"]}
{"neuroglyph": "⟨⟩α⟦⟧β⟦⟧⟲ι∈α◊ι∉β⟦⟧⊕ι⤴⟦⟧", "code": "def array_difference(arr1, arr2):\n    result = []\n    for item in arr1:\n        if item not in arr2:\n            result.append(item)\n    return result", "language": "python", "description": "Differenza tra array", "complexity": "O(n*m)", "tags": ["array", "difference", "set-operations"]}
{"neuroglyph": "⟨⟩α⟦⟧⟪⟫⟲ι∈α◊ι∈⟪⟫⟪⟫⟦ι⟧⊕ξ◈⟪⟫⟦ι⟧←ξ⤴⟪⟫", "code": "def group_by_length(words):\n    groups = {}\n    for word in words:\n        length = len(word)\n        if length in groups:\n            groups[length].append(word)\n        else:\n            groups[length] = [word]\n    return groups", "language": "python", "description": "Raggruppa parole per lunghezza", "complexity": "O(n)", "tags": ["grouping", "dictionary", "string", "length"]}
{"neuroglyph": "⟨⟩α⟦⟧⟦⟧⟲ι∈α⟦⟧⊕⟦ι⟧⤴⟦⟧", "code": "def flatten_array(nested_arr):\n    result = []\n    for item in nested_arr:\n        if isinstance(item, list):\n            result.extend(flatten_array(item))\n        else:\n            result.append(item)\n    return result", "language": "python", "description": "Appiattimento array annidato", "complexity": "O(n)", "tags": ["recursion", "array", "flatten", "nested"]}
{"neuroglyph": "⟨⟩α⟦⟧◊α≡⟦⟧⤴ξ◊α⟦ξ⟧≡ξ⤴ξ⤴⟨⟩α⟦ξ⟧⊕⟨⟩α⟦ξ⟧", "code": "def binary_search(arr, target, low=0, high=None):\n    if high is None:\n        high = len(arr) - 1\n    \n    if low > high:\n        return -1\n    \n    mid = (low + high) // 2\n    \n    if arr[mid] == target:\n        return mid\n    elif arr[mid] > target:\n        return binary_search(arr, target, low, mid - 1)\n    else:\n        return binary_search(arr, target, mid + 1, high)", "language": "python", "description": "Ricerca binaria ricorsiva", "complexity": "O(log n)", "tags": ["search", "binary-search", "recursion", "divide-conquer"]}
{"neuroglyph": "⟨⟩α𝕊◊α≡𝕊⤴𝔹⤴α≡α⟦⟧", "code": "def is_palindrome(s):\n    s = s.lower().replace(' ', '')\n    if len(s) <= 1:\n        return True\n    if s[0] != s[-1]:\n        return False\n    return is_palindrome(s[1:-1])", "language": "python", "description": "Verifica palindromo ricorsivo", "complexity": "O(n)", "tags": ["recursion", "string", "palindrome", "verification"]}
{"neuroglyph": "⟨⟩α⟦⟧⟦⟧⟲ι∈α⟦⟧⊕ι⤴⟦⟧", "code": "def get_unique_elements(arr):\n    seen = set()\n    result = []\n    for item in arr:\n        if item not in seen:\n            seen.add(item)\n            result.append(item)\n    return result", "language": "python", "description": "Estrae elementi unici mantenendo ordine", "complexity": "O(n)", "tags": ["unique", "array", "order-preserving", "set"]}
{"neuroglyph": "⟨⟩α⟦⟧⟦⟧⟲ι∈α⟦⟧⊕ι⤴⟦⟧", "code": "def prime_factors(n):\n    factors = []\n    d = 2\n    while d * d <= n:\n        while n % d == 0:\n            factors.append(d)\n            n //= d\n        d += 1\n    if n > 1:\n        factors.append(n)\n    return factors", "language": "python", "description": "Fattorizzazione in numeri primi", "complexity": "O(sqrt(n))", "tags": ["mathematics", "prime", "factorization", "number-theory"]}
{"neuroglyph": "⟨⟩α⟦⟧β⟦⟧⟦⟧⟲ι∈α⟲κ∈β⟦⟧⊕⟦ι⊗κ⟧⤴⟦⟧", "code": "def cartesian_product(list1, list2):\n    result = []\n    for item1 in list1:\n        for item2 in list2:\n            result.append((item1, item2))\n    return result", "language": "python", "description": "Prodotto cartesiano di due liste", "complexity": "O(n*m)", "tags": ["cartesian", "product", "combinations", "tuples"]}
{"neuroglyph": "⟨⟩α⟦⟧⟦⟧⟲ι∈α◊ι⊘ξ≡ξ⟦⟧⊕ι⤴⟦⟧", "code": "def find_even_numbers(numbers):\n    result = []\n    for num in numbers:\n        if num % 2 == 0:\n            result.append(num)\n    return result", "language": "python", "description": "Trova numeri pari in lista", "complexity": "O(n)", "tags": ["filter", "even", "numbers", "modulo"]}
{"neuroglyph": "function α(β) { ◊ β ≤ ξ ⤴ ξ; ⤴ α(β ⊖ ξ) ⊗ β; }", "code": "function factorial(n) {\n    if (n <= 1) {\n        return 1;\n    }\n    return factorial(n - 1) * n;\n}", "language": "javascript", "description": "Calcolo fattoriale ricorsivo", "complexity": "O(n)", "tags": ["recursion", "mathematics", "factorial"]}
{"neuroglyph": "function α(β, γ) { ⤴ β ⊕ γ; }", "code": "function add(a, b) {\n    return a + b;\n}", "language": "javascript", "description": "Funzione somma semplice", "complexity": "O(1)", "tags": ["basic", "arithmetic", "addition"]}
{"neuroglyph": "const α = β => β ⊗ β;", "code": "const square = x => x * x;", "language": "javascript", "description": "Funzione arrow per quadrato", "complexity": "O(1)", "tags": ["arrow-function", "square", "mathematics"]}
{"neuroglyph": "function α(β) { ⟦⟧ γ = ⟦⟧; ⟲ ι ∈ β { ◊ ι ⊘ ξ ≡ ξ { γ⊕ι; } } ⤴ γ; }", "code": "function filterEven(arr) {\n    let result = [];\n    for (let i of arr) {\n        if (i % 2 === 0) {\n            result.push(i);\n        }\n    }\n    return result;\n}", "language": "javascript", "description": "Filtra numeri pari", "complexity": "O(n)", "tags": ["filter", "even", "array", "loop"]}
{"neuroglyph": "class α { ⟨⟩(β) { this.γ = β; } ⟨⟩δ() { ⤴ this.γ; } }", "code": "class Container {\n    constructor(value) {\n        this.value = value;\n    }\n    \n    getValue() {\n        return this.value;\n    }\n}", "language": "javascript", "description": "Classe container semplice", "complexity": "O(1)", "tags": ["class", "constructor", "getter", "encapsulation"]}
{"neuroglyph": "fn α(β: ℕ) -> ℕ { ◊ β ≤ ξ { ⤴ ξ; } ⤴ α(β ⊖ ξ) ⊕ α(β ⊖ ξ); }", "code": "fn fibonacci(n: u32) -> u32 {\n    if n <= 1 {\n        return n;\n    }\n    return fibonacci(n - 1) + fibonacci(n - 2);\n}", "language": "rust", "description": "Fibonacci ricorsivo in Rust", "complexity": "O(2^n)", "tags": ["recursion", "fibonacci", "rust", "mathematics"]}
{"neuroglyph": "fn α(β: &⟦ℕ⟧) -> ℕ { β.iter().sum() }", "code": "fn sum_array(arr: &[i32]) -> i32 {\n    arr.iter().sum()\n}", "language": "rust", "description": "Somma elementi array", "complexity": "O(n)", "tags": ["sum", "array", "iterator", "rust"]}
{"neuroglyph": "struct α { β: ℕ, γ: 𝕊 } impl α { fn δ(&self) -> &𝕊 { &self.γ } }", "code": "struct Person {\n    age: u32,\n    name: String,\n}\n\nimpl Person {\n    fn get_name(&self) -> &String {\n        &self.name\n    }\n}", "language": "rust", "description": "Struct con metodo getter", "complexity": "O(1)", "tags": ["struct", "impl", "getter", "rust", "ownership"]}
{"neuroglyph": "func α(β ℕ) ℕ { ◊ β ≤ ξ { ⤴ ξ } ⤴ β ⊗ α(β⊖ξ) }", "code": "func factorial(n int) int {\n    if n <= 1 {\n        return 1\n    }\n    return n * factorial(n-1)\n}", "language": "go", "description": "Fattoriale ricorsivo in Go", "complexity": "O(n)", "tags": ["recursion", "factorial", "go", "mathematics"]}
{"neuroglyph": "func α(β ⟦⟧ℕ) ℕ { γ := ξ; ⟲ _, δ := range β { γ ⊕= δ } ⤴ γ }", "code": "func sumSlice(nums []int) int {\n    sum := 0\n    for _, num := range nums {\n        sum += num\n    }\n    return sum\n}", "language": "go", "description": "Somma slice in Go", "complexity": "O(n)", "tags": ["sum", "slice", "range", "go", "loop"]}
{"neuroglyph": "public class α { private ℕ β; public α(ℕ γ) { this.β = γ; } public ℕ δ() { ⤴ this.β; } }", "code": "public class Counter {\n    private int value;\n    \n    public Counter(int initialValue) {\n        this.value = initialValue;\n    }\n    \n    public int getValue() {\n        return this.value;\n    }\n}", "language": "java", "description": "Classe Counter in Java", "complexity": "O(1)", "tags": ["class", "constructor", "getter", "java", "encapsulation"]}
