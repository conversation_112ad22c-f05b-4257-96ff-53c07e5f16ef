{"text": "Crea una funzione che calcola il fattoriale di un numero", "neuroglyph": "⟨⟩α⟘Calcola fattoriale⟙◊α≤ξ⤴ξ⤴α⊗⟨⟩α⊖ξ", "language": "python", "intent": "function_creation", "complexity": "basic", "domain": "mathematics"}
{"text": "Implementa una classe per gestire una lista di studenti", "neuroglyph": "⟪⟫α⟘Gestione studenti⟙⟨⟩⟦⟧⟨⟩β⟦⟧⊕β⟨⟩β⟦⟧⊖β⟨⟩⤴⟦⟧", "language": "python", "intent": "class_creation", "complexity": "intermediate", "domain": "data_management"}
{"text": "Scrivi un algoritmo di ordinamento bubble sort", "neuroglyph": "⟨⟩α⟦⟧⟲ι⟦⟧⟲κ⟦⟧◊α⟦ι⟧≥α⟦κ⟧⟨⟩α⟦ι⟧α⟦κ⟧⤴α", "language": "python", "intent": "algorithm_implementation", "complexity": "intermediate", "domain": "sorting"}
{"text": "Crea una funzione ricorsiva per attraversare un albero binario", "neuroglyph": "⟨⟩α⟪⟫◊α≡∅⤴⟨⟩α⟦⟧⟨⟩α⟦⟧⟨⟩α⟦⟧", "language": "python", "intent": "recursive_function", "complexity": "advanced", "domain": "data_structures"}
{"text": "Implementa un sistema di cache con LRU", "neuroglyph": "⟪⟫α⟘LRU Cache⟙⟨⟩⟪⟫⟦⟧⟨⟩β⟪⟫◊β∈⟪⟫⤴⟪⟫⟦β⟧◈⟪⟫⟦β⟧←γ⟨⟩⟪⟫", "language": "python", "intent": "system_design", "complexity": "advanced", "domain": "caching"}
{"text": "Scrivi una funzione per validare un indirizzo email", "neuroglyph": "⟨⟩α𝕊⟚re⟛◊re⟦@⟧∧re⟦.⟧⤴𝔹⤴𝔹", "language": "python", "intent": "validation", "complexity": "basic", "domain": "string_processing"}
{"text": "Crea un parser per file JSON", "neuroglyph": "⟨⟩α𝕊⟚json⟛⚡⤴json⟦α⟧⚠⤴∅", "language": "python", "intent": "parsing", "complexity": "intermediate", "domain": "file_processing"}
{"text": "Implementa un algoritmo di ricerca binaria", "neuroglyph": "⟨⟩α⟦⟧β◊⟦⟧≡⟦⟧⤴⊖ξ⟦⟧←⟦⟧⊘ξ◊⟦⟧⟦⟧≡β⤴⟦⟧◊⟦⟧⟦⟧≥β⤴⟨⟩α⟦⟧β⟦⟧⟦⟧⊖ξ◈⤴⟨⟩α⟦⟧β⟦⟧⊕ξ⟦⟧", "language": "python", "intent": "search_algorithm", "complexity": "intermediate", "domain": "algorithms"}
{"text": "Crea una funzione per calcolare la distanza tra due punti", "neuroglyph": "⟨⟩α⟦⟧⟦⟧⟚math⟛⤴math⟦⟦⟦⟧⊖⟦⟧⟧⊛ξ⊕⟦⟦⟧⊖⟦⟧⟧⊛ξ⟧", "language": "python", "intent": "mathematical_calculation", "complexity": "basic", "domain": "geometry"}
{"text": "Implementa un generatore di numeri primi", "neuroglyph": "⟨⟩α⟘Genera primi⟙ι←ξ⟳𝔹⟲κ⟦ξι⟧◊ι⊘κ≡ξ⊗⤵ι←ι⊕ξ", "language": "python", "intent": "generator_function", "complexity": "intermediate", "domain": "mathematics"}
{"text": "Scrivi una classe per gestire connessioni database", "neuroglyph": "⟪⟫α⟘Database⟙⟨⟩𝕊⟨⟩⟨⟩⟨⟩⟨⟩", "language": "python", "intent": "database_management", "complexity": "advanced", "domain": "database"}
{"text": "Crea un decoratore per misurare il tempo di esecuzione", "neuroglyph": "⟨⟩α⟨⟩⟚time⟛β←time⟦⟧γ←⟨⟩δ←time⟦⟧⊖β⟦δ⊖β⟧⤴γ", "language": "python", "intent": "decorator", "complexity": "intermediate", "domain": "performance"}
{"text": "Implementa un sistema di logging personalizzato", "neuroglyph": "⟪⟫α⟘Logger⟙⟨⟩𝕊⟨⟩𝕊𝕊⟚datetime⟛⟦datetime⟦⟧𝕊𝕊⟧", "language": "python", "intent": "logging_system", "complexity": "intermediate", "domain": "utilities"}
{"text": "Scrivi una funzione per comprimere una stringa", "neuroglyph": "⟨⟩α𝕊⟦⟧⟲ι∈α◊ι≡⟦⟧⟦⟧⊕ξ◈⟦⟧⊕ι⟦⟧←ξ⤴𝕊⟦⟧", "language": "python", "intent": "compression", "complexity": "intermediate", "domain": "string_processing"}
{"text": "Crea un client HTTP asincrono", "neuroglyph": "⟪⟫α⟘HTTP Client⟙⟚aiohttp⟛⊰⟨⟩𝕊⊱⟦𝕊⟧⤴⟦⟧", "language": "python", "intent": "async_programming", "complexity": "advanced", "domain": "networking"}
{"text": "Implementa un algoritmo di hash consistente", "neuroglyph": "⟪⟫α⟘Consistent Hash⟙⟨⟩⟦⟧⟨⟩𝕊⟚hashlib⟛⤴hashlib⟦𝕊⟧⟦⟧", "language": "python", "intent": "hashing_algorithm", "complexity": "advanced", "domain": "distributed_systems"}
{"text": "Scrivi una funzione per validare password sicure", "neuroglyph": "⟨⟩α𝕊⟚re⟛◊⟦α⟧≥ξ∧re⟦⟧∧re⟦⟧∧re⟦⟧⤴𝔹⤴𝔹", "language": "python", "intent": "validation", "complexity": "basic", "domain": "security"}
{"text": "Crea un sistema di rate limiting", "neuroglyph": "⟪⟫α⟘Rate Limiter⟙⟨⟩ℕℕ⟪⟫⟨⟩𝕊⟚time⟛β←time⟦⟧◊β⊖⟪⟫⟦𝕊⟧≥ℕ⤴𝔹⟪⟫⟦𝕊⟧←β⤴𝔹", "language": "python", "intent": "rate_limiting", "complexity": "intermediate", "domain": "api_management"}
{"text": "Implementa un pool di connessioni", "neuroglyph": "⟪⟫α⟘Connection Pool⟙⟨⟩ℕ⟦⟧⟨⟩◊⟦⟧⤴⟦⟧⟦⟧⟨⟩⟦⟧⊕", "language": "python", "intent": "connection_pooling", "complexity": "advanced", "domain": "resource_management"}
{"text": "Scrivi un parser per espressioni matematiche", "neuroglyph": "⟪⟫α⟘Math Parser⟙⟨⟩𝕊⟦⟧⟨⟩𝕊⟲ι∈𝕊◊ι∈⟦⊕⊖⊗⊘⟧⟦⟧⊕ι⤴⟦⟧", "language": "python", "intent": "expression_parsing", "complexity": "advanced", "domain": "parsing"}
{"text": "Create a function to calculate factorial", "neuroglyph": "function α(β) { ◊ β ≤ ξ ⤴ ξ; ⤴ β ⊗ α(β ⊖ ξ); }", "language": "javascript", "intent": "function_creation", "complexity": "basic", "domain": "mathematics"}
{"text": "Implement a class for managing user sessions", "neuroglyph": "class α { ⟨⟩() { this.⟪⟫ = new Map(); } ⟨⟩β(γ, δ) { this.⟪⟫.set(γ, δ); } ⟨⟩ε(γ) { ⤴ this.⟪⟫.get(γ); } }", "language": "javascript", "intent": "session_management", "complexity": "intermediate", "domain": "web_development"}
{"text": "Write an async function to fetch data from API", "neuroglyph": "async function α(β) { ⚡ const γ = await fetch(β); ⤴ await γ.json(); ⚠ (δ) { console.error(δ); ⤴ null; } }", "language": "javascript", "intent": "async_programming", "complexity": "intermediate", "domain": "api_integration"}
{"text": "Create a debounce function for input handling", "neuroglyph": "function α(β, γ) { let δ; ⤴ function(...ε) { clearTimeout(δ); δ = setTimeout(() => β.apply(this, ε), γ); }; }", "language": "javascript", "intent": "utility_function", "complexity": "intermediate", "domain": "event_handling"}
{"text": "Implement a simple state machine", "neuroglyph": "class α { ⟨⟩(β) { this.γ = β; this.⟪⟫ = {}; } ⟨⟩δ(ε, ζ, η) { this.⟪⟫[ε] = { θ: ζ, ι: η }; } ⟨⟩κ(ε) { ◊ this.⟪⟫[ε] { this.γ = this.⟪⟫[ε].θ; this.⟪⟫[ε].ι(); } } }", "language": "javascript", "intent": "state_management", "complexity": "advanced", "domain": "design_patterns"}
{"text": "Write a function to deep clone an object", "neuroglyph": "function α(β) { ◊ β === null || typeof β !== 'object' ⤴ β; ◊ β instanceof Date ⤴ new Date(β.getTime()); ◊ β instanceof Array ⤴ β.map(α); const γ = {}; ⟲ const δ in β { γ[δ] = α(β[δ]); } ⤴ γ; }", "language": "javascript", "intent": "object_manipulation", "complexity": "intermediate", "domain": "data_structures"}
{"text": "Create a simple event emitter", "neuroglyph": "class α { ⟨⟩() { this.⟪⟫ = {}; } ⟨⟩β(γ, δ) { ◊ !this.⟪⟫[γ] this.⟪⟫[γ] = []; this.⟪⟫[γ].push(δ); } ⟨⟩ε(γ, ...ζ) { ◊ this.⟪⟫[γ] this.⟪⟫[γ].forEach(η => η(...ζ)); } }", "language": "javascript", "intent": "event_system", "complexity": "intermediate", "domain": "event_handling"}
{"text": "Implement a memoization decorator", "neuroglyph": "function α(β) { const γ = new Map(); ⤴ function(...δ) { const ε = JSON.stringify(δ); ◊ γ.has(ε) ⤴ γ.get(ε); const ζ = β.apply(this, δ); γ.set(ε, ζ); ⤴ ζ; }; }", "language": "javascript", "intent": "optimization", "complexity": "intermediate", "domain": "performance"}
{"text": "Write a function to validate email addresses", "neuroglyph": "function α(β) { const γ = /^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/; ⤴ γ.test(β); }", "language": "javascript", "intent": "validation", "complexity": "basic", "domain": "string_processing"}
{"text": "Create a simple router for SPA", "neuroglyph": "class α { ⟨⟩() { this.⟪⟫ = {}; window.addEventListener('popstate', this.β.bind(this)); } ⟨⟩γ(δ, ε) { this.⟪⟫[δ] = ε; } ⟨⟩ζ(δ) { history.pushState(null, null, δ); this.β(); } ⟨⟩β() { const η = window.location.pathname; ◊ this.⟪⟫[η] this.⟪⟫[η](); } }", "language": "javascript", "intent": "routing", "complexity": "advanced", "domain": "web_development"}
{"text": "Implement a struct for representing a point in 2D space", "neuroglyph": "struct α { β: ℝ, γ: ℝ } impl α { fn δ(β: ℝ, γ: ℝ) -> Self { Self { β, γ } } fn ε(&self, ζ: &α) -> ℝ { ((self.β - ζ.β).powi(2) + (self.γ - ζ.γ).powi(2)).sqrt() } }", "language": "rust", "intent": "data_structure", "complexity": "basic", "domain": "geometry"}
{"text": "Create a function to find the maximum element in a vector", "neuroglyph": "fn α(β: Vec<ℕ>) -> Option<ℕ> { β.into_iter().max() }", "language": "rust", "intent": "collection_operation", "complexity": "basic", "domain": "algorithms"}
{"text": "Implement a simple hash map", "neuroglyph": "use std::collections::HashMap; struct α<β, γ> { ⟪⟫: HashMap<β, γ> } impl<β, γ> α<β, γ> where β: Eq + std::hash::Hash { fn δ() -> Self { Self { ⟪⟫: HashMap::new() } } fn ε(&mut self, ζ: β, η: γ) { self.⟪⟫.insert(ζ, η); } fn θ(&self, ζ: &β) -> Option<&γ> { self.⟪⟫.get(ζ) } }", "language": "rust", "intent": "data_structure", "complexity": "intermediate", "domain": "collections"}
{"text": "Write a function to read a file asynchronously", "neuroglyph": "use tokio::fs; async fn α(β: &str) -> Result<String, std::io::Error> { fs::read_to_string(β).await }", "language": "rust", "intent": "file_io", "complexity": "intermediate", "domain": "async_programming"}
{"text": "Create a trait for serializable objects", "neuroglyph": "trait α { fn β(&self) -> String; fn γ(δ: &str) -> Result<Self, String> where Self: Sized; } impl α for ℕ { fn β(&self) -> String { self.to_string() } fn γ(δ: &str) -> Result<Self, String> { δ.parse().map_err(|ε| format!(\"Parse error: {}\", ε)) } }", "language": "rust", "intent": "trait_definition", "complexity": "intermediate", "domain": "serialization"}
{"text": "Implement a thread-safe counter", "neuroglyph": "use std::sync::{Arc, Mutex}; struct α { β: Arc<Mutex<ℕ>> } impl α { fn γ() -> Self { Self { β: Arc::new(Mutex::new(0)) } } fn δ(&self) { let mut ε = self.β.lock().unwrap(); *ε += 1; } fn ζ(&self) -> ℕ { *self.β.lock().unwrap() } }", "language": "rust", "intent": "concurrency", "complexity": "advanced", "domain": "thread_safety"}
{"text": "Write a function to merge two sorted vectors", "neuroglyph": "fn α(β: Vec<ℕ>, γ: Vec<ℕ>) -> Vec<ℕ> { let mut δ = Vec::new(); let (mut ε, mut ζ) = (0, 0); ⟳ ε < β.len() && ζ < γ.len() { ◊ β[ε] <= γ[ζ] { δ.push(β[ε]); ε += 1; } ◈ { δ.push(γ[ζ]); ζ += 1; } } δ.extend_from_slice(&β[ε..]); δ.extend_from_slice(&γ[ζ..]); δ }", "language": "rust", "intent": "algorithm_implementation", "complexity": "intermediate", "domain": "sorting"}
{"text": "Create a custom error type", "neuroglyph": "#[derive(Debug)] enum α { β(String), γ(ℕ), δ } impl std::fmt::Display for α { fn fmt(&self, ε: &mut std::fmt::Formatter) -> std::fmt::Result { match self { α::β(ζ) => write!(ε, \"String error: {}\", ζ), α::γ(η) => write!(ε, \"Number error: {}\", η), α::δ => write!(ε, \"Unknown error\") } } } impl std::error::Error for α {}", "language": "rust", "intent": "error_handling", "complexity": "intermediate", "domain": "error_management"}
{"text": "Implement a simple web server handler", "neuroglyph": "use warp::Filter; async fn α() { let β = warp::path!(\"hello\" / String).map(|γ: String| { format!(\"Hello, {}!\", γ) }); warp::serve(β).run(([127, 0, 0, 1], 3030)).await; }", "language": "rust", "intent": "web_server", "complexity": "advanced", "domain": "web_development"}
{"text": "Write a function to calculate Fibonacci numbers with memoization", "neuroglyph": "use std::collections::HashMap; fn α(β: ℕ, γ: &mut HashMap<ℕ, ℕ>) -> ℕ { ◊ let Some(&δ) = γ.get(&β) { ⤴ δ; } let ε = match β { 0 | 1 => β, _ => α(β - 1, γ) + α(β - 2, γ) }; γ.insert(β, ε); ε }", "language": "rust", "intent": "memoization", "complexity": "intermediate", "domain": "optimization"}
{"text": "Create a function to sum integers", "neuroglyph": "func α(β, γ ℕ) ℕ { ⤴ β ⊕ γ }", "language": "go", "intent": "basic_arithmetic", "complexity": "basic", "domain": "mathematics"}
{"text": "Implement a struct for a person with methods", "neuroglyph": "type α struct { β 𝕊; γ ℕ } func (δ *α) ε() 𝕊 { ⤴ δ.β } func (δ *α) ζ(η 𝕊) { δ.β = η }", "language": "go", "intent": "struct_definition", "complexity": "basic", "domain": "data_structures"}
{"text": "Write a function to read a file", "neuroglyph": "func α(β 𝕊) (𝕊, error) { γ, δ := ioutil.ReadFile(β); ◊ δ != nil { ⤴ \"\", δ } ⤴ string(γ), nil }", "language": "go", "intent": "file_operations", "complexity": "basic", "domain": "file_io"}
{"text": "Create a goroutine to process data concurrently", "neuroglyph": "func α(β chan ℕ, γ chan ℕ) { go func() { ⟲ δ := range β { γ <- δ * δ } close(γ) }() }", "language": "go", "intent": "concurrency", "complexity": "intermediate", "domain": "goroutines"}
{"text": "Implement a simple HTTP server", "neuroglyph": "func α() { http.HandleFunc(\"/\", func(β http.ResponseWriter, γ *http.Request) { fmt.Fprintf(β, \"Hello, World!\") }); log.Fatal(http.ListenAndServe(\":8080\", nil)) }", "language": "go", "intent": "web_server", "complexity": "intermediate", "domain": "web_development"}
{"text": "Write a function to sort a slice of integers", "neuroglyph": "func α(β []ℕ) { sort.Ints(β) }", "language": "go", "intent": "sorting", "complexity": "basic", "domain": "algorithms"}
{"text": "Create a worker pool pattern", "neuroglyph": "func α(β ℕ, γ <-chan ℕ, δ chan<- ℕ) { ⟲ ε := 0; ε < β; ε++ { go func() { ⟲ ζ := range γ { δ <- ζ * ζ } }() } }", "language": "go", "intent": "worker_pool", "complexity": "advanced", "domain": "concurrency"}
{"text": "Implement error handling with custom errors", "neuroglyph": "type α struct { β 𝕊 } func (γ α) Error() 𝕊 { ⤴ γ.β } func δ(ε 𝕊) error { ◊ ε == \"\" { ⤴ α{β: \"empty string\"} } ⤴ nil }", "language": "go", "intent": "error_handling", "complexity": "intermediate", "domain": "error_management"}
{"text": "Write a function to make HTTP requests", "neuroglyph": "func α(β 𝕊) (𝕊, error) { γ, δ := http.Get(β); ◊ δ != nil { ⤴ \"\", δ } defer γ.Body.Close(); ε, ζ := ioutil.ReadAll(γ.Body); ◊ ζ != nil { ⤴ \"\", ζ } ⤴ string(ε), nil }", "language": "go", "intent": "http_client", "complexity": "intermediate", "domain": "networking"}
{"text": "Create a simple cache with expiration", "neuroglyph": "type α struct { β map[𝕊]γ; μ sync.RWMutex } type γ struct { δ interface{}; ε time.Time } func (ζ *α) η(θ 𝕊, ι interface{}, κ time.Duration) { ζ.μ.Lock(); defer ζ.μ.Unlock(); ζ.β[θ] = γ{δ: ι, ε: time.Now().Add(κ)} }", "language": "go", "intent": "caching", "complexity": "advanced", "domain": "data_management"}
