{"components": {"SOCRATEPlanner": {"file": "docs/ultra/planner.py", "function": "Costruzione DAG di ragionamento simbolico", "features": ["<PERSON><PERSON><PERSON> simboli neuroglifi", "Identificazione pattern ragionamento", "Costruzione nodi e archi DAG", "Calcolo confidenza strutturale"]}, "SOCRATELogicSimulator": {"file": "docs/ultra/logic_simulator.py", "function": "Simulazione logica simbolica", "features": ["Applicazione regole logiche", "Rilevamento contraddizioni", "Validazione inferenze", "Calcolo confidenza logica"]}, "SOCRATEEngine": {"file": "docs/ultra/ultra_wrapper.py", "function": "Integrazione completa pipeline", "features": ["Orchestrazione componenti", "Inferenze simboliche avanzate", "<PERSON><PERSON>i pattern ricorrenti", "Meta-ragionamento"]}}, "capabilities": ["Ragionamento simbolico puro (non probabilistico)", "Costruzione grafi di ragionamento", "Validazione logica multi-step", "Rilevamento contraddizioni automatico", "Inferenze simboliche avanzate", "Pattern recognition cognitivo", "Meta-ragionamento ricorsivo", "Quantificazione incertezza logica"], "advantages": ["<PERSON> alluc<PERSON> (logica simbolica)", "Ragionamento trasparente e verificabile", "Efficienza superiore a LLM 50x più grandi", "Consistenza logica garantita", "Spiegabilità completa del ragionamento"]}