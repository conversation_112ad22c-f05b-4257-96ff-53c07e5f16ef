{"timestamp": "2025-05-24 08:19:13", "results": [{"test_case": "Fibonacci Recursivo", "description": "Algoritmo ricorsivo classico", "complexity": {"lines_of_code": 5, "characters": 108, "ast_nodes": 32, "reasoning_patterns": 17, "unique_reasoning_types": 17, "total_reasoning_instances": 65, "reasoning_density": 0.53125}, "compression": {"original_size": 108, "compressed_size": 69, "compression_ratio": 0.6388888888888888, "compression_percentage": 36.111111111111114, "semantic_density": 0.9420289855072463, "neuroglyphs": "⍸×15 ⫩×12 ⧓×6 ⊲×6 ⧮×5 ⧗×5 ⪪×3 ⎆×2 ⌾×2 ⩓×2 ⦗×1 ⎢×1 ⏓×1 ∟×1 ⩷×1 ⫳×1 ⇡×1", "reasoning_coverage": 1.0}, "consistency": {"original_patterns_count": 17, "neuroglyphs_patterns_count": 17, "common_patterns_count": 17, "total_unique_patterns": 17, "semantic_overlap": 1.0, "pattern_preservation": 1.0, "pattern_completeness": 1.0}, "scores": {"efficiency_score": 36.111111111111114, "semantic_score": 100.0, "final_score": 68.05555555555556}, "duration": 0.003461122512817383}, {"test_case": "Bubble Sort", "description": "Algoritmo di ordinamento con confronti", "complexity": {"lines_of_code": 7, "characters": 198, "ast_nodes": 92, "reasoning_patterns": 19, "unique_reasoning_types": 19, "total_reasoning_instances": 185, "reasoning_density": 0.20652173913043478}, "compression": {"original_size": 198, "compressed_size": 80, "compression_ratio": 0.40404040404040403, "compression_percentage": 59.5959595959596, "semantic_density": 2.3125, "neuroglyphs": "⍸×55 ⫩×35 ⊲×25 ⧓×18 ⧗×12 ⎆×7 ⧮×7 ⪜×6 ⪪×5 ⌾×3 ⩓×3 ⎚×2 ⦗×1 ⎢×1 ⏓×1 ∟×1 ⩷×1 ⫳×1 ⇡×1", "reasoning_coverage": 1.0}, "consistency": {"original_patterns_count": 19, "neuroglyphs_patterns_count": 19, "common_patterns_count": 19, "total_unique_patterns": 19, "semantic_overlap": 1.0, "pattern_preservation": 1.0, "pattern_completeness": 1.0}, "scores": {"efficiency_score": 59.5959595959596, "semantic_score": 100.0, "final_score": 79.79797979797979}, "duration": 0.0006458759307861328}, {"test_case": "Binary Search", "description": "Ricerca binaria con logica condizionale", "complexity": {"lines_of_code": 11, "characters": 295, "ast_nodes": 87, "reasoning_patterns": 22, "unique_reasoning_types": 22, "total_reasoning_instances": 177, "reasoning_density": 0.25287356321839083}, "compression": {"original_size": 295, "compressed_size": 93, "compression_ratio": 0.3152542372881356, "compression_percentage": 68.47457627118644, "semantic_density": 1.903225806451613, "neuroglyphs": "⍸×48 ⫩×30 ⊲×24 ⧓×14 ⧗×13 ⧮×11 ⎆×8 ⪪×6 ⩷×4 ⫳×3 ⏓×2 ∟×2 ⇡×2 ⪜×2 ⦗×1 ⎢×1 ⏟×1 ∏×1 ⏃×1 ⌾×1 ⩓×1 ⪣×1", "reasoning_coverage": 1.0}, "consistency": {"original_patterns_count": 22, "neuroglyphs_patterns_count": 22, "common_patterns_count": 22, "total_unique_patterns": 22, "semantic_overlap": 1.0, "pattern_preservation": 1.0, "pattern_completeness": 1.0}, "scores": {"efficiency_score": 68.47457627118644, "semantic_score": 100.0, "final_score": 84.23728813559322}, "duration": 0.0006089210510253906}, {"test_case": "Class Inheritance", "description": "Ereditarietà e polimorfismo", "complexity": {"lines_of_code": 14, "characters": 273, "ast_nodes": 45, "reasoning_patterns": 11, "unique_reasoning_types": 11, "total_reasoning_instances": 90, "reasoning_density": 0.24444444444444444}, "compression": {"original_size": 273, "compressed_size": 46, "compression_ratio": 0.1684981684981685, "compression_percentage": 83.15018315018314, "semantic_density": 1.9565217391304348, "neuroglyphs": "⫩×26 ⍸×17 ⧓×15 ⦗×7 ⊲×7 ⎢×4 ≵×3 ⎆×3 ⧗×3 ⪜×3 ⪪×2", "reasoning_coverage": 1.0}, "consistency": {"original_patterns_count": 11, "neuroglyphs_patterns_count": 11, "common_patterns_count": 11, "total_unique_patterns": 11, "semantic_overlap": 1.0, "pattern_preservation": 1.0, "pattern_completeness": 1.0}, "scores": {"efficiency_score": 83.15018315018314, "semantic_score": 100.0, "final_score": 91.57509157509156}, "duration": 0.0006389617919921875}, {"test_case": "Exception Handling", "description": "Gestione errori e controllo flusso", "complexity": {"lines_of_code": 12, "characters": 315, "ast_nodes": 43, "reasoning_patterns": 15, "unique_reasoning_types": 15, "total_reasoning_instances": 86, "reasoning_density": 0.3488372093023256}, "compression": {"original_size": 315, "compressed_size": 62, "compression_ratio": 0.19682539682539682, "compression_percentage": 80.31746031746032, "semantic_density": 1.3870967741935485, "neuroglyphs": "⍸×23 ⫩×16 ⊲×11 ⧓×8 ⧗×5 ⪪×5 ⎆×4 ⌾×3 ⩓×3 ⎁×2 ⧯×2 ⦗×1 ⎢×1 ⏟×1 ⧮×1", "reasoning_coverage": 1.0}, "consistency": {"original_patterns_count": 15, "neuroglyphs_patterns_count": 15, "common_patterns_count": 15, "total_unique_patterns": 15, "semantic_overlap": 1.0, "pattern_preservation": 1.0, "pattern_completeness": 1.0}, "scores": {"efficiency_score": 80.31746031746032, "semantic_score": 100.0, "final_score": 90.15873015873015}, "duration": 0.0007069110870361328}], "aggregate_stats": {"total_tests": 5, "successful_tests": 5, "failed_tests": 0, "average_efficiency_score": 65.52985808918012, "average_semantic_score": 100.0, "average_final_score": 82.76492904459005, "total_duration": 0.006456851959228516}}