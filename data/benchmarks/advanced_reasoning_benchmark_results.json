{"timestamp": "2025-05-24 08:39:50", "benchmark_type": "advanced", "results": [{"test_case": "Fibonacci Recursivo", "description": "Algoritmo ricorsivo classico", "complexity": {"lines_of_code": 5, "characters": 108, "ast_nodes": 32, "reasoning_patterns": 17, "unique_reasoning_types": 17, "total_reasoning_instances": 65, "reasoning_density": 0.53125}, "compression": {"original_size": 108, "compressed_size": 69, "compression_ratio": 0.6388888888888888, "compression_percentage": 36.111111111111114, "semantic_density": 0.9420289855072463, "neuroglyphs": "⍸×15 ⫩×12 ⧓×6 ⊲×6 ⧮×5 ⧗×5 ⪪×3 ⎆×2 ⌾×2 ⩓×2 ⦗×1 ⎢×1 ⏓×1 ∟×1 ⩷×1 ⫳×1 ⇡×1", "reasoning_coverage": 1.0}, "consistency": {"original_patterns_count": 17, "neuroglyphs_patterns_count": 17, "common_patterns_count": 17, "total_unique_patterns": 17, "semantic_overlap": 1.0, "pattern_preservation": 1.0, "pattern_completeness": 1.0}, "scores": {"efficiency_score": 36.111111111111114, "semantic_score": 100.0, "final_score": 68.05555555555556, "advanced_final_score": 55.70987654320987}, "duration": 0.001252889633178711, "advanced_metrics": {"token_costs": {"gpt4": {"original": {"estimated_tokens": 27, "char_count": 108, "unicode_symbols": 0, "python_keywords": 4, "model": "gpt4"}, "compressed": {"estimated_tokens": 28, "char_count": 69, "unicode_symbols": 34, "python_keywords": 0, "model": "gpt4"}, "reduction_percentage": -3.703703703703698}, "qwen": {"original": {"estimated_tokens": 29, "char_count": 108, "unicode_symbols": 0, "python_keywords": 4, "model": "qwen"}, "compressed": {"estimated_tokens": 29, "char_count": 69, "unicode_symbols": 34, "python_keywords": 0, "model": "qwen"}, "reduction_percentage": 0.0}, "llama": {"original": {"estimated_tokens": 28, "char_count": 108, "unicode_symbols": 0, "python_keywords": 4, "model": "llama"}, "compressed": {"estimated_tokens": 28, "char_count": 69, "unicode_symbols": 34, "python_keywords": 0, "model": "llama"}, "reduction_percentage": 0.0}, "deepseek": {"original": {"estimated_tokens": 28, "char_count": 108, "unicode_symbols": 0, "python_keywords": 4, "model": "deepseek"}, "compressed": {"estimated_tokens": 28, "char_count": 69, "unicode_symbols": 34, "python_keywords": 0, "model": "deepseek"}, "reduction_percentage": 0.0}}, "round_trip_fidelity": {"preservation_rate": 1.0, "hallucination_rate": 0.0, "fidelity_score": 1.0, "preserved_patterns": ["ng:reasoning:abstraction", "ng:reasoning:conclusion", "ng:reasoning:synthesis", "ng:reasoning:inference", "ng:reasoning:assessment", "ng:reasoning:planning", "ng:reasoning:difference", "ng:reasoning:deduction", "ng:reasoning:pattern", "ng:reasoning:evaluation", "ng:reasoning:judgment", "ng:reasoning:similarity", "ng:reasoning:analysis", "ng:reasoning:premise", "ng:reasoning:axiom", "ng:reasoning:hypothesis", "ng:reasoning:decision"], "lost_patterns": [], "hallucinated_patterns": [], "original_pattern_count": 17, "reconstructed_pattern_count": 17}, "category_metrics": {"category": "basic"}, "test_category": "basic"}}, {"test_case": "Bubble Sort", "description": "Algoritmo di ordinamento con confronti", "complexity": {"lines_of_code": 7, "characters": 198, "ast_nodes": 92, "reasoning_patterns": 19, "unique_reasoning_types": 19, "total_reasoning_instances": 185, "reasoning_density": 0.20652173913043478}, "compression": {"original_size": 198, "compressed_size": 80, "compression_ratio": 0.40404040404040403, "compression_percentage": 59.5959595959596, "semantic_density": 2.3125, "neuroglyphs": "⍸×55 ⫩×35 ⊲×25 ⧓×18 ⧗×12 ⎆×7 ⧮×7 ⪜×6 ⪪×5 ⌾×3 ⩓×3 ⎚×2 ⦗×1 ⎢×1 ⏓×1 ∟×1 ⩷×1 ⫳×1 ⇡×1", "reasoning_coverage": 1.0}, "consistency": {"original_patterns_count": 19, "neuroglyphs_patterns_count": 19, "common_patterns_count": 19, "total_unique_patterns": 19, "semantic_overlap": 1.0, "pattern_preservation": 1.0, "pattern_completeness": 1.0}, "scores": {"efficiency_score": 59.5959595959596, "semantic_score": 100.0, "final_score": 79.79797979797979, "advanced_final_score": 72.46750171972148}, "duration": 0.0011301040649414062, "advanced_metrics": {"token_costs": {"gpt4": {"original": {"estimated_tokens": 51, "char_count": 198, "unicode_symbols": 0, "python_keywords": 5, "model": "gpt4"}, "compressed": {"estimated_tokens": 32, "char_count": 80, "unicode_symbols": 38, "python_keywords": 0, "model": "gpt4"}, "reduction_percentage": 37.254901960784316}, "qwen": {"original": {"estimated_tokens": 54, "char_count": 198, "unicode_symbols": 0, "python_keywords": 5, "model": "qwen"}, "compressed": {"estimated_tokens": 33, "char_count": 80, "unicode_symbols": 38, "python_keywords": 0, "model": "qwen"}, "reduction_percentage": 38.888888888888886}, "llama": {"original": {"estimated_tokens": 52, "char_count": 198, "unicode_symbols": 0, "python_keywords": 5, "model": "llama"}, "compressed": {"estimated_tokens": 33, "char_count": 80, "unicode_symbols": 38, "python_keywords": 0, "model": "llama"}, "reduction_percentage": 36.53846153846154}, "deepseek": {"original": {"estimated_tokens": 53, "char_count": 198, "unicode_symbols": 0, "python_keywords": 5, "model": "deepseek"}, "compressed": {"estimated_tokens": 33, "char_count": 80, "unicode_symbols": 38, "python_keywords": 0, "model": "deepseek"}, "reduction_percentage": 37.735849056603776}}, "round_trip_fidelity": {"preservation_rate": 1.0, "hallucination_rate": 0.0, "fidelity_score": 1.0, "preserved_patterns": ["ng:reasoning:abstraction", "ng:reasoning:assessment", "ng:reasoning:difference", "ng:reasoning:pattern", "ng:reasoning:evaluation", "ng:reasoning:premise", "ng:reasoning:axiom", "ng:reasoning:deduction", "ng:reasoning:synthesis", "ng:reasoning:conclusion", "ng:reasoning:induction", "ng:reasoning:inference", "ng:reasoning:planning", "ng:reasoning:analysis", "ng:reasoning:judgment", "ng:reasoning:specialization", "ng:reasoning:similarity", "ng:reasoning:hypothesis", "ng:reasoning:decision"], "lost_patterns": [], "hallucinated_patterns": [], "original_pattern_count": 19, "reconstructed_pattern_count": 19}, "category_metrics": {"category": "basic"}, "test_category": "basic"}}, {"test_case": "Binary Search", "description": "Ricerca binaria con logica condizionale", "complexity": {"lines_of_code": 11, "characters": 295, "ast_nodes": 87, "reasoning_patterns": 22, "unique_reasoning_types": 22, "total_reasoning_instances": 177, "reasoning_density": 0.25287356321839083}, "compression": {"original_size": 295, "compressed_size": 93, "compression_ratio": 0.3152542372881356, "compression_percentage": 68.47457627118644, "semantic_density": 1.903225806451613, "neuroglyphs": "⍸×48 ⫩×30 ⊲×24 ⧓×14 ⧗×13 ⧮×11 ⎆×8 ⪪×6 ⩷×4 ⫳×3 ⏓×2 ∟×2 ⇡×2 ⪜×2 ⦗×1 ⎢×1 ⏟×1 ∏×1 ⏃×1 ⌾×1 ⩓×1 ⪣×1", "reasoning_coverage": 1.0}, "consistency": {"original_patterns_count": 22, "neuroglyphs_patterns_count": 22, "common_patterns_count": 22, "total_unique_patterns": 22, "semantic_overlap": 1.0, "pattern_preservation": 1.0, "pattern_completeness": 1.0}, "scores": {"efficiency_score": 68.47457627118644, "semantic_score": 100.0, "final_score": 84.23728813559322, "advanced_final_score": 78.76240251029782}, "duration": 0.0011980533599853516, "advanced_metrics": {"token_costs": {"gpt4": {"original": {"estimated_tokens": 76, "char_count": 295, "unicode_symbols": 0, "python_keywords": 5, "model": "gpt4"}, "compressed": {"estimated_tokens": 37, "char_count": 93, "unicode_symbols": 44, "python_keywords": 0, "model": "gpt4"}, "reduction_percentage": 51.315789473684205}, "qwen": {"original": {"estimated_tokens": 81, "char_count": 295, "unicode_symbols": 0, "python_keywords": 5, "model": "qwen"}, "compressed": {"estimated_tokens": 38, "char_count": 93, "unicode_symbols": 44, "python_keywords": 0, "model": "qwen"}, "reduction_percentage": 53.086419753086425}, "llama": {"original": {"estimated_tokens": 79, "char_count": 295, "unicode_symbols": 0, "python_keywords": 5, "model": "llama"}, "compressed": {"estimated_tokens": 38, "char_count": 93, "unicode_symbols": 44, "python_keywords": 0, "model": "llama"}, "reduction_percentage": 51.89873417721519}, "deepseek": {"original": {"estimated_tokens": 79, "char_count": 295, "unicode_symbols": 0, "python_keywords": 5, "model": "deepseek"}, "compressed": {"estimated_tokens": 38, "char_count": 93, "unicode_symbols": 44, "python_keywords": 0, "model": "deepseek"}, "reduction_percentage": 51.89873417721519}}, "round_trip_fidelity": {"preservation_rate": 1.0, "hallucination_rate": 0.0, "fidelity_score": 1.0, "preserved_patterns": ["ng:reasoning:contrapositive", "ng:reasoning:abstraction", "ng:reasoning:assessment", "ng:reasoning:difference", "ng:reasoning:pattern", "ng:reasoning:evaluation", "ng:reasoning:premise", "ng:reasoning:axiom", "ng:reasoning:monitoring", "ng:reasoning:deduction", "ng:reasoning:synthesis", "ng:reasoning:conclusion", "ng:reasoning:consistency", "ng:reasoning:inference", "ng:reasoning:planning", "ng:reasoning:decision", "ng:reasoning:control", "ng:reasoning:similarity", "ng:reasoning:judgment", "ng:reasoning:specialization", "ng:reasoning:hypothesis", "ng:reasoning:analysis"], "lost_patterns": [], "hallucinated_patterns": [], "original_pattern_count": 22, "reconstructed_pattern_count": 22}, "category_metrics": {"category": "basic"}, "test_category": "basic"}}, {"test_case": "Class Inheritance", "description": "Ereditarietà e polimorfismo", "complexity": {"lines_of_code": 14, "characters": 273, "ast_nodes": 45, "reasoning_patterns": 11, "unique_reasoning_types": 11, "total_reasoning_instances": 90, "reasoning_density": 0.24444444444444444}, "compression": {"original_size": 273, "compressed_size": 46, "compression_ratio": 0.1684981684981685, "compression_percentage": 83.15018315018314, "semantic_density": 1.9565217391304348, "neuroglyphs": "⫩×26 ⍸×17 ⧓×15 ⦗×7 ⊲×7 ⎢×4 ≵×3 ⎆×3 ⧗×3 ⪜×3 ⪪×2", "reasoning_coverage": 1.0}, "consistency": {"original_patterns_count": 11, "neuroglyphs_patterns_count": 11, "common_patterns_count": 11, "total_unique_patterns": 11, "semantic_overlap": 1.0, "pattern_preservation": 1.0, "pattern_completeness": 1.0}, "scores": {"efficiency_score": 83.15018315018314, "semantic_score": 100.0, "final_score": 91.57509157509156, "advanced_final_score": 88.54117656343685}, "duration": 0.0010230541229248047, "advanced_metrics": {"token_costs": {"gpt4": {"original": {"estimated_tokens": 70, "char_count": 273, "unicode_symbols": 0, "python_keywords": 9, "model": "gpt4"}, "compressed": {"estimated_tokens": 18, "char_count": 46, "unicode_symbols": 22, "python_keywords": 0, "model": "gpt4"}, "reduction_percentage": 74.28571428571429}, "qwen": {"original": {"estimated_tokens": 74, "char_count": 273, "unicode_symbols": 0, "python_keywords": 9, "model": "qwen"}, "compressed": {"estimated_tokens": 19, "char_count": 46, "unicode_symbols": 22, "python_keywords": 0, "model": "qwen"}, "reduction_percentage": 74.32432432432432}, "llama": {"original": {"estimated_tokens": 72, "char_count": 273, "unicode_symbols": 0, "python_keywords": 9, "model": "llama"}, "compressed": {"estimated_tokens": 19, "char_count": 46, "unicode_symbols": 22, "python_keywords": 0, "model": "llama"}, "reduction_percentage": 73.61111111111111}, "deepseek": {"original": {"estimated_tokens": 73, "char_count": 273, "unicode_symbols": 0, "python_keywords": 9, "model": "deepseek"}, "compressed": {"estimated_tokens": 19, "char_count": 46, "unicode_symbols": 22, "python_keywords": 0, "model": "deepseek"}, "reduction_percentage": 73.97260273972603}}, "round_trip_fidelity": {"preservation_rate": 1.0, "hallucination_rate": 0.0, "fidelity_score": 1.0, "preserved_patterns": ["ng:reasoning:conclusion", "ng:reasoning:abstraction", "ng:reasoning:classification", "ng:reasoning:synthesis", "ng:reasoning:planning", "ng:reasoning:pattern", "ng:reasoning:specialization", "ng:reasoning:premise", "ng:reasoning:axiom", "ng:reasoning:hypothesis", "ng:reasoning:analysis"], "lost_patterns": [], "hallucinated_patterns": [], "original_pattern_count": 11, "reconstructed_pattern_count": 11}, "category_metrics": {"category": "basic"}, "test_category": "basic"}}, {"test_case": "Exception Handling", "description": "Gestione errori e controllo flusso", "complexity": {"lines_of_code": 12, "characters": 315, "ast_nodes": 43, "reasoning_patterns": 15, "unique_reasoning_types": 15, "total_reasoning_instances": 86, "reasoning_density": 0.3488372093023256}, "compression": {"original_size": 315, "compressed_size": 62, "compression_ratio": 0.19682539682539682, "compression_percentage": 80.31746031746032, "semantic_density": 1.3870967741935485, "neuroglyphs": "⍸×23 ⫩×16 ⊲×11 ⧓×8 ⧗×5 ⪪×5 ⎆×4 ⌾×3 ⩓×3 ⎁×2 ⧯×2 ⦗×1 ⎢×1 ⏟×1 ⧮×1", "reasoning_coverage": 1.0}, "consistency": {"original_patterns_count": 15, "neuroglyphs_patterns_count": 15, "common_patterns_count": 15, "total_unique_patterns": 15, "semantic_overlap": 1.0, "pattern_preservation": 1.0, "pattern_completeness": 1.0}, "scores": {"efficiency_score": 80.31746031746032, "semantic_score": 100.0, "final_score": 90.15873015873015, "advanced_final_score": 86.72840734579582}, "duration": 0.0009047985076904297, "advanced_metrics": {"token_costs": {"gpt4": {"original": {"estimated_tokens": 82, "char_count": 315, "unicode_symbols": 0, "python_keywords": 5, "model": "gpt4"}, "compressed": {"estimated_tokens": 25, "char_count": 62, "unicode_symbols": 30, "python_keywords": 0, "model": "gpt4"}, "reduction_percentage": 69.51219512195121}, "qwen": {"original": {"estimated_tokens": 86, "char_count": 315, "unicode_symbols": 0, "python_keywords": 5, "model": "qwen"}, "compressed": {"estimated_tokens": 26, "char_count": 62, "unicode_symbols": 30, "python_keywords": 0, "model": "qwen"}, "reduction_percentage": 69.76744186046511}, "llama": {"original": {"estimated_tokens": 84, "char_count": 315, "unicode_symbols": 0, "python_keywords": 5, "model": "llama"}, "compressed": {"estimated_tokens": 25, "char_count": 62, "unicode_symbols": 30, "python_keywords": 0, "model": "llama"}, "reduction_percentage": 70.23809523809523}, "deepseek": {"original": {"estimated_tokens": 85, "char_count": 315, "unicode_symbols": 0, "python_keywords": 5, "model": "deepseek"}, "compressed": {"estimated_tokens": 25, "char_count": 62, "unicode_symbols": 30, "python_keywords": 0, "model": "deepseek"}, "reduction_percentage": 70.58823529411764}}, "round_trip_fidelity": {"preservation_rate": 1.0, "hallucination_rate": 0.0, "fidelity_score": 1.0, "preserved_patterns": ["ng:reasoning:abstraction", "ng:reasoning:conclusion", "ng:reasoning:synthesis", "ng:reasoning:inference", "ng:reasoning:premise", "ng:reasoning:fallacy", "ng:reasoning:planning", "ng:reasoning:deduction", "ng:reasoning:pattern", "ng:reasoning:evaluation", "ng:reasoning:error_correction", "ng:reasoning:axiom", "ng:reasoning:monitoring", "ng:reasoning:hypothesis", "ng:reasoning:analysis"], "lost_patterns": [], "hallucinated_patterns": [], "original_pattern_count": 15, "reconstructed_pattern_count": 15}, "category_metrics": {"category": "basic"}, "test_category": "basic"}}, {"test_case": "Max List Element", "description": "<PERSON><PERSON><PERSON> massimo in lista - HumanEval style", "complexity": {"lines_of_code": 8, "characters": 174, "ast_nodes": 39, "reasoning_patterns": 19, "unique_reasoning_types": 19, "total_reasoning_instances": 79, "reasoning_density": 0.48717948717948717}, "compression": {"original_size": 174, "compressed_size": 78, "compression_ratio": 0.4482758620689655, "compression_percentage": 55.172413793103445, "semantic_density": 1.0128205128205128, "neuroglyphs": "⍸×22 ⊲×12 ⫩×11 ⧓×5 ⎆×5 ⧗×5 ⧮×3 ⏓×2 ∟×2 ⏃×2 ⪪×2 ⦗×1 ⎢×1 ⎚×1 ⪜×1 ▪×1 ⩷×1 ⫳×1 ⇡×1", "reasoning_coverage": 1.0}, "consistency": {"original_patterns_count": 19, "neuroglyphs_patterns_count": 19, "common_patterns_count": 19, "total_unique_patterns": 19, "semantic_overlap": 1.0, "pattern_preservation": 1.0, "pattern_completeness": 1.0}, "scores": {"efficiency_score": 55.172413793103445, "semantic_score": 100.0, "final_score": 77.58620689655172, "advanced_final_score": 69.38956174717283}, "duration": 0.0019690990447998047, "advanced_metrics": {"token_costs": {"gpt4": {"original": {"estimated_tokens": 44, "char_count": 174, "unicode_symbols": 0, "python_keywords": 6, "model": "gpt4"}, "compressed": {"estimated_tokens": 31, "char_count": 78, "unicode_symbols": 38, "python_keywords": 0, "model": "gpt4"}, "reduction_percentage": 29.54545454545454}, "qwen": {"original": {"estimated_tokens": 47, "char_count": 174, "unicode_symbols": 0, "python_keywords": 6, "model": "qwen"}, "compressed": {"estimated_tokens": 32, "char_count": 78, "unicode_symbols": 38, "python_keywords": 0, "model": "qwen"}, "reduction_percentage": 31.914893617021278}, "llama": {"original": {"estimated_tokens": 46, "char_count": 174, "unicode_symbols": 0, "python_keywords": 6, "model": "llama"}, "compressed": {"estimated_tokens": 32, "char_count": 78, "unicode_symbols": 38, "python_keywords": 0, "model": "llama"}, "reduction_percentage": 30.434782608695656}, "deepseek": {"original": {"estimated_tokens": 46, "char_count": 174, "unicode_symbols": 0, "python_keywords": 6, "model": "deepseek"}, "compressed": {"estimated_tokens": 32, "char_count": 78, "unicode_symbols": 38, "python_keywords": 0, "model": "deepseek"}, "reduction_percentage": 30.434782608695656}}, "round_trip_fidelity": {"preservation_rate": 1.0, "hallucination_rate": 0.0, "fidelity_score": 1.0, "preserved_patterns": ["ng:reasoning:contrapositive", "ng:reasoning:abstraction", "ng:reasoning:assessment", "ng:reasoning:difference", "ng:reasoning:pattern", "ng:reasoning:evaluation", "ng:reasoning:premise", "ng:reasoning:axiom", "ng:reasoning:synthesis", "ng:reasoning:conclusion", "ng:reasoning:induction", "ng:reasoning:contradiction", "ng:reasoning:planning", "ng:reasoning:analysis", "ng:reasoning:judgment", "ng:reasoning:specialization", "ng:reasoning:similarity", "ng:reasoning:hypothesis", "ng:reasoning:decision"], "lost_patterns": [], "hallucinated_patterns": [], "original_pattern_count": 19, "reconstructed_pattern_count": 19}, "category_metrics": {"category": "algorithms", "loop_count": 1, "condition_count": 2, "complexity_indicator": 3}, "test_category": "algorithms"}}, {"test_case": "Is Palindrome", "description": "Verifica palindromo con preprocessing", "complexity": {"lines_of_code": 3, "characters": 82, "ast_nodes": 30, "reasoning_patterns": 17, "unique_reasoning_types": 17, "total_reasoning_instances": 61, "reasoning_density": 0.5666666666666667}, "compression": {"original_size": 82, "compressed_size": 69, "compression_ratio": 0.8414634146341463, "compression_percentage": 15.85365853658537, "semantic_density": 0.8840579710144928, "neuroglyphs": "⍸×14 ⫩×13 ⧓×6 ⊲×5 ⪜×3 ⪪×3 ⎆×2 ⧗×2 ⌾×2 ⩓×2 ⩷×2 ⧮×2 ⦗×1 ⎢×1 ⫳×1 ⪣×1 ⏃×1", "reasoning_coverage": 1.0}, "consistency": {"original_patterns_count": 17, "neuroglyphs_patterns_count": 17, "common_patterns_count": 17, "total_unique_patterns": 17, "semantic_overlap": 1.0, "pattern_preservation": 1.0, "pattern_completeness": 1.0}, "scores": {"efficiency_score": 15.85365853658537, "semantic_score": 100.0, "final_score": 57.926829268292686, "advanced_final_score": 42.162478442966254}, "duration": 0.0006101131439208984, "advanced_metrics": {"token_costs": {"gpt4": {"original": {"estimated_tokens": 21, "char_count": 82, "unicode_symbols": 0, "python_keywords": 2, "model": "gpt4"}, "compressed": {"estimated_tokens": 28, "char_count": 69, "unicode_symbols": 34, "python_keywords": 0, "model": "gpt4"}, "reduction_percentage": -33.33333333333333}, "qwen": {"original": {"estimated_tokens": 22, "char_count": 82, "unicode_symbols": 0, "python_keywords": 2, "model": "qwen"}, "compressed": {"estimated_tokens": 29, "char_count": 69, "unicode_symbols": 34, "python_keywords": 0, "model": "qwen"}, "reduction_percentage": -31.818181818181813}, "llama": {"original": {"estimated_tokens": 21, "char_count": 82, "unicode_symbols": 0, "python_keywords": 2, "model": "llama"}, "compressed": {"estimated_tokens": 28, "char_count": 69, "unicode_symbols": 34, "python_keywords": 0, "model": "llama"}, "reduction_percentage": -33.33333333333333}, "deepseek": {"original": {"estimated_tokens": 22, "char_count": 82, "unicode_symbols": 0, "python_keywords": 2, "model": "deepseek"}, "compressed": {"estimated_tokens": 28, "char_count": 69, "unicode_symbols": 34, "python_keywords": 0, "model": "deepseek"}, "reduction_percentage": -27.27272727272727}}, "round_trip_fidelity": {"preservation_rate": 1.0, "hallucination_rate": 0.0, "fidelity_score": 1.0, "preserved_patterns": ["ng:reasoning:contrapositive", "ng:reasoning:axiom", "ng:reasoning:conclusion", "ng:reasoning:abstraction", "ng:reasoning:inference", "ng:reasoning:premise", "ng:reasoning:planning", "ng:reasoning:difference", "ng:reasoning:deduction", "ng:reasoning:pattern", "ng:reasoning:evaluation", "ng:reasoning:similarity", "ng:reasoning:specialization", "ng:reasoning:consistency", "ng:reasoning:synthesis", "ng:reasoning:hypothesis", "ng:reasoning:analysis"], "lost_patterns": [], "hallucinated_patterns": [], "original_pattern_count": 17, "reconstructed_pattern_count": 17}, "category_metrics": {"category": "string_processing"}, "test_category": "string_processing"}}, {"test_case": "Count <PERSON><PERSON>", "description": "Conta primi con Sieve of Eratosthenes", "complexity": {"lines_of_code": 10, "characters": 264, "ast_nodes": 89, "reasoning_patterns": 19, "unique_reasoning_types": 19, "total_reasoning_instances": 179, "reasoning_density": 0.21348314606741572}, "compression": {"original_size": 264, "compressed_size": 81, "compression_ratio": 0.3068181818181818, "compression_percentage": 69.31818181818181, "semantic_density": 2.2098765432098766, "neuroglyphs": "⍸×54 ⫩×28 ⊲×24 ⧓×14 ⧗×12 ⪪×10 ⎆×8 ⧮×6 ⪜×4 ⌾×4 ⩓×4 ⏓×2 ∟×2 ⎚×2 ⦗×1 ⎢×1 ⩷×1 ⫳×1 ⇡×1", "reasoning_coverage": 1.0}, "consistency": {"original_patterns_count": 19, "neuroglyphs_patterns_count": 19, "common_patterns_count": 19, "total_unique_patterns": 19, "semantic_overlap": 1.0, "pattern_preservation": 1.0, "pattern_completeness": 1.0}, "scores": {"efficiency_score": 69.31818181818181, "semantic_score": 100.0, "final_score": 84.6590909090909, "advanced_final_score": 79.34353969927706}, "duration": 0.0020918846130371094, "advanced_metrics": {"token_costs": {"gpt4": {"original": {"estimated_tokens": 68, "char_count": 264, "unicode_symbols": 0, "python_keywords": 7, "model": "gpt4"}, "compressed": {"estimated_tokens": 32, "char_count": 81, "unicode_symbols": 38, "python_keywords": 0, "model": "gpt4"}, "reduction_percentage": 52.94117647058824}, "qwen": {"original": {"estimated_tokens": 72, "char_count": 264, "unicode_symbols": 0, "python_keywords": 7, "model": "qwen"}, "compressed": {"estimated_tokens": 33, "char_count": 81, "unicode_symbols": 38, "python_keywords": 0, "model": "qwen"}, "reduction_percentage": 54.16666666666667}, "llama": {"original": {"estimated_tokens": 70, "char_count": 264, "unicode_symbols": 0, "python_keywords": 7, "model": "llama"}, "compressed": {"estimated_tokens": 33, "char_count": 81, "unicode_symbols": 38, "python_keywords": 0, "model": "llama"}, "reduction_percentage": 52.85714285714286}, "deepseek": {"original": {"estimated_tokens": 71, "char_count": 264, "unicode_symbols": 0, "python_keywords": 7, "model": "deepseek"}, "compressed": {"estimated_tokens": 33, "char_count": 81, "unicode_symbols": 38, "python_keywords": 0, "model": "deepseek"}, "reduction_percentage": 53.52112676056338}}, "round_trip_fidelity": {"preservation_rate": 1.0, "hallucination_rate": 0.0, "fidelity_score": 1.0, "preserved_patterns": ["ng:reasoning:abstraction", "ng:reasoning:assessment", "ng:reasoning:difference", "ng:reasoning:pattern", "ng:reasoning:evaluation", "ng:reasoning:premise", "ng:reasoning:axiom", "ng:reasoning:deduction", "ng:reasoning:synthesis", "ng:reasoning:conclusion", "ng:reasoning:induction", "ng:reasoning:inference", "ng:reasoning:planning", "ng:reasoning:analysis", "ng:reasoning:judgment", "ng:reasoning:specialization", "ng:reasoning:similarity", "ng:reasoning:hypothesis", "ng:reasoning:decision"], "lost_patterns": [], "hallucinated_patterns": [], "original_pattern_count": 19, "reconstructed_pattern_count": 19}, "category_metrics": {"category": "algorithms", "loop_count": 2, "condition_count": 2, "complexity_indicator": 4}, "test_category": "algorithms"}}, {"test_case": "Binary Tree Traversal", "description": "Attraversamento inorder di albero binario", "complexity": {"lines_of_code": 15, "characters": 367, "ast_nodes": 81, "reasoning_patterns": 15, "unique_reasoning_types": 15, "total_reasoning_instances": 162, "reasoning_density": 0.18518518518518517}, "compression": {"original_size": 367, "compressed_size": 63, "compression_ratio": 0.17166212534059946, "compression_percentage": 82.83378746594006, "semantic_density": 2.5714285714285716, "neuroglyphs": "⍸×45 ⫩×43 ⊲×21 ⧓×15 ⪜×7 ⎆×5 ⧗×5 ⦗×4 ⌾×4 ⩓×4 ⎢×3 ⪪×3 ≵×1 ⏓×1 ∟×1", "reasoning_coverage": 1.0}, "consistency": {"original_patterns_count": 15, "neuroglyphs_patterns_count": 15, "common_patterns_count": 15, "total_unique_patterns": 15, "semantic_overlap": 1.0, "pattern_preservation": 1.0, "pattern_completeness": 1.0}, "scores": {"efficiency_score": 82.83378746594006, "semantic_score": 100.0, "final_score": 91.41689373297004, "advanced_final_score": 88.37987891255598}, "duration": 0.001251220703125, "advanced_metrics": {"token_costs": {"gpt4": {"original": {"estimated_tokens": 95, "char_count": 367, "unicode_symbols": 0, "python_keywords": 6, "model": "gpt4"}, "compressed": {"estimated_tokens": 25, "char_count": 63, "unicode_symbols": 30, "python_keywords": 0, "model": "gpt4"}, "reduction_percentage": 73.6842105263158}, "qwen": {"original": {"estimated_tokens": 100, "char_count": 367, "unicode_symbols": 0, "python_keywords": 6, "model": "qwen"}, "compressed": {"estimated_tokens": 26, "char_count": 63, "unicode_symbols": 30, "python_keywords": 0, "model": "qwen"}, "reduction_percentage": 74.0}, "llama": {"original": {"estimated_tokens": 98, "char_count": 367, "unicode_symbols": 0, "python_keywords": 6, "model": "llama"}, "compressed": {"estimated_tokens": 26, "char_count": 63, "unicode_symbols": 30, "python_keywords": 0, "model": "llama"}, "reduction_percentage": 73.46938775510203}, "deepseek": {"original": {"estimated_tokens": 99, "char_count": 367, "unicode_symbols": 0, "python_keywords": 6, "model": "deepseek"}, "compressed": {"estimated_tokens": 26, "char_count": 63, "unicode_symbols": 30, "python_keywords": 0, "model": "deepseek"}, "reduction_percentage": 73.73737373737373}}, "round_trip_fidelity": {"preservation_rate": 1.0, "hallucination_rate": 0.0, "fidelity_score": 1.0, "preserved_patterns": ["ng:reasoning:abstraction", "ng:reasoning:conclusion", "ng:reasoning:classification", "ng:reasoning:synthesis", "ng:reasoning:inference", "ng:reasoning:planning", "ng:reasoning:deduction", "ng:reasoning:pattern", "ng:reasoning:analysis", "ng:reasoning:judgment", "ng:reasoning:specialization", "ng:reasoning:premise", "ng:reasoning:axiom", "ng:reasoning:hypothesis", "ng:reasoning:decision"], "lost_patterns": [], "hallucinated_patterns": [], "original_pattern_count": 15, "reconstructed_pattern_count": 15}, "category_metrics": {"category": "data_structures", "class_count": 1, "method_count": 3, "structure_complexity": 5}, "test_category": "data_structures"}}, {"test_case": "Functional Map Reduce", "description": "Pattern map-reduce funzionale", "complexity": {"lines_of_code": 14, "characters": 400, "ast_nodes": 111, "reasoning_patterns": 13, "unique_reasoning_types": 13, "total_reasoning_instances": 222, "reasoning_density": 0.11711711711711711}, "compression": {"original_size": 400, "compressed_size": 55, "compression_ratio": 0.1375, "compression_percentage": 86.25, "semantic_density": 4.036363636363636, "neuroglyphs": "⍸×69 ⫩×54 ⊲×31 ⧓×26 ⧗×9 ⪪×7 ⌾×6 ⩓×6 ⎆×5 ⦗×3 ⪜×3 ⧮×2 ⎢×1", "reasoning_coverage": 1.0}, "consistency": {"original_patterns_count": 13, "neuroglyphs_patterns_count": 13, "common_patterns_count": 13, "total_unique_patterns": 13, "semantic_overlap": 1.0, "pattern_preservation": 1.0, "pattern_completeness": 1.0}, "scores": {"efficiency_score": 86.25, "semantic_score": 100.0, "final_score": 93.125, "advanced_final_score": 90.86791965101933}, "duration": 0.0014700889587402344, "advanced_metrics": {"token_costs": {"gpt4": {"original": {"estimated_tokens": 104, "char_count": 400, "unicode_symbols": 0, "python_keywords": 4, "model": "gpt4"}, "compressed": {"estimated_tokens": 22, "char_count": 55, "unicode_symbols": 26, "python_keywords": 0, "model": "gpt4"}, "reduction_percentage": 78.84615384615384}, "qwen": {"original": {"estimated_tokens": 110, "char_count": 400, "unicode_symbols": 0, "python_keywords": 4, "model": "qwen"}, "compressed": {"estimated_tokens": 22, "char_count": 55, "unicode_symbols": 26, "python_keywords": 0, "model": "qwen"}, "reduction_percentage": 80.0}, "llama": {"original": {"estimated_tokens": 107, "char_count": 400, "unicode_symbols": 0, "python_keywords": 4, "model": "llama"}, "compressed": {"estimated_tokens": 22, "char_count": 55, "unicode_symbols": 26, "python_keywords": 0, "model": "llama"}, "reduction_percentage": 79.43925233644859}, "deepseek": {"original": {"estimated_tokens": 108, "char_count": 400, "unicode_symbols": 0, "python_keywords": 4, "model": "deepseek"}, "compressed": {"estimated_tokens": 22, "char_count": 55, "unicode_symbols": 26, "python_keywords": 0, "model": "deepseek"}, "reduction_percentage": 79.62962962962963}}, "round_trip_fidelity": {"preservation_rate": 1.0, "hallucination_rate": 0.0, "fidelity_score": 1.0, "preserved_patterns": ["ng:reasoning:abstraction", "ng:reasoning:conclusion", "ng:reasoning:synthesis", "ng:reasoning:inference", "ng:reasoning:planning", "ng:reasoning:deduction", "ng:reasoning:pattern", "ng:reasoning:evaluation", "ng:reasoning:specialization", "ng:reasoning:premise", "ng:reasoning:axiom", "ng:reasoning:hypothesis", "ng:reasoning:analysis"], "lost_patterns": [], "hallucinated_patterns": [], "original_pattern_count": 13, "reconstructed_pattern_count": 13}, "category_metrics": {"category": "functional", "lambda_count": 2, "map_reduce_count": 3, "functional_complexity": 5}, "test_category": "functional"}}, {"test_case": "Async Web Scraper", "description": "Web scraping asincrono", "complexity": {"lines_of_code": 16, "characters": 466, "ast_nodes": 89, "reasoning_patterns": 16, "unique_reasoning_types": 16, "total_reasoning_instances": 178, "reasoning_density": 0.1797752808988764}, "compression": {"original_size": 466, "compressed_size": 67, "compression_ratio": 0.14377682403433475, "compression_percentage": 85.62231759656652, "semantic_density": 2.656716417910448, "neuroglyphs": "⫩×46 ⍸×44 ⧓×24 ⊲×23 ⎆×8 ⧗×8 ⪜×5 ⌾×5 ⩓×5 ⦗×2 ⎢×2 ≊×2 ⏟×1 ⎁×1 ⧯×1 ⪪×1", "reasoning_coverage": 1.0}, "consistency": {"original_patterns_count": 16, "neuroglyphs_patterns_count": 16, "common_patterns_count": 16, "total_unique_patterns": 16, "semantic_overlap": 1.0, "pattern_preservation": 1.0, "pattern_completeness": 1.0}, "scores": {"efficiency_score": 85.62231759656652, "semantic_score": 100.0, "final_score": 92.81115879828326, "advanced_final_score": 90.29242899695674}, "duration": 0.0014448165893554688, "advanced_metrics": {"token_costs": {"gpt4": {"original": {"estimated_tokens": 121, "char_count": 466, "unicode_symbols": 0, "python_keywords": 9, "model": "gpt4"}, "compressed": {"estimated_tokens": 27, "char_count": 67, "unicode_symbols": 32, "python_keywords": 0, "model": "gpt4"}, "reduction_percentage": 77.68595041322314}, "qwen": {"original": {"estimated_tokens": 127, "char_count": 466, "unicode_symbols": 0, "python_keywords": 9, "model": "qwen"}, "compressed": {"estimated_tokens": 28, "char_count": 67, "unicode_symbols": 32, "python_keywords": 0, "model": "qwen"}, "reduction_percentage": 77.95275590551181}, "llama": {"original": {"estimated_tokens": 124, "char_count": 466, "unicode_symbols": 0, "python_keywords": 9, "model": "llama"}, "compressed": {"estimated_tokens": 27, "char_count": 67, "unicode_symbols": 32, "python_keywords": 0, "model": "llama"}, "reduction_percentage": 78.2258064516129}, "deepseek": {"original": {"estimated_tokens": 125, "char_count": 466, "unicode_symbols": 0, "python_keywords": 9, "model": "deepseek"}, "compressed": {"estimated_tokens": 27, "char_count": 67, "unicode_symbols": 32, "python_keywords": 0, "model": "deepseek"}, "reduction_percentage": 78.4}}, "round_trip_fidelity": {"preservation_rate": 1.0, "hallucination_rate": 0.0, "fidelity_score": 1.0, "preserved_patterns": ["ng:reasoning:abstraction", "ng:reasoning:conclusion", "ng:reasoning:synthesis", "ng:reasoning:generalization", "ng:reasoning:inference", "ng:reasoning:premise", "ng:reasoning:fallacy", "ng:reasoning:planning", "ng:reasoning:deduction", "ng:reasoning:pattern", "ng:reasoning:specialization", "ng:reasoning:error_correction", "ng:reasoning:axiom", "ng:reasoning:monitoring", "ng:reasoning:hypothesis", "ng:reasoning:analysis"], "lost_patterns": [], "hallucinated_patterns": [], "original_pattern_count": 16, "reconstructed_pattern_count": 16}, "category_metrics": {"category": "async", "async_functions": 2, "await_calls": 2, "async_complexity": 4}, "test_category": "async"}}, {"test_case": "Simple Neural Network", "description": "Rete neurale semplice", "complexity": {"lines_of_code": 15, "characters": 465, "ast_nodes": 124, "reasoning_patterns": 15, "unique_reasoning_types": 15, "total_reasoning_instances": 248, "reasoning_density": 0.12096774193548387}, "compression": {"original_size": 465, "compressed_size": 64, "compression_ratio": 0.13763440860215054, "compression_percentage": 86.23655913978494, "semantic_density": 3.875, "neuroglyphs": "⫩×73 ⍸×67 ⊲×28 ⪜×19 ⧓×17 ⧗×9 ⎆×7 ⌾×7 ⩓×7 ⦗×4 ⎢×3 ⧮×3 ⪪×2 ≵×1 ⏃×1", "reasoning_coverage": 1.0}, "consistency": {"original_patterns_count": 15, "neuroglyphs_patterns_count": 15, "common_patterns_count": 15, "total_unique_patterns": 15, "semantic_overlap": 1.0, "pattern_preservation": 1.0, "pattern_completeness": 1.0}, "scores": {"efficiency_score": 86.23655913978494, "semantic_score": 100.0, "final_score": 93.11827956989248, "advanced_final_score": 90.79764819786756}, "duration": 0.0017578601837158203, "advanced_metrics": {"token_costs": {"gpt4": {"original": {"estimated_tokens": 121, "char_count": 465, "unicode_symbols": 0, "python_keywords": 7, "model": "gpt4"}, "compressed": {"estimated_tokens": 25, "char_count": 64, "unicode_symbols": 30, "python_keywords": 0, "model": "gpt4"}, "reduction_percentage": 79.33884297520662}, "qwen": {"original": {"estimated_tokens": 127, "char_count": 465, "unicode_symbols": 0, "python_keywords": 7, "model": "qwen"}, "compressed": {"estimated_tokens": 26, "char_count": 64, "unicode_symbols": 30, "python_keywords": 0, "model": "qwen"}, "reduction_percentage": 79.52755905511812}, "llama": {"original": {"estimated_tokens": 124, "char_count": 465, "unicode_symbols": 0, "python_keywords": 7, "model": "llama"}, "compressed": {"estimated_tokens": 26, "char_count": 64, "unicode_symbols": 30, "python_keywords": 0, "model": "llama"}, "reduction_percentage": 79.03225806451613}, "deepseek": {"original": {"estimated_tokens": 125, "char_count": 465, "unicode_symbols": 0, "python_keywords": 7, "model": "deepseek"}, "compressed": {"estimated_tokens": 26, "char_count": 64, "unicode_symbols": 30, "python_keywords": 0, "model": "deepseek"}, "reduction_percentage": 79.2}}, "round_trip_fidelity": {"preservation_rate": 1.0, "hallucination_rate": 0.0, "fidelity_score": 1.0, "preserved_patterns": ["ng:reasoning:contrapositive", "ng:reasoning:conclusion", "ng:reasoning:abstraction", "ng:reasoning:classification", "ng:reasoning:synthesis", "ng:reasoning:inference", "ng:reasoning:planning", "ng:reasoning:deduction", "ng:reasoning:pattern", "ng:reasoning:evaluation", "ng:reasoning:specialization", "ng:reasoning:premise", "ng:reasoning:axiom", "ng:reasoning:hypothesis", "ng:reasoning:analysis"], "lost_patterns": [], "hallucinated_patterns": [], "original_pattern_count": 15, "reconstructed_pattern_count": 15}, "category_metrics": {"category": "ml"}, "test_category": "ml"}}, {"test_case": "Observer Pattern", "description": "Pattern Observer per notifiche", "complexity": {"lines_of_code": 23, "characters": 524, "ast_nodes": 90, "reasoning_patterns": 14, "unique_reasoning_types": 14, "total_reasoning_instances": 180, "reasoning_density": 0.15555555555555556}, "compression": {"original_size": 524, "compressed_size": 59, "compression_ratio": 0.11259541984732824, "compression_percentage": 88.74045801526718, "semantic_density": 3.0508474576271185, "neuroglyphs": "⫩×54 ⍸×42 ⧓×26 ⊲×18 ⪜×9 ⦗×7 ⎢×5 ⎆×4 ⧗×4 ≵×3 ⌾×3 ⩓×3 ⎚×1 ⪪×1", "reasoning_coverage": 1.0}, "consistency": {"original_patterns_count": 14, "neuroglyphs_patterns_count": 14, "common_patterns_count": 14, "total_unique_patterns": 14, "semantic_overlap": 1.0, "pattern_preservation": 1.0, "pattern_completeness": 1.0}, "scores": {"efficiency_score": 88.74045801526718, "semantic_score": 100.0, "final_score": 94.37022900763358, "advanced_final_score": 92.46848340023611}, "duration": 0.0013051033020019531, "advanced_metrics": {"token_costs": {"gpt4": {"original": {"estimated_tokens": 136, "char_count": 524, "unicode_symbols": 0, "python_keywords": 10, "model": "gpt4"}, "compressed": {"estimated_tokens": 23, "char_count": 59, "unicode_symbols": 28, "python_keywords": 0, "model": "gpt4"}, "reduction_percentage": 83.08823529411764}, "qwen": {"original": {"estimated_tokens": 143, "char_count": 524, "unicode_symbols": 0, "python_keywords": 10, "model": "qwen"}, "compressed": {"estimated_tokens": 24, "char_count": 59, "unicode_symbols": 28, "python_keywords": 0, "model": "qwen"}, "reduction_percentage": 83.21678321678321}, "llama": {"original": {"estimated_tokens": 140, "char_count": 524, "unicode_symbols": 0, "python_keywords": 10, "model": "llama"}, "compressed": {"estimated_tokens": 24, "char_count": 59, "unicode_symbols": 28, "python_keywords": 0, "model": "llama"}, "reduction_percentage": 82.85714285714285}, "deepseek": {"original": {"estimated_tokens": 141, "char_count": 524, "unicode_symbols": 0, "python_keywords": 10, "model": "deepseek"}, "compressed": {"estimated_tokens": 24, "char_count": 59, "unicode_symbols": 28, "python_keywords": 0, "model": "deepseek"}, "reduction_percentage": 82.97872340425532}}, "round_trip_fidelity": {"preservation_rate": 1.0, "hallucination_rate": 0.0, "fidelity_score": 1.0, "preserved_patterns": ["ng:reasoning:conclusion", "ng:reasoning:abstraction", "ng:reasoning:classification", "ng:reasoning:synthesis", "ng:reasoning:inference", "ng:reasoning:planning", "ng:reasoning:induction", "ng:reasoning:deduction", "ng:reasoning:pattern", "ng:reasoning:specialization", "ng:reasoning:premise", "ng:reasoning:axiom", "ng:reasoning:hypothesis", "ng:reasoning:analysis"], "lost_patterns": [], "hallucinated_patterns": [], "original_pattern_count": 14, "reconstructed_pattern_count": 14}, "category_metrics": {"category": "design_patterns"}, "test_category": "design_patterns"}}], "aggregate_stats": {"total_tests": 13, "successful_tests": 13, "failed_tests": 0, "average_base_score": 84.52602564658962, "average_advanced_score": 78.91625413311644, "average_fidelity_score": 100.0, "token_reductions_by_model": {"gpt4": 51.57396829816591, "qwen": 52.69442703612954, "llama": 51.94375705093929, "deepseek": 52.67879462580409}, "category_performance": {"basic": {"count": 5, "avg_score": 76.44187293649237}, "algorithms": {"count": 2, "avg_score": 74.36655072322495}, "string_processing": {"count": 1, "avg_score": 42.162478442966254}, "data_structures": {"count": 1, "avg_score": 88.37987891255598}, "functional": {"count": 1, "avg_score": 90.86791965101933}, "async": {"count": 1, "avg_score": 90.29242899695674}, "ml": {"count": 1, "avg_score": 90.79764819786756}, "design_patterns": {"count": 1, "avg_score": 92.46848340023611}}, "total_duration": 0.01914072036743164}}