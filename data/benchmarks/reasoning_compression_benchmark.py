#!/usr/bin/env python3
"""
🧠 NEUROGLYPH ULTRA - Fase 3: Benchmark Compressione Ragionamento
================================================================

Prompt → Neuroglifi → LLM → Risposta
Misura: Consistenza semantica + Efficienza simbolica

Autore: NEUROGLYPH ULTRA Team
Data: 2025-05-23
"""

import json
import time
import logging
from pathlib import Path
from typing import Dict, List, Any, Tuple
from ast_reasoning_mapper import ASTReasoningMapper

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('logs/reasoning_benchmark.log'),
        logging.StreamHandler()
    ]
)

class ReasoningCompressionBenchmark:
    """Benchmark per compressione ragionamento con neuroglifi."""
    
    def __init__(self):
        self.mapper = ASTReasoningMapper()
        self.benchmark_results = []
        
        # Test cases per benchmark
        self.test_cases = [
            {
                "name": "Fibonacci Recursivo",
                "code": '''
def fibonacci(n):
    if n <= 1:
        return n
    else:
        return fibonacci(n-1) + fibonacci(n-2)
''',
                "description": "Algoritmo ricorsivo classico"
            },
            {
                "name": "Bubble Sort",
                "code": '''
def bubble_sort(arr):
    n = len(arr)
    for i in range(n):
        for j in range(0, n-i-1):
            if arr[j] > arr[j+1]:
                arr[j], arr[j+1] = arr[j+1], arr[j]
    return arr
''',
                "description": "Algoritmo di ordinamento con confronti"
            },
            {
                "name": "Binary Search",
                "code": '''
def binary_search(arr, target):
    left, right = 0, len(arr) - 1
    while left <= right:
        mid = (left + right) // 2
        if arr[mid] == target:
            return mid
        elif arr[mid] < target:
            left = mid + 1
        else:
            right = mid - 1
    return -1
''',
                "description": "Ricerca binaria con logica condizionale"
            },
            {
                "name": "Class Inheritance",
                "code": '''
class Animal:
    def __init__(self, name):
        self.name = name
    
    def speak(self):
        pass

class Dog(Animal):
    def speak(self):
        return f"{self.name} says Woof!"

class Cat(Animal):
    def speak(self):
        return f"{self.name} says Meow!"
''',
                "description": "Ereditarietà e polimorfismo"
            },
            {
                "name": "Exception Handling",
                "code": '''
def safe_divide(a, b):
    try:
        result = a / b
        return result
    except ZeroDivisionError:
        print("Cannot divide by zero!")
        return None
    except TypeError:
        print("Invalid types for division!")
        return None
    finally:
        print("Division operation completed.")
''',
                "description": "Gestione errori e controllo flusso"
            }
        ]
        
        logging.info(f"🧠 Inizializzato Reasoning Compression Benchmark")
        logging.info(f"📊 Test cases: {len(self.test_cases)}")
        
    def analyze_code_complexity(self, code: str) -> Dict[str, Any]:
        """Analizza complessità del codice."""
        analysis = self.mapper.analyze_python_code(code)
        
        if "error" in analysis:
            return {"error": analysis["error"]}
            
        # Metriche di complessità
        complexity_metrics = {
            "lines_of_code": len(code.strip().split('\n')),
            "characters": len(code),
            "ast_nodes": len(analysis["ast_nodes"]),
            "reasoning_patterns": len(analysis["reasoning_patterns"]),
            "unique_reasoning_types": len(set(analysis["reasoning_patterns"].keys())),
            "total_reasoning_instances": sum(analysis["reasoning_patterns"].values()),
            "reasoning_density": len(analysis["reasoning_patterns"]) / max(len(analysis["ast_nodes"]), 1)
        }
        
        return {
            "analysis": analysis,
            "complexity": complexity_metrics
        }
        
    def generate_neuroglyphs_compression(self, analysis: Dict[str, Any]) -> Dict[str, Any]:
        """Genera compressione neuroglifi e calcola efficienza."""
        if "error" in analysis:
            return {"error": analysis["error"]}
            
        # Genera rappresentazione neuroglifi
        neuroglyphs = self.mapper.generate_neuroglyphs_representation(analysis["analysis"])
        
        # Calcola metriche di compressione
        original_size = analysis["complexity"]["characters"]
        compressed_size = len(neuroglyphs)
        compression_ratio = compressed_size / max(original_size, 1)
        
        # Calcola densità semantica
        reasoning_instances = analysis["complexity"]["total_reasoning_instances"]
        semantic_density = reasoning_instances / max(compressed_size, 1)
        
        compression_metrics = {
            "original_size": original_size,
            "compressed_size": compressed_size,
            "compression_ratio": compression_ratio,
            "compression_percentage": (1 - compression_ratio) * 100,
            "semantic_density": semantic_density,
            "neuroglyphs": neuroglyphs,
            "reasoning_coverage": len(analysis["analysis"]["symbol_mappings"]) / max(len(analysis["analysis"]["reasoning_patterns"]), 1)
        }
        
        return compression_metrics
        
    def evaluate_semantic_consistency(self, original_code: str, neuroglyphs: str) -> Dict[str, Any]:
        """Valuta consistenza semantica tra codice originale e neuroglifi."""
        
        # Analisi originale
        original_analysis = self.mapper.analyze_python_code(original_code)
        
        if "error" in original_analysis:
            return {"error": original_analysis["error"]}
            
        # Estrai pattern reasoning dal codice originale
        original_patterns = set(original_analysis["reasoning_patterns"].keys())
        
        # Estrai pattern dai neuroglifi (simulazione - in un sistema reale 
        # questo richiederebbe un parser neuroglifi → pattern)
        neuroglyphs_patterns = set()
        for code, symbol_info in original_analysis["symbol_mappings"].items():
            if symbol_info["symbol"] in neuroglyphs:
                neuroglyphs_patterns.add(code)
                
        # Calcola metriche di consistenza
        intersection = original_patterns.intersection(neuroglyphs_patterns)
        union = original_patterns.union(neuroglyphs_patterns)
        
        consistency_metrics = {
            "original_patterns_count": len(original_patterns),
            "neuroglyphs_patterns_count": len(neuroglyphs_patterns),
            "common_patterns_count": len(intersection),
            "total_unique_patterns": len(union),
            "semantic_overlap": len(intersection) / max(len(union), 1),
            "pattern_preservation": len(intersection) / max(len(original_patterns), 1),
            "pattern_completeness": len(neuroglyphs_patterns) / max(len(original_patterns), 1)
        }
        
        return consistency_metrics
        
    def run_single_benchmark(self, test_case: Dict[str, Any]) -> Dict[str, Any]:
        """Esegue benchmark su un singolo test case."""
        start_time = time.time()
        
        logging.info(f"🔄 Benchmark: {test_case['name']}")
        
        # Fase 1: Analisi complessità
        complexity_result = self.analyze_code_complexity(test_case["code"])
        
        if "error" in complexity_result:
            return {
                "test_case": test_case["name"],
                "error": complexity_result["error"],
                "duration": time.time() - start_time
            }
            
        # Fase 2: Compressione neuroglifi
        compression_result = self.generate_neuroglyphs_compression(complexity_result)
        
        if "error" in compression_result:
            return {
                "test_case": test_case["name"],
                "error": compression_result["error"],
                "duration": time.time() - start_time
            }
            
        # Fase 3: Valutazione consistenza semantica
        consistency_result = self.evaluate_semantic_consistency(
            test_case["code"], 
            compression_result["neuroglyphs"]
        )
        
        if "error" in consistency_result:
            return {
                "test_case": test_case["name"],
                "error": consistency_result["error"],
                "duration": time.time() - start_time
            }
            
        # Calcola score finale
        efficiency_score = (1 - compression_result["compression_ratio"]) * 100
        semantic_score = consistency_result["semantic_overlap"] * 100
        final_score = (efficiency_score + semantic_score) / 2
        
        benchmark_result = {
            "test_case": test_case["name"],
            "description": test_case["description"],
            "complexity": complexity_result["complexity"],
            "compression": compression_result,
            "consistency": consistency_result,
            "scores": {
                "efficiency_score": efficiency_score,
                "semantic_score": semantic_score,
                "final_score": final_score
            },
            "duration": time.time() - start_time
        }
        
        logging.info(f"✅ {test_case['name']}: Score {final_score:.1f}% (Efficienza: {efficiency_score:.1f}%, Semantica: {semantic_score:.1f}%)")
        
        return benchmark_result
        
    def run_full_benchmark(self) -> Dict[str, Any]:
        """Esegue benchmark completo su tutti i test cases."""
        logging.info(f"🚀 Avvio benchmark completo su {len(self.test_cases)} test cases")
        
        start_time = time.time()
        results = []
        
        for test_case in self.test_cases:
            result = self.run_single_benchmark(test_case)
            results.append(result)
            self.benchmark_results.append(result)
            
        # Calcola statistiche aggregate
        successful_results = [r for r in results if "error" not in r]
        
        if successful_results:
            avg_efficiency = sum(r["scores"]["efficiency_score"] for r in successful_results) / len(successful_results)
            avg_semantic = sum(r["scores"]["semantic_score"] for r in successful_results) / len(successful_results)
            avg_final = sum(r["scores"]["final_score"] for r in successful_results) / len(successful_results)
            
            aggregate_stats = {
                "total_tests": len(results),
                "successful_tests": len(successful_results),
                "failed_tests": len(results) - len(successful_results),
                "average_efficiency_score": avg_efficiency,
                "average_semantic_score": avg_semantic,
                "average_final_score": avg_final,
                "total_duration": time.time() - start_time
            }
        else:
            aggregate_stats = {
                "total_tests": len(results),
                "successful_tests": 0,
                "failed_tests": len(results),
                "error": "Tutti i test sono falliti"
            }
            
        benchmark_summary = {
            "timestamp": time.strftime("%Y-%m-%d %H:%M:%S"),
            "results": results,
            "aggregate_stats": aggregate_stats
        }
        
        return benchmark_summary
        
    def save_benchmark_results(self, results: Dict[str, Any]):
        """Salva risultati benchmark in file JSON."""
        results_path = Path("logs/reasoning_benchmark_results.json")
        
        try:
            with open(results_path, 'w', encoding='utf-8') as f:
                json.dump(results, f, indent=2, ensure_ascii=False)
                
            logging.info(f"💾 Risultati salvati in: {results_path}")
            
        except Exception as e:
            logging.error(f"Errore salvataggio risultati: {e}")
            
    def print_benchmark_report(self, results: Dict[str, Any]):
        """Stampa report dettagliato del benchmark."""
        print("\n" + "="*70)
        print("🧠 NEUROGLYPH ULTRA - Reasoning Compression Benchmark Report")
        print("="*70)
        
        stats = results["aggregate_stats"]
        
        if "error" not in stats:
            print(f"📊 Test eseguiti: {stats['total_tests']}")
            print(f"✅ Test riusciti: {stats['successful_tests']}")
            print(f"❌ Test falliti: {stats['failed_tests']}")
            print(f"⚡ Efficienza media: {stats['average_efficiency_score']:.1f}%")
            print(f"🧠 Consistenza semantica media: {stats['average_semantic_score']:.1f}%")
            print(f"🎯 Score finale medio: {stats['average_final_score']:.1f}%")
            print(f"🕒 Durata totale: {stats['total_duration']:.2f}s")
            print()
            
            print("📈 RISULTATI DETTAGLIATI:")
            for result in results["results"]:
                if "error" not in result:
                    scores = result["scores"]
                    print(f"  • {result['test_case']}: {scores['final_score']:.1f}% "
                          f"(E:{scores['efficiency_score']:.1f}% S:{scores['semantic_score']:.1f}%)")
                else:
                    print(f"  • {result['test_case']}: ERROR - {result['error']}")
        else:
            print(f"❌ Benchmark fallito: {stats['error']}")
            
        print()
        print("🎉 FASE 3 COMPLETATA - REASONING COMPRESSION BENCHMARK READY!")


def main():
    """Main function per benchmark compressione ragionamento."""
    print("🧠 NEUROGLYPH ULTRA - Fase 3: Benchmark Compressione Ragionamento")
    print("="*70)
    print("📊 Prompt → Neuroglifi → LLM → Risposta")
    print("🎯 Misura: Consistenza semantica + Efficienza simbolica")
    print("="*70)
    
    # Crea directory logs se non esiste
    Path("logs").mkdir(exist_ok=True)
    
    # Inizializza benchmark
    benchmark = ReasoningCompressionBenchmark()
    
    # Esegue benchmark completo
    results = benchmark.run_full_benchmark()
    
    # Salva risultati
    benchmark.save_benchmark_results(results)
    
    # Stampa report
    benchmark.print_benchmark_report(results)
    
    return 0


if __name__ == "__main__":
    exit(main())
