# 🚨 NEUROG<PERSON>YPH EMERGENCY RECOVERY - CHECKPOINT RESTART
# Recovering from gradient explosion at step 650+
# Loading stable checkpoint and restarting with ultra-conservative settings

import os
import json
import glob
from pathlib import Path
from datetime import datetime
from transformers import AutoModelForCausalLM, AutoTokenizer, TrainingArguments
from trl import SFTTrainer
from unsloth import FastLanguageModel, is_bfloat16_supported
import torch

print("🚨 NEUROGLYPH EMERGENCY RECOVERY SYSTEM")
print("🔧 Recovering from gradient explosion...")
print("📁 Loading stable checkpoint and restarting with ultra-conservative settings")
print("=" * 80)

# 🔍 FIND BEST STABLE CHECKPOINT
print("🔍 Scanning for stable checkpoints...")
checkpoint_base_dir = CONFIG["output_dir"]
checkpoint_pattern = f"{checkpoint_base_dir}/checkpoint-*"
checkpoint_dirs = glob.glob(checkpoint_pattern)

if not checkpoint_dirs:
    print("❌ No checkpoints found!")
    print(f"   Searched in: {checkpoint_pattern}")
    raise FileNotFoundError("No checkpoints available for recovery")

# Sort checkpoints by step number
checkpoint_info = []
for checkpoint_dir in checkpoint_dirs:
    try:
        step_num = int(checkpoint_dir.split('-')[-1])
        checkpoint_info.append((step_num, checkpoint_dir))
    except ValueError:
        continue

checkpoint_info.sort()

print(f"📊 Found {len(checkpoint_info)} checkpoints:")
for step, path in checkpoint_info:
    print(f"  • Step {step}: {path}")

# 🎯 SELECT STABLE CHECKPOINT (before explosion at step 650)
stable_checkpoint = None
for step, checkpoint_dir in checkpoint_info:
    if 400 <= step <= 600:  # Sweet spot before explosion
        stable_checkpoint = checkpoint_dir
        stable_step = step

if not stable_checkpoint:
    # Fallback to latest checkpoint before 650
    for step, checkpoint_dir in reversed(checkpoint_info):
        if step < 650:
            stable_checkpoint = checkpoint_dir
            stable_step = step
            break

if not stable_checkpoint:
    print("❌ No stable checkpoint found!")
    raise ValueError("Cannot find checkpoint before gradient explosion")

print(f"\n✅ Selected stable checkpoint:")
print(f"   📁 Path: {stable_checkpoint}")
print(f"   📊 Step: {stable_step}")

# 🔧 ULTRA-CONSERVATIVE CONFIGURATION
print(f"\n🔧 Configuring ULTRA-CONSERVATIVE settings...")

CONFIG_RECOVERY = {
    # Model paths
    "base_model": CONFIG["base_model"],
    "model_name": "NEUROGLYPH_ULTIMATE_v1.0_RECOVERED",
    "output_dir": CONFIG["output_dir"] + "_recovered",
    "checkpoint_dir": CONFIG["checkpoint_dir"],
    "backup_dir": CONFIG["backup_dir"],
    "tokenizer_path": CONFIG["tokenizer_path"],
    
    # ULTRA-CONSERVATIVE TRAINING SETTINGS
    "learning_rate": 2e-5,  # DRASTICALLY REDUCED (was 1e-4)
    "num_epochs": 2,        # REDUCED epochs to prevent overfitting
    "batch_size": 1,        # MINIMAL batch size
    "gradient_accumulation_steps": 16,  # INCREASED accumulation
    "max_seq_length": CONFIG["max_seq_length"],
    
    # ENHANCED STABILITY
    "warmup_steps": 200,    # MORE warmup
    "logging_steps": 25,    # MORE frequent logging
    "save_steps": 100,      # MORE frequent saves
    "eval_steps": 50,       # FREQUENT evaluation
    
    # RECOVERY SPECIFIC
    "resume_from_checkpoint": stable_checkpoint,
    "starting_step": stable_step,
}

print(f"📊 RECOVERY CONFIGURATION:")
print(f"  • Learning rate: {CONFIG_RECOVERY['learning_rate']} (was {CONFIG['learning_rate']})")
print(f"  • Batch size: {CONFIG_RECOVERY['batch_size']} (was {CONFIG['batch_size']})")
print(f"  • Grad accumulation: {CONFIG_RECOVERY['gradient_accumulation_steps']} (was {CONFIG['gradient_accumulation_steps']})")
print(f"  • Epochs: {CONFIG_RECOVERY['num_epochs']} (was {CONFIG['num_epochs']})")
print(f"  • Warmup steps: {CONFIG_RECOVERY['warmup_steps']} (was {CONFIG['warmup_steps']})")
print(f"  • Resume from: Step {CONFIG_RECOVERY['starting_step']}")

# 📁 CREATE RECOVERY OUTPUT DIRECTORY
recovery_output_dir = Path(CONFIG_RECOVERY["output_dir"])
recovery_output_dir.mkdir(parents=True, exist_ok=True)
print(f"📁 Recovery output: {recovery_output_dir}")

# 🔄 LOAD MODEL FROM STABLE CHECKPOINT
print(f"\n🔄 Loading model from stable checkpoint...")
print(f"📁 Loading from: {stable_checkpoint}")

try:
    # Load model and tokenizer from checkpoint
    model, tokenizer = FastLanguageModel.from_pretrained(
        model_name=stable_checkpoint,
        max_seq_length=CONFIG_RECOVERY["max_seq_length"],
        dtype=None,  # Auto-detect
        load_in_4bit=True,
    )
    
    print("✅ Model loaded successfully from checkpoint!")
    print(f"📊 Model vocab size: {len(tokenizer.vocab):,}")
    
    # Verify model is not corrupted
    test_input = "Test input"
    test_tokens = tokenizer.encode(test_input, return_tensors="pt")
    with torch.no_grad():
        test_output = model(test_tokens)
        test_loss = test_output.loss if hasattr(test_output, 'loss') else 0.0
    
    print(f"🧪 Model sanity check: loss = {test_loss}")
    
    if test_loss > 5.0:
        print("⚠️ WARNING: Model may be partially corrupted")
    else:
        print("✅ Model appears stable")
        
except Exception as e:
    print(f"❌ Failed to load checkpoint: {e}")
    raise

# 🔧 CONFIGURE MODEL FOR RECOVERY TRAINING
print(f"\n🔧 Configuring model for recovery training...")

model = FastLanguageModel.get_peft_model(
    model,
    r=16,  # REDUCED rank for stability
    target_modules=["q_proj", "k_proj", "v_proj", "o_proj",
                   "gate_proj", "up_proj", "down_proj"],
    lora_alpha=16,
    lora_dropout=0.1,  # INCREASED dropout for regularization
    bias="none",
    use_gradient_checkpointing="unsloth",
    random_state=3407,
    use_rslora=False,
    loftq_config=None,
)

print("✅ Model configured with enhanced stability settings")

# 📊 CALCULATE RECOVERY TRAINING STEPS
total_examples = len(train_dataset)
effective_batch_size = CONFIG_RECOVERY["batch_size"] * CONFIG_RECOVERY["gradient_accumulation_steps"]
steps_per_epoch = total_examples // effective_batch_size
total_steps = steps_per_epoch * CONFIG_RECOVERY["num_epochs"]
remaining_steps = total_steps - CONFIG_RECOVERY["starting_step"]

print(f"\n📊 RECOVERY TRAINING CALCULATION:")
print(f"  • Total examples: {total_examples:,}")
print(f"  • Effective batch size: {effective_batch_size}")
print(f"  • Steps per epoch: {steps_per_epoch}")
print(f"  • Total steps planned: {total_steps}")
print(f"  • Starting from step: {CONFIG_RECOVERY['starting_step']}")
print(f"  • Remaining steps: {remaining_steps}")
print(f"  • Evaluation every: {CONFIG_RECOVERY['eval_steps']} steps")
print(f"  • Save every: {CONFIG_RECOVERY['save_steps']} steps")

# 🚫 DISABLE WANDB COMPLETELY
os.environ["WANDB_DISABLED"] = "true"
os.environ["WANDB_MODE"] = "disabled"
from transformers.integrations import WandbCallback
import logging
logging.getLogger("wandb").setLevel(logging.ERROR)

# 🎯 CREATE RECOVERY TRAINER
print(f"\n🎯 Creating RECOVERY trainer with ultra-conservative settings...")

trainer = SFTTrainer(
    model=model,
    tokenizer=tokenizer,
    train_dataset=train_dataset,
    eval_dataset=val_dataset,
    dataset_text_field="text",
    max_seq_length=CONFIG_RECOVERY["max_seq_length"],
    dataset_num_proc=1,  # SINGLE process for stability
    packing=False,  # CRITICAL: NO PACKING for symbol integrity
    args=TrainingArguments(
        # Output - RECOVERY Paths
        output_dir=CONFIG_RECOVERY["output_dir"],
        run_name=CONFIG_RECOVERY["model_name"],

        # Training - ULTRA-CONSERVATIVE
        num_train_epochs=CONFIG_RECOVERY["num_epochs"],
        per_device_train_batch_size=CONFIG_RECOVERY["batch_size"],
        per_device_eval_batch_size=CONFIG_RECOVERY["batch_size"],
        gradient_accumulation_steps=CONFIG_RECOVERY["gradient_accumulation_steps"],

        # Optimization - MAXIMUM STABILITY
        learning_rate=CONFIG_RECOVERY["learning_rate"],  # 2e-5 ultra-conservative
        weight_decay=0.01,
        warmup_steps=CONFIG_RECOVERY["warmup_steps"],  # 200 steps warmup
        lr_scheduler_type="cosine",

        # Mixed Precision - STABLE
        fp16=not is_bfloat16_supported(),
        bf16=is_bfloat16_supported(),

        # Logging & Saving - FREQUENT MONITORING
        logging_steps=CONFIG_RECOVERY["logging_steps"],  # 25 steps
        save_steps=CONFIG_RECOVERY["save_steps"],        # 100 steps
        eval_steps=CONFIG_RECOVERY["eval_steps"],        # 50 steps
        eval_strategy="steps",
        save_strategy="steps",
        save_total_limit=10,  # Keep MORE checkpoints for safety

        # Quality - RECOVERY Model Selection
        load_best_model_at_end=True,
        metric_for_best_model="eval_loss",
        greater_is_better=False,

        # Memory & Performance - ULTRA-SAFE
        dataloader_pin_memory=True,
        dataloader_num_workers=1,  # SINGLE worker
        remove_unused_columns=False,
        group_by_length=False,  # DISABLED for symbol safety

        # Gradient Management - MAXIMUM STABILITY
        max_grad_norm=0.3,  # REDUCED gradient clipping
        gradient_checkpointing=True,

        # Optimization - STABLE
        optim="adamw_torch",

        # Reproducibility - RECOVERY CONSISTENCY
        seed=42,
        data_seed=42,

        # Data Processing - ULTRA-SAFE
        dataloader_drop_last=False,
        ignore_data_skip=False,

        # RECOVERY SPECIFIC
        resume_from_checkpoint=CONFIG_RECOVERY["resume_from_checkpoint"],

        # COMPLETE WANDB DISABLING
        report_to=None,
        logging_dir=None,
        disable_tqdm=False,
    ),
)

# Remove wandb callbacks
if hasattr(trainer, 'callback_handler'):
    trainer.callback_handler.callbacks = [
        cb for cb in trainer.callback_handler.callbacks
        if not isinstance(cb, WandbCallback)
    ]

print("✅ RECOVERY trainer configured!")
print("🛡️ ULTRA-CONSERVATIVE features enabled:")
print(f"  • Learning rate: {CONFIG_RECOVERY['learning_rate']} (ultra-low)")
print(f"  • Batch size: {CONFIG_RECOVERY['batch_size']} (minimal)")
print(f"  • Gradient clipping: 0.3 (enhanced)")
print(f"  • Warmup steps: {CONFIG_RECOVERY['warmup_steps']} (extended)")
print(f"  • Frequent saves: every {CONFIG_RECOVERY['save_steps']} steps")
print(f"  • Frequent eval: every {CONFIG_RECOVERY['eval_steps']} steps")
print(f"  • Resume from: Step {CONFIG_RECOVERY['starting_step']}")

# 🚀 START RECOVERY TRAINING
print(f"\n🚀 STARTING RECOVERY TRAINING...")
print(f"🔧 Resuming from stable checkpoint at step {CONFIG_RECOVERY['starting_step']}")
print(f"⚛️ ATOMICITY PROTECTION: ENABLED")
print(f"🛡️ GRADIENT EXPLOSION PROTECTION: ENABLED")
print()

try:
    start_time = datetime.now()
    print(f"⏰ Recovery training started at: {start_time.strftime('%Y-%m-%d %H:%M:%S')}")
    print()

    # Start automatic backup system
    print("💾 Starting recovery backup system...")
    auto_backup.start()

    # Train the model - RECOVERY MODE
    print("🔥 Starting RECOVERY training with ultra-conservative settings...")
    trainer_stats = trainer.train(resume_from_checkpoint=CONFIG_RECOVERY["resume_from_checkpoint"])

    end_time = datetime.now()
    training_duration = end_time - start_time

    print()
    print("🎉 RECOVERY TRAINING COMPLETED!")
    print(f"⏰ Training finished at: {end_time.strftime('%Y-%m-%d %H:%M:%S')}")
    print(f"⌛ Recovery training time: {training_duration}")
    print()

    # Training statistics
    print("📊 Recovery Training Statistics:")
    print(f"  • Final train loss: {trainer_stats.training_loss:.4f}")
    print(f"  • Training steps: {trainer_stats.global_step:,}")
    print(f"  • Started from step: {CONFIG_RECOVERY['starting_step']}")
    print(f"  • Recovery steps completed: {trainer_stats.global_step - CONFIG_RECOVERY['starting_step']}")

    # 💾 SAVE RECOVERED MODEL
    print("\n💾 Saving RECOVERED NEUROGLYPH model...")

    # Save the model
    model.save_pretrained(CONFIG_RECOVERY["output_dir"])
    tokenizer.save_pretrained(CONFIG_RECOVERY["output_dir"])

    print("✅ Recovered model saved!")

    # Save recovery metadata
    recovery_metadata = {
        "model_name": CONFIG_RECOVERY["model_name"],
        "recovery_start": start_time.isoformat(),
        "recovery_end": end_time.isoformat(),
        "recovery_duration_seconds": training_duration.total_seconds(),
        "original_checkpoint": CONFIG_RECOVERY["resume_from_checkpoint"],
        "starting_step": CONFIG_RECOVERY["starting_step"],
        "final_step": trainer_stats.global_step,
        "final_loss": trainer_stats.training_loss,
        "recovery_config": CONFIG_RECOVERY,
        "gradient_explosion_detected_at": "step_650",
        "recovery_successful": True
    }

    metadata_path = Path(CONFIG_RECOVERY["output_dir"]) / "recovery_metadata.json"
    with open(metadata_path, 'w') as f:
        json.dump(recovery_metadata, f, indent=2)

    print(f"📋 Recovery metadata saved: {metadata_path}")

    # Stop backup system
    auto_backup.stop()

    print("\n🎉 NEUROGLYPH RECOVERY SUCCESSFUL!")
    print("🏆 Model recovered with ultra-conservative training!")
    print("⚛️ Symbolic integrity maintained!")
    print("🚀 Ready for deployment!")

except Exception as e:
    print(f"❌ Recovery training failed: {e}")
    try:
        auto_backup.stop()
    except:
        pass
    raise
