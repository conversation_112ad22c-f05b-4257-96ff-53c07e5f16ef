#!/usr/bin/env python3
"""
🧠 SOCRATE Logic Simulator - Simulazione Logica Simbolica Avanzata
================================================================

Simula l'esecuzione logica dei DAG di ragionamento per validare:
- Correttezza logica delle inferenze
- Consistenza delle premesse
- Rilevamento di contraddizioni
- Predizione di fallimenti
- Calcolo di probabilità di successo

Autore: NEUROGLYPH ULTRA Team
Data: 2025-05-24
"""

import logging
from typing import Dict, List, Any, Optional, Set, Tuple
from enum import Enum
from dataclasses import dataclass

# Import dal planner (assumendo che sia nello stesso package)
try:
    from .planner import ReasoningDAG, ReasoningNode, ReasoningEdge, NodeType, ReasoningType
except ImportError:
    # Fallback per import diretto
    from planner import ReasoningDAG, ReasoningNode, ReasoningEdge, NodeType, ReasoningType

logger = logging.getLogger(__name__)

class SimulationResult(Enum):
    """Risultati possibili della simulazione."""
    SUCCESS = "success"
    FAILURE = "failure"
    CONTRADICTION = "contradiction"
    UNCERTAINTY = "uncertainty"
    INCOMPLETE = "incomplete"

class LogicRule(Enum):
    """Regole logiche supportate dal simulatore."""
    MODUS_PONENS = "modus_ponens"          # A → B, A ⊢ B
    MODUS_TOLLENS = "modus_tollens"        # A → B, ¬B ⊢ ¬A
    HYPOTHETICAL_SYLLOGISM = "hyp_syll"    # A → B, B → C ⊢ A → C
    DISJUNCTIVE_SYLLOGISM = "disj_syll"    # A ∨ B, ¬A ⊢ B
    CONJUNCTION = "conjunction"             # A, B ⊢ A ∧ B
    SIMPLIFICATION = "simplification"       # A ∧ B ⊢ A
    ADDITION = "addition"                   # A ⊢ A ∨ B
    CONTRADICTION = "contradiction"         # A, ¬A ⊢ ⊥

@dataclass
class SimulationStep:
    """Singolo passo della simulazione."""
    step_id: int
    node_id: str
    rule_applied: Optional[LogicRule]
    premises: List[str]
    conclusion: str
    confidence: float
    success: bool
    error_message: Optional[str] = None

@dataclass
class SimulationTrace:
    """Traccia completa della simulazione."""
    dag_id: str
    steps: List[SimulationStep]
    final_result: SimulationResult
    overall_confidence: float
    contradictions_found: List[str]
    warnings: List[str]
    execution_time: float

class SOCRATELogicSimulator:
    """Simulatore logico avanzato per DAG di ragionamento SOCRATE."""

    def __init__(self):
        self.logic_rules = self._initialize_logic_rules()
        self.contradiction_patterns = self._initialize_contradiction_patterns()
        self.failure_indicators = self._initialize_failure_indicators()

        logger.info("🧠 SOCRATE Logic Simulator inizializzato")

    def _initialize_logic_rules(self) -> Dict[LogicRule, Dict[str, Any]]:
        """Inizializza regole logiche con i loro pattern."""
        return {
            LogicRule.MODUS_PONENS: {
                "pattern": ["premise", "implication", "conclusion"],
                "confidence_factor": 0.95,
                "description": "Se A implica B e A è vero, allora B è vero"
            },
            LogicRule.MODUS_TOLLENS: {
                "pattern": ["premise", "implication", "negation"],
                "confidence_factor": 0.90,
                "description": "Se A implica B e B è falso, allora A è falso"
            },
            LogicRule.HYPOTHETICAL_SYLLOGISM: {
                "pattern": ["implication", "implication", "conclusion"],
                "confidence_factor": 0.85,
                "description": "Se A implica B e B implica C, allora A implica C"
            },
            LogicRule.CONTRADICTION: {
                "pattern": ["premise", "negation"],
                "confidence_factor": 0.0,
                "description": "A e non-A non possono essere entrambi veri"
            }
        }

    def _initialize_contradiction_patterns(self) -> List[Dict[str, Any]]:
        """Inizializza pattern che indicano contraddizioni."""
        return [
            {"symbols": ["⊨", "¬"], "description": "Affermazione e negazione simultanee"},
            {"symbols": ["✓", "✗"], "description": "Successo e fallimento simultanei"},
            {"symbols": ["↯", "✅"], "description": "Errore e successo simultanei"},
            {"codes": ["ng:reasoning:contradiction", "ng:reasoning:premise"], "description": "Contraddizione con premessa"}
        ]

    def _initialize_failure_indicators(self) -> List[Dict[str, Any]]:
        """Inizializza indicatori di possibile fallimento."""
        return [
            {"symbol": "↯", "weight": 0.8, "description": "Simbolo di errore/fallimento"},
            {"symbol": "⚠", "weight": 0.6, "description": "Simbolo di warning"},
            {"symbol": "?", "weight": 0.4, "description": "Simbolo di incertezza"},
            {"code": "ng:reasoning:contradiction", "weight": 0.9, "description": "Contraddizione logica"},
            {"code": "ng:reasoning:error_correction", "weight": 0.7, "description": "Correzione errore necessaria"}
        ]

    def simulate_dag(self, dag: ReasoningDAG) -> SimulationTrace:
        """Simula l'esecuzione logica completa di un DAG."""
        import time
        start_time = time.time()

        logger.info(f"🔄 Simulazione DAG {dag.id} con {len(dag.nodes)} nodi")

        # Inizializza traccia
        trace = SimulationTrace(
            dag_id=dag.id,
            steps=[],
            final_result=SimulationResult.INCOMPLETE,
            overall_confidence=0.0,
            contradictions_found=[],
            warnings=[],
            execution_time=0.0
        )

        # Simula esecuzione seguendo la catena di ragionamento
        step_counter = 0

        for node_id in dag.reasoning_chain:
            if node_id not in dag.nodes:
                continue

            node = dag.nodes[node_id]
            step = self._simulate_node(node, dag, step_counter)
            trace.steps.append(step)

            # Controlla contraddizioni
            if not step.success:
                contradiction = self._check_contradictions(node, dag)
                if contradiction:
                    trace.contradictions_found.append(contradiction)

            step_counter += 1

        # Analisi finale
        trace.final_result = self._determine_final_result(trace)
        trace.overall_confidence = self._calculate_overall_confidence(trace)
        trace.warnings = self._generate_warnings(trace, dag)
        trace.execution_time = time.time() - start_time

        logger.info(f"✅ Simulazione completata: {trace.final_result.value} "
                   f"(confidenza: {trace.overall_confidence:.2f})")

        return trace

    def _simulate_node(self, node: ReasoningNode, dag: ReasoningDAG, step_id: int) -> SimulationStep:
        """Simula l'esecuzione di un singolo nodo."""

        # Trova premesse (nodi predecessori)
        premises = self._find_node_premises(node.id, dag)

        # Determina regola logica applicabile
        applicable_rule = self._find_applicable_rule(node, premises, dag)

        # Simula applicazione della regola
        success, confidence, error = self._apply_logic_rule(node, premises, applicable_rule)

        # Controlla indicatori di fallimento
        failure_risk = self._assess_failure_risk(node)
        if failure_risk > 0.7:
            success = False
            error = f"Alto rischio di fallimento: {failure_risk:.2f}"

        return SimulationStep(
            step_id=step_id,
            node_id=node.id,
            rule_applied=applicable_rule,
            premises=[p.id for p in premises],
            conclusion=node.content,
            confidence=confidence * (1 - failure_risk),
            success=success,
            error_message=error
        )

    def _find_node_premises(self, node_id: str, dag: ReasoningDAG) -> List[ReasoningNode]:
        """Trova i nodi premessa per un dato nodo."""
        premises = []

        for edge in dag.edges:
            if edge.to_node == node_id:
                if edge.from_node in dag.nodes:
                    premises.append(dag.nodes[edge.from_node])

        return premises

    def _find_applicable_rule(self, node: ReasoningNode, premises: List[ReasoningNode],
                             dag: ReasoningDAG) -> Optional[LogicRule]:
        """Trova la regola logica più appropriata per il nodo."""

        # Analizza tipo di nodo e pattern
        if node.type == NodeType.CONTRADICTION:
            return LogicRule.CONTRADICTION

        if len(premises) == 1 and node.type == NodeType.CONCLUSION:
            return LogicRule.MODUS_PONENS

        if len(premises) == 2:
            # Controlla se è un sillogismo ipotetico
            if all(p.type == NodeType.INFERENCE for p in premises):
                return LogicRule.HYPOTHETICAL_SYLLOGISM

        # Analizza simboli per determinare regola
        node_symbols = set(node.symbols)
        premise_symbols = set()
        for p in premises:
            premise_symbols.update(p.symbols)

        # Pattern specifici
        if "→" in premise_symbols and node.type == NodeType.CONCLUSION:
            return LogicRule.MODUS_PONENS

        if "¬" in node_symbols or "↯" in node_symbols:
            return LogicRule.MODUS_TOLLENS

        # Default: deduzione semplice
        return LogicRule.MODUS_PONENS if premises else None

    def _apply_logic_rule(self, node: ReasoningNode, premises: List[ReasoningNode],
                         rule: Optional[LogicRule]) -> Tuple[bool, float, Optional[str]]:
        """Applica una regola logica e restituisce risultato."""

        if rule is None:
            return True, node.confidence, None

        rule_info = self.logic_rules.get(rule, {})
        base_confidence = rule_info.get("confidence_factor", 0.7)

        if rule == LogicRule.CONTRADICTION:
            return False, 0.0, "Contraddizione logica rilevata"

        if rule == LogicRule.MODUS_PONENS:
            if len(premises) >= 1:
                # Confidenza basata sulle premesse
                premise_confidence = sum(p.confidence for p in premises) / len(premises)
                final_confidence = base_confidence * premise_confidence
                return True, final_confidence, None
            else:
                return False, 0.0, "Modus ponens richiede almeno una premessa"

        if rule == LogicRule.HYPOTHETICAL_SYLLOGISM:
            if len(premises) >= 2:
                premise_confidence = sum(p.confidence for p in premises) / len(premises)
                final_confidence = base_confidence * premise_confidence * 0.9  # Penalty per complessità
                return True, final_confidence, None
            else:
                return False, 0.0, "Sillogismo ipotetico richiede almeno due premesse"

        # Regola generica
        premise_confidence = sum(p.confidence for p in premises) / max(len(premises), 1)
        final_confidence = base_confidence * premise_confidence
        return True, final_confidence, None

    def _assess_failure_risk(self, node: ReasoningNode) -> float:
        """Valuta il rischio di fallimento per un nodo."""
        risk_score = 0.0

        # Controlla simboli di fallimento
        for symbol in node.symbols:
            for indicator in self.failure_indicators:
                if indicator.get("symbol") == symbol:
                    risk_score += indicator.get("weight", 0.0)

        # Controlla codici di fallimento
        node_code = node.metadata.get("symbol_code", "")
        for indicator in self.failure_indicators:
            if indicator.get("code") == node_code:
                risk_score += indicator.get("weight", 0.0)

        # Normalizza il rischio
        return min(1.0, risk_score)

    def _check_contradictions(self, node: ReasoningNode, dag: ReasoningDAG) -> Optional[str]:
        """Controlla se un nodo presenta contraddizioni."""

        # Controlla pattern di contraddizione
        for pattern in self.contradiction_patterns:
            if "symbols" in pattern:
                pattern_symbols = set(pattern["symbols"])
                node_symbols = set(node.symbols)

                if pattern_symbols.issubset(node_symbols):
                    return f"Contraddizione rilevata: {pattern['description']}"

            if "codes" in pattern:
                node_code = node.metadata.get("symbol_code", "")
                if node_code in pattern["codes"]:
                    return f"Contraddizione di codice: {pattern['description']}"

        # Controlla contraddizioni con altri nodi
        for other_node in dag.nodes.values():
            if other_node.id != node.id:
                if self._nodes_contradict(node, other_node):
                    return f"Contraddizione tra nodi {node.id} e {other_node.id}"

        return None

    def _nodes_contradict(self, node1: ReasoningNode, node2: ReasoningNode) -> bool:
        """Verifica se due nodi si contraddicono."""

        # Contraddizione diretta: stesso contenuto ma tipi opposti
        if (node1.content == node2.content and
            ((node1.type == NodeType.PREMISE and node2.type == NodeType.CONTRADICTION) or
             (node1.type == NodeType.CONCLUSION and node2.type == NodeType.CONTRADICTION))):
            return True

        # Contraddizione simbolica
        symbols1 = set(node1.symbols)
        symbols2 = set(node2.symbols)

        contradictory_pairs = [
            ("✓", "✗"), ("⊨", "¬"), ("↯", "✅"), ("true", "false")
        ]

        for pos, neg in contradictory_pairs:
            if pos in symbols1 and neg in symbols2:
                return True
            if neg in symbols1 and pos in symbols2:
                return True

        return False

    def _determine_final_result(self, trace: SimulationTrace) -> SimulationResult:
        """Determina il risultato finale della simulazione."""

        if trace.contradictions_found:
            return SimulationResult.CONTRADICTION

        if not trace.steps:
            return SimulationResult.INCOMPLETE

        # Conta successi e fallimenti
        successful_steps = sum(1 for step in trace.steps if step.success)
        total_steps = len(trace.steps)

        success_rate = successful_steps / total_steps if total_steps > 0 else 0

        if success_rate >= 0.8:
            return SimulationResult.SUCCESS
        elif success_rate >= 0.5:
            return SimulationResult.UNCERTAINTY
        else:
            return SimulationResult.FAILURE

    def _calculate_overall_confidence(self, trace: SimulationTrace) -> float:
        """Calcola la confidenza complessiva della simulazione."""

        if not trace.steps:
            return 0.0

        # Media pesata delle confidenze
        total_confidence = 0.0
        total_weight = 0.0

        for step in trace.steps:
            weight = 1.0 if step.success else 0.5  # Peso ridotto per step falliti
            total_confidence += step.confidence * weight
            total_weight += weight

        if total_weight == 0:
            return 0.0

        base_confidence = total_confidence / total_weight

        # Penalty per contraddizioni
        contradiction_penalty = len(trace.contradictions_found) * 0.2

        # Penalty per warnings
        warning_penalty = len(trace.warnings) * 0.1

        final_confidence = base_confidence - contradiction_penalty - warning_penalty

        return max(0.0, min(1.0, final_confidence))

    def _generate_warnings(self, trace: SimulationTrace, dag: ReasoningDAG) -> List[str]:
        """Genera warning basati sulla simulazione."""
        warnings = []

        # Warning per bassa confidenza
        if trace.overall_confidence < 0.6:
            warnings.append(f"Bassa confidenza complessiva: {trace.overall_confidence:.2f}")

        # Warning per molti step falliti
        failed_steps = sum(1 for step in trace.steps if not step.success)
        if failed_steps > len(trace.steps) * 0.3:
            warnings.append(f"Molti step falliti: {failed_steps}/{len(trace.steps)}")

        # Warning per DAG mal formato
        if len(dag.edges) == 0 and len(dag.nodes) > 1:
            warnings.append("DAG senza archi: possibile struttura mal formata")

        # Warning per nodi isolati
        connected_nodes = set()
        for edge in dag.edges:
            connected_nodes.add(edge.from_node)
            connected_nodes.add(edge.to_node)

        isolated_nodes = set(dag.nodes.keys()) - connected_nodes
        if isolated_nodes:
            warnings.append(f"Nodi isolati rilevati: {len(isolated_nodes)}")

        return warnings


# Funzioni di utilità per uso esterno
def simulate_dag(reasoning_dag: ReasoningDAG) -> SimulationTrace:
    """Funzione di convenienza per simulare un DAG."""
    simulator = SOCRATELogicSimulator()
    return simulator.simulate_dag(reasoning_dag)


def validate_reasoning_logic(reasoning_dag: ReasoningDAG) -> bool:
    """Valida la logica di un DAG di ragionamento."""
    trace = simulate_dag(reasoning_dag)
    return trace.final_result in [SimulationResult.SUCCESS, SimulationResult.UNCERTAINTY]


def check_dag_contradictions(reasoning_dag: ReasoningDAG) -> List[str]:
    """Controlla contraddizioni in un DAG."""
    trace = simulate_dag(reasoning_dag)
    return trace.contradictions_found
