# Contributing to NEUROGLIPH

Grazie per il tuo interesse nel contribuire a NEUROGLIPH! Questo documento fornisce linee guida per contribuire al progetto.

## 🚀 Come Iniziare

### 1. Setup Ambiente di Sviluppo

```bash
# Fork del repository
git clone https://github.com/YOUR_USERNAME/NEUROGLIPH.git
cd NEUROGLIPH

# Setup ambiente development
python scripts/setup.py --dev

# Installa pre-commit hooks
pre-commit install
```

### 2. Struttura del Progetto

Familiarizzati con la struttura:

- `core/` - Motore di codifica/decodifica
- `llm/` - Modelli e training LLM
- `runtime/` - Runtime di esecuzione
- `datasets/` - Dataset e preprocessing
- `scripts/` - Utility e automazione
- `docs/` - Documentazione
- `tests/` - Test suite

## 📝 Linee Guida per i Contributi

### Code Style

- **Python**: Usa Black per formatting, isort per import
- **Docstrings**: Formato Google style
- **Type Hints**: Obbligatori per funzioni pubbliche
- **Naming**: snake_case per variabili/funzioni, PascalCase per classi

Esempio:

```python
def encode_neuroglyphs(
    code: str, 
    language: str = "python",
    level: int = 2
) -> Tuple[str, Dict[str, Any]]:
    """Codifica codice sorgente in neuroglifi.
    
    Args:
        code: Codice sorgente da codificare
        language: Linguaggio di programmazione
        level: Livello di compressione (1-3)
        
    Returns:
        Tupla contenente neuroglifi codificati e metadati
        
    Raises:
        ValueError: Se il linguaggio non è supportato
    """
    pass
```

### Commit Messages

Usa Conventional Commits:

```
feat(core): add support for Rust language encoding
fix(runtime): resolve memory leak in sandbox execution
docs(api): update encoder documentation
test(core): add unit tests for symbol mapping
refactor(llm): optimize training pipeline
```

Tipi di commit:
- `feat`: Nuove funzionalità
- `fix`: Bug fixes
- `docs`: Documentazione
- `test`: Test
- `refactor`: Refactoring
- `perf`: Miglioramenti performance
- `style`: Formatting, no logic changes
- `chore`: Maintenance tasks

### Testing

- Scrivi test per tutte le nuove funzionalità
- Mantieni coverage > 80%
- Usa pytest per unit tests
- Includi integration tests per componenti complessi

```bash
# Esegui test
pytest tests/

# Con coverage
pytest --cov=core --cov=runtime tests/

# Test specifici
pytest tests/test_encoder.py::test_python_encoding
```

### Documentazione

- Aggiorna documentazione per modifiche API
- Includi esempi di utilizzo
- Usa docstrings dettagliate
- Aggiorna README se necessario

## 🎯 Aree di Contributo

### 1. Core Engine

**Priorità Alta:**
- Supporto nuovi linguaggi (Kotlin, Swift, TypeScript)
- Ottimizzazioni algoritmi di compressione
- Miglioramenti analisi semantica

**Priorità Media:**
- Cache intelligente per pattern comuni
- Parallelizzazione encoding/decoding
- Supporto incremental encoding

### 2. LLM Integration

**Priorità Alta:**
- Fine-tuning su dataset specializzati
- Ottimizzazioni per modelli specifici
- Metriche di valutazione avanzate

**Priorità Media:**
- Supporto modelli multimodali
- Integration con Hugging Face Hub
- Distributed training support

### 3. Runtime & Security

**Priorità Alta:**
- Sandbox security improvements
- Performance optimizations
- Memory management

**Priorità Media:**
- Container-based execution
- Remote execution capabilities
- Resource monitoring

### 4. Dataset & Benchmarking

**Priorità Alta:**
- Nuovi dataset di qualità
- Benchmark standardizzati
- Metriche di valutazione

**Priorità Media:**
- Synthetic data generation
- Cross-language evaluation
- Domain-specific datasets

### 5. Tools & Infrastructure

**Priorità Alta:**
- CLI tools migliorati
- Web interface
- API server

**Priorità Media:**
- IDE extensions
- CI/CD improvements
- Monitoring dashboard

## 🔄 Processo di Contribuzione

### 1. Pianificazione

1. Controlla [Issues](https://github.com/JoyciAkira/NEUROGLIPH/issues) esistenti
2. Crea nuovo issue per discussione se necessario
3. Commenta sull'issue per indicare che ci stai lavorando

### 2. Sviluppo

1. Crea branch da `main`:
   ```bash
   git checkout -b feat/your-feature-name
   ```

2. Sviluppa seguendo le linee guida
3. Scrivi test appropriati
4. Aggiorna documentazione

### 3. Testing

```bash
# Lint e formatting
black .
isort .
flake8 .

# Type checking
mypy core/ runtime/

# Test
pytest tests/

# Test integration
python scripts/test_integration.py
```

### 4. Pull Request

1. Push del branch:
   ```bash
   git push origin feat/your-feature-name
   ```

2. Crea Pull Request con:
   - Titolo descrittivo
   - Descrizione dettagliata delle modifiche
   - Link agli issue correlati
   - Screenshots se applicabile
   - Checklist completata

3. Template PR:
   ```markdown
   ## Descrizione
   Breve descrizione delle modifiche

   ## Tipo di Modifica
   - [ ] Bug fix
   - [ ] Nuova funzionalità
   - [ ] Breaking change
   - [ ] Documentazione

   ## Testing
   - [ ] Test esistenti passano
   - [ ] Nuovi test aggiunti
   - [ ] Test manuali eseguiti

   ## Checklist
   - [ ] Code follows style guidelines
   - [ ] Self-review completed
   - [ ] Documentation updated
   - [ ] No breaking changes (or documented)
   ```

### 5. Review Process

- Almeno 1 review richiesta
- CI deve passare
- Conflitti risolti
- Documentazione aggiornata

## 🐛 Reporting Bugs

### Bug Report Template

```markdown
**Descrizione Bug**
Descrizione chiara del problema

**Riproduzione**
Passi per riprodurre:
1. Vai a '...'
2. Clicca su '....'
3. Vedi errore

**Comportamento Atteso**
Cosa dovrebbe succedere

**Screenshots**
Se applicabile

**Ambiente:**
- OS: [e.g. macOS 14.0]
- Python: [e.g. 3.11.0]
- NEUROGLIPH: [e.g. 1.0.0]

**Informazioni Aggiuntive**
Qualsiasi altro contesto
```

## 💡 Feature Requests

### Feature Request Template

```markdown
**Problema da Risolvere**
Descrizione del problema o limitazione

**Soluzione Proposta**
Descrizione della soluzione desiderata

**Alternative Considerate**
Altre soluzioni valutate

**Informazioni Aggiuntive**
Contesto, esempi, mockups
```

## 📚 Risorse Utili

### Documentazione
- [API Reference](docs/api.md)
- [Architecture Guide](docs/architecture.md)
- [Development Setup](docs/development.md)

### Tools
- [Black](https://black.readthedocs.io/) - Code formatting
- [isort](https://isort.readthedocs.io/) - Import sorting
- [mypy](https://mypy.readthedocs.io/) - Type checking
- [pytest](https://pytest.org/) - Testing framework

### Community
- [GitHub Discussions](https://github.com/JoyciAkira/NEUROGLIPH/discussions)
- [Discord Server](https://discord.gg/neurogliph)
- [Twitter](https://twitter.com/neurogliph)

## 🏆 Riconoscimenti

I contributori vengono riconosciuti in:
- README.md Contributors section
- Release notes
- Hall of Fame (per contributi significativi)

## 📄 Licenza

Contribuendo a NEUROGLIPH, accetti che i tuoi contributi saranno rilasciati sotto la stessa licenza MIT del progetto.

---

Grazie per contribuire a NEUROGLIPH! 🚀
