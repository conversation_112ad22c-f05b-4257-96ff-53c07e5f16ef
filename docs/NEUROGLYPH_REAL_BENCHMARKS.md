# NEUROGLYPH REAL BENCHMARKS

## 🎯 OVERVIEW

Sistema di benchmark reali per validazione scientifica di NEUROGLYPH usando dataset ufficiali e metriche standard dell'industria.

### **CARATTERISTICHE PRINCIPALI**

✅ **Dataset Ufficiali**
- HumanEval (OpenAI) - 164 problemi Python
- MBPP (Google) - 1000+ problemi entry-level
- HumanEval+ - Test cases estesi

✅ **Inferenza Reale**
- Qwen 2.5-Coder via Ollama
- NO mock/simulazioni
- Output verificabili

✅ **Metriche Scientifiche**
- CodeBLEU score
- AST similarity
- Pass@K metrics
- Execution testing

✅ **Comparazione Rigorosa**
- Qwen standalone vs NEUROGLYPH+Qwen
- Tempi di inferenza reali
- Qualità semantica
- Correttezza funzionale

## 🏗️ ARCHITETTURA

```
neuroglyph/benchmark/
├── run_real_benchmarks.py      # Main orchestrator
├── real_benchmark_runner.py    # Test execution engine
├── benchmark_evaluator.py      # Advanced metrics
├── dataset_downloader.py       # Official datasets
├── datasets/                   # Downloaded datasets
│   ├── humaneval.jsonl
│   ├── mbpp.jsonl
│   └── humaneval_plus.jsonl
└── results/                    # Benchmark results
    ├── real_benchmark_results_*.json
    ├── final_report_*.md
    └── complete_results_*.json
```

## 🚀 QUICK START

### **1. Setup Ollama**

```bash
# Installa Ollama
curl -fsSL https://ollama.ai/install.sh | sh

# Scarica Qwen 2.5-Coder
ollama pull qwen2.5-coder:1.5b

# Verifica installazione
ollama list
```

### **2. Esegui Benchmark**

```bash
cd /Volumes/DANIELE/NEUROGLYPH

# Benchmark rapido (5 test)
python neuroglyph/benchmark/run_real_benchmarks.py

# Benchmark completo (50 test)
python neuroglyph/benchmark/run_real_benchmarks.py --num-samples 50

# Solo download dataset
python neuroglyph/benchmark/dataset_downloader.py
```

### **3. Analizza Risultati**

```bash
# Visualizza ultimo report
cat neuroglyph/benchmark/results/final_report_*.md

# Analizza risultati JSON
python -m json.tool neuroglyph/benchmark/results/complete_results_*.json
```

## 📊 METRICHE IMPLEMENTATE

### **CodeBLEU Score**
- Similarità sintattica e semantica
- Weighted average di BLEU + AST + dataflow
- Range: 0.0 - 1.0

### **AST Similarity**
- Confronto Abstract Syntax Tree
- Jaccard similarity su nodi AST
- Range: 0.0 - 1.0

### **Execution Testing**
- Test funzionalità codice generato
- Sandbox execution con timeout
- Boolean success/failure

### **Pass@K Metrics**
- Percentuale successo su K tentativi
- Standard per valutazione LLM coding
- Range: 0.0 - 1.0

### **Semantic Similarity**
- Similarità semantica avanzata
- Sequence matching + embeddings
- Range: 0.0 - 1.0

## 🧪 DATASET SUPPORTATI

### **HumanEval (OpenAI)**
```json
{
  "task_id": "HumanEval/0",
  "prompt": "def has_close_elements(numbers: List[float], threshold: float) -> bool:\n    \"\"\" Check if in given list of numbers...",
  "canonical_solution": "    for idx, elem in enumerate(numbers):\n        for idx2, elem2 in enumerate(numbers):\n...",
  "test": "def check(candidate):\n    assert candidate([1.0, 2.0, 3.0], 0.5) == False\n..."
}
```

### **MBPP (Google)**
```json
{
  "task_id": 1,
  "text": "Write a function to find the minimum cost path to reach (m, n) from (0, 0) for the given cost matrix cost[][] and a position (m, n) in cost[][].",
  "code": "def min_cost(cost, m, n): \n\tR = 3\n\tC = 3\n\ttc = [[0 for x in range(C)] for x in range(R)] \n...",
  "test_list": ["assert min_cost([[1, 2, 3], [4, 8, 2], [1, 5, 3]], 2, 2) == 8", ...]
}
```

## ⚙️ CONFIGURAZIONE

### **BenchmarkConfig**
```python
config = {
    "model": "qwen2.5-coder:1.5b",      # Modello Ollama
    "datasets": ["humaneval", "mbpp"],   # Dataset da testare
    "num_samples": 10,                   # Numero test per dataset
    "temperature": 0.1,                  # Temperatura generazione
    "max_tokens": 512,                   # Token massimi
    "use_advanced_evaluation": True,     # Metriche avanzate
    "save_detailed_results": True        # Salva risultati dettagliati
}
```

## 📈 INTERPRETAZIONE RISULTATI

### **Success Criteria**
- **Quality Score > 0.7**: Soglia qualità accettabile
- **NEUROGLYPH Improvement > 0**: Miglioramento vs baseline
- **Execution Success > 80%**: Codice funzionante
- **Time Improvement**: Velocità risposta

### **Verdetto Finale**
```
✅ NEUROGLYPH OUTPERFORMS BASELINE
- Quality improvement: +15%
- Faster response: +8%
- Execution success: 85%
→ Pronto per integrazione LLM

⚠️ RISULTATI MISTI
- Quality improvement: +5%
- Slower response: -3%
- Execution success: 70%
→ Necessari miglioramenti
```

## 🔧 TROUBLESHOOTING

### **Ollama Non Disponibile**
```bash
# Verifica servizio
ollama serve

# Test connessione
curl http://localhost:11434/api/version
```

### **Dataset Download Fallito**
```bash
# Download manuale
wget https://raw.githubusercontent.com/openai/human-eval/master/data/HumanEval.jsonl
mv HumanEval.jsonl neuroglyph/benchmark/datasets/humaneval.jsonl
```

### **Errori Memoria**
```python
# Riduci batch size
config["num_samples"] = 5
config["max_tokens"] = 256
```

## 🎯 ROADMAP

### **Fase 1: Validazione Base** ✅ COMPLETATA
- [x] HumanEval integration
- [x] Qwen via Ollama
- [x] Basic metrics
- [x] Report generation
- [x] **PRIMO TEST REALE ESEGUITO** (26/05/2025)

### **Fase 2: Metriche Avanzate** 🚧
- [x] CodeBLEU implementation
- [x] AST similarity
- [x] Execution testing
- [ ] Advanced embeddings

### **Fase 3: Dataset Expansion** 📋
- [ ] MBPP full integration
- [ ] CodeContests
- [ ] APPS dataset
- [ ] Custom NEUROGLYPH tests

### **Fase 4: Production Ready** 🚀
- [ ] CI/CD integration
- [ ] Performance optimization
- [ ] Multi-model support
- [ ] Real-time monitoring

---

*Documentazione aggiornata: 2025-05-26*
