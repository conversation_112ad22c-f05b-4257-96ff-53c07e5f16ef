#!/usr/bin/env python3
"""
🧠 NEUROGLYPH ULTRA - SOCRATE Engine Wrapper
============================================

Wrapper principale che integra tutti i componenti SOCRATE:
- DAG Planner: Costruzione grafi di ragionamento
- Logic Simulator: Validazione logica simbolica
- Symbolic Inference: Inferenze multi-step
- Pattern Recognition: Riconoscimento pattern ricorrenti

Questo è il cuore pensante di NEUROGLYPH che trasforma il ragionamento
da probabilistico a simbolico-logico.

Autore: NEUROGLYPH ULTRA Team
Data: 2025-05-24
"""

import json
import logging
import time
from typing import Dict, List, Any, Optional, Tuple
from dataclasses import dataclass, asdict
from pathlib import Path

# Import componenti SOCRATE
try:
    from .planner import SOCRATEPlanner, ReasoningDAG, build_reasoning_dag
    from .logic_simulator import SOCRATELogicSimulator, SimulationTrace, simulate_dag
except ImportError:
    # Fallback per import diretto
    from planner import SOCRATEPlanner, ReasoningDAG, build_reasoning_dag
    from logic_simulator import SOCRATELogicSimulator, SimulationTrace, simulate_dag

logger = logging.getLogger(__name__)

@dataclass
class SOCRATEResult:
    """Risultato completo dell'elaborazione SOCRATE."""
    input_symbols: List[str]
    reasoning_dag: ReasoningDAG
    simulation_trace: SimulationTrace
    inference_results: Dict[str, Any]
    pattern_analysis: Dict[str, Any]
    final_confidence: float
    processing_time: float
    success: bool
    error_message: Optional[str] = None

class SOCRATEEngine:
    """Motore principale SOCRATE per ragionamento simbolico."""

    def __init__(self):
        self.planner = SOCRATEPlanner()
        self.simulator = SOCRATELogicSimulator()
        self.processing_cache = {}

        # Statistiche di utilizzo
        self.stats = {
            "total_processed": 0,
            "successful_inferences": 0,
            "contradictions_detected": 0,
            "average_confidence": 0.0,
            "average_processing_time": 0.0
        }

        logger.info("🧠 SOCRATE Engine inizializzato")
        logger.info("🎯 Primo LLM pensante pronto per ragionamento simbolico")

    def process_ng_sequence(self, ng_input: List[str], context: Optional[Dict[str, Any]] = None) -> SOCRATEResult:
        """Elabora una sequenza di neuroglifi con ragionamento simbolico completo."""
        start_time = time.time()

        logger.info(f"🧠 SOCRATE: Elaborazione {len(ng_input)} simboli neuroglifi")

        try:
            # Fase 1: Costruzione DAG di ragionamento
            logger.info("📊 Fase 1: Costruzione DAG di ragionamento")
            reasoning_dag = self.planner.build_reasoning_dag(ng_input, context)

            # Fase 2: Simulazione logica
            logger.info("🔬 Fase 2: Simulazione logica simbolica")
            simulation_trace = self.simulator.simulate_dag(reasoning_dag)

            # Fase 3: Inferenze simboliche avanzate
            logger.info("🧮 Fase 3: Inferenze simboliche avanzate")
            inference_results = self._perform_symbolic_inference(reasoning_dag, simulation_trace)

            # Fase 4: Analisi pattern
            logger.info("🔍 Fase 4: Analisi pattern di ragionamento")
            pattern_analysis = self._analyze_reasoning_patterns(reasoning_dag, simulation_trace)

            # Calcola confidenza finale
            final_confidence = self._calculate_final_confidence(reasoning_dag, simulation_trace, inference_results)

            processing_time = time.time() - start_time

            # Crea risultato
            result = SOCRATEResult(
                input_symbols=ng_input,
                reasoning_dag=reasoning_dag,
                simulation_trace=simulation_trace,
                inference_results=inference_results,
                pattern_analysis=pattern_analysis,
                final_confidence=final_confidence,
                processing_time=processing_time,
                success=True
            )

            # Aggiorna statistiche
            self._update_stats(result)

            logger.info(f"✅ SOCRATE: Elaborazione completata in {processing_time:.3f}s "
                       f"(confidenza: {final_confidence:.2f})")

            return result

        except Exception as e:
            processing_time = time.time() - start_time
            error_msg = f"Errore elaborazione SOCRATE: {str(e)}"
            logger.error(error_msg)

            # Risultato di errore
            return SOCRATEResult(
                input_symbols=ng_input,
                reasoning_dag=ReasoningDAG("error", {}, [], [], [], [], 0.0, "error"),
                simulation_trace=SimulationTrace("error", [], "error", 0.0, [], [], 0.0),
                inference_results={},
                pattern_analysis={},
                final_confidence=0.0,
                processing_time=processing_time,
                success=False,
                error_message=error_msg
            )
