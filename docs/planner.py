#!/usr/bin/env python3
"""
🧠 SOCRATE Engine - Symbolic Operations for Cognitive Reasoning and Thinking Engine
==================================================================================

Il cuore pensante di NEUROGLYPH ULTRA che trasforma il ragionamento da probabilistico
a simbolico-logico, rendendo possibile il primo LLM che pensa come un matematico.

Componenti:
- DAG Planner: Costruisce grafi di ragionamento simbolico
- Symbolic Inference: Esegue inferenze logiche sui simboli
- Pattern Recognition: Identifica pattern di ragionamento ricorrenti
- Reasoning Validation: Valida la correttezza logica del ragionamento

Autore: NEUROGLYPH ULTRA Team
Data: 2025-05-24
"""

import json
import logging
from typing import Dict, List, Any, Optional, Tuple, Set
from dataclasses import dataclass
from enum import Enum
from pathlib import Path

# Setup logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class ReasoningType(Enum):
    """Tipi di ragionamento supportati da SOCRATE."""
    DEDUCTION = "deduction"          # A → B, A ⊢ B
    INDUCTION = "induction"          # Pattern → Generalizzazione
    ABDUCTION = "abduction"          # Effetto → Causa più probabile
    ANALOGY = "analogy"              # A:B :: C:D
    CAUSAL = "causal"                # Causa → Effetto
    TEMPORAL = "temporal"            # Prima → Dopo
    SPATIAL = "spatial"              # Relazioni spaziali
    MODAL = "modal"                  # Necessità/Possibilità
    PROBABILISTIC = "probabilistic"  # Incertezza quantificata
    METACOGNITIVE = "metacognitive"  # Ragionamento sul ragionamento

class NodeType(Enum):
    """Tipi di nodi nel DAG di ragionamento."""
    PREMISE = "premise"              # Premessa iniziale
    HYPOTHESIS = "hypothesis"        # Ipotesi da verificare
    INFERENCE = "inference"          # Passo di inferenza
    CONCLUSION = "conclusion"        # Conclusione finale
    CONDITION = "condition"          # Condizione logica
    CONTRADICTION = "contradiction"  # Contraddizione rilevata
    UNCERTAINTY = "uncertainty"      # Nodo di incertezza
    VALIDATION = "validation"        # Validazione logica

@dataclass
class ReasoningNode:
    """Nodo nel DAG di ragionamento simbolico."""
    id: str
    type: NodeType
    symbols: List[str]              # Simboli neuroglifi associati
    content: str                    # Contenuto semantico
    confidence: float               # Confidenza [0,1]
    reasoning_type: ReasoningType   # Tipo di ragionamento
    dependencies: List[str]         # ID nodi dipendenti
    metadata: Dict[str, Any]        # Metadati aggiuntivi

@dataclass
class ReasoningEdge:
    """Arco nel DAG di ragionamento."""
    from_node: str
    to_node: str
    relation_type: str              # Tipo di relazione logica
    strength: float                 # Forza della relazione [0,1]
    symbols: List[str]              # Simboli che rappresentano la relazione

@dataclass
class ReasoningDAG:
    """Grafo Aciclico Diretto per ragionamento simbolico."""
    id: str
    nodes: Dict[str, ReasoningNode]
    edges: List[ReasoningEdge]
    root_nodes: List[str]           # Nodi radice (premesse)
    leaf_nodes: List[str]           # Nodi foglia (conclusioni)
    reasoning_chain: List[str]      # Catena di ragionamento principale
    confidence_score: float         # Confidenza complessiva
    validation_status: str          # Stato validazione logica

class SOCRATEPlanner:
    """Planner principale per costruzione DAG di ragionamento simbolico."""

    def __init__(self):
        self.reasoning_symbols = self._load_reasoning_symbols()
        self.reasoning_patterns = self._load_reasoning_patterns()
        self.dag_cache = {}  # Cache per DAG già costruiti

        logger.info("🧠 SOCRATE Planner inizializzato")
        logger.info(f"📊 Simboli reasoning caricati: {len(self.reasoning_symbols)}")

    def _load_reasoning_symbols(self) -> Dict[str, Dict[str, Any]]:
        """Carica simboli reasoning dal registry."""
        try:
            registry_path = Path("../../core/symbols_registry.json")
            if not registry_path.exists():
                registry_path = Path("core/symbols_registry.json")

            with open(registry_path, 'r', encoding='utf-8') as f:
                registry = json.load(f)

            reasoning_symbols = {}
            for symbol_data in registry.get("approved_symbols", []):
                code = symbol_data.get("code", "")
                if code.startswith("ng:reasoning:"):
                    reasoning_symbols[code] = symbol_data

            return reasoning_symbols

        except Exception as e:
            logger.warning(f"Impossibile caricare simboli reasoning: {e}")
            return {}

    def _load_reasoning_patterns(self) -> Dict[str, List[str]]:
        """Carica pattern di ragionamento predefiniti."""
        return {
            "modus_ponens": ["ng:reasoning:premise", "ng:reasoning:inference", "ng:reasoning:conclusion"],
            "modus_tollens": ["ng:reasoning:premise", "ng:reasoning:contrapositive", "ng:reasoning:conclusion"],
            "syllogism": ["ng:reasoning:premise", "ng:reasoning:premise", "ng:reasoning:conclusion"],
            "induction": ["ng:reasoning:pattern", "ng:reasoning:generalization", "ng:reasoning:hypothesis"],
            "abduction": ["ng:reasoning:observation", "ng:reasoning:hypothesis", "ng:reasoning:explanation"],
            "analogy": ["ng:reasoning:similarity", "ng:reasoning:mapping", "ng:reasoning:inference"],
            "causal_chain": ["ng:reasoning:causality", "ng:reasoning:inference", "ng:reasoning:prediction"],
            "contradiction": ["ng:reasoning:premise", "ng:reasoning:contradiction", "ng:reasoning:error_correction"]
        }

    def build_reasoning_dag(self, ng_input: List[str], context: Optional[Dict[str, Any]] = None) -> ReasoningDAG:
        """Costruisce DAG di ragionamento da sequenza neuroglifi."""
        logger.info(f"🔄 Costruzione DAG da {len(ng_input)} simboli neuroglifi")

        # Genera ID univoco per il DAG
        dag_id = f"dag_{hash(tuple(ng_input))}"

        # Controlla cache
        if dag_id in self.dag_cache:
            logger.info("📋 DAG trovato in cache")
            return self.dag_cache[dag_id]

        # Analizza simboli di input
        symbol_analysis = self._analyze_input_symbols(ng_input)

        # Identifica pattern di ragionamento
        reasoning_patterns = self._identify_reasoning_patterns(symbol_analysis)

        # Costruisce nodi del DAG
        nodes = self._build_dag_nodes(symbol_analysis, reasoning_patterns, context)

        # Costruisce archi del DAG
        edges = self._build_dag_edges(nodes, reasoning_patterns)

        # Identifica nodi radice e foglia
        root_nodes, leaf_nodes = self._identify_root_and_leaf_nodes(nodes, edges)

        # Costruisce catena di ragionamento principale
        reasoning_chain = self._build_reasoning_chain(nodes, edges, root_nodes, leaf_nodes)

        # Calcola confidenza complessiva
        confidence_score = self._calculate_dag_confidence(nodes, edges)

        # Crea DAG
        dag = ReasoningDAG(
            id=dag_id,
            nodes=nodes,
            edges=edges,
            root_nodes=root_nodes,
            leaf_nodes=leaf_nodes,
            reasoning_chain=reasoning_chain,
            confidence_score=confidence_score,
            validation_status="pending"
        )

        # Salva in cache
        self.dag_cache[dag_id] = dag

        logger.info(f"✅ DAG costruito: {len(nodes)} nodi, {len(edges)} archi, confidenza {confidence_score:.2f}")

        return dag

    def _analyze_input_symbols(self, ng_input: List[str]) -> Dict[str, Any]:
        """Analizza simboli neuroglifi di input."""
        analysis = {
            "total_symbols": len(ng_input),
            "reasoning_symbols": [],
            "logic_symbols": [],
            "other_symbols": [],
            "symbol_types": {},
            "semantic_categories": {}
        }

        for symbol in ng_input:
            # Cerca il simbolo nel registry
            symbol_info = None
            for code, info in self.reasoning_symbols.items():
                if info.get("symbol") == symbol:
                    symbol_info = info
                    break

            if symbol_info:
                category = symbol_info.get("category", "unknown")
                code = symbol_info.get("code", "")

                if code.startswith("ng:reasoning:"):
                    analysis["reasoning_symbols"].append(symbol_info)
                elif code.startswith("ng:logic:"):
                    analysis["logic_symbols"].append(symbol_info)
                else:
                    analysis["other_symbols"].append(symbol_info)

                # Categorizza per tipo semantico
                semantic_type = code.split(":")[-1] if ":" in code else "unknown"
                if semantic_type not in analysis["semantic_categories"]:
                    analysis["semantic_categories"][semantic_type] = []
                analysis["semantic_categories"][semantic_type].append(symbol)

                analysis["symbol_types"][symbol] = {
                    "code": code,
                    "category": category,
                    "semantic_type": semantic_type,
                    "description": symbol_info.get("description", "")
                }
            else:
                # Simbolo non riconosciuto
                analysis["other_symbols"].append({"symbol": symbol, "unknown": True})
                analysis["symbol_types"][symbol] = {
                    "code": "unknown",
                    "category": "unknown",
                    "semantic_type": "unknown",
                    "description": "Simbolo non riconosciuto"
                }

        return analysis

    def _identify_reasoning_patterns(self, symbol_analysis: Dict[str, Any]) -> List[Dict[str, Any]]:
        """Identifica pattern di ragionamento nei simboli."""
        identified_patterns = []

        # Estrai tipi semantici presenti
        semantic_types = list(symbol_analysis["semantic_categories"].keys())

        # Controlla ogni pattern predefinito
        for pattern_name, pattern_sequence in self.reasoning_patterns.items():
            pattern_match = self._match_pattern_sequence(semantic_types, pattern_sequence)

            if pattern_match["confidence"] > 0.5:  # Soglia di confidenza
                identified_patterns.append({
                    "name": pattern_name,
                    "sequence": pattern_sequence,
                    "match_confidence": pattern_match["confidence"],
                    "matched_symbols": pattern_match["symbols"],
                    "reasoning_type": self._get_reasoning_type_for_pattern(pattern_name)
                })

        # Se nessun pattern predefinito, crea pattern generico
        if not identified_patterns:
            identified_patterns.append({
                "name": "generic_reasoning",
                "sequence": semantic_types,
                "match_confidence": 0.7,
                "matched_symbols": list(symbol_analysis["symbol_types"].keys()),
                "reasoning_type": ReasoningType.DEDUCTION
            })

        return identified_patterns

    def _match_pattern_sequence(self, semantic_types: List[str], pattern_sequence: List[str]) -> Dict[str, Any]:
        """Verifica corrispondenza con sequenza pattern."""
        # Estrai tipi semantici dai codici pattern
        pattern_types = [code.split(":")[-1] for code in pattern_sequence if ":" in code]

        # Calcola overlap
        matched_types = set(semantic_types) & set(pattern_types)
        total_types = set(semantic_types) | set(pattern_types)

        if len(total_types) == 0:
            confidence = 0.0
        else:
            confidence = len(matched_types) / len(total_types)

        return {
            "confidence": confidence,
            "symbols": list(matched_types),
            "missing_types": list(set(pattern_types) - set(semantic_types))
        }

    def _get_reasoning_type_for_pattern(self, pattern_name: str) -> ReasoningType:
        """Mappa nome pattern a tipo di ragionamento."""
        pattern_mapping = {
            "modus_ponens": ReasoningType.DEDUCTION,
            "modus_tollens": ReasoningType.DEDUCTION,
            "syllogism": ReasoningType.DEDUCTION,
            "induction": ReasoningType.INDUCTION,
            "abduction": ReasoningType.ABDUCTION,
            "analogy": ReasoningType.ANALOGY,
            "causal_chain": ReasoningType.CAUSAL,
            "contradiction": ReasoningType.DEDUCTION
        }
        return pattern_mapping.get(pattern_name, ReasoningType.DEDUCTION)

    def _build_dag_nodes(self, symbol_analysis: Dict[str, Any], reasoning_patterns: List[Dict[str, Any]],
                        context: Optional[Dict[str, Any]]) -> Dict[str, ReasoningNode]:
        """Costruisce nodi del DAG di ragionamento."""
        nodes = {}
        node_counter = 0

        # Crea nodi per ogni simbolo reasoning
        for symbol_info in symbol_analysis["reasoning_symbols"]:
            node_id = f"node_{node_counter}"
            node_counter += 1

            # Determina tipo di nodo basato sul tipo semantico
            semantic_type = symbol_info.get("code", "").split(":")[-1]
            node_type = self._determine_node_type(semantic_type)

            # Determina tipo di ragionamento
            reasoning_type = self._determine_reasoning_type(semantic_type, reasoning_patterns)

            # Calcola confidenza basata su pattern match
            confidence = self._calculate_node_confidence(symbol_info, reasoning_patterns)

            node = ReasoningNode(
                id=node_id,
                type=node_type,
                symbols=[symbol_info.get("symbol", "")],
                content=symbol_info.get("description", ""),
                confidence=confidence,
                reasoning_type=reasoning_type,
                dependencies=[],
                metadata={
                    "symbol_code": symbol_info.get("code", ""),
                    "symbol_id": symbol_info.get("id", ""),
                    "semantic_type": semantic_type
                }
            )

            nodes[node_id] = node

        # Se non ci sono simboli reasoning, crea nodi generici
        if not nodes and symbol_analysis["other_symbols"]:
            for i, symbol_info in enumerate(symbol_analysis["other_symbols"][:3]):  # Max 3 nodi generici
                node_id = f"generic_node_{i}"

                node = ReasoningNode(
                    id=node_id,
                    type=NodeType.PREMISE,
                    symbols=[symbol_info.get("symbol", "")],
                    content=symbol_info.get("description", "Nodo generico"),
                    confidence=0.6,
                    reasoning_type=ReasoningType.DEDUCTION,
                    dependencies=[],
                    metadata={"generic": True}
                )

                nodes[node_id] = node

        return nodes

    def _determine_node_type(self, semantic_type: str) -> NodeType:
        """Determina tipo di nodo basato sul tipo semantico."""
        type_mapping = {
            "premise": NodeType.PREMISE,
            "hypothesis": NodeType.HYPOTHESIS,
            "inference": NodeType.INFERENCE,
            "conclusion": NodeType.CONCLUSION,
            "deduction": NodeType.INFERENCE,
            "induction": NodeType.INFERENCE,
            "abduction": NodeType.HYPOTHESIS,
            "contradiction": NodeType.CONTRADICTION,
            "validation": NodeType.VALIDATION,
            "analysis": NodeType.INFERENCE,
            "synthesis": NodeType.INFERENCE,
            "evaluation": NodeType.VALIDATION
        }
        return type_mapping.get(semantic_type, NodeType.PREMISE)

    def _determine_reasoning_type(self, semantic_type: str, reasoning_patterns: List[Dict[str, Any]]) -> ReasoningType:
        """Determina tipo di ragionamento basato su semantica e pattern."""
        # Prima controlla i pattern identificati
        for pattern in reasoning_patterns:
            if semantic_type in pattern.get("matched_symbols", []):
                return pattern.get("reasoning_type", ReasoningType.DEDUCTION)

        # Fallback basato su tipo semantico
        type_mapping = {
            "deduction": ReasoningType.DEDUCTION,
            "induction": ReasoningType.INDUCTION,
            "abduction": ReasoningType.ABDUCTION,
            "analogy": ReasoningType.ANALOGY,
            "causality": ReasoningType.CAUSAL,
            "similarity": ReasoningType.ANALOGY,
            "pattern": ReasoningType.INDUCTION,
            "generalization": ReasoningType.INDUCTION,
            "specialization": ReasoningType.DEDUCTION,
            "metacognition": ReasoningType.METACOGNITIVE
        }
        return type_mapping.get(semantic_type, ReasoningType.DEDUCTION)

    def _calculate_node_confidence(self, symbol_info: Dict[str, Any], reasoning_patterns: List[Dict[str, Any]]) -> float:
        """Calcola confidenza del nodo."""
        base_confidence = 0.7

        # Bonus se il simbolo è in un pattern riconosciuto
        pattern_bonus = 0.0
        for pattern in reasoning_patterns:
            if symbol_info.get("symbol") in pattern.get("matched_symbols", []):
                pattern_bonus = pattern.get("match_confidence", 0.0) * 0.2
                break

        # Bonus per simboli con alta qualità
        quality_score = symbol_info.get("validation_score", 0) / 100.0
        quality_bonus = quality_score * 0.1

        return min(1.0, base_confidence + pattern_bonus + quality_bonus)

    def _build_dag_edges(self, nodes: Dict[str, ReasoningNode], reasoning_patterns: List[Dict[str, Any]]) -> List[ReasoningEdge]:
        """Costruisce archi del DAG di ragionamento."""
        edges = []

        # Converti nodi in lista per iterazione
        node_list = list(nodes.values())

        # Crea archi basati su pattern di ragionamento
        for pattern in reasoning_patterns:
            pattern_nodes = self._find_nodes_for_pattern(node_list, pattern)
            pattern_edges = self._create_edges_for_pattern(pattern_nodes, pattern)
            edges.extend(pattern_edges)

        # Se non ci sono pattern, crea archi sequenziali semplici
        if not edges and len(node_list) > 1:
            for i in range(len(node_list) - 1):
                edge = ReasoningEdge(
                    from_node=node_list[i].id,
                    to_node=node_list[i + 1].id,
                    relation_type="sequential",
                    strength=0.7,
                    symbols=[]
                )
                edges.append(edge)

        return edges

    def _find_nodes_for_pattern(self, nodes: List[ReasoningNode], pattern: Dict[str, Any]) -> List[ReasoningNode]:
        """Trova nodi che corrispondono a un pattern."""
        pattern_nodes = []
        matched_symbols = pattern.get("matched_symbols", [])

        for node in nodes:
            if any(symbol in matched_symbols for symbol in node.symbols):
                pattern_nodes.append(node)

        return pattern_nodes

    def _create_edges_for_pattern(self, pattern_nodes: List[ReasoningNode], pattern: Dict[str, Any]]) -> List[ReasoningEdge]:
        """Crea archi per un pattern specifico."""
        edges = []
        pattern_name = pattern.get("name", "")

        if pattern_name == "modus_ponens" and len(pattern_nodes) >= 2:
            # Premessa → Conclusione
            edges.append(ReasoningEdge(
                from_node=pattern_nodes[0].id,
                to_node=pattern_nodes[1].id,
                relation_type="modus_ponens",
                strength=0.9,
                symbols=["→"]
            ))

        elif pattern_name == "syllogism" and len(pattern_nodes) >= 3:
            # Premessa1 → Premessa2 → Conclusione
            edges.append(ReasoningEdge(
                from_node=pattern_nodes[0].id,
                to_node=pattern_nodes[2].id,
                relation_type="syllogism_major",
                strength=0.8,
                symbols=["→"]
            ))
            edges.append(ReasoningEdge(
                from_node=pattern_nodes[1].id,
                to_node=pattern_nodes[2].id,
                relation_type="syllogism_minor",
                strength=0.8,
                symbols=["→"]
            ))

        elif pattern_name == "causal_chain":
            # Catena causale sequenziale
            for i in range(len(pattern_nodes) - 1):
                edges.append(ReasoningEdge(
                    from_node=pattern_nodes[i].id,
                    to_node=pattern_nodes[i + 1].id,
                    relation_type="causal",
                    strength=0.85,
                    symbols=["⇒"]
                ))

        else:
            # Pattern generico: connessioni sequenziali
            for i in range(len(pattern_nodes) - 1):
                edges.append(ReasoningEdge(
                    from_node=pattern_nodes[i].id,
                    to_node=pattern_nodes[i + 1].id,
                    relation_type="inference",
                    strength=0.7,
                    symbols=["→"]
                ))

        return edges

    def _identify_root_and_leaf_nodes(self, nodes: Dict[str, ReasoningNode], edges: List[ReasoningEdge]) -> tuple:
        """Identifica nodi radice (senza predecessori) e foglia (senza successori)."""
        # Trova nodi con archi in entrata e in uscita
        nodes_with_incoming = set(edge.to_node for edge in edges)
        nodes_with_outgoing = set(edge.from_node for edge in edges)

        all_node_ids = set(nodes.keys())

        # Nodi radice: nessun arco in entrata
        root_nodes = list(all_node_ids - nodes_with_incoming)

        # Nodi foglia: nessun arco in uscita
        leaf_nodes = list(all_node_ids - nodes_with_outgoing)

        # Se non ci sono archi, il primo nodo è radice, l'ultimo è foglia
        if not edges and nodes:
            node_ids = list(nodes.keys())
            root_nodes = [node_ids[0]] if node_ids else []
            leaf_nodes = [node_ids[-1]] if node_ids else []

        return root_nodes, leaf_nodes

    def _build_reasoning_chain(self, nodes: Dict[str, ReasoningNode], edges: List[ReasoningEdge],
                              root_nodes: List[str], leaf_nodes: List[str]) -> List[str]:
        """Costruisce catena di ragionamento principale."""
        if not root_nodes or not leaf_nodes:
            return list(nodes.keys())

        # Trova il percorso più lungo da radice a foglia
        longest_path = []

        for root in root_nodes:
            for leaf in leaf_nodes:
                path = self._find_path(root, leaf, edges)
                if len(path) > len(longest_path):
                    longest_path = path

        return longest_path if longest_path else list(nodes.keys())

    def _find_path(self, start: str, end: str, edges: List[ReasoningEdge]) -> List[str]:
        """Trova percorso da nodo start a nodo end."""
        # Costruisci grafo di adiacenza
        graph = {}
        for edge in edges:
            if edge.from_node not in graph:
                graph[edge.from_node] = []
            graph[edge.from_node].append(edge.to_node)

        # DFS per trovare percorso
        def dfs(current: str, target: str, path: List[str], visited: set) -> List[str]:
            if current == target:
                return path + [current]

            if current in visited:
                return []

            visited.add(current)

            for neighbor in graph.get(current, []):
                result = dfs(neighbor, target, path + [current], visited.copy())
                if result:
                    return result

            return []

        return dfs(start, end, [], set())

    def _calculate_dag_confidence(self, nodes: Dict[str, ReasoningNode], edges: List[ReasoningEdge]) -> float:
        """Calcola confidenza complessiva del DAG."""
        if not nodes:
            return 0.0

        # Media delle confidenze dei nodi
        node_confidences = [node.confidence for node in nodes.values()]
        avg_node_confidence = sum(node_confidences) / len(node_confidences)

        # Media delle forze degli archi
        if edges:
            edge_strengths = [edge.strength for edge in edges]
            avg_edge_strength = sum(edge_strengths) / len(edge_strengths)
        else:
            avg_edge_strength = 0.5  # Default per DAG senza archi

        # Bonus per struttura ben formata
        structure_bonus = 0.0
        if len(edges) > 0 and len(nodes) > 1:
            structure_bonus = 0.1

        # Confidenza finale
        final_confidence = (avg_node_confidence * 0.6 + avg_edge_strength * 0.3 + structure_bonus)

        return min(1.0, final_confidence)


# Funzioni di utilità per uso esterno
def build_reasoning_dag(ng_input: List[str], context: Optional[Dict[str, Any]] = None) -> ReasoningDAG:
    """Funzione di convenienza per costruire DAG di ragionamento."""
    planner = SOCRATEPlanner()
    return planner.build_reasoning_dag(ng_input, context)


def analyze_reasoning_symbols(ng_input: List[str]) -> Dict[str, Any]:
    """Analizza simboli di ragionamento in una sequenza neuroglifi."""
    planner = SOCRATEPlanner()
    return planner._analyze_input_symbols(ng_input)
