# 🚀 QLoRA FINE-TUNING GUIDE CON UNSLOTH

**Guida completa per fine-tuning NEUROGLYPH LLM basata su best practices**

---

## 📋 OVERVIEW

Questa guida ti porta attraverso il processo completo di fine-tuning di un LLM usando:
- **QLoRA** (Quantized Low-Rank Adaptation)
- **Unsloth** (framework ottimizzato)
- **4-bit quantization** per efficienza memoria
- **Chat template** per modelli conversazionali

## 🎯 OBIETTIVO

Creare **NEUROGLYPH LLM** - il primo LLM simbolico nativo che:
- Comprende simboli Unicode nativamente
- Genera codice con compressione semantica
- Mantiene reversibilità simbolica perfetta

---

## 📦 FASE 1: SETUP AMBIENTE

### **1.1 Installazione Dipendenze**

```python
# Installa Unsloth e dipendenze
!pip install "unsloth[colab-new] @ git+https://github.com/unslothai/unsloth.git"
!pip install --no-deps xformers trl peft accelerate bitsandbytes

# Import librerie essenziali
import torch
from unsloth import FastLanguageModel
from datasets import Dataset
import json
import gzip
```

### **1.2 Configurazione GPU**

```python
# Verifica GPU disponibile
print(f"GPU disponibile: {torch.cuda.is_available()}")
print(f"GPU name: {torch.cuda.get_device_name(0) if torch.cuda.is_available() else 'CPU'}")

# Libera memoria GPU
torch.cuda.empty_cache()
```

---

## 🤖 FASE 2: CARICAMENTO MODELLO BASE

### **2.1 Configurazione Modello**

```python
# Configurazione modello base
model_name = "unsloth/Qwen2.5-Coder-1.5B-Instruct-bnb-4bit"
max_seq_length = 2048  # Lunghezza sequenza massima
dtype = None  # Auto-detect
load_in_4bit = True  # Quantizzazione 4-bit

# Carica modello e tokenizer
model, tokenizer = FastLanguageModel.from_pretrained(
    model_name=model_name,
    max_seq_length=max_seq_length,
    dtype=dtype,
    load_in_4bit=load_in_4bit,
    # token="hf_..." # Aggiungi se modello privato
)
```

### **2.2 Verifica Modello**

```python
# Verifica dimensioni modello
print(f"Modello caricato: {model_name}")
print(f"Parametri totali: {model.num_parameters():,}")
print(f"Memoria GPU utilizzata: {torch.cuda.memory_allocated() / 1024**3:.2f} GB")

# Test tokenizer
test_text = "Hello, how are you?"
tokens = tokenizer.encode(test_text)
print(f"Test tokenization: {len(tokens)} tokens")
```

---

## ⚙️ FASE 3: CONFIGURAZIONE QLORA

### **3.1 Setup LoRA Adapters**

```python
# Configurazione QLoRA
model = FastLanguageModel.get_peft_model(
    model,
    r=16,  # Rank delle matrici LoRA
    target_modules=[
        "q_proj", "k_proj", "v_proj", "o_proj",
        "gate_proj", "up_proj", "down_proj"
    ],
    lora_alpha=16,  # Scaling factor
    lora_dropout=0.1,  # Dropout per regolarizzazione
    bias="none",  # Bias handling
    use_gradient_checkpointing="unsloth",  # Ottimizzazione memoria
    random_state=3407,  # Seed per riproducibilità
    use_rslora=False,  # RSLoRA (opzionale)
    loftq_config=None,  # LoftQ (opzionale)
)

# Verifica parametri trainable
trainable_params = sum(p.numel() for p in model.parameters() if p.requires_grad)
total_params = sum(p.numel() for p in model.parameters())
print(f"Parametri trainable: {trainable_params:,} ({trainable_params/total_params*100:.2f}%)")
```

---

## 📊 FASE 4: PREPARAZIONE DATASET

### **4.1 Caricamento Dataset NEUROGLYPH**

```python
# Carica dataset ULTRA compressi
import gzip
import json

def load_neuroglyph_dataset():
    """Carica dataset NEUROGLYPH ULTRA."""
    
    # Carica training data
    with gzip.open('neuroglyph_training_unsloth_ULTRA.jsonl.gz', 'rt') as f:
        training_data = [json.loads(line) for line in f]
    
    # Carica validation data
    with gzip.open('neuroglyph_validation_unsloth_ULTRA.jsonl.gz', 'rt') as f:
        validation_data = [json.loads(line) for line in f]
    
    print(f"📊 Training examples: {len(training_data)}")
    print(f"📊 Validation examples: {len(validation_data)}")
    
    return training_data, validation_data

# Carica dataset
training_data, validation_data = load_neuroglyph_dataset()
```

### **4.2 Applicazione Chat Template**

```python
# Template per conversazioni
chat_template = """<|im_start|>system
You are NEUROGLYPH, an AI that understands and generates code using symbolic notation. Use Unicode symbols like ⊃, ∧, ∨, →, ∑, ∫ to represent logical and mathematical concepts in code.
<|im_end|>
<|im_start|>user
{question}
<|im_end|>
<|im_start|>assistant
{answer}
<|im_end|>"""

def format_chat_example(example):
    """Formatta esempio per chat template."""
    return chat_template.format(
        question=example["question"],
        answer=example["answer"]
    )

# Applica template a dataset
def prepare_dataset(data):
    """Prepara dataset con chat template."""
    formatted_data = []
    
    for example in data:
        formatted_text = format_chat_example(example)
        formatted_data.append({"text": formatted_text})
    
    return Dataset.from_list(formatted_data)

# Prepara datasets
train_dataset = prepare_dataset(training_data)
eval_dataset = prepare_dataset(validation_data)

print(f"✅ Dataset preparati: {len(train_dataset)} train, {len(eval_dataset)} eval")
```

### **4.3 Visualizzazione Esempio**

```python
# Mostra esempio formattato
print("📝 ESEMPIO DATASET FORMATTATO:")
print("=" * 50)
print(train_dataset[0]["text"])
print("=" * 50)
```

---

## 🏋️ FASE 5: CONFIGURAZIONE TRAINING

### **5.1 Training Arguments**

```python
from transformers import TrainingArguments
from trl import SFTTrainer

# Configurazione training
training_args = TrainingArguments(
    # Output e logging
    output_dir="./neuroglyph_training_output",
    logging_dir="./logs",
    logging_steps=10,
    
    # Training parameters
    num_train_epochs=2,
    per_device_train_batch_size=2,
    per_device_eval_batch_size=4,
    gradient_accumulation_steps=8,  # Effective batch size = 16
    
    # Optimization
    learning_rate=2e-4,
    weight_decay=0.01,
    lr_scheduler_type="cosine",
    warmup_steps=100,
    
    # Evaluation
    eval_strategy="steps",
    eval_steps=100,
    save_steps=100,
    save_total_limit=3,
    
    # Memory optimization
    dataloader_num_workers=2,
    remove_unused_columns=False,
    
    # Monitoring
    report_to="none",  # Disabilita wandb per semplicità
    run_name="neuroglyph_training",
)
```

### **5.2 Setup Trainer**

```python
# Inizializza trainer
trainer = SFTTrainer(
    model=model,
    tokenizer=tokenizer,
    train_dataset=train_dataset,
    eval_dataset=eval_dataset,
    dataset_text_field="text",
    max_seq_length=max_seq_length,
    args=training_args,
    packing=False,  # Non impacchettare sequenze
)

# Verifica setup
print("✅ Trainer configurato")
print(f"📊 Training examples: {len(train_dataset)}")
print(f"📊 Evaluation examples: {len(eval_dataset)}")
```

---

## 🚀 FASE 6: TRAINING

### **6.1 Avvio Training**

```python
# Mostra statistiche pre-training
print("📊 STATISTICHE PRE-TRAINING:")
print(f"GPU memory allocated: {torch.cuda.memory_allocated() / 1024**3:.2f} GB")
print(f"GPU memory cached: {torch.cuda.memory_reserved() / 1024**3:.2f} GB")

# Avvia training
print("\n🚀 AVVIO TRAINING...")
trainer.train()

print("\n✅ TRAINING COMPLETATO!")
```

### **6.2 Monitoring Training**

```python
# Durante il training, monitora:
# - Loss che diminuisce
# - GPU memory usage stabile
# - Tempo per step ragionevole

# Esempio output atteso:
# Step 10: loss=1.234, lr=1.5e-4
# Step 20: loss=1.123, lr=1.8e-4
# ...
# Step 100: loss=0.456, lr=2.0e-4
```

---

## 🧪 FASE 7: TESTING E VALIDAZIONE

### **7.1 Test Modello Fine-tuned**

```python
# Abilita modalità inferenza
FastLanguageModel.for_inference(model)

def test_neuroglyph_model(prompt):
    """Testa il modello NEUROGLYPH."""
    
    # Formatta prompt
    messages = [
        {"role": "system", "content": "You are NEUROGLYPH, an AI that understands symbolic notation."},
        {"role": "user", "content": prompt}
    ]
    
    # Applica chat template
    formatted_prompt = tokenizer.apply_chat_template(
        messages, 
        tokenize=False, 
        add_generation_prompt=True
    )
    
    # Genera risposta
    inputs = tokenizer(formatted_prompt, return_tensors="pt").to("cuda")
    
    with torch.no_grad():
        outputs = model.generate(
            **inputs,
            max_new_tokens=256,
            temperature=0.7,
            do_sample=True,
            pad_token_id=tokenizer.eos_token_id
        )
    
    # Decodifica risposta
    response = tokenizer.decode(outputs[0][inputs.input_ids.shape[1]:], skip_special_tokens=True)
    return response

# Test esempi
test_prompts = [
    "Using symbols ⊃ and ∧, implement logical implication",
    "Create a function that uses ∑ for mathematical summation",
    "Implement conditional logic with → symbol"
]

print("🧪 TESTING NEUROGLYPH MODEL:")
print("=" * 50)

for i, prompt in enumerate(test_prompts, 1):
    print(f"\n📝 Test {i}: {prompt}")
    response = test_neuroglyph_model(prompt)
    print(f"🤖 Response: {response}")
    print("-" * 30)
```

### **7.2 Validazione Simbolica**

```python
def validate_symbolic_understanding():
    """Valida comprensione simbolica del modello."""
    
    # Test simboli critici
    symbols_test = {
        "⊃": "logical implication",
        "∧": "logical conjunction", 
        "∨": "logical disjunction",
        "→": "directional flow",
        "∑": "mathematical summation",
        "∫": "mathematical integration"
    }
    
    results = {}
    
    for symbol, meaning in symbols_test.items():
        prompt = f"What does the symbol '{symbol}' mean in programming context?"
        response = test_neuroglyph_model(prompt)
        
        # Check se risposta contiene il significato corretto
        contains_meaning = meaning.lower() in response.lower()
        results[symbol] = {
            "response": response,
            "correct": contains_meaning
        }
    
    # Calcola accuracy
    correct_count = sum(1 for r in results.values() if r["correct"])
    accuracy = correct_count / len(results)
    
    print(f"🎯 Symbolic Understanding Accuracy: {accuracy:.1%}")
    return results

# Esegui validazione
validation_results = validate_symbolic_understanding()
```

---

## 💾 FASE 8: SALVATAGGIO MODELLO

### **8.1 Salva LoRA Adapters**

```python
# Salva solo gli adapters LoRA (leggeri)
model.save_pretrained("neuroglyph_lora_adapters")
tokenizer.save_pretrained("neuroglyph_lora_adapters")

print("✅ LoRA adapters salvati in: neuroglyph_lora_adapters/")
```

### **8.2 Merge e Salva Modello Completo**

```python
# Merge LoRA con modello base
model = FastLanguageModel.for_inference(model)

# Salva modello merged
model.save_pretrained_merged(
    "neuroglyph_merged_model",
    tokenizer,
    save_method="merged_16bit"  # o "merged_4bit" per versione quantizzata
)

print("✅ Modello completo salvato in: neuroglyph_merged_model/")
```

### **8.3 Export per Deployment**

```python
# Export in formato GGUF per deployment locale
model.save_pretrained_gguf(
    "neuroglyph_gguf",
    tokenizer,
    quantization_method="q4_k_m"  # Quantizzazione 4-bit
)

print("✅ Modello GGUF salvato in: neuroglyph_gguf/")
```

---

## 📊 FASE 9: VALUTAZIONE PERFORMANCE

### **9.1 Metriche di Successo**

```python
def evaluate_neuroglyph_performance():
    """Valuta performance complete NEUROGLYPH."""
    
    metrics = {
        "symbolic_accuracy": 0,
        "code_quality": 0,
        "response_coherence": 0,
        "symbol_usage": 0
    }
    
    # Test set di validazione
    test_cases = [
        {
            "input": "Implement logical AND using ∧ symbol",
            "expected_symbols": ["∧"],
            "expected_concept": "logical conjunction"
        },
        {
            "input": "Create summation function with ∑",
            "expected_symbols": ["∑"],
            "expected_concept": "mathematical summation"
        }
    ]
    
    total_score = 0
    
    for case in test_cases:
        response = test_neuroglyph_model(case["input"])
        
        # Check presenza simboli
        symbols_present = all(sym in response for sym in case["expected_symbols"])
        
        # Check concetto corretto
        concept_present = case["expected_concept"].lower() in response.lower()
        
        # Check qualità codice (presenza def, return, etc.)
        code_quality = "def " in response and ("return" in response or ":" in response)
        
        case_score = sum([symbols_present, concept_present, code_quality]) / 3
        total_score += case_score
        
        print(f"📝 Test: {case['input'][:30]}...")
        print(f"   Symbols: {'✅' if symbols_present else '❌'}")
        print(f"   Concept: {'✅' if concept_present else '❌'}")
        print(f"   Code Quality: {'✅' if code_quality else '❌'}")
        print(f"   Score: {case_score:.1%}\n")
    
    overall_score = total_score / len(test_cases)
    print(f"🎯 OVERALL NEUROGLYPH SCORE: {overall_score:.1%}")
    
    return overall_score

# Esegui valutazione
final_score = evaluate_neuroglyph_performance()
```

---

## 🎯 CRITERI DI SUCCESSO

### **Minimum Viable Success (MVP):**
- ✅ Training completa senza errori
- ✅ Symbolic accuracy ≥ 70%
- ✅ Modello genera codice valido
- ✅ Risponde in formato corretto

### **Target Success:**
- ✅ Symbolic accuracy ≥ 85%
- ✅ Usa simboli appropriati nel contesto
- ✅ Codice generato funzionale
- ✅ Spiegazioni coerenti

### **Breakthrough Success:**
- ✅ Symbolic accuracy ≥ 95%
- ✅ Compressione semantica evidente
- ✅ Reasoning simbolico avanzato
- ✅ Creatività nell'uso simboli

---

## 🚨 TROUBLESHOOTING

### **Problemi Comuni:**

**1. Out of Memory Error**
```python
# Riduci batch size
per_device_train_batch_size = 1
gradient_accumulation_steps = 16

# Abilita gradient checkpointing
use_gradient_checkpointing = True
```

**2. Loss non converge**
```python
# Riduci learning rate
learning_rate = 1e-4

# Aumenta warmup
warmup_steps = 200
```

**3. Simboli non riconosciuti**
```python
# Verifica tokenizer
for symbol in ["⊃", "∧", "∨"]:
    tokens = tokenizer.encode(symbol)
    print(f"{symbol}: {len(tokens)} tokens")
```

---

**🚀 NEUROGLYPH LLM TRAINING GUIDE COMPLETA! 🚀**
