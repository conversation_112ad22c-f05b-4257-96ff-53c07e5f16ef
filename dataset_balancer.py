#!/usr/bin/env python3
"""
NEUROGLYPH Dataset Balancer - Multi-hop Depth Optimization
==========================================================

Strumento chirurgico per bilanciare profondità multi-hop nel dataset SUPREME:
- Attuale: tutti a 12 step (massimo)
- Target: 30% a 3-5 step, 50% a 6-8, 20% a 9-12
- Obiettivo: LLM pensante con complessità scalabile

Autore: NEUROGLYPH Perfection Team
Data: 2025-06-01
"""

import json
import re
import random
from typing import List, Dict, Any, Tuple
from datetime import datetime

class NeuroglyphDatasetBalancer:
    """
    Balancer chirurgico per ottimizzazione multi-hop depth.
    
    Riduce step di ragionamento mantenendo coerenza logica e qualità.
    """
    
    def __init__(self):
        self.target_distribution = {
            '3-5': 0.30,  # 30% esempi semplici
            '6-8': 0.50,  # 50% esempi medi
            '9-12': 0.20  # 20% esempi complessi
        }
        
        # Pattern per identificare step di ragionamento
        self.step_patterns = [
            r'\d+\.\s+',
            r'Step\s+\d+:',
            r'Passaggio\s+\d+:',
            r'→\s+',
            r'⊢\s+',
            r'∴\s+'
        ]
        
        # Connettori logici da preservare
        self.logical_connectors = ['⊢', '∴', '→', '∧', '∨', '≈', '∀', '∃']
        
    def balance_dataset(self, dataset_path: str, output_path: str) -> Dict[str, Any]:
        """Bilancia dataset per distribuzione multi-hop ottimale."""
        
        print("🔧 NEUROGLYPH Dataset Balancer - Multi-hop Optimization")
        print("=" * 60)
        
        # Carica dataset
        with open(dataset_path, 'r', encoding='utf-8') as f:
            dataset = json.load(f)
        
        examples = dataset['examples']
        total_examples = len(examples)
        
        print(f"📊 Dataset loaded: {total_examples} examples")
        
        # Calcola target counts
        target_counts = {
            '3-5': int(total_examples * self.target_distribution['3-5']),
            '6-8': int(total_examples * self.target_distribution['6-8']),
            '9-12': int(total_examples * self.target_distribution['9-12'])
        }
        
        print(f"🎯 Target distribution:")
        for range_key, count in target_counts.items():
            percentage = count / total_examples * 100
            print(f"   {range_key} steps: {count} examples ({percentage:.1f}%)")
        
        # Bilancia esempi
        balanced_examples = []
        range_counts = {'3-5': 0, '6-8': 0, '9-12': 0}
        
        # Shuffle per distribuzione casuale
        shuffled_examples = examples.copy()
        random.shuffle(shuffled_examples)
        
        for example in shuffled_examples:
            # Determina range target per questo esempio
            target_range = self._get_target_range(range_counts, target_counts)
            
            if target_range:
                # Riduce step al range target
                balanced_example = self._reduce_steps_to_range(example, target_range)
                balanced_examples.append(balanced_example)
                range_counts[target_range] += 1
                
                if len(balanced_examples) % 100 == 0:
                    print(f"   ✅ Balanced {len(balanced_examples)}/{total_examples} examples")
        
        # Aggiorna dataset
        dataset['examples'] = balanced_examples
        dataset['statistics']['multi_hop_distribution'] = range_counts
        dataset['statistics']['balancing_timestamp'] = datetime.now().isoformat()
        
        # Salva dataset bilanciato
        with open(output_path, 'w', encoding='utf-8') as f:
            json.dump(dataset, f, indent=2, ensure_ascii=False)
        
        # Report finale
        balancing_report = {
            "original_examples": total_examples,
            "balanced_examples": len(balanced_examples),
            "target_distribution": self.target_distribution,
            "achieved_distribution": {
                range_key: count / len(balanced_examples) 
                for range_key, count in range_counts.items()
            },
            "range_counts": range_counts,
            "output_path": output_path
        }
        
        self._print_balancing_summary(balancing_report)
        
        return balancing_report
    
    def _get_target_range(self, current_counts: Dict[str, int], 
                         target_counts: Dict[str, int]) -> str:
        """Determina range target per prossimo esempio."""
        
        for range_key in ['3-5', '6-8', '9-12']:
            if current_counts[range_key] < target_counts[range_key]:
                return range_key
        
        return None  # Tutti i target raggiunti
    
    def _reduce_steps_to_range(self, example: Dict[str, Any], target_range: str) -> Dict[str, Any]:
        """Riduce step di ragionamento al range target."""
        
        # Copia esempio
        balanced_example = example.copy()
        output = example['output']
        
        # Determina target step count
        if target_range == '3-5':
            target_steps = random.randint(3, 5)
        elif target_range == '6-8':
            target_steps = random.randint(6, 8)
        else:  # '9-12'
            target_steps = random.randint(9, 12)
        
        # Riduce step mantenendo coerenza
        reduced_output = self._reduce_reasoning_steps(output, target_steps)
        
        # Aggiorna esempio
        balanced_example['output'] = reduced_output
        balanced_example['metadata']['reasoning_steps'] = target_steps
        balanced_example['metadata']['multi_hop_range'] = target_range
        balanced_example['metadata']['balanced_timestamp'] = datetime.now().isoformat()
        
        return balanced_example
    
    def _reduce_reasoning_steps(self, text: str, target_steps: int) -> str:
        """Riduce step di ragionamento mantenendo coerenza logica."""
        
        lines = text.split('\n')
        
        # Identifica sezioni
        header_lines = []
        step_lines = []
        conclusion_lines = []
        
        current_section = 'header'
        
        for line in lines:
            if any(re.search(pattern, line) for pattern in self.step_patterns):
                current_section = 'steps'
                step_lines.append(line)
            elif current_section == 'steps' and ('conclusione' in line.lower() or 
                                                'risultato' in line.lower() or
                                                '✅' in line):
                current_section = 'conclusion'
                conclusion_lines.append(line)
            elif current_section == 'header':
                header_lines.append(line)
            elif current_section == 'conclusion':
                conclusion_lines.append(line)
            else:
                step_lines.append(line)
        
        # Seleziona step rappresentativi
        if len(step_lines) > target_steps:
            # Mantieni primo, ultimo e distribuisci uniformemente gli altri
            selected_steps = []
            
            if target_steps >= 2:
                selected_steps.append(step_lines[0])  # Primo step
                
                if target_steps > 2:
                    # Step intermedi distribuiti uniformemente
                    middle_count = target_steps - 2
                    if middle_count > 0:
                        step_indices = []
                        for i in range(1, middle_count + 1):
                            index = int(i * (len(step_lines) - 1) / (middle_count + 1))
                            step_indices.append(index)
                        
                        for idx in step_indices:
                            if idx < len(step_lines):
                                selected_steps.append(step_lines[idx])
                
                selected_steps.append(step_lines[-1])  # Ultimo step
            else:
                # Solo un step
                selected_steps = [step_lines[0]]
            
            # Rinumera step
            renumbered_steps = []
            for i, step in enumerate(selected_steps, 1):
                # Sostituisci numero step
                renumbered_step = re.sub(r'^\d+\.', f'{i}.', step)
                renumbered_steps.append(renumbered_step)
            
            step_lines = renumbered_steps
        
        # Ricostruisci testo
        result_lines = header_lines + step_lines + conclusion_lines
        
        # Pulisci linee vuote eccessive
        cleaned_lines = []
        prev_empty = False
        
        for line in result_lines:
            if line.strip():
                cleaned_lines.append(line)
                prev_empty = False
            elif not prev_empty:
                cleaned_lines.append(line)
                prev_empty = True
        
        return '\n'.join(cleaned_lines)
    
    def _print_balancing_summary(self, report: Dict[str, Any]):
        """Stampa summary del bilanciamento."""
        
        print("\n" + "="*60)
        print("📊 DATASET BALANCING SUMMARY")
        print("="*60)
        
        print(f"\n🎯 BALANCING RESULTS:")
        print(f"   Original examples: {report['original_examples']}")
        print(f"   Balanced examples: {report['balanced_examples']}")
        print(f"   Output saved: {report['output_path']}")
        
        print(f"\n📈 DISTRIBUTION ACHIEVED:")
        achieved = report['achieved_distribution']
        target = report['target_distribution']
        
        for range_key in ['3-5', '6-8', '9-12']:
            achieved_pct = achieved[range_key] * 100
            target_pct = target[range_key] * 100
            count = report['range_counts'][range_key]
            
            status = "✅" if abs(achieved_pct - target_pct) < 5 else "⚠️"
            print(f"   {status} {range_key} steps: {count} ({achieved_pct:.1f}% vs {target_pct:.1f}% target)")
        
        print(f"\n🎊 MULTI-HOP OPTIMIZATION COMPLETED!")
        print(f"   Dataset now has scalable complexity distribution")
        print(f"   Ready for NEUROGLYPH GOD MODE training")
        
        print("="*60)

def main():
    """Esegue bilanciamento dataset."""
    
    input_path = "neuroglyph_supreme_god_mode_1k_test.json"
    output_path = "neuroglyph_supreme_god_mode_balanced.json"
    
    print("🔧 NEUROGLYPH Dataset Balancer")
    print("=" * 40)
    print("🎯 Multi-hop depth optimization for scalable complexity")
    
    balancer = NeuroglyphDatasetBalancer()
    
    # Esegui bilanciamento
    report = balancer.balance_dataset(input_path, output_path)
    
    print(f"\n💾 Balanced dataset saved: {output_path}")
    print(f"🎯 Ready for perfected NEUROGLYPH training!")

if __name__ == "__main__":
    main()
