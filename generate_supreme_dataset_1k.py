#!/usr/bin/env python3
"""
Genera dataset SUPREME 1K per test
==================================
"""

import sys
sys.path.append('.')

from neuroglyph_supreme_god_mode_dataset_generator import NeuroglyphSupremeGodModeGenerator

def generate_supreme_1k():
    """Genera dataset SUPREME di 1000 esempi per test."""
    
    print("🚀 Generando NEUROGLYPH SUPREME GOD MODE Dataset - 1K Test")
    print("=" * 60)
    
    generator = NeuroglyphSupremeGodModeGenerator()
    
    # Genera dataset 1K
    print("🎯 Target: 1,000 esempi di qualità ≥9.0/10")
    print("🧠 Standard: Ragionamento simbolico deterministico")
    
    dataset = generator.generate_supreme_dataset(target_size=1000, quality_threshold=9.0)
    
    if dataset:
        # Salva dataset
        filename = generator.save_supreme_dataset(dataset, "neuroglyph_supreme_god_mode_1k_test.json")
        
        print(f"\n🎊 DATASET SUPREME 1K COMPLETATO!")
        print(f"📁 File: {filename}")
        print(f"📊 Esempi: {len(dataset)}")
        
        # Mostra statistiche
        avg_quality = sum(ex['metadata']['quality_score'] for ex in dataset) / len(dataset)
        avg_symbols = sum(ex['metadata']['symbols_used'] for ex in dataset) / len(dataset)
        
        print(f"🎯 Qualità media: {avg_quality:.2f}/10")
        print(f"🔣 Simboli medi: {avg_symbols:.1f}")
        
        # Conta esempi per qualità
        supreme_count = len([ex for ex in dataset if ex['metadata']['quality_score'] >= 9.5])
        god_mode_count = len([ex for ex in dataset if ex['metadata']['quality_score'] >= 9.0])
        
        print(f"🔥 Esempi SUPREME (≥9.5): {supreme_count}")
        print(f"🎯 Esempi GOD MODE (≥9.0): {god_mode_count}")
        
        print(f"\n✅ PRONTO PER TRAINING NEUROGLYPH SUPREME GOD MODE!")
        
    else:
        print("❌ Errore nella generazione del dataset")

if __name__ == "__main__":
    generate_supreme_1k()
