{"evaluation_timestamp": "2025-06-01T23:13:38.942602", "dataset_path": "neuroglyph_supreme_god_mode_20k.json", "total_examples": 16000, "aggregate_statistics": {"symbolic_completeness": {"mean": 0.845, "median": 0.857, "std": 0.029, "min": 0.66, "max": 0.867}, "logical_structure_score": {"mean": 0.928, "median": 1.0, "std": 0.078, "min": 0.82, "max": 1.0}, "multi_hop_depth": {"mean": 12, "median": 12.0, "std": 0.0, "min": 12, "max": 12, "target_range_3_8": 0.0}, "determinism_score": {"mean": 0.94, "median": 0.92, "std": 0.035, "min": 0.92, "max": 1.0}, "zero_hallucination": {"rate": 1.0, "count_true": 16000, "count_false": 0, "total": 16000}, "symbol_quality": {"mean": 0.89, "median": 0.883, "std": 0.029, "min": 0.825, "max": 0.938}, "cognitive_tags_presence": {"mean": 0.787, "median": 0.75, "std": 0.061, "min": 0.75, "max": 1.0}, "excellence_score": {"mean": 93.59, "median": 96.85, "std": 3.99, "min": 85.89, "max": 97.6}}, "god_mode_analysis": {"quality_distribution": {"supreme_95+": 8354, "excellent_85+": 7646, "good_75+": 0, "acceptable_65+": 0, "poor_below_65": 0}, "god_mode_examples": 8354, "excellent_examples": 7646, "good_examples": 0, "total_examples": 16000, "god_mode_rate": 0.522125, "threshold_compliance": {"excellence_score": {"compliant_count": 16000, "compliance_rate": 1.0, "threshold": 85.0}, "symbolic_completeness": {"compliant_count": 15615, "compliance_rate": 0.9759375, "threshold": 0.8}, "logical_structure_score": {"compliant_count": 16000, "compliance_rate": 1.0, "threshold": 0.7}, "determinism_score": {"compliant_count": 16000, "compliance_rate": 1.0, "threshold": 0.8}, "symbol_quality": {"compliant_count": 16000, "compliance_rate": 1.0, "threshold": 0.7}, "multi_hop_depth_min": {"compliant_count": 16000, "compliance_rate": 1.0, "threshold": 3}, "multi_hop_depth_max": {"compliant_count": 0, "compliance_rate": 0.0, "threshold": 8}}, "overall_god_mode_readiness": true}, "recommendations": ["🟡 WARNING: Solo 0.0% esempi nel range multi-hop 3-8", "   → Bilanciare profondità ragionamento", "🎊 EXCELLENT: Dataset raggiunge standard SUPREME GOD MODE!", "   → Pronto per training LLM simbolico deterministico"], "detailed_results": [{"example_id": 0, "instruction": "Crea un sistema di inferenza formale utilizzando ⊢ ∴ ∧ per dimostrare proprietà metalogiche. Applica...", "metrics": {"symbolic_completeness": 0.8625, "logical_structure_score": 0.9999999999999999, "multi_hop_depth": 12, "determinism_score": 0.92, "zero_hallucination": true, "symbol_quality": 0.9375, "cognitive_tags_presence": 0.75, "excellence_score": 96.85}}, {"example_id": 1, "instruction": "Costruisci una dimostrazione formale usando ⊢ ∴ ∧ per dimostrare la decidibilità di sistemi logici. ...", "metrics": {"symbolic_completeness": 0.8625, "logical_structure_score": 0.9999999999999999, "multi_hop_depth": 12, "determinism_score": 0.92, "zero_hallucination": true, "symbol_quality": 0.9375, "cognitive_tags_presence": 0.75, "excellence_score": 96.85}}, {"example_id": 2, "instruction": "Costruisci una dimostrazione formale usando ⊢ ∴ ∧ per dimostrare la decidibilità di sistemi logici. ...", "metrics": {"symbolic_completeness": 0.8625, "logical_structure_score": 0.9999999999999999, "multi_hop_depth": 12, "determinism_score": 0.92, "zero_hallucination": true, "symbol_quality": 0.9375, "cognitive_tags_presence": 0.75, "excellence_score": 96.85}}, {"example_id": 3, "instruction": "S<PERSON>uppa una catena deduttiva rigorosa con simboli ⊢ ∴ ∧ per analizzare la consistenza logica. Usa d...", "metrics": {"symbolic_completeness": 0.8625, "logical_structure_score": 0.9999999999999999, "multi_hop_depth": 12, "determinism_score": 0.92, "zero_hallucination": true, "symbol_quality": 0.9375, "cognitive_tags_presence": 0.75, "excellence_score": 96.85}}, {"example_id": 4, "instruction": "S<PERSON>uppa una catena deduttiva rigorosa con simboli ⊢ ∴ ∧ per analizzare la consistenza logica. Usa d...", "metrics": {"symbolic_completeness": 0.8625, "logical_structure_score": 0.9999999999999999, "multi_hop_depth": 12, "determinism_score": 0.92, "zero_hallucination": true, "symbol_quality": 0.9375, "cognitive_tags_presence": 0.75, "excellence_score": 96.85}}, {"example_id": 5, "instruction": "Costruisci una dimostrazione formale usando ⊢ ∴ ∧ per dimostrare la decidibilità di sistemi logici. ...", "metrics": {"symbolic_completeness": 0.8625, "logical_structure_score": 0.9999999999999999, "multi_hop_depth": 12, "determinism_score": 0.92, "zero_hallucination": true, "symbol_quality": 0.9375, "cognitive_tags_presence": 0.75, "excellence_score": 96.85}}, {"example_id": 6, "instruction": "S<PERSON>uppa una catena deduttiva rigorosa con simboli ⊢ ∴ ∧ per analizzare la consistenza logica. Usa d...", "metrics": {"symbolic_completeness": 0.8625, "logical_structure_score": 0.9999999999999999, "multi_hop_depth": 12, "determinism_score": 0.92, "zero_hallucination": true, "symbol_quality": 0.9375, "cognitive_tags_presence": 0.75, "excellence_score": 96.85}}, {"example_id": 7, "instruction": "Costruisci una dimostrazione formale usando ⊢ ∴ ∧ per dimostrare la decidibilità di sistemi logici. ...", "metrics": {"symbolic_completeness": 0.8625, "logical_structure_score": 0.9999999999999999, "multi_hop_depth": 12, "determinism_score": 0.92, "zero_hallucination": true, "symbol_quality": 0.9375, "cognitive_tags_presence": 0.75, "excellence_score": 96.85}}, {"example_id": 8, "instruction": "Costruisci una dimostrazione formale usando ⊢ ∴ ∧ per dimostrare la decidibilità di sistemi logici. ...", "metrics": {"symbolic_completeness": 0.8625, "logical_structure_score": 0.9999999999999999, "multi_hop_depth": 12, "determinism_score": 0.92, "zero_hallucination": true, "symbol_quality": 0.9375, "cognitive_tags_presence": 0.75, "excellence_score": 96.85}}, {"example_id": 9, "instruction": "Costruisci una dimostrazione formale usando ⊢ ∴ ∧ per dimostrare la decidibilità di sistemi logici. ...", "metrics": {"symbolic_completeness": 0.8625, "logical_structure_score": 0.9999999999999999, "multi_hop_depth": 12, "determinism_score": 0.92, "zero_hallucination": true, "symbol_quality": 0.9375, "cognitive_tags_presence": 0.75, "excellence_score": 96.85}}], "neuroglyph_readiness": {"symbolic_intelligence_ready": true, "deterministic_reasoning_ready": true, "zero_hallucination_ready": true, "multi_hop_ready": true, "overall_god_mode_ready": true}}