#!/usr/bin/env python3
"""
NEUROGLYPH Visual Analysis Report Generator
==========================================

Genera report visuale completo con grafici e heatmap per analisi
degradazione qualità nella pipeline di perfezionamento.

Autore: NEUROGLYPH Analysis Team
Data: 2025-06-01
"""

import json
import matplotlib.pyplot as plt
import seaborn as sns
import pandas as pd
import numpy as np
from datetime import datetime

class NeuroglyphVisualAnalyzer:
    """Generatore report visuale per analisi NEUROGLYPH."""
    
    def __init__(self):
        self.pre_data = None
        self.post_data = None
        
    def load_evaluation_data(self, pre_path: str, post_path: str):
        """Carica dati valutazione pre e post perfezionamento."""
        
        with open(pre_path, 'r', encoding='utf-8') as f:
            self.pre_data = json.load(f)
            
        with open(post_path, 'r', encoding='utf-8') as f:
            self.post_data = json.load(f)
    
    def generate_complete_visual_report(self):
        """Genera report visuale completo."""
        
        print("📊 NEUROGLYPH Visual Analysis Report Generator")
        print("=" * 55)
        
        # Setup matplotlib
        plt.style.use('seaborn-v0_8')
        fig = plt.figure(figsize=(20, 24))
        
        # 1. Confronto metriche principali
        self._plot_metrics_comparison(fig, 1)
        
        # 2. Distribuzione qualità
        self._plot_quality_distribution(fig, 2)
        
        # 3. Heatmap degradazione
        self._plot_degradation_heatmap(fig, 3)
        
        # 4. Multi-hop depth analysis
        self._plot_multihop_analysis(fig, 4)
        
        # 5. Excellence score distribution
        self._plot_excellence_distribution(fig, 5)
        
        # 6. Compliance radar chart
        self._plot_compliance_radar(fig, 6)
        
        plt.tight_layout()
        
        # Salva report
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        filename = f"neuroglyph_visual_analysis_report_{timestamp}.png"
        plt.savefig(filename, dpi=300, bbox_inches='tight')
        
        print(f"📊 Visual report saved: {filename}")
        
        # Genera anche summary testuale
        self._generate_text_summary()
        
        plt.show()
    
    def _plot_metrics_comparison(self, fig, position):
        """Grafico confronto metriche principali."""
        
        ax = plt.subplot(3, 2, position)
        
        metrics = [
            'Excellence Score',
            'Logical Structure',
            'Symbolic Completeness',
            'Determinism Score',
            'Symbol Quality',
            'Cognitive Tags'
        ]
        
        pre_values = [
            self.pre_data['aggregate_statistics']['excellence_score']['mean'],
            self.pre_data['aggregate_statistics']['logical_structure_score']['mean'] * 100,
            self.pre_data['aggregate_statistics']['symbolic_completeness']['mean'] * 100,
            self.pre_data['aggregate_statistics']['determinism_score']['mean'] * 100,
            self.pre_data['aggregate_statistics']['symbol_quality']['mean'] * 100,
            self.pre_data['aggregate_statistics']['cognitive_tags_presence']['mean'] * 100
        ]
        
        post_values = [
            self.post_data['aggregate_statistics']['excellence_score']['mean'],
            self.post_data['aggregate_statistics']['logical_structure_score']['mean'] * 100,
            self.post_data['aggregate_statistics']['symbolic_completeness']['mean'] * 100,
            self.post_data['aggregate_statistics']['determinism_score']['mean'] * 100,
            self.post_data['aggregate_statistics']['symbol_quality']['mean'] * 100,
            self.post_data['aggregate_statistics']['cognitive_tags_presence']['mean'] * 100
        ]
        
        x = np.arange(len(metrics))
        width = 0.35
        
        bars1 = ax.bar(x - width/2, pre_values, width, label='Pre-Perfezionamento', 
                      color='#2E8B57', alpha=0.8)
        bars2 = ax.bar(x + width/2, post_values, width, label='Post-Perfezionamento', 
                      color='#DC143C', alpha=0.8)
        
        ax.set_xlabel('Metriche')
        ax.set_ylabel('Score (%)')
        ax.set_title('🔍 Confronto Metriche Pre/Post Perfezionamento', fontsize=14, fontweight='bold')
        ax.set_xticks(x)
        ax.set_xticklabels(metrics, rotation=45, ha='right')
        ax.legend()
        ax.grid(True, alpha=0.3)
        
        # Aggiungi valori sulle barre
        for bar in bars1:
            height = bar.get_height()
            ax.text(bar.get_x() + bar.get_width()/2., height + 1,
                   f'{height:.1f}', ha='center', va='bottom', fontsize=8)
        
        for bar in bars2:
            height = bar.get_height()
            ax.text(bar.get_x() + bar.get_width()/2., height + 1,
                   f'{height:.1f}', ha='center', va='bottom', fontsize=8)
    
    def _plot_quality_distribution(self, fig, position):
        """Grafico distribuzione qualità."""
        
        ax = plt.subplot(3, 2, position)
        
        categories = ['SUPREME\n(95+)', 'EXCELLENT\n(85+)', 'GOOD\n(75+)', 
                     'ACCEPTABLE\n(65+)', 'POOR\n(<65)']
        
        pre_dist = [
            self.pre_data['god_mode_analysis']['quality_distribution']['supreme_95+'],
            self.pre_data['god_mode_analysis']['quality_distribution']['excellent_85+'],
            self.pre_data['god_mode_analysis']['quality_distribution']['good_75+'],
            self.pre_data['god_mode_analysis']['quality_distribution']['acceptable_65+'],
            self.pre_data['god_mode_analysis']['quality_distribution']['poor_below_65']
        ]
        
        post_dist = [
            self.post_data['god_mode_analysis']['quality_distribution']['supreme_95+'],
            self.post_data['god_mode_analysis']['quality_distribution']['excellent_85+'],
            self.post_data['god_mode_analysis']['quality_distribution']['good_75+'],
            self.post_data['god_mode_analysis']['quality_distribution']['acceptable_65+'],
            self.post_data['god_mode_analysis']['quality_distribution']['poor_below_65']
        ]
        
        x = np.arange(len(categories))
        width = 0.35
        
        ax.bar(x - width/2, pre_dist, width, label='Pre-Perfezionamento', 
               color='#2E8B57', alpha=0.8)
        ax.bar(x + width/2, post_dist, width, label='Post-Perfezionamento', 
               color='#DC143C', alpha=0.8)
        
        ax.set_xlabel('Categorie Qualità')
        ax.set_ylabel('Numero Esempi')
        ax.set_title('📈 Distribuzione Qualità - Collasso Drammatico', fontsize=14, fontweight='bold')
        ax.set_xticks(x)
        ax.set_xticklabels(categories)
        ax.legend()
        ax.grid(True, alpha=0.3)
    
    def _plot_degradation_heatmap(self, fig, position):
        """Heatmap degradazione metriche."""
        
        ax = plt.subplot(3, 2, position)
        
        metrics = ['Excellence\nScore', 'Logical\nStructure', 'Symbolic\nCompleteness', 
                  'Multi-hop\nDepth', 'Determinism\nScore', 'Symbol\nQuality', 'Cognitive\nTags']
        
        # Calcola variazioni percentuali
        variations = [
            (self.post_data['aggregate_statistics']['excellence_score']['mean'] - 
             self.pre_data['aggregate_statistics']['excellence_score']['mean']) / 
             self.pre_data['aggregate_statistics']['excellence_score']['mean'] * 100,
            
            (self.post_data['aggregate_statistics']['logical_structure_score']['mean'] - 
             self.pre_data['aggregate_statistics']['logical_structure_score']['mean']) / 
             self.pre_data['aggregate_statistics']['logical_structure_score']['mean'] * 100,
            
            (self.post_data['aggregate_statistics']['symbolic_completeness']['mean'] - 
             self.pre_data['aggregate_statistics']['symbolic_completeness']['mean']) / 
             self.pre_data['aggregate_statistics']['symbolic_completeness']['mean'] * 100,
            
            (self.post_data['aggregate_statistics']['multi_hop_depth']['mean'] - 
             self.pre_data['aggregate_statistics']['multi_hop_depth']['mean']) / 
             self.pre_data['aggregate_statistics']['multi_hop_depth']['mean'] * 100,
            
            (self.post_data['aggregate_statistics']['determinism_score']['mean'] - 
             self.pre_data['aggregate_statistics']['determinism_score']['mean']) / 
             self.pre_data['aggregate_statistics']['determinism_score']['mean'] * 100,
            
            (self.post_data['aggregate_statistics']['symbol_quality']['mean'] - 
             self.pre_data['aggregate_statistics']['symbol_quality']['mean']) / 
             self.pre_data['aggregate_statistics']['symbol_quality']['mean'] * 100,
            
            (self.post_data['aggregate_statistics']['cognitive_tags_presence']['mean'] - 
             self.pre_data['aggregate_statistics']['cognitive_tags_presence']['mean']) / 
             self.pre_data['aggregate_statistics']['cognitive_tags_presence']['mean'] * 100
        ]
        
        # Crea matrice per heatmap
        data_matrix = np.array(variations).reshape(1, -1)
        
        # Heatmap
        sns.heatmap(data_matrix, 
                   xticklabels=metrics,
                   yticklabels=['Variazione %'],
                   annot=True, 
                   fmt='.1f',
                   cmap='RdYlGn',
                   center=0,
                   cbar_kws={'label': 'Variazione %'},
                   ax=ax)
        
        ax.set_title('🔥 Heatmap Degradazione Metriche', fontsize=14, fontweight='bold')
    
    def _plot_multihop_analysis(self, fig, position):
        """Analisi multi-hop depth."""
        
        ax = plt.subplot(3, 2, position)
        
        # Dati multi-hop
        pre_depth = self.pre_data['aggregate_statistics']['multi_hop_depth']['mean']
        post_depth = self.post_data['aggregate_statistics']['multi_hop_depth']['mean']
        
        pre_range = self.pre_data['aggregate_statistics']['multi_hop_depth'].get('target_range_3_8', 0) * 100
        post_range = self.post_data['aggregate_statistics']['multi_hop_depth'].get('target_range_3_8', 0) * 100
        
        categories = ['Depth Medio', 'Range 3-8 (%)']
        pre_values = [pre_depth, pre_range]
        post_values = [post_depth, post_range]
        
        x = np.arange(len(categories))
        width = 0.35
        
        ax.bar(x - width/2, pre_values, width, label='Pre-Perfezionamento', 
               color='#2E8B57', alpha=0.8)
        ax.bar(x + width/2, post_values, width, label='Post-Perfezionamento', 
               color='#DC143C', alpha=0.8)
        
        ax.set_xlabel('Metriche Multi-hop')
        ax.set_ylabel('Valore')
        ax.set_title('🔄 Analisi Multi-hop Depth', fontsize=14, fontweight='bold')
        ax.set_xticks(x)
        ax.set_xticklabels(categories)
        ax.legend()
        ax.grid(True, alpha=0.3)
    
    def _plot_excellence_distribution(self, fig, position):
        """Distribuzione excellence score."""
        
        ax = plt.subplot(3, 2, position)
        
        # Simula distribuzione basata su media e std
        pre_mean = self.pre_data['aggregate_statistics']['excellence_score']['mean']
        pre_std = self.pre_data['aggregate_statistics']['excellence_score']['std']
        
        post_mean = self.post_data['aggregate_statistics']['excellence_score']['mean']
        post_std = self.post_data['aggregate_statistics']['excellence_score']['std']
        
        # Genera distribuzioni simulate
        pre_scores = np.random.normal(pre_mean, pre_std, 800)
        post_scores = np.random.normal(post_mean, post_std, 800)
        
        ax.hist(pre_scores, bins=30, alpha=0.7, label='Pre-Perfezionamento', 
                color='#2E8B57', density=True)
        ax.hist(post_scores, bins=30, alpha=0.7, label='Post-Perfezionamento', 
                color='#DC143C', density=True)
        
        ax.axvline(pre_mean, color='#2E8B57', linestyle='--', linewidth=2, 
                  label=f'Media Pre: {pre_mean:.1f}')
        ax.axvline(post_mean, color='#DC143C', linestyle='--', linewidth=2, 
                  label=f'Media Post: {post_mean:.1f}')
        
        ax.set_xlabel('Excellence Score')
        ax.set_ylabel('Densità')
        ax.set_title('📊 Distribuzione Excellence Score', fontsize=14, fontweight='bold')
        ax.legend()
        ax.grid(True, alpha=0.3)
    
    def _plot_compliance_radar(self, fig, position):
        """Radar chart compliance soglie."""
        
        ax = plt.subplot(3, 2, position, projection='polar')
        
        # Metriche compliance
        metrics = ['Excellence\nScore', 'Logical\nStructure', 'Symbolic\nCompleteness', 
                  'Determinism\nScore', 'Symbol\nQuality']
        
        pre_compliance = [
            self.pre_data['god_mode_analysis']['threshold_compliance']['excellence_score']['compliance_rate'] * 100,
            self.pre_data['god_mode_analysis']['threshold_compliance']['logical_structure_score']['compliance_rate'] * 100,
            self.pre_data['god_mode_analysis']['threshold_compliance']['symbolic_completeness']['compliance_rate'] * 100,
            self.pre_data['god_mode_analysis']['threshold_compliance']['determinism_score']['compliance_rate'] * 100,
            self.pre_data['god_mode_analysis']['threshold_compliance']['symbol_quality']['compliance_rate'] * 100
        ]
        
        post_compliance = [
            self.post_data['god_mode_analysis']['threshold_compliance']['excellence_score']['compliance_rate'] * 100,
            self.post_data['god_mode_analysis']['threshold_compliance']['logical_structure_score']['compliance_rate'] * 100,
            self.post_data['god_mode_analysis']['threshold_compliance']['symbolic_completeness']['compliance_rate'] * 100,
            self.post_data['god_mode_analysis']['threshold_compliance']['determinism_score']['compliance_rate'] * 100,
            self.post_data['god_mode_analysis']['threshold_compliance']['symbol_quality']['compliance_rate'] * 100
        ]
        
        # Angoli per radar
        angles = np.linspace(0, 2 * np.pi, len(metrics), endpoint=False).tolist()
        pre_compliance += pre_compliance[:1]  # Chiudi il cerchio
        post_compliance += post_compliance[:1]
        angles += angles[:1]
        
        ax.plot(angles, pre_compliance, 'o-', linewidth=2, label='Pre-Perfezionamento', color='#2E8B57')
        ax.fill(angles, pre_compliance, alpha=0.25, color='#2E8B57')
        
        ax.plot(angles, post_compliance, 'o-', linewidth=2, label='Post-Perfezionamento', color='#DC143C')
        ax.fill(angles, post_compliance, alpha=0.25, color='#DC143C')
        
        ax.set_xticks(angles[:-1])
        ax.set_xticklabels(metrics)
        ax.set_ylim(0, 100)
        ax.set_title('🎯 Compliance Radar Chart', fontsize=14, fontweight='bold', pad=20)
        ax.legend(loc='upper right', bbox_to_anchor=(1.3, 1.0))
        ax.grid(True)
    
    def _generate_text_summary(self):
        """Genera summary testuale."""
        
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        filename = f"neuroglyph_analysis_summary_{timestamp}.txt"
        
        with open(filename, 'w', encoding='utf-8') as f:
            f.write("🔍 NEUROGLYPH PERFECTION PIPELINE - ANALYSIS SUMMARY\n")
            f.write("=" * 60 + "\n\n")
            
            f.write("📊 METRICHE PRINCIPALI:\n")
            f.write(f"Excellence Score: {self.pre_data['aggregate_statistics']['excellence_score']['mean']:.2f} → {self.post_data['aggregate_statistics']['excellence_score']['mean']:.2f}\n")
            f.write(f"Logical Structure: {self.pre_data['aggregate_statistics']['logical_structure_score']['mean']:.3f} → {self.post_data['aggregate_statistics']['logical_structure_score']['mean']:.3f}\n")
            f.write(f"Symbolic Completeness: {self.pre_data['aggregate_statistics']['symbolic_completeness']['mean']:.3f} → {self.post_data['aggregate_statistics']['symbolic_completeness']['mean']:.3f}\n")
            
            f.write(f"\n📈 DISTRIBUZIONE QUALITÀ:\n")
            f.write(f"SUPREME (95+): {self.pre_data['god_mode_analysis']['quality_distribution']['supreme_95+']} → {self.post_data['god_mode_analysis']['quality_distribution']['supreme_95+']}\n")
            f.write(f"EXCELLENT (85+): {self.pre_data['god_mode_analysis']['quality_distribution']['excellent_85+']} → {self.post_data['god_mode_analysis']['quality_distribution']['excellent_85+']}\n")
            
            f.write(f"\n🎯 RACCOMANDAZIONE:\n")
            f.write(f"Dataset originale (93.66 excellence) RACCOMANDATO per training\n")
            f.write(f"Dataset perfezionato (73.66 excellence) NON raccomandato\n")
        
        print(f"📄 Text summary saved: {filename}")

def main():
    """Genera report visuale completo."""
    
    pre_evaluation = "neuroglyph_god_mode_evaluation_20250601_222454.json"
    post_evaluation = "neuroglyph_god_mode_evaluation_20250601_223744.json"
    
    analyzer = NeuroglyphVisualAnalyzer()
    analyzer.load_evaluation_data(pre_evaluation, post_evaluation)
    analyzer.generate_complete_visual_report()

if __name__ == "__main__":
    main()
