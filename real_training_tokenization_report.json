{"analysis_type": "real_training_tokenization", "tokenizer_used": "Qwen/Qwen2.5-Coder-1.5B-Instruct", "symbols_analyzed": 66, "tokenization_results": {"single_token_symbols": [{"symbol": "⊃", "token_id": 145949, "faithful": true}, {"symbol": "∧", "token_id": 145264, "faithful": true}, {"symbol": "∨", "token_id": 145700, "faithful": true}, {"symbol": "→", "token_id": 51018, "faithful": true}, {"symbol": "∑", "token_id": 145127, "faithful": true}, {"symbol": "∫", "token_id": 145706, "faithful": true}, {"symbol": "∂", "token_id": 145371, "faithful": true}, {"symbol": "≈", "token_id": 144540, "faithful": true}, {"symbol": "π", "token_id": 48245, "faithful": true}, {"symbol": "ƒ", "token_id": 144451, "faithful": true}, {"symbol": "🔄", "token_id": 148113, "faithful": true}, {"symbol": "❓", "token_id": 145337, "faithful": true}, {"symbol": "📋", "token_id": 147193, "faithful": true}, {"symbol": "🔢", "token_id": 151405, "faithful": true}, {"symbol": "📝", "token_id": 145795, "faithful": true}], "multi_token_symbols": [{"symbol": "⟲", "tokens": [34629, 110], "token_count": 2, "faithful": true, "decoded": "⟲"}, {"symbol": "ng:operator:sub", "tokens": [968, 25, 7884, 25, 1966], "token_count": 5, "faithful": true, "decoded": "ng:operator:sub"}, {"symbol": "ng:memory:pointer", "tokens": [968, 25, 17269, 71140], "token_count": 4, "faithful": true, "decoded": "ng:memory:pointer"}, {"symbol": "ng:memory:alloc", "tokens": [968, 25, 17269, 25, 4742], "token_count": 5, "faithful": true, "decoded": "ng:memory:alloc"}, {"symbol": "ng:logic:implies", "tokens": [968, 25, 24225, 25, 6383, 550], "token_count": 6, "faithful": true, "decoded": "ng:logic:implies"}, {"symbol": "ng:logic:or_1", "tokens": [968, 25, 24225, 25, 269, 62, 16], "token_count": 7, "faithful": true, "decoded": "ng:logic:or_1"}, {"symbol": "ng:memory:alloc_1", "tokens": [968, 25, 17269, 25, 4742, 62, 16], "token_count": 7, "faithful": true, "decoded": "ng:memory:alloc_1"}, {"symbol": "ng:memory:free", "tokens": [968, 25, 17269, 25, 10593], "token_count": 5, "faithful": true, "decoded": "ng:memory:free"}, {"symbol": "ng:structure:function_1", "tokens": [968, 25, 7837, 17934, 62, 16], "token_count": 6, "faithful": true, "decoded": "ng:structure:function_1"}, {"symbol": "ng:operator:mul", "tokens": [968, 25, 7884, 25, 24160], "token_count": 5, "faithful": true, "decoded": "ng:operator:mul"}, {"symbol": "ng:operator:mod", "tokens": [968, 25, 7884, 25, 2593], "token_count": 5, "faithful": true, "decoded": "ng:operator:mod"}, {"symbol": "ng:operator:add_scalar", "tokens": [968, 25, 7884, 38488, 41652], "token_count": 5, "faithful": true, "decoded": "ng:operator:add_scalar"}, {"symbol": "ng:memory:deref", "tokens": [968, 25, 17269, 40422, 43970], "token_count": 5, "faithful": true, "decoded": "ng:memory:deref"}, {"symbol": "ng:operator:add_vector", "tokens": [968, 25, 7884, 38488, 12247], "token_count": 5, "faithful": true, "decoded": "ng:operator:add_vector"}, {"symbol": "ng:structure:property", "tokens": [968, 25, 7837, 25, 3699], "token_count": 5, "faithful": true, "decoded": "ng:structure:property"}, {"symbol": "ng:flow:return_1", "tokens": [968, 25, 4965, 66302, 62, 16], "token_count": 6, "faithful": true, "decoded": "ng:flow:return_1"}, {"symbol": "ng:memory:alloc_stack", "tokens": [968, 25, 17269, 25, 4742, 15528], "token_count": 6, "faithful": true, "decoded": "ng:memory:alloc_stack"}, {"symbol": "ng:flow:for_range", "tokens": [968, 25, 4965, 25, 1958, 9698], "token_count": 6, "faithful": true, "decoded": "ng:flow:for_range"}, {"symbol": "ng:operator:add_matrix", "tokens": [968, 25, 7884, 38488, 10193], "token_count": 5, "faithful": true, "decoded": "ng:operator:add_matrix"}, {"symbol": "ng:flow:if_conditional", "tokens": [968, 25, 4965, 25, 333, 24433, 3005], "token_count": 7, "faithful": true, "decoded": "ng:flow:if_conditional"}, {"symbol": "ng:operator:mul_scalar", "tokens": [968, 25, 7884, 25, 24160, 41652], "token_count": 6, "faithful": true, "decoded": "ng:operator:mul_scalar"}, {"symbol": "ng:logic:and_1", "tokens": [968, 25, 24225, 25, 437, 62, 16], "token_count": 7, "faithful": true, "decoded": "ng:logic:and_1"}, {"symbol": "ng:structure:function_2", "tokens": [968, 25, 7837, 17934, 62, 17], "token_count": 6, "faithful": true, "decoded": "ng:structure:function_2"}, {"symbol": "ng:structure:property_1", "tokens": [968, 25, 7837, 25, 3699, 62, 16], "token_count": 7, "faithful": true, "decoded": "ng:structure:property_1"}, {"symbol": "ng:memory:pointer_1", "tokens": [968, 25, 17269, 71140, 62, 16], "token_count": 6, "faithful": true, "decoded": "ng:memory:pointer_1"}, {"symbol": "ng:flow:break", "tokens": [968, 25, 4965, 25, 8960], "token_count": 5, "faithful": true, "decoded": "ng:flow:break"}, {"symbol": "ng:logic:not_bitwise", "tokens": [968, 25, 24225, 67844, 13996, 4482], "token_count": 6, "faithful": true, "decoded": "ng:logic:not_bitwise"}, {"symbol": "ng:logic:implies_1", "tokens": [968, 25, 24225, 25, 6383, 550, 62, 16], "token_count": 8, "faithful": true, "decoded": "ng:logic:implies_1"}, {"symbol": "ng:flow:break_1", "tokens": [968, 25, 4965, 25, 8960, 62, 16], "token_count": 7, "faithful": true, "decoded": "ng:flow:break_1"}, {"symbol": "ng:operator:div", "tokens": [968, 25, 7884, 25, 611], "token_count": 5, "faithful": true, "decoded": "ng:operator:div"}, {"symbol": "ng:operator:add_complex", "tokens": [968, 25, 7884, 38488, 41522], "token_count": 5, "faithful": true, "decoded": "ng:operator:add_complex"}, {"symbol": "ng:flow:if_ternary", "tokens": [968, 25, 4965, 25, 333, 62, 4160, 658], "token_count": 8, "faithful": true, "decoded": "ng:flow:if_ternary"}, {"symbol": "ng:logic:or_logical", "tokens": [968, 25, 24225, 25, 269, 86484], "token_count": 6, "faithful": true, "decoded": "ng:logic:or_logical"}, {"symbol": "ng:memory:deref_1", "tokens": [968, 25, 17269, 40422, 43970, 62, 16], "token_count": 7, "faithful": true, "decoded": "ng:memory:deref_1"}, {"symbol": "ng:structure:class_1", "tokens": [968, 25, 7837, 91964, 62, 16], "token_count": 6, "faithful": true, "decoded": "ng:structure:class_1"}, {"symbol": "ng:operator:div_scalar", "tokens": [968, 25, 7884, 25, 611, 41652], "token_count": 6, "faithful": true, "decoded": "ng:operator:div_scalar"}, {"symbol": "ng:memory:pointer_2", "tokens": [968, 25, 17269, 71140, 62, 17], "token_count": 6, "faithful": true, "decoded": "ng:memory:pointer_2"}, {"symbol": "ng:flow:break_2", "tokens": [968, 25, 4965, 25, 8960, 62, 17], "token_count": 7, "faithful": true, "decoded": "ng:flow:break_2"}, {"symbol": "ng:memory:deref_2", "tokens": [968, 25, 17269, 40422, 43970, 62, 17], "token_count": 7, "faithful": true, "decoded": "ng:memory:deref_2"}, {"symbol": "ng:operator:mod_integer", "tokens": [968, 25, 7884, 25, 2593, 31725], "token_count": 6, "faithful": true, "decoded": "ng:operator:mod_integer"}, {"symbol": "ng:logic:implies_strict", "tokens": [968, 25, 24225, 25, 6383, 550, 2895, 849], "token_count": 8, "faithful": true, "decoded": "ng:logic:implies_strict"}, {"symbol": "ng:flow:for_2", "tokens": [968, 25, 4965, 25, 1958, 62, 17], "token_count": 7, "faithful": true, "decoded": "ng:flow:for_2"}, {"symbol": "ng:logic:and_logical", "tokens": [968, 25, 24225, 25, 437, 86484], "token_count": 6, "faithful": true, "decoded": "ng:logic:and_logical"}, {"symbol": "ng:logic:implies_relevant", "tokens": [968, 25, 24225, 25, 6383, 550, 1288, 8367], "token_count": 8, "faithful": true, "decoded": "ng:logic:implies_relevant"}, {"symbol": "ng:structure:property_2", "tokens": [968, 25, 7837, 25, 3699, 62, 17], "token_count": 7, "faithful": true, "decoded": "ng:structure:property_2"}, {"symbol": "ng:operator:mod_polynomial", "tokens": [968, 25, 7884, 25, 2593, 47323, 25358], "token_count": 7, "faithful": true, "decoded": "ng:operator:mod_polynomial"}, {"symbol": "ng:logic:and_fuzzy", "tokens": [968, 25, 24225, 25, 437, 761, 34758], "token_count": 7, "faithful": true, "decoded": "ng:logic:and_fuzzy"}, {"symbol": "ng:operator:pow", "tokens": [968, 25, 7884, 25, 21743], "token_count": 5, "faithful": true, "decoded": "ng:operator:pow"}, {"symbol": "ng:structure:method", "tokens": [968, 25, 7837, 25, 4393], "token_count": 5, "faithful": true, "decoded": "ng:structure:method"}, {"symbol": "ng:memory:pointer_3", "tokens": [968, 25, 17269, 71140, 62, 18], "token_count": 6, "faithful": true, "decoded": "ng:memory:pointer_3"}, {"symbol": "ng:logic:not_logical", "tokens": [968, 25, 24225, 67844, 86484], "token_count": 5, "faithful": true, "decoded": "ng:logic:not_logical"}], "fallback_analysis": {}, "tokenization_stats": {"total_symbols": 66, "single_token_count": 15, "multi_token_count": 51, "single_token_rate": 0.22727272727272727, "average_tokens_per_symbol": 4.848484848484849, "total_tokens_used": 320}}, "fallback_analysis": {"fallback_patterns": {"unicode_symbols": [{"symbol": "⟲", "token_count": 2, "faithful": true}], "ng_symbols": [{"symbol": "ng:operator:sub", "token_count": 5, "faithful": true}, {"symbol": "ng:memory:pointer", "token_count": 4, "faithful": true}, {"symbol": "ng:memory:alloc", "token_count": 5, "faithful": true}, {"symbol": "ng:logic:implies", "token_count": 6, "faithful": true}, {"symbol": "ng:logic:or_1", "token_count": 7, "faithful": true}, {"symbol": "ng:memory:alloc_1", "token_count": 7, "faithful": true}, {"symbol": "ng:memory:free", "token_count": 5, "faithful": true}, {"symbol": "ng:structure:function_1", "token_count": 6, "faithful": true}, {"symbol": "ng:operator:mul", "token_count": 5, "faithful": true}, {"symbol": "ng:operator:mod", "token_count": 5, "faithful": true}, {"symbol": "ng:operator:add_scalar", "token_count": 5, "faithful": true}, {"symbol": "ng:memory:deref", "token_count": 5, "faithful": true}, {"symbol": "ng:operator:add_vector", "token_count": 5, "faithful": true}, {"symbol": "ng:structure:property", "token_count": 5, "faithful": true}, {"symbol": "ng:flow:return_1", "token_count": 6, "faithful": true}, {"symbol": "ng:memory:alloc_stack", "token_count": 6, "faithful": true}, {"symbol": "ng:flow:for_range", "token_count": 6, "faithful": true}, {"symbol": "ng:operator:add_matrix", "token_count": 5, "faithful": true}, {"symbol": "ng:flow:if_conditional", "token_count": 7, "faithful": true}, {"symbol": "ng:operator:mul_scalar", "token_count": 6, "faithful": true}, {"symbol": "ng:logic:and_1", "token_count": 7, "faithful": true}, {"symbol": "ng:structure:function_2", "token_count": 6, "faithful": true}, {"symbol": "ng:structure:property_1", "token_count": 7, "faithful": true}, {"symbol": "ng:memory:pointer_1", "token_count": 6, "faithful": true}, {"symbol": "ng:flow:break", "token_count": 5, "faithful": true}, {"symbol": "ng:logic:not_bitwise", "token_count": 6, "faithful": true}, {"symbol": "ng:logic:implies_1", "token_count": 8, "faithful": true}, {"symbol": "ng:flow:break_1", "token_count": 7, "faithful": true}, {"symbol": "ng:operator:div", "token_count": 5, "faithful": true}, {"symbol": "ng:operator:add_complex", "token_count": 5, "faithful": true}, {"symbol": "ng:flow:if_ternary", "token_count": 8, "faithful": true}, {"symbol": "ng:logic:or_logical", "token_count": 6, "faithful": true}, {"symbol": "ng:memory:deref_1", "token_count": 7, "faithful": true}, {"symbol": "ng:structure:class_1", "token_count": 6, "faithful": true}, {"symbol": "ng:operator:div_scalar", "token_count": 6, "faithful": true}, {"symbol": "ng:memory:pointer_2", "token_count": 6, "faithful": true}, {"symbol": "ng:flow:break_2", "token_count": 7, "faithful": true}, {"symbol": "ng:memory:deref_2", "token_count": 7, "faithful": true}, {"symbol": "ng:operator:mod_integer", "token_count": 6, "faithful": true}, {"symbol": "ng:logic:implies_strict", "token_count": 8, "faithful": true}, {"symbol": "ng:flow:for_2", "token_count": 7, "faithful": true}, {"symbol": "ng:logic:and_logical", "token_count": 6, "faithful": true}, {"symbol": "ng:logic:implies_relevant", "token_count": 8, "faithful": true}, {"symbol": "ng:structure:property_2", "token_count": 7, "faithful": true}, {"symbol": "ng:operator:mod_polynomial", "token_count": 7, "faithful": true}, {"symbol": "ng:logic:and_fuzzy", "token_count": 7, "faithful": true}, {"symbol": "ng:operator:pow", "token_count": 5, "faithful": true}, {"symbol": "ng:structure:method", "token_count": 5, "faithful": true}, {"symbol": "ng:memory:pointer_3", "token_count": 6, "faithful": true}, {"symbol": "ng:logic:not_logical", "token_count": 5, "faithful": true}]}, "encoding_issues": [], "critical_issues_count": 0}, "training_impact": {"token_efficiency": 0.20625, "symbolic_quality_score": 0.22096590909090907, "critical_issues": ["MAJORITY_SYMBOLS_FRAGMENTED", "HIGH_TOKEN_FRAGMENTATION", "EXCESSIVE_FALLBACK_USAGE"], "recommendations": ["🔧 Ricreare tokenizer con simboli come special tokens", "🔄 Re-training con tokenizer corretto", "⚡ Ottimizzare encoding simbolico", "📊 Validare preservazione semantica"], "training_quality_estimate": "SUBOPTIMAL"}, "overall_assessment": {"grade": "D (Poor)", "status": "PROBLEMATIC_TRAINING", "quality_score": 0.22096590909090907, "critical_issues_count": 3, "needs_retraining": true}}