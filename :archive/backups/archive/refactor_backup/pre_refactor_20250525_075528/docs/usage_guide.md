# 🧠 NEUROGLYPH ULTRA — Usage Guide

> Complete guide for NEUROGLYPH ULTRA - First Thinking Symbolic LLM with 512 USU/CTU/LCL symbols

## 🚀 ULTRA Status: 512 Simboli Completati

✅ **512 simboli NEUROGLYPH ULTRA** con criteri USU/CTU/LCL
✅ **SOCRATE Ready**: 85 simboli (42 reasoning + 43 logic)
✅ **GOD Ready**: 84 simboli (42 memory + 42 meta)
🎯 **Obiettivo**: Primo LLM che pensa simbolicamente invece di probabilisticamente

---

## 📁 `docs/ultra/` — Symbolic Reasoning + Verification Modules

### 🔁 `planner.py`
- Purpose: Generates DAG (directed acyclic graph) from NG input.
- Usage:
```python
from planner import build_reasoning_dag
dag = build_reasoning_dag(["⊘", "↯", "⊞(x)"])
```

---

### 🧠 `logic_simulator.py`
- Purpose: Simulates DAG logic to predict failure/success paths.
- Usage:
```python
from logic_simulator import simulate_dag
simulate_dag(dag)  # returns "simulation_passed" or "possible_failure"
```

---

### ✅ `roundtrip.py`
- Purpose: Ensures NG-to-code and back transformation is consistent.
- Usage:
```python
from roundtrip import roundtrip_valid
roundtrip_valid(["⊘", "↯"], "if (x == null) throw Error();")
```

---

### 🔄 `ultra_wrapper.py`
- Purpose: Full pipeline wrapper for input → NG → DAG → verify → code.
- Usage:
```python
from ultra_wrapper import generate_output
generate_output("Fix null error in token loop")
```

---

### 📦 `symbol_registry.json`
- Purpose: Stores approved, dynamic, macro and deprecated symbols.
- Fields: `status`, `version`, `category`, `aliases`

---

### 🧪 `simulate_and_reject_if_incoherent.py`
- Purpose: Blocks incoherent DAGs before generation.
- Usage:
```python
from simulate_and_reject_if_incoherent import simulate_dag
if not simulate_dag(dag): reject()
```

---

### 🧠 `symbol_evaluator.py`
- Purpose: Evaluates symbol usefulness against strict criteria.
- Usage:
```bash
python3 symbol_evaluator.py
```
- Or programmatically:
```python
from symbol_evaluator import evaluate_symbol, load_thresholds
status, reasons = evaluate_symbol(symbol_metrics, load_thresholds())
```

---

### 📊 `symbol_score_schema.json`
- Contains the thresholds for symbol evaluation.
- Editable with caution — defines what makes a symbol "approved".

---

## 📁 `core/` — Core Components (expected)

### 🧬 `encoder.py`
- Purpose: Converts natural language → NG and back.
- Functions:
```python
encode("Fix login error")  # returns ["⊘", "↯", "⊞(login)"]
decode(["⊘", "↯", "⊞(login)"])  # returns natural language
```

---

## ✅ Suggested Workflow

1. Encode text to NG with `encoder.py`
2. Build DAG with `planner.py`
3. Simulate with `logic_simulator.py`
4. Verify with `roundtrip.py`
5. Generate code via `ultra_wrapper.py`
6. Log in memory + score with `symbol_evaluator.py`
7. Update status in `symbol_registry.json`

---

## 📌 Notes

- All code should be tested in isolation before end-to-end use.
- Maintain backups of `symbol_registry.json`.
- Only symbols marked `"approved"` should be used in critical generation.

