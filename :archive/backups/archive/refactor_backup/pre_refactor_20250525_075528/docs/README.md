# NEUROGLIPH Documentation

Benvenuto nella documentazione completa di NEUROGLIPH, il sistema rivoluzionario di codifica simbolica per l'intelligenza artificiale.

## 📚 Indice

- [Introduzione](#introduzione)
- [Installazione Rapida](#installazione-rapida)
- [Architettura del Sistema](#architettura-del-sistema)
- [Guide Utente](#guide-utente)
- [API Reference](#api-reference)
- [Esempi Pratici](#esempi-pratici)
- [Contribuire](#contribuire)

## 🎯 Introduzione

NEUROGLIPH rappresenta un paradigma completamente nuovo nella rappresentazione del codice, dove ogni simbolo contiene informazioni semantiche dense e strutturali che permettono ai modelli di linguaggio di comprendere e generare codice con una precisione senza precedenti.

### Caratteristiche Principali

- **🧠 Codifica Semantica**: Trasformazione intelligente del codice in simboli neuroglifi
- **⚡ Compressione Ultra**: Riduzione fino al 75% della lunghezza del codice
- **🔄 Reversibilità Perfetta**: Decodifica senza perdita di informazioni
- **🌐 Multi-linguaggio**: Supporto per Python, JavaScript, Rust, Go, Java, C++
- **🚀 Ottimizzazione LLM**: Progettato specificamente per modelli di linguaggio

### Vantaggi Chiave

1. **Efficienza Computazionale**: Riduzione drastica dei token necessari
2. **Comprensione Semantica**: Preservazione del significato del codice
3. **Velocità di Training**: Accelerazione del training dei modelli LLM
4. **Qualità Generazione**: Miglioramento della qualità del codice generato

## 🚀 Installazione Rapida

### Prerequisiti

- Python 3.10+
- Git
- CUDA 11.8+ (opzionale, per GPU)

### Installazione Automatica

```bash
# Clona il repository
git clone https://github.com/JoyciAkira/NEUROGLIPH.git
cd NEUROGLIPH

# Esegui setup automatico
python scripts/setup.py

# Per sviluppatori
python scripts/setup.py --dev --optional
```

### Installazione Manuale

```bash
# Installa dipendenze base
pip install -r requirements.txt

# Installa dipendenze development (opzionale)
pip install -r requirements-dev.txt

# Installa dipendenze opzionali (per GPU)
pip install -r requirements-optional.txt
```

### Verifica Installazione

```python
from core.encoder import NeuroGlyphEncoder
from runtime.wrapper import NeuroGlyphRuntime

# Test encoder
encoder = NeuroGlyphEncoder()
print("✅ Encoder inizializzato correttamente")

# Test runtime
runtime = NeuroGlyphRuntime()
print("✅ Runtime inizializzato correttamente")
```

## 🏗️ Architettura del Sistema

### Componenti Principali

```
NEUROGLIPH/
├── 🧠 core/              # Motore di codifica/decodifica
│   ├── encoder.py        # Encoder principale
│   ├── symbols.json      # Dizionario simboli
│   └── tokenizer.py      # Tokenizer neuroglifi
├── 🤖 llm/               # Modelli e training LLM
│   ├── training.yaml     # Configurazione training
│   ├── model_card.md     # Documentazione modello
│   └── fine_tuning.py    # Script fine-tuning
├── ⚡ runtime/           # Runtime di esecuzione
│   ├── wrapper.py        # Wrapper esecuzione
│   └── security.py       # Sandbox sicurezza
├── 📊 datasets/          # Dataset training/validazione
│   ├── ng_to_code.jsonl  # Coppie neuroglifi-codice
│   └── text_to_ng.jsonl  # Testo-neuroglifi
└── 🛠️ scripts/          # Utility e automazione
    ├── setup.py          # Setup ambiente
    ├── train.py          # Training modelli
    └── evaluate.py       # Valutazione performance
```

### Flusso di Elaborazione

```mermaid
graph LR
    A[Codice Sorgente] --> B[Analisi Semantica]
    B --> C[Mappatura Simboli]
    C --> D[Compressione]
    D --> E[Neuroglifi]
    E --> F[LLM Processing]
    F --> G[Decodifica]
    G --> H[Codice Generato]
```

## 📖 Guide Utente

### 1. Primo Utilizzo

#### Codifica Base

```python
from core.encoder import NeuroGlyphEncoder, EncodingContext

# Inizializza encoder
encoder = NeuroGlyphEncoder()

# Codice da codificare
code = """
def fibonacci(n):
    if n <= 1:
        return n
    return fibonacci(n-1) + fibonacci(n-2)
"""

# Contesto di codifica
context = EncodingContext(
    language="python",
    encoding_level=2,  # 1=base, 2=avanzato, 3=ultra
    preserve_comments=True
)

# Codifica
encoded, metadata = encoder.encode(code, context)
print(f"Codice originale: {len(code)} caratteri")
print(f"Neuroglifi: {len(encoded)} caratteri")
print(f"Compressione: {metadata['compression_ratio']:.1%}")
```

#### Decodifica

```python
# Decodifica neuroglifi
decoded = encoder.decode(encoded, metadata)
print("Codice decodificato:")
print(decoded)
```

### 2. Esecuzione Runtime

```python
from runtime.wrapper import NeuroGlyphRuntime, ExecutionContext

# Inizializza runtime
runtime = NeuroGlyphRuntime()

# Contesto di esecuzione
context = ExecutionContext(
    language="python",
    timeout=30.0,
    sandbox_mode=True,  # Esecuzione sicura
    capture_output=True
)

# Esegui codice decodificato
result = runtime.execute(decoded, context)

if result.success:
    print(f"Output: {result.output}")
    print(f"Tempo: {result.execution_time:.3f}s")
else:
    print(f"Errore: {result.error}")
```

### 3. Training Personalizzato

```python
from llm.trainer import NeuroGlyphTrainer

# Carica configurazione
trainer = NeuroGlyphTrainer.from_config("llm/training.yaml")

# Carica dataset
trainer.load_datasets([
    "datasets/ng_to_code.jsonl",
    "datasets/text_to_ng.jsonl"
])

# Avvia training
trainer.train(
    output_dir="models/my_model",
    num_epochs=3,
    batch_size=16,
    learning_rate=2e-5
)
```

### 4. Utilizzo Avanzato

#### Batch Processing

```python
from core.encoder import encode_file

# Codifica file multipli
files = ["src/main.py", "src/utils.py", "src/models.py"]
for file_path in files:
    metadata = encode_file(
        file_path=file_path,
        output_path=f"{file_path}.ng",
        language="python",
        encoding_level=3
    )
    print(f"{file_path}: {metadata['compression_ratio']:.1%} compressione")
```

#### API Server

```python
from fastapi import FastAPI
from core.encoder import NeuroGlyphEncoder

app = FastAPI()
encoder = NeuroGlyphEncoder()

@app.post("/encode")
async def encode_code(code: str, language: str = "python"):
    context = EncodingContext(language=language)
    encoded, metadata = encoder.encode(code, context)
    return {
        "neuroglyphs": encoded,
        "metadata": metadata
    }

@app.post("/decode") 
async def decode_neuroglyphs(neuroglyphs: str, metadata: dict):
    decoded = encoder.decode(neuroglyphs, metadata)
    return {"code": decoded}
```

## 🔧 API Reference

### Core Encoder

#### `NeuroGlyphEncoder`

Classe principale per codifica/decodifica neuroglifi.

**Metodi:**

- `encode(code: str, context: EncodingContext) -> Tuple[str, Dict]`
- `decode(encoded: str, metadata: Dict) -> str`
- `get_stats() -> Dict[str, Any]`

#### `EncodingContext`

Configurazione per il processo di codifica.

**Parametri:**

- `language: str` - Linguaggio di programmazione
- `encoding_level: int` - Livello di compressione (1-3)
- `preserve_comments: bool` - Mantieni commenti
- `semantic_analysis: bool` - Abilita analisi semantica

### Runtime Wrapper

#### `NeuroGlyphRuntime`

Runtime per esecuzione sicura di codice decodificato.

**Metodi:**

- `execute(code: str, context: ExecutionContext) -> ExecutionResult`
- `get_stats() -> Dict[str, Any]`

#### `ExecutionContext`

Configurazione per l'esecuzione.

**Parametri:**

- `language: str` - Linguaggio di programmazione
- `timeout: float` - Timeout in secondi
- `memory_limit: int` - Limite memoria in bytes
- `sandbox_mode: bool` - Modalità sandbox

## 💡 Esempi Pratici

### Esempio 1: Compressione Codice

```python
# Codice complesso da comprimere
complex_code = """
class DataProcessor:
    def __init__(self, config):
        self.config = config
        self.data = []
    
    def process_batch(self, batch):
        results = []
        for item in batch:
            if self.validate_item(item):
                processed = self.transform_item(item)
                results.append(processed)
        return results
    
    def validate_item(self, item):
        return item is not None and len(item) > 0
    
    def transform_item(self, item):
        return item.upper().strip()
"""

# Codifica con livello ultra
context = EncodingContext(language="python", encoding_level=3)
encoded, metadata = encoder.encode(complex_code, context)

print(f"Riduzione: {len(complex_code)} → {len(encoded)} caratteri")
print(f"Compressione: {metadata['compression_ratio']:.1%}")
```

### Esempio 2: Generazione Multi-linguaggio

```python
# Stesso algoritmo in linguaggi diversi
algorithms = {
    "python": "def quicksort(arr): ...",
    "javascript": "function quicksort(arr) { ... }",
    "rust": "fn quicksort(arr: Vec<i32>) -> Vec<i32> { ... }"
}

# Codifica tutti
encoded_algorithms = {}
for lang, code in algorithms.items():
    context = EncodingContext(language=lang)
    encoded, metadata = encoder.encode(code, context)
    encoded_algorithms[lang] = (encoded, metadata)

# Analizza pattern comuni
print("Pattern neuroglifi comuni:")
for lang, (encoded, _) in encoded_algorithms.items():
    print(f"{lang}: {encoded[:20]}...")
```

### Esempio 3: Pipeline Completa

```python
def neuroglyph_pipeline(source_file, target_language):
    """Pipeline completa: codifica → elaborazione → decodifica."""
    
    # 1. Leggi e codifica file sorgente
    with open(source_file, 'r') as f:
        source_code = f.read()
    
    context = EncodingContext(language="python", encoding_level=2)
    encoded, metadata = encoder.encode(source_code, context)
    
    # 2. Simula elaborazione LLM (traduzione linguaggio)
    # In un caso reale, qui useresti il modello LLM
    translated_metadata = metadata.copy()
    translated_metadata['language'] = target_language
    
    # 3. Decodifica nel linguaggio target
    decoded = encoder.decode(encoded, translated_metadata)
    
    # 4. Esegui per verificare correttezza
    exec_context = ExecutionContext(language=target_language)
    result = runtime.execute(decoded, exec_context)
    
    return {
        'original_size': len(source_code),
        'compressed_size': len(encoded),
        'compression_ratio': metadata['compression_ratio'],
        'execution_success': result.success,
        'translated_code': decoded
    }

# Utilizzo
result = neuroglyph_pipeline("examples/fibonacci.py", "javascript")
print(f"Compressione: {result['compression_ratio']:.1%}")
print(f"Esecuzione: {'✅' if result['execution_success'] else '❌'}")
```

## 🤝 Contribuire

### Setup Ambiente Development

```bash
# Clone e setup
git clone https://github.com/JoyciAkira/NEUROGLIPH.git
cd NEUROGLIPH
python scripts/setup.py --dev

# Installa pre-commit hooks
pre-commit install

# Esegui test
pytest tests/
```

### Linee Guida

1. **Code Style**: Usa Black per formatting, isort per import
2. **Testing**: Scrivi test per nuove funzionalità
3. **Documentation**: Aggiorna documentazione per modifiche API
4. **Commit**: Usa conventional commits (feat:, fix:, docs:, etc.)

### Aree di Contributo

- 🔧 **Core Engine**: Miglioramenti algoritmi codifica
- 🤖 **LLM Integration**: Nuovi modelli e ottimizzazioni
- 🌐 **Language Support**: Supporto nuovi linguaggi
- 📊 **Benchmarking**: Nuovi dataset e metriche
- 📚 **Documentation**: Guide e tutorial
- 🐛 **Bug Fixes**: Correzioni e stabilità

## 📞 Supporto

- **📖 Documentazione**: [docs/](.)
- **🐛 Issues**: [GitHub Issues](https://github.com/JoyciAkira/NEUROGLIPH/issues)
- **💬 Discussioni**: [GitHub Discussions](https://github.com/JoyciAkira/NEUROGLIPH/discussions)
- **📧 Email**: <EMAIL>

## 📄 Licenza

Questo progetto è rilasciato sotto licenza MIT. Vedi [LICENSE](../LICENSE) per dettagli.

---

*Documentazione aggiornata al 2024 - NEUROGLIPH Project*
