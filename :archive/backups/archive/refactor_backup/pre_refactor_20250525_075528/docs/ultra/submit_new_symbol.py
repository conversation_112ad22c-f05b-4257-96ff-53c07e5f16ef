"""
Script per sottomettere un nuovo simbolo al registry.
"""
import json
from pathlib import Path

registry_path = Path("symbol_validation/symbols_registry.json")
symbol_path = Path("core/symbols_ultra.json")

def main():
    new_symbol = {
        "symbol": input("Unicode: ").strip(),
        "code": input("Code (ng:...): ").strip(),
        "fallback": input("ASCII fallback: ").strip(),
        "category": input("Category: ").strip(),
        "meaning": input("Meaning: ").strip(),
        "llm_support": ["openai", "qwen", "llama.cpp"],
        "token_cost": 1,
        "token_density": 1.0,
        "examples": []
    }

    with open(symbol_path, "r", encoding="utf-8") as f:
        sym_data = json.load(f)

    if any(s["symbol"] == new_symbol["symbol"] or s["code"] == new_symbol["code"] for s in sym_data["symbols"]):
        print("❌ Symbol or code already exists.")
        return

    sym_data["symbols"].append(new_symbol)
    with open(symbol_path, "w", encoding="utf-8") as f:
        json.dump(sym_data, f, indent=2, ensure_ascii=False)

    with open(registry_path, "r", encoding="utf-8") as f:
        registry = json.load(f)
    registry.append(new_symbol)
    with open(registry_path, "w", encoding="utf-8") as f:
        json.dump(registry, f, indent=2, ensure_ascii=False)

    print("✅ Symbol submitted and registered.")

if __name__ == "__main__":
    main()
