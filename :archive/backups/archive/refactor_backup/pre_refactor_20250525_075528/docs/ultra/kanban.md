# 🗂️ NEUROGLYPH ULTRA — Kanban Board

Organized view of the development progress using a Kanban-style layout. Tasks move from TODO → IN PROGRESS → DONE.

---

## 📝 TODO

- [ ] Add more NG→Code examples to `datasets/ng_to_code.jsonl`
- [ ] Write `train_socrate_lora.py` for LoRA fine-tuning
- [ ] Evaluate planner with 10 real-world prompts
- [ ] Link memory retrieval to `ultra_wrapper.py`
- [ ] Integrate roundtrip and DAG simulator in end-to-end pipeline
- [ ] Begin promotion logic for macro-symbols in `symbols.json`

---

## 🚧 IN PROGRESS

- [ ] Finalize memory retrieval scoring strategy
- [ ] Refine reasoning DAG patterns for nested logic cases
- [ ] Generate LoRA config for training with Axolotl (1.3B)

---

## ✅ DONE

- [x] Created symbolic vocabulary (1024 symbols)
- [x] Built encoder for text ⇄ NG
- [x] Implemented `planner.py` (DAG reasoning)
- [x] Created `roundtrip.py` verification logic
- [x] Implemented `memory_store.py` for symbolic memory
- [x] Designed simulation engine `logic_simulator.py`
- [x] Created full documentation in `docs/ultra/`
- [x] Generated `project_roadmap.md` + `ultra_module_index.md` + `todo.md`
