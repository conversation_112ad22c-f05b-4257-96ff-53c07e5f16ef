[{"task": "Complete NG→Code dataset (~500 entries)", "status": "in-progress", "priority": "high"}, {"task": "Implement `train_socrate_lora.py`", "status": "pending", "priority": "medium"}, {"task": "Evaluate DAG reasoning on 10 real prompts", "status": "pending", "priority": "medium"}, {"task": "Promote first macro-symbols from observed patterns", "status": "pending", "priority": "low"}, {"task": "Write ultra_runtime end-to-end test", "status": "pending", "priority": "high"}, {"task": "Link memory_store with ultra_wrapper", "status": "pending", "priority": "medium"}]