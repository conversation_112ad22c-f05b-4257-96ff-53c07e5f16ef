# ✅ NEUROGLYPH ULTRA — Operational Checklist

> Step-by-step execution guide from vocab initialization to full symbolic LLM Ultra runtime.

---

## 🔰 PHASE 0 — Initialization

- [✅] Copy `symbols_512.json` to `core/symbols.json` ✅ COMPLETED
- [✅] Validate symbol format via `load_symbols.py` ✅ COMPLETED
- [✅] **Generate 512 ULTRA symbols with USU/CTU/LCL criteria** ✅ COMPLETED
- [ ] Finalize `encoder.py` with encode/decode functions

**Status**: 🚀 **512 SIMBOLI ULTRA COMPLETATI** - USU/CTU/LCL criteria implemented, ready for first thinking LLM!

---

## 🧠 PHASE 1 — Symbolic Encoding

- [ ] Run roundtrip tests on 20 real prompts
- [ ] Populate `text_to_ng.jsonl` with valid samples
- [ ] Integrate `roundtrip.py` into encoder validation

---

## 🧩 PHASE 2 — Reasoning (SOCRATE)

- [ ] Use `planner.py` to create reasoning DAG
- [ ] Validate DAGs using `logic_simulator.py`
- [ ] Document reasoning examples in `dag_reasoning_example.json`

---

## ♾️ PHASE 3 — Memory Engine (GOD base)

- [ ] Store tasks and NG output in `memory_store.py`
- [ ] Use `symbol_registry.json` to track symbol status
- [ ] Promote first dynamic macro-symbol from reasoning DAG

---

## 🧪 PHASE 4 — Symbol Verification

- [ ] Evaluate symbols using `symbol_evaluator.py`
- [ ] Score against criteria from `symbol_score_schema.json`
- [ ] Mark deprecated/approved in `symbol_registry.json`

---

## 🧬 PHASE 5 — NG to Code Dataset

- [ ] Populate `ng_to_code.jsonl` with 500 examples
- [ ] Run roundtrip/code tests on each entry
- [ ] Log `tokens_saved` and `entropy_gain` metrics

---

## 🧠 PHASE 6 — LLM Training (SOCRATE LoRA)

- [ ] Select base model (`phi-2`, `mistral`)
- [ ] Configure `training.yaml` and `train_socrate_lora.py`
- [ ] Train on local (Mac M2) or cloud
- [ ] Evaluate with `eval_results.md`

---

## 🔄 PHASE 7 — ULTRA Runtime

- [ ] Finalize `ultra_wrapper.py` with full pipeline
- [ ] Perform 20 end-to-end tests on real prompts
- [ ] Save to `ultra_output_tests.jsonl`

---

## 🔁 PHASE 8 — Auto-Promotion and Evolution

- [ ] Auto-promote high-score patterns from memory
- [ ] Update `symbol_registry.json` with versioning
- [ ] Track symbol metrics in `symbol_stats.md`

---

## 🌐 PHASE 9 — Public Release & Demo

- [ ] Record full symbolic pipeline video
- [ ] Finalize and publish `neuroglyph_paper.md`
- [ ] Push model and docs to HuggingFace/arXiv
- [ ] Review `public_release_checklist.md`

---

✔️ *Always prefer traceable, testable, reversible symbols. Never promote a symbol that hasn’t earned its meaning.*
