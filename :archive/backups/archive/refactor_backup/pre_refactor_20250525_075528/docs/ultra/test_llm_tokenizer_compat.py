"""
Test compatibilità simboli con tokenizer (simulato per ora)
"""
import json
from pathlib import Path

with open("core/symbols_ultra.json", "r", encoding="utf-8") as f:
    data = json.load(f)

symbols = data["symbols"]

print("🔍 Simulazione Tokenizer (placeholder)")
for s in symbols:
    symbol = s["symbol"]
    code = s["code"]
    token_len = len(symbol.encode("utf-8"))
    print(f"{symbol} ({code}): token bytes = {token_len}")
