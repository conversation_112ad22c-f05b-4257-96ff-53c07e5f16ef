{"registry_version": "1.0", "created": "2024-12-19", "description": "NEUROGLYPH ULTRA Symbol Registry - Traccia storico simboli, approvazioni, rigetti", "stats": {"total_submissions": 567, "approved": 567, "rejected": 0, "pending": 0}, "approved_symbols": [{"id": "NG0001", "symbol": "⊕", "code": "ng:operator:add", "fallback": "[+]", "approved_date": "2024-12-19", "validation_score": 100.0, "status": "certified"}, {"id": "NG0002", "symbol": "⊖", "code": "ng:operator:subtract", "fallback": "[-]", "approved_date": "2024-12-19", "validation_score": 100.0, "status": "certified"}, {"id": "NG0003", "symbol": "⊗", "code": "ng:operator:multiply", "fallback": "[*]", "approved_date": "2024-12-19", "validation_score": 100.0, "status": "certified"}, {"id": "NG0004", "symbol": "⊘", "code": "ng:operator:divide", "fallback": "[/]", "approved_date": "2024-12-19", "validation_score": 100.0, "status": "certified"}, {"id": "NG0005", "symbol": "≡", "code": "ng:logic:equals", "fallback": "[==]", "approved_date": "2024-12-19", "validation_score": 100.0, "status": "certified"}, {"id": "NG0006", "symbol": "≢", "code": "ng:logic:not_equals", "fallback": "[!=]", "approved_date": "2024-12-19", "validation_score": 100.0, "status": "certified"}, {"id": "NG0007", "symbol": "∧", "code": "ng:logic:and", "fallback": "[AND]", "approved_date": "2024-12-19", "validation_score": 100.0, "status": "certified"}, {"id": "NG0008", "symbol": "∨", "code": "ng:logic:or", "fallback": "[OR]", "approved_date": "2024-12-19", "validation_score": 100.0, "status": "certified"}, {"id": "NG0009", "symbol": "¬", "code": "ng:logic:not", "fallback": "[NOT]", "approved_date": "2024-12-19", "validation_score": 100.0, "status": "certified"}, {"id": "NG0010", "symbol": "∈", "code": "ng:logic:in", "fallback": "[IN]", "approved_date": "2024-12-19", "validation_score": 100.0, "status": "certified"}, {"id": "NG0011", "symbol": "∉", "code": "ng:logic:not_in", "fallback": "[NOTIN]", "approved_date": "2024-12-19", "validation_score": 100.0, "status": "certified"}, {"id": "NG0012", "symbol": "⊨", "code": "ng:reasoning:entails", "fallback": "[ENTAILS]", "approved_date": "2024-12-19", "validation_score": 100.0, "status": "certified"}, {"id": "NG0013", "symbol": "⇌", "code": "ng:logic:biconditional", "fallback": "[IFF]", "approved_date": "2024-12-19", "validation_score": 100.0, "status": "certified"}, {"id": "NG0014", "symbol": "⟨⟩", "code": "ng:structure:function", "fallback": "[FN]", "approved_date": "2024-12-19", "validation_score": 100.0, "status": "certified"}, {"id": "NG0015", "symbol": "⟪⟫", "code": "ng:structure:class", "fallback": "[CLS]", "approved_date": "2024-12-19", "validation_score": 100.0, "status": "certified"}, {"id": "NG0016", "symbol": "◊", "code": "ng:flow:if", "fallback": "[IF]", "approved_date": "2024-12-19", "validation_score": 100.0, "status": "certified"}, {"id": "NG0017", "symbol": "◈", "code": "ng:flow:else", "fallback": "[ELSE]", "approved_date": "2024-12-19", "validation_score": 100.0, "status": "certified"}, {"id": "NG0018", "symbol": "⟲", "code": "ng:flow:for", "fallback": "[FOR]", "approved_date": "2024-12-19", "validation_score": 100.0, "status": "certified"}, {"id": "NG0019", "symbol": "⟳", "code": "ng:flow:while", "fallback": "[WHILE]", "approved_date": "2024-12-19", "validation_score": 100.0, "status": "certified"}, {"id": "NG0020", "symbol": "⤴", "code": "ng:flow:return", "fallback": "[RETURN]", "approved_date": "2024-12-19", "validation_score": 100.0, "status": "certified"}, {"id": "NG0021", "symbol": "◯", "code": "ng:operator:sub", "fallback": "[SUB]", "category": "operator", "name": "sub", "description": "Operator operation: sub", "unicode_point": "U+25EF", "approved_date": "2025-05-23", "validation_score": 95.0, "status": "certified", "token_cost": 1, "auto_generated": true, "generator": "simple"}, {"id": "NG0022", "symbol": "■", "code": "ng:memory:pointer", "fallback": "[POINTER]", "category": "memory", "name": "pointer", "description": "Memory operation: pointer", "unicode_point": "U+25A0", "approved_date": "2025-05-23", "validation_score": 95.0, "status": "certified", "token_cost": 1, "auto_generated": true, "generator": "simple"}, {"id": "NG0023", "symbol": "☎", "code": "ng:memory:alloc", "fallback": "[ALLOC]", "category": "memory", "name": "alloc", "description": "Memory operation: alloc", "unicode_point": "U+260E", "approved_date": "2025-05-23", "validation_score": 95.0, "status": "certified", "token_cost": 1, "auto_generated": true, "generator": "simple"}, {"id": "NG0024", "symbol": "≺", "code": "ng:logic:implies", "fallback": "[IMPLIES]", "category": "logic", "name": "implies", "description": "Logic operation: implies", "unicode_point": "U+227A", "approved_date": "2025-05-23", "validation_score": 95.0, "status": "certified", "token_cost": 1, "auto_generated": true, "generator": "simple"}, {"id": "NG0025", "symbol": "☬", "code": "ng:logic:or_1", "fallback": "[OR1]", "category": "logic", "name": "or_1", "description": "Logic operation: or_1", "unicode_point": "U+262C", "approved_date": "2025-05-23", "validation_score": 95.0, "status": "certified", "token_cost": 1, "auto_generated": true, "generator": "simple"}, {"id": "NG0026", "symbol": "⛑", "code": "ng:memory:alloc_1", "fallback": "[ALLOC1]", "category": "memory", "name": "alloc_1", "description": "Memory operation: alloc_1", "unicode_point": "U+26D1", "approved_date": "2025-05-23", "validation_score": 95.0, "status": "certified", "token_cost": 1, "auto_generated": true, "generator": "simple"}, {"id": "NG0027", "symbol": "◟", "code": "ng:memory:free", "fallback": "[FREE]", "category": "memory", "name": "free", "description": "Memory operation: free", "unicode_point": "U+25DF", "approved_date": "2025-05-23", "validation_score": 95.0, "status": "certified", "token_cost": 1, "auto_generated": true, "generator": "simple"}, {"id": "NG0028", "symbol": "⊬", "code": "ng:structure:function_1", "fallback": "[FUNCTION1]", "category": "structure", "name": "function_1", "description": "Structure operation: function_1", "unicode_point": "U+22AC", "approved_date": "2025-05-23", "validation_score": 95.0, "status": "certified", "token_cost": 1, "auto_generated": true, "generator": "simple"}, {"id": "NG0029", "symbol": "≯", "code": "ng:operator:mul", "fallback": "[MUL]", "category": "operator", "name": "mul", "description": "Operator operation: mul", "unicode_point": "U+226F", "approved_date": "2025-05-23", "validation_score": 95.0, "status": "certified", "token_cost": 1, "auto_generated": true, "generator": "simple"}, {"id": "NG0030", "symbol": "↢", "code": "ng:operator:mod", "fallback": "[MOD]", "category": "operator", "name": "mod", "description": "Operator operation: mod", "unicode_point": "U+21A2", "approved_date": "2025-05-23", "validation_score": 95.0, "status": "certified", "token_cost": 1, "auto_generated": true, "generator": "simple"}, {"id": "NG0031", "symbol": "◴", "code": "ng:operator:add_1", "fallback": "[ADD1]", "category": "operator", "name": "add_1", "description": "Operator operation: add_1", "unicode_point": "U+25F4", "approved_date": "2025-05-23", "validation_score": 95.0, "status": "certified", "token_cost": 1, "auto_generated": true, "generator": "simple"}, {"id": "NG0032", "symbol": "□", "code": "ng:memory:deref", "fallback": "[DEREF]", "category": "memory", "name": "deref", "description": "Memory operation: deref", "unicode_point": "U+25A1", "approved_date": "2025-05-23", "validation_score": 95.0, "status": "certified", "token_cost": 1, "auto_generated": true, "generator": "simple"}, {"id": "NG0033", "symbol": "⇵", "code": "ng:operator:add_2", "fallback": "[ADD2]", "category": "operator", "name": "add_2", "description": "Operator operation: add_2", "unicode_point": "U+21F5", "approved_date": "2025-05-23", "validation_score": 95.0, "status": "certified", "token_cost": 1, "auto_generated": true, "generator": "simple"}, {"id": "NG0034", "symbol": "⇣", "code": "ng:structure:property", "fallback": "[PROPERTY]", "category": "structure", "name": "property", "description": "Structure operation: property", "unicode_point": "U+21E3", "approved_date": "2025-05-23", "validation_score": 95.0, "status": "certified", "token_cost": 1, "auto_generated": true, "generator": "simple"}, {"id": "NG0035", "symbol": "◡", "code": "ng:flow:return_1", "fallback": "[RETURN1]", "category": "flow", "name": "return_1", "description": "Flow operation: return_1", "unicode_point": "U+25E1", "approved_date": "2025-05-23", "validation_score": 95.0, "status": "certified", "token_cost": 1, "auto_generated": true, "generator": "simple"}, {"id": "NG0036", "symbol": "◼", "code": "ng:memory:alloc_2", "fallback": "[ALLOC2]", "category": "memory", "name": "alloc_2", "description": "Memory operation: alloc_2", "unicode_point": "U+25FC", "approved_date": "2025-05-23", "validation_score": 95.0, "status": "certified", "token_cost": 1, "auto_generated": true, "generator": "simple"}, {"id": "NG0037", "symbol": "⊁", "code": "ng:flow:for_1", "fallback": "[FOR1]", "category": "flow", "name": "for_1", "description": "Flow operation: for_1", "unicode_point": "U+2281", "approved_date": "2025-05-23", "validation_score": 95.0, "status": "certified", "token_cost": 1, "auto_generated": true, "generator": "simple"}, {"id": "NG0038", "symbol": "⋲", "code": "ng:operator:add_3", "fallback": "[ADD3]", "category": "operator", "name": "add_3", "description": "Operator operation: add_3", "unicode_point": "U+22F2", "approved_date": "2025-05-23", "validation_score": 95.0, "status": "certified", "token_cost": 1, "auto_generated": true, "generator": "simple"}, {"id": "NG0039", "symbol": "≙", "code": "ng:flow:if_1", "fallback": "[IF1]", "category": "flow", "name": "if_1", "description": "Flow operation: if_1", "unicode_point": "U+2259", "approved_date": "2025-05-23", "validation_score": 95.0, "status": "certified", "token_cost": 1, "auto_generated": true, "generator": "simple"}, {"id": "NG0040", "symbol": "⋂", "code": "ng:operator:mul_1", "fallback": "[MUL1]", "category": "operator", "name": "mul_1", "description": "Operator operation: mul_1", "unicode_point": "U+22C2", "approved_date": "2025-05-23", "validation_score": 95.0, "status": "certified", "token_cost": 1, "auto_generated": true, "generator": "simple"}, {"id": "NG0041", "symbol": "♄", "code": "ng:logic:and_1", "fallback": "[AND1]", "category": "logic", "name": "and_1", "description": "Logic operation: and_1", "unicode_point": "U+2644", "approved_date": "2025-05-23", "validation_score": 95.0, "status": "certified", "token_cost": 1, "auto_generated": true, "generator": "simple"}, {"id": "NG0042", "symbol": "↤", "code": "ng:structure:function_2", "fallback": "[FUNCTION2]", "category": "structure", "name": "function_2", "description": "Structure operation: function_2", "unicode_point": "U+21A4", "approved_date": "2025-05-23", "validation_score": 95.0, "status": "certified", "token_cost": 1, "auto_generated": true, "generator": "simple"}, {"id": "NG0043", "symbol": "↰", "code": "ng:structure:property_1", "fallback": "[PROPERTY1]", "category": "structure", "name": "property_1", "description": "Structure operation: property_1", "unicode_point": "U+21B0", "approved_date": "2025-05-23", "validation_score": 95.0, "status": "certified", "token_cost": 1, "auto_generated": true, "generator": "simple"}, {"id": "NG0044", "symbol": "⚌", "code": "ng:memory:pointer_1", "fallback": "[POINTER1]", "category": "memory", "name": "pointer_1", "description": "Memory operation: pointer_1", "unicode_point": "U+268C", "approved_date": "2025-05-23", "validation_score": 95.0, "status": "certified", "token_cost": 1, "auto_generated": true, "generator": "simple"}, {"id": "NG0045", "symbol": "↘", "code": "ng:flow:break", "fallback": "[BREAK]", "category": "flow", "name": "break", "description": "Flow operation: break", "unicode_point": "U+2198", "approved_date": "2025-05-23", "validation_score": 95.0, "status": "certified", "token_cost": 1, "auto_generated": true, "generator": "simple"}, {"id": "NG0046", "symbol": "◗", "code": "ng:logic:not_1", "fallback": "[NOT1]", "category": "logic", "name": "not_1", "description": "Logic operation: not_1", "unicode_point": "U+25D7", "approved_date": "2025-05-23", "validation_score": 95.0, "status": "certified", "token_cost": 1, "auto_generated": true, "generator": "simple"}, {"id": "NG0047", "symbol": "☤", "code": "ng:logic:implies_1", "fallback": "[IMPLIES1]", "category": "logic", "name": "implies_1", "description": "Logic operation: implies_1", "unicode_point": "U+2624", "approved_date": "2025-05-23", "validation_score": 95.0, "status": "certified", "token_cost": 1, "auto_generated": true, "generator": "simple"}, {"id": "NG0048", "symbol": "⚓", "code": "ng:flow:break_1", "fallback": "[BREAK1]", "category": "flow", "name": "break_1", "description": "Flow operation: break_1", "unicode_point": "U+2693", "approved_date": "2025-05-23", "validation_score": 95.0, "status": "certified", "token_cost": 1, "auto_generated": true, "generator": "simple"}, {"id": "NG0049", "symbol": "◜", "code": "ng:operator:div", "fallback": "[DIV]", "category": "operator", "name": "div", "description": "Operator operation: div", "unicode_point": "U+25DC", "approved_date": "2025-05-23", "validation_score": 95.0, "status": "certified", "token_cost": 1, "auto_generated": true, "generator": "simple"}, {"id": "NG0050", "symbol": "◿", "code": "ng:operator:add_4", "fallback": "[ADD4]", "category": "operator", "name": "add_4", "description": "Operator operation: add_4", "unicode_point": "U+25FF", "approved_date": "2025-05-23", "validation_score": 95.0, "status": "certified", "token_cost": 1, "auto_generated": true, "generator": "simple"}, {"id": "NG0051", "symbol": "∢", "code": "ng:flow:if_2", "fallback": "[IF2]", "category": "flow", "name": "if_2", "description": "Flow operation: if_2", "unicode_point": "U+2222", "approved_date": "2025-05-23", "validation_score": 95.0, "status": "certified", "token_cost": 1, "auto_generated": true, "generator": "simple"}, {"id": "NG0052", "symbol": "◇", "code": "ng:logic:or_2", "fallback": "[OR2]", "category": "logic", "name": "or_2", "description": "Logic operation: or_2", "unicode_point": "U+25C7", "approved_date": "2025-05-23", "validation_score": 95.0, "status": "certified", "token_cost": 1, "auto_generated": true, "generator": "simple"}, {"id": "NG0053", "symbol": "☴", "code": "ng:memory:deref_1", "fallback": "[DEREF1]", "category": "memory", "name": "deref_1", "description": "Memory operation: deref_1", "unicode_point": "U+2634", "approved_date": "2025-05-23", "validation_score": 95.0, "status": "certified", "token_cost": 1, "auto_generated": true, "generator": "simple"}, {"id": "NG0054", "symbol": "◆", "code": "ng:structure:class_1", "fallback": "[CLASS1]", "category": "structure", "name": "class_1", "description": "Structure operation: class_1", "unicode_point": "U+25C6", "approved_date": "2025-05-23", "validation_score": 95.0, "status": "certified", "token_cost": 1, "auto_generated": true, "generator": "simple"}, {"id": "NG0055", "symbol": "←", "code": "ng:operator:div_1", "fallback": "[DIV1]", "category": "operator", "name": "div_1", "description": "Operator operation: div_1", "unicode_point": "U+2190", "approved_date": "2025-05-23", "validation_score": 95.0, "status": "certified", "token_cost": 1, "auto_generated": true, "generator": "simple"}, {"id": "NG0056", "symbol": "◧", "code": "ng:memory:pointer_2", "fallback": "[POINTER2]", "category": "memory", "name": "pointer_2", "description": "Memory operation: pointer_2", "unicode_point": "U+25E7", "approved_date": "2025-05-23", "validation_score": 95.0, "status": "certified", "token_cost": 1, "auto_generated": true, "generator": "simple"}, {"id": "NG0057", "symbol": "⇃", "code": "ng:flow:break_2", "fallback": "[BREAK2]", "category": "flow", "name": "break_2", "description": "Flow operation: break_2", "unicode_point": "U+21C3", "approved_date": "2025-05-23", "validation_score": 95.0, "status": "certified", "token_cost": 1, "auto_generated": true, "generator": "simple"}, {"id": "NG0058", "symbol": "♑", "code": "ng:memory:deref_2", "fallback": "[DEREF2]", "category": "memory", "name": "deref_2", "description": "Memory operation: deref_2", "unicode_point": "U+2651", "approved_date": "2025-05-23", "validation_score": 95.0, "status": "certified", "token_cost": 1, "auto_generated": true, "generator": "simple"}, {"id": "NG0059", "symbol": "⇗", "code": "ng:operator:mod_1", "fallback": "[MOD1]", "category": "operator", "name": "mod_1", "description": "Operator operation: mod_1", "unicode_point": "U+21D7", "approved_date": "2025-05-23", "validation_score": 95.0, "status": "certified", "token_cost": 1, "auto_generated": true, "generator": "simple"}, {"id": "NG0060", "symbol": "⊠", "code": "ng:logic:implies_2", "fallback": "[IMPLIES2]", "category": "logic", "name": "implies_2", "description": "Logic operation: implies_2", "unicode_point": "U+22A0", "approved_date": "2025-05-23", "validation_score": 95.0, "status": "certified", "token_cost": 1, "auto_generated": true, "generator": "simple"}, {"id": "NG0061", "symbol": "⚁", "code": "ng:flow:for_2", "fallback": "[FOR2]", "category": "flow", "name": "for_2", "description": "Flow operation: for_2", "unicode_point": "U+2681", "approved_date": "2025-05-23", "validation_score": 95.0, "status": "certified", "token_cost": 1, "auto_generated": true, "generator": "simple"}, {"id": "NG0062", "symbol": "⋉", "code": "ng:logic:and_2", "fallback": "[AND2]", "category": "logic", "name": "and_2", "description": "Logic operation: and_2", "unicode_point": "U+22C9", "approved_date": "2025-05-23", "validation_score": 95.0, "status": "certified", "token_cost": 1, "auto_generated": true, "generator": "simple"}, {"id": "NG0063", "symbol": "⋨", "code": "ng:logic:implies_3", "fallback": "[IMPLIES3]", "category": "logic", "name": "implies_3", "description": "Logic operation: implies_3", "unicode_point": "U+22E8", "approved_date": "2025-05-23", "validation_score": 95.0, "status": "certified", "token_cost": 1, "auto_generated": true, "generator": "simple"}, {"id": "NG0064", "symbol": "⚮", "code": "ng:structure:property_2", "fallback": "[PROPERTY2]", "category": "structure", "name": "property_2", "description": "Structure operation: property_2", "unicode_point": "U+26AE", "approved_date": "2025-05-23", "validation_score": 95.0, "status": "certified", "token_cost": 1, "auto_generated": true, "generator": "simple"}, {"id": "NG0065", "symbol": "⇻", "code": "ng:operator:mod_2", "fallback": "[MOD2]", "category": "operator", "name": "mod_2", "description": "Operator operation: mod_2", "unicode_point": "U+21FB", "approved_date": "2025-05-23", "validation_score": 95.0, "status": "certified", "token_cost": 1, "auto_generated": true, "generator": "simple"}, {"id": "NG0066", "symbol": "≲", "code": "ng:logic:and_3", "fallback": "[AND3]", "category": "logic", "name": "and_3", "description": "Logic operation: and_3", "unicode_point": "U+2272", "approved_date": "2025-05-23", "validation_score": 95.0, "status": "certified", "token_cost": 1, "auto_generated": true, "generator": "simple"}, {"id": "NG0067", "symbol": "▤", "code": "ng:operator:pow", "fallback": "[POW]", "category": "operator", "name": "pow", "description": "Operator operation: pow", "unicode_point": "U+25A4", "approved_date": "2025-05-23", "validation_score": 95.0, "status": "certified", "token_cost": 1, "auto_generated": true, "generator": "simple"}, {"id": "NG0068", "symbol": "⊏", "code": "ng:structure:method", "fallback": "[METHOD]", "category": "structure", "name": "method", "description": "Structure operation: method", "unicode_point": "U+228F", "approved_date": "2025-05-23", "validation_score": 95.0, "status": "certified", "token_cost": 1, "auto_generated": true, "generator": "simple"}, {"id": "NG0069", "symbol": "∅", "code": "ng:memory:pointer_3", "fallback": "[POINTER3]", "category": "memory", "name": "pointer_3", "description": "Memory operation: pointer_3", "unicode_point": "U+2205", "approved_date": "2025-05-23", "validation_score": 95.0, "status": "certified", "token_cost": 1, "auto_generated": true, "generator": "simple"}, {"id": "NG0070", "symbol": "◂", "code": "ng:logic:not_2", "fallback": "[NOT2]", "category": "logic", "name": "not_2", "description": "Logic operation: not_2", "unicode_point": "U+25C2", "approved_date": "2025-05-23", "validation_score": 95.0, "status": "certified", "token_cost": 1, "auto_generated": true, "generator": "simple"}, {"id": "NG0071", "symbol": "◱", "code": "ng:operator:mod_3", "fallback": "[MOD3]", "category": "operator", "name": "mod_3", "description": "Operator operation: mod_3", "unicode_point": "U+25F1", "approved_date": "2025-05-23", "validation_score": 95.0, "status": "certified", "token_cost": 1, "auto_generated": true, "generator": "simple"}, {"id": "NG0072", "symbol": "⇈", "code": "ng:flow:break_3", "fallback": "[BREAK3]", "category": "flow", "name": "break_3", "description": "Flow operation: break_3", "unicode_point": "U+21C8", "approved_date": "2025-05-23", "validation_score": 95.0, "status": "certified", "token_cost": 1, "auto_generated": true, "generator": "simple"}, {"id": "NG0073", "symbol": "⚳", "code": "ng:flow:while_1", "fallback": "[WHILE1]", "category": "flow", "name": "while_1", "description": "Flow operation: while_1", "unicode_point": "U+26B3", "approved_date": "2025-05-23", "validation_score": 95.0, "status": "certified", "token_cost": 1, "auto_generated": true, "generator": "simple"}, {"id": "NG0074", "symbol": "↨", "code": "ng:flow:if_3", "fallback": "[IF3]", "category": "flow", "name": "if_3", "description": "Flow operation: if_3", "unicode_point": "U+21A8", "approved_date": "2025-05-23", "validation_score": 95.0, "status": "certified", "token_cost": 1, "auto_generated": true, "generator": "simple"}, {"id": "NG0075", "symbol": "♃", "code": "ng:operator:mod_4", "fallback": "[MOD4]", "category": "operator", "name": "mod_4", "description": "Operator operation: mod_4", "unicode_point": "U+2643", "approved_date": "2025-05-23", "validation_score": 95.0, "status": "certified", "token_cost": 1, "auto_generated": true, "generator": "simple"}, {"id": "NG0076", "symbol": "△", "code": "ng:operator:div_2", "fallback": "[DIV2]", "category": "operator", "name": "div_2", "description": "Operator operation: div_2", "unicode_point": "U+25B3", "approved_date": "2025-05-23", "validation_score": 95.0, "status": "certified", "token_cost": 1, "auto_generated": true, "generator": "simple"}, {"id": "NG0077", "symbol": "⛉", "code": "ng:operator:div_3", "fallback": "[DIV3]", "category": "operator", "name": "div_3", "description": "Operator operation: div_3", "unicode_point": "U+26C9", "approved_date": "2025-05-23", "validation_score": 95.0, "status": "certified", "token_cost": 1, "auto_generated": true, "generator": "simple"}, {"id": "NG0078", "symbol": "♒", "code": "ng:structure:property_3", "fallback": "[PROPERTY3]", "category": "structure", "name": "property_3", "description": "Structure operation: property_3", "unicode_point": "U+2652", "approved_date": "2025-05-23", "validation_score": 95.0, "status": "certified", "token_cost": 1, "auto_generated": true, "generator": "simple"}, {"id": "NG0079", "symbol": "⇦", "code": "ng:structure:property_4", "fallback": "[PROPERTY4]", "category": "structure", "name": "property_4", "description": "Structure operation: property_4", "unicode_point": "U+21E6", "approved_date": "2025-05-23", "validation_score": 95.0, "status": "certified", "token_cost": 1, "auto_generated": true, "generator": "simple"}, {"id": "NG0080", "symbol": "⚼", "code": "ng:flow:for_3", "fallback": "[FOR3]", "category": "flow", "name": "for_3", "description": "Flow operation: for_3", "unicode_point": "U+26BC", "approved_date": "2025-05-23", "validation_score": 95.0, "status": "certified", "token_cost": 1, "auto_generated": true, "generator": "simple"}, {"id": "NG0081", "symbol": "◢", "code": "ng:memory:deref_3", "fallback": "[DEREF3]", "category": "memory", "name": "deref_3", "description": "Memory operation: deref_3", "unicode_point": "U+25E2", "approved_date": "2025-05-23", "validation_score": 95.0, "status": "certified", "token_cost": 1, "auto_generated": true, "generator": "simple"}, {"id": "NG0082", "symbol": "◸", "code": "ng:memory:pointer_4", "fallback": "[POINTER4]", "category": "memory", "name": "pointer_4", "description": "Memory operation: pointer_4", "unicode_point": "U+25F8", "approved_date": "2025-05-23", "validation_score": 95.0, "status": "certified", "token_cost": 1, "auto_generated": true, "generator": "simple"}, {"id": "NG0083", "symbol": "◨", "code": "ng:operator:add_5", "fallback": "[ADD5]", "category": "operator", "name": "add_5", "description": "Operator operation: add_5", "unicode_point": "U+25E8", "approved_date": "2025-05-23", "validation_score": 95.0, "status": "certified", "token_cost": 1, "auto_generated": true, "generator": "simple"}, {"id": "NG0084", "symbol": "⇆", "code": "ng:logic:and_4", "fallback": "[AND4]", "category": "logic", "name": "and_4", "description": "Logic operation: and_4", "unicode_point": "U+21C6", "approved_date": "2025-05-23", "validation_score": 95.0, "status": "certified", "token_cost": 1, "auto_generated": true, "generator": "simple"}, {"id": "NG0085", "symbol": "⊃", "code": "ng:flow:while_2", "fallback": "[WHILE2]", "category": "flow", "name": "while_2", "description": "Flow operation: while_2", "unicode_point": "U+2283", "approved_date": "2025-05-23", "validation_score": 95.0, "status": "certified", "token_cost": 1, "auto_generated": true, "generator": "simple"}, {"id": "NG0086", "symbol": "◄", "code": "ng:flow:while_3", "fallback": "[WHILE3]", "category": "flow", "name": "while_3", "description": "Flow operation: while_3", "unicode_point": "U+25C4", "approved_date": "2025-05-23", "validation_score": 95.0, "status": "certified", "token_cost": 1, "auto_generated": true, "generator": "simple"}, {"id": "NG0087", "symbol": "⇟", "code": "ng:structure:function_3", "fallback": "[FUNCTION3]", "category": "structure", "name": "function_3", "description": "Structure operation: function_3", "unicode_point": "U+21DF", "approved_date": "2025-05-23", "validation_score": 95.0, "status": "certified", "token_cost": 1, "auto_generated": true, "generator": "simple"}, {"id": "NG0088", "symbol": "∗", "code": "ng:operator:mul_2", "fallback": "[MUL2]", "category": "operator", "name": "mul_2", "description": "Operator operation: mul_2", "unicode_point": "U+2217", "approved_date": "2025-05-23", "validation_score": 95.0, "status": "certified", "token_cost": 1, "auto_generated": true, "generator": "simple"}, {"id": "NG0089", "symbol": "∿", "code": "ng:logic:implies_4", "fallback": "[IMPLIES4]", "category": "logic", "name": "implies_4", "description": "Logic operation: implies_4", "unicode_point": "U+223F", "approved_date": "2025-05-23", "validation_score": 95.0, "status": "certified", "token_cost": 1, "auto_generated": true, "generator": "simple"}, {"id": "NG0090", "symbol": "▩", "code": "ng:logic:xor", "fallback": "[XOR]", "category": "logic", "name": "xor", "description": "Logic operation: xor", "unicode_point": "U+25A9", "approved_date": "2025-05-23", "validation_score": 95.0, "status": "certified", "token_cost": 1, "auto_generated": true, "generator": "simple"}, {"id": "NG0091", "symbol": "♶", "code": "ng:flow:else_1", "fallback": "[ELSE1]", "category": "flow", "name": "else_1", "description": "Flow operation: else_1", "unicode_point": "U+2676", "approved_date": "2025-05-23", "validation_score": 95.0, "status": "certified", "token_cost": 1, "auto_generated": true, "generator": "simple"}, {"id": "NG0092", "symbol": "♋", "code": "ng:structure:method_1", "fallback": "[METHOD1]", "category": "structure", "name": "method_1", "description": "Structure operation: method_1", "unicode_point": "U+264B", "approved_date": "2025-05-23", "validation_score": 95.0, "status": "certified", "token_cost": 1, "auto_generated": true, "generator": "simple"}, {"id": "NG0093", "symbol": "☏", "code": "ng:structure:property_5", "fallback": "[PROPERTY5]", "category": "structure", "name": "property_5", "description": "Structure operation: property_5", "unicode_point": "U+260F", "approved_date": "2025-05-23", "validation_score": 95.0, "status": "certified", "token_cost": 1, "auto_generated": true, "generator": "simple"}, {"id": "NG0094", "symbol": "⊊", "code": "ng:flow:else_2", "fallback": "[ELSE2]", "category": "flow", "name": "else_2", "description": "Flow operation: else_2", "unicode_point": "U+228A", "approved_date": "2025-05-23", "validation_score": 95.0, "status": "certified", "token_cost": 1, "auto_generated": true, "generator": "simple"}, {"id": "NG0095", "symbol": "∱", "code": "ng:memory:alloc_3", "fallback": "[ALLOC3]", "category": "memory", "name": "alloc_3", "description": "Memory operation: alloc_3", "unicode_point": "U+2231", "approved_date": "2025-05-23", "validation_score": 95.0, "status": "certified", "token_cost": 1, "auto_generated": true, "generator": "simple"}, {"id": "NG0096", "symbol": "⚆", "code": "ng:operator:sub_1", "fallback": "[SUB1]", "category": "operator", "name": "sub_1", "description": "Operator operation: sub_1", "unicode_point": "U+2686", "approved_date": "2025-05-23", "validation_score": 95.0, "status": "certified", "token_cost": 1, "auto_generated": true, "generator": "simple"}, {"id": "NG0097", "symbol": "∀", "code": "ng:operator:div_4", "fallback": "[DIV4]", "category": "operator", "name": "div_4", "description": "Operator operation: div_4", "unicode_point": "U+2200", "approved_date": "2025-05-23", "validation_score": 95.0, "status": "certified", "token_cost": 1, "auto_generated": true, "generator": "simple"}, {"id": "NG0098", "symbol": "☘", "code": "ng:memory:deref_4", "fallback": "[DEREF4]", "category": "memory", "name": "deref_4", "description": "Memory operation: deref_4", "unicode_point": "U+2618", "approved_date": "2025-05-23", "validation_score": 95.0, "status": "certified", "token_cost": 1, "auto_generated": true, "generator": "simple"}, {"id": "NG0099", "symbol": "♬", "code": "ng:structure:class_2", "fallback": "[CLASS2]", "category": "structure", "name": "class_2", "description": "Structure operation: class_2", "unicode_point": "U+266C", "approved_date": "2025-05-23", "validation_score": 95.0, "status": "certified", "token_cost": 1, "auto_generated": true, "generator": "simple"}, {"id": "NG0100", "symbol": "⋁", "code": "ng:memory:pointer_5", "fallback": "[POINTER5]", "category": "memory", "name": "pointer_5", "description": "Memory operation: pointer_5", "unicode_point": "U+22C1", "approved_date": "2025-05-23", "validation_score": 95.0, "status": "certified", "token_cost": 1, "auto_generated": true, "generator": "simple"}, {"id": "NG0101", "symbol": "⋳", "code": "ng:flow:while_4", "fallback": "[WHILE4]", "category": "flow", "name": "while_4", "description": "Flow operation: while_4", "unicode_point": "U+22F3", "approved_date": "2025-05-23", "validation_score": 95.0, "status": "certified", "token_cost": 1, "auto_generated": true, "generator": "simple"}, {"id": "NG0102", "symbol": "◔", "code": "ng:flow:if_4", "fallback": "[IF4]", "category": "flow", "name": "if_4", "description": "Flow operation: if_4", "unicode_point": "U+25D4", "approved_date": "2025-05-23", "validation_score": 95.0, "status": "certified", "token_cost": 1, "auto_generated": true, "generator": "simple"}, {"id": "NG0103", "symbol": "⇑", "code": "ng:memory:alloc_4", "fallback": "[ALLOC4]", "category": "memory", "name": "alloc_4", "description": "Memory operation: alloc_4", "unicode_point": "U+21D1", "approved_date": "2025-05-23", "validation_score": 95.0, "status": "certified", "token_cost": 1, "auto_generated": true, "generator": "simple"}, {"id": "NG0104", "symbol": "⋝", "code": "ng:logic:or_3", "fallback": "[OR3]", "category": "logic", "name": "or_3", "description": "Logic operation: or_3", "unicode_point": "U+22DD", "approved_date": "2025-05-23", "validation_score": 95.0, "status": "certified", "token_cost": 1, "auto_generated": true, "generator": "simple"}, {"id": "NG0105", "symbol": "⊉", "code": "ng:flow:while_5", "fallback": "[WHILE5]", "category": "flow", "name": "while_5", "description": "Flow operation: while_5", "unicode_point": "U+2289", "approved_date": "2025-05-23", "validation_score": 95.0, "status": "certified", "token_cost": 1, "auto_generated": true, "generator": "simple"}, {"id": "NG0106", "symbol": "♨", "code": "ng:flow:while_6", "fallback": "[WHILE6]", "category": "flow", "name": "while_6", "description": "Flow operation: while_6", "unicode_point": "U+2668", "approved_date": "2025-05-23", "validation_score": 95.0, "status": "certified", "token_cost": 1, "auto_generated": true, "generator": "simple"}, {"id": "NG0107", "symbol": "⇅", "code": "ng:structure:function_4", "fallback": "[FUNCTION4]", "category": "structure", "name": "function_4", "description": "Structure operation: function_4", "unicode_point": "U+21C5", "approved_date": "2025-05-23", "validation_score": 95.0, "status": "certified", "token_cost": 1, "auto_generated": true, "generator": "simple"}, {"id": "NG0108", "symbol": "∯", "code": "ng:logic:implies_5", "fallback": "[IMPLIES5]", "category": "logic", "name": "implies_5", "description": "Logic operation: implies_5", "unicode_point": "U+222F", "approved_date": "2025-05-23", "validation_score": 95.0, "status": "certified", "token_cost": 1, "auto_generated": true, "generator": "simple"}, {"id": "NG0109", "symbol": "▰", "code": "ng:logic:or_4", "fallback": "[OR4]", "category": "logic", "name": "or_4", "description": "Logic operation: or_4", "unicode_point": "U+25B0", "approved_date": "2025-05-23", "validation_score": 95.0, "status": "certified", "token_cost": 1, "auto_generated": true, "generator": "simple"}, {"id": "NG0110", "symbol": "◒", "code": "ng:memory:pointer_6", "fallback": "[POINTER6]", "category": "memory", "name": "pointer_6", "description": "Memory operation: pointer_6", "unicode_point": "U+25D2", "approved_date": "2025-05-23", "validation_score": 95.0, "status": "certified", "token_cost": 1, "auto_generated": true, "generator": "simple"}, {"id": "NG0111", "symbol": "⇎", "code": "ng:logic:not_3", "fallback": "[NOT3]", "category": "logic", "name": "not_3", "description": "Logic operation: not_3", "unicode_point": "U+21CE", "approved_date": "2025-05-23", "validation_score": 95.0, "status": "certified", "token_cost": 1, "auto_generated": true, "generator": "simple"}, {"id": "NG0112", "symbol": "≹", "code": "ng:memory:deref_5", "fallback": "[DEREF5]", "category": "memory", "name": "deref_5", "description": "Memory operation: deref_5", "unicode_point": "U+2279", "approved_date": "2025-05-23", "validation_score": 95.0, "status": "certified", "token_cost": 1, "auto_generated": true, "generator": "simple"}, {"id": "NG0113", "symbol": "≃", "code": "ng:logic:and_5", "fallback": "[AND5]", "category": "logic", "name": "and_5", "description": "Logic operation: and_5", "unicode_point": "U+2243", "approved_date": "2025-05-23", "validation_score": 95.0, "status": "certified", "token_cost": 1, "auto_generated": true, "generator": "simple"}, {"id": "NG0114", "symbol": "↹", "code": "ng:structure:property_6", "fallback": "[PROPERTY6]", "category": "structure", "name": "property_6", "description": "Structure operation: property_6", "unicode_point": "U+21B9", "approved_date": "2025-05-23", "validation_score": 95.0, "status": "certified", "token_cost": 1, "auto_generated": true, "generator": "simple"}, {"id": "NG0115", "symbol": "⛧", "code": "ng:structure:method_2", "fallback": "[METHOD2]", "category": "structure", "name": "method_2", "description": "Structure operation: method_2", "unicode_point": "U+26E7", "approved_date": "2025-05-23", "validation_score": 95.0, "status": "certified", "token_cost": 1, "auto_generated": true, "generator": "simple"}, {"id": "NG0116", "symbol": "☯", "code": "ng:memory:free_1", "fallback": "[FREE1]", "category": "memory", "name": "free_1", "description": "Memory operation: free_1", "unicode_point": "U+262F", "approved_date": "2025-05-23", "validation_score": 95.0, "status": "certified", "token_cost": 1, "auto_generated": true, "generator": "simple"}, {"id": "NG0117", "symbol": "≁", "code": "ng:logic:implies_6", "fallback": "[IMPLIES6]", "category": "logic", "name": "implies_6", "description": "Logic operation: implies_6", "unicode_point": "U+2241", "approved_date": "2025-05-23", "validation_score": 95.0, "status": "certified", "token_cost": 1, "auto_generated": true, "generator": "simple"}, {"id": "NG0118", "symbol": "⇷", "code": "ng:operator:sub_2", "fallback": "[SUB2]", "category": "operator", "name": "sub_2", "description": "Operator operation: sub_2", "unicode_point": "U+21F7", "approved_date": "2025-05-23", "validation_score": 95.0, "status": "certified", "token_cost": 1, "auto_generated": true, "generator": "simple"}, {"id": "NG0119", "symbol": "▲", "code": "ng:structure:method_3", "fallback": "[METHOD3]", "category": "structure", "name": "method_3", "description": "Structure operation: method_3", "unicode_point": "U+25B2", "approved_date": "2025-05-23", "validation_score": 95.0, "status": "certified", "token_cost": 1, "auto_generated": true, "generator": "simple"}, {"id": "NG0120", "symbol": "◹", "code": "ng:logic:implies_7", "fallback": "[IMPLIES7]", "category": "logic", "name": "implies_7", "description": "Logic operation: implies_7", "unicode_point": "U+25F9", "approved_date": "2025-05-23", "validation_score": 95.0, "status": "certified", "token_cost": 1, "auto_generated": true, "generator": "simple"}, {"id": "NG0121", "symbol": "↥", "code": "ng:operator:pow_1", "fallback": "[POW1]", "category": "operator", "name": "pow_1", "description": "Operator operation: pow_1", "unicode_point": "U+21A5", "approved_date": "2025-05-23", "validation_score": 95.0, "status": "certified", "token_cost": 1, "auto_generated": true, "generator": "simple"}, {"id": "NG0122", "symbol": "♉", "code": "ng:logic:not_4", "fallback": "[NOT4]", "category": "logic", "name": "not_4", "description": "Logic operation: not_4", "unicode_point": "U+2649", "approved_date": "2025-05-23", "validation_score": 95.0, "status": "certified", "token_cost": 1, "auto_generated": true, "generator": "simple"}, {"id": "NG0123", "symbol": "▫", "code": "ng:structure:property_7", "fallback": "[PROPERTY7]", "category": "structure", "name": "property_7", "description": "Structure operation: property_7", "unicode_point": "U+25AB", "approved_date": "2025-05-23", "validation_score": 95.0, "status": "certified", "token_cost": 1, "auto_generated": true, "generator": "simple"}, {"id": "NG0124", "symbol": "⚒", "code": "ng:structure:class_3", "fallback": "[CLASS3]", "category": "structure", "name": "class_3", "description": "Structure operation: class_3", "unicode_point": "U+2692", "approved_date": "2025-05-23", "validation_score": 95.0, "status": "certified", "token_cost": 1, "auto_generated": true, "generator": "simple"}, {"id": "NG0125", "symbol": "◰", "code": "ng:logic:implies_8", "fallback": "[IMPLIES8]", "category": "logic", "name": "implies_8", "description": "Logic operation: implies_8", "unicode_point": "U+25F0", "approved_date": "2025-05-23", "validation_score": 95.0, "status": "certified", "token_cost": 1, "auto_generated": true, "generator": "simple"}, {"id": "NG0126", "symbol": "◵", "code": "ng:memory:pointer_7", "fallback": "[POINTER7]", "category": "memory", "name": "pointer_7", "description": "Memory operation: pointer_7", "unicode_point": "U+25F5", "approved_date": "2025-05-23", "validation_score": 95.0, "status": "certified", "token_cost": 1, "auto_generated": true, "generator": "simple"}, {"id": "NG0127", "symbol": "☙", "code": "ng:flow:if_5", "fallback": "[IF5]", "category": "flow", "name": "if_5", "description": "Flow operation: if_5", "unicode_point": "U+2619", "approved_date": "2025-05-23", "validation_score": 95.0, "status": "certified", "token_cost": 1, "auto_generated": true, "generator": "simple"}, {"id": "NG0128", "symbol": "⇪", "code": "ng:logic:implies_9", "fallback": "[IMPLIES9]", "category": "logic", "name": "implies_9", "description": "Logic operation: implies_9", "unicode_point": "U+21EA", "approved_date": "2025-05-23", "validation_score": 95.0, "status": "certified", "token_cost": 1, "auto_generated": true, "generator": "simple"}, {"id": "NG0129", "symbol": "∰", "code": "ng:structure:function_5", "fallback": "[FUNCTION5]", "category": "structure", "name": "function_5", "description": "Structure operation: function_5", "unicode_point": "U+2230", "approved_date": "2025-05-23", "validation_score": 95.0, "status": "certified", "token_cost": 1, "auto_generated": true, "generator": "simple"}, {"id": "NG0130", "symbol": "♊", "code": "ng:flow:for_4", "fallback": "[FOR4]", "category": "flow", "name": "for_4", "description": "Flow operation: for_4", "unicode_point": "U+264A", "approved_date": "2025-05-23", "validation_score": 95.0, "status": "certified", "token_cost": 1, "auto_generated": true, "generator": "simple"}, {"id": "NG0131", "symbol": "↭", "code": "ng:operator:pow_2", "fallback": "[POW2]", "category": "operator", "name": "pow_2", "description": "Operator operation: pow_2", "unicode_point": "U+21AD", "approved_date": "2025-05-23", "validation_score": 95.0, "status": "certified", "token_cost": 1, "auto_generated": true, "generator": "simple"}, {"id": "NG0132", "symbol": "⋋", "code": "ng:logic:and_6", "fallback": "[AND6]", "category": "logic", "name": "and_6", "description": "Logic operation: and_6", "unicode_point": "U+22CB", "approved_date": "2025-05-23", "validation_score": 95.0, "status": "certified", "token_cost": 1, "auto_generated": true, "generator": "simple"}, {"id": "NG0133", "symbol": "⇴", "code": "ng:memory:deref_6", "fallback": "[DEREF6]", "category": "memory", "name": "deref_6", "description": "Memory operation: deref_6", "unicode_point": "U+21F4", "approved_date": "2025-05-23", "validation_score": 95.0, "status": "certified", "token_cost": 1, "auto_generated": true, "generator": "simple"}, {"id": "NG0134", "symbol": "∺", "code": "ng:logic:xor_1", "fallback": "[XOR1]", "category": "logic", "name": "xor_1", "description": "Logic operation: xor_1", "unicode_point": "U+223A", "approved_date": "2025-05-23", "validation_score": 95.0, "status": "certified", "token_cost": 1, "auto_generated": true, "generator": "simple"}, {"id": "NG0135", "symbol": "⇒", "code": "ng:logic:not_5", "fallback": "[NOT5]", "category": "logic", "name": "not_5", "description": "Logic operation: not_5", "unicode_point": "U+21D2", "approved_date": "2025-05-23", "validation_score": 95.0, "status": "certified", "token_cost": 1, "auto_generated": true, "generator": "simple"}, {"id": "NG0136", "symbol": "◬", "code": "ng:operator:sub_3", "fallback": "[SUB3]", "category": "operator", "name": "sub_3", "description": "Operator operation: sub_3", "unicode_point": "U+25EC", "approved_date": "2025-05-23", "validation_score": 95.0, "status": "certified", "token_cost": 1, "auto_generated": true, "generator": "simple"}, {"id": "NG0137", "symbol": "⇿", "code": "ng:flow:if_6", "fallback": "[IF6]", "category": "flow", "name": "if_6", "description": "Flow operation: if_6", "unicode_point": "U+21FF", "approved_date": "2025-05-23", "validation_score": 95.0, "status": "certified", "token_cost": 1, "auto_generated": true, "generator": "simple"}, {"id": "NG0138", "symbol": "∣", "code": "ng:operator:mul_3", "fallback": "[MUL3]", "category": "operator", "name": "mul_3", "description": "Operator operation: mul_3", "unicode_point": "U+2223", "approved_date": "2025-05-23", "validation_score": 95.0, "status": "certified", "token_cost": 1, "auto_generated": true, "generator": "simple"}, {"id": "NG0139", "symbol": "☕", "code": "ng:flow:while_7", "fallback": "[WHILE7]", "category": "flow", "name": "while_7", "description": "Flow operation: while_7", "unicode_point": "U+2615", "approved_date": "2025-05-23", "validation_score": 95.0, "status": "certified", "token_cost": 1, "auto_generated": true, "generator": "simple"}, {"id": "NG0140", "symbol": "▽", "code": "ng:operator:pow_3", "fallback": "[POW3]", "category": "operator", "name": "pow_3", "description": "Operator operation: pow_3", "unicode_point": "U+25BD", "approved_date": "2025-05-23", "validation_score": 95.0, "status": "certified", "token_cost": 1, "auto_generated": true, "generator": "simple"}, {"id": "NG0141", "symbol": "⋊", "code": "ng:structure:function_6", "fallback": "[FUNCTION6]", "category": "structure", "name": "function_6", "description": "Structure operation: function_6", "unicode_point": "U+22CA", "approved_date": "2025-05-23", "validation_score": 95.0, "status": "certified", "token_cost": 1, "auto_generated": true, "generator": "simple"}, {"id": "NG0142", "symbol": "⋺", "code": "ng:flow:break_4", "fallback": "[BREAK4]", "category": "flow", "name": "break_4", "description": "Flow operation: break_4", "unicode_point": "U+22FA", "approved_date": "2025-05-23", "validation_score": 95.0, "status": "certified", "token_cost": 1, "auto_generated": true, "generator": "simple"}, {"id": "NG0143", "symbol": "◪", "code": "ng:flow:return_2", "fallback": "[RETURN2]", "category": "flow", "name": "return_2", "description": "Flow operation: return_2", "unicode_point": "U+25EA", "approved_date": "2025-05-23", "validation_score": 95.0, "status": "certified", "token_cost": 1, "auto_generated": true, "generator": "simple"}, {"id": "NG0144", "symbol": "⋓", "code": "ng:memory:alloc_5", "fallback": "[ALLOC5]", "category": "memory", "name": "alloc_5", "description": "Memory operation: alloc_5", "unicode_point": "U+22D3", "approved_date": "2025-05-23", "validation_score": 95.0, "status": "certified", "token_cost": 1, "auto_generated": true, "generator": "simple"}, {"id": "NG0145", "symbol": "≫", "code": "ng:memory:free_2", "fallback": "[FREE2]", "category": "memory", "name": "free_2", "description": "Memory operation: free_2", "unicode_point": "U+226B", "approved_date": "2025-05-23", "validation_score": 95.0, "status": "certified", "token_cost": 1, "auto_generated": true, "generator": "simple"}, {"id": "NG0146", "symbol": "⇰", "code": "ng:flow:if_7", "fallback": "[IF7]", "category": "flow", "name": "if_7", "description": "Flow operation: if_7", "unicode_point": "U+21F0", "approved_date": "2025-05-23", "validation_score": 95.0, "status": "certified", "token_cost": 1, "auto_generated": true, "generator": "simple"}, {"id": "NG0147", "symbol": "⊞", "code": "ng:flow:else_3", "fallback": "[ELSE3]", "category": "flow", "name": "else_3", "description": "Flow operation: else_3", "unicode_point": "U+229E", "approved_date": "2025-05-23", "validation_score": 95.0, "status": "certified", "token_cost": 1, "auto_generated": true, "generator": "simple"}, {"id": "NG0148", "symbol": "▥", "code": "ng:memory:free_3", "fallback": "[FREE3]", "category": "memory", "name": "free_3", "description": "Memory operation: free_3", "unicode_point": "U+25A5", "approved_date": "2025-05-23", "validation_score": 95.0, "status": "certified", "token_cost": 1, "auto_generated": true, "generator": "simple"}, {"id": "NG0149", "symbol": "↪", "code": "ng:memory:ref", "fallback": "[REF]", "category": "memory", "name": "ref", "description": "Memory operation: ref", "unicode_point": "U+21AA", "approved_date": "2025-05-23", "validation_score": 95.0, "status": "certified", "token_cost": 1, "auto_generated": true, "generator": "simple"}, {"id": "NG0150", "symbol": "↷", "code": "ng:memory:deref_7", "fallback": "[DEREF7]", "category": "memory", "name": "deref_7", "description": "Memory operation: deref_7", "unicode_point": "U+21B7", "approved_date": "2025-05-23", "validation_score": 95.0, "status": "certified", "token_cost": 1, "auto_generated": true, "generator": "simple"}, {"id": "NG0151", "symbol": "⚰", "code": "ng:flow:if_8", "fallback": "[IF8]", "category": "flow", "name": "if_8", "description": "Flow operation: if_8", "unicode_point": "U+26B0", "approved_date": "2025-05-23", "validation_score": 95.0, "status": "certified", "token_cost": 1, "auto_generated": true, "generator": "simple"}, {"id": "NG0152", "symbol": "⋩", "code": "ng:memory:ref_1", "fallback": "[REF1]", "category": "memory", "name": "ref_1", "description": "Memory operation: ref_1", "unicode_point": "U+22E9", "approved_date": "2025-05-23", "validation_score": 95.0, "status": "certified", "token_cost": 1, "auto_generated": true, "generator": "simple"}, {"id": "NG0153", "symbol": "⇕", "code": "ng:flow:for_5", "fallback": "[FOR5]", "category": "flow", "name": "for_5", "description": "Flow operation: for_5", "unicode_point": "U+21D5", "approved_date": "2025-05-23", "validation_score": 95.0, "status": "certified", "token_cost": 1, "auto_generated": true, "generator": "simple"}, {"id": "NG0154", "symbol": "◞", "code": "ng:memory:pointer_8", "fallback": "[POINTER8]", "category": "memory", "name": "pointer_8", "description": "Memory operation: pointer_8", "unicode_point": "U+25DE", "approved_date": "2025-05-23", "validation_score": 95.0, "status": "certified", "token_cost": 1, "auto_generated": true, "generator": "simple"}, {"id": "NG0155", "symbol": "◕", "code": "ng:flow:else_4", "fallback": "[ELSE4]", "category": "flow", "name": "else_4", "description": "Flow operation: else_4", "unicode_point": "U+25D5", "approved_date": "2025-05-23", "validation_score": 95.0, "status": "certified", "token_cost": 1, "auto_generated": true, "generator": "simple"}, {"id": "NG0156", "symbol": "⋟", "code": "ng:memory:ref_2", "fallback": "[REF2]", "category": "memory", "name": "ref_2", "description": "Memory operation: ref_2", "unicode_point": "U+22DF", "approved_date": "2025-05-23", "validation_score": 95.0, "status": "certified", "token_cost": 1, "auto_generated": true, "generator": "simple"}, {"id": "NG0157", "symbol": "◮", "code": "ng:memory:pointer_9", "fallback": "[POINTER9]", "category": "memory", "name": "pointer_9", "description": "Memory operation: pointer_9", "unicode_point": "U+25EE", "approved_date": "2025-05-23", "validation_score": 95.0, "status": "certified", "token_cost": 1, "auto_generated": true, "generator": "simple"}, {"id": "NG0158", "symbol": "↽", "code": "ng:structure:class_4", "fallback": "[CLASS4]", "category": "structure", "name": "class_4", "description": "Structure operation: class_4", "unicode_point": "U+21BD", "approved_date": "2025-05-23", "validation_score": 95.0, "status": "certified", "token_cost": 1, "auto_generated": true, "generator": "simple"}, {"id": "NG0159", "symbol": "♅", "code": "ng:operator:mod_5", "fallback": "[MOD5]", "category": "operator", "name": "mod_5", "description": "Operator operation: mod_5", "unicode_point": "U+2645", "approved_date": "2025-05-23", "validation_score": 95.0, "status": "certified", "token_cost": 1, "auto_generated": true, "generator": "simple"}, {"id": "NG0160", "symbol": "♽", "code": "ng:flow:if_9", "fallback": "[IF9]", "category": "flow", "name": "if_9", "description": "Flow operation: if_9", "unicode_point": "U+267D", "approved_date": "2025-05-23", "validation_score": 95.0, "status": "certified", "token_cost": 1, "auto_generated": true, "generator": "simple"}, {"id": "NG0161", "symbol": "⛢", "code": "ng:flow:if_10", "fallback": "[IF10]", "category": "flow", "name": "if_10", "description": "Flow operation: if_10", "unicode_point": "U+26E2", "approved_date": "2025-05-23", "validation_score": 95.0, "status": "certified", "token_cost": 1, "auto_generated": true, "generator": "simple"}, {"id": "NG0162", "symbol": "⚢", "code": "ng:structure:method_4", "fallback": "[METHOD4]", "category": "structure", "name": "method_4", "description": "Structure operation: method_4", "unicode_point": "U+26A2", "approved_date": "2025-05-23", "validation_score": 95.0, "status": "certified", "token_cost": 1, "auto_generated": true, "generator": "simple"}, {"id": "NG0163", "symbol": "≑", "code": "ng:flow:while_8", "fallback": "[WHILE8]", "category": "flow", "name": "while_8", "description": "Flow operation: while_8", "unicode_point": "U+2251", "approved_date": "2025-05-23", "validation_score": 95.0, "status": "certified", "token_cost": 1, "auto_generated": true, "generator": "simple"}, {"id": "NG0164", "symbol": "⋘", "code": "ng:structure:class_5", "fallback": "[CLASS5]", "category": "structure", "name": "class_5", "description": "Structure operation: class_5", "unicode_point": "U+22D8", "approved_date": "2025-05-23", "validation_score": 95.0, "status": "certified", "token_cost": 1, "auto_generated": true, "generator": "simple"}, {"id": "NG0165", "symbol": "◃", "code": "ng:flow:return_3", "fallback": "[RETURN3]", "category": "flow", "name": "return_3", "description": "Flow operation: return_3", "unicode_point": "U+25C3", "approved_date": "2025-05-23", "validation_score": 95.0, "status": "certified", "token_cost": 1, "auto_generated": true, "generator": "simple"}, {"id": "NG0166", "symbol": "⇂", "code": "ng:logic:not_6", "fallback": "[NOT6]", "category": "logic", "name": "not_6", "description": "Logic operation: not_6", "unicode_point": "U+21C2", "approved_date": "2025-05-23", "validation_score": 95.0, "status": "certified", "token_cost": 1, "auto_generated": true, "generator": "simple"}, {"id": "NG0167", "symbol": "⚑", "code": "ng:memory:deref_8", "fallback": "[DEREF8]", "category": "memory", "name": "deref_8", "description": "Memory operation: deref_8", "unicode_point": "U+2691", "approved_date": "2025-05-23", "validation_score": 95.0, "status": "certified", "token_cost": 1, "auto_generated": true, "generator": "simple"}, {"id": "NG0168", "symbol": "⇠", "code": "ng:flow:while_9", "fallback": "[WHILE9]", "category": "flow", "name": "while_9", "description": "Flow operation: while_9", "unicode_point": "U+21E0", "approved_date": "2025-05-23", "validation_score": 95.0, "status": "certified", "token_cost": 1, "auto_generated": true, "generator": "simple"}, {"id": "NG0169", "symbol": "♷", "code": "ng:structure:class_6", "fallback": "[CLASS6]", "category": "structure", "name": "class_6", "description": "Structure operation: class_6", "unicode_point": "U+2677", "approved_date": "2025-05-23", "validation_score": 95.0, "status": "certified", "token_cost": 1, "auto_generated": true, "generator": "simple"}, {"id": "NG0170", "symbol": "⚈", "code": "ng:structure:function_7", "fallback": "[FUNCTION7]", "category": "structure", "name": "function_7", "description": "Structure operation: function_7", "unicode_point": "U+2688", "approved_date": "2025-05-23", "validation_score": 95.0, "status": "certified", "token_cost": 1, "auto_generated": true, "generator": "simple"}, {"id": "NG0171", "symbol": "↧", "code": "ng:operator:sub_4", "fallback": "[SUB4]", "category": "operator", "name": "sub_4", "description": "Operator operation: sub_4", "unicode_point": "U+21A7", "approved_date": "2025-05-23", "validation_score": 95.0, "status": "certified", "token_cost": 1, "auto_generated": true, "generator": "simple"}, {"id": "NG0172", "symbol": "↮", "code": "ng:logic:and_7", "fallback": "[AND7]", "category": "logic", "name": "and_7", "description": "Logic operation: and_7", "unicode_point": "U+21AE", "approved_date": "2025-05-23", "validation_score": 95.0, "status": "certified", "token_cost": 1, "auto_generated": true, "generator": "simple"}, {"id": "NG0173", "symbol": "⊀", "code": "ng:structure:function_8", "fallback": "[FUNCTION8]", "category": "structure", "name": "function_8", "description": "Structure operation: function_8", "unicode_point": "U+2280", "approved_date": "2025-05-23", "validation_score": 95.0, "status": "certified", "token_cost": 1, "auto_generated": true, "generator": "simple"}, {"id": "NG0174", "symbol": "◦", "code": "ng:flow:return_4", "fallback": "[RETURN4]", "category": "flow", "name": "return_4", "description": "Flow operation: return_4", "unicode_point": "U+25E6", "approved_date": "2025-05-23", "validation_score": 95.0, "status": "certified", "token_cost": 1, "auto_generated": true, "generator": "simple"}, {"id": "NG0175", "symbol": "⋛", "code": "ng:logic:iff", "fallback": "[IFF]", "category": "logic", "name": "iff", "description": "Logic operation: iff", "unicode_point": "U+22DB", "approved_date": "2025-05-23", "validation_score": 95.0, "status": "certified", "token_cost": 1, "auto_generated": true, "generator": "simple"}, {"id": "NG0176", "symbol": "◲", "code": "ng:logic:or_5", "fallback": "[OR5]", "category": "logic", "name": "or_5", "description": "Logic operation: or_5", "unicode_point": "U+25F2", "approved_date": "2025-05-23", "validation_score": 95.0, "status": "certified", "token_cost": 1, "auto_generated": true, "generator": "simple"}, {"id": "NG0177", "symbol": "≚", "code": "ng:flow:for_6", "fallback": "[FOR6]", "category": "flow", "name": "for_6", "description": "Flow operation: for_6", "unicode_point": "U+225A", "approved_date": "2025-05-23", "validation_score": 95.0, "status": "certified", "token_cost": 1, "auto_generated": true, "generator": "simple"}, {"id": "NG0178", "symbol": "↩", "code": "ng:memory:pointer_10", "fallback": "[POINTER10]", "category": "memory", "name": "pointer_10", "description": "Memory operation: pointer_10", "unicode_point": "U+21A9", "approved_date": "2025-05-23", "validation_score": 95.0, "status": "certified", "token_cost": 1, "auto_generated": true, "generator": "simple"}, {"id": "NG0179", "symbol": "↗", "code": "ng:flow:while_10", "fallback": "[WHILE10]", "category": "flow", "name": "while_10", "description": "Flow operation: while_10", "unicode_point": "U+2197", "approved_date": "2025-05-23", "validation_score": 95.0, "status": "certified", "token_cost": 1, "auto_generated": true, "generator": "simple"}, {"id": "NG0180", "symbol": "⚘", "code": "ng:logic:implies_10", "fallback": "[IMPLIES10]", "category": "logic", "name": "implies_10", "description": "Logic operation: implies_10", "unicode_point": "U+2698", "approved_date": "2025-05-23", "validation_score": 95.0, "status": "certified", "token_cost": 1, "auto_generated": true, "generator": "simple"}, {"id": "NG0181", "symbol": "☛", "code": "ng:structure:method_5", "fallback": "[METHOD5]", "category": "structure", "name": "method_5", "description": "Structure operation: method_5", "unicode_point": "U+261B", "approved_date": "2025-05-23", "validation_score": 95.0, "status": "certified", "token_cost": 1, "auto_generated": true, "generator": "simple"}, {"id": "NG0182", "symbol": "⊳", "code": "ng:memory:free_4", "fallback": "[FREE4]", "category": "memory", "name": "free_4", "description": "Memory operation: free_4", "unicode_point": "U+22B3", "approved_date": "2025-05-23", "validation_score": 95.0, "status": "certified", "token_cost": 1, "auto_generated": true, "generator": "simple"}, {"id": "NG0183", "symbol": "◳", "code": "ng:flow:if_11", "fallback": "[IF11]", "category": "flow", "name": "if_11", "description": "Flow operation: if_11", "unicode_point": "U+25F3", "approved_date": "2025-05-23", "validation_score": 95.0, "status": "certified", "token_cost": 1, "auto_generated": true, "generator": "simple"}, {"id": "NG0184", "symbol": "◙", "code": "ng:memory:deref_9", "fallback": "[DEREF9]", "category": "memory", "name": "deref_9", "description": "Memory operation: deref_9", "unicode_point": "U+25D9", "approved_date": "2025-05-23", "validation_score": 95.0, "status": "certified", "token_cost": 1, "auto_generated": true, "generator": "simple"}, {"id": "NG0185", "symbol": "⛡", "code": "ng:structure:property_8", "fallback": "[PROPERTY8]", "category": "structure", "name": "property_8", "description": "Structure operation: property_8", "unicode_point": "U+26E1", "approved_date": "2025-05-23", "validation_score": 95.0, "status": "certified", "token_cost": 1, "auto_generated": true, "generator": "simple"}, {"id": "NG0186", "symbol": "◐", "code": "ng:logic:implies_11", "fallback": "[IMPLIES11]", "category": "logic", "name": "implies_11", "description": "Logic operation: implies_11", "unicode_point": "U+25D0", "approved_date": "2025-05-23", "validation_score": 95.0, "status": "certified", "token_cost": 1, "auto_generated": true, "generator": "simple"}, {"id": "NG0187", "symbol": "⛽", "code": "ng:flow:while_11", "fallback": "[WHILE11]", "category": "flow", "name": "while_11", "description": "Flow operation: while_11", "unicode_point": "U+26FD", "approved_date": "2025-05-23", "validation_score": 95.0, "status": "certified", "token_cost": 1, "auto_generated": true, "generator": "simple"}, {"id": "NG0188", "symbol": "⇱", "code": "ng:operator:div_5", "fallback": "[DIV5]", "category": "operator", "name": "div_5", "description": "Operator operation: div_5", "unicode_point": "U+21F1", "approved_date": "2025-05-23", "validation_score": 95.0, "status": "certified", "token_cost": 1, "auto_generated": true, "generator": "simple"}, {"id": "NG0189", "symbol": "∼", "code": "ng:logic:not_7", "fallback": "[NOT7]", "category": "logic", "name": "not_7", "description": "Logic operation: not_7", "unicode_point": "U+223C", "approved_date": "2025-05-23", "validation_score": 95.0, "status": "certified", "token_cost": 1, "auto_generated": true, "generator": "simple"}, {"id": "NG0190", "symbol": "≬", "code": "ng:memory:ref_3", "fallback": "[REF3]", "category": "memory", "name": "ref_3", "description": "Memory operation: ref_3", "unicode_point": "U+226C", "approved_date": "2025-05-23", "validation_score": 95.0, "status": "certified", "token_cost": 1, "auto_generated": true, "generator": "simple"}, {"id": "NG0191", "symbol": "▸", "code": "ng:memory:deref_10", "fallback": "[DEREF10]", "category": "memory", "name": "deref_10", "description": "Memory operation: deref_10", "unicode_point": "U+25B8", "approved_date": "2025-05-23", "validation_score": 95.0, "status": "certified", "token_cost": 1, "auto_generated": true, "generator": "simple"}, {"id": "NG0192", "symbol": "≏", "code": "ng:structure:property_9", "fallback": "[PROPERTY9]", "category": "structure", "name": "property_9", "description": "Structure operation: property_9", "unicode_point": "U+224F", "approved_date": "2025-05-23", "validation_score": 95.0, "status": "certified", "token_cost": 1, "auto_generated": true, "generator": "simple"}, {"id": "NG0193", "symbol": "⋆", "code": "ng:operator:div_6", "fallback": "[DIV6]", "category": "operator", "name": "div_6", "description": "Operator operation: div_6", "unicode_point": "U+22C6", "approved_date": "2025-05-23", "validation_score": 95.0, "status": "certified", "token_cost": 1, "auto_generated": true, "generator": "simple"}, {"id": "NG0194", "symbol": "⋴", "code": "ng:logic:not_8", "fallback": "[NOT8]", "category": "logic", "name": "not_8", "description": "Logic operation: not_8", "unicode_point": "U+22F4", "approved_date": "2025-05-23", "validation_score": 95.0, "status": "certified", "token_cost": 1, "auto_generated": true, "generator": "simple"}, {"id": "NG0195", "symbol": "⇚", "code": "ng:structure:property_10", "fallback": "[PROPERTY10]", "category": "structure", "name": "property_10", "description": "Structure operation: property_10", "unicode_point": "U+21DA", "approved_date": "2025-05-23", "validation_score": 95.0, "status": "certified", "token_cost": 1, "auto_generated": true, "generator": "simple"}, {"id": "NG0196", "symbol": "⇲", "code": "ng:memory:alloc_6", "fallback": "[ALLOC6]", "category": "memory", "name": "alloc_6", "description": "Memory operation: alloc_6", "unicode_point": "U+21F2", "approved_date": "2025-05-23", "validation_score": 95.0, "status": "certified", "token_cost": 1, "auto_generated": true, "generator": "simple"}, {"id": "NG0197", "symbol": "▮", "code": "ng:operator:div_7", "fallback": "[DIV7]", "category": "operator", "name": "div_7", "description": "Operator operation: div_7", "unicode_point": "U+25AE", "approved_date": "2025-05-23", "validation_score": 95.0, "status": "certified", "token_cost": 1, "auto_generated": true, "generator": "simple"}, {"id": "NG0198", "symbol": "≗", "code": "ng:memory:alloc_7", "fallback": "[ALLOC7]", "category": "memory", "name": "alloc_7", "description": "Memory operation: alloc_7", "unicode_point": "U+2257", "approved_date": "2025-05-23", "validation_score": 95.0, "status": "certified", "token_cost": 1, "auto_generated": true, "generator": "simple"}, {"id": "NG0199", "symbol": "↾", "code": "ng:structure:class_7", "fallback": "[CLASS7]", "category": "structure", "name": "class_7", "description": "Structure operation: class_7", "unicode_point": "U+21BE", "approved_date": "2025-05-23", "validation_score": 95.0, "status": "certified", "token_cost": 1, "auto_generated": true, "generator": "simple"}, {"id": "NG0200", "symbol": "◛", "code": "ng:flow:for_7", "fallback": "[FOR7]", "category": "flow", "name": "for_7", "description": "Flow operation: for_7", "unicode_point": "U+25DB", "approved_date": "2025-05-23", "validation_score": 95.0, "status": "certified", "token_cost": 1, "auto_generated": true, "generator": "simple"}, {"id": "NG0201", "symbol": "∶", "code": "ng:structure:function_9", "fallback": "[FUNCTION9]", "category": "structure", "name": "function_9", "description": "Structure operation: function_9", "unicode_point": "U+2236", "approved_date": "2025-05-23", "validation_score": 95.0, "status": "certified", "token_cost": 1, "auto_generated": true, "generator": "simple"}, {"id": "NG0202", "symbol": "≝", "code": "ng:operator:sub_5", "fallback": "[SUB5]", "category": "operator", "name": "sub_5", "description": "Operator operation: sub_5", "unicode_point": "U+225D", "approved_date": "2025-05-23", "validation_score": 95.0, "status": "certified", "token_cost": 1, "auto_generated": true, "generator": "simple"}, {"id": "NG0203", "symbol": "⛁", "code": "ng:memory:alloc_8", "fallback": "[ALLOC8]", "category": "memory", "name": "alloc_8", "description": "Memory operation: alloc_8", "unicode_point": "U+26C1", "approved_date": "2025-05-23", "validation_score": 95.0, "status": "certified", "token_cost": 1, "auto_generated": true, "generator": "simple"}, {"id": "NG0204", "symbol": "≾", "code": "ng:flow:for_8", "fallback": "[FOR8]", "category": "flow", "name": "for_8", "description": "Flow operation: for_8", "unicode_point": "U+227E", "approved_date": "2025-05-23", "validation_score": 95.0, "status": "certified", "token_cost": 1, "auto_generated": true, "generator": "simple"}, {"id": "NG0205", "symbol": "♫", "code": "ng:operator:sub_6", "fallback": "[SUB6]", "category": "operator", "name": "sub_6", "description": "Operator operation: sub_6", "unicode_point": "U+266B", "approved_date": "2025-05-23", "validation_score": 95.0, "status": "certified", "token_cost": 1, "auto_generated": true, "generator": "simple"}, {"id": "NG0206", "symbol": "⊙", "code": "ng:structure:method_6", "fallback": "[METHOD6]", "category": "structure", "name": "method_6", "description": "Structure operation: method_6", "unicode_point": "U+2299", "approved_date": "2025-05-23", "validation_score": 95.0, "status": "certified", "token_cost": 1, "auto_generated": true, "generator": "simple"}, {"id": "NG0207", "symbol": "≽", "code": "ng:operator:sub_7", "fallback": "[SUB7]", "category": "operator", "name": "sub_7", "description": "Operator operation: sub_7", "unicode_point": "U+227D", "approved_date": "2025-05-23", "validation_score": 95.0, "status": "certified", "token_cost": 1, "auto_generated": true, "generator": "simple"}, {"id": "NG0208", "symbol": "⇢", "code": "ng:memory:pointer_11", "fallback": "[POINTER11]", "category": "memory", "name": "pointer_11", "description": "Memory operation: pointer_11", "unicode_point": "U+21E2", "approved_date": "2025-05-23", "validation_score": 95.0, "status": "certified", "token_cost": 1, "auto_generated": true, "generator": "simple"}, {"id": "NG0209", "symbol": "↦", "code": "ng:operator:pow_4", "fallback": "[POW4]", "category": "operator", "name": "pow_4", "description": "Operator operation: pow_4", "unicode_point": "U+21A6", "approved_date": "2025-05-23", "validation_score": 95.0, "status": "certified", "token_cost": 1, "auto_generated": true, "generator": "simple"}, {"id": "NG0210", "symbol": "∂", "code": "ng:flow:break_5", "fallback": "[BREAK5]", "category": "flow", "name": "break_5", "description": "Flow operation: break_5", "unicode_point": "U+2202", "approved_date": "2025-05-23", "validation_score": 95.0, "status": "certified", "token_cost": 1, "auto_generated": true, "generator": "simple"}, {"id": "NG0211", "symbol": "⇋", "code": "ng:operator:add_6", "fallback": "[ADD6]", "category": "operator", "name": "add_6", "description": "Operator operation: add_6", "unicode_point": "U+21CB", "approved_date": "2025-05-23", "validation_score": 95.0, "status": "certified", "token_cost": 1, "auto_generated": true, "generator": "simple"}, {"id": "NG0212", "symbol": "◚", "code": "ng:structure:function_10", "fallback": "[FUNCTION10]", "category": "structure", "name": "function_10", "description": "Structure operation: function_10", "unicode_point": "U+25DA", "approved_date": "2025-05-23", "validation_score": 95.0, "status": "certified", "token_cost": 1, "auto_generated": true, "generator": "simple"}, {"id": "NG0213", "symbol": "⇖", "code": "ng:memory:free_5", "fallback": "[FREE5]", "category": "memory", "name": "free_5", "description": "Memory operation: free_5", "unicode_point": "U+21D6", "approved_date": "2025-05-23", "validation_score": 95.0, "status": "certified", "token_cost": 1, "auto_generated": true, "generator": "simple"}, {"id": "NG0214", "symbol": "☸", "code": "ng:flow:if_12", "fallback": "[IF12]", "category": "flow", "name": "if_12", "description": "Flow operation: if_12", "unicode_point": "U+2638", "approved_date": "2025-05-23", "validation_score": 95.0, "status": "certified", "token_cost": 1, "auto_generated": true, "generator": "simple"}, {"id": "NG0215", "symbol": "⛕", "code": "ng:logic:or_6", "fallback": "[OR6]", "category": "logic", "name": "or_6", "description": "Logic operation: or_6", "unicode_point": "U+26D5", "approved_date": "2025-05-23", "validation_score": 95.0, "status": "certified", "token_cost": 1, "auto_generated": true, "generator": "simple"}, {"id": "NG0216", "symbol": "↖", "code": "ng:logic:implies_12", "fallback": "[IMPLIES12]", "category": "logic", "name": "implies_12", "description": "Logic operation: implies_12", "unicode_point": "U+2196", "approved_date": "2025-05-23", "validation_score": 95.0, "status": "certified", "token_cost": 1, "auto_generated": true, "generator": "simple"}, {"id": "NG0217", "symbol": "⋎", "code": "ng:structure:property_11", "fallback": "[PROPERTY11]", "category": "structure", "name": "property_11", "description": "Structure operation: property_11", "unicode_point": "U+22CE", "approved_date": "2025-05-23", "validation_score": 95.0, "status": "certified", "token_cost": 1, "auto_generated": true, "generator": "simple"}, {"id": "NG0218", "symbol": "⛨", "code": "ng:logic:iff_1", "fallback": "[IFF1]", "category": "logic", "name": "iff_1", "description": "Logic operation: iff_1", "unicode_point": "U+26E8", "approved_date": "2025-05-23", "validation_score": 95.0, "status": "certified", "token_cost": 1, "auto_generated": true, "generator": "simple"}, {"id": "NG0219", "symbol": "⇙", "code": "ng:flow:return_5", "fallback": "[RETURN5]", "category": "flow", "name": "return_5", "description": "Flow operation: return_5", "unicode_point": "U+21D9", "approved_date": "2025-05-23", "validation_score": 95.0, "status": "certified", "token_cost": 1, "auto_generated": true, "generator": "simple"}, {"id": "NG0220", "symbol": "▧", "code": "ng:structure:function_11", "fallback": "[FUNCTION11]", "category": "structure", "name": "function_11", "description": "Structure operation: function_11", "unicode_point": "U+25A7", "approved_date": "2025-05-23", "validation_score": 95.0, "status": "certified", "token_cost": 1, "auto_generated": true, "generator": "simple"}, {"id": "NG0221", "symbol": "⚉", "code": "ng:flow:while_12", "fallback": "[WHILE12]", "category": "flow", "name": "while_12", "description": "Flow operation: while_12", "unicode_point": "U+2689", "approved_date": "2025-05-23", "validation_score": 95.0, "status": "certified", "token_cost": 1, "auto_generated": true, "generator": "simple"}, {"id": "NG0222", "symbol": "⇁", "code": "ng:logic:and_8", "fallback": "[AND8]", "category": "logic", "name": "and_8", "description": "Logic operation: and_8", "unicode_point": "U+21C1", "approved_date": "2025-05-23", "validation_score": 95.0, "status": "certified", "token_cost": 1, "auto_generated": true, "generator": "simple"}, {"id": "NG0223", "symbol": "◽", "code": "ng:logic:implies_13", "fallback": "[IMPLIES13]", "category": "logic", "name": "implies_13", "description": "Logic operation: implies_13", "unicode_point": "U+25FD", "approved_date": "2025-05-23", "validation_score": 95.0, "status": "certified", "token_cost": 1, "auto_generated": true, "generator": "simple"}, {"id": "NG0224", "symbol": "▼", "code": "ng:memory:deref_11", "fallback": "[DEREF11]", "category": "memory", "name": "deref_11", "description": "Memory operation: deref_11", "unicode_point": "U+25BC", "approved_date": "2025-05-23", "validation_score": 95.0, "status": "certified", "token_cost": 1, "auto_generated": true, "generator": "simple"}, {"id": "NG0225", "symbol": "↿", "code": "ng:logic:xor_2", "fallback": "[XOR2]", "category": "logic", "name": "xor_2", "description": "Logic operation: xor_2", "unicode_point": "U+21BF", "approved_date": "2025-05-23", "validation_score": 95.0, "status": "certified", "token_cost": 1, "auto_generated": true, "generator": "simple"}, {"id": "NG0226", "symbol": "▿", "code": "ng:structure:function_12", "fallback": "[FUNCTION12]", "category": "structure", "name": "function_12", "description": "Structure operation: function_12", "unicode_point": "U+25BF", "approved_date": "2025-05-23", "validation_score": 95.0, "status": "certified", "token_cost": 1, "auto_generated": true, "generator": "simple"}, {"id": "NG0227", "symbol": "☑", "code": "ng:operator:mul_4", "fallback": "[MUL4]", "category": "operator", "name": "mul_4", "description": "Operator operation: mul_4", "unicode_point": "U+2611", "approved_date": "2025-05-23", "validation_score": 95.0, "status": "certified", "token_cost": 1, "auto_generated": true, "generator": "simple"}, {"id": "NG0228", "symbol": "☿", "code": "ng:structure:property_12", "fallback": "[PROPERTY12]", "category": "structure", "name": "property_12", "description": "Structure operation: property_12", "unicode_point": "U+263F", "approved_date": "2025-05-23", "validation_score": 95.0, "status": "certified", "token_cost": 1, "auto_generated": true, "generator": "simple"}, {"id": "NG0229", "symbol": "♮", "code": "ng:operator:mod_6", "fallback": "[MOD6]", "category": "operator", "name": "mod_6", "description": "Operator operation: mod_6", "unicode_point": "U+266E", "approved_date": "2025-05-23", "validation_score": 95.0, "status": "certified", "token_cost": 1, "auto_generated": true, "generator": "simple"}, {"id": "NG0230", "symbol": "⊹", "code": "ng:structure:method_7", "fallback": "[METHOD7]", "category": "structure", "name": "method_7", "description": "Structure operation: method_7", "unicode_point": "U+22B9", "approved_date": "2025-05-23", "validation_score": 95.0, "status": "certified", "token_cost": 1, "auto_generated": true, "generator": "simple"}, {"id": "NG0231", "symbol": "⇽", "code": "ng:logic:xor_3", "fallback": "[XOR3]", "category": "logic", "name": "xor_3", "description": "Logic operation: xor_3", "unicode_point": "U+21FD", "approved_date": "2025-05-23", "validation_score": 95.0, "status": "certified", "token_cost": 1, "auto_generated": true, "generator": "simple"}, {"id": "NG0232", "symbol": "⇤", "code": "ng:flow:return_6", "fallback": "[RETURN6]", "category": "flow", "name": "return_6", "description": "Flow operation: return_6", "unicode_point": "U+21E4", "approved_date": "2025-05-23", "validation_score": 95.0, "status": "certified", "token_cost": 1, "auto_generated": true, "generator": "simple"}, {"id": "NG0233", "symbol": "☧", "code": "ng:operator:mul_5", "fallback": "[MUL5]", "category": "operator", "name": "mul_5", "description": "Operator operation: mul_5", "unicode_point": "U+2627", "approved_date": "2025-05-23", "validation_score": 95.0, "status": "certified", "token_cost": 1, "auto_generated": true, "generator": "simple"}, {"id": "NG0234", "symbol": "☮", "code": "ng:logic:iff_2", "fallback": "[IFF2]", "category": "logic", "name": "iff_2", "description": "Logic operation: iff_2", "unicode_point": "U+262E", "approved_date": "2025-05-23", "validation_score": 95.0, "status": "certified", "token_cost": 1, "auto_generated": true, "generator": "simple"}, {"id": "NG0235", "symbol": "⚋", "code": "ng:logic:and_9", "fallback": "[AND9]", "category": "logic", "name": "and_9", "description": "Logic operation: and_9", "unicode_point": "U+268B", "approved_date": "2025-05-23", "validation_score": 95.0, "status": "certified", "token_cost": 1, "auto_generated": true, "generator": "simple"}, {"id": "NG0236", "symbol": "⇀", "code": "ng:operator:pow_5", "fallback": "[POW5]", "category": "operator", "name": "pow_5", "description": "Operator operation: pow_5", "unicode_point": "U+21C0", "approved_date": "2025-05-23", "validation_score": 95.0, "status": "certified", "token_cost": 1, "auto_generated": true, "generator": "simple"}, {"id": "NG0237", "symbol": "∡", "code": "ng:memory:alloc_9", "fallback": "[ALLOC9]", "category": "memory", "name": "alloc_9", "description": "Memory operation: alloc_9", "unicode_point": "U+2221", "approved_date": "2025-05-23", "validation_score": 95.0, "status": "certified", "token_cost": 1, "auto_generated": true, "generator": "simple"}, {"id": "NG0238", "symbol": "⛣", "code": "ng:operator:mod_7", "fallback": "[MOD7]", "category": "operator", "name": "mod_7", "description": "Operator operation: mod_7", "unicode_point": "U+26E3", "approved_date": "2025-05-23", "validation_score": 95.0, "status": "certified", "token_cost": 1, "auto_generated": true, "generator": "simple"}, {"id": "NG0239", "symbol": "∷", "code": "ng:flow:if_13", "fallback": "[IF13]", "category": "flow", "name": "if_13", "description": "Flow operation: if_13", "unicode_point": "U+2237", "approved_date": "2025-05-23", "validation_score": 95.0, "status": "certified", "token_cost": 1, "auto_generated": true, "generator": "simple"}, {"id": "NG0240", "symbol": "⚗", "code": "ng:operator:sub_8", "fallback": "[SUB8]", "category": "operator", "name": "sub_8", "description": "Operator operation: sub_8", "unicode_point": "U+2697", "approved_date": "2025-05-23", "validation_score": 95.0, "status": "certified", "token_cost": 1, "auto_generated": true, "generator": "simple"}, {"id": "NG0241", "symbol": "⊧", "code": "ng:flow:else_5", "fallback": "[ELSE5]", "category": "flow", "name": "else_5", "description": "Flow operation: else_5", "unicode_point": "U+22A7", "approved_date": "2025-05-23", "validation_score": 95.0, "status": "certified", "token_cost": 1, "auto_generated": true, "generator": "simple"}, {"id": "NG0242", "symbol": "⋀", "code": "ng:memory:ref_4", "fallback": "[REF4]", "category": "memory", "name": "ref_4", "description": "Memory operation: ref_4", "unicode_point": "U+22C0", "approved_date": "2025-05-23", "validation_score": 95.0, "status": "certified", "token_cost": 1, "auto_generated": true, "generator": "simple"}, {"id": "NG0243", "symbol": "↜", "code": "ng:operator:pow_6", "fallback": "[POW6]", "category": "operator", "name": "pow_6", "description": "Operator operation: pow_6", "unicode_point": "U+219C", "approved_date": "2025-05-23", "validation_score": 95.0, "status": "certified", "token_cost": 1, "auto_generated": true, "generator": "simple"}, {"id": "NG0244", "symbol": "≎", "code": "ng:operator:pow_7", "fallback": "[POW7]", "category": "operator", "name": "pow_7", "description": "Operator operation: pow_7", "unicode_point": "U+224E", "approved_date": "2025-05-23", "validation_score": 95.0, "status": "certified", "token_cost": 1, "auto_generated": true, "generator": "simple"}, {"id": "NG0245", "symbol": "▦", "code": "ng:logic:and_10", "fallback": "[AND10]", "category": "logic", "name": "and_10", "description": "Logic operation: and_10", "unicode_point": "U+25A6", "approved_date": "2025-05-23", "validation_score": 95.0, "status": "certified", "token_cost": 1, "auto_generated": true, "generator": "simple"}, {"id": "NG0246", "symbol": "◑", "code": "ng:structure:property_13", "fallback": "[PROPERTY13]", "category": "structure", "name": "property_13", "description": "Structure operation: property_13", "unicode_point": "U+25D1", "approved_date": "2025-05-23", "validation_score": 95.0, "status": "certified", "token_cost": 1, "auto_generated": true, "generator": "simple"}, {"id": "NG0247", "symbol": "−", "code": "ng:logic:xor_4", "fallback": "[XOR4]", "category": "logic", "name": "xor_4", "description": "Logic operation: xor_4", "unicode_point": "U+2212", "approved_date": "2025-05-23", "validation_score": 95.0, "status": "certified", "token_cost": 1, "auto_generated": true, "generator": "simple"}, {"id": "NG0248", "symbol": "◘", "code": "ng:flow:return_7", "fallback": "[RETURN7]", "category": "flow", "name": "return_7", "description": "Flow operation: return_7", "unicode_point": "U+25D8", "approved_date": "2025-05-23", "validation_score": 95.0, "status": "certified", "token_cost": 1, "auto_generated": true, "generator": "simple"}, {"id": "NG0249", "symbol": "⋜", "code": "ng:memory:pointer_12", "fallback": "[POINTER12]", "category": "memory", "name": "pointer_12", "description": "Memory operation: pointer_12", "unicode_point": "U+22DC", "approved_date": "2025-05-23", "validation_score": 95.0, "status": "certified", "token_cost": 1, "auto_generated": true, "generator": "simple"}, {"id": "NG0250", "symbol": "∋", "code": "ng:structure:function_13", "fallback": "[FUNCTION13]", "category": "structure", "name": "function_13", "description": "Structure operation: function_13", "unicode_point": "U+220B", "approved_date": "2025-05-23", "validation_score": 95.0, "status": "certified", "token_cost": 1, "auto_generated": true, "generator": "simple"}, {"id": "NG0251", "symbol": "≔", "code": "ng:operator:pow_8", "fallback": "[POW8]", "category": "operator", "name": "pow_8", "description": "Operator operation: pow_8", "unicode_point": "U+2254", "approved_date": "2025-05-23", "validation_score": 95.0, "status": "certified", "token_cost": 1, "auto_generated": true, "generator": "simple"}, {"id": "NG0252", "symbol": "↝", "code": "ng:logic:implies_14", "fallback": "[IMPLIES14]", "category": "logic", "name": "implies_14", "description": "Logic operation: implies_14", "unicode_point": "U+219D", "approved_date": "2025-05-23", "validation_score": 95.0, "status": "certified", "token_cost": 1, "auto_generated": true, "generator": "simple"}, {"id": "NG0253", "symbol": "⛐", "code": "ng:memory:alloc_10", "fallback": "[ALLOC10]", "category": "memory", "name": "alloc_10", "description": "Memory operation: alloc_10", "unicode_point": "U+26D0", "approved_date": "2025-05-23", "validation_score": 95.0, "status": "certified", "token_cost": 1, "auto_generated": true, "generator": "simple"}, {"id": "NG0254", "symbol": "⊈", "code": "ng:operator:pow_9", "fallback": "[POW9]", "category": "operator", "name": "pow_9", "description": "Operator operation: pow_9", "unicode_point": "U+2288", "approved_date": "2025-05-23", "validation_score": 95.0, "status": "certified", "token_cost": 1, "auto_generated": true, "generator": "simple"}, {"id": "NG0255", "symbol": "◶", "code": "ng:operator:pow_10", "fallback": "[POW10]", "category": "operator", "name": "pow_10", "description": "Operator operation: pow_10", "unicode_point": "U+25F6", "approved_date": "2025-05-23", "validation_score": 95.0, "status": "certified", "token_cost": 1, "auto_generated": true, "generator": "simple"}, {"id": "NG0256", "symbol": "◻", "code": "ng:logic:and_11", "fallback": "[AND11]", "category": "logic", "name": "and_11", "description": "Logic operation: and_11", "unicode_point": "U+25FB", "approved_date": "2025-05-23", "validation_score": 95.0, "status": "certified", "token_cost": 1, "auto_generated": true, "generator": "simple"}, {"id": "NG0257", "symbol": "⊯", "code": "ng:logic:xor_5", "fallback": "[XOR5]", "category": "logic", "name": "xor_5", "description": "Logic operation: xor_5", "unicode_point": "U+22AF", "approved_date": "2025-05-23", "validation_score": 95.0, "status": "certified", "token_cost": 1, "auto_generated": true, "generator": "simple"}, {"id": "NG0258", "symbol": "☝", "code": "ng:logic:xor_6", "fallback": "[XOR6]", "category": "logic", "name": "xor_6", "description": "Logic operation: xor_6", "unicode_point": "U+261D", "approved_date": "2025-05-23", "validation_score": 95.0, "status": "certified", "token_cost": 1, "auto_generated": true, "generator": "simple"}, {"id": "NG0259", "symbol": "↑", "code": "ng:memory:free_6", "fallback": "[FREE6]", "category": "memory", "name": "free_6", "description": "Memory operation: free_6", "unicode_point": "U+2191", "approved_date": "2025-05-23", "validation_score": 95.0, "status": "certified", "token_cost": 1, "auto_generated": true, "generator": "simple"}, {"id": "NG0260", "symbol": "⇘", "code": "ng:logic:and_12", "fallback": "[AND12]", "category": "logic", "name": "and_12", "description": "Logic operation: and_12", "unicode_point": "U+21D8", "approved_date": "2025-05-23", "validation_score": 95.0, "status": "certified", "token_cost": 1, "auto_generated": true, "generator": "simple"}, {"id": "NG0261", "symbol": "⊿", "code": "ng:logic:not_9", "fallback": "[NOT9]", "category": "logic", "name": "not_9", "description": "Logic operation: not_9", "unicode_point": "U+22BF", "approved_date": "2025-05-23", "validation_score": 95.0, "status": "certified", "token_cost": 1, "auto_generated": true, "generator": "simple"}, {"id": "NG0262", "symbol": "↛", "code": "ng:operator:mul_6", "fallback": "[MUL6]", "category": "operator", "name": "mul_6", "description": "Operator operation: mul_6", "unicode_point": "U+219B", "approved_date": "2025-05-23", "validation_score": 95.0, "status": "certified", "token_cost": 1, "auto_generated": true, "generator": "simple"}, {"id": "NG0263", "symbol": "⋞", "code": "ng:logic:not_10", "fallback": "[NOT10]", "category": "logic", "name": "not_10", "description": "Logic operation: not_10", "unicode_point": "U+22DE", "approved_date": "2025-05-23", "validation_score": 95.0, "status": "certified", "token_cost": 1, "auto_generated": true, "generator": "simple"}, {"id": "NG0264", "symbol": "⚂", "code": "ng:operator:mul_7", "fallback": "[MUL7]", "category": "operator", "name": "mul_7", "description": "Operator operation: mul_7", "unicode_point": "U+2682", "approved_date": "2025-05-23", "validation_score": 95.0, "status": "certified", "token_cost": 1, "auto_generated": true, "generator": "simple"}, {"id": "NG0265", "symbol": "↠", "code": "ng:flow:while_13", "fallback": "[WHILE13]", "category": "flow", "name": "while_13", "description": "Flow operation: while_13", "unicode_point": "U+21A0", "approved_date": "2025-05-23", "validation_score": 95.0, "status": "certified", "token_cost": 1, "auto_generated": true, "generator": "simple"}, {"id": "NG0266", "symbol": "≐", "code": "ng:memory:ref_5", "fallback": "[REF5]", "category": "memory", "name": "ref_5", "description": "Memory operation: ref_5", "unicode_point": "U+2250", "approved_date": "2025-05-23", "validation_score": 95.0, "status": "certified", "token_cost": 1, "auto_generated": true, "generator": "simple"}, {"id": "NG0267", "symbol": "≥", "code": "ng:logic:iff_3", "fallback": "[IFF3]", "category": "logic", "name": "iff_3", "description": "Logic operation: iff_3", "unicode_point": "U+2265", "approved_date": "2025-05-23", "validation_score": 95.0, "status": "certified", "token_cost": 1, "auto_generated": true, "generator": "simple"}, {"id": "NG0268", "symbol": "≄", "code": "ng:structure:property_14", "fallback": "[PROPERTY14]", "category": "structure", "name": "property_14", "description": "Structure operation: property_14", "unicode_point": "U+2244", "approved_date": "2025-05-23", "validation_score": 95.0, "status": "certified", "token_cost": 1, "auto_generated": true, "generator": "simple"}, {"id": "NG0269", "symbol": "↲", "code": "ng:logic:not_11", "fallback": "[NOT11]", "category": "logic", "name": "not_11", "description": "Logic operation: not_11", "unicode_point": "U+21B2", "approved_date": "2025-05-23", "validation_score": 95.0, "status": "certified", "token_cost": 1, "auto_generated": true, "generator": "simple"}, {"id": "NG0270", "symbol": "☻", "code": "ng:logic:or_7", "fallback": "[OR7]", "category": "logic", "name": "or_7", "description": "Logic operation: or_7", "unicode_point": "U+263B", "approved_date": "2025-05-23", "validation_score": 95.0, "status": "certified", "token_cost": 1, "auto_generated": true, "generator": "simple"}, {"id": "NG0271", "symbol": "⇄", "code": "ng:operator:sub_9", "fallback": "[SUB9]", "category": "operator", "name": "sub_9", "description": "Operator operation: sub_9", "unicode_point": "U+21C4", "approved_date": "2025-05-23", "validation_score": 95.0, "status": "certified", "token_cost": 1, "auto_generated": true, "generator": "simple"}, {"id": "NG0272", "symbol": "◓", "code": "ng:logic:and_13", "fallback": "[AND13]", "category": "logic", "name": "and_13", "description": "Logic operation: and_13", "unicode_point": "U+25D3", "approved_date": "2025-05-23", "validation_score": 95.0, "status": "certified", "token_cost": 1, "auto_generated": true, "generator": "simple"}, {"id": "NG0273", "symbol": "↯", "code": "ng:logic:or_8", "fallback": "[OR8]", "category": "logic", "name": "or_8", "description": "Logic operation: or_8", "unicode_point": "U+21AF", "approved_date": "2025-05-23", "validation_score": 95.0, "status": "certified", "token_cost": 1, "auto_generated": true, "generator": "simple"}, {"id": "NG0274", "symbol": "⇇", "code": "ng:logic:not_12", "fallback": "[NOT12]", "category": "logic", "name": "not_12", "description": "Logic operation: not_12", "unicode_point": "U+21C7", "approved_date": "2025-05-23", "validation_score": 95.0, "status": "certified", "token_cost": 1, "auto_generated": true, "generator": "simple"}, {"id": "NG0275", "symbol": "♙", "code": "ng:logic:or_9", "fallback": "[OR9]", "category": "logic", "name": "or_9", "description": "Logic operation: or_9", "unicode_point": "U+2659", "approved_date": "2025-05-23", "validation_score": 95.0, "status": "certified", "token_cost": 1, "auto_generated": true, "generator": "simple"}, {"id": "NG0276", "symbol": "∓", "code": "ng:memory:free_7", "fallback": "[FREE7]", "category": "memory", "name": "free_7", "description": "Memory operation: free_7", "unicode_point": "U+2213", "approved_date": "2025-05-23", "validation_score": 95.0, "status": "certified", "token_cost": 1, "auto_generated": true, "generator": "simple"}, {"id": "NG0277", "symbol": "≿", "code": "ng:logic:not_13", "fallback": "[NOT13]", "category": "logic", "name": "not_13", "description": "Logic operation: not_13", "unicode_point": "U+227F", "approved_date": "2025-05-23", "validation_score": 95.0, "status": "certified", "token_cost": 1, "auto_generated": true, "generator": "simple"}, {"id": "NG0278", "symbol": "⇝", "code": "ng:structure:property_15", "fallback": "[PROPERTY15]", "category": "structure", "name": "property_15", "description": "Structure operation: property_15", "unicode_point": "U+21DD", "approved_date": "2025-05-23", "validation_score": 95.0, "status": "certified", "token_cost": 1, "auto_generated": true, "generator": "simple"}, {"id": "NG0279", "symbol": "▨", "code": "ng:operator:mod_8", "fallback": "[MOD8]", "category": "operator", "name": "mod_8", "description": "Operator operation: mod_8", "unicode_point": "U+25A8", "approved_date": "2025-05-23", "validation_score": 95.0, "status": "certified", "token_cost": 1, "auto_generated": true, "generator": "simple"}, {"id": "NG0280", "symbol": "≓", "code": "ng:flow:return_8", "fallback": "[RETURN8]", "category": "flow", "name": "return_8", "description": "Flow operation: return_8", "unicode_point": "U+2253", "approved_date": "2025-05-23", "validation_score": 95.0, "status": "certified", "token_cost": 1, "auto_generated": true, "generator": "simple"}, {"id": "NG0281", "symbol": "⋯", "code": "ng:structure:class_8", "fallback": "[CLASS8]", "category": "structure", "name": "class_8", "description": "Structure operation: class_8", "unicode_point": "U+22EF", "approved_date": "2025-05-23", "validation_score": 95.0, "status": "certified", "token_cost": 1, "auto_generated": true, "generator": "simple"}, {"id": "NG0282", "symbol": "↔", "code": "ng:operator:mod_9", "fallback": "[MOD9]", "category": "operator", "name": "mod_9", "description": "Operator operation: mod_9", "unicode_point": "U+2194", "approved_date": "2025-05-23", "validation_score": 95.0, "status": "certified", "token_cost": 1, "auto_generated": true, "generator": "simple"}, {"id": "NG0283", "symbol": "◍", "code": "ng:structure:function_14", "fallback": "[FUNCTION14]", "category": "structure", "name": "function_14", "description": "Structure operation: function_14", "unicode_point": "U+25CD", "approved_date": "2025-05-23", "validation_score": 95.0, "status": "certified", "token_cost": 1, "auto_generated": true, "generator": "simple"}, {"id": "NG0284", "symbol": "∇", "code": "ng:logic:iff_4", "fallback": "[IFF4]", "category": "logic", "name": "iff_4", "description": "Logic operation: iff_4", "unicode_point": "U+2207", "approved_date": "2025-05-23", "validation_score": 95.0, "status": "certified", "token_cost": 1, "auto_generated": true, "generator": "simple"}, {"id": "NG0285", "symbol": "⇳", "code": "ng:memory:free_8", "fallback": "[FREE8]", "category": "memory", "name": "free_8", "description": "Memory operation: free_8", "unicode_point": "U+21F3", "approved_date": "2025-05-23", "validation_score": 95.0, "status": "certified", "token_cost": 1, "auto_generated": true, "generator": "simple"}, {"id": "NG0286", "symbol": "∠", "code": "ng:logic:or_10", "fallback": "[OR10]", "category": "logic", "name": "or_10", "description": "Logic operation: or_10", "unicode_point": "U+2220", "approved_date": "2025-05-23", "validation_score": 95.0, "status": "certified", "token_cost": 1, "auto_generated": true, "generator": "simple"}, {"id": "NG0287", "symbol": "↡", "code": "ng:operator:div_8", "fallback": "[DIV8]", "category": "operator", "name": "div_8", "description": "Operator operation: div_8", "unicode_point": "U+21A1", "approved_date": "2025-05-23", "validation_score": 95.0, "status": "certified", "token_cost": 1, "auto_generated": true, "generator": "simple"}, {"id": "NG0288", "symbol": "⋾", "code": "ng:operator:div_9", "fallback": "[DIV9]", "category": "operator", "name": "div_9", "description": "Operator operation: div_9", "unicode_point": "U+22FE", "approved_date": "2025-05-23", "validation_score": 95.0, "status": "certified", "token_cost": 1, "auto_generated": true, "generator": "simple"}, {"id": "NG0289", "symbol": "⊻", "code": "ng:structure:function_15", "fallback": "[FUNCTION15]", "category": "structure", "name": "function_15", "description": "Structure operation: function_15", "unicode_point": "U+22BB", "approved_date": "2025-05-23", "validation_score": 95.0, "status": "certified", "token_cost": 1, "auto_generated": true, "generator": "simple"}, {"id": "NG0290", "symbol": "⇏", "code": "ng:flow:if_14", "fallback": "[IF14]", "category": "flow", "name": "if_14", "description": "Flow operation: if_14", "unicode_point": "U+21CF", "approved_date": "2025-05-23", "validation_score": 95.0, "status": "certified", "token_cost": 1, "auto_generated": true, "generator": "simple"}, {"id": "NG0291", "symbol": "☊", "code": "ng:flow:for_9", "fallback": "[FOR9]", "category": "flow", "name": "for_9", "description": "Flow operation: for_9", "unicode_point": "U+260A", "approved_date": "2025-05-23", "validation_score": 95.0, "status": "certified", "token_cost": 1, "auto_generated": true, "generator": "simple"}, {"id": "NG0292", "symbol": "⇓", "code": "ng:flow:for_10", "fallback": "[FOR10]", "category": "flow", "name": "for_10", "description": "Flow operation: for_10", "unicode_point": "U+21D3", "approved_date": "2025-05-23", "validation_score": 95.0, "status": "certified", "token_cost": 1, "auto_generated": true, "generator": "simple"}, {"id": "NG0293", "symbol": "☶", "code": "ng:operator:sub_10", "fallback": "[SUB10]", "category": "operator", "name": "sub_10", "description": "Operator operation: sub_10", "unicode_point": "U+2636", "approved_date": "2025-05-23", "validation_score": 95.0, "status": "certified", "token_cost": 1, "auto_generated": true, "generator": "simple"}, {"id": "NG0294", "symbol": "⇶", "code": "ng:structure:class_9", "fallback": "[CLASS9]", "category": "structure", "name": "class_9", "description": "Structure operation: class_9", "unicode_point": "U+21F6", "approved_date": "2025-05-23", "validation_score": 95.0, "status": "certified", "token_cost": 1, "auto_generated": true, "generator": "simple"}, {"id": "NG0295", "symbol": "☦", "code": "ng:operator:div_10", "fallback": "[DIV10]", "category": "operator", "name": "div_10", "description": "Operator operation: div_10", "unicode_point": "U+2626", "approved_date": "2025-05-23", "validation_score": 95.0, "status": "certified", "token_cost": 1, "auto_generated": true, "generator": "simple"}, {"id": "NG0296", "symbol": "⊋", "code": "ng:flow:for_11", "fallback": "[FOR11]", "category": "flow", "name": "for_11", "description": "Flow operation: for_11", "unicode_point": "U+228B", "approved_date": "2025-05-23", "validation_score": 95.0, "status": "certified", "token_cost": 1, "auto_generated": true, "generator": "simple"}, {"id": "NG0297", "symbol": "⇬", "code": "ng:flow:return_9", "fallback": "[RETURN9]", "category": "flow", "name": "return_9", "description": "Flow operation: return_9", "unicode_point": "U+21EC", "approved_date": "2025-05-23", "validation_score": 95.0, "status": "certified", "token_cost": 1, "auto_generated": true, "generator": "simple"}, {"id": "NG0298", "symbol": "▻", "code": "ng:operator:add_7", "fallback": "[ADD7]", "category": "operator", "name": "add_7", "description": "Operator operation: add_7", "unicode_point": "U+25BB", "approved_date": "2025-05-23", "validation_score": 95.0, "status": "certified", "token_cost": 1, "auto_generated": true, "generator": "simple"}, {"id": "NG0299", "symbol": "⚥", "code": "ng:flow:while_14", "fallback": "[WHILE14]", "category": "flow", "name": "while_14", "description": "Flow operation: while_14", "unicode_point": "U+26A5", "approved_date": "2025-05-23", "validation_score": 95.0, "status": "certified", "token_cost": 1, "auto_generated": true, "generator": "simple"}, {"id": "NG0300", "symbol": "→", "code": "ng:logic:implies_15", "fallback": "[IMPLIES15]", "category": "logic", "name": "implies_15", "description": "Logic operation: implies_15", "unicode_point": "U+2192", "approved_date": "2025-05-23", "validation_score": 95.0, "status": "certified", "token_cost": 1, "auto_generated": true, "generator": "simple"}, {"id": "NG0301", "symbol": "⇩", "code": "ng:memory:free_9", "fallback": "[FREE9]", "category": "memory", "name": "free_9", "description": "Memory operation: free_9", "unicode_point": "U+21E9", "approved_date": "2025-05-23", "validation_score": 95.0, "status": "certified", "token_cost": 1, "auto_generated": true, "generator": "simple"}, {"id": "NG0302", "symbol": "♭", "code": "ng:memory:alloc_11", "fallback": "[ALLOC11]", "category": "memory", "name": "alloc_11", "description": "Memory operation: alloc_11", "unicode_point": "U+266D", "approved_date": "2025-05-23", "validation_score": 95.0, "status": "certified", "token_cost": 1, "auto_generated": true, "generator": "simple"}, {"id": "NG0303", "symbol": "∤", "code": "ng:operator:mod_10", "fallback": "[MOD10]", "category": "operator", "name": "mod_10", "description": "Operator operation: mod_10", "unicode_point": "U+2224", "approved_date": "2025-05-23", "validation_score": 95.0, "status": "certified", "token_cost": 1, "auto_generated": true, "generator": "simple"}, {"id": "NG0304", "symbol": "⇼", "code": "ng:flow:while_15", "fallback": "[WHILE15]", "category": "flow", "name": "while_15", "description": "Flow operation: while_15", "unicode_point": "U+21FC", "approved_date": "2025-05-23", "validation_score": 95.0, "status": "certified", "token_cost": 1, "auto_generated": true, "generator": "simple"}, {"id": "NG0305", "symbol": "◅", "code": "ng:logic:xor_7", "fallback": "[XOR7]", "category": "logic", "name": "xor_7", "description": "Logic operation: xor_7", "unicode_point": "U+25C5", "approved_date": "2025-05-23", "validation_score": 95.0, "status": "certified", "token_cost": 1, "auto_generated": true, "generator": "simple"}, {"id": "NG0306", "symbol": "♥", "code": "ng:memory:alloc_12", "fallback": "[ALLOC12]", "category": "memory", "name": "alloc_12", "description": "Memory operation: alloc_12", "unicode_point": "U+2665", "approved_date": "2025-05-23", "validation_score": 95.0, "status": "certified", "token_cost": 1, "auto_generated": true, "generator": "simple"}, {"id": "NG0307", "symbol": "⇸", "code": "ng:logic:not_14", "fallback": "[NOT14]", "category": "logic", "name": "not_14", "description": "Logic operation: not_14", "unicode_point": "U+21F8", "approved_date": "2025-05-23", "validation_score": 95.0, "status": "certified", "token_cost": 1, "auto_generated": true, "generator": "simple"}, {"id": "NG0308", "symbol": "⚛", "code": "ng:flow:for_12", "fallback": "[FOR12]", "category": "flow", "name": "for_12", "description": "Flow operation: for_12", "unicode_point": "U+269B", "approved_date": "2025-05-23", "validation_score": 95.0, "status": "certified", "token_cost": 1, "auto_generated": true, "generator": "simple"}, {"id": "NG0309", "symbol": "◺", "code": "ng:flow:for_13", "fallback": "[FOR13]", "category": "flow", "name": "for_13", "description": "Flow operation: for_13", "unicode_point": "U+25FA", "approved_date": "2025-05-23", "validation_score": 95.0, "status": "certified", "token_cost": 1, "auto_generated": true, "generator": "simple"}, {"id": "NG0310", "symbol": "↶", "code": "ng:structure:property_16", "fallback": "[PROPERTY16]", "category": "structure", "name": "property_16", "description": "Structure operation: property_16", "unicode_point": "U+21B6", "approved_date": "2025-05-23", "validation_score": 95.0, "status": "certified", "token_cost": 1, "auto_generated": true, "generator": "simple"}, {"id": "NG0311", "symbol": "≠", "code": "ng:logic:not_15", "fallback": "[NOT15]", "category": "logic", "name": "not_15", "description": "Logic operation: not_15", "unicode_point": "U+2260", "approved_date": "2025-05-23", "validation_score": 95.0, "status": "certified", "token_cost": 1, "auto_generated": true, "generator": "simple"}, {"id": "NG0312", "symbol": "◥", "code": "ng:memory:deref_12", "fallback": "[DEREF12]", "category": "memory", "name": "deref_12", "description": "Memory operation: deref_12", "unicode_point": "U+25E5", "approved_date": "2025-05-23", "validation_score": 95.0, "status": "certified", "token_cost": 1, "auto_generated": true, "generator": "simple"}, {"id": "NG0313", "symbol": "⛍", "code": "ng:structure:function_16", "fallback": "[FUNCTION16]", "category": "structure", "name": "function_16", "description": "Structure operation: function_16", "unicode_point": "U+26CD", "approved_date": "2025-05-23", "validation_score": 95.0, "status": "certified", "token_cost": 1, "auto_generated": true, "generator": "simple"}, {"id": "NG0314", "symbol": "⇺", "code": "ng:structure:class_10", "fallback": "[CLASS10]", "category": "structure", "name": "class_10", "description": "Structure operation: class_10", "unicode_point": "U+21FA", "approved_date": "2025-05-23", "validation_score": 95.0, "status": "certified", "token_cost": 1, "auto_generated": true, "generator": "simple"}, {"id": "NG0315", "symbol": "☽", "code": "ng:flow:else_6", "fallback": "[ELSE6]", "category": "flow", "name": "else_6", "description": "Flow operation: else_6", "unicode_point": "U+263D", "approved_date": "2025-05-23", "validation_score": 95.0, "status": "certified", "token_cost": 1, "auto_generated": true, "generator": "simple"}, {"id": "NG0316", "symbol": "⊦", "code": "ng:logic:or_11", "fallback": "[OR11]", "category": "logic", "name": "or_11", "description": "Logic operation: or_11", "unicode_point": "U+22A6", "approved_date": "2025-05-23", "validation_score": 95.0, "status": "certified", "token_cost": 1, "auto_generated": true, "generator": "simple"}, {"id": "NG0317", "symbol": "↻", "code": "ng:memory:ref_6", "fallback": "[REF6]", "category": "memory", "name": "ref_6", "description": "Memory operation: ref_6", "unicode_point": "U+21BB", "approved_date": "2025-05-23", "validation_score": 95.0, "status": "certified", "token_cost": 1, "auto_generated": true, "generator": "simple"}, {"id": "NG0318", "symbol": "⋈", "code": "ng:structure:property_17", "fallback": "[PROPERTY17]", "category": "structure", "name": "property_17", "description": "Structure operation: property_17", "unicode_point": "U+22C8", "approved_date": "2025-05-23", "validation_score": 95.0, "status": "certified", "token_cost": 1, "auto_generated": true, "generator": "simple"}, {"id": "NG0319", "symbol": "⊾", "code": "ng:structure:property_18", "fallback": "[PROPERTY18]", "category": "structure", "name": "property_18", "description": "Structure operation: property_18", "unicode_point": "U+22BE", "approved_date": "2025-05-23", "validation_score": 95.0, "status": "certified", "token_cost": 1, "auto_generated": true, "generator": "simple"}, {"id": "NG0320", "symbol": "☀", "code": "ng:logic:iff_5", "fallback": "[IFF5]", "category": "logic", "name": "iff_5", "description": "Logic operation: iff_5", "unicode_point": "U+2600", "approved_date": "2025-05-23", "validation_score": 95.0, "status": "certified", "token_cost": 1, "auto_generated": true, "generator": "simple"}, {"id": "NG0321", "symbol": "↸", "code": "ng:flow:else_7", "fallback": "[ELSE7]", "category": "flow", "name": "else_7", "description": "Flow operation: else_7", "unicode_point": "U+21B8", "approved_date": "2025-05-23", "validation_score": 95.0, "status": "certified", "token_cost": 1, "auto_generated": true, "generator": "simple"}, {"id": "NG0322", "symbol": "⊽", "code": "ng:logic:iff_6", "fallback": "[IFF6]", "category": "logic", "name": "iff_6", "description": "Logic operation: iff_6", "unicode_point": "U+22BD", "approved_date": "2025-05-23", "validation_score": 95.0, "status": "certified", "token_cost": 1, "auto_generated": true, "generator": "simple"}, {"id": "NG0323", "symbol": "♡", "code": "ng:memory:free_10", "fallback": "[FREE10]", "category": "memory", "name": "free_10", "description": "Memory operation: free_10", "unicode_point": "U+2661", "approved_date": "2025-05-23", "validation_score": 95.0, "status": "certified", "token_cost": 1, "auto_generated": true, "generator": "simple"}, {"id": "NG0324", "symbol": "☈", "code": "ng:operator:mul_8", "fallback": "[MUL8]", "category": "operator", "name": "mul_8", "description": "Operator operation: mul_8", "unicode_point": "U+2608", "approved_date": "2025-05-23", "validation_score": 95.0, "status": "certified", "token_cost": 1, "auto_generated": true, "generator": "simple"}, {"id": "NG0325", "symbol": "∑", "code": "ng:structure:function_17", "fallback": "[FUNCTION17]", "category": "structure", "name": "function_17", "description": "Structure operation: function_17", "unicode_point": "U+2211", "approved_date": "2025-05-23", "validation_score": 95.0, "status": "certified", "token_cost": 1, "auto_generated": true, "generator": "simple"}, {"id": "NG0326", "symbol": "♧", "code": "ng:logic:and_14", "fallback": "[AND14]", "category": "logic", "name": "and_14", "description": "Logic operation: and_14", "unicode_point": "U+2667", "approved_date": "2025-05-23", "validation_score": 95.0, "status": "certified", "token_cost": 1, "auto_generated": true, "generator": "simple"}, {"id": "NG0327", "symbol": "⋮", "code": "ng:flow:while_16", "fallback": "[WHILE16]", "category": "flow", "name": "while_16", "description": "Flow operation: while_16", "unicode_point": "U+22EE", "approved_date": "2025-05-23", "validation_score": 95.0, "status": "certified", "token_cost": 1, "auto_generated": true, "generator": "simple"}, {"id": "NG0328", "symbol": "∩", "code": "ng:logic:iff_7", "fallback": "[IFF7]", "category": "logic", "name": "iff_7", "description": "Logic operation: iff_7", "unicode_point": "U+2229", "approved_date": "2025-05-23", "validation_score": 95.0, "status": "certified", "token_cost": 1, "auto_generated": true, "generator": "simple"}, {"id": "NG0329", "symbol": "⛘", "code": "ng:operator:div_11", "fallback": "[DIV11]", "category": "operator", "name": "div_11", "description": "Operator operation: div_11", "unicode_point": "U+26D8", "approved_date": "2025-05-23", "validation_score": 95.0, "status": "certified", "token_cost": 1, "auto_generated": true, "generator": "simple"}, {"id": "NG0330", "symbol": "☡", "code": "ng:flow:for_14", "fallback": "[FOR14]", "category": "flow", "name": "for_14", "description": "Flow operation: for_14", "unicode_point": "U+2621", "approved_date": "2025-05-23", "validation_score": 95.0, "status": "certified", "token_cost": 1, "auto_generated": true, "generator": "simple"}, {"id": "NG0331", "symbol": "⊆", "code": "ng:structure:function_18", "fallback": "[FUNCTION18]", "category": "structure", "name": "function_18", "description": "Structure operation: function_18", "unicode_point": "U+2286", "approved_date": "2025-05-23", "validation_score": 95.0, "status": "certified", "token_cost": 1, "auto_generated": true, "generator": "simple"}, {"id": "NG0332", "symbol": "∹", "code": "ng:logic:or_12", "fallback": "[OR12]", "category": "logic", "name": "or_12", "description": "Logic operation: or_12", "unicode_point": "U+2239", "approved_date": "2025-05-23", "validation_score": 95.0, "status": "certified", "token_cost": 1, "auto_generated": true, "generator": "simple"}, {"id": "NG0333", "symbol": "⛯", "code": "ng:flow:if_15", "fallback": "[IF15]", "category": "flow", "name": "if_15", "description": "Flow operation: if_15", "unicode_point": "U+26EF", "approved_date": "2025-05-23", "validation_score": 95.0, "status": "certified", "token_cost": 1, "auto_generated": true, "generator": "simple"}, {"id": "NG0334", "symbol": "∊", "code": "ng:operator:div_12", "fallback": "[DIV12]", "category": "operator", "name": "div_12", "description": "Operator operation: div_12", "unicode_point": "U+220A", "approved_date": "2025-05-23", "validation_score": 95.0, "status": "certified", "token_cost": 1, "auto_generated": true, "generator": "simple"}, {"id": "NG0335", "symbol": "⇉", "code": "ng:memory:deref_13", "fallback": "[DEREF13]", "category": "memory", "name": "deref_13", "description": "Memory operation: deref_13", "unicode_point": "U+21C9", "approved_date": "2025-05-23", "validation_score": 95.0, "status": "certified", "token_cost": 1, "auto_generated": true, "generator": "simple"}, {"id": "NG0336", "symbol": "⋄", "code": "ng:flow:return_10", "fallback": "[RETURN10]", "category": "flow", "name": "return_10", "description": "Flow operation: return_10", "unicode_point": "U+22C4", "approved_date": "2025-05-23", "validation_score": 95.0, "status": "certified", "token_cost": 1, "auto_generated": true, "generator": "simple"}, {"id": "NG0337", "symbol": "⚅", "code": "ng:logic:iff_8", "fallback": "[IFF8]", "category": "logic", "name": "iff_8", "description": "Logic operation: iff_8", "unicode_point": "U+2685", "approved_date": "2025-05-23", "validation_score": 95.0, "status": "certified", "token_cost": 1, "auto_generated": true, "generator": "simple"}, {"id": "NG0338", "symbol": "♿", "code": "ng:flow:for_15", "fallback": "[FOR15]", "category": "flow", "name": "for_15", "description": "Flow operation: for_15", "unicode_point": "U+267F", "approved_date": "2025-05-23", "validation_score": 95.0, "status": "certified", "token_cost": 1, "auto_generated": true, "generator": "simple"}, {"id": "NG0339", "symbol": "⊛", "code": "ng:flow:break_6", "fallback": "[BREAK6]", "category": "flow", "name": "break_6", "description": "Flow operation: break_6", "unicode_point": "U+229B", "approved_date": "2025-05-23", "validation_score": 95.0, "status": "certified", "token_cost": 1, "auto_generated": true, "generator": "simple"}, {"id": "NG0340", "symbol": "⚖", "code": "ng:logic:xor_8", "fallback": "[XOR8]", "category": "logic", "name": "xor_8", "description": "Logic operation: xor_8", "unicode_point": "U+2696", "approved_date": "2025-05-23", "validation_score": 95.0, "status": "certified", "token_cost": 1, "auto_generated": true, "generator": "simple"}, {"id": "NG0341", "symbol": "≱", "code": "ng:flow:return_11", "fallback": "[RETURN11]", "category": "flow", "name": "return_11", "description": "Flow operation: return_11", "unicode_point": "U+2271", "approved_date": "2025-05-23", "validation_score": 95.0, "status": "certified", "token_cost": 1, "auto_generated": true, "generator": "simple"}, {"id": "NG0342", "symbol": "◤", "code": "ng:memory:alloc_13", "fallback": "[ALLOC13]", "category": "memory", "name": "alloc_13", "description": "Memory operation: alloc_13", "unicode_point": "U+25E4", "approved_date": "2025-05-23", "validation_score": 95.0, "status": "certified", "token_cost": 1, "auto_generated": true, "generator": "simple"}, {"id": "NG0343", "symbol": "⋇", "code": "ng:flow:return_12", "fallback": "[RETURN12]", "category": "flow", "name": "return_12", "description": "Flow operation: return_12", "unicode_point": "U+22C7", "approved_date": "2025-05-23", "validation_score": 95.0, "status": "certified", "token_cost": 1, "auto_generated": true, "generator": "simple"}, {"id": "NG0344", "symbol": "▹", "code": "ng:operator:add_8", "fallback": "[ADD8]", "category": "operator", "name": "add_8", "description": "Operator operation: add_8", "unicode_point": "U+25B9", "approved_date": "2025-05-23", "validation_score": 95.0, "status": "certified", "token_cost": 1, "auto_generated": true, "generator": "simple"}, {"id": "NG0345", "symbol": "◠", "code": "ng:operator:mul_9", "fallback": "[MUL9]", "category": "operator", "name": "mul_9", "description": "Operator operation: mul_9", "unicode_point": "U+25E0", "approved_date": "2025-05-23", "validation_score": 95.0, "status": "certified", "token_cost": 1, "auto_generated": true, "generator": "simple"}, {"id": "NG0346", "symbol": "☵", "code": "ng:logic:implies_16", "fallback": "[IMPLIES16]", "category": "logic", "name": "implies_16", "description": "Logic operation: implies_16", "unicode_point": "U+2635", "approved_date": "2025-05-23", "validation_score": 95.0, "status": "certified", "token_cost": 1, "auto_generated": true, "generator": "simple"}, {"id": "NG0347", "symbol": "◀", "code": "ng:flow:while_17", "fallback": "[WHILE17]", "category": "flow", "name": "while_17", "description": "Flow operation: while_17", "unicode_point": "U+25C0", "approved_date": "2025-05-23", "validation_score": 95.0, "status": "certified", "token_cost": 1, "auto_generated": true, "generator": "simple"}, {"id": "NG0348", "symbol": "↳", "code": "ng:operator:div_13", "fallback": "[DIV13]", "category": "operator", "name": "div_13", "description": "Operator operation: div_13", "unicode_point": "U+21B3", "approved_date": "2025-05-23", "validation_score": 95.0, "status": "certified", "token_cost": 1, "auto_generated": true, "generator": "simple"}, {"id": "NG0349", "symbol": "∾", "code": "ng:logic:xor_9", "fallback": "[XOR9]", "category": "logic", "name": "xor_9", "description": "Logic operation: xor_9", "unicode_point": "U+223E", "approved_date": "2025-05-23", "validation_score": 95.0, "status": "certified", "token_cost": 1, "auto_generated": true, "generator": "simple"}, {"id": "NG0350", "symbol": "⛟", "code": "ng:flow:if_16", "fallback": "[IF16]", "category": "flow", "name": "if_16", "description": "Flow operation: if_16", "unicode_point": "U+26DF", "approved_date": "2025-05-23", "validation_score": 95.0, "status": "certified", "token_cost": 1, "auto_generated": true, "generator": "simple"}, {"id": "NG0351", "symbol": "↕", "code": "ng:operator:mod_11", "fallback": "[MOD11]", "category": "operator", "name": "mod_11", "description": "Operator operation: mod_11", "unicode_point": "U+2195", "approved_date": "2025-05-23", "validation_score": 95.0, "status": "certified", "token_cost": 1, "auto_generated": true, "generator": "simple"}, {"id": "NG0352", "symbol": "⛂", "code": "ng:logic:implies_17", "fallback": "[IMPLIES17]", "category": "logic", "name": "implies_17", "description": "Logic operation: implies_17", "unicode_point": "U+26C2", "approved_date": "2025-05-23", "validation_score": 95.0, "status": "certified", "token_cost": 1, "auto_generated": true, "generator": "simple"}, {"id": "NG0353", "symbol": "⛇", "code": "ng:memory:deref_14", "fallback": "[DEREF14]", "category": "memory", "name": "deref_14", "description": "Memory operation: deref_14", "unicode_point": "U+26C7", "approved_date": "2025-05-23", "validation_score": 95.0, "status": "certified", "token_cost": 1, "auto_generated": true, "generator": "simple"}, {"id": "NG0354", "symbol": "⛺", "code": "ng:logic:implies_18", "fallback": "[IMPLIES18]", "category": "logic", "name": "implies_18", "description": "Logic operation: implies_18", "unicode_point": "U+26FA", "approved_date": "2025-05-23", "validation_score": 95.0, "status": "certified", "token_cost": 1, "auto_generated": true, "generator": "simple"}, {"id": "NG0355", "symbol": "☢", "code": "ng:flow:for_16", "fallback": "[FOR16]", "category": "flow", "name": "for_16", "description": "Flow operation: for_16", "unicode_point": "U+2622", "approved_date": "2025-05-23", "validation_score": 95.0, "status": "certified", "token_cost": 1, "auto_generated": true, "generator": "simple"}, {"id": "NG0356", "symbol": "⊅", "code": "ng:memory:alloc_14", "fallback": "[ALLOC14]", "category": "memory", "name": "alloc_14", "description": "Memory operation: alloc_14", "unicode_point": "U+2285", "approved_date": "2025-05-23", "validation_score": 95.0, "status": "certified", "token_cost": 1, "auto_generated": true, "generator": "simple"}, {"id": "NG0357", "symbol": "⊒", "code": "ng:logic:or_13", "fallback": "[OR13]", "category": "logic", "name": "or_13", "description": "Logic operation: or_13", "unicode_point": "U+2292", "approved_date": "2025-05-23", "validation_score": 95.0, "status": "certified", "token_cost": 1, "auto_generated": true, "generator": "simple"}, {"id": "NG0358", "symbol": "♏", "code": "ng:memory:free_11", "fallback": "[FREE11]", "category": "memory", "name": "free_11", "description": "Memory operation: free_11", "unicode_point": "U+264F", "approved_date": "2025-05-23", "validation_score": 95.0, "status": "certified", "token_cost": 1, "auto_generated": true, "generator": "simple"}, {"id": "NG0359", "symbol": "⋔", "code": "ng:logic:not_16", "fallback": "[NOT16]", "category": "logic", "name": "not_16", "description": "Logic operation: not_16", "unicode_point": "U+22D4", "approved_date": "2025-05-23", "validation_score": 95.0, "status": "certified", "token_cost": 1, "auto_generated": true, "generator": "simple"}, {"id": "NG0360", "symbol": "▾", "code": "ng:memory:alloc_15", "fallback": "[ALLOC15]", "category": "memory", "name": "alloc_15", "description": "Memory operation: alloc_15", "unicode_point": "U+25BE", "approved_date": "2025-05-23", "validation_score": 95.0, "status": "certified", "token_cost": 1, "auto_generated": true, "generator": "simple"}, {"id": "NG0361", "symbol": "∐", "code": "ng:logic:implies_19", "fallback": "[IMPLIES19]", "category": "logic", "name": "implies_19", "description": "Logic operation: implies_19", "unicode_point": "U+2210", "approved_date": "2025-05-23", "validation_score": 95.0, "status": "certified", "token_cost": 1, "auto_generated": true, "generator": "simple"}, {"id": "NG0362", "symbol": "⋿", "code": "ng:structure:class_11", "fallback": "[CLASS11]", "category": "structure", "name": "class_11", "description": "Structure operation: class_11", "unicode_point": "U+22FF", "approved_date": "2025-05-23", "validation_score": 95.0, "status": "certified", "token_cost": 1, "auto_generated": true, "generator": "simple"}, {"id": "NG0363", "symbol": "⛲", "code": "ng:operator:div_14", "fallback": "[DIV14]", "category": "operator", "name": "div_14", "description": "Operator operation: div_14", "unicode_point": "U+26F2", "approved_date": "2025-05-23", "validation_score": 95.0, "status": "certified", "token_cost": 1, "auto_generated": true, "generator": "simple"}, {"id": "NG0364", "symbol": "⚀", "code": "ng:logic:xor_10", "fallback": "[XOR10]", "category": "logic", "name": "xor_10", "description": "Logic operation: xor_10", "unicode_point": "U+2680", "approved_date": "2025-05-23", "validation_score": 95.0, "status": "certified", "token_cost": 1, "auto_generated": true, "generator": "simple"}, {"id": "NG0365", "symbol": "⛊", "code": "ng:structure:class_12", "fallback": "[CLASS12]", "category": "structure", "name": "class_12", "description": "Structure operation: class_12", "unicode_point": "U+26CA", "approved_date": "2025-05-23", "validation_score": 95.0, "status": "certified", "token_cost": 1, "auto_generated": true, "generator": "simple"}, {"id": "NG0366", "symbol": "☥", "code": "ng:logic:iff_9", "fallback": "[IFF9]", "category": "logic", "name": "iff_9", "description": "Logic operation: iff_9", "unicode_point": "U+2625", "approved_date": "2025-05-23", "validation_score": 95.0, "status": "certified", "token_cost": 1, "auto_generated": true, "generator": "simple"}, {"id": "NG0367", "symbol": "◭", "code": "ng:flow:else_8", "fallback": "[ELSE8]", "category": "flow", "name": "else_8", "description": "Flow operation: else_8", "unicode_point": "U+25ED", "approved_date": "2025-05-23", "validation_score": 95.0, "status": "certified", "token_cost": 1, "auto_generated": true, "generator": "simple"}, {"id": "NG0368", "symbol": "◝", "code": "ng:memory:pointer_13", "fallback": "[POINTER13]", "category": "memory", "name": "pointer_13", "description": "Memory operation: pointer_13", "unicode_point": "U+25DD", "approved_date": "2025-05-23", "validation_score": 95.0, "status": "certified", "token_cost": 1, "auto_generated": true, "generator": "simple"}, {"id": "NG0369", "symbol": "☩", "code": "ng:flow:break_7", "fallback": "[BREAK7]", "category": "flow", "name": "break_7", "description": "Flow operation: break_7", "unicode_point": "U+2629", "approved_date": "2025-05-23", "validation_score": 95.0, "status": "certified", "token_cost": 1, "auto_generated": true, "generator": "simple"}, {"id": "NG0370", "symbol": "♁", "code": "ng:structure:class_13", "fallback": "[CLASS13]", "category": "structure", "name": "class_13", "description": "Structure operation: class_13", "unicode_point": "U+2641", "approved_date": "2025-05-23", "validation_score": 95.0, "status": "certified", "token_cost": 1, "auto_generated": true, "generator": "simple"}, {"id": "NG0371", "symbol": "⛋", "code": "ng:operator:sub_11", "fallback": "[SUB11]", "category": "operator", "name": "sub_11", "description": "Operator operation: sub_11", "unicode_point": "U+26CB", "approved_date": "2025-05-23", "validation_score": 95.0, "status": "certified", "token_cost": 1, "auto_generated": true, "generator": "simple"}, {"id": "NG0372", "symbol": "⋱", "code": "ng:flow:return_13", "fallback": "[RETURN13]", "category": "flow", "name": "return_13", "description": "Flow operation: return_13", "unicode_point": "U+22F1", "approved_date": "2025-05-23", "validation_score": 95.0, "status": "certified", "token_cost": 1, "auto_generated": true, "generator": "simple"}, {"id": "NG0373", "symbol": "↫", "code": "ng:structure:class_14", "fallback": "[CLASS14]", "category": "structure", "name": "class_14", "description": "Structure operation: class_14", "unicode_point": "U+21AB", "approved_date": "2025-05-23", "validation_score": 95.0, "status": "certified", "token_cost": 1, "auto_generated": true, "generator": "simple"}, {"id": "NG0374", "symbol": "☰", "code": "ng:logic:or_14", "fallback": "[OR14]", "category": "logic", "name": "or_14", "description": "Logic operation: or_14", "unicode_point": "U+2630", "approved_date": "2025-05-23", "validation_score": 95.0, "status": "certified", "token_cost": 1, "auto_generated": true, "generator": "simple"}, {"id": "NG0375", "symbol": "⊱", "code": "ng:memory:ref_7", "fallback": "[REF7]", "category": "memory", "name": "ref_7", "description": "Memory operation: ref_7", "unicode_point": "U+22B1", "approved_date": "2025-05-23", "validation_score": 95.0, "status": "certified", "token_cost": 1, "auto_generated": true, "generator": "simple"}, {"id": "NG0376", "symbol": "♻", "code": "ng:operator:sub_12", "fallback": "[SUB12]", "category": "operator", "name": "sub_12", "description": "Operator operation: sub_12", "unicode_point": "U+267B", "approved_date": "2025-05-23", "validation_score": 95.0, "status": "certified", "token_cost": 1, "auto_generated": true, "generator": "simple"}, {"id": "NG0377", "symbol": "⋹", "code": "ng:flow:break_8", "fallback": "[BREAK8]", "category": "flow", "name": "break_8", "description": "Flow operation: break_8", "unicode_point": "U+22F9", "approved_date": "2025-05-23", "validation_score": 95.0, "status": "certified", "token_cost": 1, "auto_generated": true, "generator": "simple"}, {"id": "NG0378", "symbol": "⊎", "code": "ng:memory:free_12", "fallback": "[FREE12]", "category": "memory", "name": "free_12", "description": "Memory operation: free_12", "unicode_point": "U+228E", "approved_date": "2025-05-23", "validation_score": 95.0, "status": "certified", "token_cost": 1, "auto_generated": true, "generator": "simple"}, {"id": "NG0379", "symbol": "⊝", "code": "ng:logic:implies_20", "fallback": "[IMPLIES20]", "category": "logic", "name": "implies_20", "description": "Logic operation: implies_20", "unicode_point": "U+229D", "approved_date": "2025-05-23", "validation_score": 95.0, "status": "certified", "token_cost": 1, "auto_generated": true, "generator": "simple"}, {"id": "NG0380", "symbol": "▵", "code": "ng:memory:ref_8", "fallback": "[REF8]", "category": "memory", "name": "ref_8", "description": "Memory operation: ref_8", "unicode_point": "U+25B5", "approved_date": "2025-05-23", "validation_score": 95.0, "status": "certified", "token_cost": 1, "auto_generated": true, "generator": "simple"}, {"id": "NG0381", "symbol": "≉", "code": "ng:memory:ref_9", "fallback": "[REF9]", "category": "memory", "name": "ref_9", "description": "Memory operation: ref_9", "unicode_point": "U+2249", "approved_date": "2025-05-23", "validation_score": 95.0, "status": "certified", "token_cost": 1, "auto_generated": true, "generator": "simple"}, {"id": "NG0382", "symbol": "⛭", "code": "ng:memory:alloc_16", "fallback": "[ALLOC16]", "category": "memory", "name": "alloc_16", "description": "Memory operation: alloc_16", "unicode_point": "U+26ED", "approved_date": "2025-05-23", "validation_score": 95.0, "status": "certified", "token_cost": 1, "auto_generated": true, "generator": "simple"}, {"id": "NG0383", "symbol": "∎", "code": "ng:structure:method_8", "fallback": "[METHOD8]", "category": "structure", "name": "method_8", "description": "Structure operation: method_8", "unicode_point": "U+220E", "approved_date": "2025-05-23", "validation_score": 95.0, "status": "certified", "token_cost": 1, "auto_generated": true, "generator": "simple"}, {"id": "NG0384", "symbol": "∪", "code": "ng:structure:class_15", "fallback": "[CLASS15]", "category": "structure", "name": "class_15", "description": "Structure operation: class_15", "unicode_point": "U+222A", "approved_date": "2025-05-23", "validation_score": 95.0, "status": "certified", "token_cost": 1, "auto_generated": true, "generator": "simple"}, {"id": "NG0385", "symbol": "⚯", "code": "ng:structure:method_9", "fallback": "[METHOD9]", "category": "structure", "name": "method_9", "description": "Structure operation: method_9", "unicode_point": "U+26AF", "approved_date": "2025-05-23", "validation_score": 95.0, "status": "certified", "token_cost": 1, "auto_generated": true, "generator": "simple"}, {"id": "NG0386", "symbol": "⛱", "code": "ng:operator:pow_11", "fallback": "[POW11]", "category": "operator", "name": "pow_11", "description": "Operator operation: pow_11", "unicode_point": "U+26F1", "approved_date": "2025-05-23", "validation_score": 95.0, "status": "certified", "token_cost": 1, "auto_generated": true, "generator": "simple"}, {"id": "NG0387", "symbol": "⊷", "code": "ng:structure:function_19", "fallback": "[FUNCTION19]", "category": "structure", "name": "function_19", "description": "Structure operation: function_19", "unicode_point": "U+22B7", "approved_date": "2025-05-23", "validation_score": 95.0, "status": "certified", "token_cost": 1, "auto_generated": true, "generator": "simple"}, {"id": "NG0388", "symbol": "♛", "code": "ng:memory:alloc_17", "fallback": "[ALLOC17]", "category": "memory", "name": "alloc_17", "description": "Memory operation: alloc_17", "unicode_point": "U+265B", "approved_date": "2025-05-23", "validation_score": 95.0, "status": "certified", "token_cost": 1, "auto_generated": true, "generator": "simple"}, {"id": "NG0389", "symbol": "⚫", "code": "ng:memory:free_13", "fallback": "[FREE13]", "category": "memory", "name": "free_13", "description": "Memory operation: free_13", "unicode_point": "U+26AB", "approved_date": "2025-05-23", "validation_score": 95.0, "status": "certified", "token_cost": 1, "auto_generated": true, "generator": "simple"}, {"id": "NG0390", "symbol": "∖", "code": "ng:memory:ref_10", "fallback": "[REF10]", "category": "memory", "name": "ref_10", "description": "Memory operation: ref_10", "unicode_point": "U+2216", "approved_date": "2025-05-23", "validation_score": 95.0, "status": "certified", "token_cost": 1, "auto_generated": true, "generator": "simple"}, {"id": "NG0391", "symbol": "⚻", "code": "ng:structure:method_10", "fallback": "[METHOD10]", "category": "structure", "name": "method_10", "description": "Structure operation: method_10", "unicode_point": "U+26BB", "approved_date": "2025-05-23", "validation_score": 95.0, "status": "certified", "token_cost": 1, "auto_generated": true, "generator": "simple"}, {"id": "NG0392", "symbol": "⋙", "code": "ng:memory:alloc_18", "fallback": "[ALLOC18]", "category": "memory", "name": "alloc_18", "description": "Memory operation: alloc_18", "unicode_point": "U+22D9", "approved_date": "2025-05-23", "validation_score": 95.0, "status": "certified", "token_cost": 1, "auto_generated": true, "generator": "simple"}, {"id": "NG0393", "symbol": "☟", "code": "ng:flow:if_17", "fallback": "[IF17]", "category": "flow", "name": "if_17", "description": "Flow operation: if_17", "unicode_point": "U+261F", "approved_date": "2025-05-23", "validation_score": 95.0, "status": "certified", "token_cost": 1, "auto_generated": true, "generator": "simple"}, {"id": "NG0394", "symbol": "⇞", "code": "ng:structure:method_11", "fallback": "[METHOD11]", "category": "structure", "name": "method_11", "description": "Structure operation: method_11", "unicode_point": "U+21DE", "approved_date": "2025-05-23", "validation_score": 95.0, "status": "certified", "token_cost": 1, "auto_generated": true, "generator": "simple"}, {"id": "NG0395", "symbol": "♂", "code": "ng:structure:class_16", "fallback": "[CLASS16]", "category": "structure", "name": "class_16", "description": "Structure operation: class_16", "unicode_point": "U+2642", "approved_date": "2025-05-23", "validation_score": 95.0, "status": "certified", "token_cost": 1, "auto_generated": true, "generator": "simple"}, {"id": "NG0396", "symbol": "↣", "code": "ng:memory:ref_11", "fallback": "[REF11]", "category": "memory", "name": "ref_11", "description": "Memory operation: ref_11", "unicode_point": "U+21A3", "approved_date": "2025-05-23", "validation_score": 95.0, "status": "certified", "token_cost": 1, "auto_generated": true, "generator": "simple"}, {"id": "NG0397", "symbol": "⚝", "code": "ng:memory:ref_12", "fallback": "[REF12]", "category": "memory", "name": "ref_12", "description": "Memory operation: ref_12", "unicode_point": "U+269D", "approved_date": "2025-05-23", "validation_score": 95.0, "status": "certified", "token_cost": 1, "auto_generated": true, "generator": "simple"}, {"id": "NG0398", "symbol": "♤", "code": "ng:operator:mod_12", "fallback": "[MOD12]", "category": "operator", "name": "mod_12", "description": "Operator operation: mod_12", "unicode_point": "U+2664", "approved_date": "2025-05-23", "validation_score": 95.0, "status": "certified", "token_cost": 1, "auto_generated": true, "generator": "simple"}, {"id": "NG0399", "symbol": "⇹", "code": "ng:memory:free_14", "fallback": "[FREE14]", "category": "memory", "name": "free_14", "description": "Memory operation: free_14", "unicode_point": "U+21F9", "approved_date": "2025-05-23", "validation_score": 95.0, "status": "certified", "token_cost": 1, "auto_generated": true, "generator": "simple"}, {"id": "NG0400", "symbol": "▯", "code": "ng:memory:deref_15", "fallback": "[DEREF15]", "category": "memory", "name": "deref_15", "description": "Memory operation: deref_15", "unicode_point": "U+25AF", "approved_date": "2025-05-23", "validation_score": 95.0, "status": "certified", "token_cost": 1, "auto_generated": true, "generator": "simple"}, {"id": "NG0401", "symbol": "▭", "code": "ng:operator:add_9", "fallback": "[ADD9]", "category": "operator", "name": "add_9", "description": "Operator operation: add_9", "unicode_point": "U+25AD", "approved_date": "2025-05-23", "validation_score": 95.0, "status": "certified", "token_cost": 1, "auto_generated": true, "generator": "simple"}, {"id": "NG0402", "symbol": "⛞", "code": "ng:operator:add_10", "fallback": "[ADD10]", "category": "operator", "name": "add_10", "description": "Operator operation: add_10", "unicode_point": "U+26DE", "approved_date": "2025-05-23", "validation_score": 95.0, "status": "certified", "token_cost": 1, "auto_generated": true, "generator": "simple"}, {"id": "NG0403", "symbol": "⛚", "code": "ng:operator:sub_13", "fallback": "[SUB13]", "category": "operator", "name": "sub_13", "description": "Operator operation: sub_13", "unicode_point": "U+26DA", "approved_date": "2025-05-23", "validation_score": 95.0, "status": "certified", "token_cost": 1, "auto_generated": true, "generator": "simple"}, {"id": "NG0404", "symbol": "⚧", "code": "ng:structure:class_17", "fallback": "[CLASS17]", "category": "structure", "name": "class_17", "description": "Structure operation: class_17", "unicode_point": "U+26A7", "approved_date": "2025-05-23", "validation_score": 95.0, "status": "certified", "token_cost": 1, "auto_generated": true, "generator": "simple"}, {"id": "NG0405", "symbol": "▴", "code": "ng:memory:free_15", "fallback": "[FREE15]", "category": "memory", "name": "free_15", "description": "Memory operation: free_15", "unicode_point": "U+25B4", "approved_date": "2025-05-23", "validation_score": 95.0, "status": "certified", "token_cost": 1, "auto_generated": true, "generator": "simple"}, {"id": "NG0406", "symbol": "≋", "code": "ng:operator:add_11", "fallback": "[ADD11]", "category": "operator", "name": "add_11", "description": "Operator operation: add_11", "unicode_point": "U+224B", "approved_date": "2025-05-23", "validation_score": 95.0, "status": "certified", "token_cost": 1, "auto_generated": true, "generator": "simple"}, {"id": "NG0407", "symbol": "⛥", "code": "ng:logic:implies_21", "fallback": "[IMPLIES21]", "category": "logic", "name": "implies_21", "description": "Logic operation: implies_21", "unicode_point": "U+26E5", "approved_date": "2025-05-23", "validation_score": 95.0, "status": "certified", "token_cost": 1, "auto_generated": true, "generator": "simple"}, {"id": "NG0408", "symbol": "♝", "code": "ng:memory:alloc_19", "fallback": "[ALLOC19]", "category": "memory", "name": "alloc_19", "description": "Memory operation: alloc_19", "unicode_point": "U+265D", "approved_date": "2025-05-23", "validation_score": 95.0, "status": "certified", "token_cost": 1, "auto_generated": true, "generator": "simple"}, {"id": "NG0409", "symbol": "☠", "code": "ng:memory:free_16", "fallback": "[FREE16]", "category": "memory", "name": "free_16", "description": "Memory operation: free_16", "unicode_point": "U+2620", "approved_date": "2025-05-23", "validation_score": 95.0, "status": "certified", "token_cost": 1, "auto_generated": true, "generator": "simple"}, {"id": "NG0410", "symbol": "▢", "code": "ng:operator:mod_13", "fallback": "[MOD13]", "category": "operator", "name": "mod_13", "description": "Operator operation: mod_13", "unicode_point": "U+25A2", "approved_date": "2025-05-23", "validation_score": 95.0, "status": "certified", "token_cost": 1, "auto_generated": true, "generator": "simple"}, {"id": "NG0411", "symbol": "☇", "code": "ng:logic:iff_10", "fallback": "[IFF10]", "category": "logic", "name": "iff_10", "description": "Logic operation: iff_10", "unicode_point": "U+2607", "approved_date": "2025-05-23", "validation_score": 95.0, "status": "certified", "token_cost": 1, "auto_generated": true, "generator": "simple"}, {"id": "NG0412", "symbol": "≕", "code": "ng:memory:alloc_20", "fallback": "[ALLOC20]", "category": "memory", "name": "alloc_20", "description": "Memory operation: alloc_20", "unicode_point": "U+2255", "approved_date": "2025-05-23", "validation_score": 95.0, "status": "certified", "token_cost": 1, "auto_generated": true, "generator": "simple"}, {"id": "NG0413", "symbol": "⇯", "code": "ng:logic:or_15", "fallback": "[OR15]", "category": "logic", "name": "or_15", "description": "Logic operation: or_15", "unicode_point": "U+21EF", "approved_date": "2025-05-23", "validation_score": 95.0, "status": "certified", "token_cost": 1, "auto_generated": true, "generator": "simple"}, {"id": "NG0414", "symbol": "☞", "code": "ng:flow:break_9", "fallback": "[BREAK9]", "category": "flow", "name": "break_9", "description": "Flow operation: break_9", "unicode_point": "U+261E", "approved_date": "2025-05-23", "validation_score": 95.0, "status": "certified", "token_cost": 1, "auto_generated": true, "generator": "simple"}, {"id": "NG0415", "symbol": "⇧", "code": "ng:flow:break_10", "fallback": "[BREAK10]", "category": "flow", "name": "break_10", "description": "Flow operation: break_10", "unicode_point": "U+21E7", "approved_date": "2025-05-23", "validation_score": 95.0, "status": "certified", "token_cost": 1, "auto_generated": true, "generator": "simple"}, {"id": "NG0416", "symbol": "⚍", "code": "ng:structure:class_18", "fallback": "[CLASS18]", "category": "structure", "name": "class_18", "description": "Structure operation: class_18", "unicode_point": "U+268D", "approved_date": "2025-05-23", "validation_score": 95.0, "status": "certified", "token_cost": 1, "auto_generated": true, "generator": "simple"}, {"id": "NG0417", "symbol": "↵", "code": "ng:logic:or_16", "fallback": "[OR16]", "category": "logic", "name": "or_16", "description": "Logic operation: or_16", "unicode_point": "U+21B5", "approved_date": "2025-05-23", "validation_score": 95.0, "status": "certified", "token_cost": 1, "auto_generated": true, "generator": "simple"}, {"id": "NG0418", "symbol": "☭", "code": "ng:structure:class_19", "fallback": "[CLASS19]", "category": "structure", "name": "class_19", "description": "Structure operation: class_19", "unicode_point": "U+262D", "approved_date": "2025-05-23", "validation_score": 95.0, "status": "certified", "token_cost": 1, "auto_generated": true, "generator": "simple"}, {"id": "NG0419", "symbol": "⋧", "code": "ng:operator:mul_10", "fallback": "[MUL10]", "category": "operator", "name": "mul_10", "description": "Operator operation: mul_10", "unicode_point": "U+22E7", "approved_date": "2025-05-23", "validation_score": 95.0, "status": "certified", "token_cost": 1, "auto_generated": true, "generator": "simple"}, {"id": "NG0420", "symbol": "⋬", "code": "ng:operator:mul_11", "fallback": "[MUL11]", "category": "operator", "name": "mul_11", "description": "Operator operation: mul_11", "unicode_point": "U+22EC", "approved_date": "2025-05-23", "validation_score": 95.0, "status": "certified", "token_cost": 1, "auto_generated": true, "generator": "simple"}, {"id": "NG0421", "symbol": "∞", "code": "ng:logic:not_17", "fallback": "[NOT17]", "category": "logic", "name": "not_17", "description": "Logic operation: not_17", "unicode_point": "U+221E", "approved_date": "2025-05-23", "validation_score": 95.0, "status": "certified", "token_cost": 1, "auto_generated": true, "generator": "simple"}, {"id": "NG0422", "symbol": "▶", "code": "ng:memory:pointer_14", "fallback": "[POINTER14]", "category": "memory", "name": "pointer_14", "description": "Memory operation: pointer_14", "unicode_point": "U+25B6", "approved_date": "2025-05-23", "validation_score": 95.0, "status": "certified", "token_cost": 1, "auto_generated": true, "generator": "simple"}, {"id": "NG0423", "symbol": "↬", "code": "ng:flow:break_11", "fallback": "[BREAK11]", "category": "flow", "name": "break_11", "description": "Flow operation: break_11", "unicode_point": "U+21AC", "approved_date": "2025-05-23", "validation_score": 95.0, "status": "certified", "token_cost": 1, "auto_generated": true, "generator": "simple"}, {"id": "NG0424", "symbol": "⋰", "code": "ng:memory:ref_13", "fallback": "[REF13]", "category": "memory", "name": "ref_13", "description": "Memory operation: ref_13", "unicode_point": "U+22F0", "approved_date": "2025-05-23", "validation_score": 95.0, "status": "certified", "token_cost": 1, "auto_generated": true, "generator": "simple"}, {"id": "NG0425", "symbol": "⚣", "code": "ng:flow:else_9", "fallback": "[ELSE9]", "category": "flow", "name": "else_9", "description": "Flow operation: else_9", "unicode_point": "U+26A3", "approved_date": "2025-05-23", "validation_score": 95.0, "status": "certified", "token_cost": 1, "auto_generated": true, "generator": "simple"}, {"id": "NG0426", "symbol": "♘", "code": "ng:flow:for_17", "fallback": "[FOR17]", "category": "flow", "name": "for_17", "description": "Flow operation: for_17", "unicode_point": "U+2658", "approved_date": "2025-05-23", "validation_score": 95.0, "status": "certified", "token_cost": 1, "auto_generated": true, "generator": "simple"}, {"id": "NG0427", "symbol": "⚬", "code": "ng:structure:class_20", "fallback": "[CLASS20]", "category": "structure", "name": "class_20", "description": "Structure operation: class_20", "unicode_point": "U+26AC", "approved_date": "2025-05-23", "validation_score": 95.0, "status": "certified", "token_cost": 1, "auto_generated": true, "generator": "simple"}, {"id": "NG0428", "symbol": "◣", "code": "ng:structure:method_12", "fallback": "[METHOD12]", "category": "structure", "name": "method_12", "description": "Structure operation: method_12", "unicode_point": "U+25E3", "approved_date": "2025-05-23", "validation_score": 95.0, "status": "certified", "token_cost": 1, "auto_generated": true, "generator": "simple"}, {"id": "NG0429", "symbol": "∫", "code": "ng:logic:xor_11", "fallback": "[XOR11]", "category": "logic", "name": "xor_11", "description": "Logic operation: xor_11", "unicode_point": "U+222B", "approved_date": "2025-05-23", "validation_score": 95.0, "status": "certified", "token_cost": 1, "auto_generated": true, "generator": "simple"}, {"id": "NG0430", "symbol": "♾", "code": "ng:operator:div_15", "fallback": "[DIV15]", "category": "operator", "name": "div_15", "description": "Operator operation: div_15", "unicode_point": "U+267E", "approved_date": "2025-05-23", "validation_score": 95.0, "status": "certified", "token_cost": 1, "auto_generated": true, "generator": "simple"}, {"id": "NG0431", "symbol": "↺", "code": "ng:memory:deref_16", "fallback": "[DEREF16]", "category": "memory", "name": "deref_16", "description": "Memory operation: deref_16", "unicode_point": "U+21BA", "approved_date": "2025-05-23", "validation_score": 95.0, "status": "certified", "token_cost": 1, "auto_generated": true, "generator": "simple"}, {"id": "NG0432", "symbol": "⚽", "code": "ng:memory:pointer_15", "fallback": "[POINTER15]", "category": "memory", "name": "pointer_15", "description": "Memory operation: pointer_15", "unicode_point": "U+26BD", "approved_date": "2025-05-23", "validation_score": 95.0, "status": "certified", "token_cost": 1, "auto_generated": true, "generator": "simple"}, {"id": "NG0433", "symbol": "∆", "code": "ng:structure:function_20", "fallback": "[FUNCTION20]", "category": "structure", "name": "function_20", "description": "Structure operation: function_20", "unicode_point": "U+2206", "approved_date": "2025-05-23", "validation_score": 95.0, "status": "certified", "token_cost": 1, "auto_generated": true, "generator": "simple"}, {"id": "NG0434", "symbol": "⇜", "code": "ng:flow:if_18", "fallback": "[IF18]", "category": "flow", "name": "if_18", "description": "Flow operation: if_18", "unicode_point": "U+21DC", "approved_date": "2025-05-23", "validation_score": 95.0, "status": "certified", "token_cost": 1, "auto_generated": true, "generator": "simple"}, {"id": "NG0435", "symbol": "⛒", "code": "ng:memory:deref_17", "fallback": "[DEREF17]", "category": "memory", "name": "deref_17", "description": "Memory operation: deref_17", "unicode_point": "U+26D2", "approved_date": "2025-05-23", "validation_score": 95.0, "status": "certified", "token_cost": 1, "auto_generated": true, "generator": "simple"}, {"id": "NG0436", "symbol": "◩", "code": "ng:operator:div_16", "fallback": "[DIV16]", "category": "operator", "name": "div_16", "description": "Operator operation: div_16", "unicode_point": "U+25E9", "approved_date": "2025-05-23", "validation_score": 95.0, "status": "certified", "token_cost": 1, "auto_generated": true, "generator": "simple"}, {"id": "NG0437", "symbol": "⚜", "code": "ng:memory:free_17", "fallback": "[FREE17]", "category": "memory", "name": "free_17", "description": "Memory operation: free_17", "unicode_point": "U+269C", "approved_date": "2025-05-23", "validation_score": 95.0, "status": "certified", "token_cost": 1, "auto_generated": true, "generator": "simple"}, {"id": "NG0438", "symbol": "⋦", "code": "ng:memory:pointer_16", "fallback": "[POINTER16]", "category": "memory", "name": "pointer_16", "description": "Memory operation: pointer_16", "unicode_point": "U+22E6", "approved_date": "2025-05-23", "validation_score": 95.0, "status": "certified", "token_cost": 1, "auto_generated": true, "generator": "simple"}, {"id": "NG0439", "symbol": "♈", "code": "ng:structure:class_21", "fallback": "[CLASS21]", "category": "structure", "name": "class_21", "description": "Structure operation: class_21", "unicode_point": "U+2648", "approved_date": "2025-05-23", "validation_score": 95.0, "status": "certified", "token_cost": 1, "auto_generated": true, "generator": "simple"}, {"id": "NG0440", "symbol": "↓", "code": "ng:structure:class_22", "fallback": "[CLASS22]", "category": "structure", "name": "class_22", "description": "Structure operation: class_22", "unicode_point": "U+2193", "approved_date": "2025-05-23", "validation_score": 95.0, "status": "certified", "token_cost": 1, "auto_generated": true, "generator": "simple"}, {"id": "NG0441", "symbol": "▬", "code": "ng:logic:not_18", "fallback": "[NOT18]", "category": "logic", "name": "not_18", "description": "Logic operation: not_18", "unicode_point": "U+25AC", "approved_date": "2025-05-23", "validation_score": 95.0, "status": "certified", "token_cost": 1, "auto_generated": true, "generator": "simple"}, {"id": "NG0442", "symbol": "∲", "code": "ng:operator:div_17", "fallback": "[DIV17]", "category": "operator", "name": "div_17", "description": "Operator operation: div_17", "unicode_point": "U+2232", "approved_date": "2025-05-23", "validation_score": 95.0, "status": "certified", "token_cost": 1, "auto_generated": true, "generator": "simple"}, {"id": "NG0443", "symbol": "≜", "code": "ng:structure:property_19", "fallback": "[PROPERTY19]", "category": "structure", "name": "property_19", "description": "Structure operation: property_19", "unicode_point": "U+225C", "approved_date": "2025-05-23", "validation_score": 95.0, "status": "certified", "token_cost": 1, "auto_generated": true, "generator": "simple"}, {"id": "NG0444", "symbol": "⛵", "code": "ng:structure:class_23", "fallback": "[CLASS23]", "category": "structure", "name": "class_23", "description": "Structure operation: class_23", "unicode_point": "U+26F5", "approved_date": "2025-05-23", "validation_score": 95.0, "status": "certified", "token_cost": 1, "auto_generated": true, "generator": "simple"}, {"id": "NG0445", "symbol": "⋏", "code": "ng:logic:not_19", "fallback": "[NOT19]", "category": "logic", "name": "not_19", "description": "Logic operation: not_19", "unicode_point": "U+22CF", "approved_date": "2025-05-23", "validation_score": 95.0, "status": "certified", "token_cost": 1, "auto_generated": true, "generator": "simple"}, {"id": "NG0446", "symbol": "⊪", "code": "ng:logic:implies_22", "fallback": "[IMPLIES22]", "category": "logic", "name": "implies_22", "description": "Logic operation: implies_22", "unicode_point": "U+22AA", "approved_date": "2025-05-23", "validation_score": 95.0, "status": "certified", "token_cost": 1, "auto_generated": true, "generator": "simple"}, {"id": "NG0447", "symbol": "◖", "code": "ng:structure:method_13", "fallback": "[METHOD13]", "category": "structure", "name": "method_13", "description": "Structure operation: method_13", "unicode_point": "U+25D6", "approved_date": "2025-05-23", "validation_score": 95.0, "status": "certified", "token_cost": 1, "auto_generated": true, "generator": "simple"}, {"id": "NG0448", "symbol": "◫", "code": "ng:flow:break_12", "fallback": "[BREAK12]", "category": "flow", "name": "break_12", "description": "Flow operation: break_12", "unicode_point": "U+25EB", "approved_date": "2025-05-23", "validation_score": 95.0, "status": "certified", "token_cost": 1, "auto_generated": true, "generator": "simple"}, {"id": "NG0449", "symbol": "♣", "code": "ng:memory:free_18", "fallback": "[FREE18]", "category": "memory", "name": "free_18", "description": "Memory operation: free_18", "unicode_point": "U+2663", "approved_date": "2025-05-23", "validation_score": 95.0, "status": "certified", "token_cost": 1, "auto_generated": true, "generator": "simple"}, {"id": "NG0450", "symbol": "⊰", "code": "ng:memory:deref_18", "fallback": "[DEREF18]", "category": "memory", "name": "deref_18", "description": "Memory operation: deref_18", "unicode_point": "U+22B0", "approved_date": "2025-05-23", "validation_score": 95.0, "status": "certified", "token_cost": 1, "auto_generated": true, "generator": "simple"}, {"id": "NG0451", "symbol": "⇊", "code": "ng:logic:iff_11", "fallback": "[IFF11]", "category": "logic", "name": "iff_11", "description": "Logic operation: iff_11", "unicode_point": "U+21CA", "approved_date": "2025-05-23", "validation_score": 95.0, "status": "certified", "token_cost": 1, "auto_generated": true, "generator": "simple"}, {"id": "NG0452", "symbol": "⇥", "code": "ng:memory:free_19", "fallback": "[FREE19]", "category": "memory", "name": "free_19", "description": "Memory operation: free_19", "unicode_point": "U+21E5", "approved_date": "2025-05-23", "validation_score": 95.0, "status": "certified", "token_cost": 1, "auto_generated": true, "generator": "simple"}, {"id": "NG0453", "symbol": "∁", "code": "ng:flow:break_13", "fallback": "[BREAK13]", "category": "flow", "name": "break_13", "description": "Flow operation: break_13", "unicode_point": "U+2201", "approved_date": "2025-05-23", "validation_score": 95.0, "status": "certified", "token_cost": 1, "auto_generated": true, "generator": "simple"}, {"id": "NG0454", "symbol": "∦", "code": "ng:structure:function_21", "fallback": "[FUNCTION21]", "category": "structure", "name": "function_21", "description": "Structure operation: function_21", "unicode_point": "U+2226", "approved_date": "2025-05-23", "validation_score": 95.0, "status": "certified", "token_cost": 1, "auto_generated": true, "generator": "simple"}, {"id": "NG0455", "symbol": "♗", "code": "ng:structure:property_20", "fallback": "[PROPERTY20]", "category": "structure", "name": "property_20", "description": "Structure operation: property_20", "unicode_point": "U+2657", "approved_date": "2025-05-23", "validation_score": 95.0, "status": "certified", "token_cost": 1, "auto_generated": true, "generator": "simple"}, {"id": "NG0456", "symbol": "☜", "code": "ng:logic:or_17", "fallback": "[OR17]", "category": "logic", "name": "or_17", "description": "Logic operation: or_17", "unicode_point": "U+261C", "approved_date": "2025-05-23", "validation_score": 95.0, "status": "certified", "token_cost": 1, "auto_generated": true, "generator": "simple"}, {"id": "NG0457", "symbol": "≇", "code": "ng:memory:free_20", "fallback": "[FREE20]", "category": "memory", "name": "free_20", "description": "Memory operation: free_20", "unicode_point": "U+2247", "approved_date": "2025-05-23", "validation_score": 95.0, "status": "certified", "token_cost": 1, "auto_generated": true, "generator": "simple"}, {"id": "NG0458", "symbol": "⛝", "code": "ng:operator:div_18", "fallback": "[DIV18]", "category": "operator", "name": "div_18", "description": "Operator operation: div_18", "unicode_point": "U+26DD", "approved_date": "2025-05-23", "validation_score": 95.0, "status": "certified", "token_cost": 1, "auto_generated": true, "generator": "simple"}, {"id": "NG0459", "symbol": "⛔", "code": "ng:operator:pow_12", "fallback": "[POW12]", "category": "operator", "name": "pow_12", "description": "Operator operation: pow_12", "unicode_point": "U+26D4", "approved_date": "2025-05-23", "validation_score": 95.0, "status": "certified", "token_cost": 1, "auto_generated": true, "generator": "simple"}, {"id": "NG0460", "symbol": "◎", "code": "ng:logic:or_18", "fallback": "[OR18]", "category": "logic", "name": "or_18", "description": "Logic operation: or_18", "unicode_point": "U+25CE", "approved_date": "2025-05-23", "validation_score": 95.0, "status": "certified", "token_cost": 1, "auto_generated": true, "generator": "simple"}, {"id": "NG0461", "symbol": "⋃", "code": "ng:flow:for_18", "fallback": "[FOR18]", "category": "flow", "name": "for_18", "description": "Flow operation: for_18", "unicode_point": "U+22C3", "approved_date": "2025-05-23", "validation_score": 95.0, "status": "certified", "token_cost": 1, "auto_generated": true, "generator": "simple"}, {"id": "NG0462", "symbol": "◌", "code": "ng:flow:return_14", "fallback": "[RETURN14]", "category": "flow", "name": "return_14", "description": "Flow operation: return_14", "unicode_point": "U+25CC", "approved_date": "2025-05-23", "validation_score": 95.0, "status": "certified", "token_cost": 1, "auto_generated": true, "generator": "simple"}, {"id": "NG0463", "symbol": "⛖", "code": "ng:flow:for_19", "fallback": "[FOR19]", "category": "flow", "name": "for_19", "description": "Flow operation: for_19", "unicode_point": "U+26D6", "approved_date": "2025-05-23", "validation_score": 95.0, "status": "certified", "token_cost": 1, "auto_generated": true, "generator": "simple"}, {"id": "NG0464", "symbol": "☄", "code": "ng:operator:add_12", "fallback": "[ADD12]", "category": "operator", "name": "add_12", "description": "Operator operation: add_12", "unicode_point": "U+2604", "approved_date": "2025-05-23", "validation_score": 95.0, "status": "certified", "token_cost": 1, "auto_generated": true, "generator": "simple"}, {"id": "NG0465", "symbol": "⛸", "code": "ng:flow:if_19", "fallback": "[IF19]", "category": "flow", "name": "if_19", "description": "Flow operation: if_19", "unicode_point": "U+26F8", "approved_date": "2025-05-23", "validation_score": 95.0, "status": "certified", "token_cost": 1, "auto_generated": true, "generator": "simple"}, {"id": "NG0466", "symbol": "⊵", "code": "ng:logic:xor_12", "fallback": "[XOR12]", "category": "logic", "name": "xor_12", "description": "Logic operation: xor_12", "unicode_point": "U+22B5", "approved_date": "2025-05-23", "validation_score": 95.0, "status": "certified", "token_cost": 1, "auto_generated": true, "generator": "simple"}, {"id": "NG0467", "symbol": "♦", "code": "ng:logic:xor_13", "fallback": "[XOR13]", "category": "logic", "name": "xor_13", "description": "Logic operation: xor_13", "unicode_point": "U+2666", "approved_date": "2025-05-23", "validation_score": 95.0, "status": "certified", "token_cost": 1, "auto_generated": true, "generator": "simple"}, {"id": "NG0468", "symbol": "⋵", "code": "ng:operator:add_13", "fallback": "[ADD13]", "category": "operator", "name": "add_13", "description": "Operator operation: add_13", "unicode_point": "U+22F5", "approved_date": "2025-05-23", "validation_score": 95.0, "status": "certified", "token_cost": 1, "auto_generated": true, "generator": "simple"}, {"id": "NG0469", "symbol": "☾", "code": "ng:operator:add_14", "fallback": "[ADD14]", "category": "operator", "name": "add_14", "description": "Operator operation: add_14", "unicode_point": "U+263E", "approved_date": "2025-05-23", "validation_score": 95.0, "status": "certified", "token_cost": 1, "auto_generated": true, "generator": "simple"}, {"id": "NG0470", "symbol": "▣", "code": "ng:structure:property_21", "fallback": "[PROPERTY21]", "category": "structure", "name": "property_21", "description": "Structure operation: property_21", "unicode_point": "U+25A3", "approved_date": "2025-05-23", "validation_score": 95.0, "status": "certified", "token_cost": 1, "auto_generated": true, "generator": "simple"}, {"id": "NG0471", "symbol": "⋫", "code": "ng:structure:method_14", "fallback": "[METHOD14]", "category": "structure", "name": "method_14", "description": "Structure operation: method_14", "unicode_point": "U+22EB", "approved_date": "2025-05-23", "validation_score": 95.0, "status": "certified", "token_cost": 1, "auto_generated": true, "generator": "simple"}, {"id": "NG0472", "symbol": "⛆", "code": "ng:operator:mod_14", "fallback": "[MOD14]", "category": "operator", "name": "mod_14", "description": "Operator operation: mod_14", "unicode_point": "U+26C6", "approved_date": "2025-05-23", "validation_score": 95.0, "status": "certified", "token_cost": 1, "auto_generated": true, "generator": "simple"}, {"id": "NG0473", "symbol": "⛗", "code": "ng:memory:free_21", "fallback": "[FREE21]", "category": "memory", "name": "free_21", "description": "Memory operation: free_21", "unicode_point": "U+26D7", "approved_date": "2025-05-23", "validation_score": 95.0, "status": "certified", "token_cost": 1, "auto_generated": true, "generator": "simple"}, {"id": "NG0474", "symbol": "♔", "code": "ng:logic:xor_14", "fallback": "[XOR14]", "category": "logic", "name": "xor_14", "description": "Logic operation: xor_14", "unicode_point": "U+2654", "approved_date": "2025-05-23", "validation_score": 95.0, "status": "certified", "token_cost": 1, "auto_generated": true, "generator": "simple"}, {"id": "NG0475", "symbol": "♞", "code": "ng:logic:or_19", "fallback": "[OR19]", "category": "logic", "name": "or_19", "description": "Logic operation: or_19", "unicode_point": "U+265E", "approved_date": "2025-05-23", "validation_score": 95.0, "status": "certified", "token_cost": 1, "auto_generated": true, "generator": "simple"}, {"id": "NG0476", "symbol": "⚹", "code": "ng:structure:function_22", "fallback": "[FUNCTION22]", "category": "structure", "name": "function_22", "description": "Structure operation: function_22", "unicode_point": "U+26B9", "approved_date": "2025-05-23", "validation_score": 95.0, "status": "certified", "token_cost": 1, "auto_generated": true, "generator": "simple"}, {"id": "NG0477", "symbol": "⚠", "code": "ng:flow:else_10", "fallback": "[ELSE10]", "category": "flow", "name": "else_10", "description": "Flow operation: else_10", "unicode_point": "U+26A0", "approved_date": "2025-05-23", "validation_score": 95.0, "status": "certified", "token_cost": 1, "auto_generated": true, "generator": "simple"}, {"id": "NG0478", "symbol": "⇍", "code": "ng:flow:if_20", "fallback": "[IF20]", "category": "flow", "name": "if_20", "description": "Flow operation: if_20", "unicode_point": "U+21CD", "approved_date": "2025-05-23", "validation_score": 95.0, "status": "certified", "token_cost": 1, "auto_generated": true, "generator": "simple"}, {"id": "NG0479", "symbol": "∳", "code": "ng:operator:div_19", "fallback": "[DIV19]", "category": "operator", "name": "div_19", "description": "Operator operation: div_19", "unicode_point": "U+2233", "approved_date": "2025-05-23", "validation_score": 95.0, "status": "certified", "token_cost": 1, "auto_generated": true, "generator": "simple"}, {"id": "NG0480", "symbol": "⚟", "code": "ng:logic:or_20", "fallback": "[OR20]", "category": "logic", "name": "or_20", "description": "Logic operation: or_20", "unicode_point": "U+269F", "approved_date": "2025-05-23", "validation_score": 95.0, "status": "certified", "token_cost": 1, "auto_generated": true, "generator": "simple"}, {"id": "NG0481", "symbol": "▷", "code": "ng:flow:if_21", "fallback": "[IF21]", "category": "flow", "name": "if_21", "description": "Flow operation: if_21", "unicode_point": "U+25B7", "approved_date": "2025-05-23", "validation_score": 95.0, "status": "certified", "token_cost": 1, "auto_generated": true, "generator": "simple"}, {"id": "NG0482", "symbol": "●", "code": "ng:operator:add_15", "fallback": "[ADD15]", "category": "operator", "name": "add_15", "description": "Operator operation: add_15", "unicode_point": "U+25CF", "approved_date": "2025-05-23", "validation_score": 95.0, "status": "certified", "token_cost": 1, "auto_generated": true, "generator": "simple"}, {"id": "NG0483", "symbol": "⊐", "code": "ng:structure:method_15", "fallback": "[METHOD15]", "category": "structure", "name": "method_15", "description": "Structure operation: method_15", "unicode_point": "U+2290", "approved_date": "2025-05-23", "validation_score": 95.0, "status": "certified", "token_cost": 1, "auto_generated": true, "generator": "simple"}, {"id": "NG0484", "symbol": "⚶", "code": "ng:logic:implies_23", "fallback": "[IMPLIES23]", "category": "logic", "name": "implies_23", "description": "Logic operation: implies_23", "unicode_point": "U+26B6", "approved_date": "2025-05-23", "validation_score": 95.0, "status": "certified", "token_cost": 1, "auto_generated": true, "generator": "simple"}, {"id": "NG0485", "symbol": "⇾", "code": "ng:operator:div_20", "fallback": "[DIV20]", "category": "operator", "name": "div_20", "description": "Operator operation: div_20", "unicode_point": "U+21FE", "approved_date": "2025-05-23", "validation_score": 95.0, "status": "certified", "token_cost": 1, "auto_generated": true, "generator": "simple"}, {"id": "NG0486", "symbol": "∔", "code": "ng:operator:add_16", "fallback": "[ADD16]", "category": "operator", "name": "add_16", "description": "Operator operation: add_16", "unicode_point": "U+2214", "approved_date": "2025-05-23", "validation_score": 95.0, "status": "certified", "token_cost": 1, "auto_generated": true, "generator": "simple"}, {"id": "NG0487", "symbol": "☺", "code": "ng:flow:break_14", "fallback": "[BREAK14]", "category": "flow", "name": "break_14", "description": "Flow operation: break_14", "unicode_point": "U+263A", "approved_date": "2025-05-23", "validation_score": 95.0, "status": "certified", "token_cost": 1, "auto_generated": true, "generator": "simple"}, {"id": "NG0488", "symbol": "⇐", "code": "ng:operator:add_17", "fallback": "[ADD17]", "category": "operator", "name": "add_17", "description": "Operator operation: add_17", "unicode_point": "U+21D0", "approved_date": "2025-05-23", "validation_score": 95.0, "status": "certified", "token_cost": 1, "auto_generated": true, "generator": "simple"}, {"id": "NG0489", "symbol": "⊤", "code": "ng:structure:function_23", "fallback": "[FUNCTION23]", "category": "structure", "name": "function_23", "description": "Structure operation: function_23", "unicode_point": "U+22A4", "approved_date": "2025-05-23", "validation_score": 95.0, "status": "certified", "token_cost": 1, "auto_generated": true, "generator": "simple"}, {"id": "NG0490", "symbol": "⛿", "code": "ng:structure:class_24", "fallback": "[CLASS24]", "category": "structure", "name": "class_24", "description": "Structure operation: class_24", "unicode_point": "U+26FF", "approved_date": "2025-05-23", "validation_score": 95.0, "status": "certified", "token_cost": 1, "auto_generated": true, "generator": "simple"}, {"id": "NG0491", "symbol": "☂", "code": "ng:operator:div_21", "fallback": "[DIV21]", "category": "operator", "name": "div_21", "description": "Operator operation: div_21", "unicode_point": "U+2602", "approved_date": "2025-05-23", "validation_score": 95.0, "status": "certified", "token_cost": 1, "auto_generated": true, "generator": "simple"}, {"id": "NG0492", "symbol": "⇮", "code": "ng:operator:mod_15", "fallback": "[MOD15]", "category": "operator", "name": "mod_15", "description": "Operator operation: mod_15", "unicode_point": "U+21EE", "approved_date": "2025-05-23", "validation_score": 95.0, "status": "certified", "token_cost": 1, "auto_generated": true, "generator": "simple"}, {"id": "NG0493", "symbol": "⚩", "code": "ng:flow:if_22", "fallback": "[IF22]", "category": "flow", "name": "if_22", "description": "Flow operation: if_22", "unicode_point": "U+26A9", "approved_date": "2025-05-23", "validation_score": 95.0, "status": "certified", "token_cost": 1, "auto_generated": true, "generator": "simple"}, {"id": "NG0494", "symbol": "◷", "code": "ng:operator:add_18", "fallback": "[ADD18]", "category": "operator", "name": "add_18", "description": "Operator operation: add_18", "unicode_point": "U+25F7", "approved_date": "2025-05-23", "validation_score": 95.0, "status": "certified", "token_cost": 1, "auto_generated": true, "generator": "simple"}, {"id": "NG0495", "symbol": "⊢", "code": "ng:structure:class_25", "fallback": "[CLASS25]", "category": "structure", "name": "class_25", "description": "Structure operation: class_25", "unicode_point": "U+22A2", "approved_date": "2025-05-23", "validation_score": 95.0, "status": "certified", "token_cost": 1, "auto_generated": true, "generator": "simple"}, {"id": "NG0496", "symbol": "∃", "code": "ng:logic:xor_15", "fallback": "[XOR15]", "category": "logic", "name": "xor_15", "description": "Logic operation: xor_15", "unicode_point": "U+2203", "approved_date": "2025-05-23", "validation_score": 95.0, "status": "certified", "token_cost": 1, "auto_generated": true, "generator": "simple"}, {"id": "NG0497", "symbol": "☨", "code": "ng:logic:not_20", "fallback": "[NOT20]", "category": "logic", "name": "not_20", "description": "Logic operation: not_20", "unicode_point": "U+2628", "approved_date": "2025-05-23", "validation_score": 95.0, "status": "certified", "token_cost": 1, "auto_generated": true, "generator": "simple"}, {"id": "NG0498", "symbol": "⋢", "code": "ng:logic:not_21", "fallback": "[NOT21]", "category": "logic", "name": "not_21", "description": "Logic operation: not_21", "unicode_point": "U+22E2", "approved_date": "2025-05-23", "validation_score": 95.0, "status": "certified", "token_cost": 1, "auto_generated": true, "generator": "simple"}, {"id": "NG0499", "symbol": "☗", "code": "ng:structure:property_22", "fallback": "[PROPERTY22]", "category": "structure", "name": "property_22", "description": "Structure operation: property_22", "unicode_point": "U+2617", "approved_date": "2025-05-23", "validation_score": 95.0, "status": "certified", "token_cost": 1, "auto_generated": true, "generator": "simple"}, {"id": "NG0500", "symbol": "≭", "code": "ng:logic:implies_24", "fallback": "[IMPLIES24]", "category": "logic", "name": "implies_24", "description": "Logic operation: implies_24", "unicode_point": "U+226D", "approved_date": "2025-05-23", "validation_score": 95.0, "status": "certified", "token_cost": 1, "auto_generated": true, "generator": "simple"}, {"id": "NG0501", "symbol": "⇫", "code": "ng:flow:while_18", "fallback": "[WHILE18]", "category": "flow", "name": "while_18", "description": "Flow operation: while_18", "unicode_point": "U+21EB", "approved_date": "2025-05-23", "validation_score": 95.0, "status": "certified", "token_cost": 1, "auto_generated": true, "generator": "simple"}, {"id": "NG0502", "symbol": "⊺", "code": "ng:flow:for_20", "fallback": "[FOR20]", "category": "flow", "name": "for_20", "description": "Flow operation: for_20", "unicode_point": "U+22BA", "approved_date": "2025-05-23", "validation_score": 95.0, "status": "certified", "token_cost": 1, "auto_generated": true, "generator": "simple"}, {"id": "NG0503", "symbol": "♯", "code": "ng:memory:pointer_17", "fallback": "[POINTER17]", "category": "memory", "name": "pointer_17", "description": "Memory operation: pointer_17", "unicode_point": "U+266F", "approved_date": "2025-05-23", "validation_score": 95.0, "status": "certified", "token_cost": 1, "auto_generated": true, "generator": "simple"}, {"id": "NG0504", "symbol": "≪", "code": "ng:logic:iff_12", "fallback": "[IFF12]", "category": "logic", "name": "iff_12", "description": "Logic operation: iff_12", "unicode_point": "U+226A", "approved_date": "2025-05-23", "validation_score": 95.0, "status": "certified", "token_cost": 1, "auto_generated": true, "generator": "simple"}, {"id": "NG0505", "symbol": "⊚", "code": "ng:structure:property_23", "fallback": "[PROPERTY23]", "category": "structure", "name": "property_23", "description": "Structure operation: property_23", "unicode_point": "U+229A", "approved_date": "2025-05-23", "validation_score": 95.0, "status": "certified", "token_cost": 1, "auto_generated": true, "generator": "simple"}, {"id": "NG0506", "symbol": "♰", "code": "ng:logic:and_15", "fallback": "[AND15]", "category": "logic", "name": "and_15", "description": "Logic operation: and_15", "unicode_point": "U+2670", "approved_date": "2025-05-23", "validation_score": 95.0, "status": "certified", "token_cost": 1, "auto_generated": true, "generator": "simple"}, {"id": "NG0507", "symbol": "⚤", "code": "ng:flow:else_11", "fallback": "[ELSE11]", "category": "flow", "name": "else_11", "description": "Flow operation: else_11", "unicode_point": "U+26A4", "approved_date": "2025-05-23", "validation_score": 95.0, "status": "certified", "token_cost": 1, "auto_generated": true, "generator": "simple"}, {"id": "NG0508", "symbol": "☍", "code": "ng:logic:or_21", "fallback": "[OR21]", "category": "logic", "name": "or_21", "description": "Logic operation: or_21", "unicode_point": "U+260D", "approved_date": "2025-05-23", "validation_score": 95.0, "status": "certified", "token_cost": 1, "auto_generated": true, "generator": "simple"}, {"id": "NG0509", "symbol": "◉", "code": "ng:logic:xor_16", "fallback": "[XOR16]", "category": "logic", "name": "xor_16", "description": "Logic operation: xor_16", "unicode_point": "U+25C9", "approved_date": "2025-05-23", "validation_score": 95.0, "status": "certified", "token_cost": 1, "auto_generated": true, "generator": "simple"}, {"id": "NG0510", "symbol": "⚪", "code": "ng:flow:else_12", "fallback": "[ELSE12]", "category": "flow", "name": "else_12", "description": "Flow operation: else_12", "unicode_point": "U+26AA", "approved_date": "2025-05-23", "validation_score": 95.0, "status": "certified", "token_cost": 1, "auto_generated": true, "generator": "simple"}, {"id": "NG0511", "symbol": "♠", "code": "ng:flow:if_23", "fallback": "[IF23]", "category": "flow", "name": "if_23", "description": "Flow operation: if_23", "unicode_point": "U+2660", "approved_date": "2025-05-23", "validation_score": 95.0, "status": "certified", "token_cost": 1, "auto_generated": true, "generator": "simple"}, {"id": "NG0512", "symbol": "↼", "code": "ng:structure:function_24", "fallback": "[FUNCTION24]", "category": "structure", "name": "function_24", "description": "Structure operation: function_24", "unicode_point": "U+21BC", "approved_date": "2025-05-23", "validation_score": 95.0, "status": "certified", "token_cost": 1, "auto_generated": true, "generator": "simple"}, {"id": "NG0513", "symbol": "⎢", "code": "ng:reasoning:planning", "fallback": "[PLANNING]", "category": "reasoning", "name": "planning", "description": "Reasoning operation: planning", "unicode_point": "U+23A2", "approved_date": "2025-05-23", "validation_score": 100.0, "status": "certified", "token_cost": 3, "auto_generated": true, "generator": "reasoning_specialized"}, {"id": "NG0514", "symbol": "⏐", "code": "ng:reasoning:correlation", "fallback": "[CORRELATION]", "category": "reasoning", "name": "correlation", "description": "Reasoning operation: correlation", "unicode_point": "U+23D0", "approved_date": "2025-05-23", "validation_score": 90.0, "status": "certified", "token_cost": 3, "auto_generated": true, "generator": "reasoning_specialized"}, {"id": "NG0515", "symbol": "⎆", "code": "ng:reasoning:conclusion", "fallback": "[CONCLUSION]", "category": "reasoning", "name": "conclusion", "description": "Reasoning operation: conclusion", "unicode_point": "U+2386", "approved_date": "2025-05-23", "validation_score": 100.0, "status": "certified", "token_cost": 3, "auto_generated": true, "generator": "reasoning_specialized"}, {"id": "NG0516", "symbol": "⌔", "code": "ng:reasoning:metaphor", "fallback": "[METAPHOR]", "category": "reasoning", "name": "metaphor", "description": "Reasoning operation: metaphor", "unicode_point": "U+2314", "approved_date": "2025-05-23", "validation_score": 100.0, "status": "certified", "token_cost": 3, "auto_generated": true, "generator": "reasoning_specialized"}, {"id": "NG0517", "symbol": "⍸", "code": "ng:reasoning:premise", "fallback": "[PREMISE]", "category": "reasoning", "name": "premise", "description": "Reasoning operation: premise", "unicode_point": "U+2378", "approved_date": "2025-05-23", "validation_score": 100.0, "status": "certified", "token_cost": 3, "auto_generated": true, "generator": "reasoning_specialized"}, {"id": "NG0518", "symbol": "⧮", "code": "ng:reasoning:evaluation", "fallback": "[EVALUATION]", "category": "reasoning", "name": "evaluation", "description": "Reasoning operation: evaluation", "unicode_point": "U+29EE", "approved_date": "2025-05-23", "validation_score": 100.0, "status": "certified", "token_cost": 3, "auto_generated": true, "generator": "reasoning_specialized"}, {"id": "NG0519", "symbol": "⪜", "code": "ng:reasoning:specialization", "fallback": "[SPECIALIZATION]", "category": "reasoning", "name": "specialization", "description": "Reasoning operation: specialization", "unicode_point": "U+2A9C", "approved_date": "2025-05-23", "validation_score": 90.0, "status": "certified", "token_cost": 3, "auto_generated": true, "generator": "reasoning_specialized"}, {"id": "NG0520", "symbol": "⇡", "code": "ng:reasoning:assessment", "fallback": "[ASSESSMENT]", "category": "reasoning", "name": "assessment", "description": "Reasoning operation: assessment", "unicode_point": "U+21E1", "approved_date": "2025-05-23", "validation_score": 100.0, "status": "certified", "token_cost": 3, "auto_generated": true, "generator": "reasoning_specialized"}, {"id": "NG0521", "symbol": "⊍", "code": "ng:reasoning:problem_solving", "fallback": "[PROBLEMSOLVING]", "category": "reasoning", "name": "problem_solving", "description": "Reasoning operation: problem_solving", "unicode_point": "U+228D", "approved_date": "2025-05-23", "validation_score": 90.0, "status": "certified", "token_cost": 3, "auto_generated": true, "generator": "reasoning_specialized"}, {"id": "NG0522", "symbol": "⧬", "code": "ng:reasoning:creativity", "fallback": "[CREATIVITY]", "category": "reasoning", "name": "creativity", "description": "Reasoning operation: creativity", "unicode_point": "U+29EC", "approved_date": "2025-05-23", "validation_score": 100.0, "status": "certified", "token_cost": 3, "auto_generated": true, "generator": "reasoning_specialized"}, {"id": "NG0523", "symbol": "⍛", "code": "ng:reasoning:intuition", "fallback": "[INTUITION]", "category": "reasoning", "name": "intuition", "description": "Reasoning operation: intuition", "unicode_point": "U+235B", "approved_date": "2025-05-23", "validation_score": 100.0, "status": "certified", "token_cost": 3, "auto_generated": true, "generator": "reasoning_specialized"}, {"id": "NG0524", "symbol": "⏃", "code": "ng:reasoning:contrapositive", "fallback": "[CONTRAPOSITIVE]", "category": "reasoning", "name": "contrapositive", "description": "Reasoning operation: contrapositive", "unicode_point": "U+23C3", "approved_date": "2025-05-23", "validation_score": 90.0, "status": "certified", "token_cost": 3, "auto_generated": true, "generator": "reasoning_specialized"}, {"id": "NG0525", "symbol": "⪪", "code": "ng:reasoning:axiom", "fallback": "[AXIOM]", "category": "reasoning", "name": "axiom", "description": "Reasoning operation: axiom", "unicode_point": "U+2AAA", "approved_date": "2025-05-23", "validation_score": 100.0, "status": "certified", "token_cost": 3, "auto_generated": true, "generator": "reasoning_specialized"}, {"id": "NG0526", "symbol": "⦵", "code": "ng:reasoning:self_awareness", "fallback": "[SELFAWARENESS]", "category": "reasoning", "name": "self_awareness", "description": "Reasoning operation: self_awareness", "unicode_point": "U+29B5", "approved_date": "2025-05-23", "validation_score": 90.0, "status": "certified", "token_cost": 3, "auto_generated": true, "generator": "reasoning_specialized"}, {"id": "NG0527", "symbol": "⧄", "code": "ng:reasoning:reflection", "fallback": "[REFLECTION]", "category": "reasoning", "name": "reflection", "description": "Reasoning operation: reflection", "unicode_point": "U+29C4", "approved_date": "2025-05-23", "validation_score": 100.0, "status": "certified", "token_cost": 3, "auto_generated": true, "generator": "reasoning_specialized"}, {"id": "NG0528", "symbol": "⩓", "code": "ng:reasoning:deduction", "fallback": "[DEDUCTION]", "category": "reasoning", "name": "deduction", "description": "Reasoning operation: deduction", "unicode_point": "U+2A53", "approved_date": "2025-05-23", "validation_score": 100.0, "status": "certified", "token_cost": 3, "auto_generated": true, "generator": "reasoning_specialized"}, {"id": "NG0529", "symbol": "⎭", "code": "ng:reasoning:modus_ponens", "fallback": "[MODUSPONENS]", "category": "reasoning", "name": "modus_ponens", "description": "Reasoning operation: modus_ponens", "unicode_point": "U+23AD", "approved_date": "2025-05-23", "validation_score": 90.0, "status": "certified", "token_cost": 3, "auto_generated": true, "generator": "reasoning_specialized"}, {"id": "NG0530", "symbol": "⪫", "code": "ng:reasoning:tautology", "fallback": "[TAUTOLOGY]", "category": "reasoning", "name": "tautology", "description": "Reasoning operation: tautology", "unicode_point": "U+2AAB", "approved_date": "2025-05-23", "validation_score": 100.0, "status": "certified", "token_cost": 3, "auto_generated": true, "generator": "reasoning_specialized"}, {"id": "NG0531", "symbol": "⊲", "code": "ng:reasoning:hypothesis", "fallback": "[HYPOTHESIS]", "category": "reasoning", "name": "hypothesis", "description": "Reasoning operation: hypothesis", "unicode_point": "U+22B2", "approved_date": "2025-05-23", "validation_score": 100.0, "status": "certified", "token_cost": 3, "auto_generated": true, "generator": "reasoning_specialized"}, {"id": "NG0532", "symbol": "⩢", "code": "ng:reasoning:soundness", "fallback": "[SOUNDNESS]", "category": "reasoning", "name": "soundness", "description": "Reasoning operation: soundness", "unicode_point": "U+2A62", "approved_date": "2025-05-23", "validation_score": 100.0, "status": "certified", "token_cost": 3, "auto_generated": true, "generator": "reasoning_specialized"}, {"id": "NG0533", "symbol": "⪣", "code": "ng:reasoning:consistency", "fallback": "[CONSISTENCY]", "category": "reasoning", "name": "consistency", "description": "Reasoning operation: consistency", "unicode_point": "U+2AA3", "approved_date": "2025-05-23", "validation_score": 90.0, "status": "certified", "token_cost": 3, "auto_generated": true, "generator": "reasoning_specialized"}, {"id": "NG0534", "symbol": "⏓", "code": "ng:reasoning:decision", "fallback": "[DECISION]", "category": "reasoning", "name": "decision", "description": "Reasoning operation: decision", "unicode_point": "U+23D3", "approved_date": "2025-05-23", "validation_score": 100.0, "status": "certified", "token_cost": 3, "auto_generated": true, "generator": "reasoning_specialized"}, {"id": "NG0535", "symbol": "∟", "code": "ng:reasoning:judgment", "fallback": "[JUDGMENT]", "category": "reasoning", "name": "judgment", "description": "Reasoning operation: judgment", "unicode_point": "U+221F", "approved_date": "2025-05-23", "validation_score": 100.0, "status": "certified", "token_cost": 3, "auto_generated": true, "generator": "reasoning_specialized"}, {"id": "NG0536", "symbol": "⌾", "code": "ng:reasoning:inference", "fallback": "[INFERENCE]", "category": "reasoning", "name": "inference", "description": "Reasoning operation: inference", "unicode_point": "U+233E", "approved_date": "2025-05-23", "validation_score": 100.0, "status": "certified", "token_cost": 3, "auto_generated": true, "generator": "reasoning_specialized"}, {"id": "NG0537", "symbol": "⎥", "code": "ng:reasoning:paradox", "fallback": "[PARADOX]", "category": "reasoning", "name": "paradox", "description": "Reasoning operation: paradox", "unicode_point": "U+23A5", "approved_date": "2025-05-23", "validation_score": 100.0, "status": "certified", "token_cost": 3, "auto_generated": true, "generator": "reasoning_specialized"}, {"id": "NG0538", "symbol": "⩷", "code": "ng:reasoning:similarity", "fallback": "[SIMILARITY]", "category": "reasoning", "name": "similarity", "description": "Reasoning operation: similarity", "unicode_point": "U+2A77", "approved_date": "2025-05-23", "validation_score": 100.0, "status": "certified", "token_cost": 3, "auto_generated": true, "generator": "reasoning_specialized"}, {"id": "NG0539", "symbol": "▪", "code": "ng:reasoning:contradiction", "fallback": "[CONTRADICTION]", "category": "reasoning", "name": "contradiction", "description": "Reasoning operation: contradiction", "unicode_point": "U+25AA", "approved_date": "2025-05-23", "validation_score": 90.0, "status": "certified", "token_cost": 3, "auto_generated": true, "generator": "reasoning_specialized"}, {"id": "NG0540", "symbol": "⍝", "code": "ng:reasoning:proof", "fallback": "[PROOF]", "category": "reasoning", "name": "proof", "description": "Reasoning operation: proof", "unicode_point": "U+235D", "approved_date": "2025-05-23", "validation_score": 100.0, "status": "certified", "token_cost": 3, "auto_generated": true, "generator": "reasoning_specialized"}, {"id": "NG0541", "symbol": "⪀", "code": "ng:reasoning:lemma", "fallback": "[LEMMA]", "category": "reasoning", "name": "lemma", "description": "Reasoning operation: lemma", "unicode_point": "U+2A80", "approved_date": "2025-05-23", "validation_score": 100.0, "status": "certified", "token_cost": 3, "auto_generated": true, "generator": "reasoning_specialized"}, {"id": "NG0542", "symbol": "⪲", "code": "ng:reasoning:causality", "fallback": "[CAUSALITY]", "category": "reasoning", "name": "causality", "description": "Reasoning operation: causality", "unicode_point": "U+2AB2", "approved_date": "2025-05-23", "validation_score": 100.0, "status": "certified", "token_cost": 3, "auto_generated": true, "generator": "reasoning_specialized"}, {"id": "NG0543", "symbol": "⏟", "code": "ng:reasoning:monitoring", "fallback": "[MONITORING]", "category": "reasoning", "name": "monitoring", "description": "Reasoning operation: monitoring", "unicode_point": "U+23DF", "approved_date": "2025-05-23", "validation_score": 100.0, "status": "certified", "token_cost": 3, "auto_generated": true, "generator": "reasoning_specialized"}, {"id": "NG0544", "symbol": "⎓", "code": "ng:reasoning:syllogism", "fallback": "[SYLLOGISM]", "category": "reasoning", "name": "syllogism", "description": "Reasoning operation: syllogism", "unicode_point": "U+2393", "approved_date": "2025-05-23", "validation_score": 100.0, "status": "certified", "token_cost": 3, "auto_generated": true, "generator": "reasoning_specialized"}, {"id": "NG0545", "symbol": "≘", "code": "ng:reasoning:heuristic", "fallback": "[HEURISTIC]", "category": "reasoning", "name": "heuristic", "description": "Reasoning operation: heuristic", "unicode_point": "U+2258", "approved_date": "2025-05-23", "validation_score": 100.0, "status": "certified", "token_cost": 3, "auto_generated": true, "generator": "reasoning_specialized"}, {"id": "NG0546", "symbol": "⧗", "code": "ng:reasoning:synthesis", "fallback": "[SYNTHESIS]", "category": "reasoning", "name": "synthesis", "description": "Reasoning operation: synthesis", "unicode_point": "U+29D7", "approved_date": "2025-05-23", "validation_score": 100.0, "status": "certified", "token_cost": 3, "auto_generated": true, "generator": "reasoning_specialized"}, {"id": "NG0547", "symbol": "⧓", "code": "ng:reasoning:pattern", "fallback": "[PATTERN]", "category": "reasoning", "name": "pattern", "description": "Reasoning operation: pattern", "unicode_point": "U+29D3", "approved_date": "2025-05-23", "validation_score": 100.0, "status": "certified", "token_cost": 3, "auto_generated": true, "generator": "reasoning_specialized"}, {"id": "NG0548", "symbol": "⎑", "code": "ng:reasoning:metacognition", "fallback": "[METACOGNITION]", "category": "reasoning", "name": "metacognition", "description": "Reasoning operation: metacognition", "unicode_point": "U+2391", "approved_date": "2025-05-23", "validation_score": 90.0, "status": "certified", "token_cost": 3, "auto_generated": true, "generator": "reasoning_specialized"}, {"id": "NG0549", "symbol": "⏰", "code": "ng:reasoning:completeness", "fallback": "[COMPLETENESS]", "category": "reasoning", "name": "completeness", "description": "Reasoning operation: completeness", "unicode_point": "U+23F0", "approved_date": "2025-05-23", "validation_score": 90.0, "status": "certified", "token_cost": 3, "auto_generated": true, "generator": "reasoning_specialized"}, {"id": "NG0550", "symbol": "⧯", "code": "ng:reasoning:fallacy", "fallback": "[FALLACY]", "category": "reasoning", "name": "fallacy", "description": "Reasoning operation: fallacy", "unicode_point": "U+29EF", "approved_date": "2025-05-23", "validation_score": 100.0, "status": "certified", "token_cost": 3, "auto_generated": true, "generator": "reasoning_specialized"}, {"id": "NG0551", "symbol": "⎚", "code": "ng:reasoning:induction", "fallback": "[INDUCTION]", "category": "reasoning", "name": "induction", "description": "Reasoning operation: induction", "unicode_point": "U+239A", "approved_date": "2025-05-23", "validation_score": 100.0, "status": "certified", "token_cost": 3, "auto_generated": true, "generator": "reasoning_specialized"}, {"id": "NG0552", "symbol": "⧱", "code": "ng:reasoning:strategy", "fallback": "[STRATEGY]", "category": "reasoning", "name": "strategy", "description": "Reasoning operation: strategy", "unicode_point": "U+29F1", "approved_date": "2025-05-23", "validation_score": 100.0, "status": "certified", "token_cost": 3, "auto_generated": true, "generator": "reasoning_specialized"}, {"id": "NG0553", "symbol": "≊", "code": "ng:reasoning:generalization", "fallback": "[GENERALIZATION]", "category": "reasoning", "name": "generalization", "description": "Reasoning operation: generalization", "unicode_point": "U+224A", "approved_date": "2025-05-23", "validation_score": 90.0, "status": "certified", "token_cost": 3, "auto_generated": true, "generator": "reasoning_specialized"}, {"id": "NG0554", "symbol": "⦸", "code": "ng:reasoning:analogy", "fallback": "[ANALOGY]", "category": "reasoning", "name": "analogy", "description": "Reasoning operation: analogy", "unicode_point": "U+29B8", "approved_date": "2025-05-23", "validation_score": 100.0, "status": "certified", "token_cost": 3, "auto_generated": true, "generator": "reasoning_specialized"}, {"id": "NG0555", "symbol": "⎁", "code": "ng:reasoning:error_correction", "fallback": "[ERRORCORRECTION]", "category": "reasoning", "name": "error_correction", "description": "Reasoning operation: error_correction", "unicode_point": "U+2381", "approved_date": "2025-05-23", "validation_score": 90.0, "status": "certified", "token_cost": 3, "auto_generated": true, "generator": "reasoning_specialized"}, {"id": "NG0556", "symbol": "⎯", "code": "ng:reasoning:abduction", "fallback": "[ABDUCTION]", "category": "reasoning", "name": "abduction", "description": "Reasoning operation: abduction", "unicode_point": "U+23AF", "approved_date": "2025-05-23", "validation_score": 100.0, "status": "certified", "token_cost": 3, "auto_generated": true, "generator": "reasoning_specialized"}, {"id": "NG0557", "symbol": "≵", "code": "ng:reasoning:classification", "fallback": "[CLASSIFICATION]", "category": "reasoning", "name": "classification", "description": "Reasoning operation: classification", "unicode_point": "U+2275", "approved_date": "2025-05-23", "validation_score": 90.0, "status": "certified", "token_cost": 3, "auto_generated": true, "generator": "reasoning_specialized"}, {"id": "NG0558", "symbol": "⎧", "code": "ng:reasoning:modus_tollens", "fallback": "[MODUSTOLLENS]", "category": "reasoning", "name": "modus_tollens", "description": "Reasoning operation: modus_tollens", "unicode_point": "U+23A7", "approved_date": "2025-05-23", "validation_score": 90.0, "status": "certified", "token_cost": 3, "auto_generated": true, "generator": "reasoning_specialized"}, {"id": "NG0559", "symbol": "⎬", "code": "ng:reasoning:validity", "fallback": "[VALIDITY]", "category": "reasoning", "name": "validity", "description": "Reasoning operation: validity", "unicode_point": "U+23AC", "approved_date": "2025-05-23", "validation_score": 100.0, "status": "certified", "token_cost": 3, "auto_generated": true, "generator": "reasoning_specialized"}, {"id": "NG0560", "symbol": "⌏", "code": "ng:reasoning:theorem", "fallback": "[THEOREM]", "category": "reasoning", "name": "theorem", "description": "Reasoning operation: theorem", "unicode_point": "U+230F", "approved_date": "2025-05-23", "validation_score": 100.0, "status": "certified", "token_cost": 3, "auto_generated": true, "generator": "reasoning_specialized"}, {"id": "NG0561", "symbol": "⦗", "code": "ng:reasoning:abstraction", "fallback": "[ABSTRACTION]", "category": "reasoning", "name": "abstraction", "description": "Reasoning operation: abstraction", "unicode_point": "U+2997", "approved_date": "2025-05-23", "validation_score": 90.0, "status": "certified", "token_cost": 3, "auto_generated": true, "generator": "reasoning_specialized"}, {"id": "NG0562", "symbol": "∏", "code": "ng:reasoning:control", "fallback": "[CONTROL]", "category": "reasoning", "name": "control", "description": "Reasoning operation: control", "unicode_point": "U+220F", "approved_date": "2025-05-23", "validation_score": 100.0, "status": "certified", "token_cost": 3, "auto_generated": true, "generator": "reasoning_specialized"}, {"id": "NG0563", "symbol": "⫳", "code": "ng:reasoning:difference", "fallback": "[DIFFERENCE]", "category": "reasoning", "name": "difference", "description": "Reasoning operation: difference", "unicode_point": "U+2AF3", "approved_date": "2025-05-23", "validation_score": 100.0, "status": "certified", "token_cost": 3, "auto_generated": true, "generator": "reasoning_specialized"}, {"id": "NG0564", "symbol": "⧔", "code": "ng:reasoning:insight", "fallback": "[INSIGHT]", "category": "reasoning", "name": "insight", "description": "Reasoning operation: insight", "unicode_point": "U+29D4", "approved_date": "2025-05-23", "validation_score": 100.0, "status": "certified", "token_cost": 3, "auto_generated": true, "generator": "reasoning_specialized"}, {"id": "NG0565", "symbol": "⊮", "code": "ng:reasoning:decidability", "fallback": "[DECIDABILITY]", "category": "reasoning", "name": "decidability", "description": "Reasoning operation: decidability", "unicode_point": "U+22AE", "approved_date": "2025-05-23", "validation_score": 90.0, "status": "certified", "token_cost": 3, "auto_generated": true, "generator": "reasoning_specialized"}, {"id": "NG0566", "symbol": "⫩", "code": "ng:reasoning:analysis", "fallback": "[ANALYSIS]", "category": "reasoning", "name": "analysis", "description": "Reasoning operation: analysis", "unicode_point": "U+2AE9", "approved_date": "2025-05-23", "validation_score": 100.0, "status": "certified", "token_cost": 3, "auto_generated": true, "generator": "reasoning_specialized"}, {"id": "NG0567", "symbol": "⦿", "code": "ng:reasoning:bias", "fallback": "[BIAS]", "category": "reasoning", "name": "bias", "description": "Reasoning operation: bias", "unicode_point": "U+29BF", "approved_date": "2025-05-23", "validation_score": 100.0, "status": "certified", "token_cost": 3, "auto_generated": true, "generator": "reasoning_specialized"}], "rejected_symbols": [], "pending_symbols": [], "validation_criteria": {"usu_required": ["unicode_unique", "code_unique", "visually_distinct"], "ctu_required": ["format_valid", "category_valid", "function_valid"], "lcl_required": ["utf8_compatible", "fallback_format_valid"], "minimum_score": 90.0}, "categories": {"operator": {"count": 99, "description": "Mathematical and logical operators"}, "logic": {"count": 119, "description": "Logical constructs and relationships"}, "reasoning": {"count": 56, "description": "Reasoning and inference operations"}, "structure": {"count": 91, "description": "Code structure definitions"}, "flow": {"count": 108, "description": "Control flow constructs"}, "memory": {"count": 94, "description": "memory operations"}}, "next_id": "NG0568", "reserved_ranges": {"NG0001-NG0100": "Core symbols", "NG0101-NG0200": "Extended operators", "NG0201-NG0300": "Advanced logic", "NG0301-NG0400": "Memory operations", "NG0401-NG0500": "Reasoning constructs", "NG0501-NG0512": "Meta operations"}}