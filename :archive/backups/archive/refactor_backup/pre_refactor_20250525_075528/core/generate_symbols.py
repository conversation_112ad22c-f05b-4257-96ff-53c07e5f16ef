"""
Script per generare simboli univoci NEUROGLYPH con metadati semantici.
"""
import json
import random
from pathlib import Path

# Lista simboli unicode candidati (solo esempio, va arricchita)
UNICODE_CANDIDATES = [
    "⊕", "⊖", "⊗", "⊘", "≡", "≢", "∧", "∨", "¬", "∈", "∉", "⊨", "⇌",
    "⟨⟩", "⟪⟫", "◊", "◈", "⟲", "⟳", "⤴", "⊰", "⊱", "⊲", "⊳", "⊴", "⊸"
]

FALLBACKS = {
    "⊕": "[+]", "⊖": "[-]", "⊗": "[*]", "⊘": "[/]", "≡": "[eq]", "≢": "[ne]",
    "∧": "[and]", "∨": "[or]", "¬": "[not]", "∈": "[in]", "∉": "[!in]",
    "⊨": "[models]", "⇌": "[<=>]", "⟨⟩": "[fn]", "⟪⟫": "[cls]",
    "◊": "[if]", "◈": "[else]", "⟲": "[for]", "⟳": "[while]",
    "⤴": "[return]", "⊰": "[async]", "⊱": "[await]", "⊲": "[gen]",
    "⊳": "[@]", "⊴": "[ctx]", "⊸": "[static]"
}

SEMANTIC_MAP = {
    "⊕": ("operator", "add"),
    "⊖": ("operator", "sub"),
    "⊗": ("operator", "mul"),
    "⊘": ("operator", "div"),
    "≡": ("operator", "eq"),
    "≢": ("operator", "ne"),
    "∧": ("operator", "and"),
    "∨": ("operator", "or"),
    "¬": ("operator", "not"),
    "∈": ("operator", "in"),
    "∉": ("operator", "not_in"),
    "⊨": ("logic", "entailment"),
    "⇌": ("logic", "equilibrium"),
    "⟨⟩": ("control", "function"),
    "⟪⟫": ("control", "class"),
    "◊": ("control", "if"),
    "◈": ("control", "else"),
    "⟲": ("control", "for"),
    "⟳": ("control", "while"),
    "⤴": ("control", "return"),
    "⊰": ("construct", "async"),
    "⊱": ("construct", "await"),
    "⊲": ("construct", "generator"),
    "⊳": ("construct", "decorator"),
    "⊴": ("construct", "context"),
    "⊸": ("construct", "static_method")
}

symbols = []

for sym in UNICODE_CANDIDATES:
    category, meaning = SEMANTIC_MAP.get(sym, ("unknown", "undefined"))
    symbol_entry = {
        "symbol": sym,
        "code": f"ng:{category}:{meaning}",
        "fallback": FALLBACKS.get(sym, f"[{meaning}]"),
        "category": category,
        "meaning": meaning,
        "llm_support": ["openai", "qwen", "deepseek", "llama.cpp"],
        "token_cost": 1,
        "token_density": 1.0,
        "examples": []
    }
    symbols.append(symbol_entry)

out_path = Path("core/symbols_ultra.json")
with open(out_path, "w", encoding="utf-8") as f:
    json.dump({"symbols": symbols}, f, indent=2, ensure_ascii=False)

print(f"✅ Generati {len(symbols)} simboli unici in {out_path}")
