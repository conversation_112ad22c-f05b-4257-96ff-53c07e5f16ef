"""
Validatore di simboli NEUROGLYPH per rilevare duplicati semantici e Unicode.
"""
import json
from pathlib import Path
from collections import Counter

symbols_file = Path("core/symbols_ultra.json")

with open(symbols_file, "r", encoding="utf-8") as f:
    data = json.load(f)

symbols = data["symbols"]

unicode_counts = Counter(s["symbol"] for s in symbols)
code_counts = Counter(s["code"] for s in symbols)

duplicates = {
    "unicode": [s for s, c in unicode_counts.items() if c > 1],
    "code": [c for c, c_ in code_counts.items() if c_ > 1]
}

print("🔍 Duplicate Unicode:", duplicates["unicode"])
print("🔍 Duplicate Codes:", duplicates["code"])
print("✅ Validazione completata")
