#!/usr/bin/env python3
"""
NEUROGLYPH LLM - DAG Memory Persistente
======================================

Memoria DAG per auto-correzione e apprendimento incrementale.
Ogni errore → apprendimento simbolico → patch dinamico del modello.

Questo è il componente "God Mode" per zero rigenerazioni errate.
"""

import json
import hashlib
import sqlite3
from typing import Dict, List, Tuple, Optional, Any, Set
from dataclasses import dataclass, asdict
from datetime import datetime
from pathlib import Path
import networkx as nx

@dataclass
class MemoryNode:
    """Nodo della memoria DAG"""
    id: str
    type: str  # 'input', 'transformation', 'output', 'error', 'correction'
    content: str
    metadata: Dict[str, Any]
    timestamp: str
    confidence: float
    
@dataclass
class MemoryEdge:
    """Arco della memoria DAG"""
    source: str
    target: str
    relation: str  # 'transforms_to', 'corrects', 'validates', 'conflicts'
    weight: float
    metadata: Dict[str, Any]

class DAGMemory:
    """
    Memoria DAG persistente per NEUROGLYPH LLM
    
    Mantiene un grafo diretto aciclico di:
    - Input → Trasformazioni → Output
    - Errori → Correzioni → Validazioni
    - Pattern → Ottimizzazioni → Miglioramenti
    """
    
    def __init__(self, db_path: str = "neuroglyph_memory.db"):
        self.db_path = db_path
        self.graph = nx.DiGraph()
        self.error_patterns = {}
        self.correction_cache = {}
        
        self._init_database()
        self._load_memory()
    
    def _init_database(self):
        """Inizializza database SQLite per persistenza"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        # Tabella nodi
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS memory_nodes (
                id TEXT PRIMARY KEY,
                type TEXT NOT NULL,
                content TEXT NOT NULL,
                metadata TEXT,
                timestamp TEXT NOT NULL,
                confidence REAL NOT NULL
            )
        ''')
        
        # Tabella archi
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS memory_edges (
                source TEXT NOT NULL,
                target TEXT NOT NULL,
                relation TEXT NOT NULL,
                weight REAL NOT NULL,
                metadata TEXT,
                PRIMARY KEY (source, target, relation)
            )
        ''')
        
        # Tabella pattern errori
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS error_patterns (
                pattern_hash TEXT PRIMARY KEY,
                pattern_data TEXT NOT NULL,
                frequency INTEGER DEFAULT 1,
                last_seen TEXT NOT NULL
            )
        ''')
        
        conn.commit()
        conn.close()
    
    def _load_memory(self):
        """Carica memoria dal database"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        # Carica nodi
        cursor.execute('SELECT * FROM memory_nodes')
        for row in cursor.fetchall():
            node_id, node_type, content, metadata_str, timestamp, confidence = row
            metadata = json.loads(metadata_str) if metadata_str else {}
            
            node = MemoryNode(
                id=node_id,
                type=node_type,
                content=content,
                metadata=metadata,
                timestamp=timestamp,
                confidence=confidence
            )
            
            self.graph.add_node(node_id, **asdict(node))
        
        # Carica archi
        cursor.execute('SELECT * FROM memory_edges')
        for row in cursor.fetchall():
            source, target, relation, weight, metadata_str = row
            metadata = json.loads(metadata_str) if metadata_str else {}
            
            self.graph.add_edge(source, target, 
                              relation=relation, 
                              weight=weight, 
                              metadata=metadata)
        
        # Carica pattern errori
        cursor.execute('SELECT * FROM error_patterns')
        for row in cursor.fetchall():
            pattern_hash, pattern_data, frequency, last_seen = row
            self.error_patterns[pattern_hash] = {
                'data': json.loads(pattern_data),
                'frequency': frequency,
                'last_seen': last_seen
            }
        
        conn.close()
        print(f"✅ Memoria caricata: {len(self.graph.nodes)} nodi, {len(self.graph.edges)} archi")
    
    def add_transformation(self, 
                         input_text: str, 
                         output_text: str, 
                         transformation_type: str,
                         confidence: float = 1.0,
                         metadata: Optional[Dict] = None) -> str:
        """
        Aggiunge una trasformazione alla memoria
        
        Args:
            input_text: Testo input
            output_text: Testo output
            transformation_type: Tipo trasformazione (e.g., 'neuroglyph_to_code')
            confidence: Confidenza nella trasformazione
            metadata: Metadati aggiuntivi
            
        Returns:
            ID del nodo trasformazione
        """
        timestamp = datetime.now().isoformat()
        metadata = metadata or {}
        
        # Crea hash per input e output
        input_hash = self._hash_content(input_text)
        output_hash = self._hash_content(output_text)
        transform_hash = self._hash_content(f"{input_text}→{output_text}→{transformation_type}")
        
        # Crea nodi
        input_node = MemoryNode(
            id=f"input_{input_hash}",
            type="input",
            content=input_text,
            metadata=metadata,
            timestamp=timestamp,
            confidence=1.0
        )
        
        output_node = MemoryNode(
            id=f"output_{output_hash}",
            type="output", 
            content=output_text,
            metadata=metadata,
            timestamp=timestamp,
            confidence=confidence
        )
        
        transform_node = MemoryNode(
            id=f"transform_{transform_hash}",
            type="transformation",
            content=transformation_type,
            metadata={**metadata, 'input_hash': input_hash, 'output_hash': output_hash},
            timestamp=timestamp,
            confidence=confidence
        )
        
        # Aggiungi al grafo
        for node in [input_node, output_node, transform_node]:
            self.graph.add_node(node.id, **asdict(node))
        
        # Aggiungi archi
        self.graph.add_edge(input_node.id, transform_node.id, 
                          relation="transforms_to", weight=confidence)
        self.graph.add_edge(transform_node.id, output_node.id,
                          relation="produces", weight=confidence)
        
        # Salva nel database
        self._save_nodes([input_node, output_node, transform_node])
        self._save_edges([
            (input_node.id, transform_node.id, "transforms_to", confidence, {}),
            (transform_node.id, output_node.id, "produces", confidence, {})
        ])
        
        return transform_node.id
    
    def record_error(self, 
                    input_text: str, 
                    expected_output: str, 
                    actual_output: str,
                    error_type: str,
                    metadata: Optional[Dict] = None) -> str:
        """
        Registra un errore nella memoria
        
        Args:
            input_text: Input che ha causato l'errore
            expected_output: Output atteso
            actual_output: Output effettivo (errato)
            error_type: Tipo di errore
            metadata: Metadati aggiuntivi
            
        Returns:
            ID del nodo errore
        """
        timestamp = datetime.now().isoformat()
        metadata = metadata or {}
        
        # Crea pattern errore
        error_pattern = {
            'input': input_text,
            'expected': expected_output,
            'actual': actual_output,
            'type': error_type,
            'metadata': metadata
        }
        
        pattern_hash = self._hash_content(json.dumps(error_pattern, sort_keys=True))
        
        # Aggiorna frequenza pattern
        if pattern_hash in self.error_patterns:
            self.error_patterns[pattern_hash]['frequency'] += 1
            self.error_patterns[pattern_hash]['last_seen'] = timestamp
        else:
            self.error_patterns[pattern_hash] = {
                'data': error_pattern,
                'frequency': 1,
                'last_seen': timestamp
            }
        
        # Crea nodo errore
        error_hash = self._hash_content(f"error_{input_text}_{actual_output}_{timestamp}")
        error_node = MemoryNode(
            id=f"error_{error_hash}",
            type="error",
            content=json.dumps(error_pattern),
            metadata={**metadata, 'pattern_hash': pattern_hash},
            timestamp=timestamp,
            confidence=0.0  # Errore = confidenza zero
        )
        
        self.graph.add_node(error_node.id, **asdict(error_node))
        
        # Salva nel database
        self._save_nodes([error_node])
        self._save_error_pattern(pattern_hash, error_pattern, timestamp)
        
        return error_node.id
    
    def add_correction(self, 
                      error_node_id: str, 
                      corrected_output: str,
                      correction_method: str,
                      confidence: float = 0.9) -> str:
        """
        Aggiunge una correzione per un errore
        
        Args:
            error_node_id: ID del nodo errore
            corrected_output: Output corretto
            correction_method: Metodo di correzione
            confidence: Confidenza nella correzione
            
        Returns:
            ID del nodo correzione
        """
        timestamp = datetime.now().isoformat()
        
        correction_hash = self._hash_content(f"correction_{error_node_id}_{corrected_output}")
        correction_node = MemoryNode(
            id=f"correction_{correction_hash}",
            type="correction",
            content=corrected_output,
            metadata={
                'method': correction_method,
                'error_node': error_node_id
            },
            timestamp=timestamp,
            confidence=confidence
        )
        
        self.graph.add_node(correction_node.id, **asdict(correction_node))
        self.graph.add_edge(error_node_id, correction_node.id,
                          relation="corrects", weight=confidence)
        
        # Cache correzione per lookup veloce
        if error_node_id not in self.correction_cache:
            self.correction_cache[error_node_id] = []
        self.correction_cache[error_node_id].append(correction_node.id)
        
        # Salva nel database
        self._save_nodes([correction_node])
        self._save_edges([(error_node_id, correction_node.id, "corrects", confidence, {})])
        
        return correction_node.id
    
    def find_similar_patterns(self, input_text: str, threshold: float = 0.8) -> List[Dict]:
        """
        Trova pattern simili nella memoria per prevenire errori
        
        Args:
            input_text: Testo input da analizzare
            threshold: Soglia di similarità
            
        Returns:
            Lista di pattern simili con metadati
        """
        similar_patterns = []
        
        for pattern_hash, pattern_info in self.error_patterns.items():
            pattern_input = pattern_info['data']['input']
            similarity = self._compute_similarity(input_text, pattern_input)
            
            if similarity >= threshold:
                similar_patterns.append({
                    'pattern_hash': pattern_hash,
                    'similarity': similarity,
                    'frequency': pattern_info['frequency'],
                    'data': pattern_info['data'],
                    'last_seen': pattern_info['last_seen']
                })
        
        # Ordina per similarità e frequenza
        similar_patterns.sort(key=lambda x: (x['similarity'], x['frequency']), reverse=True)
        
        return similar_patterns
    
    def get_correction_suggestions(self, input_text: str) -> List[Dict]:
        """
        Ottieni suggerimenti di correzione basati sulla memoria
        
        Args:
            input_text: Testo input
            
        Returns:
            Lista di suggerimenti di correzione
        """
        suggestions = []
        
        # Trova pattern simili
        similar_patterns = self.find_similar_patterns(input_text, threshold=0.7)
        
        for pattern in similar_patterns:
            pattern_data = pattern['data']
            
            # Cerca correzioni per questo pattern
            error_nodes = [n for n, d in self.graph.nodes(data=True) 
                          if d.get('type') == 'error' and 
                          d.get('metadata', {}).get('pattern_hash') == pattern['pattern_hash']]
            
            for error_node in error_nodes:
                # Trova correzioni per questo errore
                corrections = [n for n in self.graph.successors(error_node)
                             if self.graph.nodes[n].get('type') == 'correction']
                
                for correction_node in corrections:
                    correction_data = self.graph.nodes[correction_node]
                    suggestions.append({
                        'input': pattern_data['input'],
                        'expected': pattern_data['expected'],
                        'correction': correction_data['content'],
                        'method': correction_data['metadata'].get('method', ''),
                        'confidence': correction_data['confidence'],
                        'similarity': pattern['similarity']
                    })
        
        return suggestions
    
    def _hash_content(self, content: str) -> str:
        """Crea hash del contenuto"""
        return hashlib.sha256(content.encode('utf-8')).hexdigest()[:16]
    
    def _compute_similarity(self, text1: str, text2: str) -> float:
        """Calcola similarità tra due testi"""
        if text1 == text2:
            return 1.0
        
        # Similarità basata su caratteri comuni (implementazione semplificata)
        if len(text1) == 0 and len(text2) == 0:
            return 1.0
        if len(text1) == 0 or len(text2) == 0:
            return 0.0
        
        common_chars = sum(1 for c1, c2 in zip(text1, text2) if c1 == c2)
        max_len = max(len(text1), len(text2))
        
        return common_chars / max_len
    
    def _save_nodes(self, nodes: List[MemoryNode]):
        """Salva nodi nel database"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        for node in nodes:
            cursor.execute('''
                INSERT OR REPLACE INTO memory_nodes 
                (id, type, content, metadata, timestamp, confidence)
                VALUES (?, ?, ?, ?, ?, ?)
            ''', (
                node.id, node.type, node.content,
                json.dumps(node.metadata), node.timestamp, node.confidence
            ))
        
        conn.commit()
        conn.close()
    
    def _save_edges(self, edges: List[Tuple]):
        """Salva archi nel database"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        for source, target, relation, weight, metadata in edges:
            cursor.execute('''
                INSERT OR REPLACE INTO memory_edges
                (source, target, relation, weight, metadata)
                VALUES (?, ?, ?, ?, ?)
            ''', (source, target, relation, weight, json.dumps(metadata)))
        
        conn.commit()
        conn.close()
    
    def _save_error_pattern(self, pattern_hash: str, pattern_data: Dict, timestamp: str):
        """Salva pattern errore nel database"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        cursor.execute('''
            INSERT OR REPLACE INTO error_patterns
            (pattern_hash, pattern_data, frequency, last_seen)
            VALUES (?, ?, ?, ?)
        ''', (
            pattern_hash, 
            json.dumps(pattern_data),
            self.error_patterns[pattern_hash]['frequency'],
            timestamp
        ))
        
        conn.commit()
        conn.close()

def demo_dag_memory():
    """Demo della memoria DAG"""
    print("🧠 NEUROGLYPH LLM - DAG Memory Demo")
    print("=" * 60)
    
    memory = DAGMemory("demo_memory.db")
    
    # Test 1: Aggiungi trasformazione corretta
    print("🧪 Test 1: Trasformazione corretta")
    transform_id = memory.add_transformation(
        input_text="⟨⟩α⊕β⤴α⊕β",
        output_text="def add(a, b): return a + b",
        transformation_type="neuroglyph_to_code",
        confidence=0.95
    )
    print(f"   Trasformazione aggiunta: {transform_id}")
    
    # Test 2: Registra errore
    print("\n🧪 Test 2: Registrazione errore")
    error_id = memory.record_error(
        input_text="⟨⟩α⊕β⤴α⊕β",
        expected_output="def add(a, b): return a + b",
        actual_output="def add(a, b): return a * b",  # Errore: * invece di +
        error_type="operator_confusion"
    )
    print(f"   Errore registrato: {error_id}")
    
    # Test 3: Aggiungi correzione
    print("\n🧪 Test 3: Correzione errore")
    correction_id = memory.add_correction(
        error_node_id=error_id,
        corrected_output="def add(a, b): return a + b",
        correction_method="symbol_validation",
        confidence=0.98
    )
    print(f"   Correzione aggiunta: {correction_id}")
    
    # Test 4: Cerca pattern simili
    print("\n🧪 Test 4: Pattern simili")
    similar = memory.find_similar_patterns("⟨⟩α⊕β⤴α⊕β")
    print(f"   Pattern simili trovati: {len(similar)}")
    for pattern in similar:
        print(f"      Similarità: {pattern['similarity']:.2f}, Frequenza: {pattern['frequency']}")
    
    # Test 5: Suggerimenti correzione
    print("\n🧪 Test 5: Suggerimenti correzione")
    suggestions = memory.get_correction_suggestions("⟨⟩α⊕β⤴α⊕β")
    print(f"   Suggerimenti trovati: {len(suggestions)}")
    for suggestion in suggestions:
        print(f"      Correzione: {suggestion['correction'][:50]}...")
        print(f"      Confidenza: {suggestion['confidence']:.2f}")
    
    print(f"\n📊 Statistiche memoria:")
    print(f"   Nodi: {len(memory.graph.nodes)}")
    print(f"   Archi: {len(memory.graph.edges)}")
    print(f"   Pattern errori: {len(memory.error_patterns)}")
    
    print("\n✅ Demo DAG Memory completata!")

if __name__ == "__main__":
    demo_dag_memory()
