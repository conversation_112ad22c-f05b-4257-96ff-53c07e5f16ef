"""
NEUROGLYPH ULTRA Symbol Loader and Validator
============================================

Validates and loads the ULTRA symbol format for the first thinking symbolic LLM.
Ensures every symbol meets the strict criteria for symbolic reasoning and GOD memory integration.
"""

import json
import logging
from typing import Dict, List, Any, Tuple, Optional
from pathlib import Path
import unicodedata
import re

# Configurazione logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class SymbolValidationError(Exception):
    """Eccezione per errori di validazione simboli."""
    pass


class UltraSymbolLoader:
    """
    Caricatore e validatore per simboli NEUROGLYPH ULTRA.
    
    Caratteristiche:
    - Validazione formato ULTRA (id, symbol, name, description, category, aliases, status, version)
    - Verifica unicità simboli e ID
    - Controllo categorie valide
    - Validazione Unicode per simboli
    - Integrazione con SOCRATE e GOD
    """
    
    VALID_CATEGORIES = {
        "action", "structure", "state", "logic", "entity", "domain", 
        "flow", "data", "meta", "system", "memory", "reasoning"
    }
    
    VALID_STATUSES = {"approved", "candidate", "deprecated", "experimental", "macro"}
    
    def __init__(self):
        """Inizializza il loader ULTRA."""
        self.symbols = []
        self.symbol_map = {}  # symbol -> entry
        self.id_map = {}      # id -> entry
        self.name_map = {}    # name -> entry
        self.validation_stats = {
            "total_symbols": 0,
            "valid_symbols": 0,
            "invalid_symbols": 0,
            "categories": {},
            "statuses": {},
            "unicode_issues": 0,
            "duplicate_issues": 0
        }
    
    def load_and_validate(self, symbols_path: str = "core/symbols.json") -> Dict[str, Any]:
        """
        Carica e valida i simboli ULTRA.
        
        Args:
            symbols_path: Percorso al file simboli
            
        Returns:
            Dizionario con simboli validati e statistiche
            
        Raises:
            SymbolValidationError: Se la validazione fallisce
        """
        logger.info(f"🧠 Caricamento simboli ULTRA da: {symbols_path}")
        
        # Carica file JSON
        try:
            with open(symbols_path, 'r', encoding='utf-8') as f:
                raw_symbols = json.load(f)
        except FileNotFoundError:
            raise SymbolValidationError(f"File simboli non trovato: {symbols_path}")
        except json.JSONDecodeError as e:
            raise SymbolValidationError(f"Errore parsing JSON: {e}")
        
        if not isinstance(raw_symbols, list):
            raise SymbolValidationError("Il file simboli deve contenere un array JSON")
        
        self.validation_stats["total_symbols"] = len(raw_symbols)
        logger.info(f"📊 Trovati {len(raw_symbols)} simboli da validare")
        
        # Valida ogni simbolo
        for i, symbol_entry in enumerate(raw_symbols):
            try:
                self._validate_symbol_entry(symbol_entry, i)
                self.symbols.append(symbol_entry)
                self.validation_stats["valid_symbols"] += 1
                
                # Aggiorna mappe
                self.symbol_map[symbol_entry["symbol"]] = symbol_entry
                self.id_map[symbol_entry["id"]] = symbol_entry
                self.name_map[symbol_entry["name"]] = symbol_entry
                
                # Aggiorna statistiche
                category = symbol_entry["category"]
                status = symbol_entry["status"]
                self.validation_stats["categories"][category] = self.validation_stats["categories"].get(category, 0) + 1
                self.validation_stats["statuses"][status] = self.validation_stats["statuses"].get(status, 0) + 1
                
            except SymbolValidationError as e:
                logger.warning(f"⚠️ Simbolo {i} non valido: {e}")
                self.validation_stats["invalid_symbols"] += 1
        
        # Verifica risultati finali
        if self.validation_stats["invalid_symbols"] > 0:
            logger.warning(f"⚠️ {self.validation_stats['invalid_symbols']} simboli non validi trovati")
        
        if self.validation_stats["valid_symbols"] == 0:
            raise SymbolValidationError("Nessun simbolo valido trovato!")
        
        logger.info(f"✅ Validazione completata: {self.validation_stats['valid_symbols']}/{self.validation_stats['total_symbols']} simboli validi")
        
        return {
            "symbols": self.symbols,
            "symbol_map": self.symbol_map,
            "id_map": self.id_map,
            "name_map": self.name_map,
            "stats": self.validation_stats
        }
    
    def _validate_symbol_entry(self, entry: Dict[str, Any], index: int) -> None:
        """Valida un singolo entry simbolo."""
        # Campi richiesti
        required_fields = ["id", "symbol", "name", "description", "category", "aliases", "status", "version"]
        for field in required_fields:
            if field not in entry:
                raise SymbolValidationError(f"Campo richiesto mancante: {field}")
        
        # Validazione ID
        symbol_id = entry["id"]
        if not isinstance(symbol_id, str) or not symbol_id.startswith("NG"):
            raise SymbolValidationError(f"ID non valido: {symbol_id}")
        
        if symbol_id in self.id_map:
            raise SymbolValidationError(f"ID duplicato: {symbol_id}")
            self.validation_stats["duplicate_issues"] += 1
        
        # Validazione simbolo Unicode
        symbol = entry["symbol"]
        if not isinstance(symbol, str) or len(symbol) == 0:
            raise SymbolValidationError(f"Simbolo non valido: {symbol}")
        
        if symbol in self.symbol_map:
            raise SymbolValidationError(f"Simbolo duplicato: {symbol}")
            self.validation_stats["duplicate_issues"] += 1
        
        # Verifica Unicode
        try:
            unicodedata.name(symbol[0])  # Verifica che sia un carattere Unicode valido
        except ValueError:
            logger.warning(f"⚠️ Simbolo con Unicode non standard: {symbol}")
            self.validation_stats["unicode_issues"] += 1
        
        # Validazione nome
        name = entry["name"]
        if not isinstance(name, str) or len(name) == 0:
            raise SymbolValidationError(f"Nome non valido: {name}")
        
        if name in self.name_map:
            raise SymbolValidationError(f"Nome duplicato: {name}")
        
        # Validazione categoria
        category = entry["category"]
        if category not in self.VALID_CATEGORIES:
            raise SymbolValidationError(f"Categoria non valida: {category}. Valide: {self.VALID_CATEGORIES}")
        
        # Validazione status
        status = entry["status"]
        if status not in self.VALID_STATUSES:
            raise SymbolValidationError(f"Status non valido: {status}. Validi: {self.VALID_STATUSES}")
        
        # Validazione aliases
        aliases = entry["aliases"]
        if not isinstance(aliases, list):
            raise SymbolValidationError("Aliases deve essere una lista")
        
        # Validazione descrizione
        description = entry["description"]
        if not isinstance(description, str) or len(description) < 10:
            raise SymbolValidationError("Descrizione deve essere una stringa di almeno 10 caratteri")
        
        # Validazione versione
        version = entry["version"]
        if not isinstance(version, str) or not re.match(r'^\d+\.\d+$', version):
            raise SymbolValidationError(f"Versione non valida: {version}")
    
    def get_symbols_by_category(self, category: str) -> List[Dict[str, Any]]:
        """Ritorna tutti i simboli di una categoria specifica."""
        return [s for s in self.symbols if s["category"] == category]
    
    def get_symbols_by_status(self, status: str) -> List[Dict[str, Any]]:
        """Ritorna tutti i simboli con uno status specifico."""
        return [s for s in self.symbols if s["status"] == status]
    
    def get_approved_symbols(self) -> List[Dict[str, Any]]:
        """Ritorna solo i simboli approvati per uso in produzione."""
        return self.get_symbols_by_status("approved")
    
    def find_symbol_by_name(self, name: str) -> Optional[Dict[str, Any]]:
        """Trova un simbolo per nome."""
        return self.name_map.get(name)
    
    def find_symbol_by_unicode(self, symbol: str) -> Optional[Dict[str, Any]]:
        """Trova un simbolo per carattere Unicode."""
        return self.symbol_map.get(symbol)
    
    def validate_for_socrate(self) -> Dict[str, Any]:
        """Valida simboli per integrazione SOCRATE (reasoning)."""
        reasoning_symbols = self.get_symbols_by_category("logic") + self.get_symbols_by_category("reasoning")
        
        if len(reasoning_symbols) < 10:
            logger.warning("⚠️ Pochi simboli di reasoning per SOCRATE")
        
        return {
            "reasoning_symbols": len(reasoning_symbols),
            "logic_symbols": len(self.get_symbols_by_category("logic")),
            "flow_symbols": len(self.get_symbols_by_category("flow")),
            "socrate_ready": len(reasoning_symbols) >= 10
        }
    
    def validate_for_god(self) -> Dict[str, Any]:
        """Valida simboli per integrazione GOD (memory)."""
        memory_symbols = self.get_symbols_by_category("memory") + self.get_symbols_by_category("meta")
        
        if len(memory_symbols) < 5:
            logger.warning("⚠️ Pochi simboli di memoria per GOD")
        
        return {
            "memory_symbols": len(memory_symbols),
            "meta_symbols": len(self.get_symbols_by_category("meta")),
            "system_symbols": len(self.get_symbols_by_category("system")),
            "god_ready": len(memory_symbols) >= 5
        }
    
    def generate_report(self) -> str:
        """Genera un report completo della validazione."""
        report = []
        report.append("🧠 NEUROGLYPH ULTRA - Symbol Validation Report")
        report.append("=" * 50)
        report.append(f"📊 Total Symbols: {self.validation_stats['total_symbols']}")
        report.append(f"✅ Valid Symbols: {self.validation_stats['valid_symbols']}")
        report.append(f"❌ Invalid Symbols: {self.validation_stats['invalid_symbols']}")
        report.append(f"⚠️ Unicode Issues: {self.validation_stats['unicode_issues']}")
        report.append(f"🔄 Duplicate Issues: {self.validation_stats['duplicate_issues']}")
        report.append("")
        
        report.append("📂 Categories:")
        for category, count in sorted(self.validation_stats['categories'].items()):
            report.append(f"  {category}: {count}")
        report.append("")
        
        report.append("🏷️ Statuses:")
        for status, count in sorted(self.validation_stats['statuses'].items()):
            report.append(f"  {status}: {count}")
        report.append("")
        
        # Validazione SOCRATE e GOD
        socrate_validation = self.validate_for_socrate()
        god_validation = self.validate_for_god()
        
        report.append("🧩 SOCRATE Integration:")
        report.append(f"  Reasoning symbols: {socrate_validation['reasoning_symbols']}")
        report.append(f"  Logic symbols: {socrate_validation['logic_symbols']}")
        report.append(f"  Ready: {'✅' if socrate_validation['socrate_ready'] else '❌'}")
        report.append("")
        
        report.append("♾️ GOD Integration:")
        report.append(f"  Memory symbols: {god_validation['memory_symbols']}")
        report.append(f"  Meta symbols: {god_validation['meta_symbols']}")
        report.append(f"  Ready: {'✅' if god_validation['god_ready'] else '❌'}")
        
        return "\n".join(report)


def load_symbols(symbols_path: str = "core/symbols.json") -> Dict[str, Any]:
    """
    Funzione di convenienza per caricare e validare simboli ULTRA.
    
    Args:
        symbols_path: Percorso al file simboli
        
    Returns:
        Dizionario con simboli validati
    """
    loader = UltraSymbolLoader()
    return loader.load_and_validate(symbols_path)


def validate_symbols_for_ultra() -> bool:
    """
    Valida che i simboli siano pronti per NEUROGLYPH ULTRA.
    
    Returns:
        True se i simboli sono validi per ULTRA
    """
    try:
        loader = UltraSymbolLoader()
        result = loader.load_and_validate()
        
        # Verifica requisiti ULTRA
        socrate_validation = loader.validate_for_socrate()
        god_validation = loader.validate_for_god()
        
        ultra_ready = (
            socrate_validation["socrate_ready"] and 
            god_validation["god_ready"] and
            result["stats"]["valid_symbols"] >= 100  # Minimo 100 simboli validi
        )
        
        if ultra_ready:
            logger.info("🚀 Simboli pronti per NEUROGLYPH ULTRA!")
        else:
            logger.warning("⚠️ Simboli non ancora pronti per ULTRA")
        
        return ultra_ready
        
    except Exception as e:
        logger.error(f"❌ Errore validazione ULTRA: {e}")
        return False


if __name__ == "__main__":
    # Test di validazione
    print("🧠 NEUROGLYPH ULTRA Symbol Validation")
    print("=" * 40)
    
    try:
        loader = UltraSymbolLoader()
        result = loader.load_and_validate()
        
        print(loader.generate_report())
        
        # Test ULTRA readiness
        ultra_ready = validate_symbols_for_ultra()
        print(f"\n🚀 ULTRA Ready: {'✅ YES' if ultra_ready else '❌ NO'}")
        
    except Exception as e:
        print(f"❌ Errore: {e}")
        exit(1)
