#!/usr/bin/env python3
"""
NEUROGLYPH LLM - Symbolic Validator
==================================

Strato di validazione simbolica per garantire zero allucinazioni.
Ogni output deve passare da: simboli → AST → codice → validatore semantico → sandbox.

Questo è il componente chiave per raggiungere "zero errori, zero allucinazioni".
"""

import ast
import sys
import subprocess
import tempfile
import json
from typing import Dict, List, Tuple, Optional, Any
from pathlib import Path
from dataclasses import dataclass
from enum import Enum

class ValidationResult(Enum):
    """Risultati possibili della validazione"""
    VALID = "valid"
    SYNTAX_ERROR = "syntax_error"
    SEMANTIC_ERROR = "semantic_error"
    EXECUTION_ERROR = "execution_error"
    SECURITY_ERROR = "security_error"
    NEUROGLYPH_ERROR = "neuroglyph_error"

@dataclass
class ValidationReport:
    """Report dettagliato della validazione"""
    result: ValidationResult
    confidence: float
    errors: List[str]
    warnings: List[str]
    ast_valid: bool
    execution_valid: bool
    semantic_consistency: float
    neuroglyph_fidelity: float
    
class SymbolicValidator:
    """
    Validatore simbolico per NEUROGLYPH LLM
    
    Garantisce che ogni output sia:
    1. Sintatticamente corretto
    2. Semanticamente coerente
    3. Eseguibile in sandbox
    4. Fedele ai simboli neuroglifi
    """
    
    def __init__(self, symbols_registry_path: str = "symbols_registry.json"):
        self.symbols_registry = self._load_symbols_registry(symbols_registry_path)
        self.forbidden_operations = {
            'import os', 'import subprocess', 'import sys',
            'exec', 'eval', 'open', '__import__',
            'file', 'input', 'raw_input'
        }
    
    def _load_symbols_registry(self, path: str) -> Dict[str, Any]:
        """Carica registry simboli neuroglifi"""
        try:
            with open(path, 'r', encoding='utf-8') as f:
                return json.load(f)
        except FileNotFoundError:
            print(f"⚠️ Registry simboli non trovato: {path}")
            return {}
    
    def validate_complete_pipeline(self, 
                                 neuroglyphs: str, 
                                 generated_code: str) -> ValidationReport:
        """
        Validazione completa pipeline NEUROGLYPH
        
        Args:
            neuroglyphs: Simboli neuroglifi input
            generated_code: Codice generato dal modello
            
        Returns:
            ValidationReport con tutti i controlli
        """
        errors = []
        warnings = []
        
        # 1. Validazione sintassi AST
        ast_valid, ast_errors = self._validate_ast(generated_code)
        if ast_errors:
            errors.extend(ast_errors)
        
        # 2. Validazione semantica
        semantic_score, semantic_errors = self._validate_semantics(generated_code)
        if semantic_errors:
            errors.extend(semantic_errors)
        
        # 3. Validazione esecuzione sandbox
        exec_valid, exec_errors = self._validate_execution(generated_code)
        if exec_errors:
            errors.extend(exec_errors)
        
        # 4. Validazione fedeltà neuroglifi
        ng_fidelity, ng_errors = self._validate_neuroglyph_fidelity(
            neuroglyphs, generated_code
        )
        if ng_errors:
            errors.extend(ng_errors)
        
        # 5. Validazione sicurezza
        security_errors = self._validate_security(generated_code)
        if security_errors:
            errors.extend(security_errors)
        
        # Determina risultato finale
        if security_errors:
            result = ValidationResult.SECURITY_ERROR
        elif not ast_valid:
            result = ValidationResult.SYNTAX_ERROR
        elif semantic_score < 0.8:
            result = ValidationResult.SEMANTIC_ERROR
        elif not exec_valid:
            result = ValidationResult.EXECUTION_ERROR
        elif ng_fidelity < 0.9:
            result = ValidationResult.NEUROGLYPH_ERROR
        else:
            result = ValidationResult.VALID
        
        # Calcola confidence
        confidence = min(semantic_score, ng_fidelity) if ast_valid and exec_valid else 0.0
        
        return ValidationReport(
            result=result,
            confidence=confidence,
            errors=errors,
            warnings=warnings,
            ast_valid=ast_valid,
            execution_valid=exec_valid,
            semantic_consistency=semantic_score,
            neuroglyph_fidelity=ng_fidelity
        )
    
    def _validate_ast(self, code: str) -> Tuple[bool, List[str]]:
        """Validazione AST Python"""
        try:
            ast.parse(code)
            return True, []
        except SyntaxError as e:
            return False, [f"Syntax Error: {e}"]
        except Exception as e:
            return False, [f"AST Error: {e}"]
    
    def _validate_semantics(self, code: str) -> Tuple[float, List[str]]:
        """Validazione semantica del codice"""
        errors = []
        score = 1.0
        
        try:
            tree = ast.parse(code)
            
            # Controlla strutture semantiche
            for node in ast.walk(tree):
                # Variabili non definite
                if isinstance(node, ast.Name) and isinstance(node.ctx, ast.Load):
                    # Qui dovremmo controllare scope, ma per ora semplifichiamo
                    pass
                
                # Funzioni senza return
                if isinstance(node, ast.FunctionDef):
                    has_return = any(isinstance(n, ast.Return) for n in ast.walk(node))
                    if not has_return and node.name != '__init__':
                        errors.append(f"Function {node.name} has no return statement")
                        score -= 0.1
                
                # Divisioni per zero potenziali
                if isinstance(node, ast.BinOp) and isinstance(node.op, ast.Div):
                    if isinstance(node.right, ast.Constant) and node.right.value == 0:
                        errors.append("Division by zero detected")
                        score -= 0.3
        
        except Exception as e:
            errors.append(f"Semantic analysis error: {e}")
            score = 0.0
        
        return max(0.0, score), errors
    
    def _validate_execution(self, code: str) -> Tuple[bool, List[str]]:
        """Validazione esecuzione in sandbox"""
        try:
            # Crea file temporaneo
            with tempfile.NamedTemporaryFile(mode='w', suffix='.py', delete=False) as f:
                f.write(code)
                temp_file = f.name
            
            # Esegui in subprocess isolato con timeout
            result = subprocess.run([
                sys.executable, '-c', f'''
import ast
import sys

# Carica e valida il codice
with open("{temp_file}", "r") as f:
    code = f.read()

# Compila senza eseguire
try:
    compile(code, "{temp_file}", "exec")
    print("COMPILE_SUCCESS")
except Exception as e:
    print(f"COMPILE_ERROR: {{e}}")
    sys.exit(1)
'''
            ], capture_output=True, text=True, timeout=5)
            
            # Cleanup
            Path(temp_file).unlink()
            
            if result.returncode == 0 and "COMPILE_SUCCESS" in result.stdout:
                return True, []
            else:
                return False, [f"Execution validation failed: {result.stderr}"]
                
        except subprocess.TimeoutExpired:
            return False, ["Execution timeout"]
        except Exception as e:
            return False, [f"Execution error: {e}"]
    
    def _validate_security(self, code: str) -> List[str]:
        """Validazione sicurezza - blocca operazioni pericolose"""
        errors = []
        
        # Controlla operazioni vietate
        for forbidden in self.forbidden_operations:
            if forbidden in code:
                errors.append(f"Forbidden operation detected: {forbidden}")
        
        # Controlla AST per operazioni pericolose
        try:
            tree = ast.parse(code)
            for node in ast.walk(tree):
                # Import pericolosi
                if isinstance(node, ast.Import):
                    for alias in node.names:
                        if alias.name in ['os', 'subprocess', 'sys']:
                            errors.append(f"Dangerous import: {alias.name}")
                
                # Chiamate eval/exec
                if isinstance(node, ast.Call):
                    if isinstance(node.func, ast.Name):
                        if node.func.id in ['eval', 'exec', '__import__']:
                            errors.append(f"Dangerous function call: {node.func.id}")
        
        except Exception:
            pass  # Se non riusciamo a parsare, altri validatori cattureranno l'errore
        
        return errors
    
    def _validate_neuroglyph_fidelity(self, 
                                    neuroglyphs: str, 
                                    code: str) -> Tuple[float, List[str]]:
        """Validazione fedeltà ai simboli neuroglifi"""
        errors = []
        
        if not self.symbols_registry:
            return 0.5, ["Symbols registry not available"]
        
        # Qui implementeremo la logica di round-trip:
        # neuroglyphs → code → neuroglyphs_reconstructed
        # e misureremo la similarità
        
        # Per ora, implementazione semplificata
        try:
            # Conta simboli riconosciuti
            recognized_symbols = 0
            total_symbols = 0
            
            for char in neuroglyphs:
                if char.strip():  # Ignora spazi
                    total_symbols += 1
                    # Cerca il simbolo nel registry
                    for symbol_data in self.symbols_registry.get('symbols', []):
                        if symbol_data.get('symbol') == char:
                            recognized_symbols += 1
                            break
            
            if total_symbols == 0:
                return 0.0, ["No neuroglyphs found"]
            
            fidelity = recognized_symbols / total_symbols
            
            if fidelity < 0.9:
                errors.append(f"Low neuroglyph fidelity: {fidelity:.2f}")
            
            return fidelity, errors
            
        except Exception as e:
            return 0.0, [f"Neuroglyph validation error: {e}"]

def demo_symbolic_validator():
    """Demo del validatore simbolico"""
    print("🔒 NEUROGLYPH LLM - Symbolic Validator Demo")
    print("=" * 60)
    
    validator = SymbolicValidator()
    
    # Test case 1: Codice valido
    neuroglyphs1 = "⟨⟩α⊕β⤴α⊕β"
    code1 = "def add(a, b):\n    return a + b"
    
    print("🧪 Test 1: Codice valido")
    report1 = validator.validate_complete_pipeline(neuroglyphs1, code1)
    print(f"   Risultato: {report1.result.value}")
    print(f"   Confidence: {report1.confidence:.2f}")
    print(f"   AST Valid: {report1.ast_valid}")
    print(f"   Execution Valid: {report1.execution_valid}")
    
    # Test case 2: Codice con errori
    neuroglyphs2 = "⟨⟩α÷0"
    code2 = "def divide_by_zero(a):\n    return a / 0"
    
    print("\n🧪 Test 2: Codice con errori")
    report2 = validator.validate_complete_pipeline(neuroglyphs2, code2)
    print(f"   Risultato: {report2.result.value}")
    print(f"   Errori: {report2.errors}")
    
    # Test case 3: Codice pericoloso
    neuroglyphs3 = "⟨⟩import_os"
    code3 = "import os\nos.system('rm -rf /')"
    
    print("\n🧪 Test 3: Codice pericoloso")
    report3 = validator.validate_complete_pipeline(neuroglyphs3, code3)
    print(f"   Risultato: {report3.result.value}")
    print(f"   Errori: {report3.errors}")
    
    print("\n✅ Demo completata!")

if __name__ == "__main__":
    demo_symbolic_validator()
