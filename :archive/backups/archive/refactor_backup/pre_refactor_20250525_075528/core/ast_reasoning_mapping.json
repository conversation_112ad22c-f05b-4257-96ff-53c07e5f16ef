{"version": "1.0", "description": "NEUROGLYPH ULTRA - AST to Reasoning Symbols Mapping", "created": "2025-05-23", "ast_reasoning_mappings": {"ast.Compare": ["ng:reasoning:similarity", "ng:reasoning:difference", "ng:reasoning:evaluation"], "ast.Eq": ["ng:reasoning:similarity", "ng:reasoning:consistency"], "ast.NotEq": ["ng:reasoning:difference", "ng:reasoning:contradiction"], "ast.Lt": ["ng:reasoning:evaluation", "ng:reasoning:assessment"], "ast.LtE": ["ng:reasoning:evaluation", "ng:reasoning:assessment"], "ast.Gt": ["ng:reasoning:evaluation", "ng:reasoning:assessment"], "ast.GtE": ["ng:reasoning:evaluation", "ng:reasoning:assessment"], "ast.Is": ["ng:reasoning:similarity", "ng:reasoning:classification"], "ast.IsNot": ["ng:reasoning:difference", "ng:reasoning:classification"], "ast.In": ["ng:reasoning:classification", "ng:reasoning:pattern"], "ast.NotIn": ["ng:reasoning:classification", "ng:reasoning:difference"], "ast.BoolOp": ["ng:reasoning:inference", "ng:reasoning:deduction"], "ast.And": ["ng:reasoning:synthesis", "ng:reasoning:consistency"], "ast.Or": ["ng:reasoning:generalization", "ng:reasoning:hypothesis"], "ast.Not": ["ng:reasoning:contradiction", "ng:reasoning:contrapositive"], "ast.If": ["ng:reasoning:decision", "ng:reasoning:judgment"], "ast.IfExp": ["ng:reasoning:decision", "ng:reasoning:evaluation"], "ast.For": ["ng:reasoning:pattern", "ng:reasoning:induction"], "ast.While": ["ng:reasoning:monitoring", "ng:reasoning:control"], "ast.Break": ["ng:reasoning:decision", "ng:reasoning:control"], "ast.Continue": ["ng:reasoning:strategy", "ng:reasoning:control"], "ast.FunctionDef": ["ng:reasoning:abstraction", "ng:reasoning:planning"], "ast.AsyncFunctionDef": ["ng:reasoning:abstraction", "ng:reasoning:planning"], "ast.Lambda": ["ng:reasoning:abstraction", "ng:reasoning:synthesis"], "ast.Return": ["ng:reasoning:conclusion", "ng:reasoning:synthesis"], "ast.Yield": ["ng:reasoning:generalization", "ng:reasoning:pattern"], "ast.YieldFrom": ["ng:reasoning:generalization", "ng:reasoning:abstraction"], "ast.Try": ["ng:reasoning:hypothesis", "ng:reasoning:monitoring"], "ast.ExceptHandler": ["ng:reasoning:error_correction", "ng:reasoning:fallacy"], "ast.Raise": ["ng:reasoning:contradiction", "ng:reasoning:error_correction"], "ast.Assert": ["ng:reasoning:proof", "ng:reasoning:validity"], "ast.ClassDef": ["ng:reasoning:classification", "ng:reasoning:abstraction"], "ast.Attribute": ["ng:reasoning:specialization", "ng:reasoning:analysis"], "ast.Subscript": ["ng:reasoning:specialization", "ng:reasoning:pattern"], "ast.Assign": ["ng:reasoning:premise", "ng:reasoning:hypothesis"], "ast.AnnAssign": ["ng:reasoning:premise", "ng:reasoning:classification"], "ast.AugAssign": ["ng:reasoning:synthesis", "ng:reasoning:generalization"], "ast.ListComp": ["ng:reasoning:pattern", "ng:reasoning:generalization"], "ast.SetComp": ["ng:reasoning:pattern", "ng:reasoning:classification"], "ast.DictComp": ["ng:reasoning:pattern", "ng:reasoning:analogy"], "ast.GeneratorExp": ["ng:reasoning:pattern", "ng:reasoning:induction"], "ast.Call": ["ng:reasoning:inference", "ng:reasoning:deduction"], "ast.Starred": ["ng:reasoning:generalization", "ng:reasoning:pattern"], "ast.NamedExpr": ["ng:reasoning:premise", "ng:reasoning:hypothesis"], "ast.Match": ["ng:reasoning:pattern", "ng:reasoning:classification"], "ast.MatchValue": ["ng:reasoning:similarity", "ng:reasoning:pattern"], "ast.MatchSingleton": ["ng:reasoning:classification", "ng:reasoning:validity"], "ast.MatchSequence": ["ng:reasoning:pattern", "ng:reasoning:induction"], "ast.MatchMapping": ["ng:reasoning:analogy", "ng:reasoning:pattern"], "ast.MatchClass": ["ng:reasoning:classification", "ng:reasoning:specialization"], "ast.MatchStar": ["ng:reasoning:generalization", "ng:reasoning:pattern"], "ast.MatchAs": ["ng:reasoning:analogy", "ng:reasoning:abstraction"], "ast.MatchOr": ["ng:reasoning:generalization", "ng:reasoning:hypothesis"]}, "reasoning_symbols_count": 56, "mapped_ast_patterns": 53}