{"instruction": "Converti questa funzione Python in neuroglifi", "input": "def modulo(a, b):\n    return a % b", "output": "⟨⟩α%β⤴α%β", "task_type": "code_to_symbol", "difficulty": "basic", "language": "python", "symbols_used": ["⟨", "⟩", "α", "β", "%", "⤴"]}
{"instruction": "Converti questa funzione Python in neuroglifi", "input": "def modulo(a, b):\n    return a % b", "output": "⟨⟩α%β⤴α%β", "task_type": "code_to_symbol", "difficulty": "basic", "language": "python", "symbols_used": ["⟨", "⟩", "α", "β", "%", "⤴"]}
{"instruction": "Traduci questi neuroglifi in una funzione Python per power", "input": "⟨⟩α^β⤴α^β", "output": "def power(a, b):\n    return a ** b", "task_type": "symbol_to_code", "difficulty": "basic", "language": "python", "symbols_used": ["⟨", "⟩", "α", "β", "^", "⤴"], "validation_code": "assert power(5, 3) == 5 ** 3"}
{"instruction": "Traduci questi neuroglifi in una funzione Python per divide", "input": "⟨⟩α⊘β⤴α⊘β", "output": "def divide(a, b):\n    return a / b", "task_type": "symbol_to_code", "difficulty": "basic", "language": "python", "symbols_used": ["⟨", "⟩", "α", "β", "⊘", "⤴"], "validation_code": "assert divide(5, 3) == 5 / 3"}
{"instruction": "Converti questa funzione Python in neuroglifi", "input": "def divide(a, b):\n    return a / b", "output": "⟨⟩α⊘β⤴α⊘β", "task_type": "code_to_symbol", "difficulty": "basic", "language": "python", "symbols_used": ["⟨", "⟩", "α", "β", "⊘", "⤴"]}
{"instruction": "Traduci questi neuroglifi in una funzione Python per multiply", "input": "⟨⟩α⊗β⤴α⊗β", "output": "def multiply(a, b):\n    return a * b", "task_type": "symbol_to_code", "difficulty": "basic", "language": "python", "symbols_used": ["⟨", "⟩", "α", "β", "⊗", "⤴"], "validation_code": "assert multiply(5, 3) == 5 * 3"}
{"instruction": "Converti questa funzione Python in neuroglifi", "input": "def subtract(a, b):\n    return a - b", "output": "⟨⟩α⊖β⤴α⊖β", "task_type": "code_to_symbol", "difficulty": "basic", "language": "python", "symbols_used": ["⟨", "⟩", "α", "β", "⊖", "⤴"]}
{"instruction": "Converti questa funzione Python in neuroglifi", "input": "def subtract(a, b):\n    return a - b", "output": "⟨⟩α⊖β⤴α⊖β", "task_type": "code_to_symbol", "difficulty": "basic", "language": "python", "symbols_used": ["⟨", "⟩", "α", "β", "⊖", "⤴"]}
{"instruction": "Converti questa funzione Python in neuroglifi", "input": "def subtract(a, b):\n    return a - b", "output": "⟨⟩α⊖β⤴α⊖β", "task_type": "code_to_symbol", "difficulty": "basic", "language": "python", "symbols_used": ["⟨", "⟩", "α", "β", "⊖", "⤴"]}
{"instruction": "Converti questa funzione Python in neuroglifi", "input": "def multiply(a, b):\n    return a * b", "output": "⟨⟩α⊗β⤴α⊗β", "task_type": "code_to_symbol", "difficulty": "basic", "language": "python", "symbols_used": ["⟨", "⟩", "α", "β", "⊗", "⤴"]}
{"instruction": "Traduci questi neuroglifi in una funzione Python per subtract", "input": "⟨⟩α⊖β⤴α⊖β", "output": "def subtract(a, b):\n    return a - b", "task_type": "symbol_to_code", "difficulty": "basic", "language": "python", "symbols_used": ["⟨", "⟩", "α", "β", "⊖", "⤴"], "validation_code": "assert subtract(5, 3) == 5 - 3"}
{"instruction": "Converti questa funzione Python in neuroglifi", "input": "def subtract(a, b):\n    return a - b", "output": "⟨⟩α⊖β⤴α⊖β", "task_type": "code_to_symbol", "difficulty": "basic", "language": "python", "symbols_used": ["⟨", "⟩", "α", "β", "⊖", "⤴"]}
{"instruction": "Converti questa funzione Python in neuroglifi", "input": "def modulo(a, b):\n    return a % b", "output": "⟨⟩α%β⤴α%β", "task_type": "code_to_symbol", "difficulty": "basic", "language": "python", "symbols_used": ["⟨", "⟩", "α", "β", "%", "⤴"]}
{"instruction": "Traduci questi neuroglifi in PYTHON", "input": "⟨⟩α⊕β⤴α⊕β", "output": "def add(a, b):\n    return a + b", "task_type": "symbol_to_code", "difficulty": "intermediate", "language": "python", "symbols_used": ["⟨", "⟩", "α", "β", "⊕", "⤴"]}
{"instruction": "Traduci questi neuroglifi in una funzione Python per multiply", "input": "⟨⟩α⊗β⤴α⊗β", "output": "def multiply(a, b):\n    return a * b", "task_type": "symbol_to_code", "difficulty": "basic", "language": "python", "symbols_used": ["⟨", "⟩", "α", "β", "⊗", "⤴"], "validation_code": "assert multiply(5, 3) == 5 * 3"}
