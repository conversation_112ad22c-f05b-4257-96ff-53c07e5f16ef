{"instruction": "Converti questi neuroglifi in una funzione Python", "input": "⟨⟩α⊕β⤴α⊕β", "output": "def add(a, b):\n    return a + b"}
{"instruction": "Traduci neuroglifi in operazione logica Python", "input": "⟨⟩α∨β⤴α∨β", "output": "def logical_or(a, b):\n    return a or b"}
{"instruction": "Traduci neuroglifi in iterazione Python", "input": "⟨⟩∏ᵢ₌₁ⁿαᵢ", "output": "def product_range(n):\n    result = 1\n    for i in range(1, n+1):\n        result *= i\n    return result"}
