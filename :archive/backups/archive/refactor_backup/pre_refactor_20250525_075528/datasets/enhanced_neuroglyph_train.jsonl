{"instruction": "Traduci questi neuroglifi in una funzione Python per modulo", "input": "⟨⟩α%β⤴α%β", "output": "def modulo(a, b):\n    return a % b", "task_type": "symbol_to_code", "difficulty": "basic", "language": "python", "symbols_used": ["⟨", "⟩", "α", "β", "%", "⤴"], "validation_code": "assert modulo(5, 3) == 5 % 3"}
{"instruction": "Converti questa funzione Python in neuroglifi", "input": "def subtract(a, b):\n    return a - b", "output": "⟨⟩α⊖β⤴α⊖β", "task_type": "code_to_symbol", "difficulty": "basic", "language": "python", "symbols_used": ["⟨", "⟩", "α", "β", "⊖", "⤴"]}
{"instruction": "Converti questa funzione Python in neuroglifi", "input": "def divide(a, b):\n    return a / b", "output": "⟨⟩α⊘β⤴α⊘β", "task_type": "code_to_symbol", "difficulty": "basic", "language": "python", "symbols_used": ["⟨", "⟩", "α", "β", "⊘", "⤴"]}
{"instruction": "Traduci questi neuroglifi in una funzione Python per add", "input": "⟨⟩α⊕β⤴α⊕β", "output": "def add(a, b):\n    return a + b", "task_type": "symbol_to_code", "difficulty": "basic", "language": "python", "symbols_used": ["⟨", "⟩", "α", "β", "⊕", "⤴"], "validation_code": "assert add(5, 3) == 5 + 3"}
{"instruction": "Traduci questi neuroglifi in una funzione Python per divide", "input": "⟨⟩α⊘β⤴α⊘β", "output": "def divide(a, b):\n    return a / b", "task_type": "symbol_to_code", "difficulty": "basic", "language": "python", "symbols_used": ["⟨", "⟩", "α", "β", "⊘", "⤴"], "validation_code": "assert divide(5, 3) == 5 / 3"}
{"instruction": "Converti questa funzione Python in neuroglifi", "input": "def divide(a, b):\n    return a / b", "output": "⟨⟩α⊘β⤴α⊘β", "task_type": "code_to_symbol", "difficulty": "basic", "language": "python", "symbols_used": ["⟨", "⟩", "α", "β", "⊘", "⤴"]}
{"instruction": "Traduci questi neuroglifi in una funzione Python per add", "input": "⟨⟩α⊕β⤴α⊕β", "output": "def add(a, b):\n    return a + b", "task_type": "symbol_to_code", "difficulty": "basic", "language": "python", "symbols_used": ["⟨", "⟩", "α", "β", "⊕", "⤴"], "validation_code": "assert add(5, 3) == 5 + 3"}
{"instruction": "Converti questa funzione Python in neuroglifi", "input": "def subtract(a, b):\n    return a - b", "output": "⟨⟩α⊖β⤴α⊖β", "task_type": "code_to_symbol", "difficulty": "basic", "language": "python", "symbols_used": ["⟨", "⟩", "α", "β", "⊖", "⤴"]}
{"instruction": "Converti questa funzione Python in neuroglifi", "input": "def add(a, b):\n    return a + b", "output": "⟨⟩α⊕β⤴α⊕β", "task_type": "code_to_symbol", "difficulty": "basic", "language": "python", "symbols_used": ["⟨", "⟩", "α", "β", "⊕", "⤴"]}
{"instruction": "Converti questa funzione Python in neuroglifi", "input": "def power(a, b):\n    return a ** b", "output": "⟨⟩α^β⤴α^β", "task_type": "code_to_symbol", "difficulty": "basic", "language": "python", "symbols_used": ["⟨", "⟩", "α", "β", "^", "⤴"]}
{"instruction": "Converti questa funzione Python in neuroglifi", "input": "def divide(a, b):\n    return a / b", "output": "⟨⟩α⊘β⤴α⊘β", "task_type": "code_to_symbol", "difficulty": "basic", "language": "python", "symbols_used": ["⟨", "⟩", "α", "β", "⊘", "⤴"]}
{"instruction": "Converti questa funzione Python in neuroglifi", "input": "def power(a, b):\n    return a ** b", "output": "⟨⟩α^β⤴α^β", "task_type": "code_to_symbol", "difficulty": "basic", "language": "python", "symbols_used": ["⟨", "⟩", "α", "β", "^", "⤴"]}
{"instruction": "Converti questa funzione Python in neuroglifi", "input": "def divide(a, b):\n    return a / b", "output": "⟨⟩α⊘β⤴α⊘β", "task_type": "code_to_symbol", "difficulty": "basic", "language": "python", "symbols_used": ["⟨", "⟩", "α", "β", "⊘", "⤴"]}
{"instruction": "Converti questa funzione Python in neuroglifi", "input": "def subtract(a, b):\n    return a - b", "output": "⟨⟩α⊖β⤴α⊖β", "task_type": "code_to_symbol", "difficulty": "basic", "language": "python", "symbols_used": ["⟨", "⟩", "α", "β", "⊖", "⤴"]}
{"instruction": "Converti questa funzione Python in neuroglifi", "input": "def subtract(a, b):\n    return a - b", "output": "⟨⟩α⊖β⤴α⊖β", "task_type": "code_to_symbol", "difficulty": "basic", "language": "python", "symbols_used": ["⟨", "⟩", "α", "β", "⊖", "⤴"]}
{"instruction": "Traduci questi neuroglifi in una funzione Python per subtract", "input": "⟨⟩α⊖β⤴α⊖β", "output": "def subtract(a, b):\n    return a - b", "task_type": "symbol_to_code", "difficulty": "basic", "language": "python", "symbols_used": ["⟨", "⟩", "α", "β", "⊖", "⤴"], "validation_code": "assert subtract(5, 3) == 5 - 3"}
{"instruction": "Traduci questi neuroglifi in una funzione Python per add", "input": "⟨⟩α⊕β⤴α⊕β", "output": "def add(a, b):\n    return a + b", "task_type": "symbol_to_code", "difficulty": "basic", "language": "python", "symbols_used": ["⟨", "⟩", "α", "β", "⊕", "⤴"], "validation_code": "assert add(5, 3) == 5 + 3"}
{"instruction": "Converti questa funzione Python in neuroglifi", "input": "def divide(a, b):\n    return a / b", "output": "⟨⟩α⊘β⤴α⊘β", "task_type": "code_to_symbol", "difficulty": "basic", "language": "python", "symbols_used": ["⟨", "⟩", "α", "β", "⊘", "⤴"]}
{"instruction": "Converti questa funzione Python in neuroglifi", "input": "def multiply(a, b):\n    return a * b", "output": "⟨⟩α⊗β⤴α⊗β", "task_type": "code_to_symbol", "difficulty": "basic", "language": "python", "symbols_used": ["⟨", "⟩", "α", "β", "⊗", "⤴"]}
{"instruction": "Trova e correggi l'errore in questi neuroglifi", "input": "⟨⟩ƒ⟨n⟩◊n≤0⤴1◈⤴n⊗ƒ⟨n⊕1⟩", "output": "⟨⟩ƒ⟨n⟩◊n≤0⤴1◈⤴n⊗ƒ⟨n⊖1⟩", "task_type": "debugging", "difficulty": "advanced", "language": "symbol", "reasoning_steps": ["1. La funzione dovrebbe calcolare il fattoriale", "2. Caso base: n≤0 ritorna 1 ✓", "3. ERRORE: ricorsione con n⊕1 (n+1) causa loop infinito", "4. CORREZIONE: deve essere n⊖1 (n-1) per convergere", "5. Risultato: fattoriale corretto"], "symbols_used": ["⟨", "⟩", "ƒ", "◊", "≤", "⤴", "◈", "⊗", "⊖"]}
{"instruction": "Traduci questi neuroglifi in una funzione Python per divide", "input": "⟨⟩α⊘β⤴α⊘β", "output": "def divide(a, b):\n    return a / b", "task_type": "symbol_to_code", "difficulty": "basic", "language": "python", "symbols_used": ["⟨", "⟩", "α", "β", "⊘", "⤴"], "validation_code": "assert divide(5, 3) == 5 / 3"}
{"instruction": "Converti questa funzione Python in neuroglifi", "input": "def add(a, b):\n    return a + b", "output": "⟨⟩α⊕β⤴α⊕β", "task_type": "code_to_symbol", "difficulty": "basic", "language": "python", "symbols_used": ["⟨", "⟩", "α", "β", "⊕", "⤴"]}
{"instruction": "Traduci questi neuroglifi in una funzione Python per subtract", "input": "⟨⟩α⊖β⤴α⊖β", "output": "def subtract(a, b):\n    return a - b", "task_type": "symbol_to_code", "difficulty": "basic", "language": "python", "symbols_used": ["⟨", "⟩", "α", "β", "⊖", "⤴"], "validation_code": "assert subtract(5, 3) == 5 - 3"}
{"instruction": "Traduci questa definizione di classe in Python", "input": "⟪Calculator⟫⟨⟩ƒ⟨α,β⟩⤴α⊕β⟨⟩ƒ⟨α,β⟩⤴α⊗β", "output": "class Calculator:\n    def add(self, a, b):\n        return a + b\n    \n    def multiply(self, a, b):\n        return a * b", "task_type": "symbol_to_code", "difficulty": "advanced", "language": "python", "reasoning_steps": ["1. ⟪Calculator⟫ definisce classe Calculator", "2. Primo ⟨⟩ƒ⟨α,β⟩⤴α⊕β è metodo add", "3. Secondo ⟨⟩ƒ⟨α,β⟩⤴α⊗β è metodo multiply", "4. Ogni metodo ha parametri α,β e return operation"], "symbols_used": ["⟪", "⟫", "⟨", "⟩", "ƒ", "α", "β", "⤴", "⊕", "⊗"]}
{"instruction": "Traduci questi neuroglifi in GO", "input": "⟨⟩α⊕β⤴α⊕β", "output": "func add(a int, b int) int {\n    return a + b\n}", "task_type": "symbol_to_code", "difficulty": "intermediate", "language": "go", "symbols_used": ["⟨", "⟩", "α", "β", "⊕", "⤴"]}
{"instruction": "Traduci questi neuroglifi in una funzione Python per multiply", "input": "⟨⟩α⊗β⤴α⊗β", "output": "def multiply(a, b):\n    return a * b", "task_type": "symbol_to_code", "difficulty": "basic", "language": "python", "symbols_used": ["⟨", "⟩", "α", "β", "⊗", "⤴"], "validation_code": "assert multiply(5, 3) == 5 * 3"}
{"instruction": "Traduci questi neuroglifi in una funzione Python per multiply", "input": "⟨⟩α⊗β⤴α⊗β", "output": "def multiply(a, b):\n    return a * b", "task_type": "symbol_to_code", "difficulty": "basic", "language": "python", "symbols_used": ["⟨", "⟩", "α", "β", "⊗", "⤴"], "validation_code": "assert multiply(5, 3) == 5 * 3"}
{"instruction": "Traduci questi neuroglifi in JAVASCRIPT", "input": "⟨⟩α⊕β⤴α⊕β", "output": "function add(a, b) {\n    return a + b;\n}", "task_type": "symbol_to_code", "difficulty": "intermediate", "language": "javascript", "symbols_used": ["⟨", "⟩", "α", "β", "⊕", "⤴"]}
{"instruction": "Converti questa funzione Python in neuroglifi", "input": "def multiply(a, b):\n    return a * b", "output": "⟨⟩α⊗β⤴α⊗β", "task_type": "code_to_symbol", "difficulty": "basic", "language": "python", "symbols_used": ["⟨", "⟩", "α", "β", "⊗", "⤴"]}
{"instruction": "Converti questa funzione Python in neuroglifi", "input": "def divide(a, b):\n    return a / b", "output": "⟨⟩α⊘β⤴α⊘β", "task_type": "code_to_symbol", "difficulty": "basic", "language": "python", "symbols_used": ["⟨", "⟩", "α", "β", "⊘", "⤴"]}
{"instruction": "Traduci questi neuroglifi in una funzione Python per modulo", "input": "⟨⟩α%β⤴α%β", "output": "def modulo(a, b):\n    return a % b", "task_type": "symbol_to_code", "difficulty": "basic", "language": "python", "symbols_used": ["⟨", "⟩", "α", "β", "%", "⤴"], "validation_code": "assert modulo(5, 3) == 5 % 3"}
{"instruction": "Converti questa funzione Python in neuroglifi", "input": "def add(a, b):\n    return a + b", "output": "⟨⟩α⊕β⤴α⊕β", "task_type": "code_to_symbol", "difficulty": "basic", "language": "python", "symbols_used": ["⟨", "⟩", "α", "β", "⊕", "⤴"]}
{"instruction": "Traduci questi neuroglifi in una funzione Python per add", "input": "⟨⟩α⊕β⤴α⊕β", "output": "def add(a, b):\n    return a + b", "task_type": "symbol_to_code", "difficulty": "basic", "language": "python", "symbols_used": ["⟨", "⟩", "α", "β", "⊕", "⤴"], "validation_code": "assert add(5, 3) == 5 + 3"}
{"instruction": "Converti questa funzione Python in neuroglifi", "input": "def add(a, b):\n    return a + b", "output": "⟨⟩α⊕β⤴α⊕β", "task_type": "code_to_symbol", "difficulty": "basic", "language": "python", "symbols_used": ["⟨", "⟩", "α", "β", "⊕", "⤴"]}
{"instruction": "Converti questa funzione Python in neuroglifi", "input": "def divide(a, b):\n    return a / b", "output": "⟨⟩α⊘β⤴α⊘β", "task_type": "code_to_symbol", "difficulty": "basic", "language": "python", "symbols_used": ["⟨", "⟩", "α", "β", "⊘", "⤴"]}
{"instruction": "Converti questa funzione Python in neuroglifi", "input": "def add(a, b):\n    return a + b", "output": "⟨⟩α⊕β⤴α⊕β", "task_type": "code_to_symbol", "difficulty": "basic", "language": "python", "symbols_used": ["⟨", "⟩", "α", "β", "⊕", "⤴"]}
{"instruction": "Traduci questi neuroglifi in una funzione Python per multiply", "input": "⟨⟩α⊗β⤴α⊗β", "output": "def multiply(a, b):\n    return a * b", "task_type": "symbol_to_code", "difficulty": "basic", "language": "python", "symbols_used": ["⟨", "⟩", "α", "β", "⊗", "⤴"], "validation_code": "assert multiply(5, 3) == 5 * 3"}
{"instruction": "Converti questa funzione Python in neuroglifi", "input": "def power(a, b):\n    return a ** b", "output": "⟨⟩α^β⤴α^β", "task_type": "code_to_symbol", "difficulty": "basic", "language": "python", "symbols_used": ["⟨", "⟩", "α", "β", "^", "⤴"]}
{"instruction": "Traduci questa list comprehension simbolica", "input": "⟨⟩[α²|α∈[1..n]∧α%2=0]", "output": "def even_squares(n):\n    return [x**2 for x in range(1, n+1) if x % 2 == 0]", "task_type": "symbol_to_code", "difficulty": "advanced", "language": "python", "reasoning_steps": ["1. ⟨⟩ definisce funzione", "2. [α²|...] è list comprehension con α²", "3. α∈[1..n] è il range di iterazione", "4. ∧α%2=0 è la condizione di filtro (numeri pari)"], "symbols_used": ["⟨", "⟩", "[", "]", "²", "|", "∈", "∧", "%", "="]}
{"instruction": "Traduci questi neuroglifi in una funzione Python per modulo", "input": "⟨⟩α%β⤴α%β", "output": "def modulo(a, b):\n    return a % b", "task_type": "symbol_to_code", "difficulty": "basic", "language": "python", "symbols_used": ["⟨", "⟩", "α", "β", "%", "⤴"], "validation_code": "assert modulo(5, 3) == 5 % 3"}
{"instruction": "Converti questa funzione Python in neuroglifi", "input": "def divide(a, b):\n    return a / b", "output": "⟨⟩α⊘β⤴α⊘β", "task_type": "code_to_symbol", "difficulty": "basic", "language": "python", "symbols_used": ["⟨", "⟩", "α", "β", "⊘", "⤴"]}
{"instruction": "Traduci questi neuroglifi in una funzione Python per divide", "input": "⟨⟩α⊘β⤴α⊘β", "output": "def divide(a, b):\n    return a / b", "task_type": "symbol_to_code", "difficulty": "basic", "language": "python", "symbols_used": ["⟨", "⟩", "α", "β", "⊘", "⤴"], "validation_code": "assert divide(5, 3) == 5 / 3"}
{"instruction": "Converti questa funzione Python in neuroglifi", "input": "def power(a, b):\n    return a ** b", "output": "⟨⟩α^β⤴α^β", "task_type": "code_to_symbol", "difficulty": "basic", "language": "python", "symbols_used": ["⟨", "⟩", "α", "β", "^", "⤴"]}
{"instruction": "Traduci questi neuroglifi in una funzione Python per divide", "input": "⟨⟩α⊘β⤴α⊘β", "output": "def divide(a, b):\n    return a / b", "task_type": "symbol_to_code", "difficulty": "basic", "language": "python", "symbols_used": ["⟨", "⟩", "α", "β", "⊘", "⤴"], "validation_code": "assert divide(5, 3) == 5 / 3"}
{"instruction": "Converti questa funzione Python in neuroglifi", "input": "def divide(a, b):\n    return a / b", "output": "⟨⟩α⊘β⤴α⊘β", "task_type": "code_to_symbol", "difficulty": "basic", "language": "python", "symbols_used": ["⟨", "⟩", "α", "β", "⊘", "⤴"]}
{"instruction": "Converti questa funzione Python in neuroglifi", "input": "def modulo(a, b):\n    return a % b", "output": "⟨⟩α%β⤴α%β", "task_type": "code_to_symbol", "difficulty": "basic", "language": "python", "symbols_used": ["⟨", "⟩", "α", "β", "%", "⤴"]}
{"instruction": "Traduci questi neuroglifi in una funzione Python per add", "input": "⟨⟩α⊕β⤴α⊕β", "output": "def add(a, b):\n    return a + b", "task_type": "symbol_to_code", "difficulty": "basic", "language": "python", "symbols_used": ["⟨", "⟩", "α", "β", "⊕", "⤴"], "validation_code": "assert add(5, 3) == 5 + 3"}
{"instruction": "Converti questa funzione Python in neuroglifi", "input": "def power(a, b):\n    return a ** b", "output": "⟨⟩α^β⤴α^β", "task_type": "code_to_symbol", "difficulty": "basic", "language": "python", "symbols_used": ["⟨", "⟩", "α", "β", "^", "⤴"]}
{"instruction": "Converti questa funzione Python in neuroglifi", "input": "def modulo(a, b):\n    return a % b", "output": "⟨⟩α%β⤴α%β", "task_type": "code_to_symbol", "difficulty": "basic", "language": "python", "symbols_used": ["⟨", "⟩", "α", "β", "%", "⤴"]}
{"instruction": "Traduci questi neuroglifi in RUST", "input": "⟨⟩α⊕β⤴α⊕β", "output": "fn add(a: i32, b: i32) -> i32 {\n    a + b\n}", "task_type": "symbol_to_code", "difficulty": "intermediate", "language": "rust", "symbols_used": ["⟨", "⟩", "α", "β", "⊕", "⤴"]}
{"instruction": "Traduci questi neuroglifi in una funzione Python per modulo", "input": "⟨⟩α%β⤴α%β", "output": "def modulo(a, b):\n    return a % b", "task_type": "symbol_to_code", "difficulty": "basic", "language": "python", "symbols_used": ["⟨", "⟩", "α", "β", "%", "⤴"], "validation_code": "assert modulo(5, 3) == 5 % 3"}
{"instruction": "Converti questa funzione Python in neuroglifi", "input": "def add(a, b):\n    return a + b", "output": "⟨⟩α⊕β⤴α⊕β", "task_type": "code_to_symbol", "difficulty": "basic", "language": "python", "symbols_used": ["⟨", "⟩", "α", "β", "⊕", "⤴"]}
{"instruction": "Traduci questi neuroglifi in una funzione Python per subtract", "input": "⟨⟩α⊖β⤴α⊖β", "output": "def subtract(a, b):\n    return a - b", "task_type": "symbol_to_code", "difficulty": "basic", "language": "python", "symbols_used": ["⟨", "⟩", "α", "β", "⊖", "⤴"], "validation_code": "assert subtract(5, 3) == 5 - 3"}
{"instruction": "Traduci questi neuroglifi in una funzione Python per power", "input": "⟨⟩α^β⤴α^β", "output": "def power(a, b):\n    return a ** b", "task_type": "symbol_to_code", "difficulty": "basic", "language": "python", "symbols_used": ["⟨", "⟩", "α", "β", "^", "⤴"], "validation_code": "assert power(5, 3) == 5 ** 3"}
{"instruction": "Converti questa funzione Python in neuroglifi", "input": "def subtract(a, b):\n    return a - b", "output": "⟨⟩α⊖β⤴α⊖β", "task_type": "code_to_symbol", "difficulty": "basic", "language": "python", "symbols_used": ["⟨", "⟩", "α", "β", "⊖", "⤴"]}
{"instruction": "Traduci questi neuroglifi in una funzione Python per subtract", "input": "⟨⟩α⊖β⤴α⊖β", "output": "def subtract(a, b):\n    return a - b", "task_type": "symbol_to_code", "difficulty": "basic", "language": "python", "symbols_used": ["⟨", "⟩", "α", "β", "⊖", "⤴"], "validation_code": "assert subtract(5, 3) == 5 - 3"}
{"instruction": "Converti questa funzione Python in neuroglifi", "input": "def multiply(a, b):\n    return a * b", "output": "⟨⟩α⊗β⤴α⊗β", "task_type": "code_to_symbol", "difficulty": "basic", "language": "python", "symbols_used": ["⟨", "⟩", "α", "β", "⊗", "⤴"]}
{"instruction": "Converti questa funzione Python in neuroglifi", "input": "def add(a, b):\n    return a + b", "output": "⟨⟩α⊕β⤴α⊕β", "task_type": "code_to_symbol", "difficulty": "basic", "language": "python", "symbols_used": ["⟨", "⟩", "α", "β", "⊕", "⤴"]}
{"instruction": "Converti questa funzione Python in neuroglifi", "input": "def subtract(a, b):\n    return a - b", "output": "⟨⟩α⊖β⤴α⊖β", "task_type": "code_to_symbol", "difficulty": "basic", "language": "python", "symbols_used": ["⟨", "⟩", "α", "β", "⊖", "⤴"]}
{"instruction": "Converti questa funzione Python in neuroglifi", "input": "def multiply(a, b):\n    return a * b", "output": "⟨⟩α⊗β⤴α⊗β", "task_type": "code_to_symbol", "difficulty": "basic", "language": "python", "symbols_used": ["⟨", "⟩", "α", "β", "⊗", "⤴"]}
{"instruction": "Converti questa funzione Python in neuroglifi", "input": "def multiply(a, b):\n    return a * b", "output": "⟨⟩α⊗β⤴α⊗β", "task_type": "code_to_symbol", "difficulty": "basic", "language": "python", "symbols_used": ["⟨", "⟩", "α", "β", "⊗", "⤴"]}
{"instruction": "Traduci questi neuroglifi in una funzione Python per power", "input": "⟨⟩α^β⤴α^β", "output": "def power(a, b):\n    return a ** b", "task_type": "symbol_to_code", "difficulty": "basic", "language": "python", "symbols_used": ["⟨", "⟩", "α", "β", "^", "⤴"], "validation_code": "assert power(5, 3) == 5 ** 3"}
{"instruction": "Traduci questa struttura condizionale in Python", "input": "⟨⟩◊α>β⤴α◈⤴β", "output": "def max_value(a, b):\n    if a > b:\n        return a\n    else:\n        return b", "task_type": "symbol_to_code", "difficulty": "intermediate", "language": "python", "reasoning_steps": ["1. ⟨⟩ indica definizione di funzione", "2. ◊ indica condizione if", "3. α>β è la condizione di confronto", "4. ⤴α è il ramo then (return a)", "5. ◈⤴β è il ramo else (return b)"], "symbols_used": ["⟨", "⟩", "◊", ">", "⤴", "◈", "α", "β"]}
{"instruction": "Converti questa funzione Python in neuroglifi", "input": "def multiply(a, b):\n    return a * b", "output": "⟨⟩α⊗β⤴α⊗β", "task_type": "code_to_symbol", "difficulty": "basic", "language": "python", "symbols_used": ["⟨", "⟩", "α", "β", "⊗", "⤴"]}
{"instruction": "Traduci questi neuroglifi in una funzione Python per add", "input": "⟨⟩α⊕β⤴α⊕β", "output": "def add(a, b):\n    return a + b", "task_type": "symbol_to_code", "difficulty": "basic", "language": "python", "symbols_used": ["⟨", "⟩", "α", "β", "⊕", "⤴"], "validation_code": "assert add(5, 3) == 5 + 3"}
{"instruction": "Traduci questi neuroglifi in una funzione Python per add", "input": "⟨⟩α⊕β⤴α⊕β", "output": "def add(a, b):\n    return a + b", "task_type": "symbol_to_code", "difficulty": "basic", "language": "python", "symbols_used": ["⟨", "⟩", "α", "β", "⊕", "⤴"], "validation_code": "assert add(5, 3) == 5 + 3"}
{"instruction": "Implementa pattern matching dai neuroglifi", "input": "⟨⟩ƒ⟨data⟩◊data∈ℤ⤴'number'◈◊data∈𝕊⤴'string'◈◊data∈[]⤴'list'◈⤴'unknown'", "output": "def type_matcher(data):\n    if isinstance(data, int):\n        return 'number'\n    elif isinstance(data, str):\n        return 'string'\n    elif isinstance(data, list):\n        return 'list'\n    else:\n        return 'unknown'", "task_type": "symbol_to_code", "difficulty": "expert", "language": "python", "reasoning_steps": ["1. ⟨⟩ƒ⟨data⟩ definisce funzione type_matcher", "2. ◊data∈ℤ controlla se è intero", "3. ◊data∈𝕊 controlla se è stringa", "4. ◊data∈[] controlla se è lista", "5. Ultimo ⤴'unknown' è caso default"], "symbols_used": ["⟨", "⟩", "ƒ", "◊", "∈", "ℤ", "𝕊", "[]", "⤴", "◈"]}
{"instruction": "Traduci questi neuroglifi in una funzione Python per power", "input": "⟨⟩α^β⤴α^β", "output": "def power(a, b):\n    return a ** b", "task_type": "symbol_to_code", "difficulty": "basic", "language": "python", "symbols_used": ["⟨", "⟩", "α", "β", "^", "⤴"], "validation_code": "assert power(5, 3) == 5 ** 3"}
{"instruction": "Converti questa funzione Python in neuroglifi", "input": "def multiply(a, b):\n    return a * b", "output": "⟨⟩α⊗β⤴α⊗β", "task_type": "code_to_symbol", "difficulty": "basic", "language": "python", "symbols_used": ["⟨", "⟩", "α", "β", "⊗", "⤴"]}
{"instruction": "Converti questa funzione Python in neuroglifi", "input": "def modulo(a, b):\n    return a % b", "output": "⟨⟩α%β⤴α%β", "task_type": "code_to_symbol", "difficulty": "basic", "language": "python", "symbols_used": ["⟨", "⟩", "α", "β", "%", "⤴"]}
{"instruction": "Converti questa funzione Python in neuroglifi", "input": "def power(a, b):\n    return a ** b", "output": "⟨⟩α^β⤴α^β", "task_type": "code_to_symbol", "difficulty": "basic", "language": "python", "symbols_used": ["⟨", "⟩", "α", "β", "^", "⤴"]}
{"instruction": "Traduci questi neuroglifi in una funzione Python per power", "input": "⟨⟩α^β⤴α^β", "output": "def power(a, b):\n    return a ** b", "task_type": "symbol_to_code", "difficulty": "basic", "language": "python", "symbols_used": ["⟨", "⟩", "α", "β", "^", "⤴"], "validation_code": "assert power(5, 3) == 5 ** 3"}
{"instruction": "Traduci questi neuroglifi in una funzione Python per add", "input": "⟨⟩α⊕β⤴α⊕β", "output": "def add(a, b):\n    return a + b", "task_type": "symbol_to_code", "difficulty": "basic", "language": "python", "symbols_used": ["⟨", "⟩", "α", "β", "⊕", "⤴"], "validation_code": "assert add(5, 3) == 5 + 3"}
{"instruction": "Traduci questi neuroglifi in una funzione Python per multiply", "input": "⟨⟩α⊗β⤴α⊗β", "output": "def multiply(a, b):\n    return a * b", "task_type": "symbol_to_code", "difficulty": "basic", "language": "python", "symbols_used": ["⟨", "⟩", "α", "β", "⊗", "⤴"], "validation_code": "assert multiply(5, 3) == 5 * 3"}
{"instruction": "Traduci questi neuroglifi in una funzione Python per modulo", "input": "⟨⟩α%β⤴α%β", "output": "def modulo(a, b):\n    return a % b", "task_type": "symbol_to_code", "difficulty": "basic", "language": "python", "symbols_used": ["⟨", "⟩", "α", "β", "%", "⤴"], "validation_code": "assert modulo(5, 3) == 5 % 3"}
{"instruction": "Converti questa funzione Python in neuroglifi", "input": "def power(a, b):\n    return a ** b", "output": "⟨⟩α^β⤴α^β", "task_type": "code_to_symbol", "difficulty": "basic", "language": "python", "symbols_used": ["⟨", "⟩", "α", "β", "^", "⤴"]}
{"instruction": "Converti questa funzione Python in neuroglifi", "input": "def power(a, b):\n    return a ** b", "output": "⟨⟩α^β⤴α^β", "task_type": "code_to_symbol", "difficulty": "basic", "language": "python", "symbols_used": ["⟨", "⟩", "α", "β", "^", "⤴"]}
{"instruction": "Converti questa funzione Python in neuroglifi", "input": "def modulo(a, b):\n    return a % b", "output": "⟨⟩α%β⤴α%β", "task_type": "code_to_symbol", "difficulty": "basic", "language": "python", "symbols_used": ["⟨", "⟩", "α", "β", "%", "⤴"]}
{"instruction": "Converti questa funzione Python in neuroglifi", "input": "def add(a, b):\n    return a + b", "output": "⟨⟩α⊕β⤴α⊕β", "task_type": "code_to_symbol", "difficulty": "basic", "language": "python", "symbols_used": ["⟨", "⟩", "α", "β", "⊕", "⤴"]}
{"instruction": "Converti questa funzione Python in neuroglifi", "input": "def subtract(a, b):\n    return a - b", "output": "⟨⟩α⊖β⤴α⊖β", "task_type": "code_to_symbol", "difficulty": "basic", "language": "python", "symbols_used": ["⟨", "⟩", "α", "β", "⊖", "⤴"]}
{"instruction": "Traduci questi neuroglifi in una funzione Python per modulo", "input": "⟨⟩α%β⤴α%β", "output": "def modulo(a, b):\n    return a % b", "task_type": "symbol_to_code", "difficulty": "basic", "language": "python", "symbols_used": ["⟨", "⟩", "α", "β", "%", "⤴"], "validation_code": "assert modulo(5, 3) == 5 % 3"}
{"instruction": "Converti questa funzione Python in neuroglifi", "input": "def add(a, b):\n    return a + b", "output": "⟨⟩α⊕β⤴α⊕β", "task_type": "code_to_symbol", "difficulty": "basic", "language": "python", "symbols_used": ["⟨", "⟩", "α", "β", "⊕", "⤴"]}
{"instruction": "Converti questa funzione Python in neuroglifi", "input": "def power(a, b):\n    return a ** b", "output": "⟨⟩α^β⤴α^β", "task_type": "code_to_symbol", "difficulty": "basic", "language": "python", "symbols_used": ["⟨", "⟩", "α", "β", "^", "⤴"]}
{"instruction": "Traduci questi neuroglifi in una funzione Python per modulo", "input": "⟨⟩α%β⤴α%β", "output": "def modulo(a, b):\n    return a % b", "task_type": "symbol_to_code", "difficulty": "basic", "language": "python", "symbols_used": ["⟨", "⟩", "α", "β", "%", "⤴"], "validation_code": "assert modulo(5, 3) == 5 % 3"}
{"instruction": "Converti questa funzione Python in neuroglifi", "input": "def multiply(a, b):\n    return a * b", "output": "⟨⟩α⊗β⤴α⊗β", "task_type": "code_to_symbol", "difficulty": "basic", "language": "python", "symbols_used": ["⟨", "⟩", "α", "β", "⊗", "⤴"]}
{"instruction": "Traduci questi neuroglifi in una funzione Python per modulo", "input": "⟨⟩α%β⤴α%β", "output": "def modulo(a, b):\n    return a % b", "task_type": "symbol_to_code", "difficulty": "basic", "language": "python", "symbols_used": ["⟨", "⟩", "α", "β", "%", "⤴"], "validation_code": "assert modulo(5, 3) == 5 % 3"}
{"instruction": "Traduci questi neuroglifi in una funzione Python per divide", "input": "⟨⟩α⊘β⤴α⊘β", "output": "def divide(a, b):\n    return a / b", "task_type": "symbol_to_code", "difficulty": "basic", "language": "python", "symbols_used": ["⟨", "⟩", "α", "β", "⊘", "⤴"], "validation_code": "assert divide(5, 3) == 5 / 3"}
{"instruction": "Traduci questi neuroglifi in una funzione Python per subtract", "input": "⟨⟩α⊖β⤴α⊖β", "output": "def subtract(a, b):\n    return a - b", "task_type": "symbol_to_code", "difficulty": "basic", "language": "python", "symbols_used": ["⟨", "⟩", "α", "β", "⊖", "⤴"], "validation_code": "assert subtract(5, 3) == 5 - 3"}
{"instruction": "Traduci questi neuroglifi in una funzione Python per add", "input": "⟨⟩α⊕β⤴α⊕β", "output": "def add(a, b):\n    return a + b", "task_type": "symbol_to_code", "difficulty": "basic", "language": "python", "symbols_used": ["⟨", "⟩", "α", "β", "⊕", "⤴"], "validation_code": "assert add(5, 3) == 5 + 3"}
{"instruction": "Traduci questi neuroglifi in una funzione Python per multiply", "input": "⟨⟩α⊗β⤴α⊗β", "output": "def multiply(a, b):\n    return a * b", "task_type": "symbol_to_code", "difficulty": "basic", "language": "python", "symbols_used": ["⟨", "⟩", "α", "β", "⊗", "⤴"], "validation_code": "assert multiply(5, 3) == 5 * 3"}
{"instruction": "Converti questa funzione Python in neuroglifi", "input": "def modulo(a, b):\n    return a % b", "output": "⟨⟩α%β⤴α%β", "task_type": "code_to_symbol", "difficulty": "basic", "language": "python", "symbols_used": ["⟨", "⟩", "α", "β", "%", "⤴"]}
{"instruction": "Traduci questi neuroglifi in una funzione Python per multiply", "input": "⟨⟩α⊗β⤴α⊗β", "output": "def multiply(a, b):\n    return a * b", "task_type": "symbol_to_code", "difficulty": "basic", "language": "python", "symbols_used": ["⟨", "⟩", "α", "β", "⊗", "⤴"], "validation_code": "assert multiply(5, 3) == 5 * 3"}
{"instruction": "Traduci questi neuroglifi in una funzione Python per power", "input": "⟨⟩α^β⤴α^β", "output": "def power(a, b):\n    return a ** b", "task_type": "symbol_to_code", "difficulty": "basic", "language": "python", "symbols_used": ["⟨", "⟩", "α", "β", "^", "⤴"], "validation_code": "assert power(5, 3) == 5 ** 3"}
{"instruction": "Traduci questi neuroglifi in una funzione Python per divide", "input": "⟨⟩α⊘β⤴α⊘β", "output": "def divide(a, b):\n    return a / b", "task_type": "symbol_to_code", "difficulty": "basic", "language": "python", "symbols_used": ["⟨", "⟩", "α", "β", "⊘", "⤴"], "validation_code": "assert divide(5, 3) == 5 / 3"}
{"instruction": "Converti questa funzione Python in neuroglifi", "input": "def modulo(a, b):\n    return a % b", "output": "⟨⟩α%β⤴α%β", "task_type": "code_to_symbol", "difficulty": "basic", "language": "python", "symbols_used": ["⟨", "⟩", "α", "β", "%", "⤴"]}
{"instruction": "Traduci questi neuroglifi in una funzione Python per multiply", "input": "⟨⟩α⊗β⤴α⊗β", "output": "def multiply(a, b):\n    return a * b", "task_type": "symbol_to_code", "difficulty": "basic", "language": "python", "symbols_used": ["⟨", "⟩", "α", "β", "⊗", "⤴"], "validation_code": "assert multiply(5, 3) == 5 * 3"}
{"instruction": "Traduci questi neuroglifi in una funzione Python per add", "input": "⟨⟩α⊕β⤴α⊕β", "output": "def add(a, b):\n    return a + b", "task_type": "symbol_to_code", "difficulty": "basic", "language": "python", "symbols_used": ["⟨", "⟩", "α", "β", "⊕", "⤴"], "validation_code": "assert add(5, 3) == 5 + 3"}
{"instruction": "Traduci questi neuroglifi in una funzione Python per modulo", "input": "⟨⟩α%β⤴α%β", "output": "def modulo(a, b):\n    return a % b", "task_type": "symbol_to_code", "difficulty": "basic", "language": "python", "symbols_used": ["⟨", "⟩", "α", "β", "%", "⤴"], "validation_code": "assert modulo(5, 3) == 5 % 3"}
{"instruction": "Implementa questo ciclo dai neuroglifi", "input": "⟨⟩⟳i∈[1..n]⊞αᵢ", "output": "def sum_range(n):\n    result = []\n    for i in range(1, n+1):\n        result.append(i)\n    return result", "task_type": "symbol_to_code", "difficulty": "intermediate", "language": "python", "reasoning_steps": ["1. ⟨⟩ definisce funzione", "2. ⟳ indica ciclo for", "3. i∈[1..n] è il range di iterazione", "4. ⊞αᵢ significa append dell'elemento i"], "symbols_used": ["⟨", "⟩", "⟳", "∈", "⊞", "α"]}
{"instruction": "Traduci questi neuroglifi in una funzione Python per divide", "input": "⟨⟩α⊘β⤴α⊘β", "output": "def divide(a, b):\n    return a / b", "task_type": "symbol_to_code", "difficulty": "basic", "language": "python", "symbols_used": ["⟨", "⟩", "α", "β", "⊘", "⤴"], "validation_code": "assert divide(5, 3) == 5 / 3"}
{"instruction": "Converti questa funzione Python in neuroglifi", "input": "def power(a, b):\n    return a ** b", "output": "⟨⟩α^β⤴α^β", "task_type": "code_to_symbol", "difficulty": "basic", "language": "python", "symbols_used": ["⟨", "⟩", "α", "β", "^", "⤴"]}
{"instruction": "Traduci questi neuroglifi in una funzione Python per multiply", "input": "⟨⟩α⊗β⤴α⊗β", "output": "def multiply(a, b):\n    return a * b", "task_type": "symbol_to_code", "difficulty": "basic", "language": "python", "symbols_used": ["⟨", "⟩", "α", "β", "⊗", "⤴"], "validation_code": "assert multiply(5, 3) == 5 * 3"}
{"instruction": "Traduci questi neuroglifi in una funzione Python per divide", "input": "⟨⟩α⊘β⤴α⊘β", "output": "def divide(a, b):\n    return a / b", "task_type": "symbol_to_code", "difficulty": "basic", "language": "python", "symbols_used": ["⟨", "⟩", "α", "β", "⊘", "⤴"], "validation_code": "assert divide(5, 3) == 5 / 3"}
{"instruction": "Traduci questi neuroglifi in una funzione Python per modulo", "input": "⟨⟩α%β⤴α%β", "output": "def modulo(a, b):\n    return a % b", "task_type": "symbol_to_code", "difficulty": "basic", "language": "python", "symbols_used": ["⟨", "⟩", "α", "β", "%", "⤴"], "validation_code": "assert modulo(5, 3) == 5 % 3"}
{"instruction": "Traduci questi neuroglifi in una funzione Python per power", "input": "⟨⟩α^β⤴α^β", "output": "def power(a, b):\n    return a ** b", "task_type": "symbol_to_code", "difficulty": "basic", "language": "python", "symbols_used": ["⟨", "⟩", "α", "β", "^", "⤴"], "validation_code": "assert power(5, 3) == 5 ** 3"}
{"instruction": "Traduci questi neuroglifi in una funzione Python per power", "input": "⟨⟩α^β⤴α^β", "output": "def power(a, b):\n    return a ** b", "task_type": "symbol_to_code", "difficulty": "basic", "language": "python", "symbols_used": ["⟨", "⟩", "α", "β", "^", "⤴"], "validation_code": "assert power(5, 3) == 5 ** 3"}
{"instruction": "Converti questa funzione Python in neuroglifi", "input": "def multiply(a, b):\n    return a * b", "output": "⟨⟩α⊗β⤴α⊗β", "task_type": "code_to_symbol", "difficulty": "basic", "language": "python", "symbols_used": ["⟨", "⟩", "α", "β", "⊗", "⤴"]}
{"instruction": "Traduci questi neuroglifi in una funzione Python per power", "input": "⟨⟩α^β⤴α^β", "output": "def power(a, b):\n    return a ** b", "task_type": "symbol_to_code", "difficulty": "basic", "language": "python", "symbols_used": ["⟨", "⟩", "α", "β", "^", "⤴"], "validation_code": "assert power(5, 3) == 5 ** 3"}
{"instruction": "Implementa la sequenza di Fibonacci con ragionamento step-by-step", "input": "⟨⟩ƒ⟨n⟩◊n≤1⤴n◈⤴ƒ⟨n⊖1⟩⊕ƒ⟨n⊖2⟩", "output": "def fibonacci(n):\n    if n <= 1:\n        return n\n    return fibonacci(n-1) + fibonacci(n-2)", "task_type": "chain_of_thought", "difficulty": "intermediate", "language": "python", "reasoning_steps": ["1. ⟨⟩ƒ⟨n⟩ definisce funzione fibonacci con parametro n", "2. ◊n≤1 controlla caso base (n <= 1)", "3. ⤴n ritorna n per caso base", "4. ◈ indica ramo else", "5. ƒ⟨n⊖1⟩⊕ƒ⟨n⊖2⟩ chiama ricorsivamente fib(n-1) + fib(n-2)"], "symbols_used": ["⟨", "⟩", "ƒ", "◊", "≤", "⤴", "◈", "⊖", "⊕"]}
{"instruction": "Converti questa funzione Python in neuroglifi", "input": "def subtract(a, b):\n    return a - b", "output": "⟨⟩α⊖β⤴α⊖β", "task_type": "code_to_symbol", "difficulty": "basic", "language": "python", "symbols_used": ["⟨", "⟩", "α", "β", "⊖", "⤴"]}
{"instruction": "Converti questa funzione Python in neuroglifi", "input": "def add(a, b):\n    return a + b", "output": "⟨⟩α⊕β⤴α⊕β", "task_type": "code_to_symbol", "difficulty": "basic", "language": "python", "symbols_used": ["⟨", "⟩", "α", "β", "⊕", "⤴"]}
{"instruction": "Converti questa funzione Python in neuroglifi", "input": "def modulo(a, b):\n    return a % b", "output": "⟨⟩α%β⤴α%β", "task_type": "code_to_symbol", "difficulty": "basic", "language": "python", "symbols_used": ["⟨", "⟩", "α", "β", "%", "⤴"]}
{"instruction": "Traduci questi neuroglifi in una funzione Python per subtract", "input": "⟨⟩α⊖β⤴α⊖β", "output": "def subtract(a, b):\n    return a - b", "task_type": "symbol_to_code", "difficulty": "basic", "language": "python", "symbols_used": ["⟨", "⟩", "α", "β", "⊖", "⤴"], "validation_code": "assert subtract(5, 3) == 5 - 3"}
{"instruction": "Converti questa funzione Python in neuroglifi", "input": "def multiply(a, b):\n    return a * b", "output": "⟨⟩α⊗β⤴α⊗β", "task_type": "code_to_symbol", "difficulty": "basic", "language": "python", "symbols_used": ["⟨", "⟩", "α", "β", "⊗", "⤴"]}
{"instruction": "Traduci questi neuroglifi in una funzione Python per divide", "input": "⟨⟩α⊘β⤴α⊘β", "output": "def divide(a, b):\n    return a / b", "task_type": "symbol_to_code", "difficulty": "basic", "language": "python", "symbols_used": ["⟨", "⟩", "α", "β", "⊘", "⤴"], "validation_code": "assert divide(5, 3) == 5 / 3"}
{"instruction": "Traduci questi neuroglifi in una funzione Python per add", "input": "⟨⟩α⊕β⤴α⊕β", "output": "def add(a, b):\n    return a + b", "task_type": "symbol_to_code", "difficulty": "basic", "language": "python", "symbols_used": ["⟨", "⟩", "α", "β", "⊕", "⤴"], "validation_code": "assert add(5, 3) == 5 + 3"}
{"instruction": "Implementa quicksort dai neuroglifi con ragionamento completo", "input": "⟨⟩ƒ⟨arr⟩◊|arr|≤1⤴arr◈⤴⟨pivot=arr[0]⟩⟨left=[α|α∈arr∧α<pivot]⟩⟨right=[α|α∈arr∧α>pivot]⟩ƒ⟨left⟩⊕[pivot]⊕ƒ⟨right⟩", "output": "def quicksort(arr):\n    if len(arr) <= 1:\n        return arr\n    pivot = arr[0]\n    left = [x for x in arr[1:] if x < pivot]\n    right = [x for x in arr[1:] if x > pivot]\n    return quicksort(left) + [pivot] + quicksort(right)", "task_type": "chain_of_thought", "difficulty": "expert", "language": "python", "reasoning_steps": ["1. ⟨⟩ƒ⟨arr⟩ definisce funzione quicksort", "2. ◊|arr|≤1⤴arr caso base: array vuoto o singolo", "3. ⟨pivot=arr[0]⟩ sceglie primo elemento come pivot", "4. ⟨left=[α|α∈arr∧α<pivot]⟩ filtra elementi minori", "5. ⟨right=[α|α∈arr∧α>pivot]⟩ filtra elementi maggiori", "6. ƒ⟨left⟩⊕[pivot]⊕ƒ⟨right⟩ ricombina ricorsivamente"], "symbols_used": ["⟨", "⟩", "ƒ", "◊", "|", "≤", "⤴", "◈", "=", "[", "]", "∈", "∧", "<", ">", "⊕"], "validation_code": "assert quicksort([3,1,4,1,5]) == [1,1,3,4,5]"}
{"instruction": "Converti questa funzione Python in neuroglifi", "input": "def add(a, b):\n    return a + b", "output": "⟨⟩α⊕β⤴α⊕β", "task_type": "code_to_symbol", "difficulty": "basic", "language": "python", "symbols_used": ["⟨", "⟩", "α", "β", "⊕", "⤴"]}
{"instruction": "Traduci questi neuroglifi in una funzione Python per divide", "input": "⟨⟩α⊘β⤴α⊘β", "output": "def divide(a, b):\n    return a / b", "task_type": "symbol_to_code", "difficulty": "basic", "language": "python", "symbols_used": ["⟨", "⟩", "α", "β", "⊘", "⤴"], "validation_code": "assert divide(5, 3) == 5 / 3"}
{"instruction": "Converti questa funzione Python in neuroglifi", "input": "def modulo(a, b):\n    return a % b", "output": "⟨⟩α%β⤴α%β", "task_type": "code_to_symbol", "difficulty": "basic", "language": "python", "symbols_used": ["⟨", "⟩", "α", "β", "%", "⤴"]}
{"instruction": "Traduci questi neuroglifi in una funzione Python per power", "input": "⟨⟩α^β⤴α^β", "output": "def power(a, b):\n    return a ** b", "task_type": "symbol_to_code", "difficulty": "basic", "language": "python", "symbols_used": ["⟨", "⟩", "α", "β", "^", "⤴"], "validation_code": "assert power(5, 3) == 5 ** 3"}
{"instruction": "Converti questa funzione Python in neuroglifi", "input": "def power(a, b):\n    return a ** b", "output": "⟨⟩α^β⤴α^β", "task_type": "code_to_symbol", "difficulty": "basic", "language": "python", "symbols_used": ["⟨", "⟩", "α", "β", "^", "⤴"]}
{"instruction": "Traduci questi neuroglifi in una funzione Python per subtract", "input": "⟨⟩α⊖β⤴α⊖β", "output": "def subtract(a, b):\n    return a - b", "task_type": "symbol_to_code", "difficulty": "basic", "language": "python", "symbols_used": ["⟨", "⟩", "α", "β", "⊖", "⤴"], "validation_code": "assert subtract(5, 3) == 5 - 3"}
{"instruction": "Traduci questi neuroglifi in una funzione Python per modulo", "input": "⟨⟩α%β⤴α%β", "output": "def modulo(a, b):\n    return a % b", "task_type": "symbol_to_code", "difficulty": "basic", "language": "python", "symbols_used": ["⟨", "⟩", "α", "β", "%", "⤴"], "validation_code": "assert modulo(5, 3) == 5 % 3"}
{"instruction": "Converti questa funzione Python in neuroglifi", "input": "def add(a, b):\n    return a + b", "output": "⟨⟩α⊕β⤴α⊕β", "task_type": "code_to_symbol", "difficulty": "basic", "language": "python", "symbols_used": ["⟨", "⟩", "α", "β", "⊕", "⤴"]}
{"instruction": "Traduci questi neuroglifi in una funzione Python per add", "input": "⟨⟩α⊕β⤴α⊕β", "output": "def add(a, b):\n    return a + b", "task_type": "symbol_to_code", "difficulty": "basic", "language": "python", "symbols_used": ["⟨", "⟩", "α", "β", "⊕", "⤴"], "validation_code": "assert add(5, 3) == 5 + 3"}
{"instruction": "Traduci questi neuroglifi in una funzione Python per subtract", "input": "⟨⟩α⊖β⤴α⊖β", "output": "def subtract(a, b):\n    return a - b", "task_type": "symbol_to_code", "difficulty": "basic", "language": "python", "symbols_used": ["⟨", "⟩", "α", "β", "⊖", "⤴"], "validation_code": "assert subtract(5, 3) == 5 - 3"}
{"instruction": "Traduci questi neuroglifi in una funzione Python per subtract", "input": "⟨⟩α⊖β⤴α⊖β", "output": "def subtract(a, b):\n    return a - b", "task_type": "symbol_to_code", "difficulty": "basic", "language": "python", "symbols_used": ["⟨", "⟩", "α", "β", "⊖", "⤴"], "validation_code": "assert subtract(5, 3) == 5 - 3"}
{"instruction": "Converti questa funzione Python in neuroglifi", "input": "def divide(a, b):\n    return a / b", "output": "⟨⟩α⊘β⤴α⊘β", "task_type": "code_to_symbol", "difficulty": "basic", "language": "python", "symbols_used": ["⟨", "⟩", "α", "β", "⊘", "⤴"]}
{"instruction": "Traduci questi neuroglifi in JAVA", "input": "⟨⟩α⊕β⤴α⊕β", "output": "public static int add(int a, int b) {\n    return a + b;\n}", "task_type": "symbol_to_code", "difficulty": "intermediate", "language": "java", "symbols_used": ["⟨", "⟩", "α", "β", "⊕", "⤴"]}
{"instruction": "Traduci questi neuroglifi in una funzione Python per power", "input": "⟨⟩α^β⤴α^β", "output": "def power(a, b):\n    return a ** b", "task_type": "symbol_to_code", "difficulty": "basic", "language": "python", "symbols_used": ["⟨", "⟩", "α", "β", "^", "⤴"], "validation_code": "assert power(5, 3) == 5 ** 3"}
{"instruction": "Implementa questo decoratore dai neuroglifi", "input": "⟨⟩@⟨timer⟩ƒ⟨⟩⤴⊙time⊙ƒ⊙⊙time", "output": "def timer(func):\n    def wrapper(*args, **kwargs):\n        start = time.time()\n        result = func(*args, **kwargs)\n        end = time.time()\n        print(f'Time: {end - start}')\n        return result\n    return wrapper", "task_type": "symbol_to_code", "difficulty": "advanced", "language": "python", "reasoning_steps": ["1. ⟨⟩@⟨timer⟩ definisce decoratore timer", "2. ƒ⟨⟩ è la funzione wrapper", "3. ⊙time⊙ rappresenta misurazione tempo", "4. ƒ è l'esecuzione della funzione originale"], "symbols_used": ["⟨", "⟩", "@", "ƒ", "⤴", "⊙"]}
{"instruction": "Traduci questi neuroglifi in una funzione Python per subtract", "input": "⟨⟩α⊖β⤴α⊖β", "output": "def subtract(a, b):\n    return a - b", "task_type": "symbol_to_code", "difficulty": "basic", "language": "python", "symbols_used": ["⟨", "⟩", "α", "β", "⊖", "⤴"], "validation_code": "assert subtract(5, 3) == 5 - 3"}
{"instruction": "Traduci questi neuroglifi in una funzione Python per multiply", "input": "⟨⟩α⊗β⤴α⊗β", "output": "def multiply(a, b):\n    return a * b", "task_type": "symbol_to_code", "difficulty": "basic", "language": "python", "symbols_used": ["⟨", "⟩", "α", "β", "⊗", "⤴"], "validation_code": "assert multiply(5, 3) == 5 * 3"}
{"instruction": "Traduci questi neuroglifi in una funzione Python per subtract", "input": "⟨⟩α⊖β⤴α⊖β", "output": "def subtract(a, b):\n    return a - b", "task_type": "symbol_to_code", "difficulty": "basic", "language": "python", "symbols_used": ["⟨", "⟩", "α", "β", "⊖", "⤴"], "validation_code": "assert subtract(5, 3) == 5 - 3"}
