{"instruction": "Implementa la funzione rappresentata dai neuroglifi", "input": "⟨⟩α⊖β⤴α⊖β", "output": "def subtract(a, b):\n    return a - b"}
{"instruction": "Genera codice Python dai neuroglifi", "input": "⟨⟩α⊘β⤴α⊘β", "output": "def divide(a, b):\n    return a / b"}
{"instruction": "Implementa il ciclo rappresentato dai neuroglifi", "input": "⟨⟩∑ᵢ₌₁ⁿαᵢ", "output": "def sum_range(n):\n    return sum(range(1, n+1))"}
{"instruction": "Implementa la struttura condizionale dai neuroglifi", "input": "⟨⟩α≤1?α:ƒ⟨α-1⟩", "output": "def factorial(n):\n    return n if n <= 1 else factorial(n-1)"}
{"instruction": "Converti in notazione neuroglifo", "input": "def max_of_three(a, b, c):\n    return max(a, b, c)", "output": "⟨⟩max(α,β,γ)⤴max(α,β,γ)"}
{"instruction": "Converti simboli in funzione condizionale", "input": "⟨⟩α<β?α:β", "output": "def min_value(a, b):\n    return a if a < b else b"}
{"instruction": "Implementa la funzione logica dai neuroglifi", "input": "⟨⟩α∧β⤴α∧β", "output": "def logical_and(a, b):\n    return a and b"}
{"instruction": "Rappresenta questa funzione con neuroglifi", "input": "def is_even(n):\n    return n % 2 == 0", "output": "⟨⟩α%2=0⤴α%2=0"}
{"instruction": "Traduci neuroglifi in operazione lista", "input": "⟨⟩[α₁,α₂,...,αₙ]→α₁", "output": "def first_element(lst):\n    return lst[0] if lst else None"}
{"instruction": "Implementa operazione su lista dai neuroglifi", "input": "⟨⟩[α₁,α₂,...,αₙ]→|α|", "output": "def list_length(lst):\n    return len(lst)"}
{"instruction": "Implementa l'algoritmo di ordinamento dai neuroglifi", "input": "⟨⟩[α]→sort([α])", "output": "def bubble_sort(arr):\n    n = len(arr)\n    for i in range(n):\n        for j in range(0, n-i-1):\n            if arr[j] > arr[j+1]:\n                arr[j], arr[j+1] = arr[j+1], arr[j]\n    return arr"}
{"instruction": "Implementa operazione dizionario dai neuroglifi", "input": "⟨⟩{κ:ν}→ν[κ]", "output": "def get_value(dictionary, key):\n    return dictionary.get(key)"}
{"instruction": "Traduci i simboli neuroglifi in codice Python", "input": "⟨⟩α⊗β⤴α⊗β", "output": "def multiply(a, b):\n    return a * b"}
{"instruction": "Traduci neuroglifi in operazione chiavi", "input": "⟨⟩{κ:ν}→[κ]", "output": "def get_keys(dictionary):\n    return list(dictionary.keys())"}
{"instruction": "Traduci neuroglifi in ricerca binaria", "input": "⟨⟩search(α,β)→idx", "output": "def binary_search(arr, target):\n    left, right = 0, len(arr) - 1\n    while left <= right:\n        mid = (left + right) // 2\n        if arr[mid] == target:\n            return mid\n        elif arr[mid] < target:\n            left = mid + 1\n        else:\n            right = mid - 1\n    return -1"}
{"instruction": "Converti neuroglifi in funzione Python", "input": "⟨⟩α^β⤴α**β", "output": "def power(a, b):\n    return a ** b"}
{"instruction": "Converti questa funzione Python in neuroglifi", "input": "def add(a, b):\n    return a + b", "output": "⟨⟩α⊕β⤴α⊕β"}
{"instruction": "Traduci neuroglifi in controllo condizionale", "input": "⟨⟩α>β?α:β", "output": "def max_value(a, b):\n    return a if a > b else b"}
{"instruction": "Traduci neuroglifi in calcolo fattoriale", "input": "⟨⟩n!⤴n≤1?1:n×(n-1)!", "output": "def factorial(n):\n    if n <= 1:\n        return 1\n    return n * factorial(n-1)"}
{"instruction": "Converti simboli in funzione di negazione", "input": "⟨⟩¬α⤴¬α", "output": "def logical_not(a):\n    return not a"}
{"instruction": "Traduci il codice Python in simboli neuroglifi", "input": "def multiply(x, y):\n    return x * y", "output": "⟨⟩α⊗β⤴α⊗β"}
{"instruction": "Converti simboli in funzione lista", "input": "⟨⟩[α₁,α₂,...,αₙ]→αₙ", "output": "def last_element(lst):\n    return lst[-1] if lst else None"}
{"instruction": "Implementa la sequenza di Fibonacci dai neuroglifi", "input": "⟨⟩ƒ⟨n⟩⤴n≤1?n:ƒ⟨n-1⟩⊕ƒ⟨n-2⟩", "output": "def fibonacci(n):\n    if n <= 1:\n        return n\n    return fibonacci(n-1) + fibonacci(n-2)"}
{"instruction": "Genera codice Python dai neuroglifi.", "input": "⟨⟩α⊕β⤴α⊕β", "output": "def add(a, b):\n    return a + b", "task_type": "symbol_to_code", "difficulty": "easy", "language": "python"}
{"instruction": "Traduci la seguente funzione Python in simboli neuroglifi.", "input": "def subtract(a, b):\n    return a - b", "output": "⟨⟩α⊖β⤴α⊖β", "task_type": "code_to_symbol", "difficulty": "easy", "language": "python"}
{"instruction": "Spiega cosa fa questa sequenza di neuroglifi.", "input": "⟨⟩◊α≤1⤴1◈⤴α⊗ƒ⟨α⊖1⟩", "output": "Calcola il fattoriale di α in modo ricorsivo.", "task_type": "symbol_reasoning", "difficulty": "medium", "language": "symbol"}
{"instruction": "Traduci questi neuroglifi in codice Rust.", "input": "⟨⟩α⊗β⤴α⊗β", "output": "fn multiply(a: i32, b: i32) -> i32 {\n    a * b\n}", "task_type": "symbol_to_code", "difficulty": "easy", "language": "rust"}
{"instruction": "Cosa restituisce questa funzione simbolica se α=7 e β=3?", "input": "⟨⟩α⊖β⤴α⊖β", "output": "4", "task_type": "symbol_reasoning", "difficulty": "easy", "language": "symbol"}
{"instruction": "Traduci questa funzione Python in simboli neuroglifi.", "input": "def max_value(a, b):\n    return a if a > b else b", "output": "⟨⟩◊α>β⤴α◈⤴β", "task_type": "code_to_symbol", "difficulty": "medium", "language": "python"}
{"instruction": "Traduci questi neuroglifi in codice Python.", "input": "⟨⟩[α₁,α₂,...,αₙ]→|α|", "output": "def list_length(lst):\n    return len(lst)", "task_type": "symbol_to_code", "difficulty": "easy", "language": "python"}
{"instruction": "Traduci questa funzione Python in simboli neuroglifi.", "input": "def logical_not(a):\n    return not a", "output": "⟨⟩¬α⤴¬α", "task_type": "code_to_symbol", "difficulty": "easy", "language": "python"}
{"instruction": "Spiega cosa fa questa funzione simbolica.", "input": "⟨⟩⟪⟫μ⟐α⟑◊⟦⟧⊕", "output": "Definisce una classe con metodo che somma una lista se una condizione è verificata.", "task_type": "symbol_reasoning", "difficulty": "hard", "language": "symbol"}
{"instruction": "Traduci questi neuroglifi in codice JavaScript.", "input": "⟨⟩α⊘β⤴α⊘β", "output": "function divide(a, b) {\n    return a / b;\n}", "task_type": "symbol_to_code", "difficulty": "easy", "language": "js"}
