#!/usr/bin/env python3
"""
NEUROGLYPH LLM - Enhanced Dataset Generator
==========================================

Genera dataset migliorato con:
- Simboli standardizzati e consistenti
- Progressività di difficoltà
- Chain-of-thought reasoning
- Esempi multi-linguaggio
- Validazione semantica
"""

import json
import random
from typing import List, Dict, Any, Tuple
from pathlib import Path
from dataclasses import dataclass
from enum import Enum

class DifficultyLevel(Enum):
    BASIC = "basic"
    INTERMEDIATE = "intermediate"
    ADVANCED = "advanced"
    EXPERT = "expert"

class TaskType(Enum):
    SYMBOL_TO_CODE = "symbol_to_code"
    CODE_TO_SYMBOL = "code_to_symbol"
    SYMBOL_REASONING = "symbol_reasoning"
    CHAIN_OF_THOUGHT = "chain_of_thought"
    MULTI_STEP = "multi_step"
    DEBUGGING = "debugging"

@dataclass
class NeuroglyphExample:
    instruction: str
    input: str
    output: str
    task_type: TaskType
    difficulty: DifficultyLevel
    language: str
    reasoning_steps: List[str] = None
    symbols_used: List[str] = None
    validation_code: str = None

class EnhancedNeuroglyphDataset:
    """Generatore dataset NEUROGLYPH migliorato"""
    
    def __init__(self):
        # Simboli standardizzati
        self.symbols = {
            # Operatori aritmetici
            "add": "⊕", "subtract": "⊖", "multiply": "⊗", "divide": "⊘", "power": "^",
            "modulo": "%", "equals": "=", "not_equals": "≠",
            
            # Operatori logici
            "and": "∧", "or": "∨", "not": "¬", "xor": "⊻",
            
            # Comparatori
            "greater": ">", "less": "<", "greater_equal": "≥", "less_equal": "≤",
            
            # Strutture di controllo
            "if": "◊", "then": "⤴", "else": "◈", "endif": "◇",
            "while": "⟲", "for": "⟳", "break": "⊣", "continue": "⊢",
            
            # Funzioni e scope
            "function": "ƒ", "lambda": "λ", "return": "⤴", "yield": "⤵",
            "scope_start": "⟨", "scope_end": "⟩", "param": "α", "param2": "β", "param3": "γ",
            
            # Strutture dati
            "list": "[]", "dict": "{}", "set": "∅", "tuple": "()",
            "append": "⊞", "remove": "⊟", "length": "|·|", "index": "→",
            
            # Tipi
            "int": "ℤ", "float": "ℝ", "string": "𝕊", "bool": "𝔹", "none": "∅",
            
            # Classi e oggetti
            "class": "⟪", "method": "⟫", "property": "⟦", "constructor": "⟧",
            "self": "⊙", "super": "⊚", "instance": "⊛",
            
            # Iteratori e generatori
            "map": "⟼", "filter": "⟽", "reduce": "⟾", "zip": "⟿",
            
            # Eccezioni
            "try": "⊳", "except": "⊴", "finally": "⊵", "raise": "⊶"
        }
        
        self.examples = []
    
    def generate_basic_examples(self) -> List[NeuroglyphExample]:
        """Genera esempi base con simboli standardizzati"""
        examples = []
        
        # Operazioni aritmetiche base
        arithmetic_ops = [
            ("add", "⊕", "def add(a, b):\n    return a + b"),
            ("subtract", "⊖", "def subtract(a, b):\n    return a - b"),
            ("multiply", "⊗", "def multiply(a, b):\n    return a * b"),
            ("divide", "⊘", "def divide(a, b):\n    return a / b"),
            ("power", "^", "def power(a, b):\n    return a ** b"),
            ("modulo", "%", "def modulo(a, b):\n    return a % b")
        ]
        
        for op_name, symbol, code in arithmetic_ops:
            # Symbol to Code
            examples.append(NeuroglyphExample(
                instruction=f"Traduci questi neuroglifi in una funzione Python per {op_name}",
                input=f"⟨⟩α{symbol}β⤴α{symbol}β",
                output=code,
                task_type=TaskType.SYMBOL_TO_CODE,
                difficulty=DifficultyLevel.BASIC,
                language="python",
                symbols_used=["⟨", "⟩", "α", "β", symbol, "⤴"],
                validation_code=f"assert {op_name}(5, 3) == 5 {symbol.replace('⊕', '+').replace('⊖', '-').replace('⊗', '*').replace('⊘', '/').replace('^', '**')} 3"
            ))
            
            # Code to Symbol
            examples.append(NeuroglyphExample(
                instruction=f"Converti questa funzione Python in neuroglifi",
                input=code,
                output=f"⟨⟩α{symbol}β⤴α{symbol}β",
                task_type=TaskType.CODE_TO_SYMBOL,
                difficulty=DifficultyLevel.BASIC,
                language="python",
                symbols_used=["⟨", "⟩", "α", "β", symbol, "⤴"]
            ))
        
        return examples
    
    def generate_intermediate_examples(self) -> List[NeuroglyphExample]:
        """Genera esempi intermedi con controllo di flusso"""
        examples = []
        
        # Condizionali
        examples.append(NeuroglyphExample(
            instruction="Traduci questa struttura condizionale in Python",
            input="⟨⟩◊α>β⤴α◈⤴β",
            output="def max_value(a, b):\n    if a > b:\n        return a\n    else:\n        return b",
            task_type=TaskType.SYMBOL_TO_CODE,
            difficulty=DifficultyLevel.INTERMEDIATE,
            language="python",
            reasoning_steps=[
                "1. ⟨⟩ indica definizione di funzione",
                "2. ◊ indica condizione if",
                "3. α>β è la condizione di confronto",
                "4. ⤴α è il ramo then (return a)",
                "5. ◈⤴β è il ramo else (return b)"
            ],
            symbols_used=["⟨", "⟩", "◊", ">", "⤴", "◈", "α", "β"]
        ))
        
        # Cicli
        examples.append(NeuroglyphExample(
            instruction="Implementa questo ciclo dai neuroglifi",
            input="⟨⟩⟳i∈[1..n]⊞αᵢ",
            output="def sum_range(n):\n    result = []\n    for i in range(1, n+1):\n        result.append(i)\n    return result",
            task_type=TaskType.SYMBOL_TO_CODE,
            difficulty=DifficultyLevel.INTERMEDIATE,
            language="python",
            reasoning_steps=[
                "1. ⟨⟩ definisce funzione",
                "2. ⟳ indica ciclo for",
                "3. i∈[1..n] è il range di iterazione",
                "4. ⊞αᵢ significa append dell'elemento i"
            ],
            symbols_used=["⟨", "⟩", "⟳", "∈", "⊞", "α"]
        ))
        
        # Fibonacci con reasoning
        examples.append(NeuroglyphExample(
            instruction="Implementa la sequenza di Fibonacci con ragionamento step-by-step",
            input="⟨⟩ƒ⟨n⟩◊n≤1⤴n◈⤴ƒ⟨n⊖1⟩⊕ƒ⟨n⊖2⟩",
            output="def fibonacci(n):\n    if n <= 1:\n        return n\n    return fibonacci(n-1) + fibonacci(n-2)",
            task_type=TaskType.CHAIN_OF_THOUGHT,
            difficulty=DifficultyLevel.INTERMEDIATE,
            language="python",
            reasoning_steps=[
                "1. ⟨⟩ƒ⟨n⟩ definisce funzione fibonacci con parametro n",
                "2. ◊n≤1 controlla caso base (n <= 1)",
                "3. ⤴n ritorna n per caso base",
                "4. ◈ indica ramo else",
                "5. ƒ⟨n⊖1⟩⊕ƒ⟨n⊖2⟩ chiama ricorsivamente fib(n-1) + fib(n-2)"
            ],
            symbols_used=["⟨", "⟩", "ƒ", "◊", "≤", "⤴", "◈", "⊖", "⊕"]
        ))
        
        return examples
    
    def generate_advanced_examples(self) -> List[NeuroglyphExample]:
        """Genera esempi avanzati con strutture dati complesse"""
        examples = []
        
        # Classi e oggetti
        examples.append(NeuroglyphExample(
            instruction="Traduci questa definizione di classe in Python",
            input="⟪Calculator⟫⟨⟩ƒ⟨α,β⟩⤴α⊕β⟨⟩ƒ⟨α,β⟩⤴α⊗β",
            output="class Calculator:\n    def add(self, a, b):\n        return a + b\n    \n    def multiply(self, a, b):\n        return a * b",
            task_type=TaskType.SYMBOL_TO_CODE,
            difficulty=DifficultyLevel.ADVANCED,
            language="python",
            reasoning_steps=[
                "1. ⟪Calculator⟫ definisce classe Calculator",
                "2. Primo ⟨⟩ƒ⟨α,β⟩⤴α⊕β è metodo add",
                "3. Secondo ⟨⟩ƒ⟨α,β⟩⤴α⊗β è metodo multiply",
                "4. Ogni metodo ha parametri α,β e return operation"
            ],
            symbols_used=["⟪", "⟫", "⟨", "⟩", "ƒ", "α", "β", "⤴", "⊕", "⊗"]
        ))
        
        # List comprehension
        examples.append(NeuroglyphExample(
            instruction="Traduci questa list comprehension simbolica",
            input="⟨⟩[α²|α∈[1..n]∧α%2=0]",
            output="def even_squares(n):\n    return [x**2 for x in range(1, n+1) if x % 2 == 0]",
            task_type=TaskType.SYMBOL_TO_CODE,
            difficulty=DifficultyLevel.ADVANCED,
            language="python",
            reasoning_steps=[
                "1. ⟨⟩ definisce funzione",
                "2. [α²|...] è list comprehension con α²",
                "3. α∈[1..n] è il range di iterazione",
                "4. ∧α%2=0 è la condizione di filtro (numeri pari)"
            ],
            symbols_used=["⟨", "⟩", "[", "]", "²", "|", "∈", "∧", "%", "="]
        ))
        
        # Decoratori
        examples.append(NeuroglyphExample(
            instruction="Implementa questo decoratore dai neuroglifi",
            input="⟨⟩@⟨timer⟩ƒ⟨⟩⤴⊙time⊙ƒ⊙⊙time",
            output="def timer(func):\n    def wrapper(*args, **kwargs):\n        start = time.time()\n        result = func(*args, **kwargs)\n        end = time.time()\n        print(f'Time: {end - start}')\n        return result\n    return wrapper",
            task_type=TaskType.SYMBOL_TO_CODE,
            difficulty=DifficultyLevel.ADVANCED,
            language="python",
            reasoning_steps=[
                "1. ⟨⟩@⟨timer⟩ definisce decoratore timer",
                "2. ƒ⟨⟩ è la funzione wrapper",
                "3. ⊙time⊙ rappresenta misurazione tempo",
                "4. ƒ è l'esecuzione della funzione originale"
            ],
            symbols_used=["⟨", "⟩", "@", "ƒ", "⤴", "⊙"]
        ))
        
        return examples
    
    def generate_expert_examples(self) -> List[NeuroglyphExample]:
        """Genera esempi expert con algoritmi complessi"""
        examples = []
        
        # Algoritmo di ordinamento
        examples.append(NeuroglyphExample(
            instruction="Implementa quicksort dai neuroglifi con ragionamento completo",
            input="⟨⟩ƒ⟨arr⟩◊|arr|≤1⤴arr◈⤴⟨pivot=arr[0]⟩⟨left=[α|α∈arr∧α<pivot]⟩⟨right=[α|α∈arr∧α>pivot]⟩ƒ⟨left⟩⊕[pivot]⊕ƒ⟨right⟩",
            output="def quicksort(arr):\n    if len(arr) <= 1:\n        return arr\n    pivot = arr[0]\n    left = [x for x in arr[1:] if x < pivot]\n    right = [x for x in arr[1:] if x > pivot]\n    return quicksort(left) + [pivot] + quicksort(right)",
            task_type=TaskType.CHAIN_OF_THOUGHT,
            difficulty=DifficultyLevel.EXPERT,
            language="python",
            reasoning_steps=[
                "1. ⟨⟩ƒ⟨arr⟩ definisce funzione quicksort",
                "2. ◊|arr|≤1⤴arr caso base: array vuoto o singolo",
                "3. ⟨pivot=arr[0]⟩ sceglie primo elemento come pivot",
                "4. ⟨left=[α|α∈arr∧α<pivot]⟩ filtra elementi minori",
                "5. ⟨right=[α|α∈arr∧α>pivot]⟩ filtra elementi maggiori",
                "6. ƒ⟨left⟩⊕[pivot]⊕ƒ⟨right⟩ ricombina ricorsivamente"
            ],
            symbols_used=["⟨", "⟩", "ƒ", "◊", "|", "≤", "⤴", "◈", "=", "[", "]", "∈", "∧", "<", ">", "⊕"],
            validation_code="assert quicksort([3,1,4,1,5]) == [1,1,3,4,5]"
        ))
        
        # Pattern matching
        examples.append(NeuroglyphExample(
            instruction="Implementa pattern matching dai neuroglifi",
            input="⟨⟩ƒ⟨data⟩◊data∈ℤ⤴'number'◈◊data∈𝕊⤴'string'◈◊data∈[]⤴'list'◈⤴'unknown'",
            output="def type_matcher(data):\n    if isinstance(data, int):\n        return 'number'\n    elif isinstance(data, str):\n        return 'string'\n    elif isinstance(data, list):\n        return 'list'\n    else:\n        return 'unknown'",
            task_type=TaskType.SYMBOL_TO_CODE,
            difficulty=DifficultyLevel.EXPERT,
            language="python",
            reasoning_steps=[
                "1. ⟨⟩ƒ⟨data⟩ definisce funzione type_matcher",
                "2. ◊data∈ℤ controlla se è intero",
                "3. ◊data∈𝕊 controlla se è stringa",
                "4. ◊data∈[] controlla se è lista",
                "5. Ultimo ⤴'unknown' è caso default"
            ],
            symbols_used=["⟨", "⟩", "ƒ", "◊", "∈", "ℤ", "𝕊", "[]", "⤴", "◈"]
        ))
        
        return examples
    
    def generate_multi_language_examples(self) -> List[NeuroglyphExample]:
        """Genera esempi multi-linguaggio"""
        examples = []
        
        # Stesso algoritmo in linguaggi diversi
        neuroglyph = "⟨⟩α⊕β⤴α⊕β"
        
        languages = [
            ("python", "def add(a, b):\n    return a + b"),
            ("javascript", "function add(a, b) {\n    return a + b;\n}"),
            ("rust", "fn add(a: i32, b: i32) -> i32 {\n    a + b\n}"),
            ("go", "func add(a int, b int) int {\n    return a + b\n}"),
            ("java", "public static int add(int a, int b) {\n    return a + b;\n}")
        ]
        
        for lang, code in languages:
            examples.append(NeuroglyphExample(
                instruction=f"Traduci questi neuroglifi in {lang.upper()}",
                input=neuroglyph,
                output=code,
                task_type=TaskType.SYMBOL_TO_CODE,
                difficulty=DifficultyLevel.INTERMEDIATE,
                language=lang,
                symbols_used=["⟨", "⟩", "α", "β", "⊕", "⤴"]
            ))
        
        return examples
    
    def generate_debugging_examples(self) -> List[NeuroglyphExample]:
        """Genera esempi di debugging simbolico"""
        examples = []
        
        # Errore logico
        examples.append(NeuroglyphExample(
            instruction="Trova e correggi l'errore in questi neuroglifi",
            input="⟨⟩ƒ⟨n⟩◊n≤0⤴1◈⤴n⊗ƒ⟨n⊕1⟩",  # Errore: n+1 invece di n-1
            output="⟨⟩ƒ⟨n⟩◊n≤0⤴1◈⤴n⊗ƒ⟨n⊖1⟩",  # Corretto: n-1
            task_type=TaskType.DEBUGGING,
            difficulty=DifficultyLevel.ADVANCED,
            language="symbol",
            reasoning_steps=[
                "1. La funzione dovrebbe calcolare il fattoriale",
                "2. Caso base: n≤0 ritorna 1 ✓",
                "3. ERRORE: ricorsione con n⊕1 (n+1) causa loop infinito",
                "4. CORREZIONE: deve essere n⊖1 (n-1) per convergere",
                "5. Risultato: fattoriale corretto"
            ],
            symbols_used=["⟨", "⟩", "ƒ", "◊", "≤", "⤴", "◈", "⊗", "⊖"]
        ))
        
        return examples
    
    def generate_complete_dataset(self, 
                                target_size: int = 200) -> List[NeuroglyphExample]:
        """Genera dataset completo bilanciato"""
        print(f"🔄 Generazione dataset migliorato (target: {target_size} esempi)")
        
        all_examples = []
        
        # Distribuzione bilanciata
        basic_examples = self.generate_basic_examples()
        intermediate_examples = self.generate_intermediate_examples()
        advanced_examples = self.generate_advanced_examples()
        expert_examples = self.generate_expert_examples()
        multi_lang_examples = self.generate_multi_language_examples()
        debug_examples = self.generate_debugging_examples()
        
        # Aggiungi tutti gli esempi
        all_examples.extend(basic_examples)
        all_examples.extend(intermediate_examples)
        all_examples.extend(advanced_examples)
        all_examples.extend(expert_examples)
        all_examples.extend(multi_lang_examples)
        all_examples.extend(debug_examples)
        
        # Genera esempi aggiuntivi se necessario
        while len(all_examples) < target_size:
            # Genera più esempi base per bilanciare
            additional = self.generate_basic_examples()
            all_examples.extend(additional[:target_size - len(all_examples)])
        
        # Mescola mantenendo bilanciamento
        random.shuffle(all_examples)
        
        print(f"✅ Dataset generato: {len(all_examples)} esempi")
        
        # Statistiche
        by_difficulty = {}
        by_task_type = {}
        by_language = {}
        
        for example in all_examples:
            by_difficulty[example.difficulty.value] = by_difficulty.get(example.difficulty.value, 0) + 1
            by_task_type[example.task_type.value] = by_task_type.get(example.task_type.value, 0) + 1
            by_language[example.language] = by_language.get(example.language, 0) + 1
        
        print(f"📊 Per difficoltà: {by_difficulty}")
        print(f"📊 Per tipo task: {by_task_type}")
        print(f"📊 Per linguaggio: {by_language}")
        
        return all_examples
    
    def save_enhanced_dataset(self, examples: List[NeuroglyphExample], 
                            filename: str = "enhanced_neuroglyph_train.jsonl"):
        """Salva dataset migliorato"""
        output_path = Path("datasets") / filename
        output_path.parent.mkdir(exist_ok=True)
        
        with open(output_path, "w", encoding="utf-8") as f:
            for example in examples:
                # Converti in formato JSONL con metadati
                json_example = {
                    "instruction": example.instruction,
                    "input": example.input,
                    "output": example.output,
                    "task_type": example.task_type.value,
                    "difficulty": example.difficulty.value,
                    "language": example.language
                }
                
                # Aggiungi campi opzionali se presenti
                if example.reasoning_steps:
                    json_example["reasoning_steps"] = example.reasoning_steps
                
                if example.symbols_used:
                    json_example["symbols_used"] = example.symbols_used
                
                if example.validation_code:
                    json_example["validation_code"] = example.validation_code
                
                f.write(json.dumps(json_example, ensure_ascii=False) + "\n")
        
        print(f"💾 Dataset migliorato salvato: {output_path}")
        return output_path

def main():
    """Genera dataset migliorato"""
    print("🧠 NEUROGLYPH LLM - Enhanced Dataset Generator")
    print("🎯 Dataset con simboli standardizzati e reasoning")
    print("=" * 70)
    
    generator = EnhancedNeuroglyphDataset()
    
    # Genera dataset completo
    examples = generator.generate_complete_dataset(target_size=150)
    
    # Salva dataset
    output_path = generator.save_enhanced_dataset(examples)
    
    # Crea split train/test
    random.shuffle(examples)
    split_idx = int(len(examples) * 0.9)  # 90% train, 10% test
    
    train_examples = examples[:split_idx]
    test_examples = examples[split_idx:]
    
    train_path = generator.save_enhanced_dataset(train_examples, "enhanced_neuroglyph_train.jsonl")
    test_path = generator.save_enhanced_dataset(test_examples, "enhanced_neuroglyph_test.jsonl")
    
    print(f"\n📊 SPLIT FINALE:")
    print(f"   Train: {len(train_examples)} esempi")
    print(f"   Test: {len(test_examples)} esempi")
    
    # Mostra esempi
    print(f"\n📝 ESEMPI MIGLIORATI:")
    print("=" * 70)
    
    for i, example in enumerate(examples[:3]):
        print(f"\n🔸 Esempio {i+1} ({example.difficulty.value}, {example.task_type.value}):")
        print(f"   Instruction: {example.instruction}")
        print(f"   Input: {example.input}")
        print(f"   Output: {example.output[:80]}...")
        if example.reasoning_steps:
            print(f"   Reasoning: {len(example.reasoning_steps)} steps")
        if example.symbols_used:
            print(f"   Symbols: {example.symbols_used}")
    
    print(f"\n🎉 Dataset NEUROGLYPH migliorato pronto!")
    print(f"📁 Files: {train_path}, {test_path}")
    
    return train_path, test_path

if __name__ == "__main__":
    main()
