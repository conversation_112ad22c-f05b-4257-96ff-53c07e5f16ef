#!/bin/bash
# NEUROGLYPH LLM - Metal GPU Setup Script
# Configura accelerazione GPU per Mac M2

echo "🚀 NEUROGLYPH LLM - Metal GPU Setup"
echo "🎯 Configurazione accelerazione GPU per Mac M2"
echo "=" * 60

# Verifica sistema
echo "🔍 Verifica sistema..."
if [[ $(uname -m) != "arm64" ]]; then
    echo "❌ Questo script è per Mac M1/M2 (ARM64)"
    exit 1
fi

echo "✅ Mac ARM64 rilevato"

# Verifica memoria
echo "💾 Verifica memoria disponibile..."
python3 -c "
import psutil
mem = psutil.virtual_memory()
available = mem.available/(1024**3)
print(f'Memoria disponibile: {available:.1f} GB')
if available < 1.5:
    print('❌ ERRORE: Memoria troppo limitata ({:.1f}GB)'.format(available))
    print('💡 Chiudi altre applicazioni per liberare memoria')
    print('📱 Minimo richiesto: >1.5GB')
    exit(1)
elif available < 2.5:
    print('⚠️ ATTENZIONE: Memoria limitata ({:.1f}GB)'.format(available))
    print('💡 Procedo con setup leggero')
    print('📱 Raccomandato: >3GB per performance ottimali')
else:
    print('✅ Memoria sufficiente per Metal GPU')
"

if [ $? -ne 0 ]; then
    echo ""
    echo "🧹 LIBERA MEMORIA:"
    echo "   - Chiudi browser (Chrome, Safari, Firefox)"
    echo "   - Chiudi app di comunicazione (Slack, Discord)"
    echo "   - Chiudi app multimediali (Spotify, VLC)"
    echo "   - Riavvia il script quando hai >3GB liberi"
    exit 1
fi

# Backup configurazione attuale
echo "💾 Backup configurazione attuale..."
if command -v pip show llama-cpp-python &> /dev/null; then
    pip show llama-cpp-python > backup_llama_cpp_info.txt
    echo "✅ Info attuale salvata in backup_llama_cpp_info.txt"
fi

# Crea virtual environment per Metal
echo "🔧 Creazione virtual environment Metal..."
if [ -d "venv_metal" ]; then
    echo "⚠️ venv_metal esiste già, rimuovo..."
    rm -rf venv_metal
fi

python3 -m venv venv_metal
source venv_metal/bin/activate

echo "✅ Virtual environment creato e attivato"

# Aggiorna pip
echo "📦 Aggiornamento pip..."
pip install --upgrade pip

# Installa dipendenze base
echo "📚 Installazione dipendenze base..."
pip install psutil numpy

# Disinstalla versione esistente se presente
echo "🗑️ Rimozione versione esistente llama-cpp-python..."
pip uninstall llama-cpp-python -y 2>/dev/null || true

# Installa llama-cpp-python con Metal support
echo "🚀 Installazione llama-cpp-python con Metal support..."
echo "⏳ Questo può richiedere 5-10 minuti..."

CMAKE_ARGS="-DLLAMA_METAL=on" pip install llama-cpp-python --no-cache-dir --verbose

if [ $? -ne 0 ]; then
    echo "❌ Errore installazione Metal version"
    echo "🔧 Provo installazione standard..."
    pip install llama-cpp-python --no-cache-dir

    if [ $? -ne 0 ]; then
        echo "❌ Installazione fallita completamente"
        exit 1
    else
        echo "⚠️ Installazione standard riuscita (senza Metal optimizations)"
    fi
else
    echo "✅ llama-cpp-python con Metal installato!"
fi

# Test installazione
echo "🧪 Test installazione..."
python3 -c "
try:
    from llama_cpp import Llama
    print('✅ llama-cpp-python importato correttamente')

    # Verifica attributi Metal
    import llama_cpp
    metal_attrs = [attr for attr in dir(llama_cpp) if 'metal' in attr.lower()]
    if metal_attrs:
        print(f'✅ Attributi Metal trovati: {metal_attrs}')
    else:
        print('⚠️ Nessun attributo Metal specifico trovato')

    print('✅ Test importazione completato')

except ImportError as e:
    print(f'❌ Errore importazione: {e}')
    exit(1)
except Exception as e:
    print(f'⚠️ Warning durante test: {e}')
"

if [ $? -ne 0 ]; then
    echo "❌ Test installazione fallito"
    exit 1
fi

# Crea configurazione ottimizzata
echo "⚙️ Creazione configurazione ottimizzata..."
cat > config/metal_gpu_config.py << 'EOF'
"""
NEUROGLYPH LLM - Configurazione Metal GPU Ottimizzata
Configurazione per Mac M2 8GB con accelerazione GPU
"""

from llama_cpp import Llama

def create_metal_llm(model_path, gpu_layers=10):
    """
    Crea istanza Llama con Metal GPU ottimizzato

    Args:
        model_path: Path al modello GGUF
        gpu_layers: Numero di layer su GPU (10-20 raccomandato)
    """

    return Llama(
        model_path=model_path,
        n_ctx=512,                    # Context window ottimizzato
        n_threads=2,                  # Meno thread CPU per GPU
        n_gpu_layers=gpu_layers,      # Layer su Metal GPU
        verbose=False,                # Riduci output verboso
        use_mmap=True,               # Memory mapping efficiente
        use_mlock=False,             # Non bloccare memoria
        n_batch=128,                 # Batch size ottimizzato
        main_gpu=0,                  # GPU principale
        # Metal specific optimizations
        tensor_split=None,           # Lascia gestire automaticamente
    )

def test_metal_performance(model_path):
    """Test performance Metal vs CPU"""
    import time

    print("🔄 Test performance Metal GPU...")

    # Test Metal GPU
    try:
        llm_metal = create_metal_llm(model_path, gpu_layers=10)

        start_time = time.time()
        response = llm_metal("def hello():", max_tokens=10, temperature=0.1)
        metal_time = time.time() - start_time

        print(f"🚀 Metal GPU: {metal_time:.2f}s")

        # Test CPU only
        llm_cpu = Llama(
            model_path=model_path,
            n_ctx=512,
            n_threads=4,
            n_gpu_layers=0,  # CPU only
            verbose=False,
            use_mmap=True
        )

        start_time = time.time()
        response = llm_cpu("def hello():", max_tokens=10, temperature=0.1)
        cpu_time = time.time() - start_time

        print(f"🖥️ CPU only: {cpu_time:.2f}s")

        if metal_time < cpu_time:
            speedup = cpu_time / metal_time
            print(f"⚡ Speedup Metal: {speedup:.2f}x")
            return True
        else:
            print("⚠️ Metal non più veloce di CPU")
            return False

    except Exception as e:
        print(f"❌ Errore test: {e}")
        return False

# Configurazione raccomandata per NEUROGLYPH LLM
NEUROGLYPH_METAL_CONFIG = {
    "model_path": "model/qwen2.5coder/qwen2.5-coder-7b-instruct-q4_k_m.gguf",
    "n_ctx": 512,
    "n_threads": 2,
    "n_gpu_layers": 10,  # Inizia con 10, prova 15-20 se stabile
    "n_batch": 128,
    "use_mmap": True,
    "use_mlock": False,
    "verbose": False
}
EOF

echo "✅ Configurazione salvata in config/metal_gpu_config.py"

# Crea script di attivazione
cat > activate_metal.sh << 'EOF'
#!/bin/bash
# Attiva environment Metal GPU per NEUROGLYPH LLM
echo "🚀 Attivazione Metal GPU environment..."
source venv_metal/bin/activate
echo "✅ Environment Metal attivato"
echo "💡 Per testare: python3 -c 'from config.metal_gpu_config import test_metal_performance; test_metal_performance(\"model/qwen2.5coder/qwen2.5-coder-7b-instruct-q4_k_m.gguf\")'"
EOF

chmod +x activate_metal.sh

# Test finale
echo "🎉 Setup Metal GPU completato!"
echo ""
echo "📋 PROSSIMI PASSI:"
echo "   1. Attiva environment: source venv_metal/bin/activate"
echo "   2. Oppure usa: ./activate_metal.sh"
echo "   3. Testa performance: python3 scripts/test_metal_performance.py"
echo ""
echo "⚙️ CONFIGURAZIONE:"
echo "   - Virtual env: venv_metal/"
echo "   - Config: config/metal_gpu_config.py"
echo "   - Attivazione: ./activate_metal.sh"
echo ""
echo "🚀 NEUROGLYPH LLM è pronto per Metal GPU acceleration!"
