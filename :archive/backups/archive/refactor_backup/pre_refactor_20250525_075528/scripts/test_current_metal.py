#!/usr/bin/env python3
"""
Test Metal GPU con la versione attuale di llama-cpp-python
Verifica se possiamo abilitare GPU senza reinstallazione
"""

import os
import sys
import time
import psutil

def check_memory():
    """Verifica memoria disponibile"""
    memory = psutil.virtual_memory()
    available_gb = memory.available / (1024**3)
    print(f"💾 Memoria disponibile: {available_gb:.1f} GB")
    return available_gb > 2.0

def test_metal_configuration():
    """Testa diverse configurazioni Metal"""
    print("🚀 TEST CONFIGURAZIONI METAL GPU")
    print("=" * 60)
    
    if not check_memory():
        print("⚠️ Memoria limitata - chiudi altre app")
        return False
    
    try:
        from llama_cpp import Llama
        
        model_path = "model/qwen2.5coder/qwen2.5-coder-7b-instruct-q4_k_m.gguf"
        
        if not os.path.exists(model_path):
            print(f"❌ Modello non trovato: {model_path}")
            return False
        
        # Test diverse configurazioni GPU layers
        gpu_configs = [
            {"layers": 0, "name": "CPU Only"},
            {"layers": 5, "name": "5 GPU Layers"},
            {"layers": 10, "name": "10 GPU Layers"},
            {"layers": 20, "name": "20 GPU Layers"},
            {"layers": 35, "name": "35 GPU Layers"},
        ]
        
        results = []
        
        for config in gpu_configs:
            print(f"\n🔄 Test: {config['name']}")
            print("-" * 40)
            
            try:
                start_time = time.time()
                
                llm = Llama(
                    model_path=model_path,
                    n_ctx=256,                    # Context ridotto per velocità
                    n_threads=2 if config['layers'] > 0 else 4,  # Meno thread se usiamo GPU
                    n_gpu_layers=config['layers'],
                    verbose=False,
                    use_mmap=True,
                    use_mlock=False
                )
                
                load_time = time.time() - start_time
                print(f"   ✅ Caricamento: {load_time:.2f}s")
                
                # Test inferenza veloce
                start_time = time.time()
                response = llm(
                    "def hello():",
                    max_tokens=5,
                    temperature=0.1,
                    echo=False
                )
                inference_time = time.time() - start_time
                
                output = response['choices'][0]['text']
                print(f"   ⚡ Inferenza: {inference_time:.2f}s")
                print(f"   📄 Output: {repr(output[:30])}")
                
                results.append({
                    'config': config['name'],
                    'layers': config['layers'],
                    'load_time': load_time,
                    'inference_time': inference_time,
                    'success': True
                })
                
                # Libera memoria
                del llm
                
            except Exception as e:
                print(f"   ❌ Errore: {e}")
                results.append({
                    'config': config['name'],
                    'layers': config['layers'],
                    'load_time': 0,
                    'inference_time': 0,
                    'success': False,
                    'error': str(e)
                })
        
        # Analizza risultati
        print("\n📊 RISULTATI COMPARATIVI")
        print("=" * 60)
        
        successful_results = [r for r in results if r['success']]
        
        if len(successful_results) == 0:
            print("❌ Nessuna configurazione funzionante")
            return False
        
        # Trova la configurazione più veloce
        fastest = min(successful_results, key=lambda x: x['inference_time'])
        cpu_only = next((r for r in successful_results if r['layers'] == 0), None)
        
        print(f"{'Configurazione':<20} {'Caricamento':<12} {'Inferenza':<10} {'Status'}")
        print("-" * 60)
        
        for result in results:
            if result['success']:
                status = "✅"
                if result == fastest:
                    status += " 🏆 FASTEST"
                print(f"{result['config']:<20} {result['load_time']:<12.2f} {result['inference_time']:<10.2f} {status}")
            else:
                print(f"{result['config']:<20} {'FAILED':<12} {'FAILED':<10} ❌")
        
        # Calcola speedup
        if cpu_only and fastest['layers'] > 0:
            speedup = cpu_only['inference_time'] / fastest['inference_time']
            print(f"\n🚀 SPEEDUP METAL GPU: {speedup:.2f}x")
            
            if speedup > 1.5:
                print("🎉 METAL GPU OFFRE ACCELERAZIONE SIGNIFICATIVA!")
                print(f"💡 Configurazione raccomandata: {fastest['layers']} GPU layers")
                return True
            else:
                print("⚠️ Metal GPU non offre vantaggi significativi")
        
        return len(successful_results) > 1
        
    except ImportError:
        print("❌ llama-cpp-python non installato")
        return False
    except Exception as e:
        print(f"❌ Errore generale: {e}")
        return False

def generate_optimal_config(best_layers):
    """Genera configurazione ottimale"""
    print(f"\n💡 CONFIGURAZIONE OTTIMALE PER NEUROGLYPH LLM")
    print("=" * 60)
    
    config = f"""
# Configurazione Metal GPU ottimizzata per Mac M2 8GB
from llama_cpp import Llama

llm = Llama(
    model_path="model/qwen2.5coder/qwen2.5-coder-7b-instruct-q4_k_m.gguf",
    n_ctx=512,                    # Context window
    n_threads=2,                  # Thread CPU ridotti per GPU
    n_gpu_layers={best_layers},              # GPU layers ottimali
    verbose=False,
    use_mmap=True,               # Memory mapping efficiente
    use_mlock=False,             # Non bloccare memoria
    n_batch=128,                 # Batch size
    main_gpu=0,                  # GPU principale
)

# Generazione ottimizzata
response = llm(
    prompt,
    max_tokens=150,
    temperature=0.7,
    top_p=0.9,
    echo=False
)
"""
    
    print(config)
    
    # Salva configurazione
    with open("config/optimal_metal_config.py", "w") as f:
        f.write(config)
    
    print("✅ Configurazione salvata in: config/optimal_metal_config.py")

def main():
    """Test principale"""
    print("🧠 QWEN2.5-CODER METAL GPU OPTIMIZATION")
    print("🎯 Trova configurazione ottimale per Mac M2")
    print("=" * 70)
    
    success = test_metal_configuration()
    
    if success:
        print("\n🎉 METAL GPU FUNZIONA!")
        print("🚀 NEUROGLYPH LLM può usare accelerazione GPU")
    else:
        print("\n⚠️ Metal GPU non disponibile o non vantaggioso")
        print("🔧 NEUROGLYPH LLM userà CPU ottimizzata")
    
    return success

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
