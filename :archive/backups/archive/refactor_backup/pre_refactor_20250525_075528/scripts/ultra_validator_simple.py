#!/usr/bin/env python3
"""
🧠 NEUROGLYPH ULTRA - Simple Validator
======================================

Validatore semplificato che implementa i criteri base USU/CTU/LCL.
Versione funzionante per la pipeline automatica.

Usage: python ultra_validator_simple.py <symbol> <code> <fallback> <category> <meaning>
"""

import json
import sys
import unicodedata
from typing import Dict, Any, Set
from pathlib import Path

class UltraValidatorSimple:
    """Validatore semplificato per NEUROGLYPH ULTRA."""
    
    def __init__(self):
        # Categorie valide
        self.valid_categories = {
            "control", "operator", "data_type", "reasoning", "logic", 
            "memory", "meta", "structure", "semantic_marker", "construct", 
            "entity", "state", "flow", "variable"
        }
        
        # Cache simboli esistenti
        self.existing_symbols: Set[str] = set()
        self.existing_codes: Set[str] = set()
        
        self._load_existing()
        
    def _load_existing(self):
        """Carica simboli esistenti."""
        try:
            registry_path = Path("core/symbols_registry.json")
            if registry_path.exists():
                with open(registry_path, 'r', encoding='utf-8') as f:
                    registry = json.load(f)
                    
                for symbol_data in registry.get("approved_symbols", []):
                    self.existing_symbols.add(symbol_data["symbol"])
                    self.existing_codes.add(symbol_data["code"])
        except:
            pass  # Ignora errori di caricamento
            
    def validate_symbol(self, symbol: str, code: str, fallback: str, category: str, meaning: str) -> Dict[str, Any]:
        """Validazione completa semplificata."""
        result = {
            "valid": True,
            "score": 100.0,
            "errors": [],
            "warnings": [],
            "token_cost": 1,
            "token_density": 1.0
        }
        
        # 1. USU - Unicità Simbolica Universale
        if len(symbol) != 1:
            result["errors"].append("Symbol must be single character")
            result["valid"] = False
            result["score"] -= 25
            
        try:
            unicodedata.name(symbol)
        except ValueError:
            result["errors"].append("Invalid Unicode character")
            result["valid"] = False
            result["score"] -= 25
            
        if symbol in self.existing_symbols:
            result["errors"].append("Symbol already exists")
            result["valid"] = False
            result["score"] -= 25
            
        if code in self.existing_codes:
            result["errors"].append("Code already exists")
            result["valid"] = False
            result["score"] -= 25
            
        # 2. CTU - Codifica Testuale Unificata
        if not code.startswith("ng:"):
            result["errors"].append("Code must start with 'ng:'")
            result["valid"] = False
            result["score"] -= 20
            
        if len(code.split(":")) < 3:
            result["errors"].append("Code must have format ng:category:function")
            result["valid"] = False
            result["score"] -= 20
            
        if not fallback or not fallback.isascii():
            result["errors"].append("Fallback must be ASCII")
            result["valid"] = False
            result["score"] -= 15
            
        # 3. LCL - LLM Compatibility Layer
        token_cost = self._estimate_token_cost(symbol)
        result["token_cost"] = token_cost
        
        if token_cost > 2:
            result["errors"].append(f"Token cost {token_cost} > 2")
            result["valid"] = False
            result["score"] -= 20
            
        result["token_density"] = 1.0 / token_cost
        
        # 4. Categorie
        if category not in self.valid_categories:
            result["errors"].append(f"Invalid category: {category}")
            result["valid"] = False
            result["score"] -= 15
            
        if not meaning or len(meaning.strip()) < 2:
            result["errors"].append("Meaning too short")
            result["valid"] = False
            result["score"] -= 10
            
        # 5. Score qualità finale
        quality_score = (
            0.4 * result["token_density"] +
            0.3 * (1 if token_cost <= 2 else 0) +
            0.2 * (1 if len(symbol) == 1 else 0) +
            0.1 * (1 if fallback.isascii() else 0)
        ) * 100
        
        # Score finale (media tra validazione e qualità)
        result["score"] = (result["score"] + quality_score) / 2
        
        return result
        
    def _estimate_token_cost(self, symbol: str) -> int:
        """Stima costo token."""
        code_point = ord(symbol)
        
        if code_point < 0x80:  # ASCII
            return 1
        elif 0x2000 <= code_point <= 0x2FFF:  # Mathematical symbols
            return 1
        else:
            return 2


def main():
    """Test del validatore."""
    if len(sys.argv) < 6:
        print("Usage: python ultra_validator_simple.py <symbol> <code> <fallback> <category> <meaning>")
        sys.exit(1)
        
    symbol = sys.argv[1]
    code = sys.argv[2]
    fallback = sys.argv[3]
    category = sys.argv[4]
    meaning = sys.argv[5]
    
    validator = UltraValidatorSimple()
    result = validator.validate_symbol(symbol, code, fallback, category, meaning)
    
    print(f"Valid: {result['valid']}")
    print(f"Score: {result['score']:.1f}%")
    print(f"Token Cost: {result['token_cost']}")
    print(f"Token Density: {result['token_density']:.2f}")
    
    if result["errors"]:
        print("Errors:")
        for error in result["errors"]:
            print(f"  - {error}")
            
    if result["warnings"]:
        print("Warnings:")
        for warning in result["warnings"]:
            print(f"  - {warning}")
    
    sys.exit(0 if result["valid"] else 1)


if __name__ == "__main__":
    main()
