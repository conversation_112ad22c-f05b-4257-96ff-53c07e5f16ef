#!/usr/bin/env python3
"""
🧪 NEUROGLYPH ULTRA - Pipeline Test Suite
==========================================

Test completo della pipeline automatica di generazione simboli.
Verifica tutti i componenti prima dell'esecuzione completa.

Usage: python test_ultra_pipeline.py [--quick] [--verbose]
"""

import sys
import os
import json
import tempfile
import shutil
from pathlib import Path
from typing import Dict, Any, List

# Import del generatore
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

def test_imports():
    """Testa che tutti gli import necessari funzionino."""
    print("🔍 Testing imports...")
    
    try:
        from generate_512_symbols_ultra_pipeline import UltraSymbolGenerator
        print("✅ UltraSymbolGenerator import OK")
    except ImportError as e:
        print(f"❌ UltraSymbolGenerator import failed: {e}")
        return False
        
    try:
        from tools.validate_symbol import SymbolValidator
        print("✅ SymbolValidator import OK")
    except ImportError as e:
        print(f"⚠️ SymbolValidator import failed: {e}")
        
    try:
        from tools.test_llm_tokenizer_compat import TokenizerCompatibilityTester
        print("✅ TokenizerCompatibilityTester import OK")
    except ImportError as e:
        print(f"⚠️ TokenizerCompatibilityTester import failed: {e}")
        
    try:
        from tools.score_symbol_quality import SymbolQualityScorer
        print("✅ SymbolQualityScorer import OK")
    except ImportError as e:
        print(f"⚠️ SymbolQualityScorer import failed: {e}")
        
    return True

def test_config_loading():
    """Testa caricamento configurazione."""
    print("\n🔍 Testing config loading...")
    
    config_path = Path("scripts/ultra_pipeline_config.json")
    if not config_path.exists():
        print(f"❌ Config file not found: {config_path}")
        return False
        
    try:
        with open(config_path, 'r', encoding='utf-8') as f:
            config = json.load(f)
            
        required_keys = ["categories", "unicode_ranges", "semantic_names", "generation_settings"]
        for key in required_keys:
            if key not in config:
                print(f"❌ Missing config key: {key}")
                return False
                
        print("✅ Config loading OK")
        print(f"  • Categories: {len(config['categories'])}")
        print(f"  • Unicode ranges: {len(config['unicode_ranges'])}")
        print(f"  • Target symbols: {config['generation_settings']['target_symbols']}")
        
        return True
        
    except Exception as e:
        print(f"❌ Config loading failed: {e}")
        return False

def test_symbol_generation():
    """Testa generazione di un singolo simbolo."""
    print("\n🔍 Testing symbol generation...")
    
    try:
        from generate_512_symbols_ultra_pipeline import UltraSymbolGenerator
        
        # Crea generatore con target piccolo per test
        generator = UltraSymbolGenerator(target_count=1, min_score=80.0, batch_size=1)
        
        # Testa generazione simbolo
        symbol_data = generator.generate_unique_symbol("operator", "arithmetic")
        
        if symbol_data:
            print("✅ Symbol generation OK")
            print(f"  • Symbol: {symbol_data['symbol']}")
            print(f"  • Code: {symbol_data['code']}")
            print(f"  • Fallback: {symbol_data['fallback']}")
            print(f"  • Unicode: {symbol_data['unicode_point']}")
            return True
        else:
            print("❌ Symbol generation failed")
            return False
            
    except Exception as e:
        print(f"❌ Symbol generation error: {e}")
        return False

def test_validation_pipeline():
    """Testa pipeline di validazione."""
    print("\n🔍 Testing validation pipeline...")
    
    try:
        from generate_512_symbols_ultra_pipeline import UltraSymbolGenerator
        
        generator = UltraSymbolGenerator(target_count=1, min_score=80.0, batch_size=1)
        
        # Simbolo di test
        test_symbol = {
            "symbol": "⊕",
            "code": "ng:test:add",
            "fallback": "[ADD]",
            "category": "operator",
            "name": "add",
            "description": "Test addition operator",
            "semantic_field": "arithmetic",
            "unicode_point": "U+2295"
        }
        
        # Testa validazione
        result = generator.validate_symbol(test_symbol)
        
        if result and "score" in result:
            print("✅ Validation pipeline OK")
            print(f"  • Score: {result['score']:.1f}%")
            print(f"  • Valid: {result['valid']}")
            return True
        else:
            print("❌ Validation pipeline failed")
            return False
            
    except Exception as e:
        print(f"❌ Validation pipeline error: {e}")
        return False

def test_registry_operations():
    """Testa operazioni sul registry."""
    print("\n🔍 Testing registry operations...")
    
    try:
        from generate_512_symbols_ultra_pipeline import UltraSymbolGenerator
        
        # Crea directory temporanea per test
        with tempfile.TemporaryDirectory() as temp_dir:
            temp_path = Path(temp_dir)
            
            # Crea generatore con path temporaneo
            generator = UltraSymbolGenerator(target_count=1, min_score=80.0, batch_size=1)
            generator.registry_path = temp_path / "test_registry.json"
            
            # Simbolo di test
            test_symbol = {
                "symbol": "⊕",
                "code": "ng:test:add",
                "fallback": "[ADD]",
                "category": "operator",
                "name": "add",
                "description": "Test addition operator",
                "semantic_field": "arithmetic",
                "unicode_point": "U+2295"
            }
            
            test_validation = {
                "score": 95.0,
                "valid": True,
                "token_cost": 1
            }
            
            # Testa approvazione simbolo
            success = generator.approve_symbol(test_symbol, test_validation)
            
            if success and generator.registry_path.exists():
                print("✅ Registry operations OK")
                
                # Verifica contenuto registry
                with open(generator.registry_path, 'r', encoding='utf-8') as f:
                    registry = json.load(f)
                    
                if len(registry["approved_symbols"]) == 1:
                    print("  • Symbol approved and saved")
                    return True
                else:
                    print("❌ Symbol not found in registry")
                    return False
            else:
                print("❌ Registry operations failed")
                return False
                
    except Exception as e:
        print(f"❌ Registry operations error: {e}")
        return False

def test_batch_generation():
    """Testa generazione batch."""
    print("\n🔍 Testing batch generation...")
    
    try:
        from generate_512_symbols_ultra_pipeline import UltraSymbolGenerator
        
        # Crea directory temporanea per test
        with tempfile.TemporaryDirectory() as temp_dir:
            temp_path = Path(temp_dir)
            
            generator = UltraSymbolGenerator(target_count=3, min_score=70.0, batch_size=2)
            generator.registry_path = temp_path / "test_registry.json"
            
            # Testa generazione batch
            batch_results = generator.generate_batch()
            
            if batch_results:
                approved = sum(1 for r in batch_results if r["status"] == "approved")
                print(f"✅ Batch generation OK")
                print(f"  • Batch size: {len(batch_results)}")
                print(f"  • Approved: {approved}")
                return True
            else:
                print("⚠️ Batch generation returned empty results")
                return True  # Può essere normale se non ci sono simboli da generare
                
    except Exception as e:
        print(f"❌ Batch generation error: {e}")
        return False

def run_quick_test():
    """Esegue test rapido dei componenti principali."""
    print("🧪 NEUROGLYPH ULTRA - Quick Test Suite")
    print("=" * 50)
    
    tests = [
        ("Imports", test_imports),
        ("Config Loading", test_config_loading),
        ("Symbol Generation", test_symbol_generation),
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        try:
            if test_func():
                passed += 1
            else:
                print(f"❌ {test_name} FAILED")
        except Exception as e:
            print(f"❌ {test_name} ERROR: {e}")
    
    print(f"\n📊 Quick Test Results: {passed}/{total} passed")
    return passed == total

def run_full_test():
    """Esegue test completo di tutti i componenti."""
    print("🧪 NEUROGLYPH ULTRA - Full Test Suite")
    print("=" * 50)
    
    tests = [
        ("Imports", test_imports),
        ("Config Loading", test_config_loading),
        ("Symbol Generation", test_symbol_generation),
        ("Validation Pipeline", test_validation_pipeline),
        ("Registry Operations", test_registry_operations),
        ("Batch Generation", test_batch_generation),
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        try:
            if test_func():
                passed += 1
                print(f"✅ {test_name} PASSED")
            else:
                print(f"❌ {test_name} FAILED")
        except Exception as e:
            print(f"❌ {test_name} ERROR: {e}")
            import traceback
            traceback.print_exc()
    
    print(f"\n📊 Full Test Results: {passed}/{total} passed")
    
    if passed == total:
        print("🎉 ALL TESTS PASSED! Pipeline ready for production.")
        return True
    else:
        print("⚠️ Some tests failed. Check errors above.")
        return False

def main():
    """Main function."""
    import argparse
    
    parser = argparse.ArgumentParser(description="Test NEUROGLYPH ULTRA pipeline")
    parser.add_argument("--quick", action="store_true", help="Run quick test only")
    parser.add_argument("--verbose", action="store_true", help="Verbose output")
    
    args = parser.parse_args()
    
    if args.quick:
        success = run_quick_test()
    else:
        success = run_full_test()
    
    sys.exit(0 if success else 1)

if __name__ == "__main__":
    main()
