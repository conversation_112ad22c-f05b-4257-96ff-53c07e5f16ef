#!/usr/bin/env python3
"""
NEUROGLYPH LLM - Cleanup Symbols Files
=====================================

Identifica il file symbols corretto e archivia/elimina i duplicati.
Mantiene solo core/symbols_registry.json come fonte di verità.
"""

import os
import json
import shutil
from pathlib import Path
from datetime import datetime

def analyze_symbols_files():
    """Analizza tutti i file symbols per identificare il migliore"""
    print("🔍 ANALISI FILES SYMBOLS")
    print("=" * 60)
    
    # Trova tutti i file symbols
    symbols_files = []
    
    for root, dirs, files in os.walk("."):
        for file in files:
            if "symbol" in file.lower() and file.endswith(('.json', '.jsonl')):
                file_path = Path(root) / file
                symbols_files.append(file_path)
    
    # Analizza ogni file
    file_analysis = []
    
    for file_path in symbols_files:
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                if file_path.suffix == '.jsonl':
                    # JSONL file
                    lines = f.readlines()
                    size = len(lines)
                    content_type = "jsonl"
                    symbols_count = size
                else:
                    # JSON file
                    data = json.load(f)
                    size = len(str(data))
                    
                    # Determina tipo contenuto
                    if isinstance(data, dict):
                        if "approved_symbols" in data:
                            symbols_count = len(data["approved_symbols"])
                            content_type = "registry"
                        elif "symbols" in data:
                            symbols_count = len(data["symbols"])
                            content_type = "collection"
                        else:
                            symbols_count = len(data)
                            content_type = "dict"
                    elif isinstance(data, list):
                        symbols_count = len(data)
                        content_type = "list"
                    else:
                        symbols_count = 0
                        content_type = "unknown"
            
            file_analysis.append({
                'path': file_path,
                'size_bytes': file_path.stat().st_size,
                'symbols_count': symbols_count,
                'content_type': content_type,
                'modified': file_path.stat().st_mtime
            })
            
        except Exception as e:
            print(f"⚠️ Errore analisi {file_path}: {e}")
    
    # Ordina per numero simboli (decrescente)
    file_analysis.sort(key=lambda x: x['symbols_count'], reverse=True)
    
    # Mostra analisi
    print(f"\n📊 TROVATI {len(file_analysis)} FILES SYMBOLS:")
    print("-" * 80)
    print(f"{'File':<40} {'Simboli':<10} {'Tipo':<12} {'Dimensione':<10}")
    print("-" * 80)
    
    for analysis in file_analysis:
        size_mb = analysis['size_bytes'] / (1024 * 1024)
        print(f"{str(analysis['path']):<40} {analysis['symbols_count']:<10} {analysis['content_type']:<12} {size_mb:.1f}MB")
    
    return file_analysis

def identify_correct_file(file_analysis):
    """Identifica il file corretto da mantenere"""
    print(f"\n🎯 IDENTIFICAZIONE FILE CORRETTO")
    print("=" * 60)
    
    # Criteri per file corretto:
    # 1. core/symbols_registry.json (se esiste)
    # 2. Più simboli
    # 3. Tipo "registry" 
    # 4. Più recente
    
    # Cerca core/symbols_registry.json
    registry_file = None
    for analysis in file_analysis:
        if str(analysis['path']) == "core/symbols_registry.json":
            registry_file = analysis
            break
    
    if registry_file:
        print(f"✅ TROVATO FILE PRINCIPALE: core/symbols_registry.json")
        print(f"   Simboli: {registry_file['symbols_count']}")
        print(f"   Tipo: {registry_file['content_type']}")
        print(f"   Dimensione: {registry_file['size_bytes'] / (1024*1024):.1f}MB")
        return registry_file
    
    # Se non trovato, prendi quello con più simboli di tipo registry
    for analysis in file_analysis:
        if analysis['content_type'] == 'registry':
            print(f"✅ FILE CORRETTO IDENTIFICATO: {analysis['path']}")
            print(f"   Simboli: {analysis['symbols_count']}")
            print(f"   Tipo: {analysis['content_type']}")
            return analysis
    
    # Fallback: file con più simboli
    if file_analysis:
        best_file = file_analysis[0]
        print(f"⚠️ FALLBACK - FILE PIÙ GRANDE: {best_file['path']}")
        print(f"   Simboli: {best_file['symbols_count']}")
        return best_file
    
    return None

def create_archive_directory():
    """Crea directory di archivio"""
    archive_dir = Path("archive/symbols_old")
    archive_dir.mkdir(parents=True, exist_ok=True)
    return archive_dir

def cleanup_duplicate_files(correct_file, all_files):
    """Archivia file duplicati e mantiene solo quello corretto"""
    print(f"\n🧹 PULIZIA FILES DUPLICATI")
    print("=" * 60)
    
    archive_dir = create_archive_directory()
    
    files_to_archive = []
    files_to_keep = []
    
    for analysis in all_files:
        file_path = analysis['path']
        
        # Mantieni il file corretto
        if file_path == correct_file['path']:
            files_to_keep.append(file_path)
            print(f"✅ MANTIENI: {file_path}")
            continue
        
        # Mantieni anche symbols_registry.json se non è il file corretto
        if str(file_path) == "core/symbols_registry.json" and file_path != correct_file['path']:
            files_to_keep.append(file_path)
            print(f"✅ MANTIENI: {file_path} (file principale)")
            continue
        
        # Archivia tutti gli altri
        files_to_archive.append(file_path)
    
    # Archivia file duplicati
    archived_count = 0
    for file_path in files_to_archive:
        try:
            # Crea nome file con timestamp
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            archive_name = f"{file_path.stem}_{timestamp}{file_path.suffix}"
            archive_path = archive_dir / archive_name
            
            # Sposta file
            shutil.move(str(file_path), str(archive_path))
            print(f"📦 ARCHIVIATO: {file_path} → {archive_path}")
            archived_count += 1
            
        except Exception as e:
            print(f"❌ Errore archiviazione {file_path}: {e}")
    
    print(f"\n📊 RISULTATI PULIZIA:")
    print(f"   ✅ Files mantenuti: {len(files_to_keep)}")
    print(f"   📦 Files archiviati: {archived_count}")
    
    return files_to_keep, archived_count

def verify_correct_file():
    """Verifica che il file corretto sia utilizzabile"""
    print(f"\n🔍 VERIFICA FILE CORRETTO")
    print("=" * 60)
    
    registry_path = Path("core/symbols_registry.json")
    
    if not registry_path.exists():
        print(f"❌ File principale non trovato: {registry_path}")
        return False
    
    try:
        with open(registry_path, 'r', encoding='utf-8') as f:
            data = json.load(f)
        
        # Verifica struttura
        if "approved_symbols" in data:
            symbols_count = len(data["approved_symbols"])
            print(f"✅ Struttura registry corretta")
            print(f"✅ Simboli approvati: {symbols_count}")
            
            # Verifica alcuni simboli
            if symbols_count > 0:
                first_symbol = data["approved_symbols"][0]
                required_fields = ["id", "symbol", "code", "fallback"]
                
                missing_fields = [field for field in required_fields if field not in first_symbol]
                if missing_fields:
                    print(f"⚠️ Campi mancanti: {missing_fields}")
                else:
                    print(f"✅ Struttura simboli corretta")
            
            return True
        else:
            print(f"⚠️ Struttura registry non standard")
            return False
            
    except Exception as e:
        print(f"❌ Errore verifica: {e}")
        return False

def update_component_references():
    """Aggiorna riferimenti nei componenti per usare il file corretto"""
    print(f"\n🔧 AGGIORNAMENTO RIFERIMENTI")
    print("=" * 60)
    
    # File che potrebbero referenziare symbols files
    component_files = [
        "core/symbolic_validator.py",
        "core/neuroglyph_tokenizer.py", 
        "core/dag_memory.py",
        "scripts/validate_symbol.py",
        "scripts/score_symbol_quality.py"
    ]
    
    correct_path = "symbols_registry.json"
    
    for file_path in component_files:
        if Path(file_path).exists():
            try:
                with open(file_path, 'r', encoding='utf-8') as f:
                    content = f.read()
                
                # Cerca pattern di riferimenti a file symbols
                old_patterns = [
                    "symbols.json",
                    "symbols_certified.json", 
                    "symbols_512_complete.json",
                    "symbols_ultra.json"
                ]
                
                updated = False
                for pattern in old_patterns:
                    if pattern in content and pattern != correct_path:
                        content = content.replace(pattern, correct_path)
                        updated = True
                
                if updated:
                    with open(file_path, 'w', encoding='utf-8') as f:
                        f.write(content)
                    print(f"✅ Aggiornato: {file_path}")
                else:
                    print(f"✓ OK: {file_path}")
                    
            except Exception as e:
                print(f"⚠️ Errore aggiornamento {file_path}: {e}")

def main():
    """Pulizia principale"""
    print("🧹 NEUROGLYPH LLM - Cleanup Symbols Files")
    print("🎯 Mantieni solo core/symbols_registry.json come fonte di verità")
    print("=" * 70)
    
    # Analizza tutti i file
    file_analysis = analyze_symbols_files()
    
    if not file_analysis:
        print("❌ Nessun file symbols trovato")
        return False
    
    # Identifica file corretto
    correct_file = identify_correct_file(file_analysis)
    
    if not correct_file:
        print("❌ Impossibile identificare file corretto")
        return False
    
    # Pulizia duplicati
    kept_files, archived_count = cleanup_duplicate_files(correct_file, file_analysis)
    
    # Verifica file corretto
    if verify_correct_file():
        print("✅ File corretto verificato")
    else:
        print("⚠️ File corretto ha problemi")
    
    # Aggiorna riferimenti
    update_component_references()
    
    print(f"\n🎉 PULIZIA COMPLETATA!")
    print(f"✅ File principale: core/symbols_registry.json")
    print(f"📦 Files archiviati: {archived_count}")
    print(f"🧹 Repository pulito e organizzato")
    
    return True

if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)
