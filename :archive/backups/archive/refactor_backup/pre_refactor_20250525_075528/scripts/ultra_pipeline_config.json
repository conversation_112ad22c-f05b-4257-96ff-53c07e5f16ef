{"pipeline_version": "1.0", "description": "NEUROGLYPH ULTRA - Configuration for automated 512 symbol generation", "generation_settings": {"target_symbols": 512, "min_validation_score": 90.0, "batch_size": 10, "max_attempts_per_symbol": 100, "pause_between_batches": 1.0}, "validation_weights": {"usu_score": 0.25, "ctu_score": 0.2, "lcl_score": 0.2, "tokenizer_score": 0.2, "quality_score": 0.15}, "categories": {"operator": {"count_target": 64, "percentage": 12.5, "description": "Mathematical and logical operators", "semantic_fields": ["arithmetic", "comparison", "bitwise", "assignment", "increment", "modulo", "power", "root", "factorial", "matrix", "vector", "tensor", "complex", "quaternion"], "priority": 1}, "logic": {"count_target": 96, "percentage": 18.75, "description": "Logical constructs and reasoning", "semantic_fields": ["propositional", "predicate", "modal", "temporal", "fuzzy", "quantum", "probabilistic", "deontic", "epistemic", "belief", "inference", "proof", "theorem", "axiom", "lemma"], "priority": 1}, "structure": {"count_target": 80, "percentage": 15.6, "description": "Code structures and data types", "semantic_fields": ["class", "function", "method", "property", "interface", "module", "package", "namespace", "scope", "closure", "lambda", "generator", "iterator", "decorator", "annotation"], "priority": 2}, "flow": {"count_target": 72, "percentage": 14.1, "description": "Control flow constructs", "semantic_fields": ["conditional", "loop", "branch", "jump", "call", "return", "exception", "try", "catch", "finally", "async", "await", "yield", "break", "continue", "goto"], "priority": 2}, "memory": {"count_target": 64, "percentage": 12.5, "description": "Memory management operations", "semantic_fields": ["allocate", "deallocate", "reference", "pointer", "address", "stack", "heap", "cache", "buffer", "register", "volatile", "persistent", "transient", "immutable", "mutable"], "priority": 3}, "reasoning": {"count_target": 56, "percentage": 10.9, "description": "Advanced reasoning processes", "semantic_fields": ["induction", "deduction", "abduction", "analogy", "metaphor", "causality", "correlation", "pattern", "similarity", "difference", "abstraction", "generalization", "specialization", "classification"], "priority": 3}, "meta": {"count_target": 48, "percentage": 9.4, "description": "Meta-programming operations", "semantic_fields": ["reflection", "introspection", "metacognition", "self_reference", "recursion", "fixpoint", "transformation", "compilation", "interpretation", "evaluation", "optimization", "debugging"], "priority": 4}, "quantum": {"count_target": 32, "percentage": 6.25, "description": "Quantum computing concepts", "semantic_fields": ["qubit", "superposition", "entanglement", "measurement", "gate", "circuit", "algorithm", "teleportation", "cryptography", "error_correction", "decoherence", "interference"], "priority": 4}}, "unicode_ranges": [{"name": "Arrows", "start": "0x2190", "end": "0x21FF", "description": "Arrow symbols"}, {"name": "Mathematical Operators", "start": "0x2200", "end": "0x22FF", "description": "Mathematical operator symbols"}, {"name": "Miscellaneous Technical", "start": "0x2300", "end": "0x23FF", "description": "Technical symbols"}, {"name": "Control Pictures", "start": "0x2400", "end": "0x243F", "description": "Control character representations"}, {"name": "Optical Character Recognition", "start": "0x2440", "end": "0x245F", "description": "OCR symbols"}, {"name": "Enclosed Alphanumerics", "start": "0x2460", "end": "0x24FF", "description": "Circled and enclosed characters"}, {"name": "Box Drawing", "start": "0x2500", "end": "0x257F", "description": "Box drawing characters"}, {"name": "Block Elements", "start": "0x2580", "end": "0x259F", "description": "Block element symbols"}, {"name": "Geometric Shapes", "start": "0x25A0", "end": "0x25FF", "description": "Geometric shape symbols"}, {"name": "Miscellaneous Symbols", "start": "0x2600", "end": "0x26FF", "description": "Miscellaneous symbols"}, {"name": "Dingbats", "start": "0x2700", "end": "0x27BF", "description": "Dingbat symbols"}, {"name": "Miscellaneous Mathematical Symbols-A", "start": "0x27C0", "end": "0x27EF", "description": "Additional mathematical symbols"}, {"name": "Supplemental Arrows-A", "start": "0x27F0", "end": "0x27FF", "description": "Additional arrow symbols"}, {"name": "Supplemental Arrows-B", "start": "0x2900", "end": "0x297F", "description": "More arrow symbols"}, {"name": "Miscellaneous Mathematical Symbols-B", "start": "0x2980", "end": "0x29FF", "description": "More mathematical symbols"}, {"name": "Supplemental Mathematical Operators", "start": "0x2A00", "end": "0x2AFF", "description": "Additional mathematical operators"}, {"name": "Miscellaneous Symbols and Arrows", "start": "0x2B00", "end": "0x2BFF", "description": "Mixed symbols and arrows"}], "semantic_names": {"arithmetic": ["add", "sub", "mul", "div", "mod", "pow", "sqrt", "abs", "neg", "inc", "dec"], "comparison": ["eq", "ne", "lt", "le", "gt", "ge", "cmp", "min", "max", "clamp"], "bitwise": ["and", "or", "xor", "not", "shl", "shr", "rol", "ror", "mask", "flip"], "assignment": ["assign", "add_assign", "sub_assign", "mul_assign", "div_assign", "mod_assign"], "matrix": ["transpose", "inverse", "determinant", "trace", "eigenvalue", "eigenvector"], "propositional": ["and", "or", "not", "implies", "iff", "xor", "nand", "nor"], "predicate": ["forall", "exists", "unique", "lambda", "apply", "compose", "curry"], "modal": ["necessary", "possible", "knows", "believes", "ought", "permitted"], "temporal": ["always", "eventually", "until", "since", "next", "previous"], "inference": ["entails", "proves", "derives", "concludes", "assumes", "supposes"], "class": ["class", "interface", "abstract", "concrete", "inherit", "implement"], "function": ["function", "method", "procedure", "lambda", "closure", "partial"], "module": ["module", "package", "namespace", "import", "export", "include"], "scope": ["global", "local", "private", "public", "protected", "static"], "conditional": ["if", "else", "elif", "switch", "case", "default", "when"], "loop": ["for", "while", "do", "repeat", "until", "foreach", "map", "filter"], "exception": ["try", "catch", "throw", "finally", "raise", "handle", "rescue"], "async": ["async", "await", "promise", "future", "task", "thread", "process"], "allocate": ["alloc", "malloc", "calloc", "realloc", "new", "create", "reserve"], "reference": ["ref", "deref", "pointer", "address", "offset", "index", "slice"], "cache": ["cache", "memoize", "buffer", "prefetch", "evict", "flush", "sync"], "induction": ["induct", "base_case", "inductive_step", "generalize", "pattern"], "deduction": ["deduce", "syllogism", "modus_ponens", "modus_tollens", "contrapositive"], "analogy": ["analogous", "similar", "metaphor", "compare", "contrast", "relate"], "reflection": ["reflect", "introspect", "meta", "self", "type_of", "instance_of"], "recursion": ["recurse", "fixpoint", "iterate", "unfold", "fold", "reduce"], "compilation": ["compile", "parse", "lex", "optimize", "transform", "generate"], "qubit": ["qubit", "superposition", "collapse", "measure", "observe", "state"], "entanglement": ["entangle", "bell_state", "epr", "spooky", "correlate", "separate"], "gate": ["<PERSON><PERSON><PERSON>", "pauli_x", "pauli_y", "pauli_z", "cnot", "to<PERSON><PERSON>", "fredkin"]}, "quality_criteria": {"min_unicode_uniqueness": 0.95, "min_semantic_clarity": 0.9, "max_token_cost": 2, "min_token_density": 0.85, "max_fallback_length": 8, "required_ascii_fallback": true}, "output_paths": {"registry": "core/symbols_registry.json", "symbols": "core/symbols_ultra.json", "logs": "logs/ultra_generation.log", "reports": "logs/ultra_generation_report.txt"}}