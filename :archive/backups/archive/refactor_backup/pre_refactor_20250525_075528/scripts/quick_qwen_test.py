#!/usr/bin/env python3
"""
Test rapido per Qwen2.5-Coder-7B su Mac M2 8GB
Verifica solo caricamento e compatibilità base
"""

import os
import sys
import time
import psutil
from pathlib import Path

def check_memory():
    """Verifica memoria disponibile"""
    memory = psutil.virtual_memory()
    available_gb = memory.available / (1024**3)
    used_percent = memory.percent
    
    print(f"💾 Memoria disponibile: {available_gb:.1f} GB ({100-used_percent:.1f}% libera)")
    
    if available_gb < 2.0:
        print("⚠️ ATTENZIONE: Memoria molto limitata")
        print("💡 Chiudi altre applicazioni per liberare memoria")
        return False
    elif available_gb < 4.0:
        print("⚠️ Memoria limitata ma sufficiente per test base")
        return True
    else:
        print("✅ Memoria sufficiente")
        return True

def test_model_loading_light():
    """Test caricamento modello con configurazione leggera"""
    print("\n🧠 TEST CARICAMENTO LEGGERO")
    print("=" * 50)
    
    try:
        from llama_cpp import Llama
        
        model_path = "model/qwen2.5coder/qwen2.5-coder-7b-instruct-q4_k_m.gguf"
        
        if not os.path.exists(model_path):
            print(f"❌ Modello non trovato: {model_path}")
            return False
        
        print(f"🔄 Caricamento modello con configurazione ottimizzata...")
        start_time = time.time()
        
        # Configurazione molto leggera per Mac M2 8GB
        llm = Llama(
            model_path=model_path,
            n_ctx=512,         # Context ridotto
            n_threads=4,       # Meno thread
            n_gpu_layers=0,    # Solo CPU per ora
            verbose=False,
            use_mmap=True,     # Memory mapping
            use_mlock=False,   # Non bloccare memoria
            n_batch=128        # Batch size ridotto
        )
        
        load_time = time.time() - start_time
        print(f"✅ Modello caricato in {load_time:.2f} secondi")
        
        return llm
        
    except Exception as e:
        print(f"❌ Errore caricamento: {e}")
        return None

def test_simple_generation(llm):
    """Test generazione molto semplice"""
    print("\n⚡ TEST GENERAZIONE SEMPLICE")
    print("=" * 50)
    
    if llm is None:
        print("❌ Modello non caricato")
        return False
    
    try:
        # Prompt molto semplice
        prompt = "def hello():"
        
        print(f"📝 Prompt: {prompt}")
        print("🔄 Generazione...")
        
        start_time = time.time()
        
        response = llm(
            prompt,
            max_tokens=20,     # Molto pochi token
            temperature=0.1,   # Deterministico
            echo=False,
            stop=["\n\n"]     # Stop early
        )
        
        inference_time = time.time() - start_time
        generated_text = response['choices'][0]['text']
        
        print(f"⏱️ Tempo: {inference_time:.2f}s")
        print(f"📄 Output: {repr(generated_text)}")
        
        if len(generated_text.strip()) > 0:
            print("✅ Generazione riuscita!")
            return True
        else:
            print("⚠️ Output vuoto")
            return False
        
    except Exception as e:
        print(f"❌ Errore generazione: {e}")
        return False

def main():
    """Test rapido"""
    print("🚀 QWEN2.5-CODER QUICK TEST")
    print("🎯 Mac M2 8GB Compatibility Check")
    print("=" * 50)
    
    # Check memoria
    if not check_memory():
        print("\n❌ Memoria insufficiente per test sicuro")
        print("💡 Chiudi altre app e riprova")
        return False
    
    # Test caricamento
    llm = test_model_loading_light()
    if llm is None:
        print("\n❌ Caricamento fallito")
        return False
    
    # Test generazione
    success = test_simple_generation(llm)
    
    # Risultato
    print("\n" + "=" * 50)
    if success:
        print("🎉 QWEN2.5-CODER FUNZIONA SU MAC M2 8GB!")
        print("✅ Modello compatibile")
        print("✅ Generazione funzionante")
        print("💡 Pronto per integrazione NEUROGLYPH")
    else:
        print("⚠️ Test parzialmente riuscito")
        print("🔧 Potrebbe richiedere ottimizzazioni")
    
    return success

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
