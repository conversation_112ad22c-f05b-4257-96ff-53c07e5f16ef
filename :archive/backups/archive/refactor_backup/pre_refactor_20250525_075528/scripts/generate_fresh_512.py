#!/usr/bin/env python3
"""
🧠 NEUROGLYPH ULTRA - Fresh 512 Symbol Generator
================================================

Generatore che crea 512 simboli freschi seguendo i criteri ULTRA definitivi.
Bypassa problemi con registry esistente e crea un nuovo set pulito.

Usage: python generate_fresh_512.py [--output fresh_symbols.json]
"""

import json
import sys
import random
import unicodedata
from datetime import datetime
from typing import Dict, Any, List, Set
from pathlib import Path
import argparse

class Fresh512Generator:
    """Generatore di 512 simboli NEUROGLYPH ULTRA freschi."""
    
    def __init__(self, output_file: str = "core/fresh_512_symbols.json"):
        self.output_file = Path(output_file)
        
        # Set per evitare duplicati
        self.used_symbols: Set[str] = set()
        self.used_codes: Set[str] = set()
        self.used_fallbacks: Set[str] = set()
        
        # Contatori
        self.generated_count = 0
        self.attempt_count = 0
        
        # Categorie con distribuzione ULTRA (totale 512)
        self.categories = {
            "logic": {
                "target": 96,  # 18.75%
                "meanings": ["and", "or", "not", "implies", "iff", "xor", "nand", "nor", "forall", "exists", 
                           "unique", "lambda", "apply", "compose", "curry", "necessary", "possible", "knows", 
                           "believes", "ought", "permitted", "always", "eventually", "until", "since", "next", 
                           "previous", "entails", "proves", "derives", "concludes", "assumes", "supposes",
                           "infer", "deduce", "induce", "reason", "think", "conclude", "premise", "axiom",
                           "theorem", "lemma", "proof", "valid", "sound", "complete", "consistent"]
            },
            "structure": {
                "target": 80,  # 15.6%
                "meanings": ["class", "interface", "abstract", "concrete", "inherit", "implement", "function", 
                           "method", "procedure", "lambda", "closure", "partial", "module", "package", 
                           "namespace", "import", "export", "include", "global", "local", "private", "public", 
                           "protected", "static", "array", "list", "dict", "set", "tuple", "struct",
                           "object", "instance", "property", "field", "attribute", "member", "constructor",
                           "destructor", "getter", "setter", "accessor", "mutator", "factory", "builder"]
            },
            "flow": {
                "target": 72,  # 14.1%
                "meanings": ["if", "else", "elif", "switch", "case", "default", "when", "for", "while", "do", 
                           "repeat", "until", "foreach", "map", "filter", "try", "catch", "throw", "finally", 
                           "raise", "handle", "rescue", "async", "await", "promise", "future", "task", "thread", 
                           "process", "yield", "break", "continue", "return", "goto", "call", "invoke",
                           "execute", "run", "start", "stop", "pause", "resume", "suspend", "terminate"]
            },
            "operator": {
                "target": 64,  # 12.5%
                "meanings": ["add", "sub", "mul", "div", "mod", "pow", "sqrt", "abs", "neg", "inc", "dec", 
                           "eq", "ne", "lt", "le", "gt", "ge", "min", "max", "clamp", "and", "or", "xor", 
                           "not", "shl", "shr", "assign", "add_assign", "sub_assign", "mul_assign", "div_assign",
                           "mod_assign", "xor_assign", "or_assign", "and_assign", "concat", "append", "prepend"]
            },
            "memory": {
                "target": 64,  # 12.5%
                "meanings": ["alloc", "malloc", "calloc", "realloc", "new", "create", "reserve", "free", 
                           "delete", "destroy", "ref", "deref", "pointer", "address", "offset", "index", 
                           "slice", "cache", "memoize", "buffer", "prefetch", "evict", "flush", "sync", 
                           "stack", "heap", "register", "volatile", "persistent", "transient", "immutable", 
                           "mutable", "copy", "move", "clone", "share", "borrow", "own", "lease", "lock"]
            },
            "reasoning": {
                "target": 56,  # 10.9%
                "meanings": ["induct", "base_case", "inductive_step", "generalize", "pattern", "deduce", 
                           "syllogism", "modus_ponens", "modus_tollens", "contrapositive", "analogous", 
                           "similar", "metaphor", "compare", "contrast", "relate", "causality", "correlation", 
                           "similarity", "difference", "abstraction", "generalization", "specialization", 
                           "classification", "categorize", "cluster", "group", "separate", "analyze",
                           "synthesize", "evaluate", "judge", "decide", "choose", "select", "prefer"]
            },
            "meta": {
                "target": 48,  # 9.4%
                "meanings": ["reflect", "introspect", "meta", "self", "type_of", "instance_of", "recurse", 
                           "fixpoint", "iterate", "unfold", "fold", "reduce", "compile", "parse", "lex", 
                           "optimize", "transform", "generate", "interpret", "evaluate", "debug", "profile", 
                           "trace", "monitor", "measure", "benchmark", "test", "verify", "validate",
                           "check", "assert", "ensure", "guarantee", "prove", "disprove", "refute"]
            },
            "quantum": {
                "target": 32,  # 6.25%
                "meanings": ["qubit", "superposition", "collapse", "measure", "observe", "state", "entangle", 
                           "bell_state", "epr", "spooky", "correlate", "separate", "hadamard", "pauli_x", 
                           "pauli_y", "pauli_z", "cnot", "toffoli", "fredkin", "teleport", "encrypt", 
                           "decrypt", "error_correct", "decohere", "interfere", "phase", "amplitude",
                           "probability", "wave", "particle", "duality", "uncertainty"]
            }
        }
        
        # Unicode ranges ottimizzati per LLM (1 token garantito)
        self.unicode_ranges = [
            (0x2190, 0x21FF),  # Arrows
            (0x2200, 0x22FF),  # Mathematical Operators  
            (0x2300, 0x23FF),  # Miscellaneous Technical
            (0x25A0, 0x25FF),  # Geometric Shapes
            (0x2600, 0x26FF),  # Miscellaneous Symbols
            (0x27C0, 0x27EF),  # Miscellaneous Mathematical Symbols-A
            (0x2980, 0x29FF),  # Miscellaneous Mathematical Symbols-B
            (0x2A00, 0x2AFF),  # Supplemental Mathematical Operators
        ]
        
    def generate_symbol_for_meaning(self, category: str, meaning: str) -> Dict[str, Any]:
        """Genera simbolo per categoria e significato specifico."""
        max_attempts = 100
        
        for attempt in range(max_attempts):
            self.attempt_count += 1
            
            try:
                # Seleziona range Unicode
                start, end = random.choice(self.unicode_ranges)
                unicode_point = random.randint(start, end)
                symbol = chr(unicode_point)
                
                # Verifica unicità
                if symbol in self.used_symbols:
                    continue
                    
                # Verifica carattere valido
                try:
                    unicode_name = unicodedata.name(symbol)
                except ValueError:
                    continue
                    
                # Genera codice
                code = f"ng:{category}:{meaning}"
                if code in self.used_codes:
                    # Aggiungi suffisso numerico
                    suffix = 1
                    while f"{code}_{suffix}" in self.used_codes:
                        suffix += 1
                    code = f"{code}_{suffix}"
                    
                # Genera fallback
                fallback_base = meaning.upper().replace('_', '')[:6]
                fallback = f"[{fallback_base}]"
                if fallback in self.used_fallbacks:
                    suffix = 1
                    while f"[{fallback_base}{suffix}]" in self.used_fallbacks:
                        suffix += 1
                    fallback = f"[{fallback_base}{suffix}]"
                    
                # Validazione base
                if not self._validate_basic(symbol, code, fallback):
                    continue
                    
                # Calcola score
                token_cost = self._estimate_token_cost(symbol)
                token_density = 1.0 / token_cost
                quality_score = (
                    0.4 * token_density +
                    0.3 * (1 if token_cost <= 2 else 0) +
                    0.2 * (1 if len(symbol) == 1 else 0) +
                    0.1 * (1 if fallback.isascii() else 0)
                ) * 100
                
                # Crea simbolo
                symbol_data = {
                    "id": f"NG{self.generated_count + 1:04d}",
                    "symbol": symbol,
                    "code": code,
                    "fallback": fallback,
                    "category": category,
                    "meaning": meaning,
                    "description": f"{category.title()} operation: {meaning}",
                    "unicode_point": f"U+{unicode_point:04X}",
                    "unicode_name": unicode_name,
                    "token_cost": token_cost,
                    "token_density": token_density,
                    "quality_score": quality_score,
                    "llm_support": ["openai", "qwen", "llama"],
                    "created": datetime.now().strftime("%Y-%m-%d"),
                    "generator": "fresh_512_v1"
                }
                
                # Aggiorna set usati
                self.used_symbols.add(symbol)
                self.used_codes.add(code)
                self.used_fallbacks.add(fallback)
                
                return symbol_data
                
            except Exception as e:
                continue
                
        raise Exception(f"Could not generate symbol for {category}:{meaning} after {max_attempts} attempts")
        
    def _validate_basic(self, symbol: str, code: str, fallback: str) -> bool:
        """Validazione base."""
        if len(symbol) != 1:
            return False
        if not code.startswith("ng:"):
            return False
        if len(code.split(":")) < 3:
            return False
        if not fallback.isascii():
            return False
        return True
        
    def _estimate_token_cost(self, symbol: str) -> int:
        """Stima costo token."""
        code_point = ord(symbol)
        if code_point < 0x80:  # ASCII
            return 1
        elif 0x2000 <= code_point <= 0x2FFF:  # Mathematical symbols
            return 1
        else:
            return 2
            
    def generate_all_symbols(self) -> List[Dict[str, Any]]:
        """Genera tutti i 512 simboli."""
        all_symbols = []
        
        print("🚀 Generating 512 NEUROGLYPH ULTRA symbols...")
        print("=" * 60)
        
        for category, config in self.categories.items():
            target = config["target"]
            meanings = config["meanings"]
            
            print(f"🔄 {category}: generating {target} symbols...")
            
            # Seleziona meanings per questa categoria
            if len(meanings) >= target:
                selected_meanings = random.sample(meanings, target)
            else:
                # Se non ci sono abbastanza meanings, ripeti alcuni
                selected_meanings = meanings * (target // len(meanings) + 1)
                selected_meanings = selected_meanings[:target]
                
            # Genera simboli
            category_symbols = []
            for i, meaning in enumerate(selected_meanings):
                try:
                    symbol_data = self.generate_symbol_for_meaning(category, meaning)
                    category_symbols.append(symbol_data)
                    self.generated_count += 1
                    
                    if (i + 1) % 10 == 0:
                        print(f"  ✅ {i + 1}/{target} symbols generated")
                        
                except Exception as e:
                    print(f"  ❌ Failed to generate symbol for {meaning}: {e}")
                    
            all_symbols.extend(category_symbols)
            print(f"✅ {category}: {len(category_symbols)}/{target} symbols completed")
            
        print(f"\n🎉 Generation completed!")
        print(f"✅ Total symbols: {len(all_symbols)}")
        print(f"📊 Attempts: {self.attempt_count}")
        print(f"⚡ Success rate: {(len(all_symbols)/self.attempt_count*100):.1f}%")
        
        return all_symbols
        
    def save_symbols(self, symbols: List[Dict[str, Any]]):
        """Salva simboli in file JSON."""
        output_data = {
            "registry_version": "3.0",
            "description": "NEUROGLYPH ULTRA - Fresh 512 Symbols",
            "created": datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
            "total_symbols": len(symbols),
            "generator": "fresh_512_v1",
            "criteria": {
                "usu": "Unicode Symbolic Uniqueness",
                "ctu": "Coded Textual Unification", 
                "lcl": "LLM Compatibility Layer"
            },
            "categories": {cat: {"target": config["target"], "actual": len([s for s in symbols if s["category"] == cat])} 
                         for cat, config in self.categories.items()},
            "symbols": symbols
        }
        
        # Crea directory se non esiste
        self.output_file.parent.mkdir(parents=True, exist_ok=True)
        
        # Salva file
        with open(self.output_file, 'w', encoding='utf-8') as f:
            json.dump(output_data, f, indent=2, ensure_ascii=False)
            
        print(f"💾 Symbols saved to: {self.output_file}")
        print(f"📁 File size: {self.output_file.stat().st_size / 1024:.1f} KB")


def main():
    """Main function."""
    parser = argparse.ArgumentParser(description="Generate fresh 512 NEUROGLYPH ULTRA symbols")
    parser.add_argument("--output", default="core/fresh_512_symbols.json", help="Output file path")
    
    args = parser.parse_args()
    
    print("🧠 NEUROGLYPH ULTRA - Fresh 512 Symbol Generator")
    print("=" * 60)
    
    generator = Fresh512Generator(output_file=args.output)
    
    try:
        symbols = generator.generate_all_symbols()
        generator.save_symbols(symbols)
        
        print("\n🎉 SUCCESS! 512 NEUROGLYPH ULTRA symbols generated!")
        print("🚀 Ready for first thinking LLM!")
        
    except Exception as e:
        print(f"\n❌ ERROR: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)


if __name__ == "__main__":
    main()
