#!/usr/bin/env python3
"""
NEUROGLYPH ULTRA - Symbol Validation Tool
==========================================

Valida rigorosamente i 512 simboli secondo criteri USU/CTU/LCL:
- USU: Unicità Simbolica Universale (duplicati, similarità visiva, Unicode)
- CTU: Codifica Testuale Unificata (formato ng:category:function)
- LCL: LLM Compatibility Layer (tokenizer, costo, visibilità)
"""

import json
import unicodedata
from typing import List, Dict, Any, Set, Tuple
from collections import defaultdict
import re

class UltraSymbolValidator:
    """Validatore rigoroso per simboli NEUROGLYPH ULTRA."""
    
    def __init__(self, symbols_path: str = "core/symbols.json"):
        self.symbols_path = symbols_path
        self.symbols = []
        self.validation_report = {
            "total_symbols": 0,
            "usu_validation": {},
            "ctu_validation": {},
            "lcl_validation": {},
            "errors": [],
            "warnings": [],
            "score": 0.0
        }
    
    def load_symbols(self) -> bool:
        """Carica simboli dal file JSON."""
        try:
            with open(self.symbols_path, 'r', encoding='utf-8') as f:
                self.symbols = json.load(f)
            self.validation_report["total_symbols"] = len(self.symbols)
            print(f"📊 Caricati {len(self.symbols)} simboli da validare")
            return True
        except Exception as e:
            self.validation_report["errors"].append(f"Errore caricamento: {e}")
            return False
    
    def validate_usu_criteria(self) -> Dict[str, Any]:
        """Valida criteri USU - Unicità Simbolica Universale."""
        print("🔹 Validazione USU - Unicità Simbolica Universale...")
        
        usu_results = {
            "unique_symbols": True,
            "unique_unicode_points": True,
            "visual_similarity_check": True,
            "semantic_atomicity": True,
            "duplicates": [],
            "similar_symbols": [],
            "multi_meaning_symbols": []
        }
        
        # 1. Test duplicati simboli
        symbols_seen = set()
        for i, symbol_entry in enumerate(self.symbols):
            symbol = symbol_entry.get("symbol", "")
            if symbol in symbols_seen:
                usu_results["duplicates"].append({
                    "symbol": symbol,
                    "positions": [j for j, s in enumerate(self.symbols) if s.get("symbol") == symbol]
                })
                usu_results["unique_symbols"] = False
            symbols_seen.add(symbol)
        
        # 2. Test duplicati Unicode points
        unicode_points_seen = set()
        for symbol_entry in self.symbols:
            unicode_point = symbol_entry.get("unicode_point", "")
            if unicode_point in unicode_points_seen:
                usu_results["unique_unicode_points"] = False
            unicode_points_seen.add(unicode_point)
        
        # 3. Test similarità visiva (simboli che differiscono di poco)
        for i, symbol1 in enumerate(self.symbols):
            for j, symbol2 in enumerate(self.symbols[i+1:], i+1):
                if self._are_visually_similar(symbol1.get("symbol", ""), symbol2.get("symbol", "")):
                    usu_results["similar_symbols"].append({
                        "symbol1": symbol1.get("symbol"),
                        "symbol2": symbol2.get("symbol"),
                        "names": [symbol1.get("name"), symbol2.get("name")]
                    })
                    usu_results["visual_similarity_check"] = False
        
        # 4. Test atomicità semantica (un simbolo = un significato)
        for symbol_entry in enumerate(self.symbols):
            if self._has_multiple_meanings(symbol_entry):
                usu_results["multi_meaning_symbols"].append(symbol_entry)
                usu_results["semantic_atomicity"] = False
        
        self.validation_report["usu_validation"] = usu_results
        return usu_results
    
    def validate_ctu_criteria(self) -> Dict[str, Any]:
        """Valida criteri CTU - Codifica Testuale Unificata."""
        print("🔹 Validazione CTU - Codifica Testuale Unificata...")
        
        ctu_results = {
            "valid_format": True,
            "unique_codes": True,
            "valid_categories": True,
            "valid_functions": True,
            "invalid_codes": [],
            "duplicate_codes": [],
            "invalid_categories": [],
            "invalid_functions": []
        }
        
        codes_seen = set()
        valid_categories = {
            "action", "structure", "state", "logic", "entity", 
            "domain", "flow", "data", "meta", "system", "memory", "reasoning"
        }
        
        for symbol_entry in self.symbols:
            code = symbol_entry.get("code", "")
            
            # 1. Test formato ng:category:function
            if not re.match(r'^ng:[a-z_]+:[a-z_]+$', code):
                ctu_results["invalid_codes"].append({
                    "symbol": symbol_entry.get("symbol"),
                    "code": code,
                    "reason": "Formato non valido (deve essere ng:category:function)"
                })
                ctu_results["valid_format"] = False
            
            # 2. Test duplicati codici
            if code in codes_seen:
                ctu_results["duplicate_codes"].append(code)
                ctu_results["unique_codes"] = False
            codes_seen.add(code)
            
            # 3. Test categoria valida
            parts = code.split(":")
            if len(parts) == 3:
                category = parts[1]
                function = parts[2]
                
                if category not in valid_categories:
                    ctu_results["invalid_categories"].append({
                        "symbol": symbol_entry.get("symbol"),
                        "category": category
                    })
                    ctu_results["valid_categories"] = False
                
                # 4. Test funzione valida (solo lettere minuscole e underscore)
                if not re.match(r'^[a-z_]+$', function):
                    ctu_results["invalid_functions"].append({
                        "symbol": symbol_entry.get("symbol"),
                        "function": function
                    })
                    ctu_results["valid_functions"] = False
        
        self.validation_report["ctu_validation"] = ctu_results
        return ctu_results
    
    def validate_lcl_criteria(self) -> Dict[str, Any]:
        """Valida criteri LCL - LLM Compatibility Layer."""
        print("🔹 Validazione LCL - LLM Compatibility Layer...")
        
        lcl_results = {
            "utf8_compatible": True,
            "ascii_fallback_valid": True,
            "font_compatible": True,
            "tokenizer_friendly": True,
            "problematic_symbols": [],
            "invalid_fallbacks": [],
            "font_issues": [],
            "tokenizer_issues": []
        }
        
        for symbol_entry in self.symbols:
            symbol = symbol_entry.get("symbol", "")
            fallback = symbol_entry.get("fallback", "")
            
            # 1. Test UTF-8 compatibility
            try:
                symbol.encode('utf-8')
            except UnicodeEncodeError:
                lcl_results["problematic_symbols"].append({
                    "symbol": symbol,
                    "issue": "UTF-8 encoding error"
                })
                lcl_results["utf8_compatible"] = False
            
            # 2. Test ASCII fallback format
            if not re.match(r'^\[[A-Z_]+\]$', fallback):
                lcl_results["invalid_fallbacks"].append({
                    "symbol": symbol,
                    "fallback": fallback,
                    "reason": "Deve essere formato [UPPERCASE]"
                })
                lcl_results["ascii_fallback_valid"] = False
            
            # 3. Test caratteri problematici per font
            if self._has_font_issues(symbol):
                lcl_results["font_issues"].append({
                    "symbol": symbol,
                    "issue": "Potenziali problemi di visualizzazione"
                })
                lcl_results["font_compatible"] = False
            
            # 4. Test tokenizer friendliness (simboli che potrebbero essere problematici)
            if self._has_tokenizer_issues(symbol):
                lcl_results["tokenizer_issues"].append({
                    "symbol": symbol,
                    "issue": "Potenziali problemi con tokenizer"
                })
                lcl_results["tokenizer_friendly"] = False
        
        self.validation_report["lcl_validation"] = lcl_results
        return lcl_results
    
    def _are_visually_similar(self, symbol1: str, symbol2: str) -> bool:
        """Controlla se due simboli sono visivamente simili."""
        if not symbol1 or not symbol2:
            return False
        
        # Simboli identici
        if symbol1 == symbol2:
            return True
        
        # Simboli con nomi Unicode simili
        try:
            name1 = unicodedata.name(symbol1[0], "").lower()
            name2 = unicodedata.name(symbol2[0], "").lower()
            
            # Controlli di similarità
            similar_patterns = [
                ("circle", "ring"), ("square", "rectangle"), 
                ("left", "right"), ("up", "down"),
                ("small", "large"), ("black", "white")
            ]
            
            for pattern1, pattern2 in similar_patterns:
                if (pattern1 in name1 and pattern2 in name2) or (pattern2 in name1 and pattern1 in name2):
                    return True
                    
        except ValueError:
            pass
        
        return False
    
    def _has_multiple_meanings(self, symbol_entry: Any) -> bool:
        """Controlla se un simbolo ha significati multipli."""
        # Placeholder - da implementare logica più sofisticata
        description = symbol_entry.get("description", "").lower()
        aliases = symbol_entry.get("aliases", [])
        
        # Se ha troppi alias diversi, potrebbe non essere atomico
        return len(aliases) > 5
    
    def _has_font_issues(self, symbol: str) -> bool:
        """Controlla se un simbolo ha potenziali problemi di font."""
        if not symbol:
            return True
        
        # Caratteri che spesso hanno problemi di visualizzazione
        problematic_ranges = [
            (0x1F000, 0x1F9FF),  # Emoji e simboli vari
            (0x2600, 0x26FF),    # Simboli vari
            (0x1F300, 0x1F5FF),  # Simboli e pittogrammi vari
        ]
        
        for char in symbol:
            code_point = ord(char)
            for start, end in problematic_ranges:
                if start <= code_point <= end:
                    return True
        
        return False
    
    def _has_tokenizer_issues(self, symbol: str) -> bool:
        """Controlla se un simbolo ha potenziali problemi con tokenizer."""
        if not symbol:
            return True
        
        # Simboli che spesso causano problemi ai tokenizer
        problematic_chars = set("🧠📌🔍💾🎯📊🔄⚙🔧")
        
        return any(char in problematic_chars for char in symbol)
    
    def generate_report(self) -> str:
        """Genera report completo di validazione."""
        report = []
        report.append("🧠 NEUROGLYPH ULTRA - Symbol Validation Report")
        report.append("=" * 60)
        report.append(f"📊 Total Symbols: {self.validation_report['total_symbols']}")
        report.append("")
        
        # USU Results
        usu = self.validation_report.get("usu_validation", {})
        report.append("🔹 USU - Unicità Simbolica Universale:")
        report.append(f"  ✅ Simboli unici: {usu.get('unique_symbols', False)}")
        report.append(f"  ✅ Unicode points unici: {usu.get('unique_unicode_points', False)}")
        report.append(f"  ✅ Similarità visiva OK: {usu.get('visual_similarity_check', False)}")
        report.append(f"  ✅ Atomicità semantica: {usu.get('semantic_atomicity', False)}")
        
        if usu.get("duplicates"):
            report.append(f"  ❌ Duplicati trovati: {len(usu['duplicates'])}")
        if usu.get("similar_symbols"):
            report.append(f"  ⚠️ Simboli simili: {len(usu['similar_symbols'])}")
        
        report.append("")
        
        # CTU Results
        ctu = self.validation_report.get("ctu_validation", {})
        report.append("🔹 CTU - Codifica Testuale Unificata:")
        report.append(f"  ✅ Formato valido: {ctu.get('valid_format', False)}")
        report.append(f"  ✅ Codici unici: {ctu.get('unique_codes', False)}")
        report.append(f"  ✅ Categorie valide: {ctu.get('valid_categories', False)}")
        report.append(f"  ✅ Funzioni valide: {ctu.get('valid_functions', False)}")
        
        if ctu.get("invalid_codes"):
            report.append(f"  ❌ Codici non validi: {len(ctu['invalid_codes'])}")
        
        report.append("")
        
        # LCL Results
        lcl = self.validation_report.get("lcl_validation", {})
        report.append("🔹 LCL - LLM Compatibility Layer:")
        report.append(f"  ✅ UTF-8 compatibile: {lcl.get('utf8_compatible', False)}")
        report.append(f"  ✅ ASCII fallback valido: {lcl.get('ascii_fallback_valid', False)}")
        report.append(f"  ✅ Font compatibile: {lcl.get('font_compatible', False)}")
        report.append(f"  ✅ Tokenizer friendly: {lcl.get('tokenizer_friendly', False)}")
        
        if lcl.get("problematic_symbols"):
            report.append(f"  ⚠️ Simboli problematici: {len(lcl['problematic_symbols'])}")
        
        report.append("")
        
        # Score finale
        total_checks = 12  # Numero totale di controlli
        passed_checks = sum([
            usu.get('unique_symbols', False),
            usu.get('unique_unicode_points', False), 
            usu.get('visual_similarity_check', False),
            usu.get('semantic_atomicity', False),
            ctu.get('valid_format', False),
            ctu.get('unique_codes', False),
            ctu.get('valid_categories', False),
            ctu.get('valid_functions', False),
            lcl.get('utf8_compatible', False),
            lcl.get('ascii_fallback_valid', False),
            lcl.get('font_compatible', False),
            lcl.get('tokenizer_friendly', False)
        ])
        
        score = (passed_checks / total_checks) * 100
        self.validation_report["score"] = score
        
        report.append(f"🎯 VALIDATION SCORE: {score:.1f}% ({passed_checks}/{total_checks})")
        
        if score >= 90:
            report.append("🚀 ULTRA READY: Simboli pronti per primo LLM pensante!")
        elif score >= 75:
            report.append("⚠️ NEEDS IMPROVEMENT: Alcuni problemi da risolvere")
        else:
            report.append("❌ NOT READY: Molti problemi da correggere")
        
        return "\n".join(report)
    
    def validate_all(self) -> bool:
        """Esegue validazione completa."""
        if not self.load_symbols():
            return False
        
        self.validate_usu_criteria()
        self.validate_ctu_criteria() 
        self.validate_lcl_criteria()
        
        return True

def main():
    """Esegue validazione completa dei simboli ULTRA."""
    print("🧠 NEUROGLYPH ULTRA - Symbol Validation")
    print("=" * 50)
    
    validator = UltraSymbolValidator()
    
    if validator.validate_all():
        report = validator.generate_report()
        print(report)
        
        # Salva report
        with open("validation_report.txt", "w", encoding="utf-8") as f:
            f.write(report)
        print(f"\n💾 Report salvato in: validation_report.txt")
        
        return validator.validation_report["score"] >= 90
    else:
        print("❌ Errore durante validazione")
        return False

if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)
