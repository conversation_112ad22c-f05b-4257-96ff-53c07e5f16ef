#!/usr/bin/env python3
"""
NEUROGLYPH LLM - Test Setup Training
===================================

Test rapido per verificare che tutto sia pronto per il training.
"""

import os
import sys
import json
import torch
from pathlib import Path

def test_basic_imports():
    """Test import delle librerie necessarie"""
    print("📦 TEST IMPORT LIBRERIE")
    print("=" * 50)
    
    try:
        import torch
        print(f"✅ PyTorch {torch.__version__}")
        
        from transformers import AutoTokenizer, AutoModelForCausalLM
        print("✅ Transformers")
        
        from datasets import Dataset
        print("✅ Datasets")
        
        from peft import LoraConfig, get_peft_model
        print("✅ PEFT")
        
        return True
        
    except ImportError as e:
        print(f"❌ Import fallito: {e}")
        return False

def test_model_loading():
    """Test caricamento modello"""
    print("\n🧠 TEST CARICAMENTO MODELLO")
    print("=" * 50)
    
    try:
        from transformers import AutoTokenizer
        
        model_path = "model/qwen2.5coder"
        
        # Test tokenizer
        print("📝 Test tokenizer...")
        tokenizer = AutoTokenizer.from_pretrained(model_path)
        print(f"✅ Tokenizer caricato (vocab: {tokenizer.vocab_size})")
        
        # Test tokenizzazione
        test_text = "def hello(): return 'Hello World'"
        tokens = tokenizer.encode(test_text)
        decoded = tokenizer.decode(tokens)
        
        print(f"   Input: {test_text}")
        print(f"   Tokens: {len(tokens)}")
        print(f"   Decoded: {decoded}")
        
        return True
        
    except Exception as e:
        print(f"❌ Errore caricamento: {e}")
        return False

def test_dataset_loading():
    """Test caricamento dataset"""
    print("\n📊 TEST DATASET")
    print("=" * 50)
    
    try:
        train_path = "datasets/neuroglyph_train.jsonl"
        
        if not Path(train_path).exists():
            print(f"❌ Dataset non trovato: {train_path}")
            return False
        
        # Carica esempi
        examples = []
        with open(train_path, "r", encoding="utf-8") as f:
            for line in f:
                if line.strip():
                    examples.append(json.loads(line))
        
        print(f"✅ Dataset caricato: {len(examples)} esempi")
        
        # Mostra primo esempio
        if examples:
            example = examples[0]
            print(f"\n📝 Primo esempio:")
            print(f"   Instruction: {example['instruction']}")
            print(f"   Input: {example['input']}")
            print(f"   Output: {example['output'][:50]}...")
        
        return True
        
    except Exception as e:
        print(f"❌ Errore dataset: {e}")
        return False

def test_memory_usage():
    """Test utilizzo memoria"""
    print("\n💾 TEST MEMORIA")
    print("=" * 50)
    
    try:
        import psutil
        
        memory = psutil.virtual_memory()
        available_gb = memory.available / (1024**3)
        used_gb = memory.used / (1024**3)
        total_gb = memory.total / (1024**3)
        
        print(f"📊 Memoria totale: {total_gb:.1f} GB")
        print(f"📊 Memoria usata: {used_gb:.1f} GB")
        print(f"📊 Memoria disponibile: {available_gb:.1f} GB")
        print(f"📊 Percentuale libera: {100 * memory.available / memory.total:.1f}%")
        
        if available_gb < 2.0:
            print("⚠️ Memoria limitata - chiudi altre app")
            return False
        else:
            print("✅ Memoria sufficiente per training")
            return True
        
    except Exception as e:
        print(f"❌ Errore memoria: {e}")
        return False

def test_pytorch_mps():
    """Test PyTorch MPS"""
    print("\n🚀 TEST PYTORCH MPS")
    print("=" * 50)
    
    try:
        if torch.backends.mps.is_available():
            print("✅ MPS disponibile")
            
            # Test veloce
            device = torch.device("mps")
            x = torch.randn(100, 100, device=device)
            y = torch.randn(100, 100, device=device)
            z = torch.matmul(x, y)
            
            print("✅ MPS funziona correttamente")
            return True
        else:
            print("⚠️ MPS non disponibile - userà CPU")
            return True
            
    except Exception as e:
        print(f"❌ Errore MPS: {e}")
        return False

def test_lora_config():
    """Test configurazione LoRA"""
    print("\n🔧 TEST CONFIGURAZIONE LORA")
    print("=" * 50)
    
    try:
        from peft import LoraConfig, TaskType
        
        # Configurazione LoRA per Qwen2
        lora_config = LoraConfig(
            r=16,
            lora_alpha=16,
            target_modules=[
                "q_proj", "k_proj", "v_proj", "o_proj",
                "gate_proj", "up_proj", "down_proj"
            ],
            lora_dropout=0.0,
            bias="none",
            task_type=TaskType.CAUSAL_LM
        )
        
        print("✅ Configurazione LoRA creata")
        print(f"   Rank: {lora_config.r}")
        print(f"   Alpha: {lora_config.lora_alpha}")
        print(f"   Target modules: {len(lora_config.target_modules)}")
        
        return True
        
    except Exception as e:
        print(f"❌ Errore LoRA config: {e}")
        return False

def create_minimal_training_test():
    """Crea test di training minimale"""
    print("\n🧪 CREAZIONE TEST TRAINING MINIMALE")
    print("=" * 50)
    
    script_content = '''#!/usr/bin/env python3
"""
Test training minimale - 1 step per verificare pipeline
"""

import torch
from transformers import AutoTokenizer, AutoModelForCausalLM, TrainingArguments, Trainer
from peft import LoraConfig, get_peft_model, TaskType
from datasets import Dataset

def minimal_training_test():
    print("🧪 Test training minimale...")
    
    # Carica tokenizer
    tokenizer = AutoTokenizer.from_pretrained("model/qwen2.5coder")
    if tokenizer.pad_token is None:
        tokenizer.pad_token = tokenizer.eos_token
    
    # Carica modello
    model = AutoModelForCausalLM.from_pretrained(
        "model/qwen2.5coder",
        torch_dtype=torch.float16,
        device_map="auto",
        load_in_4bit=True
    )
    
    # Applica LoRA
    lora_config = LoraConfig(
        r=8,  # Ridotto per test
        lora_alpha=8,
        target_modules=["q_proj", "v_proj"],  # Solo 2 moduli per test
        lora_dropout=0.0,
        bias="none",
        task_type=TaskType.CAUSAL_LM
    )
    
    model = get_peft_model(model, lora_config)
    
    # Dataset minimale
    data = [{"text": "def add(a, b): return a + b"}]
    dataset = Dataset.from_list(data)
    
    def tokenize(examples):
        return tokenizer(examples["text"], truncation=True, padding=True, max_length=128)
    
    dataset = dataset.map(tokenize, batched=True)
    
    # Training args minimali
    training_args = TrainingArguments(
        output_dir="./test_output",
        num_train_epochs=1,
        per_device_train_batch_size=1,
        max_steps=1,  # Solo 1 step
        logging_steps=1,
        save_steps=1,
        fp16=True,
        report_to=None
    )
    
    # Trainer
    trainer = Trainer(
        model=model,
        args=training_args,
        train_dataset=dataset,
        tokenizer=tokenizer
    )
    
    # 1 step di training
    trainer.train()
    
    print("✅ Test training minimale completato!")
    return True

if __name__ == "__main__":
    minimal_training_test()
'''
    
    with open("scripts/minimal_training_test.py", "w") as f:
        f.write(script_content)
    
    print("✅ Test minimale creato: scripts/minimal_training_test.py")

def main():
    """Test principale"""
    print("🧠 NEUROGLYPH LLM - Test Setup Training")
    print("🎯 Verifica che tutto sia pronto per il training")
    print("=" * 70)
    
    tests = [
        ("Import librerie", test_basic_imports),
        ("Caricamento modello", test_model_loading),
        ("Dataset", test_dataset_loading),
        ("Memoria", test_memory_usage),
        ("PyTorch MPS", test_pytorch_mps),
        ("Configurazione LoRA", test_lora_config)
    ]
    
    results = {}
    
    for test_name, test_func in tests:
        try:
            results[test_name] = test_func()
        except Exception as e:
            print(f"❌ Errore in {test_name}: {e}")
            results[test_name] = False
    
    # Crea test minimale
    create_minimal_training_test()
    
    # Risultati finali
    print("\n📊 RISULTATI FINALI")
    print("=" * 70)
    
    all_passed = True
    for test_name, passed in results.items():
        status = "✅" if passed else "❌"
        print(f"   {status} {test_name}")
        if not passed:
            all_passed = False
    
    if all_passed:
        print("\n🎉 TUTTI I TEST PASSATI!")
        print("✅ Setup pronto per training NEUROGLYPH LLM")
        print("🚀 Prossimo step: python3 scripts/train_neuroglyph_qlora.py")
    else:
        print("\n⚠️ Alcuni test falliti")
        print("🔧 Risolvi i problemi prima del training")
    
    return all_passed

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
