#!/usr/bin/env python3
"""
🧠 NEUROGLYPH ULTRA - Generatore Simboli Reasoning per SOCRATE
================================================================

Fase 1: Espansione SOCRATE
- Genera 55 simboli ng:reasoning:* mancanti
- Validazione con criteri ULTRA (USU/CTU/LCL)
- Focus su pattern di ragionamento avanzato

Autore: NEUROGLYPH ULTRA Team
Data: 2025-05-23
"""

import json
import logging
import random
import time
from datetime import datetime
from pathlib import Path
from typing import Dict, List, Set, Any, Optional

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('logs/reasoning_generation.log'),
        logging.StreamHandler()
    ]
)

class ReasoningSymbolGenerator:
    """Generatore specializzato per simboli ng:reasoning:* SOCRATE."""

    def __init__(self, target_count: int = 55, min_score: float = 90.0):
        self.target_count = target_count
        self.min_score = min_score

        # Paths
        self.registry_path = Path("core/symbols_registry.json")
        self.log_path = Path("logs/reasoning_generation.log")

        # Stato
        self.existing_symbols: Set[str] = set()
        self.existing_codes: Set[str] = set()
        self.generated_count = 0
        self.stats = {
            "start_time": datetime.now(),
            "total_attempts": 0,
            "successful_generations": 0,
            "failed_validations": 0,
            "duplicate_rejections": 0
        }

        # Carica simboli esistenti
        self._load_existing_symbols()

        # Semantic fields per reasoning SOCRATE
        self.reasoning_semantics = [
            # Core reasoning processes
            "induction", "deduction", "abduction", "analogy", "metaphor",
            "causality", "correlation", "pattern", "similarity", "difference",
            "abstraction", "generalization", "specialization", "classification",

            # Advanced reasoning
            "inference", "proof", "theorem", "axiom", "lemma", "hypothesis",
            "premise", "conclusion", "syllogism", "modus_ponens", "modus_tollens",

            # Logical reasoning
            "contrapositive", "contradiction", "tautology", "paradox", "fallacy",
            "soundness", "validity", "consistency", "completeness", "decidability",

            # Cognitive reasoning
            "intuition", "insight", "creativity", "problem_solving", "decision",
            "judgment", "evaluation", "assessment", "analysis", "synthesis",

            # Meta-reasoning
            "reflection", "metacognition", "self_awareness", "monitoring", "control",
            "planning", "strategy", "heuristic", "bias", "error_correction"
        ]

        # Unicode ranges ottimizzati per reasoning
        self.unicode_ranges = [
            (0x2190, 0x21FF),  # Arrows (per inferenze)
            (0x2200, 0x22FF),  # Mathematical Operators (per logica)
            (0x2300, 0x23FF),  # Miscellaneous Technical
            (0x25A0, 0x25FF),  # Geometric Shapes (per strutture)
            (0x2980, 0x29FF),  # Miscellaneous Mathematical Symbols-B
            (0x2A00, 0x2AFF),  # Supplemental Mathematical Operators
        ]

        logging.info(f"🧠 Inizializzato generatore reasoning: target {target_count} simboli")

    def _load_existing_symbols(self):
        """Carica simboli esistenti dal registry."""
        if not self.registry_path.exists():
            logging.warning(f"Registry non trovato: {self.registry_path}")
            return

        try:
            with open(self.registry_path, 'r', encoding='utf-8') as f:
                registry = json.load(f)

            for symbol_data in registry.get("approved_symbols", []):
                self.existing_symbols.add(symbol_data.get("symbol", ""))
                self.existing_codes.add(symbol_data.get("code", ""))

            reasoning_count = sum(1 for s in registry.get("approved_symbols", [])
                                if s.get("code", "").startswith("ng:reasoning:"))

            logging.info(f"📊 Caricati {len(self.existing_symbols)} simboli esistenti")
            logging.info(f"🧠 Simboli reasoning esistenti: {reasoning_count}")

        except Exception as e:
            logging.error(f"Errore caricamento registry: {e}")

    def _generate_unique_symbol(self) -> Optional[str]:
        """Genera un simbolo Unicode unico."""
        max_attempts = 1000

        for _ in range(max_attempts):
            # Seleziona range Unicode casuale
            start, end = random.choice(self.unicode_ranges)
            code_point = random.randint(start, end)

            try:
                symbol = chr(code_point)

                # Verifica unicità
                if symbol not in self.existing_symbols:
                    return symbol

            except ValueError:
                continue

        return None

    def _generate_reasoning_code(self) -> Optional[str]:
        """Genera un codice ng:reasoning:* unico."""
        max_attempts = 100

        for _ in range(max_attempts):
            semantic = random.choice(self.reasoning_semantics)
            code = f"ng:reasoning:{semantic}"

            if code not in self.existing_codes:
                return code

        return None

    def _validate_symbol(self, symbol: str, code: str, fallback: str) -> Dict[str, Any]:
        """Valida simbolo secondo criteri ULTRA."""
        validation = {
            "valid": True,
            "score": 0.0,
            "usu_score": 0.0,
            "ctu_score": 0.0,
            "lcl_score": 0.0,
            "errors": [],
            "warnings": []
        }

        # USU - Unicode Simbolico Unico
        usu_score = 100.0
        if symbol in self.existing_symbols:
            usu_score = 0.0
            validation["errors"].append("Simbolo già esistente")
        if len(symbol.encode('utf-8')) > 4:
            usu_score *= 0.8
            validation["warnings"].append("Simbolo multi-byte")

        # CTU - Codifica Testuale Unificata
        ctu_score = 100.0
        if not code.startswith("ng:reasoning:"):
            ctu_score = 0.0
            validation["errors"].append("Codice non reasoning")
        if code in self.existing_codes:
            ctu_score = 0.0
            validation["errors"].append("Codice già esistente")

        # LCL - Linguaggio Compatibile LLM
        lcl_score = 100.0
        if len(fallback) > 12:
            lcl_score *= 0.7
            validation["warnings"].append("Fallback troppo lungo")
        if not fallback.startswith("[") or not fallback.endswith("]"):
            lcl_score *= 0.9
            validation["warnings"].append("Formato fallback non standard")

        # Score finale
        validation["usu_score"] = usu_score
        validation["ctu_score"] = ctu_score
        validation["lcl_score"] = lcl_score
        validation["score"] = (usu_score + ctu_score + lcl_score) / 3.0
        validation["valid"] = validation["score"] >= self.min_score and len(validation["errors"]) == 0

        return validation

    def generate_reasoning_symbol(self) -> Optional[Dict[str, Any]]:
        """Genera un singolo simbolo reasoning."""
        self.stats["total_attempts"] += 1

        # Genera componenti
        symbol = self._generate_unique_symbol()
        if not symbol:
            self.stats["failed_validations"] += 1
            return None

        code = self._generate_reasoning_code()
        if not code:
            self.stats["duplicate_rejections"] += 1
            return None

        # Estrai semantic name dal codice
        semantic_name = code.split(":")[-1]
        fallback = f"[{semantic_name.upper().replace('_', '')}]"

        # Valida
        validation = self._validate_symbol(symbol, code, fallback)

        if not validation["valid"]:
            self.stats["failed_validations"] += 1
            logging.debug(f"❌ Validazione fallita: {validation['errors']}")
            return None

        # Crea entry simbolo
        next_id = f"NG{513 + self.generated_count:04d}"  # Continua da NG0513

        symbol_entry = {
            "id": next_id,
            "symbol": symbol,
            "code": code,
            "fallback": fallback,
            "category": "reasoning",
            "name": semantic_name,
            "description": f"Reasoning operation: {semantic_name}",
            "unicode_point": f"U+{ord(symbol):04X}",
            "approved_date": datetime.now().strftime("%Y-%m-%d"),
            "validation_score": validation["score"],
            "status": "certified",
            "token_cost": len(symbol.encode('utf-8')),
            "auto_generated": True,
            "generator": "reasoning_specialized"
        }

        # Aggiorna stato
        self.existing_symbols.add(symbol)
        self.existing_codes.add(code)
        self.generated_count += 1
        self.stats["successful_generations"] += 1

        logging.info(f"✅ Generato {next_id}: {symbol} {code} (score: {validation['score']:.1f})")

        return symbol_entry

    def generate_batch(self, batch_size: int = 10) -> List[Dict[str, Any]]:
        """Genera un batch di simboli reasoning."""
        batch = []
        max_attempts = batch_size * 50  # Limite tentativi per batch
        attempts = 0

        while len(batch) < batch_size and attempts < max_attempts:
            attempts += 1
            symbol_entry = self.generate_reasoning_symbol()

            if symbol_entry:
                batch.append(symbol_entry)

        logging.info(f"📦 Batch completato: {len(batch)}/{batch_size} simboli in {attempts} tentativi")
        return batch

    def save_to_registry(self, symbols: List[Dict[str, Any]]):
        """Salva simboli nel registry principale."""
        if not symbols:
            return

        try:
            # Carica registry esistente
            with open(self.registry_path, 'r', encoding='utf-8') as f:
                registry = json.load(f)

            # Aggiungi nuovi simboli
            registry["approved_symbols"].extend(symbols)
            registry["stats"]["approved"] += len(symbols)
            registry["stats"]["total_submissions"] += len(symbols)

            # Aggiorna conteggio categoria reasoning
            if "reasoning" in registry["categories"]:
                registry["categories"]["reasoning"]["count"] += len(symbols)
            else:
                registry["categories"]["reasoning"] = {
                    "count": len(symbols),
                    "description": "Reasoning and inference operations"
                }

            # Aggiorna next_id
            last_id = max(int(s["id"][2:]) for s in registry["approved_symbols"])
            registry["next_id"] = f"NG{last_id + 1:04d}"

            # Salva registry aggiornato
            with open(self.registry_path, 'w', encoding='utf-8') as f:
                json.dump(registry, f, indent=2, ensure_ascii=False)

            logging.info(f"💾 Salvati {len(symbols)} simboli nel registry")

        except Exception as e:
            logging.error(f"Errore salvataggio registry: {e}")

    def generate_all_reasoning_symbols(self) -> List[Dict[str, Any]]:
        """Genera tutti i simboli reasoning necessari."""
        logging.info(f"🚀 Avvio generazione {self.target_count} simboli reasoning...")

        all_symbols = []
        batch_size = 10

        while len(all_symbols) < self.target_count:
            remaining = self.target_count - len(all_symbols)
            current_batch_size = min(batch_size, remaining)

            logging.info(f"🔄 Generazione batch {len(all_symbols)+1}-{len(all_symbols)+current_batch_size}")

            batch = self.generate_batch(current_batch_size)
            all_symbols.extend(batch)

            # Progress update
            progress = len(all_symbols) / self.target_count * 100
            logging.info(f"📈 Progresso: {len(all_symbols)}/{self.target_count} ({progress:.1f}%)")

            # Pausa tra batch per evitare overload
            time.sleep(0.1)

        logging.info(f"🎉 Generazione completata: {len(all_symbols)} simboli reasoning")
        return all_symbols

    def print_final_report(self, symbols: List[Dict[str, Any]]):
        """Stampa report finale della generazione."""
        duration = (datetime.now() - self.stats["start_time"]).total_seconds() / 60
        success_rate = (self.stats["successful_generations"] / max(self.stats["total_attempts"], 1)) * 100

        print("\n" + "="*60)
        print("🧠 NEUROGLYPH ULTRA - Reasoning Symbols Generation Report")
        print("="*60)
        print(f"🎯 Target: {self.target_count} simboli")
        print(f"✅ Generati: {len(symbols)} simboli")
        print(f"📊 Success rate: {success_rate:.1f}%")
        print(f"⚡ Rate medio: {len(symbols)/max(duration, 0.1):.1f} simboli/minuto")
        print(f"🕒 Tempo totale: {duration:.1f} minuti")
        print()
        print("📈 STATISTICHE DETTAGLIATE:")
        print(f"  • Tentativi totali: {self.stats['total_attempts']}")
        print(f"  • Generazioni riuscite: {self.stats['successful_generations']}")
        print(f"  • Validazioni fallite: {self.stats['failed_validations']}")
        print(f"  • Duplicati rigettati: {self.stats['duplicate_rejections']}")
        print()
        print("🧠 ESEMPI SIMBOLI REASONING GENERATI:")
        for i, symbol in enumerate(symbols[:5]):
            print(f"  • {symbol['id']}: {symbol['symbol']} {symbol['code']} - {symbol['name']}")
        if len(symbols) > 5:
            print(f"  ... e altri {len(symbols)-5} simboli")
        print()
        print("🚀 STATO FINALE:")
        print(f"  • Registry: {self.registry_path}")
        print(f"  • Log: {self.log_path}")
        print(f"  • Simboli reasoning pronti per SOCRATE: True")
        print()
        print("🎉 FASE 1 COMPLETATA - SOCRATE REASONING READY!")


def main():
    """Main function per generazione simboli reasoning."""
    print("🧠 NEUROGLYPH ULTRA - Fase 1: Espansione SOCRATE")
    print("="*60)
    print("🎯 Generazione 55 simboli ng:reasoning:* per SOCRATE")
    print("📊 Validazione con criteri ULTRA (USU/CTU/LCL)")
    print("🧠 Focus su pattern di ragionamento avanzato")
    print("="*60)

    # Crea directory logs se non esiste
    Path("logs").mkdir(exist_ok=True)

    # Inizializza generatore
    generator = ReasoningSymbolGenerator(target_count=55, min_score=90.0)

    # Genera tutti i simboli reasoning
    symbols = generator.generate_all_reasoning_symbols()

    if symbols:
        # Salva nel registry
        generator.save_to_registry(symbols)

        # Report finale
        generator.print_final_report(symbols)

        # Salva report dettagliato
        report_path = Path("logs/reasoning_generation_report.txt")
        with open(report_path, 'w', encoding='utf-8') as f:
            f.write("🧠 NEUROGLYPH ULTRA - Reasoning Symbols Generation Report\n")
            f.write("="*60 + "\n")
            f.write(f"Generated: {len(symbols)} reasoning symbols\n")
            f.write(f"Success rate: {(generator.stats['successful_generations'] / max(generator.stats['total_attempts'], 1)) * 100:.1f}%\n")
            f.write("\nGenerated symbols:\n")
            for symbol in symbols:
                f.write(f"{symbol['id']}: {symbol['symbol']} {symbol['code']} - {symbol['name']}\n")

        print(f"💾 Report salvato in: {report_path}")

    else:
        print("❌ Nessun simbolo generato!")
        return 1

    return 0


if __name__ == "__main__":
    exit(main())
