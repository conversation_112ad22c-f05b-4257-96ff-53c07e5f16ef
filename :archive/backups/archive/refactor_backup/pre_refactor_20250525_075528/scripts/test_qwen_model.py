#!/usr/bin/env python3
"""
Test script per Qwen2.5-Coder-7B su Mac M2
Verifica compatibilità, performance e integrazione con NEUROGLYPH
"""

import os
import sys
import time
import psutil
from pathlib import Path

# Aggiungi path progetto
sys.path.insert(0, str(Path(__file__).parent.parent))

def check_system_requirements():
    """Verifica requisiti sistema"""
    print("🔍 VERIFICA REQUISITI SISTEMA")
    print("=" * 50)
    
    # Memoria
    memory = psutil.virtual_memory()
    print(f"💾 Memoria totale: {memory.total / (1024**3):.1f} GB")
    print(f"💾 Memoria disponibile: {memory.available / (1024**3):.1f} GB")
    print(f"💾 Memoria usata: {memory.percent:.1f}%")
    
    # CPU
    print(f"🖥️ CPU cores: {psutil.cpu_count()}")
    
    # Disco
    disk = psutil.disk_usage('/')
    print(f"💿 Spazio disco libero: {disk.free / (1024**3):.1f} GB")
    
    # Verifica se abbiamo abbastanza memoria
    available_gb = memory.available / (1024**3)
    if available_gb >= 6:
        print("✅ Memoria sufficiente per Qwen2.5-Coder Q4_K_M")
        return True
    else:
        print("⚠️ Memoria potrebbe essere insufficiente")
        return False

def check_model_file():
    """Verifica presenza e dimensione modello"""
    print("\n📁 VERIFICA MODELLO")
    print("=" * 50)
    
    model_path = Path("model/qwen2.5coder/qwen2.5-coder-7b-instruct-q4_k_m.gguf")
    
    if model_path.exists():
        size_gb = model_path.stat().st_size / (1024**3)
        print(f"✅ Modello trovato: {model_path}")
        print(f"📊 Dimensione: {size_gb:.1f} GB")
        
        if 4.0 <= size_gb <= 5.0:
            print("✅ Dimensione corretta per Q4_K_M")
            return True
        else:
            print("⚠️ Dimensione inaspettata")
            return False
    else:
        print(f"❌ Modello non trovato: {model_path}")
        return False

def test_llama_cpp_installation():
    """Testa installazione llama-cpp-python"""
    print("\n🐍 TEST LLAMA-CPP-PYTHON")
    print("=" * 50)
    
    try:
        # Prova import
        from llama_cpp import Llama
        print("✅ llama-cpp-python importato correttamente")
        
        # Verifica versione
        import llama_cpp
        print(f"📦 Versione: {llama_cpp.__version__ if hasattr(llama_cpp, '__version__') else 'N/A'}")
        
        return True
        
    except ImportError as e:
        print(f"❌ llama-cpp-python non installato: {e}")
        print("💡 Installa con: pip install llama-cpp-python")
        return False
    except Exception as e:
        print(f"❌ Errore llama-cpp-python: {e}")
        return False

def test_model_loading():
    """Testa caricamento modello"""
    print("\n🧠 TEST CARICAMENTO MODELLO")
    print("=" * 50)
    
    try:
        from llama_cpp import Llama
        
        model_path = "model/qwen2.5coder/qwen2.5-coder-7b-instruct-q4_k_m.gguf"
        
        print(f"🔄 Caricamento modello: {model_path}")
        start_time = time.time()
        
        # Carica modello con configurazione ottimizzata per Mac M2
        llm = Llama(
            model_path=model_path,
            n_ctx=2048,        # Context window
            n_threads=8,       # Thread per CPU
            n_gpu_layers=0,    # Usa CPU per ora (Metal support varies)
            verbose=False
        )
        
        load_time = time.time() - start_time
        print(f"✅ Modello caricato in {load_time:.2f} secondi")
        
        return llm
        
    except Exception as e:
        print(f"❌ Errore caricamento modello: {e}")
        return None

def test_simple_inference(llm):
    """Testa inferenza semplice"""
    print("\n⚡ TEST INFERENZA")
    print("=" * 50)
    
    if llm is None:
        print("❌ Modello non caricato, skip test inferenza")
        return False
    
    try:
        # Test prompt semplice
        prompt = "Write a Python function to calculate factorial:"
        
        print(f"📝 Prompt: {prompt}")
        print("🔄 Generazione in corso...")
        
        start_time = time.time()
        
        response = llm(
            prompt,
            max_tokens=150,
            temperature=0.7,
            top_p=0.9,
            echo=False
        )
        
        inference_time = time.time() - start_time
        generated_text = response['choices'][0]['text']
        
        print(f"⏱️ Tempo inferenza: {inference_time:.2f} secondi")
        print(f"📊 Token generati: {len(generated_text.split())}")
        print(f"🚀 Velocità: {len(generated_text.split()) / inference_time:.1f} token/sec")
        
        print("\n📄 RISPOSTA GENERATA:")
        print("-" * 40)
        print(generated_text)
        print("-" * 40)
        
        return True
        
    except Exception as e:
        print(f"❌ Errore inferenza: {e}")
        return False

def test_coding_capability(llm):
    """Testa capacità di coding"""
    print("\n🔧 TEST CAPACITÀ CODING")
    print("=" * 50)
    
    if llm is None:
        print("❌ Modello non caricato, skip test coding")
        return False
    
    try:
        # Test coding più complesso
        prompt = """Write a Python function to implement merge sort algorithm:

def merge_sort(arr):"""
        
        print(f"📝 Coding prompt: {prompt}")
        print("🔄 Generazione codice...")
        
        start_time = time.time()
        
        response = llm(
            prompt,
            max_tokens=300,
            temperature=0.3,  # Più deterministico per coding
            top_p=0.9,
            echo=False
        )
        
        inference_time = time.time() - start_time
        generated_code = response['choices'][0]['text']
        
        print(f"⏱️ Tempo generazione: {inference_time:.2f} secondi")
        
        print("\n💻 CODICE GENERATO:")
        print("-" * 40)
        print(prompt + generated_code)
        print("-" * 40)
        
        # Verifica se il codice sembra valido
        if "def " in generated_code and "return" in generated_code:
            print("✅ Codice sembra strutturalmente valido")
            return True
        else:
            print("⚠️ Codice potrebbe essere incompleto")
            return False
        
    except Exception as e:
        print(f"❌ Errore test coding: {e}")
        return False

def main():
    """Funzione principale"""
    print("🧠 QWEN2.5-CODER-7B TEST SUITE")
    print("🎯 Verifica compatibilità Mac M2 8GB")
    print("=" * 60)
    
    # Test sequenziali
    tests_passed = 0
    total_tests = 5
    
    # Test 1: Requisiti sistema
    if check_system_requirements():
        tests_passed += 1
    
    # Test 2: File modello
    if check_model_file():
        tests_passed += 1
    
    # Test 3: llama-cpp-python
    if test_llama_cpp_installation():
        tests_passed += 1
        
        # Test 4: Caricamento modello
        llm = test_model_loading()
        if llm is not None:
            tests_passed += 1
            
            # Test 5: Inferenza
            if test_simple_inference(llm):
                tests_passed += 1
            
            # Test bonus: Coding
            test_coding_capability(llm)
    
    # Risultati finali
    print("\n" + "=" * 60)
    print("📊 RISULTATI FINALI")
    print("=" * 60)
    print(f"✅ Test superati: {tests_passed}/{total_tests}")
    print(f"📈 Percentuale successo: {tests_passed/total_tests*100:.1f}%")
    
    if tests_passed >= 4:
        print("\n🎉 QWEN2.5-CODER FUNZIONA PERFETTAMENTE!")
        print("🚀 Pronto per integrazione NEUROGLYPH LLM")
        print("💡 Prossimo step: LoRA fine-tuning con dataset neuroglifi")
    elif tests_passed >= 2:
        print("\n⚠️ Setup parzialmente funzionante")
        print("🔧 Alcuni componenti necessitano configurazione")
    else:
        print("\n❌ Setup non funzionante")
        print("🛠️ Richiede troubleshooting")
    
    return tests_passed >= 4

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
