#!/usr/bin/env python3
"""
NEUROGLYPH ULTRA - LLM Tokenizer Compatibility Tester
======================================================

Verifica se i simboli sono tokenizzabili singolarmente e calcola costo token reale.
Testa con tokenizer comuni: tiktoken (OpenAI), transformers (HuggingFace).

Usage: python test_llm_tokenizer_compat.py "⊕"
"""

import sys
import json
from typing import Dict, Any, List, Optional

class TokenizerCompatibilityTester:
    """Tester per compatibilità tokenizer LLM."""
    
    def __init__(self):
        self.results = {
            "symbol": "",
            "tiktoken_available": False,
            "transformers_available": False,
            "tiktoken_results": {},
            "transformers_results": {},
            "overall_score": 0.0,
            "recommendations": []
        }
    
    def test_symbol(self, symbol: str) -> Dict[str, Any]:
        """Testa un simbolo con tutti i tokenizer disponibili."""
        self.results["symbol"] = symbol
        
        # Test tiktoken (OpenAI)
        self.results["tiktoken_results"] = self._test_tiktoken(symbol)
        self.results["tiktoken_available"] = self.results["tiktoken_results"].get("available", False)
        
        # Test transformers (HuggingFace)
        self.results["transformers_results"] = self._test_transformers(symbol)
        self.results["transformers_available"] = self.results["transformers_results"].get("available", False)
        
        # Calcola score complessivo
        self.results["overall_score"] = self._calculate_overall_score()
        
        # Genera raccomandazioni
        self.results["recommendations"] = self._generate_recommendations()
        
        return self.results
    
    def _test_tiktoken(self, symbol: str) -> Dict[str, Any]:
        """Testa compatibilità con tiktoken (OpenAI)."""
        try:
            import tiktoken
            
            results = {
                "available": True,
                "encodings": {},
                "best_encoding": None,
                "min_tokens": float('inf'),
                "max_tokens": 0,
                "avg_tokens": 0.0,
                "single_token": False,
                "errors": []
            }
            
            # Test encoding comuni
            encoding_names = ["cl100k_base", "p50k_base", "r50k_base", "gpt2"]
            token_counts = []
            
            for encoding_name in encoding_names:
                try:
                    enc = tiktoken.get_encoding(encoding_name)
                    
                    # Encode symbol
                    tokens = enc.encode(symbol)
                    token_count = len(tokens)
                    token_counts.append(token_count)
                    
                    # Decode per verificare roundtrip
                    decoded = enc.decode(tokens)
                    roundtrip_ok = decoded == symbol
                    
                    # Token details
                    token_ids = tokens
                    token_strings = []
                    for token_id in token_ids:
                        try:
                            token_str = enc.decode([token_id])
                            token_strings.append(token_str)
                        except:
                            token_strings.append(f"<token_{token_id}>")
                    
                    results["encodings"][encoding_name] = {
                        "token_count": token_count,
                        "tokens": token_ids,
                        "token_strings": token_strings,
                        "roundtrip_ok": roundtrip_ok,
                        "single_token": token_count == 1
                    }
                    
                    # Track best (lowest token count)
                    if token_count < results["min_tokens"]:
                        results["min_tokens"] = token_count
                        results["best_encoding"] = encoding_name
                    
                    results["max_tokens"] = max(results["max_tokens"], token_count)
                    
                except Exception as e:
                    results["encodings"][encoding_name] = {
                        "error": str(e)
                    }
                    results["errors"].append(f"{encoding_name}: {e}")
            
            # Calcola statistiche
            if token_counts:
                results["avg_tokens"] = sum(token_counts) / len(token_counts)
                results["single_token"] = any(count == 1 for count in token_counts)
            
            return results
            
        except ImportError:
            return {
                "available": False,
                "error": "tiktoken not installed (pip install tiktoken)"
            }
        except Exception as e:
            return {
                "available": False,
                "error": f"tiktoken error: {e}"
            }
    
    def _test_transformers(self, symbol: str) -> Dict[str, Any]:
        """Testa compatibilità con transformers tokenizer."""
        try:
            from transformers import AutoTokenizer
            
            results = {
                "available": True,
                "tokenizers": {},
                "best_tokenizer": None,
                "min_tokens": float('inf'),
                "max_tokens": 0,
                "avg_tokens": 0.0,
                "single_token": False,
                "errors": []
            }
            
            # Tokenizer comuni da testare (lightweight)
            tokenizer_names = [
                "gpt2",
                "microsoft/DialoGPT-small",
                "facebook/opt-125m"
            ]
            
            token_counts = []
            
            for tokenizer_name in tokenizer_names:
                try:
                    # Carica tokenizer
                    tokenizer = AutoTokenizer.from_pretrained(tokenizer_name)
                    
                    # Encode symbol
                    tokens = tokenizer.encode(symbol, add_special_tokens=False)
                    token_count = len(tokens)
                    token_counts.append(token_count)
                    
                    # Decode per verificare roundtrip
                    decoded = tokenizer.decode(tokens, skip_special_tokens=True)
                    roundtrip_ok = decoded == symbol
                    
                    # Token details
                    token_strings = []
                    for token_id in tokens:
                        try:
                            token_str = tokenizer.decode([token_id], skip_special_tokens=True)
                            token_strings.append(token_str)
                        except:
                            token_strings.append(f"<token_{token_id}>")
                    
                    results["tokenizers"][tokenizer_name] = {
                        "token_count": token_count,
                        "tokens": tokens,
                        "token_strings": token_strings,
                        "roundtrip_ok": roundtrip_ok,
                        "single_token": token_count == 1
                    }
                    
                    # Track best
                    if token_count < results["min_tokens"]:
                        results["min_tokens"] = token_count
                        results["best_tokenizer"] = tokenizer_name
                    
                    results["max_tokens"] = max(results["max_tokens"], token_count)
                    
                except Exception as e:
                    results["tokenizers"][tokenizer_name] = {
                        "error": str(e)
                    }
                    results["errors"].append(f"{tokenizer_name}: {e}")
            
            # Calcola statistiche
            if token_counts:
                results["avg_tokens"] = sum(token_counts) / len(token_counts)
                results["single_token"] = any(count == 1 for count in token_counts)
            
            return results
            
        except ImportError:
            return {
                "available": False,
                "error": "transformers not installed (pip install transformers)"
            }
        except Exception as e:
            return {
                "available": False,
                "error": f"transformers error: {e}"
            }
    
    def _calculate_overall_score(self) -> float:
        """Calcola score complessivo di compatibilità (0-100)."""
        score = 0.0
        max_score = 100.0
        
        # tiktoken score (50% del totale)
        if self.results["tiktoken_available"]:
            tiktoken_res = self.results["tiktoken_results"]
            
            # Bonus per disponibilità
            score += 10
            
            # Score basato su token count
            min_tokens = tiktoken_res.get("min_tokens", float('inf'))
            if min_tokens == 1:
                score += 25  # Perfetto: single token
            elif min_tokens == 2:
                score += 20  # Buono: 2 token
            elif min_tokens <= 3:
                score += 15  # Accettabile: 3 token
            else:
                score += 5   # Problematico: >3 token
            
            # Bonus per single token in almeno un encoding
            if tiktoken_res.get("single_token", False):
                score += 10
            
            # Penalty per errori
            error_count = len(tiktoken_res.get("errors", []))
            score -= error_count * 2
        
        # transformers score (50% del totale)
        if self.results["transformers_available"]:
            transformers_res = self.results["transformers_results"]
            
            # Bonus per disponibilità
            score += 10
            
            # Score basato su token count
            min_tokens = transformers_res.get("min_tokens", float('inf'))
            if min_tokens == 1:
                score += 25  # Perfetto: single token
            elif min_tokens == 2:
                score += 20  # Buono: 2 token
            elif min_tokens <= 3:
                score += 15  # Accettabile: 3 token
            else:
                score += 5   # Problematico: >3 token
            
            # Bonus per single token in almeno un tokenizer
            if transformers_res.get("single_token", False):
                score += 10
            
            # Penalty per errori
            error_count = len(transformers_res.get("errors", []))
            score -= error_count * 2
        
        # Penalty se nessun tokenizer disponibile
        if not self.results["tiktoken_available"] and not self.results["transformers_available"]:
            score = 0
        
        return max(0.0, min(max_score, score))
    
    def _generate_recommendations(self) -> List[str]:
        """Genera raccomandazioni basate sui risultati."""
        recommendations = []
        
        # Analisi tiktoken
        if self.results["tiktoken_available"]:
            tiktoken_res = self.results["tiktoken_results"]
            min_tokens = tiktoken_res.get("min_tokens", float('inf'))
            
            if min_tokens == 1:
                recommendations.append("✅ Ottimo: simbolo tokenizzato come singolo token in tiktoken")
            elif min_tokens <= 2:
                recommendations.append("⚠️ Accettabile: simbolo richiede 2 token in tiktoken")
            else:
                recommendations.append(f"❌ Problematico: simbolo richiede {min_tokens} token in tiktoken")
            
            if not tiktoken_res.get("single_token", False):
                recommendations.append("⚠️ Nessun encoding tiktoken produce single token")
        
        # Analisi transformers
        if self.results["transformers_available"]:
            transformers_res = self.results["transformers_results"]
            min_tokens = transformers_res.get("min_tokens", float('inf'))
            
            if min_tokens == 1:
                recommendations.append("✅ Ottimo: simbolo tokenizzato come singolo token in transformers")
            elif min_tokens <= 2:
                recommendations.append("⚠️ Accettabile: simbolo richiede 2 token in transformers")
            else:
                recommendations.append(f"❌ Problematico: simbolo richiede {min_tokens} token in transformers")
        
        # Raccomandazioni generali
        overall_score = self.results["overall_score"]
        if overall_score >= 80:
            recommendations.append("🚀 RACCOMANDAZIONE: Simbolo ottimo per LLM")
        elif overall_score >= 60:
            recommendations.append("⚠️ RACCOMANDAZIONE: Simbolo accettabile ma non ottimale")
        else:
            recommendations.append("❌ RACCOMANDAZIONE: Simbolo problematico per LLM")
        
        return recommendations
    
    def generate_report(self) -> str:
        """Genera report dettagliato."""
        lines = []
        lines.append("🧠 NEUROGLYPH ULTRA - LLM Tokenizer Compatibility Report")
        lines.append("=" * 60)
        lines.append(f"Symbol: {self.results['symbol']}")
        lines.append(f"Overall Score: {self.results['overall_score']:.1f}/100")
        lines.append("")
        
        # tiktoken results
        lines.append("🔹 tiktoken (OpenAI) Results:")
        if self.results["tiktoken_available"]:
            tiktoken_res = self.results["tiktoken_results"]
            lines.append(f"  Available: ✅")
            lines.append(f"  Min tokens: {tiktoken_res.get('min_tokens', 'N/A')}")
            lines.append(f"  Max tokens: {tiktoken_res.get('max_tokens', 'N/A')}")
            lines.append(f"  Avg tokens: {tiktoken_res.get('avg_tokens', 0):.1f}")
            lines.append(f"  Single token possible: {'✅' if tiktoken_res.get('single_token') else '❌'}")
            lines.append(f"  Best encoding: {tiktoken_res.get('best_encoding', 'N/A')}")
            
            if tiktoken_res.get("errors"):
                lines.append("  Errors:")
                for error in tiktoken_res["errors"]:
                    lines.append(f"    - {error}")
        else:
            lines.append(f"  Available: ❌")
            lines.append(f"  Error: {self.results['tiktoken_results'].get('error', 'Unknown')}")
        
        lines.append("")
        
        # transformers results
        lines.append("🔹 transformers (HuggingFace) Results:")
        if self.results["transformers_available"]:
            transformers_res = self.results["transformers_results"]
            lines.append(f"  Available: ✅")
            lines.append(f"  Min tokens: {transformers_res.get('min_tokens', 'N/A')}")
            lines.append(f"  Max tokens: {transformers_res.get('max_tokens', 'N/A')}")
            lines.append(f"  Avg tokens: {transformers_res.get('avg_tokens', 0):.1f}")
            lines.append(f"  Single token possible: {'✅' if transformers_res.get('single_token') else '❌'}")
            lines.append(f"  Best tokenizer: {transformers_res.get('best_tokenizer', 'N/A')}")
            
            if transformers_res.get("errors"):
                lines.append("  Errors:")
                for error in transformers_res["errors"]:
                    lines.append(f"    - {error}")
        else:
            lines.append(f"  Available: ❌")
            lines.append(f"  Error: {self.results['transformers_results'].get('error', 'Unknown')}")
        
        lines.append("")
        
        # Recommendations
        lines.append("🎯 Recommendations:")
        for rec in self.results["recommendations"]:
            lines.append(f"  {rec}")
        
        return "\n".join(lines)

def main():
    """Main function per command line usage."""
    if len(sys.argv) != 2:
        print("Usage: python test_llm_tokenizer_compat.py <symbol>")
        print("Example: python test_llm_tokenizer_compat.py '⊕'")
        sys.exit(1)
    
    symbol = sys.argv[1]
    
    print(f"🧠 Testing LLM tokenizer compatibility for: {symbol}")
    print("=" * 50)
    
    tester = TokenizerCompatibilityTester()
    results = tester.test_symbol(symbol)
    
    report = tester.generate_report()
    print(report)
    
    # Salva risultati
    output_file = f"tokenizer_test_{symbol.replace('/', '_')}.json"
    with open(output_file, "w", encoding="utf-8") as f:
        json.dump(results, f, indent=2, ensure_ascii=False)
    
    print(f"\n💾 Results saved to: {output_file}")
    
    # Exit code basato su score
    exit_code = 0 if results["overall_score"] >= 60 else 1
    sys.exit(exit_code)

if __name__ == "__main__":
    main()
