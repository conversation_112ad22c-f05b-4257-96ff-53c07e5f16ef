#!/usr/bin/env python3
"""
Test specifico per Metal GPU con Qwen2.5-Coder
Verifica se possiamo usare accelerazione GPU su Mac M2
"""

import os
import sys
import time
import psutil
from pathlib import Path

def check_metal_support():
    """Verifica supporto Metal"""
    print("🔍 VERIFICA SUPPORTO METAL")
    print("=" * 50)
    
    try:
        # Check PyTorch MPS
        import torch
        if torch.backends.mps.is_available():
            print("✅ PyTorch MPS disponibile")
            if torch.backends.mps.is_built():
                print("✅ PyTorch MPS compilato")
            else:
                print("⚠️ PyTorch MPS non compilato")
        else:
            print("❌ PyTorch MPS non disponibile")
        
        # Check Metal framework
        try:
            import Metal
            print("✅ Metal framework disponibile")
        except ImportError:
            print("⚠️ Metal framework non importabile (normale)")
        
        return True
        
    except Exception as e:
        print(f"❌ Errore verifica Metal: {e}")
        return False

def test_cpu_only():
    """Test con solo CPU per baseline"""
    print("\n🖥️ TEST CPU ONLY (BASELINE)")
    print("=" * 50)
    
    try:
        from llama_cpp import Llama
        
        model_path = "model/qwen2.5coder/qwen2.5-coder-7b-instruct-q4_k_m.gguf"
        
        print("🔄 Caricamento modello (CPU only)...")
        start_time = time.time()
        
        llm = Llama(
            model_path=model_path,
            n_ctx=256,
            n_threads=4,
            n_gpu_layers=0,    # SOLO CPU
            verbose=False,
            use_mmap=True,
            use_mlock=False
        )
        
        load_time = time.time() - start_time
        print(f"✅ Caricamento CPU: {load_time:.2f}s")
        
        # Test inferenza
        start_time = time.time()
        response = llm("def add(a, b):", max_tokens=10, temperature=0.1)
        inference_time = time.time() - start_time
        
        print(f"⏱️ Inferenza CPU: {inference_time:.2f}s")
        print(f"📄 Output: {repr(response['choices'][0]['text'][:50])}")
        
        return llm, inference_time
        
    except Exception as e:
        print(f"❌ Errore CPU test: {e}")
        return None, 0

def test_metal_gpu():
    """Test con Metal GPU"""
    print("\n🚀 TEST METAL GPU")
    print("=" * 50)
    
    try:
        from llama_cpp import Llama
        
        model_path = "model/qwen2.5coder/qwen2.5-coder-7b-instruct-q4_k_m.gguf"
        
        print("🔄 Caricamento modello (Metal GPU)...")
        start_time = time.time()
        
        # Prova diverse configurazioni Metal
        gpu_layers_options = [35, 20, 10, 5]  # Prova diversi layer su GPU
        
        for n_gpu_layers in gpu_layers_options:
            try:
                print(f"   Tentativo con {n_gpu_layers} GPU layers...")
                
                llm = Llama(
                    model_path=model_path,
                    n_ctx=256,
                    n_threads=2,           # Meno thread CPU quando usiamo GPU
                    n_gpu_layers=n_gpu_layers,  # Layer su GPU
                    verbose=False,
                    use_mmap=True,
                    use_mlock=False,
                    # Metal specific
                    main_gpu=0,
                    tensor_split=None
                )
                
                load_time = time.time() - start_time
                print(f"✅ Caricamento Metal ({n_gpu_layers} layers): {load_time:.2f}s")
                
                # Test inferenza
                start_time = time.time()
                response = llm("def add(a, b):", max_tokens=10, temperature=0.1)
                inference_time = time.time() - start_time
                
                print(f"⚡ Inferenza Metal: {inference_time:.2f}s")
                print(f"📄 Output: {repr(response['choices'][0]['text'][:50])}")
                
                return llm, inference_time, n_gpu_layers
                
            except Exception as e:
                print(f"   ❌ Fallito con {n_gpu_layers} layers: {e}")
                continue
        
        print("❌ Nessuna configurazione Metal funzionante")
        return None, 0, 0
        
    except Exception as e:
        print(f"❌ Errore Metal test: {e}")
        return None, 0, 0

def compare_performance(cpu_time, metal_time, gpu_layers):
    """Confronta performance CPU vs Metal"""
    print("\n📊 CONFRONTO PERFORMANCE")
    print("=" * 50)
    
    if cpu_time > 0 and metal_time > 0:
        speedup = cpu_time / metal_time
        print(f"🖥️ CPU only: {cpu_time:.2f}s")
        print(f"🚀 Metal GPU ({gpu_layers} layers): {metal_time:.2f}s")
        print(f"⚡ Speedup: {speedup:.2f}x")
        
        if speedup > 1.2:
            print("✅ Metal GPU è significativamente più veloce!")
            return True
        elif speedup > 1.0:
            print("✅ Metal GPU è leggermente più veloce")
            return True
        else:
            print("⚠️ Metal GPU non offre vantaggi")
            return False
    else:
        print("❌ Impossibile confrontare (test falliti)")
        return False

def check_memory_usage():
    """Monitora uso memoria durante test"""
    memory = psutil.virtual_memory()
    print(f"\n💾 Memoria attuale:")
    print(f"   Totale: {memory.total/(1024**3):.1f} GB")
    print(f"   Disponibile: {memory.available/(1024**3):.1f} GB")
    print(f"   Usata: {memory.percent:.1f}%")
    
    return memory.available/(1024**3) > 2.0  # Almeno 2GB liberi

def main():
    """Test completo Metal GPU"""
    print("🚀 QWEN2.5-CODER METAL GPU TEST")
    print("🎯 Mac M2 GPU Acceleration Check")
    print("=" * 60)
    
    # Verifica memoria
    if not check_memory_usage():
        print("⚠️ Memoria limitata - chiudi altre app per test affidabili")
    
    # Verifica supporto Metal
    check_metal_support()
    
    # Test CPU baseline
    cpu_llm, cpu_time = test_cpu_only()
    
    # Test Metal GPU
    metal_llm, metal_time, gpu_layers = test_metal_gpu()
    
    # Confronto
    metal_works = compare_performance(cpu_time, metal_time, gpu_layers)
    
    # Risultato finale
    print("\n" + "=" * 60)
    print("🎯 RISULTATO FINALE")
    print("=" * 60)
    
    if metal_works:
        print("🎉 METAL GPU FUNZIONA E ACCELERA QWEN2.5-CODER!")
        print(f"✅ Configurazione ottimale: {gpu_layers} GPU layers")
        print("🚀 Raccomandazione: Usa Metal GPU per NEUROGLYPH LLM")
        
        # Configurazione raccomandata
        print(f"\n💡 CONFIGURAZIONE RACCOMANDATA:")
        print(f"```python")
        print(f"llm = Llama(")
        print(f"    model_path='model/qwen2.5coder/qwen2.5-coder-7b-instruct-q4_k_m.gguf',")
        print(f"    n_ctx=512,")
        print(f"    n_threads=2,")
        print(f"    n_gpu_layers={gpu_layers},  # Metal GPU")
        print(f"    use_mmap=True")
        print(f")")
        print(f"```")
        
    elif cpu_time > 0:
        print("⚠️ Metal GPU non offre vantaggi, usa CPU")
        print("🔧 Raccomandazione: Configurazione CPU ottimizzata")
        
        print(f"\n💡 CONFIGURAZIONE CPU OTTIMIZZATA:")
        print(f"```python")
        print(f"llm = Llama(")
        print(f"    model_path='model/qwen2.5coder/qwen2.5-coder-7b-instruct-q4_k_m.gguf',")
        print(f"    n_ctx=512,")
        print(f"    n_threads=4,")
        print(f"    n_gpu_layers=0,  # CPU only")
        print(f"    use_mmap=True")
        print(f")")
        print(f"```")
        
    else:
        print("❌ Test falliti - problemi di configurazione")
        print("🔧 Verifica installazione llama-cpp-python")
    
    return metal_works

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
