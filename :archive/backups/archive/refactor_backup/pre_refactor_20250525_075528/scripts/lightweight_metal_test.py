#!/usr/bin/env python3
"""
Test Metal GPU ultra-leggero per Mac M2 con memoria limitata
"""

import os
import sys
import time

def test_metal_info():
    """Verifica info Metal senza caricare modello"""
    print("🔍 VERIFICA SUPPORTO METAL")
    print("=" * 50)

    try:
        from llama_cpp import llama_cpp

        # Verifica se Metal è compilato
        print("📦 llama-cpp-python version:", getattr(llama_cpp, '__version__', 'unknown'))

        # Prova a vedere le capacità Metal
        try:
            # Questo dovrebbe mostrare info Metal se disponibile
            from llama_cpp import Llama

            # Test con modello fittizio per vedere messaggi Metal
            print("🔄 Test inizializzazione Metal...")

            # Questo fallirà ma mostrerà i messaggi Metal
            try:
                llm = Llama(
                    model_path="fake_model.gguf",  # Non esiste
                    n_gpu_layers=1,
                    verbose=True
                )
            except Exception as e:
                error_msg = str(e)
                if "metal" in error_msg.lower():
                    print("✅ Metal support rilevato nei messaggi di errore")
                elif "ggml_metal" in error_msg.lower():
                    print("✅ ggml_metal rilevato")
                else:
                    print("⚠️ Nessun riferimento Metal nell'errore")
                print(f"📄 Errore (normale): {error_msg[:100]}...")

        except Exception as e:
            print(f"❌ Errore test Metal: {e}")

        return True

    except ImportError:
        print("❌ llama-cpp-python non disponibile")
        return False

def check_metal_compilation():
    """Verifica se llama-cpp è compilato con Metal"""
    print("\n🔧 VERIFICA COMPILAZIONE METAL")
    print("=" * 50)

    try:
        import llama_cpp

        # Cerca attributi Metal
        metal_attrs = [attr for attr in dir(llama_cpp) if 'metal' in attr.lower()]
        if metal_attrs:
            print(f"✅ Attributi Metal trovati: {metal_attrs}")
        else:
            print("⚠️ Nessun attributo Metal trovato")

        # Verifica costanti
        if hasattr(llama_cpp, 'LLAMA_SUPPORTS_GPU_OFFLOAD'):
            print("✅ GPU offload supportato")
        else:
            print("⚠️ GPU offload non rilevato")

        return True

    except Exception as e:
        print(f"❌ Errore verifica: {e}")
        return False

def test_pytorch_metal():
    """Verifica PyTorch Metal Performance Shaders"""
    print("\n🚀 VERIFICA PYTORCH MPS")
    print("=" * 50)

    try:
        import torch

        print(f"📦 PyTorch version: {torch.__version__}")

        if torch.backends.mps.is_available():
            print("✅ MPS (Metal Performance Shaders) disponibile")

            if torch.backends.mps.is_built():
                print("✅ MPS compilato correttamente")

                # Test veloce MPS
                try:
                    device = torch.device("mps")
                    x = torch.randn(100, 100, device=device)
                    y = torch.randn(100, 100, device=device)

                    start_time = time.time()
                    z = torch.matmul(x, y)
                    mps_time = time.time() - start_time

                    print(f"⚡ Test MPS matrix multiply: {mps_time:.4f}s")
                    print("✅ MPS funziona correttamente")

                    return True

                except Exception as e:
                    print(f"❌ Errore test MPS: {e}")
            else:
                print("⚠️ MPS non compilato")
        else:
            print("❌ MPS non disponibile")

        return False

    except ImportError:
        print("⚠️ PyTorch non installato")
        return False
    except Exception as e:
        print(f"❌ Errore PyTorch: {e}")
        return False

def generate_metal_recommendations():
    """Genera raccomandazioni per Metal"""
    print("\n💡 RACCOMANDAZIONI METAL GPU")
    print("=" * 50)

    recommendations = []

    # Verifica memoria
    import psutil
    memory = psutil.virtual_memory()
    available_gb = memory.available / (1024**3)

    if available_gb < 3.0:
        recommendations.append("🧹 Libera memoria: chiudi app non essenziali")
        recommendations.append(f"   Attuale: {available_gb:.1f}GB, Target: >3GB")

    # Raccomandazioni installazione
    recommendations.extend([
        "🔧 Per Metal support completo:",
        "   1. Crea virtual environment: python3 -m venv venv_metal",
        "   2. Attiva: source venv_metal/bin/activate",
        "   3. Installa con Metal: CMAKE_ARGS='-DLLAMA_METAL=on' pip install llama-cpp-python",
        "",
        "⚙️ Configurazione raccomandata:",
        "   n_gpu_layers=10-20 (inizia con 10)",
        "   n_threads=2 (meno thread CPU quando usi GPU)",
        "   n_ctx=512 (context ridotto per memoria)",
        "",
        "📊 Performance attese:",
        "   CPU only: 2-5 token/sec",
        "   Metal GPU: 8-15 token/sec (3-5x speedup)",
    ])

    for rec in recommendations:
        print(rec)

def create_metal_setup_script():
    """Crea script per setup Metal"""
    script_content = '''#!/bin/bash
# Setup Metal GPU per NEUROGLYPH LLM
echo "🚀 NEUROGLYPH LLM - Metal GPU Setup"

# Verifica memoria
echo "💾 Verifica memoria..."
python3 -c "
import psutil
mem = psutil.virtual_memory()
available = mem.available/(1024**3)
print(f'Memoria disponibile: {available:.1f} GB')
if available < 3.0:
    print('⚠️ ATTENZIONE: Chiudi altre app per liberare memoria')
    exit(1)
else:
    print('✅ Memoria sufficiente')
"

if [ $? -ne 0 ]; then
    echo "❌ Memoria insufficiente. Chiudi altre applicazioni."
    exit 1
fi

# Crea virtual environment
echo "🔧 Creazione virtual environment..."
python3 -m venv venv_metal
source venv_metal/bin/activate

# Installa dipendenze base
echo "📦 Installazione dipendenze..."
pip install --upgrade pip
pip install psutil

# Installa llama-cpp-python con Metal
echo "🚀 Installazione llama-cpp-python con Metal support..."
CMAKE_ARGS="-DLLAMA_METAL=on" pip install llama-cpp-python --no-cache-dir

# Test installazione
echo "🧪 Test installazione..."
python3 -c "
from llama_cpp import Llama
print('✅ llama-cpp-python con Metal installato correttamente')
"

echo "🎉 Setup Metal completato!"
echo "💡 Per usare: source venv_metal/bin/activate"
'''

    with open("scripts/setup_metal.sh", "w") as f:
        f.write(script_content)

    os.chmod("scripts/setup_metal.sh", 0o755)
    print("✅ Script setup creato: scripts/setup_metal.sh")

def main():
    """Test principale"""
    print("🧠 NEUROGLYPH LLM - Metal GPU Analysis")
    print("🎯 Verifica supporto Metal su Mac M2")
    print("=" * 60)

    # Test componenti
    metal_info = test_metal_info()
    metal_compilation = check_metal_compilation()
    pytorch_mps = test_pytorch_metal()

    # Risultati
    print("\n📊 RISULTATI ANALISI")
    print("=" * 60)

    if pytorch_mps:
        print("🎉 METAL SUPPORT DISPONIBILE!")
        print("✅ PyTorch MPS funziona")
        print("💡 llama-cpp-python può beneficiare di Metal")
    elif metal_info or metal_compilation:
        print("⚠️ METAL PARZIALMENTE SUPPORTATO")
        print("🔧 Potrebbe richiedere reinstallazione ottimizzata")
    else:
        print("❌ METAL NON RILEVATO")
        print("🛠️ Richiede installazione con Metal support")

    # Genera raccomandazioni
    generate_metal_recommendations()

    # Crea script setup
    create_metal_setup_script()

    print(f"\n🚀 PROSSIMO STEP:")
    print(f"   bash scripts/setup_metal.sh")

    return pytorch_mps or metal_info

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
