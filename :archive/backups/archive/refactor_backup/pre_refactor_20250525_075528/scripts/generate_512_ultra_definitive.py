#!/usr/bin/env python3
"""
🧠 NEUROGLYPH ULTRA - Definitive 512 Symbol Generator
====================================================

Generatore definitivo che usa il validatore ULTRA per creare 512 simboli perfetti.
Implementa tutti i criteri USU/CTU/LCL con scoring preciso.

Usage: python generate_512_ultra_definitive.py [--target 512] [--min-score 90]
"""

import json
import sys
import os
import random
import unicodedata
from datetime import datetime
from typing import Dict, Any, List, Set, Optional
from pathlib import Path
import argparse

# Import del validatore ULTRA
sys.path.append(os.path.dirname(os.path.abspath(__file__)))
from neuroglyph_ultra_validator import NeuroGlyphUltraValidator

class UltraDefinitiveGenerator:
    """Generatore definitivo per 512 simboli NEUROGLYPH ULTRA."""
    
    def __init__(self, target_count: int = 512, min_score: float = 90.0):
        self.target_count = target_count
        self.min_score = min_score
        
        # Validatore ULTRA
        self.validator = NeuroGlyphUltraValidator()
        
        # Paths
        self.registry_path = Path("core/symbols_registry.json")
        
        # Stato
        self.generated_count = 0
        self.attempt_count = 0
        
        # Carica stato esistente
        self._load_existing_count()
        
        # Categorie con distribuzione ULTRA
        self.categories_config = {
            "operator": {
                "target": 64,
                "meanings": ["add", "sub", "mul", "div", "mod", "pow", "sqrt", "abs", "neg", "inc", "dec", 
                           "eq", "ne", "lt", "le", "gt", "ge", "min", "max", "clamp", "and", "or", "xor", 
                           "not", "shl", "shr", "assign", "add_assign", "sub_assign", "mul_assign"]
            },
            "logic": {
                "target": 96,
                "meanings": ["and", "or", "not", "implies", "iff", "xor", "nand", "nor", "forall", "exists", 
                           "unique", "lambda", "apply", "compose", "curry", "necessary", "possible", "knows", 
                           "believes", "ought", "permitted", "always", "eventually", "until", "since", "next", 
                           "previous", "entails", "proves", "derives", "concludes", "assumes", "supposes"]
            },
            "structure": {
                "target": 80,
                "meanings": ["class", "interface", "abstract", "concrete", "inherit", "implement", "function", 
                           "method", "procedure", "lambda", "closure", "partial", "module", "package", 
                           "namespace", "import", "export", "include", "global", "local", "private", "public", 
                           "protected", "static", "array", "list", "dict", "set", "tuple", "struct"]
            },
            "flow": {
                "target": 72,
                "meanings": ["if", "else", "elif", "switch", "case", "default", "when", "for", "while", "do", 
                           "repeat", "until", "foreach", "map", "filter", "try", "catch", "throw", "finally", 
                           "raise", "handle", "rescue", "async", "await", "promise", "future", "task", "thread", 
                           "process", "yield", "break", "continue", "return", "goto"]
            },
            "memory": {
                "target": 64,
                "meanings": ["alloc", "malloc", "calloc", "realloc", "new", "create", "reserve", "free", 
                           "delete", "destroy", "ref", "deref", "pointer", "address", "offset", "index", 
                           "slice", "cache", "memoize", "buffer", "prefetch", "evict", "flush", "sync", 
                           "stack", "heap", "register", "volatile", "persistent", "transient", "immutable", "mutable"]
            },
            "reasoning": {
                "target": 56,
                "meanings": ["induct", "base_case", "inductive_step", "generalize", "pattern", "deduce", 
                           "syllogism", "modus_ponens", "modus_tollens", "contrapositive", "analogous", 
                           "similar", "metaphor", "compare", "contrast", "relate", "causality", "correlation", 
                           "similarity", "difference", "abstraction", "generalization", "specialization", 
                           "classification", "categorize", "cluster", "group", "separate"]
            },
            "meta": {
                "target": 48,
                "meanings": ["reflect", "introspect", "meta", "self", "type_of", "instance_of", "recurse", 
                           "fixpoint", "iterate", "unfold", "fold", "reduce", "compile", "parse", "lex", 
                           "optimize", "transform", "generate", "interpret", "evaluate", "debug", "profile", 
                           "trace", "monitor", "measure", "benchmark"]
            },
            "quantum": {
                "target": 32,
                "meanings": ["qubit", "superposition", "collapse", "measure", "observe", "state", "entangle", 
                           "bell_state", "epr", "spooky", "correlate", "separate", "hadamard", "pauli_x", 
                           "pauli_y", "pauli_z", "cnot", "toffoli", "fredkin", "teleport", "encrypt", 
                           "decrypt", "error_correct", "decohere", "interfere"]
            }
        }
        
        # Unicode ranges ottimizzati per LLM
        self.unicode_ranges = [
            (0x2190, 0x21FF),  # Arrows - ottimo per flow
            (0x2200, 0x22FF),  # Mathematical Operators - perfetto per operator/logic
            (0x2300, 0x23FF),  # Miscellaneous Technical - buono per meta
            (0x25A0, 0x25FF),  # Geometric Shapes - ottimo per structure
            (0x2600, 0x26FF),  # Miscellaneous Symbols - buono per memory
            (0x27C0, 0x27EF),  # Miscellaneous Mathematical Symbols-A
            (0x2980, 0x29FF),  # Miscellaneous Mathematical Symbols-B
            (0x2A00, 0x2AFF),  # Supplemental Mathematical Operators
        ]
        
    def _load_existing_count(self):
        """Carica conteggio simboli esistenti."""
        try:
            if self.registry_path.exists():
                with open(self.registry_path, 'r', encoding='utf-8') as f:
                    registry = json.load(f)
                self.generated_count = len(registry.get("approved_symbols", []))
                print(f"📊 Simboli esistenti: {self.generated_count}")
        except Exception as e:
            print(f"⚠️ Warning: Could not load existing count: {e}")
            self.generated_count = 0
            
    def generate_symbol_candidate(self, category: str, meaning: str) -> Optional[Dict[str, Any]]:
        """Genera candidato simbolo per categoria e significato."""
        max_attempts = 50
        
        for attempt in range(max_attempts):
            try:
                # Seleziona range Unicode appropriato
                start, end = random.choice(self.unicode_ranges)
                unicode_point = random.randint(start, end)
                symbol = chr(unicode_point)
                
                # Verifica che sia un carattere valido
                try:
                    unicodedata.name(symbol)
                except ValueError:
                    continue
                    
                # Genera codice
                code = f"ng:{category}:{meaning}"
                
                # Genera fallback
                fallback_base = meaning.upper().replace('_', '')[:6]
                fallback = f"[{fallback_base}]"
                
                # Genera descrizione
                description = f"{category.title()} {meaning}: {unicodedata.name(symbol).lower()}"
                
                return {
                    "symbol": symbol,
                    "code": code,
                    "fallback": fallback,
                    "category": category,
                    "meaning": meaning,
                    "description": description,
                    "unicode_point": f"U+{unicode_point:04X}",
                    "attempt": attempt + 1
                }
                
            except Exception as e:
                continue
                
        return None
        
    def generate_and_validate_symbol(self, category: str, meaning: str) -> Optional[Dict[str, Any]]:
        """Genera e valida simbolo secondo criteri ULTRA."""
        max_attempts = 100
        
        for attempt in range(max_attempts):
            self.attempt_count += 1
            
            # Genera candidato
            candidate = self.generate_symbol_candidate(category, meaning)
            if not candidate:
                continue
                
            # Valida con validatore ULTRA
            validation_result = self.validator.validate_symbol_complete(
                candidate["symbol"],
                candidate["code"], 
                candidate["fallback"],
                candidate["category"],
                candidate["meaning"],
                candidate["description"]
            )
            
            # Verifica se passa i criteri
            if validation_result["valid"] and validation_result["score"] >= self.min_score:
                # Aggiunge dati di validazione al candidato
                candidate.update({
                    "validation_score": validation_result["score"],
                    "quality_score": validation_result["quality_score"],
                    "token_cost": validation_result["token_cost"],
                    "token_density": validation_result["token_density"],
                    "llm_support": validation_result["llm_support"],
                    "validation_details": validation_result
                })
                
                return candidate
                
        return None
        
    def approve_symbol(self, symbol_data: Dict[str, Any]) -> bool:
        """Approva simbolo e lo salva nel registry."""
        try:
            # Carica registry
            if self.registry_path.exists():
                with open(self.registry_path, 'r', encoding='utf-8') as f:
                    registry = json.load(f)
            else:
                registry = {
                    "registry_version": "2.0",
                    "created": datetime.now().strftime("%Y-%m-%d"),
                    "description": "NEUROGLYPH ULTRA Symbol Registry - Definitive Generator",
                    "stats": {"total_submissions": 0, "approved": 0, "rejected": 0},
                    "approved_symbols": [],
                    "categories": {},
                    "next_id": "NG0001",
                    "validation_criteria": {
                        "usu_required": ["unicode_unique", "code_unique", "visually_distinct"],
                        "ctu_required": ["format_valid", "category_valid", "function_valid"],
                        "lcl_required": ["utf8_compatible", "fallback_format_valid", "token_cost_le_2"],
                        "minimum_score": self.min_score
                    }
                }
            
            # Genera ID
            next_id_num = int(registry.get("next_id", "NG0001")[2:]) if registry.get("next_id") else 1
            new_id = f"NG{next_id_num:04d}"
            
            # Crea entry approvata
            approved_symbol = {
                "id": new_id,
                "symbol": symbol_data["symbol"],
                "code": symbol_data["code"],
                "fallback": symbol_data["fallback"],
                "category": symbol_data["category"],
                "meaning": symbol_data["meaning"],
                "description": symbol_data["description"],
                "unicode_point": symbol_data["unicode_point"],
                "approved_date": datetime.now().strftime("%Y-%m-%d"),
                "validation_score": symbol_data["validation_score"],
                "quality_score": symbol_data["quality_score"],
                "token_cost": symbol_data["token_cost"],
                "token_density": symbol_data["token_density"],
                "llm_support": symbol_data["llm_support"],
                "status": "certified_ultra",
                "generator": "definitive_ultra_v2"
            }
            
            # Aggiorna registry
            registry["approved_symbols"].append(approved_symbol)
            registry["stats"]["approved"] += 1
            registry["stats"]["total_submissions"] += 1
            registry["next_id"] = f"NG{next_id_num + 1:04d}"
            
            # Aggiorna conteggio categoria
            category = symbol_data["category"]
            if category not in registry["categories"]:
                registry["categories"][category] = {"count": 0, "target": self.categories_config[category]["target"]}
            registry["categories"][category]["count"] += 1
            
            # Salva
            with open(self.registry_path, 'w', encoding='utf-8') as f:
                json.dump(registry, f, indent=2, ensure_ascii=False)
            
            self.generated_count += 1
            
            print(f"✅ {new_id}: {symbol_data['symbol']} ({symbol_data['code']}) - Score: {symbol_data['validation_score']:.1f}%")
            return True
            
        except Exception as e:
            print(f"❌ Errore approvazione: {e}")
            return False
            
    def generate_symbols_for_category(self, category: str, needed: int) -> int:
        """Genera simboli per una categoria specifica."""
        meanings = self.categories_config[category]["meanings"]
        generated = 0
        
        # Distribuisci meanings
        meanings_needed = min(needed, len(meanings))
        selected_meanings = random.sample(meanings, meanings_needed)
        
        for meaning in selected_meanings:
            if generated >= needed:
                break
                
            symbol_data = self.generate_and_validate_symbol(category, meaning)
            if symbol_data and self.approve_symbol(symbol_data):
                generated += 1
                
        return generated
        
    def run_generation(self) -> bool:
        """Esegue generazione completa."""
        print(f"🚀 NEUROGLYPH ULTRA - Definitive Generation")
        print(f"🎯 Target: {self.target_count} simboli")
        print(f"📊 Esistenti: {self.generated_count}")
        print(f"🔥 Da generare: {self.target_count - self.generated_count}")
        print(f"📈 Score minimo: {self.min_score}%")
        print("=" * 60)
        
        if self.generated_count >= self.target_count:
            print("✅ Target già raggiunto!")
            return True
            
        # Genera per categoria secondo distribuzione
        for category, config in self.categories_config.items():
            if self.generated_count >= self.target_count:
                break
                
            # Calcola quanti servono per questa categoria
            current_count = 0
            try:
                if self.registry_path.exists():
                    with open(self.registry_path, 'r', encoding='utf-8') as f:
                        registry = json.load(f)
                    current_count = registry.get("categories", {}).get(category, {}).get("count", 0)
            except:
                current_count = 0
                
            needed = config["target"] - current_count
            if needed <= 0:
                print(f"✅ {category}: target già raggiunto ({current_count}/{config['target']})")
                continue
                
            print(f"🔄 {category}: generazione {needed} simboli...")
            generated = self.generate_symbols_for_category(category, needed)
            print(f"📈 {category}: {generated}/{needed} generati")
            
        # Report finale
        print("\n" + "=" * 60)
        print(f"🎉 Generazione completata!")
        print(f"✅ Simboli totali: {self.generated_count}")
        print(f"📊 Tentativi: {self.attempt_count}")
        print(f"⚡ Success rate: {(self.generated_count/max(1, self.attempt_count)*100):.1f}%")
        
        return self.generated_count >= self.target_count


def main():
    """Main function."""
    parser = argparse.ArgumentParser(description="NEUROGLYPH ULTRA Definitive Generator")
    parser.add_argument("--target", type=int, default=512, help="Target symbol count")
    parser.add_argument("--min-score", type=float, default=90.0, help="Minimum validation score")
    
    args = parser.parse_args()
    
    generator = UltraDefinitiveGenerator(target_count=args.target, min_score=args.min_score)
    success = generator.run_generation()
    
    sys.exit(0 if success else 1)


if __name__ == "__main__":
    main()
