#!/usr/bin/env python3
"""
NEUROGLYPH SMART RESTORE FROM BACKUP
Ripristina simboli di qualità dal backup mantenendo zero duplicati
"""

import json
import sys
from datetime import datetime
from collections import defaultdict

def smart_restore_from_backup():
    """Ripristina intelligentemente simboli di qualità dal backup."""
    
    print("🔄 NEUROGLYPH SMART RESTORE FROM BACKUP")
    print("=" * 50)
    
    # Carica backup prima della pulizia
    backup_path = "neuroglyph/core/locked_registry_godmode_v9.backup_before_cleanup_20250526_094538.json"
    with open(backup_path, 'r', encoding='utf-8') as f:
        backup_data = json.load(f)
    
    backup_symbols = backup_data.get('approved_symbols', [])
    print(f"📊 Backup caricato: {len(backup_symbols)} simboli")
    
    # Carica registry attuale (pulito)
    registry_path = "neuroglyph/core/locked_registry_godmode_v9.json"
    with open(registry_path, 'r', encoding='utf-8') as f:
        current_data = json.load(f)
    
    current_symbols = current_data.get('approved_symbols', [])
    print(f"📊 Registry attuale: {len(current_symbols)} simboli")
    
    # FASE 1: Identifica simboli di alta qualità dal backup
    print("\n🔍 FASE 1: Identificazione simboli di qualità...")
    
    quality_criteria = {
        'score': 95.0,
        'fallback_length': 8,
        'has_code': True,
        'has_category': True
    }
    
    quality_backup_symbols = []
    for symbol in backup_symbols:
        # Criteri di qualità
        score = symbol.get('score', 0)
        fallback = symbol.get('fallback', '')
        code = symbol.get('code', '')
        category = symbol.get('category', '')
        
        if (score >= quality_criteria['score'] and
            len(fallback) <= quality_criteria['fallback_length'] and
            code.startswith('ng:') and
            len(code.split(':')) >= 3 and
            category and category != 'unknown'):
            quality_backup_symbols.append(symbol)
    
    print(f"✅ Simboli di qualità nel backup: {len(quality_backup_symbols)}")
    
    # FASE 2: Rimuovi duplicati mantenendo i migliori
    print("\n🔧 FASE 2: Deduplicazione intelligente...")
    
    # Raccogli simboli attuali per evitare duplicati
    current_codes = {s.get('code', '') for s in current_symbols}
    current_symbols_unicode = {s.get('symbol', '') for s in current_symbols}
    current_fallbacks = {s.get('fallback', '') for s in current_symbols}
    
    # Deduplicazione per codice
    unique_by_code = {}
    for symbol in quality_backup_symbols:
        code = symbol.get('code', '')
        if code not in current_codes:  # Non già presente nel registry attuale
            if code not in unique_by_code:
                unique_by_code[code] = symbol
            else:
                # Mantieni quello con score più alto
                existing = unique_by_code[code]
                if symbol.get('score', 0) > existing.get('score', 0):
                    unique_by_code[code] = symbol
    
    symbols_after_code = list(unique_by_code.values())
    print(f"✅ Dopo dedup per codice: {len(symbols_after_code)} simboli")
    
    # Deduplicazione per simbolo Unicode
    unique_by_symbol = {}
    for symbol in symbols_after_code:
        unicode_char = symbol.get('symbol', '')
        if unicode_char not in current_symbols_unicode:  # Non già presente
            if unicode_char not in unique_by_symbol:
                unique_by_symbol[unicode_char] = symbol
            else:
                # Mantieni quello con score più alto
                existing = unique_by_symbol[unicode_char]
                if symbol.get('score', 0) > existing.get('score', 0):
                    unique_by_symbol[unicode_char] = symbol
    
    symbols_after_unicode = list(unique_by_symbol.values())
    print(f"✅ Dopo dedup per Unicode: {len(symbols_after_unicode)} simboli")
    
    # Deduplicazione per fallback
    unique_by_fallback = {}
    for symbol in symbols_after_unicode:
        fallback = symbol.get('fallback', '')
        if fallback not in current_fallbacks:  # Non già presente
            if fallback not in unique_by_fallback:
                unique_by_fallback[fallback] = symbol
            else:
                # Mantieni quello con score più alto
                existing = unique_by_fallback[fallback]
                if symbol.get('score', 0) > existing.get('score', 0):
                    unique_by_fallback[fallback] = symbol
    
    symbols_to_restore = list(unique_by_fallback.values())
    print(f"✅ Simboli da ripristinare: {len(symbols_to_restore)} simboli")
    
    # FASE 3: Bilanciamento categorie
    print("\n⚖️ FASE 3: Bilanciamento categorie...")
    
    # Raggruppa per categoria
    by_category = defaultdict(list)
    for symbol in symbols_to_restore:
        category = symbol.get('category', 'unknown')
        by_category[category].append(symbol)
    
    # Limita per evitare sbilanciamenti
    max_per_category = 100  # Limite ragionevole
    balanced_symbols = []
    
    for category, cat_symbols in by_category.items():
        # Ordina per score decrescente
        cat_symbols.sort(key=lambda x: x.get('score', 0), reverse=True)
        
        # Prendi i migliori
        selected = cat_symbols[:max_per_category]
        balanced_symbols.extend(selected)
        
        if len(cat_symbols) > max_per_category:
            print(f"   {category}: {len(selected)}/{len(cat_symbols)} simboli (limitato)")
        else:
            print(f"   {category}: {len(selected)} simboli")
    
    print(f"✅ Simboli bilanciati: {len(balanced_symbols)} simboli")
    
    # FASE 4: Aggiungi al registry attuale
    print("\n➕ FASE 4: Aggiunta al registry...")
    
    # Combina simboli attuali + simboli ripristinati
    combined_symbols = current_symbols + balanced_symbols
    
    # Aggiorna registry
    current_data['approved_symbols'] = combined_symbols
    
    # Aggiorna statistiche
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    current_data['stats']['smart_restore'] = timestamp
    current_data['stats']['symbols_restored'] = len(balanced_symbols)
    current_data['stats']['total_symbols'] = len(combined_symbols)
    
    # Calcola copertura cognitiva
    cognitive_domains = [
        'neural_architectures', 'quantum_computing', 'symbolic_ai', 'meta_programming',
        'reasoning_patterns', 'cognitive_architectures', 'knowledge_representation',
        'semantic_understanding', 'contextual_reasoning', 'causal_reasoning',
        'analogical_reasoning', 'creative_thinking', 'problem_solving', 'learning_algorithms',
        'consciousness_models', 'self_reflection', 'goal_oriented_behavior',
        'emotional_intelligence', 'social_cognition', 'multimodal_reasoning',
        'temporal_cognition', 'language_understanding', 'memory_architectures',
        'attention_systems', 'decision_making', 'adaptive_intelligence',
        'reasoning_core', 'knowledge_systems', 'cognitive_control', 'learning_adaptation',
        'consciousness_meta'
    ]
    
    cognitive_symbols = len([s for s in combined_symbols 
                           if any(domain in s.get('code', '') for domain in cognitive_domains)])
    
    cognitive_coverage = cognitive_symbols / len(combined_symbols) * 100
    
    current_data['stats']['cognitive_coverage_restored'] = cognitive_coverage
    current_data['stats']['cognitive_symbols_restored'] = cognitive_symbols
    current_data['stats']['registry_quality'] = "HIGH_RESTORED"
    
    # Backup del registry attuale prima del restore
    backup_current_path = f"neuroglyph/core/locked_registry_godmode_v9.backup_before_restore_{timestamp}.json"
    with open(backup_current_path, 'w', encoding='utf-8') as f:
        json.dump(current_data, f, indent=2, ensure_ascii=False)
    print(f"💾 Backup pre-restore salvato: {backup_current_path}")
    
    # Salva registry ripristinato
    with open(registry_path, 'w', encoding='utf-8') as f:
        json.dump(current_data, f, indent=2, ensure_ascii=False)
    
    print(f"\n🎉 RIPRISTINO INTELLIGENTE COMPLETATO!")
    print(f"📊 Simboli prima: {len(current_symbols)}")
    print(f"📊 Simboli ripristinati: {len(balanced_symbols)}")
    print(f"📊 Simboli totali: {len(combined_symbols)}")
    print(f"🧠 Copertura cognitiva: {cognitive_coverage:.1f}%")
    print(f"✅ Registry ripristinato con qualità alta e zero duplicati")
    
    return len(combined_symbols), cognitive_coverage

if __name__ == "__main__":
    total, coverage = smart_restore_from_backup()
    
    print(f"\n🎯 RISULTATO FINALE:")
    print(f"📊 Simboli totali: {total}")
    print(f"🧠 Copertura cognitiva: {coverage:.1f}%")
    
    if total >= 1800 and coverage >= 25.0:
        print("🎉 SUCCESSO: Registry ottimale ripristinato!")
    elif total >= 1500:
        print("✅ BUONO: Registry bilanciato ripristinato")
    else:
        print("⚠️  PARZIALE: Potrebbe servire ulteriore espansione")
