#!/usr/bin/env python3
"""
Test training minimale - 1 step per verificare pipeline
"""

import torch
from transformers import AutoTokenizer, AutoModelForCausalLM, TrainingArguments, Trainer
from peft import LoraConfig, get_peft_model, TaskType
from datasets import Dataset

def minimal_training_test():
    print("🧪 Test training minimale...")
    
    # Carica tokenizer
    tokenizer = AutoTokenizer.from_pretrained("model/qwen2.5coder")
    if tokenizer.pad_token is None:
        tokenizer.pad_token = tokenizer.eos_token
    
    # Carica modello
    model = AutoModelForCausalLM.from_pretrained(
        "model/qwen2.5coder",
        torch_dtype=torch.float16,
        device_map="auto",
        load_in_4bit=True
    )
    
    # Applica LoRA
    lora_config = LoraConfig(
        r=8,  # Ridotto per test
        lora_alpha=8,
        target_modules=["q_proj", "v_proj"],  # Solo 2 moduli per test
        lora_dropout=0.0,
        bias="none",
        task_type=TaskType.CAUSAL_LM
    )
    
    model = get_peft_model(model, lora_config)
    
    # Dataset minimale
    data = [{"text": "def add(a, b): return a + b"}]
    dataset = Dataset.from_list(data)
    
    def tokenize(examples):
        return tokenizer(examples["text"], truncation=True, padding=True, max_length=128)
    
    dataset = dataset.map(tokenize, batched=True)
    
    # Training args minimali
    training_args = TrainingArguments(
        output_dir="./test_output",
        num_train_epochs=1,
        per_device_train_batch_size=1,
        max_steps=1,  # Solo 1 step
        logging_steps=1,
        save_steps=1,
        fp16=True,
        report_to=None
    )
    
    # Trainer
    trainer = Trainer(
        model=model,
        args=training_args,
        train_dataset=dataset,
        tokenizer=tokenizer
    )
    
    # 1 step di training
    trainer.train()
    
    print("✅ Test training minimale completato!")
    return True

if __name__ == "__main__":
    minimal_training_test()
