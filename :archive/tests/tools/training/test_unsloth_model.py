#!/usr/bin/env python3
"""
NEUROGLYPH LLM - Test Modello Unsloth
====================================

Test semplificato per verificare che il modello Unsloth funzioni correttamente.
"""

import os
import sys
from pathlib import Path

def test_model_files():
    """Verifica che tutti i file del modello siano presenti"""
    print("📁 VERIFICA FILES MODELLO")
    print("=" * 50)
    
    model_path = Path("model/qwen2.5coder")
    
    if not model_path.exists():
        print(f"❌ Directory modello non trovata: {model_path}")
        return False
    
    essential_files = [
        "config (1).json",
        "tokenizer.json",
        "tokenizer_config (1).json", 
        "model.safetensors",
        "vocab.json"
    ]
    
    all_present = True
    for file in essential_files:
        file_path = model_path / file
        if file_path.exists():
            size_mb = file_path.stat().st_size / (1024*1024)
            print(f"   ✅ {file} ({size_mb:.1f}MB)")
        else:
            print(f"   ❌ {file} mancante")
            all_present = False
    
    return all_present

def test_dependencies():
    """Verifica dipendenze installate"""
    print("\n📦 VERIFICA DIPENDENZE")
    print("=" * 50)
    
    dependencies = [
        ("torch", "PyTorch"),
        ("transformers", "Transformers"),
        ("datasets", "Datasets"),
        ("accelerate", "Accelerate"),
        ("peft", "PEFT")
    ]
    
    all_available = True
    for module, name in dependencies:
        try:
            __import__(module)
            print(f"   ✅ {name}")
        except ImportError:
            print(f"   ❌ {name} non installato")
            all_available = False
    
    return all_available

def test_pytorch_mps():
    """Verifica PyTorch MPS"""
    print("\n🚀 VERIFICA PYTORCH MPS")
    print("=" * 50)
    
    try:
        import torch
        print(f"   PyTorch version: {torch.__version__}")
        
        if torch.backends.mps.is_available():
            print("   ✅ MPS disponibile")
            
            # Test veloce MPS
            device = torch.device("mps")
            x = torch.randn(10, 10, device=device)
            y = torch.randn(10, 10, device=device)
            z = torch.matmul(x, y)
            print("   ✅ MPS funziona correttamente")
            return True
        else:
            print("   ⚠️ MPS non disponibile")
            return False
            
    except Exception as e:
        print(f"   ❌ Errore PyTorch: {e}")
        return False

def test_tokenizer_only():
    """Test solo tokenizer (più leggero)"""
    print("\n🔤 TEST TOKENIZER")
    print("=" * 50)
    
    try:
        from transformers import AutoTokenizer
        
        # Prova entrambi i path possibili
        model_paths = ["model/qwen2.5coder", "./model/qwen2.5coder"]
        tokenizer = None
        
        for path in model_paths:
            try:
                print(f"   Tentativo caricamento da: {path}")
                tokenizer = AutoTokenizer.from_pretrained(path)
                print(f"   ✅ Tokenizer caricato da: {path}")
                break
            except Exception as e:
                print(f"   ⚠️ Fallito da {path}: {str(e)[:50]}...")
                continue
        
        if tokenizer is None:
            print("   ❌ Impossibile caricare tokenizer")
            return False
        
        # Test tokenizzazione
        test_text = "def hello():"
        tokens = tokenizer.encode(test_text)
        decoded = tokenizer.decode(tokens)
        
        print(f"   Input: {test_text}")
        print(f"   Tokens: {tokens}")
        print(f"   Decoded: {decoded}")
        print(f"   Vocab size: {tokenizer.vocab_size}")
        
        return True
        
    except Exception as e:
        print(f"   ❌ Errore tokenizer: {e}")
        return False

def create_simple_training_script():
    """Crea script di training semplificato"""
    print("\n🔧 CREAZIONE SCRIPT TRAINING SEMPLIFICATO")
    print("=" * 50)
    
    script_content = '''#!/usr/bin/env python3
"""
NEUROGLYPH LLM - Training Script Semplificato
"""

import torch
from transformers import AutoTokenizer, AutoModelForCausalLM
from datasets import Dataset
import json

def create_sample_dataset():
    """Crea dataset di esempio per neuroglifi"""
    data = [
        {
            "instruction": "Converti neuroglifi in codice Python",
            "input": "⟨⟩α⊕β⤴α⊕β",
            "output": "def add(a, b):\\n    return a + b"
        },
        {
            "instruction": "Genera codice da neuroglifi",
            "input": "⟨⟩ƒ⟨n⟩⤴n≤1?n:ƒ⟨n-1⟩⊕ƒ⟨n-2⟩", 
            "output": "def fibonacci(n):\\n    return n if n <= 1 else fibonacci(n-1) + fibonacci(n-2)"
        },
        {
            "instruction": "Implementa funzione da simboli",
            "input": "⟨⟩α×β⤴α×β",
            "output": "def multiply(a, b):\\n    return a * b"
        }
    ]
    
    return Dataset.from_list(data)

def format_prompt(example):
    """Formatta prompt per training"""
    return f"""### Instruction:
{example['instruction']}

### Input:
{example['input']}

### Response:
{example['output']}"""

def main():
    """Training principale"""
    print("🚀 NEUROGLYPH LLM - Training Semplificato")
    
    # Carica tokenizer
    model_path = "model/qwen2.5coder"
    tokenizer = AutoTokenizer.from_pretrained(model_path)
    
    # Crea dataset
    dataset = create_sample_dataset()
    
    print(f"📊 Dataset: {len(dataset)} esempi")
    
    # Mostra esempio formattato
    example = dataset[0]
    formatted = format_prompt(example)
    print(f"\\n📝 Esempio formattato:")
    print(formatted)
    
    # Tokenizza esempio
    tokens = tokenizer.encode(formatted)
    print(f"\\n🔤 Tokens: {len(tokens)} token")
    print(f"   Primi 10: {tokens[:10]}")
    
    print("\\n✅ Setup training completato!")
    print("💡 Per training completo, installare Unsloth")

if __name__ == "__main__":
    main()
'''
    
    with open("scripts/simple_training.py", "w") as f:
        f.write(script_content)
    
    print("   ✅ Script creato: scripts/simple_training.py")

def main():
    """Test principale"""
    print("🧠 NEUROGLYPH LLM - Test Setup Unsloth")
    print("🎯 Verifica modello e dipendenze")
    print("=" * 70)
    
    # Test componenti
    files_ok = test_model_files()
    deps_ok = test_dependencies()
    mps_ok = test_pytorch_mps()
    tokenizer_ok = test_tokenizer_only()
    
    # Crea script training
    create_simple_training_script()
    
    # Risultati finali
    print("\n📊 RISULTATI FINALI")
    print("=" * 70)
    
    results = {
        "Files modello": files_ok,
        "Dipendenze": deps_ok,
        "PyTorch MPS": mps_ok,
        "Tokenizer": tokenizer_ok
    }
    
    all_ok = all(results.values())
    
    for test, result in results.items():
        status = "✅" if result else "❌"
        print(f"   {status} {test}")
    
    if all_ok:
        print("\n🎉 SETUP UNSLOTH COMPLETATO!")
        print("✅ Modello pronto per training")
        print("🚀 Prossimo step: python3 scripts/simple_training.py")
    else:
        print("\n⚠️ Setup incompleto")
        print("🔧 Risolvi i problemi evidenziati")
    
    return all_ok

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
