#!/usr/bin/env python3
"""
NEUROGLYPH LLM - TEST SEMPLICE COGNITIVE RUNTIME
================================================

Test semplificato del sistema cognitivo con registry ridotto.

Autore: NEUROGLYPH ULTRA Team
Data: 2025-05-26
Licenza: MIT
"""

import sys
import os
import json
from datetime import datetime

# Aggiungi path per import
current_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.append(current_dir)

def create_test_registry():
    """Crea un registry di test ridotto."""
    test_registry = {
        "registry_version": "1.0",
        "created": "2025-05-26",
        "description": "NEUROGLYPH Test Registry - Versione ridotta per test",
        "stats": {
            "total_submissions": 10,
            "approved": 10,
            "rejected": 0,
            "pending": 0
        },
        "approved_symbols": [
            {
                "id": "NG0001",
                "symbol": "◯",
                "code": "ng:operator:sub",
                "fallback": "[SUB]",
                "category": "operator",
                "name": "sub",
                "description": "Operator operation: sub",
                "unicode_point": "U+25EF",
                "approved_date": "2025-05-26",
                "validation_score": 95.0,
                "status": "validated",
                "token_cost": 1,
                "score": 95.0,
                "token_density": 0.92,
                "tier": "base",
                "valid": True,
                "cognitive_domain": True
            },
            {
                "id": "NG0002",
                "symbol": "■",
                "code": "ng:memory:pointer",
                "fallback": "[PTR]",
                "category": "memory",
                "name": "pointer",
                "description": "Memory operation: pointer",
                "unicode_point": "U+25A0",
                "approved_date": "2025-05-26",
                "validation_score": 95.0,
                "status": "validated",
                "token_cost": 1,
                "score": 95.0,
                "token_density": 1.0,
                "tier": "base",
                "valid": True,
                "cognitive_domain": True
            },
            {
                "id": "NG0003",
                "symbol": "⫗",
                "code": "ng:memory:alloc",
                "fallback": "[ALLOC]",
                "category": "memory",
                "name": "alloc",
                "description": "Memory operation: alloc",
                "unicode_point": "U+2AD7",
                "approved_date": "2025-05-26",
                "validation_score": 98.0,
                "status": "validated",
                "token_cost": 1,
                "score": 98.0,
                "token_density": 0.97,
                "tier": "base",
                "valid": True,
                "cognitive_domain": True
            },
            {
                "id": "NG0004",
                "symbol": "≺",
                "code": "ng:logic:implies",
                "fallback": "[IMP]",
                "category": "logic",
                "name": "implies",
                "description": "Logic operation: implies",
                "unicode_point": "U+227A",
                "approved_date": "2025-05-26",
                "validation_score": 95.0,
                "status": "validated",
                "token_cost": 1,
                "score": 95.0,
                "token_density": 0.96,
                "tier": "base",
                "valid": True,
                "cognitive_domain": True
            },
            {
                "id": "NG0005",
                "symbol": "⌀",
                "code": "ng:logic:or",
                "fallback": "[OR]",
                "category": "logic",
                "name": "or",
                "description": "Logic operation: or",
                "unicode_point": "U+2300",
                "approved_date": "2025-05-26",
                "validation_score": 98.0,
                "status": "validated",
                "token_cost": 1,
                "score": 98.0,
                "token_density": 0.94,
                "tier": "base",
                "valid": True,
                "cognitive_domain": True
            },
            {
                "id": "NG0006",
                "symbol": "⊬",
                "code": "ng:structure:function",
                "fallback": "[FUNC]",
                "category": "structure",
                "name": "function",
                "description": "Structure operation: function",
                "unicode_point": "U+22AC",
                "approved_date": "2025-05-26",
                "validation_score": 95.0,
                "status": "validated",
                "token_cost": 1,
                "score": 95.0,
                "token_density": 0.92,
                "tier": "base",
                "valid": True,
                "cognitive_domain": True
            },
            {
                "id": "NG0007",
                "symbol": "≯",
                "code": "ng:operator:mul",
                "fallback": "[MUL]",
                "category": "operator",
                "name": "mul",
                "description": "Operator operation: mul",
                "unicode_point": "U+226F",
                "approved_date": "2025-05-26",
                "validation_score": 95.0,
                "status": "validated",
                "token_cost": 1,
                "score": 95.0,
                "token_density": 0.98,
                "tier": "base",
                "valid": True,
                "cognitive_domain": True
            },
            {
                "id": "NG0008",
                "symbol": "◺",
                "code": "ng:operator:mod",
                "fallback": "[MOD]",
                "category": "operator",
                "name": "mod",
                "description": "Operator operation: mod",
                "unicode_point": "U+25FA",
                "approved_date": "2025-05-26",
                "validation_score": 95.0,
                "status": "validated",
                "token_cost": 1,
                "score": 95.0,
                "token_density": 0.98,
                "tier": "base",
                "valid": True,
                "cognitive_domain": True
            },
            {
                "id": "NG0009",
                "symbol": "⤀",
                "code": "ng:flow:loop",
                "fallback": "[LOOP]",
                "category": "flow",
                "name": "loop",
                "description": "Flow operation: loop",
                "unicode_point": "U+2900",
                "approved_date": "2025-05-26",
                "validation_score": 98.0,
                "status": "validated",
                "token_cost": 1,
                "score": 98.0,
                "token_density": 0.92,
                "tier": "base",
                "valid": True,
                "cognitive_domain": True
            },
            {
                "id": "NG0010",
                "symbol": "◟",
                "code": "ng:memory:free",
                "fallback": "[FREE]",
                "category": "memory",
                "name": "free",
                "description": "Memory operation: free",
                "unicode_point": "U+25DF",
                "approved_date": "2025-05-26",
                "validation_score": 95.0,
                "status": "validated",
                "token_cost": 1,
                "score": 95.0,
                "token_density": 0.94,
                "tier": "base",
                "valid": True,
                "cognitive_domain": True
            }
        ]
    }
    
    # Salva registry di test
    with open('test_registry.json', 'w', encoding='utf-8') as f:
        json.dump(test_registry, f, indent=2, ensure_ascii=False)
    
    return 'test_registry.json'

def test_cognitive_components():
    """Test dei componenti cognitivi con registry ridotto."""
    print("🧠 NEUROGLYPH COGNITIVE RUNTIME - TEST SEMPLICE")
    print("=" * 60)
    print(f"📅 Data: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print()
    
    # Crea registry di test
    print("📝 Creando registry di test...")
    test_registry_path = create_test_registry()
    print(f"✅ Registry di test creato: {test_registry_path}")
    
    try:
        # Test 1: CognitiveState
        print("\n🧠 Test 1: CognitiveState")
        print("-" * 40)
        
        from cognitive_state import CognitiveState
        
        cognitive_state = CognitiveState(test_registry_path)
        print(f"✅ CognitiveState creato")
        
        # Test attivazione domini
        test_symbols = ["◯", "■", "⫗"]
        activated_domains = cognitive_state.activate_domains_for_symbols(test_symbols)
        print(f"✅ Domini attivati: {len(activated_domains)}")
        
        # Test stato
        status = cognitive_state.get_cognitive_status()
        print(f"✅ Coscienza: {status['consciousness_level']:.2f}")
        print(f"✅ Simboli caricati: {len(cognitive_state._symbol_registry)}")
        
        # Test 2: ThinkingEngine
        print("\n🧠 Test 2: ThinkingEngine")
        print("-" * 40)
        
        from thinking_engine import ThinkingEngine, ThinkingMode
        
        engine = ThinkingEngine(cognitive_state, test_registry_path)
        print(f"✅ ThinkingEngine creato")
        
        # Test pensiero
        process = engine.think(test_symbols, ThinkingMode.ANALYTICAL)
        print(f"✅ Pensiero completato: {process.success}")
        print(f"✅ Confidenza: {process.final_confidence:.2f}")
        print(f"✅ Step: {len(process.reasoning_steps)}")
        
        # Test 3: CognitiveIntegration
        print("\n🧠 Test 3: CognitiveIntegration")
        print("-" * 40)
        
        from cognitive_integration import CognitiveIntegration, IntegrationMode
        
        integration = CognitiveIntegration(IntegrationMode.ENHANCED, test_registry_path)
        print(f"✅ CognitiveIntegration creato")
        
        # Test pensiero con testo
        response = integration.think("Crea una funzione", ThinkingMode.ANALYTICAL)
        print(f"✅ Risposta generata: {len(response)} caratteri")
        print(f"✅ Risposta: {response[:100]}...")
        
        # Test pensiero simbolico
        symbolic_response = integration.think_symbolic(test_symbols, ThinkingMode.CREATIVE)
        print(f"✅ Simboli output: {symbolic_response}")
        
        # Test 4: Stato finale del sistema
        print("\n📊 Test 4: Stato Sistema")
        print("-" * 40)
        
        final_status = integration.get_cognitive_status()
        print(f"✅ Modalità: {final_status['mode']}")
        print(f"✅ Richieste totali: {final_status['performance_metrics']['total_requests']}")
        print(f"✅ Tasso successo: {final_status['performance_metrics']['successful_requests'] / max(1, final_status['performance_metrics']['total_requests']):.2f}")
        print(f"✅ Confidenza media: {final_status['performance_metrics']['average_confidence']:.2f}")
        
        # Componenti attivi
        components = final_status['components']
        print(f"✅ DAG Memory: {components['dag_memory_available']}")
        print(f"✅ Encoder: {components['encoder_available']}")
        
        print("\n🎉 TUTTI I TEST SUPERATI! NEUROGLYPH COGNITIVE RUNTIME FUNZIONA!")
        return True
        
    except Exception as e:
        print(f"\n❌ Errore durante i test: {e}")
        import traceback
        traceback.print_exc()
        return False
    
    finally:
        # Cleanup
        if os.path.exists(test_registry_path):
            os.remove(test_registry_path)
            print(f"\n🧹 Registry di test rimosso: {test_registry_path}")

if __name__ == "__main__":
    success = test_cognitive_components()
    exit(0 if success else 1)
