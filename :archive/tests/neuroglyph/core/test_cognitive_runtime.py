#!/usr/bin/env python3
"""
NEUROGLYPH LLM - TEST COGNITIVE RUNTIME
=======================================

Test completo del sistema di runtime cognitivo di NEUROGLYPH LLM.
Verifica l'integrazione di tutti i componenti e le funzionalità di pensiero.

Autore: NEUROGLYPH ULTRA Team
Data: 2025-05-26
Licenza: MIT
"""

import sys
import os
import time
import json
from datetime import datetime

# Aggiungi path per import
current_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.append(current_dir)

# Import componenti NEUROGLYPH
from cognitive_state import CognitiveState, CognitiveDomainType
from thinking_engine import ThinkingEngine, ThinkingMode
from cognitive_integration import CognitiveIntegration, IntegrationMode, CognitiveRequest, CognitiveResponse

def test_cognitive_state():
    """Test del CognitiveState."""
    print("🧠 Test CognitiveState")
    print("-" * 40)

    # Crea cognitive state
    cognitive_state = CognitiveState()

    # Test attivazione domini
    test_symbols = ["◯", "■", "⫗", "≺", "⌀"]
    activated_domains = cognitive_state.activate_domains_for_symbols(test_symbols)

    print(f"✅ Simboli test: {test_symbols}")
    print(f"✅ Domini attivati: {len(activated_domains)}")
    print(f"✅ Entropia cognitiva: {cognitive_state.calculate_cognitive_entropy():.2f}")

    # Test rilevamento loop
    loop_detected = cognitive_state.detect_cognitive_loops()
    print(f"✅ Loop rilevati: {loop_detected}")

    # Test stato
    status = cognitive_state.get_cognitive_status()
    print(f"✅ Coscienza: {status['consciousness_level']:.2f}")
    print(f"✅ Auto-consapevolezza: {status['self_awareness']:.2f}")

    return True

def test_thinking_engine():
    """Test del ThinkingEngine."""
    print("\n🧠 Test ThinkingEngine")
    print("-" * 40)

    # Crea thinking engine
    engine = ThinkingEngine()

    # Test pensiero analitico
    test_symbols = ["⊬", "■", "⫗"]
    print(f"🧪 Test analitico: {test_symbols}")

    process = engine.think(test_symbols, ThinkingMode.ANALYTICAL)
    print(f"✅ Successo: {process.success}")
    print(f"✅ Confidenza: {process.final_confidence:.2f}")
    print(f"✅ Step ragionamento: {len(process.reasoning_steps)}")
    print(f"✅ Domini attivi: {len(process.active_domains)}")

    # Test pensiero creativo
    print(f"\n🎨 Test creativo: {test_symbols}")

    creative_process = engine.think(test_symbols, ThinkingMode.CREATIVE)
    print(f"✅ Successo: {creative_process.success}")
    print(f"✅ Confidenza: {creative_process.final_confidence:.2f}")
    print(f"✅ Output simboli: {creative_process.output_symbols}")

    # Test pensiero riflessivo
    print(f"\n🤔 Test riflessivo: {test_symbols}")

    reflective_process = engine.think(test_symbols, ThinkingMode.REFLECTIVE)
    print(f"✅ Successo: {reflective_process.success}")
    print(f"✅ Confidenza: {reflective_process.final_confidence:.2f}")

    # Stato engine
    status = engine.get_thinking_status()
    print(f"\n📊 Statistiche Engine:")
    print(f"  • Pensieri totali: {status['statistics']['total_thoughts']}")
    print(f"  • Tasso successo: {status['statistics']['successful_thoughts'] / max(1, status['statistics']['total_thoughts']):.2f}")
    print(f"  • Confidenza media: {status['statistics']['average_confidence']:.2f}")

    return True

def test_cognitive_integration():
    """Test del CognitiveIntegration."""
    print("\n🧠 Test CognitiveIntegration")
    print("-" * 40)

    # Test modalità diverse
    modes = [IntegrationMode.BASIC, IntegrationMode.ENHANCED, IntegrationMode.ULTRA]

    for mode in modes:
        print(f"\n🎯 Test modalità: {mode.value}")

        integration = CognitiveIntegration(mode)

        # Test pensiero con testo
        test_text = "Implementa una funzione ricorsiva per calcolare fibonacci"
        response = integration.think(test_text, ThinkingMode.ANALYTICAL)

        print(f"✅ Input: {test_text[:50]}...")
        print(f"✅ Output: {response[:100]}...")

        # Test pensiero simbolico
        test_symbols = ["⊬", "≺", "◯", "■"]
        symbolic_response = integration.think_symbolic(test_symbols, ThinkingMode.CREATIVE)

        print(f"✅ Simboli input: {test_symbols}")
        print(f"✅ Simboli output: {symbolic_response}")

        # Stato sistema
        status = integration.get_cognitive_status()
        print(f"✅ Modalità: {status['mode']}")
        print(f"✅ Richieste: {status['performance_metrics']['total_requests']}")
        print(f"✅ Successo: {status['performance_metrics']['successful_requests'] / max(1, status['performance_metrics']['total_requests']):.2f}")

    return True

def test_advanced_scenarios():
    """Test scenari avanzati."""
    print("\n🚀 Test Scenari Avanzati")
    print("-" * 40)

    integration = CognitiveIntegration(IntegrationMode.ULTRA)

    # Scenario 1: Risoluzione problema complesso
    print("🧪 Scenario 1: Problema complesso")
    complex_request = CognitiveRequest(
        input_text="Crea un algoritmo di ordinamento ottimizzato con gestione memoria dinamica",
        thinking_mode=ThinkingMode.PROBLEM_SOLVING,
        response_type=CognitiveResponse.CODE,
        require_validation=True
    )

    result = integration.process_cognitive_request(complex_request)
    print(f"✅ Successo: {result.success}")
    print(f"✅ Confidenza: {result.confidence:.2f}")
    print(f"✅ Validazione: {result.validation_passed}")
    print(f"✅ Domini attivi: {len(result.active_domains)}")
    print(f"✅ Output: {result.output_text[:100]}...")

    # Scenario 2: Pensiero creativo
    print("\n🎨 Scenario 2: Creatività")
    creative_request = CognitiveRequest(
        input_text="Inventa una nuova struttura dati per AI simbolica",
        thinking_mode=ThinkingMode.CREATIVE,
        response_type=CognitiveResponse.MIXED,
        require_validation=False
    )

    result = integration.process_cognitive_request(creative_request)
    print(f"✅ Successo: {result.success}")
    print(f"✅ Confidenza: {result.confidence:.2f}")
    print(f"✅ Output: {result.output_text[:150]}...")

    # Scenario 3: Auto-riflessione
    print("\n🤔 Scenario 3: Auto-riflessione")
    reflective_request = CognitiveRequest(
        input_text="Analizza i tuoi processi di pensiero precedenti",
        thinking_mode=ThinkingMode.REFLECTIVE,
        response_type=CognitiveResponse.NATURAL,
        require_validation=True
    )

    result = integration.process_cognitive_request(reflective_request)
    print(f"✅ Successo: {result.success}")
    print(f"✅ Confidenza: {result.confidence:.2f}")
    print(f"✅ Traccia ragionamento: {len(result.reasoning_trace)} step")

    return True

def test_performance_metrics():
    """Test metriche di performance."""
    print("\n📊 Test Performance Metrics")
    print("-" * 40)

    integration = CognitiveIntegration(IntegrationMode.ENHANCED)

    # Esegui serie di test per raccogliere metriche
    test_cases = [
        ("Calcola la complessità di un algoritmo", ThinkingMode.ANALYTICAL),
        ("Crea una metafora per spiegare l'AI", ThinkingMode.CREATIVE),
        ("Trova errori nel codice", ThinkingMode.CRITICAL),
        ("Rifletti sui risultati ottenuti", ThinkingMode.REFLECTIVE),
        ("Risolvi questo problema logico", ThinkingMode.PROBLEM_SOLVING)
    ]

    start_time = time.time()

    for i, (text, mode) in enumerate(test_cases):
        print(f"🧪 Test {i+1}: {mode.value}")
        response = integration.think(text, mode)
        print(f"  ✅ Completato: {len(response)} caratteri")

    total_time = time.time() - start_time

    # Analizza metriche finali
    status = integration.get_cognitive_status()
    metrics = status['performance_metrics']

    print(f"\n📈 Metriche Finali:")
    print(f"  • Tempo totale: {total_time:.2f}s")
    print(f"  • Richieste totali: {metrics['total_requests']}")
    print(f"  • Tasso successo: {metrics['successful_requests'] / max(1, metrics['total_requests']):.2f}")
    print(f"  • Confidenza media: {metrics['average_confidence']:.2f}")
    print(f"  • Tempo medio: {metrics['average_processing_time']:.3f}s")
    print(f"  • Validazione successo: {metrics['validation_success_rate']:.2f}")

    # Distribuzione modalità
    print(f"\n🎯 Distribuzione Modalità:")
    for mode, count in metrics['mode_usage'].items():
        if count > 0:
            print(f"  • {mode}: {count}")

    # Domini più utilizzati
    print(f"\n🧠 Domini Più Utilizzati:")
    domain_freq = metrics['domain_activation_frequency']
    sorted_domains = sorted(domain_freq.items(), key=lambda x: x[1], reverse=True)
    for domain, freq in sorted_domains[:5]:
        print(f"  • {domain}: {freq}")

    return True

def main():
    """Test principale del sistema cognitivo."""
    print("🧠 NEUROGLYPH COGNITIVE RUNTIME - TEST COMPLETO")
    print("=" * 60)
    print(f"📅 Data: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print()

    try:
        # Esegui tutti i test
        tests = [
            ("CognitiveState", test_cognitive_state),
            ("ThinkingEngine", test_thinking_engine),
            ("CognitiveIntegration", test_cognitive_integration),
            ("Scenari Avanzati", test_advanced_scenarios),
            ("Performance Metrics", test_performance_metrics)
        ]

        results = {}

        for test_name, test_func in tests:
            print(f"\n🚀 Eseguendo test: {test_name}")
            try:
                start_time = time.time()
                success = test_func()
                duration = time.time() - start_time

                results[test_name] = {
                    'success': success,
                    'duration': duration
                }

                print(f"✅ {test_name} completato in {duration:.2f}s")

            except Exception as e:
                print(f"❌ {test_name} fallito: {e}")
                results[test_name] = {
                    'success': False,
                    'error': str(e)
                }

        # Riepilogo finale
        print("\n" + "=" * 60)
        print("📊 RIEPILOGO TEST")
        print("=" * 60)

        total_tests = len(tests)
        successful_tests = sum(1 for r in results.values() if r.get('success', False))

        print(f"✅ Test completati: {successful_tests}/{total_tests}")
        print(f"📈 Tasso successo: {successful_tests/total_tests*100:.1f}%")

        for test_name, result in results.items():
            status = "✅ PASS" if result.get('success', False) else "❌ FAIL"
            duration = result.get('duration', 0)
            print(f"  {status} {test_name} ({duration:.2f}s)")

        if successful_tests == total_tests:
            print("\n🎉 TUTTI I TEST SUPERATI! NEUROGLYPH COGNITIVE RUNTIME FUNZIONA PERFETTAMENTE!")
        else:
            print(f"\n⚠️ {total_tests - successful_tests} test falliti. Verificare i componenti.")

        return successful_tests == total_tests

    except Exception as e:
        print(f"\n❌ Errore critico durante i test: {e}")
        return False

if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)
