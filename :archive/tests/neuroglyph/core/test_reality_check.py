#!/usr/bin/env python3
"""
NEUROGLYPH - REALITY CHECK TEST SUITE
====================================

Test suite per validare che NEUROGLYPH produca intelligenza reale,
non solo simulazione di intelligenza.

Autore: NEUROGLYPH ULTRA Team
Data: 2025-05-26
"""

import sys
import os
import time
import json
import logging
from datetime import datetime
from typing import Dict, List, Any, Tuple

# Aggiungi path per import moduli NEUROGLYPH
current_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.append(current_dir)

from expand_thought_universe import create_thought_universe_expander, ExpansionMode
from symbol_mutator import create_symbol_mutator, MutationStrategy
from cognitive_integration import create_cognitive_integration, IntegrationMode, CognitiveRequest, ThinkingMode

# Configurazione logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class RealityCheckSuite:
    """Suite di test per verificare l'intelligenza reale di NEUROGLYPH."""

    def __init__(self):
        self.results = {}
        self.start_time = datetime.now()

        # Inizializza componenti
        self.cognitive_integration = create_cognitive_integration(IntegrationMode.ENHANCED)
        self.symbol_mutator = create_symbol_mutator(
            registry_path="neuroglyph/core/locked_registry_godmode_v9.json",
            strategy=MutationStrategy.CREATIVE
        )
        self.expander = create_thought_universe_expander(ExpansionMode.CREATIVE)

        logger.info("🧪 Reality Check Suite inizializzata")

    def test_logical_reasoning(self) -> Dict[str, Any]:
        """Test 1: Capacità di ragionamento logico."""
        logger.info("🧠 Test 1: Ragionamento Logico")

        # Test problems logici semplici
        logic_tests = [
            {
                "input": "Se A > B e B > C, cosa possiamo dire di A e C?",
                "expected_concept": "transitivity",
                "symbols_expected": ["⊃", "∧", "→"]
            },
            {
                "input": "Tutti i gatti sono mammiferi. Fluffy è un gatto. Cosa è Fluffy?",
                "expected_concept": "syllogism",
                "symbols_expected": ["∀", "∈", "→"]
            },
            {
                "input": "Se piove, allora la strada è bagnata. La strada non è bagnata. Cosa possiamo concludere?",
                "expected_concept": "modus_tollens",
                "symbols_expected": ["¬", "→", "∴"]
            }
        ]

        results = []
        for i, test in enumerate(logic_tests):
            try:
                # Processa con NEUROGLYPH
                request = CognitiveRequest(
                    input_text=test["input"],
                    thinking_mode=ThinkingMode.ANALYTICAL,
                    require_validation=True
                )

                result = self.cognitive_integration.process_cognitive_request(request)

                # Analizza risultato
                test_result = {
                    "test_id": i + 1,
                    "input": test["input"],
                    "success": result.success,
                    "output_symbols": result.output_symbols if result.success else [],
                    "confidence": result.confidence if result.success else 0.0,
                    "reasoning_trace": result.reasoning_trace if result.success else [],
                    "expected_symbols_found": any(sym in result.output_symbols for sym in test["symbols_expected"]) if result.success else False
                }

                results.append(test_result)

                logger.info(f"  Test {i+1}: {'✅' if result.success else '❌'} "
                          f"Confidenza: {result.confidence:.2f}")

            except Exception as e:
                logger.error(f"  Test {i+1}: 💥 Errore: {e}")
                results.append({
                    "test_id": i + 1,
                    "input": test["input"],
                    "success": False,
                    "error": str(e)
                })

        # Calcola score complessivo
        successful_tests = sum(1 for r in results if r.get("success", False))
        logic_score = successful_tests / len(logic_tests)

        return {
            "test_name": "logical_reasoning",
            "score": logic_score,
            "details": results,
            "summary": f"{successful_tests}/{len(logic_tests)} test logici superati"
        }

    def test_symbol_mutation_quality(self) -> Dict[str, Any]:
        """Test 2: Qualità delle mutazioni simboliche."""
        logger.info("🧬 Test 2: Qualità Mutazioni Simboliche")

        # Genera batch di mutazioni
        mutation_results = []
        for i in range(10):
            result = self.symbol_mutator.generate_mutation()
            if result.success and result.candidate:
                mutation_results.append({
                    "mutation_id": i + 1,
                    "symbol": result.candidate.mutated_symbol,
                    "type": result.candidate.mutation_type.value,
                    "confidence": result.candidate.confidence_score,
                    "collision_risk": result.candidate.collision_risk,
                    "unicode_safe": result.candidate.unicode_safe,
                    "category": result.candidate.semantic_category
                })

        # Analizza qualità
        if mutation_results:
            avg_confidence = sum(r["confidence"] for r in mutation_results) / len(mutation_results)
            avg_collision_risk = sum(r["collision_risk"] for r in mutation_results) / len(mutation_results)
            unicode_safe_count = sum(1 for r in mutation_results if r["unicode_safe"])
            unique_symbols = len(set(r["symbol"] for r in mutation_results))

            quality_score = (
                avg_confidence * 0.4 +
                (1 - avg_collision_risk) * 0.3 +
                (unicode_safe_count / len(mutation_results)) * 0.2 +
                (unique_symbols / len(mutation_results)) * 0.1
            )
        else:
            quality_score = 0.0

        logger.info(f"  Mutazioni generate: {len(mutation_results)}/10")
        logger.info(f"  Score qualità: {quality_score:.2f}")

        return {
            "test_name": "symbol_mutation_quality",
            "score": quality_score,
            "details": {
                "mutations_generated": len(mutation_results),
                "average_confidence": avg_confidence if mutation_results else 0,
                "average_collision_risk": avg_collision_risk if mutation_results else 1,
                "unicode_safe_percentage": (unicode_safe_count / len(mutation_results)) if mutation_results else 0,
                "symbol_uniqueness": (unique_symbols / len(mutation_results)) if mutation_results else 0,
                "mutations": mutation_results
            },
            "summary": f"{len(mutation_results)} mutazioni con qualità {quality_score:.2f}"
        }

    def test_intelligence_growth(self) -> Dict[str, Any]:
        """Test 3: Crescita reale dell'intelligenza."""
        logger.info("📈 Test 3: Crescita Intelligenza")

        # Misura QI iniziale
        initial_status = self.expander.get_expansion_status()
        initial_iq = initial_status["intelligence_metrics"]["intelligence_quotient"]

        # Esegui cicli di espansione
        logger.info("  Eseguendo 5 cicli di espansione...")
        expansion_results = self.expander.run_finite_expansion(cycles=5)

        # Misura QI finale
        final_status = self.expander.get_expansion_status()
        final_iq = final_status["intelligence_metrics"]["intelligence_quotient"]

        # Analizza crescita
        iq_growth = final_iq - initial_iq
        successful_cycles = sum(1 for cycle in expansion_results if cycle.successful_mutations > 0)
        total_mutations = sum(cycle.successful_mutations for cycle in expansion_results)
        total_abstractions = sum(cycle.new_abstractions for cycle in expansion_results)

        # Score basato su crescita effettiva
        growth_score = min(iq_growth / 10.0, 1.0) if iq_growth > 0 else 0.0

        logger.info(f"  QI iniziale: {initial_iq:.1f}")
        logger.info(f"  QI finale: {final_iq:.1f}")
        logger.info(f"  Crescita: {iq_growth:.1f}")
        logger.info(f"  Mutazioni riuscite: {total_mutations}")

        return {
            "test_name": "intelligence_growth",
            "score": growth_score,
            "details": {
                "initial_iq": initial_iq,
                "final_iq": final_iq,
                "iq_growth": iq_growth,
                "successful_cycles": successful_cycles,
                "total_cycles": len(expansion_results),
                "total_mutations": total_mutations,
                "total_abstractions": total_abstractions,
                "cycles": [
                    {
                        "cycle_id": cycle.cycle_id,
                        "successful_mutations": cycle.successful_mutations,
                        "failed_mutations": cycle.failed_mutations,
                        "new_abstractions": cycle.new_abstractions,
                        "cognitive_growth": cycle.cognitive_growth
                    }
                    for cycle in expansion_results
                ]
            },
            "summary": f"QI cresciuto da {initial_iq:.1f} a {final_iq:.1f} (+{iq_growth:.1f})"
        }

    def test_semantic_coherence(self) -> Dict[str, Any]:
        """Test 4: Coerenza semantica degli output."""
        logger.info("🎯 Test 4: Coerenza Semantica")

        # Test con input semanticamente correlati
        semantic_tests = [
            {
                "input": "matematica e logica",
                "expected_domains": ["logic", "math", "reasoning"]
            },
            {
                "input": "memoria e apprendimento",
                "expected_domains": ["memory", "learning", "cognitive"]
            },
            {
                "input": "creatività e innovazione",
                "expected_domains": ["creative", "innovation", "meta"]
            }
        ]

        coherence_results = []
        for i, test in enumerate(semantic_tests):
            try:
                request = CognitiveRequest(
                    input_text=test["input"],
                    thinking_mode=ThinkingMode.CREATIVE,
                    require_validation=True
                )

                result = self.cognitive_integration.process_cognitive_request(request)

                # Verifica coerenza semantica
                domain_match = False
                if result.success and result.active_domains:
                    active_domain_names = [domain.value for domain in result.active_domains]
                    domain_match = any(
                        expected in active_domain_names
                        for expected in test["expected_domains"]
                    )

                coherence_results.append({
                    "test_id": i + 1,
                    "input": test["input"],
                    "success": result.success,
                    "domain_match": domain_match,
                    "active_domains": active_domain_names if result.success else [],
                    "confidence": result.confidence if result.success else 0.0
                })

                logger.info(f"  Test {i+1}: {'✅' if domain_match else '❌'} "
                          f"Domini: {active_domain_names if result.success else 'None'}")

            except Exception as e:
                logger.error(f"  Test {i+1}: 💥 Errore: {e}")
                coherence_results.append({
                    "test_id": i + 1,
                    "input": test["input"],
                    "success": False,
                    "error": str(e)
                })

        # Calcola score coerenza
        coherent_tests = sum(1 for r in coherence_results if r.get("domain_match", False))
        coherence_score = coherent_tests / len(semantic_tests)

        return {
            "test_name": "semantic_coherence",
            "score": coherence_score,
            "details": coherence_results,
            "summary": f"{coherent_tests}/{len(semantic_tests)} test di coerenza superati"
        }

    def run_full_suite(self) -> Dict[str, Any]:
        """Esegue tutti i test e genera report finale."""
        logger.info("🚀 Avvio Reality Check Suite Completa")

        # Esegui tutti i test
        test_results = []

        test_results.append(self.test_logical_reasoning())
        test_results.append(self.test_symbol_mutation_quality())
        test_results.append(self.test_intelligence_growth())
        test_results.append(self.test_semantic_coherence())

        # Calcola score complessivo
        overall_score = sum(result["score"] for result in test_results) / len(test_results)

        # Determina verdetto
        if overall_score >= 0.8:
            verdict = "🏆 ECCELLENTE - Sistema dimostra intelligenza reale"
        elif overall_score >= 0.6:
            verdict = "✅ BUONO - Sistema funziona con alcune limitazioni"
        elif overall_score >= 0.4:
            verdict = "⚠️ SUFFICIENTE - Sistema ha potenziale ma serve miglioramento"
        else:
            verdict = "❌ INSUFFICIENTE - Sistema non dimostra intelligenza reale"

        duration = (datetime.now() - self.start_time).total_seconds()

        final_report = {
            "timestamp": datetime.now().isoformat(),
            "duration_seconds": duration,
            "overall_score": overall_score,
            "verdict": verdict,
            "test_results": test_results,
            "summary": {
                "total_tests": len(test_results),
                "average_score": overall_score,
                "best_performing_test": max(test_results, key=lambda x: x["score"])["test_name"],
                "worst_performing_test": min(test_results, key=lambda x: x["score"])["test_name"]
            }
        }

        logger.info(f"🏁 Reality Check completato in {duration:.1f}s")
        logger.info(f"📊 Score complessivo: {overall_score:.2f}")
        logger.info(f"🎯 Verdetto: {verdict}")

        return final_report


def main():
    """Esegue la Reality Check Suite."""
    print("🧪 NEUROGLYPH Reality Check Suite")
    print("=" * 60)

    # Crea e esegui suite
    suite = RealityCheckSuite()
    report = suite.run_full_suite()

    # Salva report
    report_file = f"reality_check_report_{int(time.time())}.json"
    with open(report_file, 'w') as f:
        json.dump(report, f, indent=2)

    print(f"\n📄 Report salvato in: {report_file}")
    print(f"🎯 Verdetto finale: {report['verdict']}")

    return report


if __name__ == "__main__":
    main()
