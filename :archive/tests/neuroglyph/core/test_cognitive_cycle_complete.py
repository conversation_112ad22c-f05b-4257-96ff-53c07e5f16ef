#!/usr/bin/env python3
"""
NEUROGLYPH LLM - TEST CICLO COGNITIVO COMPLETO
==============================================

Test completo della chiusura del ciclo cognitivo:
INPUT → SIMBOLI → PENSIERO → VALIDAZIONE → OUTPUT → CODICE

Verifica l'integrazione di tutti i componenti ULTRA/GOD MODE:
- Enhanced Symbol Loader
- Cognitive Runtime Engine  
- Advanced Encoder/Decoder
- Complete Cognitive Integration

Autore: NEUROGLYPH ULTRA Team
Data: 2025-05-26
Licenza: MIT
"""

import sys
import os
import time
import json
from datetime import datetime

# Aggiungi path per import
current_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.append(current_dir)

# Import componenti NEUROGLYPH
from cognitive_state import CognitiveState, CognitiveDomainType
from thinking_engine import ThinkingEngine, ThinkingMode
from cognitive_integration import CognitiveIntegration, IntegrationMode, CognitiveRequest, CognitiveResponse
from enhanced_symbol_loader import EnhancedSymbolLoader, LoadingStrategy

def create_test_registry():
    """Crea registry di test per ciclo completo."""
    test_registry = {
        "registry_version": "2.0",
        "created": "2025-05-26",
        "description": "NEUROGLYPH Test Registry - Ciclo Cognitivo Completo",
        "stats": {
            "total_submissions": 20,
            "approved": 20,
            "rejected": 0,
            "pending": 0
        },
        "approved_symbols": [
            # Simboli logici
            {
                "id": "NG0001", "symbol": "≺", "code": "ng:logic:implies", "fallback": "[IMP]",
                "category": "logic", "name": "implies", "description": "Logic operation: implies",
                "unicode_point": "U+227A", "approved_date": "2025-05-26", "validation_score": 95.0,
                "status": "validated", "token_cost": 1, "score": 95.0, "token_density": 0.96,
                "tier": "base", "valid": True, "cognitive_domain": True
            },
            {
                "id": "NG0002", "symbol": "⌀", "code": "ng:logic:or", "fallback": "[OR]",
                "category": "logic", "name": "or", "description": "Logic operation: or",
                "unicode_point": "U+2300", "approved_date": "2025-05-26", "validation_score": 98.0,
                "status": "validated", "token_cost": 1, "score": 98.0, "token_density": 0.94,
                "tier": "base", "valid": True, "cognitive_domain": True
            },
            # Simboli memoria
            {
                "id": "NG0003", "symbol": "■", "code": "ng:memory:pointer", "fallback": "[PTR]",
                "category": "memory", "name": "pointer", "description": "Memory operation: pointer",
                "unicode_point": "U+25A0", "approved_date": "2025-05-26", "validation_score": 95.0,
                "status": "validated", "token_cost": 1, "score": 95.0, "token_density": 1.0,
                "tier": "base", "valid": True, "cognitive_domain": True
            },
            {
                "id": "NG0004", "symbol": "⫗", "code": "ng:memory:alloc", "fallback": "[ALLOC]",
                "category": "memory", "name": "alloc", "description": "Memory operation: alloc",
                "unicode_point": "U+2AD7", "approved_date": "2025-05-26", "validation_score": 98.0,
                "status": "validated", "token_cost": 1, "score": 98.0, "token_density": 0.97,
                "tier": "base", "valid": True, "cognitive_domain": True
            },
            # Simboli struttura
            {
                "id": "NG0005", "symbol": "⊬", "code": "ng:structure:function", "fallback": "[FUNC]",
                "category": "structure", "name": "function", "description": "Structure operation: function",
                "unicode_point": "U+22AC", "approved_date": "2025-05-26", "validation_score": 95.0,
                "status": "validated", "token_cost": 1, "score": 95.0, "token_density": 0.92,
                "tier": "base", "valid": True, "cognitive_domain": True
            },
            {
                "id": "NG0006", "symbol": "⍍", "code": "ng:structure:class", "fallback": "[CLASS]",
                "category": "structure", "name": "class", "description": "Structure operation: class",
                "unicode_point": "U+234D", "approved_date": "2025-05-26", "validation_score": 96.0,
                "status": "validated", "token_cost": 1, "score": 96.0, "token_density": 0.93,
                "tier": "base", "valid": True, "cognitive_domain": True
            },
            # Simboli operatori
            {
                "id": "NG0007", "symbol": "◯", "code": "ng:operator:sub", "fallback": "[SUB]",
                "category": "operator", "name": "sub", "description": "Operator operation: sub",
                "unicode_point": "U+25EF", "approved_date": "2025-05-26", "validation_score": 95.0,
                "status": "validated", "token_cost": 1, "score": 95.0, "token_density": 0.92,
                "tier": "base", "valid": True, "cognitive_domain": True
            },
            {
                "id": "NG0008", "symbol": "≯", "code": "ng:operator:mul", "fallback": "[MUL]",
                "category": "operator", "name": "mul", "description": "Operator operation: mul",
                "unicode_point": "U+226F", "approved_date": "2025-05-26", "validation_score": 95.0,
                "status": "validated", "token_cost": 1, "score": 95.0, "token_density": 0.98,
                "tier": "base", "valid": True, "cognitive_domain": True
            },
            # Simboli flusso
            {
                "id": "NG0009", "symbol": "⤀", "code": "ng:flow:loop", "fallback": "[LOOP]",
                "category": "flow", "name": "loop", "description": "Flow operation: loop",
                "unicode_point": "U+2900", "approved_date": "2025-05-26", "validation_score": 98.0,
                "status": "validated", "token_cost": 1, "score": 98.0, "token_density": 0.92,
                "tier": "base", "valid": True, "cognitive_domain": True
            },
            {
                "id": "NG0010", "symbol": "⟲", "code": "ng:flow:repeat", "fallback": "[REPEAT]",
                "category": "flow", "name": "repeat", "description": "Flow operation: repeat",
                "unicode_point": "U+27F2", "approved_date": "2025-05-26", "validation_score": 94.0,
                "status": "validated", "token_cost": 1, "score": 94.0, "token_density": 0.91,
                "tier": "base", "valid": True, "cognitive_domain": True
            },
            # Simboli ragionamento
            {
                "id": "NG0011", "symbol": "⟨", "code": "ng:reasoning:problem", "fallback": "[PROB]",
                "category": "reasoning", "name": "problem", "description": "Reasoning operation: problem",
                "unicode_point": "U+27E8", "approved_date": "2025-05-26", "validation_score": 97.0,
                "status": "validated", "token_cost": 1, "score": 97.0, "token_density": 0.95,
                "tier": "base", "valid": True, "cognitive_domain": True
            },
            {
                "id": "NG0012", "symbol": "⟩", "code": "ng:reasoning:solution", "fallback": "[SOL]",
                "category": "reasoning", "name": "solution", "description": "Reasoning operation: solution",
                "unicode_point": "U+27E9", "approved_date": "2025-05-26", "validation_score": 97.0,
                "status": "validated", "token_cost": 1, "score": 97.0, "token_density": 0.95,
                "tier": "base", "valid": True, "cognitive_domain": True
            },
            # Simboli creativi
            {
                "id": "NG0013", "symbol": "◊", "code": "ng:creative:generate", "fallback": "[GEN]",
                "category": "creative", "name": "generate", "description": "Creative operation: generate",
                "unicode_point": "U+25CA", "approved_date": "2025-05-26", "validation_score": 96.0,
                "status": "validated", "token_cost": 1, "score": 96.0, "token_density": 0.94,
                "tier": "base", "valid": True, "cognitive_domain": True
            },
            {
                "id": "NG0014", "symbol": "⊕", "code": "ng:creative:combine", "fallback": "[COMB]",
                "category": "creative", "name": "combine", "description": "Creative operation: combine",
                "unicode_point": "U+2295", "approved_date": "2025-05-26", "validation_score": 95.0,
                "status": "validated", "token_cost": 1, "score": 95.0, "token_density": 0.93,
                "tier": "base", "valid": True, "cognitive_domain": True
            },
            # Simboli meta
            {
                "id": "NG0015", "symbol": "⟡", "code": "ng:meta:reflect", "fallback": "[REFL]",
                "category": "meta", "name": "reflect", "description": "Meta operation: reflect",
                "unicode_point": "U+27E1", "approved_date": "2025-05-26", "validation_score": 98.0,
                "status": "validated", "token_cost": 1, "score": 98.0, "token_density": 0.96,
                "tier": "base", "valid": True, "cognitive_domain": True
            }
        ]
    }
    
    # Salva registry di test
    with open('test_complete_registry.json', 'w', encoding='utf-8') as f:
        json.dump(test_registry, f, indent=2, ensure_ascii=False)
    
    return 'test_complete_registry.json'

def test_enhanced_symbol_loader():
    """Test Enhanced Symbol Loader."""
    print("🧠 Test 1: Enhanced Symbol Loader")
    print("-" * 50)
    
    # Crea registry di test
    registry_path = create_test_registry()
    
    # Test diverse strategie
    strategies = [LoadingStrategy.COGNITIVE, LoadingStrategy.ADAPTIVE, LoadingStrategy.EAGER]
    
    for strategy in strategies:
        print(f"\n📊 Test strategia: {strategy.value}")
        
        loader = EnhancedSymbolLoader(registry_path, strategy)
        metrics = loader.load_symbols()
        
        print(f"  ✅ Simboli caricati: {metrics.loaded_symbols}/{metrics.total_symbols}")
        print(f"  ✅ Copertura cognitiva: {metrics.cognitive_coverage:.1%}")
        print(f"  ✅ Tempo caricamento: {metrics.loading_time:.3f}s")
        
        # Test recupero simboli
        test_symbol = "≺"
        symbol_data = loader.get_symbol(test_symbol)
        print(f"  ✅ Recupero simbolo {test_symbol}: {'OK' if symbol_data else 'FAIL'}")
        
        # Test domini
        logic_symbols = loader.get_symbols_by_domain("logic")
        print(f"  ✅ Simboli logici: {len(logic_symbols)}")
    
    return True

def test_cognitive_cycle_complete():
    """Test ciclo cognitivo completo."""
    print("\n🧠 Test 2: Ciclo Cognitivo Completo")
    print("-" * 50)
    
    # Crea sistema integrato
    registry_path = 'test_complete_registry.json'
    integration = CognitiveIntegration(IntegrationMode.ULTRA, registry_path)
    
    # Test casi complessi
    test_cases = [
        {
            "name": "Algoritmo Fibonacci",
            "input": "Crea una funzione ricorsiva per calcolare fibonacci con gestione memoria ottimizzata",
            "mode": ThinkingMode.PROBLEM_SOLVING,
            "response": CognitiveResponse.CODE
        },
        {
            "name": "Struttura Dati Creativa",
            "input": "Inventa una nuova struttura dati per AI simbolica con operazioni logiche avanzate",
            "mode": ThinkingMode.CREATIVE,
            "response": CognitiveResponse.MIXED
        },
        {
            "name": "Analisi Algoritmica",
            "input": "Analizza la complessità computazionale di un algoritmo di ordinamento",
            "mode": ThinkingMode.ANALYTICAL,
            "response": CognitiveResponse.NATURAL
        },
        {
            "name": "Auto-riflessione",
            "input": "Rifletti sui tuoi processi di pensiero e identifica pattern di miglioramento",
            "mode": ThinkingMode.REFLECTIVE,
            "response": CognitiveResponse.NATURAL
        }
    ]
    
    results = []
    
    for i, test_case in enumerate(test_cases):
        print(f"\n🧪 Test Case {i+1}: {test_case['name']}")
        print(f"📝 Input: {test_case['input'][:60]}...")
        print(f"🎯 Modalità: {test_case['mode'].value}")
        
        start_time = time.time()
        
        # Crea richiesta cognitiva
        request = CognitiveRequest(
            input_text=test_case['input'],
            thinking_mode=test_case['mode'],
            response_type=test_case['response'],
            require_validation=True
        )
        
        # Processa richiesta
        result = integration.process_cognitive_request(request)
        
        processing_time = time.time() - start_time
        
        # Analizza risultati
        print(f"  ✅ Successo: {result.success}")
        print(f"  ✅ Confidenza: {result.confidence:.2f}")
        print(f"  ✅ Validazione: {result.validation_passed}")
        print(f"  ✅ Domini attivi: {len(result.active_domains)}")
        print(f"  ✅ Simboli input: {len(result.input_symbols)}")
        print(f"  ✅ Simboli output: {len(result.output_symbols)}")
        print(f"  ✅ Tempo: {processing_time:.3f}s")
        print(f"  📊 Output: {result.output_text[:100]}...")
        
        results.append({
            'test_case': test_case['name'],
            'success': result.success,
            'confidence': result.confidence,
            'validation': result.validation_passed,
            'domains': len(result.active_domains),
            'processing_time': processing_time,
            'output_length': len(result.output_text)
        })
    
    return results

def test_cognitive_performance():
    """Test performance sistema cognitivo."""
    print("\n🧠 Test 3: Performance Cognitiva")
    print("-" * 50)
    
    registry_path = 'test_complete_registry.json'
    integration = CognitiveIntegration(IntegrationMode.GOD_MODE, registry_path)
    
    # Test stress con richieste multiple
    stress_inputs = [
        "Crea algoritmo di sorting",
        "Implementa struttura dati",
        "Ottimizza performance",
        "Gestisci memoria dinamica",
        "Valida input utente"
    ]
    
    start_time = time.time()
    results = []
    
    for i, input_text in enumerate(stress_inputs):
        result = integration.think(input_text, ThinkingMode.ANALYTICAL)
        results.append(len(result))
        print(f"  ✅ Richiesta {i+1}: {len(result)} caratteri output")
    
    total_time = time.time() - start_time
    
    # Analizza performance
    status = integration.get_cognitive_status()
    metrics = status['performance_metrics']
    
    print(f"\n📈 Metriche Performance:")
    print(f"  • Tempo totale: {total_time:.2f}s")
    print(f"  • Richieste/sec: {len(stress_inputs)/total_time:.1f}")
    print(f"  • Richieste totali: {metrics['total_requests']}")
    print(f"  • Tasso successo: {metrics['successful_requests'] / max(1, metrics['total_requests']):.2f}")
    print(f"  • Confidenza media: {metrics['average_confidence']:.2f}")
    print(f"  • Tempo medio: {metrics['average_processing_time']:.3f}s")
    
    return metrics

def main():
    """Test principale ciclo cognitivo completo."""
    print("🧠 NEUROGLYPH CICLO COGNITIVO COMPLETO - TEST")
    print("=" * 70)
    print(f"📅 Data: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print()
    
    try:
        # Esegui tutti i test
        tests = [
            ("Enhanced Symbol Loader", test_enhanced_symbol_loader),
            ("Ciclo Cognitivo Completo", test_cognitive_cycle_complete),
            ("Performance Cognitiva", test_cognitive_performance)
        ]
        
        all_results = {}
        
        for test_name, test_func in tests:
            print(f"\n🚀 Eseguendo: {test_name}")
            try:
                start_time = time.time()
                result = test_func()
                duration = time.time() - start_time
                
                all_results[test_name] = {
                    'success': True,
                    'result': result,
                    'duration': duration
                }
                
                print(f"✅ {test_name} completato in {duration:.2f}s")
                
            except Exception as e:
                print(f"❌ {test_name} fallito: {e}")
                all_results[test_name] = {
                    'success': False,
                    'error': str(e)
                }
        
        # Riepilogo finale
        print("\n" + "=" * 70)
        print("📊 RIEPILOGO CICLO COGNITIVO COMPLETO")
        print("=" * 70)
        
        successful_tests = sum(1 for r in all_results.values() if r.get('success', False))
        total_tests = len(tests)
        
        print(f"✅ Test superati: {successful_tests}/{total_tests}")
        print(f"📈 Tasso successo: {successful_tests/total_tests*100:.1f}%")
        
        for test_name, result in all_results.items():
            status = "✅ PASS" if result.get('success', False) else "❌ FAIL"
            duration = result.get('duration', 0)
            print(f"  {status} {test_name} ({duration:.2f}s)")
        
        if successful_tests == total_tests:
            print("\n🎉 CICLO COGNITIVO COMPLETO FUNZIONA PERFETTAMENTE!")
            print("🧠 NEUROGLYPH LLM: INPUT → SIMBOLI → PENSIERO → OUTPUT → CODICE ✅")
        else:
            print(f"\n⚠️ {total_tests - successful_tests} test falliti.")
        
        return successful_tests == total_tests
        
    except Exception as e:
        print(f"\n❌ Errore critico: {e}")
        return False
    
    finally:
        # Cleanup
        for file in ['test_complete_registry.json']:
            if os.path.exists(file):
                os.remove(file)

if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)
