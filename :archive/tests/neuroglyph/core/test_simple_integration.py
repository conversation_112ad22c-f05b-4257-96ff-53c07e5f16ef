#!/usr/bin/env python3
"""
NEUROGLYPH - TEST INTEGRAZIONE SEMPLICE
=======================================

Test semplificato per verificare che l'integrazione LLM funzioni
senza blocchi o problemi di import.

Autore: NEUROGLYPH ULTRA Team
Data: 2025-05-26
"""

import sys
import os
import time
from datetime import datetime

# Aggiungi path
current_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.append(current_dir)

def test_basic_imports():
    """Test import base senza LLM."""
    print("🧪 Test 1: Import base...")

    try:
        from cognitive_state import create_cognitive_state
        print("  ✅ cognitive_state")

        from thinking_engine import ThinkingEngine
        print("  ✅ thinking_engine")

        from symbol_mutator import SymbolMutator
        print("  ✅ symbol_mutator")

        return True

    except Exception as e:
        print(f"  ❌ Errore import: {e}")
        return False

def test_cognitive_basic():
    """Test funzionalità cognitive base."""
    print("\n🧪 Test 2: Cognitive State...")

    try:
        from cognitive_state import create_cognitive_state, CognitiveIntensity

        # Crea stato cognitivo
        cognitive_state = create_cognitive_state("neuroglyph/core/locked_registry_godmode_v9.json")

        # Test attivazione domini (metodo corretto)
        cognitive_state.activate_domains_by_symbols(["⊃", "∧"])

        print(f"  ✅ Domini attivi: {len(cognitive_state.active_domains)}")
        print(f"  ✅ Intensità: {cognitive_state.current_intensity}")

        return True

    except Exception as e:
        print(f"  ❌ Errore cognitive: {e}")
        return False

def test_thinking_engine():
    """Test thinking engine."""
    print("\n🧪 Test 3: Thinking Engine...")

    try:
        from cognitive_state import create_cognitive_state
        from thinking_engine import ThinkingEngine, ThinkingMode

        # Crea componenti
        cognitive_state = create_cognitive_state("neuroglyph/core/locked_registry_godmode_v9.json")
        thinking_engine = ThinkingEngine(cognitive_state, "neuroglyph/core/locked_registry_godmode_v9.json")

        # Test pensiero (API corretta)
        input_symbols = ["⊃", "∧", "→"]
        thought_process = thinking_engine.think(
            input_symbols,
            ThinkingMode.ANALYTICAL,
            0.7
        )

        print(f"  ✅ Pensiero generato: {thought_process.success}")
        print(f"  ✅ Confidenza: {thought_process.final_confidence:.2f}")
        print(f"  ✅ Output simboli: {len(thought_process.output_symbols)}")

        return thought_process.success

    except Exception as e:
        print(f"  ❌ Errore thinking: {e}")
        return False

def test_symbol_mutation():
    """Test mutazione simbolica."""
    print("\n🧪 Test 4: Symbol Mutation...")

    try:
        from symbol_mutator import create_symbol_mutator, MutationStrategy

        # Crea mutatore
        mutator = create_symbol_mutator(
            registry_path="neuroglyph/core/locked_registry_godmode_v9.json",
            strategy=MutationStrategy.CONSERVATIVE
        )

        print(f"  ✅ Mutator creato: {len(mutator.symbol_loader.symbol_profiles)} simboli")
        print(f"  ✅ Cluster semantici: {len(mutator.semantic_clusters)}")
        print(f"  ✅ Pool Unicode: {len(mutator.unicode_pool)}")

        # Test mutazione
        result = mutator.generate_mutation()

        print(f"  ✅ Mutazione: {result.success}")
        if result.success and result.candidate:
            print(f"  ✅ Nuovo simbolo: {result.candidate.mutated_symbol}")
            print(f"  ✅ Confidenza: {result.candidate.confidence_score:.2f}")

        return True

    except Exception as e:
        print(f"  ❌ Errore mutation: {e}")
        return False

def test_llm_connector_mock():
    """Test LLM Connector con Mock (senza Qwen)."""
    print("\n🧪 Test 5: LLM Connector (Mock)...")

    try:
        from llm_connector import create_llm_connector, LLMBackend

        # Crea connector con Mock
        connector = create_llm_connector(LLMBackend.MOCK)

        print(f"  ✅ Connector creato: {connector.backend.value}")

        # Test NL → simboli
        test_input = "Se A è maggiore di B, cosa possiamo dire?"
        symbols = connector.natural_language_to_symbols(test_input)

        print(f"  ✅ NL→Simboli: '{test_input[:30]}...' → {symbols}")

        # Test simboli → NL
        test_symbols = ["⊃", "∧", "→"]
        output = connector.symbols_to_natural_language(test_symbols, test_input, "logic")

        print(f"  ✅ Simboli→NL: {test_symbols} → '{output[:50]}...'")

        return True

    except Exception as e:
        print(f"  ❌ Errore LLM connector: {e}")
        return False

def test_cognitive_integration():
    """Test integrazione cognitiva completa."""
    print("\n🧪 Test 6: Cognitive Integration...")

    try:
        from cognitive_integration import create_cognitive_integration, IntegrationMode, CognitiveRequest, ThinkingMode

        # Crea integrazione
        integration = create_cognitive_integration(IntegrationMode.ENHANCED)

        print(f"  ✅ Integration creata: {integration.mode.value}")
        print(f"  ✅ LLM Connector: {integration.llm_connector.backend.value}")

        # Test richiesta cognitiva
        request = CognitiveRequest(
            input_text="Analizza la logica: se A implica B e B implica C, cosa possiamo dire di A e C?",
            thinking_mode=ThinkingMode.ANALYTICAL,
            require_validation=True
        )

        result = integration.process_cognitive_request(request)

        print(f"  ✅ Processo completato: {result.success}")
        print(f"  ✅ Confidenza: {result.confidence:.2f}")
        print(f"  ✅ Domini attivi: {len(result.active_domains)}")
        print(f"  ✅ Output simboli: {len(result.output_symbols)}")

        if result.output_text:
            print(f"  ✅ Output text: '{result.output_text[:50]}...'")

        return result.success

    except Exception as e:
        print(f"  ❌ Errore integration: {e}")
        return False

def main():
    """Esegue tutti i test."""
    print("🧪 NEUROGLYPH - Test Integrazione Semplice")
    print("=" * 60)

    start_time = time.time()

    # Esegui test
    tests = [
        test_basic_imports,
        test_cognitive_basic,
        test_thinking_engine,
        test_symbol_mutation,
        test_llm_connector_mock,
        test_cognitive_integration
    ]

    results = []
    for test in tests:
        try:
            result = test()
            results.append(result)
        except Exception as e:
            print(f"  💥 Test fallito: {e}")
            results.append(False)

    # Risultati finali
    duration = time.time() - start_time
    successful = sum(results)
    total = len(results)

    print(f"\n🏁 Test completati in {duration:.1f}s")
    print(f"📊 Risultati: {successful}/{total} test superati")

    if successful == total:
        print("🎉 TUTTI I TEST SUPERATI - Sistema funzionante!")
        verdict = "✅ SUCCESSO"
    elif successful >= total * 0.8:
        print("✅ MAGGIOR PARTE TEST SUPERATI - Sistema quasi funzionante")
        verdict = "🟡 PARZIALE"
    else:
        print("❌ MOLTI TEST FALLITI - Sistema ha problemi")
        verdict = "❌ FALLIMENTO"

    print(f"🎯 Verdetto finale: {verdict}")

    return successful == total


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
