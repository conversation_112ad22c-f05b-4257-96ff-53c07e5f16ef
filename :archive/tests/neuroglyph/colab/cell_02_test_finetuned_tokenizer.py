# 🔤 NEUROGLYPH COLAB - CELLA 2: TEST TOKENIZER FINE-TUNED
# ========================================================
# Test approfondito del tokenizer del modello fine-tuned

from transformers import AutoTokenizer
from rich.console import Console
from rich.table import Table
from rich.panel import Panel
from rich.progress import track
import json
from pathlib import Path

console = Console()
console.print(Panel.fit("🔤 TEST TOKENIZER FINE-TUNED", style="bold blue"))

# Verifica variabili dalla cella precedente
required_vars = ['MODEL_PATH', 'critical_symbols', 'base_tokenizer']
missing_vars = [var for var in required_vars if var not in globals()]

if missing_vars:
    console.print(f"❌ [bold red]Variabili mancanti: {missing_vars}[/bold red]")
    console.print("🔄 Esegui prima la CELLA 1: Setup e Diagnostica")
    raise RuntimeError("Variabili mancanti dalla cella precedente")

# 1. Carica Tokenizer Fine-tuned
console.print("\n🔤 [bold]Caricamento Tokenizer Fine-tuned[/bold]")

try:
    # Prova a caricare tokenizer dal modello fine-tuned
    finetuned_tokenizer = AutoTokenizer.from_pretrained(MODEL_PATH)
    console.print(f"✅ Tokenizer fine-tuned caricato da: {MODEL_PATH}")
    console.print(f"📊 Vocab size: {len(finetuned_tokenizer.vocab)}")
    
    # Confronta con tokenizer base
    base_vocab_size = len(base_tokenizer.vocab)
    vocab_diff = len(finetuned_tokenizer.vocab) - base_vocab_size
    
    console.print(f"📈 Differenza vocab: {vocab_diff} tokens")
    
    if vocab_diff > 0:
        console.print(f"✅ [green]Tokenizer espanso: +{vocab_diff} tokens[/green]")
    elif vocab_diff == 0:
        console.print(f"⚠️ [yellow]Stesso vocab size del base model[/yellow]")
    else:
        console.print(f"❌ [red]Vocab size ridotto: {vocab_diff}[/red]")
    
    tokenizer_loaded = True
    
except Exception as e:
    console.print(f"❌ Errore caricamento tokenizer fine-tuned: {e}")
    console.print("🔄 Usando tokenizer base per confronto")
    finetuned_tokenizer = base_tokenizer
    tokenizer_loaded = False

# 2. Test Comparativo Tokenizzazione
console.print("\n🔍 [bold]Test Comparativo Tokenizzazione[/bold]")

# Seleziona simboli per test
test_symbols = critical_symbols[:20] if len(critical_symbols) >= 20 else critical_symbols
if not test_symbols:
    test_symbols = ["⚡", "🔄", "📊", "🧠", "∑", "∫", "∂", "∧", "∨", "→", 
                   "ng:operator:add", "ng:logic:implies", "ng:memory:alloc"]

# Crea tabella comparativa
comparison_table = Table(title="🔍 Confronto Tokenizzazione: Base vs Fine-tuned")
comparison_table.add_column("Simbolo", style="cyan")
comparison_table.add_column("Base Tokens", style="yellow")
comparison_table.add_column("Fine-tuned Tokens", style="green")
comparison_table.add_column("Miglioramento", style="magenta")
comparison_table.add_column("Status", style="bold")

improvements = 0
degradations = 0
unchanged = 0

console.print("🔄 Analizzando tokenizzazione simboli...")

for symbol in track(test_symbols, description="Testing symbols..."):
    try:
        # Tokenizzazione base
        base_tokens = base_tokenizer.encode(symbol, add_special_tokens=False)
        base_count = len(base_tokens)
        
        # Tokenizzazione fine-tuned
        ft_tokens = finetuned_tokenizer.encode(symbol, add_special_tokens=False)
        ft_count = len(ft_tokens)
        
        # Calcola miglioramento
        if ft_count < base_count:
            improvements += 1
            improvement = f"✅ -{base_count - ft_count}"
            status = "MIGLIORATO"
        elif ft_count > base_count:
            degradations += 1
            improvement = f"❌ +{ft_count - base_count}"
            status = "PEGGIORATO"
        else:
            unchanged += 1
            improvement = "➖ 0"
            status = "INVARIATO"
        
        # Formatta tokens per display
        base_display = str(base_tokens) if base_count <= 3 else f"[{base_count} tokens]"
        ft_display = str(ft_tokens) if ft_count <= 3 else f"[{ft_count} tokens]"
        
        comparison_table.add_row(
            symbol,
            base_display,
            ft_display,
            improvement,
            status
        )
        
    except Exception as e:
        comparison_table.add_row(
            symbol,
            "ERROR",
            "ERROR", 
            "❌",
            f"ERROR: {str(e)[:20]}"
        )

console.print(comparison_table)

# 3. Statistiche Comparative
console.print("\n📊 [bold]Statistiche Comparative[/bold]")

total_tested = len(test_symbols)
improvement_rate = improvements / total_tested if total_tested > 0 else 0
degradation_rate = degradations / total_tested if total_tested > 0 else 0

stats_table = Table(title="📈 Statistiche Tokenizzazione")
stats_table.add_column("Metrica", style="cyan")
stats_table.add_column("Valore", style="yellow")
stats_table.add_column("Percentuale", style="green")

stats_table.add_row("Simboli Migliorati", str(improvements), f"{improvement_rate:.1%}")
stats_table.add_row("Simboli Peggiorati", str(degradations), f"{degradation_rate:.1%}")
stats_table.add_row("Simboli Invariati", str(unchanged), f"{unchanged/total_tested:.1%}")
stats_table.add_row("Totale Testati", str(total_tested), "100%")

console.print(stats_table)

# 4. Test Fedeltà Simbolica
console.print("\n🎯 [bold]Test Fedeltà Simbolica[/bold]")

fidelity_issues = []
perfect_symbols = []

for symbol in test_symbols[:10]:  # Test primi 10 per velocità
    try:
        # Test con tokenizer fine-tuned
        tokens = finetuned_tokenizer.encode(symbol, add_special_tokens=False)
        decoded = finetuned_tokenizer.decode(tokens, skip_special_tokens=True)
        
        if decoded.strip() == symbol:
            perfect_symbols.append(symbol)
        else:
            fidelity_issues.append({
                "symbol": symbol,
                "expected": symbol,
                "actual": decoded.strip(),
                "tokens": tokens
            })
    except Exception as e:
        fidelity_issues.append({
            "symbol": symbol,
            "error": str(e)
        })

fidelity_rate = len(perfect_symbols) / len(test_symbols[:10])

console.print(f"✅ Simboli perfetti: {len(perfect_symbols)}/10 ({fidelity_rate:.1%})")
console.print(f"❌ Problemi fedeltà: {len(fidelity_issues)}")

if fidelity_issues:
    console.print("\n🔍 [bold]Problemi Fedeltà Identificati:[/bold]")
    for issue in fidelity_issues[:5]:  # Mostra primi 5
        if 'error' in issue:
            console.print(f"❌ {issue['symbol']}: ERROR - {issue['error']}")
        else:
            console.print(f"❌ {issue['symbol']}: '{issue['expected']}' → '{issue['actual']}'")

# 5. Test Frasi Simboliche
console.print("\n📝 [bold]Test Frasi Simboliche[/bold]")

test_sentences = [
    "Using ⚡🔄📊 implement quicksort algorithm",
    "Apply ∑∫∂ for numerical integration", 
    "Logic with ∧∨→ operators",
    "Function ng:operator:add with ng:memory:alloc"
]

sentence_table = Table(title="📝 Test Preservazione Simboli in Frasi")
sentence_table.add_column("Frase", style="cyan")
sentence_table.add_column("Tokens", style="yellow")
sentence_table.add_column("Preservazione", style="green")

for sentence in test_sentences:
    try:
        tokens = finetuned_tokenizer.encode(sentence)
        decoded = finetuned_tokenizer.decode(tokens)
        
        # Conta simboli preservati
        symbols_in_sentence = [s for s in test_symbols if s in sentence]
        symbols_preserved = [s for s in symbols_in_sentence if s in decoded]
        preservation_rate = len(symbols_preserved) / len(symbols_in_sentence) if symbols_in_sentence else 1.0
        
        sentence_table.add_row(
            sentence[:40] + "..." if len(sentence) > 40 else sentence,
            str(len(tokens)),
            f"{preservation_rate:.1%} ({len(symbols_preserved)}/{len(symbols_in_sentence)})"
        )
        
    except Exception as e:
        sentence_table.add_row(
            sentence[:40] + "...",
            "ERROR",
            f"ERROR: {str(e)[:20]}"
        )

console.print(sentence_table)

# 6. Valutazione Finale Tokenizer
console.print("\n🎯 [bold]Valutazione Finale Tokenizer[/bold]")

# Calcola score complessivo
tokenizer_score = (
    improvement_rate * 0.4 +  # 40% peso miglioramenti
    fidelity_rate * 0.4 +     # 40% peso fedeltà
    (1 - degradation_rate) * 0.2  # 20% peso non-degradazioni
)

if tokenizer_score >= 0.8:
    grade = "A (Excellent)"
    color = "green"
    verdict = "✅ TOKENIZER OTTIMO"
elif tokenizer_score >= 0.6:
    grade = "B (Good)" 
    color = "yellow"
    verdict = "✅ TOKENIZER BUONO"
elif tokenizer_score >= 0.4:
    grade = "C (Fair)"
    color = "yellow"
    verdict = "⚠️ TOKENIZER ACCETTABILE"
else:
    grade = "D (Poor)"
    color = "red"
    verdict = "❌ TOKENIZER PROBLEMATICO"

console.print(f"\n📊 [bold]Score Tokenizer: {tokenizer_score:.3f}[/bold]")
console.print(f"📝 [bold]Grade: {grade}[/bold]")
console.print(f"🎯 [bold {color}]{verdict}[/bold {color}]")

# 7. Raccomandazioni
console.print("\n🔧 [bold]Raccomandazioni[/bold]")

recommendations = []

if improvement_rate < 0.3:
    recommendations.append("🔄 Considerare re-training con tokenizer simbolico")

if fidelity_rate < 0.8:
    recommendations.append("🔍 Verificare encoding/decoding simboli")

if degradation_rate > 0.2:
    recommendations.append("⚠️ Investigare cause degradazione tokenizzazione")

if tokenizer_score < 0.6:
    recommendations.append("🚨 CRITICO: Ricreare tokenizer con simboli come special tokens")

if not recommendations:
    recommendations.append("✅ Tokenizer funziona bene, procedere con test generazione")

for i, rec in enumerate(recommendations, 1):
    console.print(f"{i}. {rec}")

# Salva risultati per cella successiva
globals()['finetuned_tokenizer'] = finetuned_tokenizer
globals()['tokenizer_score'] = tokenizer_score
globals()['fidelity_rate'] = fidelity_rate
globals()['improvement_rate'] = improvement_rate

console.print(Panel.fit(
    f"🔤 TOKENIZER TEST COMPLETATO\n"
    f"📊 Score: {tokenizer_score:.3f}\n"
    f"🎯 Grade: {grade}\n"
    f"✅ Fedeltà: {fidelity_rate:.1%}\n"
    f"📈 Miglioramenti: {improvement_rate:.1%}",
    style=f"bold {color}"
))

print("🚀 Pronto per CELLA 3: Test Generazione Simbolica")
