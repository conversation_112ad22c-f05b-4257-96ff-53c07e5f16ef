# 🚀 NEUROGLYPH COLAB - CELLA 3: TEST GENERAZIONE SIMBOLICA
# =========================================================
# Test delle capacità di generazione simbolica del modello fine-tuned

from transformers import AutoModelForCausalLM
from rich.console import Console
from rich.table import Table
from rich.panel import Panel
from rich.progress import track
import torch
import time
import re

console = Console()
console.print(Panel.fit("🚀 TEST GENERAZIONE SIMBOLICA", style="bold blue"))

# Verifica variabili dalle celle precedenti
required_vars = ['MODEL_PATH', 'finetuned_tokenizer', 'critical_symbols', 'tokenizer_score']
missing_vars = [var for var in required_vars if var not in globals()]

if missing_vars:
    console.print(f"❌ [bold red]Variabili mancanti: {missing_vars}[/bold red]")
    console.print("🔄 Esegui prima le CELLE 1 e 2")
    raise RuntimeError("Variabili mancanti dalle celle precedenti")

# 1. Caricamento Modello Fine-tuned
console.print("\n🧠 [bold]Caricamento Modello Fine-tuned[/bold]")

try:
    # Carica modello con ottimizzazioni per Colab
    model = AutoModelForCausalLM.from_pretrained(
        MODEL_PATH,
        torch_dtype=torch.float16,
        device_map="auto",
        trust_remote_code=True,
        low_cpu_mem_usage=True
    )
    
    console.print(f"✅ Modello caricato da: {MODEL_PATH}")
    console.print(f"🔧 Device: {model.device}")
    console.print(f"📊 Parametri: {model.num_parameters():,}")
    
    model_loaded = True
    
except Exception as e:
    console.print(f"❌ Errore caricamento modello: {e}")
    console.print("🔄 Impossibile procedere con test generazione")
    model_loaded = False

if not model_loaded:
    console.print("❌ [bold red]STOP: Modello non caricato[/bold red]")
    raise RuntimeError("Modello non disponibile")

# 2. Configurazione Generazione
console.print("\n⚙️ [bold]Configurazione Generazione[/bold]")

generation_config = {
    "max_length": 200,
    "max_new_tokens": 150,
    "temperature": 0.1,  # Bassa per consistenza
    "top_p": 0.9,
    "do_sample": True,
    "pad_token_id": finetuned_tokenizer.eos_token_id,
    "eos_token_id": finetuned_tokenizer.eos_token_id,
    "repetition_penalty": 1.1
}

console.print("✅ Configurazione generazione ottimizzata per simboli")

# 3. Test Prompts Simbolici
console.print("\n🧪 [bold]Test Prompts Simbolici[/bold]")

# Prompts di test con difficoltà crescente
test_prompts = [
    {
        "id": "BASIC_01",
        "prompt": "Using symbols ⚡🔄📊, implement quicksort algorithm:",
        "expected_symbols": ["⚡", "🔄", "📊"],
        "difficulty": "basic",
        "domain": "algorithms"
    },
    {
        "id": "MATH_01", 
        "prompt": "Using symbols ∑∫∂, solve numerical integration:",
        "expected_symbols": ["∑", "∫", "∂"],
        "difficulty": "intermediate",
        "domain": "mathematics"
    },
    {
        "id": "LOGIC_01",
        "prompt": "Using symbols ∧∨→, implement propositional logic:",
        "expected_symbols": ["∧", "∨", "→"],
        "difficulty": "intermediate", 
        "domain": "logic"
    },
    {
        "id": "ADVANCED_01",
        "prompt": "Using symbols 🧠📊⚡🔄, create ML pipeline:",
        "expected_symbols": ["🧠", "📊", "⚡", "🔄"],
        "difficulty": "advanced",
        "domain": "machine_learning"
    },
    {
        "id": "NG_SYMBOLS_01",
        "prompt": "Implement function using ng:operator:add and ng:memory:alloc:",
        "expected_symbols": ["ng:operator:add", "ng:memory:alloc"],
        "difficulty": "expert",
        "domain": "neuroglyph"
    }
]

# Tabella risultati
results_table = Table(title="🧪 Risultati Generazione Simbolica")
results_table.add_column("Test ID", style="cyan")
results_table.add_column("Simboli Attesi", style="yellow")
results_table.add_column("Simboli Generati", style="green")
results_table.add_column("Retention", style="magenta")
results_table.add_column("Qualità", style="bold")

generation_results = []

console.print("🔄 Eseguendo test generazione...")

for test_case in track(test_prompts, description="Generating..."):
    try:
        # Tokenizza prompt
        inputs = finetuned_tokenizer(
            test_case["prompt"], 
            return_tensors="pt",
            padding=True,
            truncation=True
        ).to(model.device)
        
        # Genera risposta
        start_time = time.time()
        
        with torch.no_grad():
            outputs = model.generate(
                **inputs,
                **generation_config
            )
        
        generation_time = time.time() - start_time
        
        # Decodifica risposta
        full_response = finetuned_tokenizer.decode(outputs[0], skip_special_tokens=True)
        generated_text = full_response[len(test_case["prompt"]):].strip()
        
        # Analizza simboli nella risposta
        expected_symbols = test_case["expected_symbols"]
        found_symbols = []
        
        for symbol in expected_symbols:
            if symbol in generated_text:
                found_symbols.append(symbol)
        
        # Calcola retention rate
        retention_rate = len(found_symbols) / len(expected_symbols) if expected_symbols else 0
        
        # Valuta qualità risposta
        quality_score = evaluate_response_quality(generated_text, test_case)
        
        # Salva risultato
        result = {
            "test_id": test_case["id"],
            "prompt": test_case["prompt"],
            "generated_text": generated_text,
            "expected_symbols": expected_symbols,
            "found_symbols": found_symbols,
            "retention_rate": retention_rate,
            "quality_score": quality_score,
            "generation_time": generation_time,
            "difficulty": test_case["difficulty"],
            "domain": test_case["domain"]
        }
        
        generation_results.append(result)
        
        # Aggiungi alla tabella
        quality_label = get_quality_label(quality_score)
        
        results_table.add_row(
            test_case["id"],
            str(len(expected_symbols)),
            str(len(found_symbols)),
            f"{retention_rate:.1%}",
            quality_label
        )
        
    except Exception as e:
        console.print(f"❌ Errore test {test_case['id']}: {e}")
        
        # Aggiungi risultato di errore
        results_table.add_row(
            test_case["id"],
            str(len(test_case["expected_symbols"])),
            "ERROR",
            "0%",
            "❌ FAIL"
        )

console.print(results_table)

# 4. Analisi Dettagliata Risultati
console.print("\n📊 [bold]Analisi Dettagliata Risultati[/bold]")

if generation_results:
    # Calcola statistiche aggregate
    avg_retention = sum(r["retention_rate"] for r in generation_results) / len(generation_results)
    avg_quality = sum(r["quality_score"] for r in generation_results) / len(generation_results)
    avg_time = sum(r["generation_time"] for r in generation_results) / len(generation_results)
    
    # Statistiche per difficoltà
    difficulty_stats = {}
    for result in generation_results:
        diff = result["difficulty"]
        if diff not in difficulty_stats:
            difficulty_stats[diff] = []
        difficulty_stats[diff].append(result["retention_rate"])
    
    # Mostra statistiche
    stats_table = Table(title="📈 Statistiche Aggregate")
    stats_table.add_column("Metrica", style="cyan")
    stats_table.add_column("Valore", style="yellow")
    stats_table.add_column("Valutazione", style="green")
    
    stats_table.add_row("Retention Media", f"{avg_retention:.1%}", get_retention_evaluation(avg_retention))
    stats_table.add_row("Qualità Media", f"{avg_quality:.2f}/1.0", get_quality_evaluation(avg_quality))
    stats_table.add_row("Tempo Medio", f"{avg_time:.2f}s", get_time_evaluation(avg_time))
    
    console.print(stats_table)
    
    # Mostra esempi migliori
    console.print("\n🏆 [bold]Migliori Esempi Generati[/bold]")
    
    best_results = sorted(generation_results, key=lambda x: x["retention_rate"] * x["quality_score"], reverse=True)
    
    for i, result in enumerate(best_results[:2], 1):
        console.print(f"\n🥇 [bold]Esempio {i} - {result['test_id']}[/bold]")
        console.print(f"📝 Prompt: {result['prompt']}")
        console.print(f"🔣 Simboli: {result['found_symbols']}")
        console.print(f"📊 Retention: {result['retention_rate']:.1%}")
        console.print(f"✨ Output: {result['generated_text'][:150]}...")

# 5. Test Reasoning Simbolico
console.print("\n🧠 [bold]Test Reasoning Simbolico[/bold]")

reasoning_prompts = [
    "If ⚡ means fast and 🔄 means loop, what does ⚡🔄 mean?",
    "Explain the relationship between ∑ (sum) and ∫ (integral):",
    "How do ∧ (AND) and ∨ (OR) combine in logic?"
]

reasoning_results = []

for prompt in reasoning_prompts:
    try:
        inputs = finetuned_tokenizer(prompt, return_tensors="pt").to(model.device)
        
        with torch.no_grad():
            outputs = model.generate(
                **inputs,
                max_new_tokens=100,
                temperature=0.1,
                do_sample=True
            )
        
        response = finetuned_tokenizer.decode(outputs[0], skip_special_tokens=True)
        answer = response[len(prompt):].strip()
        
        reasoning_results.append({
            "prompt": prompt,
            "answer": answer
        })
        
        console.print(f"❓ {prompt}")
        console.print(f"💭 {answer[:100]}...")
        console.print()
        
    except Exception as e:
        console.print(f"❌ Errore reasoning: {e}")

# 6. Valutazione Finale
console.print("\n🎯 [bold]Valutazione Finale Generazione[/bold]")

if generation_results:
    # Calcola score finale
    final_score = (avg_retention * 0.5) + (avg_quality * 0.3) + (min(avg_time/2, 1) * 0.2)
    
    if final_score >= 0.8:
        grade = "A (Excellent)"
        color = "green"
        verdict = "🏆 GENERAZIONE ECCELLENTE"
    elif final_score >= 0.6:
        grade = "B (Good)"
        color = "yellow" 
        verdict = "✅ GENERAZIONE BUONA"
    elif final_score >= 0.4:
        grade = "C (Fair)"
        color = "yellow"
        verdict = "⚠️ GENERAZIONE ACCETTABILE"
    else:
        grade = "D (Poor)"
        color = "red"
        verdict = "❌ GENERAZIONE PROBLEMATICA"
    
    console.print(f"📊 [bold]Score Finale: {final_score:.3f}[/bold]")
    console.print(f"📝 [bold]Grade: {grade}[/bold]")
    console.print(f"🎯 [bold {color}]{verdict}[/bold {color}]")

# Salva risultati per cella successiva
globals()['generation_results'] = generation_results
globals()['final_score'] = final_score if generation_results else 0
globals()['avg_retention'] = avg_retention if generation_results else 0

console.print(Panel.fit(
    f"🚀 GENERAZIONE TEST COMPLETATO\n"
    f"📊 Score: {final_score:.3f}\n"
    f"🎯 Grade: {grade}\n"
    f"🔣 Retention: {avg_retention:.1%}\n"
    f"⚡ Tempo medio: {avg_time:.2f}s",
    style=f"bold {color}"
))

# Funzioni helper
def evaluate_response_quality(text, test_case):
    """Valuta qualità della risposta generata."""
    score = 0.0
    
    # Presenza di codice
    if "def " in text or "class " in text or "function" in text:
        score += 0.3
    
    # Presenza di commenti/spiegazioni
    if "#" in text or "/*" in text or len(text.split()) > 10:
        score += 0.2
    
    # Lunghezza appropriata
    if 50 <= len(text) <= 500:
        score += 0.2
    
    # Coerenza con dominio
    domain_keywords = {
        "algorithms": ["sort", "algorithm", "complexity", "efficient"],
        "mathematics": ["equation", "formula", "calculate", "solve"],
        "logic": ["logic", "boolean", "condition", "if"],
        "machine_learning": ["model", "data", "train", "predict"],
        "neuroglyph": ["function", "memory", "operator", "ng:"]
    }
    
    domain = test_case.get("domain", "")
    if domain in domain_keywords:
        keywords_found = sum(1 for kw in domain_keywords[domain] if kw in text.lower())
        score += min(keywords_found * 0.1, 0.3)
    
    return min(score, 1.0)

def get_quality_label(score):
    """Ottieni label qualità."""
    if score >= 0.8:
        return "🏆 EXCELLENT"
    elif score >= 0.6:
        return "✅ GOOD"
    elif score >= 0.4:
        return "⚠️ FAIR"
    else:
        return "❌ POOR"

def get_retention_evaluation(rate):
    """Valuta retention rate."""
    if rate >= 0.8:
        return "🏆 Excellent"
    elif rate >= 0.6:
        return "✅ Good"
    elif rate >= 0.4:
        return "⚠️ Fair"
    else:
        return "❌ Poor"

def get_quality_evaluation(score):
    """Valuta quality score."""
    if score >= 0.8:
        return "🏆 Excellent"
    elif score >= 0.6:
        return "✅ Good"
    elif score >= 0.4:
        return "⚠️ Fair"
    else:
        return "❌ Poor"

def get_time_evaluation(time_sec):
    """Valuta tempo di generazione."""
    if time_sec <= 2:
        return "⚡ Fast"
    elif time_sec <= 5:
        return "✅ Good"
    elif time_sec <= 10:
        return "⚠️ Slow"
    else:
        return "❌ Too Slow"

print("🚀 Pronto per CELLA 4: Benchmark Finale")
