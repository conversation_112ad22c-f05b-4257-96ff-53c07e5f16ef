#!/usr/bin/env python3
"""
Test manuale del simbolo ∀ (forall) con framework NEUROGLYPH ULTRA
"""

import json
import re

def test_symbol_forall():
    """Test completo del simbolo ∀"""
    
    # Dati simbolo
    symbol = "∀"
    code = "ng:logic:forall"
    fallback = "[FORALL]"
    category = "logic"
    name = "forall"
    description = "Universal quantifier - for all elements"
    
    print("🧠 NEUROGLYPH ULTRA - Symbol Test: ∀ (forall)")
    print("=" * 60)
    print(f"Symbol: {symbol}")
    print(f"Code: {code}")
    print(f"Fallback: {fallback}")
    print(f"Category: {category}")
    print(f"Name: {name}")
    print(f"Description: {description}")
    print()
    
    # STEP 1: Validazione USU/CTU/LCL
    print("🔹 STEP 1: Validation (validate_symbol.py)")
    print("-" * 40)
    
    validation_score = 0
    max_score = 100
    
    # USU - Unicità Simbolica Universale
    print("USU - Unicità Simbolica Universale:")
    
    # Carica simboli esistenti
    try:
        with open("core/symbols.json", "r", encoding="utf-8") as f:
            existing_symbols = json.load(f)
        
        existing_symbol_list = [s.get("symbol", "") for s in existing_symbols]
        existing_codes = [s.get("code", "") for s in existing_symbols]
        existing_fallbacks = [s.get("fallback", "") for s in existing_symbols]
        
        # Test unicità
        unicode_unique = symbol not in existing_symbol_list
        code_unique = code not in existing_codes
        fallback_unique = fallback not in existing_fallbacks
        
        print(f"  Unicode unique: {'✅' if unicode_unique else '❌'}")
        print(f"  Code unique: {'✅' if code_unique else '❌'}")
        print(f"  Fallback unique: {'✅' if fallback_unique else '❌'}")
        
        if unicode_unique: validation_score += 20
        if code_unique: validation_score += 20
        if fallback_unique: validation_score += 5
        
    except Exception as e:
        print(f"  Error loading symbols: {e}")
        validation_score -= 10
    
    # CTU - Codifica Testuale Unificata
    print("\nCTU - Codifica Testuale Unificata:")
    
    format_valid = bool(re.match(r'^ng:[a-z_]+:[a-z_]+$', code))
    valid_categories = {"operator", "logic", "structure", "flow", "data", 
                       "memory", "reasoning", "meta", "system", "action",
                       "state", "entity", "domain"}
    category_valid = category in valid_categories
    
    print(f"  Format valid: {'✅' if format_valid else '❌'}")
    print(f"  Category valid: {'✅' if category_valid else '❌'}")
    
    if format_valid: validation_score += 20
    if category_valid: validation_score += 15
    
    # LCL - LLM Compatibility Layer
    print("\nLCL - LLM Compatibility Layer:")
    
    try:
        symbol.encode('utf-8')
        utf8_compatible = True
    except:
        utf8_compatible = False
    
    fallback_format_valid = bool(re.match(r'^\[[A-Z_]+\]$', fallback))
    
    print(f"  UTF-8 compatible: {'✅' if utf8_compatible else '❌'}")
    print(f"  Fallback format: {'✅' if fallback_format_valid else '❌'}")
    
    if utf8_compatible: validation_score += 10
    if fallback_format_valid: validation_score += 10
    
    print(f"\n📊 Validation Score: {validation_score}/100")
    validation_passed = validation_score >= 90
    print(f"🎯 Validation Result: {'✅ PASSED' if validation_passed else '❌ FAILED'}")
    
    if not validation_passed:
        print("❌ Symbol rejected - fix validation issues")
        return False
    
    # STEP 2: Token Cost Test
    print("\n🔹 STEP 2: Token Cost Test (test_llm_tokenizer_compat.py)")
    print("-" * 40)
    
    # Stima token cost (simulato)
    utf8_bytes = len(symbol.encode('utf-8'))
    estimated_token_cost = 1 if utf8_bytes <= 3 else 2
    
    print(f"UTF-8 bytes: {utf8_bytes}")
    print(f"Estimated token cost: {estimated_token_cost}")
    
    # Simboli matematici sono spesso single token
    if ord(symbol) in range(0x2200, 0x22FF):  # Mathematical operators
        print("✅ Mathematical symbol - likely single token in most tokenizers")
        token_score = 90
    else:
        token_score = 70
    
    print(f"📊 Token Compatibility Score: {token_score}/100")
    token_passed = estimated_token_cost <= 2
    print(f"🎯 Token Test Result: {'✅ PASSED' if token_passed else '❌ FAILED'}")
    
    if not token_passed:
        print("❌ Symbol rejected - token cost too high")
        return False
    
    # STEP 3: Quality Analysis
    print("\n🔹 STEP 3: Quality Analysis (score_symbol_quality.py)")
    print("-" * 40)
    
    # Calcola quality scores
    density_score = 85  # Simbolo conciso, significato denso
    cost_score = 90     # 3 byte UTF-8, rendering semplice
    overlap_score = 95  # Nessuna sovrapposizione
    uniqueness_score = 90  # Simbolo matematico distintivo
    llm_efficiency_score = 85  # Tokenizer-friendly
    
    # Media pesata
    weights = {"density": 0.25, "cost": 0.20, "overlap": 0.20, 
              "uniqueness": 0.20, "llm_efficiency": 0.15}
    
    quality_score = (
        density_score * weights["density"] +
        cost_score * weights["cost"] +
        overlap_score * weights["overlap"] +
        uniqueness_score * weights["uniqueness"] +
        llm_efficiency_score * weights["llm_efficiency"]
    )
    
    print(f"Density (25%): {density_score}/100")
    print(f"Cost (20%): {cost_score}/100")
    print(f"Overlap (20%): {overlap_score}/100")
    print(f"Uniqueness (20%): {uniqueness_score}/100")
    print(f"LLM Efficiency (15%): {llm_efficiency_score}/100")
    print(f"\n📊 Overall Quality Score: {quality_score:.1f}/100")
    
    if quality_score >= 90:
        grade = "A+"
    elif quality_score >= 85:
        grade = "A"
    elif quality_score >= 80:
        grade = "A-"
    else:
        grade = "B+"
    
    print(f"🎓 Quality Grade: {grade}")
    
    quality_passed = quality_score >= 70
    print(f"🎯 Quality Result: {'✅ PASSED' if quality_passed else '❌ FAILED'}")
    
    # STEP 4: Final Decision
    print("\n🔹 STEP 4: Submission Decision (submit_new_symbol.py)")
    print("-" * 40)
    
    overall_score = (validation_score + token_score + quality_score) / 3
    print(f"📊 Combined Score: {overall_score:.1f}/100")
    
    if validation_passed and token_passed and quality_passed and overall_score >= 85:
        print("🚀 DECISION: AUTO-APPROVED")
        print("✅ Symbol meets all ULTRA criteria")
        print("✅ Ready for addition to core/symbols.json")
        print("✅ Will be added to symbols_registry.json")
        
        # Simula aggiunta simbolo
        new_symbol = {
            "id": "NG0021",
            "symbol": symbol,
            "name": name,
            "description": description,
            "category": category,
            "aliases": ["universal", "all", "every"],
            "status": "approved",
            "version": "1.0",
            "code": code,
            "fallback": fallback,
            "unicode_point": "U+2200",
            "token_cost": estimated_token_cost,
            "token_density": 1.0,
            "llm_support": ["openai", "qwen", "deepseek", "llama"]
        }
        
        print("\n📝 New Symbol Entry:")
        print(json.dumps(new_symbol, indent=2, ensure_ascii=False))
        
        return True
        
    else:
        print("❌ DECISION: REJECTED")
        print("❌ Symbol does not meet ULTRA criteria")
        return False

if __name__ == "__main__":
    success = test_symbol_forall()
    print(f"\n🎯 FINAL RESULT: {'SUCCESS' if success else 'FAILURE'}")
