#!/usr/bin/env python3
"""
⚡ NEUROGLYPH Quick Symbolic Test
===============================

Test rapido per verificare immediatamente le capacità simboliche di NEUROGLYPH.
Può essere eseguito direttamente in Colab o localmente.

Usage:
    python quick_symbolic_test.py
"""

import json
import time
from typing import List, Dict, Any

def test_neuroglyph_tokenizer():
    """Test rapido del tokenizer NEUROGLYPH."""
    
    print("🧩 NEUROGLYPH Tokenizer Test")
    print("-" * 40)
    
    try:
        from transformers import AutoTokenizer
        
        # Carica tokenizer (assumendo che sia disponibile)
        tokenizer_path = "/content/drive/MyDrive/NEUROGLYPH/checkpoints/final"
        tokenizer = AutoTokenizer.from_pretrained(tokenizer_path)
        
        # Test simboli critici
        test_symbols = ["⚡", "🔄", "📊", "🧠", "∑", "∫", "∂", "∧", "∨", "→"]
        
        print("🔍 Testing symbol tokenization:")
        for symbol in test_symbols:
            tokens = tokenizer.encode(symbol, add_special_tokens=False)
            decoded = tokenizer.decode(tokens)
            
            # Verifica che il simbolo sia tokenizzato correttamente
            if decoded.strip() == symbol:
                print(f"✅ {symbol} → {tokens} → {decoded} (PERFECT)")
            else:
                print(f"❌ {symbol} → {tokens} → {decoded} (BROKEN)")
        
        # Test frase simbolica
        symbolic_text = "Using ⚡🔄📊 implement fast algorithm"
        tokens = tokenizer.encode(symbolic_text)
        decoded = tokenizer.decode(tokens)
        
        print(f"\n🧪 Symbolic text test:")
        print(f"Input:  {symbolic_text}")
        print(f"Tokens: {tokens}")
        print(f"Output: {decoded}")
        
        return True
        
    except Exception as e:
        print(f"❌ Tokenizer test failed: {e}")
        return False

def test_neuroglyph_generation():
    """Test rapido di generazione simbolica."""
    
    print("\n🚀 NEUROGLYPH Generation Test")
    print("-" * 40)
    
    try:
        from transformers import AutoTokenizer, AutoModelForCausalLM
        import torch
        
        # Carica modello
        model_path = "/content/drive/MyDrive/NEUROGLYPH/checkpoints/final"
        tokenizer = AutoTokenizer.from_pretrained(model_path)
        model = AutoModelForCausalLM.from_pretrained(model_path)
        
        # Test prompts simbolici
        test_prompts = [
            "Using symbols ⚡🔄📊, implement quicksort:",
            "Using symbols 🧠📊⚡, create ML pipeline:",
            "Using symbols ∑∫∂, solve integration:",
            "Using symbols ∧∨→, implement logic:"
        ]
        
        results = []
        
        for i, prompt in enumerate(test_prompts):
            print(f"\n🧪 Test {i+1}: {prompt}")
            
            # Tokenize input
            inputs = tokenizer(prompt, return_tensors="pt")
            
            # Generate
            start_time = time.time()
            with torch.no_grad():
                outputs = model.generate(
                    **inputs,
                    max_length=inputs['input_ids'].shape[1] + 100,
                    temperature=0.1,
                    do_sample=True,
                    pad_token_id=tokenizer.eos_token_id
                )
            
            generation_time = time.time() - start_time
            
            # Decode output
            generated_text = tokenizer.decode(outputs[0], skip_special_tokens=True)
            response = generated_text[len(prompt):].strip()
            
            print(f"⏱️  Time: {generation_time:.2f}s")
            print(f"📝 Response: {response[:200]}...")
            
            # Analizza simboli
            symbols_in_prompt = [s for s in ["⚡", "🔄", "📊", "🧠", "∑", "∫", "∂", "∧", "∨", "→"] if s in prompt]
            symbols_in_response = [s for s in symbols_in_prompt if s in response]
            
            symbol_retention = len(symbols_in_response) / len(symbols_in_prompt) if symbols_in_prompt else 0
            
            print(f"🔣 Symbol retention: {symbol_retention:.2%}")
            print(f"🔍 Symbols found: {symbols_in_response}")
            
            results.append({
                "prompt": prompt,
                "response": response,
                "symbols_expected": symbols_in_prompt,
                "symbols_found": symbols_in_response,
                "symbol_retention": symbol_retention,
                "generation_time": generation_time
            })
        
        # Statistiche aggregate
        avg_retention = sum(r['symbol_retention'] for r in results) / len(results)
        avg_time = sum(r['generation_time'] for r in results) / len(results)
        
        print(f"\n📊 AGGREGATE RESULTS:")
        print(f"🎯 Average Symbol Retention: {avg_retention:.2%}")
        print(f"⏱️  Average Generation Time: {avg_time:.2f}s")
        
        if avg_retention >= 0.8:
            print("🏆 EXCELLENT symbolic capabilities!")
        elif avg_retention >= 0.6:
            print("✅ GOOD symbolic capabilities!")
        else:
            print("📈 Symbolic capabilities need improvement.")
        
        return results
        
    except Exception as e:
        print(f"❌ Generation test failed: {e}")
        return []

def test_symbolic_reasoning():
    """Test rapido di reasoning simbolico."""
    
    print("\n🧠 NEUROGLYPH Reasoning Test")
    print("-" * 40)
    
    # Test cases di reasoning
    reasoning_tests = [
        {
            "prompt": "If ⚡ means fast and 🔄 means loop, what does ⚡🔄 mean?",
            "expected_concepts": ["fast", "loop", "efficient", "iteration"]
        },
        {
            "prompt": "Using ∑ and ∫, explain the relationship between discrete and continuous math:",
            "expected_concepts": ["sum", "integral", "discrete", "continuous", "limit"]
        },
        {
            "prompt": "If ∧ is AND and ∨ is OR, simplify: (A ∧ B) ∨ (A ∧ ¬B)",
            "expected_concepts": ["and", "or", "simplify", "logic", "boolean"]
        }
    ]
    
    try:
        from transformers import AutoTokenizer, AutoModelForCausalLM
        import torch
        
        model_path = "/content/drive/MyDrive/NEUROGLYPH/checkpoints/final"
        tokenizer = AutoTokenizer.from_pretrained(model_path)
        model = AutoModelForCausalLM.from_pretrained(model_path)
        
        reasoning_scores = []
        
        for i, test in enumerate(reasoning_tests):
            print(f"\n🧪 Reasoning Test {i+1}:")
            print(f"❓ {test['prompt']}")
            
            inputs = tokenizer(test['prompt'], return_tensors="pt")
            
            with torch.no_grad():
                outputs = model.generate(
                    **inputs,
                    max_length=inputs['input_ids'].shape[1] + 150,
                    temperature=0.1,
                    do_sample=True,
                    pad_token_id=tokenizer.eos_token_id
                )
            
            response = tokenizer.decode(outputs[0], skip_special_tokens=True)
            answer = response[len(test['prompt']):].strip()
            
            print(f"💭 Answer: {answer[:150]}...")
            
            # Valuta reasoning
            concepts_found = sum(1 for concept in test['expected_concepts'] 
                               if concept.lower() in answer.lower())
            reasoning_score = concepts_found / len(test['expected_concepts'])
            
            print(f"🎯 Reasoning Score: {reasoning_score:.2%}")
            reasoning_scores.append(reasoning_score)
        
        avg_reasoning = sum(reasoning_scores) / len(reasoning_scores)
        print(f"\n🧠 AVERAGE REASONING SCORE: {avg_reasoning:.2%}")
        
        if avg_reasoning >= 0.8:
            print("🏆 EXCELLENT reasoning capabilities!")
        elif avg_reasoning >= 0.6:
            print("✅ GOOD reasoning capabilities!")
        else:
            print("📈 Reasoning capabilities need improvement.")
        
        return avg_reasoning
        
    except Exception as e:
        print(f"❌ Reasoning test failed: {e}")
        return 0.0

def main():
    """Main function per test rapido."""
    
    print("⚡ NEUROGLYPH QUICK SYMBOLIC TEST")
    print("🎯 Rapid validation of symbolic capabilities")
    print("=" * 60)
    
    # Test 1: Tokenizer
    tokenizer_ok = test_neuroglyph_tokenizer()
    
    # Test 2: Generation
    generation_results = test_neuroglyph_generation()
    
    # Test 3: Reasoning
    reasoning_score = test_symbolic_reasoning()
    
    # Summary
    print("\n" + "=" * 60)
    print("📊 QUICK TEST SUMMARY")
    print("=" * 60)
    
    print(f"🧩 Tokenizer: {'✅ PASS' if tokenizer_ok else '❌ FAIL'}")
    print(f"🚀 Generation: {'✅ PASS' if generation_results else '❌ FAIL'}")
    print(f"🧠 Reasoning: {reasoning_score:.2%}")
    
    if generation_results:
        avg_retention = sum(r['symbol_retention'] for r in generation_results) / len(generation_results)
        print(f"🔣 Symbol Retention: {avg_retention:.2%}")
    
    # Overall verdict
    overall_score = (
        (1.0 if tokenizer_ok else 0.0) * 0.3 +
        (avg_retention if generation_results else 0.0) * 0.4 +
        reasoning_score * 0.3
    )
    
    print(f"\n🎯 OVERALL SCORE: {overall_score:.2%}")
    
    if overall_score >= 0.9:
        print("🏆 NEUROGLYPH IS WORLD-CLASS! 🚀")
    elif overall_score >= 0.8:
        print("🌟 NEUROGLYPH IS EXCELLENT! ✨")
    elif overall_score >= 0.7:
        print("✅ NEUROGLYPH IS GOOD! 👍")
    else:
        print("📈 NEUROGLYPH needs improvement.")
    
    print("\n🎉 Quick test completed!")

if __name__ == "__main__":
    main()
