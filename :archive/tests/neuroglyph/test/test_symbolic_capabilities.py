#!/usr/bin/env python3
"""
🧪 NEUROGLYPH Symbolic Capabilities Test
=======================================

Test completo delle capacità simboliche di NEUROGLYPH dopo il training.
Verifica che il modello gestisca correttamente i simboli e generi codice simbolico.

Features:
- Test tokenizer simbolico
- Test generazione codice con simboli
- Test reasoning simbolico
- Test zero hallucinations
- Benchmark performance simboliche
"""

import json
import time
import logging
from pathlib import Path
from typing import Dict, List, Any, Optional
from dataclasses import dataclass
import requests

# Setup logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

@dataclass
class SymbolicTestResult:
    """Risultato di un test simbolico."""
    test_id: str
    test_name: str
    input_prompt: str
    expected_symbols: List[str]
    model_output: str
    symbols_found: List[str]
    symbols_accuracy: float
    code_quality: float
    reasoning_quality: float
    hallucination_detected: bool
    execution_success: bool
    response_time: float
    overall_score: float

class NeuroglyphSymbolicTester:
    """Tester per capacità simboliche NEUROGLYPH."""
    
    def __init__(self, model_endpoint: str = "http://localhost:11434"):
        self.model_endpoint = model_endpoint
        self.test_results = []
        
        # Simboli critici da testare
        self.critical_symbols = [
            "⊃", "∧", "∨", "→", "∑", "∫", "∂", "≈", "π", "ƒ",
            "🔄", "❓", "📋", "🔢", "📝", "⟲", "⚡", "🧠", "📊"
        ]
        
        # Test cases simbolici
        self.symbolic_tests = self._create_symbolic_test_cases()
        
        logger.info("🧪 NEUROGLYPH Symbolic Tester inizializzato")
        logger.info(f"🎯 {len(self.symbolic_tests)} test cases caricati")

    def _create_symbolic_test_cases(self) -> List[Dict]:
        """Crea test cases per capacità simboliche."""
        
        return [
            {
                "id": "SYM001",
                "name": "Basic Symbol Recognition",
                "prompt": "Using symbols ⚡🔄📊, implement a fast sorting algorithm:",
                "expected_symbols": ["⚡", "🔄", "📊"],
                "expected_concepts": ["sorting", "algorithm", "performance"],
                "difficulty": "basic"
            },
            {
                "id": "SYM002", 
                "name": "Mathematical Reasoning",
                "prompt": "Using symbols ∑∫∂, implement numerical integration with error bounds:",
                "expected_symbols": ["∑", "∫", "∂"],
                "expected_concepts": ["integration", "numerical", "error"],
                "difficulty": "intermediate"
            },
            {
                "id": "SYM003",
                "name": "Logic Reasoning",
                "prompt": "Using symbols ∧∨→, implement propositional logic solver:",
                "expected_symbols": ["∧", "∨", "→"],
                "expected_concepts": ["logic", "propositional", "solver"],
                "difficulty": "advanced"
            },
            {
                "id": "SYM004",
                "name": "Complex Symbolic Reasoning",
                "prompt": "Using symbols 🧠📊⚡🔄, create ML pipeline with feature engineering:",
                "expected_symbols": ["🧠", "📊", "⚡", "🔄"],
                "expected_concepts": ["machine learning", "pipeline", "features"],
                "difficulty": "expert"
            },
            {
                "id": "SYM005",
                "name": "Multi-Domain Integration",
                "prompt": "Using symbols ƒ∑🔄📋, implement recursive function with memoization:",
                "expected_symbols": ["ƒ", "∑", "🔄", "📋"],
                "expected_concepts": ["recursion", "memoization", "optimization"],
                "difficulty": "god_tier"
            }
        ]

    def query_neuroglyph(self, prompt: str, model_name: str = "neuroglyph") -> Dict[str, Any]:
        """Query NEUROGLYPH model."""
        
        start_time = time.time()
        
        try:
            response = requests.post(
                f"{self.model_endpoint}/api/generate",
                json={
                    "model": model_name,
                    "prompt": prompt,
                    "stream": False,
                    "options": {
                        "temperature": 0.1,
                        "top_p": 0.9,
                        "max_tokens": 1024,
                        "stop": ["<|im_end|>"]
                    }
                },
                timeout=60
            )
            
            response_time = time.time() - start_time
            
            if response.status_code == 200:
                result = response.json()
                return {
                    "output": result.get("response", ""),
                    "response_time": response_time,
                    "success": True,
                    "error": None
                }
            else:
                return {
                    "output": "",
                    "response_time": response_time,
                    "success": False,
                    "error": f"HTTP {response.status_code}"
                }
                
        except Exception as e:
            return {
                "output": "",
                "response_time": time.time() - start_time,
                "success": False,
                "error": str(e)
            }

    def analyze_symbolic_output(self, output: str, expected_symbols: List[str]) -> Dict[str, Any]:
        """Analizza output per capacità simboliche."""
        
        # Trova simboli nell'output
        symbols_found = []
        for symbol in expected_symbols:
            if symbol in output:
                symbols_found.append(symbol)
        
        # Calcola accuratezza simbolica
        symbols_accuracy = len(symbols_found) / len(expected_symbols) if expected_symbols else 0.0
        
        # Valuta qualità del codice
        code_quality = self._evaluate_code_quality(output)
        
        # Valuta qualità del reasoning
        reasoning_quality = self._evaluate_reasoning_quality(output, expected_symbols)
        
        # Rileva allucinazioni
        hallucination_detected = self._detect_hallucinations(output)
        
        return {
            "symbols_found": symbols_found,
            "symbols_accuracy": symbols_accuracy,
            "code_quality": code_quality,
            "reasoning_quality": reasoning_quality,
            "hallucination_detected": hallucination_detected
        }

    def _evaluate_code_quality(self, output: str) -> float:
        """Valuta qualità del codice generato."""
        
        score = 0.0
        
        # Presenza di strutture di codice
        if "def " in output:
            score += 0.3
        if "return" in output:
            score += 0.2
        if "class " in output:
            score += 0.2
        if "#" in output:  # Commenti
            score += 0.1
        if "import" in output:
            score += 0.1
        if len(output.split('\n')) > 5:  # Codice non triviale
            score += 0.1
        
        return min(score, 1.0)

    def _evaluate_reasoning_quality(self, output: str, expected_symbols: List[str]) -> float:
        """Valuta qualità del reasoning simbolico."""
        
        score = 0.0
        
        # Presenza di spiegazioni
        if any(word in output.lower() for word in ["because", "since", "therefore", "thus"]):
            score += 0.3
        
        # Uso corretto dei simboli nel contesto
        for symbol in expected_symbols:
            if symbol in output:
                # Verifica che il simbolo sia usato in contesto appropriato
                if self._symbol_used_correctly(symbol, output):
                    score += 0.2
        
        # Presenza di step logici
        if any(word in output.lower() for word in ["step", "first", "then", "finally"]):
            score += 0.2
        
        return min(score, 1.0)

    def _symbol_used_correctly(self, symbol: str, output: str) -> bool:
        """Verifica uso corretto del simbolo."""
        
        # Mapping simbolo -> contesti appropriati
        symbol_contexts = {
            "⚡": ["fast", "quick", "speed", "performance", "efficient"],
            "🔄": ["loop", "iterate", "cycle", "repeat", "process"],
            "📊": ["data", "analysis", "chart", "statistics", "metrics"],
            "🧠": ["intelligence", "learning", "neural", "ai", "smart"],
            "∑": ["sum", "total", "aggregate", "accumulate"],
            "∫": ["integral", "integration", "continuous"],
            "∂": ["derivative", "partial", "gradient"],
            "∧": ["and", "conjunction", "both"],
            "∨": ["or", "disjunction", "either"],
            "→": ["implies", "then", "arrow", "direction"]
        }
        
        contexts = symbol_contexts.get(symbol, [])
        return any(context in output.lower() for context in contexts)

    def _detect_hallucinations(self, output: str) -> bool:
        """Rileva possibili allucinazioni."""
        
        # Pattern di allucinazioni comuni
        hallucination_patterns = [
            "ng:invalid:",
            "undefined_symbol",
            "error_symbol",
            "fake_function",
            "nonexistent_library"
        ]
        
        return any(pattern in output for pattern in hallucination_patterns)

    def run_symbolic_test(self, test_case: Dict) -> SymbolicTestResult:
        """Esegue un singolo test simbolico."""
        
        logger.info(f"🧪 Eseguendo test: {test_case['name']}")
        
        # Query modello
        response = self.query_neuroglyph(test_case['prompt'])
        
        if not response['success']:
            logger.error(f"❌ Errore query: {response['error']}")
            return SymbolicTestResult(
                test_id=test_case['id'],
                test_name=test_case['name'],
                input_prompt=test_case['prompt'],
                expected_symbols=test_case['expected_symbols'],
                model_output="",
                symbols_found=[],
                symbols_accuracy=0.0,
                code_quality=0.0,
                reasoning_quality=0.0,
                hallucination_detected=True,
                execution_success=False,
                response_time=response['response_time'],
                overall_score=0.0
            )
        
        # Analizza output
        analysis = self.analyze_symbolic_output(
            response['output'], 
            test_case['expected_symbols']
        )
        
        # Calcola score complessivo
        overall_score = (
            analysis['symbols_accuracy'] * 0.4 +
            analysis['code_quality'] * 0.3 +
            analysis['reasoning_quality'] * 0.3
        )
        
        # Penalizza allucinazioni
        if analysis['hallucination_detected']:
            overall_score *= 0.5
        
        return SymbolicTestResult(
            test_id=test_case['id'],
            test_name=test_case['name'],
            input_prompt=test_case['prompt'],
            expected_symbols=test_case['expected_symbols'],
            model_output=response['output'],
            symbols_found=analysis['symbols_found'],
            symbols_accuracy=analysis['symbols_accuracy'],
            code_quality=analysis['code_quality'],
            reasoning_quality=analysis['reasoning_quality'],
            hallucination_detected=analysis['hallucination_detected'],
            execution_success=True,
            response_time=response['response_time'],
            overall_score=overall_score
        )

    def run_complete_symbolic_test_suite(self) -> Dict[str, Any]:
        """Esegue suite completa di test simbolici."""
        
        logger.info("🚀 Iniziando test suite simbolica completa...")
        
        results = []
        
        for test_case in self.symbolic_tests:
            result = self.run_symbolic_test(test_case)
            results.append(result)
            
            # Log risultato
            logger.info(f"✅ {test_case['name']}: Score {result.overall_score:.3f}")
        
        # Calcola statistiche aggregate
        stats = self._calculate_aggregate_stats(results)
        
        # Salva risultati
        self._save_results(results, stats)
        
        return {
            "results": results,
            "stats": stats
        }

    def _calculate_aggregate_stats(self, results: List[SymbolicTestResult]) -> Dict[str, Any]:
        """Calcola statistiche aggregate."""
        
        if not results:
            return {}
        
        return {
            "total_tests": len(results),
            "avg_overall_score": sum(r.overall_score for r in results) / len(results),
            "avg_symbols_accuracy": sum(r.symbols_accuracy for r in results) / len(results),
            "avg_code_quality": sum(r.code_quality for r in results) / len(results),
            "avg_reasoning_quality": sum(r.reasoning_quality for r in results) / len(results),
            "avg_response_time": sum(r.response_time for r in results) / len(results),
            "hallucination_rate": sum(1 for r in results if r.hallucination_detected) / len(results),
            "success_rate": sum(1 for r in results if r.execution_success) / len(results),
            "perfect_symbolic_tests": sum(1 for r in results if r.symbols_accuracy == 1.0),
            "high_quality_tests": sum(1 for r in results if r.overall_score >= 0.8)
        }

    def _save_results(self, results: List[SymbolicTestResult], stats: Dict[str, Any]):
        """Salva risultati dei test."""
        
        timestamp = time.strftime("%Y%m%d_%H%M%S")
        results_file = f"symbolic_test_results_{timestamp}.json"
        
        output = {
            "timestamp": timestamp,
            "model": "neuroglyph",
            "test_type": "symbolic_capabilities",
            "aggregate_stats": stats,
            "detailed_results": [
                {
                    "test_id": r.test_id,
                    "test_name": r.test_name,
                    "input_prompt": r.input_prompt,
                    "expected_symbols": r.expected_symbols,
                    "symbols_found": r.symbols_found,
                    "symbols_accuracy": r.symbols_accuracy,
                    "code_quality": r.code_quality,
                    "reasoning_quality": r.reasoning_quality,
                    "hallucination_detected": r.hallucination_detected,
                    "overall_score": r.overall_score,
                    "response_time": r.response_time
                }
                for r in results
            ]
        }
        
        with open(results_file, 'w') as f:
            json.dump(output, f, indent=2)
        
        logger.info(f"💾 Risultati salvati: {results_file}")

def main():
    """Main function per test simbolici."""
    
    print("🧪 NEUROGLYPH SYMBOLIC CAPABILITIES TEST")
    print("🎯 Validazione capacità simboliche post-training")
    print("=" * 60)
    
    # Inizializza tester
    tester = NeuroglyphSymbolicTester()
    
    # Esegui test suite
    results = tester.run_complete_symbolic_test_suite()
    
    # Report finale
    stats = results['stats']
    print(f"\n📊 RISULTATI FINALI:")
    print(f"🎯 Overall Score: {stats['avg_overall_score']:.3f}")
    print(f"🔣 Symbols Accuracy: {stats['avg_symbols_accuracy']:.3f}")
    print(f"💻 Code Quality: {stats['avg_code_quality']:.3f}")
    print(f"🧠 Reasoning Quality: {stats['avg_reasoning_quality']:.3f}")
    print(f"⚡ Response Time: {stats['avg_response_time']:.2f}s")
    print(f"🚫 Hallucination Rate: {stats['hallucination_rate']:.3f}")
    print(f"✅ Success Rate: {stats['success_rate']:.3f}")
    
    # Verdetto
    if stats['avg_overall_score'] >= 0.9:
        print("\n🏆 NEUROGLYPH HA CAPACITÀ SIMBOLICHE PERFETTE!")
    elif stats['avg_overall_score'] >= 0.8:
        print("\n🌟 NEUROGLYPH HA ECCELLENTI CAPACITÀ SIMBOLICHE!")
    elif stats['avg_overall_score'] >= 0.7:
        print("\n✅ NEUROGLYPH HA BUONE CAPACITÀ SIMBOLICHE!")
    else:
        print("\n📈 NEUROGLYPH necessita miglioramenti simbolici.")

if __name__ == "__main__":
    main()
