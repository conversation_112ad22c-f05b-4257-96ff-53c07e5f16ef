#!/usr/bin/env python3
"""
🧪 NEUROGLYPH Fixed Tokenizer Test
=================================

Test delle capacità simboliche di NEUROGLYPH dopo il fix del tokenizer.
Verifica che ora i simboli siano gestiti correttamente.
"""

import json
from transformers import AutoTokenizer
from pathlib import Path

def test_fixed_tokenizer():
    """Test del tokenizer NEUROGLYPH dopo il fix."""
    
    print("🧪 NEUROGLYPH FIXED TOKENIZER TEST")
    print("=" * 50)
    
    try:
        # Carica tokenizer aggiornato
        tokenizer_path = "neuroglyph/training/colab_package"
        tokenizer = AutoTokenizer.from_pretrained(tokenizer_path)
        
        print(f"✅ Tokenizer caricato da: {tokenizer_path}")
        print(f"📊 Vocab size: {len(tokenizer.vocab)}")
        
        # Test simboli critici NEUROGLYPH
        critical_symbols = [
            "⚡", "🔄", "📊", "🧠", "📋", "🔢", "📝", "❓",
            "∑", "∫", "∂", "≈", "π", "ƒ", "∧", "∨", "→", "⊃",
            "ng:operator:add", "ng:logic:implies", "ng:memory:alloc"
        ]
        
        print(f"\n🔍 Testing {len(critical_symbols)} critical symbols:")
        
        perfect_tokens = 0
        single_tokens = 0
        
        for symbol in critical_symbols:
            # Tokenize
            tokens = tokenizer.encode(symbol, add_special_tokens=False)
            
            # Detokenize
            decoded = tokenizer.decode(tokens, skip_special_tokens=True)
            
            # Verifica fedeltà
            is_perfect = decoded.strip() == symbol
            is_single = len(tokens) == 1
            
            status = "✅" if is_perfect else "❌"
            token_info = f"[{len(tokens)} tokens]" if not is_single else "[1 token]"
            
            print(f"{status} {symbol} → {tokens} → '{decoded}' {token_info}")
            
            if is_perfect:
                perfect_tokens += 1
            if is_single:
                single_tokens += 1
        
        # Statistiche
        fidelity_rate = perfect_tokens / len(critical_symbols)
        single_token_rate = single_tokens / len(critical_symbols)
        
        print(f"\n📊 RESULTS:")
        print(f"🎯 Perfect fidelity: {perfect_tokens}/{len(critical_symbols)} ({fidelity_rate:.1%})")
        print(f"⚡ Single tokens: {single_tokens}/{len(critical_symbols)} ({single_token_rate:.1%})")
        
        # Test frase simbolica complessa
        print(f"\n🧪 Testing complex symbolic sentence:")
        
        symbolic_sentence = "Using ⚡🔄📊 implement algorithm with ∑∫∂ and ng:operator:add"
        tokens = tokenizer.encode(symbolic_sentence)
        decoded = tokenizer.decode(tokens)
        
        print(f"Input:  {symbolic_sentence}")
        print(f"Tokens: {len(tokens)} tokens")
        print(f"Output: {decoded}")
        
        # Verifica che tutti i simboli siano preservati
        symbols_in_input = ["⚡", "🔄", "📊", "∑", "∫", "∂", "ng:operator:add"]
        symbols_preserved = sum(1 for s in symbols_in_input if s in decoded)
        preservation_rate = symbols_preserved / len(symbols_in_input)
        
        print(f"🔣 Symbol preservation: {symbols_preserved}/{len(symbols_in_input)} ({preservation_rate:.1%})")
        
        # Verdetto finale
        if fidelity_rate >= 0.95 and preservation_rate >= 0.9:
            print(f"\n🏆 TOKENIZER FIX SUCCESSFUL!")
            print(f"✅ NEUROGLYPH tokenizer is now WORLD-CLASS!")
        elif fidelity_rate >= 0.8:
            print(f"\n✅ TOKENIZER SIGNIFICANTLY IMPROVED!")
            print(f"📈 Good symbolic capabilities achieved!")
        else:
            print(f"\n⚠️ TOKENIZER NEEDS MORE WORK")
            print(f"📈 Some improvements but not sufficient yet.")
        
        return {
            "fidelity_rate": fidelity_rate,
            "single_token_rate": single_token_rate,
            "preservation_rate": preservation_rate,
            "perfect_tokens": perfect_tokens,
            "total_tested": len(critical_symbols)
        }
        
    except Exception as e:
        print(f"❌ Error testing tokenizer: {e}")
        return {"error": str(e)}

def test_symbolic_generation():
    """Test generazione simbolica con tokenizer corretto."""
    
    print(f"\n🚀 SYMBOLIC GENERATION TEST")
    print("-" * 40)
    
    try:
        from transformers import AutoModelForCausalLM
        import torch
        
        # Carica modello e tokenizer
        model_path = "neuroglyph/training/colab_package"
        tokenizer = AutoTokenizer.from_pretrained(model_path)
        
        # Per ora testiamo solo il tokenizer
        # Il modello fine-tuned dovrebbe essere caricato da Google Drive
        print("⚠️ Model loading skipped - testing tokenizer only")
        print("🔧 For full test, load fine-tuned model from Google Drive")
        
        # Test prompt simbolico
        prompt = "Using symbols ⚡🔄📊, implement quicksort algorithm:"
        
        # Tokenize
        tokens = tokenizer.encode(prompt)
        decoded = tokenizer.decode(tokens)
        
        print(f"🧪 Symbolic prompt test:")
        print(f"Input:  {prompt}")
        print(f"Tokens: {len(tokens)}")
        print(f"Output: {decoded}")
        
        # Verifica preservazione simboli
        symbols = ["⚡", "🔄", "📊"]
        preserved = sum(1 for s in symbols if s in decoded)
        
        print(f"🔣 Symbols preserved: {preserved}/{len(symbols)}")
        
        if preserved == len(symbols):
            print("✅ Perfect symbolic prompt handling!")
        else:
            print("⚠️ Some symbols lost in tokenization")
        
        return True
        
    except Exception as e:
        print(f"❌ Error in generation test: {e}")
        return False

def main():
    """Main test function."""
    
    print("🧪 NEUROGLYPH TOKENIZER VALIDATION")
    print("🎯 Post-fix symbolic capabilities test")
    print("=" * 60)
    
    # Test 1: Tokenizer fidelity
    tokenizer_results = test_fixed_tokenizer()
    
    # Test 2: Symbolic generation
    generation_ok = test_symbolic_generation()
    
    # Summary
    print(f"\n" + "=" * 60)
    print("📊 FINAL SUMMARY")
    print("=" * 60)
    
    if "error" not in tokenizer_results:
        print(f"🎯 Tokenizer Fidelity: {tokenizer_results['fidelity_rate']:.1%}")
        print(f"⚡ Single Token Rate: {tokenizer_results['single_token_rate']:.1%}")
        print(f"🔣 Symbol Preservation: {tokenizer_results['preservation_rate']:.1%}")
        
        overall_score = (
            tokenizer_results['fidelity_rate'] * 0.5 +
            tokenizer_results['single_token_rate'] * 0.3 +
            tokenizer_results['preservation_rate'] * 0.2
        )
        
        print(f"🏆 Overall Score: {overall_score:.1%}")
        
        if overall_score >= 0.95:
            print(f"\n🚀 NEUROGLYPH TOKENIZER IS PERFECT!")
        elif overall_score >= 0.85:
            print(f"\n🌟 NEUROGLYPH TOKENIZER IS EXCELLENT!")
        elif overall_score >= 0.75:
            print(f"\n✅ NEUROGLYPH TOKENIZER IS GOOD!")
        else:
            print(f"\n📈 NEUROGLYPH TOKENIZER NEEDS IMPROVEMENT")
    
    print(f"🚀 Generation Test: {'✅ PASS' if generation_ok else '❌ FAIL'}")
    
    print(f"\n🎉 Tokenizer validation completed!")
    print(f"🔧 Next: Load fine-tuned model and test full capabilities!")

if __name__ == "__main__":
    main()
