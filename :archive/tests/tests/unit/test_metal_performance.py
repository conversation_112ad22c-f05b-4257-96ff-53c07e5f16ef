#!/usr/bin/env python3
"""
NEUROGLYPH LLM - Test Performance Metal GPU
Confronta performance CPU vs Metal GPU per Qwen2.5-Coder
"""

import os
import sys
import time
import psutil
from pathlib import Path

def check_environment():
    """Verifica environment e memoria"""
    print("🔍 VERIFICA ENVIRONMENT")
    print("=" * 50)
    
    # Memoria
    memory = psutil.virtual_memory()
    available_gb = memory.available / (1024**3)
    print(f"💾 Memoria disponibile: {available_gb:.1f} GB")
    
    if available_gb < 2.5:
        print("⚠️ Memoria limitata - chiudi altre app")
        return False
    
    # Modello
    model_path = "model/qwen2.5coder/qwen2.5-coder-7b-instruct-q4_k_m.gguf"
    if not os.path.exists(model_path):
        print(f"❌ Modello non trovato: {model_path}")
        return False
    
    print(f"✅ Modello trovato: {Path(model_path).stat().st_size / (1024**3):.1f} GB")
    
    # llama-cpp-python
    try:
        from llama_cpp import Llama
        print("✅ llama-cpp-python disponibile")
        return True
    except ImportError:
        print("❌ llama-cpp-python non installato")
        return False

def test_cpu_performance():
    """Test performance CPU only"""
    print("\n🖥️ TEST PERFORMANCE CPU")
    print("=" * 50)
    
    try:
        from llama_cpp import Llama
        
        model_path = "model/qwen2.5coder/qwen2.5-coder-7b-instruct-q4_k_m.gguf"
        
        print("🔄 Caricamento modello (CPU only)...")
        start_time = time.time()
        
        llm = Llama(
            model_path=model_path,
            n_ctx=256,           # Context ridotto per velocità
            n_threads=4,         # Tutti i thread CPU
            n_gpu_layers=0,      # SOLO CPU
            verbose=False,
            use_mmap=True,
            use_mlock=False,
            n_batch=64
        )
        
        load_time = time.time() - start_time
        print(f"✅ Caricamento CPU: {load_time:.2f}s")
        
        # Test inferenza
        prompts = [
            "def add(a, b):",
            "def factorial(n):",
            "def fibonacci(n):"
        ]
        
        total_inference_time = 0
        total_tokens = 0
        
        for i, prompt in enumerate(prompts):
            print(f"   Test {i+1}/3: {prompt}")
            
            start_time = time.time()
            response = llm(
                prompt,
                max_tokens=20,
                temperature=0.1,
                echo=False
            )
            inference_time = time.time() - start_time
            
            generated_text = response['choices'][0]['text']
            token_count = len(generated_text.split())
            
            total_inference_time += inference_time
            total_tokens += token_count
            
            print(f"      Tempo: {inference_time:.2f}s, Token: {token_count}")
        
        avg_inference_time = total_inference_time / len(prompts)
        tokens_per_sec = total_tokens / total_inference_time if total_inference_time > 0 else 0
        
        print(f"\n📊 RISULTATI CPU:")
        print(f"   Caricamento: {load_time:.2f}s")
        print(f"   Inferenza media: {avg_inference_time:.2f}s")
        print(f"   Token/sec: {tokens_per_sec:.1f}")
        
        # Cleanup
        del llm
        
        return {
            'load_time': load_time,
            'avg_inference_time': avg_inference_time,
            'tokens_per_sec': tokens_per_sec,
            'success': True
        }
        
    except Exception as e:
        print(f"❌ Errore CPU test: {e}")
        return {'success': False, 'error': str(e)}

def test_metal_performance():
    """Test performance Metal GPU"""
    print("\n🚀 TEST PERFORMANCE METAL GPU")
    print("=" * 50)
    
    try:
        from llama_cpp import Llama
        
        model_path = "model/qwen2.5coder/qwen2.5-coder-7b-instruct-q4_k_m.gguf"
        
        # Prova diverse configurazioni GPU layers
        gpu_configs = [5, 10, 15, 20]
        best_config = None
        best_performance = float('inf')
        
        for gpu_layers in gpu_configs:
            print(f"\n🔄 Test {gpu_layers} GPU layers...")
            
            try:
                start_time = time.time()
                
                llm = Llama(
                    model_path=model_path,
                    n_ctx=256,
                    n_threads=2,              # Meno thread per GPU
                    n_gpu_layers=gpu_layers,  # Layer su GPU
                    verbose=False,
                    use_mmap=True,
                    use_mlock=False,
                    n_batch=64
                )
                
                load_time = time.time() - start_time
                print(f"   Caricamento: {load_time:.2f}s")
                
                # Test inferenza veloce
                start_time = time.time()
                response = llm(
                    "def test():",
                    max_tokens=10,
                    temperature=0.1,
                    echo=False
                )
                inference_time = time.time() - start_time
                
                print(f"   Inferenza: {inference_time:.2f}s")
                
                # Salva se è la migliore configurazione
                if inference_time < best_performance:
                    best_performance = inference_time
                    best_config = {
                        'gpu_layers': gpu_layers,
                        'load_time': load_time,
                        'inference_time': inference_time
                    }
                
                # Cleanup
                del llm
                
            except Exception as e:
                print(f"   ❌ Fallito con {gpu_layers} layers: {e}")
                continue
        
        if best_config is None:
            print("❌ Nessuna configurazione Metal funzionante")
            return {'success': False, 'error': 'No working Metal config'}
        
        # Test completo con migliore configurazione
        print(f"\n🏆 Test completo con {best_config['gpu_layers']} GPU layers...")
        
        llm = Llama(
            model_path=model_path,
            n_ctx=256,
            n_threads=2,
            n_gpu_layers=best_config['gpu_layers'],
            verbose=False,
            use_mmap=True,
            use_mlock=False,
            n_batch=64
        )
        
        # Test inferenza completo
        prompts = [
            "def add(a, b):",
            "def factorial(n):",
            "def fibonacci(n):"
        ]
        
        total_inference_time = 0
        total_tokens = 0
        
        for i, prompt in enumerate(prompts):
            print(f"   Test {i+1}/3: {prompt}")
            
            start_time = time.time()
            response = llm(
                prompt,
                max_tokens=20,
                temperature=0.1,
                echo=False
            )
            inference_time = time.time() - start_time
            
            generated_text = response['choices'][0]['text']
            token_count = len(generated_text.split())
            
            total_inference_time += inference_time
            total_tokens += token_count
            
            print(f"      Tempo: {inference_time:.2f}s, Token: {token_count}")
        
        avg_inference_time = total_inference_time / len(prompts)
        tokens_per_sec = total_tokens / total_inference_time if total_inference_time > 0 else 0
        
        print(f"\n📊 RISULTATI METAL GPU:")
        print(f"   GPU layers: {best_config['gpu_layers']}")
        print(f"   Caricamento: {best_config['load_time']:.2f}s")
        print(f"   Inferenza media: {avg_inference_time:.2f}s")
        print(f"   Token/sec: {tokens_per_sec:.1f}")
        
        # Cleanup
        del llm
        
        return {
            'gpu_layers': best_config['gpu_layers'],
            'load_time': best_config['load_time'],
            'avg_inference_time': avg_inference_time,
            'tokens_per_sec': tokens_per_sec,
            'success': True
        }
        
    except Exception as e:
        print(f"❌ Errore Metal test: {e}")
        return {'success': False, 'error': str(e)}

def compare_results(cpu_results, metal_results):
    """Confronta risultati CPU vs Metal"""
    print("\n📊 CONFRONTO PERFORMANCE")
    print("=" * 60)
    
    if not cpu_results['success'] or not metal_results['success']:
        print("❌ Impossibile confrontare - test falliti")
        return False
    
    # Confronto caricamento
    load_speedup = cpu_results['load_time'] / metal_results['load_time']
    print(f"🔄 CARICAMENTO:")
    print(f"   CPU: {cpu_results['load_time']:.2f}s")
    print(f"   Metal: {metal_results['load_time']:.2f}s")
    print(f"   Speedup: {load_speedup:.2f}x")
    
    # Confronto inferenza
    inference_speedup = cpu_results['avg_inference_time'] / metal_results['avg_inference_time']
    print(f"\n⚡ INFERENZA:")
    print(f"   CPU: {cpu_results['avg_inference_time']:.2f}s")
    print(f"   Metal: {metal_results['avg_inference_time']:.2f}s")
    print(f"   Speedup: {inference_speedup:.2f}x")
    
    # Confronto throughput
    throughput_speedup = metal_results['tokens_per_sec'] / cpu_results['tokens_per_sec'] if cpu_results['tokens_per_sec'] > 0 else 0
    print(f"\n🚀 THROUGHPUT:")
    print(f"   CPU: {cpu_results['tokens_per_sec']:.1f} token/sec")
    print(f"   Metal: {metal_results['tokens_per_sec']:.1f} token/sec")
    print(f"   Speedup: {throughput_speedup:.2f}x")
    
    # Raccomandazione
    print(f"\n💡 RACCOMANDAZIONE:")
    if inference_speedup > 1.5:
        print(f"🎉 METAL GPU OFFRE ACCELERAZIONE SIGNIFICATIVA!")
        print(f"✅ Usa Metal GPU con {metal_results['gpu_layers']} layers")
        print(f"🚀 Performance {inference_speedup:.1f}x superiori")
        return True
    elif inference_speedup > 1.1:
        print(f"✅ Metal GPU offre miglioramenti moderati")
        print(f"⚙️ Considera Metal per workload intensivi")
        return True
    else:
        print(f"⚠️ Metal GPU non offre vantaggi significativi")
        print(f"🖥️ Usa configurazione CPU ottimizzata")
        return False

def main():
    """Test principale"""
    print("🧠 NEUROGLYPH LLM - Metal GPU Performance Test")
    print("🎯 Confronto CPU vs Metal GPU su Mac M2")
    print("=" * 70)
    
    # Verifica environment
    if not check_environment():
        print("❌ Environment non pronto")
        return False
    
    # Test CPU
    cpu_results = test_cpu_performance()
    
    # Test Metal
    metal_results = test_metal_performance()
    
    # Confronto
    metal_advantage = compare_results(cpu_results, metal_results)
    
    # Salva risultati
    results = {
        'cpu': cpu_results,
        'metal': metal_results,
        'metal_advantage': metal_advantage
    }
    
    import json
    with open('metal_performance_results.json', 'w') as f:
        json.dump(results, f, indent=2)
    
    print(f"\n✅ Risultati salvati in: metal_performance_results.json")
    
    return metal_advantage

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
