"""
NEUROGLYPH LLM - Test Suite per Coding Reasoning
===============================================

Test completi per verificare il funzionamento del SOCRATECodeSynthesizer
e dimostrare la superiorità del "codice pensato, non generato".

Autore: NEUROGLYPH Team
Data: 2025
Licenza: MIT
"""

import unittest
import sys
import os
import time
from typing import List, Dict, Any

# Aggiungi il path del progetto
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from neuroglyph.core.socrate_code_synthesizer import (
    SOCRATECodeSynthesizer, 
    CodeSpecification, 
    create_demo_specification
)
from neuroglyph.core.ast_corrector import ASTValidator, ASTCorrector

class TestSOCRATECodeSynthesizer(unittest.TestCase):
    """Test suite per SOCRATECodeSynthesizer"""
    
    def setUp(self):
        """Setup per ogni test"""
        self.synthesizer = SOCRATECodeSynthesizer()
        self.validator = ASTValidator()
        self.corrector = ASTCorrector()
    
    def test_initialization(self):
        """Test inizializzazione del synthesizer"""
        self.assertIsNotNone(self.synthesizer)
        self.assertIsNotNone(self.synthesizer.reasoning_symbols)
        self.assertIsNotNone(self.synthesizer.ast_mapping)
        
        # Verifica statistiche iniziali
        stats = self.synthesizer.get_stats()
        self.assertEqual(stats["total_synthesized"], 0)
        self.assertEqual(stats["successful_validations"], 0)
    
    def test_sorting_code_generation(self):
        """Test generazione codice per sorting"""
        spec = CodeSpecification(
            goal="Ordina una lista di numeri",
            input_types=["List[int]"],
            output_type="List[int]",
            constraints=["efficient"],
            test_cases=[
                {"input": [3, 1, 4, 1, 5], "expected": [1, 1, 3, 4, 5]},
                {"input": [], "expected": []},
                {"input": [1], "expected": [1]}
            ]
        )
        
        # Genera codice
        start_time = time.time()
        generated_code, reasoning_dag = self.synthesizer.synthesize_code(spec)
        generation_time = time.time() - start_time
        
        # Verifica risultati
        self.assertIsNotNone(generated_code)
        self.assertIsNotNone(reasoning_dag)
        self.assertIn("def ", generated_code)
        self.assertIn("sort", generated_code.lower())
        
        # Verifica DAG di ragionamento
        self.assertGreater(len(reasoning_dag.steps), 0)
        self.assertTrue(reasoning_dag.is_valid)
        self.assertGreater(reasoning_dag.validation_score, 0.5)
        
        # Verifica che il codice sia sintatticamente corretto
        validation_result = self.validator.validate(generated_code)
        self.assertTrue(validation_result.is_valid)
        
        print(f"\n✅ Test Sorting - Tempo generazione: {generation_time:.3f}s")
        print(f"📊 DAG steps: {len(reasoning_dag.steps)}")
        print(f"📊 Validation score: {reasoning_dag.validation_score:.2f}")
    
    def test_searching_code_generation(self):
        """Test generazione codice per searching"""
        spec = CodeSpecification(
            goal="Cerca un elemento in una lista",
            input_types=["List[int]", "int"],
            output_type="int",
            constraints=["simple"],
            test_cases=[
                {"input": ([1, 2, 3, 4, 5], 3), "expected": 2},
                {"input": ([1, 2, 3], 5), "expected": -1}
            ]
        )
        
        generated_code, reasoning_dag = self.synthesizer.synthesize_code(spec)
        
        # Verifica risultati
        self.assertIsNotNone(generated_code)
        self.assertIn("def ", generated_code)
        self.assertIn("search", generated_code.lower())
        
        # Verifica validazione
        validation_result = self.validator.validate(generated_code)
        self.assertTrue(validation_result.is_valid)
        
        print(f"\n✅ Test Searching completato")
    
    def test_reasoning_dag_validation(self):
        """Test validazione DAG di ragionamento"""
        spec = create_demo_specification("Calcola la somma di una lista")
        
        # Genera DAG
        generated_code, reasoning_dag = self.synthesizer.synthesize_code(spec)
        
        # Verifica proprietà del DAG
        self.assertIsNotNone(reasoning_dag.goal)
        self.assertGreater(len(reasoning_dag.steps), 0)
        
        # Verifica che tutti i passaggi siano validati
        for step in reasoning_dag.steps:
            self.assertIsNotNone(step.premise)
            self.assertIsNotNone(step.inference)
            self.assertIsNotNone(step.conclusion)
            self.assertGreater(len(step.neuroglyphs), 0)
            self.assertGreater(step.confidence, 0.0)
        
        print(f"\n✅ Test DAG Validation completato")
    
    def test_ast_correction(self):
        """Test correzione automatica AST"""
        # Codice con errori
        buggy_code = """
def broken_function(x):
    result = x * 2
    # Manca return statement
"""
        
        # Corregge automaticamente
        corrected_code, validation = self.corrector.correct(buggy_code)
        
        # Verifica correzione
        self.assertIsNotNone(corrected_code)
        self.assertTrue(validation.is_valid)
        self.assertIn("return", corrected_code)
        
        print(f"\n✅ Test AST Correction completato")
    
    def test_performance_benchmark(self):
        """Test performance del synthesizer"""
        specs = [
            create_demo_specification("Ordina lista"),
            create_demo_specification("Cerca elemento"),
            create_demo_specification("Calcola somma"),
            create_demo_specification("Filtra elementi"),
            create_demo_specification("Mappa valori")
        ]
        
        total_time = 0
        successful_generations = 0
        
        for i, spec in enumerate(specs):
            start_time = time.time()
            try:
                generated_code, reasoning_dag = self.synthesizer.synthesize_code(spec)
                generation_time = time.time() - start_time
                total_time += generation_time
                
                if reasoning_dag.is_valid:
                    successful_generations += 1
                
                print(f"   Test {i+1}: {generation_time:.3f}s - {'✅' if reasoning_dag.is_valid else '❌'}")
                
            except Exception as e:
                print(f"   Test {i+1}: ❌ Errore - {e}")
        
        avg_time = total_time / len(specs)
        success_rate = successful_generations / len(specs)
        
        # Verifica performance
        self.assertLess(avg_time, 5.0)  # Meno di 5 secondi per generazione
        self.assertGreater(success_rate, 0.8)  # Almeno 80% di successo
        
        print(f"\n📊 Performance Benchmark:")
        print(f"   - Tempo medio: {avg_time:.3f}s")
        print(f"   - Tasso successo: {success_rate:.1%}")
        print(f"   - Generazioni riuscite: {successful_generations}/{len(specs)}")
    
    def test_code_quality_metrics(self):
        """Test metriche di qualità del codice generato"""
        spec = create_demo_specification("Implementa algoritmo di ordinamento efficiente")
        
        generated_code, reasoning_dag = self.synthesizer.synthesize_code(spec)
        validation_result = self.validator.validate(generated_code)
        
        # Verifica metriche di qualità
        self.assertTrue(validation_result.is_valid)
        self.assertGreater(validation_result.score, 80.0)  # Score > 80
        self.assertLess(len(validation_result.errors), 2)  # Massimo 1 errore
        
        # Verifica caratteristiche del codice
        self.assertIn("def ", generated_code)  # Ha funzioni
        self.assertIn("return", generated_code)  # Ha return statements
        
        # Verifica che non contenga pattern problematici
        self.assertNotIn("eval", generated_code)
        self.assertNotIn("exec", generated_code)
        
        print(f"\n📊 Code Quality Metrics:")
        print(f"   - Validation score: {validation_result.score:.1f}/100")
        print(f"   - Errori: {len(validation_result.errors)}")
        print(f"   - Warning: {len(validation_result.warnings)}")
    
    def test_neuroglyphs_integration(self):
        """Test integrazione con simboli neuroglifi"""
        spec = create_demo_specification("Implementa logica condizionale")
        
        generated_code, reasoning_dag = self.synthesizer.synthesize_code(spec)
        
        # Verifica che i neuroglifi siano stati utilizzati
        used_neuroglyphs = set()
        for step in reasoning_dag.steps:
            used_neuroglyphs.update(step.neuroglyphs)
        
        self.assertGreater(len(used_neuroglyphs), 0)
        
        # Verifica che ci siano neuroglifi di diverse categorie
        categories = set()
        for neuroglyph in used_neuroglyphs:
            if neuroglyph.startswith("ng:"):
                category = neuroglyph.split(":")[1]
                categories.add(category)
        
        print(f"\n🧠 Neuroglyphs Integration:")
        print(f"   - Neuroglifi utilizzati: {len(used_neuroglyphs)}")
        print(f"   - Categorie: {list(categories)}")
        print(f"   - Esempi: {list(used_neuroglyphs)[:5]}")


class TestIntegrationDemo(unittest.TestCase):
    """Test di integrazione completa"""
    
    def test_complete_workflow(self):
        """Test workflow completo: specifica → ragionamento → codice → validazione"""
        print("\n🧠 NEUROGLYPH LLM - Test Workflow Completo")
        print("=" * 60)
        
        # FASE 1: Creazione specifica
        spec = CodeSpecification(
            goal="Implementa una funzione per calcolare il fattoriale",
            input_types=["int"],
            output_type="int",
            constraints=["recursive", "efficient"],
            test_cases=[
                {"input": 0, "expected": 1},
                {"input": 1, "expected": 1},
                {"input": 5, "expected": 120}
            ]
        )
        
        print(f"🎯 Obiettivo: {spec.goal}")
        
        # FASE 2: Generazione codice
        synthesizer = SOCRATECodeSynthesizer()
        start_time = time.time()
        
        generated_code, reasoning_dag = synthesizer.synthesize_code(spec)
        generation_time = time.time() - start_time
        
        print(f"⏱️ Tempo generazione: {generation_time:.3f}s")
        
        # FASE 3: Validazione
        validator = ASTValidator()
        validation_result = validator.validate(generated_code)
        
        # FASE 4: Correzione se necessaria
        if not validation_result.is_valid:
            corrector = ASTCorrector()
            generated_code, validation_result = corrector.correct(generated_code)
        
        # FASE 5: Risultati finali
        print(f"\n✅ CODICE GENERATO:")
        print("-" * 40)
        print(generated_code)
        print("-" * 40)
        
        print(f"\n📊 RISULTATI:")
        print(f"   - DAG steps: {len(reasoning_dag.steps)}")
        print(f"   - DAG valid: {reasoning_dag.is_valid}")
        print(f"   - Validation score: {validation_result.score:.1f}/100")
        print(f"   - Code valid: {validation_result.is_valid}")
        
        # Verifica successo
        self.assertTrue(reasoning_dag.is_valid)
        self.assertTrue(validation_result.is_valid)
        self.assertGreater(validation_result.score, 70.0)
        
        print(f"\n🎉 Workflow completato con successo!")


def run_all_tests():
    """Esegue tutti i test"""
    print("🧠 NEUROGLYPH LLM - Test Suite Completa")
    print("=" * 60)
    
    # Crea test suite
    loader = unittest.TestLoader()
    suite = unittest.TestSuite()
    
    # Aggiungi test classes
    suite.addTests(loader.loadTestsFromTestCase(TestSOCRATECodeSynthesizer))
    suite.addTests(loader.loadTestsFromTestCase(TestIntegrationDemo))
    
    # Esegui test
    runner = unittest.TextTestRunner(verbosity=2)
    result = runner.run(suite)
    
    # Statistiche finali
    print(f"\n📊 RISULTATI FINALI:")
    print(f"   - Test eseguiti: {result.testsRun}")
    print(f"   - Successi: {result.testsRun - len(result.failures) - len(result.errors)}")
    print(f"   - Fallimenti: {len(result.failures)}")
    print(f"   - Errori: {len(result.errors)}")
    
    if result.wasSuccessful():
        print(f"\n🎉 TUTTI I TEST SUPERATI! NEUROGLYPH LLM FUNZIONA PERFETTAMENTE!")
    else:
        print(f"\n⚠️ Alcuni test falliti. Verificare implementazione.")
    
    return result.wasSuccessful()


if __name__ == "__main__":
    run_all_tests()
