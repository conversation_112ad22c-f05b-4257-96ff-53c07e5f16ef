#!/usr/bin/env python3
"""
NEUROGLYPH ULTRA - LLM Compatibility Tester
============================================

Testa la compatibilità reale dei simboli con tokenizer LLM comuni:
- tiktoken (OpenAI GPT)
- Transformers tokenizers (HuggingFace)
- Conta token per simbolo
- Verifica encoding/decoding
"""

import json
from typing import List, Dict, Any
import sys

def test_tiktoken_compatibility(symbols: List[Dict[str, Any]]) -> Dict[str, Any]:
    """Testa compatibilità con tiktoken (OpenAI)."""
    try:
        import tiktoken
        
        # Test con diversi encoding
        encodings = ["cl100k_base", "p50k_base", "r50k_base"]
        results = {
            "available": True,
            "encodings_tested": encodings,
            "symbol_costs": {},
            "problematic_symbols": [],
            "avg_cost": 0.0
        }
        
        for encoding_name in encodings:
            try:
                enc = tiktoken.get_encoding(encoding_name)
                costs = []
                
                for symbol_entry in symbols[:50]:  # Test primi 50 per velocità
                    symbol = symbol_entry.get("symbol", "")
                    if symbol:
                        tokens = enc.encode(symbol)
                        cost = len(tokens)
                        costs.append(cost)
                        
                        if cost > 3:  # Simbolo che costa troppi token
                            results["problematic_symbols"].append({
                                "symbol": symbol,
                                "cost": cost,
                                "encoding": encoding_name
                            })
                
                results["symbol_costs"][encoding_name] = {
                    "avg_cost": sum(costs) / len(costs) if costs else 0,
                    "max_cost": max(costs) if costs else 0,
                    "min_cost": min(costs) if costs else 0
                }
                
            except Exception as e:
                results["symbol_costs"][encoding_name] = {"error": str(e)}
        
        # Calcola costo medio generale
        all_costs = []
        for enc_data in results["symbol_costs"].values():
            if isinstance(enc_data, dict) and "avg_cost" in enc_data:
                all_costs.append(enc_data["avg_cost"])
        
        results["avg_cost"] = sum(all_costs) / len(all_costs) if all_costs else 0
        
        return results
        
    except ImportError:
        return {
            "available": False,
            "error": "tiktoken non installato (pip install tiktoken)"
        }

def test_transformers_compatibility(symbols: List[Dict[str, Any]]) -> Dict[str, Any]:
    """Testa compatibilità con tokenizer Transformers."""
    try:
        from transformers import AutoTokenizer
        
        # Tokenizer comuni da testare
        tokenizer_names = [
            "microsoft/DialoGPT-medium",
            "gpt2", 
            "facebook/opt-350m"
        ]
        
        results = {
            "available": True,
            "tokenizers_tested": [],
            "symbol_costs": {},
            "problematic_symbols": [],
            "avg_cost": 0.0
        }
        
        for tokenizer_name in tokenizer_names:
            try:
                tokenizer = AutoTokenizer.from_pretrained(tokenizer_name)
                costs = []
                
                for symbol_entry in symbols[:30]:  # Test primi 30
                    symbol = symbol_entry.get("symbol", "")
                    if symbol:
                        tokens = tokenizer.encode(symbol, add_special_tokens=False)
                        cost = len(tokens)
                        costs.append(cost)
                        
                        if cost > 3:
                            results["problematic_symbols"].append({
                                "symbol": symbol,
                                "cost": cost,
                                "tokenizer": tokenizer_name
                            })
                
                results["symbol_costs"][tokenizer_name] = {
                    "avg_cost": sum(costs) / len(costs) if costs else 0,
                    "max_cost": max(costs) if costs else 0,
                    "min_cost": min(costs) if costs else 0
                }
                results["tokenizers_tested"].append(tokenizer_name)
                
            except Exception as e:
                results["symbol_costs"][tokenizer_name] = {"error": str(e)}
        
        # Calcola costo medio
        all_costs = []
        for tok_data in results["symbol_costs"].values():
            if isinstance(tok_data, dict) and "avg_cost" in tok_data:
                all_costs.append(tok_data["avg_cost"])
        
        results["avg_cost"] = sum(all_costs) / len(all_costs) if all_costs else 0
        
        return results
        
    except ImportError:
        return {
            "available": False,
            "error": "transformers non installato (pip install transformers)"
        }

def test_unicode_properties(symbols: List[Dict[str, Any]]) -> Dict[str, Any]:
    """Testa proprietà Unicode dei simboli."""
    import unicodedata
    
    results = {
        "categories": {},
        "scripts": {},
        "problematic_symbols": [],
        "emoji_count": 0,
        "math_symbols": 0,
        "geometric_shapes": 0
    }
    
    for symbol_entry in symbols:
        symbol = symbol_entry.get("symbol", "")
        if not symbol:
            continue
            
        for char in symbol:
            try:
                # Categoria Unicode
                category = unicodedata.category(char)
                results["categories"][category] = results["categories"].get(category, 0) + 1
                
                # Script Unicode
                script = unicodedata.name(char, "UNKNOWN").split()[0] if unicodedata.name(char, "") else "UNKNOWN"
                results["scripts"][script] = results["scripts"].get(script, 0) + 1
                
                # Conteggi specifici
                code_point = ord(char)
                if 0x1F000 <= code_point <= 0x1F9FF:  # Emoji range
                    results["emoji_count"] += 1
                elif 0x2200 <= code_point <= 0x22FF:  # Mathematical operators
                    results["math_symbols"] += 1
                elif 0x25A0 <= code_point <= 0x25FF:  # Geometric shapes
                    results["geometric_shapes"] += 1
                
                # Simboli potenzialmente problematici
                if category.startswith("C"):  # Control characters
                    results["problematic_symbols"].append({
                        "symbol": char,
                        "issue": f"Control character (category: {category})"
                    })
                
            except Exception as e:
                results["problematic_symbols"].append({
                    "symbol": char,
                    "issue": f"Unicode error: {e}"
                })
    
    return results

def generate_compatibility_report(symbols: List[Dict[str, Any]]) -> str:
    """Genera report di compatibilità LLM."""
    report = []
    report.append("🧠 NEUROGLYPH ULTRA - LLM Compatibility Report")
    report.append("=" * 60)
    report.append(f"📊 Testing {len(symbols)} symbols")
    report.append("")
    
    # Test tiktoken
    print("🔍 Testing tiktoken compatibility...")
    tiktoken_results = test_tiktoken_compatibility(symbols)
    report.append("🔹 tiktoken (OpenAI) Compatibility:")
    if tiktoken_results.get("available"):
        report.append(f"  ✅ Available: {tiktoken_results['available']}")
        report.append(f"  📊 Average token cost: {tiktoken_results.get('avg_cost', 0):.2f}")
        report.append(f"  ⚠️ Problematic symbols: {len(tiktoken_results.get('problematic_symbols', []))}")
        
        for encoding, data in tiktoken_results.get("symbol_costs", {}).items():
            if isinstance(data, dict) and "avg_cost" in data:
                report.append(f"    {encoding}: avg={data['avg_cost']:.2f}, max={data['max_cost']}")
    else:
        report.append(f"  ❌ Not available: {tiktoken_results.get('error', 'Unknown error')}")
    
    report.append("")
    
    # Test transformers
    print("🔍 Testing transformers compatibility...")
    transformers_results = test_transformers_compatibility(symbols)
    report.append("🔹 Transformers (HuggingFace) Compatibility:")
    if transformers_results.get("available"):
        report.append(f"  ✅ Available: {transformers_results['available']}")
        report.append(f"  📊 Average token cost: {transformers_results.get('avg_cost', 0):.2f}")
        report.append(f"  ⚠️ Problematic symbols: {len(transformers_results.get('problematic_symbols', []))}")
        report.append(f"  🧪 Tokenizers tested: {len(transformers_results.get('tokenizers_tested', []))}")
    else:
        report.append(f"  ❌ Not available: {transformers_results.get('error', 'Unknown error')}")
    
    report.append("")
    
    # Test Unicode properties
    print("🔍 Testing Unicode properties...")
    unicode_results = test_unicode_properties(symbols)
    report.append("🔹 Unicode Properties:")
    report.append(f"  📊 Emoji symbols: {unicode_results['emoji_count']}")
    report.append(f"  📊 Math symbols: {unicode_results['math_symbols']}")
    report.append(f"  📊 Geometric shapes: {unicode_results['geometric_shapes']}")
    report.append(f"  ⚠️ Problematic symbols: {len(unicode_results['problematic_symbols'])}")
    
    # Top categories
    top_categories = sorted(unicode_results['categories'].items(), key=lambda x: x[1], reverse=True)[:5]
    report.append("  📈 Top Unicode categories:")
    for category, count in top_categories:
        report.append(f"    {category}: {count}")
    
    report.append("")
    
    # Raccomandazioni
    report.append("🎯 Recommendations:")
    
    total_problematic = (
        len(tiktoken_results.get('problematic_symbols', [])) +
        len(transformers_results.get('problematic_symbols', [])) +
        len(unicode_results.get('problematic_symbols', []))
    )
    
    if total_problematic == 0:
        report.append("  ✅ All symbols appear LLM-compatible!")
    elif total_problematic < 10:
        report.append(f"  ⚠️ {total_problematic} symbols may need review")
    else:
        report.append(f"  ❌ {total_problematic} symbols have compatibility issues")
    
    avg_cost = (tiktoken_results.get('avg_cost', 0) + transformers_results.get('avg_cost', 0)) / 2
    if avg_cost <= 1.5:
        report.append("  ✅ Token cost is efficient")
    elif avg_cost <= 3.0:
        report.append("  ⚠️ Token cost is moderate")
    else:
        report.append("  ❌ Token cost is high - consider optimization")
    
    return "\n".join(report)

def main():
    """Esegue test di compatibilità LLM."""
    print("🧠 NEUROGLYPH ULTRA - LLM Compatibility Testing")
    print("=" * 60)
    
    # Carica simboli
    try:
        with open("core/symbols.json", "r", encoding="utf-8") as f:
            symbols = json.load(f)
        print(f"📊 Loaded {len(symbols)} symbols")
    except Exception as e:
        print(f"❌ Error loading symbols: {e}")
        return False
    
    # Genera report
    report = generate_compatibility_report(symbols)
    print(report)
    
    # Salva report
    with open("llm_compatibility_report.txt", "w", encoding="utf-8") as f:
        f.write(report)
    print(f"\n💾 Report saved to: llm_compatibility_report.txt")
    
    return True

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
