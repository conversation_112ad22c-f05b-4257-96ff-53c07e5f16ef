#!/usr/bin/env python3
"""
NEUROGLYPH GOD MODE REAL MODEL TESTER
=====================================

Testa il modello NEUROGLYPH GOD MODE fine-tuned con 9,236 simboli unicode ULTIMATE,
verificando le capacità di ragionamento simbolico avanzate e l'uso corretto dei token.

Questo script:
1. Carica il modello NEUROGLYPH GOD MODE fine-tuned (o base)
2. <PERSON>ica il tokenizer con 9,236 simboli NEUROGLYPH ULTIMATE  
3. Testa ragionamento simbolico cognitivo con simboli reali
4. Valida l'output contro il SOCRATE engine
5. Misura performance simbolica e meta-cognitive
6. Verifica zero splitting e atomicità simboli

Autore: NEUROGLYPH ULTRA Team
Data: 2025-01-26
Versione: GOD MODE v1.0
"""

import os
import sys
import torch
import json
import time
from datetime import datetime
from pathlib import Path
from typing import Dict, List, Any, Optional

# Add NEUROGLYPH paths
current_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.append(current_dir)

try:
    from transformers import AutoTokenizer, AutoModelForCausalLM
    from neuroglyph_symbolic_interface import SymbolicReasoningInterface
    print("✅ Required libraries imported successfully")
except ImportError as e:
    print(f"❌ Failed to import required libraries: {e}")
    print("Install with: pip install transformers torch")
    sys.exit(1)

class NeuroglyphGodModeTester:
    """Tester per il modello NEUROGLYPH GOD MODE reale."""
    
    def __init__(self):
        """Inizializza tester con modello e tokenizer NEUROGLYPH GOD MODE."""
        
        # Possibili paths per modello fine-tuned (in ordine di priorità)
        self.model_paths = [
            "/Volumes/DANIELE/NEUROGLYPH/neuroglyph_god_mode_final",  # Colab GOD MODE output
            "/Volumes/DANIELE/NEUROGLYPH/models/god_mode",           # Directory dedicata
            "/Volumes/DANIELE/NEUROGLYPH/models/base",               # Fallback base model
            "Qwen/Qwen2.5-Coder-1.5B-Instruct"                     # Fallback online
        ]
        
        # Paths per tokenizer e registry
        self.tokenizer_paths = [
            "/Volumes/DANIELE/NEUROGLYPH/neuroglyph/training/zero_splitting_tokenizer",
            "/Volumes/DANIELE/NEUROGLYPH/neuroglyph/training/colab_god_mode_package/zero_splitting_tokenizer"
        ]
        
        self.ultimate_registry_paths = [
            "/Volumes/DANIELE/NEUROGLYPH/neuroglyph/training/colab_god_mode_package/neuroglyph_ULTIMATE_registry.json",
            "/Volumes/DANIELE/NEUROGLYPH/neuroglyph_ULTIMATE_registry.json",
            "/Volumes/DANIELE/NEUROGLYPH/neuroglyph/core/locked_registry_godmode_v9.json"
        ]
        
        # Device detection
        self.device = self._detect_device()
        print(f"🔧 Using device: {self.device}")
        
        # Initialize symbolic interface
        self.symbolic_interface = SymbolicReasoningInterface()
        
        # Test state
        self.test_results = {
            "model_loaded": False,
            "tokenizer_loaded": False,
            "unicode_symbols_count": 0,
            "test_problems": [],
            "symbolic_accuracy": 0.0,
            "device": self.device,
            "timestamp": datetime.now().isoformat()
        }
        
        # Load model and tokenizer
        self.model = None
        self.tokenizer = None
        self._load_components()
        
    def _detect_device(self) -> str:
        """Rileva device ottimale."""
        if torch.cuda.is_available():
            return "cuda"
        elif hasattr(torch.backends, 'mps') and torch.backends.mps.is_available():
            return "mps"
        else:
            return "cpu"
    
    def _load_components(self):
        """Carica modello e tokenizer NEUROGLYPH."""
        print("🔄 Loading NEUROGLYPH model and tokenizer...")
        
        # Load tokenizer
        if os.path.exists(self.tokenizer_path):
            try:
                print(f"📥 Loading tokenizer from: {self.tokenizer_path}")
                self.tokenizer = AutoTokenizer.from_pretrained(self.tokenizer_path)
                self.test_results["tokenizer_loaded"] = True
                
                # Check unicode symbols
                vocab_size = len(self.tokenizer.get_vocab())
                print(f"   Tokenizer vocab size: {vocab_size}")
                
                # Check for unicode symbols
                unicode_count = self._count_unicode_symbols()
                self.test_results["unicode_symbols_count"] = unicode_count
                print(f"   Unicode symbols detected: {unicode_count}")
                
            except Exception as e:
                print(f"❌ Failed to load tokenizer: {e}")
                return
        else:
            print(f"❌ Tokenizer path not found: {self.tokenizer_path}")
            return
        
        # Load model
        if os.path.exists(self.model_path):
            try:
                print(f"📥 Loading model from: {self.model_path}")
                
                # Load with appropriate settings for device
                if self.device == "mps":
                    self.model = AutoModelForCausalLM.from_pretrained(
                        self.model_path,
                        torch_dtype=torch.float16,
                        device_map="auto"
                    )
                elif self.device == "cuda":
                    self.model = AutoModelForCausalLM.from_pretrained(
                        self.model_path,
                        torch_dtype=torch.float16,
                        device_map="auto"
                    )
                else:
                    self.model = AutoModelForCausalLM.from_pretrained(
                        self.model_path,
                        torch_dtype=torch.float32
                    )
                
                self.model.eval()
                self.test_results["model_loaded"] = True
                print(f"✅ Model loaded successfully")
                
            except Exception as e:
                print(f"❌ Failed to load model: {e}")
                print(f"   Error details: {type(e).__name__}: {e}")
                return
        else:
            print(f"❌ Model path not found: {self.model_path}")
            return
    
    def _count_unicode_symbols(self) -> int:
        """Conta simboli unicode nel tokenizer."""
        if not self.tokenizer:
            return 0
        
        unicode_count = 0
        vocab = self.tokenizer.get_vocab()
        
        for token in vocab.keys():
            # Check for mathematical and logical unicode symbols
            if any(ord(char) > 127 for char in token):
                # Specific check for mathematical/logical symbols
                if any(char in token for char in ['→', '∧', '∨', '¬', '∀', '∃', '⊢', '◊', '∴', '∵']):
                    unicode_count += 1
                elif any(0x2190 <= ord(char) <= 0x21FF for char in token):  # Arrows
                    unicode_count += 1
                elif any(0x2200 <= ord(char) <= 0x22FF for char in token):  # Mathematical operators
                    unicode_count += 1
                elif any(0x2300 <= ord(char) <= 0x23FF for char in token):  # Miscellaneous technical
                    unicode_count += 1
        
        return unicode_count
    
    def test_symbolic_reasoning(self) -> Dict[str, Any]:
        """Testa capacità di ragionamento simbolico del modello."""
        print("🧠 Testing NEUROGLYPH symbolic reasoning capabilities...")
        
        if not self.model or not self.tokenizer:
            print("❌ Model or tokenizer not loaded")
            return {"error": "Components not loaded"}
        
        # Test problems con simboli unicode
        test_problems = [
            {
                "problem": "If it rains, the ground gets wet. The ground is not wet. Did it rain?",
                "expected_symbols": ["→", "¬", "⊢"],
                "expected_pattern": "modus_tollens"
            },
            {
                "problem": "All humans are mortal. Socrates is human. Is Socrates mortal?",
                "expected_symbols": ["∀", "→", "⊢"],
                "expected_pattern": "universal_instantiation"
            },
            {
                "problem": "Either the system is secure or it has vulnerabilities. It's not secure. Does it have vulnerabilities?",
                "expected_symbols": ["∨", "¬", "⊢"],
                "expected_pattern": "disjunctive_syllogism"
            },
            {
                "problem": "If the server is online, then it responds to ping. The server is online. Does it respond to ping?",
                "expected_symbols": ["→", "⊢"],
                "expected_pattern": "modus_ponens"
            },
            {
                "problem": "All cats are mammals. All mammals are animals. Are all cats animals?",
                "expected_symbols": ["∀", "→", "⊢"],
                "expected_pattern": "hypothetical_syllogism"
            }
        ]
        
        results = []
        total_symbolic_score = 0.0
        
        for i, test_case in enumerate(test_problems):
            print(f"\\n🔍 Test {i+1}: {test_case['problem'][:50]}...")
            
            result = self._test_single_problem(test_case)
            results.append(result)
            total_symbolic_score += result.get("symbolic_score", 0.0)
            
            print(f"   Symbolic score: {result.get('symbolic_score', 0.0):.2f}")
            print(f"   Unicode symbols used: {result.get('unicode_symbols_used', [])}")
            print(f"   Pattern detected: {result.get('pattern_detected', 'none')}")
        
        avg_symbolic_score = total_symbolic_score / len(results) if results else 0.0
        
        test_summary = {
            "total_problems": len(test_problems),
            "results": results,
            "average_symbolic_score": avg_symbolic_score,
            "unicode_tokenizer_working": self.test_results["unicode_symbols_count"] > 0,
            "model_performance": "excellent" if avg_symbolic_score > 0.8 else "good" if avg_symbolic_score > 0.6 else "needs_improvement"
        }
        
        self.test_results["test_problems"] = results
        self.test_results["symbolic_accuracy"] = avg_symbolic_score
        
        return test_summary
    
    def _test_single_problem(self, test_case: Dict[str, Any]) -> Dict[str, Any]:
        """Testa singolo problema di ragionamento."""
        problem = test_case["problem"]
        expected_symbols = test_case["expected_symbols"]
        expected_pattern = test_case["expected_pattern"]
        
        try:
            # Generate response using NEUROGLYPH
            response = self._generate_response(problem)
            
            # Analyze response
            analysis = self._analyze_response(response, expected_symbols, expected_pattern)
            
            # Get symbolic solution for comparison
            symbolic_repr = self.symbolic_interface.parse_natural_language(problem)
            symbolic_solution = self.symbolic_interface.solve_symbolically(symbolic_repr)
            
            # Validate against symbolic interface
            validation = self.symbolic_interface.validate_reasoning_output(response, symbolic_solution)
            
            result = {
                "problem": problem,
                "response": response,
                "expected_symbols": expected_symbols,
                "expected_pattern": expected_pattern,
                "unicode_symbols_used": analysis["unicode_symbols_found"],
                "pattern_detected": analysis["pattern_detected"],
                "symbolic_score": validation["logic_score"],
                "reasoning_steps": analysis["reasoning_steps"],
                "has_conclusion": analysis["has_conclusion"],
                "symbolic_validation": validation
            }
            
            return result
            
        except Exception as e:
            print(f"   ⚠️ Error testing problem: {e}")
            return {
                "problem": problem,
                "error": str(e),
                "symbolic_score": 0.0
            }
    
    def _generate_response(self, problem: str, max_length: int = 512) -> str:
        """Genera risposta usando il modello NEUROGLYPH."""
        
        # Format input using chat template if available
        if hasattr(self.tokenizer, 'chat_template') and self.tokenizer.chat_template:
            messages = [
                {"role": "user", "content": problem}
            ]
            formatted_input = self.tokenizer.apply_chat_template(messages, tokenize=False, add_generation_prompt=True)
        else:
            # Fallback formatting
            formatted_input = f"<|im_start|>user\\n{problem}<|im_end|>\\n<|im_start|>assistant\\n"
        
        # Tokenize
        inputs = self.tokenizer(formatted_input, return_tensors="pt")
        
        # Move to device
        if self.device != "cpu":
            inputs = {k: v.to(self.device) for k, v in inputs.items()}
        
        # Generate
        with torch.no_grad():
            outputs = self.model.generate(
                **inputs,
                max_new_tokens=max_length,
                temperature=0.7,
                do_sample=True,
                pad_token_id=self.tokenizer.eos_token_id,
                eos_token_id=self.tokenizer.eos_token_id
            )
        
        # Decode response
        response = self.tokenizer.decode(outputs[0], skip_special_tokens=True)
        
        # Extract only the assistant's response
        if "<|im_start|>assistant" in response:
            response = response.split("<|im_start|>assistant")[-1]
        if "<|im_end|>" in response:
            response = response.split("<|im_end|>")[0]
        
        return response.strip()
    
    def _analyze_response(self, response: str, expected_symbols: List[str], expected_pattern: str) -> Dict[str, Any]:
        """Analizza la risposta del modello."""
        
        analysis = {
            "unicode_symbols_found": [],
            "pattern_detected": "none",
            "reasoning_steps": 0,
            "has_conclusion": False,
            "logical_structure_score": 0.0
        }
        
        # Check for unicode symbols
        for symbol in expected_symbols:
            if symbol in response:
                analysis["unicode_symbols_found"].append(symbol)
        
        # Check for additional logical symbols
        additional_symbols = ["◊", "∴", "∵", "∧", "∨", "⊢"]
        for symbol in additional_symbols:
            if symbol in response and symbol not in analysis["unicode_symbols_found"]:
                analysis["unicode_symbols_found"].append(symbol)
        
        # Count reasoning steps
        analysis["reasoning_steps"] = response.count("◊")
        
        # Check for conclusion
        analysis["has_conclusion"] = "⊢" in response or "therefore" in response.lower() or "∴" in response
        
        # Pattern detection
        if "modus ponens" in response.lower():
            analysis["pattern_detected"] = "modus_ponens"
        elif "modus tollens" in response.lower():
            analysis["pattern_detected"] = "modus_tollens"
        elif "disjunctive syllogism" in response.lower():
            analysis["pattern_detected"] = "disjunctive_syllogism"
        elif "universal instantiation" in response.lower():
            analysis["pattern_detected"] = "universal_instantiation"
        elif expected_pattern in response.lower():
            analysis["pattern_detected"] = expected_pattern
        
        # Calculate logical structure score
        score = 0.0
        if analysis["unicode_symbols_found"]:
            score += 0.4
        if analysis["reasoning_steps"] > 0:
            score += 0.3
        if analysis["has_conclusion"]:
            score += 0.3
        
        analysis["logical_structure_score"] = score
        
        return analysis
    
    def test_unicode_tokenization(self) -> Dict[str, Any]:
        """Testa tokenizzazione di simboli unicode."""
        print("🔤 Testing unicode symbol tokenization...")
        
        if not self.tokenizer:
            return {"error": "Tokenizer not loaded"}
        
        # Test symbols
        test_symbols = [
            "→", "∧", "∨", "¬", "∀", "∃", "⊢", "◊", "∴", "∵",
            "∈", "∉", "⊆", "⊇", "∩", "∪", "∅", "ℕ", "ℤ", "ℝ"
        ]
        
        tokenization_results = {}
        
        for symbol in test_symbols:
            try:
                tokens = self.tokenizer.tokenize(symbol)
                token_ids = self.tokenizer.encode(symbol, add_special_tokens=False)
                
                tokenization_results[symbol] = {
                    "tokens": tokens,
                    "token_ids": token_ids,
                    "single_token": len(tokens) == 1,
                    "correctly_tokenized": len(tokens) <= 2  # Allow some flexibility
                }
                
            except Exception as e:
                tokenization_results[symbol] = {
                    "error": str(e),
                    "correctly_tokenized": False
                }
        
        # Calculate success rate
        successful = sum(1 for result in tokenization_results.values() 
                        if result.get("correctly_tokenized", False))
        success_rate = successful / len(test_symbols)
        
        return {
            "test_symbols": test_symbols,
            "results": tokenization_results,
            "success_rate": success_rate,
            "successful_symbols": successful,
            "total_symbols": len(test_symbols)
        }
    
    def run_complete_test(self) -> Dict[str, Any]:
        """Esegue test completo del modello NEUROGLYPH."""
        print("🚀 Running complete NEUROGLYPH model test...")
        print("=" * 60)
        
        # Test 1: Unicode tokenization
        print("\\n📝 PHASE 1: Unicode Tokenization Test")
        unicode_results = self.test_unicode_tokenization()
        
        # Test 2: Symbolic reasoning
        print("\\n🧠 PHASE 2: Symbolic Reasoning Test")
        reasoning_results = self.test_symbolic_reasoning()
        
        # Compile final results
        final_results = {
            "model_info": {
                "model_path": self.model_path,
                "tokenizer_path": self.tokenizer_path,
                "device": self.device,
                "model_loaded": self.test_results["model_loaded"],
                "tokenizer_loaded": self.test_results["tokenizer_loaded"],
                "unicode_symbols_count": self.test_results["unicode_symbols_count"]
            },
            "unicode_tokenization": unicode_results,
            "symbolic_reasoning": reasoning_results,
            "overall_performance": self._calculate_overall_performance(unicode_results, reasoning_results),
            "timestamp": datetime.now().isoformat()
        }
        
        # Save results
        self._save_test_results(final_results)
        
        # Print summary
        self._print_test_summary(final_results)
        
        return final_results
    
    def _calculate_overall_performance(self, unicode_results: Dict, reasoning_results: Dict) -> Dict[str, Any]:
        """Calcola performance complessiva."""
        
        unicode_score = unicode_results.get("success_rate", 0.0)
        reasoning_score = reasoning_results.get("average_symbolic_score", 0.0)
        
        # Weighted average (reasoning more important)
        overall_score = (unicode_score * 0.3) + (reasoning_score * 0.7)
        
        performance_level = "excellent" if overall_score > 0.8 else "good" if overall_score > 0.6 else "needs_improvement"
        
        return {
            "unicode_score": unicode_score,
            "reasoning_score": reasoning_score,
            "overall_score": overall_score,
            "performance_level": performance_level,
            "ready_for_production": overall_score > 0.7
        }
    
    def _save_test_results(self, results: Dict[str, Any]):
        """Salva risultati dei test."""
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        results_file = f"neuroglyph_model_test_results_{timestamp}.json"
        
        try:
            with open(results_file, 'w') as f:
                json.dump(results, f, indent=2, ensure_ascii=False)
            print(f"\\n💾 Test results saved to: {results_file}")
        except Exception as e:
            print(f"\\n⚠️ Failed to save results: {e}")
    
    def _print_test_summary(self, results: Dict[str, Any]):
        """Stampa summary dei risultati."""
        print("\\n🎯 NEUROGLYPH MODEL TEST SUMMARY")
        print("=" * 60)
        
        model_info = results["model_info"]
        print(f"Model Loaded: {'✅' if model_info['model_loaded'] else '❌'}")
        print(f"Tokenizer Loaded: {'✅' if model_info['tokenizer_loaded'] else '❌'}")
        print(f"Unicode Symbols: {model_info['unicode_symbols_count']}")
        print(f"Device: {model_info['device']}")
        
        if "unicode_tokenization" in results:
            unicode_results = results["unicode_tokenization"]
            print(f"\\n📝 Unicode Tokenization:")
            print(f"  Success Rate: {unicode_results.get('success_rate', 0.0):.1%}")
            print(f"  Successful: {unicode_results.get('successful_symbols', 0)}/{unicode_results.get('total_symbols', 0)}")
        
        if "symbolic_reasoning" in results:
            reasoning_results = results["symbolic_reasoning"]
            print(f"\\n🧠 Symbolic Reasoning:")
            print(f"  Average Score: {reasoning_results.get('average_symbolic_score', 0.0):.2f}")
            print(f"  Problems Tested: {reasoning_results.get('total_problems', 0)}")
            print(f"  Performance: {reasoning_results.get('model_performance', 'unknown')}")
        
        if "overall_performance" in results:
            overall = results["overall_performance"]
            print(f"\\n🎯 Overall Performance:")
            print(f"  Overall Score: {overall['overall_score']:.2f}")
            print(f"  Performance Level: {overall['performance_level']}")
            print(f"  Production Ready: {'✅' if overall['ready_for_production'] else '❌'}")
        
        print("=" * 60)

def main():
    """Entry point per test del modello NEUROGLYPH."""
    print("🧠 NEUROGLYPH REAL MODEL TESTER")
    print("Testing fine-tuned model with unicode symbols")
    print("=" * 50)
    
    try:
        # Initialize tester
        tester = NeuroglyphModelTester()
        
        # Run complete test
        results = tester.run_complete_test()
        
        # Exit with success code if performance is good
        overall_performance = results.get("overall_performance", {})
        success = overall_performance.get("ready_for_production", False)
        
        sys.exit(0 if success else 1)
        
    except KeyboardInterrupt:
        print("\\n⚠️ Test interrupted by user")
        sys.exit(1)
    except Exception as e:
        print(f"\\n❌ Fatal error: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
