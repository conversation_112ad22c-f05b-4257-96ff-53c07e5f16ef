#!/usr/bin/env python3
"""
NEUROGLYPH GOD MODE COMPREHENSIVE TESTER
========================================

Testa il modello NEUROGLYPH GOD MODE fine-tuned con 9,236 simboli ULTIMATE,
verificando le capacità avanzate di ragionamento simbolico e meta-cognizione.

Features:
1. Auto-detection del modello fine-tuned vs base
2. Testing completo con 9,236 simboli ULTIMATE
3. Verifica zero splitting atomicity  
4. Testing capacità cognitive avanzate
5. Integrazione SOCRATE engine
6. Benchmark performance simbolica
7. Validazione meta-reasoning

Autore: NEUROGLYPH ULTRA Team
Data: 2025-01-26
Versione: GOD MODE v2.0
"""

import os
import sys
import torch
import json
import time
import re
from datetime import datetime
from pathlib import Path
from typing import Dict, List, Any, Optional, Tuple
from dataclasses import dataclass
from rich.console import Console
from rich.table import Table
from rich.panel import Panel
from rich.progress import track

# Add NEUROGLYPH paths
current_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.append(current_dir)

try:
    from transformers import AutoTokenizer, AutoModelForCausalLM, TextStreamer
    from neuroglyph_symbolic_interface import SymbolicReasoningInterface
    print("✅ Required libraries imported successfully")
except ImportError as e:
    print(f"❌ Failed to import required libraries: {e}")
    print("Install with: pip install transformers torch rich")
    sys.exit(1)

console = Console()

@dataclass
class GodModeTestResult:
    """Risultato di un singolo test GOD MODE."""
    problem: str
    model_output: str
    symbolic_score: float
    unicode_symbols_used: List[str]
    pattern_detected: str
    reasoning_chain_present: bool
    meta_cognition_present: bool
    atomicity_score: float
    execution_time: float
    socrate_validation: Dict[str, Any]

class NeuroglyphGodModeTester:
    """Tester completo per NEUROGLYPH GOD MODE."""
    
    def __init__(self):
        """Inizializza tester GOD MODE."""
        console.print(Panel.fit("🧠 NEUROGLYPH GOD MODE TESTER", style="bold blue"))
        
        # Possibili paths per modello fine-tuned (in ordine di priorità)
        self.model_paths = [
            "/Volumes/DANIELE/NEUROGLYPH/neuroglyph/models/Neuroglypgh_god_mode_final",
            "/Volumes/DANIELE/NEUROGLYPH/neuroglyph_god_mode_final",
            "/Volumes/DANIELE/NEUROGLYPH/models/god_mode",
            "/Volumes/DANIELE/NEUROGLYPH/models/neuroglyph_god_mode",
            "/Volumes/DANIELE/NEUROGLYPH/models/base",
            "Qwen/Qwen2.5-Coder-1.5B-Instruct"
        ]
        
        # Paths per tokenizer e registry
        self.tokenizer_paths = [
            "/Volumes/DANIELE/NEUROGLYPH/neuroglyph/models/Neuroglypgh_god_mode_final",
            "/Volumes/DANIELE/NEUROGLYPH/neuroglyph/training/colab_god_mode_package/tokenizer",
            "/Volumes/DANIELE/NEUROGLYPH/neuroglyph/training/zero_splitting_tokenizer",
            "/Volumes/DANIELE/NEUROGLYPH/models/base"
        ]
        
        self.ultimate_registry_paths = [
            "/Volumes/DANIELE/NEUROGLYPH/neuroglyph/training/colab_god_mode_package/neuroglyph_ULTIMATE_registry.json",
            "/Volumes/DANIELE/NEUROGLYPH/neuroglyph_ULTIMATE_registry.json",
            "/Volumes/DANIELE/NEUROGLYPH/neuroglyph/core/locked_registry_godmode_v9.json"
        ]
        
        # Device detection
        self.device = self._detect_device()
        console.print(f"🔧 Device: {self.device}")
        
        # Initialize symbolic interface
        self.symbolic_interface = SymbolicReasoningInterface()
        
        # Test state
        self.test_results = {
            "model_type": "unknown",
            "model_loaded": False,
            "tokenizer_loaded": False,
            "ultimate_symbols_count": 0,
            "atomicity_rate": 0.0,
            "god_mode_detected": False,
            "test_problems": [],
            "cognitive_accuracy": 0.0,
            "meta_reasoning_score": 0.0,
            "device": self.device,
            "timestamp": datetime.now().isoformat()
        }
        
        # Components
        self.model = None
        self.tokenizer = None
        self.ultimate_registry = None
        
        # Load everything
        self._load_components()
        
    def _detect_device(self) -> str:
        """Rileva device ottimale per NEUROGLYPH."""
        if torch.cuda.is_available():
            device = "cuda"
            console.print(f"🚀 CUDA available: {torch.cuda.get_device_name()}")
        elif hasattr(torch.backends, 'mps') and torch.backends.mps.is_available():
            device = "mps"
            console.print("🍎 Metal Performance Shaders available (Apple Silicon)")
        else:
            device = "cpu"
            console.print("💾 Using CPU (slower but compatible)")
        
        return device
    
    def _load_components(self):
        """Carica tutti i componenti NEUROGLYPH GOD MODE."""
        console.print("\\n🔄 Loading NEUROGLYPH GOD MODE components...")
        
        # 1. Load ULTIMATE registry
        self._load_ultimate_registry()
        
        # 2. Load tokenizer
        self._load_tokenizer()
        
        # 3. Load model
        self._load_model()
        
        # 4. Analyze capabilities
        self._analyze_god_mode_capabilities()
    
    def _load_ultimate_registry(self):
        """Carica ULTIMATE registry con 9,236 simboli."""
        console.print("📦 Loading ULTIMATE registry...")
        
        for path in self.ultimate_registry_paths:
            if os.path.exists(path):
                try:
                    with open(path, 'r', encoding='utf-8') as f:
                        self.ultimate_registry = json.load(f)
                    
                    # Handle ULTIMATE registry format
                    if isinstance(self.ultimate_registry, dict):
                        if 'approved_symbols' in self.ultimate_registry:
                            symbols_count = len(self.ultimate_registry['approved_symbols'])
                        elif 'symbols' in self.ultimate_registry:
                            symbols_count = len(self.ultimate_registry['symbols'])
                        elif 'stats' in self.ultimate_registry and 'total_symbols' in self.ultimate_registry['stats']:
                            symbols_count = self.ultimate_registry['stats']['total_symbols']
                        else:
                            symbols_count = 0
                    elif isinstance(self.ultimate_registry, list):
                        symbols_count = len(self.ultimate_registry)
                    else:
                        symbols_count = 0
                    
                    console.print(f"✅ ULTIMATE registry loaded: {symbols_count:,} symbols")
                    console.print(f"   Registry type: {self.ultimate_registry.get('version', 'Unknown')}")
                    console.print(f"   Status: {self.ultimate_registry.get('status', 'Unknown')}")
                    
                    self.test_results["ultimate_symbols_count"] = symbols_count
                    return
                    
                except Exception as e:
                    console.print(f"⚠️ Failed to load registry from {path}: {e}")
                    continue
        
        console.print("❌ No ULTIMATE registry found")
    
    def _load_tokenizer(self):
        """Carica tokenizer con simboli NEUROGLYPH."""
        console.print("🔤 Loading NEUROGLYPH tokenizer...")
        
        for path in self.tokenizer_paths:
            if os.path.exists(path):
                try:
                    self.tokenizer = AutoTokenizer.from_pretrained(path)
                    vocab_size = len(self.tokenizer.get_vocab())
                    
                    console.print(f"✅ Tokenizer loaded from: {path}")
                    console.print(f"   Vocab size: {vocab_size:,}")
                    
                    # Test atomicity
                    atomicity = self._test_symbol_atomicity()
                    self.test_results["atomicity_rate"] = atomicity
                    self.test_results["tokenizer_loaded"] = True
                    
                    console.print(f"   Atomicity rate: {atomicity:.1f}%")
                    
                    if atomicity >= 95.0:
                        console.print("🎊 Excellent atomicity achieved!")
                    elif atomicity >= 90.0:
                        console.print("✅ Good atomicity")
                    else:
                        console.print("⚠️ Low atomicity - splitting detected")
                    
                    return
                    
                except Exception as e:
                    console.print(f"⚠️ Failed to load tokenizer from {path}: {e}")
                    continue
        
        console.print("❌ No NEUROGLYPH tokenizer found")
    
    def _load_model(self):
        """Carica modello NEUROGLYPH (fine-tuned o base)."""
        console.print("🧠 Loading NEUROGLYPH model...")

        for i, path in enumerate(self.model_paths):
            model_type = "fine-tuned" if i < 4 else "base"

            if path.startswith("Qwen/") or os.path.exists(path):
                try:
                    console.print(f"📥 Trying {model_type} model: {path}")

                    # Check if this is a LoRA adapter (fine-tuned model)
                    if model_type == "fine-tuned" and os.path.exists(os.path.join(path, "adapter_config.json")):
                        console.print("🔧 Detected LoRA adapter - loading with PEFT...")

                        try:
                            from peft import PeftModel, PeftConfig

                            # Load base model first
                            base_model_name = "Qwen/Qwen2.5-Coder-1.5B-Instruct"
                            console.print(f"📦 Loading base model: {base_model_name}")

                            if self.device == "mps":
                                base_model = AutoModelForCausalLM.from_pretrained(
                                    base_model_name,
                                    torch_dtype=torch.float16,
                                    device_map="auto",
                                    trust_remote_code=True
                                )
                            elif self.device == "cuda":
                                base_model = AutoModelForCausalLM.from_pretrained(
                                    base_model_name,
                                    torch_dtype=torch.float16,
                                    device_map="auto",
                                    trust_remote_code=True,
                                    load_in_4bit=True
                                )
                            else:
                                base_model = AutoModelForCausalLM.from_pretrained(
                                    base_model_name,
                                    torch_dtype=torch.float32,
                                    trust_remote_code=True
                                )

                            # Load LoRA adapter
                            console.print(f"🧠 Loading NEUROGLYPH LoRA adapter: {path}")
                            self.model = PeftModel.from_pretrained(base_model, path)

                        except ImportError:
                            console.print("⚠️ PEFT not available, trying direct load...")
                            # Fallback to direct loading
                            if self.device == "mps":
                                self.model = AutoModelForCausalLM.from_pretrained(
                                    path,
                                    torch_dtype=torch.float16,
                                    device_map="auto",
                                    trust_remote_code=True
                                )
                            elif self.device == "cuda":
                                self.model = AutoModelForCausalLM.from_pretrained(
                                    path,
                                    torch_dtype=torch.float16,
                                    device_map="auto",
                                    trust_remote_code=True,
                                    load_in_4bit=True
                                )
                            else:
                                self.model = AutoModelForCausalLM.from_pretrained(
                                    path,
                                    torch_dtype=torch.float32,
                                    trust_remote_code=True
                                )
                    else:
                        # Regular model loading
                        if self.device == "mps":
                            self.model = AutoModelForCausalLM.from_pretrained(
                                path,
                                torch_dtype=torch.float16,
                                device_map="auto",
                                trust_remote_code=True
                            )
                        elif self.device == "cuda":
                            self.model = AutoModelForCausalLM.from_pretrained(
                                path,
                                torch_dtype=torch.float16,
                                device_map="auto",
                                trust_remote_code=True,
                                load_in_4bit=True
                            )
                        else:
                            self.model = AutoModelForCausalLM.from_pretrained(
                                path,
                                torch_dtype=torch.float32,
                                trust_remote_code=True
                            )

                    self.model.eval()
                    self.test_results["model_loaded"] = True
                    self.test_results["model_type"] = model_type

                    console.print(f"✅ {model_type.title()} model loaded successfully!")

                    if model_type == "fine-tuned":
                        console.print("🎊 NEUROGLYPH GOD MODE model detected!")
                        self.test_results["god_mode_detected"] = True

                    return

                except Exception as e:
                    console.print(f"⚠️ Failed to load from {path}: {e}")
                    continue

        console.print("❌ No compatible model found")
    
    def _test_symbol_atomicity(self) -> float:
        """Testa atomicità simboli (zero splitting)."""
        if not self.tokenizer or not self.ultimate_registry:
            return 0.0
        
        # Estrai simboli dal registry
        if isinstance(self.ultimate_registry, list):
            test_symbols = [s.get('symbol', s) if isinstance(s, dict) else s 
                          for s in self.ultimate_registry[:100]]  # Test primi 100
        else:
            symbols = self.ultimate_registry.get('symbols', [])
            test_symbols = [s.get('symbol', s) if isinstance(s, dict) else s 
                          for s in symbols[:100]]
        
        atomic_count = 0
        total_tested = 0
        
        for symbol in test_symbols:
            if isinstance(symbol, str) and len(symbol.strip()) > 0:
                tokens = self.tokenizer.tokenize(symbol)
                if len(tokens) == 1:  # Atomic (non-split)
                    atomic_count += 1
                total_tested += 1
        
        return (atomic_count / total_tested * 100) if total_tested > 0 else 0.0
    
    def _analyze_god_mode_capabilities(self):
        """Analizza capacità GOD MODE rilevate."""
        console.print("\\n🔍 Analyzing NEUROGLYPH GOD MODE capabilities...")
        
        capabilities_table = Table(title="🧠 NEUROGLYPH Capabilities Detected")
        capabilities_table.add_column("Component", style="cyan")
        capabilities_table.add_column("Status", style="green")
        capabilities_table.add_column("Details", style="white")
        
        # Model
        model_status = "✅ Loaded" if self.test_results["model_loaded"] else "❌ Failed"
        model_type = self.test_results["model_type"].title()
        capabilities_table.add_row("Model", model_status, f"{model_type} model")
        
        # Tokenizer
        tokenizer_status = "✅ Loaded" if self.test_results["tokenizer_loaded"] else "❌ Failed"
        vocab_info = f"Vocab: {len(self.tokenizer.get_vocab()):,}" if self.tokenizer else "N/A"
        capabilities_table.add_row("Tokenizer", tokenizer_status, vocab_info)
        
        # ULTIMATE Registry
        registry_status = "✅ Loaded" if self.ultimate_registry else "❌ Missing"
        symbols_info = f"{self.test_results['ultimate_symbols_count']:,} symbols"
        capabilities_table.add_row("ULTIMATE Registry", registry_status, symbols_info)
        
        # Atomicity
        atomicity = self.test_results["atomicity_rate"]
        atomicity_status = "🎊 Perfect" if atomicity >= 98 else "✅ Good" if atomicity >= 90 else "⚠️ Low"
        capabilities_table.add_row("Zero Splitting", atomicity_status, f"{atomicity:.1f}% atomic")
        
        # GOD MODE
        god_mode_status = "🎊 ACTIVE" if self.test_results["god_mode_detected"] else "📦 Base Model"
        god_mode_info = "Fine-tuned detected" if self.test_results["god_mode_detected"] else "Using base model"
        capabilities_table.add_row("GOD MODE", god_mode_status, god_mode_info)
        
        console.print(capabilities_table)
        
        # Summary
        if all([self.test_results["model_loaded"], 
                self.test_results["tokenizer_loaded"], 
                self.test_results["atomicity_rate"] >= 90]):
            console.print("\\n🎊 [bold green]NEUROGLYPH system ready for testing![/bold green]")
        else:
            console.print("\\n⚠️ [bold yellow]System partially ready - some components missing[/bold yellow]")
    
    def test_god_mode_reasoning(self) -> Dict[str, Any]:
        """Testa capacità avanzate di ragionamento GOD MODE."""
        console.print("\\n🧠 Testing NEUROGLYPH GOD MODE reasoning...")
        
        if not self.model or not self.tokenizer:
            console.print("❌ Cannot test - model or tokenizer not loaded")
            return {"error": "Components not loaded"}
        
        # Test problems progressivi per GOD MODE
        god_mode_tests = [
            # Livello 1: Logica Formale Base
            {
                "category": "Formal Logic",
                "problem": "If it rains, the ground gets wet. The ground is not wet. What can we conclude?",
                "expected_symbols": ["→", "¬", "⊢"],
                "expected_pattern": "modus_tollens",
                "complexity": 0.3
            },
            
            # Livello 2: Ragionamento Multi-Step
            {
                "category": "Multi-Step Reasoning", 
                "problem": "All birds can fly. Penguins are birds. Penguins cannot fly. Identify the logical inconsistency.",
                "expected_symbols": ["∀", "→", "¬", "⊥"],
                "expected_pattern": "contradiction",
                "complexity": 0.6
            },
            
            # Livello 3: Meta-Cognizione
            {
                "category": "Meta-Cognition",
                "problem": "Analyze your own reasoning process: How do you determine if a logical argument is valid?",
                "expected_symbols": ["◊", "⟨", "⟩", "∴"],
                "expected_pattern": "meta_reasoning",
                "complexity": 0.8
            },
            
            # Livello 4: Symbolic Compression
            {
                "category": "Symbolic Compression",
                "problem": "Express the concept 'recursive function that computes Fibonacci numbers' using minimal symbolic representation.",
                "expected_symbols": ["λ", "ℕ", "→", "∃"],
                "expected_pattern": "symbolic_compression", 
                "complexity": 0.9
            },
            
            # Livello 5: GOD MODE Challenge
            {
                "category": "GOD MODE Challenge",
                "problem": "Prove that your reasoning is self-consistent and explain the meta-logical foundations of your proof.",
                "expected_symbols": ["⊢", "⊨", "◊", "∴", "∵"],
                "expected_pattern": "self_reflection",
                "complexity": 1.0
            }
        ]
        
        results = []
        total_cognitive_score = 0.0
        total_meta_score = 0.0
        
        for i, test_case in enumerate(track(god_mode_tests, description="Testing GOD MODE...")):
            console.print(f"\\n🔍 [{test_case['category']}] {test_case['problem'][:60]}...")
            
            result = self._test_single_god_mode_problem(test_case)
            results.append(result)
            
            total_cognitive_score += result.symbolic_score
            total_meta_score += result.meta_cognition_present * 1.0
            
            # Display result
            console.print(f"   Symbolic Score: {result.symbolic_score:.2f}")
            console.print(f"   Symbols Used: {result.unicode_symbols_used}")
            console.print(f"   Meta-Cognition: {'✅' if result.meta_cognition_present else '❌'}")
            console.print(f"   Reasoning Chain: {'✅' if result.reasoning_chain_present else '❌'}")
        
        # Calculate final scores
        avg_cognitive_score = total_cognitive_score / len(results) if results else 0.0
        avg_meta_score = total_meta_score / len(results) if results else 0.0
        
        self.test_results["test_problems"] = results
        self.test_results["cognitive_accuracy"] = avg_cognitive_score
        self.test_results["meta_reasoning_score"] = avg_meta_score
        
        # Display summary
        self._display_god_mode_summary(avg_cognitive_score, avg_meta_score, results)
        
        return {
            "total_problems": len(god_mode_tests),
            "results": [self._result_to_dict(r) for r in results],
            "cognitive_accuracy": avg_cognitive_score,
            "meta_reasoning_score": avg_meta_score,
            "god_mode_performance": self._evaluate_god_mode_performance(avg_cognitive_score, avg_meta_score),
            "atomicity_rate": self.test_results["atomicity_rate"],
            "model_type": self.test_results["model_type"]
        }
    
    def _test_single_god_mode_problem(self, test_case: Dict[str, Any]) -> GodModeTestResult:
        """Testa singolo problema GOD MODE."""
        start_time = time.time()
        
        # Prepara prompt per NEUROGLYPH
        prompt = self._create_neuroglyph_prompt(test_case["problem"])
        
        # Generate response
        try:
            inputs = self.tokenizer(prompt, return_tensors="pt")
            if self.device != "cpu":
                inputs = {k: v.to(self.device) for k, v in inputs.items()}
            
            with torch.no_grad():
                outputs = self.model.generate(
                    **inputs,
                    max_new_tokens=256,
                    temperature=0.1,
                    do_sample=True,
                    pad_token_id=self.tokenizer.eos_token_id,
                    repetition_penalty=1.1
                )
            
            response = self.tokenizer.decode(outputs[0], skip_special_tokens=True)
            model_output = response[len(prompt):].strip()
            
        except Exception as e:
            console.print(f"❌ Error generating response: {e}")
            model_output = f"Error: {e}"
        
        execution_time = time.time() - start_time
        
        # Analyze response
        unicode_symbols = self._extract_unicode_symbols(model_output)
        symbolic_score = self._calculate_symbolic_score(model_output, test_case)
        pattern_detected = self._detect_reasoning_pattern(model_output)
        reasoning_chain = self._check_reasoning_chain(model_output)
        meta_cognition = self._check_meta_cognition(model_output)
        atomicity_score = self._check_atomicity_in_output(model_output)
        
        # SOCRATE validation if available
        socrate_validation = self._validate_with_socrate(test_case["problem"], model_output)
        
        return GodModeTestResult(
            problem=test_case["problem"],
            model_output=model_output,
            symbolic_score=symbolic_score,
            unicode_symbols_used=unicode_symbols,
            pattern_detected=pattern_detected,
            reasoning_chain_present=reasoning_chain,
            meta_cognition_present=meta_cognition,
            atomicity_score=atomicity_score,
            execution_time=execution_time,
            socrate_validation=socrate_validation
        )
    
    def _create_neuroglyph_prompt(self, problem: str) -> str:
        """Crea prompt ottimizzato per NEUROGLYPH."""
        return f"""<|im_start|>user
{problem}

Please use formal symbolic reasoning with unicode symbols (→, ∧, ∨, ¬, ∀, ∃, ⊢, ◊) and show your reasoning chain step by step.
<|im_end|>
<|im_start|>assistant
"""
    
    def _extract_unicode_symbols(self, text: str) -> List[str]:
        """Estrae simboli unicode dal testo."""
        unicode_symbols = []
        logical_symbols = ['→', '∧', '∨', '¬', '∀', '∃', '⊢', '◊', '∴', '∵', 'λ', '⟨', '⟩', '⊥', '⊨']
        
        for symbol in logical_symbols:
            if symbol in text:
                unicode_symbols.append(symbol)
        
        return unicode_symbols
    
    def _calculate_symbolic_score(self, output: str, test_case: Dict[str, Any]) -> float:
        """Calcola score simbolico."""
        score = 0.0
        
        # Presenza simboli attesi
        expected_symbols = test_case.get("expected_symbols", [])
        found_symbols = self._extract_unicode_symbols(output)
        
        if expected_symbols:
            symbol_coverage = len(set(found_symbols) & set(expected_symbols)) / len(expected_symbols)
            score += symbol_coverage * 0.4
        
        # Presenza catena di ragionamento
        if "◊" in output or "step" in output.lower() or "therefore" in output.lower():
            score += 0.3
        
        # Uso di operatori logici
        if any(symbol in output for symbol in ['→', '∧', '∨', '¬', '⊢']):
            score += 0.3
        
        return min(score, 1.0)
    
    def _detect_reasoning_pattern(self, output: str) -> str:
        """Rileva pattern di ragionamento."""
        patterns = {
            "modus_ponens": ["if.*then", "→.*therefore"],
            "modus_tollens": ["if.*then.*not", "→.*¬"],
            "meta_reasoning": ["reasoning", "analyze", "meta", "◊"],
            "contradiction": ["contradiction", "inconsistent", "⊥"],
            "symbolic_compression": ["minimal", "compress", "represent"]
        }
        
        output_lower = output.lower()
        for pattern_name, keywords in patterns.items():
            if any(keyword in output_lower for keyword in keywords):
                return pattern_name
        
        return "unknown"
    
    def _check_reasoning_chain(self, output: str) -> bool:
        """Controlla presenza catena di ragionamento."""
        chain_indicators = ["step", "therefore", "◊", "⊢", "given", "apply", "thus", "hence"]
        return any(indicator in output.lower() for indicator in chain_indicators)
    
    def _check_meta_cognition(self, output: str) -> bool:
        """Controlla presenza meta-cognizione."""
        meta_indicators = ["analyze", "reasoning", "think", "consider", "meta", "reflect", "process"]
        return any(indicator in output.lower() for indicator in meta_indicators)
    
    def _check_atomicity_in_output(self, output: str) -> float:
        """Controlla atomicità simboli nell'output."""
        if not self.tokenizer:
            return 0.0
        
        unicode_symbols = self._extract_unicode_symbols(output)
        if not unicode_symbols:
            return 1.0  # No symbols to check
        
        atomic_count = 0
        for symbol in unicode_symbols:
            tokens = self.tokenizer.tokenize(symbol)
            if len(tokens) == 1:
                atomic_count += 1
        
        return atomic_count / len(unicode_symbols) if unicode_symbols else 1.0
    
    def _validate_with_socrate(self, problem: str, output: str) -> Dict[str, Any]:
        """Valida con SOCRATE engine."""
        try:
            if self.symbolic_interface:
                symbolic_repr = self.symbolic_interface.parse_natural_language(problem)
                symbolic_solution = self.symbolic_interface.solve_symbolically(symbolic_repr)
                validation = self.symbolic_interface.validate_reasoning_output(output, symbolic_solution)

                return {
                    "validation_available": True,
                    "logic_score": validation.get("logic_score", 0.0),
                    "consistency_score": validation.get("consistency_score", 0.0),
                    "symbolic_chain_present": validation.get("symbolic_chain_present", False)
                }
        except Exception as e:
            console.print(f"⚠️ SOCRATE validation failed: {e}")

        return {"validation_available": False, "error": "SOCRATE not available"}


    
    def _display_god_mode_summary(self, cognitive_score: float, meta_score: float, results: List[GodModeTestResult]):
        """Mostra summary GOD MODE."""
        summary_table = Table(title="🎊 NEUROGLYPH GOD MODE Test Summary")
        summary_table.add_column("Metric", style="cyan")
        summary_table.add_column("Score", style="green") 
        summary_table.add_column("Status", style="white")
        
        # Cognitive accuracy
        cog_status = "🎊 Excellent" if cognitive_score >= 0.8 else "✅ Good" if cognitive_score >= 0.6 else "⚠️ Needs Improvement"
        summary_table.add_row("Cognitive Accuracy", f"{cognitive_score:.2%}", cog_status)
        
        # Meta-reasoning
        meta_status = "🎊 Advanced" if meta_score >= 0.8 else "✅ Present" if meta_score >= 0.4 else "❌ Limited"
        summary_table.add_row("Meta-Reasoning", f"{meta_score:.2%}", meta_status)
        
        # Atomicity
        atomicity = self.test_results["atomicity_rate"]
        atom_status = "🎊 Perfect" if atomicity >= 98 else "✅ Good" if atomicity >= 90 else "⚠️ Splitting"
        summary_table.add_row("Symbol Atomicity", f"{atomicity:.1f}%", atom_status)
        
        # Model type
        model_type = self.test_results["model_type"].title()
        model_status = "🎊 GOD MODE" if self.test_results["god_mode_detected"] else "📦 Base Model"
        summary_table.add_row("Model Type", model_type, model_status)
        
        # Overall performance
        overall_score = (cognitive_score + meta_score + atomicity/100) / 3
        overall_status = "🎊 GOD TIER" if overall_score >= 0.9 else "🌟 EXCELLENT" if overall_score >= 0.7 else "✅ GOOD" if overall_score >= 0.5 else "⚠️ DEVELOPING"
        summary_table.add_row("Overall Performance", f"{overall_score:.2%}", overall_status)
        
        console.print(summary_table)
        
        # Final verdict
        if overall_score >= 0.9:
            console.print("\\n🎊 [bold green]NEUROGLYPH GOD MODE ACHIEVEMENT UNLOCKED![/bold green]")
            console.print("🧠 First truly intelligent LLM capabilities confirmed!")
        elif overall_score >= 0.7:
            console.print("\\n🌟 [bold blue]EXCELLENT NEUROGLYPH PERFORMANCE![/bold blue]")
            console.print("🚀 Advanced symbolic reasoning capabilities detected!")
        elif overall_score >= 0.5:
            console.print("\\n✅ [bold yellow]GOOD NEUROGLYPH FOUNDATION![/bold yellow]")
            console.print("🔧 Solid base for symbolic reasoning with room for improvement")
        else:
            console.print("\\n⚠️ [bold red]NEUROGLYPH NEEDS IMPROVEMENT[/bold red]")
            console.print("🛠️ Consider additional training or model fine-tuning")
    
    def _evaluate_god_mode_performance(self, cognitive_score: float, meta_score: float) -> str:
        """Valuta performance GOD MODE."""
        overall = (cognitive_score + meta_score) / 2
        
        if overall >= 0.9:
            return "GOD_TIER"
        elif overall >= 0.8:
            return "EXCELLENT"
        elif overall >= 0.6:
            return "GOOD"
        elif overall >= 0.4:
            return "DEVELOPING"
        else:
            return "NEEDS_IMPROVEMENT"
    
    def _result_to_dict(self, result: GodModeTestResult) -> Dict[str, Any]:
        """Converte risultato in dict."""
        return {
            "problem": result.problem,
            "model_output": result.model_output,
            "symbolic_score": result.symbolic_score,
            "unicode_symbols_used": result.unicode_symbols_used,
            "pattern_detected": result.pattern_detected,
            "reasoning_chain_present": result.reasoning_chain_present,
            "meta_cognition_present": result.meta_cognition_present,
            "atomicity_score": result.atomicity_score,
            "execution_time": result.execution_time,
            "socrate_validation": result.socrate_validation
        }
    
    def save_test_results(self, filename: Optional[str] = None):
        """Salva risultati test."""
        if filename is None:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"neuroglyph_god_mode_test_{timestamp}.json"
        
        current_dir = os.path.dirname(os.path.abspath(__file__))
        filepath = os.path.join(current_dir, filename)
        
        try:
            with open(filepath, 'w', encoding='utf-8') as f:
                json.dump(self.test_results, f, indent=2, ensure_ascii=False)
            
            console.print(f"💾 Test results saved to: {filepath}")
            return filepath
            
        except Exception as e:
            console.print(f"❌ Failed to save results: {e}")
            return None
    
    def interactive_test(self):
        """Modalità test interattiva."""
        console.print("\\n🎮 NEUROGLYPH GOD MODE Interactive Testing")
        console.print("Enter 'quit' to exit, 'auto' for automated tests")
        
        while True:
            try:
                problem = input("\\n🧠 Enter reasoning problem: ").strip()
                
                if problem.lower() == 'quit':
                    break
                elif problem.lower() == 'auto':
                    self.test_god_mode_reasoning()
                    break
                elif problem:
                    # Test single problem
                    test_case = {
                        "problem": problem,
                        "expected_symbols": [],
                        "expected_pattern": "custom",
                        "complexity": 0.5
                    }
                    
                    result = self._test_single_god_mode_problem(test_case)
                    
                    console.print(f"\\n📝 Model Output:")
                    console.print(Panel(result.model_output, style="green"))
                    console.print(f"🔍 Symbolic Score: {result.symbolic_score:.2f}")
                    console.print(f"🔤 Unicode Symbols: {result.unicode_symbols_used}")
                    console.print(f"🧠 Meta-Cognition: {'✅' if result.meta_cognition_present else '❌'}")
                    
            except KeyboardInterrupt:
                break
            except Exception as e:
                console.print(f"❌ Error: {e}")
        
        console.print("\\n👋 NEUROGLYPH GOD MODE testing session ended!")

def main():
    """Main function."""
    console.print(Panel.fit("🧠 NEUROGLYPH GOD MODE TESTER v2.0", style="bold blue"))
    
    try:
        # Initialize tester
        tester = NeuroglyphGodModeTester()
        
        if not tester.test_results["model_loaded"]:
            console.print("❌ Cannot proceed - no model loaded")
            return
        
        # Menu
        console.print("\\n🎯 Choose testing mode:")
        console.print("1. 🚀 Automated GOD MODE Testing")
        console.print("2. 🎮 Interactive Testing")
        console.print("3. 📊 Quick Capabilities Check")
        
        choice = input("\\nChoice (1-3): ").strip()
        
        if choice == "1":
            results = tester.test_god_mode_reasoning()
            tester.save_test_results()
            
        elif choice == "2":
            tester.interactive_test()
            
        elif choice == "3":
            console.print("\\n📊 NEUROGLYPH Capabilities Summary:")
            console.print(f"   Model Type: {tester.test_results['model_type']}")
            console.print(f"   GOD MODE: {'✅' if tester.test_results['god_mode_detected'] else '❌'}")
            console.print(f"   Atomicity: {tester.test_results['atomicity_rate']:.1f}%")
            console.print(f"   Symbols: {tester.test_results['ultimate_symbols_count']:,}")
            
        else:
            console.print("Invalid choice")
            
    except KeyboardInterrupt:
        console.print("\\n\\n👋 NEUROGLYPH testing interrupted by user")
    except Exception as e:
        console.print(f"\\n❌ Unexpected error: {e}")
        console.print("Check your installation and try again")

if __name__ == "__main__":
    main()
