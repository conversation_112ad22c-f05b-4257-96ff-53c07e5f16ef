#!/usr/bin/env python3
"""
NEUROGLYPH GOD MODE COMPREHENSIVE TESTER
========================================

Testa il modello NEUROGLYPH GOD MODE fine-tuned con 9,236 simboli ULTIMATE,
verificando le capacità avanzate di ragionamento simbolico e meta-cognizione.

Autore: NEUROGLYPH ULTRA Team
Data: 2025-06-01
Versione: GOD MODE v2.0
"""

import os
import sys
import torch
import json
import time
import re
from datetime import datetime
from pathlib import Path
from typing import Dict, List, Any, Optional, Tuple
from dataclasses import dataclass
from rich.console import Console
from rich.table import Table
from rich.panel import Panel
from rich.progress import track

# Add NEUROGLYPH paths
current_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.append(current_dir)

try:
    from transformers import AutoTokenizer, AutoModelForCausalLM, TextStreamer
    from neuroglyph_symbolic_interface import SymbolicReasoningInterface
    print("✅ Required libraries imported successfully")
except ImportError as e:
    print(f"❌ Failed to import required libraries: {e}")
    print("Install with: pip install transformers torch rich")
    sys.exit(1)

console = Console()

@dataclass
class GodModeTestResult:
    """Risultato di un singolo test GOD MODE."""
    problem: str
    model_output: str
    symbolic_score: float
    unicode_symbols_used: List[str]
    pattern_detected: str
    reasoning_chain_present: bool
    meta_cognition_present: bool
    atomicity_score: float
    execution_time: float
    socrate_validation: Dict[str, Any]

class NeuroglyphGodModeTester:
    """Tester completo per NEUROGLYPH GOD MODE."""
    
    def __init__(self):
        """Inizializza tester GOD MODE."""
        console.print(Panel.fit("🧠 NEUROGLYPH GOD MODE TESTER", style="bold blue"))
        
        # Possibili paths per modello fine-tuned (in ordine di priorità)
        self.model_paths = [
            "/Volumes/DANIELE/NEUROGLYPH/neuroglyph_god_mode_final",
            "/Volumes/DANIELE/NEUROGLYPH/models/god_mode", 
            "/Volumes/DANIELE/NEUROGLYPH/models/neuroglyph_god_mode",
            "/Volumes/DANIELE/NEUROGLYPH/models/base",
            "Qwen/Qwen2.5-Coder-1.5B-Instruct"
        ]
        
        # Paths per tokenizer e registry
        self.tokenizer_paths = [
            "/Volumes/DANIELE/NEUROGLYPH/neuroglyph/training/colab_god_mode_package/tokenizer",
            "/Volumes/DANIELE/NEUROGLYPH/neuroglyph/training/zero_splitting_tokenizer",
            "/Volumes/DANIELE/NEUROGLYPH/models/base"
        ]
        
        self.ultimate_registry_paths = [
            "/Volumes/DANIELE/NEUROGLYPH/neuroglyph/training/colab_god_mode_package/neuroglyph_ULTIMATE_registry.json",
            "/Volumes/DANIELE/NEUROGLYPH/neuroglyph_ULTIMATE_registry.json",
            "/Volumes/DANIELE/NEUROGLYPH/neuroglyph/core/locked_registry_godmode_v9.json"
        ]
        
        # Device detection
        self.device = self._detect_device()
        console.print(f"🔧 Device: {self.device}")
        
        # Initialize symbolic interface
        try:
            self.symbolic_interface = SymbolicReasoningInterface()
        except Exception as e:
            console.print(f"⚠️ SOCRATE interface not available: {e}")
            self.symbolic_interface = None
        
        # Test state
        self.test_results = {
            "model_type": "unknown",
            "model_loaded": False,
            "tokenizer_loaded": False,
            "ultimate_symbols_count": 0,
            "atomicity_rate": 0.0,
            "god_mode_detected": False,
            "test_problems": [],
            "cognitive_accuracy": 0.0,
            "meta_reasoning_score": 0.0,
            "device": self.device,
            "timestamp": datetime.now().isoformat()
        }
        
        # Components
        self.model = None
        self.tokenizer = None
        self.ultimate_registry = None
        
        # Load everything
        self._load_components()
        
    def _detect_device(self) -> str:
        """Rileva device ottimale per NEUROGLYPH."""
        if torch.cuda.is_available():
            device = "cuda"
            console.print(f"🚀 CUDA available: {torch.cuda.get_device_name()}")
        elif hasattr(torch.backends, 'mps') and torch.backends.mps.is_available():
            device = "mps"
            console.print("🍎 Metal Performance Shaders available (Apple Silicon)")
        else:
            device = "cpu"
            console.print("💾 Using CPU (slower but compatible)")
        
        return device
    
    def _load_components(self):
        """Carica tutti i componenti NEUROGLYPH GOD MODE."""
        console.print("\n🔄 Loading NEUROGLYPH GOD MODE components...")
        
        # 1. Load ULTIMATE registry
        self._load_ultimate_registry()
        
        # 2. Load tokenizer
        self._load_tokenizer()
        
        # 3. Load model
        self._load_model()
        
        # 4. Analyze capabilities
        self._analyze_god_mode_capabilities()
    
    def _load_ultimate_registry(self):
        """Carica ULTIMATE registry con 9,236 simboli."""
        console.print("📦 Loading ULTIMATE registry...")
        
        for path in self.ultimate_registry_paths:
            if os.path.exists(path):
                try:
                    with open(path, 'r', encoding='utf-8') as f:
                        self.ultimate_registry = json.load(f)
                    
                    # Handle ULTIMATE registry format
                    if isinstance(self.ultimate_registry, dict):
                        if 'approved_symbols' in self.ultimate_registry:
                            symbols_count = len(self.ultimate_registry['approved_symbols'])
                        elif 'symbols' in self.ultimate_registry:
                            symbols_count = len(self.ultimate_registry['symbols'])
                        elif 'stats' in self.ultimate_registry and 'total_symbols' in self.ultimate_registry['stats']:
                            symbols_count = self.ultimate_registry['stats']['total_symbols']
                        else:
                            symbols_count = 0
                    elif isinstance(self.ultimate_registry, list):
                        symbols_count = len(self.ultimate_registry)
                    else:
                        symbols_count = 0
                    
                    console.print(f"✅ ULTIMATE registry loaded: {symbols_count:,} symbols")
                    console.print(f"   Registry type: {self.ultimate_registry.get('version', 'Unknown')}")
                    console.print(f"   Status: {self.ultimate_registry.get('status', 'Unknown')}")
                    
                    self.test_results["ultimate_symbols_count"] = symbols_count
                    return
                    
                except Exception as e:
                    console.print(f"⚠️ Failed to load registry from {path}: {e}")
                    continue
        
        console.print("❌ No ULTIMATE registry found")
    
    def _load_tokenizer(self):
        """Carica tokenizer con simboli NEUROGLYPH."""
        console.print("🔤 Loading NEUROGLYPH tokenizer...")
        
        for path in self.tokenizer_paths:
            if os.path.exists(path):
                try:
                    self.tokenizer = AutoTokenizer.from_pretrained(path)
                    vocab_size = len(self.tokenizer.get_vocab())
                    
                    console.print(f"✅ Tokenizer loaded from: {path}")
                    console.print(f"   Vocab size: {vocab_size:,}")
                    
                    # Test atomicity
                    atomicity = self._test_symbol_atomicity()
                    self.test_results["atomicity_rate"] = atomicity
                    self.test_results["tokenizer_loaded"] = True
                    
                    console.print(f"   Atomicity rate: {atomicity:.1f}%")
                    
                    if atomicity >= 95.0:
                        console.print("🎊 Excellent atomicity achieved!")
                    elif atomicity >= 90.0:
                        console.print("✅ Good atomicity")
                    else:
                        console.print("⚠️ Low atomicity - splitting detected")
                    
                    return
                    
                except Exception as e:
                    console.print(f"⚠️ Failed to load tokenizer from {path}: {e}")
                    continue
        
        console.print("❌ No NEUROGLYPH tokenizer found")
    
    def _load_model(self):
        """Carica modello NEUROGLYPH (fine-tuned o base)."""
        console.print("🧠 Loading NEUROGLYPH model...")
        
        for i, path in enumerate(self.model_paths):
            model_type = "fine-tuned" if i < 3 else "base"
            
            if path.startswith("Qwen/") or os.path.exists(path):
                try:
                    console.print(f"📥 Trying {model_type} model: {path}")
                    
                    # Load with appropriate settings
                    if self.device == "mps":
                        self.model = AutoModelForCausalLM.from_pretrained(
                            path,
                            torch_dtype=torch.float16,
                            device_map="auto",
                            trust_remote_code=True
                        )
                    elif self.device == "cuda":
                        self.model = AutoModelForCausalLM.from_pretrained(
                            path,
                            torch_dtype=torch.float16,
                            device_map="auto",
                            trust_remote_code=True,
                            load_in_4bit=True
                        )
                    else:
                        self.model = AutoModelForCausalLM.from_pretrained(
                            path,
                            torch_dtype=torch.float32,
                            trust_remote_code=True
                        )
                    
                    self.model.eval()
                    self.test_results["model_loaded"] = True
                    self.test_results["model_type"] = model_type
                    
                    console.print(f"✅ {model_type.title()} model loaded successfully!")
                    
                    if model_type == "fine-tuned":
                        console.print("🎊 NEUROGLYPH GOD MODE model detected!")
                        self.test_results["god_mode_detected"] = True
                    
                    return
                    
                except Exception as e:
                    console.print(f"⚠️ Failed to load from {path}: {e}")
                    continue
        
        console.print("❌ No compatible model found")
    
    def _test_symbol_atomicity(self) -> float:
        """Testa atomicità simboli (zero splitting)."""
        if not self.tokenizer or not self.ultimate_registry:
            return 0.0
        
        # Estrai simboli dal registry per test
        test_symbols = []
        if isinstance(self.ultimate_registry, dict) and 'approved_symbols' in self.ultimate_registry:
            # ULTIMATE registry format
            for symbol_entry in self.ultimate_registry['approved_symbols'][:100]:
                if isinstance(symbol_entry, dict) and 'symbol' in symbol_entry:
                    test_symbols.append(symbol_entry['symbol'])
        
        # Fallback per altri formati
        if not test_symbols:
            logical_symbols = ['→', '∧', '∨', '¬', '∀', '∃', '⊢', '◊', '∴', '∵', 'λ', '⟨', '⟩', '⊥', '⊨']
            test_symbols = logical_symbols
        
        atomic_count = 0
        total_tested = 0
        
        for symbol in test_symbols:
            if isinstance(symbol, str) and len(symbol.strip()) > 0:
                tokens = self.tokenizer.tokenize(symbol)
                if len(tokens) == 1:  # Atomic (non-split)
                    atomic_count += 1
                total_tested += 1
        
        return (atomic_count / total_tested * 100) if total_tested > 0 else 0.0
    
    def _analyze_god_mode_capabilities(self):
        """Analizza capacità GOD MODE rilevate."""
        console.print("\n🔍 Analyzing NEUROGLYPH GOD MODE capabilities...")
        
        capabilities_table = Table(title="🧠 NEUROGLYPH Capabilities Detected")
        capabilities_table.add_column("Component", style="cyan")
        capabilities_table.add_column("Status", style="green")
        capabilities_table.add_column("Details", style="white")
        
        # Model
        model_status = "✅ Loaded" if self.test_results["model_loaded"] else "❌ Failed"
        model_type = self.test_results["model_type"].title()
        capabilities_table.add_row("Model", model_status, f"{model_type} model")
        
        # Tokenizer
        tokenizer_status = "✅ Loaded" if self.test_results["tokenizer_loaded"] else "❌ Failed"
        vocab_info = f"Vocab: {len(self.tokenizer.get_vocab()):,}" if self.tokenizer else "N/A"
        capabilities_table.add_row("Tokenizer", tokenizer_status, vocab_info)
        
        # ULTIMATE Registry
        registry_status = "✅ Loaded" if self.ultimate_registry else "❌ Missing"
        symbols_info = f"{self.test_results['ultimate_symbols_count']:,} symbols"
        capabilities_table.add_row("ULTIMATE Registry", registry_status, symbols_info)
        
        # Atomicity
        atomicity = self.test_results["atomicity_rate"]
        atomicity_status = "🎊 Perfect" if atomicity >= 98 else "✅ Good" if atomicity >= 90 else "⚠️ Low"
        capabilities_table.add_row("Zero Splitting", atomicity_status, f"{atomicity:.1f}% atomic")
        
        # GOD MODE
        god_mode_status = "🎊 ACTIVE" if self.test_results["god_mode_detected"] else "📦 Base Model"
        god_mode_info = "Fine-tuned detected" if self.test_results["god_mode_detected"] else "Using base model"
        capabilities_table.add_row("GOD MODE", god_mode_status, god_mode_info)
        
        console.print(capabilities_table)
        
        # Summary
        if all([self.test_results["model_loaded"], 
                self.test_results["tokenizer_loaded"], 
                self.test_results["atomicity_rate"] >= 90]):
            console.print("\n🎊 [bold green]NEUROGLYPH system ready for testing![/bold green]")
        else:
            console.print("\n⚠️ [bold yellow]System partially ready - some components missing[/bold yellow]")

def main():
    """Main function."""
    console.print(Panel.fit("🧠 NEUROGLYPH GOD MODE TESTER v2.0", style="bold blue"))
    
    try:
        # Initialize tester
        tester = NeuroglyphGodModeTester()
        
        if not tester.test_results["model_loaded"]:
            console.print("❌ Cannot proceed - no model loaded")
            console.print("\n💡 Il modello fine-tuned non è presente localmente.")
            console.print("📝 Per continuare, devi:")
            console.print("   1. Scaricare il modello da Google Colab")
            console.print("   2. Salvarlo in: /Volumes/DANIELE/NEUROGLYPH/neuroglyph_god_mode_final")
            console.print("   3. Rieseguire questo tester")
            return
        
        console.print(f"\n🎯 NEUROGLYPH GOD MODE Status:")
        console.print(f"   Model Type: {tester.test_results['model_type']}")
        console.print(f"   GOD MODE: {'✅' if tester.test_results['god_mode_detected'] else '❌'}")
        console.print(f"   Atomicity: {tester.test_results['atomicity_rate']:.1f}%")
        console.print(f"   Symbols: {tester.test_results['ultimate_symbols_count']:,}")
            
    except KeyboardInterrupt:
        console.print("\n\n👋 NEUROGLYPH testing interrupted by user")
    except Exception as e:
        console.print(f"\n❌ Unexpected error: {e}")
        console.print("Check your installation and try again")

if __name__ == "__main__":
    main()
