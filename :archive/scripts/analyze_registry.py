#!/usr/bin/env python3
"""
NEUROGLYPH Registry Analysis & 8000 Symbol Expansion Strategy
Analizza il registry attuale e crea strategia per espansione semantica controllata
"""

import json
from collections import Counter, defaultdict
from pathlib import Path
import unicodedata

def analyze_registry():
    """Analizza il registry NEUROGLYPH attuale."""

    registry_path = "neuroglyph/core/locked_registry_godmode_v9.json"

    try:
        with open(registry_path, 'r', encoding='utf-8') as f:
            registry = json.load(f)
    except Exception as e:
        print(f"❌ Errore caricamento registry: {e}")
        return

    symbols = registry.get('approved_symbols', [])

    print("🧠 NEUROGLYPH Registry Analysis")
    print("=" * 50)
    print(f"📊 Current symbols: {len(symbols)}")
    print(f"🎯 Target: 8000 symbols")
    print(f"📈 Gap: {8000 - len(symbols)} symbols needed")
    print(f"📋 Expansion factor: {8000 / len(symbols):.2f}x")
    print()
    
    # Analisi categorie
    categories = Counter()
    for symbol in symbols:
        cat = symbol.get('category', 'unknown')
        categories[cat] += 1
    
    print("🏷️ Current Categories:")
    for cat, count in categories.most_common():
        percentage = (count / len(symbols)) * 100
        print(f"  {cat:15} {count:4} symbols ({percentage:5.1f}%)")
    print()
    
    # Analisi fallback patterns
    fallback_patterns = Counter()
    for symbol in symbols:
        fallback = symbol.get('fallback', '')
        if fallback.startswith('[') and fallback.endswith(']'):
            pattern = fallback[1:-1]
            if len(pattern) <= 8:
                fallback_patterns[pattern] += 1
    
    print("🔤 Top Fallback Patterns:")
    for pattern, count in fallback_patterns.most_common(15):
        print(f"  [{pattern:8}]: {count:3} symbols")
    print()
    
    # Analisi score distribution
    scores = [s.get('score', 0) for s in symbols if 'score' in s]
    if scores:
        avg_score = sum(scores) / len(scores)
        min_score = min(scores)
        max_score = max(scores)
        high_quality = len([s for s in scores if s >= 95])
        
        print("📈 Score Distribution:")
        print(f"  Average: {avg_score:.2f}")
        print(f"  Range: {min_score} - {max_score}")
        print(f"  High quality (≥95): {high_quality} ({(high_quality/len(scores)*100):.1f}%)")
        print()
    
    # Analisi Unicode blocks
    unicode_blocks = defaultdict(int)
    for symbol in symbols:
        unicode_point = symbol.get('unicode_point', '')
        if unicode_point.startswith('U+'):
            try:
                code_point = int(unicode_point[2:], 16)
                # Determina il blocco Unicode approssimativo
                if 0x0000 <= code_point <= 0x007F:
                    block = "Basic Latin"
                elif 0x2000 <= code_point <= 0x206F:
                    block = "General Punctuation"
                elif 0x2070 <= code_point <= 0x209F:
                    block = "Superscripts/Subscripts"
                elif 0x20A0 <= code_point <= 0x20CF:
                    block = "Currency Symbols"
                elif 0x2100 <= code_point <= 0x214F:
                    block = "Letterlike Symbols"
                elif 0x2190 <= code_point <= 0x21FF:
                    block = "Arrows"
                elif 0x2200 <= code_point <= 0x22FF:
                    block = "Mathematical Operators"
                elif 0x2300 <= code_point <= 0x23FF:
                    block = "Miscellaneous Technical"
                elif 0x2400 <= code_point <= 0x243F:
                    block = "Control Pictures"
                elif 0x2500 <= code_point <= 0x257F:
                    block = "Box Drawing"
                elif 0x2580 <= code_point <= 0x259F:
                    block = "Block Elements"
                elif 0x25A0 <= code_point <= 0x25FF:
                    block = "Geometric Shapes"
                elif 0x2600 <= code_point <= 0x26FF:
                    block = "Miscellaneous Symbols"
                elif 0x2700 <= code_point <= 0x27BF:
                    block = "Dingbats"
                elif 0x2900 <= code_point <= 0x297F:
                    block = "Supplemental Arrows-B"
                elif 0x2A00 <= code_point <= 0x2AFF:
                    block = "Supplemental Mathematical Operators"
                elif 0x1F600 <= code_point <= 0x1F64F:
                    block = "Emoticons"
                elif 0x1F680 <= code_point <= 0x1F6FF:
                    block = "Transport and Map Symbols"
                else:
                    block = f"Other (U+{code_point:04X})"
                
                unicode_blocks[block] += 1
            except ValueError:
                unicode_blocks["Invalid"] += 1
    
    print("🔣 Unicode Block Distribution:")
    for block, count in sorted(unicode_blocks.items(), key=lambda x: x[1], reverse=True):
        percentage = (count / len(symbols)) * 100
        print(f"  {block:30} {count:4} ({percentage:5.1f}%)")
    print()
    
    # Strategia di espansione
    print("🚀 EXPANSION STRATEGY TO 8000 SYMBOLS")
    print("=" * 50)
    
    # Calcola espansione per categoria
    expansion_needed = 8000 - len(symbols)
    
    print(f"📊 Current distribution suggests:")
    for cat, count in categories.most_common():
        target_count = int((count / len(symbols)) * 8000)
        expansion = target_count - count
        print(f"  {cat:15}: {count:4} → {target_count:4} (+{expansion:4})")
    
    print()
    print("💡 STRATEGIC EXPANSION PLAN:")
    print("1. 🎯 Semantic Uniqueness: Every symbol = 1 atomic concept")
    print("2. 📊 Quality Threshold: Score ≥ 95.0 mandatory")
    print("3. 🔤 Fallback Compliance: Max 8 chars, ng:domain:concept format")
    print("4. 🌐 Unicode Diversity: Spread across safe blocks")
    print("5. 🧠 Conceptual Domains: Expand cognitive coverage")
    print("6. ⚡ Validation Pipeline: USU/CTU/LCL criteria enforcement")
    print()

    # Genera strategia di espansione dettagliata
    generate_expansion_strategy(symbols, categories)

def generate_expansion_strategy(symbols, categories):
    """Genera strategia dettagliata per espansione a 8000 simboli."""

    current_count = len(symbols)
    target_count = 8000
    expansion_needed = target_count - current_count

    print("🚀 DETAILED EXPANSION STRATEGY")
    print("=" * 50)

    # Definisci domini cognitivi per espansione
    cognitive_domains = {
        "logic": {
            "current": categories.get("logic", 0),
            "concepts": ["implication", "biconditional", "modal_logic", "temporal_logic",
                        "fuzzy_logic", "quantum_logic", "paraconsistent", "relevance_logic"],
            "target_expansion": 400
        },
        "memory": {
            "current": categories.get("memory", 0),
            "concepts": ["allocation", "deallocation", "garbage_collection", "reference_counting",
                        "memory_mapping", "virtual_memory", "cache_coherence", "memory_barriers"],
            "target_expansion": 350
        },
        "operator": {
            "current": categories.get("operator", 0),
            "concepts": ["arithmetic", "bitwise", "comparison", "assignment", "logical",
                        "ternary", "null_coalescing", "pattern_matching", "destructuring"],
            "target_expansion": 300
        },
        "structure": {
            "current": categories.get("structure", 0),
            "concepts": ["classes", "interfaces", "traits", "mixins", "modules", "namespaces",
                        "generics", "type_parameters", "constraints", "variance"],
            "target_expansion": 280
        },
        "flow": {
            "current": categories.get("flow", 0),
            "concepts": ["conditionals", "loops", "exceptions", "coroutines", "generators",
                        "async_await", "continuations", "tail_calls", "trampolines"],
            "target_expansion": 250
        },
        "ai": {
            "current": categories.get("ai", 0),
            "concepts": ["neural_networks", "transformers", "attention", "embeddings",
                        "reinforcement_learning", "symbolic_ai", "knowledge_graphs", "reasoning"],
            "target_expansion": 400
        },
        "cognitive": {
            "current": categories.get("cognitive", 0),
            "concepts": ["consciousness", "metacognition", "introspection", "self_awareness",
                        "theory_of_mind", "intentionality", "qualia", "phenomenology"],
            "target_expansion": 350
        },
        "mathematical": {
            "current": categories.get("mathematical", 0),
            "concepts": ["topology", "category_theory", "type_theory", "lambda_calculus",
                        "combinatorics", "graph_theory", "number_theory", "abstract_algebra"],
            "target_expansion": 300
        },
        "quantum": {
            "current": categories.get("quantum", 0),
            "concepts": ["superposition", "entanglement", "decoherence", "measurement",
                        "quantum_gates", "quantum_algorithms", "quantum_error_correction"],
            "target_expansion": 200
        },
        "philosophical": {
            "current": categories.get("philosophical", 0),
            "concepts": ["ontology", "epistemology", "metaphysics", "ethics", "aesthetics",
                        "philosophy_of_mind", "philosophy_of_language", "modal_metaphysics"],
            "target_expansion": 180
        }
    }

    # Calcola distribuzione target
    total_target_expansion = sum(domain["target_expansion"] for domain in cognitive_domains.values())

    print(f"📊 Domain Expansion Plan:")
    print(f"Total expansion needed: {expansion_needed}")
    print(f"Planned expansion: {total_target_expansion}")
    print(f"Buffer: {expansion_needed - total_target_expansion}")
    print()

    for domain_name, domain_info in cognitive_domains.items():
        current = domain_info["current"]
        target_exp = domain_info["target_expansion"]
        final_count = current + target_exp

        print(f"🔹 {domain_name:12}: {current:4} → {final_count:4} (+{target_exp:3}) concepts:")
        for i, concept in enumerate(domain_info["concepts"][:4]):  # Show first 4 concepts
            print(f"    • {concept}")
        if len(domain_info["concepts"]) > 4:
            print(f"    • ... and {len(domain_info['concepts']) - 4} more")
        print()

if __name__ == "__main__":
    analyze_registry()
