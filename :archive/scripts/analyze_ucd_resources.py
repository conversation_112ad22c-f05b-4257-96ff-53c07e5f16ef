#!/usr/bin/env python3
"""
NEUROGLYPH UCD RESOURCES ANALYZER
Analizza tutti i file UCD per identificare simboli aggiuntivi utili per NEUROGLYPH
"""

import json
import os
from pathlib import Path
from typing import Dict, List, Set
from collections import Counter

class UCDResourceAnalyzer:
    """Analizzatore completo delle risorse UCD per NEUROGLYPH."""
    
    def __init__(self):
        self.ucd_dir = Path("neuroglyph/UCD")
        self.output_dir = Path("neuroglyph/core")
        self.logs_dir = Path("neuroglyph/logs")
        
        # File UCD di interesse per NEUROGLYPH
        self.relevant_files = {
            "Blocks.txt": "Unicode block definitions",
            "PropList.txt": "Unicode property lists", 
            "Scripts.txt": "Script assignments",
            "emoji/emoji-data.txt": "Emoji properties",
            "NamedSequences.txt": "Named character sequences",
            "SpecialCasing.txt": "Special case mappings",
            "PropertyValueAliases.txt": "Property value aliases"
        }
        
        self.analysis_results = {}
    
    def analyze_blocks_file(self) -> Dict:
        """Analizza Blocks.txt per identificare blocchi rilevanti."""
        
        blocks_file = self.ucd_dir / "Blocks.txt"
        if not blocks_file.exists():
            return {}
        
        print("🔍 Analyzing Unicode Blocks...")
        
        relevant_blocks = []
        
        with open(blocks_file, 'r', encoding='utf-8') as f:
            for line in f:
                line = line.strip()
                if line.startswith('#') or not line:
                    continue
                
                # Format: 0000..007F; Basic Latin
                if '..' in line and ';' in line:
                    range_part, name = line.split(';', 1)
                    name = name.strip()
                    
                    # Identifica blocchi rilevanti per NEUROGLYPH
                    relevant_keywords = [
                        'mathematical', 'symbols', 'arrows', 'operators',
                        'technical', 'geometric', 'miscellaneous',
                        'supplemental', 'dingbats', 'pictographs'
                    ]
                    
                    if any(keyword in name.lower() for keyword in relevant_keywords):
                        start_hex, end_hex = range_part.split('..')
                        start_code = int(start_hex, 16)
                        end_code = int(end_hex, 16)
                        
                        relevant_blocks.append({
                            'name': name,
                            'start': start_code,
                            'end': end_code,
                            'range': f"U+{start_hex}..U+{end_hex}",
                            'size': end_code - start_code + 1
                        })
        
        print(f"✅ Found {len(relevant_blocks)} relevant Unicode blocks")
        return {'relevant_blocks': relevant_blocks}
    
    def analyze_emoji_data(self) -> Dict:
        """Analizza emoji-data.txt per emoji semanticamente utili."""
        
        emoji_file = self.ucd_dir / "emoji" / "emoji-data.txt"
        if not emoji_file.exists():
            return {}
        
        print("🔍 Analyzing Emoji Data...")
        
        semantic_emoji = []
        
        with open(emoji_file, 'r', encoding='utf-8') as f:
            for line in f:
                line = line.strip()
                if line.startswith('#') or not line:
                    continue
                
                # Format: 1F9E0          ; Emoji                # E5.0  [1] (🧠)
                if ';' in line and 'Emoji' in line:
                    parts = line.split(';')
                    if len(parts) >= 2:
                        code_part = parts[0].strip()
                        
                        # Estrai codepoint
                        if '..' in code_part:
                            start_hex, end_hex = code_part.split('..')
                            # Per ora prendiamo solo emoji singoli
                            if int(end_hex, 16) - int(start_hex, 16) < 10:
                                codepoint = int(start_hex, 16)
                        else:
                            codepoint = int(code_part, 16)
                        
                        try:
                            emoji_char = chr(codepoint)
                            
                            # Filtra emoji semanticamente rilevanti
                            if self._is_semantic_emoji(emoji_char, line):
                                semantic_emoji.append({
                                    'emoji': emoji_char,
                                    'codepoint': f"U+{codepoint:04X}",
                                    'line': line
                                })
                        except ValueError:
                            continue
        
        print(f"✅ Found {len(semantic_emoji)} semantic emoji")
        return {'semantic_emoji': semantic_emoji}
    
    def _is_semantic_emoji(self, emoji: str, line: str) -> bool:
        """Verifica se emoji è semanticamente rilevante per NEUROGLYPH."""
        
        # Emoji concettuali rilevanti
        semantic_patterns = [
            '🧠',  # brain
            '⚡',  # lightning
            '⚙️',  # gear
            '🔧',  # wrench
            '🔬',  # microscope
            '⚗️',  # alembic
            '🧪',  # test tube
            '📊',  # bar chart
            '📈',  # chart increasing
            '📉',  # chart decreasing
            '🎯',  # target
            '🔍',  # magnifying glass
            '💡',  # light bulb
            '🔑',  # key
            '🔒',  # lock
            '🔓',  # unlock
            '⚖️',  # scales
            '🎲',  # dice
            '🧩',  # puzzle piece
            '🔗',  # link
            '⛓️',  # chains
            '🌐',  # globe
            '💻',  # laptop
            '🖥️',  # desktop
            '📱',  # mobile
            '🤖',  # robot
            '🧬',  # dna
            '⚛️',  # atom
            '🔬',  # microscope
        ]
        
        return emoji in semantic_patterns
    
    def analyze_named_sequences(self) -> Dict:
        """Analizza NamedSequences.txt per sequenze nominate."""
        
        sequences_file = self.ucd_dir / "NamedSequences.txt"
        if not sequences_file.exists():
            return {}
        
        print("🔍 Analyzing Named Sequences...")
        
        relevant_sequences = []
        
        with open(sequences_file, 'r', encoding='utf-8') as f:
            for line in f:
                line = line.strip()
                if line.startswith('#') or not line:
                    continue
                
                # Format: KEYCAP SEQUENCE;0030 FE0F 20E3
                if ';' in line:
                    name, sequence = line.split(';', 1)
                    name = name.strip()
                    sequence = sequence.strip()
                    
                    # Filtra sequenze rilevanti
                    if any(keyword in name.lower() for keyword in 
                           ['mathematical', 'arrow', 'symbol', 'sign']):
                        relevant_sequences.append({
                            'name': name,
                            'sequence': sequence
                        })
        
        print(f"✅ Found {len(relevant_sequences)} relevant named sequences")
        return {'named_sequences': relevant_sequences}
    
    def analyze_property_list(self) -> Dict:
        """Analizza PropList.txt per proprietà Unicode rilevanti."""
        
        proplist_file = self.ucd_dir / "PropList.txt"
        if not proplist_file.exists():
            return {}
        
        print("🔍 Analyzing Property List...")
        
        relevant_properties = {}
        
        with open(proplist_file, 'r', encoding='utf-8') as f:
            current_property = None
            
            for line in f:
                line = line.strip()
                if line.startswith('#') or not line:
                    continue
                
                # Format: 0021..0023    ; Terminal_Punctuation # Po   [3] EXCLAMATION MARK..NUMBER SIGN
                if ';' in line:
                    range_part, prop_part = line.split(';', 1)
                    prop_name = prop_part.split('#')[0].strip()
                    
                    # Proprietà rilevanti per NEUROGLYPH
                    relevant_props = [
                        'Math', 'Alphabetic', 'Ideographic', 'Diacritic',
                        'Extender', 'Logical_Order_Exception', 'White_Space'
                    ]
                    
                    if prop_name in relevant_props:
                        if prop_name not in relevant_properties:
                            relevant_properties[prop_name] = []
                        
                        relevant_properties[prop_name].append({
                            'range': range_part.strip(),
                            'line': line
                        })
        
        print(f"✅ Found {len(relevant_properties)} relevant properties")
        return {'relevant_properties': relevant_properties}
    
    def generate_comprehensive_analysis(self) -> Dict:
        """Genera analisi completa di tutte le risorse UCD."""
        
        print("🧠 NEUROGLYPH UCD COMPREHENSIVE ANALYSIS")
        print("=" * 50)
        
        # Analizza tutti i file rilevanti
        self.analysis_results = {
            'blocks_analysis': self.analyze_blocks_file(),
            'emoji_analysis': self.analyze_emoji_data(),
            'sequences_analysis': self.analyze_named_sequences(),
            'properties_analysis': self.analyze_property_list()
        }
        
        # Statistiche generali
        stats = {
            'relevant_blocks': len(self.analysis_results['blocks_analysis'].get('relevant_blocks', [])),
            'semantic_emoji': len(self.analysis_results['emoji_analysis'].get('semantic_emoji', [])),
            'named_sequences': len(self.analysis_results['sequences_analysis'].get('named_sequences', [])),
            'relevant_properties': len(self.analysis_results['properties_analysis'].get('relevant_properties', {}))
        }
        
        self.analysis_results['summary_stats'] = stats
        
        return self.analysis_results
    
    def save_analysis_results(self) -> bool:
        """Salva risultati dell'analisi."""
        
        print("💾 Saving UCD analysis results...")
        
        # Crea directory se necessarie
        self.output_dir.mkdir(exist_ok=True)
        self.logs_dir.mkdir(exist_ok=True)
        
        output_file = self.output_dir / "ucd_comprehensive_analysis.json"
        
        try:
            with open(output_file, 'w', encoding='utf-8') as f:
                json.dump(self.analysis_results, f, indent=2, ensure_ascii=False)
            
            print(f"✅ Analysis saved: {output_file}")
            
            return True
            
        except Exception as e:
            print(f"❌ Error saving analysis: {e}")
            return False
    
    def print_analysis_summary(self):
        """Stampa riassunto dell'analisi."""
        
        if not self.analysis_results:
            return
        
        stats = self.analysis_results.get('summary_stats', {})
        
        print(f"\n📊 UCD ANALYSIS SUMMARY")
        print("=" * 30)
        print(f"Relevant Unicode blocks: {stats.get('relevant_blocks', 0)}")
        print(f"Semantic emoji: {stats.get('semantic_emoji', 0)}")
        print(f"Named sequences: {stats.get('named_sequences', 0)}")
        print(f"Relevant properties: {stats.get('relevant_properties', 0)}")
        
        # Dettagli blocchi più interessanti
        blocks = self.analysis_results.get('blocks_analysis', {}).get('relevant_blocks', [])
        if blocks:
            print(f"\n🔹 TOP RELEVANT BLOCKS:")
            for block in sorted(blocks, key=lambda x: x['size'], reverse=True)[:10]:
                print(f"   {block['name']:30} {block['range']:15} ({block['size']:4} chars)")
        
        # Emoji semantici
        emoji_list = self.analysis_results.get('emoji_analysis', {}).get('semantic_emoji', [])
        if emoji_list:
            print(f"\n🔹 SEMANTIC EMOJI FOUND:")
            for emoji_data in emoji_list[:20]:  # Prime 20
                print(f"   {emoji_data['emoji']} {emoji_data['codepoint']}")

def main():
    """Esegue analisi completa UCD."""
    
    analyzer = UCDResourceAnalyzer()
    
    # Genera analisi
    results = analyzer.generate_comprehensive_analysis()
    
    # Salva risultati
    success = analyzer.save_analysis_results()
    
    # Stampa riassunto
    analyzer.print_analysis_summary()
    
    if success:
        print(f"\n🎊 UCD ANALYSIS COMPLETE!")
        print(f"✅ Comprehensive analysis saved")
        print(f"✅ Ready for symbol extraction enhancement")
        
        return True
    else:
        print(f"\n❌ ANALYSIS FAILED")
        return False

if __name__ == "__main__":
    success = main()
    
    if success:
        print(f"\n🚀 NEXT: Use analysis to enhance NEUROGLYPH symbol extraction")
    else:
        print(f"\n🔧 TROUBLESHOOTING NEEDED")
