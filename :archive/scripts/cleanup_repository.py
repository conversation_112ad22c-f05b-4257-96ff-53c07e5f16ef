#!/usr/bin/env python3
"""
NEUROGLYPH Repository Cleanup & Reorganization
==============================================

Script per pulire e riorganizzare il repository NEUROGLYPH:
1. Identifica file obsoleti/ridondanti
2. Sposta file in :archive
3. Riorganizza struttura pulita
4. Mantiene solo componenti essenziali

Autore: NEUROGLYPH Team
Data: 2025-01-27
"""

import os
import shutil
import json
from pathlib import Path
from datetime import datetime
from typing import Dict, List, Set, Tuple
import re

class NeuroglyphRepositoryCleanup:
    """Cleanup intelligente del repository NEUROGLYPH"""
    
    def __init__(self, root_path: str = "."):
        self.root = Path(root_path)
        self.archive_dir = self.root / ":archive"
        self.timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        
        # File essenziali da mantenere
        self.essential_files = {
            # Core components
            "neuroglyph/core/thinking_engine.py",
            "neuroglyph/core/cognitive_integration.py", 
            "neuroglyph/core/cognitive_state.py",
            "neuroglyph/core/dag_memory.py",
            "neuroglyph/core/neuroglyph_llm_pipeline.py",
            "neuroglyph/core/neuroglyph_tokenizer.py",
            "neuroglyph/core/symbolic_validator.py",
            "neuroglyph/core/encoder.py",
            "neuroglyph/core/locked_registry_godmode_v9.json",
            
            # Main registry and configs
            "README.md",
            "neuroglyph/README.md",
            
            # Essential scripts
            "neuroglyph_integrated_runner.py",
            "run_neuroglyph_god_mode.py",
            
            # Training essentials
            "tools/training/train_neuroglyph_qlora.py",
            "NEUROGLYPH_GOD_MODE_Colab.ipynb",
            
            # Documentation
            "docs/README.md",
            "docs/NEUROGLYPH_ULTRA_README.md"
        }
        
        # Pattern di file da archiviare
        self.archive_patterns = [
            r".*backup.*",
            r".*duplicate.*", 
            r".*old.*",
            r".*test.*\.py$",
            r".*_test\.py$",
            r".*analysis.*\.json$",
            r".*report.*\.txt$",
            r".*_20\d{6}_\d{6}.*",  # File con timestamp
            r".*symbols_registry_backup.*",
            r".*tokenizer_analysis.*",
            r".*reality_check.*",
            r".*integration_report.*"
        ]
        
    def analyze_repository(self) -> Dict[str, List[Path]]:
        """Analizza tutti i file del repository"""
        print("🔍 ANALISI REPOSITORY NEUROGLYPH")
        print("=" * 60)
        
        categories = {
            "essential": [],
            "archive_candidates": [],
            "duplicates": [],
            "obsolete_scripts": [],
            "backup_files": [],
            "test_files": [],
            "unknown": []
        }
        
        for file_path in self.root.rglob("*"):
            if file_path.is_file() and not self._should_ignore(file_path):
                category = self._categorize_file(file_path)
                categories[category].append(file_path)
        
        # Mostra statistiche
        for category, files in categories.items():
            if files:
                print(f"📁 {category}: {len(files)} files")
        
        return categories
    
    def _should_ignore(self, path: Path) -> bool:
        """Determina se ignorare un file/directory"""
        ignore_patterns = [
            "__pycache__",
            ".git",
            ".DS_Store", 
            "venv_metal",
            ":archive"
        ]
        
        return any(pattern in str(path) for pattern in ignore_patterns)
    
    def _categorize_file(self, file_path: Path) -> str:
        """Categorizza un file"""
        relative_path = str(file_path.relative_to(self.root))
        
        # File essenziali
        if relative_path in self.essential_files:
            return "essential"
        
        # Pattern di archivio
        for pattern in self.archive_patterns:
            if re.match(pattern, relative_path, re.IGNORECASE):
                if "backup" in relative_path.lower():
                    return "backup_files"
                elif "test" in relative_path.lower():
                    return "test_files"
                else:
                    return "archive_candidates"
        
        # Duplicati (file con nomi simili)
        if self._is_duplicate(file_path):
            return "duplicates"
        
        # Script obsoleti
        if self._is_obsolete_script(file_path):
            return "obsolete_scripts"
        
        return "unknown"
    
    def _is_duplicate(self, file_path: Path) -> bool:
        """Identifica file duplicati"""
        name = file_path.name.lower()
        
        # Pattern di duplicati comuni
        duplicate_indicators = [
            "_copy", "_backup", "_old", "_v2", "_v3", 
            "_final", "_complete", "_ultra", "_clean"
        ]
        
        return any(indicator in name for indicator in duplicate_indicators)
    
    def _is_obsolete_script(self, file_path: Path) -> bool:
        """Identifica script obsoleti"""
        if not file_path.suffix == ".py":
            return False
            
        name = file_path.name.lower()
        
        # Script obsoleti comuni
        obsolete_patterns = [
            "analyze_", "cleanup_", "fix_", "quick_", 
            "execute_", "validate_", "generate_", "create_"
        ]
        
        # Escludi script essenziali
        if str(file_path.relative_to(self.root)) in self.essential_files:
            return False
            
        return any(name.startswith(pattern) for pattern in obsolete_patterns)
    
    def create_archive_structure(self):
        """Crea struttura di archivio"""
        print("\n📁 CREAZIONE STRUTTURA ARCHIVIO")
        print("=" * 60)
        
        archive_structure = {
            "backup_files": "backups",
            "test_files": "tests", 
            "duplicates": "duplicates",
            "obsolete_scripts": "scripts",
            "archive_candidates": "misc"
        }
        
        for category, folder in archive_structure.items():
            folder_path = self.archive_dir / folder
            folder_path.mkdir(parents=True, exist_ok=True)
            print(f"   📂 Creato: {folder_path}")
    
    def move_files_to_archive(self, categories: Dict[str, List[Path]]):
        """Sposta file nell'archivio"""
        print("\n📦 SPOSTAMENTO FILE IN ARCHIVIO")
        print("=" * 60)
        
        archive_mapping = {
            "backup_files": "backups",
            "test_files": "tests",
            "duplicates": "duplicates", 
            "obsolete_scripts": "scripts",
            "archive_candidates": "misc"
        }
        
        total_moved = 0
        
        for category, folder in archive_mapping.items():
            files = categories.get(category, [])
            if not files:
                continue
                
            print(f"\n📁 Spostamento {category} ({len(files)} files):")
            
            for file_path in files:
                try:
                    # Mantieni struttura relativa
                    relative_path = file_path.relative_to(self.root)
                    target_path = self.archive_dir / folder / relative_path
                    
                    # Crea directory se necessaria
                    target_path.parent.mkdir(parents=True, exist_ok=True)
                    
                    # Sposta file
                    shutil.move(str(file_path), str(target_path))
                    print(f"   ✅ {relative_path} → :archive/{folder}/")
                    total_moved += 1
                    
                except Exception as e:
                    print(f"   ❌ Errore spostando {file_path}: {e}")
        
        print(f"\n📊 Totale file spostati: {total_moved}")
    
    def create_clean_structure(self):
        """Crea struttura pulita del repository"""
        print("\n🏗️ CREAZIONE STRUTTURA PULITA")
        print("=" * 60)
        
        clean_structure = {
            "neuroglyph/": {
                "core/": "Componenti core NEUROGLYPH",
                "ng_think/": "Moduli NG-THINK",
                "models/": "Modelli fine-tuned",
                "docs/": "Documentazione tecnica"
            },
            "tools/": {
                "training/": "Script di training",
                "evaluation/": "Script di valutazione"
            },
            "docs/": {
                "architecture/": "Documentazione architettura",
                "api/": "Documentazione API",
                "guides/": "Guide utente"
            },
            "tests/": {
                "unit/": "Test unitari",
                "integration/": "Test di integrazione"
            }
        }
        
        for main_dir, subdirs in clean_structure.items():
            main_path = Path(main_dir)
            main_path.mkdir(exist_ok=True)
            
            if isinstance(subdirs, dict):
                for subdir, description in subdirs.items():
                    subdir_path = main_path / subdir
                    subdir_path.mkdir(exist_ok=True)
                    
                    # Crea README per ogni directory
                    readme_path = subdir_path / "README.md"
                    if not readme_path.exists():
                        readme_content = f"# {subdir.rstrip('/')}\n\n{description}\n"
                        readme_path.write_text(readme_content)
                    
                    print(f"   📂 {subdir_path}")
    
    def generate_cleanup_report(self, categories: Dict[str, List[Path]]):
        """Genera report di pulizia"""
        print("\n📊 GENERAZIONE REPORT")
        print("=" * 60)
        
        report = {
            "cleanup_date": datetime.now().isoformat(),
            "summary": {
                "total_files_analyzed": sum(len(files) for files in categories.values()),
                "files_archived": sum(len(files) for cat, files in categories.items() 
                                    if cat != "essential" and cat != "unknown"),
                "essential_files_kept": len(categories.get("essential", [])),
                "unknown_files": len(categories.get("unknown", []))
            },
            "categories": {cat: len(files) for cat, files in categories.items()},
            "archive_location": str(self.archive_dir)
        }
        
        report_path = Path("docs/CLEANUP_REPORT.md")
        report_path.parent.mkdir(exist_ok=True)
        
        content = f"""# NEUROGLYPH Repository Cleanup Report

**Data pulizia:** {report['cleanup_date']}

## Riepilogo

- **File analizzati:** {report['summary']['total_files_analyzed']}
- **File archiviati:** {report['summary']['files_archived']}
- **File essenziali mantenuti:** {report['summary']['essential_files_kept']}
- **File sconosciuti:** {report['summary']['unknown_files']}

## Categorie

"""
        
        for category, count in report['categories'].items():
            content += f"- **{category}:** {count} files\n"
        
        content += f"\n## Archivio\n\nFile archiviati in: `{report['archive_location']}`\n"
        
        report_path.write_text(content)
        print(f"   📄 Report salvato: {report_path}")
        
        return report

def main():
    """Esegue pulizia completa del repository"""
    print("🧹 NEUROGLYPH REPOSITORY CLEANUP")
    print("🎯 Pulizia intelligente e riorganizzazione")
    print("=" * 70)
    
    cleanup = NeuroglyphRepositoryCleanup()
    
    # 1. Analisi repository
    categories = cleanup.analyze_repository()
    
    # 2. Creazione struttura archivio
    cleanup.create_archive_structure()
    
    # 3. Spostamento file in archivio
    cleanup.move_files_to_archive(categories)
    
    # 4. Creazione struttura pulita
    cleanup.create_clean_structure()
    
    # 5. Generazione report
    report = cleanup.generate_cleanup_report(categories)
    
    print("\n✅ PULIZIA COMPLETATA!")
    print(f"📊 {report['summary']['files_archived']} file archiviati")
    print(f"📁 Archivio: {cleanup.archive_dir}")
    print(f"📄 Report: docs/CLEANUP_REPORT.md")

if __name__ == "__main__":
    main()
