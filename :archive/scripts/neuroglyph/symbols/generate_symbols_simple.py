#!/usr/bin/env python3
"""
🧠 NEUROGLYPH ULTRA - Simple Symbol Generator
=============================================

Versione semplificata per generare simboli con validazione base.
Bypassa i validatori complessi per debugging.

Usage: python generate_symbols_simple.py [--count 10]
"""

import json
import sys
import os
import random
import unicodedata
from datetime import datetime
from typing import Dict, Any, List, Set
from pathlib import Path
import argparse

class SimpleSymbolGenerator:
    """Generatore semplificato di simboli NEUROGLYPH."""
    
    def __init__(self, target_count: int = 10):
        self.target_count = target_count
        self.registry_path = Path("core/symbols_registry.json")
        
        # Stato
        self.existing_symbols: Set[str] = set()
        self.existing_codes: Set[str] = set()
        self.generated_count = 0
        
        # Carica simboli esistenti
        self._load_existing_symbols()
        
        # Unicode ranges sicuri
        self.unicode_ranges = [
            (0x2190, 0x21FF),  # Arrows
            (0x2200, 0x22FF),  # Mathematical Operators
            (0x25A0, 0x25FF),  # Geometric Shapes
            (0x2600, 0x26FF),  # Miscellaneous Symbols
        ]
        
        # Categorie semplici
        self.categories = {
            "operator": ["add", "sub", "mul", "div", "mod", "pow"],
            "logic": ["and", "or", "not", "implies", "iff", "xor"],
            "structure": ["class", "function", "method", "property"],
            "flow": ["if", "else", "for", "while", "return", "break"],
            "memory": ["alloc", "free", "ref", "deref", "pointer"],
        }
        
    def _load_existing_symbols(self):
        """Carica simboli esistenti."""
        try:
            if self.registry_path.exists():
                with open(self.registry_path, 'r', encoding='utf-8') as f:
                    registry = json.load(f)
                    
                for symbol_data in registry.get("approved_symbols", []):
                    self.existing_symbols.add(symbol_data["symbol"])
                    self.existing_codes.add(symbol_data["code"])
                    
                self.generated_count = len(registry.get("approved_symbols", []))
                print(f"📊 Caricati {self.generated_count} simboli esistenti")
                
        except Exception as e:
            print(f"❌ Errore caricamento simboli: {e}")
            
    def generate_unique_symbol(self) -> Dict[str, Any]:
        """Genera un simbolo unico."""
        max_attempts = 100
        
        for attempt in range(max_attempts):
            try:
                # Seleziona range Unicode
                start, end = random.choice(self.unicode_ranges)
                unicode_point = random.randint(start, end)
                symbol = chr(unicode_point)
                
                # Verifica unicità
                if symbol in self.existing_symbols:
                    continue
                    
                # Verifica che sia un carattere valido
                try:
                    unicodedata.name(symbol)
                except ValueError:
                    continue
                    
                # Seleziona categoria e nome
                category = random.choice(list(self.categories.keys()))
                name = random.choice(self.categories[category])
                
                # Genera codice
                code = f"ng:{category}:{name}"
                
                # Verifica unicità codice
                if code in self.existing_codes:
                    # Aggiungi suffisso numerico
                    suffix = 1
                    while f"{code}_{suffix}" in self.existing_codes:
                        suffix += 1
                    code = f"{code}_{suffix}"
                    name = f"{name}_{suffix}"
                    
                # Genera fallback
                fallback = f"[{name.upper().replace('_', '')}]"
                
                return {
                    "symbol": symbol,
                    "code": code,
                    "fallback": fallback,
                    "category": category,
                    "name": name,
                    "description": f"{category.title()} operation: {name}",
                    "unicode_point": f"U+{unicode_point:04X}",
                    "attempt": attempt + 1
                }
                
            except Exception as e:
                print(f"⚠️ Errore generazione (tentativo {attempt + 1}): {e}")
                continue
                
        print(f"❌ Impossibile generare simbolo unico dopo {max_attempts} tentativi")
        return None
        
    def validate_symbol_simple(self, symbol_data: Dict[str, Any]) -> bool:
        """Validazione semplificata."""
        try:
            # Controlli base
            if not symbol_data["symbol"]:
                return False
                
            if len(symbol_data["symbol"]) != 1:
                return False
                
            if not symbol_data["code"].startswith("ng:"):
                return False
                
            if not symbol_data["fallback"].startswith("[") or not symbol_data["fallback"].endswith("]"):
                return False
                
            # Verifica unicità
            if symbol_data["symbol"] in self.existing_symbols:
                return False
                
            if symbol_data["code"] in self.existing_codes:
                return False
                
            return True
            
        except Exception as e:
            print(f"❌ Errore validazione: {e}")
            return False
            
    def approve_symbol(self, symbol_data: Dict[str, Any]) -> bool:
        """Approva e salva simbolo."""
        try:
            # Carica registry
            if self.registry_path.exists():
                with open(self.registry_path, 'r', encoding='utf-8') as f:
                    registry = json.load(f)
            else:
                registry = {
                    "registry_version": "1.0",
                    "created": datetime.now().strftime("%Y-%m-%d"),
                    "description": "NEUROGLYPH ULTRA Symbol Registry - Simple Generator",
                    "stats": {"total_submissions": 0, "approved": 0, "rejected": 0, "pending": 0},
                    "approved_symbols": [],
                    "rejected_symbols": [],
                    "pending_symbols": [],
                    "categories": {},
                    "next_id": "NG0001"
                }
            
            # Genera ID
            next_id_num = int(registry.get("next_id", "NG0001")[2:]) if registry.get("next_id") else 1
            new_id = f"NG{next_id_num:04d}"
            
            # Crea entry
            approved_symbol = {
                "id": new_id,
                "symbol": symbol_data["symbol"],
                "code": symbol_data["code"],
                "fallback": symbol_data["fallback"],
                "category": symbol_data["category"],
                "name": symbol_data["name"],
                "description": symbol_data["description"],
                "unicode_point": symbol_data["unicode_point"],
                "approved_date": datetime.now().strftime("%Y-%m-%d"),
                "validation_score": 95.0,  # Score fisso per versione semplice
                "status": "certified",
                "token_cost": 1,
                "auto_generated": True,
                "generator": "simple"
            }
            
            # Aggiorna registry
            registry["approved_symbols"].append(approved_symbol)
            registry["stats"]["approved"] += 1
            registry["stats"]["total_submissions"] += 1
            registry["next_id"] = f"NG{next_id_num + 1:04d}"
            
            # Aggiorna conteggio categoria
            category = symbol_data["category"]
            if category not in registry["categories"]:
                registry["categories"][category] = {"count": 0, "description": f"{category} operations"}
            registry["categories"][category]["count"] += 1
            
            # Salva
            with open(self.registry_path, 'w', encoding='utf-8') as f:
                json.dump(registry, f, indent=2, ensure_ascii=False)
            
            # Aggiorna stato
            self.existing_symbols.add(symbol_data["symbol"])
            self.existing_codes.add(symbol_data["code"])
            self.generated_count += 1
            
            print(f"✅ Simbolo approvato: {symbol_data['symbol']} ({new_id}) - {symbol_data['code']}")
            return True
            
        except Exception as e:
            print(f"❌ Errore approvazione: {e}")
            return False
            
    def generate_symbols(self) -> bool:
        """Genera simboli fino al target."""
        print(f"🚀 Generazione {self.target_count} simboli")
        print(f"📊 Simboli esistenti: {self.generated_count}")
        
        needed = self.target_count - self.generated_count
        if needed <= 0:
            print("✅ Target già raggiunto!")
            return True
            
        print(f"🎯 Simboli da generare: {needed}")
        
        success_count = 0
        attempt_count = 0
        
        while success_count < needed and attempt_count < needed * 10:  # Max 10x tentativi
            attempt_count += 1
            
            # Genera simbolo
            symbol_data = self.generate_unique_symbol()
            if not symbol_data:
                continue
                
            # Valida
            if not self.validate_symbol_simple(symbol_data):
                continue
                
            # Approva
            if self.approve_symbol(symbol_data):
                success_count += 1
                
            # Progress
            if success_count % 5 == 0 or success_count == needed:
                print(f"📈 Progresso: {success_count}/{needed} simboli generati")
                
        print(f"\n🎉 Generazione completata!")
        print(f"✅ Simboli generati: {success_count}")
        print(f"📊 Tentativi totali: {attempt_count}")
        print(f"⚡ Success rate: {(success_count/attempt_count*100):.1f}%")
        
        return success_count >= needed


def main():
    """Main function."""
    parser = argparse.ArgumentParser(description="Simple NEUROGLYPH symbol generator")
    parser.add_argument("--count", type=int, default=10, help="Number of symbols to generate")
    
    args = parser.parse_args()
    
    if args.count <= 0:
        print("❌ Count must be > 0")
        sys.exit(1)
        
    print("🧠 NEUROGLYPH ULTRA - Simple Symbol Generator")
    print("=" * 50)
    
    generator = SimpleSymbolGenerator(target_count=args.count)
    success = generator.generate_symbols()
    
    sys.exit(0 if success else 1)


if __name__ == "__main__":
    main()
