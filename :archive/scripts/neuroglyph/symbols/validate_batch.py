#!/usr/bin/env python3
"""
NEUROGLYPH LLM - Batch Validator
===============================

Valida e approva batch di simboli ULTRA con controlli rigorosi.
Implementa pipeline di validazione completa per garantire qualità.
"""

import json
import unicodedata
from pathlib import Path
from typing import Dict, List, Tuple, Optional
from datetime import datetime

class BatchValidator:
    """Validatore batch simboli NEUROGLYPH"""
    
    def __init__(self, 
                 registry_path: str = "neuroglyph/core/symbols_registry.json",
                 proposal_path: str = "neuroglyph/symbols/ultra_batch1_proposal.json"):
        self.registry_path = Path(registry_path)
        self.proposal_path = Path(proposal_path)
        
        # Carica registry e proposta
        self.registry = self._load_registry()
        self.proposal = self._load_proposal()
        
        # Thresholds validazione
        self.validation_thresholds = {
            "min_score": 1.0,
            "max_tokenizer_cost": 2,
            "max_collision_risk": 0.3,
            "min_semantic_clarity": 0.7
        }
    
    def _load_registry(self) -> Dict:
        """Carica registry simboli"""
        if self.registry_path.exists():
            with open(self.registry_path, 'r', encoding='utf-8') as f:
                return json.load(f)
        return {"approved_symbols": []}
    
    def _load_proposal(self) -> Dict:
        """Carica proposta batch"""
        if self.proposal_path.exists():
            with open(self.proposal_path, 'r', encoding='utf-8') as f:
                return json.load(f)
        return {"symbols": []}
    
    def validate_batch(self) -> Tuple[List[Dict], List[Dict], Dict]:
        """
        Valida batch completo
        
        Returns:
            (approved_symbols, rejected_symbols, validation_report)
        """
        print("🔍 VALIDAZIONE BATCH ULTRA")
        print("=" * 60)
        
        symbols = self.proposal.get("symbols", [])
        print(f"📊 Simboli da validare: {len(symbols)}")
        
        approved = []
        rejected = []
        validation_stats = {
            "total_symbols": len(symbols),
            "approved_count": 0,
            "rejected_count": 0,
            "rejection_reasons": {},
            "validation_date": datetime.now().isoformat()
        }
        
        for i, symbol in enumerate(symbols):
            print(f"🔍 Validazione simbolo {i+1}/{len(symbols)}: {symbol.get('symbol', 'N/A')}")
            
            is_valid, issues = self._validate_single_symbol(symbol)
            
            if is_valid:
                # Approva simbolo
                approved_symbol = symbol.copy()
                approved_symbol["status"] = "approved"
                approved_symbol["validation_date"] = datetime.now().strftime("%Y-%m-%d")
                approved.append(approved_symbol)
                validation_stats["approved_count"] += 1
                print(f"   ✅ APPROVATO")
            else:
                # Rigetta simbolo
                rejected_symbol = symbol.copy()
                rejected_symbol["status"] = "rejected"
                rejected_symbol["rejection_reasons"] = issues
                rejected_symbol["validation_date"] = datetime.now().strftime("%Y-%m-%d")
                rejected.append(rejected_symbol)
                validation_stats["rejected_count"] += 1
                
                # Conta ragioni rigetto
                for issue in issues:
                    validation_stats["rejection_reasons"][issue] = validation_stats["rejection_reasons"].get(issue, 0) + 1
                
                print(f"   ❌ RIGETTATO: {', '.join(issues)}")
        
        print(f"\n📊 RISULTATI VALIDAZIONE:")
        print(f"   ✅ Approvati: {validation_stats['approved_count']}")
        print(f"   ❌ Rigettati: {validation_stats['rejected_count']}")
        print(f"   📈 Tasso approvazione: {100 * validation_stats['approved_count'] / validation_stats['total_symbols']:.1f}%")
        
        return approved, rejected, validation_stats
    
    def _validate_single_symbol(self, symbol: Dict) -> Tuple[bool, List[str]]:
        """
        Valida singolo simbolo
        
        Returns:
            (is_valid, issues_list)
        """
        issues = []
        
        # 1. Validazione Unicode
        if not self._validate_unicode(symbol):
            issues.append("unicode_invalid")
        
        # 2. Validazione unicità
        if not self._validate_uniqueness(symbol):
            issues.append("not_unique")
        
        # 3. Validazione formato ng: code
        if not self._validate_ng_code_format(symbol):
            issues.append("invalid_ng_code")
        
        # 4. Validazione fallback
        if not self._validate_fallback(symbol):
            issues.append("invalid_fallback")
        
        # 5. Validazione score qualità
        if not self._validate_quality_score(symbol):
            issues.append("low_quality_score")
        
        # 6. Validazione costo tokenizer
        if not self._validate_tokenizer_cost(symbol):
            issues.append("high_tokenizer_cost")
        
        # 7. Validazione collisione visiva
        if not self._validate_visual_collision(symbol):
            issues.append("visual_collision")
        
        # 8. Validazione chiarezza semantica
        if not self._validate_semantic_clarity(symbol):
            issues.append("low_semantic_clarity")
        
        return len(issues) == 0, issues
    
    def _validate_unicode(self, symbol: Dict) -> bool:
        """Valida Unicode del simbolo"""
        try:
            symbol_char = symbol.get("symbol", "")
            
            # Controlli base
            if not symbol_char:
                return False
            
            # Codificabile UTF-8
            symbol_char.encode('utf-8')
            
            # Per simboli multi-carattere (emoji), controlla ogni carattere
            if len(symbol_char) > 1:
                for char in symbol_char:
                    try:
                        unicodedata.name(char)
                        category = unicodedata.category(char)
                        if category.startswith('C'):  # Control characters
                            return False
                    except ValueError:
                        return False
                return True
            
            # Simbolo singolo
            unicodedata.name(symbol_char)
            category = unicodedata.category(symbol_char)
            if category.startswith('C'):  # Control characters
                return False
            
            return True
            
        except (UnicodeError, ValueError):
            return False
    
    def _validate_uniqueness(self, symbol: Dict) -> bool:
        """Valida unicità simbolo e codice"""
        symbol_char = symbol.get("symbol", "")
        ng_code = symbol.get("code", "")
        
        # Simboli esistenti
        existing_symbols = {s["symbol"] for s in self.registry.get("approved_symbols", [])}
        existing_codes = {s["code"] for s in self.registry.get("approved_symbols", [])}
        
        # Controlla unicità
        if symbol_char in existing_symbols:
            return False
        
        if ng_code in existing_codes:
            return False
        
        return True
    
    def _validate_ng_code_format(self, symbol: Dict) -> bool:
        """Valida formato ng: code"""
        ng_code = symbol.get("code", "")
        
        # Deve iniziare con "ng:"
        if not ng_code.startswith("ng:"):
            return False
        
        # Deve avere almeno 3 parti separate da ":"
        parts = ng_code.split(":")
        if len(parts) < 3:
            return False
        
        # Parti non vuote
        if any(not part for part in parts):
            return False
        
        return True
    
    def _validate_fallback(self, symbol: Dict) -> bool:
        """Valida fallback ASCII"""
        fallback = symbol.get("fallback", "")
        
        # Deve esistere
        if not fallback:
            return False
        
        # Deve essere ASCII
        try:
            fallback.encode('ascii')
        except UnicodeEncodeError:
            return False
        
        # Lunghezza ragionevole
        if len(fallback) > 20:
            return False
        
        return True
    
    def _validate_quality_score(self, symbol: Dict) -> bool:
        """Valida score qualità"""
        score = symbol.get("validation_score", 0.0)
        return score >= self.validation_thresholds["min_score"]
    
    def _validate_tokenizer_cost(self, symbol: Dict) -> bool:
        """Valida costo tokenizer stimato"""
        symbol_char = symbol.get("symbol", "")
        
        # Stima costo (euristica)
        if len(symbol_char) == 0:
            return False
        elif len(symbol_char) == 1:
            code_point = ord(symbol_char)
            if code_point < 0x10000:
                cost = 1
            else:
                cost = 2
        else:
            # Multi-character (emoji)
            cost = len(symbol_char)
        
        return cost <= self.validation_thresholds["max_tokenizer_cost"]
    
    def _validate_visual_collision(self, symbol: Dict) -> bool:
        """Valida collisione visiva"""
        symbol_char = symbol.get("symbol", "")
        
        # Simboli esistenti
        existing_symbols = [s["symbol"] for s in self.registry.get("approved_symbols", [])]
        
        # Controlla similarità (euristica semplice)
        for existing in existing_symbols:
            if self._are_visually_similar(symbol_char, existing):
                return False
        
        return True
    
    def _are_visually_similar(self, symbol1: str, symbol2: str) -> bool:
        """Controlla similarità visiva tra simboli"""
        try:
            # Per simboli singoli, usa nomi Unicode
            if len(symbol1) == 1 and len(symbol2) == 1:
                name1 = unicodedata.name(symbol1, "").lower()
                name2 = unicodedata.name(symbol2, "").lower()
                
                words1 = set(name1.split())
                words2 = set(name2.split())
                
                if len(words1) == 0 or len(words2) == 0:
                    return False
                
                common = words1.intersection(words2)
                similarity = len(common) / max(len(words1), len(words2))
                
                return similarity > 0.6
            
            # Per emoji/multi-char, confronto diretto
            return symbol1 == symbol2
            
        except:
            return False
    
    def _validate_semantic_clarity(self, symbol: Dict) -> bool:
        """Valida chiarezza semantica"""
        clarity = 1.0
        
        # Ha descrizione?
        description = symbol.get("description", "")
        if not description or len(description) < 5:
            clarity -= 0.3
        
        # Ha categoria?
        category = symbol.get("category", "")
        if not category:
            clarity -= 0.2
        
        # Codice ng: ben formato?
        ng_code = symbol.get("code", "")
        if not ng_code.startswith("ng:") or len(ng_code.split(":")) < 3:
            clarity -= 0.3
        
        return clarity >= self.validation_thresholds["min_semantic_clarity"]
    
    def apply_approved_symbols(self, approved_symbols: List[Dict]) -> bool:
        """Applica simboli approvati al registry"""
        print(f"\n💾 APPLICAZIONE SIMBOLI APPROVATI")
        print("=" * 60)
        
        if not approved_symbols:
            print("⚠️ Nessun simbolo da applicare")
            return False
        
        # Backup registry
        backup_path = self.registry_path.parent / f"symbols_registry_backup_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        if self.registry_path.exists():
            with open(backup_path, 'w', encoding='utf-8') as f:
                json.dump(self.registry, f, indent=2, ensure_ascii=False)
            print(f"💾 Backup creato: {backup_path}")
        
        # Aggiorna registry
        current_symbols = self.registry.get("approved_symbols", [])
        
        # Aggiungi simboli approvati
        for symbol in approved_symbols:
            current_symbols.append(symbol)
        
        # Aggiorna stats
        self.registry["approved_symbols"] = current_symbols
        self.registry["stats"] = {
            "total_submissions": len(current_symbols),
            "approved": len(current_symbols),
            "rejected": 0,  # Non tracciamo rigetti nel registry
            "pending": 0,
            "last_update": datetime.now().strftime("%Y-%m-%d")
        }
        
        # Salva registry aggiornato
        with open(self.registry_path, 'w', encoding='utf-8') as f:
            json.dump(self.registry, f, indent=2, ensure_ascii=False)
        
        print(f"✅ Registry aggiornato: +{len(approved_symbols)} simboli")
        print(f"📊 Simboli totali: {len(current_symbols)}")
        
        return True
    
    def save_validation_report(self, approved: List[Dict], rejected: List[Dict], stats: Dict):
        """Salva report validazione"""
        report_path = self.proposal_path.parent / f"batch_validation_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        
        report = {
            "validation_info": {
                "batch_number": self.proposal.get("batch_info", {}).get("batch_number", 1),
                "tier": self.proposal.get("batch_info", {}).get("tier", "ultra"),
                "theme": self.proposal.get("batch_info", {}).get("theme", "unknown"),
                "validation_date": datetime.now().isoformat(),
                "validator_version": "1.0.0"
            },
            "statistics": stats,
            "approved_symbols": approved,
            "rejected_symbols": rejected,
            "validation_thresholds": self.validation_thresholds
        }
        
        with open(report_path, 'w', encoding='utf-8') as f:
            json.dump(report, f, indent=2, ensure_ascii=False)
        
        print(f"📊 Report validazione salvato: {report_path}")

def main():
    """Validazione batch principale"""
    print("🔍 NEUROGLYPH LLM - Batch Validator")
    print("🎯 Validazione rigorosa batch ULTRA")
    print("=" * 70)
    
    validator = BatchValidator()
    
    # Valida batch
    approved, rejected, stats = validator.validate_batch()
    
    # Salva report
    validator.save_validation_report(approved, rejected, stats)
    
    # Applica simboli approvati se ci sono
    if approved:
        print(f"\n🎯 APPLICAZIONE SIMBOLI APPROVATI")
        confirm = input(f"Vuoi applicare {len(approved)} simboli approvati al registry? (y/N): ")
        
        if confirm.lower() == 'y':
            success = validator.apply_approved_symbols(approved)
            if success:
                print(f"🎉 BATCH APPLICATO CON SUCCESSO!")
                print(f"✅ {len(approved)} simboli aggiunti al registry")
                print(f"🎯 NEUROGLYPH ULTRA Tier: {567 + len(approved)}/1024 simboli")
            else:
                print(f"❌ Errore applicazione batch")
        else:
            print(f"⏸ Applicazione batch annullata")
    else:
        print(f"⚠️ Nessun simbolo approvato da applicare")
    
    print(f"\n🎉 Validazione batch completata!")

if __name__ == "__main__":
    main()
