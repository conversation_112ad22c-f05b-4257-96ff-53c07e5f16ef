#!/usr/bin/env python3
"""
NEUROGLYPH ULTRA - 512 Symbols Generator
========================================

Genera 512 simboli unici seguendo i criteri USU/CTU/LCL:
- USU: Unicità Simbolica Universale
- CTU: Codifica Testuale Unificata (ng:category:function)
- LCL: LLM Compatibility Layer
"""

import json
import unicodedata
from typing import List, Dict, Any

# Categorie NEUROGLYPH ULTRA
CATEGORIES = [
    "action", "structure", "state", "logic", "entity", "domain", 
    "flow", "data", "meta", "system", "memory", "reasoning"
]

# Pool di simboli Unicode sicuri per LLM
UNICODE_SYMBOLS = [
    # Mathematical Operators
    "⊕", "⊖", "⊗", "⊘", "⊙", "⊚", "⊛", "⊜", "⊝", "⊞", "⊟", "⊠", "⊡",
    "⊢", "⊣", "⊤", "⊥", "⊦", "⊧", "⊨", "⊩", "⊪", "⊫", "⊬", "⊭", "⊮", "⊯",
    "⊰", "⊱", "⊲", "⊳", "⊴", "⊵", "⊶", "⊷", "⊸", "⊹", "⊺", "⊻", "⊼", "⊽",
    
    # Arrows
    "→", "←", "↑", "↓", "↔", "↕", "↖", "↗", "↘", "↙", "↚", "↛", "↜", "↝", "↞", "↟",
    "↠", "↡", "↢", "↣", "↤", "↥", "↦", "↧", "↨", "↩", "↪", "↫", "↬", "↭", "↮", "↯",
    "⇀", "⇁", "⇂", "⇃", "⇄", "⇅", "⇆", "⇇", "⇈", "⇉", "⇊", "⇋", "⇌", "⇍", "⇎", "⇏",
    "⤴", "⤵", "⤶", "⤷", "⤸", "⤹", "⤺", "⤻", "⤼", "⤽", "⤾", "⤿", "⥀", "⥁", "⥂", "⥃",
    
    # Geometric Shapes
    "◊", "◈", "◉", "◎", "●", "◐", "◑", "◒", "◓", "◔", "◕", "◖", "◗", "◘", "◙", "◚",
    "◛", "◜", "◝", "◞", "◟", "◠", "◡", "◢", "◣", "◤", "◥", "◦", "◧", "◨", "◩", "◪",
    "◫", "◬", "◭", "◮", "◯", "◰", "◱", "◲", "◳", "◴", "◵", "◶", "◷", "◸", "◹", "◺",
    "◻", "◼", "◽", "◾", "◿", "⬒", "⬓", "⬔", "⬕", "⬖", "⬗", "⬘", "⬙", "⬚", "⬛", "⬜",
    
    # Logic Symbols
    "∀", "∃", "∄", "∅", "∆", "∇", "∈", "∉", "∊", "∋", "∌", "∍", "∎", "∏", "∐", "∑",
    "∧", "∨", "∩", "∪", "∫", "∬", "∭", "∮", "∯", "∰", "∱", "∲", "∳", "∴", "∵", "∶",
    "∷", "∸", "∹", "∺", "∻", "∼", "∽", "∾", "∿", "≀", "≁", "≂", "≃", "≄", "≅", "≆",
    "≇", "≈", "≉", "≊", "≋", "≌", "≍", "≎", "≏", "≐", "≑", "≒", "≓", "≔", "≕", "≖",
    "≗", "≘", "≙", "≚", "≛", "≜", "≝", "≞", "≟", "≠", "≡", "≢", "≣", "≤", "≥", "≦",
    
    # Brackets and Delimiters
    "⟨", "⟩", "⟪", "⟫", "⟬", "⟭", "⟮", "⟯", "⟰", "⟱", "⟲", "⟳", "⟴", "⟵", "⟶", "⟷",
    "⟸", "⟹", "⟺", "⟻", "⟼", "⟽", "⟾", "⟿", "⤀", "⤁", "⤂", "⤃", "⤄", "⤅", "⤆", "⤇",
    
    # Miscellaneous Symbols
    "⚀", "⚁", "⚂", "⚃", "⚄", "⚅", "⚆", "⚇", "⚈", "⚉", "⚊", "⚋", "⚌", "⚍", "⚎", "⚏",
    "⚐", "⚑", "⚒", "⚓", "⚔", "⚕", "⚖", "⚗", "⚘", "⚙", "⚚", "⚛", "⚜", "⚝", "⚞", "⚟",
    "⚠", "⚡", "⚢", "⚣", "⚤", "⚥", "⚦", "⚧", "⚨", "⚩", "⚪", "⚫", "⚬", "⚭", "⚮", "⚯",
    "⚰", "⚱", "⚲", "⚳", "⚴", "⚵", "⚶", "⚷", "⚸", "⚹", "⚺", "⚻", "⚼", "⚽", "⚾", "⚿",
    
    # Additional Mathematical
    "⧀", "⧁", "⧂", "⧃", "⧄", "⧅", "⧆", "⧇", "⧈", "⧉", "⧊", "⧋", "⧌", "⧍", "⧎", "⧏",
    "⧐", "⧑", "⧒", "⧓", "⧔", "⧕", "⧖", "⧗", "⧘", "⧙", "⧚", "⧛", "⧜", "⧝", "⧞", "⧟",
    
    # Check marks and crosses
    "✓", "✔", "✕", "✖", "✗", "✘", "✙", "✚", "✛", "✜", "✝", "✞", "✟", "✠", "✡", "✢",
    "✣", "✤", "✥", "✦", "✧", "✨", "✩", "✪", "✫", "✬", "✭", "✮", "✯", "✰", "✱", "✲",
    
    # Stars and asterisks
    "⋆", "⋇", "⋈", "⋉", "⋊", "⋋", "⋌", "⋍", "⋎", "⋏", "⋐", "⋑", "⋒", "⋓", "⋔", "⋕",
    "⋖", "⋗", "⋘", "⋙", "⋚", "⋛", "⋜", "⋝", "⋞", "⋟", "⋠", "⋡", "⋢", "⋣", "⋤", "⋥",
    
    # Emoji symbols (LLM-safe)
    "📌", "📍", "📎", "📏", "📐", "📑", "📒", "📓", "📔", "📕", "📖", "📗", "📘", "📙",
    "📚", "📛", "📜", "📝", "📞", "📟", "📠", "📡", "📢", "📣", "📤", "📥", "📦", "📧",
    "📨", "📩", "📪", "📫", "📬", "📭", "📮", "📯", "📰", "📱", "📲", "📳", "📴", "📵",
    "🔍", "🔎", "🔏", "🔐", "🔑", "🔒", "🔓", "🔔", "🔕", "🔖", "🔗", "🔘", "🔙", "🔚",
    "🔛", "🔜", "🔝", "🔞", "🔟", "🔠", "🔡", "🔢", "🔣", "🔤", "🔥", "🔦", "🔧", "🔨",
    "🧠", "🧩", "🧪", "🧫", "🧬", "🧭", "🧮", "🧯", "🧰", "🧱", "🧲", "🧳", "🧴", "🧵",
    "⚙", "⚡", "⚠", "⚽", "⚾", "⛄", "⛅", "⛆", "⛇", "⛈", "⛉", "⛊", "⛋", "⛌", "⛍",
    "🎯", "🎰", "🎱", "🎲", "🎳", "🎴", "🎵", "🎶", "🎷", "🎸", "🎹", "🎺", "🎻", "🎼",
    "📊", "📋", "📌", "📍", "📎", "📏", "💾", "💿", "📀", "📁", "📂", "📃", "📄", "📅"
]

# Nomi semantici per ogni categoria
SEMANTIC_NAMES = {
    "action": [
        "fix", "create", "delete", "abort", "start", "stop", "pause", "resume", "reset", "clear",
        "copy", "move", "merge", "split", "join", "separate", "combine", "extract", "insert", "remove",
        "update", "modify", "change", "transform", "convert", "translate", "encode", "decode", "compress", "expand",
        "validate", "verify", "check", "test", "debug", "trace", "monitor", "observe", "measure", "count",
        "search", "find", "locate", "discover", "explore", "navigate", "browse", "scan", "filter", "sort",
        "build", "compile", "deploy", "install", "configure", "setup", "initialize", "prepare", "ready", "execute"
    ],
    "structure": [
        "function", "class", "block", "module", "package", "namespace", "scope", "context", "frame", "stack",
        "queue", "list", "array", "vector", "matrix", "table", "tree", "graph", "node", "edge",
        "interface", "protocol", "contract", "schema", "template", "pattern", "blueprint", "model", "view", "controller",
        "component", "element", "widget", "control", "panel", "window", "dialog", "form", "field", "input",
        "output", "stream", "buffer", "cache", "pool", "registry", "repository", "database", "collection", "set"
    ],
    "state": [
        "error", "warning", "success", "null", "empty", "full", "ready", "busy", "idle", "active",
        "inactive", "enabled", "disabled", "visible", "hidden", "open", "closed", "locked", "unlocked", "secure",
        "valid", "invalid", "dirty", "clean", "fresh", "stale", "new", "old", "current", "previous",
        "next", "first", "last", "initial", "final", "temporary", "permanent", "volatile", "stable", "unstable",
        "connected", "disconnected", "online", "offline", "available", "unavailable", "pending", "complete", "partial", "failed"
    ],
    "logic": [
        "and", "or", "not", "implies", "equals", "entails", "biconditional", "subset", "superset", "intersection", "union",
        "true", "false", "maybe", "unknown", "exists", "forall", "some", "none", "all", "any",
        "if", "then", "else", "when", "while", "until", "unless", "because", "since", "therefore",
        "greater", "less", "equal", "different", "similar", "identical", "equivalent", "compatible", "incompatible", "consistent",
        "inconsistent", "valid", "invalid", "sound", "unsound", "complete", "incomplete", "decidable", "undecidable", "provable"
    ],
    "entity": [
        "variable", "constant", "parameter", "argument", "value", "reference", "pointer", "address", "identifier", "name",
        "object", "instance", "type", "class", "interface", "trait", "mixin", "prototype", "factory", "builder",
        "user", "client", "server", "service", "resource", "asset", "property", "attribute", "field", "member",
        "record", "tuple", "struct", "union", "enum", "variant", "option", "result", "either", "maybe",
        "token", "symbol", "literal", "expression", "statement", "declaration", "definition", "annotation", "comment", "documentation"
    ],
    "domain": [
        "web", "mobile", "desktop", "server", "cloud", "edge", "iot", "embedded", "real_time", "batch",
        "database", "storage", "memory", "cache", "network", "security", "crypto", "auth", "permission", "role",
        "ui", "ux", "frontend", "backend", "middleware", "api", "rest", "graphql", "rpc", "messaging",
        "ml", "ai", "data", "analytics", "visualization", "reporting", "monitoring", "logging", "metrics", "alerts",
        "game", "graphics", "audio", "video", "image", "text", "document", "file", "stream", "protocol"
    ],
    "flow": [
        "if", "else", "for_loop", "while_loop", "return", "yield", "async", "await", "break", "continue",
        "try", "catch", "finally", "throw", "raise", "handle", "recover", "retry", "timeout", "cancel",
        "call", "invoke", "execute", "run", "start", "stop", "pause", "resume", "schedule", "defer",
        "sequence", "parallel", "concurrent", "serial", "pipeline", "workflow", "process", "thread", "task", "job",
        "event", "trigger", "signal", "notify", "broadcast", "publish", "subscribe", "listen", "respond", "react"
    ],
    "data": [
        "string", "number", "boolean", "array", "object", "map", "set", "list", "tuple", "record",
        "json", "xml", "csv", "binary", "text", "image", "audio", "video", "document", "file",
        "byte", "bit", "word", "block", "page", "segment", "chunk", "packet", "frame", "message",
        "key", "value", "pair", "entry", "item", "element", "node", "leaf", "branch", "root",
        "header", "body", "footer", "metadata", "payload", "content", "data", "info", "detail", "summary"
    ],
    "meta": [
        "target", "measure", "iterate", "configure", "optimize", "profile", "benchmark", "test", "mock", "stub",
        "annotation", "decorator", "attribute", "property", "metadata", "reflection", "introspection", "inspection", "analysis", "synthesis",
        "generation", "compilation", "interpretation", "evaluation", "execution", "simulation", "emulation", "virtualization", "abstraction", "encapsulation",
        "inheritance", "composition", "aggregation", "association", "dependency", "coupling", "cohesion", "modularity", "reusability", "maintainability",
        "scalability", "performance", "efficiency", "reliability", "availability", "consistency", "integrity", "security", "privacy", "compliance"
    ],
    "system": [
        "kernel", "driver", "daemon", "service", "process", "thread", "fiber", "coroutine", "task", "job",
        "scheduler", "dispatcher", "allocator", "garbage_collector", "memory_manager", "file_system", "network_stack", "protocol_handler", "device_manager", "interrupt_handler",
        "bootloader", "firmware", "bios", "uefi", "hypervisor", "container", "sandbox", "jail", "chroot", "namespace",
        "registry", "configuration", "environment", "variable", "path", "library", "framework", "runtime", "virtual_machine", "interpreter",
        "compiler", "linker", "loader", "debugger", "profiler", "tracer", "monitor", "logger", "auditor", "analyzer"
    ],
    "memory": [
        "store", "retrieve", "pin", "index", "link", "pattern", "evolve", "cache", "buffer", "pool",
        "heap", "stack", "register", "page", "segment", "block", "chunk", "slab", "arena", "zone",
        "allocate", "deallocate", "free", "reserve", "commit", "protect", "map", "unmap", "flush", "sync",
        "remember", "forget", "recall", "recognize", "associate", "relate", "connect", "bind", "reference", "dereference",
        "persist", "volatile", "permanent", "temporary", "backup", "restore", "snapshot", "checkpoint", "rollback", "commit"
    ],
    "reasoning": [
        "think", "analyze", "synthesize", "deduce", "induce", "infer", "conclude", "prove", "disprove", "verify",
        "reason", "logic", "intuition", "insight", "understanding", "comprehension", "knowledge", "wisdom", "intelligence", "consciousness",
        "plan", "strategy", "tactic", "approach", "method", "technique", "algorithm", "heuristic", "pattern", "model",
        "simulate", "predict", "forecast", "estimate", "approximate", "calculate", "compute", "solve", "optimize", "minimize",
        "maximize", "balance", "trade_off", "compromise", "decide", "choose", "select", "prefer", "prioritize", "rank"
    ]
}

def generate_512_symbols() -> List[Dict[str, Any]]:
    """Genera 512 simboli NEUROGLYPH ULTRA seguendo criteri USU/CTU/LCL."""
    symbols = []
    symbol_index = 1
    used_symbols = set()
    used_names = set()
    
    # Distribuisci simboli tra categorie
    symbols_per_category = 512 // len(CATEGORIES)
    remaining_symbols = 512 % len(CATEGORIES)
    
    for cat_idx, category in enumerate(CATEGORIES):
        # Calcola quanti simboli per questa categoria
        count = symbols_per_category
        if cat_idx < remaining_symbols:
            count += 1
        
        names = SEMANTIC_NAMES[category]
        
        for i in range(count):
            # Trova simbolo Unicode unico
            symbol = None
            for candidate in UNICODE_SYMBOLS:
                if candidate not in used_symbols:
                    symbol = candidate
                    used_symbols.add(candidate)
                    break
            
            if not symbol:
                # Fallback se finiscono i simboli Unicode
                symbol = f"⟨{symbol_index:03d}⟩"
                used_symbols.add(symbol)
            
            # Trova nome unico
            name = names[i % len(names)]
            if name in used_names:
                name = f"{name}_{i // len(names) + 1}"
            used_names.add(name)
            
            # Genera Unicode point
            try:
                unicode_point = f"U+{ord(symbol[0]):04X}"
                if len(symbol) > 1:
                    unicode_point += f"U+{ord(symbol[1]):04X}"
            except:
                unicode_point = f"U+{symbol_index + 0x2000:04X}"
            
            # Crea entry simbolo
            symbol_entry = {
                "id": f"NG{symbol_index:04d}",
                "symbol": symbol,
                "name": name,
                "description": f"{name.replace('_', ' ').title()} concept in {category} - {get_description(name, category)}",
                "category": category,
                "aliases": get_aliases(name),
                "status": "approved",
                "version": "1.0",
                "code": f"ng:{category}:{name}",
                "fallback": f"[{name.upper().replace('_', '')}]",
                "unicode_point": unicode_point
            }
            
            symbols.append(symbol_entry)
            symbol_index += 1
    
    return symbols

def get_description(name: str, category: str) -> str:
    """Genera descrizione semantica per il simbolo."""
    descriptions = {
        "action": "operation that modifies state",
        "structure": "organizational construct",
        "state": "condition or status indicator",
        "logic": "logical relationship or operation",
        "entity": "data container or object",
        "domain": "domain-specific concept",
        "flow": "control flow construct",
        "data": "data type or structure",
        "meta": "meta-programming concept",
        "system": "system-level component",
        "memory": "memory management operation",
        "reasoning": "cognitive or reasoning process"
    }
    return descriptions.get(category, "semantic concept")

def get_aliases(name: str) -> List[str]:
    """Genera alias per il simbolo."""
    alias_map = {
        "fix": ["repair", "correct", "resolve"],
        "create": ["make", "build", "generate"],
        "delete": ["remove", "destroy", "eliminate"],
        "error": ["failure", "exception", "crash"],
        "success": ["complete", "done", "finished"],
        "function": ["method", "procedure", "routine"],
        "class": ["type", "template", "blueprint"],
        "if": ["condition", "branch", "test"],
        "for_loop": ["iterate", "repeat", "cycle"],
        "variable": ["var", "data", "value"]
    }
    return alias_map.get(name, [name.replace("_", ""), name.split("_")[0] if "_" in name else name])

def main():
    """Genera e salva 512 simboli NEUROGLYPH ULTRA."""
    print("🧠 Generazione 512 Simboli NEUROGLYPH ULTRA...")
    
    symbols = generate_512_symbols()
    
    print(f"✅ Generati {len(symbols)} simboli")
    print(f"📊 Categorie: {len(CATEGORIES)}")
    print(f"🔤 Simboli per categoria: ~{512 // len(CATEGORIES)}")
    
    # Salva file
    output_file = "core/symbols_512_complete.json"
    with open(output_file, 'w', encoding='utf-8') as f:
        json.dump(symbols, f, indent=2, ensure_ascii=False)
    
    print(f"💾 Salvato in: {output_file}")
    
    # Statistiche
    categories_count = {}
    for symbol in symbols:
        cat = symbol["category"]
        categories_count[cat] = categories_count.get(cat, 0) + 1
    
    print("\n📈 Distribuzione per categoria:")
    for cat, count in sorted(categories_count.items()):
        print(f"  {cat}: {count}")
    
    print(f"\n🚀 NEUROGLYPH ULTRA: 512 simboli pronti per il primo LLM simbolico pensante!")

if __name__ == "__main__":
    main()
