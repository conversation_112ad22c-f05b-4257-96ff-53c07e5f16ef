#!/usr/bin/env python3
"""
NEUROGLYPH FASE 2 - ANALYZE REGISTRY GAPS
Analisi precisa dei gap per espansione GOD-tier verso 2048 simboli
"""

import json
import sys
from pathlib import Path
from datetime import datetime
from typing import Dict, List, Any, Tuple
from collections import defaultdict

class RegistryGapAnalyzer:
    """Analyzer per identificare gap precisi nel registry NEUROGLYPH."""
    
    def __init__(self, registry_path: str = "neuroglyph/core/symbols_registry.json"):
        self.registry_path = Path(registry_path)
        self.registry = {}
        self.analysis_timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        
        # Target GOD-tier per dominio
        self.god_tier_targets = {
            "neural_architectures": 76,
            "quantum_computing": 76,
            "symbolic_ai": 76,
            "meta_programming": 128,
            "distributed_systems": 128,
            "type_theory": 64,
            "category_theory": 64,
            "formal_verification": 64,
            "cognitive_modeling": 64,
            "philosophical_concepts": 64,
            "mathematical_structures": 64,
            "machine_learning": 128,
            "concurrency_advanced": 64,
            "logical_dynamics": 32,
            "runtime_structures": 32,
            "abstract_operators": 32,
            "linguistic_mappings": 32,
            "reserved_expansion": 64
        }
        
        # Sotto-domini critici per espansione
        self.critical_subdomains = {
            "neural_architectures": [
                "transformers", "attention_mechanisms", "layer_normalization",
                "activation_functions", "gradient_flow", "backpropagation",
                "neural_topology", "weight_initialization", "regularization",
                "dropout_variants", "batch_normalization", "residual_connections",
                "skip_connections", "dense_layers", "convolutional_layers",
                "recurrent_layers", "lstm_gates", "gru_mechanisms",
                "embedding_layers", "positional_encoding"
            ],
            "quantum_computing": [
                "quantum_gates", "qubit_operations", "quantum_entanglement",
                "superposition_states", "quantum_measurement", "decoherence",
                "quantum_circuits", "quantum_algorithms", "quantum_error_correction",
                "quantum_teleportation", "quantum_cryptography", "quantum_annealing",
                "quantum_supremacy", "quantum_interference", "quantum_parallelism",
                "quantum_fourier_transform", "grover_algorithm", "shor_algorithm",
                "quantum_machine_learning", "variational_quantum_eigensolver"
            ],
            "symbolic_ai": [
                "knowledge_graphs", "ontology_reasoning", "semantic_networks",
                "logical_inference", "rule_based_systems", "expert_systems",
                "symbolic_learning", "concept_formation", "analogical_reasoning",
                "causal_reasoning", "temporal_reasoning", "spatial_reasoning",
                "modal_logic", "fuzzy_logic", "probabilistic_logic",
                "description_logic", "first_order_logic", "higher_order_logic",
                "automated_theorem_proving", "symbolic_regression"
            ],
            "meta_programming": [
                "code_generation", "ast_manipulation", "macro_systems",
                "template_metaprogramming", "reflection_mechanisms", "introspection",
                "dynamic_compilation", "jit_compilation", "bytecode_generation",
                "source_transformation", "program_synthesis", "code_analysis",
                "static_analysis", "dynamic_analysis", "program_verification",
                "model_driven_development", "domain_specific_languages",
                "language_workbenches", "compiler_construction", "interpreter_design"
            ]
        }
        
    def load_registry(self) -> bool:
        """Carica il registry dei simboli."""
        try:
            with open(self.registry_path, 'r', encoding='utf-8') as f:
                self.registry = json.load(f)
            print(f"✅ Registry caricato: {len(self.registry.get('approved_symbols', []))} simboli")
            return True
        except Exception as e:
            print(f"❌ Errore caricamento registry: {e}")
            return False
    
    def analyze_current_distribution(self) -> Dict[str, Any]:
        """Analizza la distribuzione attuale dei simboli per dominio."""
        symbols = self.registry.get("approved_symbols", [])
        
        domain_counts = defaultdict(int)
        category_counts = defaultdict(int)
        subdomain_analysis = defaultdict(lambda: defaultdict(int))
        
        for symbol in symbols:
            category = symbol.get("category", "unknown")
            name = symbol.get("name", "")
            code = symbol.get("code", "")
            
            # Conta per categoria
            category_counts[category] += 1
            
            # Estrai dominio dalla categoria o dal code
            if ":" in code:
                parts = code.split(":")
                if len(parts) >= 2:
                    domain = parts[1]
                    domain_counts[domain] += 1
                    
                    # Analisi sotto-domini
                    if len(parts) >= 3:
                        subdomain = parts[2]
                        subdomain_analysis[domain][subdomain] += 1
            else:
                domain_counts[category] += 1
        
        return {
            "total_symbols": len(symbols),
            "domain_counts": dict(domain_counts),
            "category_counts": dict(category_counts),
            "subdomain_analysis": dict(subdomain_analysis)
        }
    
    def calculate_gaps(self, current_distribution: Dict[str, Any]) -> Dict[str, Any]:
        """Calcola i gap precisi per ogni dominio."""
        domain_counts = current_distribution["domain_counts"]
        gaps = {}
        total_gap = 0
        
        for domain, target in self.god_tier_targets.items():
            current = domain_counts.get(domain, 0)
            gap = max(0, target - current)
            gaps[domain] = {
                "current": current,
                "target": target,
                "gap": gap,
                "coverage_percent": (current / target * 100) if target > 0 else 100
            }
            total_gap += gap
        
        # Calcola gap totale verso 2048
        current_total = current_distribution["total_symbols"]
        total_target = 2048
        overall_gap = total_target - current_total
        
        return {
            "domain_gaps": gaps,
            "total_current": current_total,
            "total_target": total_target,
            "overall_gap": overall_gap,
            "calculated_gap_sum": total_gap
        }
    
    def identify_critical_subdomains(self, current_distribution: Dict[str, Any]) -> Dict[str, List[str]]:
        """Identifica sotto-domini critici mancanti o sottorappresentati."""
        subdomain_analysis = current_distribution["subdomain_analysis"]
        critical_missing = {}
        
        for domain, subdomains in self.critical_subdomains.items():
            current_subdomains = set(subdomain_analysis.get(domain, {}).keys())
            missing_subdomains = []
            weak_subdomains = []
            
            for subdomain in subdomains:
                if subdomain not in current_subdomains:
                    missing_subdomains.append(subdomain)
                elif subdomain_analysis[domain][subdomain] < 2:  # Sottorappresentato
                    weak_subdomains.append(f"{subdomain} ({subdomain_analysis[domain][subdomain]})")
            
            if missing_subdomains or weak_subdomains:
                critical_missing[domain] = {
                    "missing": missing_subdomains,
                    "weak": weak_subdomains,
                    "total_critical": len(missing_subdomains) + len(weak_subdomains)
                }
        
        return critical_missing
    
    def generate_expansion_plan(self, gaps: Dict[str, Any], critical_subdomains: Dict[str, List[str]]) -> Dict[str, Any]:
        """Genera piano di espansione dettagliato."""
        domain_gaps = gaps["domain_gaps"]
        
        # Ordina domini per priorità (gap più alto = priorità più alta)
        priority_domains = sorted(
            domain_gaps.items(),
            key=lambda x: (x[1]["gap"], x[1]["coverage_percent"]),
            reverse=True
        )
        
        expansion_plan = {
            "phase_1_critical": [],  # Gap > 50 simboli
            "phase_2_important": [],  # Gap 20-50 simboli
            "phase_3_completion": [],  # Gap < 20 simboli
            "batch_recommendations": {}
        }
        
        for domain, gap_info in priority_domains:
            gap = gap_info["gap"]
            if gap == 0:
                continue
                
            domain_plan = {
                "domain": domain,
                "gap": gap,
                "current": gap_info["current"],
                "target": gap_info["target"],
                "coverage": gap_info["coverage_percent"],
                "critical_subdomains": critical_subdomains.get(domain, {}),
                "recommended_batches": max(1, gap // 10)  # Batch da 10 simboli
            }
            
            if gap > 50:
                expansion_plan["phase_1_critical"].append(domain_plan)
            elif gap >= 20:
                expansion_plan["phase_2_important"].append(domain_plan)
            else:
                expansion_plan["phase_3_completion"].append(domain_plan)
        
        # Raccomandazioni batch
        for phase_name, domains in [
            ("phase_1", expansion_plan["phase_1_critical"]),
            ("phase_2", expansion_plan["phase_2_important"]),
            ("phase_3", expansion_plan["phase_3_completion"])
        ]:
            if domains:
                expansion_plan["batch_recommendations"][phase_name] = {
                    "total_symbols_needed": sum(d["gap"] for d in domains),
                    "estimated_batches": sum(d["recommended_batches"] for d in domains),
                    "priority_order": [d["domain"] for d in domains]
                }
        
        return expansion_plan
    
    def save_gap_analysis(self, current_distribution: Dict[str, Any], 
                         gaps: Dict[str, Any], critical_subdomains: Dict[str, List[str]],
                         expansion_plan: Dict[str, Any]) -> str:
        """Salva analisi completa dei gap."""
        analysis_path = f"neuroglyph/symbols/registry_gap_analysis_{self.analysis_timestamp}.json"
        
        analysis_data = {
            "analysis_timestamp": self.analysis_timestamp,
            "current_state": current_distribution,
            "gap_analysis": gaps,
            "critical_subdomains": critical_subdomains,
            "expansion_plan": expansion_plan,
            "recommendations": {
                "immediate_actions": [
                    "Iniziare con phase_1_critical domains",
                    "Generare simboli per neural_architectures (+59)",
                    "Espandere quantum_computing (+48)",
                    "Completare symbolic_ai (+32)",
                    "Potenziare meta_programming (+66)"
                ],
                "quality_criteria": [
                    "Mantenere validation_score ≥ 95.0",
                    "Applicare criteri USU/CTU/LCL",
                    "Validazione incrementale batch da 10",
                    "Zero collisioni semantiche/visive"
                ],
                "next_scripts": [
                    "generate_symbols_ultra_pipeline.py --target 2048 --score-min 95.0",
                    "validate_batch_symbols.py --batch-size 10",
                    "lock_god_tier_registry.py --hash-validation"
                ]
            }
        }
        
        with open(analysis_path, 'w', encoding='utf-8') as f:
            json.dump(analysis_data, f, indent=2, ensure_ascii=False)
        
        return analysis_path

def main():
    """Esegue analisi completa dei gap."""
    print("🧠 NEUROGLYPH LLM - FASE 2: ANALYZE REGISTRY GAPS")
    print("🎯 Analisi precisa per espansione GOD-tier verso 2048 simboli")
    print("=" * 70)
    
    # Crea analyzer
    analyzer = RegistryGapAnalyzer()
    
    # Carica registry
    if not analyzer.load_registry():
        sys.exit(1)
    
    # Analizza distribuzione attuale
    current_distribution = analyzer.analyze_current_distribution()
    print(f"📊 Simboli attuali: {current_distribution['total_symbols']}")
    
    # Calcola gap
    gaps = analyzer.calculate_gaps(current_distribution)
    print(f"🎯 Gap verso 2048: {gaps['overall_gap']} simboli")
    
    # Identifica sotto-domini critici
    critical_subdomains = analyzer.identify_critical_subdomains(current_distribution)
    
    # Genera piano di espansione
    expansion_plan = analyzer.generate_expansion_plan(gaps, critical_subdomains)
    
    # Salva analisi
    analysis_path = analyzer.save_gap_analysis(
        current_distribution, gaps, critical_subdomains, expansion_plan
    )
    print(f"📄 Analisi gap salvata: {analysis_path}")
    
    # Stampa summary
    print(f"\n📈 SUMMARY GAP ANALYSIS:")
    print(f"  🔴 Phase 1 Critical: {len(expansion_plan['phase_1_critical'])} domini")
    print(f"  🟠 Phase 2 Important: {len(expansion_plan['phase_2_important'])} domini")
    print(f"  🟡 Phase 3 Completion: {len(expansion_plan['phase_3_completion'])} domini")
    
    if expansion_plan["phase_1_critical"]:
        print(f"\n🚨 DOMINI CRITICI (Phase 1):")
        for domain_plan in expansion_plan["phase_1_critical"]:
            print(f"  • {domain_plan['domain']}: +{domain_plan['gap']} simboli ({domain_plan['coverage']:.1f}% coverage)")
    
    print(f"\n🚀 PROSSIMI PASSI:")
    print(f"  1. Generare simboli per domini Phase 1")
    print(f"  2. Validazione incrementale batch da 10")
    print(f"  3. Applicare criteri USU/CTU/LCL rigorosi")
    print(f"  4. Lock registry GOD-tier con hash validation")
    
    return True

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
