#!/usr/bin/env python3
"""
NEUROGLYPH ULTRA - Symbol Validator
====================================

Validatore rigoroso per singoli simboli secondo criteri USU/CTU/LCL:
- USU: Unicità Simbolica Universale
- CTU: Codifica Testuale Unificata  
- LCL: LLM Compatibility Layer

Usage: python validate_symbol.py "⊕" "ng:operator:add" "[+]"
"""

import json
import sys
import unicodedata
import re
from typing import Dict, Any, List, Optional

class SymbolValidator:
    """Validatore rigoroso per simboli NEUROGLYPH ULTRA."""
    
    def __init__(self, symbols_file: str = "core/symbols_registry.json"):
        self.symbols_file = symbols_file
        self.existing_symbols = self._load_existing_symbols()
        
    def _load_existing_symbols(self) -> List[Dict[str, Any]]:
        """Carica simboli esistenti."""
        try:
            with open(self.symbols_file, 'r', encoding='utf-8') as f:
                return json.load(f)
        except:
            return []
    
    def validate_symbol(self, symbol: str, code: str, fallback: str, 
                       category: str = "", name: str = "", 
                       description: str = "") -> Dict[str, Any]:
        """Valida un simbolo secondo criteri USU/CTU/LCL."""
        
        result = {
            "symbol": symbol,
            "code": code,
            "fallback": fallback,
            "valid": True,
            "score": 0.0,
            "errors": [],
            "warnings": [],
            "usu_check": {},
            "ctu_check": {},
            "lcl_check": {}
        }
        
        # USU - Unicità Simbolica Universale
        result["usu_check"] = self._validate_usu(symbol, code, fallback)
        
        # CTU - Codifica Testuale Unificata
        result["ctu_check"] = self._validate_ctu(code, category)
        
        # LCL - LLM Compatibility Layer
        result["lcl_check"] = self._validate_lcl(symbol, fallback)
        
        # Calcola score finale
        result["score"] = self._calculate_score(result)
        result["valid"] = result["score"] >= 90.0
        
        return result
    
    def _validate_usu(self, symbol: str, code: str, fallback: str) -> Dict[str, Any]:
        """Valida criteri USU - Unicità Simbolica Universale."""
        usu = {
            "unicode_unique": True,
            "code_unique": True,
            "fallback_unique": True,
            "visually_distinct": True,
            "semantically_atomic": True,
            "errors": [],
            "warnings": []
        }
        
        # 1. Unicità Unicode
        for existing in self.existing_symbols:
            if existing.get("symbol") == symbol:
                usu["unicode_unique"] = False
                usu["errors"].append(f"Simbolo '{symbol}' già esistente")
                break
        
        # 2. Unicità codice
        for existing in self.existing_symbols:
            if existing.get("code") == code:
                usu["code_unique"] = False
                usu["errors"].append(f"Codice '{code}' già esistente")
                break
        
        # 3. Unicità fallback
        for existing in self.existing_symbols:
            if existing.get("fallback") == fallback:
                usu["fallback_unique"] = False
                usu["warnings"].append(f"Fallback '{fallback}' già esistente")
                break
        
        # 4. Distinzione visiva
        for existing in self.existing_symbols:
            existing_symbol = existing.get("symbol", "")
            if self._are_visually_similar(symbol, existing_symbol):
                usu["visually_distinct"] = False
                usu["warnings"].append(f"Simbolo visivamente simile a '{existing_symbol}'")
        
        # 5. Atomicità semantica (placeholder - da implementare logica più sofisticata)
        if len(symbol) > 2:
            usu["semantically_atomic"] = False
            usu["warnings"].append("Simbolo potenzialmente non atomico (troppo lungo)")
        
        return usu
    
    def _validate_ctu(self, code: str, category: str) -> Dict[str, Any]:
        """Valida criteri CTU - Codifica Testuale Unificata."""
        ctu = {
            "format_valid": True,
            "category_valid": True,
            "function_valid": True,
            "errors": [],
            "warnings": []
        }
        
        # 1. Formato ng:category:function
        if not re.match(r'^ng:[a-z_]+:[a-z_]+$', code):
            ctu["format_valid"] = False
            ctu["errors"].append(f"Formato codice non valido: '{code}' (deve essere ng:category:function)")
            return ctu
        
        parts = code.split(":")
        if len(parts) != 3:
            ctu["format_valid"] = False
            ctu["errors"].append("Codice deve avere esattamente 3 parti separate da ':'")
            return ctu
        
        _, code_category, function = parts
        
        # 2. Categoria valida
        valid_categories = {
            "operator", "logic", "structure", "flow", "data", 
            "memory", "reasoning", "meta", "system", "action",
            "state", "entity", "domain"
        }
        
        if code_category not in valid_categories:
            ctu["category_valid"] = False
            ctu["errors"].append(f"Categoria non valida: '{code_category}'")
        
        # 3. Funzione valida
        if not re.match(r'^[a-z_]+$', function):
            ctu["function_valid"] = False
            ctu["errors"].append(f"Funzione non valida: '{function}' (solo lettere minuscole e underscore)")
        
        # Verifica coerenza con categoria fornita
        if category and category != code_category:
            ctu["warnings"].append(f"Categoria fornita '{category}' diversa da codice '{code_category}'")
        
        return ctu
    
    def _validate_lcl(self, symbol: str, fallback: str) -> Dict[str, Any]:
        """Valida criteri LCL - LLM Compatibility Layer."""
        lcl = {
            "utf8_compatible": True,
            "fallback_format_valid": True,
            "font_safe": True,
            "tokenizer_friendly": True,
            "token_cost_estimate": 1,
            "errors": [],
            "warnings": []
        }
        
        # 1. Compatibilità UTF-8
        try:
            symbol.encode('utf-8')
        except UnicodeEncodeError:
            lcl["utf8_compatible"] = False
            lcl["errors"].append("Simbolo non compatibile UTF-8")
        
        # 2. Formato fallback ASCII
        if not re.match(r'^\[[A-Z_+\-*/!=<>]+\]$', fallback):
            lcl["fallback_format_valid"] = False
            lcl["errors"].append(f"Fallback non valido: '{fallback}' (deve essere [UPPERCASE])")
        
        # 3. Sicurezza font (evita caratteri problematici)
        problematic_ranges = [
            (0x1F000, 0x1F9FF),  # Emoji
            (0x2600, 0x26FF),    # Simboli vari
        ]
        
        for char in symbol:
            code_point = ord(char)
            for start, end in problematic_ranges:
                if start <= code_point <= end:
                    lcl["font_safe"] = False
                    lcl["warnings"].append(f"Simbolo potenzialmente problematico per font: U+{code_point:04X}")
                    break
        
        # 4. Stima costo token (semplificata)
        lcl["token_cost_estimate"] = len(symbol.encode('utf-8'))
        if lcl["token_cost_estimate"] > 3:
            lcl["tokenizer_friendly"] = False
            lcl["warnings"].append(f"Costo token stimato alto: {lcl['token_cost_estimate']}")
        
        return lcl
    
    def _are_visually_similar(self, symbol1: str, symbol2: str) -> bool:
        """Controlla se due simboli sono visivamente simili."""
        if not symbol1 or not symbol2 or symbol1 == symbol2:
            return False
        
        try:
            name1 = unicodedata.name(symbol1[0], "").lower()
            name2 = unicodedata.name(symbol2[0], "").lower()
            
            # Pattern di similarità
            similar_patterns = [
                ("circle", "ring"), ("square", "rectangle"),
                ("left", "right"), ("up", "down"),
                ("small", "large"), ("black", "white")
            ]
            
            for pattern1, pattern2 in similar_patterns:
                if ((pattern1 in name1 and pattern2 in name2) or 
                    (pattern2 in name1 and pattern1 in name2)):
                    return True
        except ValueError:
            pass
        
        return False
    
    def _calculate_score(self, result: Dict[str, Any]) -> float:
        """Calcola score di qualità del simbolo (0-100)."""
        score = 100.0
        
        # USU penalties
        usu = result["usu_check"]
        if not usu["unicode_unique"]: score -= 30
        if not usu["code_unique"]: score -= 30
        if not usu["fallback_unique"]: score -= 5
        if not usu["visually_distinct"]: score -= 10
        if not usu["semantically_atomic"]: score -= 5
        
        # CTU penalties
        ctu = result["ctu_check"]
        if not ctu["format_valid"]: score -= 25
        if not ctu["category_valid"]: score -= 15
        if not ctu["function_valid"]: score -= 10
        
        # LCL penalties
        lcl = result["lcl_check"]
        if not lcl["utf8_compatible"]: score -= 20
        if not lcl["fallback_format_valid"]: score -= 10
        if not lcl["font_safe"]: score -= 5
        if not lcl["tokenizer_friendly"]: score -= 5
        
        return max(0.0, score)
    
    def generate_report(self, result: Dict[str, Any]) -> str:
        """Genera report di validazione."""
        lines = []
        lines.append("🧠 NEUROGLYPH ULTRA - Symbol Validation Report")
        lines.append("=" * 50)
        lines.append(f"Symbol: {result['symbol']}")
        lines.append(f"Code: {result['code']}")
        lines.append(f"Fallback: {result['fallback']}")
        lines.append(f"Score: {result['score']:.1f}/100")
        lines.append(f"Valid: {'✅ YES' if result['valid'] else '❌ NO'}")
        lines.append("")
        
        # USU Results
        usu = result["usu_check"]
        lines.append("🔹 USU - Unicità Simbolica Universale:")
        lines.append(f"  Unicode unique: {'✅' if usu['unicode_unique'] else '❌'}")
        lines.append(f"  Code unique: {'✅' if usu['code_unique'] else '❌'}")
        lines.append(f"  Fallback unique: {'✅' if usu['fallback_unique'] else '⚠️'}")
        lines.append(f"  Visually distinct: {'✅' if usu['visually_distinct'] else '⚠️'}")
        lines.append(f"  Semantically atomic: {'✅' if usu['semantically_atomic'] else '⚠️'}")
        
        # CTU Results
        ctu = result["ctu_check"]
        lines.append("")
        lines.append("🔹 CTU - Codifica Testuale Unificata:")
        lines.append(f"  Format valid: {'✅' if ctu['format_valid'] else '❌'}")
        lines.append(f"  Category valid: {'✅' if ctu['category_valid'] else '❌'}")
        lines.append(f"  Function valid: {'✅' if ctu['function_valid'] else '❌'}")
        
        # LCL Results
        lcl = result["lcl_check"]
        lines.append("")
        lines.append("🔹 LCL - LLM Compatibility Layer:")
        lines.append(f"  UTF-8 compatible: {'✅' if lcl['utf8_compatible'] else '❌'}")
        lines.append(f"  Fallback format valid: {'✅' if lcl['fallback_format_valid'] else '❌'}")
        lines.append(f"  Font safe: {'✅' if lcl['font_safe'] else '⚠️'}")
        lines.append(f"  Tokenizer friendly: {'✅' if lcl['tokenizer_friendly'] else '⚠️'}")
        lines.append(f"  Token cost estimate: {lcl['token_cost_estimate']}")
        
        # Errors and warnings
        all_errors = []
        all_warnings = []
        
        for check in [usu, ctu, lcl]:
            all_errors.extend(check.get("errors", []))
            all_warnings.extend(check.get("warnings", []))
        
        if all_errors:
            lines.append("")
            lines.append("❌ Errors:")
            for error in all_errors:
                lines.append(f"  - {error}")
        
        if all_warnings:
            lines.append("")
            lines.append("⚠️ Warnings:")
            for warning in all_warnings:
                lines.append(f"  - {warning}")
        
        lines.append("")
        if result["valid"]:
            lines.append("🚀 SYMBOL APPROVED: Ready for NEUROGLYPH ULTRA!")
        else:
            lines.append("❌ SYMBOL REJECTED: Fix errors before approval")
        
        return "\n".join(lines)

def main():
    """Valida un simbolo da command line."""
    if len(sys.argv) < 4:
        print("Usage: python validate_symbol.py <symbol> <code> <fallback> [category] [name]")
        print("Example: python validate_symbol.py '⊕' 'ng:operator:add' '[+]' 'operator' 'add'")
        sys.exit(1)
    
    symbol = sys.argv[1]
    code = sys.argv[2]
    fallback = sys.argv[3]
    category = sys.argv[4] if len(sys.argv) > 4 else ""
    name = sys.argv[5] if len(sys.argv) > 5 else ""
    
    validator = SymbolValidator()
    result = validator.validate_symbol(symbol, code, fallback, category, name)
    
    report = validator.generate_report(result)
    print(report)
    
    # Salva risultato
    with open(f"validation_{symbol.replace('/', '_')}.json", "w", encoding="utf-8") as f:
        json.dump(result, f, indent=2, ensure_ascii=False)
    
    sys.exit(0 if result["valid"] else 1)

if __name__ == "__main__":
    main()
