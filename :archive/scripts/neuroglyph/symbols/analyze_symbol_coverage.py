#!/usr/bin/env python3
"""
NEUROGLYPH Symbol Coverage Analyzer
===================================

Analizza la copertura simbolica per coding e concetti, verifica completezza
per NEUROGLYPH LLM e identifica gap nel vocabolario.

Usage: python analyze_symbol_coverage.py --registry symbols_registry.json --report
"""

import json
import sys
import argparse
from typing import Dict, Any, List, Set
from pathlib import Path
from collections import defaultdict, Counter

class SymbolCoverageAnalyzer:
    """Analizza copertura simbolica per NEUROGLYPH LLM."""
    
    def __init__(self, registry_path: str):
        self.registry_path = Path(registry_path)
        self.registry_data = self._load_registry()
        self.symbols = self.registry_data.get('approved_symbols', [])
        
        # Definisce domini critici per NEUROGLYPH LLM
        self.critical_domains = {
            "coding_fundamentals": {
                "required_categories": ["operator", "logic", "flow", "structure", "memory"],
                "min_symbols_per_category": 20,
                "description": "Operazioni base per rappresentazione codice"
            },
            "advanced_programming": {
                "required_categories": ["advanced_coding", "meta_programming", "concurrency_advanced"],
                "min_symbols_per_category": 30,
                "description": "Costrutti avanzati per programmazione complessa"
            },
            "symbolic_reasoning": {
                "required_categories": ["symbolic_ai", "formal_verification", "category_theory"],
                "min_symbols_per_category": 20,
                "description": "Ragionamento simbolico e verifica formale"
            },
            "distributed_computing": {
                "required_categories": ["distributed_systems", "protocol_design", "cryptographic_primitives"],
                "min_symbols_per_category": 15,
                "description": "Sistemi distribuiti e protocolli"
            },
            "ai_ml_concepts": {
                "required_categories": ["neural_architectures", "machine_learning", "cognitive_modeling"],
                "min_symbols_per_category": 20,
                "description": "Architetture neurali e machine learning"
            },
            "theoretical_cs": {
                "required_categories": ["type_theory", "compiler_internals", "runtime_systems"],
                "min_symbols_per_category": 15,
                "description": "Computer science teorica"
            }
        }
        
        # Pattern di codice che devono essere rappresentabili
        self.code_patterns = {
            "basic_operations": ["add", "sub", "mul", "div", "mod", "pow"],
            "logical_operations": ["and", "or", "not", "xor", "implies"],
            "comparison_operations": ["eq", "ne", "lt", "gt", "le", "ge"],
            "control_flow": ["if", "else", "for", "while", "break", "continue", "return"],
            "data_structures": ["list", "dict", "set", "tuple", "array", "tree", "graph"],
            "function_concepts": ["function", "method", "lambda", "closure", "decorator"],
            "class_concepts": ["class", "object", "inheritance", "polymorphism", "encapsulation"],
            "memory_management": ["alloc", "free", "pointer", "reference", "garbage_collect"],
            "concurrency": ["thread", "process", "lock", "atomic", "async", "await"],
            "error_handling": ["try", "catch", "throw", "exception", "error", "panic"]
        }
    
    def _load_registry(self) -> Dict[str, Any]:
        """Carica registry simboli."""
        try:
            with open(self.registry_path, 'r', encoding='utf-8') as f:
                return json.load(f)
        except FileNotFoundError:
            raise FileNotFoundError(f"Registry non trovato: {self.registry_path}")
    
    def analyze_complete_coverage(self) -> Dict[str, Any]:
        """Analizza copertura completa del vocabolario simbolico."""
        
        analysis = {
            "overview": self._analyze_overview(),
            "domain_coverage": self._analyze_domain_coverage(),
            "code_pattern_coverage": self._analyze_code_pattern_coverage(),
            "quality_metrics": self._analyze_quality_metrics(),
            "gaps_and_recommendations": self._identify_gaps(),
            "readiness_assessment": self._assess_llm_readiness()
        }
        
        return analysis
    
    def _analyze_overview(self) -> Dict[str, Any]:
        """Analizza overview generale del vocabolario."""
        
        total_symbols = len(self.symbols)
        categories = Counter(s.get('category', 'unknown') for s in self.symbols)
        tiers = Counter(s.get('tier', 'unknown') for s in self.symbols)
        
        # Calcola distribuzione Unicode
        unicode_ranges = defaultdict(int)
        for symbol in self.symbols:
            if 'unicode_point' in symbol:
                try:
                    code_point = int(symbol['unicode_point'].replace('U+', ''), 16)
                    if code_point < 0x1000:
                        unicode_ranges['Basic Latin + Extensions'] += 1
                    elif code_point < 0x2000:
                        unicode_ranges['Latin Extended'] += 1
                    elif code_point < 0x3000:
                        unicode_ranges['Mathematical Symbols'] += 1
                    elif code_point < 0x10000:
                        unicode_ranges['Miscellaneous Symbols'] += 1
                    else:
                        unicode_ranges['Extended Unicode'] += 1
                except ValueError:
                    unicode_ranges['Invalid'] += 1
        
        return {
            "total_symbols": total_symbols,
            "categories_count": len(categories),
            "category_distribution": dict(categories.most_common()),
            "tier_distribution": dict(tiers),
            "unicode_distribution": dict(unicode_ranges),
            "avg_symbols_per_category": total_symbols / len(categories) if categories else 0
        }
    
    def _analyze_domain_coverage(self) -> Dict[str, Any]:
        """Analizza copertura per domini critici."""
        
        coverage_results = {}
        
        for domain_name, domain_config in self.critical_domains.items():
            required_categories = domain_config["required_categories"]
            min_per_category = domain_config["min_symbols_per_category"]
            
            domain_symbols = [s for s in self.symbols if s.get('category') in required_categories]
            category_counts = Counter(s.get('category') for s in domain_symbols)
            
            # Verifica copertura per categoria
            category_coverage = {}
            for category in required_categories:
                count = category_counts.get(category, 0)
                coverage_percentage = min(100, (count / min_per_category) * 100)
                
                category_coverage[category] = {
                    "count": count,
                    "required": min_per_category,
                    "coverage_percentage": coverage_percentage,
                    "status": "✅" if count >= min_per_category else "⚠️" if count >= min_per_category * 0.7 else "❌"
                }
            
            # Calcola copertura dominio
            total_coverage = sum(min(100, cc["coverage_percentage"]) for cc in category_coverage.values())
            domain_coverage_percentage = total_coverage / len(required_categories)
            
            coverage_results[domain_name] = {
                "description": domain_config["description"],
                "total_symbols": len(domain_symbols),
                "coverage_percentage": domain_coverage_percentage,
                "status": "✅" if domain_coverage_percentage >= 90 else "⚠️" if domain_coverage_percentage >= 70 else "❌",
                "category_coverage": category_coverage
            }
        
        return coverage_results
    
    def _analyze_code_pattern_coverage(self) -> Dict[str, Any]:
        """Analizza copertura pattern di codice."""
        
        # Estrae nomi/concetti dai simboli
        symbol_names = set()
        symbol_codes = set()
        
        for symbol in self.symbols:
            if 'name' in symbol:
                symbol_names.add(symbol['name'].lower())
            if 'code' in symbol:
                # Estrae ultima parte del codice ng:category:concept
                parts = symbol['code'].split(':')
                if len(parts) >= 3:
                    symbol_codes.add(parts[-1].lower())
        
        all_symbol_concepts = symbol_names.union(symbol_codes)
        
        pattern_coverage = {}
        
        for pattern_name, required_concepts in self.code_patterns.items():
            covered_concepts = []
            missing_concepts = []
            
            for concept in required_concepts:
                # Cerca match esatto o parziale
                if concept in all_symbol_concepts:
                    covered_concepts.append(concept)
                else:
                    # Cerca match parziale
                    partial_matches = [c for c in all_symbol_concepts if concept in c or c in concept]
                    if partial_matches:
                        covered_concepts.append(f"{concept} (via {partial_matches[0]})")
                    else:
                        missing_concepts.append(concept)
            
            coverage_percentage = (len(covered_concepts) / len(required_concepts)) * 100
            
            pattern_coverage[pattern_name] = {
                "required_concepts": required_concepts,
                "covered_concepts": covered_concepts,
                "missing_concepts": missing_concepts,
                "coverage_percentage": coverage_percentage,
                "status": "✅" if coverage_percentage >= 90 else "⚠️" if coverage_percentage >= 70 else "❌"
            }
        
        return pattern_coverage
    
    def _analyze_quality_metrics(self) -> Dict[str, Any]:
        """Analizza metriche di qualità del vocabolario."""
        
        # Verifica unicità
        symbols_set = set(s['symbol'] for s in self.symbols)
        codes_set = set(s['code'] for s in self.symbols)
        unicode_set = set(s.get('unicode_point', '') for s in self.symbols if s.get('unicode_point'))
        
        # Verifica formato
        valid_codes = sum(1 for s in self.symbols if s.get('code', '').startswith('ng:'))
        valid_fallbacks = sum(1 for s in self.symbols if s.get('fallback', '').startswith('['))
        
        # Verifica completezza metadati
        complete_symbols = sum(1 for s in self.symbols 
                             if all(key in s for key in ['symbol', 'code', 'fallback', 'category']))
        
        # Calcola score qualità medio
        quality_scores = [s.get('validation_score', 0) for s in self.symbols if 'validation_score' in s]
        avg_quality_score = sum(quality_scores) / len(quality_scores) if quality_scores else 0
        
        return {
            "uniqueness": {
                "symbols_unique": len(symbols_set) == len(self.symbols),
                "codes_unique": len(codes_set) == len(self.symbols),
                "unicode_unique": len(unicode_set) == len([s for s in self.symbols if s.get('unicode_point')])
            },
            "format_compliance": {
                "valid_codes_percentage": (valid_codes / len(self.symbols)) * 100,
                "valid_fallbacks_percentage": (valid_fallbacks / len(self.symbols)) * 100
            },
            "metadata_completeness": {
                "complete_symbols_percentage": (complete_symbols / len(self.symbols)) * 100
            },
            "quality_scores": {
                "average_score": avg_quality_score,
                "symbols_with_scores": len(quality_scores),
                "high_quality_symbols": sum(1 for score in quality_scores if score >= 90)
            }
        }
    
    def _identify_gaps(self) -> Dict[str, Any]:
        """Identifica gap nel vocabolario e raccomandazioni."""
        
        gaps = {
            "critical_missing_domains": [],
            "underrepresented_categories": [],
            "missing_code_patterns": [],
            "quality_issues": [],
            "recommendations": []
        }
        
        # Identifica domini critici mancanti
        domain_coverage = self._analyze_domain_coverage()
        for domain, coverage in domain_coverage.items():
            if coverage["coverage_percentage"] < 70:
                gaps["critical_missing_domains"].append({
                    "domain": domain,
                    "coverage": coverage["coverage_percentage"],
                    "description": coverage["description"]
                })
        
        # Identifica categorie sottorappresentate
        overview = self._analyze_overview()
        avg_per_category = overview["avg_symbols_per_category"]
        
        for category, count in overview["category_distribution"].items():
            if count < avg_per_category * 0.5:  # Meno della metà della media
                gaps["underrepresented_categories"].append({
                    "category": category,
                    "count": count,
                    "expected": int(avg_per_category)
                })
        
        # Identifica pattern di codice mancanti
        pattern_coverage = self._analyze_code_pattern_coverage()
        for pattern, coverage in pattern_coverage.items():
            if coverage["coverage_percentage"] < 80:
                gaps["missing_code_patterns"].append({
                    "pattern": pattern,
                    "coverage": coverage["coverage_percentage"],
                    "missing": coverage["missing_concepts"]
                })
        
        # Genera raccomandazioni
        if gaps["critical_missing_domains"]:
            gaps["recommendations"].append("Priorità ALTA: Completare domini critici mancanti")
        
        if gaps["missing_code_patterns"]:
            gaps["recommendations"].append("Priorità MEDIA: Aggiungere simboli per pattern di codice mancanti")
        
        if gaps["underrepresented_categories"]:
            gaps["recommendations"].append("Priorità BASSA: Bilanciare distribuzione categorie")
        
        return gaps
    
    def _assess_llm_readiness(self) -> Dict[str, Any]:
        """Valuta readiness per NEUROGLYPH LLM."""
        
        overview = self._analyze_overview()
        domain_coverage = self._analyze_domain_coverage()
        pattern_coverage = self._analyze_code_pattern_coverage()
        quality = self._analyze_quality_metrics()
        
        # Calcola score readiness
        total_symbols_score = min(100, (overview["total_symbols"] / 2048) * 100)
        
        domain_scores = [coverage["coverage_percentage"] for coverage in domain_coverage.values()]
        avg_domain_score = sum(domain_scores) / len(domain_scores) if domain_scores else 0
        
        pattern_scores = [coverage["coverage_percentage"] for coverage in pattern_coverage.values()]
        avg_pattern_score = sum(pattern_scores) / len(pattern_scores) if pattern_scores else 0
        
        quality_score = quality["quality_scores"]["average_score"]
        
        # Score finale pesato
        final_readiness_score = (
            total_symbols_score * 0.2 +
            avg_domain_score * 0.3 +
            avg_pattern_score * 0.3 +
            quality_score * 0.2
        )
        
        # Determina status
        if final_readiness_score >= 95:
            status = "🎉 READY FOR PRODUCTION"
            readiness_level = "PRODUCTION"
        elif final_readiness_score >= 85:
            status = "🚀 READY FOR TESTING"
            readiness_level = "TESTING"
        elif final_readiness_score >= 70:
            status = "🔄 DEVELOPMENT READY"
            readiness_level = "DEVELOPMENT"
        else:
            status = "⚠️ NEEDS MORE WORK"
            readiness_level = "INCOMPLETE"
        
        return {
            "final_score": final_readiness_score,
            "status": status,
            "readiness_level": readiness_level,
            "component_scores": {
                "symbol_count": total_symbols_score,
                "domain_coverage": avg_domain_score,
                "pattern_coverage": avg_pattern_score,
                "quality": quality_score
            },
            "requirements_met": {
                "minimum_symbols": overview["total_symbols"] >= 1024,
                "critical_domains": avg_domain_score >= 80,
                "code_patterns": avg_pattern_score >= 80,
                "quality_threshold": quality_score >= 85
            }
        }

def main():
    """Analizza copertura simbolica da command line."""
    parser = argparse.ArgumentParser(description="NEUROGLYPH Symbol Coverage Analyzer")
    parser.add_argument("--registry", default="neuroglyph/core/symbols_registry.json",
                       help="Path al registry simboli")
    parser.add_argument("--report", action="store_true",
                       help="Genera report dettagliato")
    parser.add_argument("--output", default="symbol_coverage_analysis.json",
                       help="File output per analisi")
    
    args = parser.parse_args()
    
    # Verifica file registry
    if not Path(args.registry).exists():
        print(f"❌ Registry non trovato: {args.registry}")
        sys.exit(1)
    
    print("🔍 NEUROGLYPH Symbol Coverage Analyzer")
    print("=" * 50)
    
    try:
        # Crea analyzer
        analyzer = SymbolCoverageAnalyzer(args.registry)
        
        # Esegui analisi completa
        analysis = analyzer.analyze_complete_coverage()
        
        # Salva risultati
        with open(args.output, 'w', encoding='utf-8') as f:
            json.dump(analysis, f, indent=2, ensure_ascii=False)
        
        # Report console
        overview = analysis["overview"]
        readiness = analysis["readiness_assessment"]
        
        print(f"📊 OVERVIEW:")
        print(f"  • Simboli totali: {overview['total_symbols']}")
        print(f"  • Categorie: {overview['categories_count']}")
        print(f"  • Distribuzione tier: {overview['tier_distribution']}")
        
        print(f"\n🎯 READINESS ASSESSMENT:")
        print(f"  • Score finale: {readiness['final_score']:.1f}%")
        print(f"  • Status: {readiness['status']}")
        print(f"  • Livello: {readiness['readiness_level']}")
        
        print(f"\n📋 COMPONENT SCORES:")
        for component, score in readiness["component_scores"].items():
            print(f"  • {component}: {score:.1f}%")
        
        # Report dettagliato
        if args.report:
            gaps = analysis["gaps_and_recommendations"]
            
            if gaps["critical_missing_domains"]:
                print(f"\n⚠️ DOMINI CRITICI MANCANTI:")
                for gap in gaps["critical_missing_domains"]:
                    print(f"  • {gap['domain']}: {gap['coverage']:.1f}% - {gap['description']}")
            
            if gaps["recommendations"]:
                print(f"\n💡 RACCOMANDAZIONI:")
                for rec in gaps["recommendations"]:
                    print(f"  • {rec}")
        
        print(f"\n📄 Analisi completa salvata: {args.output}")
        
    except Exception as e:
        print(f"❌ Errore durante analisi: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
