#!/usr/bin/env python3
"""
NEUROGLYPH FALLBACK SIMPLE FIXER
Corregge rapidamente fallback > 8 caratteri
"""

import json
import sys
from pathlib import Path
from datetime import datetime

def fix_fallback_simple(registry_path: str):
    """Corregge fallback in modo semplice e veloce."""
    
    # Carica registry
    with open(registry_path, 'r', encoding='utf-8') as f:
        registry = json.load(f)
    
    symbols = registry.get('approved_symbols', [])
    non_compliant = [s for s in symbols if len(s.get('fallback', '')) > 8]
    
    print(f"📊 Simboli totali: {len(symbols)}")
    print(f"❌ Simboli non conformi: {len(non_compliant)}")
    
    if not non_compliant:
        print("✅ Tutti i simboli sono già conformi!")
        return
    
    # Mapping per abbreviazioni comuni
    abbreviations = {
        "POINTER": "PTR",
        "IMPLIES": "IMP", 
        "PROPERTY": "PROP",
        "RETURN": "RET",
        "FUNCTION": "FUNC",
        "OPERATOR": "OP",
        "MEMORY": "MEM",
        "STRUCTURE": "STRUCT",
        "LOGICAL": "LOG",
        "CONDITIONAL": "COND",
        "ALLOCATION": "ALLOC",
        "DEREFERENCE": "DEREF",
        "BREAK": "BRK",
        "WHILE": "WHI",
        "SWITCH": "SWI",
        "SCALAR": "SCAL",
        "VECTOR": "VECT",
        "MATRIX": "MATR",
        "COMPLEX": "CMPLX",
        "INTEGER": "INT",
        "POLYNOMIAL": "POLY",
        "NEURAL": "NEUR",
        "TRANSFORMER": "TRANS",
        "ATTENTION": "ATTN",
        "QUANTUM": "QNTM",
        "SYMBOLIC": "SYMB"
    }
    
    fixes_applied = 0
    used_fallbacks = set()
    
    # Raccogli fallback esistenti
    for symbol in symbols:
        fallback = symbol.get('fallback', '')
        if len(fallback) <= 8:
            used_fallbacks.add(fallback)
    
    print(f"🔧 Inizio correzione...")
    
    for i, symbol in enumerate(non_compliant):
        original_fallback = symbol.get('fallback', '')
        clean_fallback = original_fallback.strip('[]')
        
        # Strategia 1: Rimuovi numeri alla fine
        import re
        base_fallback = re.sub(r'\d+$', '', clean_fallback)
        
        # Strategia 2: Applica abbreviazioni
        for long_form, short_form in abbreviations.items():
            if long_form in base_fallback:
                base_fallback = base_fallback.replace(long_form, short_form)
                break
        
        # Strategia 3: Tronca a 8 caratteri
        if len(base_fallback) > 8:
            base_fallback = base_fallback[:8]
        
        # Strategia 4: Assicura unicità
        new_fallback = f"[{base_fallback}]"
        counter = 1
        
        while new_fallback in used_fallbacks:
            if len(base_fallback) <= 6:
                new_fallback = f"[{base_fallback}{counter}]"
            else:
                new_fallback = f"[{base_fallback[:7]}{counter}]"
            counter += 1
            
            if counter > 99:
                # Fallback di emergenza
                symbol_id = symbol.get('id', 'UNK')[-3:]
                new_fallback = f"[SYM{symbol_id}]"
                break
        
        # Applica correzione
        symbol['fallback'] = new_fallback
        symbol['original_fallback'] = original_fallback
        symbol['fallback_fixed'] = True
        
        used_fallbacks.add(new_fallback)
        fixes_applied += 1
        
        print(f"  {i+1:3d}/{len(non_compliant)} {original_fallback} → {new_fallback}")
    
    # Aggiorna statistiche
    registry['stats']['fallback_fix_timestamp'] = datetime.now().isoformat()
    registry['stats']['fallback_fixes_applied'] = fixes_applied
    registry['stats']['fallback_compliant'] = len([s for s in symbols if len(s.get('fallback', '')) <= 8])
    
    # Backup
    backup_path = Path(registry_path).with_suffix(f".backup_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json")
    with open(backup_path, 'w', encoding='utf-8') as f:
        json.dump(registry, f, indent=2, ensure_ascii=False)
    print(f"💾 Backup salvato: {backup_path}")
    
    # Salva registry corretto
    with open(registry_path, 'w', encoding='utf-8') as f:
        json.dump(registry, f, indent=2, ensure_ascii=False)
    
    print(f"\n🎉 CORREZIONE COMPLETATA!")
    print(f"📊 Simboli corretti: {fixes_applied}")
    print(f"✅ Registry aggiornato")

if __name__ == "__main__":
    registry_path = "neuroglyph/core/locked_registry_godmode_v9.json"
    if len(sys.argv) > 1:
        registry_path = sys.argv[1]
    
    print("🔧 NEUROGLYPH FALLBACK SIMPLE FIXER")
    print("=" * 40)
    
    fix_fallback_simple(registry_path)
