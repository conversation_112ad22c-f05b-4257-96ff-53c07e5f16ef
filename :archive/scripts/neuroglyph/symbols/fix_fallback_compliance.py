#!/usr/bin/env python3
"""
NEUROGLYPH FALLBACK COMPLIANCE FIXER
Corregge simboli con fallback > 8 caratteri preservando semantica
"""

import json
import sys
import re
from pathlib import Path
from datetime import datetime
from typing import Dict, List, Any, Tuple

class FallbackComplianceFixer:
    def __init__(self, registry_path: str):
        self.registry_path = Path(registry_path)
        self.registry = {}
        self.fixes_applied = 0
        self.timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        
        # Mapping semantico per abbreviazioni intelligenti
        self.semantic_mappings = {
            # Neural architectures
            "TRANSFORMER": "TRANS",
            "ATTENTION": "ATTN", 
            "MECHANISM": "MECH",
            "ARCHITECTURE": "ARCH",
            "NEURAL": "NEUR",
            "NETWORK": "NET",
            "LAYER": "LYR",
            "NORMALIZATION": "NORM",
            "ACTIVATION": "ACTIV",
            "FUNCTION": "FUNC",
            "GRADIENT": "GRAD",
            "BACKPROP": "BPROP",
            "EMBEDDING": "EMBED",
            "POSITIONAL": "POS",
            "ENCODING": "ENC",
            
            # Symbolic AI
            "LOGICAL": "LOG",
            "INFERENCE": "INFER",
            "REASONING": "REASON",
            "THEOREM": "THRM",
            "PROVING": "PROOF",
            "PLANNING": "PLAN",
            "ALGORITHM": "ALGO",
            "AUTOMATED": "AUTO",
            "SYMBOLIC": "SYMB",
            "REGRESSION": "REGR",
            
            # Quantum computing
            "QUANTUM": "QNTM",
            "SUPERPOSITION": "SUPER",
            "ENTANGLEMENT": "ENTGL",
            "CORRECTION": "CORR",
            "ERROR": "ERR",
            "GATES": "GATE",
            
            # Meta programming
            "PROGRAMMING": "PROG",
            "METAPROG": "META",
            "REFLECTION": "REFL",
            "INTROSPECTION": "INTRO",
            "GENERATION": "GEN",
            "COMPILATION": "COMP",
            
            # General
            "OPERATION": "OP",
            "OPERATOR": "OPR",
            "STRUCTURE": "STRUCT",
            "PROPERTY": "PROP",
            "POINTER": "PTR",
            "MEMORY": "MEM",
            "ALLOCATION": "ALLOC",
            "DEREFERENCE": "DEREF",
            "IMPLIES": "IMP",
            "CONDITIONAL": "COND",
            "TERNARY": "TERN",
            "LOGICAL": "LOG",
            "BITWISE": "BIT",
            "SCALAR": "SCAL",
            "VECTOR": "VECT",
            "MATRIX": "MATR",
            "COMPLEX": "CMPLX",
            "INTEGER": "INT",
            "POLYNOMIAL": "POLY"
        }
    
    def load_registry(self) -> bool:
        """Carica registry."""
        try:
            with open(self.registry_path, 'r', encoding='utf-8') as f:
                self.registry = json.load(f)
            print(f"✅ Registry caricato: {len(self.registry.get('approved_symbols', []))} simboli")
            return True
        except Exception as e:
            print(f"❌ Errore caricamento registry: {e}")
            return False
    
    def generate_smart_fallback(self, original_fallback: str, code: str, name: str) -> str:
        """Genera fallback intelligente ≤8 caratteri preservando semantica."""
        # Rimuovi brackets
        clean_fallback = original_fallback.strip("[]")
        
        # Se già ≤8, mantieni
        if len(clean_fallback) <= 8:
            return f"[{clean_fallback}]"
        
        # Strategia 1: Usa mappings semantici
        for long_term, short_term in self.semantic_mappings.items():
            if long_term in clean_fallback:
                clean_fallback = clean_fallback.replace(long_term, short_term)
                if len(clean_fallback) <= 8:
                    return f"[{clean_fallback}]"
        
        # Strategia 2: Estrai da code ng:category:function
        if code.startswith("ng:"):
            parts = code.split(":")
            if len(parts) >= 3:
                category = parts[1][:3].upper()  # Prime 3 lettere categoria
                function = parts[2][:4].upper()  # Prime 4 lettere funzione
                candidate = f"{category}{function}"
                if len(candidate) <= 8:
                    return f"[{candidate}]"
        
        # Strategia 3: Usa name
        if len(name) <= 8:
            return f"[{name.upper()}]"
        
        # Strategia 4: Abbreviazione intelligente del name
        name_parts = name.split("_")
        if len(name_parts) > 1:
            # Prendi prime 2-3 lettere di ogni parte
            abbreviated = ""
            for part in name_parts:
                if len(abbreviated) + 2 <= 8:
                    abbreviated += part[:2].upper()
                elif len(abbreviated) + 1 <= 8:
                    abbreviated += part[:1].upper()
            if abbreviated:
                return f"[{abbreviated}]"
        
        # Strategia 5: Fallback generico con ID
        symbol_id = clean_fallback[:6] if len(clean_fallback) >= 6 else clean_fallback
        return f"[{symbol_id[:8]}]"
    
    def fix_symbol_fallback(self, symbol: Dict[str, Any]) -> Tuple[bool, str]:
        """Corregge fallback di un simbolo."""
        original_fallback = symbol.get("fallback", "")
        
        if len(original_fallback) <= 8:
            return False, "Already compliant"
        
        new_fallback = self.generate_smart_fallback(
            original_fallback,
            symbol.get("code", ""),
            symbol.get("name", "")
        )
        
        # Verifica unicità nel registry
        existing_fallbacks = {s.get("fallback", "") for s in self.registry.get("approved_symbols", [])}
        counter = 1
        base_fallback = new_fallback
        
        while new_fallback in existing_fallbacks:
            # Aggiungi numero per unicità
            base_clean = base_fallback.strip("[]")
            if len(base_clean) <= 6:
                new_fallback = f"[{base_clean}{counter}]"
            else:
                new_fallback = f"[{base_clean[:7]}{counter}]"
            counter += 1
            
            if counter > 99:  # Safety check
                new_fallback = f"[SYM{symbol.get('id', 'X')[-4:]}]"
                break
        
        symbol["fallback"] = new_fallback
        symbol["fallback_fixed"] = True
        symbol["fallback_fix_timestamp"] = datetime.now().isoformat()
        symbol["original_fallback"] = original_fallback
        
        return True, f"Fixed: {original_fallback} → {new_fallback}"
    
    def process_registry(self) -> bool:
        """Processa tutti i simboli del registry."""
        symbols = self.registry.get("approved_symbols", [])
        non_compliant = [s for s in symbols if len(s.get("fallback", "")) > 8]
        
        print(f"🔍 Simboli non conformi trovati: {len(non_compliant)}")
        
        if not non_compliant:
            print("✅ Tutti i simboli sono già conformi!")
            return True
        
        print(f"🔧 Inizio correzione fallback...")
        
        for i, symbol in enumerate(non_compliant):
            fixed, message = self.fix_symbol_fallback(symbol)
            if fixed:
                self.fixes_applied += 1
                print(f"  {i+1:3d}/{len(non_compliant)} {message}")
        
        # Aggiorna statistiche
        self.registry["stats"]["fallback_fix_applied"] = self.timestamp
        self.registry["stats"]["fallback_fixes_count"] = self.fixes_applied
        self.registry["stats"]["fallback_compliant"] = len([s for s in symbols if len(s.get("fallback", "")) <= 8])
        
        return True
    
    def save_registry(self) -> bool:
        """Salva registry corretto."""
        try:
            backup_path = self.registry_path.with_suffix(f".backup_{self.timestamp}.json")
            
            # Backup
            with open(backup_path, 'w', encoding='utf-8') as f:
                json.dump(self.registry, f, indent=2, ensure_ascii=False)
            print(f"💾 Backup salvato: {backup_path}")
            
            # Salva registry corretto
            with open(self.registry_path, 'w', encoding='utf-8') as f:
                json.dump(self.registry, f, indent=2, ensure_ascii=False)
            print(f"✅ Registry corretto salvato: {self.registry_path}")
            
            return True
        except Exception as e:
            print(f"❌ Errore salvataggio: {e}")
            return False

def main():
    import argparse
    
    parser = argparse.ArgumentParser(description="Fix NEUROGLYPH fallback compliance")
    parser.add_argument("--registry", default="neuroglyph/core/locked_registry_godmode_v9.json",
                       help="Path to registry file")
    parser.add_argument("--max-length", type=int, default=8,
                       help="Maximum fallback length")
    parser.add_argument("--preserve-semantics", action="store_true",
                       help="Preserve semantic meaning in abbreviations")
    
    args = parser.parse_args()
    
    print("🔧 NEUROGLYPH FALLBACK COMPLIANCE FIXER")
    print("=" * 40)
    
    fixer = FallbackComplianceFixer(args.registry)
    
    if not fixer.load_registry():
        sys.exit(1)
    
    if not fixer.process_registry():
        sys.exit(1)
    
    if not fixer.save_registry():
        sys.exit(1)
    
    print(f"\n🎉 CORREZIONE COMPLETATA!")
    print(f"📊 Simboli corretti: {fixer.fixes_applied}")
    print(f"✅ Registry aggiornato e salvato")

if __name__ == "__main__":
    main()
