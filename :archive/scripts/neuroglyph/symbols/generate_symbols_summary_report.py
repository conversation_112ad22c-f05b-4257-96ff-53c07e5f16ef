#!/usr/bin/env python3
"""
🧠 NEUROGLYPH ULTRA - Report Symbols Summary Generator
================================================================

Genera report_symbols_summary.md con percentuali e stato completo 
per ogni categoria di simboli NEUROGLYPH.

Autore: NEUROGLYPH ULTRA Team
Data: 2025-05-23
"""

import json
import logging
from pathlib import Path
from typing import Dict, List, Any
from datetime import datetime

# Setup logging
logging.basicConfig(level=logging.INFO)

class SymbolsSummaryReporter:
    """Generatore report completo simboli NEUROGLYPH."""
    
    def __init__(self):
        self.registry_path = Path("core/symbols_registry.json")
        self.config_path = Path("scripts/ultra_pipeline_config.json")
        self.report_path = Path("docs/report_symbols_summary.md")
        
        # Carica dati
        self.registry_data = self._load_registry()
        self.config_data = self._load_config()
        
        logging.info(f"🧠 Inizializzato Symbols Summary Reporter")
        
    def _load_registry(self) -> Dict[str, Any]:
        """Carica registry simboli."""
        try:
            with open(self.registry_path, 'r', encoding='utf-8') as f:
                return json.load(f)
        except Exception as e:
            logging.error(f"Errore caricamento registry: {e}")
            return {}
            
    def _load_config(self) -> Dict[str, Any]:
        """Carica configurazione pipeline."""
        try:
            with open(self.config_path, 'r', encoding='utf-8') as f:
                return json.load(f)
        except Exception as e:
            logging.warning(f"Configurazione non trovata: {e}")
            return {}
            
    def analyze_category_distribution(self) -> Dict[str, Any]:
        """Analizza distribuzione simboli per categoria."""
        categories_analysis = {}
        
        # Conta simboli per categoria dal registry
        category_counts = {}
        for symbol in self.registry_data.get("approved_symbols", []):
            category = symbol.get("category", "unknown")
            if category in category_counts:
                category_counts[category] += 1
            else:
                category_counts[category] = 1
                
        # Confronta con target dalla configurazione
        config_categories = self.config_data.get("categories", {})
        
        for category, count in category_counts.items():
            target = config_categories.get(category, {}).get("count_target", 0)
            percentage = config_categories.get(category, {}).get("percentage", 0)
            
            if target > 0:
                completion_rate = (count / target) * 100
                status = "✅ COMPLETO" if completion_rate >= 100 else "🔄 IN CORSO" if completion_rate >= 50 else "⚠️ BASSO"
            else:
                completion_rate = 0
                status = "❓ NON CONFIGURATO"
                
            categories_analysis[category] = {
                "current_count": count,
                "target_count": target,
                "target_percentage": percentage,
                "completion_rate": completion_rate,
                "status": status,
                "description": config_categories.get(category, {}).get("description", "Categoria non documentata")
            }
            
        return categories_analysis
        
    def analyze_symbol_quality(self) -> Dict[str, Any]:
        """Analizza qualità simboli."""
        quality_analysis = {
            "total_symbols": len(self.registry_data.get("approved_symbols", [])),
            "certified_symbols": 0,
            "auto_generated": 0,
            "manual_symbols": 0,
            "average_score": 0.0,
            "score_distribution": {"90-100": 0, "80-89": 0, "70-79": 0, "below_70": 0}
        }
        
        total_score = 0
        for symbol in self.registry_data.get("approved_symbols", []):
            # Status
            if symbol.get("status") == "certified":
                quality_analysis["certified_symbols"] += 1
                
            # Generazione
            if symbol.get("auto_generated", False):
                quality_analysis["auto_generated"] += 1
            else:
                quality_analysis["manual_symbols"] += 1
                
            # Score
            score = symbol.get("validation_score", 0)
            total_score += score
            
            if score >= 90:
                quality_analysis["score_distribution"]["90-100"] += 1
            elif score >= 80:
                quality_analysis["score_distribution"]["80-89"] += 1
            elif score >= 70:
                quality_analysis["score_distribution"]["70-79"] += 1
            else:
                quality_analysis["score_distribution"]["below_70"] += 1
                
        if quality_analysis["total_symbols"] > 0:
            quality_analysis["average_score"] = total_score / quality_analysis["total_symbols"]
            
        return quality_analysis
        
    def generate_markdown_report(self) -> str:
        """Genera report completo in formato Markdown."""
        categories_analysis = self.analyze_category_distribution()
        quality_analysis = self.analyze_symbol_quality()
        
        # Header
        report = f"""# 🧠 NEUROGLYPH ULTRA - Symbols Summary Report

**Generato:** {datetime.now().strftime("%Y-%m-%d %H:%M:%S")}  
**Versione Registry:** {self.registry_data.get("registry_version", "N/A")}  
**Simboli Totali:** {quality_analysis["total_symbols"]}/512  

---

## 📊 Stato Generale

### Statistiche Globali
- **Simboli Approvati:** {self.registry_data.get("stats", {}).get("approved", 0)}
- **Simboli Rigettati:** {self.registry_data.get("stats", {}).get("rejected", 0)}
- **Simboli Pending:** {self.registry_data.get("stats", {}).get("pending", 0)}
- **Score Medio:** {quality_analysis["average_score"]:.1f}%

### Qualità Simboli
- **Certificati:** {quality_analysis["certified_symbols"]} ({quality_analysis["certified_symbols"]/max(quality_analysis["total_symbols"],1)*100:.1f}%)
- **Auto-generati:** {quality_analysis["auto_generated"]} ({quality_analysis["auto_generated"]/max(quality_analysis["total_symbols"],1)*100:.1f}%)
- **Manuali:** {quality_analysis["manual_symbols"]} ({quality_analysis["manual_symbols"]/max(quality_analysis["total_symbols"],1)*100:.1f}%)

### Distribuzione Score
- **90-100%:** {quality_analysis["score_distribution"]["90-100"]} simboli
- **80-89%:** {quality_analysis["score_distribution"]["80-89"]} simboli  
- **70-79%:** {quality_analysis["score_distribution"]["70-79"]} simboli
- **<70%:** {quality_analysis["score_distribution"]["below_70"]} simboli

---

## 🏷️ Distribuzione per Categoria

| Categoria | Simboli | Target | Completamento | Status | Descrizione |
|-----------|---------|--------|---------------|--------|-------------|
"""

        # Tabella categorie
        for category, data in sorted(categories_analysis.items()):
            report += f"| **{category}** | {data['current_count']} | {data['target_count']} | {data['completion_rate']:.1f}% | {data['status']} | {data['description']} |\n"
            
        # Sezione dettagli categorie
        report += f"""
---

## 📈 Dettagli per Categoria

"""

        for category, data in sorted(categories_analysis.items()):
            progress_bar = "█" * int(data['completion_rate'] // 10) + "░" * (10 - int(data['completion_rate'] // 10))
            
            report += f"""### {category.upper()}
- **Simboli Attuali:** {data['current_count']}/{data['target_count']}
- **Completamento:** {data['completion_rate']:.1f}% `{progress_bar}`
- **Status:** {data['status']}
- **Descrizione:** {data['description']}

"""

        # Sezione reasoning (focus speciale)
        reasoning_data = categories_analysis.get("reasoning", {})
        if reasoning_data:
            report += f"""---

## 🧠 Focus: Categoria Reasoning (SOCRATE)

La categoria **reasoning** è fondamentale per SOCRATE, il sistema di ragionamento simbolico di NEUROGLYPH.

### Stato Reasoning
- **Simboli Generati:** {reasoning_data['current_count']}/{reasoning_data['target_count']}
- **Completamento:** {reasoning_data['completion_rate']:.1f}%
- **Status:** {reasoning_data['status']}

### Pattern Reasoning Coperti
I simboli reasoning coprono i seguenti pattern di ragionamento:
- **Deduzione/Induzione:** Ragionamento logico formale
- **Analogia/Metafora:** Ragionamento per similarità  
- **Causalità/Correlazione:** Relazioni causa-effetto
- **Astrazione/Generalizzazione:** Ragionamento di alto livello
- **Metacognizione:** Ragionamento sul ragionamento
- **Controllo/Monitoraggio:** Gestione processi cognitivi

"""

        # Footer
        report += f"""---

## 🚀 Prossimi Passi

### Priorità Immediate
1. **Completare categorie sotto-rappresentate** (< 80% completamento)
2. **Migliorare qualità simboli** con score < 90%
3. **Validare mapping AST → Reasoning** per SOCRATE
4. **Testare compressione neuroglifi** su casi reali

### Obiettivi ULTRA
- [{'✅' if quality_analysis['total_symbols'] >= 512 else '🔄'}] **512 simboli totali**
- [{'✅' if quality_analysis['average_score'] >= 90 else '🔄'}] **Score medio ≥ 90%**
- [{'✅' if reasoning_data.get('completion_rate', 0) >= 100 else '🔄'}] **Reasoning completo per SOCRATE**
- [{'✅' if all(data['completion_rate'] >= 80 for data in categories_analysis.values()) else '🔄'}] **Tutte categorie ≥ 80%**

---

## 📝 Note Tecniche

### Criteri Validazione
- **USU:** Unicode Simbolico Unico
- **CTU:** Codifica Testuale Unificata  
- **LCL:** Linguaggio Compatibile LLM

### Generazione
- **Pipeline automatica** con validazione ULTRA
- **Batch processing** per efficienza
- **Quality gates** per garantire standard

### Utilizzo
- **AST Mapping:** Collegamento pattern codice → simboli
- **Compressione:** Riduzione dimensioni mantenendo semantica
- **LLM Integration:** Compatibilità con tokenizer moderni

---

*Report generato automaticamente da NEUROGLYPH ULTRA Pipeline*  
*Per aggiornamenti: `python3 scripts/generate_symbols_summary_report.py`*
"""

        return report
        
    def save_report(self):
        """Salva report in file Markdown."""
        # Assicura che la directory docs esista
        self.report_path.parent.mkdir(exist_ok=True)
        
        # Genera e salva report
        report_content = self.generate_markdown_report()
        
        try:
            with open(self.report_path, 'w', encoding='utf-8') as f:
                f.write(report_content)
                
            logging.info(f"📄 Report salvato in: {self.report_path}")
            return True
            
        except Exception as e:
            logging.error(f"Errore salvataggio report: {e}")
            return False


def main():
    """Main function per generazione report simboli."""
    print("🧠 NEUROGLYPH ULTRA - Symbols Summary Report Generator")
    print("="*60)
    print("📊 Generazione report completo simboli con percentuali")
    print("📄 Output: docs/report_symbols_summary.md")
    print("="*60)
    
    # Inizializza reporter
    reporter = SymbolsSummaryReporter()
    
    # Genera e salva report
    if reporter.save_report():
        print("✅ Report generato con successo!")
        print(f"📄 File: {reporter.report_path}")
        
        # Mostra statistiche rapide
        quality = reporter.analyze_symbol_quality()
        categories = reporter.analyze_category_distribution()
        
        print(f"\n📊 STATISTICHE RAPIDE:")
        print(f"  • Simboli totali: {quality['total_symbols']}/512")
        print(f"  • Score medio: {quality['average_score']:.1f}%")
        print(f"  • Categorie: {len(categories)}")
        
        reasoning_data = categories.get("reasoning", {})
        if reasoning_data:
            print(f"  • Reasoning: {reasoning_data['current_count']}/{reasoning_data['target_count']} ({reasoning_data['completion_rate']:.1f}%)")
            
    else:
        print("❌ Errore generazione report!")
        return 1
        
    return 0


if __name__ == "__main__":
    exit(main())
