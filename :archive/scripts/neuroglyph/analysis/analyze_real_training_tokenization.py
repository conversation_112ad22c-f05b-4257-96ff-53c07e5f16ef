#!/usr/bin/env python3
"""
🔍 NEUROGLYPH Real Training Tokenization Analysis
================================================

Analizza come i simboli NEUROGLYPH sono stati REALMENTE tokenizzati durante il training.
Verifica se sono stati processati come single tokens o fallback subtokens.

CRITICAL INSIGHT:
- QLoRA fine-tuning NON modifica il tokenizer
- I simboli potrebbero essere stati processati come subtokens
- Il modello potrebbe aver imparato rappresentazioni subottimali
"""

import json
from transformers import AutoTokenizer
from pathlib import Path
from typing import Dict, List, Any
import logging

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class RealTrainingTokenizationAnalyzer:
    """Analizza la tokenizzazione reale durante il training."""
    
    def __init__(self):
        self.base_tokenizer_path = "Qwen/Qwen2.5-Coder-1.5B-Instruct"
        self.locked_state_file = "neuroglyph/training/safety/tokenizer_locked_state.json"
        
        # Carica tokenizer base (quello usato nel training)
        self.base_tokenizer = AutoTokenizer.from_pretrained(self.base_tokenizer_path)
        
        # Carica simboli critici
        with open(self.locked_state_file, 'r') as f:
            locked_state = json.load(f)
            self.critical_symbols = locked_state["critical_symbols"]
        
        logger.info("🔍 Real Training Tokenization Analyzer inizializzato")

    def analyze_training_tokenization(self) -> Dict[str, Any]:
        """Analizza come i simboli sono stati tokenizzati durante il training."""
        
        logger.info("🧪 Analizzando tokenizzazione reale del training...")
        
        results = {
            "single_token_symbols": [],
            "multi_token_symbols": [],
            "fallback_analysis": {},
            "tokenization_stats": {}
        }
        
        single_count = 0
        multi_count = 0
        total_tokens_used = 0
        
        print("🔍 REAL TRAINING TOKENIZATION ANALYSIS")
        print("=" * 60)
        print("Symbol → Tokens → Decoded → Status")
        print("-" * 60)
        
        for symbol in self.critical_symbols:
            # Tokenizza con il tokenizer base (quello usato nel training)
            tokens = self.base_tokenizer.encode(symbol, add_special_tokens=False)
            decoded = self.base_tokenizer.decode(tokens, skip_special_tokens=True)
            
            # Analizza risultato
            is_single = len(tokens) == 1
            is_faithful = decoded.strip() == symbol
            
            if is_single:
                single_count += 1
                results["single_token_symbols"].append({
                    "symbol": symbol,
                    "token_id": tokens[0],
                    "faithful": is_faithful
                })
                status = "✅ SINGLE"
            else:
                multi_count += 1
                results["multi_token_symbols"].append({
                    "symbol": symbol,
                    "tokens": tokens,
                    "token_count": len(tokens),
                    "faithful": is_faithful,
                    "decoded": decoded
                })
                status = f"❌ {len(tokens)} TOKENS"
            
            total_tokens_used += len(tokens)
            
            # Mostra risultato
            tokens_str = str(tokens) if len(tokens) <= 3 else f"[{len(tokens)} tokens]"
            print(f"{symbol:20} → {tokens_str:15} → '{decoded:15}' → {status}")
        
        # Calcola statistiche
        results["tokenization_stats"] = {
            "total_symbols": len(self.critical_symbols),
            "single_token_count": single_count,
            "multi_token_count": multi_count,
            "single_token_rate": single_count / len(self.critical_symbols),
            "average_tokens_per_symbol": total_tokens_used / len(self.critical_symbols),
            "total_tokens_used": total_tokens_used
        }
        
        return results

    def analyze_fallback_impact(self, tokenization_results: Dict) -> Dict[str, Any]:
        """Analizza l'impatto dei fallback subtokens."""
        
        logger.info("📊 Analizzando impatto fallback subtokens...")
        
        multi_token_symbols = tokenization_results["multi_token_symbols"]
        
        # Analizza pattern di fallback
        fallback_patterns = {}
        encoding_issues = []
        
        for symbol_data in multi_token_symbols:
            symbol = symbol_data["symbol"]
            tokens = symbol_data["tokens"]
            decoded = symbol_data["decoded"]
            
            # Analizza pattern di encoding
            if not symbol_data["faithful"]:
                encoding_issues.append({
                    "symbol": symbol,
                    "expected": symbol,
                    "actual": decoded,
                    "tokens": tokens
                })
            
            # Categorizza tipo di fallback
            if symbol.startswith("ng:"):
                category = "ng_symbols"
            elif any(ord(c) > 127 for c in symbol):
                category = "unicode_symbols"
            else:
                category = "ascii_symbols"
            
            if category not in fallback_patterns:
                fallback_patterns[category] = []
            
            fallback_patterns[category].append({
                "symbol": symbol,
                "token_count": len(tokens),
                "faithful": symbol_data["faithful"]
            })
        
        return {
            "fallback_patterns": fallback_patterns,
            "encoding_issues": encoding_issues,
            "critical_issues_count": len(encoding_issues)
        }

    def estimate_training_impact(self, tokenization_results: Dict) -> Dict[str, Any]:
        """Stima l'impatto sulla qualità del training."""
        
        logger.info("⚖️ Stimando impatto sulla qualità del training...")
        
        stats = tokenization_results["tokenization_stats"]
        
        # Calcola inefficienze
        token_efficiency = 1.0 / stats["average_tokens_per_symbol"]
        
        # Stima perdita di informazione simbolica
        single_rate = stats["single_token_rate"]
        
        # Calcola score di qualità simbolica
        symbolic_quality_score = (single_rate * 0.7) + (token_efficiency * 0.3)
        
        # Identifica problemi critici
        critical_issues = []
        
        if single_rate < 0.5:
            critical_issues.append("MAJORITY_SYMBOLS_FRAGMENTED")
        
        if stats["average_tokens_per_symbol"] > 2.0:
            critical_issues.append("HIGH_TOKEN_FRAGMENTATION")
        
        if len(tokenization_results["multi_token_symbols"]) > 20:
            critical_issues.append("EXCESSIVE_FALLBACK_USAGE")
        
        # Raccomandazioni
        recommendations = []
        
        if single_rate < 0.8:
            recommendations.append("🔧 Ricreare tokenizer con simboli come special tokens")
            recommendations.append("🔄 Re-training con tokenizer corretto")
        
        if stats["average_tokens_per_symbol"] > 1.5:
            recommendations.append("⚡ Ottimizzare encoding simbolico")
            recommendations.append("📊 Validare preservazione semantica")
        
        return {
            "token_efficiency": token_efficiency,
            "symbolic_quality_score": symbolic_quality_score,
            "critical_issues": critical_issues,
            "recommendations": recommendations,
            "training_quality_estimate": "SUBOPTIMAL" if symbolic_quality_score < 0.7 else "GOOD"
        }

    def generate_comprehensive_report(self) -> Dict[str, Any]:
        """Genera report completo dell'analisi."""
        
        logger.info("📋 Generando report completo...")
        
        # 1. Analizza tokenizzazione reale
        tokenization_results = self.analyze_training_tokenization()
        
        # 2. Analizza impatto fallback
        fallback_analysis = self.analyze_fallback_impact(tokenization_results)
        
        # 3. Stima impatto training
        training_impact = self.estimate_training_impact(tokenization_results)
        
        # 4. Compila report finale
        report = {
            "analysis_type": "real_training_tokenization",
            "tokenizer_used": self.base_tokenizer_path,
            "symbols_analyzed": len(self.critical_symbols),
            "tokenization_results": tokenization_results,
            "fallback_analysis": fallback_analysis,
            "training_impact": training_impact,
            "overall_assessment": self._calculate_overall_assessment(training_impact)
        }
        
        # Salva report
        report_file = f"real_training_tokenization_report.json"
        with open(report_file, 'w') as f:
            json.dump(report, f, indent=2, ensure_ascii=False)
        
        logger.info(f"📋 Report salvato: {report_file}")
        return report

    def _calculate_overall_assessment(self, training_impact: Dict) -> Dict[str, Any]:
        """Calcola assessment complessivo."""
        
        quality_score = training_impact["symbolic_quality_score"]
        critical_issues = len(training_impact["critical_issues"])
        
        if quality_score >= 0.8 and critical_issues == 0:
            grade = "A (Excellent)"
            status = "OPTIMAL_TRAINING"
        elif quality_score >= 0.7 and critical_issues <= 1:
            grade = "B (Good)"
            status = "ACCEPTABLE_TRAINING"
        elif quality_score >= 0.5:
            grade = "C (Fair)"
            status = "SUBOPTIMAL_TRAINING"
        else:
            grade = "D (Poor)"
            status = "PROBLEMATIC_TRAINING"
        
        return {
            "grade": grade,
            "status": status,
            "quality_score": quality_score,
            "critical_issues_count": critical_issues,
            "needs_retraining": quality_score < 0.7
        }

def main():
    """Main function per analisi tokenizzazione reale."""
    
    print("🔍 NEUROGLYPH REAL TRAINING TOKENIZATION ANALYSIS")
    print("🎯 Analyzing how symbols were ACTUALLY tokenized during training")
    print("=" * 80)
    
    # Inizializza analyzer
    analyzer = RealTrainingTokenizationAnalyzer()
    
    # Genera report completo
    report = analyzer.generate_comprehensive_report()
    
    # Mostra risultati chiave
    stats = report["tokenization_results"]["tokenization_stats"]
    impact = report["training_impact"]
    assessment = report["overall_assessment"]
    
    print(f"\n📊 KEY FINDINGS:")
    print(f"🔣 Total symbols analyzed: {stats['total_symbols']}")
    print(f"✅ Single token symbols: {stats['single_token_count']} ({stats['single_token_rate']:.1%})")
    print(f"❌ Multi-token symbols: {stats['multi_token_count']}")
    print(f"📈 Average tokens per symbol: {stats['average_tokens_per_symbol']:.2f}")
    
    print(f"\n⚖️ TRAINING IMPACT:")
    print(f"🎯 Symbolic quality score: {impact['symbolic_quality_score']:.3f}")
    print(f"⚡ Token efficiency: {impact['token_efficiency']:.3f}")
    print(f"🚨 Critical issues: {len(impact['critical_issues'])}")
    
    print(f"\n📝 OVERALL ASSESSMENT:")
    print(f"📊 Grade: {assessment['grade']}")
    print(f"🔧 Status: {assessment['status']}")
    print(f"🔄 Needs retraining: {'YES' if assessment['needs_retraining'] else 'NO'}")
    
    # Raccomandazioni
    if impact["recommendations"]:
        print(f"\n🔧 RECOMMENDATIONS:")
        for i, rec in enumerate(impact["recommendations"], 1):
            print(f"{i}. {rec}")
    
    print(f"\n🎉 Analysis completed!")
    print(f"📄 Full report: real_training_tokenization_report.json")

if __name__ == "__main__":
    main()
