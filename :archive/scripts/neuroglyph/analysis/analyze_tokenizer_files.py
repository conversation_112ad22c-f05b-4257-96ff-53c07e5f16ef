#!/usr/bin/env python3
"""
🔍 NEUROGLYPH Tokenizer Analysis
===============================

Analizza i file del tokenizer NEUROGLYPH per verificare la gestione dei simboli.
Esamina neuroglyph_tokenizer.model e neuroglyph_tokenizer.vocab.

Features:
- Analisi vocabolario simbolico
- Verifica mappings simboli
- Test tokenization/detokenization
- Validazione integrità simbolica
"""

import json
import struct
from pathlib import Path
from typing import Dict, List, Any, Optional
import logging

# Setup logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class NeuroglyphTokenizerAnalyzer:
    """Analizzatore per tokenizer NEUROGLYPH."""
    
    def __init__(self, tokenizer_dir: str = "neuroglyph/training/colab_package"):
        self.tokenizer_dir = Path(tokenizer_dir)
        self.vocab_file = self.tokenizer_dir / "neuroglyph_tokenizer.vocab"
        self.model_file = self.tokenizer_dir / "neuroglyph_tokenizer.model"
        
        self.vocab_data = {}
        self.model_data = {}
        self.symbol_mappings = {}
        
        logger.info("🔍 NEUROGLYPH Tokenizer Analyzer inizializzato")

    def analyze_vocab_file(self) -> Dict[str, Any]:
        """Analizza il file .vocab del tokenizer."""
        
        logger.info(f"📊 Analizzando vocab file: {self.vocab_file}")
        
        if not self.vocab_file.exists():
            logger.error(f"❌ File vocab non trovato: {self.vocab_file}")
            return {}
        
        vocab_entries = []
        symbol_entries = []
        special_entries = []
        
        try:
            with open(self.vocab_file, 'r', encoding='utf-8') as f:
                for line_num, line in enumerate(f, 1):
                    line = line.strip()
                    if not line:
                        continue
                    
                    parts = line.split('\t')
                    if len(parts) >= 2:
                        token = parts[0]
                        score = float(parts[1]) if parts[1] != '0' else 0.0
                        
                        vocab_entries.append({
                            "token": token,
                            "score": score,
                            "line": line_num
                        })
                        
                        # Classifica token
                        if self._is_neuroglyph_symbol(token):
                            symbol_entries.append({
                                "token": token,
                                "score": score,
                                "line": line_num
                            })
                        elif self._is_special_token(token):
                            special_entries.append({
                                "token": token,
                                "score": score,
                                "line": line_num
                            })
        
        except Exception as e:
            logger.error(f"❌ Errore lettura vocab: {e}")
            return {}
        
        analysis = {
            "total_entries": len(vocab_entries),
            "symbol_entries": len(symbol_entries),
            "special_entries": len(special_entries),
            "regular_entries": len(vocab_entries) - len(symbol_entries) - len(special_entries),
            "symbols_found": symbol_entries,
            "special_tokens": special_entries,
            "vocab_size": len(vocab_entries)
        }
        
        logger.info(f"📊 Vocab analysis: {analysis['total_entries']} total, {analysis['symbol_entries']} symbols")
        return analysis

    def _is_neuroglyph_symbol(self, token: str) -> bool:
        """Verifica se un token è un simbolo NEUROGLYPH."""
        
        # Simboli Unicode comuni in NEUROGLYPH
        neuroglyph_symbols = [
            "⚡", "🔄", "📊", "🧠", "📋", "🔢", "📝", "❓",
            "∑", "∫", "∂", "≈", "π", "ƒ", "∧", "∨", "→", "⊃",
            "⟲", "⋟", "⦀", "⟬", "⪕", "◿", "★", "⊵"
        ]
        
        # Verifica simboli diretti
        if token in neuroglyph_symbols:
            return True
        
        # Verifica pattern ng:
        if token.startswith("ng:"):
            return True
        
        # Verifica simboli Unicode matematici/tecnici
        if any(ord(c) > 127 and (
            0x2000 <= ord(c) <= 0x2BFF or  # Mathematical symbols
            0x1F000 <= ord(c) <= 0x1F9FF    # Emoji blocks
        ) for c in token):
            return True
        
        return False

    def _is_special_token(self, token: str) -> bool:
        """Verifica se un token è speciale."""
        
        special_patterns = [
            "<unk>", "<s>", "</s>", "<pad>", "<mask>",
            "<|im_start|>", "<|im_end|>", "<|endoftext|>",
            "<NG_START>", "<NG_END>", "<SCOPE>", "<SEM>", "<COMP>"
        ]
        
        return any(pattern in token for pattern in special_patterns)

    def analyze_model_file(self) -> Dict[str, Any]:
        """Analizza il file .model del tokenizer."""
        
        logger.info(f"🔧 Analizzando model file: {self.model_file}")
        
        if not self.model_file.exists():
            logger.error(f"❌ File model non trovato: {self.model_file}")
            return {}
        
        try:
            file_size = self.model_file.stat().st_size
            
            # Leggi header del file
            with open(self.model_file, 'rb') as f:
                # Primi bytes per identificare formato
                header = f.read(16)
                
                analysis = {
                    "file_size": file_size,
                    "header_bytes": header.hex(),
                    "format": self._identify_model_format(header),
                    "readable": file_size > 0
                }
            
            logger.info(f"🔧 Model analysis: {file_size} bytes, format: {analysis['format']}")
            return analysis
            
        except Exception as e:
            logger.error(f"❌ Errore lettura model: {e}")
            return {}

    def _identify_model_format(self, header: bytes) -> str:
        """Identifica il formato del file model."""
        
        # SentencePiece model magic number
        if header.startswith(b'\x08\x01'):
            return "SentencePiece"
        
        # Hugging Face tokenizer
        if b'tokenizer' in header.lower():
            return "HuggingFace"
        
        # Generic binary
        return "Binary"

    def test_symbol_tokenization(self) -> Dict[str, Any]:
        """Testa la tokenizzazione dei simboli."""
        
        logger.info("🧪 Testing symbol tokenization...")
        
        # Simboli critici da testare
        test_symbols = [
            "⚡", "🔄", "📊", "🧠", "∑", "∫", "∂", "∧", "∨", "→",
            "ng:operator:add", "ng:logic:implies", "ng:memory:alloc"
        ]
        
        try:
            # Prova a caricare tokenizer se disponibile
            from transformers import AutoTokenizer
            
            tokenizer = AutoTokenizer.from_pretrained(
                str(self.tokenizer_dir),
                local_files_only=True
            )
            
            results = []
            
            for symbol in test_symbols:
                try:
                    # Tokenize
                    tokens = tokenizer.encode(symbol, add_special_tokens=False)
                    
                    # Detokenize
                    decoded = tokenizer.decode(tokens, skip_special_tokens=True)
                    
                    # Verifica fedeltà
                    is_faithful = decoded.strip() == symbol
                    
                    results.append({
                        "symbol": symbol,
                        "tokens": tokens,
                        "decoded": decoded,
                        "faithful": is_faithful,
                        "token_count": len(tokens)
                    })
                    
                except Exception as e:
                    results.append({
                        "symbol": symbol,
                        "tokens": [],
                        "decoded": "",
                        "faithful": False,
                        "error": str(e)
                    })
            
            # Statistiche
            faithful_count = sum(1 for r in results if r.get('faithful', False))
            single_token_count = sum(1 for r in results if len(r.get('tokens', [])) == 1)
            
            analysis = {
                "total_tested": len(test_symbols),
                "faithful_tokenization": faithful_count,
                "single_token_symbols": single_token_count,
                "fidelity_rate": faithful_count / len(test_symbols),
                "single_token_rate": single_token_count / len(test_symbols),
                "detailed_results": results
            }
            
            logger.info(f"🧪 Tokenization test: {faithful_count}/{len(test_symbols)} faithful")
            return analysis
            
        except ImportError:
            logger.warning("⚠️ Transformers non disponibile per test tokenization")
            return {"error": "transformers_not_available"}
        except Exception as e:
            logger.error(f"❌ Errore test tokenization: {e}")
            return {"error": str(e)}

    def compare_with_locked_state(self) -> Dict[str, Any]:
        """Confronta con lo stato locked del tokenizer."""
        
        logger.info("🔒 Confrontando con tokenizer locked state...")
        
        locked_state_file = Path("neuroglyph/training/safety/tokenizer_locked_state.json")
        
        if not locked_state_file.exists():
            logger.warning("⚠️ File locked state non trovato")
            return {"error": "locked_state_not_found"}
        
        try:
            with open(locked_state_file, 'r') as f:
                locked_state = json.load(f)
            
            locked_symbols = locked_state.get("locked_symbol_ids", {})
            critical_symbols = locked_state.get("critical_symbols", [])
            
            # Verifica presenza simboli critici nel vocab
            vocab_analysis = self.analyze_vocab_file()
            vocab_symbols = [entry["token"] for entry in vocab_analysis.get("symbols_found", [])]
            
            missing_symbols = []
            present_symbols = []
            
            for symbol in critical_symbols:
                if symbol in vocab_symbols:
                    present_symbols.append(symbol)
                else:
                    missing_symbols.append(symbol)
            
            comparison = {
                "locked_symbols_count": len(locked_symbols),
                "critical_symbols_count": len(critical_symbols),
                "present_in_vocab": len(present_symbols),
                "missing_from_vocab": len(missing_symbols),
                "coverage_rate": len(present_symbols) / len(critical_symbols) if critical_symbols else 0,
                "missing_symbols": missing_symbols,
                "present_symbols": present_symbols
            }
            
            logger.info(f"🔒 Locked state comparison: {len(present_symbols)}/{len(critical_symbols)} symbols present")
            return comparison
            
        except Exception as e:
            logger.error(f"❌ Errore confronto locked state: {e}")
            return {"error": str(e)}

    def generate_comprehensive_report(self) -> Dict[str, Any]:
        """Genera report completo dell'analisi tokenizer."""
        
        logger.info("📋 Generando report completo...")
        
        report = {
            "timestamp": str(Path(__file__).stat().st_mtime),
            "tokenizer_directory": str(self.tokenizer_dir),
            "vocab_analysis": self.analyze_vocab_file(),
            "model_analysis": self.analyze_model_file(),
            "tokenization_test": self.test_symbol_tokenization(),
            "locked_state_comparison": self.compare_with_locked_state()
        }
        
        # Calcola score complessivo
        vocab_score = 1.0 if report["vocab_analysis"].get("symbol_entries", 0) > 0 else 0.0
        model_score = 1.0 if report["model_analysis"].get("readable", False) else 0.0
        tokenization_score = report["tokenization_test"].get("fidelity_rate", 0.0)
        locked_score = report["locked_state_comparison"].get("coverage_rate", 0.0)
        
        overall_score = (vocab_score + model_score + tokenization_score + locked_score) / 4
        
        report["overall_assessment"] = {
            "vocab_score": vocab_score,
            "model_score": model_score,
            "tokenization_score": tokenization_score,
            "locked_state_score": locked_score,
            "overall_score": overall_score,
            "grade": self._calculate_grade(overall_score)
        }
        
        # Salva report
        report_file = f"tokenizer_analysis_report_{int(time.time())}.json"
        with open(report_file, 'w') as f:
            json.dump(report, f, indent=2, ensure_ascii=False)
        
        logger.info(f"📋 Report salvato: {report_file}")
        return report

    def _calculate_grade(self, score: float) -> str:
        """Calcola grade basato su score."""
        
        if score >= 0.9:
            return "A+ (Excellent)"
        elif score >= 0.8:
            return "A (Very Good)"
        elif score >= 0.7:
            return "B (Good)"
        elif score >= 0.6:
            return "C (Fair)"
        else:
            return "D (Needs Improvement)"

def main():
    """Main function per analisi tokenizer."""
    
    print("🔍 NEUROGLYPH TOKENIZER ANALYSIS")
    print("🎯 Comprehensive tokenizer file analysis")
    print("=" * 60)
    
    # Inizializza analyzer
    analyzer = NeuroglyphTokenizerAnalyzer()
    
    # Genera report completo
    report = analyzer.generate_comprehensive_report()
    
    # Mostra risultati
    assessment = report["overall_assessment"]
    
    print(f"\n📊 ANALYSIS RESULTS:")
    print(f"🔤 Vocab Score: {assessment['vocab_score']:.2f}")
    print(f"🔧 Model Score: {assessment['model_score']:.2f}")
    print(f"🧪 Tokenization Score: {assessment['tokenization_score']:.2f}")
    print(f"🔒 Locked State Score: {assessment['locked_state_score']:.2f}")
    print(f"🎯 Overall Score: {assessment['overall_score']:.2f}")
    print(f"📝 Grade: {assessment['grade']}")
    
    # Dettagli simboli
    vocab_analysis = report["vocab_analysis"]
    if vocab_analysis:
        print(f"\n🔣 SYMBOL DETAILS:")
        print(f"📊 Total vocab entries: {vocab_analysis.get('total_entries', 0)}")
        print(f"🔣 Symbol entries: {vocab_analysis.get('symbol_entries', 0)}")
        print(f"⚙️ Special tokens: {vocab_analysis.get('special_entries', 0)}")
    
    # Test tokenization
    tokenization_test = report["tokenization_test"]
    if tokenization_test and "fidelity_rate" in tokenization_test:
        print(f"\n🧪 TOKENIZATION TEST:")
        print(f"✅ Fidelity rate: {tokenization_test['fidelity_rate']:.2%}")
        print(f"🎯 Single token rate: {tokenization_test.get('single_token_rate', 0):.2%}")
    
    print(f"\n🎉 Analysis completed!")

if __name__ == "__main__":
    import time
    main()
