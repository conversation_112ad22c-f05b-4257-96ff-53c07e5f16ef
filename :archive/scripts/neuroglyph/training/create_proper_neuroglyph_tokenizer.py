#!/usr/bin/env python3
"""
🔧 Create Proper NEUROGLYPH Tokenizer
====================================

Crea il tokenizer NEUROGLYPH corretto con simboli come special tokens.
Questo tokenizer deve essere usato PRIMA del training per garantire
che i simboli siano processati come single tokens.

CRITICAL: Questo risolve il problema della frammentazione simbolica.
"""

import json
from transformers import AutoTokenizer
from pathlib import Path
from typing import Dict, List, Any
import logging

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class ProperNeuroglyphTokenizer:
    """Crea tokenizer NEUROGLYPH con simboli integrati correttamente."""
    
    def __init__(self):
        self.base_model = "Qwen/Qwen2.5-Coder-1.5B-Instruct"
        self.locked_state_file = "neuroglyph/training/safety/tokenizer_locked_state.json"
        self.output_dir = "neuroglyph/training/proper_tokenizer"
        
        # Carica simboli critici
        with open(self.locked_state_file, 'r') as f:
            locked_state = json.load(f)
            self.critical_symbols = locked_state["critical_symbols"]
        
        logger.info("🔧 Proper NEUROGLYPH Tokenizer Creator inizializzato")

    def create_proper_tokenizer(self) -> AutoTokenizer:
        """Crea tokenizer con simboli NEUROGLYPH come special tokens."""
        
        logger.info("🚀 Creando tokenizer NEUROGLYPH corretto...")
        
        # 1. Carica tokenizer base
        tokenizer = AutoTokenizer.from_pretrained(self.base_model)
        original_vocab_size = len(tokenizer.vocab)
        
        logger.info(f"📊 Tokenizer base caricato: {original_vocab_size} tokens")
        
        # 2. Separa simboli Unicode da simboli ng:
        unicode_symbols = []
        ng_symbols = []
        
        for symbol in self.critical_symbols:
            if symbol.startswith("ng:"):
                ng_symbols.append(symbol)
            else:
                unicode_symbols.append(symbol)
        
        logger.info(f"🔣 Simboli Unicode: {len(unicode_symbols)}")
        logger.info(f"🧠 Simboli ng:: {len(ng_symbols)}")
        
        # 3. Aggiungi simboli come special tokens
        special_tokens_dict = {
            "additional_special_tokens": self.critical_symbols
        }
        
        num_added = tokenizer.add_special_tokens(special_tokens_dict)
        logger.info(f"✅ Aggiunti {num_added} special tokens")
        
        # 4. Verifica che tutti i simboli siano single tokens
        self._verify_single_token_mapping(tokenizer)
        
        # 5. Salva tokenizer
        Path(self.output_dir).mkdir(parents=True, exist_ok=True)
        tokenizer.save_pretrained(self.output_dir)
        
        logger.info(f"💾 Tokenizer salvato in: {self.output_dir}")
        logger.info(f"📈 Vocab size: {original_vocab_size} → {len(tokenizer.vocab)}")
        
        return tokenizer

    def _verify_single_token_mapping(self, tokenizer: AutoTokenizer) -> Dict[str, Any]:
        """Verifica che tutti i simboli siano mappati come single tokens."""
        
        logger.info("🔍 Verificando mapping single token...")
        
        verification_results = {
            "single_token_symbols": [],
            "multi_token_symbols": [],
            "failed_symbols": []
        }
        
        print("\n🔍 SYMBOL VERIFICATION")
        print("=" * 50)
        print("Symbol → Tokens → Status")
        print("-" * 50)
        
        for symbol in self.critical_symbols:
            try:
                tokens = tokenizer.encode(symbol, add_special_tokens=False)
                decoded = tokenizer.decode(tokens, skip_special_tokens=True)
                
                if len(tokens) == 1 and decoded.strip() == symbol:
                    verification_results["single_token_symbols"].append({
                        "symbol": symbol,
                        "token_id": tokens[0]
                    })
                    status = "✅ SINGLE"
                elif len(tokens) == 1:
                    verification_results["failed_symbols"].append({
                        "symbol": symbol,
                        "tokens": tokens,
                        "decoded": decoded,
                        "issue": "DECODING_MISMATCH"
                    })
                    status = "❌ DECODE_FAIL"
                else:
                    verification_results["multi_token_symbols"].append({
                        "symbol": symbol,
                        "tokens": tokens,
                        "token_count": len(tokens)
                    })
                    status = f"❌ {len(tokens)} TOKENS"
                
                print(f"{symbol:25} → {tokens[0] if len(tokens)==1 else tokens} → {status}")
                
            except Exception as e:
                verification_results["failed_symbols"].append({
                    "symbol": symbol,
                    "error": str(e)
                })
                print(f"{symbol:25} → ERROR: {e}")
        
        # Statistiche
        total = len(self.critical_symbols)
        single_count = len(verification_results["single_token_symbols"])
        multi_count = len(verification_results["multi_token_symbols"])
        failed_count = len(verification_results["failed_symbols"])
        
        success_rate = single_count / total
        
        print(f"\n📊 VERIFICATION RESULTS:")
        print(f"✅ Single tokens: {single_count}/{total} ({success_rate:.1%})")
        print(f"❌ Multi tokens: {multi_count}")
        print(f"💥 Failed: {failed_count}")
        
        if success_rate >= 0.95:
            print("🏆 EXCELLENT: Tokenizer is properly configured!")
        elif success_rate >= 0.8:
            print("✅ GOOD: Most symbols properly tokenized")
        else:
            print("⚠️ WARNING: Many symbols not properly tokenized")
        
        return verification_results

    def create_training_config(self, tokenizer: AutoTokenizer) -> Dict[str, Any]:
        """Crea configurazione per training con tokenizer corretto."""
        
        config = {
            "tokenizer_path": self.output_dir,
            "vocab_size": len(tokenizer.vocab),
            "special_tokens_count": len(self.critical_symbols),
            "model_config_updates": {
                "vocab_size": len(tokenizer.vocab),
                "pad_token_id": tokenizer.pad_token_id,
                "eos_token_id": tokenizer.eos_token_id,
                "bos_token_id": tokenizer.bos_token_id
            },
            "training_notes": [
                "CRITICAL: Use this tokenizer for training",
                "Model vocab_size must be updated to match tokenizer",
                "Embedding layer must be resized before training",
                "All symbols should tokenize as single tokens"
            ]
        }
        
        # Salva configurazione
        config_file = Path(self.output_dir) / "training_config.json"
        with open(config_file, 'w') as f:
            json.dump(config, f, indent=2)
        
        logger.info(f"📋 Training config salvata: {config_file}")
        return config

    def generate_retraining_script(self) -> str:
        """Genera script per re-training con tokenizer corretto."""
        
        script = f'''#!/usr/bin/env python3
"""
🔄 NEUROGLYPH Re-training with Proper Tokenizer
==============================================

Re-training script che usa il tokenizer NEUROGLYPH corretto.
CRITICAL: Questo risolve il problema della frammentazione simbolica.
"""

from transformers import AutoTokenizer, AutoModelForCausalLM
from unsloth import FastLanguageModel
import torch

def retrain_neuroglyph_proper():
    print("🔄 Re-training NEUROGLYPH with proper tokenizer...")
    
    # 1. Carica tokenizer corretto
    tokenizer = AutoTokenizer.from_pretrained("{self.output_dir}")
    print(f"✅ Tokenizer caricato: {{len(tokenizer.vocab)}} tokens")
    
    # 2. Carica modello base
    model, _ = FastLanguageModel.from_pretrained(
        model_name="{self.base_model}",
        max_seq_length=2048,
        dtype=None,
        load_in_4bit=True,
    )
    
    # 3. CRITICAL: Ridimensiona embedding layer
    model.resize_token_embeddings(len(tokenizer.vocab))
    print(f"🔧 Embedding layer resized to {{len(tokenizer.vocab)}}")
    
    # 4. Verifica simboli
    test_symbols = ["⚡", "🔄", "ng:operator:add", "ng:logic:implies"]
    for symbol in test_symbols:
        tokens = tokenizer.encode(symbol, add_special_tokens=False)
        print(f"🔍 {{symbol}} → {{tokens}} ({{len(tokens)}} tokens)")
    
    # 5. Configura LoRA
    model = FastLanguageModel.get_peft_model(
        model,
        r=16,
        target_modules=["q_proj", "k_proj", "v_proj", "o_proj",
                       "gate_proj", "up_proj", "down_proj"],
        lora_alpha=16,
        lora_dropout=0,
        bias="none",
        use_gradient_checkpointing="unsloth",
        random_state=3407,
    )
    
    print("🚀 Ready for proper NEUROGLYPH training!")
    return model, tokenizer

if __name__ == "__main__":
    model, tokenizer = retrain_neuroglyph_proper()
    print("✅ Setup completed - ready for training!")
'''
        
        script_file = "retrain_neuroglyph_proper.py"
        with open(script_file, 'w') as f:
            f.write(script)
        
        logger.info(f"📝 Re-training script generato: {script_file}")
        return script

def main():
    """Main function per creazione tokenizer corretto."""
    
    print("🔧 NEUROGLYPH PROPER TOKENIZER CREATOR")
    print("🎯 Creating tokenizer with symbols as special tokens")
    print("=" * 60)
    
    # Inizializza creator
    creator = ProperNeuroglyphTokenizer()
    
    # Crea tokenizer corretto
    tokenizer = creator.create_proper_tokenizer()
    
    # Crea configurazione training
    config = creator.create_training_config(tokenizer)
    
    # Genera script re-training
    script = creator.generate_retraining_script()
    
    print(f"\n🎉 PROPER TOKENIZER CREATED!")
    print(f"📁 Location: {creator.output_dir}")
    print(f"📊 Vocab size: {len(tokenizer.vocab)}")
    print(f"🔣 Special tokens: {len(creator.critical_symbols)}")
    
    print(f"\n🔄 NEXT STEPS:")
    print(f"1. 🧪 Test tokenizer: python test_proper_tokenizer.py")
    print(f"2. 🔄 Re-train model: python retrain_neuroglyph_proper.py")
    print(f"3. 🔍 Validate results: python validate_symbolic_training.py")
    
    print(f"\n⚠️ CRITICAL: Use this tokenizer for all future training!")

if __name__ == "__main__":
    main()
