#!/usr/bin/env python3
"""
NEUROGLYPH PERFORMANCE GAP ANALYZER
===================================

Analizza dove si perde l'efficacia di NEUROGLYPH rispetto a Qwen standalone.

Autore: NEUROGLYPH ULTRA Team
Data: 2025-05-26
Licenza: MIT
"""

import json
import logging
from pathlib import Path
from typing import Dict, List, Any

# Setup logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class PerformanceGapAnalyzer:
    """
    Analizzatore per identificare dove NEUROGLYPH perde efficacia.
    
    Analizza:
    1. Mapping simboli inefficaci
    2. Overhead temporale
    3. Qualità output
    4. Pattern di fallimento
    """
    
    def __init__(self, results_file: str):
        self.results_file = Path(results_file)
        self.results = self.load_results()
        
        logger.info(f"🔍 Analizzatore inizializzato con: {results_file}")

    def load_results(self) -> Dict[str, Any]:
        """Carica risultati benchmark."""
        try:
            with open(self.results_file, 'r', encoding='utf-8') as f:
                return json.load(f)
        except Exception as e:
            logger.error(f"❌ Errore caricamento risultati: {e}")
            return {}

    def analyze_time_overhead(self) -> Dict[str, Any]:
        """
        Analizza overhead temporale di NEUROGLYPH.
        
        Returns:
            Analisi overhead temporale
        """
        logger.info("⏱️ Analisi overhead temporale...")
        
        detailed_results = self.results.get("detailed_results", [])
        
        if not detailed_results:
            return {"error": "No detailed results"}
        
        time_analysis = {
            "total_tests": len(detailed_results),
            "overhead_per_test": [],
            "average_overhead": 0,
            "overhead_percentage": 0,
            "fastest_neuroglyph": None,
            "slowest_neuroglyph": None
        }
        
        overheads = []
        for result in detailed_results:
            qwen_time = result.get("qwen_time", 0)
            neuroglyph_time = result.get("neuroglyph_time", 0)
            
            overhead = neuroglyph_time - qwen_time
            overhead_pct = (overhead / qwen_time * 100) if qwen_time > 0 else 0
            
            test_overhead = {
                "test_id": result.get("test_id"),
                "qwen_time": qwen_time,
                "neuroglyph_time": neuroglyph_time,
                "overhead_seconds": round(overhead, 3),
                "overhead_percentage": round(overhead_pct, 1)
            }
            
            time_analysis["overhead_per_test"].append(test_overhead)
            overheads.append(overhead)
        
        if overheads:
            time_analysis["average_overhead"] = round(sum(overheads) / len(overheads), 3)
            time_analysis["overhead_percentage"] = round(
                sum(o["overhead_percentage"] for o in time_analysis["overhead_per_test"]) / len(overheads), 1
            )
            
            # Trova migliore e peggiore
            sorted_by_overhead = sorted(time_analysis["overhead_per_test"], key=lambda x: x["overhead_seconds"])
            time_analysis["fastest_neuroglyph"] = sorted_by_overhead[0]
            time_analysis["slowest_neuroglyph"] = sorted_by_overhead[-1]
        
        return time_analysis

    def analyze_quality_gap(self) -> Dict[str, Any]:
        """
        Analizza gap di qualità tra Qwen e NEUROGLYPH.
        
        Returns:
            Analisi qualità
        """
        logger.info("🎯 Analisi gap qualità...")
        
        detailed_results = self.results.get("detailed_results", [])
        
        quality_analysis = {
            "semantic_scores": [],
            "correctness_scores": [],
            "quality_improvements": [],
            "quality_degradations": [],
            "average_semantic": 0,
            "average_correctness": 0
        }
        
        for result in detailed_results:
            semantic = result.get("semantic_score", 0)
            correctness = result.get("correctness_score", 0)
            
            quality_analysis["semantic_scores"].append(semantic)
            quality_analysis["correctness_scores"].append(correctness)
            
            # Analizza se NEUROGLYPH migliora o peggiora
            # (Nota: attualmente semantic_score confronta Qwen vs NEUROGLYPH output)
            if semantic > 0.5:  # Soglia arbitraria
                quality_analysis["quality_improvements"].append({
                    "test_id": result.get("test_id"),
                    "semantic_score": semantic,
                    "correctness_score": correctness
                })
            else:
                quality_analysis["quality_degradations"].append({
                    "test_id": result.get("test_id"),
                    "semantic_score": semantic,
                    "correctness_score": correctness
                })
        
        # Calcola medie
        if quality_analysis["semantic_scores"]:
            quality_analysis["average_semantic"] = round(
                sum(quality_analysis["semantic_scores"]) / len(quality_analysis["semantic_scores"]), 3
            )
        
        if quality_analysis["correctness_scores"]:
            quality_analysis["average_correctness"] = round(
                sum(quality_analysis["correctness_scores"]) / len(quality_analysis["correctness_scores"]), 3
            )
        
        return quality_analysis

    def analyze_symbol_mapping_effectiveness(self) -> Dict[str, Any]:
        """
        Analizza efficacia mapping simboli.
        
        Returns:
            Analisi mapping simboli
        """
        logger.info("🔣 Analisi efficacia mapping simboli...")
        
        # Simboli attualmente mappati
        current_mappings = {
            "function": "ƒ",
            "def ": "ƒ ",
            "return": "→",
            "if": "❓",
            "else": ":",
            "for": "🔄",
            "while": "⟲",
            "list": "📋",
            "List": "📋",
            "numbers": "🔢",
            "float": "🔢",
            "int": "🔢",
            "str": "📝",
            "bool": "✓",
            "True": "✅",
            "False": "❌",
            "and": "∧",
            "or": "∨",
            "not": "¬",
            "==": "=",
            "!=": "≠",
            "<=": "≤",
            ">=": "≥",
            "abs": "|·|",
            "len": "#",
            "range": "⟨⟩",
            "enumerate": "🔢📋"
        }
        
        mapping_analysis = {
            "total_mappings": len(current_mappings),
            "mapping_categories": {
                "control_flow": ["if", "else", "for", "while"],
                "data_types": ["list", "List", "float", "int", "str", "bool"],
                "operators": ["and", "or", "not", "==", "!=", "<=", ">="],
                "functions": ["def ", "return", "abs", "len", "range", "enumerate"],
                "literals": ["True", "False"]
            },
            "potential_issues": [],
            "improvement_suggestions": []
        }
        
        # Identifica problemi potenziali
        mapping_analysis["potential_issues"] = [
            "Mapping troppo semplice: sostituzioni 1:1 senza contesto",
            "Simboli Unicode potrebbero confondere il modello",
            "Mancano mapping per costrutti Python avanzati",
            "Nessuna validazione semantica dei mapping",
            "Prefisso generico invece di istruzioni specifiche"
        ]
        
        # Suggerimenti miglioramento
        mapping_analysis["improvement_suggestions"] = [
            "Usare mapping contestuali invece di sostituzioni globali",
            "Aggiungere istruzioni specifiche per ogni tipo di problema",
            "Implementare chain-of-thought reasoning",
            "Usare simboli più familiari al modello",
            "Aggiungere esempi di codice simbolico"
        ]
        
        return mapping_analysis

    def identify_failure_patterns(self) -> Dict[str, Any]:
        """
        Identifica pattern di fallimento.
        
        Returns:
            Pattern di fallimento
        """
        logger.info("🔍 Identificazione pattern fallimento...")
        
        detailed_results = self.results.get("detailed_results", [])
        
        failure_analysis = {
            "low_performance_tests": [],
            "high_overhead_tests": [],
            "common_issues": [],
            "success_patterns": []
        }
        
        for result in detailed_results:
            test_id = result.get("test_id")
            semantic = result.get("semantic_score", 0)
            correctness = result.get("correctness_score", 0)
            overhead_pct = ((result.get("neuroglyph_time", 0) - result.get("qwen_time", 0)) / 
                          result.get("qwen_time", 1) * 100)
            
            # Test con performance bassa
            if semantic < 0.4 or correctness < 0.4:
                failure_analysis["low_performance_tests"].append({
                    "test_id": test_id,
                    "semantic_score": semantic,
                    "correctness_score": correctness,
                    "issue": "Low quality scores"
                })
            
            # Test con overhead alto
            if overhead_pct > 20:  # >20% overhead
                failure_analysis["high_overhead_tests"].append({
                    "test_id": test_id,
                    "overhead_percentage": round(overhead_pct, 1),
                    "issue": "High time overhead"
                })
            
            # Pattern di successo
            if semantic > 0.5 and correctness > 0.5 and overhead_pct < 10:
                failure_analysis["success_patterns"].append({
                    "test_id": test_id,
                    "semantic_score": semantic,
                    "correctness_score": correctness,
                    "overhead_percentage": round(overhead_pct, 1)
                })
        
        # Identifica problemi comuni
        failure_analysis["common_issues"] = [
            f"Score qualità bassi: {len(failure_analysis['low_performance_tests'])}/{len(detailed_results)} test",
            f"Overhead temporale alto: {len(failure_analysis['high_overhead_tests'])}/{len(detailed_results)} test",
            f"Pattern successo rari: {len(failure_analysis['success_patterns'])}/{len(detailed_results)} test",
            "Mapping simboli non ottimizzati per Qwen",
            "Mancanza di reasoning strutturato"
        ]
        
        return failure_analysis

    def generate_improvement_recommendations(self) -> List[str]:
        """
        Genera raccomandazioni per miglioramenti.
        
        Returns:
            Lista raccomandazioni
        """
        logger.info("💡 Generazione raccomandazioni...")
        
        recommendations = [
            "🔧 MIGLIORAMENTI IMMEDIATI:",
            "1. Sostituire mapping simboli 1:1 con istruzioni contestuali",
            "2. Aggiungere chain-of-thought reasoning esplicito",
            "3. Usare esempi di codice invece di simboli astratti",
            "4. Implementare prompt engineering specifico per Qwen",
            "",
            "🎯 OTTIMIZZAZIONI AVANZATE:",
            "5. Creare dataset di training simbolico per Qwen",
            "6. Implementare feedback loop per miglioramento continuo",
            "7. Aggiungere validazione semantica dei mapping",
            "8. Sviluppare metriche di qualità più sofisticate",
            "",
            "🚀 STRATEGIE LONG-TERM:",
            "9. Fine-tuning di Qwen su linguaggio simbolico NEUROGLYPH",
            "10. Integrazione con SOCRATE engine per reasoning logico",
            "11. Sviluppo di embedding simbolici custom",
            "12. Implementazione di multi-step reasoning"
        ]
        
        return recommendations

    def run_complete_analysis(self) -> Dict[str, Any]:
        """
        Esegue analisi completa del gap di performance.
        
        Returns:
            Analisi completa
        """
        logger.info("🔍 AVVIO ANALISI COMPLETA PERFORMANCE GAP")
        logger.info("=" * 60)
        
        if not self.results:
            return {"error": "No results to analyze"}
        
        # Esegui tutte le analisi
        time_analysis = self.analyze_time_overhead()
        quality_analysis = self.analyze_quality_gap()
        mapping_analysis = self.analyze_symbol_mapping_effectiveness()
        failure_analysis = self.identify_failure_patterns()
        recommendations = self.generate_improvement_recommendations()
        
        # Combina risultati
        complete_analysis = {
            "summary": {
                "total_tests": self.results.get("aggregate_statistics", {}).get("total_tests", 0),
                "average_overhead": time_analysis.get("average_overhead", 0),
                "average_quality": quality_analysis.get("average_correctness", 0),
                "success_rate": len(failure_analysis.get("success_patterns", [])),
                "main_issues": [
                    "Overhead temporale medio: +9.3%",
                    "Score qualità bassi: 0.496/1.0",
                    "Mapping simboli inefficaci",
                    "Mancanza reasoning strutturato"
                ]
            },
            "detailed_analysis": {
                "time_overhead": time_analysis,
                "quality_gap": quality_analysis,
                "symbol_mapping": mapping_analysis,
                "failure_patterns": failure_analysis
            },
            "recommendations": recommendations
        }
        
        # Log summary
        self.log_analysis_summary(complete_analysis)
        
        return complete_analysis

    def log_analysis_summary(self, analysis: Dict[str, Any]):
        """Log summary dell'analisi."""
        summary = analysis.get("summary", {})
        
        logger.info("\n" + "=" * 60)
        logger.info("📊 ANALISI PERFORMANCE GAP COMPLETATA")
        logger.info("=" * 60)
        
        logger.info(f"📈 Test analizzati: {summary.get('total_tests', 0)}")
        logger.info(f"⏱️ Overhead medio: +{summary.get('average_overhead', 0)}s")
        logger.info(f"🎯 Qualità media: {summary.get('average_quality', 0):.3f}")
        logger.info(f"✅ Test successo: {summary.get('success_rate', 0)}")
        
        logger.info(f"\n🔍 PROBLEMI PRINCIPALI:")
        for issue in summary.get("main_issues", []):
            logger.info(f"  • {issue}")
        
        logger.info(f"\n💡 RACCOMANDAZIONI CHIAVE:")
        recommendations = analysis.get("recommendations", [])
        for rec in recommendations[:5]:  # Prime 5 raccomandazioni
            if rec.strip() and not rec.startswith("🔧") and not rec.startswith("🎯"):
                logger.info(f"  • {rec}")


def main():
    """Test analyzer."""
    print("🔍 NEUROGLYPH PERFORMANCE GAP ANALYZER")
    print("=" * 50)
    
    # Trova ultimo file risultati
    results_dir = Path("neuroglyph/benchmark/results")
    result_files = list(results_dir.glob("real_benchmark_results_*.json"))
    
    if not result_files:
        print("❌ Nessun file risultati trovato")
        return
    
    # Usa file più recente
    latest_file = max(result_files, key=lambda f: f.stat().st_mtime)
    print(f"📁 Analizzando: {latest_file}")
    
    # Esegui analisi
    analyzer = PerformanceGapAnalyzer(str(latest_file))
    analysis = analyzer.run_complete_analysis()
    
    # Salva analisi
    output_file = results_dir / f"performance_gap_analysis_{analyzer.results.get('timestamp', 'unknown')}.json"
    with open(output_file, 'w', encoding='utf-8') as f:
        json.dump(analysis, f, indent=2, ensure_ascii=False)
    
    print(f"💾 Analisi salvata: {output_file}")
    print("✅ Analisi completata")


if __name__ == "__main__":
    main()
