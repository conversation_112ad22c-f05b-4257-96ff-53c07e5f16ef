#!/usr/bin/env python3
# 🔧 NEUROGLYPH Tokenizer Fix Script

import json
from transformers import AutoTokenizer

def fix_neuroglyph_tokenizer():
    print("🔧 Fixing NEUROGLYPH tokenizer...")
    
    # 1. Carica tokenizer base Qwen
    base_tokenizer = AutoTokenizer.from_pretrained("Qwen/Qwen2.5-Coder-1.5B-Instruct")
    
    # 2. Carica simboli NEUROGLYPH
    with open("neuroglyph/training/safety/tokenizer_locked_state.json", 'r') as f:
        locked_state = json.load(f)
    
    critical_symbols = locked_state["critical_symbols"]
    
    # 3. Aggiungi simboli al vocabolario
    new_tokens = []
    for symbol in critical_symbols:
        if symbol not in base_tokenizer.vocab:
            new_tokens.append(symbol)
    
    if new_tokens:
        print(f"📝 Aggiungendo {len(new_tokens)} nuovi simboli...")
        base_tokenizer.add_tokens(new_tokens)
    
    # 4. Salva tokenizer aggiornato
    base_tokenizer.save_pretrained("neuroglyph/training/colab_package/")
    print("✅ Tokenizer NEUROGLYPH aggiornato!")

if __name__ == "__main__":
    fix_neuroglyph_tokenizer()
