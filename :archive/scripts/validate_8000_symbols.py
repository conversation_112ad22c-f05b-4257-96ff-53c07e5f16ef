#!/usr/bin/env python3
"""
NEUROGLYPH 8000 SYMBOLS VALIDATION
Valida i 8000 simboli per preparazione tokenizer
"""

import json
import unicodedata
from collections import Counter
from datetime import datetime

def load_8000_registry():
    """Carica registry finale con 8000 simboli."""
    registry_path = "neuroglyph/core/neuroglyph_8000_ULTRA_SAFE.json"
    
    try:
        with open(registry_path, 'r', encoding='utf-8') as f:
            registry = json.load(f)
        
        symbols = registry.get('approved_symbols', [])
        print(f"✅ Registry 8000 caricato: {len(symbols)} simboli")
        return registry, symbols
        
    except Exception as e:
        print(f"❌ Errore caricamento registry: {e}")
        return None, []

def validate_symbol_uniqueness(symbols):
    """Valida unicità simboli."""
    
    print("🔍 Validating symbol uniqueness...")
    
    symbol_counts = Counter()
    fallback_counts = Counter()
    
    for symbol_data in symbols:
        symbol = symbol_data.get('symbol', '')
        fallback = symbol_data.get('fallback', '')
        
        if symbol:
            symbol_counts[symbol] += 1
        if fallback:
            fallback_counts[fallback] += 1
    
    # Trova duplicati
    duplicate_symbols = [s for s, count in symbol_counts.items() if count > 1]
    duplicate_fallbacks = [f for f, count in fallback_counts.items() if count > 1]
    
    print(f"📊 Uniqueness Results:")
    print(f"   Total symbols: {len(symbol_counts)}")
    print(f"   Duplicate symbols: {len(duplicate_symbols)}")
    print(f"   Total fallbacks: {len(fallback_counts)}")
    print(f"   Duplicate fallbacks: {len(duplicate_fallbacks)}")
    
    if duplicate_symbols:
        print(f"⚠️ Duplicate symbols found: {duplicate_symbols[:5]}...")
    if duplicate_fallbacks:
        print(f"⚠️ Duplicate fallbacks found: {duplicate_fallbacks[:5]}...")
    
    uniqueness_score = (len(symbol_counts) - len(duplicate_symbols)) / len(symbol_counts) * 100
    print(f"✅ Uniqueness score: {uniqueness_score:.2f}%")
    
    return uniqueness_score >= 99.0

def validate_unicode_safety(symbols):
    """Valida sicurezza Unicode."""
    
    print("🔍 Validating Unicode safety...")
    
    unicode_symbols = []
    ng_symbols = []
    problematic_symbols = []
    
    for symbol_data in symbols:
        symbol = symbol_data.get('symbol', '')
        
        if symbol.startswith('ng:'):
            ng_symbols.append(symbol)
        else:
            unicode_symbols.append(symbol)
            
            # Verifica renderizzabilità
            try:
                # Test encoding/decoding
                encoded = symbol.encode('utf-8')
                decoded = encoded.decode('utf-8')
                
                # Verifica categoria Unicode
                category = unicodedata.category(symbol)
                if category.startswith('C'):  # Control characters
                    problematic_symbols.append((symbol, "Control character"))
                elif decoded != symbol:
                    problematic_symbols.append((symbol, "Encoding issue"))
                elif len(symbol) != 1:
                    problematic_symbols.append((symbol, "Multi-character"))
                    
            except Exception as e:
                problematic_symbols.append((symbol, f"Error: {e}"))
    
    print(f"📊 Unicode Safety Results:")
    print(f"   Unicode symbols: {len(unicode_symbols)}")
    print(f"   ng: format symbols: {len(ng_symbols)}")
    print(f"   Problematic symbols: {len(problematic_symbols)}")
    
    if problematic_symbols:
        print(f"⚠️ Problematic symbols: {problematic_symbols[:3]}...")
    
    safety_score = (len(unicode_symbols) - len(problematic_symbols)) / len(unicode_symbols) * 100 if unicode_symbols else 100
    print(f"✅ Unicode safety score: {safety_score:.2f}%")
    
    return safety_score >= 95.0

def validate_fallback_compliance(symbols):
    """Valida compliance fallback."""
    
    print("🔍 Validating fallback compliance...")
    
    compliant_fallbacks = 0
    non_compliant = []
    
    for symbol_data in symbols:
        fallback = symbol_data.get('fallback', '')
        
        if fallback:
            # Verifica lunghezza
            if len(fallback) <= 8:
                compliant_fallbacks += 1
            else:
                non_compliant.append((fallback, f"Too long: {len(fallback)} chars"))
        else:
            non_compliant.append(("", "Missing fallback"))
    
    print(f"📊 Fallback Compliance Results:")
    print(f"   Compliant fallbacks: {compliant_fallbacks}")
    print(f"   Non-compliant: {len(non_compliant)}")
    
    if non_compliant:
        print(f"⚠️ Non-compliant fallbacks: {non_compliant[:3]}...")
    
    compliance_score = compliant_fallbacks / len(symbols) * 100
    print(f"✅ Fallback compliance score: {compliance_score:.2f}%")
    
    return compliance_score >= 95.0

def analyze_symbol_distribution(symbols):
    """Analizza distribuzione simboli per domini."""
    
    print("🔍 Analyzing symbol distribution...")
    
    category_counts = Counter()
    domain_counts = Counter()
    symbol_types = Counter()
    
    for symbol_data in symbols:
        category = symbol_data.get('category', 'unknown')
        domain = symbol_data.get('domain', 'unknown')
        symbol_type = symbol_data.get('symbol_type', 'unknown')
        
        category_counts[category] += 1
        domain_counts[domain] += 1
        symbol_types[symbol_type] += 1
    
    print(f"📊 Distribution Analysis:")
    print(f"   Categories: {len(category_counts)}")
    print(f"   Domains: {len(domain_counts)}")
    print(f"   Symbol types: {dict(symbol_types)}")
    
    print(f"\n🏷️ Top Categories:")
    for category, count in category_counts.most_common(10):
        percentage = (count / len(symbols)) * 100
        print(f"   {category:15}: {count:4} ({percentage:5.1f}%)")
    
    # Verifica bilanciamento
    max_category = max(category_counts.values())
    min_category = min(category_counts.values())
    balance_ratio = min_category / max_category if max_category > 0 else 0
    
    print(f"\n⚖️ Balance Analysis:")
    print(f"   Max category size: {max_category}")
    print(f"   Min category size: {min_category}")
    print(f"   Balance ratio: {balance_ratio:.3f}")
    print(f"   Balance quality: {'Good' if balance_ratio >= 0.1 else 'Needs improvement'}")

def generate_tokenizer_preparation_report(registry, symbols, validation_results):
    """Genera report di preparazione tokenizer."""
    
    print("\n📋 TOKENIZER PREPARATION REPORT")
    print("=" * 50)
    
    report = {
        "timestamp": datetime.now().isoformat(),
        "total_symbols": len(symbols),
        "target_achieved": len(symbols) >= 8000,
        "validation_results": validation_results,
        "tokenizer_readiness": all(validation_results.values()),
        "recommendations": []
    }
    
    # Analizza readiness
    if report["tokenizer_readiness"]:
        print("✅ TOKENIZER READY!")
        print("   All validation checks passed")
        print("   Symbols ready for integration")
        
        report["recommendations"] = [
            "Proceed with tokenizer integration",
            "Use additional_special_tokens for all symbols",
            "Validate 1:1 mapping after integration",
            "Monitor tokenizer stability during training"
        ]
    else:
        print("⚠️ TOKENIZER NEEDS ATTENTION")
        failed_checks = [check for check, passed in validation_results.items() if not passed]
        print(f"   Failed checks: {failed_checks}")
        
        report["recommendations"] = [
            "Fix validation issues before tokenizer integration",
            "Review failed validation checks",
            "Consider symbol cleanup if needed"
        ]
    
    # Salva report
    report_path = "neuroglyph/core/tokenizer_preparation_report.json"
    with open(report_path, 'w', encoding='utf-8') as f:
        json.dump(report, f, indent=2, ensure_ascii=False)
    
    print(f"✅ Report salvato: {report_path}")
    
    return report

def main():
    """Esegue validazione completa 8000 simboli."""
    
    print("🧠 NEUROGLYPH 8000 SYMBOLS VALIDATION")
    print("=" * 60)
    
    # 1. Carica registry
    registry, symbols = load_8000_registry()
    if not symbols:
        print("❌ Cannot proceed without symbols")
        return False
    
    # 2. Esegui validazioni
    validation_results = {
        "uniqueness": validate_symbol_uniqueness(symbols),
        "unicode_safety": validate_unicode_safety(symbols),
        "fallback_compliance": validate_fallback_compliance(symbols)
    }
    
    # 3. Analizza distribuzione
    analyze_symbol_distribution(symbols)
    
    # 4. Genera report
    report = generate_tokenizer_preparation_report(registry, symbols, validation_results)
    
    # 5. Risultato finale
    if report["tokenizer_readiness"]:
        print(f"\n🎊 VALIDATION SUCCESS!")
        print(f"✅ 8000 simboli validati e pronti")
        print(f"✅ Tokenizer integration: GO!")
        print(f"✅ GOD MODE training: READY!")
        
        return True
    else:
        print(f"\n⚠️ VALIDATION ISSUES FOUND")
        print(f"🔧 Review and fix before proceeding")
        
        return False

if __name__ == "__main__":
    success = main()
    
    if success:
        print(f"\n🚀 NEXT: Tokenizer Integration")
        print(f"   Use neuroglyph_8000_tokenizer_implementation.py")
        print(f"   Ensure all 8000 symbols remain atomic")
    else:
        print(f"\n🔧 FIX VALIDATION ISSUES FIRST")
