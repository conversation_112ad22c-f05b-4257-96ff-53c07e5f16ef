"""
NEUROGLYPH LLM - Configurazione Metal GPU Ottimizzata
Configurazione per Mac M2 8GB con accelerazione GPU
"""

from llama_cpp import <PERSON>lam<PERSON>

def create_metal_llm(model_path, gpu_layers=10):
    """
    Crea istanza Llama con Metal GPU ottimizzato

    Args:
        model_path: Path al modello GGUF
        gpu_layers: Numero di layer su GPU (10-20 raccomandato)
    """

    return Llama(
        model_path=model_path,
        n_ctx=512,                    # Context window ottimizzato
        n_threads=2,                  # Meno thread CPU per GPU
        n_gpu_layers=gpu_layers,      # Layer su Metal GPU
        verbose=False,                # Riduci output verboso
        use_mmap=True,               # Memory mapping efficiente
        use_mlock=False,             # Non bloccare memoria
        n_batch=128,                 # Batch size ottimizzato
        main_gpu=0,                  # GPU principale
        # Metal specific optimizations
        tensor_split=None,           # Lascia gestire automaticamente
    )

def test_metal_performance(model_path):
    """Test performance Metal vs CPU"""
    import time

    print("🔄 Test performance Metal GPU...")

    # Test Metal GPU
    try:
        llm_metal = create_metal_llm(model_path, gpu_layers=10)

        start_time = time.time()
        response = llm_metal("def hello():", max_tokens=10, temperature=0.1)
        metal_time = time.time() - start_time

        print(f"🚀 Metal GPU: {metal_time:.2f}s")

        # Test CPU only
        llm_cpu = Llama(
            model_path=model_path,
            n_ctx=512,
            n_threads=4,
            n_gpu_layers=0,  # CPU only
            verbose=False,
            use_mmap=True
        )

        start_time = time.time()
        response = llm_cpu("def hello():", max_tokens=10, temperature=0.1)
        cpu_time = time.time() - start_time

        print(f"🖥️ CPU only: {cpu_time:.2f}s")

        if metal_time < cpu_time:
            speedup = cpu_time / metal_time
            print(f"⚡ Speedup Metal: {speedup:.2f}x")
            return True
        else:
            print("⚠️ Metal non più veloce di CPU")
            return False

    except Exception as e:
        print(f"❌ Errore test: {e}")
        return False

# Configurazione raccomandata per NEUROGLYPH LLM
NEUROGLYPH_METAL_CONFIG = {
    "model_path": "model/qwen2.5coder/qwen2.5-coder-7b-instruct-q4_k_m.gguf",
    "n_ctx": 512,
    "n_threads": 2,
    "n_gpu_layers": 10,  # Inizia con 10, prova 15-20 se stabile
    "n_batch": 128,
    "use_mmap": True,
    "use_mlock": False,
    "verbose": False
}
