{"timestamp": "1748724599.67961", "tokenizer_directory": "neuroglyph/training/colab_package", "vocab_analysis": {"total_entries": 8000, "symbol_entries": 2549, "special_entries": 3, "regular_entries": 5448, "symbols_found": [{"token": "▁", "score": -3.09952, "line": 4}, {"token": "▁Ġ", "score": -3.20624, "line": 5}, {"token": "▁.", "score": -4.4231, "line": 8}, {"token": "▁_", "score": -4.5211, "line": 11}, {"token": "3⁄4", "score": -4.9558, "line": 19}, {"token": "▁å", "score": -5.04529, "line": 21}, {"token": "1⁄2", "score": -5.06933, "line": 23}, {"token": "1⁄4", "score": -5.09131, "line": 24}, {"token": "▁̧", "score": -5.23018, "line": 27}, {"token": "▁à", "score": -5.24007, "line": 28}, {"token": "▁è", "score": -5.33051, "line": 32}, {"token": "▁(", "score": -5.34511, "line": 34}, {"token": "▁ä", "score": -5.39118, "line": 35}, {"token": "▁̈", "score": -5.41155, "line": 37}, {"token": "▁́", "score": -5.504, "line": 41}, {"token": "▁æ", "score": -5.5374, "line": 44}, {"token": "▁̄", "score": -5.56213, "line": 48}, {"token": "▁-", "score": -5.6445, "line": 52}, {"token": "▁ç", "score": -5.68222, "line": 56}, {"token": "▁ĉ", "score": -6.05016, "line": 73}, {"token": "▁×", "score": -6.09685, "line": 76}, {"token": "▁ĠÐ", "score": -6.09691, "line": 77}, {"token": "▁é", "score": -6.14512, "line": 83}, {"token": "▁Ø", "score": -6.16767, "line": 86}, {"token": "▁/", "score": -6.17774, "line": 88}, {"token": "▁Ð", "score": -6.32796, "line": 93}, {"token": "▁ĠØ", "score": -6.39678, "line": 98}, {"token": "▁ð", "score": -6.41879, "line": 100}, {"token": "▁ĠS", "score": -6.45551, "line": 105}, {"token": "▁ï", "score": -6.47639, "line": 108}, {"token": "▁̧2", "score": -6.52579, "line": 114}, {"token": "▁̧£", "score": -6.65181, "line": 123}, {"token": "▁ë", "score": -6.65188, "line": 124}, {"token": "▁á", "score": -6.67847, "line": 126}, {"token": "▁ì", "score": -6.68495, "line": 127}, {"token": "▁ãģ", "score": -6.95596, "line": 145}, {"token": "▁̧Ð", "score": -6.973, "line": 148}, {"token": "▁ĠB", "score": -7.00336, "line": 150}, {"token": "▁Ã", "score": -7.03173, "line": 151}, {"token": "▁ĠC", "score": -7.05539, "line": 153}, {"token": "▁ê", "score": -7.10588, "line": 157}, {"token": "▁ĠP", "score": -7.12191, "line": 158}, {"token": "▁Ġre", "score": -7.14221, "line": 160}, {"token": "▁̧±", "score": -7.16291, "line": 161}, {"token": "▁̧Ńà", "score": -7.17668, "line": 163}, {"token": "▁̧¡", "score": -7.19411, "line": 166}, {"token": "▁åħ", "score": -7.20959, "line": 167}, {"token": "▁S", "score": -7.21384, "line": 168}, {"token": "▁̈×", "score": -7.21498, "line": 169}, {"token": "▁̧¢", "score": -7.2902, "line": 179}, {"token": "▁́Ð", "score": -7.313, "line": 183}, {"token": "▁̧ģà", "score": -7.32513, "line": 185}, {"token": "▁ĠF", "score": -7.36176, "line": 187}, {"token": "▁ĠA", "score": -7.38217, "line": 188}, {"token": "▁=", "score": -7.38841, "line": 190}, {"token": "▁<", "score": -7.41153, "line": 191}, {"token": "▁,", "score": -7.41754, "line": 192}, {"token": "▁ĠE", "score": -7.43025, "line": 195}, {"token": "▁̧§", "score": -7.43212, "line": 196}, {"token": "▁̧Ļà", "score": -7.44262, "line": 199}, {"token": "▁:", "score": -7.44731, "line": 200}, {"token": "▁̧Ļ", "score": -7.47942, "line": 204}, {"token": "▁A", "score": -7.49855, "line": 208}, {"token": "▁Ġc", "score": -7.50177, "line": 210}, {"token": "▁̧¥", "score": -7.50351, "line": 211}, {"token": "▁C", "score": -7.50573, "line": 212}, {"token": "▁ĠD", "score": -7.5142, "line": 214}, {"token": "▁̧aà", "score": -7.52628, "line": 216}, {"token": "▁ĠT", "score": -7.53001, "line": 217}, {"token": "▁ĠM", "score": -7.53762, "line": 220}, {"token": "▁P", "score": -7.56872, "line": 222}, {"token": "▁̧ĩ", "score": -7.57061, "line": 223}, {"token": "▁ãĤ", "score": -7.57395, "line": 224}, {"token": "▁[", "score": -7.58728, "line": 226}, {"token": "▁i", "score": -7.616, "line": 232}, {"token": "▁Ġb", "score": -7.61992, "line": 234}, {"token": "▁̧μ", "score": -7.62477, "line": 236}, {"token": "▁ĠG", "score": -7.62609, "line": 237}, {"token": "▁>", "score": -7.6287, "line": 238}, {"token": "▁̧ļ", "score": -7.64375, "line": 242}, {"token": "▁ĠN", "score": -7.66467, "line": 244}, {"token": "▁åı", "score": -7.67635, "line": 247}, {"token": "▁Ġw", "score": -7.68414, "line": 249}, {"token": "▁Ġp", "score": -7.71261, "line": 252}, {"token": "▁\\", "score": -7.73685, "line": 257}, {"token": "▁̧Ĺà", "score": -7.73822, "line": 259}, {"token": "▁c", "score": -7.77223, "line": 265}, {"token": "▁Ġf", "score": -7.7785, "line": 266}, {"token": "▁Ġun", "score": -7.77869, "line": 267}, {"token": "▁Ġì", "score": -7.7809, "line": 269}, {"token": "▁B", "score": -7.78802, "line": 270}, {"token": "▁Ġm", "score": -7.78935, "line": 271}, {"token": "▁)", "score": -7.79049, "line": 272}, {"token": "▁̧Ħà", "score": -7.79215, "line": 273}, {"token": "▁ĠH", "score": -7.80053, "line": 275}, {"token": "▁ĠÃ", "score": -7.80246, "line": 277}, {"token": "▁̧°", "score": -7.80452, "line": 279}, {"token": "▁ĠW", "score": -7.81579, "line": 283}, {"token": "▁I", "score": -7.81612, "line": 284}, {"token": "▁M", "score": -7.8188, "line": 285}, {"token": "▁Ġde", "score": -7.84179, "line": 288}, {"token": "▁̧«", "score": -7.84848, "line": 289}, {"token": "▁̧Ń", "score": -7.85619, "line": 292}, {"token": "▁̈Ø", "score": -7.86319, "line": 294}, {"token": "▁ĠK", "score": -7.86482, "line": 295}, {"token": "▁ĠR", "score": -7.88679, "line": 300}, {"token": "▁Ġh", "score": -7.88825, "line": 301}, {"token": "▁Ġin", "score": -7.88835, "line": 302}, {"token": "▁E", "score": -7.88967, "line": 303}, {"token": "▁D", "score": -7.89263, "line": 305}, {"token": "▁̧ķà", "score": -7.8973, "line": 306}, {"token": "▁ĠRe", "score": -7.91481, "line": 307}, {"token": "▁u", "score": -7.91928, "line": 308}, {"token": "▁\"", "score": -7.94706, "line": 318}, {"token": "▁́à", "score": -7.94901, "line": 319}, {"token": "▁ãĥ", "score": -7.95167, "line": 320}, {"token": "▁ðŁ", "score": -7.96177, "line": 323}, {"token": "▁p", "score": -7.96198, "line": 324}, {"token": "▁o", "score": -7.98125, "line": 333}, {"token": "▁F", "score": -7.99672, "line": 335}, {"token": "▁̧Ĭ", "score": -8.00022, "line": 336}, {"token": "▁ĠÑģÐ", "score": -8.00825, "line": 338}, {"token": "▁Ġg", "score": -8.03851, "line": 344}, {"token": "▁Ġt", "score": -8.04254, "line": 345}, {"token": "▁$", "score": -8.05195, "line": 348}, {"token": "▁̧Ľà", "score": -8.06534, "line": 350}, {"token": "▁ĠI", "score": -8.07352, "line": 351}, {"token": "▁̧Ķà", "score": -8.08241, "line": 355}, {"token": "▁T", "score": -8.09146, "line": 358}, {"token": "▁'", "score": -8.09438, "line": 360}, {"token": "▁Ġë", "score": -8.11759, "line": 363}, {"token": "▁e", "score": -8.11769, "line": 364}, {"token": "▁åľ", "score": -8.13278, "line": 369}, {"token": "▁äo", "score": -8.14446, "line": 371}, {"token": "▁çĶ", "score": -8.14791, "line": 373}, {"token": "▁Ġcon", "score": -8.14797, "line": 374}, {"token": "▁æĸ", "score": -8.14971, "line": 375}, {"token": "▁çĽ", "score": -8.15199, "line": 376}, {"token": "▁̧ĩà", "score": -8.16047, "line": 377}, {"token": "▁Ġco", "score": -8.17206, "line": 379}, {"token": "▁Ġk", "score": -8.17664, "line": 382}, {"token": "▁ĠO", "score": -8.18992, "line": 386}, {"token": "▁æĬ", "score": -8.19312, "line": 387}, {"token": "▁H", "score": -8.19484, "line": 388}, {"token": "▁re", "score": -8.1955, "line": 389}, {"token": "▁æĹ", "score": -8.1959, "line": 390}, {"token": "▁f", "score": -8.20953, "line": 392}, {"token": "▁Ġd", "score": -8.2159, "line": 393}, {"token": "▁åĽ", "score": -8.2204, "line": 396}, {"token": "▁G", "score": -8.23476, "line": 401}, {"token": "▁ĠV", "score": -8.24401, "line": 403}, {"token": "▁åĪ", "score": -8.25112, "line": 406}, {"token": "▁N", "score": -8.25404, "line": 407}, {"token": "▁̄Ø", "score": -8.26665, "line": 408}, {"token": "▁̧ļà", "score": -8.26671, "line": 409}, {"token": "▁åį", "score": -8.28127, "line": 416}, {"token": "▁R", "score": -8.28473, "line": 418}, {"token": "▁ÙħØ", "score": -8.29351, "line": 420}, {"token": "▁Ġse", "score": -8.29633, "line": 421}, {"token": "▁b", "score": -8.31495, "line": 428}, {"token": "▁æİ", "score": -8.33084, "line": 430}, {"token": "▁æĺ", "score": -8.33971, "line": 434}, {"token": "▁̧ģ", "score": -8.34715, "line": 440}, {"token": "▁is", "score": -8.3536, "line": 445}, {"token": "▁Ä", "score": -8.35943, "line": 447}, {"token": "▁̧a", "score": -8.36087, "line": 448}, {"token": "▁in", "score": -8.36372, "line": 449}, {"token": "▁æĶ", "score": -8.36682, "line": 452}, {"token": "▁Ġbe", "score": -8.37137, "line": 453}, {"token": "▁̧Ĭà", "score": -8.38437, "line": 455}, {"token": "▁Ġê", "score": -8.38947, "line": 456}, {"token": "¥1⁄2", "score": -8.4034, "line": 459}, {"token": "▁ĠCo", "score": -8.40367, "line": 460}, {"token": "▁g", "score": -8.42377, "line": 474}, {"token": "▁U", "score": -8.4274, "line": 476}, {"token": "▁̧Īà", "score": -8.42933, "line": 478}, {"token": "▁w", "score": -8.4368, "line": 480}, {"token": "▁get", "score": -8.43878, "line": 481}, {"token": "▁ĠJ", "score": -8.44587, "line": 487}, {"token": "▁}", "score": -8.45287, "line": 490}, {"token": "▁Ġdis", "score": -8.45303, "line": 491}, {"token": "▁æī", "score": -8.45448, "line": 492}, {"token": "▁̧į", "score": -8.46369, "line": 495}, {"token": "▁̧ĭ", "score": -8.46569, "line": 497}, {"token": "▁Ġj", "score": -8.47688, "line": 500}, {"token": "▁ØaØ", "score": -8.47757, "line": 501}, {"token": "▁O", "score": -8.47822, "line": 502}, {"token": "▁æĢ", "score": -8.48021, "line": 503}, {"token": "▁W", "score": -8.48563, "line": 505}, {"token": "▁m", "score": -8.49018, "line": 508}, {"token": "▁Ġpo", "score": -8.49278, "line": 509}, {"token": "▁@", "score": -8.49487, "line": 511}, {"token": "▁æľ", "score": -8.49526, "line": 512}, {"token": "▁]", "score": -8.4967, "line": 514}, {"token": "▁̧Ķ", "score": -8.51163, "line": 521}, {"token": "▁åIJ", "score": -8.52929, "line": 523}, {"token": "▁h", "score": -8.53358, "line": 525}, {"token": "▁éĺ", "score": -8.54673, "line": 531}, {"token": "▁Ġget", "score": -8.55397, "line": 535}, {"token": "▁çļĦ", "score": -8.55889, "line": 539}, {"token": "▁áo", "score": -8.55942, "line": 540}, {"token": "▁Re", "score": -8.56594, "line": 543}, {"token": "▁Ġsp", "score": -8.56659, "line": 544}, {"token": "▁ĠÐoÐ", "score": -8.56938, "line": 548}, {"token": "▁j", "score": -8.56967, "line": 549}, {"token": "▁çī", "score": -8.57142, "line": 551}, {"token": "▁̧Ģ", "score": -8.57379, "line": 553}, {"token": "▁̧o", "score": -8.58193, "line": 559}, {"token": "▁ĠDe", "score": -8.58271, "line": 560}, {"token": "▁̄ä", "score": -8.58279, "line": 561}, {"token": "▁;", "score": -8.58342, "line": 562}, {"token": "▁Ġv", "score": -8.58552, "line": 564}, {"token": "▁ac", "score": -8.60202, "line": 573}, {"token": "▁æŃ", "score": -8.60391, "line": 574}, {"token": "▁̧»", "score": -8.60407, "line": 575}, {"token": "▁̧Ģä", "score": -8.61859, "line": 580}, {"token": "▁çİ", "score": -8.62828, "line": 585}, {"token": "▁æĭ", "score": -8.64139, "line": 590}, {"token": "▁*", "score": -8.6427, "line": 592}, {"token": "▁éĢ", "score": -8.64308, "line": 593}, {"token": "▁×©×", "score": -8.64899, "line": 594}, {"token": "▁x", "score": -8.6576, "line": 600}, {"token": "▁+", "score": -8.66006, "line": 602}, {"token": "▁éĤ", "score": -8.66184, "line": 603}, {"token": "▁éĥ", "score": -8.66619, "line": 604}, {"token": "▁ur", "score": -8.67499, "line": 610}, {"token": "▁̧Ĥ", "score": -8.67947, "line": 611}, {"token": "▁ol", "score": -8.68062, "line": 612}, {"token": "▁âĢ", "score": -8.68182, "line": 614}, {"token": "▁̧à", "score": -8.68336, "line": 615}, {"token": "▁́Ø", "score": -8.68478, "line": 617}, {"token": "▁Ġdi", "score": -8.69347, "line": 619}, {"token": "▁̧Ĥà", "score": -8.69607, "line": 620}, {"token": "▁V", "score": -8.70143, "line": 623}, {"token": "▁éķ", "score": -8.70654, "line": 626}, {"token": "▁èĥ", "score": -8.71523, "line": 629}, {"token": "▁̧·", "score": -8.72646, "line": 633}, {"token": "▁åĨ", "score": -8.72731, "line": 634}, {"token": "▁̧3", "score": -8.7274, "line": 635}, {"token": "▁Ġpro", "score": -8.73125, "line": 638}, {"token": "▁ĠÑĢÐ", "score": -8.73447, "line": 639}, {"token": "▁æķ", "score": -8.7478, "line": 643}, {"token": "▁ĠIn", "score": -8.74966, "line": 644}, {"token": "▁it", "score": -8.75344, "line": 645}, {"token": "▁ço", "score": -8.75377, "line": 646}, {"token": "▁Ġno", "score": -8.75815, "line": 648}, {"token": "▁or", "score": -8.75873, "line": 649}, {"token": "▁ĠdÃ", "score": -8.76025, "line": 651}, {"token": "▁̧Ģå", "score": -8.77351, "line": 657}, {"token": "▁Ġme", "score": -8.77358, "line": 658}, {"token": "▁æł", "score": -8.77496, "line": 662}, {"token": "▁ĠAl", "score": -8.77578, "line": 664}, {"token": "▁åŃ", "score": -8.77756, "line": 665}, {"token": "▁Å", "score": -8.77827, "line": 666}, {"token": "▁un", "score": -8.78096, "line": 669}, {"token": "▁×¤×", "score": -8.78488, "line": 674}, {"token": "▁Ġdo", "score": -8.78876, "line": 677}, {"token": "▁ĠmÃ", "score": -8.7902, "line": 679}, {"token": "▁ĠRo", "score": -8.79427, "line": 682}, {"token": "▁Ġpa", "score": -8.79853, "line": 684}, {"token": "▁Ġpre", "score": -8.79938, "line": 685}, {"token": "▁()", "score": -8.8007, "line": 686}, {"token": "▁J", "score": -8.80466, "line": 690}, {"token": "▁éģ", "score": -8.80718, "line": 692}, {"token": "▁r", "score": -8.80875, "line": 695}, {"token": "▁éĶ", "score": -8.81059, "line": 698}, {"token": "▁åĬ", "score": -8.81214, "line": 700}, {"token": "▁ic", "score": -8.81511, "line": 705}, {"token": "▁ro", "score": -8.81707, "line": 707}, {"token": "▁Ġst", "score": -8.82409, "line": 715}, {"token": "▁Ġto", "score": -8.82752, "line": 719}, {"token": "▁̧1", "score": -8.83015, "line": 720}, {"token": "▁v", "score": -8.83541, "line": 723}, {"token": "▁ig", "score": -8.84019, "line": 729}, {"token": "▁ĠBe", "score": -8.84352, "line": 735}, {"token": "▁Ġba", "score": -8.84463, "line": 736}, {"token": "▁éĴ", "score": -8.86829, "line": 751}, {"token": "▁ĠCon", "score": -8.86967, "line": 752}, {"token": "▁́å", "score": -8.88032, "line": 757}, {"token": "▁̧Ī", "score": -8.88757, "line": 762}, {"token": "▁éĻ", "score": -8.88786, "line": 764}, {"token": "▁as", "score": -8.8891, "line": 765}, {"token": "▁̄1", "score": -8.89153, "line": 766}, {"token": "▁éĵ", "score": -8.8918, "line": 767}, {"token": "▁Ġge", "score": -8.89902, "line": 773}, {"token": "▁×¢×", "score": -8.90149, "line": 774}, {"token": "▁ar", "score": -8.90186, "line": 776}, {"token": "▁In", "score": -8.90309, "line": 777}, {"token": "▁̧įå", "score": -8.90838, "line": 780}, {"token": "▁éĽ", "score": -8.91226, "line": 782}, {"token": "▁K", "score": -8.91463, "line": 784}, {"token": "▁×§×", "score": -8.91477, "line": 785}, {"token": "▁ĠQ", "score": -8.92963, "line": 797}, {"token": "▁èĤ", "score": -8.93045, "line": 798}, {"token": "▁!", "score": -8.93083, "line": 799}, {"token": "▁Æ", "score": -8.93614, "line": 805}, {"token": "▁?", "score": -8.9432, "line": 810}, {"token": "▁ri", "score": -8.94402, "line": 813}, {"token": "▁k", "score": -8.94704, "line": 816}, {"token": "1⁄2¦", "score": -8.94837, "line": 818}, {"token": "▁åij", "score": -8.95226, "line": 820}, {"token": "▁#", "score": -8.95599, "line": 826}, {"token": "▁Ġsub", "score": -8.95652, "line": 828}, {"token": "▁̧Ģæ", "score": -8.95745, "line": 829}, {"token": "▁Ġcom", "score": -8.96056, "line": 831}, {"token": "▁èĭ", "score": -8.96078, "line": 832}, {"token": "▁âĺ", "score": -8.96267, "line": 835}, {"token": "▁âľ", "score": -8.96267, "line": 836}, {"token": "▁ĠLe", "score": -8.96498, "line": 837}, {"token": "▁æĽ", "score": -8.96597, "line": 838}, {"token": "▁ðŁį", "score": -8.96673, "line": 839}, {"token": "▁ĠCh", "score": -8.96967, "line": 842}, {"token": "▁en", "score": -8.96993, "line": 844}, {"token": "▁æĪ", "score": -8.97263, "line": 848}, {"token": "▁ĠU", "score": -8.97511, "line": 851}, {"token": "▁ðŁĴ", "score": -8.98268, "line": 854}, {"token": "▁ðŁĺ", "score": -8.98439, "line": 857}, {"token": "▁ĠLa", "score": -8.98484, "line": 858}, {"token": "▁ðŁIJ", "score": -8.98549, "line": 859}, {"token": "▁æĥ", "score": -8.98595, "line": 860}, {"token": "▁ðŁij", "score": -8.98739, "line": 863}, {"token": "▁Ġbi", "score": -8.98919, "line": 866}, {"token": "▁Ġbo", "score": -8.98935, "line": 867}, {"token": "▁̧Ńå", "score": -8.99145, "line": 870}, {"token": "▁ag", "score": -8.99478, "line": 873}, {"token": "▁st", "score": -8.99676, "line": 875}, {"token": "▁Ġgr", "score": -8.99835, "line": 876}, {"token": "▁ĠLo", "score": -9.00111, "line": 878}, {"token": "▁èĪ", "score": -9.00927, "line": 884}, {"token": "▁âĶ", "score": -9.0103, "line": 885}, {"token": "▁Ġro", "score": -9.01348, "line": 890}, {"token": "▁Ġma", "score": -9.01793, "line": 892}, {"token": "▁ĠRa", "score": -9.02024, "line": 894}, {"token": "▁ap", "score": -9.02358, "line": 896}, {"token": "▁Ġper", "score": -9.02824, "line": 900}, {"token": "▁Ġcar", "score": -9.03167, "line": 903}, {"token": "▁çĻ", "score": -9.03192, "line": 905}, {"token": "▁èĬ", "score": -9.03312, "line": 907}, {"token": "▁ul", "score": -9.03712, "line": 909}, {"token": "▁Ġsu", "score": -9.03726, "line": 910}, {"token": "▁í", "score": -9.03745, "line": 911}, {"token": "▁éĹ", "score": -9.03995, "line": 912}, {"token": "▁Ġho", "score": -9.04185, "line": 913}, {"token": "▁ïo", "score": -9.04359, "line": 917}, {"token": "▁ĠgÃ", "score": -9.05064, "line": 924}, {"token": "▁æĮ", "score": -9.05079, "line": 926}, {"token": "▁Ġvi", "score": -9.05144, "line": 927}, {"token": "▁ĠMar", "score": -9.05509, "line": 931}, {"token": "▁ĠPa", "score": -9.05589, "line": 932}, {"token": "▁̈è", "score": -9.0573, "line": 933}, {"token": "▁åī", "score": -9.05988, "line": 936}, {"token": "▁Î", "score": -9.06078, "line": 940}, {"token": "▁ðŁı", "score": -9.06462, "line": 943}, {"token": "▁ðŁĮ", "score": -9.06575, "line": 944}, {"token": "▁ðŁĵ", "score": -9.06685, "line": 945}, {"token": "▁̧¤", "score": -9.06803, "line": 947}, {"token": "▁ab", "score": -9.06953, "line": 949}, {"token": "▁ðŁĶ", "score": -9.06967, "line": 950}, {"token": "▁ĠMa", "score": -9.06986, "line": 951}, {"token": "▁an", "score": -9.07058, "line": 952}, {"token": "▁le", "score": -9.08059, "line": 956}, {"token": "▁ðŁİ", "score": -9.08108, "line": 957}, {"token": "▁̧Ñı", "score": -9.08118, "line": 958}, {"token": "▁Ġstr", "score": -9.0816, "line": 959}, {"token": "▁çļĦå", "score": -9.08446, "line": 965}, {"token": "▁̧Ģç", "score": -9.08463, "line": 966}, {"token": "▁æĤ", "score": -9.08886, "line": 967}, {"token": "▁Ġpe", "score": -9.08964, "line": 968}, {"token": "▁oc", "score": -9.09593, "line": 974}, {"token": "▁çŁ", "score": -9.09739, "line": 975}, {"token": "▁Ġhe", "score": -9.10382, "line": 981}, {"token": "▁èį", "score": -9.10409, "line": 982}, {"token": "▁éļ", "score": -9.10514, "line": 983}, {"token": "▁z", "score": -9.10682, "line": 986}, {"token": "▁çĭ", "score": -9.10836, "line": 987}, {"token": "▁Ġfi", "score": -9.11521, "line": 993}, {"token": "▁ĠSt", "score": -9.11593, "line": 994}, {"token": "▁ÙĪØ", "score": -9.12145, "line": 997}, {"token": "▁åĮ", "score": -9.12321, "line": 999}, {"token": "▁%", "score": -9.12463, "line": 1000}, {"token": "▁̈å", "score": -9.12513, "line": 1001}, {"token": "▁̄å", "score": -9.12624, "line": 1003}, {"token": "▁Â", "score": -9.12673, "line": 1004}, {"token": "▁èIJ", "score": -9.13066, "line": 1007}, {"token": "▁ĠTh", "score": -9.134, "line": 1010}, {"token": "▁Ġdr", "score": -9.13423, "line": 1011}, {"token": "▁åİ", "score": -9.1346, "line": 1014}, {"token": "▁ĠMo", "score": -9.14002, "line": 1016}, {"token": "▁̧©", "score": -9.14122, "line": 1017}, {"token": "▁am", "score": -9.15904, "line": 1028}, {"token": "▁im", "score": -9.16214, "line": 1032}, {"token": "▁Ġna", "score": -9.16227, "line": 1033}, {"token": "▁çı", "score": -9.16444, "line": 1038}, {"token": "▁ĠLi", "score": -9.16519, "line": 1039}, {"token": "▁ãĢ", "score": -9.16726, "line": 1041}, {"token": "▁ĠÙĪØ", "score": -9.17155, "line": 1047}, {"token": "▁Ġsc", "score": -9.17265, "line": 1049}, {"token": "▁ĠAn", "score": -9.17289, "line": 1050}, {"token": "▁ch", "score": -9.17431, "line": 1054}, {"token": "▁ĠZ", "score": -9.17542, "line": 1056}, {"token": "▁ÑĢÐ", "score": -9.17692, "line": 1057}, {"token": "▁al", "score": -9.17693, "line": 1058}, {"token": "▁æĻ", "score": -9.1776, "line": 1059}, {"token": "▁âĻ", "score": -9.18775, "line": 1064}, {"token": "▁âķ", "score": -9.18775, "line": 1065}, {"token": "▁el", "score": -9.18841, "line": 1068}, {"token": "▁æģ", "score": -9.19084, "line": 1070}, {"token": "▁åĵ", "score": -9.19348, "line": 1073}, {"token": "▁ØŃØ", "score": -9.19424, "line": 1074}, {"token": "▁çŃ", "score": -9.19753, "line": 1077}, {"token": "▁Ġso", "score": -9.19803, "line": 1078}, {"token": "▁Ġac", "score": -9.19851, "line": 1079}, {"token": "▁æį", "score": -9.1998, "line": 1081}, {"token": "▁æŁ", "score": -9.20125, "line": 1082}, {"token": "▁at", "score": -9.20446, "line": 1086}, {"token": "▁']", "score": -9.20811, "line": 1092}, {"token": "▁Ï", "score": -9.21251, "line": 1096}, {"token": "▁çĤ", "score": -9.21252, "line": 1097}, {"token": "▁̧ľà", "score": -9.21437, "line": 1099}, {"token": "▁çĸ", "score": -9.21973, "line": 1101}, {"token": "▁̧å", "score": -9.22015, "line": 1102}, {"token": "▁Ġpr", "score": -9.22164, "line": 1106}, {"token": "▁Ġlo", "score": -9.22305, "line": 1107}, {"token": "▁Ġon", "score": -9.22333, "line": 1108}, {"token": "▁×¡×", "score": -9.22894, "line": 1112}, {"token": "▁Ġtr", "score": -9.23052, "line": 1113}, {"token": "▁ra", "score": -9.24558, "line": 1126}, {"token": "▁âĨ", "score": -9.24776, "line": 1128}, {"token": "▁âĸ", "score": -9.24776, "line": 1129}, {"token": "▁âĹ", "score": -9.24776, "line": 1130}, {"token": "▁ut", "score": -9.24837, "line": 1132}, {"token": "▁éa", "score": -9.24971, "line": 1134}, {"token": "▁To", "score": -9.25053, "line": 1136}, {"token": "▁ÐoÐ", "score": -9.25179, "line": 1143}, {"token": "▁ĠHo", "score": -9.25255, "line": 1145}, {"token": "▁Ġgl", "score": -9.25819, "line": 1151}, {"token": "▁ĠMe", "score": -9.26002, "line": 1153}, {"token": "▁Ġla", "score": -9.26015, "line": 1154}, {"token": "▁Ġover", "score": -9.26153, "line": 1155}, {"token": "▁(\"", "score": -9.26348, "line": 1157}, {"token": "▁Ġz", "score": -9.26434, "line": 1159}, {"token": "▁èİ", "score": -9.26599, "line": 1160}, {"token": "▁Ġset", "score": -9.26959, "line": 1163}, {"token": "▁Ġta", "score": -9.27022, "line": 1165}, {"token": "▁De", "score": -9.27113, "line": 1167}, {"token": "▁ÙĤØ", "score": -9.27585, "line": 1173}, {"token": "▁âĢĻ", "score": -9.27664, "line": 1175}, {"token": "▁çģ", "score": -9.27877, "line": 1178}, {"token": "▁Ġab", "score": -9.28309, "line": 1183}, {"token": "▁')", "score": -9.28401, "line": 1185}, {"token": "▁Ġbu", "score": -9.28504, "line": 1188}, {"token": "▁ĠÑĤÐ", "score": -9.29056, "line": 1193}, {"token": "▁Ġinter", "score": -9.29179, "line": 1196}, {"token": "▁lo", "score": -9.2978, "line": 1206}, {"token": "▁ĠCa", "score": -9.30487, "line": 1214}, {"token": "▁çĪ", "score": -9.30626, "line": 1215}, {"token": "▁ĠHa", "score": -9.31963, "line": 1225}, {"token": "▁ĠBo", "score": -9.32014, "line": 1226}, {"token": "▁ÑģÐ", "score": -9.32113, "line": 1227}, {"token": "▁̧ī", "score": -9.32227, "line": 1229}, {"token": "▁Ġcu", "score": -9.32523, "line": 1233}, {"token": "®3⁄4", "score": -9.32524, "line": 1234}, {"token": "▁èĢ", "score": -9.32809, "line": 1237}, {"token": "▁Ġfor", "score": -9.32992, "line": 1238}, {"token": "▁ĠTo", "score": -9.33054, "line": 1240}, {"token": "▁Ġx", "score": -9.33489, "line": 1244}, {"token": "▁ĠGe", "score": -9.33545, "line": 1246}, {"token": "▁Ġout", "score": -9.33682, "line": 1249}, {"token": "▁Ġex", "score": -9.34555, "line": 1258}, {"token": "▁çľ", "score": -9.34845, "line": 1264}, {"token": "▁ĠsÃ", "score": -9.35068, "line": 1266}, {"token": "▁//", "score": -9.35151, "line": 1267}, {"token": "▁ĠUn", "score": -9.35359, "line": 1270}, {"token": "▁ĠVi", "score": -9.35552, "line": 1273}, {"token": "▁âĪ", "score": -9.35656, "line": 1274}, {"token": "▁éħ", "score": -9.3593, "line": 1276}, {"token": "▁åĢ", "score": -9.3607, "line": 1280}, {"token": "▁æIJ", "score": -9.3646, "line": 1285}, {"token": "▁Ġan", "score": -9.36489, "line": 1287}, {"token": "▁Ġfo", "score": -9.36582, "line": 1288}, {"token": "▁av", "score": -9.3673, "line": 1290}, {"token": "▁&", "score": -9.37178, "line": 1294}, {"token": "▁Ġth", "score": -9.37391, "line": 1297}, {"token": "▁̧ł", "score": -9.37394, "line": 1298}, {"token": "▁åĴ", "score": -9.37481, "line": 1301}, {"token": "▁̧įè", "score": -9.37842, "line": 1304}, {"token": "▁âĦ", "score": -9.37982, "line": 1306}, {"token": "▁âĩ", "score": -9.37982, "line": 1307}, {"token": "▁et", "score": -9.38363, "line": 1315}, {"token": "▁ïŃ", "score": -9.38386, "line": 1316}, {"token": "▁Ġpu", "score": -9.38512, "line": 1319}, {"token": "▁iv", "score": -9.3859, "line": 1320}, {"token": "▁Ġru", "score": -9.38633, "line": 1322}, {"token": "▁Ġte", "score": -9.38687, "line": 1324}, {"token": "▁ĠtÃ", "score": -9.39096, "line": 1326}, {"token": "▁ir", "score": -9.39528, "line": 1328}, {"token": "▁ĠbÃ", "score": -9.39734, "line": 1330}, {"token": "▁|", "score": -9.39744, "line": 1331}, {"token": "▁Ġver", "score": -9.39766, "line": 1332}, {"token": "▁ĠPro", "score": -9.39801, "line": 1333}, {"token": "▁èĩ", "score": -9.40073, "line": 1336}, {"token": "▁èĦ", "score": -9.40634, "line": 1343}, {"token": "▁Y", "score": -9.40818, "line": 1345}, {"token": "▁̧ĸà", "score": -9.41001, "line": 1348}, {"token": "▁ðŁļ", "score": -9.41095, "line": 1350}, {"token": "▁èĮ", "score": -9.42045, "line": 1359}, {"token": "▁ĠNe", "score": -9.42108, "line": 1361}, {"token": "▁åĩ", "score": -9.4276, "line": 1365}, {"token": "▁âļ", "score": -9.42802, "line": 1366}, {"token": "▁èħ", "score": -9.43388, "line": 1371}, {"token": "▁od", "score": -9.43393, "line": 1372}, {"token": "▁çķ", "score": -9.44092, "line": 1377}, {"token": "▁cl", "score": -9.44296, "line": 1379}, {"token": "▁́è", "score": -9.4474, "line": 1385}, {"token": "▁ĠX", "score": -9.44766, "line": 1386}, {"token": "▁il", "score": -9.45016, "line": 1388}, {"token": "▁Ġsh", "score": -9.45057, "line": 1389}, {"token": "▁ÙģØ", "score": -9.45069, "line": 1390}, {"token": "▁Ġli", "score": -9.45078, "line": 1391}, {"token": "▁pr", "score": -9.45094, "line": 1393}, {"token": "▁co", "score": -9.45356, "line": 1400}, {"token": "▁̧ĺà", "score": -9.45431, "line": 1404}, {"token": "▁ÑĤÐ", "score": -9.45522, "line": 1405}, {"token": "▁Ġda", "score": -9.45906, "line": 1410}, {"token": "▁Ġmin", "score": -9.45956, "line": 1411}, {"token": "▁̧įä", "score": -9.46222, "line": 1414}, {"token": "▁Ġfa", "score": -9.46344, "line": 1417}, {"token": "▁ĠAr", "score": -9.46624, "line": 1424}, {"token": "▁çł", "score": -9.46785, "line": 1427}, {"token": "▁Ġcol", "score": -9.4775, "line": 1441}, {"token": "▁Ġtra", "score": -9.4782, "line": 1442}, {"token": "▁ĠDo", "score": -9.4782, "line": 1443}, {"token": "▁âĤ", "score": -9.47866, "line": 1445}, {"token": "▁be", "score": -9.48248, "line": 1450}, {"token": "▁ĠJa", "score": -9.48587, "line": 1452}, {"token": "▁and", "score": -9.48848, "line": 1456}, {"token": "▁̈ĭ", "score": -9.48898, "line": 1457}, {"token": "▁\">", "score": -9.49003, "line": 1461}, {"token": "▁em", "score": -9.49116, "line": 1464}, {"token": "▁̧ÑĤ", "score": -9.49582, "line": 1467}, {"token": "▁Ġbl", "score": -9.49613, "line": 1468}, {"token": "▁op", "score": -9.50242, "line": 1469}, {"token": "▁æľĢ", "score": -9.50244, "line": 1470}, {"token": "▁́»", "score": -9.50619, "line": 1476}, {"token": "▁set", "score": -9.50657, "line": 1477}, {"token": "▁Un", "score": -9.50872, "line": 1480}, {"token": "▁Ġad", "score": -9.50897, "line": 1481}, {"token": "▁çĮ", "score": -9.50978, "line": 1482}, {"token": "▁ĠSp", "score": -9.51351, "line": 1487}, {"token": "▁̧ĵ", "score": -9.51999, "line": 1494}, {"token": "▁ĠrÃ", "score": -9.52114, "line": 1496}, {"token": "▁âĢľ", "score": -9.5239, "line": 1499}, {"token": "▁iz", "score": -9.52431, "line": 1500}, {"token": "▁ãģĹãģ", "score": -9.52455, "line": 1501}, {"token": "▁Ġpi", "score": -9.52508, "line": 1502}, {"token": "▁ìĹ", "score": -9.5258, "line": 1504}, {"token": "▁çļĦæ", "score": -9.5259, "line": 1505}, {"token": "▁ad", "score": -9.52678, "line": 1508}, {"token": "▁ĠÙħØ", "score": -9.52882, "line": 1509}, {"token": "▁pl", "score": -9.52945, "line": 1511}, {"token": "▁̄1⁄4", "score": -9.53247, "line": 1521}, {"token": "▁Ġexp", "score": -9.53549, "line": 1526}, {"token": "▁('", "score": -9.53589, "line": 1527}, {"token": "▁íķ", "score": -9.54001, "line": 1532}, {"token": "▁Ġmis", "score": -9.5417, "line": 1535}, {"token": "▁ĠCom", "score": -9.54191, "line": 1536}, {"token": "▁=\"", "score": -9.54603, "line": 1540}, {"token": "▁̧ÑĤÐ", "score": -9.54641, "line": 1541}, {"token": "▁\"]", "score": -9.5468, "line": 1542}, {"token": "▁èī", "score": -9.54853, "line": 1545}, {"token": "▁̧ĥ", "score": -9.54883, "line": 1546}, {"token": "▁Ġnew", "score": -9.55023, "line": 1547}, {"token": "▁Ġwe", "score": -9.55035, "line": 1548}, {"token": "▁Ġvo", "score": -9.5507, "line": 1549}, {"token": "▁Ġdu", "score": -9.55199, "line": 1551}, {"token": "▁æı", "score": -9.55573, "line": 1554}, {"token": "▁Ġni", "score": -9.55612, "line": 1556}, {"token": "▁Ġam", "score": -9.55642, "line": 1558}, {"token": "▁Ġfl", "score": -9.55819, "line": 1559}, {"token": "▁çļĦç", "score": -9.56072, "line": 1562}, {"token": "▁åŁ", "score": -9.56347, "line": 1568}, {"token": "▁Ġle", "score": -9.56384, "line": 1569}, {"token": "▁áĪ", "score": -9.5653, "line": 1572}, {"token": "▁Ġsta", "score": -9.56589, "line": 1573}, {"token": "▁Ġim", "score": -9.56638, "line": 1574}, {"token": "▁Ġnu", "score": -9.56809, "line": 1575}, {"token": "▁çĥ", "score": -9.56978, "line": 1577}, {"token": "▁åĸ", "score": -9.57426, "line": 1584}, {"token": "▁×¦×", "score": -9.57445, "line": 1585}, {"token": "▁æļ", "score": -9.57453, "line": 1586}, {"token": "▁ÙĨØ", "score": -9.57602, "line": 1589}, {"token": "▁ĠHe", "score": -9.57944, "line": 1594}, {"token": "▁Ġunder", "score": -9.58138, "line": 1597}, {"token": "▁çĦ", "score": -9.58152, "line": 1598}, {"token": "▁Ġman", "score": -9.58224, "line": 1599}, {"token": "▁ĠPe", "score": -9.5831, "line": 1600}, {"token": "▁çļĦè", "score": -9.58486, "line": 1606}, {"token": "▁̧¶", "score": -9.58611, "line": 1607}, {"token": "▁âĵ", "score": -9.58835, "line": 1609}, {"token": "▁çļĦä", "score": -9.59176, "line": 1616}, {"token": "▁ĠKa", "score": -9.5959, "line": 1620}, {"token": "▁ĠPh", "score": -9.59898, "line": 1624}, {"token": "▁ĠvÃ", "score": -9.60089, "line": 1629}, {"token": "▁ëĭ", "score": -9.60265, "line": 1631}, {"token": "▁ĠÑĦÐ", "score": -9.60717, "line": 1638}, {"token": "▁Ġju", "score": -9.60762, "line": 1640}, {"token": "▁end", "score": -9.61012, "line": 1642}, {"token": "▁Co", "score": -9.61053, "line": 1644}, {"token": "▁̧Ģè", "score": -9.61528, "line": 1648}, {"token": "▁ĠDi", "score": -9.6171, "line": 1650}, {"token": "▁Ġauto", "score": -9.6172, "line": 1651}, {"token": "▁áĥ", "score": -9.61853, "line": 1654}, {"token": "▁èģ", "score": -9.61859, "line": 1655}, {"token": "▁to", "score": -9.61893, "line": 1656}, {"token": "▁̈ä", "score": -9.61893, "line": 1657}, {"token": "▁ĠCar", "score": -9.62069, "line": 1659}, {"token": "1⁄2¿", "score": -9.621, "line": 1660}, {"token": "▁os", "score": -9.62202, "line": 1661}, {"token": "▁ĠQu", "score": -9.62333, "line": 1663}, {"token": "▁åĶ", "score": -9.62501, "line": 1664}, {"token": "▁âĢĶ", "score": -9.62571, "line": 1665}, {"token": "▁Q", "score": -9.628, "line": 1667}, {"token": "▁{", "score": -9.62989, "line": 1671}, {"token": "1⁄2¬", "score": -9.63063, "line": 1672}, {"token": "▁Ġfr", "score": -9.63118, "line": 1673}, {"token": "▁̧ÑĩÐ", "score": -9.63421, "line": 1678}, {"token": "▁ĠTa", "score": -9.63533, "line": 1682}, {"token": "▁åĭ", "score": -9.63544, "line": 1683}, {"token": "▁ĠApp", "score": -9.63598, "line": 1684}, {"token": "▁ot", "score": -9.63611, "line": 1685}, {"token": "▁Ġva", "score": -9.63713, "line": 1686}, {"token": "▁Ġdec", "score": -9.63941, "line": 1688}, {"token": "▁åIJĦ", "score": -9.64083, "line": 1689}, {"token": "▁åģ", "score": -9.64553, "line": 1697}, {"token": "▁çħ", "score": -9.64693, "line": 1699}, {"token": "▁id", "score": -9.64936, "line": 1707}, {"token": "¤3⁄4", "score": -9.64991, "line": 1709}, {"token": "▁åĤ", "score": -9.65206, "line": 1715}, {"token": "▁de", "score": -9.65297, "line": 1716}, {"token": "▁èı", "score": -9.65673, "line": 1719}, {"token": "▁no", "score": -9.66442, "line": 1732}, {"token": "▁he", "score": -9.66514, "line": 1733}, {"token": "▁̧ľ", "score": -9.67227, "line": 1738}, {"token": "▁Ġpl", "score": -9.67262, "line": 1739}, {"token": "▁åo", "score": -9.67365, "line": 1742}, {"token": "▁æĹł", "score": -9.67578, "line": 1744}, {"token": "▁áĢ", "score": -9.67646, "line": 1745}, {"token": "▁̄æ", "score": -9.67831, "line": 1752}, {"token": "▁̄ģ", "score": -9.67873, "line": 1754}, {"token": "▁âĬ", "score": -9.67932, "line": 1756}, {"token": "▁Ġcl", "score": -9.67939, "line": 1757}, {"token": "▁ìĺ", "score": -9.6797, "line": 1759}, {"token": "▁ĠUI", "score": -9.68079, "line": 1760}, {"token": "▁Ġel", "score": -9.68315, "line": 1764}, {"token": "▁ĠhÃ", "score": -9.68354, "line": 1766}, {"token": "▁çĹ", "score": -9.68402, "line": 1768}, {"token": "▁ĠAd", "score": -9.68451, "line": 1771}, {"token": "▁çIJ", "score": -9.68472, "line": 1772}, {"token": "▁ud", "score": -9.68613, "line": 1774}, {"token": "1⁄2®", "score": -9.68983, "line": 1781}, {"token": "▁̧oä", "score": -9.69044, "line": 1782}, {"token": "▁̧ĵà", "score": -9.69131, "line": 1784}, {"token": "▁add", "score": -9.69136, "line": 1785}, {"token": "▁Ġmi", "score": -9.69909, "line": 1794}, {"token": "▁ìķ", "score": -9.70265, "line": 1797}, {"token": "▁str", "score": -9.70431, "line": 1799}, {"token": "▁çij", "score": -9.705, "line": 1800}, {"token": "▁ĠÄijá", "score": -9.70521, "line": 1801}, {"token": "▁ðŁķ", "score": -9.7056, "line": 1802}, {"token": "▁̄Ķ", "score": -9.70786, "line": 1804}, {"token": "▁ĠPar", "score": -9.70975, "line": 1808}, {"token": "▁̄Ĩ", "score": -9.71137, "line": 1812}, {"token": "▁âĮ", "score": -9.71157, "line": 1816}, {"token": "▁ãħ", "score": -9.71157, "line": 1817}, {"token": "▁ĠGu", "score": -9.71228, "line": 1819}, {"token": "▁éľ", "score": -9.71399, "line": 1822}, {"token": "▁Ġup", "score": -9.71559, "line": 1826}, {"token": "▁́ä", "score": -9.71561, "line": 1827}, {"token": "▁ĠPre", "score": -9.7167, "line": 1831}, {"token": "1⁄2¢", "score": -9.71982, "line": 1837}, {"token": "▁æħ", "score": -9.7218, "line": 1842}, {"token": "▁Ġka", "score": -9.72672, "line": 1845}, {"token": "▁Ġis", "score": -9.72713, "line": 1846}, {"token": "▁Ġop", "score": -9.72882, "line": 1848}, {"token": "▁uc", "score": -9.72948, "line": 1849}, {"token": "▁ĠOr", "score": -9.73066, "line": 1851}, {"token": "▁Ġche", "score": -9.7323, "line": 1852}, {"token": "▁Ġgo", "score": -9.73361, "line": 1857}, {"token": "▁́¢", "score": -9.7337, "line": 1858}, {"token": "▁`", "score": -9.73418, "line": 1859}, {"token": "▁ìĤ", "score": -9.73777, "line": 1860}, {"token": "▁çĺ", "score": -9.74058, "line": 1863}, {"token": "▁us", "score": -9.74359, "line": 1866}, {"token": "▁åıijå", "score": -9.74478, "line": 1868}, {"token": "▁...", "score": -9.74597, "line": 1875}, {"token": "▁ĠMan", "score": -9.74603, "line": 1876}, {"token": "▁̈æ", "score": -9.74676, "line": 1878}, {"token": "▁Con", "score": -9.74725, "line": 1880}, {"token": "▁ĠnÃ", "score": -9.74881, "line": 1884}, {"token": "▁ĠJe", "score": -9.74887, "line": 1885}, {"token": "▁ib", "score": -9.74917, "line": 1887}, {"token": "▁Ġap", "score": -9.75518, "line": 1893}, {"token": "▁̧¦", "score": -9.75817, "line": 1899}, {"token": "▁åĺ", "score": -9.7594, "line": 1904}, {"token": "▁St", "score": -9.76181, "line": 1910}, {"token": "▁Ġpar", "score": -9.76308, "line": 1912}, {"token": "▁æij", "score": -9.76312, "line": 1913}, {"token": "▁ĠGra", "score": -9.76432, "line": 1916}, {"token": "▁æĦ", "score": -9.76674, "line": 1919}, {"token": "▁̈Ģ", "score": -9.76704, "line": 1920}, {"token": "▁èĻ", "score": -9.76816, "line": 1923}, {"token": "▁if", "score": -9.76841, "line": 1925}, {"token": "▁åł", "score": -9.76948, "line": 1928}, {"token": "▁Ġcha", "score": -9.77049, "line": 1931}, {"token": "▁̧ÑģÐ", "score": -9.77144, "line": 1933}, {"token": "▁pe", "score": -9.77203, "line": 1935}, {"token": "▁èo", "score": -9.77248, "line": 1937}, {"token": "▁ãĤĴ", "score": -9.77301, "line": 1939}, {"token": "▁Ġmu", "score": -9.77422, "line": 1940}, {"token": "▁́æ", "score": -9.77495, "line": 1942}, {"token": "▁ou", "score": -9.77529, "line": 1943}, {"token": "▁Ġsk", "score": -9.77555, "line": 1944}, {"token": "▁Ġprop", "score": -9.77688, "line": 1947}, {"token": "▁̄è", "score": -9.77973, "line": 1953}, {"token": "▁ëĦ", "score": -9.78614, "line": 1967}, {"token": "▁èĽ", "score": -9.78642, "line": 1968}, {"token": "▁Ġsal", "score": -9.78692, "line": 1969}, {"token": "▁ìł", "score": -9.78728, "line": 1971}, {"token": "▁ow", "score": -9.78857, "line": 1974}, {"token": "▁æĵ", "score": -9.79498, "line": 1981}, {"token": "▁ĠLu", "score": -9.80042, "line": 1985}, {"token": "▁ĠFor", "score": -9.80162, "line": 1986}, {"token": "▁Ġtá", "score": -9.81178, "line": 1997}, {"token": "▁ant", "score": -9.8119, "line": 1998}, {"token": "▁ĠHu", "score": -9.81197, "line": 1999}, {"token": "▁èĴ", "score": -9.81331, "line": 2001}, {"token": "▁æīĭ", "score": -9.81409, "line": 2004}, {"token": "▁Ġtu", "score": -9.81441, "line": 2005}, {"token": "▁Ġtrans", "score": -9.81505, "line": 2007}, {"token": "▁ãİ", "score": -9.8151, "line": 2008}, {"token": "▁ĠTr", "score": -9.81523, "line": 2012}, {"token": "▁ip", "score": -9.81539, "line": 2013}, {"token": "▁ĠÅ", "score": -9.81827, "line": 2018}, {"token": "▁ĠcÃ", "score": -9.82029, "line": 2021}, {"token": "▁ĠDis", "score": -9.82265, "line": 2023}, {"token": "▁))", "score": -9.82399, "line": 2028}, {"token": "▁ĠJo", "score": -9.8272, "line": 2032}, {"token": "▁̧įç", "score": -9.82917, "line": 2036}, {"token": "▁̈ãģ", "score": -9.82923, "line": 2037}, {"token": "▁__", "score": -9.82999, "line": 2038}, {"token": "▁Ġinf", "score": -9.83125, "line": 2042}, {"token": "▁èĵ", "score": -9.83295, "line": 2046}, {"token": "▁ĠWe", "score": -9.83307, "line": 2047}, {"token": "▁\")", "score": -9.83331, "line": 2049}, {"token": "▁ĠFi", "score": -9.83454, "line": 2051}, {"token": "▁Ġint", "score": -9.83527, "line": 2053}, {"token": "▁æĪIJ", "score": -9.8376, "line": 2056}, {"token": "▁Ġind", "score": -9.83946, "line": 2061}, {"token": "▁Ġchi", "score": -9.83982, "line": 2062}, {"token": "▁ëĤ", "score": -9.84194, "line": 2066}, {"token": "▁ĠMÃ", "score": -9.84224, "line": 2068}, {"token": "▁æĪij", "score": -9.84246, "line": 2069}, {"token": "▁ĠVo", "score": -9.84366, "line": 2071}, {"token": "▁ĠEl", "score": -9.84462, "line": 2073}, {"token": "▁ne", "score": -9.84484, "line": 2075}, {"token": "▁li", "score": -9.84556, "line": 2076}, {"token": "▁ãģaãģ", "score": -9.84626, "line": 2077}, {"token": "▁äoĮ", "score": -9.84825, "line": 2079}, {"token": "▁èľ", "score": -9.84968, "line": 2081}, {"token": "▁̧ĭà", "score": -9.8505, "line": 2083}, {"token": "▁ìĸ", "score": -9.85166, "line": 2085}, {"token": "▁ìĦ", "score": -9.85179, "line": 2087}, {"token": "▁ĠWi", "score": -9.85183, "line": 2088}, {"token": "▁âī", "score": -9.85214, "line": 2089}, {"token": "▁âı", "score": -9.85214, "line": 2090}, {"token": "▁åIJĮ", "score": -9.85215, "line": 2091}, {"token": "▁Ġmar", "score": -9.85449, "line": 2100}, {"token": "▁Pro", "score": -9.85541, "line": 2102}, {"token": "▁çĬ", "score": -9.85632, "line": 2104}, {"token": "▁íĮ", "score": -9.85646, "line": 2106}, {"token": "▁æıIJ", "score": -9.859, "line": 2110}, {"token": "▁́ë", "score": -9.86113, "line": 2115}, {"token": "▁ðŁĩ", "score": -9.86131, "line": 2116}, {"token": "▁̧Ľ", "score": -9.86248, "line": 2121}, {"token": "▁̄Ħ", "score": -9.86251, "line": 2122}, {"token": "▁Ġga", "score": -9.86254, "line": 2123}, {"token": "▁Ġcor", "score": -9.86451, "line": 2126}, {"token": "▁ak", "score": -9.86502, "line": 2128}, {"token": "▁åĹ", "score": -9.86639, "line": 2131}, {"token": "▁Ġje", "score": -9.86679, "line": 2132}, {"token": "▁æo", "score": -9.87184, "line": 2136}, {"token": "▁̧ĸ", "score": -9.87396, "line": 2138}, {"token": "▁̄ç", "score": -9.87528, "line": 2139}, {"token": "▁ens", "score": -9.87629, "line": 2143}, {"token": "▁ĠEx", "score": -9.87743, "line": 2145}, {"token": "▁ÙĬØ", "score": -9.88025, "line": 2150}, {"token": "▁́§", "score": -9.8803, "line": 2151}, {"token": "▁ob", "score": -9.88228, "line": 2153}, {"token": "▁çĴ", "score": -9.88276, "line": 2155}, {"token": "▁Ġcan", "score": -9.88329, "line": 2158}, {"token": "▁éĨ", "score": -9.88489, "line": 2159}, {"token": "▁åķĨ", "score": -9.88568, "line": 2161}, {"token": "▁Ch", "score": -9.88574, "line": 2162}, {"token": "▁az", "score": -9.88625, "line": 2164}, {"token": "▁ĠMi", "score": -9.88713, "line": 2165}, {"token": "▁q", "score": -9.88833, "line": 2166}, {"token": "▁̄ÙĬØ", "score": -9.88857, "line": 2167}, {"token": "▁âİ", "score": -9.8906, "line": 2170}, {"token": "▁âĽ", "score": -9.8906, "line": 2171}, {"token": "▁ãĦ", "score": -9.8906, "line": 2172}, {"token": "▁èij", "score": -9.89102, "line": 2176}, {"token": "▁ĠHar", "score": -9.89443, "line": 2186}, {"token": "▁åĪĨ", "score": -9.89538, "line": 2189}, {"token": "▁ĠprÃ", "score": -9.89574, "line": 2193}, {"token": "▁con", "score": -9.89633, "line": 2194}, {"token": "▁ĠNS", "score": -9.89645, "line": 2196}, {"token": "▁̈ç", "score": -9.89648, "line": 2197}, {"token": "▁èļ", "score": -9.90304, "line": 2206}, {"token": "▁ist", "score": -9.90474, "line": 2209}, {"token": "▁çŃī", "score": -9.90675, "line": 2211}, {"token": "▁ĠBar", "score": -9.91154, "line": 2217}, {"token": "▁ĠkÃ", "score": -9.91243, "line": 2219}, {"token": "▁um", "score": -9.91279, "line": 2220}, {"token": "▁ĠTra", "score": -9.91341, "line": 2221}, {"token": "▁])", "score": -9.91432, "line": 2223}, {"token": "23⁄4", "score": -9.91857, "line": 2230}, {"token": "▁Ġlu", "score": -9.91999, "line": 2232}, {"token": "▁ĠÑĥÐ", "score": -9.92025, "line": 2234}, {"token": "▁æīĵ", "score": -9.92118, "line": 2235}, {"token": "▁ell", "score": -9.92285, "line": 2237}, {"token": "▁̧ķ", "score": -9.92468, "line": 2241}, {"token": "▁æĪijä", "score": -9.92559, "line": 2243}, {"token": "▁åİŁ", "score": -9.92748, "line": 2249}, {"token": "▁Ġapp", "score": -9.92966, "line": 2256}, {"token": "▁ĠMc", "score": -9.92972, "line": 2257}, {"token": "▁ba", "score": -9.93058, "line": 2261}, {"token": "▁og", "score": -9.93105, "line": 2267}, {"token": "▁ĠTe", "score": -9.93231, "line": 2273}, {"token": "▁̈ÙĬØ", "score": -9.93495, "line": 2287}, {"token": "▁ĠGi", "score": -9.93519, "line": 2288}, {"token": "▁Ġmil", "score": -9.93757, "line": 2292}, {"token": "▁åĨľ", "score": -9.94204, "line": 2295}, {"token": "▁Ġcho", "score": -9.94268, "line": 2296}, {"token": "▁ÃŃ", "score": -9.94535, "line": 2299}, {"token": "▁ill", "score": -9.94979, "line": 2306}, {"token": "▁IN", "score": -9.95161, "line": 2309}, {"token": "▁ĠGa", "score": -9.95478, "line": 2312}, {"token": "▁ãģĵãģ", "score": -9.95578, "line": 2313}, {"token": "▁ëł", "score": -9.95799, "line": 2317}, {"token": "▁ĠMon", "score": -9.95845, "line": 2319}, {"token": "▁ÑĥÐ", "score": -9.96473, "line": 2325}, {"token": "▁æľīå", "score": -9.9674, "line": 2330}, {"token": "▁Ġjo", "score": -9.96853, "line": 2332}, {"token": "▁ĠJu", "score": -9.96865, "line": 2333}, {"token": "▁æĴ", "score": -9.9694, "line": 2334}, {"token": "▁ĠCor", "score": -9.97006, "line": 2335}, {"token": "▁ÑģÑĤÐ", "score": -9.97117, "line": 2339}, {"token": "▁̧æĪı", "score": -9.97235, "line": 2345}, {"token": "▁ĠDu", "score": -9.97286, "line": 2349}, {"token": "▁RE", "score": -9.97299, "line": 2351}, {"token": "▁ge", "score": -9.97312, "line": 2352}, {"token": "▁Ġdes", "score": -9.97421, "line": 2353}, {"token": "▁ĠÄijÃ", "score": -9.97798, "line": 2359}, {"token": "▁ĠPo", "score": -9.97842, "line": 2360}, {"token": "▁æľīä", "score": -9.9786, "line": 2362}, {"token": "▁AR", "score": -9.9826, "line": 2374}, {"token": "▁Ġcri", "score": -9.98413, "line": 2378}, {"token": "▁Ġpla", "score": -9.98466, "line": 2379}, {"token": "▁́£", "score": -9.98605, "line": 2380}, {"token": "▁rÃ", "score": -9.98793, "line": 2383}, {"token": "▁Ġwy", "score": -9.99106, "line": 2385}, {"token": "▁tr", "score": -9.99138, "line": 2386}, {"token": "▁Al", "score": -9.99325, "line": 2390}, {"token": "▁íĥ", "score": -9.99396, "line": 2391}, {"token": "▁æľĢå", "score": -9.99755, "line": 2397}, {"token": "▁Lo", "score": -9.99858, "line": 2399}, {"token": "▁ĠKo", "score": -10.0004, "line": 2401}, {"token": "▁ëIJ", "score": -10.0024, "line": 2404}, {"token": "▁Ġcre", "score": -10.003, "line": 2405}, {"token": "▁ìĥ", "score": -10.0045, "line": 2407}, {"token": "▁ìľ", "score": -10.0047, "line": 2408}, {"token": "▁lic", "score": -10.0062, "line": 2410}, {"token": "▁çļ", "score": -10.008, "line": 2412}, {"token": "▁̧ħ", "score": -10.0084, "line": 2414}, {"token": "▁Le", "score": -10.009, "line": 2415}, {"token": "▁åıĺ", "score": -10.0098, "line": 2416}, {"token": "▁ĠAb", "score": -10.0132, "line": 2420}, {"token": "▁ĠNo", "score": -10.0138, "line": 2422}, {"token": "▁Pa", "score": -10.0161, "line": 2430}, {"token": "▁Com", "score": -10.0167, "line": 2435}, {"token": "▁ça", "score": -10.0175, "line": 2437}, {"token": "▁ĠAm", "score": -10.0188, "line": 2444}, {"token": "▁Ù", "score": -10.0203, "line": 2448}, {"token": "▁ëĵ", "score": -10.0212, "line": 2450}, {"token": "▁Ġoff", "score": -10.0218, "line": 2451}, {"token": "▁íĹ", "score": -10.022, "line": 2454}, {"token": "▁ìĽ", "score": -10.0222, "line": 2456}, {"token": "▁ĠGo", "score": -10.0222, "line": 2457}, {"token": "▁éo", "score": -10.0223, "line": 2458}, {"token": "▁̧įæ", "score": -10.0225, "line": 2459}, {"token": "▁Ġrep", "score": -10.0234, "line": 2461}, {"token": "▁ĠlÃ", "score": -10.0245, "line": 2466}, {"token": "▁ĠBi", "score": -10.0252, "line": 2467}, {"token": "▁Be", "score": -10.0261, "line": 2471}, {"token": "▁ĠSha", "score": -10.0323, "line": 2481}, {"token": "▁ðŁĽ", "score": -10.0323, "line": 2482}, {"token": "▁Ġcur", "score": -10.0353, "line": 2488}, {"token": "▁ach", "score": -10.0357, "line": 2489}, {"token": "▁Ġko", "score": -10.0395, "line": 2491}, {"token": "▁̄¥", "score": -10.0414, "line": 2493}, {"token": "▁Ġsol", "score": -10.043, "line": 2496}, {"token": "▁éŁ", "score": -10.045, "line": 2500}, {"token": "▁NS", "score": -10.0479, "line": 2503}, {"token": "▁up", "score": -10.0537, "line": 2514}, {"token": "▁ìĬ", "score": -10.0563, "line": 2517}, {"token": "▁íĸ", "score": -10.0569, "line": 2519}, {"token": "▁èĸ", "score": -10.0571, "line": 2520}, {"token": "▁ub", "score": -10.0585, "line": 2521}, {"token": "▁âģ", "score": -10.0612, "line": 2525}, {"token": "▁̧Ĥåľo", "score": -10.0616, "line": 2530}, {"token": "▁éĦ", "score": -10.0622, "line": 2533}, {"token": "▁ĠFe", "score": -10.0648, "line": 2538}, {"token": "▁́Ń", "score": -10.0654, "line": 2540}, {"token": "▁ĠÑĩÐ", "score": -10.0676, "line": 2546}, {"token": "▁ðIJ", "score": -10.0688, "line": 2547}, {"token": "▁ĠÄijáo", "score": -10.0708, "line": 2553}, {"token": "▁ay", "score": -10.0711, "line": 2555}, {"token": "▁ðŁħ", "score": -10.072, "line": 2558}, {"token": "▁Ġconc", "score": -10.0741, "line": 2563}, {"token": "▁ma", "score": -10.0741, "line": 2565}, {"token": "▁qui", "score": -10.0747, "line": 2567}, {"token": "▁For", "score": -10.0749, "line": 2568}, {"token": "▁erv", "score": -10.0752, "line": 2570}, {"token": "▁́ł", "score": -10.0759, "line": 2574}, {"token": "▁ĠRu", "score": -10.078, "line": 2577}, {"token": "▁Ġfre", "score": -10.0814, "line": 2584}, {"token": "▁ëį", "score": -10.0839, "line": 2588}, {"token": "▁per", "score": -10.0852, "line": 2593}, {"token": "▁ĠNi", "score": -10.0862, "line": 2595}, {"token": "▁Get", "score": -10.0872, "line": 2596}, {"token": "▁ĠâĢ", "score": -10.0881, "line": 2598}, {"token": "▁UI", "score": -10.0906, "line": 2601}, {"token": "▁Ġmat", "score": -10.0915, "line": 2603}, {"token": "▁ÑĪÐ", "score": -10.0916, "line": 2604}, {"token": "▁ĠÂ", "score": -10.0942, "line": 2607}, {"token": "▁éŃ", "score": -10.0942, "line": 2608}, {"token": "▁Ġya", "score": -10.0959, "line": 2611}, {"token": "▁Ġhu", "score": -10.0965, "line": 2613}, {"token": "▁̧İ", "score": -10.0985, "line": 2622}, {"token": "▁au", "score": -10.0988, "line": 2623}, {"token": "▁ĠVer", "score": -10.0996, "line": 2627}, {"token": "▁ĠWo", "score": -10.1056, "line": 2635}, {"token": "▁̧įà", "score": -10.1076, "line": 2638}, {"token": "▁ĠPer", "score": -10.1078, "line": 2639}, {"token": "▁RO", "score": -10.1079, "line": 2640}, {"token": "▁ĠØaØ", "score": -10.1082, "line": 2641}, {"token": "▁ãģĦãģ", "score": -10.1116, "line": 2656}, {"token": "▁the", "score": -10.1122, "line": 2659}, {"token": "▁íİ", "score": -10.1124, "line": 2660}, {"token": "▁ok", "score": -10.1128, "line": 2661}, {"token": "▁çĨ", "score": -10.115, "line": 2666}, {"token": "▁íĽ", "score": -10.1153, "line": 2667}, {"token": "▁íĵ", "score": -10.1156, "line": 2668}, {"token": "▁ĠyÃ", "score": -10.1156, "line": 2669}, {"token": "▁);", "score": -10.116, "line": 2672}, {"token": "▁Ġfu", "score": -10.1193, "line": 2677}, {"token": "▁ĠEn", "score": -10.1217, "line": 2682}, {"token": "▁ĠAss", "score": -10.1218, "line": 2683}, {"token": "▁fÃ", "score": -10.1223, "line": 2686}, {"token": "▁Ġti", "score": -10.1231, "line": 2688}, {"token": "▁Ġuser", "score": -10.1234, "line": 2690}, {"token": "▁åĽĽ", "score": -10.1244, "line": 2694}, {"token": "▁ess", "score": -10.1266, "line": 2699}, {"token": "▁åıį", "score": -10.1272, "line": 2703}, {"token": "▁Ġhand", "score": -10.1274, "line": 2704}, {"token": "▁ind", "score": -10.1276, "line": 2705}, {"token": "▁çĩ", "score": -10.13, "line": 2709}, {"token": "▁ĠÑģÑĤÐ", "score": -10.1301, "line": 2710}, {"token": "▁åģļ", "score": -10.1301, "line": 2711}, {"token": "▁ĠMil", "score": -10.1318, "line": 2714}, {"token": "▁̧Ñħ", "score": -10.1319, "line": 2715}, {"token": "▁ik", "score": -10.1325, "line": 2717}, {"token": "▁ver", "score": -10.1345, "line": 2725}, {"token": "▁amp", "score": -10.1359, "line": 2728}, {"token": "▁ann", "score": -10.1395, "line": 2734}, {"token": "▁áł", "score": -10.1405, "line": 2736}, {"token": "▁Ġcompl", "score": -10.1501, "line": 2749}, {"token": "▁ìħ", "score": -10.1501, "line": 2750}, {"token": "▁̄į", "score": -10.1522, "line": 2754}, {"token": "▁ass", "score": -10.1549, "line": 2762}, {"token": "▁èĢģ", "score": -10.1553, "line": 2765}, {"token": "▁Ġhá", "score": -10.1562, "line": 2767}, {"token": "▁̧įåı", "score": -10.1565, "line": 2768}, {"token": "▁ĠMor", "score": -10.1603, "line": 2789}, {"token": "▁Ġwh", "score": -10.1603, "line": 2790}, {"token": "▁äoĨä", "score": -10.1615, "line": 2796}, {"token": "▁ĠMin", "score": -10.1622, "line": 2799}, {"token": "▁Ġart", "score": -10.1632, "line": 2802}, {"token": "▁Ġq", "score": -10.1653, "line": 2809}, {"token": "▁Ġvá", "score": -10.166, "line": 2811}, {"token": "▁ĠCan", "score": -10.1666, "line": 2812}, {"token": "▁eg", "score": -10.1687, "line": 2818}, {"token": "▁Ġaf", "score": -10.1694, "line": 2821}, {"token": "▁ĠFre", "score": -10.1702, "line": 2823}, {"token": "▁Ġzo", "score": -10.1704, "line": 2824}, {"token": "▁bo", "score": -10.1717, "line": 2828}, {"token": "▁åĩł", "score": -10.1727, "line": 2830}, {"token": "▁åIJį", "score": -10.1749, "line": 2835}, {"token": "▁æķĻ", "score": -10.1773, "line": 2841}, {"token": "▁Ġsur", "score": -10.178, "line": 2844}, {"token": "▁ĠBra", "score": -10.1804, "line": 2846}, {"token": "▁ĠTri", "score": -10.1819, "line": 2850}, {"token": "▁åįģä", "score": -10.182, "line": 2851}, {"token": "▁Ġtri", "score": -10.1822, "line": 2852}, {"token": "▁̄Į", "score": -10.1842, "line": 2856}, {"token": "▁ĠÑħÐ", "score": -10.189, "line": 2862}, {"token": "▁vi", "score": -10.1915, "line": 2864}, {"token": "▁ĠHer", "score": -10.1932, "line": 2866}, {"token": "▁äooæ", "score": -10.1937, "line": 2867}, {"token": "▁par", "score": -10.1939, "line": 2868}, {"token": "▁Ġtw", "score": -10.1941, "line": 2869}, {"token": "▁Ġfac", "score": -10.1953, "line": 2871}, {"token": "▁Ġpara", "score": -10.1955, "line": 2872}, {"token": "▁With", "score": -10.1959, "line": 2874}, {"token": "▁ang", "score": -10.1989, "line": 2881}, {"token": "▁áĬ", "score": -10.1991, "line": 2882}, {"token": "▁ĠYa", "score": -10.1999, "line": 2884}, {"token": "▁ĠHi", "score": -10.2002, "line": 2885}, {"token": "▁ug", "score": -10.201, "line": 2887}, {"token": "▁̧Ñģ", "score": -10.203, "line": 2890}, {"token": "▁Ġstart", "score": -10.2035, "line": 2891}, {"token": "▁om", "score": -10.2036, "line": 2892}, {"token": "▁Ġchá", "score": -10.2051, "line": 2896}, {"token": "▁çĵ", "score": -10.2053, "line": 2897}, {"token": "▁Ġthá", "score": -10.206, "line": 2899}, {"token": "▁ĠÄij", "score": -10.206, "line": 2900}, {"token": "▁Ġtext", "score": -10.2064, "line": 2902}, {"token": "▁Ġshow", "score": -10.2078, "line": 2905}, {"token": "▁ati", "score": -10.2078, "line": 2906}, {"token": "▁Ġbra", "score": -10.208, "line": 2907}, {"token": "▁ĠĠĠĠĠĠĠĠĠĠĠĠ", "score": -10.2081, "line": 2908}, {"token": "▁ha", "score": -10.2084, "line": 2909}, {"token": "▁̧Ģé", "score": -10.2085, "line": 2910}, {"token": "▁da", "score": -10.2096, "line": 2911}, {"token": "▁åıĹ", "score": -10.2102, "line": 2913}, {"token": "▁̧Ðo", "score": -10.2109, "line": 2914}, {"token": "▁ãĪ", "score": -10.2115, "line": 2916}, {"token": "▁çľŁ", "score": -10.2118, "line": 2923}, {"token": "▁èĨ", "score": -10.2119, "line": 2926}, {"token": "▁ord", "score": -10.2127, "line": 2930}, {"token": "▁ëĶ", "score": -10.2129, "line": 2932}, {"token": "▁Ġsha", "score": -10.2131, "line": 2934}, {"token": "▁ÙĥØ", "score": -10.2132, "line": 2935}, {"token": "▁ìĪ", "score": -10.2135, "line": 2939}, {"token": "▁áī", "score": -10.2138, "line": 2940}, {"token": "▁ãĢĤ", "score": -10.215, "line": 2947}, {"token": "▁íħ", "score": -10.2159, "line": 2951}, {"token": "▁Ġcra", "score": -10.2176, "line": 2954}, {"token": "▁Ġtáo", "score": -10.2236, "line": 2973}, {"token": "▁Ġste", "score": -10.2244, "line": 2975}, {"token": "▁OL", "score": -10.2255, "line": 2978}, {"token": "▁ĠìĤ", "score": -10.2263, "line": 2979}, {"token": "▁ìĭ", "score": -10.2279, "line": 2982}, {"token": "▁̈é", "score": -10.2318, "line": 2985}, {"token": "▁ĠShe", "score": -10.2326, "line": 2987}, {"token": "▁ðŁĹ", "score": -10.2332, "line": 2989}, {"token": "▁={", "score": -10.2359, "line": 2994}, {"token": "▁Ġpor", "score": -10.2366, "line": 2995}, {"token": "▁yn", "score": -10.2368, "line": 2996}, {"token": "▁åĩoå", "score": -10.2368, "line": 2997}, {"token": "▁ĠthÃ", "score": -10.2377, "line": 2998}, {"token": "▁ert", "score": -10.2391, "line": 3000}, {"token": "▁ĠMu", "score": -10.2395, "line": 3001}, {"token": "▁Ġcontra", "score": -10.2396, "line": 3002}, {"token": "▁ĠUser", "score": -10.2418, "line": 3005}, {"token": "▁Z", "score": -10.2425, "line": 3008}, {"token": "▁Ġser", "score": -10.2429, "line": 3013}, {"token": "▁Ġcas", "score": -10.2454, "line": 3015}, {"token": "▁Ġfla", "score": -10.2493, "line": 3019}, {"token": "▁Ġwork", "score": -10.2509, "line": 3022}, {"token": "▁ìĨ", "score": -10.2537, "line": 3028}, {"token": "▁̧ÑĢÐ", "score": -10.254, "line": 3029}, {"token": "▁ĉĉĉ", "score": -10.254, "line": 3030}, {"token": "▁Ġuse", "score": -10.2552, "line": 3034}, {"token": "▁áħ", "score": -10.2579, "line": 3039}, {"token": "▁Ġair", "score": -10.258, "line": 3040}, {"token": "▁åĬłå", "score": -10.2602, "line": 3049}, {"token": "▁ec", "score": -10.2617, "line": 3051}, {"token": "▁åĥ", "score": -10.2618, "line": 3052}, {"token": "▁̄Ń", "score": -10.2624, "line": 3053}, {"token": "▁Ġprov", "score": -10.2632, "line": 3054}, {"token": "▁Ġsuper", "score": -10.2636, "line": 3055}, {"token": "▁ni", "score": -10.265, "line": 3057}, {"token": "▁Ġmá", "score": -10.2659, "line": 3063}, {"token": "▁ific", "score": -10.2662, "line": 3064}, {"token": "▁ãī", "score": -10.267, "line": 3068}, {"token": "▁ÙħÙĨØ", "score": -10.2675, "line": 3078}, {"token": "▁áĭ", "score": -10.2676, "line": 3079}, {"token": "▁ëĸ", "score": -10.2684, "line": 3083}, {"token": "▁Ġpost", "score": -10.2685, "line": 3084}, {"token": "▁Ġdeb", "score": -10.2686, "line": 3086}, {"token": "▁çĶŁäo", "score": -10.2697, "line": 3091}, {"token": "▁ĠÐľÐ", "score": -10.2699, "line": 3093}, {"token": "▁ili", "score": -10.2708, "line": 3096}, {"token": "▁fl", "score": -10.2708, "line": 3098}, {"token": "▁̈æĢ", "score": -10.2726, "line": 3106}, {"token": "▁̄Ķè", "score": -10.2735, "line": 3109}, {"token": "▁PL", "score": -10.2778, "line": 3117}, {"token": "▁Ġmedi", "score": -10.2802, "line": 3121}, {"token": "▁ĠGar", "score": -10.2806, "line": 3122}, {"token": "▁ke", "score": -10.2811, "line": 3124}, {"token": "▁çĶŁæ", "score": -10.2812, "line": 3125}, {"token": "▁ðŁĸ", "score": -10.283, "line": 3127}, {"token": "▁ĠChe", "score": -10.2848, "line": 3131}, {"token": "▁ĠNor", "score": -10.2869, "line": 3137}, {"token": "▁Ġnot", "score": -10.2887, "line": 3143}, {"token": "▁lÃ", "score": -10.29, "line": 3146}, {"token": "▁̄Ł", "score": -10.2903, "line": 3147}, {"token": "▁ov", "score": -10.2916, "line": 3148}, {"token": "▁::", "score": -10.2933, "line": 3150}, {"token": "▁Ġprec", "score": -10.2955, "line": 3153}, {"token": "▁app", "score": -10.2975, "line": 3157}, {"token": "▁Ġpri", "score": -10.2993, "line": 3160}, {"token": "▁und", "score": -10.3004, "line": 3163}, {"token": "▁ĠFile", "score": -10.3023, "line": 3166}, {"token": "▁Ġinc", "score": -10.3026, "line": 3167}, {"token": "▁Dis", "score": -10.304, "line": 3169}, {"token": "▁áIJ", "score": -10.3066, "line": 3173}, {"token": "▁ÑĩÐ", "score": -10.3069, "line": 3174}, {"token": "▁Ġíķ", "score": -10.3081, "line": 3177}, {"token": "▁̈İ", "score": -10.3141, "line": 3185}, {"token": "▁íĻ", "score": -10.3155, "line": 3187}, {"token": "▁pos", "score": -10.3198, "line": 3194}, {"token": "▁Tra", "score": -10.3219, "line": 3199}, {"token": "▁Ġback", "score": -10.3233, "line": 3202}, {"token": "▁af", "score": -10.3239, "line": 3203}, {"token": "▁ĠWal", "score": -10.3246, "line": 3204}, {"token": "▁âĭ", "score": -10.3258, "line": 3205}, {"token": "▁âŁ", "score": -10.3258, "line": 3206}, {"token": "▁Ġpass", "score": -10.3265, "line": 3218}, {"token": "▁ìļ", "score": -10.3272, "line": 3223}, {"token": "▁Ġsyn", "score": -10.3273, "line": 3224}, {"token": "▁Ġpen", "score": -10.3273, "line": 3225}, {"token": "▁ĠÐŁÐ", "score": -10.3274, "line": 3228}, {"token": "▁̧Łà", "score": -10.3282, "line": 3231}, {"token": "▁ĠPri", "score": -10.3321, "line": 3245}, {"token": "▁ĠĠĠĠĠĠĠĠĠĠĠĠĠĠ", "score": -10.3323, "line": 3246}, {"token": "▁bi", "score": -10.3336, "line": 3255}, {"token": "▁Ġtháo", "score": -10.3339, "line": 3257}, {"token": "▁Ġháo", "score": -10.3359, "line": 3261}, {"token": "▁Ġanti", "score": -10.336, "line": 3262}, {"token": "▁ĠCha", "score": -10.3374, "line": 3268}, {"token": "▁dÄ", "score": -10.3382, "line": 3271}, {"token": "▁ÙħÙ", "score": -10.3382, "line": 3272}, {"token": "▁pa", "score": -10.3385, "line": 3273}, {"token": "▁äooå", "score": -10.3396, "line": 3277}, {"token": "▁ze", "score": -10.3398, "line": 3279}, {"token": "▁ëĪ", "score": -10.34, "line": 3280}, {"token": "▁Ġmulti", "score": -10.3401, "line": 3281}, {"token": "▁err", "score": -10.3434, "line": 3291}, {"token": "▁ĠDr", "score": -10.3441, "line": 3293}, {"token": "▁åįģå", "score": -10.3442, "line": 3294}, {"token": "▁ĠReg", "score": -10.3442, "line": 3295}, {"token": "▁enti", "score": -10.3475, "line": 3303}, {"token": "▁èĢĥ", "score": -10.3509, "line": 3308}, {"token": "▁íĬ", "score": -10.3526, "line": 3312}, {"token": "▁rit", "score": -10.3548, "line": 3314}, {"token": "▁sp", "score": -10.3557, "line": 3316}, {"token": "▁Ġqua", "score": -10.3592, "line": 3328}, {"token": "▁Ar", "score": -10.3605, "line": 3332}, {"token": "▁).", "score": -10.3628, "line": 3337}, {"token": "▁æľįå", "score": -10.363, "line": 3338}, {"token": "▁Ġbar", "score": -10.3654, "line": 3344}, {"token": "▁̧ÐoÐ", "score": -10.3655, "line": 3345}, {"token": "▁çį", "score": -10.3656, "line": 3346}, {"token": "▁ĠSte", "score": -10.3668, "line": 3349}, {"token": "▁̧īå", "score": -10.3684, "line": 3353}, {"token": "▁ĠBur", "score": -10.3685, "line": 3354}, {"token": "▁éħį", "score": -10.3705, "line": 3356}, {"token": "▁ĠfÃ", "score": -10.3709, "line": 3358}, {"token": "▁ðŁĻ", "score": -10.3757, "line": 3365}, {"token": "▁Ġqui", "score": -10.3779, "line": 3367}, {"token": "▁ĠInter", "score": -10.3781, "line": 3368}, {"token": "▁ëħ", "score": -10.3792, "line": 3371}, {"token": "▁ain", "score": -10.3821, "line": 3379}, {"token": "▁ĠAg", "score": -10.3838, "line": 3386}, {"token": "▁Pre", "score": -10.3849, "line": 3389}, {"token": "▁ĠSch", "score": -10.387, "line": 3394}, {"token": "▁mÃ", "score": -10.388, "line": 3396}, {"token": "▁̧įæĺ", "score": -10.3883, "line": 3397}, {"token": "▁âĴ", "score": -10.3883, "line": 3398}, {"token": "▁Ġelectr", "score": -10.3884, "line": 3403}, {"token": "▁Ġkhá", "score": -10.3885, "line": 3404}, {"token": "▁ĠPat", "score": -10.3886, "line": 3407}, {"token": "▁ĠÐłÐ", "score": -10.391, "line": 3427}, {"token": "▁éĢļ", "score": -10.3925, "line": 3433}, {"token": "▁íĨ", "score": -10.3933, "line": 3436}, {"token": "▁íģ", "score": -10.3949, "line": 3444}, {"token": "▁ust", "score": -10.3965, "line": 3447}, {"token": "▁áĦ", "score": -10.4001, "line": 3455}, {"token": "▁ĠchÃ", "score": -10.4014, "line": 3458}, {"token": "▁Ġsn", "score": -10.4014, "line": 3459}, {"token": "▁Ġfra", "score": -10.4063, "line": 3469}, {"token": "▁̧aå", "score": -10.4063, "line": 3470}, {"token": "▁Ġìĸ", "score": -10.4107, "line": 3484}, {"token": "▁ff", "score": -10.4108, "line": 3485}, {"token": "▁fo", "score": -10.4145, "line": 3491}, {"token": "▁ĠIm", "score": -10.415, "line": 3492}, {"token": "▁ĠtrÃ", "score": -10.4158, "line": 3493}, {"token": "▁Of", "score": -10.4177, "line": 3495}, {"token": "▁æĨ", "score": -10.4188, "line": 3498}, {"token": "▁Sp", "score": -10.4202, "line": 3504}, {"token": "▁Me", "score": -10.4224, "line": 3507}, {"token": "▁äoĶ", "score": -10.4229, "line": 3509}, {"token": "▁ĠSta", "score": -10.4244, "line": 3513}, {"token": "▁Or", "score": -10.4264, "line": 3516}, {"token": "▁æĪijå", "score": -10.4266, "line": 3518}, {"token": "▁ĠphÃ", "score": -10.4276, "line": 3521}, {"token": "▁çłĶ", "score": -10.4291, "line": 3522}, {"token": "▁Ġec", "score": -10.4306, "line": 3526}, {"token": "▁̧ÑģÑĤ", "score": -10.4358, "line": 3536}, {"token": "▁ëĬ", "score": -10.4363, "line": 3537}, {"token": "▁ĠSal", "score": -10.4372, "line": 3541}, {"token": "▁Ġdist", "score": -10.4404, "line": 3549}, {"token": "▁Ġcru", "score": -10.4414, "line": 3551}, {"token": "▁ĠTu", "score": -10.4417, "line": 3552}, {"token": "▁æĩ", "score": -10.4426, "line": 3554}, {"token": "▁åįĥ", "score": -10.4443, "line": 3557}, {"token": "▁ĠÄIJ", "score": -10.4468, "line": 3562}, {"token": "▁Ġlá", "score": -10.4536, "line": 3573}, {"token": "▁Ġgro", "score": -10.4547, "line": 3574}, {"token": "▁̧ĸçķĮ", "score": -10.4551, "line": 3584}, {"token": "▁Ġbá", "score": -10.4558, "line": 3599}, {"token": "▁íĺ", "score": -10.4562, "line": 3601}, {"token": "▁Ġdiff", "score": -10.4567, "line": 3606}, {"token": "▁ëģ", "score": -10.4582, "line": 3618}, {"token": "▁ĠìĹ", "score": -10.4583, "line": 3619}, {"token": "▁ëĥ", "score": -10.4592, "line": 3621}, {"token": "▁̧aäoo", "score": -10.4596, "line": 3622}, {"token": "▁Ġtele", "score": -10.4597, "line": 3626}, {"token": "▁Ġwor", "score": -10.4625, "line": 3639}, {"token": "▁åģľ", "score": -10.4634, "line": 3643}, {"token": "▁ath", "score": -10.4638, "line": 3644}, {"token": "▁Ġcáo", "score": -10.4647, "line": 3645}, {"token": "▁Ġmáo", "score": -10.4662, "line": 3648}, {"token": "▁ĠYo", "score": -10.467, "line": 3650}, {"token": "▁Ġngh", "score": -10.468, "line": 3653}, {"token": "▁íľ", "score": -10.469, "line": 3658}, {"token": "▁̈éĩı", "score": -10.47, "line": 3660}, {"token": "▁ĠPol", "score": -10.4718, "line": 3667}, {"token": "▁Ġmus", "score": -10.472, "line": 3669}, {"token": "▁ãĤĤãģ", "score": -10.4749, "line": 3676}, {"token": "▁arr", "score": -10.4759, "line": 3677}, {"token": "▁lar", "score": -10.4763, "line": 3679}, {"token": "▁ĠShi", "score": -10.4773, "line": 3681}, {"token": "▁bu", "score": -10.4789, "line": 3684}, {"token": "▁sub", "score": -10.4812, "line": 3686}, {"token": "▁ĠOp", "score": -10.4862, "line": 3698}, {"token": "▁̧ħæ", "score": -10.4923, "line": 3703}, {"token": "▁izz", "score": -10.4926, "line": 3704}, {"token": "▁ier", "score": -10.4927, "line": 3705}, {"token": "▁ÙħÙĪØ", "score": -10.4943, "line": 3707}, {"token": "▁Ġza", "score": -10.4945, "line": 3709}, {"token": "▁̧åħ", "score": -10.4972, "line": 3713}, {"token": "▁Ġhome", "score": -10.4992, "line": 3718}, {"token": "▁alle", "score": -10.5015, "line": 3722}, {"token": "▁Te", "score": -10.5041, "line": 3731}, {"token": "▁Ġsex", "score": -10.5046, "line": 3733}, {"token": "▁Ġsil", "score": -10.5059, "line": 3737}, {"token": "▁ëı", "score": -10.5082, "line": 3739}, {"token": "▁Ġsem", "score": -10.5101, "line": 3743}, {"token": "▁èĢģå", "score": -10.5101, "line": 3744}, {"token": "▁áĨ", "score": -10.5102, "line": 3745}, {"token": "▁Ġke", "score": -10.5106, "line": 3748}, {"token": "▁Ġcel", "score": -10.5116, "line": 3750}, {"token": "▁Ġreli", "score": -10.5142, "line": 3753}, {"token": "▁æĬĹ", "score": -10.5151, "line": 3754}, {"token": "▁eng", "score": -10.5177, "line": 3757}, {"token": "▁Ġsou", "score": -10.5202, "line": 3761}, {"token": "▁();", "score": -10.522, "line": 3764}, {"token": "▁ĠNg", "score": -10.5222, "line": 3767}, {"token": "▁Ġfran", "score": -10.5267, "line": 3784}, {"token": "▁rop", "score": -10.5276, "line": 3796}, {"token": "▁ìī", "score": -10.5294, "line": 3804}, {"token": "▁yl", "score": -10.5309, "line": 3813}, {"token": "▁AC", "score": -10.5322, "line": 3816}, {"token": "▁íı", "score": -10.5333, "line": 3819}, {"token": "▁ĠsÄ", "score": -10.5343, "line": 3822}, {"token": "▁Ġcháo", "score": -10.5359, "line": 3826}, {"token": "▁umb", "score": -10.5365, "line": 3828}, {"token": "▁̄ÙĬ", "score": -10.537, "line": 3829}, {"token": "▁Ġcla", "score": -10.5371, "line": 3830}, {"token": "▁Ġbáo", "score": -10.5373, "line": 3832}, {"token": "▁Ho", "score": -10.5374, "line": 3833}, {"token": "▁ìĮ", "score": -10.5384, "line": 3837}, {"token": "▁áĮ", "score": -10.5395, "line": 3840}, {"token": "▁ëĮ", "score": -10.5406, "line": 3843}, {"token": "▁æĭĽ", "score": -10.5414, "line": 3846}, {"token": "▁LO", "score": -10.542, "line": 3847}, {"token": "▁ðŁĨ", "score": -10.5426, "line": 3849}, {"token": "▁́Ñĥ", "score": -10.5439, "line": 3852}, {"token": "▁Ġstat", "score": -10.544, "line": 3853}, {"token": "▁avi", "score": -10.5448, "line": 3854}, {"token": "▁ym", "score": -10.5448, "line": 3855}, {"token": "▁Ġneg", "score": -10.5451, "line": 3856}, {"token": "▁̈ÙĬ", "score": -10.5503, "line": 3866}, {"token": "▁Ġgar", "score": -10.5535, "line": 3871}, {"token": "▁æĸĩåĮĸ", "score": -10.5538, "line": 3873}, {"token": "▁æľīæ", "score": -10.557, "line": 3877}, {"token": "▁ĠpÃ", "score": -10.5575, "line": 3879}, {"token": "▁Ġbre", "score": -10.5576, "line": 3881}, {"token": "▁æĬĬ", "score": -10.5657, "line": 3900}, {"token": "▁ĠBÃ", "score": -10.5668, "line": 3903}, {"token": "▁ĠĠĠĠĠĠĠĠ", "score": -10.5678, "line": 3904}, {"token": "▁ĠWar", "score": -10.5678, "line": 3905}, {"token": "▁Ġih", "score": -10.5679, "line": 3907}, {"token": "▁ari", "score": -10.5687, "line": 3908}, {"token": "▁ĠFran", "score": -10.5705, "line": 3912}, {"token": "▁ott", "score": -10.5724, "line": 3915}, {"token": "▁ich", "score": -10.5737, "line": 3916}, {"token": "▁Ġtre", "score": -10.5745, "line": 3919}, {"token": "▁æīį", "score": -10.5749, "line": 3922}, {"token": "▁ãģĻãģ", "score": -10.5767, "line": 3924}, {"token": "▁ĠAp", "score": -10.5786, "line": 3929}, {"token": "▁ata", "score": -10.581, "line": 3932}, {"token": "▁éļı", "score": -10.5866, "line": 3936}, {"token": "▁Ver", "score": -10.5883, "line": 3939}, {"token": "▁Ġcit", "score": -10.5887, "line": 3940}, {"token": "▁́ì", "score": -10.5888, "line": 3942}, {"token": "▁Inter", "score": -10.5902, "line": 3943}, {"token": "▁Ġgá", "score": -10.5909, "line": 3944}, {"token": "▁yp", "score": -10.5986, "line": 3957}, {"token": "▁èĩaç", "score": -10.5997, "line": 3960}, {"token": "▁̧ĭæ", "score": -10.6025, "line": 3963}, {"token": "▁ãĬ", "score": -10.6034, "line": 3968}, {"token": "▁ĠÑįÐ", "score": -10.6034, "line": 3969}, {"token": "▁ëŃ", "score": -10.6036, "line": 3982}, {"token": "▁ØaÙ", "score": -10.6044, "line": 3991}, {"token": "▁Ġconse", "score": -10.6058, "line": 4004}, {"token": "▁éĩįå", "score": -10.606, "line": 4007}, {"token": "▁åıijè", "score": -10.6062, "line": 4009}, {"token": "▁Ġcá", "score": -10.6068, "line": 4015}, {"token": "▁alt", "score": -10.608, "line": 4020}, {"token": "▁ãģłãģ", "score": -10.6091, "line": 4024}, {"token": "▁ew", "score": -10.6093, "line": 4026}, {"token": "▁íĭ", "score": -10.6114, "line": 4032}, {"token": "▁Ġsá", "score": -10.6118, "line": 4033}, {"token": "▁íķĺì", "score": -10.612, "line": 4034}, {"token": "▁Ġngá", "score": -10.6126, "line": 4037}, {"token": "▁Ġalt", "score": -10.6129, "line": 4040}, {"token": "▁ere", "score": -10.613, "line": 4041}, {"token": "▁Ġláo", "score": -10.614, "line": 4047}, {"token": "▁ĠĠĠĠ", "score": -10.6153, "line": 4050}, {"token": "▁ĠÄIJá", "score": -10.6161, "line": 4053}, {"token": "▁UN", "score": -10.6161, "line": 4055}, {"token": "▁̧ÑĢ", "score": -10.6181, "line": 4058}, {"token": "▁íĢ", "score": -10.6189, "line": 4059}, {"token": "▁ĠBal", "score": -10.6192, "line": 4060}, {"token": "▁̈re", "score": -10.6203, "line": 4063}, {"token": "▁̧Ĭæ", "score": -10.6205, "line": 4064}, {"token": "▁Ġcompar", "score": -10.6206, "line": 4065}, {"token": "▁ĠCre", "score": -10.625, "line": 4083}, {"token": "▁gre", "score": -10.6284, "line": 4088}, {"token": "▁åįı", "score": -10.63, "line": 4093}, {"token": "▁Ġbru", "score": -10.6346, "line": 4103}, {"token": "▁̈æĪ", "score": -10.6348, "line": 4104}, {"token": "▁ime", "score": -10.636, "line": 4106}, {"token": "▁ĠGÃ", "score": -10.637, "line": 4108}, {"token": "▁eli", "score": -10.639, "line": 4112}, {"token": "▁åıĮ", "score": -10.6406, "line": 4115}, {"token": "▁Ġein", "score": -10.6475, "line": 4128}, {"token": "▁ĠBen", "score": -10.6482, "line": 4129}, {"token": "▁ĠHan", "score": -10.6486, "line": 4132}, {"token": "▁ĠHÃ", "score": -10.6487, "line": 4133}, {"token": "▁Ġentr", "score": -10.657, "line": 4145}, {"token": "▁ĠMedi", "score": -10.6606, "line": 4149}, {"token": "▁̄ıä", "score": -10.6628, "line": 4153}, {"token": "▁Ġpay", "score": -10.6643, "line": 4159}, {"token": "▁æĢİ", "score": -10.6656, "line": 4160}, {"token": "▁}`", "score": -10.667, "line": 4165}, {"token": "▁Ġstu", "score": -10.6795, "line": 4188}, {"token": "▁]=", "score": -10.6805, "line": 4190}, {"token": "▁ĠTÃ", "score": -10.6811, "line": 4192}, {"token": "▁ìį", "score": -10.6832, "line": 4195}, {"token": "▁'])", "score": -10.6835, "line": 4197}, {"token": "▁Ġcontinu", "score": -10.6865, "line": 4201}, {"token": "▁ãĤŃãĥ", "score": -10.6867, "line": 4210}, {"token": "▁̧įæĸŃ", "score": -10.6869, "line": 4218}, {"token": "▁ĠÑĥÑģÐ", "score": -10.6873, "line": 4225}, {"token": "▁̧įåIJĮ", "score": -10.6873, "line": 4226}, {"token": "▁̈ĭåo", "score": -10.6875, "line": 4229}, {"token": "▁̧Ĭå", "score": -10.6875, "line": 4230}, {"token": "▁Ġviol", "score": -10.6879, "line": 4234}, {"token": "▁aj", "score": -10.6881, "line": 4236}, {"token": "▁ĠbaÅŁ", "score": -10.6882, "line": 4240}, {"token": "▁ĠÎ", "score": -10.6889, "line": 4242}, {"token": "▁ĠÐĴÐ", "score": -10.6889, "line": 4243}, {"token": "▁Ġgrand", "score": -10.689, "line": 4245}, {"token": "▁ĠÐļÐ", "score": -10.6891, "line": 4246}, {"token": "▁Cpp", "score": -10.6895, "line": 4254}, {"token": "▁Ġfilm", "score": -10.6909, "line": 4262}, {"token": "▁dr", "score": -10.6936, "line": 4277}, {"token": "▁]]", "score": -10.6938, "line": 4279}, {"token": "▁åĪĨå", "score": -10.6942, "line": 4280}, {"token": "▁ãĤĴè", "score": -10.6948, "line": 4284}, {"token": "▁Ġdzi", "score": -10.6953, "line": 4286}, {"token": "▁ãĤĴä", "score": -10.6956, "line": 4287}, {"token": "▁()))", "score": -10.6967, "line": 4288}, {"token": "▁Ġyap", "score": -10.6974, "line": 4291}, {"token": "▁Ġsin", "score": -10.6993, "line": 4295}, {"token": "▁̧Ģåı", "score": -10.7003, "line": 4299}, {"token": "▁Wi", "score": -10.7004, "line": 4300}, {"token": "▁çİĭ", "score": -10.7018, "line": 4306}, {"token": "▁åĢŁ", "score": -10.7037, "line": 4312}, {"token": "▁íĶ", "score": -10.7043, "line": 4316}, {"token": "▁Ġdan", "score": -10.7048, "line": 4317}, {"token": "▁̧Ñĩ", "score": -10.705, "line": 4318}, {"token": "▁Ġhab", "score": -10.705, "line": 4319}, {"token": "▁̧ÑĤÑĮ", "score": -10.7058, "line": 4321}, {"token": "▁ien", "score": -10.7164, "line": 4344}, {"token": "▁Gra", "score": -10.724, "line": 4353}, {"token": "▁æĻo", "score": -10.7242, "line": 4354}, {"token": "▁Ġinform", "score": -10.7283, "line": 4362}, {"token": "▁ĠVal", "score": -10.7283, "line": 4363}, {"token": "▁Ġsla", "score": -10.7292, "line": 4364}, {"token": "▁Ġsav", "score": -10.7322, "line": 4370}, {"token": "▁åĵa", "score": -10.734, "line": 4372}, {"token": "▁̧įåĪ", "score": -10.736, "line": 4377}, {"token": "▁'>", "score": -10.7361, "line": 4378}, {"token": "▁ru", "score": -10.74, "line": 4386}, {"token": "▁̈éĹ", "score": -10.7404, "line": 4387}, {"token": "▁adi", "score": -10.7414, "line": 4392}, {"token": "▁Ġgri", "score": -10.7447, "line": 4400}, {"token": "▁Ġqual", "score": -10.7455, "line": 4401}, {"token": "▁ĠDan", "score": -10.7455, "line": 4402}, {"token": "▁ĠSea", "score": -10.7469, "line": 4405}, {"token": "▁̈åĽ", "score": -10.7482, "line": 4407}, {"token": "▁oli", "score": -10.7513, "line": 4410}, {"token": "▁bra", "score": -10.7542, "line": 4417}, {"token": "▁çľĭä", "score": -10.7551, "line": 4422}, {"token": "▁qua", "score": -10.7557, "line": 4426}, {"token": "▁ãĤĭãģ", "score": -10.757, "line": 4429}, {"token": "▁ëĨ", "score": -10.7586, "line": 4431}, {"token": "▁Ġresid", "score": -10.7624, "line": 4438}, {"token": "▁Ġrap", "score": -10.7635, "line": 4440}, {"token": "▁ĠBan", "score": -10.7648, "line": 4442}, {"token": "▁̧ÑĨ", "score": -10.7655, "line": 4443}, {"token": "▁Ġrefer", "score": -10.7656, "line": 4444}, {"token": "▁Ġalle", "score": -10.7708, "line": 4451}, {"token": "▁Ġhur", "score": -10.7715, "line": 4454}, {"token": "▁Ġtrá", "score": -10.7728, "line": 4455}, {"token": "▁Ġsens", "score": -10.7735, "line": 4457}, {"token": "▁ĠkhÃ", "score": -10.7777, "line": 4488}, {"token": "▁åŁoç", "score": -10.7784, "line": 4507}, {"token": "▁Ġdá", "score": -10.7785, "line": 4508}, {"token": "▁ipp", "score": -10.7792, "line": 4520}, {"token": "▁ãģķãģ", "score": -10.78, "line": 4529}, {"token": "▁Ġcompet", "score": -10.7807, "line": 4534}, {"token": "▁Ġblo", "score": -10.7827, "line": 4550}, {"token": "▁Ġflu", "score": -10.7831, "line": 4558}, {"token": "▁ĠgiÃ", "score": -10.7833, "line": 4560}, {"token": "▁Ġaudi", "score": -10.7846, "line": 4565}, {"token": "▁íĤ", "score": -10.7853, "line": 4568}, {"token": "▁íĪ", "score": -10.7892, "line": 4586}, {"token": "▁ãĥĹãĥ", "score": -10.7902, "line": 4592}, {"token": "▁̈åĪĨ", "score": -10.7933, "line": 4604}, {"token": "▁Ġdar", "score": -10.7935, "line": 4605}, {"token": "▁ãģĭãģ", "score": -10.7948, "line": 4609}, {"token": "▁ĠMal", "score": -10.7955, "line": 4611}, {"token": "▁Bu", "score": -10.7968, "line": 4616}, {"token": "▁ÙĨÙ", "score": -10.7972, "line": 4618}, {"token": "▁Ġpur", "score": -10.7993, "line": 4622}, {"token": "▁Ġsimpl", "score": -10.8058, "line": 4636}, {"token": "▁åĩoç", "score": -10.8063, "line": 4637}, {"token": "▁ĠaÃ", "score": -10.8064, "line": 4639}, {"token": "▁Ġneu", "score": -10.807, "line": 4640}, {"token": "▁̈æĦı", "score": -10.8075, "line": 4642}, {"token": "▁ail", "score": -10.8086, "line": 4647}, {"token": "▁Ġìĺ", "score": -10.8102, "line": 4649}, {"token": "▁Ġclo", "score": -10.8103, "line": 4650}, {"token": "▁ĠDia", "score": -10.8117, "line": 4654}, {"token": "▁Ġpod", "score": -10.8162, "line": 4662}, {"token": "▁ĠSam", "score": -10.8198, "line": 4671}, {"token": "▁ank", "score": -10.8219, "line": 4677}, {"token": "▁æīĵå", "score": -10.8236, "line": 4680}, {"token": "▁ĠYu", "score": -10.8248, "line": 4684}, {"token": "▁orn", "score": -10.8271, "line": 4689}, {"token": "▁̄æĢ", "score": -10.8272, "line": 4690}, {"token": "▁Ġamb", "score": -10.8274, "line": 4691}, {"token": "▁äoĨå", "score": -10.8282, "line": 4694}, {"token": "▁åIJĮæ", "score": -10.8319, "line": 4698}, {"token": "▁atur", "score": -10.8319, "line": 4699}, {"token": "▁åĬŁ", "score": -10.8325, "line": 4701}, {"token": "▁̧ÑĨÐ", "score": -10.8329, "line": 4702}, {"token": "▁åĩı", "score": -10.8334, "line": 4703}, {"token": "▁çļĦé", "score": -10.8423, "line": 4713}, {"token": "▁ĠKar", "score": -10.843, "line": 4715}, {"token": "▁ond", "score": -10.8432, "line": 4716}, {"token": "▁Ġscar", "score": -10.8433, "line": 4718}, {"token": "▁Ġbro", "score": -10.8447, "line": 4720}, {"token": "▁Ġrid", "score": -10.8454, "line": 4722}, {"token": "▁mu", "score": -10.8461, "line": 4724}, {"token": "▁ĠLan", "score": -10.8461, "line": 4725}, {"token": "▁ort", "score": -10.8463, "line": 4726}, {"token": "▁anc", "score": -10.8463, "line": 4727}, {"token": "▁Ġshe", "score": -10.8485, "line": 4731}, {"token": "▁åĪĨæ", "score": -10.8553, "line": 4741}, {"token": "▁åħļå", "score": -10.8557, "line": 4743}, {"token": "▁pect", "score": -10.8562, "line": 4744}, {"token": "▁Ġregul", "score": -10.863, "line": 4752}, {"token": "▁leg", "score": -10.8642, "line": 4754}, {"token": "▁Vi", "score": -10.8648, "line": 4755}, {"token": "▁ĠCru", "score": -10.8707, "line": 4768}, {"token": "▁ome", "score": -10.8714, "line": 4771}, {"token": "▁Ġann", "score": -10.8726, "line": 4775}, {"token": "▁Ġná", "score": -10.874, "line": 4783}, {"token": "▁)))", "score": -10.8748, "line": 4786}, {"token": "▁ÑİÑīÐ", "score": -10.8776, "line": 4797}, {"token": "▁Ġparticip", "score": -10.8776, "line": 4799}, {"token": "▁Ġconsult", "score": -10.8776, "line": 4807}, {"token": "▁ãĥķãĤ", "score": -10.8776, "line": 4808}, {"token": "▁ãĥijãĥ", "score": -10.8776, "line": 4811}, {"token": "▁çoaå", "score": -10.8779, "line": 4820}, {"token": "▁ĠÑģÑĢÐ", "score": -10.878, "line": 4822}, {"token": "▁íļ", "score": -10.8781, "line": 4825}, {"token": "▁Ġcomput", "score": -10.8785, "line": 4830}, {"token": "▁Ġtravel", "score": -10.8787, "line": 4834}, {"token": "▁Ġutiliz", "score": -10.8787, "line": 4835}, {"token": "▁èĩaè", "score": -10.8788, "line": 4838}, {"token": "▁ycl", "score": -10.8789, "line": 4842}, {"token": "▁ĠArch", "score": -10.8809, "line": 4859}, {"token": "▁ĠFac", "score": -10.8815, "line": 4860}, {"token": "▁Ġnháo", "score": -10.8816, "line": 4862}, {"token": "▁':", "score": -10.8819, "line": 4864}, {"token": "▁Ġrespect", "score": -10.882, "line": 4866}, {"token": "▁Ġgiáo", "score": -10.8823, "line": 4868}, {"token": "▁Ġhat", "score": -10.8824, "line": 4869}, {"token": "▁Ġdomin", "score": -10.8824, "line": 4870}, {"token": "▁ĠÐĶÐ", "score": -10.883, "line": 4874}, {"token": "▁ĠSign", "score": -10.8833, "line": 4875}, {"token": "▁ĠAcc", "score": -10.8833, "line": 4876}, {"token": "▁Ġcamp", "score": -10.8835, "line": 4877}, {"token": "▁Ġobserv", "score": -10.8849, "line": 4883}, {"token": "▁Ġneces", "score": -10.8857, "line": 4887}, {"token": "▁ØŃÙ", "score": -10.8859, "line": 4889}, {"token": "▁Ġorganis", "score": -10.8866, "line": 4891}, {"token": "▁Ġmotiv", "score": -10.8878, "line": 4901}, {"token": "▁Ġvari", "score": -10.8939, "line": 4920}, {"token": "▁éļIJ", "score": -10.8944, "line": 4922}, {"token": "▁Ġcritic", "score": -10.8953, "line": 4924}, {"token": "▁Ġsáo", "score": -10.8958, "line": 4925}, {"token": "▁enc", "score": -10.8972, "line": 4927}, {"token": "▁ĠÅŁi", "score": -10.8974, "line": 4929}, {"token": "▁ĠĠĠĠĠĠĠĠĠĠ", "score": -10.898, "line": 4933}, {"token": "▁çŃīå", "score": -10.8992, "line": 4935}, {"token": "▁éĢĢ", "score": -10.9006, "line": 4940}, {"token": "▁̧īä", "score": -10.9036, "line": 4952}, {"token": "▁ĠMat", "score": -10.9063, "line": 4960}, {"token": "▁Ġquot", "score": -10.9069, "line": 4963}, {"token": "▁ĠMac", "score": -10.9082, "line": 4967}, {"token": "▁Ġsegu", "score": -10.909, "line": 4970}, {"token": "▁Ġcompa", "score": -10.9104, "line": 4975}, {"token": "▁ĠGri", "score": -10.9113, "line": 4978}, {"token": "▁istr", "score": -10.9172, "line": 4988}, {"token": "▁ĠLeg", "score": -10.9185, "line": 4991}, {"token": "▁Ġforg", "score": -10.9198, "line": 4993}, {"token": "▁Ġpromot", "score": -10.9206, "line": 4994}, {"token": "▁éĢĤ", "score": -10.9228, "line": 4998}, {"token": "▁́ÑĥÐ", "score": -10.9238, "line": 4999}, {"token": "▁Ġalter", "score": -10.9267, "line": 5003}, {"token": "▁ull", "score": -10.93, "line": 5009}, {"token": "▁ifi", "score": -10.9301, "line": 5010}, {"token": "▁Ġtend", "score": -10.9327, "line": 5018}, {"token": "▁ĠCra", "score": -10.936, "line": 5025}, {"token": "▁---", "score": -10.9402, "line": 5031}, {"token": "▁ĠShar", "score": -10.9418, "line": 5035}, {"token": "▁air", "score": -10.9425, "line": 5039}, {"token": "▁ĠCro", "score": -10.9434, "line": 5041}, {"token": "▁Ġgam", "score": -10.9465, "line": 5046}, {"token": "▁ĠOver", "score": -10.9484, "line": 5051}, {"token": "▁ounc", "score": -10.9554, "line": 5064}, {"token": "▁èĩaæ", "score": -10.959, "line": 5070}, {"token": "▁ĠBat", "score": -10.9591, "line": 5071}, {"token": "▁ĉgl", "score": -10.9621, "line": 5075}, {"token": "▁ĠArm", "score": -10.9647, "line": 5077}, {"token": "▁åĩoè", "score": -10.9649, "line": 5078}, {"token": "▁Ġprinc", "score": -10.9667, "line": 5082}, {"token": "▁'))", "score": -10.9672, "line": 5083}, {"token": "▁Ġkom", "score": -10.9672, "line": 5084}, {"token": "▁̧Ñİ", "score": -10.9684, "line": 5089}, {"token": "▁ĠCur", "score": -10.9687, "line": 5090}, {"token": "▁ãģĤãģ", "score": -10.9696, "line": 5092}, {"token": "▁ĠTro", "score": -10.9707, "line": 5095}, {"token": "▁itt", "score": -10.9713, "line": 5096}, {"token": "▁Ġmot", "score": -10.9718, "line": 5097}, {"token": "▁ĠFra", "score": -10.9748, "line": 5100}, {"token": "▁Ġrever", "score": -10.9769, "line": 5109}, {"token": "▁ĠZu", "score": -10.9785, "line": 5112}, {"token": "▁Ġimpe", "score": -10.9808, "line": 5115}, {"token": "▁Ġbull", "score": -10.986, "line": 5123}, {"token": "▁Ġanal", "score": -10.9865, "line": 5124}, {"token": "▁Ġdiscre", "score": -10.9878, "line": 5127}, {"token": "▁Ġinvest", "score": -10.9887, "line": 5129}, {"token": "▁Ġimprov", "score": -10.9887, "line": 5144}, {"token": "▁Ġinvestigat", "score": -10.9888, "line": 5157}, {"token": "▁Ġhorr", "score": -10.989, "line": 5169}, {"token": "▁ĠAssembly", "score": -10.989, "line": 5170}, {"token": "▁Ġimagin", "score": -10.9892, "line": 5178}, {"token": "▁Ġprac", "score": -10.9892, "line": 5180}, {"token": "▁̧aæľĪ", "score": -10.9893, "line": 5186}, {"token": "▁ĠLiber", "score": -10.99, "line": 5198}, {"token": "▁åIJĪä", "score": -10.9907, "line": 5209}, {"token": "▁Ġrealiz", "score": -10.9909, "line": 5211}, {"token": "▁Ġweak", "score": -10.9911, "line": 5216}, {"token": "▁ÐŁÐ", "score": -10.9914, "line": 5220}, {"token": "▁Ġrepresent", "score": -10.9918, "line": 5226}, {"token": "▁Sign", "score": -10.9918, "line": 5227}, {"token": "▁æĺİç", "score": -10.992, "line": 5228}, {"token": "▁Ġmoder", "score": -10.9921, "line": 5229}, {"token": "▁Ġnomin", "score": -10.9927, "line": 5235}, {"token": "▁ØaÙĪØ", "score": -10.9929, "line": 5238}, {"token": "▁ĠLÃ", "score": -10.9932, "line": 5241}, {"token": "▁ĠÐIJÐ", "score": -10.9938, "line": 5244}, {"token": "▁Ġpháo", "score": -10.9941, "line": 5246}, {"token": "▁åŁoæ", "score": -10.9954, "line": 5253}, {"token": "▁Ġharm", "score": -10.9962, "line": 5259}, {"token": "▁erg", "score": -10.9963, "line": 5260}, {"token": "▁ĉRT", "score": -10.9973, "line": 5264}, {"token": "▁ĠHor", "score": -10.9981, "line": 5271}, {"token": "▁Ġesta", "score": -10.9992, "line": 5276}, {"token": "▁Ġká", "score": -10.9998, "line": 5283}, {"token": "▁íį", "score": -11.0002, "line": 5289}, {"token": "▁Ġalleg", "score": -11.0011, "line": 5292}, {"token": "▁uni", "score": -11.0021, "line": 5294}, {"token": "▁ãģĻãĤĭãģ", "score": -11.0024, "line": 5298}, {"token": "▁ĠTHE", "score": -11.0057, "line": 5304}, {"token": "▁ĠArt", "score": -11.0059, "line": 5307}, {"token": "▁ĠCap", "score": -11.0063, "line": 5309}, {"token": "▁çļĩ", "score": -11.0101, "line": 5319}, {"token": "▁æīĢå", "score": -11.0101, "line": 5321}, {"token": "▁Ġbarr", "score": -11.0105, "line": 5322}, {"token": "▁ĠBul", "score": -11.0106, "line": 5323}, {"token": "▁æľīè", "score": -11.0118, "line": 5326}, {"token": "▁Ġvac", "score": -11.0118, "line": 5328}, {"token": "▁Ġthu", "score": -11.0124, "line": 5332}, {"token": "▁Ġcorp", "score": -11.0163, "line": 5341}, {"token": "▁̧ľè", "score": -11.0177, "line": 5346}, {"token": "▁ÙĩÙ", "score": -11.0184, "line": 5348}, {"token": "▁Ġmol", "score": -11.0202, "line": 5353}, {"token": "▁ĠMad", "score": -11.021, "line": 5357}, {"token": "▁ugg", "score": -11.0213, "line": 5358}, {"token": "▁]))", "score": -11.0276, "line": 5370}, {"token": "▁Ġtiá", "score": -11.0278, "line": 5371}, {"token": "▁Ġadapt", "score": -11.0279, "line": 5372}, {"token": "▁Ġadvis", "score": -11.0307, "line": 5376}, {"token": "▁abb", "score": -11.0319, "line": 5380}, {"token": "▁æĪIJç", "score": -11.0321, "line": 5381}, {"token": "▁\"])", "score": -11.0358, "line": 5387}, {"token": "▁̧ĵä", "score": -11.0368, "line": 5389}, {"token": "▁Ġfur", "score": -11.0384, "line": 5391}, {"token": "▁ĠGlo", "score": -11.0408, "line": 5393}, {"token": "▁gra", "score": -11.0424, "line": 5395}, {"token": "▁Ġgrav", "score": -11.0433, "line": 5400}, {"token": "▁ĠCri", "score": -11.0458, "line": 5403}, {"token": "▁ÙĥÙ", "score": -11.0468, "line": 5405}, {"token": "▁ĠThá", "score": -11.0474, "line": 5406}, {"token": "▁Ġsumm", "score": -11.0494, "line": 5408}, {"token": "▁åıijæ", "score": -11.0499, "line": 5411}, {"token": "▁oph", "score": -11.0563, "line": 5420}, {"token": "▁Ġappr", "score": -11.0567, "line": 5421}, {"token": "▁Ġrá", "score": -11.0571, "line": 5422}, {"token": "▁ãĥIJãĥ", "score": -11.0584, "line": 5424}, {"token": "▁éĩįç", "score": -11.0591, "line": 5425}, {"token": "▁duc", "score": -11.0598, "line": 5429}, {"token": "▁çaģå", "score": -11.0647, "line": 5435}, {"token": "▁Ġchu", "score": -11.0663, "line": 5437}, {"token": "▁ange", "score": -11.0696, "line": 5440}, {"token": "▁Ġstor", "score": -11.07, "line": 5441}, {"token": "▁ump", "score": -11.0721, "line": 5444}, {"token": "▁ÙĤÙ", "score": -11.0748, "line": 5448}, {"token": "▁imit", "score": -11.076, "line": 5449}, {"token": "▁amm", "score": -11.0804, "line": 5453}, {"token": "▁ĠUr", "score": -11.0836, "line": 5456}, {"token": "▁Ġíĺ", "score": -11.0846, "line": 5457}, {"token": "▁mar", "score": -11.0879, "line": 5461}, {"token": "▁æĦıå", "score": -11.0919, "line": 5466}, {"token": "▁ĠDam", "score": -11.0928, "line": 5468}, {"token": "▁åģļå", "score": -11.0968, "line": 5473}, {"token": "▁æĮĩå", "score": -11.0986, "line": 5476}, {"token": "▁******", "score": -11.0986, "line": 5477}, {"token": "▁ĠHá", "score": -11.1039, "line": 5488}, {"token": "▁ãģĮãģ", "score": -11.1056, "line": 5492}, {"token": "▁ark", "score": -11.1058, "line": 5493}, {"token": "▁xf", "score": -11.1096, "line": 5497}, {"token": "▁Ġeg", "score": -11.1116, "line": 5500}, {"token": "▁Ġdispos", "score": -11.1116, "line": 5501}, {"token": "▁åıijç", "score": -11.1117, "line": 5502}, {"token": "▁ĠĠĠĠĠĠ", "score": -11.1123, "line": 5503}, {"token": "▁Tri", "score": -11.1129, "line": 5504}, {"token": "▁ĠHigh", "score": -11.1129, "line": 5505}, {"token": "▁ĠSol", "score": -11.113, "line": 5506}, {"token": "▁Ġrac", "score": -11.1137, "line": 5509}, {"token": "▁)}", "score": -11.1137, "line": 5510}, {"token": "▁Ġgratuit", "score": -11.1137, "line": 5517}, {"token": "▁Ġdetermin", "score": -11.1137, "line": 5528}, {"token": "▁̧ĮæľĽ", "score": -11.1137, "line": 5531}, {"token": "▁Ġsurviv", "score": -11.1137, "line": 5541}, {"token": "▁Ġkullan", "score": -11.1138, "line": 5546}, {"token": "▁Ġassist", "score": -11.1139, "line": 5559}, {"token": "▁ãĤaãĥ", "score": -11.114, "line": 5566}, {"token": "▁vid", "score": -11.1141, "line": 5577}, {"token": "▁Ġattend", "score": -11.1142, "line": 5584}, {"token": "▁Ġprogramm", "score": -11.1144, "line": 5588}, {"token": "▁ĠyaÅŁ", "score": -11.1144, "line": 5589}, {"token": "▁Ġadult", "score": -11.1145, "line": 5597}, {"token": "▁Ġformula", "score": -11.1146, "line": 5599}, {"token": "▁Ġtheor", "score": -11.1148, "line": 5603}, {"token": "▁pons", "score": -11.1149, "line": 5607}, {"token": "▁omb", "score": -11.115, "line": 5610}, {"token": "▁Ġdefend", "score": -11.115, "line": 5611}, {"token": "▁Ġhaz", "score": -11.1152, "line": 5613}, {"token": "▁æĦıè", "score": -11.1152, "line": 5614}, {"token": "▁Ġbomb", "score": -11.1153, "line": 5616}, {"token": "▁Ġfinanc", "score": -11.1153, "line": 5618}, {"token": "▁Ġscrap", "score": -11.1154, "line": 5620}, {"token": "▁ãģĵãĤĮ", "score": -11.1155, "line": 5623}, {"token": "▁ãĤĦãģ", "score": -11.1158, "line": 5626}, {"token": "▁èģĶç", "score": -11.1158, "line": 5627}, {"token": "▁çIJĨè", "score": -11.1158, "line": 5630}, {"token": "▁éĩijè", "score": -11.1159, "line": 5631}, {"token": "▁ĠJuli", "score": -11.1161, "line": 5636}, {"token": "▁\"/>", "score": -11.1168, "line": 5643}, {"token": "▁'],", "score": -11.1169, "line": 5646}, {"token": "▁Ġpropos", "score": -11.117, "line": 5648}, {"token": "▁ĠquÃ", "score": -11.118, "line": 5657}, {"token": "▁Ġhair", "score": -11.1185, "line": 5662}, {"token": "▁Ġattract", "score": -11.1185, "line": 5664}, {"token": "▁æīĢè", "score": -11.1193, "line": 5669}, {"token": "▁ðIJĮ", "score": -11.1195, "line": 5671}, {"token": "▁Ġemerg", "score": -11.12, "line": 5677}, {"token": "▁̧oäoĨ", "score": -11.1204, "line": 5681}, {"token": "▁ĠIndi", "score": -11.1223, "line": 5689}, {"token": "▁Ġej", "score": -11.123, "line": 5694}, {"token": "▁entr", "score": -11.1242, "line": 5703}, {"token": "▁ĠTele", "score": -11.1243, "line": 5704}, {"token": "▁ĠNav", "score": -11.1243, "line": 5705}, {"token": "▁Ġnáo", "score": -11.1244, "line": 5706}, {"token": "▁Ġpopul", "score": -11.1247, "line": 5707}, {"token": "▁Ġtráo", "score": -11.1247, "line": 5708}, {"token": "▁quir", "score": -11.1251, "line": 5709}, {"token": "▁ĠcrÃ", "score": -11.1256, "line": 5713}, {"token": "▁']]", "score": -11.1261, "line": 5714}, {"token": "▁íĴ", "score": -11.1277, "line": 5719}, {"token": "▁Ġmerc", "score": -11.1283, "line": 5722}, {"token": "▁Ġdáo", "score": -11.1285, "line": 5723}, {"token": "▁æĦŁå", "score": -11.1295, "line": 5726}, {"token": "▁Ġmagn", "score": -11.13, "line": 5729}, {"token": "▁Ġcommun", "score": -11.1311, "line": 5732}, {"token": "▁Ġneuro", "score": -11.133, "line": 5740}, {"token": "▁Ġrob", "score": -11.1341, "line": 5742}, {"token": "▁Ġtreat", "score": -11.1343, "line": 5745}, {"token": "▁Ġexecut", "score": -11.1352, "line": 5748}, {"token": "▁æīĵæ", "score": -11.1373, "line": 5756}, {"token": "▁Ġedit", "score": -11.1374, "line": 5757}, {"token": "▁Ġeduca", "score": -11.1379, "line": 5758}, {"token": "▁stÃ", "score": -11.138, "line": 5759}, {"token": "▁ĠVan", "score": -11.1384, "line": 5763}, {"token": "▁åİĨå", "score": -11.1416, "line": 5766}, {"token": "▁Ġsay", "score": -11.1426, "line": 5769}, {"token": "▁Ġhyp", "score": -11.1431, "line": 5771}, {"token": "▁Ġpsycho", "score": -11.1442, "line": 5777}, {"token": "▁Ġeas", "score": -11.1449, "line": 5780}, {"token": "▁Ġleg", "score": -11.1458, "line": 5782}, {"token": "▁æľoå", "score": -11.1461, "line": 5784}, {"token": "▁Ġpast", "score": -11.1468, "line": 5785}, {"token": "▁ĉĉĠĠĠĠ", "score": -11.148, "line": 5787}, {"token": "▁olv", "score": -11.1481, "line": 5788}, {"token": "▁çĶŁç", "score": -11.1511, "line": 5792}, {"token": "▁Ġcav", "score": -11.1532, "line": 5797}, {"token": "▁ØaÙĪ", "score": -11.1584, "line": 5800}, {"token": "▁Ġshr", "score": -11.161, "line": 5804}, {"token": "▁Ġlive", "score": -11.1664, "line": 5811}, {"token": "▁Ġparti", "score": -11.1697, "line": 5821}, {"token": "▁æĪijè", "score": -11.1742, "line": 5823}, {"token": "▁Ju", "score": -11.1748, "line": 5825}, {"token": "▁sha", "score": -11.175, "line": 5826}, {"token": "▁asur", "score": -11.175, "line": 5827}, {"token": "▁åoŁ", "score": -11.1756, "line": 5828}, {"token": "▁ibr", "score": -11.1766, "line": 5830}, {"token": "▁Ġbor", "score": -11.1768, "line": 5831}, {"token": "▁Ġservi", "score": -11.1772, "line": 5832}, {"token": "▁Ġretr", "score": -11.1779, "line": 5833}, {"token": "▁̄ĨåĪ", "score": -11.1781, "line": 5835}, {"token": "▁][", "score": -11.18, "line": 5839}, {"token": "▁Ġobe", "score": -11.1852, "line": 5848}, {"token": "▁ĠĠĠĠĠĠĠ", "score": -11.1862, "line": 5849}, {"token": "▁Ġvet", "score": -11.1883, "line": 5852}, {"token": "▁__(", "score": -11.1911, "line": 5853}, {"token": "▁Ġorganiz", "score": -11.1953, "line": 5857}, {"token": "▁Fac", "score": -11.196, "line": 5858}, {"token": "▁Ġcirc", "score": -11.2007, "line": 5862}, {"token": "▁̧ĭåį", "score": -11.2017, "line": 5864}, {"token": "▁ask", "score": -11.202, "line": 5865}, {"token": "▁åģı", "score": -11.2096, "line": 5874}, {"token": "▁CHA", "score": -11.2103, "line": 5875}, {"token": "▁Ġfee", "score": -11.2123, "line": 5877}, {"token": "▁Ġyay", "score": -11.2132, "line": 5879}, {"token": "▁Ġposi", "score": -11.2157, "line": 5882}, {"token": "▁ĉĉĉĉĉĉĉĉĉ", "score": -11.2157, "line": 5883}, {"token": "▁Ġcompos", "score": -11.2232, "line": 5886}, {"token": "▁opp", "score": -11.23, "line": 5899}, {"token": "▁Ġstand", "score": -11.2321, "line": 5906}, {"token": "▁Ġslu", "score": -11.2355, "line": 5909}, {"token": "▁ĠChi", "score": -11.2359, "line": 5910}, {"token": "▁ĠYe", "score": -11.2362, "line": 5912}, {"token": "▁̧ĵé", "score": -11.2365, "line": 5914}, {"token": "▁Ġindicat", "score": -11.2386, "line": 5920}, {"token": "▁̈ãģĮãģ", "score": -11.2388, "line": 5921}, {"token": "▁===", "score": -11.2422, "line": 5928}, {"token": "▁Ġfam", "score": -11.2447, "line": 5934}, {"token": "▁Ġnhi", "score": -11.245, "line": 5936}, {"token": "▁Ġgovern", "score": -11.2459, "line": 5938}, {"token": "▁erne", "score": -11.2465, "line": 5941}, {"token": "▁Ġexpos", "score": -11.2493, "line": 5943}, {"token": "▁Ġgiá", "score": -11.2499, "line": 5945}, {"token": "▁Ġphá", "score": -11.25, "line": 5946}, {"token": "▁ĠInvest", "score": -11.2516, "line": 5950}, {"token": "▁åĩoä", "score": -11.2529, "line": 5952}, {"token": "▁åĩoæ", "score": -11.2531, "line": 5953}, {"token": "▁Ġemploy", "score": -11.2543, "line": 5959}, {"token": "▁Ġdesp", "score": -11.2558, "line": 5960}, {"token": "▁Ġaccus", "score": -11.256, "line": 5961}, {"token": "▁Ġcollaborat", "score": -11.2566, "line": 5963}, {"token": "▁Ġdemonstrat", "score": -11.2566, "line": 5973}, {"token": "▁Ġillustrat", "score": -11.2566, "line": 5974}, {"token": "▁Ġprosecut", "score": -11.2566, "line": 5976}, {"token": "▁Ġrecruit", "score": -11.2566, "line": 5977}, {"token": "▁Ġenthus", "score": -11.2566, "line": 5981}, {"token": "▁Ġextrem", "score": -11.2566, "line": 5984}, {"token": "▁Ġmurder", "score": -11.2566, "line": 5986}, {"token": "▁ĠsaÄŁl", "score": -11.2566, "line": 5991}, {"token": "▁ãģķãĤĮãģ", "score": -11.2566, "line": 5995}, {"token": "▁Ġbeliev", "score": -11.2566, "line": 6004}, {"token": "▁ãĥģãĥ", "score": -11.2566, "line": 6007}, {"token": "▁Ġlibert", "score": -11.2566, "line": 6020}, {"token": "▁Ġinitiat", "score": -11.2567, "line": 6030}, {"token": "▁Ġprofession", "score": -11.2567, "line": 6031}, {"token": "▁Ġsubsid", "score": -11.2568, "line": 6039}, {"token": "▁cript", "score": -11.2568, "line": 6040}, {"token": "▁̧ëŁ", "score": -11.2568, "line": 6044}, {"token": "▁Ġassum", "score": -11.2569, "line": 6052}, {"token": "▁ÙĤÙĪØ", "score": -11.2569, "line": 6058}, {"token": "▁ĠSuper", "score": -11.257, "line": 6066}, {"token": "▁Ġsitu", "score": -11.257, "line": 6067}, {"token": "▁Ġequip", "score": -11.257, "line": 6068}, {"token": "▁ØaÙĨØ", "score": -11.257, "line": 6069}, {"token": "▁Ġphotograph", "score": -11.2571, "line": 6075}, {"token": "▁èĩaä", "score": -11.2574, "line": 6082}, {"token": "▁åĪĽä", "score": -11.2574, "line": 6084}, {"token": "▁Ġchamp", "score": -11.2578, "line": 6090}, {"token": "▁Ġinspir", "score": -11.2579, "line": 6095}, {"token": "▁æĦŁè", "score": -11.2579, "line": 6096}, {"token": "▁æİĴæ", "score": -11.258, "line": 6100}, {"token": "▁Ġevery", "score": -11.2582, "line": 6105}, {"token": "▁]+", "score": -11.2583, "line": 6109}, {"token": "▁Ġhiá", "score": -11.2588, "line": 6119}, {"token": "▁æľĪä", "score": -11.2588, "line": 6120}, {"token": "▁Ġprotest", "score": -11.2589, "line": 6121}, {"token": "▁Ġprze", "score": -11.2592, "line": 6131}, {"token": "▁ĠthÆ", "score": -11.2595, "line": 6137}, {"token": "▁=\\\"", "score": -11.2596, "line": 6138}, {"token": "▁Ġconten", "score": -11.2596, "line": 6139}, {"token": "▁Ġexplor", "score": -11.2598, "line": 6141}, {"token": "▁Ġcool", "score": -11.2598, "line": 6142}, {"token": "▁ĠBru", "score": -11.2599, "line": 6144}, {"token": "▁Ġarriv", "score": -11.2599, "line": 6145}, {"token": "▁Ġshoot", "score": -11.2602, "line": 6149}, {"token": "▁\\\">", "score": -11.2603, "line": 6152}, {"token": "▁)?", "score": -11.2604, "line": 6153}, {"token": "▁Ġreflect", "score": -11.2607, "line": 6158}, {"token": "▁æīĢä", "score": -11.2608, "line": 6159}, {"token": "▁ĠÙĪÙĩ", "score": -11.261, "line": 6163}, {"token": "▁ÐĽÐ", "score": -11.2611, "line": 6166}, {"token": "▁grÃ", "score": -11.2614, "line": 6169}, {"token": "▁Nav", "score": -11.2618, "line": 6171}, {"token": "▁].", "score": -11.2624, "line": 6173}, {"token": "▁äoĨè", "score": -11.2626, "line": 6175}, {"token": "▁ĠInstitut", "score": -11.2628, "line": 6177}, {"token": "▁Ġsurg", "score": -11.2638, "line": 6184}, {"token": "▁ANG", "score": -11.2645, "line": 6190}, {"token": "▁Ġnest", "score": -11.2649, "line": 6192}, {"token": "▁Ġthere", "score": -11.2655, "line": 6198}, {"token": "▁åoĶæ", "score": -11.2696, "line": 6211}, {"token": "▁Ġimpos", "score": -11.2708, "line": 6222}, {"token": "▁Ġcalculat", "score": -11.271, "line": 6223}, {"token": "▁åıaè", "score": -11.2713, "line": 6225}, {"token": "▁ĠPlat", "score": -11.2722, "line": 6228}, {"token": "▁åĨħé", "score": -11.2725, "line": 6230}, {"token": "▁ĠUnivers", "score": -11.2731, "line": 6233}, {"token": "▁Ġbow", "score": -11.2757, "line": 6244}, {"token": "▁Ġtour", "score": -11.2764, "line": 6245}, {"token": "▁__)", "score": -11.281, "line": 6259}, {"token": "▁̈Ģãģ", "score": -11.2822, "line": 6263}, {"token": "▁Ġdivid", "score": -11.2826, "line": 6264}, {"token": "▁Ġeconom", "score": -11.286, "line": 6279}, {"token": "▁prec", "score": -11.2863, "line": 6280}, {"token": "▁Ġsettl", "score": -11.2872, "line": 6286}, {"token": "▁tw", "score": -11.2894, "line": 6290}, {"token": "▁Mark", "score": -11.2916, "line": 6294}, {"token": "▁Ġsenti", "score": -11.2919, "line": 6295}, {"token": "▁etr", "score": -11.2982, "line": 6303}, {"token": "▁äoĭå", "score": -11.2989, "line": 6305}, {"token": "▁ãģĬå", "score": -11.2993, "line": 6307}, {"token": "▁åĪĽæ", "score": -11.3018, "line": 6309}, {"token": "▁Ġsuit", "score": -11.3053, "line": 6316}, {"token": "▁Ġensu", "score": -11.3092, "line": 6320}, {"token": "▁Ġtap", "score": -11.3105, "line": 6322}, {"token": "▁sembl", "score": -11.3105, "line": 6323}, {"token": "▁Ġprepar", "score": -11.311, "line": 6324}, {"token": "▁Ġevaluat", "score": -11.3121, "line": 6326}, {"token": "▁Ġcomplet", "score": -11.3131, "line": 6329}, {"token": "▁xF", "score": -11.3143, "line": 6331}, {"token": "▁Ġurg", "score": -11.3184, "line": 6335}, {"token": "▁Ġrh", "score": -11.3225, "line": 6339}, {"token": "▁Ġdivers", "score": -11.325, "line": 6341}, {"token": "▁Ġlate", "score": -11.3255, "line": 6342}, {"token": "▁ĠLaur", "score": -11.3309, "line": 6347}, {"token": "▁ologi", "score": -11.3312, "line": 6349}, {"token": "▁Ġplat", "score": -11.3349, "line": 6355}, {"token": "▁Ġslow", "score": -11.3434, "line": 6363}, {"token": "▁amin", "score": -11.3452, "line": 6365}, {"token": "▁']))", "score": -11.3498, "line": 6371}, {"token": "▁æĹłç", "score": -11.3525, "line": 6374}, {"token": "▁Ġheat", "score": -11.3529, "line": 6375}, {"token": "▁Ġaqu", "score": -11.3564, "line": 6377}, {"token": "▁Ġirre", "score": -11.364, "line": 6384}, {"token": "▁ĠScot", "score": -11.3658, "line": 6386}, {"token": "▁Ġoccur", "score": -11.3703, "line": 6390}, {"token": "▁ĠĠĠĠĉ", "score": -11.3757, "line": 6395}, {"token": "▁ĠOff", "score": -11.3812, "line": 6400}, {"token": "▁Ġoppos", "score": -11.3878, "line": 6404}, {"token": "▁Ġcondo", "score": -11.3879, "line": 6405}, {"token": "▁Ġlov", "score": -11.3912, "line": 6409}, {"token": "▁èĢĮå", "score": -11.3924, "line": 6410}, {"token": "▁Ġsuggest", "score": -11.3929, "line": 6411}, {"token": "▁ĉĉĉĠĠĠĠ", "score": -11.3936, "line": 6414}, {"token": "▁çĽijç", "score": -11.398, "line": 6420}, {"token": "▁Ġconstitu", "score": -11.4021, "line": 6425}, {"token": "▁Ġconven", "score": -11.4033, "line": 6428}, {"token": "▁Ġthro", "score": -11.4056, "line": 6430}, {"token": "▁Ġdeclar", "score": -11.4091, "line": 6434}, {"token": "▁ãģĮå", "score": -11.4098, "line": 6435}, {"token": "▁Ġannounc", "score": -11.4116, "line": 6438}, {"token": "▁Ġachiev", "score": -11.4123, "line": 6440}, {"token": "▁Ġsanit", "score": -11.4145, "line": 6443}, {"token": "▁ĠProvid", "score": -11.4155, "line": 6445}, {"token": "▁Ġappar", "score": -11.4156, "line": 6446}, {"token": "▁Ġdevelop", "score": -11.416, "line": 6447}, {"token": "▁Ġnhá", "score": -11.4167, "line": 6449}, {"token": "▁Ġexpress", "score": -11.4169, "line": 6451}, {"token": "▁Ġadvertis", "score": -11.4198, "line": 6457}, {"token": "▁çľĭå", "score": -11.4219, "line": 6462}, {"token": "▁Ġperform", "score": -11.4223, "line": 6465}, {"token": "▁Ġenjoy", "score": -11.4232, "line": 6471}, {"token": "▁Ġexhibit", "score": -11.4232, "line": 6472}, {"token": "▁Ġsupplement", "score": -11.4232, "line": 6484}, {"token": "▁Ġoblig", "score": -11.4232, "line": 6490}, {"token": "▁Ġdiplom", "score": -11.4232, "line": 6491}, {"token": "▁Ġadvoc", "score": -11.4232, "line": 6492}, {"token": "▁Ġdisrupt", "score": -11.4232, "line": 6493}, {"token": "▁Ġpunish", "score": -11.4232, "line": 6501}, {"token": "▁Ġrecommend", "score": -11.4232, "line": 6502}, {"token": "▁ãģĦãģŁãģłãģ", "score": -11.4232, "line": 6506}, {"token": "▁ãĤĤãĤīãģ", "score": -11.4232, "line": 6507}, {"token": "▁Ġhydr", "score": -11.4232, "line": 6510}, {"token": "▁ĠGovern", "score": -11.4233, "line": 6514}, {"token": "▁Ġwonder", "score": -11.4233, "line": 6517}, {"token": "▁ãģaãĤĵãģ", "score": -11.4233, "line": 6528}, {"token": "▁ĠEvery", "score": -11.4233, "line": 6529}, {"token": "▁ĉĉĠ", "score": -11.4233, "line": 6531}, {"token": "▁ãĥijãĤ", "score": -11.4233, "line": 6534}, {"token": "▁ĠëĮĢí", "score": -11.4233, "line": 6535}, {"token": "▁Ġlesbi", "score": -11.4233, "line": 6537}, {"token": "▁Ġendors", "score": -11.4233, "line": 6540}, {"token": "▁Ġperpet", "score": -11.4233, "line": 6543}, {"token": "▁Ġbreath", "score": -11.4234, "line": 6553}, {"token": "▁çĶŁæĢģ", "score": -11.4234, "line": 6554}, {"token": "▁Ġhonor", "score": -11.4234, "line": 6555}, {"token": "▁Ġacknowledg", "score": -11.4234, "line": 6560}, {"token": "▁Ġintroduc", "score": -11.4234, "line": 6566}, {"token": "▁ãĥaãĥ", "score": -11.4235, "line": 6576}, {"token": "▁Ġagree", "score": -11.4235, "line": 6577}, {"token": "▁çīĪæ", "score": -11.4236, "line": 6582}, {"token": "▁Ġenumer", "score": -11.4236, "line": 6586}, {"token": "▁Ġmanifest", "score": -11.4237, "line": 6587}, {"token": "▁Ġswing", "score": -11.4237, "line": 6589}, {"token": "▁ĉĠĠ", "score": -11.4237, "line": 6592}, {"token": "▁Ġpractic", "score": -11.4237, "line": 6593}, {"token": "▁Ġapolog", "score": -11.4237, "line": 6596}, {"token": "▁Ġsail", "score": -11.4238, "line": 6599}, {"token": "▁Ġreplic", "score": -11.4238, "line": 6601}, {"token": "▁Ġremind", "score": -11.4238, "line": 6603}, {"token": "▁Ġthank", "score": -11.4239, "line": 6609}, {"token": "▁ĠdeÄŁi", "score": -11.4239, "line": 6610}, {"token": "▁Ġmoment", "score": -11.4239, "line": 6614}, {"token": "▁ĠDevelop", "score": -11.424, "line": 6615}, {"token": "▁Ġfresh", "score": -11.424, "line": 6616}, {"token": "▁Ġfear", "score": -11.424, "line": 6617}, {"token": "▁Ġíģ", "score": -11.424, "line": 6618}, {"token": "▁Ġpleas", "score": -11.4241, "line": 6622}, {"token": "▁×¤×¡×", "score": -11.4241, "line": 6625}, {"token": "▁Ġrail", "score": -11.4242, "line": 6632}, {"token": "▁Ġcrack", "score": -11.4242, "line": 6633}, {"token": "▁ãĥķãĥ", "score": -11.4243, "line": 6636}, {"token": "▁ipher", "score": -11.4243, "line": 6637}, {"token": "▁;\">", "score": -11.4244, "line": 6638}, {"token": "▁Ġcomplain", "score": -11.4245, "line": 6646}, {"token": "▁Ġresist", "score": -11.4245, "line": 6647}, {"token": "▁Ġpursu", "score": -11.4245, "line": 6648}, {"token": "▁Ġpolitic", "score": -11.4246, "line": 6650}, {"token": "▁Ġdiscover", "score": -11.4246, "line": 6652}, {"token": "▁'=>", "score": -11.4246, "line": 6656}, {"token": "▁Ġthiá", "score": -11.4247, "line": 6658}, {"token": "▁%;", "score": -11.4249, "line": 6662}, {"token": "▁Ġspark", "score": -11.4252, "line": 6667}, {"token": "▁ĠJenn", "score": -11.4253, "line": 6669}, {"token": "▁åīįä", "score": -11.4255, "line": 6673}, {"token": "▁Ġburn", "score": -11.4256, "line": 6674}, {"token": "▁Ġmog", "score": -11.4257, "line": 6680}, {"token": "▁Ġchrom", "score": -11.4257, "line": 6681}, {"token": "▁Ġconserv", "score": -11.4258, "line": 6684}, {"token": "▁Ġpitch", "score": -11.426, "line": 6686}, {"token": "▁Ġnear", "score": -11.426, "line": 6687}, {"token": "▁ĠFlor", "score": -11.4262, "line": 6690}, {"token": "▁Ġembod", "score": -11.4264, "line": 6692}, {"token": "▁ĠYÃ", "score": -11.4265, "line": 6696}, {"token": "▁Ġinterven", "score": -11.4267, "line": 6697}, {"token": "▁Over", "score": -11.4267, "line": 6698}, {"token": "▁Ġhunt", "score": -11.4273, "line": 6702}, {"token": "▁Ġkiá", "score": -11.4274, "line": 6703}, {"token": "▁Ġkick", "score": -11.4275, "line": 6704}, {"token": "▁ĠElli", "score": -11.4277, "line": 6706}, {"token": "▁Ġspecific", "score": -11.4278, "line": 6708}, {"token": "▁ĠHunt", "score": -11.4279, "line": 6710}, {"token": "▁ĠnhÃ", "score": -11.428, "line": 6712}, {"token": "▁Ġretir", "score": -11.4281, "line": 6713}, {"token": "▁Ġhack", "score": -11.4282, "line": 6714}, {"token": "▁Ġflee", "score": -11.4286, "line": 6716}, {"token": "▁ĠMiss", "score": -11.4293, "line": 6721}, {"token": "▁Ġpolic", "score": -11.4293, "line": 6722}, {"token": "▁Ġdonn", "score": -11.4294, "line": 6723}, {"token": "▁ount", "score": -11.4302, "line": 6728}, {"token": "▁éĽĨå", "score": -11.4308, "line": 6729}, {"token": "▁Ġvita", "score": -11.4308, "line": 6730}, {"token": "▁Ġpossess", "score": -11.432, "line": 6739}, {"token": "▁ĠRoman", "score": -11.4323, "line": 6741}, {"token": "▁Ġrefin", "score": -11.433, "line": 6748}, {"token": "▁Ġassembl", "score": -11.4333, "line": 6750}, {"token": "▁Ġarrang", "score": -11.434, "line": 6753}, {"token": "▁̄ÙĬÙħÙĤ", "score": -11.4343, "line": 6754}, {"token": "▁ĠDomin", "score": -11.4394, "line": 6776}, {"token": "▁Ġinsul", "score": -11.4401, "line": 6781}, {"token": "▁åĪĽå", "score": -11.4421, "line": 6787}, {"token": "▁Ġdiese", "score": -11.4429, "line": 6790}, {"token": "▁enerat", "score": -11.4432, "line": 6792}, {"token": "▁Ġassociat", "score": -11.4451, "line": 6795}, {"token": "▁åįĹæ", "score": -11.4462, "line": 6799}, {"token": "▁åĪĩå", "score": -11.4468, "line": 6800}, {"token": "▁Ġadvers", "score": -11.4469, "line": 6802}, {"token": "▁Ġsymp", "score": -11.4469, "line": 6804}, {"token": "▁Ġpurchas", "score": -11.447, "line": 6805}, {"token": "▁ĠMerc", "score": -11.4473, "line": 6807}, {"token": "▁åIJĪè", "score": -11.4493, "line": 6811}, {"token": "▁çļĦæĥħ", "score": -11.4499, "line": 6813}, {"token": "▁Ġliter", "score": -11.4516, "line": 6815}, {"token": "▁äooç", "score": -11.4531, "line": 6820}, {"token": "▁æijĦå", "score": -11.4544, "line": 6823}, {"token": "▁Ġresembl", "score": -11.4572, "line": 6827}, {"token": "▁éĩijå", "score": -11.4629, "line": 6833}, {"token": "▁ĠComput", "score": -11.4639, "line": 6838}, {"token": "▁ĠBrit", "score": -11.4656, "line": 6842}, {"token": "▁Ġquant", "score": -11.4687, "line": 6847}, {"token": "▁Ġcreat", "score": -11.4715, "line": 6854}, {"token": "▁Ġunivers", "score": -11.4731, "line": 6855}, {"token": "▁*****", "score": -11.4746, "line": 6857}, {"token": "▁Ġcentr", "score": -11.4769, "line": 6861}, {"token": "▁ATA", "score": -11.4786, "line": 6863}, {"token": "▁Ġrecurs", "score": -11.4791, "line": 6866}, {"token": "▁æľīç", "score": -11.4806, "line": 6870}, {"token": "▁Ġignor", "score": -11.4812, "line": 6872}, {"token": "▁ĠSwa", "score": -11.4822, "line": 6875}, {"token": "▁ĠMatt", "score": -11.4835, "line": 6877}, {"token": "▁Ġcaus", "score": -11.4836, "line": 6878}, {"token": "▁Ġintens", "score": -11.491, "line": 6881}, {"token": "▁ĠDefen", "score": -11.4986, "line": 6887}, {"token": "▁Ġwol", "score": -11.4989, "line": 6888}, {"token": "▁ĉĉĉĉĉĉĉĉ", "score": -11.5006, "line": 6890}, {"token": "▁xE", "score": -11.51, "line": 6896}, {"token": "▁åŃIJå", "score": -11.5129, "line": 6899}, {"token": "▁Ġvict", "score": -11.5269, "line": 6915}, {"token": "▁Ġseiz", "score": -11.527, "line": 6916}, {"token": "▁creat", "score": -11.528, "line": 6918}, {"token": "▁forc", "score": -11.5343, "line": 6924}, {"token": "▁Ġmeasur", "score": -11.5364, "line": 6926}, {"token": "▁Ġdecorat", "score": -11.5377, "line": 6927}, {"token": "▁icia", "score": -11.54, "line": 6929}, {"token": "▁Ġsqua", "score": -11.543, "line": 6932}, {"token": "▁xB", "score": -11.5469, "line": 6934}, {"token": "▁atori", "score": -11.5521, "line": 6936}, {"token": "▁Ġissu", "score": -11.5523, "line": 6937}, {"token": "▁))))", "score": -11.554, "line": 6941}, {"token": "▁ĠRespons", "score": -11.5666, "line": 6949}, {"token": "▁Ġthem", "score": -11.5752, "line": 6952}, {"token": "▁ĠFlu", "score": -11.5767, "line": 6954}, {"token": "▁ĠInitial", "score": -11.5867, "line": 6960}, {"token": "▁Ġearn", "score": -11.5924, "line": 6963}, {"token": "▁ĉĉĉĉĉĉĉĉĉĉ", "score": -11.5955, "line": 6964}, {"token": "▁Ġinvit", "score": -11.6029, "line": 6965}, {"token": "▁ĠCompar", "score": -11.6066, "line": 6968}, {"token": "▁Ġstyl", "score": -11.6071, "line": 6969}, {"token": "▁ĠEntr", "score": -11.6113, "line": 6972}, {"token": "▁Ġconsist", "score": -11.6141, "line": 6975}, {"token": "▁Ġcommunicat", "score": -11.6162, "line": 6978}, {"token": "▁Ġnarr", "score": -11.6202, "line": 6983}, {"token": "▁Ġmassa", "score": -11.6217, "line": 6986}, {"token": "▁Ġincid", "score": -11.6219, "line": 6987}, {"token": "▁ĉĠĠĠĠĠĠĠ", "score": -11.6231, "line": 6990}, {"token": "▁Ġprofes", "score": -11.6232, "line": 6991}, {"token": "▁Ġcorrespond", "score": -11.6232, "line": 6993}, {"token": "▁Ġexhaust", "score": -11.6232, "line": 6994}, {"token": "▁Ġprohibit", "score": -11.6232, "line": 6995}, {"token": "▁Ġsacrific", "score": -11.6232, "line": 6996}, {"token": "▁Ġscholar", "score": -11.6232, "line": 6997}, {"token": "▁Ġszczeg", "score": -11.6232, "line": 6998}, {"token": "▁Ġdiscrimin", "score": -11.6232, "line": 7014}, {"token": "▁Ġnegotiat", "score": -11.6232, "line": 7015}, {"token": "▁Ġphilosoph", "score": -11.6232, "line": 7016}, {"token": "▁Install", "score": -11.6232, "line": 7029}, {"token": "▁ĠIntegr", "score": -11.6232, "line": 7030}, {"token": "▁Ġexploit", "score": -11.6232, "line": 7031}, {"token": "▁Ġinhibit", "score": -11.6232, "line": 7032}, {"token": "▁Ġknock", "score": -11.6232, "line": 7033}, {"token": "▁ĠoluÅŁ", "score": -11.6232, "line": 7034}, {"token": "▁Ġpossibilit", "score": -11.6232, "line": 7035}, {"token": "▁Ġtrouble", "score": -11.6232, "line": 7036}, {"token": "▁Ġdisagree", "score": -11.6232, "line": 7041}, {"token": "▁Ġitalian", "score": -11.6232, "line": 7042}, {"token": "▁ãĥĹãĥŃãĤ", "score": -11.6232, "line": 7044}, {"token": "▁ĠDemocr", "score": -11.6232, "line": 7045}, {"token": "▁ĠInstall", "score": -11.6232, "line": 7046}, {"token": "▁Ġeliminat", "score": -11.6232, "line": 7047}, {"token": "▁Ġwarrant", "score": -11.6232, "line": 7048}, {"token": "▁Ġstrugg", "score": -11.6232, "line": 7052}, {"token": "▁Ġfavour", "score": -11.6232, "line": 7054}, {"token": "▁Ġcounsel", "score": -11.6232, "line": 7057}, {"token": "▁Ġdisturb", "score": -11.6232, "line": 7059}, {"token": "▁̧įéĶĻ", "score": -11.6232, "line": 7060}, {"token": "▁åħħåĪĨ", "score": -11.6232, "line": 7063}, {"token": "▁Ġenhance", "score": -11.6232, "line": 7067}, {"token": "▁ĠOlymp", "score": -11.6232, "line": 7069}, {"token": "▁Ġreject", "score": -11.6232, "line": 7072}, {"token": "▁Ġengage", "score": -11.6232, "line": 7073}, {"token": "▁ĠkarÅŁ", "score": -11.6232, "line": 7074}, {"token": "▁Ġincorporat", "score": -11.6232, "line": 7075}, {"token": "▁Ġwithdraw", "score": -11.6232, "line": 7078}, {"token": "▁Ġbenefici", "score": -11.6233, "line": 7080}, {"token": "▁Ġexceed", "score": -11.6233, "line": 7083}, {"token": "▁toBe", "score": -11.6233, "line": 7084}, {"token": "▁éĩĩç", "score": -11.6233, "line": 7087}, {"token": "▁Ġsustain", "score": -11.6233, "line": 7089}, {"token": "▁Ġclimb", "score": -11.6233, "line": 7091}, {"token": "▁Ġaccord", "score": -11.6233, "line": 7092}, {"token": "▁Ġproceed", "score": -11.6233, "line": 7095}, {"token": "▁Ġportray", "score": -11.6233, "line": 7097}, {"token": "▁Ġtherap", "score": -11.6233, "line": 7099}, {"token": "▁Ġaccelerat", "score": -11.6233, "line": 7103}, {"token": "▁ãĥĸãĥ", "score": -11.6233, "line": 7108}, {"token": "▁Ġdiferen", "score": -11.6233, "line": 7114}, {"token": "▁chedule", "score": -11.6233, "line": 7122}, {"token": "▁Ġsmooth", "score": -11.6233, "line": 7124}, {"token": "▁Ġsuffer", "score": -11.6233, "line": 7127}, {"token": "▁Ġregard", "score": -11.6233, "line": 7128}, {"token": "▁Ġadmir", "score": -11.6233, "line": 7131}, {"token": "▁Ġdepict", "score": -11.6233, "line": 7134}, {"token": "▁ĠClean", "score": -11.6234, "line": 7136}, {"token": "▁Ġyoung", "score": -11.6234, "line": 7137}, {"token": "▁Ġdistinct", "score": -11.6234, "line": 7139}, {"token": "▁ĠSouth", "score": -11.6234, "line": 7140}, {"token": "▁ĠBrew", "score": -11.6234, "line": 7144}, {"token": "▁Ġfemin", "score": -11.6234, "line": 7146}, {"token": "▁Ġkvinn", "score": -11.6234, "line": 7148}, {"token": "▁ãģĤãĤĬãģĮ", "score": -11.6234, "line": 7150}, {"token": "▁Ġsurround", "score": -11.6234, "line": 7153}, {"token": "▁Ġawake", "score": -11.6234, "line": 7160}, {"token": "▁ÙħÙĨØaØ", "score": -11.6235, "line": 7163}, {"token": "▁Ġimpact", "score": -11.6235, "line": 7164}, {"token": "▁ØŃÙħØ", "score": -11.6235, "line": 7168}, {"token": "▁ÙĥØaØ", "score": -11.6235, "line": 7170}, {"token": "▁ĠProtect", "score": -11.6235, "line": 7175}, {"token": "▁ĠCommun", "score": -11.6235, "line": 7177}, {"token": "▁Ġclinic", "score": -11.6236, "line": 7187}, {"token": "▁ÙħØŃØ", "score": -11.6236, "line": 7190}, {"token": "▁éĻįä", "score": -11.6237, "line": 7195}, {"token": "▁Ġdescend", "score": -11.6237, "line": 7196}, {"token": "▁ĠJohn", "score": -11.6237, "line": 7200}, {"token": "▁=\"\">", "score": -11.6238, "line": 7202}, {"token": "▁ĠTurn", "score": -11.6238, "line": 7203}, {"token": "▁ĠAnaly", "score": -11.6239, "line": 7205}, {"token": "▁̧ĢåĪĩ", "score": -11.6239, "line": 7208}, {"token": "▁̄ÙĬÙĨØ", "score": -11.624, "line": 7213}, {"token": "▁']=", "score": -11.624, "line": 7214}, {"token": "▁Ġaddict", "score": -11.624, "line": 7215}, {"token": "▁ĠLinked", "score": -11.624, "line": 7217}, {"token": "▁×¦×¤×", "score": -11.624, "line": 7218}, {"token": "▁Ġsmok", "score": -11.624, "line": 7219}, {"token": "▁ĠÄijÆ", "score": -11.6241, "line": 7225}, {"token": "▁Ġinsist", "score": -11.6241, "line": 7227}, {"token": "▁Ġbattle", "score": -11.6242, "line": 7230}, {"token": "▁ĠItal", "score": -11.6242, "line": 7236}, {"token": "▁Ġunters", "score": -11.6242, "line": 7237}, {"token": "▁Ġgather", "score": -11.6242, "line": 7238}, {"token": "▁ĠSky", "score": -11.6244, "line": 7241}, {"token": "▁urrenc", "score": -11.6244, "line": 7244}, {"token": "▁Ġdistract", "score": -11.6245, "line": 7246}, {"token": "▁éĩįä", "score": -11.6245, "line": 7247}, {"token": "▁Ġcrush", "score": -11.6247, "line": 7249}, {"token": "▁Ġdisappear", "score": -11.6248, "line": 7251}, {"token": "▁vertis", "score": -11.6248, "line": 7252}, {"token": "▁Ġinterview", "score": -11.6248, "line": 7253}, {"token": "▁ancell", "score": -11.625, "line": 7254}, {"token": "▁Ġfantas", "score": -11.6252, "line": 7256}, {"token": "▁ĠWilli", "score": -11.6254, "line": 7260}, {"token": "▁Ġmelt", "score": -11.6254, "line": 7261}, {"token": "▁Ġpropag", "score": -11.6255, "line": 7266}, {"token": "▁ĠWild", "score": -11.6255, "line": 7267}, {"token": "▁Ġstress", "score": -11.6257, "line": 7271}, {"token": "▁Ġinsan", "score": -11.626, "line": 7277}, {"token": "▁Ġmoral", "score": -11.6263, "line": 7281}, {"token": "▁Ġrefus", "score": -11.6265, "line": 7287}, {"token": "▁Ġspecializ", "score": -11.6267, "line": 7289}, {"token": "▁Ġkhu", "score": -11.6271, "line": 7294}, {"token": "▁Ġnarrow", "score": -11.628, "line": 7300}, {"token": "▁ãĤĪãģ", "score": -11.6282, "line": 7302}, {"token": "▁æĮĩç", "score": -11.6285, "line": 7304}, {"token": "▁Opera", "score": -11.6286, "line": 7305}, {"token": "▁åħħæ", "score": -11.6287, "line": 7306}, {"token": "▁Ġsanct", "score": -11.6289, "line": 7307}, {"token": "▁Ġunderstand", "score": -11.6289, "line": 7308}, {"token": "▁////////////", "score": -11.6293, "line": 7309}, {"token": "▁\":[", "score": -11.6304, "line": 7313}, {"token": "▁Ġinfluenc", "score": -11.6316, "line": 7316}, {"token": "▁Ġoptimiz", "score": -11.6329, "line": 7319}, {"token": "▁Ġquáo", "score": -11.6333, "line": 7320}, {"token": "▁åħļæ", "score": -11.6333, "line": 7321}, {"token": "▁Ġmanufacture", "score": -11.6342, "line": 7324}, {"token": "▁Ġrecogniz", "score": -11.6355, "line": 7326}, {"token": "▁Ġchalleng", "score": -11.6355, "line": 7327}, {"token": "▁Ġaggregat", "score": -11.6356, "line": 7329}, {"token": "▁Ġprostitut", "score": -11.6361, "line": 7333}, {"token": "▁rypt", "score": -11.6371, "line": 7336}, {"token": "▁Ġbroke", "score": -11.6378, "line": 7338}, {"token": "▁ĠKris", "score": -11.6381, "line": 7340}, {"token": "▁Ġescap", "score": -11.6404, "line": 7347}, {"token": "▁Ġintegrat", "score": -11.6437, "line": 7357}, {"token": "▁ĠOpera", "score": -11.6442, "line": 7359}, {"token": "▁Ġmutat", "score": -11.6442, "line": 7360}, {"token": "▁Ġargu", "score": -11.6444, "line": 7361}, {"token": "▁åĩĨå", "score": -11.646, "line": 7362}, {"token": "▁ĠVari", "score": -11.6468, "line": 7364}, {"token": "▁Ġpresum", "score": -11.6485, "line": 7365}, {"token": "▁Qui", "score": -11.6512, "line": 7374}, {"token": "▁Ġgreat", "score": -11.6525, "line": 7376}, {"token": "▁>>(", "score": -11.6557, "line": 7380}, {"token": "▁Ġappear", "score": -11.6561, "line": 7381}, {"token": "▁Ġparticul", "score": -11.658, "line": 7383}, {"token": "▁Ġsupervis", "score": -11.6606, "line": 7386}, {"token": "▁Ġevi", "score": -11.6624, "line": 7390}, {"token": "▁rupt", "score": -11.6628, "line": 7391}, {"token": "▁íķĺë", "score": -11.6649, "line": 7393}, {"token": "▁Ġrubb", "score": -11.6651, "line": 7394}, {"token": "▁Ġstimulat", "score": -11.6689, "line": 7399}, {"token": "▁Ġretriev", "score": -11.6713, "line": 7401}, {"token": "▁Ġterminat", "score": -11.6715, "line": 7402}, {"token": "▁Ġdonat", "score": -11.6828, "line": 7410}, {"token": "▁Ġrealis", "score": -11.684, "line": 7411}, {"token": "▁veni", "score": -11.685, "line": 7412}, {"token": "▁åıaæ", "score": -11.6858, "line": 7415}, {"token": "▁Ġimpress", "score": -11.6896, "line": 7416}, {"token": "▁Ġlicens", "score": -11.7011, "line": 7425}, {"token": "▁Ġleas", "score": -11.7049, "line": 7429}, {"token": "▁diÄŁ", "score": -11.7056, "line": 7431}, {"token": "▁Ġadopt", "score": -11.7104, "line": 7433}, {"token": "▁ALLE", "score": -11.7157, "line": 7436}, {"token": "▁Ġintercept", "score": -11.7234, "line": 7437}, {"token": "▁Ġaffili", "score": -11.7318, "line": 7439}, {"token": "▁Ġgenera", "score": -11.7371, "line": 7443}, {"token": "▁Ġdiscipl", "score": -11.7382, "line": 7444}, {"token": "▁Ġmerg", "score": -11.739, "line": 7445}, {"token": "▁etermin", "score": -11.7396, "line": 7446}, {"token": "▁Ġcoordinat", "score": -11.7408, "line": 7447}, {"token": "▁Ġfigur", "score": -11.7474, "line": 7452}, {"token": "▁ĠWinn", "score": -11.7554, "line": 7455}, {"token": "▁Ġcharg", "score": -11.7599, "line": 7457}, {"token": "▁Writ", "score": -11.7689, "line": 7459}, {"token": "▁ãĥĽãĥ", "score": -11.7752, "line": 7461}, {"token": "▁Seg", "score": -11.7764, "line": 7462}, {"token": "▁Marc", "score": -11.7979, "line": 7471}, {"token": "▁ĠĠĠĠĠ", "score": -11.7986, "line": 7472}, {"token": "▁Ġaccumulat", "score": -11.8005, "line": 7476}, {"token": "▁ĠAudi", "score": -11.8013, "line": 7477}, {"token": "▁Ġinitializ", "score": -11.8064, "line": 7479}, {"token": "▁Ġallocat", "score": -11.8126, "line": 7482}, {"token": "▁Ġadvanc", "score": -11.8192, "line": 7485}, {"token": "▁Ġpharmac", "score": -11.82, "line": 7486}, {"token": "▁Ġdiscuss", "score": -11.8533, "line": 7491}, {"token": "▁Ġimplement", "score": -11.8584, "line": 7495}, {"token": "▁Ġdestin", "score": -11.8615, "line": 7496}, {"token": "▁Ġconfigur", "score": -11.8629, "line": 7497}, {"token": "▁Ġexerc", "score": -11.8641, "line": 7498}, {"token": "▁Ġpetit", "score": -11.8657, "line": 7499}, {"token": "▁Ġinflu", "score": -11.8659, "line": 7500}, {"token": "▁ĠBrig", "score": -11.8682, "line": 7505}, {"token": "▁ĠCatal", "score": -11.8703, "line": 7511}, {"token": "▁uzz", "score": -11.8707, "line": 7513}, {"token": "▁Ġfavor", "score": -11.8713, "line": 7515}, {"token": "▁ĉĉĠĠĠĠĠĠĠ", "score": -11.8732, "line": 7519}, {"token": "▁Ġvolunt", "score": -11.8732, "line": 7520}, {"token": "▁ĠParticip", "score": -11.8732, "line": 7524}, {"token": "▁Ġaccomplish", "score": -11.8732, "line": 7525}, {"token": "▁Ġembarrass", "score": -11.8732, "line": 7526}, {"token": "▁przedsiÄĻbior", "score": -11.8732, "line": 7548}, {"token": "▁ãģĭãĤĤãģĹãĤĮ", "score": -11.8732, "line": 7549}, {"token": "▁ĠDISCLAIM", "score": -11.8732, "line": 7550}, {"token": "▁ĠRecommend", "score": -11.8732, "line": 7551}, {"token": "▁Ġadolescen", "score": -11.8732, "line": 7552}, {"token": "▁Ġaffirm", "score": -11.8732, "line": 7553}, {"token": "▁Ġcontrovers", "score": -11.8732, "line": 7554}, {"token": "▁Ġcultivat", "score": -11.8732, "line": 7555}, {"token": "▁Ġencourage", "score": -11.8732, "line": 7556}, {"token": "▁Ġentrepreneur", "score": -11.8732, "line": 7557}, {"token": "▁Ġguarante", "score": -11.8732, "line": 7558}, {"token": "▁Ġmanipulat", "score": -11.8732, "line": 7559}, {"token": "▁ĠmiÄĻdz", "score": -11.8732, "line": 7560}, {"token": "▁Ġneighbour", "score": -11.8732, "line": 7561}, {"token": "▁Ġphenomen", "score": -11.8732, "line": 7562}, {"token": "▁Ġreluct", "score": -11.8732, "line": 7563}, {"token": "▁Ġabsorb", "score": -11.8732, "line": 7572}, {"token": "▁Ġassault", "score": -11.8732, "line": 7573}, {"token": "▁Ġbureaucr", "score": -11.8732, "line": 7574}, {"token": "▁Ġmechanic", "score": -11.8732, "line": 7575}, {"token": "▁Ġadventure", "score": -11.8732, "line": 7582}, {"token": "▁Ġeffort", "score": -11.8732, "line": 7583}, {"token": "▁Ġintimidat", "score": -11.8732, "line": 7584}, {"token": "▁Ġpollut", "score": -11.8732, "line": 7585}, {"token": "▁Ġstrengthen", "score": -11.8732, "line": 7586}, {"token": "▁Ġsucceed", "score": -11.8732, "line": 7587}, {"token": "▁Ġvulner", "score": -11.8732, "line": 7588}, {"token": "▁ĠìķĬì", "score": -11.8732, "line": 7589}, {"token": "▁ĠìľĦíķ", "score": -11.8732, "line": 7590}, {"token": "▁ĠPalestin", "score": -11.8732, "line": 7595}, {"token": "▁Ġarbitr", "score": -11.8732, "line": 7596}, {"token": "▁Ġlegitim", "score": -11.8732, "line": 7597}, {"token": "▁Ġterritor", "score": -11.8732, "line": 7598}, {"token": "▁ĠBuddh", "score": -11.8732, "line": 7602}, {"token": "▁Ġcompreh", "score": -11.8732, "line": 7603}, {"token": "▁Ġinvolve", "score": -11.8732, "line": 7604}, {"token": "▁ĠwÅĤaÅĽ", "score": -11.8732, "line": 7605}, {"token": "▁Ġapproach", "score": -11.8732, "line": 7610}, {"token": "▁Ġsociet", "score": -11.8732, "line": 7611}, {"token": "▁inherit", "score": -11.8732, "line": 7617}, {"token": "▁Ġastronom", "score": -11.8732, "line": 7618}, {"token": "▁Ġmillenn", "score": -11.8732, "line": 7619}, {"token": "▁Ġolmad", "score": -11.8732, "line": 7620}, {"token": "▁Ġcondemn", "score": -11.8732, "line": 7623}, {"token": "▁Ġlegislat", "score": -11.8732, "line": 7624}, {"token": "▁Ġscream", "score": -11.8732, "line": 7625}, {"token": "▁Ġabandon", "score": -11.8732, "line": 7628}, {"token": "▁Ġhappen", "score": -11.8732, "line": 7629}, {"token": "▁Ġragazz", "score": -11.8732, "line": 7630}, {"token": "▁schlÃ", "score": -11.8732, "line": 7634}, {"token": "▁ĠIslam", "score": -11.8732, "line": 7635}, {"token": "▁Ġinherit", "score": -11.8732, "line": 7636}, {"token": "▁Ġceremon", "score": -11.8732, "line": 7638}, {"token": "▁Ġsubstant", "score": -11.8732, "line": 7639}, {"token": "▁Ġtweak", "score": -11.8732, "line": 7641}, {"token": "▁ĠIntelli", "score": -11.8732, "line": 7644}, {"token": "▁Ġentertain", "score": -11.8732, "line": 7645}, {"token": "▁Ġhumili", "score": -11.8732, "line": 7646}, {"token": "▁Ġneglig", "score": -11.8732, "line": 7647}, {"token": "▁Ġwzgl", "score": -11.8732, "line": 7648}, {"token": "▁ĠIllegal", "score": -11.8732, "line": 7649}, {"token": "▁Ġdoubt", "score": -11.8732, "line": 7650}, {"token": "▁Ġthrill", "score": -11.8732, "line": 7651}, {"token": "▁ORIZ", "score": -11.8732, "line": 7654}, {"token": "▁ĠFriend", "score": -11.8732, "line": 7655}, {"token": "▁Ġinnovat", "score": -11.8732, "line": 7658}, {"token": "▁æĪĸèĢħ", "score": -11.8733, "line": 7660}, {"token": "▁ĠImplement", "score": -11.8733, "line": 7661}, {"token": "▁Ġescort", "score": -11.8733, "line": 7662}, {"token": "▁æīĢéľĢ", "score": -11.8733, "line": 7663}, {"token": "▁ĠBrowser", "score": -11.8733, "line": 7666}, {"token": "▁Ġcambi", "score": -11.8733, "line": 7667}, {"token": "▁Ġconvict", "score": -11.8733, "line": 7668}, {"token": "▁Ġdemocr", "score": -11.8733, "line": 7669}, {"token": "▁Ġcompens", "score": -11.8733, "line": 7670}, {"token": "▁hydr", "score": -11.8733, "line": 7672}, {"token": "▁++){", "score": -11.8733, "line": 7674}, {"token": "▁Ġexamine", "score": -11.8733, "line": 7677}, {"token": "▁Analy", "score": -11.8733, "line": 7678}, {"token": "▁Ġallerg", "score": -11.8733, "line": 7679}, {"token": "▁Ġpenetrat", "score": -11.8733, "line": 7680}, {"token": "▁̈ãģĦãģĨãģ", "score": -11.8733, "line": 7682}, {"token": "▁Ġpioneer", "score": -11.8733, "line": 7683}, {"token": "▁Ġinjur", "score": -11.8733, "line": 7686}, {"token": "▁Ġdeutsch", "score": -11.8733, "line": 7687}, {"token": "▁ĠThank", "score": -11.8733, "line": 7690}, {"token": "▁Ġcrowd", "score": -11.8733, "line": 7691}, {"token": "▁Ġshock", "score": -11.8733, "line": 7694}, {"token": "▁Every", "score": -11.8733, "line": 7698}, {"token": "▁Ġpresident", "score": -11.8733, "line": 7699}, {"token": "▁.*;", "score": -11.8733, "line": 7701}, {"token": "▁Ġnutrit", "score": -11.8733, "line": 7702}, {"token": "▁Ġspeculat", "score": -11.8733, "line": 7703}, {"token": "▁Ġmunic", "score": -11.8733, "line": 7705}, {"token": "▁Ġvolunteer", "score": -11.8733, "line": 7711}, {"token": "▁ĠConsult", "score": -11.8733, "line": 7712}, {"token": "▁Ġsuprem", "score": -11.8733, "line": 7713}, {"token": "▁ujÄħc", "score": -11.8733, "line": 7715}, {"token": "▁Ġreveal", "score": -11.8733, "line": 7717}, {"token": "▁Ġfulfill", "score": -11.8733, "line": 7720}, {"token": "▁Ġdream", "score": -11.8733, "line": 7723}, {"token": "▁Ġobtain", "score": -11.8733, "line": 7724}, {"token": "▁ãĥĢãĤ", "score": -11.8733, "line": 7727}, {"token": "▁Ġannoy", "score": -11.8733, "line": 7731}, {"token": "▁Ġappeal", "score": -11.8733, "line": 7732}, {"token": "▁Ġthreaten", "score": -11.8733, "line": 7733}, {"token": "▁ĠTitan", "score": -11.8733, "line": 7735}, {"token": "▁Ġdilig", "score": -11.8733, "line": 7737}, {"token": "▁--;", "score": -11.8733, "line": 7739}, {"token": "▁erializ", "score": -11.8733, "line": 7740}, {"token": "▁Ġempower", "score": -11.8733, "line": 7741}, {"token": "▁ĠKhÃ", "score": -11.8733, "line": 7742}, {"token": "▁->{", "score": -11.8733, "line": 7743}, {"token": "▁Ġconvey", "score": -11.8734, "line": 7744}, {"token": "▁Ġmyster", "score": -11.8734, "line": 7746}, {"token": "▁Ġsweat", "score": -11.8734, "line": 7747}, {"token": "▁Ġrenov", "score": -11.8734, "line": 7748}, {"token": "▁Ġhappi", "score": -11.8734, "line": 7749}, {"token": "▁Ġgreet", "score": -11.8734, "line": 7751}, {"token": "▁Ġspoil", "score": -11.8734, "line": 7754}, {"token": "▁Ġrelax", "score": -11.8734, "line": 7759}, {"token": "▁*******", "score": -11.8734, "line": 7760}, {"token": "▁ĠBrook", "score": -11.8734, "line": 7761}, {"token": "▁ÅĤoÅ", "score": -11.8734, "line": 7762}, {"token": "▁ĠKurd", "score": -11.8734, "line": 7763}, {"token": "▁ĠaÅŁa", "score": -11.8734, "line": 7764}, {"token": "▁Digit", "score": -11.8734, "line": 7769}, {"token": "▁ĠLouis", "score": -11.8734, "line": 7770}, {"token": "▁'].'", "score": -11.8735, "line": 7776}, {"token": "▁oggle", "score": -11.8735, "line": 7779}, {"token": "▁ĠbaÄŁl", "score": -11.8735, "line": 7782}, {"token": "▁Ġconvinc", "score": -11.8735, "line": 7783}, {"token": "▁Ġcrawl", "score": -11.8735, "line": 7787}, {"token": "▁Ġmultipli", "score": -11.8735, "line": 7790}, {"token": "▁ĠbyÅĤ", "score": -11.8735, "line": 7792}, {"token": "▁Ġexplod", "score": -11.8735, "line": 7794}, {"token": "▁éŃĶæ", "score": -11.8735, "line": 7795}, {"token": "▁ĠSpiel", "score": -11.8735, "line": 7796}, {"token": "▁ĠIndust", "score": -11.8736, "line": 7799}, {"token": "▁Ġsuppress", "score": -11.8736, "line": 7802}, {"token": "▁Ġexclud", "score": -11.8736, "line": 7803}, {"token": "▁Ġexcit", "score": -11.8736, "line": 7804}, {"token": "▁Ġjaki", "score": -11.8737, "line": 7808}, {"token": "▁Ġsmell", "score": -11.8737, "line": 7809}, {"token": "▁Ġinvad", "score": -11.8737, "line": 7810}, {"token": "▁Ġacompa", "score": -11.8737, "line": 7811}, {"token": "▁Ġdedic", "score": -11.8737, "line": 7812}, {"token": "▁Ġsincer", "score": -11.8738, "line": 7814}, {"token": "▁ĠRiver", "score": -11.8738, "line": 7816}, {"token": "▁Download", "score": -11.8739, "line": 7818}, {"token": "▁gÅĤo", "score": -11.8739, "line": 7820}, {"token": "▁Ġsharp", "score": -11.8739, "line": 7822}, {"token": "▁ĠSnap", "score": -11.8739, "line": 7824}, {"token": "▁ãģķãĤĵãģ", "score": -11.874, "line": 7825}, {"token": "▁Ġmirac", "score": -11.874, "line": 7827}, {"token": "▁Ġfestiv", "score": -11.8742, "line": 7836}, {"token": "▁Ġescalat", "score": -11.8742, "line": 7838}, {"token": "▁Ġelevat", "score": -11.8743, "line": 7839}, {"token": "▁ĠPrincip", "score": -11.8743, "line": 7840}, {"token": "▁Ġdisappoint", "score": -11.8743, "line": 7841}, {"token": "▁Ġfertil", "score": -11.8744, "line": 7844}, {"token": "▁Ġhypoth", "score": -11.8744, "line": 7845}, {"token": "▁ĠwiÄĻ", "score": -11.8745, "line": 7850}, {"token": "▁Ġdifferentia", "score": -11.8745, "line": 7851}, {"token": "▁Ġconfus", "score": -11.8746, "line": 7852}, {"token": "▁Ġjewel", "score": -11.8748, "line": 7857}, {"token": "▁Ġcinema", "score": -11.8751, "line": 7863}, {"token": "▁Ġsigu", "score": -11.8753, "line": 7865}, {"token": "▁ĠRank", "score": -11.8753, "line": 7866}, {"token": "▁Ġsalari", "score": -11.8758, "line": 7875}, {"token": "▁Ġunsere", "score": -11.8758, "line": 7876}, {"token": "▁Ġbulun", "score": -11.8758, "line": 7877}, {"token": "▁æģIJæ", "score": -11.8759, "line": 7878}, {"token": "▁æİĮæ", "score": -11.876, "line": 7879}, {"token": "▁ĠIhre", "score": -11.8761, "line": 7880}, {"token": "▁Ġanalog", "score": -11.8761, "line": 7881}, {"token": "▁Ġfamilia", "score": -11.8763, "line": 7883}, {"token": "▁POSIT", "score": -11.8765, "line": 7884}, {"token": "▁Ġcustomiz", "score": -11.8766, "line": 7887}, {"token": "▁Ġcabe", "score": -11.8766, "line": 7889}, {"token": "▁ĠTiá", "score": -11.8767, "line": 7890}, {"token": "▁CLUD", "score": -11.8768, "line": 7894}, {"token": "▁gregat", "score": -11.8773, "line": 7896}, {"token": "▁Ġsentenc", "score": -11.8779, "line": 7900}, {"token": "▁Ġaggress", "score": -11.878, "line": 7901}, {"token": "▁Ġauthorit", "score": -11.878, "line": 7902}, {"token": "▁Ġtiene", "score": -11.8782, "line": 7903}, {"token": "▁ìĭľì", "score": -11.8787, "line": 7907}, {"token": "▁Ġdeduct", "score": -11.8806, "line": 7914}, {"token": "▁Ġobsess", "score": -11.8807, "line": 7915}, {"token": "▁('.')", "score": -11.8828, "line": 7925}, {"token": "▁ĠTerra", "score": -11.8849, "line": 7930}, {"token": "▁ĠbÄĻd", "score": -11.8873, "line": 7933}, {"token": "▁Ġcommut", "score": -11.8889, "line": 7939}, {"token": "▁Ġmodific", "score": -11.8911, "line": 7941}, {"token": "▁Ġenforc", "score": -11.8917, "line": 7943}, {"token": "▁ĠDEA", "score": -11.8921, "line": 7944}, {"token": "▁Ġregime", "score": -11.8927, "line": 7945}, {"token": "▁Ġexempl", "score": -11.8942, "line": 7946}, {"token": "▁(',')", "score": -11.8966, "line": 7950}, {"token": "▁ĠChoi", "score": -11.8973, "line": 7952}, {"token": "▁ĠDepend", "score": -11.8977, "line": 7953}, {"token": "▁modifi", "score": -11.8988, "line": 7956}, {"token": "▁egrat", "score": -11.902, "line": 7958}, {"token": "▁Ġsubscrib", "score": -11.9024, "line": 7959}, {"token": "▁(\".\"", "score": -11.9053, "line": 7961}, {"token": "▁Ġimmigr", "score": -11.907, "line": 7962}, {"token": "▁--)", "score": -11.9082, "line": 7964}, {"token": "▁Ġarchitect", "score": -11.9094, "line": 7965}, {"token": "▁Ġgraduat", "score": -11.9119, "line": 7970}, {"token": "▁Ġillumin", "score": -11.9126, "line": 7971}, {"token": "▁Ġinici", "score": -11.9155, "line": 7973}, {"token": "▁Ġtradition", "score": -11.9178, "line": 7975}, {"token": "▁ÙĤÙĪÙ", "score": -11.9292, "line": 7981}, {"token": "▁Initializ", "score": -11.9342, "line": 7983}, {"token": "▁Ġdefinit", "score": -11.9397, "line": 7984}, {"token": "▁ĠnastÄĻ", "score": -11.9415, "line": 7985}, {"token": "▁ÑĢÑĥÑ", "score": -11.9422, "line": 7986}, {"token": "⁄", "score": -13.7065, "line": 7998}], "special_tokens": [{"token": "<unk>", "score": 0.0, "line": 1}, {"token": "<s>", "score": 0.0, "line": 2}, {"token": "</s>", "score": 0.0, "line": 3}], "vocab_size": 8000}, "model_analysis": {"file_size": 359359, "header_bytes": "0a0e0a053c756e6b3e15000000001802", "format": "Binary", "readable": true}, "tokenization_test": {"error": "Unrecognized model in neuroglyph/training/colab_package. Should have a `model_type` key in its config.json, or contain one of the following strings in its name: albert, align, altclip, aria, aria_text, audio-spectrogram-transformer, autoformer, aya_vision, bamba, bark, bart, beit, bert, bert-generation, big_bird, bigbird_pegasus, biogpt, bit, bitnet, blenderbot, blenderbot-small, blip, blip-2, blip_2_qformer, bloom, bridgetower, bros, camembert, canine, chameleon, chinese_clip, chinese_clip_vision_model, clap, clip, clip_text_model, clip_vision_model, clipseg, clvp, code_llama, codegen, cohere, cohere2, colpali, conditional_detr, convbert, convnext, convnextv2, cpmant, csm, ctrl, cvt, d_fine, dab-detr, dac, data2vec-audio, data2vec-text, data2vec-vision, dbrx, deberta, deberta-v2, decision_transformer, deepseek_v3, deformable_detr, deit, depth_anything, depth_pro, deta, detr, diffllama, dinat, dinov2, dinov2_with_registers, distilbert, donut-swin, dpr, dpt, efficientformer, efficientnet, electra, emu3, encodec, encoder-decoder, ernie, ernie_m, esm, falcon, falcon_mamba, fastspeech2_conformer, flaubert, flava, fnet, focalnet, fsmt, funnel, fuyu, gemma, gemma2, gemma3, gemma3_text, git, glm, glm4, glpn, got_ocr2, gpt-sw3, gpt2, gpt_bigcode, gpt_neo, gpt_neox, gpt_neox_japanese, gptj, gptsan-japanese, granite, granite_speech, granitemoe, granitemoehybrid, granitemoeshared, granitevision, graphormer, grounding-dino, groupvit, helium, hgnet_v2, hiera, hubert, ibert, idefics, idefics2, idefics3, idefics3_vision, ijepa, imagegpt, informer, instructblip, instructblipvideo, internvl, internvl_vision, jamba, janus, jetmoe, jukebox, kosmos-2, layoutlm, layoutlmv2, layoutlmv3, led, levit, lilt, llama, llama4, llama4_text, llava, llava_next, llava_next_video, llava_onevision, longformer, longt5, luke, lxmert, m2m_100, mamba, mamba2, marian, markuplm, mask2former, maskformer, maskformer-swin, mbart, mctct, mega, megatron-bert, mgp-str, mimi, mistral, mistral3, mixtral, mlcd, mllama, mobilebert, mobilenet_v1, mobilenet_v2, mobilevit, mobilevitv2, modernbert, moonshine, moshi, mpnet, mpt, mra, mt5, musicgen, musicgen_melody, mvp, nat, nemotron, nezha, nllb-moe, nougat, nystromformer, olmo, olmo2, olmoe, omdet-turbo, oneformer, open-llama, openai-gpt, opt, owlv2, owlvit, paligemma, patchtsmixer, patchtst, pegasus, pegasus_x, perceiver, persimmon, phi, phi3, phi4_multimodal, phimoe, pix2struct, pixtral, plbart, poolformer, pop2piano, prompt_depth_anything, prophetnet, pvt, pvt_v2, qdqbert, qwen2, qwen2_5_omni, qwen2_5_vl, qwen2_5_vl_text, qwen2_audio, qwen2_audio_encoder, qwen2_moe, qwen2_vl, qwen2_vl_text, qwen3, qwen3_moe, rag, realm, recurrent_gemma, reformer, regnet, rembert, resnet, retribert, roberta, roberta-prelayernorm, roc_bert, roformer, rt_detr, rt_detr_resnet, rt_detr_v2, rwkv, sam, sam_hq, sam_hq_vision_model, sam_vision_model, seamless_m4t, seamless_m4t_v2, segformer, seggpt, sew, sew-d, shieldgemma2, siglip, siglip2, siglip_vision_model, smolvlm, smolvlm_vision, speech-encoder-decoder, speech_to_text, speech_to_text_2, speecht5, splinter, squeezebert, stablelm, starcoder2, superglue, superpoint, swiftformer, swin, swin2sr, swinv2, switch_transformers, t5, table-transformer, tapas, textnet, time_series_transformer, timesfm, timesformer, timm_backbone, timm_wrapper, trajectory_transformer, transfo-xl, trocr, tvlt, tvp, udop, umt5, unispeech, unispeech-sat, univnet, upernet, van, video_llava, videomae, vilt, vipllava, vision-encoder-decoder, vision-text-dual-encoder, visual_bert, vit, vit_hybrid, vit_mae, vit_msn, vitdet, vitmatte, vitpose, vitpose_backbone, vits, vivit, wav2vec2, wav2vec2-bert, wav2vec2-conformer, wavlm, whisper, xclip, xglm, xlm, xlm-prophetnet, xlm-roberta, xlm-roberta-xl, xlnet, xmod, yolos, yoso, zamba, zamba2, zoedepth"}, "locked_state_comparison": {"locked_symbols_count": 66, "critical_symbols_count": 66, "present_in_vocab": 0, "missing_from_vocab": 66, "coverage_rate": 0.0, "missing_symbols": ["⊃", "∧", "∨", "→", "∑", "∫", "∂", "≈", "π", "ƒ", "🔄", "❓", "📋", "🔢", "📝", "⟲", "ng:operator:sub", "ng:memory:pointer", "ng:memory:alloc", "ng:logic:implies", "ng:logic:or_1", "ng:memory:alloc_1", "ng:memory:free", "ng:structure:function_1", "ng:operator:mul", "ng:operator:mod", "ng:operator:add_scalar", "ng:memory:deref", "ng:operator:add_vector", "ng:structure:property", "ng:flow:return_1", "ng:memory:alloc_stack", "ng:flow:for_range", "ng:operator:add_matrix", "ng:flow:if_conditional", "ng:operator:mul_scalar", "ng:logic:and_1", "ng:structure:function_2", "ng:structure:property_1", "ng:memory:pointer_1", "ng:flow:break", "ng:logic:not_bitwise", "ng:logic:implies_1", "ng:flow:break_1", "ng:operator:div", "ng:operator:add_complex", "ng:flow:if_ternary", "ng:logic:or_logical", "ng:memory:deref_1", "ng:structure:class_1", "ng:operator:div_scalar", "ng:memory:pointer_2", "ng:flow:break_2", "ng:memory:deref_2", "ng:operator:mod_integer", "ng:logic:implies_strict", "ng:flow:for_2", "ng:logic:and_logical", "ng:logic:implies_relevant", "ng:structure:property_2", "ng:operator:mod_polynomial", "ng:logic:and_fuzzy", "ng:operator:pow", "ng:structure:method", "ng:memory:pointer_3", "ng:logic:not_logical"], "present_symbols": []}, "overall_assessment": {"vocab_score": 1.0, "model_score": 1.0, "tokenization_score": 0.0, "locked_state_score": 0.0, "overall_score": 0.5, "grade": "D (Needs Improvement)"}}