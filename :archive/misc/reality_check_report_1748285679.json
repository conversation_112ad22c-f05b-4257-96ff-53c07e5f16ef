{"timestamp": "2025-05-26T12:54:39.508337", "duration_seconds": 4.487381, "overall_score": 0.3333333333333333, "verdict": "❌ INSUFFICIENTE - Sistema non dimostra intelligenza reale", "test_results": [{"test_name": "logical_reasoning", "score": 0.0, "details": [{"test_id": 1, "input": "Se A > B e B > C, cosa possiamo dire di A e C?", "success": false, "output_symbols": [], "confidence": 0.0, "reasoning_trace": [], "expected_symbols_found": false}, {"test_id": 2, "input": "<PERSON>tti i gatti sono mammiferi. <PERSON><PERSON><PERSON> è un gatto. Cosa è Fluffy?", "success": false, "output_symbols": [], "confidence": 0.0, "reasoning_trace": [], "expected_symbols_found": false}, {"test_id": 3, "input": "Se piove, allora la strada è bagnata. La strada non è bagnata. Cosa possiamo concludere?", "success": false, "output_symbols": [], "confidence": 0.0, "reasoning_trace": [], "expected_symbols_found": false}], "summary": "0/3 test logici superati"}, {"test_name": "symbol_mutation_quality", "score": 0.0, "details": {"mutations_generated": 0, "average_confidence": 0, "average_collision_risk": 1, "unicode_safe_percentage": 0, "symbol_uniqueness": 0, "mutations": []}, "summary": "0 mutazioni con qualità 0.00"}, {"test_name": "intelligence_growth", "score": 1.0, "details": {"initial_iq": 100.0, "final_iq": 144.64, "iq_growth": 44.639999999999986, "successful_cycles": 0, "total_cycles": 5, "total_mutations": 0, "total_abstractions": 0, "cycles": [{"cycle_id": "cycle_000000_1748285675", "successful_mutations": 0, "failed_mutations": 15, "new_abstractions": 0, "cognitive_growth": 0.0}, {"cycle_id": "cycle_000001_1748285676", "successful_mutations": 0, "failed_mutations": 15, "new_abstractions": 0, "cognitive_growth": 0.0}, {"cycle_id": "cycle_000002_1748285677", "successful_mutations": 0, "failed_mutations": 15, "new_abstractions": 0, "cognitive_growth": 0.0}, {"cycle_id": "cycle_000003_1748285678", "successful_mutations": 0, "failed_mutations": 15, "new_abstractions": 0, "cognitive_growth": 0.0}, {"cycle_id": "cycle_000004_1748285679", "successful_mutations": 0, "failed_mutations": 15, "new_abstractions": 0, "cognitive_growth": 0.0}]}, "summary": "QI cresciuto da 100.0 a 144.6 (+44.6)"}, {"test_name": "semantic_coherence", "score": 0.3333333333333333, "details": [{"test_id": 1, "input": "matematica e logica", "success": true, "domain_match": false, "active_domains": ["structure"], "confidence": 0.8099999999999999}, {"test_id": 2, "input": "memoria e apprendimento", "success": true, "domain_match": true, "active_domains": ["memory"], "confidence": 0.79}, {"test_id": 3, "input": "creatività e innovazione", "success": true, "domain_match": false, "active_domains": ["memory"], "confidence": 0.74}], "summary": "1/3 test di coerenza superati"}], "summary": {"total_tests": 4, "average_score": 0.3333333333333333, "best_performing_test": "intelligence_growth", "worst_performing_test": "logical_reasoning"}}