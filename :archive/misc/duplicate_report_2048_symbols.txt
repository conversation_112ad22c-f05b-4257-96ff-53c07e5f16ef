
🔍 NEUROGLYPH - Duplicate Analysis Report
=========================================

📊 OVERVIEW:
  • Total symbols: 2048
  • Registry health: NEEDS_CLEANUP
  • Total duplicate issues: 29

❌ SYMBOL_DUPLICATES:
  • Found: 24 duplicate groups
  • Affected entries: 71
    - symbol: '⚡' (5 occurrences)
      → {'id': 'NG0568', 'code': 'ng:async:function'}
      → {'id': 'NG0611', 'code': 'ng:worker:executor'}
      → {'id': 'NG0618', 'code': 'ng:schedule:immediate'}
    - symbol: '⏳' (6 occurrences)
      → {'id': 'NG0569', 'code': 'ng:async:await'}
      → {'id': 'NG0619', 'code': 'ng:schedule:delayed'}
      → {'id': 'NG0636', 'code': 'ng:sync:wait'}
    - symbol: '⏸' (2 occurrences)
      → {'id': 'NG0570', 'code': 'ng:async:pause'}
      → {'id': 'NG0668', 'code': 'ng:thread:suspend'}
    - symbol: '⏱' (4 occurrences)
      → {'id': 'NG0573', 'code': 'ng:async:timer'}
      → {'id': 'NG0669', 'code': 'ng:worker:timeout'}
      → {'id': 'NG0672', 'code': 'ng:sync:timeout'}
    - symbol: '🔮' (2 occurrences)
      → {'id': 'NG0575', 'code': 'ng:async:future'}
      → {'id': 'NG0644', 'code': 'ng:future:future'}
    ... e altri 19 gruppi

✅ CODE_DUPLICATES: No duplicates found

❌ ID_DUPLICATES:
  • Found: 5 duplicate groups
  • Affected entries: 10
    - id: 'NG0685' (2 occurrences)
      → {'symbol': '🎉', 'code': 'ng:completion:success'}
      → {'symbol': '⟴', 'code': 'ng:lifecycle:init'}
    - id: 'NG0686' (2 occurrences)
      → {'symbol': '📝', 'code': 'ng:completion:callback'}
      → {'symbol': '⟨', 'code': 'ng:generic:open'}
    - id: 'NG0687' (2 occurrences)
      → {'symbol': '🔔', 'code': 'ng:completion:handler'}
      → {'symbol': '⟩', 'code': 'ng:generic:close'}
    - id: 'NG0688' (2 occurrences)
      → {'symbol': '⚡', 'code': 'ng:completion:immediate'}
      → {'symbol': '⟪', 'code': 'ng:generic:nested_open'}
    - id: 'NG0689' (2 occurrences)
      → {'symbol': '⏳', 'code': 'ng:completion:deferred'}
      → {'symbol': '⟫', 'code': 'ng:generic:nested_close'}

✅ UNICODE_DUPLICATES: No duplicates found

💡 RACCOMANDAZIONI:
  • Eseguire cleanup automatico con --fix
  • Verificare integrità dopo cleanup
  • Aggiornare pipeline di validazione
