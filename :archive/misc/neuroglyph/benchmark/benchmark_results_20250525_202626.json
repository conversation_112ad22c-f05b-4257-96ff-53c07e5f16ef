{"benchmark_timestamp": "20250525_202626", "execution_time_seconds": 0.0, "registry_symbols_count": 1790, "compression_results": {"test_cases": 3, "total_compression_ratio": 3.67, "avg_compression_ratio": 3.88, "token_savings": 32, "detailed_results": [{"id": "SC001", "description": "Function definition compression", "normal_tokens": 17, "neuroglyph_tokens": 4, "compression_ratio": 4.25, "token_savings": 13, "efficiency_gain": 76.5}, {"id": "SC002", "description": "Loop structure compression", "normal_tokens": 15, "neuroglyph_tokens": 3, "compression_ratio": 5.0, "token_savings": 12, "efficiency_gain": 80.0}, {"id": "SC003", "description": "Conditional logic compression", "normal_tokens": 12, "neuroglyph_tokens": 5, "compression_ratio": 2.4, "token_savings": 7, "efficiency_gain": 58.3}], "efficiency_gain_percent": 72.7}, "reasoning_results": {"test_cases": 3, "reasoning_accuracy": 100.0, "avg_reasoning_steps": 2.0, "complexity_distribution": {"basic": 2, "intermediate": 1}, "detailed_results": [{"id": "SR001", "description": "Mathematical reasoning", "is_correct": true, "reasoning_steps": 3, "complexity": "basic", "symbolic_efficiency": 3.33}, {"id": "SR002", "description": "Logical inference", "is_correct": true, "reasoning_steps": 2, "complexity": "intermediate", "symbolic_efficiency": 5.0}, {"id": "SR003", "description": "Set theory operations", "is_correct": true, "reasoning_steps": 1, "complexity": "basic", "symbolic_efficiency": 10.0}]}, "generation_results": {"test_cases": 3, "generation_accuracy": 100.0, "avg_code_length": 101.7, "complexity_handling": {"trivial": 1, "basic": 1, "intermediate": 1}, "detailed_results": [{"id": "CG001", "description": "Simple function generation", "is_correct": true, "code_length": 31, "complexity": "trivial", "neuroglyph_efficiency": 0.516}, {"id": "CG002", "description": "Loop with condition", "is_correct": true, "code_length": 64, "complexity": "basic", "neuroglyph_efficiency": 0.688}, {"id": "CG003", "description": "Class definition", "is_correct": true, "code_length": 210, "complexity": "intermediate", "neuroglyph_efficiency": 0.248}]}, "fidelity_results": {"test_cases": 2, "avg_fidelity_score": 1.0, "perfect_reconstructions": 2, "detailed_results": [{"id": "RTF001", "description": "Simple arithmetic preservation", "fidelity_score": 1.0, "is_perfect": true, "information_loss": 0.0}, {"id": "RTF002", "description": "Control flow preservation", "fidelity_score": 1.0, "is_perfect": true, "information_loss": 0.0}], "perfect_reconstruction_rate": 100.0}, "overall_results": {"overall_score": 93.3, "component_scores": {"compression": 77.6, "reasoning": 100.0, "generation": 100.0, "fidelity": 100.0}, "weights": {"compression": 0.3, "reasoning": 0.25, "generation": 0.25, "fidelity": 0.2}, "grade": "A+ (Excellent)"}}