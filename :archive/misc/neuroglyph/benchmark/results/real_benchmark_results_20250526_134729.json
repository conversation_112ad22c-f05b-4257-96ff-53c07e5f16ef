{"timestamp": "20250526_134729", "config": {"model": "qwen2.5-coder:1.5b", "num_samples": 2, "max_tokens": 512, "temperature": 0.1}, "execution_time_seconds": 29.23, "aggregate_statistics": {"total_tests": 2, "success_rates": {"qwen_standalone": 100.0, "neuroglyph_enhanced": 100.0}, "average_response_times": {"qwen_standalone": 6.976, "neuroglyph_enhanced": 7.536, "time_improvement_percent": -8.0}, "quality_metrics": {"average_semantic_similarity": 0.361, "average_correctness_score": 0.541, "combined_quality_score": 0.451}, "performance_summary": {"neuroglyph_advantage": false, "faster_response": false, "quality_threshold_met": false}}, "detailed_results": [{"test_id": "HumanEval/0", "prompt_preview": "def has_close_elements(numbers: List[float], threshold: float) -> bool:\n    \"\"\" Check if in given li...", "qwen_time": 6.339, "neuroglyph_time": 7.876, "qwen_success": true, "neuroglyph_success": true, "semantic_score": 0.427, "correctness_score": 0.531, "qwen_response_preview": "To solve this problem, we can use a simple approach to iterate through the list and check if any two consecutive numbers are closer than the given threshold. Here's how you can implement it:\n\n```pytho...", "neuroglyph_response_preview": "To solve this problem using logical symbols and structured thinking, we need to define a function that checks if any two elements in a given list are closer to each other than a specified threshold. H..."}, {"test_id": "HumanEval/1", "prompt_preview": "def separate_paren_groups(paren_string: str) -> List[str]:\n    \"\"\" Input to this function is a strin...", "qwen_time": 7.613, "neuroglyph_time": 7.196, "qwen_success": true, "neuroglyph_success": true, "semantic_score": 0.295, "correctness_score": 0.551, "qwen_response_preview": "```python\nfrom typing import List\n\ndef separate_paren_groups(paren_string: str) -> List[str]:\n    # Remove spaces from the input string\n    paren_string = paren_string.replace(\" \", \"\")\n    \n    # Init...", "neuroglyph_response_preview": "To solve this problem, we can use a stack-based approach to keep track of the parentheses and their nesting levels. Here's how you can implement it in Python:\n\n```python\ndef separate_paren_groups(pare..."}]}