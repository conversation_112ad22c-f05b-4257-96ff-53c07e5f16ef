{"timestamp": "20250526_134846", "config": {"model": "qwen2.5-coder:1.5b", "num_samples": 5, "max_tokens": 512, "temperature": 0.1}, "execution_time_seconds": 67.81, "aggregate_statistics": {"total_tests": 5, "success_rates": {"qwen_standalone": 100.0, "neuroglyph_enhanced": 100.0}, "average_response_times": {"qwen_standalone": 6.619, "neuroglyph_enhanced": 6.941, "time_improvement_percent": -4.9}, "quality_metrics": {"average_semantic_similarity": 0.493, "average_correctness_score": 0.499, "combined_quality_score": 0.496}, "performance_summary": {"neuroglyph_advantage": false, "faster_response": false, "quality_threshold_met": false}}, "detailed_results": [{"test_id": "HumanEval/0", "prompt_preview": "from typing import List\n\n\ndef has_close_elements(numbers: List[float], threshold: float) -> bool:\n  ...", "qwen_time": 6.306, "neuroglyph_time": 8.095, "qwen_success": true, "neuroglyph_success": true, "semantic_score": 0.511, "correctness_score": 0.533, "qwen_response_preview": "To solve this problem, we can use a simple approach to iterate through the list and check if any two consecutive numbers are closer than the given threshold. Here's how you can implement it:\n\n```pytho...", "neuroglyph_response_preview": "To solve this problem using logical symbols and structured thinking, we need to determine if any two elements in the given list are closer than a specified threshold. Here's how we can approach it:\n\n1..."}, {"test_id": "HumanEval/1", "prompt_preview": "from typing import List\n\n\ndef separate_paren_groups(paren_string: str) -> List[str]:\n    \"\"\" Input t...", "qwen_time": 7.937, "neuroglyph_time": 7.999, "qwen_success": true, "neuroglyph_success": true, "semantic_score": 0.454, "correctness_score": 0.539, "qwen_response_preview": "To solve this problem, we can use a stack to keep track of the opening parentheses. When we encounter a closing parenthesis, we pop from the stack until we find a matching opening parenthesis. This wa...", "neuroglyph_response_preview": "To solve this problem, we can use a stack-based approach. The idea is to iterate through each character of the input string and keep track of the nesting level using a stack. When we encounter an open..."}, {"test_id": "HumanEval/2", "prompt_preview": "\n\ndef truncate_number(number: float) -> float:\n    \"\"\" Given a positive floating point number, it ca...", "qwen_time": 6.447, "neuroglyph_time": 5.923, "qwen_success": true, "neuroglyph_success": true, "semantic_score": 0.613, "correctness_score": 0.321, "qwen_response_preview": "To solve this problem, we can use Python's built-in `math` module to get the integer part of the number and then subtract it from the original number to obtain the decimal part. Here's how you can imp...", "neuroglyph_response_preview": "To solve this problem, we can use Python's built-in `math` module to handle floating-point arithmetic and extract the decimal part of a given positive number. Here's how you can implement the `truncat..."}, {"test_id": "HumanEval/3", "prompt_preview": "from typing import List\n\n\ndef below_zero(operations: List[int]) -> bool:\n    \"\"\" You're given a list...", "qwen_time": 6.081, "neuroglyph_time": 4.831, "qwen_success": true, "neuroglyph_success": true, "semantic_score": 0.469, "correctness_score": 0.557, "qwen_response_preview": "To solve this problem, we need to iterate through the list of operations and keep track of the current balance. If at any point the balance falls below zero, we should return `True`. Otherwise, after ...", "neuroglyph_response_preview": "To solve this problem using logical symbols and structured thinking, we can use a simple algorithm that iterates through the list of operations and checks if the balance falls below zero at any point...."}, {"test_id": "HumanEval/4", "prompt_preview": "from typing import List\n\n\ndef mean_absolute_deviation(numbers: List[float]) -> float:\n    \"\"\" For a ...", "qwen_time": 6.324, "neuroglyph_time": 7.856, "qwen_success": true, "neuroglyph_success": true, "semantic_score": 0.419, "correctness_score": 0.548, "qwen_response_preview": "Certainly! Below is the implementation of the `mean_absolute_deviation` function in Python:\n\n```python\nfrom typing import List\n\ndef mean_absolute_deviation(numbers: List[float]) -> float:\n    \"\"\" For ...", "neuroglyph_response_preview": "To solve the problem of calculating Mean Absolute Deviation (MAD) using logical symbols and structured thinking, we can follow these steps:\n\n1. **Define the input**: We have a list of numbers.\n2. **Ca..."}]}