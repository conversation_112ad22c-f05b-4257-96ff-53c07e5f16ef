{"summary": {"total_tests": 5, "average_overhead": 0.322, "average_quality": 0.5, "success_rate": 0, "main_issues": ["Overhead temporale medio: +9.3%", "Score qualità bassi: 0.496/1.0", "Mapping simboli inefficaci", "<PERSON><PERSON><PERSON> reasoning strutturato"]}, "detailed_analysis": {"time_overhead": {"total_tests": 5, "overhead_per_test": [{"test_id": "HumanEval/0", "qwen_time": 6.306, "neuroglyph_time": 8.095, "overhead_seconds": 1.789, "overhead_percentage": 28.4}, {"test_id": "HumanEval/1", "qwen_time": 7.937, "neuroglyph_time": 7.999, "overhead_seconds": 0.062, "overhead_percentage": 0.8}, {"test_id": "HumanEval/2", "qwen_time": 6.447, "neuroglyph_time": 5.923, "overhead_seconds": -0.524, "overhead_percentage": -8.1}, {"test_id": "HumanEval/3", "qwen_time": 6.081, "neuroglyph_time": 4.831, "overhead_seconds": -1.25, "overhead_percentage": -20.6}, {"test_id": "HumanEval/4", "qwen_time": 6.324, "neuroglyph_time": 7.856, "overhead_seconds": 1.532, "overhead_percentage": 24.2}], "average_overhead": 0.322, "overhead_percentage": 4.9, "fastest_neuroglyph": {"test_id": "HumanEval/3", "qwen_time": 6.081, "neuroglyph_time": 4.831, "overhead_seconds": -1.25, "overhead_percentage": -20.6}, "slowest_neuroglyph": {"test_id": "HumanEval/0", "qwen_time": 6.306, "neuroglyph_time": 8.095, "overhead_seconds": 1.789, "overhead_percentage": 28.4}}, "quality_gap": {"semantic_scores": [0.511, 0.454, 0.613, 0.469, 0.419], "correctness_scores": [0.533, 0.539, 0.321, 0.557, 0.548], "quality_improvements": [{"test_id": "HumanEval/0", "semantic_score": 0.511, "correctness_score": 0.533}, {"test_id": "HumanEval/2", "semantic_score": 0.613, "correctness_score": 0.321}], "quality_degradations": [{"test_id": "HumanEval/1", "semantic_score": 0.454, "correctness_score": 0.539}, {"test_id": "HumanEval/3", "semantic_score": 0.469, "correctness_score": 0.557}, {"test_id": "HumanEval/4", "semantic_score": 0.419, "correctness_score": 0.548}], "average_semantic": 0.493, "average_correctness": 0.5}, "symbol_mapping": {"total_mappings": 27, "mapping_categories": {"control_flow": ["if", "else", "for", "while"], "data_types": ["list", "List", "float", "int", "str", "bool"], "operators": ["and", "or", "not", "==", "!=", "<=", ">="], "functions": ["def ", "return", "abs", "len", "range", "enumerate"], "literals": ["True", "False"]}, "potential_issues": ["Mapping troppo semplice: sostituzioni 1:1 senza contesto", "Simboli Unicode potrebbero confondere il modello", "Mancano mapping per costrutti Python avanzati", "Nessuna validazione semantica dei mapping", "Prefisso generico invece di istruzioni specifiche"], "improvement_suggestions": ["Usare mapping contestuali invece di sostituzioni globali", "Aggiungere istruzioni specifiche per ogni tipo di problema", "Implementare chain-of-thought reasoning", "Usare simboli più familiari al modello", "Aggiungere esempi di codice simbolico"]}, "failure_patterns": {"low_performance_tests": [{"test_id": "HumanEval/2", "semantic_score": 0.613, "correctness_score": 0.321, "issue": "Low quality scores"}], "high_overhead_tests": [{"test_id": "HumanEval/0", "overhead_percentage": 28.4, "issue": "High time overhead"}, {"test_id": "HumanEval/4", "overhead_percentage": 24.2, "issue": "High time overhead"}], "common_issues": ["Score qualità bassi: 1/5 test", "Overhead temporale alto: 2/5 test", "Pattern successo rari: 0/5 test", "Mapping simboli non ottimizzati per Qwen", "Mancanza di reasoning strutturato"], "success_patterns": []}}, "recommendations": ["🔧 MIGLIORAMENTI IMMEDIATI:", "1. Sostituire mapping simboli 1:1 con istruzioni contestuali", "2. Aggiungere chain-of-thought reasoning esplicito", "3. Usare esempi di codice invece di simboli astratti", "4. Implementare prompt engineering specifico per Qwen", "", "🎯 OTTIMIZZAZIONI AVANZATE:", "5. <PERSON><PERSON>re dataset di training simbolico per Qwen", "6. Implementare feedback loop per miglioramento continuo", "7. Aggiungere validazione semantica dei mapping", "8. S<PERSON>uppare metriche di qualità più sofisticate", "", "🚀 STRATEGIE LONG-TERM:", "9. Fine-tuning di Qwen su linguaggio simbolico NEUROGLYPH", "10. Integrazione con SOCRATE engine per reasoning logico", "11. <PERSON><PERSON><PERSON><PERSON> di embedding simbolici custom", "12. Implementazione di multi-step reasoning"]}