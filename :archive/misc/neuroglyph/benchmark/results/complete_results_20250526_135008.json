{"humaneval": {"dataset": "HumanEval", "num_tests": 5, "timestamp": "20250526_135008", "config": {"model": "qwen2.5-coder:1.5b", "datasets": ["humaneval"], "num_samples": 5, "temperature": 0.1, "max_tokens": 512, "use_advanced_evaluation": true, "save_detailed_results": true}, "aggregate_statistics": {"total_tests": 5, "performance_comparison": {"neuroglyph_outperforms_qwen": true, "neuroglyph_faster": false, "quality_threshold_met": false, "overall_success": false}, "quality_metrics": {"qwen_avg_score": 0.007, "neuroglyph_avg_score": 0.01, "neuroglyph_improvement": 0.003}, "time_analysis": {"qwen_avg_time": 12.756, "neuroglyph_avg_time": 13.937, "time_improvement_percent": -9.3}, "success_rates": {"qwen_execution_success": 0.0, "neuroglyph_execution_success": 0.0}}, "detailed_results": [{"task_id": "HumanEval/0", "prompt_preview": "from typing import List\n\n\ndef has_close_elements(numbers: List[float], threshold: float) -> bool:\n  ...", "benchmark_metrics": {"qwen_time": 6.209427833557129, "neuroglyph_time": 7.523303270339966, "qwen_success": true, "neuroglyph_success": true, "semantic_similarity": 0.5297297297297298}, "advanced_evaluation": {"qwen": {"code_bleu": 0.0, "ast_similarity": 0.0, "execution_success": false, "overall_score": 0.008}, "neuroglyph": {"code_bleu": 0.021, "ast_similarity": 0.0, "execution_success": false, "overall_score": 0.016}}}, {"task_id": "HumanEval/32", "prompt_preview": "import math\n\n\ndef poly(xs: list, x: float):\n    \"\"\"\n    Evaluates polynomial with coefficients xs at...", "benchmark_metrics": {"qwen_time": 9.133002996444702, "neuroglyph_time": 9.257163047790527, "qwen_success": true, "neuroglyph_success": true, "semantic_similarity": 0.21224489795918366}, "advanced_evaluation": {"qwen": {"code_bleu": 0.0, "ast_similarity": 0.0, "execution_success": false, "overall_score": 0.007}, "neuroglyph": {"code_bleu": 0.0, "ast_similarity": 0.0, "execution_success": false, "overall_score": 0.005}}}, {"task_id": "HumanEval/64", "prompt_preview": "\nFIX = \"\"\"\nAdd more test cases.\n\"\"\"\n\ndef vowels_count(s):\n    \"\"\"Write a function vowels_count which...", "benchmark_metrics": {"qwen_time": 6.8329918384552, "neuroglyph_time": 15.788341760635376, "qwen_success": true, "neuroglyph_success": true, "semantic_similarity": 0.2804232804232804}, "advanced_evaluation": {"qwen": {"code_bleu": 0.0, "ast_similarity": 0.0, "execution_success": false, "overall_score": 0.007}, "neuroglyph": {"code_bleu": 0.015, "ast_similarity": 0.0, "execution_success": false, "overall_score": 0.01}}}, {"task_id": "HumanEval/96", "prompt_preview": "\ndef count_up_to(n):\n    \"\"\"Implement a function that takes an non-negative integer and returns an a...", "benchmark_metrics": {"qwen_time": 24.787539958953857, "neuroglyph_time": 21.535687923431396, "qwen_success": true, "neuroglyph_success": true, "semantic_similarity": 0.36936936936936937}, "advanced_evaluation": {"qwen": {"code_bleu": 0.016, "ast_similarity": 0.0, "execution_success": false, "overall_score": 0.01}, "neuroglyph": {"code_bleu": 0.021, "ast_similarity": 0.0, "execution_success": false, "overall_score": 0.016}}}, {"task_id": "HumanEval/128", "prompt_preview": "\ndef prod_signs(arr):\n    \"\"\"\n    You are given an array arr of integers and you need to return\n    ...", "benchmark_metrics": {"qwen_time": 16.816967725753784, "neuroglyph_time": 15.582206964492798, "qwen_success": true, "neuroglyph_success": true, "semantic_similarity": 0.5443786982248521}, "advanced_evaluation": {"qwen": {"code_bleu": 0.0, "ast_similarity": 0.0, "execution_success": false, "overall_score": 0.003}, "neuroglyph": {"code_bleu": 0.0, "ast_similarity": 0.0, "execution_success": false, "overall_score": 0.004}}}]}}