{"test_timestamp": "20250525_202743", "neuroglyph_results": [{"test_id": "INFERENCE_001", "description": "Simple function with neuroglyphs", "prompt": "Implement this function: ⚡ ƒ(📋) → Σ(even_numbers)", "response": "def sum_even_numbers(numbers):\n    return sum(x for x in numbers if x % 2 == 0)", "inference_time": 0.105, "token_count": 14, "quality_metrics": {"contains_expected_keywords": true, "syntactically_correct": true, "functionally_complete": true, "code_length": 79, "readability_score": 20, "overall_quality": 100}, "complexity": "basic"}, {"test_id": "INFERENCE_002", "description": "Loop with condition", "prompt": "Write code for: 🔄(i=0; i<n; i++) ❓(arr[i]>threshold) → result.append(arr[i])", "response": "for i in range(n):\n    if arr[i] > threshold:\n        result.append(arr[i])", "inference_time": 0.1, "token_count": 9, "quality_metrics": {"contains_expected_keywords": true, "syntactically_correct": true, "functionally_complete": false, "code_length": 75, "readability_score": 30, "overall_quality": 60}, "complexity": "intermediate"}, {"test_id": "INFERENCE_003", "description": "Class definition", "prompt": "Create: 🏗️ Calculator(+, -, *, /) → {add(), sub(), mul(), div()}", "response": "class Calculator:\n    def add(self, a, b):\n        return a + b\n    def sub(self, a, b):\n        return a - b\n    def mul(self, a, b):\n        return a * b\n    def div(self, a, b):\n        return a / b if b != 0 else None", "inference_time": 0.105, "token_count": 40, "quality_metrics": {"contains_expected_keywords": true, "syntactically_correct": true, "functionally_complete": true, "code_length": 221, "readability_score": 90, "overall_quality": 100}, "complexity": "intermediate"}], "baseline_results": [{"test_id": "BASELINE_001", "description": "Simple function baseline", "prompt": "Implement a function that takes a list and returns the sum of all even numbers", "response": "def sum_even_numbers(numbers):\n    total = 0\n    for num in numbers:\n        if num % 2 == 0:\n            total += num\n    return total", "inference_time": 0.104, "token_count": 20, "quality_metrics": {"contains_expected_keywords": true, "syntactically_correct": true, "functionally_complete": true, "code_length": 135, "readability_score": 60, "overall_quality": 100}, "complexity": "basic"}, {"test_id": "BASELINE_002", "description": "Loop with condition baseline", "prompt": "Write code for a loop from 0 to n that appends array elements greater than threshold to result", "response": "result = []\nfor i in range(n):\n    if arr[i] > threshold:\n        result.append(arr[i])", "inference_time": 0.105, "token_count": 12, "quality_metrics": {"contains_expected_keywords": true, "syntactically_correct": true, "functionally_complete": false, "code_length": 87, "readability_score": 40, "overall_quality": 60}, "complexity": "intermediate"}, {"test_id": "BASELINE_003", "description": "Class definition baseline", "prompt": "Create a Calculator class with methods for addition, subtraction, multiplication and division", "response": "class Calculator:\n    def __init__(self):\n        pass\n    \n    def addition(self, a, b):\n        return a + b\n    \n    def subtraction(self, a, b):\n        return a - b\n    \n    def multiplication(self, a, b):\n        return a * b\n    \n    def division(self, a, b):\n        if b != 0:\n            return a / b\n        else:\n            return \"Error: Division by zero\" ", "inference_time": 0.105, "token_count": 47, "quality_metrics": {"contains_expected_keywords": true, "syntactically_correct": true, "functionally_complete": true, "code_length": 370, "readability_score": 100, "overall_quality": 100}, "complexity": "intermediate"}], "comparison_metrics": {"neuroglyph_metrics": {"avg_inference_time": 0.103, "avg_quality_score": 86.7, "avg_response_tokens": 21.0}, "baseline_metrics": {"avg_inference_time": 0.105, "avg_quality_score": 86.7, "avg_response_tokens": 26.3}, "performance_comparison": {"time_improvement": 1.3, "quality_improvement": 0.0, "token_efficiency": 20.3}, "neuroglyph_advantage": {"faster_inference": true, "better_quality": false, "more_efficient": true}}}