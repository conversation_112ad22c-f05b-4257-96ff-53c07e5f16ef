{"benchmark_timestamp": "20250525_210726", "execution_time": 0.0, "codegen_results": {"total_tests": 5, "avg_compression_ratio": 3.06, "token_efficiency": 206.0, "semantic_accuracy": 91.8, "humaneval_results": [{"id": "HE001", "description": "List comprehension with filter", "verbose_tokens": 23, "neuroglyph_tokens": 7, "compression_ratio": 3.29, "expected_compression": 3.2, "semantic_accuracy": 95.0, "complexity": "basic"}, {"id": "HE002", "description": "Recursive function with base case", "verbose_tokens": 18, "neuroglyph_tokens": 8, "compression_ratio": 2.25, "expected_compression": 2.8, "semantic_accuracy": 95.0, "complexity": "intermediate"}, {"id": "HE003", "description": "Class with inheritance and polymorphism", "verbose_tokens": 19, "neuroglyph_tokens": 5, "compression_ratio": 3.8, "expected_compression": 4.1, "semantic_accuracy": 95.0, "complexity": "advanced"}], "mbpp_results": [{"id": "MB001", "description": "String manipulation with conditions", "verbose_tokens": 17, "neuroglyph_tokens": 4, "compression_ratio": 4.25, "expected_compression": 2.5, "semantic_accuracy": 92.0, "complexity": "basic"}, {"id": "MB002", "description": "Dictionary operations with nested loops", "verbose_tokens": 12, "neuroglyph_tokens": 7, "compression_ratio": 1.71, "expected_compression": 3.7, "semantic_accuracy": 82.0, "complexity": "intermediate"}]}, "reasoning_results": {"multi_hop_tests": 2, "concept_composition_tests": 1, "avg_reasoning_depth": 5.0, "reasoning_accuracy": 100.0, "modularity_score": 1.0, "detailed_results": [{"id": "MH001", "type": "multi_hop", "description": "5-step algorithm decomposition", "reasoning_depth": 5, "is_correct": true, "modularity_score": 1.0, "complexity": "intermediate"}, {"id": "MH002", "type": "multi_hop", "description": "Memory allocation trace", "reasoning_depth": 5, "is_correct": true, "modularity_score": 1.0, "complexity": "advanced"}, {"id": "CC001", "type": "concept_composition", "description": "Compose iterator + filter + map", "concepts": ["⟲ iterator", "🔍 filter", "🗺️ map"], "composition_success": true, "modularity_score": 1.0, "complexity": "intermediate"}]}, "reversibility_results": {"symbol_to_code_tests": 2, "code_to_symbol_tests": 1, "debug_tracing_tests": 1, "avg_reversibility_score": 0.95, "debug_accuracy": 100.0, "bidirectional_fidelity": 97.5, "detailed_results": [{"id": "S2C001", "type": "symbol_to_code", "reversibility_score": 1.0, "conversion_success": true}, {"id": "S2C002", "type": "symbol_to_code", "reversibility_score": 0.9, "conversion_success": true}, {"id": "C2S001", "type": "code_to_symbol", "compression_ratio": 4.2, "reverse_success": true}, {"id": "DT001", "type": "debug_tracing", "trace_depth": 5, "debug_success": true}]}, "overall_results": {"overall_codegen_ultra_score": 90.0, "axis_scores": {"codegen_simbolico": 76.5, "reasoning_modulare": 100.0, "reversibilita_debug": 97.5}, "weights": {"codegen_simbolico": 0.4, "reasoning_modulare": 0.35, "reversibilita_debug": 0.25}, "readiness_for_llm": true, "world_class_potential": false}}