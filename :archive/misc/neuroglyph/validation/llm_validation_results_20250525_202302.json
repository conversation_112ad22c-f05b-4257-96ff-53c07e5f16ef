{"validation_timestamp": "20250525_202302", "registry_hash": "9d4457879acee31b73b0773bd43fa9d57558ea1f269abeec9c5d164defb180ab", "total_symbols": 1790, "llm_quality_score": 69.5, "tokenization_stats": {"total_symbols": 1790, "single_token_symbols": 1735, "multi_token_symbols": 55, "problematic_symbols": [{"id": "NG0012", "symbol": "⊨", "fallback": "[ENTAILS]", "fallback_length": 9, "reason": "Long fallback"}, {"id": "NG0022", "symbol": "■", "fallback": "[POINTER]", "fallback_length": 9, "reason": "Long fallback"}, {"id": "NG0024", "symbol": "≺", "fallback": "[IMPLIES]", "fallback_length": 9, "reason": "Long fallback"}, {"id": "NG0028", "symbol": "⊬", "fallback": "[FUNCTION1]", "fallback_length": 11, "reason": "Long fallback"}, {"id": "NG0034", "symbol": "⇣", "fallback": "[PROPERTY]", "fallback_length": 10, "reason": "Long fallback"}, {"id": "NG0035", "symbol": "◡", "fallback": "[RETURN1]", "fallback_length": 9, "reason": "Long fallback"}, {"id": "NG0042", "symbol": "↤", "fallback": "[FUNCTION2]", "fallback_length": 11, "reason": "Long fallback"}, {"id": "NG0043", "symbol": "↰", "fallback": "[PROPERTY1]", "fallback_length": 11, "reason": "Long fallback"}, {"id": "NG0044", "symbol": "⨀", "fallback": "[POINTER1]", "fallback_length": 10, "reason": "Long fallback"}, {"id": "NG0047", "symbol": "⌁", "fallback": "[IMPLIES1]", "fallback_length": 10, "reason": "Long fallback"}, {"id": "NG0056", "symbol": "◧", "fallback": "[POINTER2]", "fallback_length": 10, "reason": "Long fallback"}, {"id": "NG0060", "symbol": "⊠", "fallback": "[IMPLIES2]", "fallback_length": 10, "reason": "Long fallback"}, {"id": "NG0063", "symbol": "⋨", "fallback": "[IMPLIES3]", "fallback_length": 10, "reason": "Long fallback"}, {"id": "NG0064", "symbol": "⚮", "fallback": "[PROPERTY2]", "fallback_length": 11, "reason": "Long fallback"}, {"id": "NG0069", "symbol": "∅", "fallback": "[POINTER3]", "fallback_length": 10, "reason": "Long fallback"}, {"id": "NG0078", "symbol": "⬂", "fallback": "[PROPERTY3]", "fallback_length": 11, "reason": "Long fallback"}, {"id": "NG0079", "symbol": "⇦", "fallback": "[PROPERTY4]", "fallback_length": 11, "reason": "Long fallback"}, {"id": "NG0082", "symbol": "◸", "fallback": "[POINTER4]", "fallback_length": 10, "reason": "Long fallback"}, {"id": "NG0087", "symbol": "⇟", "fallback": "[FUNCTION3]", "fallback_length": 11, "reason": "Long fallback"}, {"id": "NG0089", "symbol": "∿", "fallback": "[IMPLIES4]", "fallback_length": 10, "reason": "Long fallback"}, {"id": "NG0092", "symbol": "⤃", "fallback": "[METHOD1]", "fallback_length": 9, "reason": "Long fallback"}, {"id": "NG0093", "symbol": "⦃", "fallback": "[PROPERTY5]", "fallback_length": 11, "reason": "Long fallback"}, {"id": "NG0100", "symbol": "⋁", "fallback": "[POINTER5]", "fallback_length": 10, "reason": "Long fallback"}, {"id": "NG0107", "symbol": "⇅", "fallback": "[FUNCTION4]", "fallback_length": 11, "reason": "Long fallback"}, {"id": "NG0108", "symbol": "∯", "fallback": "[IMPLIES5]", "fallback_length": 10, "reason": "Long fallback"}, {"id": "NG0110", "symbol": "◒", "fallback": "[POINTER6]", "fallback_length": 10, "reason": "Long fallback"}, {"id": "NG0114", "symbol": "↹", "fallback": "[PROPERTY6]", "fallback_length": 11, "reason": "Long fallback"}, {"id": "NG0115", "symbol": "✄", "fallback": "[METHOD2]", "fallback_length": 9, "reason": "Long fallback"}, {"id": "NG0117", "symbol": "≁", "fallback": "[IMPLIES6]", "fallback_length": 10, "reason": "Long fallback"}, {"id": "NG0119", "symbol": "▲", "fallback": "[METHOD3]", "fallback_length": 9, "reason": "Long fallback"}, {"id": "NG0120", "symbol": "◹", "fallback": "[IMPLIES7]", "fallback_length": 10, "reason": "Long fallback"}, {"id": "NG0123", "symbol": "▫", "fallback": "[PROPERTY7]", "fallback_length": 11, "reason": "Long fallback"}, {"id": "NG0125", "symbol": "◰", "fallback": "[IMPLIES8]", "fallback_length": 10, "reason": "Long fallback"}, {"id": "NG0126", "symbol": "◵", "fallback": "[POINTER7]", "fallback_length": 10, "reason": "Long fallback"}, {"id": "NG0128", "symbol": "⇪", "fallback": "[IMPLIES9]", "fallback_length": 10, "reason": "Long fallback"}, {"id": "NG0129", "symbol": "∰", "fallback": "[FUNCTION5]", "fallback_length": 11, "reason": "Long fallback"}, {"id": "NG0141", "symbol": "⋊", "fallback": "[FUNCTION6]", "fallback_length": 11, "reason": "Long fallback"}, {"id": "NG0143", "symbol": "◪", "fallback": "[RETURN2]", "fallback_length": 9, "reason": "Long fallback"}, {"id": "NG0154", "symbol": "◞", "fallback": "[POINTER8]", "fallback_length": 10, "reason": "Long fallback"}, {"id": "NG0157", "symbol": "◮", "fallback": "[POINTER9]", "fallback_length": 10, "reason": "Long fallback"}, {"id": "NG0162", "symbol": "⚢", "fallback": "[METHOD4]", "fallback_length": 9, "reason": "Long fallback"}, {"id": "NG0165", "symbol": "◃", "fallback": "[RETURN3]", "fallback_length": 9, "reason": "Long fallback"}, {"id": "NG0170", "symbol": "⬆", "fallback": "[FUNCTION7]", "fallback_length": 11, "reason": "Long fallback"}, {"id": "NG0173", "symbol": "⊀", "fallback": "[FUNCTION8]", "fallback_length": 11, "reason": "Long fallback"}, {"id": "NG0174", "symbol": "◦", "fallback": "[RETURN4]", "fallback_length": 9, "reason": "Long fallback"}, {"id": "NG0178", "symbol": "↩", "fallback": "[POINTER10]", "fallback_length": 11, "reason": "Long fallback"}, {"id": "NG0179", "symbol": "↗", "fallback": "[WHILE10]", "fallback_length": 9, "reason": "Long fallback"}, {"id": "NG0180", "symbol": "⌇", "fallback": "[IMPLIES10]", "fallback_length": 11, "reason": "Long fallback"}, {"id": "NG0181", "symbol": "✇", "fallback": "[METHOD5]", "fallback_length": 9, "reason": "Long fallback"}, {"id": "NG0185", "symbol": "⦇", "fallback": "[PROPERTY8]", "fallback_length": 11, "reason": "Long fallback"}, {"id": "NG0186", "symbol": "◐", "fallback": "[IMPLIES11]", "fallback_length": 11, "reason": "Long fallback"}, {"id": "NG0187", "symbol": "⨇", "fallback": "[WHILE11]", "fallback_length": 9, "reason": "Long fallback"}, {"id": "NG0191", "symbol": "▸", "fallback": "[DEREF10]", "fallback_length": 9, "reason": "Long fallback"}, {"id": "NG0192", "symbol": "≏", "fallback": "[PROPERTY9]", "fallback_length": 11, "reason": "Long fallback"}, {"id": "NG0195", "symbol": "⇚", "fallback": "[PROPERTY10]", "fallback_length": 12, "reason": "Long fallback"}, {"id": "NG0201", "symbol": "∶", "fallback": "[FUNCTION9]", "fallback_length": 11, "reason": "Long fallback"}, {"id": "NG0206", "symbol": "⊙", "fallback": "[METHOD6]", "fallback_length": 9, "reason": "Long fallback"}, {"id": "NG0208", "symbol": "⇢", "fallback": "[POINTER11]", "fallback_length": 11, "reason": "Long fallback"}, {"id": "NG0212", "symbol": "◚", "fallback": "[FUNCTION10]", "fallback_length": 12, "reason": "Long fallback"}, {"id": "NG0216", "symbol": "↖", "fallback": "[IMPLIES12]", "fallback_length": 11, "reason": "Long fallback"}, {"id": "NG0217", "symbol": "⋎", "fallback": "[PROPERTY11]", "fallback_length": 12, "reason": "Long fallback"}, {"id": "NG0219", "symbol": "⇙", "fallback": "[RETURN5]", "fallback_length": 9, "reason": "Long fallback"}, {"id": "NG0220", "symbol": "▧", "fallback": "[FUNCTION11]", "fallback_length": 12, "reason": "Long fallback"}, {"id": "NG0221", "symbol": "⌉", "fallback": "[WHILE12]", "fallback_length": 9, "reason": "Long fallback"}, {"id": "NG0223", "symbol": "◽", "fallback": "[IMPLIES13]", "fallback_length": 11, "reason": "Long fallback"}, {"id": "NG0224", "symbol": "▼", "fallback": "[DEREF11]", "fallback_length": 9, "reason": "Long fallback"}, {"id": "NG0226", "symbol": "▿", "fallback": "[FUNCTION12]", "fallback_length": 12, "reason": "Long fallback"}, {"id": "NG0228", "symbol": "⦉", "fallback": "[PROPERTY12]", "fallback_length": 12, "reason": "Long fallback"}, {"id": "NG0230", "symbol": "⊹", "fallback": "[METHOD7]", "fallback_length": 9, "reason": "Long fallback"}, {"id": "NG0232", "symbol": "⇤", "fallback": "[RETURN6]", "fallback_length": 9, "reason": "Long fallback"}, {"id": "NG0246", "symbol": "◑", "fallback": "[PROPERTY13]", "fallback_length": 12, "reason": "Long fallback"}, {"id": "NG0248", "symbol": "◘", "fallback": "[RETURN7]", "fallback_length": 9, "reason": "Long fallback"}, {"id": "NG0249", "symbol": "⋜", "fallback": "[POINTER12]", "fallback_length": 11, "reason": "Long fallback"}, {"id": "NG0250", "symbol": "∋", "fallback": "[FUNCTION13]", "fallback_length": 12, "reason": "Long fallback"}, {"id": "NG0252", "symbol": "↝", "fallback": "[IMPLIES14]", "fallback_length": 11, "reason": "Long fallback"}, {"id": "NG0253", "symbol": "⬊", "fallback": "[ALLOC10]", "fallback_length": 9, "reason": "Long fallback"}, {"id": "NG0265", "symbol": "↠", "fallback": "[WHILE13]", "fallback_length": 9, "reason": "Long fallback"}, {"id": "NG0268", "symbol": "≄", "fallback": "[PROPERTY14]", "fallback_length": 12, "reason": "Long fallback"}, {"id": "NG0278", "symbol": "⇝", "fallback": "[PROPERTY15]", "fallback_length": 12, "reason": "Long fallback"}, {"id": "NG0280", "symbol": "≓", "fallback": "[RETURN8]", "fallback_length": 9, "reason": "Long fallback"}, {"id": "NG0283", "symbol": "◍", "fallback": "[FUNCTION14]", "fallback_length": 12, "reason": "Long fallback"}, {"id": "NG0289", "symbol": "⊻", "fallback": "[FUNCTION15]", "fallback_length": 12, "reason": "Long fallback"}, {"id": "NG0297", "symbol": "⇬", "fallback": "[RETURN9]", "fallback_length": 9, "reason": "Long fallback"}, {"id": "NG0299", "symbol": "⚥", "fallback": "[WHILE14]", "fallback_length": 9, "reason": "Long fallback"}, {"id": "NG0300", "symbol": "→", "fallback": "[IMPLIES15]", "fallback_length": 11, "reason": "Long fallback"}, {"id": "NG0302", "symbol": "⦌", "fallback": "[ALLOC11]", "fallback_length": 9, "reason": "Long fallback"}, {"id": "NG0304", "symbol": "⇼", "fallback": "[WHILE15]", "fallback_length": 9, "reason": "Long fallback"}, {"id": "NG0306", "symbol": "⨌", "fallback": "[ALLOC12]", "fallback_length": 9, "reason": "Long fallback"}, {"id": "NG0310", "symbol": "↶", "fallback": "[PROPERTY16]", "fallback_length": 12, "reason": "Long fallback"}, {"id": "NG0312", "symbol": "◥", "fallback": "[DEREF12]", "fallback_length": 9, "reason": "Long fallback"}, {"id": "NG0313", "symbol": "⬌", "fallback": "[FUNCTION16]", "fallback_length": 12, "reason": "Long fallback"}, {"id": "NG0314", "symbol": "⇺", "fallback": "[CLASS10]", "fallback_length": 9, "reason": "Long fallback"}, {"id": "NG0318", "symbol": "⋈", "fallback": "[PROPERTY17]", "fallback_length": 12, "reason": "Long fallback"}, {"id": "NG0319", "symbol": "⊾", "fallback": "[PROPERTY18]", "fallback_length": 12, "reason": "Long fallback"}, {"id": "NG0325", "symbol": "∑", "fallback": "[FUNCTION17]", "fallback_length": 12, "reason": "Long fallback"}, {"id": "NG0327", "symbol": "⋮", "fallback": "[WHILE16]", "fallback_length": 9, "reason": "Long fallback"}, {"id": "NG0331", "symbol": "⊆", "fallback": "[FUNCTION18]", "fallback_length": 12, "reason": "Long fallback"}, {"id": "NG0335", "symbol": "⇉", "fallback": "[DEREF13]", "fallback_length": 9, "reason": "Long fallback"}, {"id": "NG0336", "symbol": "⋄", "fallback": "[RETURN10]", "fallback_length": 10, "reason": "Long fallback"}, {"id": "NG0341", "symbol": "≱", "fallback": "[RETURN11]", "fallback_length": 10, "reason": "Long fallback"}, {"id": "NG0342", "symbol": "◤", "fallback": "[ALLOC13]", "fallback_length": 9, "reason": "Long fallback"}, {"id": "NG0343", "symbol": "⋇", "fallback": "[RETURN12]", "fallback_length": 10, "reason": "Long fallback"}, {"id": "NG0346", "symbol": "⬏", "fallback": "[IMPLIES16]", "fallback_length": 11, "reason": "Long fallback"}, {"id": "NG0347", "symbol": "◀", "fallback": "[WHILE17]", "fallback_length": 9, "reason": "Long fallback"}, {"id": "NG0352", "symbol": "✐", "fallback": "[IMPLIES17]", "fallback_length": 11, "reason": "Long fallback"}, {"id": "NG0353", "symbol": "⬐", "fallback": "[DEREF14]", "fallback_length": 9, "reason": "Long fallback"}, {"id": "NG0354", "symbol": "⌑", "fallback": "[IMPLIES18]", "fallback_length": 11, "reason": "Long fallback"}, {"id": "NG0356", "symbol": "⊅", "fallback": "[ALLOC14]", "fallback_length": 9, "reason": "Long fallback"}, {"id": "NG0360", "symbol": "▾", "fallback": "[ALLOC15]", "fallback_length": 9, "reason": "Long fallback"}, {"id": "NG0361", "symbol": "∐", "fallback": "[IMPLIES19]", "fallback_length": 11, "reason": "Long fallback"}, {"id": "NG0362", "symbol": "⋿", "fallback": "[CLASS11]", "fallback_length": 9, "reason": "Long fallback"}, {"id": "NG0365", "symbol": "⦒", "fallback": "[CLASS12]", "fallback_length": 9, "reason": "Long fallback"}, {"id": "NG0368", "symbol": "◝", "fallback": "[POINTER13]", "fallback_length": 11, "reason": "Long fallback"}, {"id": "NG0370", "symbol": "✓", "fallback": "[CLASS13]", "fallback_length": 9, "reason": "Long fallback"}, {"id": "NG0372", "symbol": "⋱", "fallback": "[RETURN13]", "fallback_length": 10, "reason": "Long fallback"}, {"id": "NG0373", "symbol": "↫", "fallback": "[CLASS14]", "fallback_length": 9, "reason": "Long fallback"}, {"id": "NG0379", "symbol": "⊝", "fallback": "[IMPLIES20]", "fallback_length": 11, "reason": "Long fallback"}, {"id": "NG0382", "symbol": "∕", "fallback": "[ALLOC16]", "fallback_length": 9, "reason": "Long fallback"}, {"id": "NG0383", "symbol": "∎", "fallback": "[METHOD8]", "fallback_length": 9, "reason": "Long fallback"}, {"id": "NG0384", "symbol": "∪", "fallback": "[CLASS15]", "fallback_length": 9, "reason": "Long fallback"}, {"id": "NG0385", "symbol": "⚯", "fallback": "[METHOD9]", "fallback_length": 9, "reason": "Long fallback"}, {"id": "NG0387", "symbol": "⊷", "fallback": "[FUNCTION19]", "fallback_length": 12, "reason": "Long fallback"}, {"id": "NG0388", "symbol": "✕", "fallback": "[ALLOC17]", "fallback_length": 9, "reason": "Long fallback"}, {"id": "NG0391", "symbol": "⚻", "fallback": "[METHOD10]", "fallback_length": 10, "reason": "Long fallback"}, {"id": "NG0392", "symbol": "⋙", "fallback": "[ALLOC18]", "fallback_length": 9, "reason": "Long fallback"}, {"id": "NG0394", "symbol": "⇞", "fallback": "[METHOD11]", "fallback_length": 10, "reason": "Long fallback"}, {"id": "NG0395", "symbol": "⨕", "fallback": "[CLASS16]", "fallback_length": 9, "reason": "Long fallback"}, {"id": "NG0400", "symbol": "▯", "fallback": "[DEREF15]", "fallback_length": 9, "reason": "Long fallback"}, {"id": "NG0404", "symbol": "⚧", "fallback": "[CLASS17]", "fallback_length": 9, "reason": "Long fallback"}, {"id": "NG0407", "symbol": "⬖", "fallback": "[IMPLIES21]", "fallback_length": 11, "reason": "Long fallback"}, {"id": "NG0408", "symbol": "⌗", "fallback": "[ALLOC19]", "fallback_length": 9, "reason": "Long fallback"}, {"id": "NG0412", "symbol": "≕", "fallback": "[ALLOC20]", "fallback_length": 9, "reason": "Long fallback"}, {"id": "NG0415", "symbol": "⇧", "fallback": "[BREAK10]", "fallback_length": 9, "reason": "Long fallback"}, {"id": "NG0416", "symbol": "⌘", "fallback": "[CLASS18]", "fallback_length": 9, "reason": "Long fallback"}, {"id": "NG0418", "symbol": "⤘", "fallback": "[CLASS19]", "fallback_length": 9, "reason": "Long fallback"}, {"id": "NG0422", "symbol": "▶", "fallback": "[POINTER14]", "fallback_length": 11, "reason": "Long fallback"}, {"id": "NG0423", "symbol": "↬", "fallback": "[BREAK11]", "fallback_length": 9, "reason": "Long fallback"}, {"id": "NG0427", "symbol": "⚬", "fallback": "[CLASS20]", "fallback_length": 9, "reason": "Long fallback"}, {"id": "NG0428", "symbol": "◣", "fallback": "[METHOD12]", "fallback_length": 10, "reason": "Long fallback"}, {"id": "NG0431", "symbol": "↺", "fallback": "[DEREF16]", "fallback_length": 9, "reason": "Long fallback"}, {"id": "NG0432", "symbol": "⨙", "fallback": "[POINTER15]", "fallback_length": 11, "reason": "Long fallback"}, {"id": "NG0433", "symbol": "∆", "fallback": "[FUNCTION20]", "fallback_length": 12, "reason": "Long fallback"}, {"id": "NG0435", "symbol": "⬙", "fallback": "[DEREF17]", "fallback_length": 9, "reason": "Long fallback"}, {"id": "NG0438", "symbol": "⋦", "fallback": "[POINTER16]", "fallback_length": 11, "reason": "Long fallback"}, {"id": "NG0439", "symbol": "√", "fallback": "[CLASS21]", "fallback_length": 9, "reason": "Long fallback"}, {"id": "NG0440", "symbol": "↓", "fallback": "[CLASS22]", "fallback_length": 9, "reason": "Long fallback"}, {"id": "NG0443", "symbol": "≜", "fallback": "[PROPERTY19]", "fallback_length": 12, "reason": "Long fallback"}, {"id": "NG0444", "symbol": "⌚", "fallback": "[CLASS23]", "fallback_length": 9, "reason": "Long fallback"}, {"id": "NG0446", "symbol": "⊪", "fallback": "[IMPLIES22]", "fallback_length": 11, "reason": "Long fallback"}, {"id": "NG0447", "symbol": "◖", "fallback": "[METHOD13]", "fallback_length": 10, "reason": "Long fallback"}, {"id": "NG0448", "symbol": "◫", "fallback": "[BREAK12]", "fallback_length": 9, "reason": "Long fallback"}, {"id": "NG0450", "symbol": "⊰", "fallback": "[DEREF18]", "fallback_length": 9, "reason": "Long fallback"}, {"id": "NG0453", "symbol": "∁", "fallback": "[BREAK13]", "fallback_length": 9, "reason": "Long fallback"}, {"id": "NG0454", "symbol": "∦", "fallback": "[FUNCTION21]", "fallback_length": 12, "reason": "Long fallback"}, {"id": "NG0455", "symbol": "✚", "fallback": "[PROPERTY20]", "fallback_length": 12, "reason": "Long fallback"}, {"id": "NG0462", "symbol": "◌", "fallback": "[RETURN14]", "fallback_length": 10, "reason": "Long fallback"}, {"id": "NG0470", "symbol": "▣", "fallback": "[PROPERTY21]", "fallback_length": 12, "reason": "Long fallback"}, {"id": "NG0471", "symbol": "⋫", "fallback": "[METHOD14]", "fallback_length": 10, "reason": "Long fallback"}, {"id": "NG0476", "symbol": "⚹", "fallback": "[FUNCTION22]", "fallback_length": 12, "reason": "Long fallback"}, {"id": "NG0483", "symbol": "⊐", "fallback": "[METHOD15]", "fallback_length": 10, "reason": "Long fallback"}, {"id": "NG0484", "symbol": "⚶", "fallback": "[IMPLIES23]", "fallback_length": 11, "reason": "Long fallback"}, {"id": "NG0487", "symbol": "⤝", "fallback": "[BREAK14]", "fallback_length": 9, "reason": "Long fallback"}, {"id": "NG0489", "symbol": "⊤", "fallback": "[FUNCTION23]", "fallback_length": 12, "reason": "Long fallback"}, {"id": "NG0490", "symbol": "⦝", "fallback": "[CLASS24]", "fallback_length": 9, "reason": "Long fallback"}, {"id": "NG0495", "symbol": "⊢", "fallback": "[CLASS25]", "fallback_length": 9, "reason": "Long fallback"}, {"id": "NG0499", "symbol": "⌞", "fallback": "[PROPERTY22]", "fallback_length": 12, "reason": "Long fallback"}, {"id": "NG0500", "symbol": "≭", "fallback": "[IMPLIES24]", "fallback_length": 11, "reason": "Long fallback"}, {"id": "NG0501", "symbol": "⇫", "fallback": "[WHILE18]", "fallback_length": 9, "reason": "Long fallback"}, {"id": "NG0503", "symbol": "⤞", "fallback": "[POINTER17]", "fallback_length": 11, "reason": "Long fallback"}, {"id": "NG0505", "symbol": "⊚", "fallback": "[PROPERTY23]", "fallback_length": 12, "reason": "Long fallback"}, {"id": "NG0512", "symbol": "↼", "fallback": "[FUNCTION24]", "fallback_length": 12, "reason": "Long fallback"}, {"id": "NG0513", "symbol": "⎢", "token_cost": 3, "reason": "High token cost"}, {"id": "NG0513", "symbol": "⎢", "fallback": "[PLANNING]", "fallback_length": 10, "reason": "Long fallback"}, {"id": "NG0514", "symbol": "⏐", "token_cost": 3, "reason": "High token cost"}, {"id": "NG0515", "symbol": "⎆", "token_cost": 3, "reason": "High token cost"}, {"id": "NG0515", "symbol": "⎆", "fallback": "[CONCLU..]", "fallback_length": 10, "reason": "Long fallback"}, {"id": "NG0516", "symbol": "⌔", "token_cost": 3, "reason": "High token cost"}, {"id": "NG0516", "symbol": "⌔", "fallback": "[METAPHOR]", "fallback_length": 10, "reason": "Long fallback"}, {"id": "NG0517", "symbol": "⍸", "token_cost": 3, "reason": "High token cost"}, {"id": "NG0517", "symbol": "⍸", "fallback": "[PREMISE]", "fallback_length": 9, "reason": "Long fallback"}, {"id": "NG0518", "symbol": "⧮", "token_cost": 3, "reason": "High token cost"}, {"id": "NG0518", "symbol": "⧮", "fallback": "[EVALUA..]", "fallback_length": 10, "reason": "Long fallback"}, {"id": "NG0519", "symbol": "⪜", "token_cost": 3, "reason": "High token cost"}, {"id": "NG0520", "symbol": "⇡", "token_cost": 3, "reason": "High token cost"}, {"id": "NG0520", "symbol": "⇡", "fallback": "[ASSESS..]", "fallback_length": 10, "reason": "Long fallback"}, {"id": "NG0521", "symbol": "⊍", "token_cost": 3, "reason": "High token cost"}, {"id": "NG0522", "symbol": "⧬", "token_cost": 3, "reason": "High token cost"}, {"id": "NG0522", "symbol": "⧬", "fallback": "[CREATI..]", "fallback_length": 10, "reason": "Long fallback"}, {"id": "NG0523", "symbol": "⍛", "token_cost": 3, "reason": "High token cost"}, {"id": "NG0523", "symbol": "⍛", "fallback": "[INTUIT..]", "fallback_length": 10, "reason": "Long fallback"}, {"id": "NG0524", "symbol": "⏃", "token_cost": 3, "reason": "High token cost"}, {"id": "NG0525", "symbol": "⪪", "token_cost": 3, "reason": "High token cost"}, {"id": "NG0526", "symbol": "⦵", "token_cost": 3, "reason": "High token cost"}, {"id": "NG0527", "symbol": "⧄", "token_cost": 3, "reason": "High token cost"}, {"id": "NG0527", "symbol": "⧄", "fallback": "[REFLEC..]", "fallback_length": 10, "reason": "Long fallback"}, {"id": "NG0528", "symbol": "⩓", "token_cost": 3, "reason": "High token cost"}, {"id": "NG0528", "symbol": "⩓", "fallback": "[DEDUCT..]", "fallback_length": 10, "reason": "Long fallback"}, {"id": "NG0529", "symbol": "⎭", "token_cost": 3, "reason": "High token cost"}, {"id": "NG0529", "symbol": "⎭", "fallback": "[MPONENS]", "fallback_length": 9, "reason": "Long fallback"}, {"id": "NG0530", "symbol": "⪫", "token_cost": 3, "reason": "High token cost"}, {"id": "NG0530", "symbol": "⪫", "fallback": "[TAUTOL..]", "fallback_length": 10, "reason": "Long fallback"}, {"id": "NG0531", "symbol": "⊲", "token_cost": 3, "reason": "High token cost"}, {"id": "NG0531", "symbol": "⊲", "fallback": "[HYPOTH..]", "fallback_length": 10, "reason": "Long fallback"}, {"id": "NG0532", "symbol": "⩢", "token_cost": 3, "reason": "High token cost"}, {"id": "NG0532", "symbol": "⩢", "fallback": "[SOUNDN..]", "fallback_length": 10, "reason": "Long fallback"}, {"id": "NG0533", "symbol": "⪣", "token_cost": 3, "reason": "High token cost"}, {"id": "NG0533", "symbol": "⪣", "fallback": "[CONSIST]", "fallback_length": 9, "reason": "Long fallback"}, {"id": "NG0534", "symbol": "⏓", "token_cost": 3, "reason": "High token cost"}, {"id": "NG0534", "symbol": "⏓", "fallback": "[DECISION]", "fallback_length": 10, "reason": "Long fallback"}, {"id": "NG0535", "symbol": "∟", "token_cost": 3, "reason": "High token cost"}, {"id": "NG0535", "symbol": "∟", "fallback": "[JUDGMENT]", "fallback_length": 10, "reason": "Long fallback"}, {"id": "NG0536", "symbol": "⌾", "token_cost": 3, "reason": "High token cost"}, {"id": "NG0536", "symbol": "⌾", "fallback": "[INFERE..]", "fallback_length": 10, "reason": "Long fallback"}, {"id": "NG0537", "symbol": "⎥", "token_cost": 3, "reason": "High token cost"}, {"id": "NG0537", "symbol": "⎥", "fallback": "[PARADOX]", "fallback_length": 9, "reason": "Long fallback"}, {"id": "NG0538", "symbol": "⩷", "token_cost": 3, "reason": "High token cost"}, {"id": "NG0538", "symbol": "⩷", "fallback": "[SIMILA..]", "fallback_length": 10, "reason": "Long fallback"}, {"id": "NG0539", "symbol": "▪", "token_cost": 3, "reason": "High token cost"}, {"id": "NG0540", "symbol": "⍝", "token_cost": 3, "reason": "High token cost"}, {"id": "NG0541", "symbol": "⪀", "token_cost": 3, "reason": "High token cost"}, {"id": "NG0542", "symbol": "⪲", "token_cost": 3, "reason": "High token cost"}, {"id": "NG0542", "symbol": "⪲", "fallback": "[CAUSAL..]", "fallback_length": 10, "reason": "Long fallback"}, {"id": "NG0543", "symbol": "⏟", "token_cost": 3, "reason": "High token cost"}, {"id": "NG0543", "symbol": "⏟", "fallback": "[MONITO..]", "fallback_length": 10, "reason": "Long fallback"}, {"id": "NG0544", "symbol": "⎓", "token_cost": 3, "reason": "High token cost"}, {"id": "NG0544", "symbol": "⎓", "fallback": "[SYLLOG..]", "fallback_length": 10, "reason": "Long fallback"}, {"id": "NG0545", "symbol": "≘", "token_cost": 3, "reason": "High token cost"}, {"id": "NG0545", "symbol": "≘", "fallback": "[HEURIS..]", "fallback_length": 10, "reason": "Long fallback"}, {"id": "NG0546", "symbol": "⧗", "token_cost": 3, "reason": "High token cost"}, {"id": "NG0546", "symbol": "⧗", "fallback": "[SYNTHE..]", "fallback_length": 10, "reason": "Long fallback"}, {"id": "NG0547", "symbol": "⧓", "token_cost": 3, "reason": "High token cost"}, {"id": "NG0547", "symbol": "⧓", "fallback": "[PATTERN]", "fallback_length": 9, "reason": "Long fallback"}, {"id": "NG0548", "symbol": "⎑", "token_cost": 3, "reason": "High token cost"}, {"id": "NG0549", "symbol": "⏰", "token_cost": 3, "reason": "High token cost"}, {"id": "NG0549", "symbol": "⏰", "fallback": "[COMPLETE]", "fallback_length": 10, "reason": "Long fallback"}, {"id": "NG0550", "symbol": "⧯", "token_cost": 3, "reason": "High token cost"}, {"id": "NG0550", "symbol": "⧯", "fallback": "[FALLACY]", "fallback_length": 9, "reason": "Long fallback"}, {"id": "NG0551", "symbol": "⎚", "token_cost": 3, "reason": "High token cost"}, {"id": "NG0551", "symbol": "⎚", "fallback": "[INDUCT..]", "fallback_length": 10, "reason": "Long fallback"}, {"id": "NG0552", "symbol": "⧱", "token_cost": 3, "reason": "High token cost"}, {"id": "NG0552", "symbol": "⧱", "fallback": "[STRATEGY]", "fallback_length": 10, "reason": "Long fallback"}, {"id": "NG0553", "symbol": "≊", "token_cost": 3, "reason": "High token cost"}, {"id": "NG0553", "symbol": "≊", "fallback": "[GENERAL]", "fallback_length": 9, "reason": "Long fallback"}, {"id": "NG0554", "symbol": "⦸", "token_cost": 3, "reason": "High token cost"}, {"id": "NG0554", "symbol": "⦸", "fallback": "[ANALOGY]", "fallback_length": 9, "reason": "Long fallback"}, {"id": "NG0555", "symbol": "⎁", "token_cost": 3, "reason": "High token cost"}, {"id": "NG0555", "symbol": "⎁", "fallback": "[ERRCORR]", "fallback_length": 9, "reason": "Long fallback"}, {"id": "NG0556", "symbol": "⎯", "token_cost": 3, "reason": "High token cost"}, {"id": "NG0556", "symbol": "⎯", "fallback": "[ABDUCT..]", "fallback_length": 10, "reason": "Long fallback"}, {"id": "NG0557", "symbol": "≵", "token_cost": 3, "reason": "High token cost"}, {"id": "NG0558", "symbol": "⎧", "token_cost": 3, "reason": "High token cost"}, {"id": "NG0558", "symbol": "⎧", "fallback": "[MTOLLENS]", "fallback_length": 10, "reason": "Long fallback"}, {"id": "NG0559", "symbol": "⎬", "token_cost": 3, "reason": "High token cost"}, {"id": "NG0559", "symbol": "⎬", "fallback": "[VALIDITY]", "fallback_length": 10, "reason": "Long fallback"}, {"id": "NG0560", "symbol": "⌏", "token_cost": 3, "reason": "High token cost"}, {"id": "NG0560", "symbol": "⌏", "fallback": "[THEOREM]", "fallback_length": 9, "reason": "Long fallback"}, {"id": "NG0561", "symbol": "⦗", "token_cost": 3, "reason": "High token cost"}, {"id": "NG0561", "symbol": "⦗", "fallback": "[ABSTRACT]", "fallback_length": 10, "reason": "Long fallback"}, {"id": "NG0562", "symbol": "∏", "token_cost": 3, "reason": "High token cost"}, {"id": "NG0562", "symbol": "∏", "fallback": "[CONTROL]", "fallback_length": 9, "reason": "Long fallback"}, {"id": "NG0563", "symbol": "⫳", "token_cost": 3, "reason": "High token cost"}, {"id": "NG0563", "symbol": "⫳", "fallback": "[DIFFER..]", "fallback_length": 10, "reason": "Long fallback"}, {"id": "NG0564", "symbol": "⧔", "token_cost": 3, "reason": "High token cost"}, {"id": "NG0564", "symbol": "⧔", "fallback": "[INSIGHT]", "fallback_length": 9, "reason": "Long fallback"}, {"id": "NG0565", "symbol": "⊮", "token_cost": 3, "reason": "High token cost"}, {"id": "NG0566", "symbol": "⫩", "token_cost": 3, "reason": "High token cost"}, {"id": "NG0566", "symbol": "⫩", "fallback": "[ANALYSIS]", "fallback_length": 10, "reason": "Long fallback"}, {"id": "NG0567", "symbol": "⦿", "token_cost": 3, "reason": "High token cost"}, {"id": "NG0882", "symbol": "⬟", "fallback": "[ASTTRANSFORMFN]", "fallback_length": 16, "reason": "Long fallback"}, {"id": "NG0883", "symbol": "⩒", "fallback": "[SYNTAX..]", "fallback_length": 10, "reason": "Long fallback"}, {"id": "NG0885", "symbol": "⣖", "fallback": "[CODEGE..]", "fallback_length": 10, "reason": "Long fallback"}, {"id": "NG0886", "symbol": "⏘", "fallback": "[METAMETA]", "fallback_length": 10, "reason": "Long fallback"}, {"id": "NG0887", "symbol": "⌠", "fallback": "[INTROSPECTPROC]", "fallback_length": 16, "reason": "Long fallback"}, {"id": "NG0888", "symbol": "⢠", "fallback": "[INTROS..]", "fallback_length": 10, "reason": "Long fallback"}, {"id": "NG0889", "symbol": "⥮", "fallback": "[INTROS..]", "fallback_length": 10, "reason": "Long fallback"}, {"id": "NG0890", "symbol": "⦠", "fallback": "[DYNAMICDISPATCHFN]", "fallback_length": 19, "reason": "Long fallback"}, {"id": "NG0891", "symbol": "✒", "fallback": "[DYNAMICDISPATCHFN1]", "fallback_length": 20, "reason": "Long fallback"}, {"id": "NG0892", "symbol": "⨠", "fallback": "[METAOBJECTSFN]", "fallback_length": 15, "reason": "Long fallback"}, {"id": "NG0893", "symbol": "⠏", "fallback": "[METAOB..]", "fallback_length": 10, "reason": "Long fallback"}, {"id": "NG0894", "symbol": "⫘", "fallback": "[BYTECODE]", "fallback_length": 10, "reason": "Long fallback"}, {"id": "NG0895", "symbol": "⬠", "fallback": "[BYTECODESYS]", "fallback_length": 13, "reason": "Long fallback"}, {"id": "NG0896", "symbol": "⨪", "fallback": "[JITCOM..]", "fallback_length": 10, "reason": "Long fallback"}, {"id": "NG0897", "symbol": "⨄", "fallback": "[JITCOM..]", "fallback_length": 10, "reason": "Long fallback"}, {"id": "NG0898", "symbol": "⌡", "fallback": "[GARBAGECOLLECTION]", "fallback_length": 19, "reason": "Long fallback"}, {"id": "NG0899", "symbol": "◁", "fallback": "[GARBAGECOLLECTION1]", "fallback_length": 20, "reason": "Long fallback"}, {"id": "NG0900", "symbol": "✡", "fallback": "[MEMORYPOOLSMETA]", "fallback_length": 17, "reason": "Long fallback"}, {"id": "NG0901", "symbol": "⭭", "fallback": "[MEMORY..]", "fallback_length": 10, "reason": "Long fallback"}, {"id": "NG0902", "symbol": "⦡", "fallback": "[STACKFRAMES]", "fallback_length": 13, "reason": "Long fallback"}, {"id": "NG0903", "symbol": "⨡", "fallback": "[STACKFRAMESFN]", "fallback_length": 15, "reason": "Long fallback"}, {"id": "NG0904", "symbol": "⬡", "fallback": "[HEAPMANAGEMENTSYS]", "fallback_length": 19, "reason": "Long fallback"}, {"id": "NG0905", "symbol": "⌢", "fallback": "[HEAPMANAGEMENTPROC]", "fallback_length": 20, "reason": "Long fallback"}, {"id": "NG0906", "symbol": "❣", "fallback": "[COROUT..]", "fallback_length": 10, "reason": "Long fallback"}, {"id": "NG0907", "symbol": "⠌", "fallback": "[COROUT..]", "fallback_length": 10, "reason": "Long fallback"}, {"id": "NG0908", "symbol": "✢", "fallback": "[GENERATORSFN]", "fallback_length": 14, "reason": "Long fallback"}, {"id": "NG0909", "symbol": "⬢", "fallback": "[GENERATORSSYS]", "fallback_length": 15, "reason": "Long fallback"}, {"id": "NG0910", "symbol": "⢓", "fallback": "[ITERAT..]", "fallback_length": 10, "reason": "Long fallback"}, {"id": "NG0911", "symbol": "⬹", "fallback": "[ITERAT..]", "fallback_length": 10, "reason": "Long fallback"}, {"id": "NG0912", "symbol": "✝", "fallback": "[COMPRE..]", "fallback_length": 10, "reason": "Long fallback"}, {"id": "NG0913", "symbol": "⌣", "fallback": "[COMPREHENSIONSSYS]", "fallback_length": 19, "reason": "Long fallback"}, {"id": "NG0914", "symbol": "⨣", "fallback": "[DECORATORSFN]", "fallback_length": 14, "reason": "Long fallback"}, {"id": "NG0915", "symbol": "⧟", "fallback": "[DECORA..]", "fallback_length": 10, "reason": "Long fallback"}, {"id": "NG0916", "symbol": "⥿", "fallback": "[CONTEX..]", "fallback_length": 10, "reason": "Long fallback"}, {"id": "NG0917", "symbol": "⬫", "fallback": "[CONTEX..]", "fallback_length": 10, "reason": "Long fallback"}, {"id": "NG0918", "symbol": "⬣", "fallback": "[DESCRIPTORSOP]", "fallback_length": 15, "reason": "Long fallback"}, {"id": "NG0919", "symbol": "⌤", "fallback": "[DESCRIPTORSCORE]", "fallback_length": 17, "reason": "Long fallback"}, {"id": "NG0920", "symbol": "⠣", "fallback": "[METACL..]", "fallback_length": 10, "reason": "Long fallback"}, {"id": "NG0921", "symbol": "∥", "fallback": "[METACLASSESPROC]", "fallback_length": 17, "reason": "Long fallback"}, {"id": "NG0922", "symbol": "⧡", "fallback": "[METACL..]", "fallback_length": 10, "reason": "Long fallback"}, {"id": "NG0923", "symbol": "⯮", "fallback": "[METACLASSESSYS1]", "fallback_length": 17, "reason": "Long fallback"}, {"id": "NG0924", "symbol": "⌥", "fallback": "[METACLASSESCTRL]", "fallback_length": 17, "reason": "Long fallback"}, {"id": "NG0925", "symbol": "✥", "fallback": "[METACLASSESMETA]", "fallback_length": 17, "reason": "Long fallback"}, {"id": "NG0926", "symbol": "⨊", "fallback": "[METACL..]", "fallback_length": 10, "reason": "Long fallback"}, {"id": "NG0927", "symbol": "⫙", "fallback": "[METACL..]", "fallback_length": 10, "reason": "Long fallback"}, {"id": "NG0928", "symbol": "⬥", "fallback": "[METACLASSES1]", "fallback_length": 14, "reason": "Long fallback"}, {"id": "NG0929", "symbol": "⫏", "fallback": "[METACLASSESSYS2]", "fallback_length": 17, "reason": "Long fallback"}, {"id": "NG0930", "symbol": "⌦", "fallback": "[METACLASSESCTRL1]", "fallback_length": 18, "reason": "Long fallback"}, {"id": "NG0931", "symbol": "⨦", "fallback": "[METACLASSESSYS3]", "fallback_length": 17, "reason": "Long fallback"}, {"id": "NG0932", "symbol": "⦎", "fallback": "[PARSET..]", "fallback_length": 10, "reason": "Long fallback"}, {"id": "NG0933", "symbol": "⬦", "fallback": "[PARSETREECTRL]", "fallback_length": 15, "reason": "Long fallback"}, {"id": "NG0934", "symbol": "⌧", "fallback": "[ASTNODECORE]", "fallback_length": 13, "reason": "Long fallback"}, {"id": "NG0935", "symbol": "✧", "fallback": "[PARSETREESYS]", "fallback_length": 14, "reason": "Long fallback"}, {"id": "NG0936", "symbol": "⬧", "fallback": "[SYNTAXTREEFN]", "fallback_length": 14, "reason": "Long fallback"}, {"id": "NG0937", "symbol": "⣋", "fallback": "[ASTNOD..]", "fallback_length": 10, "reason": "Long fallback"}, {"id": "NG0938", "symbol": "⬈", "fallback": "[PARSET..]", "fallback_length": 10, "reason": "Long fallback"}, {"id": "NG0939", "symbol": "⌨", "fallback": "[SYNTAXTREEOP]", "fallback_length": 14, "reason": "Long fallback"}, {"id": "NG0940", "symbol": "✨", "fallback": "[PARSETREEMETA]", "fallback_length": 15, "reason": "Long fallback"}, {"id": "NG0941", "symbol": "⢐", "fallback": "[ASTNOD..]", "fallback_length": 10, "reason": "Long fallback"}, {"id": "NG0942", "symbol": "⤨", "fallback": "[ASTTRANSFORMMETA]", "fallback_length": 18, "reason": "Long fallback"}, {"id": "NG0943", "symbol": "⯘", "fallback": "[ASTNODE]", "fallback_length": 9, "reason": "Long fallback"}, {"id": "NG0944", "symbol": "⨨", "fallback": "[CODEGENCTRL]", "fallback_length": 13, "reason": "Long fallback"}, {"id": "NG0945", "symbol": "⬨", "fallback": "[CODEGENOP1]", "fallback_length": 12, "reason": "Long fallback"}, {"id": "NG0946", "symbol": "⤧", "fallback": "[EMITCTRL]", "fallback_length": 10, "reason": "Long fallback"}, {"id": "NG0947", "symbol": "⏙", "fallback": "[EMITCORE]", "fallback_length": 10, "reason": "Long fallback"}, {"id": "NG0948", "symbol": "⦛", "fallback": "[EMITCTRL1]", "fallback_length": 11, "reason": "Long fallback"}, {"id": "NG0949", "symbol": "⡗", "fallback": "[GENERA..]", "fallback_length": 10, "reason": "Long fallback"}, {"id": "NG0950", "symbol": "〈", "fallback": "[GENERATEPROC]", "fallback_length": 14, "reason": "Long fallback"}, {"id": "NG0951", "symbol": "⨩", "fallback": "[CODEGENPROC]", "fallback_length": 13, "reason": "Long fallback"}, {"id": "NG0952", "symbol": "⪠", "fallback": "[COMPIL..]", "fallback_length": 10, "reason": "Long fallback"}, {"id": "NG0953", "symbol": "〉", "fallback": "[GENERATESYS1]", "fallback_length": 14, "reason": "Long fallback"}, {"id": "NG0954", "symbol": "⯙", "fallback": "[EMITPROC]", "fallback_length": 10, "reason": "Long fallback"}, {"id": "NG0956", "symbol": "⤪", "fallback": "[INTROSPECTCORE]", "fallback_length": 16, "reason": "Long fallback"}, {"id": "NG0957", "symbol": "⦪", "fallback": "[INTROSPECTSYS]", "fallback_length": 15, "reason": "Long fallback"}, {"id": "NG0958", "symbol": "⌫", "fallback": "[REFLECTMETA]", "fallback_length": 13, "reason": "Long fallback"}, {"id": "NG0959", "symbol": "○", "fallback": "[INTROSPECTMETA]", "fallback_length": 16, "reason": "Long fallback"}, {"id": "NG0960", "symbol": "⥳", "fallback": "[INTROS..]", "fallback_length": 10, "reason": "Long fallback"}, {"id": "NG0961", "symbol": "✫", "fallback": "[REFLECTMETA1]", "fallback_length": 14, "reason": "Long fallback"}, {"id": "NG0962", "symbol": "⦫", "fallback": "[METAMETA1]", "fallback_length": 11, "reason": "Long fallback"}, {"id": "NG0963", "symbol": "⨫", "fallback": "[REFLECTCTRL]", "fallback_length": 13, "reason": "Long fallback"}, {"id": "NG0964", "symbol": "⯜", "fallback": "[INTROSPECT1]", "fallback_length": 13, "reason": "Long fallback"}, {"id": "NG0965", "symbol": "∬", "fallback": "[METAMETA2]", "fallback_length": 11, "reason": "Long fallback"}, {"id": "NG0966", "symbol": "⌬", "fallback": "[MIRRORSYS]", "fallback_length": 11, "reason": "Long fallback"}, {"id": "NG0967", "symbol": "⨬", "fallback": "[REFLECTOP]", "fallback_length": 11, "reason": "Long fallback"}, {"id": "NG0968", "symbol": "∭", "fallback": "[INTROSPECTIONMETA]", "fallback_length": 19, "reason": "Long fallback"}, {"id": "NG0969", "symbol": "⮸", "fallback": "[INTROS..]", "fallback_length": 10, "reason": "Long fallback"}, {"id": "NG0970", "symbol": "❑", "fallback": "[INTROSPECTIONMETA1]", "fallback_length": 20, "reason": "Long fallback"}, {"id": "NG0971", "symbol": "⮷", "fallback": "[INTROSPECTIONCORE1]", "fallback_length": 20, "reason": "Long fallback"}, {"id": "NG0972", "symbol": "⌭", "fallback": "[INTROSPECTION1]", "fallback_length": 16, "reason": "Long fallback"}, {"id": "NG0973", "symbol": "⪘", "fallback": "[INTROS..]", "fallback_length": 10, "reason": "Long fallback"}, {"id": "NG0974", "symbol": "⣠", "fallback": "[INTROSPECTIONCORE2]", "fallback_length": 20, "reason": "Long fallback"}, {"id": "NG0975", "symbol": "⨭", "fallback": "[INTROSPECTIONOP]", "fallback_length": 17, "reason": "Long fallback"}, {"id": "NG0976", "symbol": "⬭", "fallback": "[INTROSPECTIONFN1]", "fallback_length": 18, "reason": "Long fallback"}, {"id": "NG0977", "symbol": "⨞", "fallback": "[INTROS..]", "fallback_length": 10, "reason": "Long fallback"}, {"id": "NG0978", "symbol": "⨂", "fallback": "[INTROSPECTIONCORE3]", "fallback_length": 20, "reason": "Long fallback"}, {"id": "NG0979", "symbol": "∮", "fallback": "[INTROSPECTIONPROC]", "fallback_length": 19, "reason": "Long fallback"}, {"id": "NG0980", "symbol": "⌮", "fallback": "[DYNAMICDISPATCHSYS]", "fallback_length": 20, "reason": "Long fallback"}, {"id": "NG0981", "symbol": "⧚", "fallback": "[DYNAMI..]", "fallback_length": 10, "reason": "Long fallback"}, {"id": "NG0982", "symbol": "⪟", "fallback": "[DYNAMI..]", "fallback_length": 10, "reason": "Long fallback"}, {"id": "NG0983", "symbol": "⢖", "fallback": "[DYNAMICDISPATCH1]", "fallback_length": 18, "reason": "Long fallback"}, {"id": "NG0984", "symbol": "⤮", "fallback": "[DYNAMICDISPATCHFN2]", "fallback_length": 20, "reason": "Long fallback"}, {"id": "NG0985", "symbol": "𝝏", "fallback": "[DYNAMICDISPATCHFN3]", "fallback_length": 20, "reason": "Long fallback"}, {"id": "NG0986", "symbol": "⤗", "fallback": "[DYNAMICDISPATCH2]", "fallback_length": 18, "reason": "Long fallback"}, {"id": "NG0987", "symbol": "⮐", "fallback": "[DYNAMICDISPATCH3]", "fallback_length": 18, "reason": "Long fallback"}, {"id": "NG0988", "symbol": "✼", "fallback": "[DYNAMICDISPATCH4]", "fallback_length": 18, "reason": "Long fallback"}, {"id": "NG0989", "symbol": "⬮", "fallback": "[DYNAMICDISPATCHOP1]", "fallback_length": 20, "reason": "Long fallback"}, {"id": "NG0990", "symbol": "⫟", "fallback": "[DYNAMICDISPATCHFN4]", "fallback_length": 20, "reason": "Long fallback"}, {"id": "NG0991", "symbol": "⮶", "fallback": "[DYNAMICDISPATCH5]", "fallback_length": 18, "reason": "Long fallback"}, {"id": "NG0992", "symbol": "⫄", "fallback": "[METAOBJECTSFN1]", "fallback_length": 16, "reason": "Long fallback"}, {"id": "NG0993", "symbol": "⧠", "fallback": "[METAOB..]", "fallback_length": 10, "reason": "Long fallback"}, {"id": "NG0994", "symbol": "⌯", "fallback": "[METAOBJECTS1]", "fallback_length": 14, "reason": "Long fallback"}, {"id": "NG0995", "symbol": "⧲", "fallback": "[METAOB..]", "fallback_length": 10, "reason": "Long fallback"}, {"id": "NG0996", "symbol": "⦯", "fallback": "[METAOBJECTS2]", "fallback_length": 14, "reason": "Long fallback"}, {"id": "NG0997", "symbol": "⬯", "fallback": "[METAOBJECTSFN2]", "fallback_length": 16, "reason": "Long fallback"}, {"id": "NG0998", "symbol": "⌰", "fallback": "[METAOBJECTSSYS]", "fallback_length": 16, "reason": "Long fallback"}, {"id": "NG0999", "symbol": "⤰", "fallback": "[METAOBJECTSOP1]", "fallback_length": 16, "reason": "Long fallback"}, {"id": "NG1000", "symbol": "❘", "fallback": "[METAOBJECTSFN3]", "fallback_length": 16, "reason": "Long fallback"}, {"id": "NG1001", "symbol": "⬰", "fallback": "[METAOBJECTSOP2]", "fallback_length": 16, "reason": "Long fallback"}, {"id": "NG1002", "symbol": "⥸", "fallback": "[METAOBJECTSSYS1]", "fallback_length": 17, "reason": "Long fallback"}, {"id": "NG1003", "symbol": "⠁", "fallback": "[METAOBJECTSSYS2]", "fallback_length": 17, "reason": "Long fallback"}, {"id": "NG1004", "symbol": "⌱", "fallback": "[BYTECODECTRL]", "fallback_length": 14, "reason": "Long fallback"}, {"id": "NG1005", "symbol": "⨱", "fallback": "[BYTECODEFN]", "fallback_length": 12, "reason": "Long fallback"}, {"id": "NG1006", "symbol": "⯐", "fallback": "[BYTECO..]", "fallback_length": 10, "reason": "Long fallback"}, {"id": "NG1007", "symbol": "⣐", "fallback": "[BYTECO..]", "fallback_length": 10, "reason": "Long fallback"}, {"id": "NG1008", "symbol": "⬿", "fallback": "[BYTECO..]", "fallback_length": 10, "reason": "Long fallback"}, {"id": "NG1009", "symbol": "❫", "fallback": "[BYTECODESYS1]", "fallback_length": 14, "reason": "Long fallback"}, {"id": "NG1010", "symbol": "⬱", "fallback": "[BYTECODEOP]", "fallback_length": 12, "reason": "Long fallback"}, {"id": "NG1011", "symbol": "❤", "fallback": "[BYTECODE1]", "fallback_length": 11, "reason": "Long fallback"}, {"id": "NG1012", "symbol": "❗", "fallback": "[BYTECODESYS2]", "fallback_length": 14, "reason": "Long fallback"}, {"id": "NG1013", "symbol": "⌲", "fallback": "[BYTECODE2]", "fallback_length": 11, "reason": "Long fallback"}, {"id": "NG1014", "symbol": "⠮", "fallback": "[BYTECODECTRL1]", "fallback_length": 15, "reason": "Long fallback"}, {"id": "NG1015", "symbol": "✲", "fallback": "[BYTECODE3]", "fallback_length": 11, "reason": "Long fallback"}, {"id": "NG1016", "symbol": "➴", "fallback": "[JITCOM..]", "fallback_length": 10, "reason": "Long fallback"}, {"id": "NG1017", "symbol": "⦲", "fallback": "[JITCOMPILATIONSYS1]", "fallback_length": 20, "reason": "Long fallback"}, {"id": "NG1018", "symbol": "⌳", "fallback": "[JITCOMPILATIONFN]", "fallback_length": 18, "reason": "Long fallback"}, {"id": "NG1019", "symbol": "⠀", "fallback": "[JITCOM..]", "fallback_length": 10, "reason": "Long fallback"}, {"id": "NG1020", "symbol": "⥫", "fallback": "[JITCOMPILATIONFN1]", "fallback_length": 19, "reason": "Long fallback"}, {"id": "NG1021", "symbol": "⤳", "fallback": "[JITCOMPILATIONOP]", "fallback_length": 18, "reason": "Long fallback"}, {"id": "NG1022", "symbol": "⨳", "fallback": "[JITCOMPILATIONPROC]", "fallback_length": 20, "reason": "Long fallback"}, {"id": "NG1023", "symbol": "⫉", "fallback": "[JITCOMPILATIONSYS2]", "fallback_length": 20, "reason": "Long fallback"}, {"id": "NG1024", "symbol": "⭙", "fallback": "[JITCOMPILATIONOP1]", "fallback_length": 19, "reason": "Long fallback"}, {"id": "NG1025", "symbol": "⬳", "fallback": "[JITCOMPILATIONSYS3]", "fallback_length": 20, "reason": "Long fallback"}, {"id": "NG1026", "symbol": "⫬", "fallback": "[JITCOMPILATIONOP2]", "fallback_length": 19, "reason": "Long fallback"}, {"id": "NG1027", "symbol": "∴", "fallback": "[JITCOMPILATION]", "fallback_length": 16, "reason": "Long fallback"}, {"id": "NG1028", "symbol": "⠂", "fallback": "[GARBAGECOLLECTION2]", "fallback_length": 20, "reason": "Long fallback"}, {"id": "NG1029", "symbol": "⦙", "fallback": "[GARBAGECOLLECTION3]", "fallback_length": 20, "reason": "Long fallback"}, {"id": "NG1030", "symbol": "⭌", "fallback": "[GARBAGECOLLECTION4]", "fallback_length": 20, "reason": "Long fallback"}, {"id": "NG1031", "symbol": "⌴", "fallback": "[GARBAGECOLLECTION5]", "fallback_length": 20, "reason": "Long fallback"}, {"id": "NG1032", "symbol": "⨚", "fallback": "[GARBAGECOLLECTION6]", "fallback_length": 20, "reason": "Long fallback"}, {"id": "NG1033", "symbol": "⮟", "fallback": "[GARBAGECOLLECTION7]", "fallback_length": 20, "reason": "Long fallback"}, {"id": "NG1034", "symbol": "✳", "fallback": "[GARBAGECOLLECTION8]", "fallback_length": 20, "reason": "Long fallback"}, {"id": "NG1035", "symbol": "⥇", "fallback": "[GARBAGECOLLECTION9]", "fallback_length": 20, "reason": "Long fallback"}, {"id": "NG1036", "symbol": "✴", "fallback": "[MEMORYPOOLSFN]", "fallback_length": 15, "reason": "Long fallback"}, {"id": "NG1037", "symbol": "⨴", "fallback": "[MEMORYPOOLSSYS]", "fallback_length": 16, "reason": "Long fallback"}, {"id": "NG1038", "symbol": "⢈", "fallback": "[MEMORYPOOLS1]", "fallback_length": 14, "reason": "Long fallback"}, {"id": "NG1039", "symbol": "⬴", "fallback": "[MEMORYPOOLSCORE]", "fallback_length": 17, "reason": "Long fallback"}, {"id": "NG1040", "symbol": "➞", "fallback": "[MEMORY..]", "fallback_length": 10, "reason": "Long fallback"}, {"id": "NG1041", "symbol": "∵", "fallback": "[MEMORYPOOLSPROC]", "fallback_length": 17, "reason": "Long fallback"}, {"id": "NG1042", "symbol": "⧹", "fallback": "[MEMORYPOOLSSYS1]", "fallback_length": 17, "reason": "Long fallback"}, {"id": "NG1043", "symbol": "⌵", "fallback": "[MEMORYPOOLSPROC1]", "fallback_length": 18, "reason": "Long fallback"}, {"id": "NG1044", "symbol": "✵", "fallback": "[MEMORYPOOLSMETA1]", "fallback_length": 18, "reason": "Long fallback"}, {"id": "NG1045", "symbol": "⤵", "fallback": "[MEMORYPOOLSCORE1]", "fallback_length": 18, "reason": "Long fallback"}, {"id": "NG1046", "symbol": "⨥", "fallback": "[MEMORYPOOLSSYS2]", "fallback_length": 17, "reason": "Long fallback"}, {"id": "NG1047", "symbol": "⬵", "fallback": "[MEMORYPOOLSCTRL]", "fallback_length": 17, "reason": "Long fallback"}, {"id": "NG1048", "symbol": "⌶", "fallback": "[STACKFRAMESPROC]", "fallback_length": 17, "reason": "Long fallback"}, {"id": "NG1049", "symbol": "⯦", "fallback": "[STACKFRAMESFN1]", "fallback_length": 16, "reason": "Long fallback"}, {"id": "NG1050", "symbol": "✱", "fallback": "[STACKF..]", "fallback_length": 10, "reason": "Long fallback"}, {"id": "NG1051", "symbol": "⤭", "fallback": "[STACKF..]", "fallback_length": 10, "reason": "Long fallback"}, {"id": "NG1052", "symbol": "⡘", "fallback": "[STACKFRAMESSYS1]", "fallback_length": 17, "reason": "Long fallback"}, {"id": "NG1053", "symbol": "✶", "fallback": "[STACKFRAMESCORE]", "fallback_length": 17, "reason": "Long fallback"}, {"id": "NG1054", "symbol": "⯨", "fallback": "[STACKFRAMES1]", "fallback_length": 14, "reason": "Long fallback"}, {"id": "NG1055", "symbol": "⤶", "fallback": "[STACKFRAMESCORE1]", "fallback_length": 18, "reason": "Long fallback"}, {"id": "NG1056", "symbol": "⦶", "fallback": "[STACKFRAMESMETA]", "fallback_length": 17, "reason": "Long fallback"}, {"id": "NG1057", "symbol": "⬶", "fallback": "[STACKFRAMESMETA1]", "fallback_length": 18, "reason": "Long fallback"}, {"id": "NG1058", "symbol": "⌷", "fallback": "[STACKFRAMESFN2]", "fallback_length": 16, "reason": "Long fallback"}, {"id": "NG1059", "symbol": "✷", "fallback": "[STACKFRAMESCORE2]", "fallback_length": 18, "reason": "Long fallback"}, {"id": "NG1060", "symbol": "❄", "fallback": "[HEAPMANAGEMENTSYS1]", "fallback_length": 20, "reason": "Long fallback"}, {"id": "NG1061", "symbol": "⪴", "fallback": "[HEAPMA..]", "fallback_length": 10, "reason": "Long fallback"}, {"id": "NG1062", "symbol": "⤷", "fallback": "[HEAPMANAGEMENTSYS2]", "fallback_length": 20, "reason": "Long fallback"}, {"id": "NG1063", "symbol": "⬷", "fallback": "[HEAPMANAGEMENTFN]", "fallback_length": 18, "reason": "Long fallback"}, {"id": "NG1064", "symbol": "⥔", "fallback": "[HEAPMA..]", "fallback_length": 10, "reason": "Long fallback"}, {"id": "NG1065", "symbol": "∸", "fallback": "[HEAPMANAGEMENTFN1]", "fallback_length": 19, "reason": "Long fallback"}, {"id": "NG1066", "symbol": "⡔", "fallback": "[HEAPMA..]", "fallback_length": 10, "reason": "Long fallback"}, {"id": "NG1067", "symbol": "⌸", "fallback": "[HEAPMANAGEMENTMETA]", "fallback_length": 20, "reason": "Long fallback"}, {"id": "NG1068", "symbol": "✞", "fallback": "[HEAPMANAGEMENT1]", "fallback_length": 17, "reason": "Long fallback"}, {"id": "NG1069", "symbol": "✸", "fallback": "[HEAPMANAGEMENTCORE]", "fallback_length": 20, "reason": "Long fallback"}, {"id": "NG1070", "symbol": "⪒", "fallback": "[HEAPMANAGEMENTFN2]", "fallback_length": 19, "reason": "Long fallback"}, {"id": "NG1071", "symbol": "⮙", "fallback": "[HEAPMANAGEMENT2]", "fallback_length": 17, "reason": "Long fallback"}, {"id": "NG1072", "symbol": "⦩", "fallback": "[COROUT..]", "fallback_length": 10, "reason": "Long fallback"}, {"id": "NG1073", "symbol": "⤸", "fallback": "[COROUTINESOP1]", "fallback_length": 15, "reason": "Long fallback"}, {"id": "NG1074", "symbol": "⠎", "fallback": "[COROUT..]", "fallback_length": 10, "reason": "Long fallback"}, {"id": "NG1075", "symbol": "⨸", "fallback": "[COROUTINESCTRL1]", "fallback_length": 17, "reason": "Long fallback"}, {"id": "NG1076", "symbol": "❛", "fallback": "[COROUT..]", "fallback_length": 10, "reason": "Long fallback"}, {"id": "NG1077", "symbol": "⬸", "fallback": "[COROUTINESFN1]", "fallback_length": 15, "reason": "Long fallback"}, {"id": "NG1078", "symbol": "⌹", "fallback": "[COROUTINESOP2]", "fallback_length": 15, "reason": "Long fallback"}, {"id": "NG1079", "symbol": "⤹", "fallback": "[COROUTINES1]", "fallback_length": 13, "reason": "Long fallback"}, {"id": "NG1080", "symbol": "⦹", "fallback": "[COROUTINES2]", "fallback_length": 13, "reason": "Long fallback"}, {"id": "NG1081", "symbol": "⦓", "fallback": "[COROUTINESMETA1]", "fallback_length": 17, "reason": "Long fallback"}, {"id": "NG1082", "symbol": "⠹", "fallback": "[COROUT..]", "fallback_length": 10, "reason": "Long fallback"}, {"id": "NG1083", "symbol": "⌺", "fallback": "[COROUTINESSYS]", "fallback_length": 15, "reason": "Long fallback"}, {"id": "NG1084", "symbol": "✺", "fallback": "[GENERATORSCTRL]", "fallback_length": 16, "reason": "Long fallback"}, {"id": "NG1085", "symbol": "⤺", "fallback": "[GENERATORSOP]", "fallback_length": 14, "reason": "Long fallback"}, {"id": "NG1086", "symbol": "⨺", "fallback": "[GENERATORSSYS1]", "fallback_length": 16, "reason": "Long fallback"}, {"id": "NG1087", "symbol": "∻", "fallback": "[GENERATORSMETA]", "fallback_length": 16, "reason": "Long fallback"}, {"id": "NG1088", "symbol": "⌻", "fallback": "[GENERATORSMETA1]", "fallback_length": 17, "reason": "Long fallback"}, {"id": "NG1089", "symbol": "⤻", "fallback": "[GENERATORSPROC]", "fallback_length": 16, "reason": "Long fallback"}, {"id": "NG1090", "symbol": "⨻", "fallback": "[GENERATORSCTRL1]", "fallback_length": 17, "reason": "Long fallback"}, {"id": "NG1091", "symbol": "⪥", "fallback": "[GENERATORSMETA2]", "fallback_length": 17, "reason": "Long fallback"}, {"id": "NG1092", "symbol": "⬻", "fallback": "[GENERATORSCTRL2]", "fallback_length": 17, "reason": "Long fallback"}, {"id": "NG1093", "symbol": "⌼", "fallback": "[GENERATORS]", "fallback_length": 12, "reason": "Long fallback"}, {"id": "NG1094", "symbol": "⤼", "fallback": "[GENERATORSPROC1]", "fallback_length": 17, "reason": "Long fallback"}, {"id": "NG1095", "symbol": "⨼", "fallback": "[GENERATORSCTRL3]", "fallback_length": 17, "reason": "Long fallback"}, {"id": "NG1096", "symbol": "⬼", "fallback": "[ITERATORSCORE]", "fallback_length": 15, "reason": "Long fallback"}, {"id": "NG1097", "symbol": "⣛", "fallback": "[ITERAT..]", "fallback_length": 10, "reason": "Long fallback"}, {"id": "NG1098", "symbol": "∽", "fallback": "[ITERATORSFN1]", "fallback_length": 14, "reason": "Long fallback"}, {"id": "NG1099", "symbol": "⌽", "fallback": "[ITERATORS]", "fallback_length": 11, "reason": "Long fallback"}, {"id": "NG1100", "symbol": "⫍", "fallback": "[ITERATORSCORE1]", "fallback_length": 16, "reason": "Long fallback"}, {"id": "NG1101", "symbol": "⭼", "fallback": "[ITERATORSSYS1]", "fallback_length": 15, "reason": "Long fallback"}, {"id": "NG1102", "symbol": "⤽", "fallback": "[ITERATORSCTRL1]", "fallback_length": 16, "reason": "Long fallback"}, {"id": "NG1103", "symbol": "⪨", "fallback": "[ITERATORS1]", "fallback_length": 12, "reason": "Long fallback"}, {"id": "NG1104", "symbol": "✏", "fallback": "[ITERATORSCTRL2]", "fallback_length": 16, "reason": "Long fallback"}, {"id": "NG1105", "symbol": "⭃", "fallback": "[ITERATORSCORE2]", "fallback_length": 16, "reason": "Long fallback"}, {"id": "NG1106", "symbol": "⥘", "fallback": "[ITERATORSFN2]", "fallback_length": 14, "reason": "Long fallback"}, {"id": "NG1107", "symbol": "⦽", "fallback": "[ITERATORSMETA]", "fallback_length": 15, "reason": "Long fallback"}, {"id": "NG1108", "symbol": "⫅", "fallback": "[COMPRE..]", "fallback_length": 10, "reason": "Long fallback"}, {"id": "NG1109", "symbol": "⬽", "fallback": "[COMPREHENSIONSPROC]", "fallback_length": 20, "reason": "Long fallback"}, {"id": "NG1110", "symbol": "✾", "fallback": "[COMPREHENSIONSOP1]", "fallback_length": 19, "reason": "Long fallback"}, {"id": "NG1111", "symbol": "⤾", "fallback": "[COMPREHENSIONSSYS1]", "fallback_length": 20, "reason": "Long fallback"}, {"id": "NG1112", "symbol": "⢧", "fallback": "[COMPREHENSIONSFN1]", "fallback_length": 19, "reason": "Long fallback"}, {"id": "NG1113", "symbol": "⤙", "fallback": "[COMPRE..]", "fallback_length": 10, "reason": "Long fallback"}, {"id": "NG1114", "symbol": "⨾", "fallback": "[COMPREHENSIONS]", "fallback_length": 16, "reason": "Long fallback"}, {"id": "NG1115", "symbol": "⬾", "fallback": "[COMPREHENSIONSOP2]", "fallback_length": 19, "reason": "Long fallback"}, {"id": "NG1116", "symbol": "⌿", "fallback": "[COMPREHENSIONSSYS2]", "fallback_length": 20, "reason": "Long fallback"}, {"id": "NG1117", "symbol": "✿", "fallback": "[COMPREHENSIONS1]", "fallback_length": 17, "reason": "Long fallback"}, {"id": "NG1118", "symbol": "⤿", "fallback": "[COMPREHENSIONSMETA]", "fallback_length": 20, "reason": "Long fallback"}, {"id": "NG1119", "symbol": "≀", "fallback": "[COMPREHENSIONSSYS3]", "fallback_length": 20, "reason": "Long fallback"}, {"id": "NG1120", "symbol": "⍀", "fallback": "[DECORATORSPROC]", "fallback_length": 16, "reason": "Long fallback"}, {"id": "NG1121", "symbol": "⧀", "fallback": "[DECORATORSOP]", "fallback_length": 14, "reason": "Long fallback"}, {"id": "NG1122", "symbol": "⩀", "fallback": "[DECORATORSPROC1]", "fallback_length": 17, "reason": "Long fallback"}, {"id": "NG1123", "symbol": "⧳", "fallback": "[DECORATORSPROC2]", "fallback_length": 17, "reason": "Long fallback"}, {"id": "NG1124", "symbol": "⭀", "fallback": "[DECORATORSCTRL1]", "fallback_length": 17, "reason": "Long fallback"}, {"id": "NG1125", "symbol": "⍁", "fallback": "[DECORATORSPROC3]", "fallback_length": 17, "reason": "Long fallback"}, {"id": "NG1126", "symbol": "⦢", "fallback": "[DECORATORSPROC4]", "fallback_length": 17, "reason": "Long fallback"}, {"id": "NG1127", "symbol": "⥼", "fallback": "[DECORA..]", "fallback_length": 10, "reason": "Long fallback"}, {"id": "NG1128", "symbol": "⧁", "fallback": "[DECORATORSPROC5]", "fallback_length": 17, "reason": "Long fallback"}, {"id": "NG1129", "symbol": "⩁", "fallback": "[DECORATORSCORE]", "fallback_length": 16, "reason": "Long fallback"}, {"id": "NG1130", "symbol": "⡣", "fallback": "[DECORA..]", "fallback_length": 10, "reason": "Long fallback"}, {"id": "NG1131", "symbol": "➬", "fallback": "[DECORATORSSYS1]", "fallback_length": 16, "reason": "Long fallback"}, {"id": "NG1132", "symbol": "⭁", "fallback": "[CONTEXTMANAGERSOP]", "fallback_length": 19, "reason": "Long fallback"}, {"id": "NG1133", "symbol": "≂", "fallback": "[CONTEXTMANAGERSOP1]", "fallback_length": 20, "reason": "Long fallback"}, {"id": "NG1134", "symbol": "⯄", "fallback": "[CONTEXTMANAGERS1]", "fallback_length": 18, "reason": "Long fallback"}, {"id": "NG1135", "symbol": "⍂", "fallback": "[CONTEXTMANAGERSOP2]", "fallback_length": 20, "reason": "Long fallback"}, {"id": "NG1136", "symbol": "⥂", "fallback": "[CONTEXTMANAGERSOP3]", "fallback_length": 20, "reason": "Long fallback"}, {"id": "NG1137", "symbol": "⭂", "fallback": "[CONTEXTMANAGERS2]", "fallback_length": 18, "reason": "Long fallback"}, {"id": "NG1138", "symbol": "➤", "fallback": "[CONTEX..]", "fallback_length": 10, "reason": "Long fallback"}, {"id": "NG1139", "symbol": "⍃", "fallback": "[CONTEXTMANAGERS3]", "fallback_length": 18, "reason": "Long fallback"}, {"id": "NG1140", "symbol": "⧃", "fallback": "[CONTEXTMANAGERSOP4]", "fallback_length": 20, "reason": "Long fallback"}, {"id": "NG1141", "symbol": "⩃", "fallback": "[CONTEXTMANAGERSOP5]", "fallback_length": 20, "reason": "Long fallback"}, {"id": "NG1142", "symbol": "⍄", "fallback": "[CONTEXTMANAGERSOP6]", "fallback_length": 20, "reason": "Long fallback"}, {"id": "NG1143", "symbol": "✩", "fallback": "[CONTEXTMANAGERSFN1]", "fallback_length": 20, "reason": "Long fallback"}, {"id": "NG1144", "symbol": "⩄", "fallback": "[DESCRIPTORSMETA]", "fallback_length": 17, "reason": "Long fallback"}, {"id": "NG1145", "symbol": "⡑", "fallback": "[DESCRI..]", "fallback_length": 10, "reason": "Long fallback"}, {"id": "NG1146", "symbol": "⢣", "fallback": "[DESCRIPTORSOP1]", "fallback_length": 16, "reason": "Long fallback"}, {"id": "NG1147", "symbol": "⭄", "fallback": "[DESCRIPTORSPROC1]", "fallback_length": 18, "reason": "Long fallback"}, {"id": "NG1148", "symbol": "≅", "fallback": "[DESCRIPTORSPROC2]", "fallback_length": 18, "reason": "Long fallback"}, {"id": "NG1149", "symbol": "⥣", "fallback": "[DESCRIPTORSOP2]", "fallback_length": 16, "reason": "Long fallback"}, {"id": "NG1150", "symbol": "⍅", "fallback": "[DESCRIPTORSFN]", "fallback_length": 15, "reason": "Long fallback"}, {"id": "NG1151", "symbol": "❅", "fallback": "[DESCRIPTORSMETA1]", "fallback_length": 18, "reason": "Long fallback"}, {"id": "NG1152", "symbol": "⧅", "fallback": "[DESCRI..]", "fallback_length": 10, "reason": "Long fallback"}, {"id": "NG1153", "symbol": "⠿", "fallback": "[DESCRIPTORSFN1]", "fallback_length": 16, "reason": "Long fallback"}, {"id": "NG1154", "symbol": "⩅", "fallback": "[DESCRIPTORSCORE1]", "fallback_length": 18, "reason": "Long fallback"}, {"id": "NG1155", "symbol": "⦣", "fallback": "[DESCRIPTORSMETA2]", "fallback_length": 18, "reason": "Long fallback"}, {"id": "NG1156", "symbol": "❱", "fallback": "[METACL..]", "fallback_length": 10, "reason": "Long fallback"}, {"id": "NG1157", "symbol": "⯛", "fallback": "[METACLASSESMETA1]", "fallback_length": 18, "reason": "Long fallback"}, {"id": "NG1158", "symbol": "≆", "fallback": "[METACLASSESSYS4]", "fallback_length": 17, "reason": "Long fallback"}, {"id": "NG1159", "symbol": "⍆", "fallback": "[METACLASSES2]", "fallback_length": 14, "reason": "Long fallback"}, {"id": "NG1160", "symbol": "⣃", "fallback": "[METACLASSESCTRL2]", "fallback_length": 18, "reason": "Long fallback"}, {"id": "NG1161", "symbol": "⥆", "fallback": "[METACLASSESSYS5]", "fallback_length": 17, "reason": "Long fallback"}, {"id": "NG1162", "symbol": "⨰", "fallback": "[METACLASSES3]", "fallback_length": 14, "reason": "Long fallback"}, {"id": "NG1163", "symbol": "❍", "fallback": "[METACLASSESOP1]", "fallback_length": 16, "reason": "Long fallback"}, {"id": "NG1164", "symbol": "⯅", "fallback": "[METACLASSESOP2]", "fallback_length": 16, "reason": "Long fallback"}, {"id": "NG1165", "symbol": "⧆", "fallback": "[METACLASSESMETA2]", "fallback_length": 18, "reason": "Long fallback"}, {"id": "NG1166", "symbol": "⭆", "fallback": "[METACLASSESCTRL3]", "fallback_length": 18, "reason": "Long fallback"}, {"id": "NG1167", "symbol": "⥙", "fallback": "[METACLASSESSYS6]", "fallback_length": 17, "reason": "Long fallback"}, {"id": "NG1168", "symbol": "⥝", "fallback": "[METACLASSESPROC1]", "fallback_length": 18, "reason": "Long fallback"}, {"id": "NG1169", "symbol": "⤢", "fallback": "[METACLASSES4]", "fallback_length": 14, "reason": "Long fallback"}, {"id": "NG1170", "symbol": "⍇", "fallback": "[METACLASSESOP3]", "fallback_length": 16, "reason": "Long fallback"}, {"id": "NG1171", "symbol": "⮈", "fallback": "[METACLASSESFN1]", "fallback_length": 16, "reason": "Long fallback"}, {"id": "NG1172", "symbol": "❇", "fallback": "[METACLASSESSYS7]", "fallback_length": 17, "reason": "Long fallback"}, {"id": "NG1173", "symbol": "⧩", "fallback": "[METACLASSESMETA3]", "fallback_length": 18, "reason": "Long fallback"}, {"id": "NG1174", "symbol": "⧇", "fallback": "[METACLASSESMETA4]", "fallback_length": 18, "reason": "Long fallback"}, {"id": "NG1175", "symbol": "⭇", "fallback": "[METACLASSESCORE1]", "fallback_length": 18, "reason": "Long fallback"}, {"id": "NG1176", "symbol": "≈", "fallback": "[METACLASSESMETA5]", "fallback_length": 18, "reason": "Long fallback"}, {"id": "NG1177", "symbol": "⍈", "fallback": "[METACLASSESSYS8]", "fallback_length": 17, "reason": "Long fallback"}, {"id": "NG1178", "symbol": "❠", "fallback": "[METACLASSESSYS9]", "fallback_length": 17, "reason": "Long fallback"}, {"id": "NG1179", "symbol": "⭈", "fallback": "[METACLASSESCORE2]", "fallback_length": 18, "reason": "Long fallback"}, {"id": "NG1180", "symbol": "⍉", "fallback": "[METACLASSESMETA6]", "fallback_length": 18, "reason": "Long fallback"}, {"id": "NG1181", "symbol": "⧷", "fallback": "[METACLASSESFN2]", "fallback_length": 16, "reason": "Long fallback"}, {"id": "NG1182", "symbol": "⩉", "fallback": "[METACLASSESFN3]", "fallback_length": 16, "reason": "Long fallback"}, {"id": "NG1183", "symbol": "⭉", "fallback": "[METACLASSESSYS10]", "fallback_length": 18, "reason": "Long fallback"}, {"id": "NG1184", "symbol": "⍊", "fallback": "[METACLASSESMETA7]", "fallback_length": 18, "reason": "Long fallback"}, {"id": "NG1185", "symbol": "❊", "fallback": "[METACLASSESPROC2]", "fallback_length": 18, "reason": "Long fallback"}, {"id": "NG1186", "symbol": "⥊", "fallback": "[METACLASSESCTRL4]", "fallback_length": 18, "reason": "Long fallback"}, {"id": "NG1187", "symbol": "⠒", "fallback": "[METACLASSESCORE3]", "fallback_length": 18, "reason": "Long fallback"}, {"id": "NG1188", "symbol": "⩶", "fallback": "[CODEAS..]", "fallback_length": 10, "reason": "Long fallback"}, {"id": "NG1189", "symbol": "⧿", "fallback": "[CODEAS..]", "fallback_length": 10, "reason": "Long fallback"}, {"id": "NG1190", "symbol": "⦼", "fallback": "[CODEAS..]", "fallback_length": 10, "reason": "Long fallback"}, {"id": "NG1191", "symbol": "⧊", "fallback": "[CODEASDATACORE]", "fallback_length": 16, "reason": "Long fallback"}, {"id": "NG1192", "symbol": "⥈", "fallback": "[CODEAS..]", "fallback_length": 10, "reason": "Long fallback"}, {"id": "NG1193", "symbol": "⩊", "fallback": "[CODEASDATAOP]", "fallback_length": 14, "reason": "Long fallback"}, {"id": "NG1194", "symbol": "⯚", "fallback": "[CODEASDATA1]", "fallback_length": 13, "reason": "Long fallback"}, {"id": "NG1195", "symbol": "⣹", "fallback": "[CODEASDATA2]", "fallback_length": 13, "reason": "Long fallback"}, {"id": "NG1196", "symbol": "⭊", "fallback": "[CODEASDATA3]", "fallback_length": 13, "reason": "Long fallback"}, {"id": "NG1197", "symbol": "⥯", "fallback": "[CODEAS..]", "fallback_length": 10, "reason": "Long fallback"}, {"id": "NG1198", "symbol": "⥍", "fallback": "[CODEASDATACORE1]", "fallback_length": 17, "reason": "Long fallback"}, {"id": "NG1199", "symbol": "⍋", "fallback": "[CODEASDATAOP1]", "fallback_length": 15, "reason": "Long fallback"}, {"id": "NG1200", "symbol": "⥋", "fallback": "[CODEASDATA4]", "fallback_length": 13, "reason": "Long fallback"}, {"id": "NG1201", "symbol": "⧋", "fallback": "[CODEASDATASYS1]", "fallback_length": 16, "reason": "Long fallback"}, {"id": "NG1202", "symbol": "⩋", "fallback": "[MACROSYSTEMSCTRL]", "fallback_length": 18, "reason": "Long fallback"}, {"id": "NG1203", "symbol": "⭋", "fallback": "[MACROSYSTEMSCTRL1]", "fallback_length": 19, "reason": "Long fallback"}, {"id": "NG1204", "symbol": "≌", "fallback": "[MACROSYSTEMSPROC]", "fallback_length": 18, "reason": "Long fallback"}, {"id": "NG1205", "symbol": "⍌", "fallback": "[MACROSYSTEMSFN]", "fallback_length": 16, "reason": "Long fallback"}, {"id": "NG1206", "symbol": "✽", "fallback": "[MACROSYSTEMSCTRL2]", "fallback_length": 19, "reason": "Long fallback"}, {"id": "NG1207", "symbol": "❌", "fallback": "[MACROSYSTEMS]", "fallback_length": 14, "reason": "Long fallback"}, {"id": "NG1208", "symbol": "⢁", "fallback": "[MACROS..]", "fallback_length": 10, "reason": "Long fallback"}, {"id": "NG1209", "symbol": "⥌", "fallback": "[MACROSYSTEMS1]", "fallback_length": 15, "reason": "Long fallback"}, {"id": "NG1210", "symbol": "≍", "fallback": "[MACROSYSTEMS2]", "fallback_length": 15, "reason": "Long fallback"}, {"id": "NG1211", "symbol": "⡶", "fallback": "[MACROS..]", "fallback_length": 10, "reason": "Long fallback"}, {"id": "NG1212", "symbol": "⦭", "fallback": "[MACROSYSTEMSOP1]", "fallback_length": 17, "reason": "Long fallback"}, {"id": "NG1213", "symbol": "⥅", "fallback": "[MACROSYSTEMS3]", "fallback_length": 15, "reason": "Long fallback"}, {"id": "NG1214", "symbol": "⢹", "fallback": "[MACROSYSTEMSOP2]", "fallback_length": 17, "reason": "Long fallback"}, {"id": "NG1215", "symbol": "⧛", "fallback": "[MACROSYSTEMSPROC1]", "fallback_length": 19, "reason": "Long fallback"}, {"id": "NG1216", "symbol": "⍍", "fallback": "[STAGEDCOMPUTATION]", "fallback_length": 19, "reason": "Long fallback"}, {"id": "NG1217", "symbol": "⧍", "fallback": "[STAGEDCOMPUTATION1]", "fallback_length": 20, "reason": "Long fallback"}, {"id": "NG1218", "symbol": "⮜", "fallback": "[STAGEDCOMPUTATION2]", "fallback_length": 20, "reason": "Long fallback"}, {"id": "NG1219", "symbol": "⩍", "fallback": "[STAGEDCOMPUTATION3]", "fallback_length": 20, "reason": "Long fallback"}, {"id": "NG1220", "symbol": "⭍", "fallback": "[STAGEDCOMPUTATION4]", "fallback_length": 20, "reason": "Long fallback"}, {"id": "NG1221", "symbol": "⬬", "fallback": "[STAGEDCOMPUTATION5]", "fallback_length": 20, "reason": "Long fallback"}, {"id": "NG1222", "symbol": "⍎", "fallback": "[STAGEDCOMPUTATION6]", "fallback_length": 20, "reason": "Long fallback"}, {"id": "NG1223", "symbol": "❎", "fallback": "[STAGEDCOMPUTATION7]", "fallback_length": 20, "reason": "Long fallback"}, {"id": "NG1224", "symbol": "⮘", "fallback": "[STAGEDCOMPUTATION8]", "fallback_length": 20, "reason": "Long fallback"}, {"id": "NG1225", "symbol": "⭎", "fallback": "[STAGEDCOMPUTATION9]", "fallback_length": 20, "reason": "Long fallback"}, {"id": "NG1226", "symbol": "⣘", "fallback": "[PARTIALEVALUATION1]", "fallback_length": 20, "reason": "Long fallback"}, {"id": "NG1227", "symbol": "⍏", "fallback": "[PARTIALEVALUATION2]", "fallback_length": 20, "reason": "Long fallback"}, {"id": "NG1228", "symbol": "❏", "fallback": "[PARTIALEVALUATION3]", "fallback_length": 20, "reason": "Long fallback"}, {"id": "NG1229", "symbol": "⧏", "fallback": "[PARTIALEVALUATION4]", "fallback_length": 20, "reason": "Long fallback"}, {"id": "NG1230", "symbol": "⭏", "fallback": "[PARTIALEVALUATION5]", "fallback_length": 20, "reason": "Long fallback"}, {"id": "NG1231", "symbol": "⍐", "fallback": "[PARTIALEVALUATION6]", "fallback_length": 20, "reason": "Long fallback"}, {"id": "NG1232", "symbol": "⣂", "fallback": "[PARTIALEVALUATION7]", "fallback_length": 20, "reason": "Long fallback"}, {"id": "NG1233", "symbol": "⡆", "fallback": "[PARTIALEVALUATION8]", "fallback_length": 20, "reason": "Long fallback"}, {"id": "NG1234", "symbol": "❐", "fallback": "[PARTIALEVALUATION9]", "fallback_length": 20, "reason": "Long fallback"}, {"id": "NG1235", "symbol": "⨹", "fallback": "[PROGRA..]", "fallback_length": 10, "reason": "Long fallback"}, {"id": "NG1236", "symbol": "⡭", "fallback": "[PROGRA..]", "fallback_length": 10, "reason": "Long fallback"}, {"id": "NG1237", "symbol": "⩟", "fallback": "[PROGRA..]", "fallback_length": 10, "reason": "Long fallback"}, {"id": "NG1238", "symbol": "⥐", "fallback": "[PROGRAMSYNTHESIS1]", "fallback_length": 19, "reason": "Long fallback"}, {"id": "NG1239", "symbol": "⧐", "fallback": "[PROGRAMSYNTHESIS2]", "fallback_length": 19, "reason": "Long fallback"}, {"id": "NG1240", "symbol": "⍑", "fallback": "[PROGRAMSYNTHESIS3]", "fallback_length": 19, "reason": "Long fallback"}, {"id": "NG1241", "symbol": "⦴", "fallback": "[PROGRAMSYNTHESIS4]", "fallback_length": 19, "reason": "Long fallback"}, {"id": "NG1242", "symbol": "⥑", "fallback": "[PROGRAMSYNTHESIS5]", "fallback_length": 19, "reason": "Long fallback"}, {"id": "NG1243", "symbol": "⩑", "fallback": "[PROGRAMSYNTHESIS6]", "fallback_length": 19, "reason": "Long fallback"}, {"id": "NG1244", "symbol": "⣩", "fallback": "[PROGRAMSYNTHESIS7]", "fallback_length": 19, "reason": "Long fallback"}, {"id": "NG1245", "symbol": "≒", "fallback": "[PROGRAMSYNTHESIS8]", "fallback_length": 19, "reason": "Long fallback"}, {"id": "NG1246", "symbol": "❥", "fallback": "[PROGRAMSYNTHESIS9]", "fallback_length": 19, "reason": "Long fallback"}, {"id": "NG1247", "symbol": "⍒", "fallback": "[PROGRAMSYNTHESIS10]", "fallback_length": 20, "reason": "Long fallback"}, {"id": "NG1248", "symbol": "⧥", "fallback": "[PROGRAMSYNTHESIS11]", "fallback_length": 20, "reason": "Long fallback"}, {"id": "NG1249", "symbol": "⭐", "fallback": "[CODETR..]", "fallback_length": 10, "reason": "Long fallback"}, {"id": "NG1250", "symbol": "⏚", "fallback": "[RAFTCORE]", "fallback_length": 10, "reason": "Long fallback"}, {"id": "NG1252", "symbol": "❒", "fallback": "[PAXOSCORE]", "fallback_length": 11, "reason": "Long fallback"}, {"id": "NG1253", "symbol": "⏛", "fallback": "[PBFTSYS]", "fallback_length": 9, "reason": "Long fallback"}, {"id": "NG1255", "symbol": "⮭", "fallback": "[CONSEN..]", "fallback_length": 10, "reason": "Long fallback"}, {"id": "NG1256", "symbol": "⧒", "fallback": "[CONSENSUSOP1]", "fallback_length": 14, "reason": "Long fallback"}, {"id": "NG1257", "symbol": "⭒", "fallback": "[DISTRIBUTEDLOCKSFN]", "fallback_length": 20, "reason": "Long fallback"}, {"id": "NG1258", "symbol": "⢵", "fallback": "[DISTRI..]", "fallback_length": 10, "reason": "Long fallback"}, {"id": "NG1259", "symbol": "⧴", "fallback": "[DISTRIBUTEDLOCKS1]", "fallback_length": 19, "reason": "Long fallback"}, {"id": "NG1260", "symbol": "⍓", "fallback": "[DISTRIBUTEDLOCKS3]", "fallback_length": 19, "reason": "Long fallback"}, {"id": "NG1261", "symbol": "⤡", "fallback": "[DISTRIBUTEDLOCKS4]", "fallback_length": 19, "reason": "Long fallback"}, {"id": "NG1262", "symbol": "⨋", "fallback": "[DISTRIBUTEDLOCKS5]", "fallback_length": 19, "reason": "Long fallback"}, {"id": "NG1263", "symbol": "✟", "fallback": "[VECTOR..]", "fallback_length": 10, "reason": "Long fallback"}, {"id": "NG1264", "symbol": "⥓", "fallback": "[VECTORCLOCKSCTRL]", "fallback_length": 18, "reason": "Long fallback"}, {"id": "NG1265", "symbol": "⭓", "fallback": "[VECTORCLOCKSSYS]", "fallback_length": 17, "reason": "Long fallback"}, {"id": "NG1266", "symbol": "⮊", "fallback": "[VECTORCLOCKSOP1]", "fallback_length": 17, "reason": "Long fallback"}, {"id": "NG1267", "symbol": "⣔", "fallback": "[VECTOR..]", "fallback_length": 10, "reason": "Long fallback"}, {"id": "NG1268", "symbol": "⍔", "fallback": "[VECTORCLOCKSPROC]", "fallback_length": 18, "reason": "Long fallback"}, {"id": "NG1269", "symbol": "⨤", "fallback": "[CAPTHE..]", "fallback_length": 10, "reason": "Long fallback"}, {"id": "NG1270", "symbol": "⥬", "fallback": "[CAPTHEOREMCORE2]", "fallback_length": 17, "reason": "Long fallback"}, {"id": "NG1271", "symbol": "❔", "fallback": "[CAPTHEOREMFN]", "fallback_length": 14, "reason": "Long fallback"}, {"id": "NG1272", "symbol": "✪", "fallback": "[CAPTHE..]", "fallback_length": 10, "reason": "Long fallback"}, {"id": "NG1273", "symbol": "⮹", "fallback": "[CAPTHE..]", "fallback_length": 10, "reason": "Long fallback"}, {"id": "NG1274", "symbol": "⡪", "fallback": "[CAPTHEOREMPROC1]", "fallback_length": 17, "reason": "Long fallback"}, {"id": "NG1275", "symbol": "⩔", "fallback": "[GOSSIPPROTOCOLSOP]", "fallback_length": 19, "reason": "Long fallback"}, {"id": "NG1276", "symbol": "⡡", "fallback": "[GOSSIPPROTOCOLS1]", "fallback_length": 18, "reason": "Long fallback"}, {"id": "NG1277", "symbol": "⦤", "fallback": "[GOSSIP..]", "fallback_length": 10, "reason": "Long fallback"}, {"id": "NG1278", "symbol": "⣙", "fallback": "[GOSSIPPROTOCOLSOP2]", "fallback_length": 20, "reason": "Long fallback"}, {"id": "NG1279", "symbol": "⥠", "fallback": "[GOSSIP..]", "fallback_length": 10, "reason": "Long fallback"}, {"id": "NG1280", "symbol": "⭔", "fallback": "[GOSSIPPROTOCOLSFN1]", "fallback_length": 20, "reason": "Long fallback"}, {"id": "NG1281", "symbol": "⪾", "fallback": "[GOSSIPPROTOCOLSFN2]", "fallback_length": 20, "reason": "Long fallback"}, {"id": "NG1282", "symbol": "⪂", "fallback": "[LEADER..]", "fallback_length": 10, "reason": "Long fallback"}, {"id": "NG1283", "symbol": "⍕", "fallback": "[LEADERELECTIONCORE]", "fallback_length": 20, "reason": "Long fallback"}, {"id": "NG1284", "symbol": "❕", "fallback": "[LEADERELECTIONCTRL]", "fallback_length": 20, "reason": "Long fallback"}, {"id": "NG1285", "symbol": "⪚", "fallback": "[LEADER..]", "fallback_length": 10, "reason": "Long fallback"}, {"id": "NG1286", "symbol": "⩕", "fallback": "[LEADERELECTIONOP]", "fallback_length": 18, "reason": "Long fallback"}, {"id": "NG1287", "symbol": "≖", "fallback": "[LEADERELECTIONMETA]", "fallback_length": 20, "reason": "Long fallback"}, {"id": "NG1288", "symbol": "⦷", "fallback": "[LEADERELECTIONOP1]", "fallback_length": 19, "reason": "Long fallback"}, {"id": "NG1289", "symbol": "⤥", "fallback": "[LEADER..]", "fallback_length": 10, "reason": "Long fallback"}, {"id": "NG1290", "symbol": "⍖", "fallback": "[SHARDINGCORE]", "fallback_length": 14, "reason": "Long fallback"}, {"id": "NG1291", "symbol": "❖", "fallback": "[SHARDINGFN]", "fallback_length": 12, "reason": "Long fallback"}, {"id": "NG1292", "symbol": "⥖", "fallback": "[SHARDINGSYS]", "fallback_length": 13, "reason": "Long fallback"}, {"id": "NG1293", "symbol": "⬪", "fallback": "[SHARDI..]", "fallback_length": 10, "reason": "Long fallback"}, {"id": "NG1294", "symbol": "⭖", "fallback": "[SHARDINGMETA1]", "fallback_length": 15, "reason": "Long fallback"}, {"id": "NG1295", "symbol": "⯊", "fallback": "[SHARDINGOP1]", "fallback_length": 13, "reason": "Long fallback"}, {"id": "NG1296", "symbol": "⍗", "fallback": "[SHARDINGOP2]", "fallback_length": 13, "reason": "Long fallback"}, {"id": "NG1297", "symbol": "⭗", "fallback": "[SHARDINGSYS1]", "fallback_length": 14, "reason": "Long fallback"}, {"id": "NG1298", "symbol": "⭦", "fallback": "[REPLIC..]", "fallback_length": 10, "reason": "Long fallback"}, {"id": "NG1299", "symbol": "⍘", "fallback": "[REPLICATIONSYS]", "fallback_length": 16, "reason": "Long fallback"}, {"id": "NG1300", "symbol": "⩘", "fallback": "[REPLICATIONCTRL]", "fallback_length": 17, "reason": "Long fallback"}, {"id": "NG1301", "symbol": "⯼", "fallback": "[REPLIC..]", "fallback_length": 10, "reason": "Long fallback"}, {"id": "NG1302", "symbol": "⧉", "fallback": "[REPLIC..]", "fallback_length": 10, "reason": "Long fallback"}, {"id": "NG1303", "symbol": "⭘", "fallback": "[REPLICATION1]", "fallback_length": 14, "reason": "Long fallback"}, {"id": "NG1304", "symbol": "⠋", "fallback": "[REPLICATIONSYS1]", "fallback_length": 17, "reason": "Long fallback"}, {"id": "NG1305", "symbol": "⍙", "fallback": "[LOADBALANCING]", "fallback_length": 15, "reason": "Long fallback"}, {"id": "NG1306", "symbol": "⩙", "fallback": "[LOADBALANCING1]", "fallback_length": 16, "reason": "Long fallback"}, {"id": "NG1307", "symbol": "⍚", "fallback": "[LOADBALANCINGSYS]", "fallback_length": 18, "reason": "Long fallback"}, {"id": "NG1308", "symbol": "⥚", "fallback": "[LOADBALANCINGOP]", "fallback_length": 17, "reason": "Long fallback"}, {"id": "NG1309", "symbol": "⤫", "fallback": "[LOADBA..]", "fallback_length": 10, "reason": "Long fallback"}, {"id": "NG1310", "symbol": "⧽", "fallback": "[LOADBA..]", "fallback_length": 10, "reason": "Long fallback"}, {"id": "NG1311", "symbol": "⧫", "fallback": "[LOADBA..]", "fallback_length": 10, "reason": "Long fallback"}, {"id": "NG1312", "symbol": "⦏", "fallback": "[LOADBALANCINGPROC1]", "fallback_length": 20, "reason": "Long fallback"}, {"id": "NG1313", "symbol": "⩚", "fallback": "[LOADBALANCINGCORE]", "fallback_length": 19, "reason": "Long fallback"}, {"id": "NG1314", "symbol": "⠗", "fallback": "[CIRCUI..]", "fallback_length": 10, "reason": "Long fallback"}, {"id": "NG1315", "symbol": "⥏", "fallback": "[CIRCUI..]", "fallback_length": 10, "reason": "Long fallback"}, {"id": "NG1316", "symbol": "≛", "fallback": "[CIRCUITBREAKERS]", "fallback_length": 17, "reason": "Long fallback"}, {"id": "NG1317", "symbol": "❚", "fallback": "[CIRCUI..]", "fallback_length": 10, "reason": "Long fallback"}, {"id": "NG1318", "symbol": "⢳", "fallback": "[CIRCUITBREAKERS1]", "fallback_length": 18, "reason": "Long fallback"}, {"id": "NG1319", "symbol": "⭛", "fallback": "[CIRCUITBREAKERS2]", "fallback_length": 18, "reason": "Long fallback"}, {"id": "NG1320", "symbol": "⍜", "fallback": "[CIRCUITBREAKERSFN1]", "fallback_length": 20, "reason": "Long fallback"}, {"id": "NG1321", "symbol": "⫵", "fallback": "[CIRCUITBREAKERSFN2]", "fallback_length": 20, "reason": "Long fallback"}, {"id": "NG1322", "symbol": "❡", "fallback": "[CIRCUITBREAKERSOP1]", "fallback_length": 20, "reason": "Long fallback"}, {"id": "NG1323", "symbol": "⥜", "fallback": "[CIRCUITBREAKERS3]", "fallback_length": 18, "reason": "Long fallback"}, {"id": "NG1324", "symbol": "⩜", "fallback": "[CIRCUITBREAKERSFN3]", "fallback_length": 20, "reason": "Long fallback"}, {"id": "NG1325", "symbol": "⩆", "fallback": "[CIRCUITBREAKERSOP3]", "fallback_length": 20, "reason": "Long fallback"}, {"id": "NG1326", "symbol": "❃", "fallback": "[CIRCUITBREAKERSFN4]", "fallback_length": 20, "reason": "Long fallback"}, {"id": "NG1327", "symbol": "❀", "fallback": "[CIRCUITBREAKERSOP4]", "fallback_length": 20, "reason": "Long fallback"}, {"id": "NG1328", "symbol": "⧝", "fallback": "[CIRCUITBREAKERSFN5]", "fallback_length": 20, "reason": "Long fallback"}, {"id": "NG1329", "symbol": "⢩", "fallback": "[CIRCUITBREAKERSOP5]", "fallback_length": 20, "reason": "Long fallback"}, {"id": "NG1330", "symbol": "≞", "fallback": "[CIRCUITBREAKERS4]", "fallback_length": 18, "reason": "Long fallback"}, {"id": "NG1331", "symbol": "⮢", "fallback": "[CIRCUITBREAKERSOP6]", "fallback_length": 20, "reason": "Long fallback"}, {"id": "NG1332", "symbol": "⡾", "fallback": "[CIRCUITBREAKERSFN6]", "fallback_length": 20, "reason": "Long fallback"}, {"id": "NG1333", "symbol": "⍞", "fallback": "[CIRCUITBREAKERSFN7]", "fallback_length": 20, "reason": "Long fallback"}, {"id": "NG1334", "symbol": "⤦", "fallback": "[CIRCUITBREAKERSOP7]", "fallback_length": 20, "reason": "Long fallback"}, {"id": "NG1335", "symbol": "◾", "fallback": "[CIRCUITBREAKERSFN8]", "fallback_length": 20, "reason": "Long fallback"}, {"id": "NG1336", "symbol": "✦", "fallback": "[CIRCUITBREAKERSFN9]", "fallback_length": 20, "reason": "Long fallback"}, {"id": "NG1337", "symbol": "⭞", "fallback": "[CIRCUITBREAKERS5]", "fallback_length": 18, "reason": "Long fallback"}, {"id": "NG1338", "symbol": "≟", "fallback": "[CIRCUITBREAKERS6]", "fallback_length": 18, "reason": "Long fallback"}, {"id": "NG1339", "symbol": "⍟", "fallback": "[CIRCUITBREAKERS7]", "fallback_length": 18, "reason": "Long fallback"}, {"id": "NG1340", "symbol": "❟", "fallback": "[CIRCUITBREAKERS9]", "fallback_length": 18, "reason": "Long fallback"}, {"id": "NG1341", "symbol": "⥎", "fallback": "[CIRCUITBREAKERS10]", "fallback_length": 19, "reason": "Long fallback"}, {"id": "NG1342", "symbol": "⍠", "fallback": "[CIRCUITBREAKERS11]", "fallback_length": 19, "reason": "Long fallback"}, {"id": "NG1343", "symbol": "⩠", "fallback": "[CIRCUITBREAKERS12]", "fallback_length": 19, "reason": "Long fallback"}, {"id": "NG1344", "symbol": "⦑", "fallback": "[CIRCUITBREAKERS13]", "fallback_length": 19, "reason": "Long fallback"}, {"id": "NG1345", "symbol": "⭠", "fallback": "[CIRCUITBREAKERSOP8]", "fallback_length": 20, "reason": "Long fallback"}, {"id": "NG1346", "symbol": "⍡", "fallback": "[CIRCUITBREAKERS14]", "fallback_length": 19, "reason": "Long fallback"}, {"id": "NG1347", "symbol": "⨿", "fallback": "[CIRCUITBREAKERSOP9]", "fallback_length": 20, "reason": "Long fallback"}, {"id": "NG1348", "symbol": "❓", "fallback": "[CIRCUITBREAKERS15]", "fallback_length": 19, "reason": "Long fallback"}, {"id": "NG1349", "symbol": "⩡", "fallback": "[CIRCUITBREAKERS16]", "fallback_length": 19, "reason": "Long fallback"}, {"id": "NG1350", "symbol": "⭡", "fallback": "[CIRCUITBREAKERS17]", "fallback_length": 19, "reason": "Long fallback"}, {"id": "NG1351", "symbol": "⍢", "fallback": "[CIRCUITBREAKERS18]", "fallback_length": 19, "reason": "Long fallback"}, {"id": "NG1352", "symbol": "⮕", "fallback": "[CIRCUITBREAKERS19]", "fallback_length": 19, "reason": "Long fallback"}, {"id": "NG1353", "symbol": "❢", "fallback": "[CIRCUITBREAKERS20]", "fallback_length": 19, "reason": "Long fallback"}, {"id": "NG1354", "symbol": "⪧", "fallback": "[CIRCUITBREAKERS21]", "fallback_length": 19, "reason": "Long fallback"}, {"id": "NG1355", "symbol": "≣", "fallback": "[CIRCUITBREAKERS22]", "fallback_length": 19, "reason": "Long fallback"}, {"id": "NG1356", "symbol": "⭑", "fallback": "[CIRCUITBREAKERS23]", "fallback_length": 19, "reason": "Long fallback"}, {"id": "NG1357", "symbol": "⍣", "fallback": "[CIRCUITBREAKERS25]", "fallback_length": 19, "reason": "Long fallback"}, {"id": "NG1359", "symbol": "⤎", "fallback": "[PAULIOP]", "fallback_length": 9, "reason": "Long fallback"}, {"id": "NG1360", "symbol": "⠰", "fallback": "[HADAMA..]", "fallback_length": 10, "reason": "Long fallback"}, {"id": "NG1361", "symbol": "⏝", "fallback": "[CNOTPROC]", "fallback_length": 10, "reason": "Long fallback"}, {"id": "NG1362", "symbol": "⤑", "fallback": "[PAULIOP1]", "fallback_length": 10, "reason": "Long fallback"}, {"id": "NG1363", "symbol": "⯝", "fallback": "[QGATEOP]", "fallback_length": 9, "reason": "Long fallback"}, {"id": "NG1364", "symbol": "⮨", "fallback": "[QUANTU..]", "fallback_length": 10, "reason": "Long fallback"}, {"id": "NG1365", "symbol": "⫔", "fallback": "[QUANTU..]", "fallback_length": 10, "reason": "Long fallback"}, {"id": "NG1366", "symbol": "⩣", "fallback": "[QUANTUMCIRCUITSFN1]", "fallback_length": 20, "reason": "Long fallback"}, {"id": "NG1367", "symbol": "⥰", "fallback": "[QUANTUMCIRCUITSFN2]", "fallback_length": 20, "reason": "Long fallback"}, {"id": "NG1368", "symbol": "⭣", "fallback": "[QUANTUMCIRCUITSOP1]", "fallback_length": 20, "reason": "Long fallback"}, {"id": "NG1369", "symbol": "≤", "fallback": "[QUANTUMCIRCUITSFN3]", "fallback_length": 20, "reason": "Long fallback"}, {"id": "NG1370", "symbol": "⭕", "fallback": "[SUPERP..]", "fallback_length": 10, "reason": "Long fallback"}, {"id": "NG1371", "symbol": "⡄", "fallback": "[SUPERP..]", "fallback_length": 10, "reason": "Long fallback"}, {"id": "NG1372", "symbol": "⍤", "fallback": "[SUPERPOSITION1]", "fallback_length": 16, "reason": "Long fallback"}, {"id": "NG1373", "symbol": "⥤", "fallback": "[SUPERPOSITIONFN]", "fallback_length": 17, "reason": "Long fallback"}, {"id": "NG1374", "symbol": "⦨", "fallback": "[SUPERPOSITION2]", "fallback_length": 16, "reason": "Long fallback"}, {"id": "NG1375", "symbol": "⮎", "fallback": "[ENTANG..]", "fallback_length": 10, "reason": "Long fallback"}, {"id": "NG1376", "symbol": "⩤", "fallback": "[ENTANGLEMENTFN]", "fallback_length": 16, "reason": "Long fallback"}, {"id": "NG1377", "symbol": "❞", "fallback": "[ENTANG..]", "fallback_length": 10, "reason": "Long fallback"}, {"id": "NG1378", "symbol": "⪩", "fallback": "[ENTANG..]", "fallback_length": 10, "reason": "Long fallback"}, {"id": "NG1379", "symbol": "✃", "fallback": "[ENTANGLEMENTFN1]", "fallback_length": 17, "reason": "Long fallback"}, {"id": "NG1380", "symbol": "⮀", "fallback": "[ENTANG..]", "fallback_length": 10, "reason": "Long fallback"}, {"id": "NG1381", "symbol": "⤉", "fallback": "[QUANTU..]", "fallback_length": 10, "reason": "Long fallback"}, {"id": "NG1382", "symbol": "⍥", "fallback": "[QUANTUMALGORITHMS2]", "fallback_length": 20, "reason": "Long fallback"}, {"id": "NG1383", "symbol": "⥥", "fallback": "[QUANTUMALGORITHMS3]", "fallback_length": 20, "reason": "Long fallback"}, {"id": "NG1384", "symbol": "⭸", "fallback": "[QUANTUMALGORITHMS4]", "fallback_length": 20, "reason": "Long fallback"}, {"id": "NG1385", "symbol": "⭤", "fallback": "[QUANTUMALGORITHMS6]", "fallback_length": 20, "reason": "Long fallback"}, {"id": "NG1386", "symbol": "⩥", "fallback": "[LOGICALINFERENCEFN]", "fallback_length": 20, "reason": "Long fallback"}, {"id": "NG1387", "symbol": "⭥", "fallback": "[LOGICALINFERENCEOP]", "fallback_length": 20, "reason": "Long fallback"}, {"id": "NG1388", "symbol": "≦", "fallback": "[LOGICALINFERENCE]", "fallback_length": 18, "reason": "Long fallback"}, {"id": "NG1389", "symbol": "⍦", "fallback": "[LOGICALINFERENCE3]", "fallback_length": 19, "reason": "Long fallback"}, {"id": "NG1390", "symbol": "✹", "fallback": "[LOGICALINFERENCE4]", "fallback_length": 19, "reason": "Long fallback"}, {"id": "NG1391", "symbol": "⤓", "fallback": "[LOGICALINFERENCE5]", "fallback_length": 19, "reason": "Long fallback"}, {"id": "NG1392", "symbol": "❦", "fallback": "[LOGICALINFERENCE7]", "fallback_length": 19, "reason": "Long fallback"}, {"id": "NG1393", "symbol": "❴", "fallback": "[THEORE..]", "fallback_length": 10, "reason": "Long fallback"}, {"id": "NG1394", "symbol": "⧜", "fallback": "[THEORE..]", "fallback_length": 10, "reason": "Long fallback"}, {"id": "NG1395", "symbol": "⫴", "fallback": "[THEOREMPROVINGSYS1]", "fallback_length": 20, "reason": "Long fallback"}, {"id": "NG1396", "symbol": "⣥", "fallback": "[THEORE..]", "fallback_length": 10, "reason": "Long fallback"}, {"id": "NG1397", "symbol": "⩖", "fallback": "[THEOREMPROVINGSYS2]", "fallback_length": 20, "reason": "Long fallback"}, {"id": "NG1398", "symbol": "⥛", "fallback": "[THEOREMPROVING1]", "fallback_length": 17, "reason": "Long fallback"}, {"id": "NG1399", "symbol": "⥦", "fallback": "[THEOREMPROVINGMETA]", "fallback_length": 20, "reason": "Long fallback"}, {"id": "NG1400", "symbol": "⧦", "fallback": "[THEOREMPROVINGSYS3]", "fallback_length": 20, "reason": "Long fallback"}, {"id": "NG1401", "symbol": "≧", "fallback": "[EXPERTSYSTEMSPROC]", "fallback_length": 19, "reason": "Long fallback"}, {"id": "NG1402", "symbol": "⍧", "fallback": "[EXPERTSYSTEMSCTRL]", "fallback_length": 19, "reason": "Long fallback"}, {"id": "NG1403", "symbol": "❧", "fallback": "[EXPERTSYSTEMSCORE1]", "fallback_length": 20, "reason": "Long fallback"}, {"id": "NG1404", "symbol": "⥀", "fallback": "[EXPERTSYSTEMSMETA1]", "fallback_length": 20, "reason": "Long fallback"}, {"id": "NG1405", "symbol": "⧎", "fallback": "[EXPERTSYSTEMSPROC2]", "fallback_length": 20, "reason": "Long fallback"}, {"id": "NG1406", "symbol": "⥧", "fallback": "[EXPERTSYSTEMSSYS]", "fallback_length": 18, "reason": "Long fallback"}, {"id": "NG1407", "symbol": "⧧", "fallback": "[EXPERTSYSTEMSOP]", "fallback_length": 17, "reason": "Long fallback"}, {"id": "NG1408", "symbol": "⥶", "fallback": "[EXPERTSYSTEMSMETA2]", "fallback_length": 20, "reason": "Long fallback"}, {"id": "NG1409", "symbol": "⭧", "fallback": "[SEMANTICNETWORKSFN]", "fallback_length": 20, "reason": "Long fallback"}, {"id": "NG1410", "symbol": "≨", "fallback": "[SEMANTICNETWORKS3]", "fallback_length": 19, "reason": "Long fallback"}, {"id": "NG1411", "symbol": "⍨", "fallback": "[SEMANTICNETWORKS4]", "fallback_length": 19, "reason": "Long fallback"}, {"id": "NG1412", "symbol": "❨", "fallback": "[SEMANTICNETWORKS5]", "fallback_length": 19, "reason": "Long fallback"}, {"id": "NG1413", "symbol": "✌", "fallback": "[SEMANTICNETWORKS6]", "fallback_length": 19, "reason": "Long fallback"}, {"id": "NG1414", "symbol": "⭨", "fallback": "[SEMANTICNETWORKS7]", "fallback_length": 19, "reason": "Long fallback"}, {"id": "NG1415", "symbol": "⥒", "fallback": "[SEMANTICNETWORKS8]", "fallback_length": 19, "reason": "Long fallback"}, {"id": "NG1416", "symbol": "⨶", "fallback": "[ONTOLO..]", "fallback_length": 10, "reason": "Long fallback"}, {"id": "NG1417", "symbol": "⬛", "fallback": "[ONTOLOGIESSYS1]", "fallback_length": 16, "reason": "Long fallback"}, {"id": "NG1418", "symbol": "⮃", "fallback": "[ONTOLO..]", "fallback_length": 10, "reason": "Long fallback"}, {"id": "NG1419", "symbol": "➪", "fallback": "[ONTOLOGIESSYS2]", "fallback_length": 16, "reason": "Long fallback"}, {"id": "NG1420", "symbol": "≩", "fallback": "[ONTOLOGIESSYS3]", "fallback_length": 16, "reason": "Long fallback"}, {"id": "NG1421", "symbol": "⍩", "fallback": "[ONTOLOGIESCORE2]", "fallback_length": 17, "reason": "Long fallback"}, {"id": "NG1422", "symbol": "⭩", "fallback": "[DESCRIPTIONLOGICS1]", "fallback_length": 20, "reason": "Long fallback"}, {"id": "NG1423", "symbol": "⍪", "fallback": "[DESCRIPTIONLOGICS2]", "fallback_length": 20, "reason": "Long fallback"}, {"id": "NG1424", "symbol": "❪", "fallback": "[DESCRIPTIONLOGICS4]", "fallback_length": 20, "reason": "Long fallback"}, {"id": "NG1425", "symbol": "⧪", "fallback": "[DESCRIPTIONLOGICS5]", "fallback_length": 20, "reason": "Long fallback"}, {"id": "NG1426", "symbol": "⩪", "fallback": "[DESCRIPTIONLOGICS6]", "fallback_length": 20, "reason": "Long fallback"}, {"id": "NG1427", "symbol": "⥡", "fallback": "[DESCRIPTIONLOGICS7]", "fallback_length": 20, "reason": "Long fallback"}, {"id": "NG1428", "symbol": "⍫", "fallback": "[DESCRIPTIONLOGICS8]", "fallback_length": 20, "reason": "Long fallback"}, {"id": "NG1429", "symbol": "⯯", "fallback": "[AUTOMA..]", "fallback_length": 10, "reason": "Long fallback"}, {"id": "NG1430", "symbol": "⩫", "fallback": "[MULTIHEADSYS]", "fallback_length": 14, "reason": "Long fallback"}, {"id": "NG1431", "symbol": "⭫", "fallback": "[MULTIHEADMETA]", "fallback_length": 15, "reason": "Long fallback"}, {"id": "NG1432", "symbol": "⯉", "fallback": "[MULTIH..]", "fallback_length": 10, "reason": "Long fallback"}, {"id": "NG1433", "symbol": "⍬", "fallback": "[ATTENTIONSYS]", "fallback_length": 14, "reason": "Long fallback"}, {"id": "NG1434", "symbol": "⥞", "fallback": "[ATTENT..]", "fallback_length": 10, "reason": "Long fallback"}, {"id": "NG1435", "symbol": "⩬", "fallback": "[CROSSATTNPROC]", "fallback_length": 15, "reason": "Long fallback"}, {"id": "NG1436", "symbol": "⮺", "fallback": "[ATTENTIONOP1]", "fallback_length": 14, "reason": "Long fallback"}, {"id": "NG1437", "symbol": "⩿", "fallback": "[TRANSF..]", "fallback_length": 10, "reason": "Long fallback"}, {"id": "NG1438", "symbol": "⦍", "fallback": "[TRANSFORMERBLOCKS1]", "fallback_length": 20, "reason": "Long fallback"}, {"id": "NG1439", "symbol": "⭬", "fallback": "[TRANSFORMERBLOCKS3]", "fallback_length": 20, "reason": "Long fallback"}, {"id": "NG1440", "symbol": "⥕", "fallback": "[TRANSFORMERBLOCKS4]", "fallback_length": 20, "reason": "Long fallback"}, {"id": "NG1441", "symbol": "⍭", "fallback": "[TRANSFORMERBLOCKS5]", "fallback_length": 20, "reason": "Long fallback"}, {"id": "NG1442", "symbol": "⭪", "fallback": "[TRANSFORMERBLOCKS7]", "fallback_length": 20, "reason": "Long fallback"}, {"id": "NG1443", "symbol": "➷", "fallback": "[LOSSFU..]", "fallback_length": 10, "reason": "Long fallback"}, {"id": "NG1444", "symbol": "❭", "fallback": "[LOSSFUNCTIONSPROC]", "fallback_length": 19, "reason": "Long fallback"}, {"id": "NG1445", "symbol": "⥭", "fallback": "[LOSSFUNCTIONSCORE]", "fallback_length": 19, "reason": "Long fallback"}, {"id": "NG1446", "symbol": "⧭", "fallback": "[LOSSFUNCTIONSMETA1]", "fallback_length": 20, "reason": "Long fallback"}, {"id": "NG1447", "symbol": "⏞", "fallback": "[CTLMETA]", "fallback_length": 9, "reason": "Long fallback"}, {"id": "NG1448", "symbol": "⫞", "fallback": "[TEMPORAL]", "fallback_length": 10, "reason": "Long fallback"}, {"id": "NG1449", "symbol": "≮", "fallback": "[MODELCHECKFN]", "fallback_length": 14, "reason": "Long fallback"}, {"id": "NG1450", "symbol": "➡", "fallback": "[MODELC..]", "fallback_length": 10, "reason": "Long fallback"}, {"id": "NG1451", "symbol": "⣫", "fallback": "[VERIFY..]", "fallback_length": 10, "reason": "Long fallback"}, {"id": "NG1452", "symbol": "⫡", "fallback": "[TEMPOR..]", "fallback_length": 10, "reason": "Long fallback"}, {"id": "NG1453", "symbol": "⍮", "fallback": "[THEOREMPROVINGFN]", "fallback_length": 18, "reason": "Long fallback"}, {"id": "NG1454", "symbol": "⥢", "fallback": "[THEORE..]", "fallback_length": 10, "reason": "Long fallback"}, {"id": "NG1455", "symbol": "⩮", "fallback": "[THEOREMPROVING]", "fallback_length": 16, "reason": "Long fallback"}, {"id": "NG1456", "symbol": "⭮", "fallback": "[THEOREMPROVINGOP]", "fallback_length": 18, "reason": "Long fallback"}, {"id": "NG1457", "symbol": "⩞", "fallback": "[THEOREMPROVINGFN1]", "fallback_length": 19, "reason": "Long fallback"}, {"id": "NG1458", "symbol": "⍯", "fallback": "[STATICANALYSIS]", "fallback_length": 16, "reason": "Long fallback"}, {"id": "NG1459", "symbol": "➛", "fallback": "[STATIC..]", "fallback_length": 10, "reason": "Long fallback"}, {"id": "NG1460", "symbol": "⦱", "fallback": "[STATIC..]", "fallback_length": 10, "reason": "Long fallback"}, {"id": "NG1461", "symbol": "❯", "fallback": "[STATICANALYSIS1]", "fallback_length": 17, "reason": "Long fallback"}, {"id": "NG1462", "symbol": "⩯", "fallback": "[STATICANALYSISMETA]", "fallback_length": 20, "reason": "Long fallback"}, {"id": "NG1463", "symbol": "≰", "fallback": "[SYMBOLICEXECUTION]", "fallback_length": 19, "reason": "Long fallback"}, {"id": "NG1464", "symbol": "⡧", "fallback": "[SYMBOLICEXECUTION1]", "fallback_length": 20, "reason": "Long fallback"}, {"id": "NG1465", "symbol": "⪁", "fallback": "[SYMBOLICEXECUTION3]", "fallback_length": 20, "reason": "Long fallback"}, {"id": "NG1466", "symbol": "⍰", "fallback": "[SYMBOLICEXECUTION4]", "fallback_length": 20, "reason": "Long fallback"}, {"id": "NG1467", "symbol": "⍱", "fallback": "[TEMPORALLOGICFN]", "fallback_length": 17, "reason": "Long fallback"}, {"id": "NG1468", "symbol": "⥱", "fallback": "[TEMPORALLOGICPROC]", "fallback_length": 19, "reason": "Long fallback"}, {"id": "NG1469", "symbol": "✔", "fallback": "[TEMPORALLOGICCTRL1]", "fallback_length": 20, "reason": "Long fallback"}, {"id": "NG1470", "symbol": "⩱", "fallback": "[HOARELOGICMETA]", "fallback_length": 16, "reason": "Long fallback"}, {"id": "NG1471", "symbol": "⩗", "fallback": "[HOAREL..]", "fallback_length": 10, "reason": "Long fallback"}, {"id": "NG1472", "symbol": "⤜", "fallback": "[HOAREL..]", "fallback_length": 10, "reason": "Long fallback"}, {"id": "NG1473", "symbol": "⍲", "fallback": "[HOARELOGICMETA1]", "fallback_length": 17, "reason": "Long fallback"}, {"id": "NG1474", "symbol": "⡠", "fallback": "[HOARELOGICMETA2]", "fallback_length": 17, "reason": "Long fallback"}, {"id": "NG1475", "symbol": "❲", "fallback": "[HOARELOGICSYS]", "fallback_length": 15, "reason": "Long fallback"}, {"id": "NG1476", "symbol": "⩲", "fallback": "[SEPARATIONLOGICFN]", "fallback_length": 19, "reason": "Long fallback"}, {"id": "NG1477", "symbol": "≳", "fallback": "[SEPARATIONLOGICFN1]", "fallback_length": 20, "reason": "Long fallback"}, {"id": "NG1478", "symbol": "⍳", "fallback": "[SEPARATIONLOGIC]", "fallback_length": 17, "reason": "Long fallback"}, {"id": "NG1479", "symbol": "❳", "fallback": "[SEPARATIONLOGIC1]", "fallback_length": 18, "reason": "Long fallback"}, {"id": "NG1480", "symbol": "⦰", "fallback": "[SEPARATIONLOGIC3]", "fallback_length": 18, "reason": "Long fallback"}, {"id": "NG1481", "symbol": "⩳", "fallback": "[SEPARATIONLOGIC4]", "fallback_length": 18, "reason": "Long fallback"}, {"id": "NG1482", "symbol": "⭳", "fallback": "[SEPARATIONLOGIC5]", "fallback_length": 18, "reason": "Long fallback"}, {"id": "NG1483", "symbol": "≴", "fallback": "[SEPARATIONLOGICFN2]", "fallback_length": 20, "reason": "Long fallback"}, {"id": "NG1484", "symbol": "⨐", "fallback": "[SEPARATIONLOGIC6]", "fallback_length": 18, "reason": "Long fallback"}, {"id": "NG1485", "symbol": "⍴", "fallback": "[SEPARATIONLOGICOP]", "fallback_length": 19, "reason": "Long fallback"}, {"id": "NG1486", "symbol": "⨲", "fallback": "[SEPARATIONLOGICFN3]", "fallback_length": 20, "reason": "Long fallback"}, {"id": "NG1487", "symbol": "⩴", "fallback": "[SEPARATIONLOGIC7]", "fallback_length": 18, "reason": "Long fallback"}, {"id": "NG1488", "symbol": "⭴", "fallback": "[SEPARATIONLOGICFN4]", "fallback_length": 20, "reason": "Long fallback"}, {"id": "NG1489", "symbol": "⍵", "fallback": "[SEPARATIONLOGIC8]", "fallback_length": 18, "reason": "Long fallback"}, {"id": "NG1490", "symbol": "✭", "fallback": "[SEPARATIONLOGICOP1]", "fallback_length": 20, "reason": "Long fallback"}, {"id": "NG1491", "symbol": "❝", "fallback": "[SEPARATIONLOGIC9]", "fallback_length": 18, "reason": "Long fallback"}, {"id": "NG1492", "symbol": "⥪", "fallback": "[SEPARATIONLOGICFN5]", "fallback_length": 20, "reason": "Long fallback"}, {"id": "NG1493", "symbol": "❵", "fallback": "[SEPARATIONLOGICFN6]", "fallback_length": 20, "reason": "Long fallback"}, {"id": "NG1494", "symbol": "⧵", "fallback": "[SEPARATIONLOGIC10]", "fallback_length": 19, "reason": "Long fallback"}, {"id": "NG1495", "symbol": "⩵", "fallback": "[SEPARATIONLOGICFN7]", "fallback_length": 20, "reason": "Long fallback"}, {"id": "NG1496", "symbol": "⯞", "fallback": "[MAPMETA]", "fallback_length": 9, "reason": "Long fallback"}, {"id": "NG1498", "symbol": "⋠", "fallback": "[FUNCTOR]", "fallback_length": 9, "reason": "Long fallback"}, {"id": "NG1499", "symbol": "⭵", "fallback": "[FUNCTOROP]", "fallback_length": 11, "reason": "Long fallback"}, {"id": "NG1500", "symbol": "⏠", "fallback": "[FMAPMETA]", "fallback_length": 10, "reason": "Long fallback"}, {"id": "NG1501", "symbol": "≶", "fallback": "[MONADSSYS]", "fallback_length": 11, "reason": "Long fallback"}, {"id": "NG1502", "symbol": "⦺", "fallback": "[MONADS..]", "fallback_length": 10, "reason": "Long fallback"}, {"id": "NG1503", "symbol": "⫛", "fallback": "[MONADS..]", "fallback_length": 10, "reason": "Long fallback"}, {"id": "NG1504", "symbol": "⤖", "fallback": "[MONADSOP1]", "fallback_length": 11, "reason": "Long fallback"}, {"id": "NG1505", "symbol": "⍶", "fallback": "[COMONADSCORE]", "fallback_length": 14, "reason": "Long fallback"}, {"id": "NG1506", "symbol": "❶", "fallback": "[COMONADSCORE1]", "fallback_length": 15, "reason": "Long fallback"}, {"id": "NG1507", "symbol": "⧤", "fallback": "[COMONA..]", "fallback_length": 10, "reason": "Long fallback"}, {"id": "NG1508", "symbol": "⧶", "fallback": "[COMONADSSYS]", "fallback_length": 13, "reason": "Long fallback"}, {"id": "NG1509", "symbol": "⦜", "fallback": "[COMONADSMETA1]", "fallback_length": 15, "reason": "Long fallback"}, {"id": "NG1510", "symbol": "➚", "fallback": "[COMONADSCORE2]", "fallback_length": 15, "reason": "Long fallback"}, {"id": "NG1511", "symbol": "⭶", "fallback": "[ADJUNCTIONSPROC]", "fallback_length": 17, "reason": "Long fallback"}, {"id": "NG1512", "symbol": "≷", "fallback": "[ADJUNCTIONS1]", "fallback_length": 14, "reason": "Long fallback"}, {"id": "NG1513", "symbol": "⢉", "fallback": "[ADJUNCTIONS2]", "fallback_length": 14, "reason": "Long fallback"}, {"id": "NG1514", "symbol": "⍷", "fallback": "[ADJUNCTIONSCORE]", "fallback_length": 17, "reason": "Long fallback"}, {"id": "NG1515", "symbol": "⫕", "fallback": "[LIMITS..]", "fallback_length": 10, "reason": "Long fallback"}, {"id": "NG1516", "symbol": "❷", "fallback": "[LIMITSMETA]", "fallback_length": 12, "reason": "Long fallback"}, {"id": "NG1517", "symbol": "⫠", "fallback": "[LIMITSFN]", "fallback_length": 10, "reason": "Long fallback"}, {"id": "NG1518", "symbol": "⫲", "fallback": "[LIMITSMETA1]", "fallback_length": 13, "reason": "Long fallback"}, {"id": "NG1519", "symbol": "⥷", "fallback": "[LIMITSSYS1]", "fallback_length": 12, "reason": "Long fallback"}, {"id": "NG1520", "symbol": "≸", "fallback": "[COLIMITSMETA]", "fallback_length": 14, "reason": "Long fallback"}, {"id": "NG1521", "symbol": "❸", "fallback": "[COLIMITSFN]", "fallback_length": 12, "reason": "Long fallback"}, {"id": "NG1522", "symbol": "➿", "fallback": "[COLIMITSMETA1]", "fallback_length": 15, "reason": "Long fallback"}, {"id": "NG1523", "symbol": "⧸", "fallback": "[COLIMITSCTRL]", "fallback_length": 14, "reason": "Long fallback"}, {"id": "NG1524", "symbol": "⩸", "fallback": "[TOPOICORE]", "fallback_length": 11, "reason": "Long fallback"}, {"id": "NG1525", "symbol": "⯠", "fallback": "[TOPOIOP]", "fallback_length": 9, "reason": "Long fallback"}, {"id": "NG1526", "symbol": "⋡", "fallback": "[TOPOIFN]", "fallback_length": 9, "reason": "Long fallback"}, {"id": "NG1527", "symbol": "⍹", "fallback": "[TOPOICORE1]", "fallback_length": 12, "reason": "Long fallback"}, {"id": "NG1528", "symbol": "⤬", "fallback": "[TOPOIM..]", "fallback_length": 10, "reason": "Long fallback"}, {"id": "NG1529", "symbol": "❹", "fallback": "[SHEAVESOP]", "fallback_length": 11, "reason": "Long fallback"}, {"id": "NG1530", "symbol": "⥹", "fallback": "[SHEAVESCTRL]", "fallback_length": 13, "reason": "Long fallback"}, {"id": "NG1531", "symbol": "⩹", "fallback": "[SHEAVESCTRL1]", "fallback_length": 14, "reason": "Long fallback"}, {"id": "NG1532", "symbol": "⭹", "fallback": "[SHEAVESFN]", "fallback_length": 11, "reason": "Long fallback"}, {"id": "NG1533", "symbol": "⍺", "fallback": "[SHEAVESSYS]", "fallback_length": 12, "reason": "Long fallback"}, {"id": "NG1534", "symbol": "⣳", "fallback": "[SHEAVESCTRL2]", "fallback_length": 14, "reason": "Long fallback"}, {"id": "NG1535", "symbol": "❺", "fallback": "[SHEAVESOP2]", "fallback_length": 12, "reason": "Long fallback"}, {"id": "NG1536", "symbol": "✻", "fallback": "[SHEAVESFN1]", "fallback_length": 12, "reason": "Long fallback"}, {"id": "NG1537", "symbol": "⡦", "fallback": "[SHEAVESOP3]", "fallback_length": 12, "reason": "Long fallback"}, {"id": "NG1538", "symbol": "⥺", "fallback": "[SHEAVESPROC]", "fallback_length": 13, "reason": "Long fallback"}, {"id": "NG1539", "symbol": "⩺", "fallback": "[SIGMATYPEPROC]", "fallback_length": 15, "reason": "Long fallback"}, {"id": "NG1540", "symbol": "⭺", "fallback": "[SIGMATYPECORE]", "fallback_length": 15, "reason": "Long fallback"}, {"id": "NG1541", "symbol": "≻", "fallback": "[DEPPAIROP]", "fallback_length": 11, "reason": "Long fallback"}, {"id": "NG1542", "symbol": "⫌", "fallback": "[PITYPE..]", "fallback_length": 10, "reason": "Long fallback"}, {"id": "NG1543", "symbol": "⦮", "fallback": "[INDEXE..]", "fallback_length": 10, "reason": "Long fallback"}, {"id": "NG1544", "symbol": "⍻", "fallback": "[LINEARTYPESPROC]", "fallback_length": 17, "reason": "Long fallback"}, {"id": "NG1545", "symbol": "⧙", "fallback": "[LINEAR..]", "fallback_length": 10, "reason": "Long fallback"}, {"id": "NG1546", "symbol": "⨈", "fallback": "[LINEAR..]", "fallback_length": 10, "reason": "Long fallback"}, {"id": "NG1547", "symbol": "⠴", "fallback": "[LINEAR..]", "fallback_length": 10, "reason": "Long fallback"}, {"id": "NG1548", "symbol": "⡍", "fallback": "[LINEARTYPESMETA1]", "fallback_length": 18, "reason": "Long fallback"}, {"id": "NG1549", "symbol": "⠾", "fallback": "[SESSIO..]", "fallback_length": 10, "reason": "Long fallback"}, {"id": "NG1550", "symbol": "⬘", "fallback": "[SESSIO..]", "fallback_length": 10, "reason": "Long fallback"}, {"id": "NG1551", "symbol": "⥩", "fallback": "[SESSIO..]", "fallback_length": 10, "reason": "Long fallback"}, {"id": "NG1552", "symbol": "⦾", "fallback": "[SESSIONTYPESCORE1]", "fallback_length": 19, "reason": "Long fallback"}, {"id": "NG1553", "symbol": "❻", "fallback": "[SESSIONTYPESOP1]", "fallback_length": 17, "reason": "Long fallback"}, {"id": "NG1554", "symbol": "⥻", "fallback": "[SESSIONTYPES]", "fallback_length": 14, "reason": "Long fallback"}, {"id": "NG1555", "symbol": "𝝯", "fallback": "[EFFECTTYPESSYS1]", "fallback_length": 17, "reason": "Long fallback"}, {"id": "NG1556", "symbol": "⯓", "fallback": "[EFFECTTYPESSYS2]", "fallback_length": 17, "reason": "Long fallback"}, {"id": "NG1557", "symbol": "⩻", "fallback": "[EFFECTTYPESCTRL]", "fallback_length": 17, "reason": "Long fallback"}, {"id": "NG1558", "symbol": "⭻", "fallback": "[EFFECTTYPESPROC]", "fallback_length": 17, "reason": "Long fallback"}, {"id": "NG1559", "symbol": "≼", "fallback": "[REFINEMENTTYPESOP]", "fallback_length": 19, "reason": "Long fallback"}, {"id": "NG1560", "symbol": "⍼", "fallback": "[REFINEMENTTYPES1]", "fallback_length": 18, "reason": "Long fallback"}, {"id": "NG1561", "symbol": "⡟", "fallback": "[REFINE..]", "fallback_length": 10, "reason": "Long fallback"}, {"id": "NG1562", "symbol": "⣉", "fallback": "[REFINEMENTTYPESFN1]", "fallback_length": 20, "reason": "Long fallback"}, {"id": "NG1563", "symbol": "❼", "fallback": "[REFINEMENTTYPESFN2]", "fallback_length": 20, "reason": "Long fallback"}, {"id": "NG1564", "symbol": "⩼", "fallback": "[INTERSECTIONTYPES1]", "fallback_length": 20, "reason": "Long fallback"}, {"id": "NG1565", "symbol": "⥟", "fallback": "[INTERSECTIONTYPES2]", "fallback_length": 20, "reason": "Long fallback"}, {"id": "NG1566", "symbol": "⣿", "fallback": "[INTERSECTIONTYPES4]", "fallback_length": 20, "reason": "Long fallback"}, {"id": "NG1567", "symbol": "⮯", "fallback": "[UNIONT..]", "fallback_length": 10, "reason": "Long fallback"}, {"id": "NG1568", "symbol": "✠", "fallback": "[UNIONT..]", "fallback_length": 10, "reason": "Long fallback"}, {"id": "NG1569", "symbol": "⍽", "fallback": "[UNIONTYPES1]", "fallback_length": 13, "reason": "Long fallback"}, {"id": "NG1570", "symbol": "❽", "fallback": "[UNIONTYPESMETA1]", "fallback_length": 17, "reason": "Long fallback"}, {"id": "NG1571", "symbol": "⩈", "fallback": "[GRADUALTYPINGCORE1]", "fallback_length": 20, "reason": "Long fallback"}, {"id": "NG1572", "symbol": "⢲", "fallback": "[GRADUA..]", "fallback_length": 10, "reason": "Long fallback"}, {"id": "NG1573", "symbol": "⡲", "fallback": "[GRADUALTYPINGPROC1]", "fallback_length": 20, "reason": "Long fallback"}, {"id": "NG1574", "symbol": "⣱", "fallback": "[GRADUA..]", "fallback_length": 10, "reason": "Long fallback"}, {"id": "NG1575", "symbol": "⩽", "fallback": "[GRADUALTYPINGFN]", "fallback_length": 17, "reason": "Long fallback"}, {"id": "NG1577", "symbol": "⏡", "fallback": "[SPAWNOP]", "fallback_length": 9, "reason": "Long fallback"}, {"id": "NG1578", "symbol": "⍾", "fallback": "[MAILBOXCORE]", "fallback_length": 13, "reason": "Long fallback"}, {"id": "NG1579", "symbol": "❾", "fallback": "[ACTORMETA]", "fallback_length": 11, "reason": "Long fallback"}, {"id": "NG1580", "symbol": "⭟", "fallback": "[ACTORMETA1]", "fallback_length": 12, "reason": "Long fallback"}, {"id": "NG1581", "symbol": "⥾", "fallback": "[MESSAGESYS]", "fallback_length": 12, "reason": "Long fallback"}, {"id": "NG1582", "symbol": "⧾", "fallback": "[MESSAGEOP]", "fallback_length": 11, "reason": "Long fallback"}, {"id": "NG1583", "symbol": "⦖", "fallback": "[SPAWNC..]", "fallback_length": 10, "reason": "Long fallback"}, {"id": "NG1584", "symbol": "⭚", "fallback": "[CSPCHA..]", "fallback_length": 10, "reason": "Long fallback"}, {"id": "NG1585", "symbol": "⩾", "fallback": "[CSPCHANNELSMETA]", "fallback_length": 17, "reason": "Long fallback"}, {"id": "NG1586", "symbol": "⧖", "fallback": "[CSPCHA..]", "fallback_length": 10, "reason": "Long fallback"}, {"id": "NG1587", "symbol": "⧌", "fallback": "[CSPCHANNELSPROC1]", "fallback_length": 18, "reason": "Long fallback"}, {"id": "NG1588", "symbol": "⭾", "fallback": "[CSPCHANNELSFN2]", "fallback_length": 16, "reason": "Long fallback"}, {"id": "NG1589", "symbol": "⍿", "fallback": "[CSPCHANNELSCTRL]", "fallback_length": 17, "reason": "Long fallback"}, {"id": "NG1590", "symbol": "❿", "fallback": "[CSPCHANNELSOP1]", "fallback_length": 16, "reason": "Long fallback"}, {"id": "NG1591", "symbol": "⎀", "fallback": "[CSPCHANNELSSYS1]", "fallback_length": 17, "reason": "Long fallback"}, {"id": "NG1592", "symbol": "➀", "fallback": "[LOCKFREEALGORITHMS]", "fallback_length": 20, "reason": "Long fallback"}, {"id": "NG1593", "symbol": "➁", "fallback": "[WAITFREEALGORITHMS]", "fallback_length": 20, "reason": "Long fallback"}, {"id": "NG1594", "symbol": "⡬", "fallback": "[MEMORY..]", "fallback_length": 10, "reason": "Long fallback"}, {"id": "NG1595", "symbol": "⮁", "fallback": "[MEMORYORDERINGCTRL]", "fallback_length": 20, "reason": "Long fallback"}, {"id": "NG1596", "symbol": "⊂", "fallback": "[MEMORYORDERINGOP]", "fallback_length": 18, "reason": "Long fallback"}, {"id": "NG1597", "symbol": "⎂", "fallback": "[MEMORYORDERINGOP1]", "fallback_length": 19, "reason": "Long fallback"}, {"id": "NG1598", "symbol": "⦧", "fallback": "[MEMORYORDERINGOP2]", "fallback_length": 19, "reason": "Long fallback"}, {"id": "NG1599", "symbol": "⨷", "fallback": "[MEMORY..]", "fallback_length": 10, "reason": "Long fallback"}, {"id": "NG1600", "symbol": "➂", "fallback": "[MEMORYORDERINGFN1]", "fallback_length": 19, "reason": "Long fallback"}, {"id": "NG1601", "symbol": "⮂", "fallback": "[MEMORYORDERING]", "fallback_length": 16, "reason": "Long fallback"}, {"id": "NG1602", "symbol": "✁", "fallback": "[MEMORY..]", "fallback_length": 10, "reason": "Long fallback"}, {"id": "NG1603", "symbol": "⡃", "fallback": "[MEMORYORDERINGOP4]", "fallback_length": 19, "reason": "Long fallback"}, {"id": "NG1604", "symbol": "⯎", "fallback": "[MEMORYORDERINGSYS1]", "fallback_length": 20, "reason": "Long fallback"}, {"id": "NG1605", "symbol": "⎃", "fallback": "[ATOMICOPERATIONSOP]", "fallback_length": 20, "reason": "Long fallback"}, {"id": "NG1606", "symbol": "⦔", "fallback": "[ATOMIC..]", "fallback_length": 10, "reason": "Long fallback"}, {"id": "NG1607", "symbol": "⫨", "fallback": "[ATOMICOPERATIONS1]", "fallback_length": 19, "reason": "Long fallback"}, {"id": "NG1608", "symbol": "➃", "fallback": "[ATOMICOPERATIONS4]", "fallback_length": 19, "reason": "Long fallback"}, {"id": "NG1609", "symbol": "❙", "fallback": "[ATOMICOPERATIONS8]", "fallback_length": 19, "reason": "Long fallback"}, {"id": "NG1610", "symbol": "⯺", "fallback": "[ATOMICOPERATIONS9]", "fallback_length": 19, "reason": "Long fallback"}, {"id": "NG1611", "symbol": "⪃", "fallback": "[ATOMICOPERATIONS10]", "fallback_length": 20, "reason": "Long fallback"}, {"id": "NG1612", "symbol": "⊄", "fallback": "[COMPAREANDSWAPOP]", "fallback_length": 18, "reason": "Long fallback"}, {"id": "NG1613", "symbol": "⎄", "fallback": "[COMPAREANDSWAPCORE]", "fallback_length": 20, "reason": "Long fallback"}, {"id": "NG1614", "symbol": "➄", "fallback": "[COMPAREANDSWAPPROC]", "fallback_length": 20, "reason": "Long fallback"}, {"id": "NG1615", "symbol": "⩨", "fallback": "[COMPAREANDSWAPSYS1]", "fallback_length": 20, "reason": "Long fallback"}, {"id": "NG1616", "symbol": "⮄", "fallback": "[COMPAREANDSWAPFN]", "fallback_length": 18, "reason": "Long fallback"}, {"id": "NG1617", "symbol": "⫝", "fallback": "[COMPAREANDSWAPSYS2]", "fallback_length": 20, "reason": "Long fallback"}, {"id": "NG1618", "symbol": "⎅", "fallback": "[COMPAREANDSWAPSYS4]", "fallback_length": 20, "reason": "Long fallback"}, {"id": "NG1619", "symbol": "➅", "fallback": "[HAZARDPOINTERSPROC]", "fallback_length": 20, "reason": "Long fallback"}, {"id": "NG1620", "symbol": "⪅", "fallback": "[HAZARDPOINTERSSYS]", "fallback_length": 19, "reason": "Long fallback"}, {"id": "NG1621", "symbol": "⦐", "fallback": "[HAZARD..]", "fallback_length": 10, "reason": "Long fallback"}, {"id": "NG1622", "symbol": "⫯", "fallback": "[HAZARDPOINTERSFN1]", "fallback_length": 19, "reason": "Long fallback"}, {"id": "NG1623", "symbol": "⠥", "fallback": "[HAZARDPOINTERSOP2]", "fallback_length": 19, "reason": "Long fallback"}, {"id": "NG1624", "symbol": "❬", "fallback": "[HAZARD..]", "fallback_length": 10, "reason": "Long fallback"}, {"id": "NG1625", "symbol": "⮅", "fallback": "[HAZARDPOINTERSOP3]", "fallback_length": 19, "reason": "Long fallback"}, {"id": "NG1626", "symbol": "➆", "fallback": "[HAZARDPOINTERSSYS1]", "fallback_length": 20, "reason": "Long fallback"}, {"id": "NG1627", "symbol": "⠆", "fallback": "[HAZARDPOINTERSFN3]", "fallback_length": 19, "reason": "Long fallback"}, {"id": "NG1628", "symbol": "⪆", "fallback": "[HAZARDPOINTERSSYS2]", "fallback_length": 20, "reason": "Long fallback"}, {"id": "NG1629", "symbol": "⮡", "fallback": "[HAZARDPOINTERSSYS3]", "fallback_length": 20, "reason": "Long fallback"}, {"id": "NG1630", "symbol": "⨏", "fallback": "[HAZARDPOINTERSOP4]", "fallback_length": 19, "reason": "Long fallback"}, {"id": "NG1631", "symbol": "⮆", "fallback": "[HAZARDPOINTERSSYS5]", "fallback_length": 20, "reason": "Long fallback"}, {"id": "NG1632", "symbol": "⩎", "fallback": "[HAZARDPOINTERSFN6]", "fallback_length": 19, "reason": "Long fallback"}, {"id": "NG1633", "symbol": "⊇", "fallback": "[HAZARDPOINTERSOP5]", "fallback_length": 19, "reason": "Long fallback"}, {"id": "NG1634", "symbol": "⎇", "fallback": "[HAZARDPOINTERSSYS7]", "fallback_length": 20, "reason": "Long fallback"}, {"id": "NG1635", "symbol": "➇", "fallback": "[HAZARDPOINTERS1]", "fallback_length": 17, "reason": "Long fallback"}, {"id": "NG1636", "symbol": "⪇", "fallback": "[HAZARDPOINTERSSYS9]", "fallback_length": 20, "reason": "Long fallback"}, {"id": "NG1637", "symbol": "⮇", "fallback": "[HAZARDPOINTERS3]", "fallback_length": 17, "reason": "Long fallback"}, {"id": "NG1638", "symbol": "⎈", "fallback": "[HAZARDPOINTERSOP6]", "fallback_length": 19, "reason": "Long fallback"}, {"id": "NG1639", "symbol": "➈", "fallback": "[HAZARDPOINTERS4]", "fallback_length": 17, "reason": "Long fallback"}, {"id": "NG1640", "symbol": "⭷", "fallback": "[HAZARDPOINTERSOP7]", "fallback_length": 19, "reason": "Long fallback"}, {"id": "NG1641", "symbol": "⪈", "fallback": "[HAZARDPOINTERSOP8]", "fallback_length": 19, "reason": "Long fallback"}, {"id": "NG1642", "symbol": "⡢", "fallback": "[HAZARDPOINTERS6]", "fallback_length": 17, "reason": "Long fallback"}, {"id": "NG1643", "symbol": "❉", "fallback": "[HAZARDPOINTERSFN8]", "fallback_length": 19, "reason": "Long fallback"}, {"id": "NG1644", "symbol": "⎉", "fallback": "[HAZARDPOINTERSOP11]", "fallback_length": 20, "reason": "Long fallback"}, {"id": "NG1645", "symbol": "➉", "fallback": "[HAZARDPOINTERS8]", "fallback_length": 17, "reason": "Long fallback"}, {"id": "NG1646", "symbol": "⪉", "fallback": "[HAZARDPOINTERS9]", "fallback_length": 17, "reason": "Long fallback"}, {"id": "NG1647", "symbol": "⬀", "fallback": "[HAZARDPOINTERSFN9]", "fallback_length": 19, "reason": "Long fallback"}, {"id": "NG1648", "symbol": "⎊", "fallback": "[HAZARDPOINTERS11]", "fallback_length": 18, "reason": "Long fallback"}, {"id": "NG1649", "symbol": "➊", "fallback": "[HAZARDPOINTERSOP12]", "fallback_length": 20, "reason": "Long fallback"}, {"id": "NG1650", "symbol": "⪊", "fallback": "[HAZARDPOINTERSOP13]", "fallback_length": 20, "reason": "Long fallback"}, {"id": "NG1651", "symbol": "⬒", "fallback": "[HAZARDPOINTERSFN10]", "fallback_length": 20, "reason": "Long fallback"}, {"id": "NG1652", "symbol": "⯥", "fallback": "[HAZARDPOINTERSOP14]", "fallback_length": 20, "reason": "Long fallback"}, {"id": "NG1653", "symbol": "❆", "fallback": "[HAZARDPOINTERS12]", "fallback_length": 18, "reason": "Long fallback"}, {"id": "NG1654", "symbol": "⎋", "fallback": "[HAZARDPOINTERS13]", "fallback_length": 18, "reason": "Long fallback"}, {"id": "NG1655", "symbol": "➋", "fallback": "[TRAININGPROC]", "fallback_length": 14, "reason": "Long fallback"}, {"id": "NG1656", "symbol": "⤌", "fallback": "[PREDIC..]", "fallback_length": 10, "reason": "Long fallback"}, {"id": "NG1657", "symbol": "⮼", "fallback": "[TRAINING]", "fallback_length": 10, "reason": "Long fallback"}, {"id": "NG1658", "symbol": "⤕", "fallback": "[TRAINI..]", "fallback_length": 10, "reason": "Long fallback"}, {"id": "NG1659", "symbol": "⬕", "fallback": "[TRAININGPROC1]", "fallback_length": 15, "reason": "Long fallback"}, {"id": "NG1660", "symbol": "⥃", "fallback": "[PREDIC..]", "fallback_length": 10, "reason": "Long fallback"}, {"id": "NG1661", "symbol": "⫺", "fallback": "[REGRES..]", "fallback_length": 10, "reason": "Long fallback"}, {"id": "NG1662", "symbol": "⮋", "fallback": "[PREDICTIONPROC1]", "fallback_length": 17, "reason": "Long fallback"}, {"id": "NG1663", "symbol": "⮬", "fallback": "[CLASSI..]", "fallback_length": 10, "reason": "Long fallback"}, {"id": "NG1664", "symbol": "⊌", "fallback": "[CLASSIFIER]", "fallback_length": 12, "reason": "Long fallback"}, {"id": "NG1665", "symbol": "⎌", "fallback": "[TRAININGCTRL]", "fallback_length": 14, "reason": "Long fallback"}, {"id": "NG1666", "symbol": "⤩", "fallback": "[TRAININGPROC2]", "fallback_length": 15, "reason": "Long fallback"}, {"id": "NG1667", "symbol": "⪤", "fallback": "[PREDIC..]", "fallback_length": 10, "reason": "Long fallback"}, {"id": "NG1668", "symbol": "⠼", "fallback": "[TRAININGCTRL1]", "fallback_length": 15, "reason": "Long fallback"}, {"id": "NG1669", "symbol": "➌", "fallback": "[PREDICTIONFN]", "fallback_length": 14, "reason": "Long fallback"}, {"id": "NG1670", "symbol": "⎍", "fallback": "[TRAININGOP1]", "fallback_length": 13, "reason": "Long fallback"}, {"id": "NG1671", "symbol": "⢗", "fallback": "[DEEPLE..]", "fallback_length": 10, "reason": "Long fallback"}, {"id": "NG1672", "symbol": "⦳", "fallback": "[DEEPLE..]", "fallback_length": 10, "reason": "Long fallback"}, {"id": "NG1673", "symbol": "➍", "fallback": "[DEEPLEARNING1]", "fallback_length": 15, "reason": "Long fallback"}, {"id": "NG1674", "symbol": "⧞", "fallback": "[DEEPLE..]", "fallback_length": 10, "reason": "Long fallback"}, {"id": "NG1675", "symbol": "⨘", "fallback": "[DEEPLE..]", "fallback_length": 10, "reason": "Long fallback"}, {"id": "NG1676", "symbol": "⪍", "fallback": "[DEEPLEARNING2]", "fallback_length": 15, "reason": "Long fallback"}, {"id": "NG1677", "symbol": "⮍", "fallback": "[DEEPLEARNINGCTRL]", "fallback_length": 18, "reason": "Long fallback"}, {"id": "NG1678", "symbol": "⎎", "fallback": "[DEEPLEARNINGMETA]", "fallback_length": 18, "reason": "Long fallback"}, {"id": "NG1679", "symbol": "➎", "fallback": "[DEEPLEARNINGSYS1]", "fallback_length": 18, "reason": "Long fallback"}, {"id": "NG1680", "symbol": "⪎", "fallback": "[DEEPLEARNINGCTRL1]", "fallback_length": 19, "reason": "Long fallback"}, {"id": "NG1681", "symbol": "✀", "fallback": "[DEEPLEARNINGSYS2]", "fallback_length": 18, "reason": "Long fallback"}, {"id": "NG1682", "symbol": "⩛", "fallback": "[DEEPLEARNINGCORE1]", "fallback_length": 19, "reason": "Long fallback"}, {"id": "NG1683", "symbol": "⎏", "fallback": "[DEEPLEARNINGCTRL2]", "fallback_length": 19, "reason": "Long fallback"}, {"id": "NG1684", "symbol": "⪌", "fallback": "[DEEPLEARNINGOP1]", "fallback_length": 17, "reason": "Long fallback"}, {"id": "NG1685", "symbol": "➏", "fallback": "[DEEPLEARNINGMETA1]", "fallback_length": 19, "reason": "Long fallback"}, {"id": "NG1686", "symbol": "⤔", "fallback": "[DEEPLE..]", "fallback_length": 10, "reason": "Long fallback"}, {"id": "NG1687", "symbol": "⫼", "fallback": "[MODELE..]", "fallback_length": 10, "reason": "Long fallback"}, {"id": "NG1688", "symbol": "⪏", "fallback": "[MODELEVALUATION]", "fallback_length": 17, "reason": "Long fallback"}, {"id": "NG1689", "symbol": "⭜", "fallback": "[MODELE..]", "fallback_length": 10, "reason": "Long fallback"}, {"id": "NG1690", "symbol": "⎐", "fallback": "[MODELEVALUATIONFN]", "fallback_length": 19, "reason": "Long fallback"}, {"id": "NG1691", "symbol": "⦥", "fallback": "[MODELEVALUATION1]", "fallback_length": 18, "reason": "Long fallback"}, {"id": "NG1692", "symbol": "➐", "fallback": "[MODELEVALUATION2]", "fallback_length": 18, "reason": "Long fallback"}, {"id": "NG1693", "symbol": "⪐", "fallback": "[MODELEVALUATIONOP1]", "fallback_length": 20, "reason": "Long fallback"}, {"id": "NG1694", "symbol": "⩰", "fallback": "[MODELEVALUATIONFN1]", "fallback_length": 20, "reason": "Long fallback"}, {"id": "NG1695", "symbol": "⦘", "fallback": "[MODELEVALUATION3]", "fallback_length": 18, "reason": "Long fallback"}, {"id": "NG1696", "symbol": "⣶", "fallback": "[MODELEVALUATIONFN2]", "fallback_length": 20, "reason": "Long fallback"}, {"id": "NG1697", "symbol": "⊑", "fallback": "[MODELEVALUATIONOP2]", "fallback_length": 20, "reason": "Long fallback"}, {"id": "NG1698", "symbol": "⥄", "fallback": "[MODELEVALUATION4]", "fallback_length": 18, "reason": "Long fallback"}, {"id": "NG1699", "symbol": "➑", "fallback": "[MODELEVALUATION5]", "fallback_length": 18, "reason": "Long fallback"}, {"id": "NG1700", "symbol": "⪑", "fallback": "[MODELEVALUATION6]", "fallback_length": 18, "reason": "Long fallback"}, {"id": "NG1701", "symbol": "❂", "fallback": "[MODELEVALUATION7]", "fallback_length": 18, "reason": "Long fallback"}, {"id": "NG1702", "symbol": "⮑", "fallback": "[MODELEVALUATIONOP3]", "fallback_length": 20, "reason": "Long fallback"}, {"id": "NG1703", "symbol": "⎒", "fallback": "[FEATUREENGINEERING]", "fallback_length": 20, "reason": "Long fallback"}, {"id": "NG1704", "symbol": "⥴", "fallback": "[ENSEMB..]", "fallback_length": 10, "reason": "Long fallback"}, {"id": "NG1705", "symbol": "➒", "fallback": "[ENSEMBLEMETHODSOP1]", "fallback_length": 20, "reason": "Long fallback"}, {"id": "NG1706", "symbol": "⧢", "fallback": "[ENSEMB..]", "fallback_length": 10, "reason": "Long fallback"}, {"id": "NG1707", "symbol": "⯾", "fallback": "[ENSEMB..]", "fallback_length": 10, "reason": "Long fallback"}, {"id": "NG1708", "symbol": "⮒", "fallback": "[ENSEMBLEMETHODSFN1]", "fallback_length": 20, "reason": "Long fallback"}, {"id": "NG1709", "symbol": "⊓", "fallback": "[ENSEMBLEMETHODS1]", "fallback_length": 18, "reason": "Long fallback"}, {"id": "NG1710", "symbol": "➓", "fallback": "[ENSEMBLEMETHODSFN2]", "fallback_length": 20, "reason": "Long fallback"}, {"id": "NG1711", "symbol": "⪓", "fallback": "[ENSEMBLEMETHODSSYS]", "fallback_length": 20, "reason": "Long fallback"}, {"id": "NG1712", "symbol": "⢿", "fallback": "[ENSEMBLEMETHODS2]", "fallback_length": 18, "reason": "Long fallback"}, {"id": "NG1713", "symbol": "⮓", "fallback": "[ENSEMBLEMETHODSFN3]", "fallback_length": 20, "reason": "Long fallback"}, {"id": "NG1714", "symbol": "⪋", "fallback": "[ENSEMBLEMETHODSFN4]", "fallback_length": 20, "reason": "Long fallback"}, {"id": "NG1715", "symbol": "⊔", "fallback": "[ENSEMBLEMETHODSOP2]", "fallback_length": 20, "reason": "Long fallback"}, {"id": "NG1716", "symbol": "✙", "fallback": "[ENSEMBLEMETHODSFN5]", "fallback_length": 20, "reason": "Long fallback"}, {"id": "NG1717", "symbol": "⤱", "fallback": "[ENSEMBLEMETHODSOP3]", "fallback_length": 20, "reason": "Long fallback"}, {"id": "NG1718", "symbol": "⎔", "fallback": "[ENSEMBLEMETHODSFN6]", "fallback_length": 20, "reason": "Long fallback"}, {"id": "NG1719", "symbol": "➽", "fallback": "[ENSEMBLEMETHODS3]", "fallback_length": 18, "reason": "Long fallback"}, {"id": "NG1720", "symbol": "➔", "fallback": "[ENSEMBLEMETHODSFN7]", "fallback_length": 20, "reason": "Long fallback"}, {"id": "NG1721", "symbol": "⪄", "fallback": "[ENSEMBLEMETHODSOP4]", "fallback_length": 20, "reason": "Long fallback"}, {"id": "NG1722", "symbol": "⮳", "fallback": "[ENSEMBLEMETHODS4]", "fallback_length": 18, "reason": "Long fallback"}, {"id": "NG1723", "symbol": "⪔", "fallback": "[ENSEMBLEMETHODS5]", "fallback_length": 18, "reason": "Long fallback"}, {"id": "NG1724", "symbol": "⯕", "fallback": "[ENSEMBLEMETHODSFN8]", "fallback_length": 20, "reason": "Long fallback"}, {"id": "NG1725", "symbol": "⎕", "fallback": "[ENSEMBLEMETHODSOP5]", "fallback_length": 20, "reason": "Long fallback"}, {"id": "NG1726", "symbol": "⪕", "fallback": "[ENSEMBLEMETHODSOP6]", "fallback_length": 20, "reason": "Long fallback"}, {"id": "NG1727", "symbol": "⤏", "fallback": "[ENSEMBLEMETHODSOP7]", "fallback_length": 20, "reason": "Long fallback"}, {"id": "NG1728", "symbol": "⫮", "fallback": "[ENSEMBLEMETHODSOP8]", "fallback_length": 20, "reason": "Long fallback"}, {"id": "NG1729", "symbol": "⎖", "fallback": "[ENSEMBLEMETHODSOP9]", "fallback_length": 20, "reason": "Long fallback"}, {"id": "NG1730", "symbol": "⠧", "fallback": "[ENSEMBLEMETHODS6]", "fallback_length": 18, "reason": "Long fallback"}, {"id": "NG1731", "symbol": "➖", "fallback": "[ENSEMBLEMETHODS7]", "fallback_length": 18, "reason": "Long fallback"}, {"id": "NG1732", "symbol": "⮖", "fallback": "[ENSEMBLEMETHODSFN9]", "fallback_length": 20, "reason": "Long fallback"}, {"id": "NG1733", "symbol": "⎗", "fallback": "[ENSEMBLEMETHODS8]", "fallback_length": 18, "reason": "Long fallback"}, {"id": "NG1734", "symbol": "➗", "fallback": "[ENSEMBLEMETHODS9]", "fallback_length": 18, "reason": "Long fallback"}, {"id": "NG1735", "symbol": "⪗", "fallback": "[ENSEMBLEMETHODS10]", "fallback_length": 19, "reason": "Long fallback"}, {"id": "NG1736", "symbol": "⮗", "fallback": "[ENSEMBLEMETHODS11]", "fallback_length": 19, "reason": "Long fallback"}, {"id": "NG1737", "symbol": "⧑", "fallback": "[ENSEMBLEMETHODS12]", "fallback_length": 19, "reason": "Long fallback"}, {"id": "NG1738", "symbol": "⎘", "fallback": "[ENSEMBLEMETHODS13]", "fallback_length": 19, "reason": "Long fallback"}, {"id": "NG1739", "symbol": "➘", "fallback": "[ENSEMBLEMETHODS14]", "fallback_length": 19, "reason": "Long fallback"}, {"id": "NG1740", "symbol": "⬞", "fallback": "[ENSEMBLEMETHODS15]", "fallback_length": 19, "reason": "Long fallback"}, {"id": "NG1741", "symbol": "⎙", "fallback": "[ENSEMBLEMETHODS16]", "fallback_length": 19, "reason": "Long fallback"}, {"id": "NG1742", "symbol": "➙", "fallback": "[ENSEMBLEMETHODS17]", "fallback_length": 19, "reason": "Long fallback"}, {"id": "NG1743", "symbol": "⮚", "fallback": "[ENSEMBLEMETHODS18]", "fallback_length": 19, "reason": "Long fallback"}, {"id": "NG1744", "symbol": "⎛", "fallback": "[ENSEMBLEMETHODS19]", "fallback_length": 19, "reason": "Long fallback"}, {"id": "NG1745", "symbol": "⪛", "fallback": "[ENSEMBLEMETHODS20]", "fallback_length": 19, "reason": "Long fallback"}, {"id": "NG1746", "symbol": "⥵", "fallback": "[ENSEMBLEMETHODS21]", "fallback_length": 19, "reason": "Long fallback"}, {"id": "NG1747", "symbol": "⮛", "fallback": "[ENSEMBLEMETHODS22]", "fallback_length": 19, "reason": "Long fallback"}, {"id": "NG1748", "symbol": "⊜", "fallback": "[ENSEMBLEMETHODS23]", "fallback_length": 19, "reason": "Long fallback"}, {"id": "NG1749", "symbol": "⩝", "fallback": "[ENSEMBLEMETHODS24]", "fallback_length": 19, "reason": "Long fallback"}, {"id": "NG1750", "symbol": "⎜", "fallback": "[ENSEMBLEMETHODS25]", "fallback_length": 19, "reason": "Long fallback"}, {"id": "NG1751", "symbol": "⎝", "fallback": "[ENSEMBLEMETHODS26]", "fallback_length": 19, "reason": "Long fallback"}, {"id": "NG1752", "symbol": "➝", "fallback": "[ENSEMBLEMETHODS27]", "fallback_length": 19, "reason": "Long fallback"}, {"id": "NG1753", "symbol": "⪝", "fallback": "[ENSEMBLEMETHODS28]", "fallback_length": 19, "reason": "Long fallback"}, {"id": "NG1754", "symbol": "⮝", "fallback": "[ENSEMBLEMETHODS29]", "fallback_length": 19, "reason": "Long fallback"}, {"id": "NG1755", "symbol": "⎞", "fallback": "[ENSEMBLEMETHODS30]", "fallback_length": 19, "reason": "Long fallback"}, {"id": "NG1756", "symbol": "⪞", "fallback": "[ENSEMBLEMETHODS31]", "fallback_length": 19, "reason": "Long fallback"}, {"id": "NG1757", "symbol": "⊟", "fallback": "[ENSEMBLEMETHODS32]", "fallback_length": 19, "reason": "Long fallback"}, {"id": "NG1758", "symbol": "⎟", "fallback": "[ENSEMBLEMETHODS33]", "fallback_length": 19, "reason": "Long fallback"}, {"id": "NG1759", "symbol": "➟", "fallback": "[ENSEMBLEMETHODS34]", "fallback_length": 19, "reason": "Long fallback"}, {"id": "NG1760", "symbol": "⎠", "fallback": "[ENSEMBLEMETHODS35]", "fallback_length": 19, "reason": "Long fallback"}, {"id": "NG1761", "symbol": "⮠", "fallback": "[ENSEMBLEMETHODS36]", "fallback_length": 19, "reason": "Long fallback"}, {"id": "NG1762", "symbol": "⤠", "fallback": "[ENSEMBLEMETHODS37]", "fallback_length": 19, "reason": "Long fallback"}, {"id": "NG1763", "symbol": "⊡", "fallback": "[ENSEMBLEMETHODS38]", "fallback_length": 19, "reason": "Long fallback"}, {"id": "NG1764", "symbol": "⮪", "fallback": "[ENSEMBLEMETHODS39]", "fallback_length": 19, "reason": "Long fallback"}, {"id": "NG1765", "symbol": "⎡", "fallback": "[ENSEMBLEMETHODS40]", "fallback_length": 19, "reason": "Long fallback"}, {"id": "NG1766", "symbol": "⪡", "fallback": "[ENSEMBLEMETHODS41]", "fallback_length": 19, "reason": "Long fallback"}, {"id": "NG1767", "symbol": "⭰", "fallback": "[ENSEMBLEMETHODS42]", "fallback_length": 19, "reason": "Long fallback"}, {"id": "NG1768", "symbol": "⥁", "fallback": "[ENSEMBLEMETHODS43]", "fallback_length": 19, "reason": "Long fallback"}, {"id": "NG1769", "symbol": "⩭", "fallback": "[ENSEMBLEMETHODS44]", "fallback_length": 19, "reason": "Long fallback"}, {"id": "NG1770", "symbol": "⦻", "fallback": "[ENSEMBLEMETHODS45]", "fallback_length": 19, "reason": "Long fallback"}, {"id": "NG1771", "symbol": "⨓", "fallback": "[ENSEMBLEMETHODS46]", "fallback_length": 19, "reason": "Long fallback"}, {"id": "NG1772", "symbol": "➢", "fallback": "[ENSEMBLEMETHODS47]", "fallback_length": 19, "reason": "Long fallback"}, {"id": "NG1773", "symbol": "⭲", "fallback": "[ENSEMBLEMETHODS48]", "fallback_length": 19, "reason": "Long fallback"}, {"id": "NG1774", "symbol": "⪢", "fallback": "[ENSEMBLEMETHODS49]", "fallback_length": 19, "reason": "Long fallback"}, {"id": "NG1775", "symbol": "✮", "fallback": "[ENSEMBLEMETHODS50]", "fallback_length": 19, "reason": "Long fallback"}, {"id": "NG1776", "symbol": "⬁", "fallback": "[ENSEMBLEMETHODS51]", "fallback_length": 19, "reason": "Long fallback"}, {"id": "NG1777", "symbol": "⥽", "fallback": "[ENSEMBLEMETHODS52]", "fallback_length": 19, "reason": "Long fallback"}, {"id": "NG1779", "symbol": "⢂", "fallback": "[RINGOP1]", "fallback_length": 9, "reason": "Long fallback"}, {"id": "NG1780", "symbol": "⊣", "fallback": "[FIELDMETA]", "fallback_length": 11, "reason": "Long fallback"}, {"id": "NG1781", "symbol": "⎣", "fallback": "[ALGEBRAPROC]", "fallback_length": 13, "reason": "Long fallback"}, {"id": "NG1782", "symbol": "⢼", "fallback": "[ALGEBRA]", "fallback_length": 9, "reason": "Long fallback"}, {"id": "NG1783", "symbol": "❩", "fallback": "[RINGMETA]", "fallback_length": 10, "reason": "Long fallback"}, {"id": "NG1785", "symbol": "⩂", "fallback": "[ALGEBRA1]", "fallback_length": 10, "reason": "Long fallback"}, {"id": "NG1786", "symbol": "➣", "fallback": "[GROUPCTRL]", "fallback_length": 11, "reason": "Long fallback"}, {"id": "NG1787", "symbol": "⎤", "fallback": "[GROUPCTRL1]", "fallback_length": 12, "reason": "Long fallback"}, {"id": "NG1788", "symbol": "⮤", "fallback": "[TOPOLOGICALSPACES]", "fallback_length": 19, "reason": "Long fallback"}, {"id": "NG1789", "symbol": "⩩", "fallback": "[TOPOLOGICALSPACES1]", "fallback_length": 20, "reason": "Long fallback"}, {"id": "NG1790", "symbol": "⧼", "fallback": "[TOPOLOGICALSPACES2]", "fallback_length": 20, "reason": "Long fallback"}, {"id": "NG1791", "symbol": "⢬", "fallback": "[TOPOLOGICALSPACES3]", "fallback_length": 20, "reason": "Long fallback"}, {"id": "NG1792", "symbol": "⧕", "fallback": "[TOPOLOGICALSPACES4]", "fallback_length": 20, "reason": "Long fallback"}, {"id": "NG1793", "symbol": "⮔", "fallback": "[MEASUR..]", "fallback_length": 10, "reason": "Long fallback"}, {"id": "NG1794", "symbol": "⊥", "fallback": "[MEASURETHEORYFN]", "fallback_length": 17, "reason": "Long fallback"}, {"id": "NG1795", "symbol": "➥", "fallback": "[MEASURETHEORYOP]", "fallback_length": 17, "reason": "Long fallback"}, {"id": "NG1796", "symbol": "⮥", "fallback": "[MEASURETHEORYOP1]", "fallback_length": 18, "reason": "Long fallback"}, {"id": "NG1797", "symbol": "⎦", "fallback": "[MEASURETHEORYMETA]", "fallback_length": 19, "reason": "Long fallback"}, {"id": "NG1798", "symbol": "➦", "fallback": "[MEASURETHEORYPROC]", "fallback_length": 19, "reason": "Long fallback"}, {"id": "NG1799", "symbol": "⮦", "fallback": "[MEASURETHEORY]", "fallback_length": 15, "reason": "Long fallback"}, {"id": "NG1800", "symbol": "➧", "fallback": "[MEASURETHEORYFN1]", "fallback_length": 18, "reason": "Long fallback"}, {"id": "NG1801", "symbol": "❮", "fallback": "[MEASURETHEORYPROC1]", "fallback_length": 20, "reason": "Long fallback"}, {"id": "NG1802", "symbol": "✣", "fallback": "[MEASUR..]", "fallback_length": 10, "reason": "Long fallback"}, {"id": "NG1803", "symbol": "⮧", "fallback": "[NUMBERTHEORYMETA]", "fallback_length": 18, "reason": "Long fallback"}, {"id": "NG1804", "symbol": "➠", "fallback": "[NUMBER..]", "fallback_length": 10, "reason": "Long fallback"}, {"id": "NG1805", "symbol": "⣁", "fallback": "[NUMBERTHEORYCTRL1]", "fallback_length": 19, "reason": "Long fallback"}, {"id": "NG1806", "symbol": "⤚", "fallback": "[NUMBER..]", "fallback_length": 10, "reason": "Long fallback"}, {"id": "NG1807", "symbol": "⤆", "fallback": "[NUMBER..]", "fallback_length": 10, "reason": "Long fallback"}, {"id": "NG1808", "symbol": "⎨", "fallback": "[NUMBERTHEORYPROC]", "fallback_length": 18, "reason": "Long fallback"}, {"id": "NG1809", "symbol": "➨", "fallback": "[NUMBERTHEORYMETA1]", "fallback_length": 19, "reason": "Long fallback"}, {"id": "NG1810", "symbol": "⊩", "fallback": "[NUMBERTHEORYCORE]", "fallback_length": 18, "reason": "Long fallback"}, {"id": "NG1811", "symbol": "❰", "fallback": "[NUMBERTHEORYPROC1]", "fallback_length": 19, "reason": "Long fallback"}, {"id": "NG1812", "symbol": "⩐", "fallback": "[NUMBERTHEORYPROC2]", "fallback_length": 19, "reason": "Long fallback"}, {"id": "NG1813", "symbol": "⎩", "fallback": "[COMBINATORICS]", "fallback_length": 15, "reason": "Long fallback"}, {"id": "NG1814", "symbol": "⯤", "fallback": "[COMBIN..]", "fallback_length": 10, "reason": "Long fallback"}, {"id": "NG1815", "symbol": "⩦", "fallback": "[COMBIN..]", "fallback_length": 10, "reason": "Long fallback"}, {"id": "NG1816", "symbol": "⭿", "fallback": "[COMBIN..]", "fallback_length": 10, "reason": "Long fallback"}, {"id": "NG1817", "symbol": "⨑", "fallback": "[COMBINATORICSSYS1]", "fallback_length": 19, "reason": "Long fallback"}, {"id": "NG1818", "symbol": "➩", "fallback": "[COMBINATORICSPROC]", "fallback_length": 19, "reason": "Long fallback"}, {"id": "NG1819", "symbol": "⩏", "fallback": "[COMBIN..]", "fallback_length": 10, "reason": "Long fallback"}, {"id": "NG1820", "symbol": "⮩", "fallback": "[COMBINATORICSCORE]", "fallback_length": 19, "reason": "Long fallback"}, {"id": "NG1821", "symbol": "⭅", "fallback": "[COMBINATORICSPROC1]", "fallback_length": 20, "reason": "Long fallback"}, {"id": "NG1822", "symbol": "⤇", "fallback": "[COMBINATORICSOP1]", "fallback_length": 18, "reason": "Long fallback"}, {"id": "NG1823", "symbol": "⎪", "fallback": "[COMBINATORICSCORE1]", "fallback_length": 20, "reason": "Long fallback"}, {"id": "NG1824", "symbol": "⊫", "fallback": "[COMBINATORICSOP2]", "fallback_length": 18, "reason": "Long fallback"}, {"id": "NG1825", "symbol": "⎫", "fallback": "[COMBINATORICSCORE2]", "fallback_length": 20, "reason": "Long fallback"}, {"id": "NG1826", "symbol": "⮫", "fallback": "[COMBINATORICSMETA1]", "fallback_length": 20, "reason": "Long fallback"}, {"id": "NG1827", "symbol": "⠲", "fallback": "[COMBINATORICSCORE3]", "fallback_length": 20, "reason": "Long fallback"}, {"id": "NG1828", "symbol": "⪬", "fallback": "[COMBINATORICSCTRL]", "fallback_length": 19, "reason": "Long fallback"}, {"id": "NG1829", "symbol": "⊭", "fallback": "[COMBINATORICSFN1]", "fallback_length": 18, "reason": "Long fallback"}, {"id": "NG1830", "symbol": "⣕", "fallback": "[COMBINATORICSCORE4]", "fallback_length": 20, "reason": "Long fallback"}, {"id": "NG1831", "symbol": "⥲", "fallback": "[COMBINATORICSMETA2]", "fallback_length": 20, "reason": "Long fallback"}, {"id": "NG1832", "symbol": "⥉", "fallback": "[COMBINATORICSCTRL1]", "fallback_length": 20, "reason": "Long fallback"}, {"id": "NG1833", "symbol": "⪭", "fallback": "[COMBINATORICSFN2]", "fallback_length": 18, "reason": "Long fallback"}, {"id": "NG1834", "symbol": "⮌", "fallback": "[COMBINATORICSMETA3]", "fallback_length": 20, "reason": "Long fallback"}, {"id": "NG1835", "symbol": "⎮", "fallback": "[COMBINATORICSOP3]", "fallback_length": 18, "reason": "Long fallback"}, {"id": "NG1836", "symbol": "⪮", "fallback": "[COMBINATORICSCORE5]", "fallback_length": 20, "reason": "Long fallback"}, {"id": "NG1837", "symbol": "⮮", "fallback": "[COMBINATORICSOP4]", "fallback_length": 18, "reason": "Long fallback"}, {"id": "NG1838", "symbol": "⫶", "fallback": "[COMBINATORICSFN3]", "fallback_length": 18, "reason": "Long fallback"}, {"id": "NG1839", "symbol": "⤲", "fallback": "[COMBINATORICSFN4]", "fallback_length": 18, "reason": "Long fallback"}, {"id": "NG1840", "symbol": "➯", "fallback": "[COMBINATORICSCTRL2]", "fallback_length": 20, "reason": "Long fallback"}, {"id": "NG1841", "symbol": "⮰", "fallback": "[COMBINATORICSMETA4]", "fallback_length": 20, "reason": "Long fallback"}, {"id": "NG1842", "symbol": "𝞉", "fallback": "[KNOWLE..]", "fallback_length": 10, "reason": "Long fallback"}, {"id": "NG1843", "symbol": "⤣", "fallback": "[JUSTIF..]", "fallback_length": 10, "reason": "Long fallback"}, {"id": "NG1844", "symbol": "⣡", "fallback": "[JUSTIF..]", "fallback_length": 10, "reason": "Long fallback"}, {"id": "NG1845", "symbol": "⪯", "fallback": "[KNOWLEDGECTRL1]", "fallback_length": 16, "reason": "Long fallback"}, {"id": "NG1846", "symbol": "⢢", "fallback": "[BELIEF..]", "fallback_length": 10, "reason": "Long fallback"}, {"id": "NG1847", "symbol": "❈", "fallback": "[BELIEF..]", "fallback_length": 10, "reason": "Long fallback"}, {"id": "NG1848", "symbol": "⎰", "fallback": "[KNOWLEDGEPROC]", "fallback_length": 15, "reason": "Long fallback"}, {"id": "NG1849", "symbol": "⧘", "fallback": "[BELIEFFN]", "fallback_length": 10, "reason": "Long fallback"}, {"id": "NG1850", "symbol": "⢞", "fallback": "[JUSTIF..]", "fallback_length": 10, "reason": "Long fallback"}, {"id": "NG1851", "symbol": "⪰", "fallback": "[BELIEFSYS]", "fallback_length": 11, "reason": "Long fallback"}, {"id": "NG1852", "symbol": "⎱", "fallback": "[ONTOLOGYOP]", "fallback_length": 12, "reason": "Long fallback"}, {"id": "NG1853", "symbol": "⪱", "fallback": "[ONTOLOGYSYS]", "fallback_length": 13, "reason": "Long fallback"}, {"id": "NG1854", "symbol": "⮱", "fallback": "[ONTOLOGYSYS1]", "fallback_length": 14, "reason": "Long fallback"}, {"id": "NG1855", "symbol": "⎲", "fallback": "[ONTOLOGYOP1]", "fallback_length": 13, "reason": "Long fallback"}, {"id": "NG1856", "symbol": "⤯", "fallback": "[ONTOLOGYSYS2]", "fallback_length": 14, "reason": "Long fallback"}, {"id": "NG1857", "symbol": "➲", "fallback": "[ONTOLOGYOP2]", "fallback_length": 13, "reason": "Long fallback"}, {"id": "NG1858", "symbol": "⎳", "fallback": "[ONTOLOGYMETA]", "fallback_length": 14, "reason": "Long fallback"}, {"id": "NG1859", "symbol": "➳", "fallback": "[ONTOLOGYOP3]", "fallback_length": 13, "reason": "Long fallback"}, {"id": "NG1860", "symbol": "⪳", "fallback": "[ONTOLOGYOP4]", "fallback_length": 13, "reason": "Long fallback"}, {"id": "NG1861", "symbol": "⢡", "fallback": "[ONTOLO..]", "fallback_length": 10, "reason": "Long fallback"}, {"id": "NG1862", "symbol": "⊴", "fallback": "[LOGICPHILOSOPHYOP]", "fallback_length": 19, "reason": "Long fallback"}, {"id": "NG1863", "symbol": "⎴", "fallback": "[LOGICPHILOSOPHY]", "fallback_length": 17, "reason": "Long fallback"}, {"id": "NG1864", "symbol": "⮴", "fallback": "[LOGICPHILOSOPHYFN]", "fallback_length": 19, "reason": "Long fallback"}, {"id": "NG1865", "symbol": "✬", "fallback": "[LOGICPHILOSOPHYFN1]", "fallback_length": 20, "reason": "Long fallback"}, {"id": "NG1866", "symbol": "⢫", "fallback": "[LOGICPHILOSOPHYOP1]", "fallback_length": 20, "reason": "Long fallback"}, {"id": "NG1867", "symbol": "⎵", "fallback": "[LOGICPHILOSOPHYFN2]", "fallback_length": 20, "reason": "Long fallback"}, {"id": "NG1868", "symbol": "⯡", "fallback": "[LOGICP..]", "fallback_length": 10, "reason": "Long fallback"}, {"id": "NG1869", "symbol": "➱", "fallback": "[LOGICPHILOSOPHYOP2]", "fallback_length": 20, "reason": "Long fallback"}, {"id": "NG1870", "symbol": "⪙", "fallback": "[LOGICPHILOSOPHYFN3]", "fallback_length": 20, "reason": "Long fallback"}, {"id": "NG1871", "symbol": "⮵", "fallback": "[LOGICPHILOSOPHYOP3]", "fallback_length": 20, "reason": "Long fallback"}, {"id": "NG1872", "symbol": "⊶", "fallback": "[PHILOSOPHYOFMINDFN]", "fallback_length": 20, "reason": "Long fallback"}, {"id": "NG1873", "symbol": "⨵", "fallback": "[PHILOS..]", "fallback_length": 10, "reason": "Long fallback"}, {"id": "NG1874", "symbol": "⎶", "fallback": "[PHILOSOPHYOFMIND1]", "fallback_length": 19, "reason": "Long fallback"}, {"id": "NG1875", "symbol": "➶", "fallback": "[PHILOSOPHYOFMINDOP]", "fallback_length": 20, "reason": "Long fallback"}, {"id": "NG1876", "symbol": "⪶", "fallback": "[PHILOSOPHYOFMIND2]", "fallback_length": 19, "reason": "Long fallback"}, {"id": "NG1877", "symbol": "⧈", "fallback": "[PHILOSOPHYOFMIND3]", "fallback_length": 19, "reason": "Long fallback"}, {"id": "NG1878", "symbol": "⮉", "fallback": "[PHILOSOPHYOFMIND4]", "fallback_length": 19, "reason": "Long fallback"}, {"id": "NG1879", "symbol": "⫢", "fallback": "[ETHICSOP]", "fallback_length": 10, "reason": "Long fallback"}, {"id": "NG1880", "symbol": "⎷", "fallback": "[ETHICSOP1]", "fallback_length": 11, "reason": "Long fallback"}, {"id": "NG1881", "symbol": "⪷", "fallback": "[ETHICSMETA]", "fallback_length": 12, "reason": "Long fallback"}, {"id": "NG1882", "symbol": "⊸", "fallback": "[ETHICSCORE]", "fallback_length": 12, "reason": "Long fallback"}, {"id": "NG1884", "symbol": "⎸", "fallback": "[ETHICS1]", "fallback_length": 9, "reason": "Long fallback"}, {"id": "NG1885", "symbol": "➸", "fallback": "[ETHICSCORE1]", "fallback_length": 13, "reason": "Long fallback"}, {"id": "NG1886", "symbol": "⯖", "fallback": "[ETHICSFN]", "fallback_length": 10, "reason": "Long fallback"}, {"id": "NG1887", "symbol": "⪵", "fallback": "[ETHICS2]", "fallback_length": 9, "reason": "Long fallback"}, {"id": "NG1888", "symbol": "⪸", "fallback": "[ETHICS3]", "fallback_length": 9, "reason": "Long fallback"}, {"id": "NG1889", "symbol": "⮞", "fallback": "[METAPH..]", "fallback_length": 10, "reason": "Long fallback"}, {"id": "NG1890", "symbol": "⠕", "fallback": "[METAPH..]", "fallback_length": 10, "reason": "Long fallback"}, {"id": "NG1891", "symbol": "➰", "fallback": "[METAPHYSICSCTRL1]", "fallback_length": 18, "reason": "Long fallback"}, {"id": "NG1892", "symbol": "⎹", "fallback": "[METAPHYSICSCTRL2]", "fallback_length": 18, "reason": "Long fallback"}, {"id": "NG1893", "symbol": "✆", "fallback": "[METAPH..]", "fallback_length": 10, "reason": "Long fallback"}, {"id": "NG1894", "symbol": "⧻", "fallback": "[METAPH..]", "fallback_length": 10, "reason": "Long fallback"}, {"id": "NG1895", "symbol": "⦬", "fallback": "[METAPHYSICSCTRL3]", "fallback_length": 18, "reason": "Long fallback"}, {"id": "NG1896", "symbol": "➹", "fallback": "[METAPHYSICSCTRL4]", "fallback_length": 18, "reason": "Long fallback"}, {"id": "NG1897", "symbol": "⪹", "fallback": "[METAPHYSICSMETA1]", "fallback_length": 18, "reason": "Long fallback"}, {"id": "NG1898", "symbol": "⎺", "fallback": "[METAPHYSICSCORE]", "fallback_length": 17, "reason": "Long fallback"}, {"id": "NG1899", "symbol": "➺", "fallback": "[METAPHYSICS1]", "fallback_length": 14, "reason": "Long fallback"}, {"id": "NG1900", "symbol": "⪺", "fallback": "[METAPHYSICSFN1]", "fallback_length": 16, "reason": "Long fallback"}, {"id": "NG1901", "symbol": "⎻", "fallback": "[METAPHYSICSCTRL5]", "fallback_length": 18, "reason": "Long fallback"}, {"id": "NG1902", "symbol": "➻", "fallback": "[METAPHYSICSFN2]", "fallback_length": 16, "reason": "Long fallback"}, {"id": "NG1903", "symbol": "❜", "fallback": "[METAPHYSICSFN3]", "fallback_length": 16, "reason": "Long fallback"}, {"id": "NG1904", "symbol": "⪻", "fallback": "[METAPHYSICSPROC]", "fallback_length": 17, "reason": "Long fallback"}, {"id": "NG1905", "symbol": "⮻", "fallback": "[METAPHYSICSCTRL6]", "fallback_length": 18, "reason": "Long fallback"}, {"id": "NG1906", "symbol": "⬲", "fallback": "[ATTENT..]", "fallback_length": 10, "reason": "Long fallback"}, {"id": "NG1907", "symbol": "⨢", "fallback": "[COGNIT..]", "fallback_length": 10, "reason": "Long fallback"}, {"id": "NG1908", "symbol": "⊼", "fallback": "[REASONINGFN]", "fallback_length": 13, "reason": "Long fallback"}, {"id": "NG1909", "symbol": "⨧", "fallback": "[ATTENTIONPROC1]", "fallback_length": 16, "reason": "Long fallback"}, {"id": "NG1910", "symbol": "⧨", "fallback": "[MEMORY..]", "fallback_length": 10, "reason": "Long fallback"}, {"id": "NG1911", "symbol": "➕", "fallback": "[MEMORYOP]", "fallback_length": 10, "reason": "Long fallback"}, {"id": "NG1912", "symbol": "⎼", "fallback": "[COGNITION]", "fallback_length": 11, "reason": "Long fallback"}, {"id": "NG1913", "symbol": "➼", "fallback": "[MEMORYMETA]", "fallback_length": 12, "reason": "Long fallback"}, {"id": "NG1914", "symbol": "⪼", "fallback": "[REASONING]", "fallback_length": 11, "reason": "Long fallback"}, {"id": "NG1915", "symbol": "⩇", "fallback": "[ATTENT..]", "fallback_length": 10, "reason": "Long fallback"}, {"id": "NG1916", "symbol": "➜", "fallback": "[MEMORY..]", "fallback_length": 10, "reason": "Long fallback"}, {"id": "NG1917", "symbol": "⎽", "fallback": "[MEMORYMODELS]", "fallback_length": 14, "reason": "Long fallback"}, {"id": "NG1918", "symbol": "⩧", "fallback": "[MEMORY..]", "fallback_length": 10, "reason": "Long fallback"}, {"id": "NG1919", "symbol": "➫", "fallback": "[MEMORYMODELS1]", "fallback_length": 15, "reason": "Long fallback"}, {"id": "NG1920", "symbol": "⪽", "fallback": "[MEMORYMODELSOP1]", "fallback_length": 17, "reason": "Long fallback"}, {"id": "NG1921", "symbol": "⮽", "fallback": "[MEMORYMODELSCTRL]", "fallback_length": 18, "reason": "Long fallback"}, {"id": "NG1922", "symbol": "⎾", "fallback": "[MEMORYMODELS2]", "fallback_length": 15, "reason": "Long fallback"}, {"id": "NG1923", "symbol": "✤", "fallback": "[MEMORYMODELSPROC1]", "fallback_length": 19, "reason": "Long fallback"}, {"id": "NG1924", "symbol": "➾", "fallback": "[MEMORYMODELSCTRL1]", "fallback_length": 19, "reason": "Long fallback"}, {"id": "NG1925", "symbol": "⮾", "fallback": "[MEMORYMODELSSYS]", "fallback_length": 17, "reason": "Long fallback"}, {"id": "NG1926", "symbol": "✂", "fallback": "[ATTENT..]", "fallback_length": 10, "reason": "Long fallback"}, {"id": "NG1927", "symbol": "⎿", "fallback": "[ATTENTIONMODELS1]", "fallback_length": 18, "reason": "Long fallback"}, {"id": "NG1928", "symbol": "⠉", "fallback": "[ATTENT..]", "fallback_length": 10, "reason": "Long fallback"}, {"id": "NG1929", "symbol": "⪿", "fallback": "[ATTENTIONMODELSFN]", "fallback_length": 19, "reason": "Long fallback"}, {"id": "NG1930", "symbol": "⧺", "fallback": "[ATTENT..]", "fallback_length": 10, "reason": "Long fallback"}, {"id": "NG1931", "symbol": "⢸", "fallback": "[ATTENTIONMODELSOP1]", "fallback_length": 20, "reason": "Long fallback"}, {"id": "NG1932", "symbol": "⨯", "fallback": "[ATTENTIONMODELSFN1]", "fallback_length": 20, "reason": "Long fallback"}, {"id": "NG1933", "symbol": "⢮", "fallback": "[ATTENTIONMODELS2]", "fallback_length": 18, "reason": "Long fallback"}, {"id": "NG1934", "symbol": "⮲", "fallback": "[ATTENTIONMODELSOP2]", "fallback_length": 20, "reason": "Long fallback"}, {"id": "NG1935", "symbol": "⮿", "fallback": "[ATTENTIONMODELS3]", "fallback_length": 18, "reason": "Long fallback"}, {"id": "NG1936", "symbol": "⏀", "fallback": "[DECISIONMAKINGPROC]", "fallback_length": 20, "reason": "Long fallback"}, {"id": "NG1937", "symbol": "⧰", "fallback": "[DECISI..]", "fallback_length": 10, "reason": "Long fallback"}, {"id": "NG1938", "symbol": "⫀", "fallback": "[DECISIONMAKINGCTRL]", "fallback_length": 20, "reason": "Long fallback"}, {"id": "NG1939", "symbol": "⯀", "fallback": "[DECISIONMAKINGOP]", "fallback_length": 18, "reason": "Long fallback"}, {"id": "NG1940", "symbol": "⣽", "fallback": "[DECISI..]", "fallback_length": 10, "reason": "Long fallback"}, {"id": "NG1941", "symbol": "⥗", "fallback": "[DECISI..]", "fallback_length": 10, "reason": "Long fallback"}, {"id": "NG1942", "symbol": "⏁", "fallback": "[DECISIONMAKINGFN1]", "fallback_length": 19, "reason": "Long fallback"}, {"id": "NG1943", "symbol": "❋", "fallback": "[DECISIONMAKINGOP1]", "fallback_length": 19, "reason": "Long fallback"}, {"id": "NG1944", "symbol": "⫁", "fallback": "[DECISIONMAKING]", "fallback_length": 16, "reason": "Long fallback"}, {"id": "NG1945", "symbol": "⠠", "fallback": "[DECISIONMAKING1]", "fallback_length": 17, "reason": "Long fallback"}, {"id": "NG1946", "symbol": "⯁", "fallback": "[LEARNINGMECHANISMS]", "fallback_length": 20, "reason": "Long fallback"}, {"id": "NG1947", "symbol": "✘", "fallback": "[PERCEP..]", "fallback_length": 10, "reason": "Long fallback"}, {"id": "NG1948", "symbol": "⏂", "fallback": "[PERCEPTIONMODELSOP]", "fallback_length": 20, "reason": "Long fallback"}, {"id": "NG1949", "symbol": "⫂", "fallback": "[PERCEPTIONMODELSFN]", "fallback_length": 20, "reason": "Long fallback"}, {"id": "NG1950", "symbol": "⡹", "fallback": "[PERCEPTIONMODELS1]", "fallback_length": 19, "reason": "Long fallback"}, {"id": "NG1951", "symbol": "⯂", "fallback": "[PERCEPTIONMODELS2]", "fallback_length": 19, "reason": "Long fallback"}, {"id": "NG1952", "symbol": "⫃", "fallback": "[PERCEPTIONMODELS3]", "fallback_length": 19, "reason": "Long fallback"}, {"id": "NG1953", "symbol": "⏄", "fallback": "[PERCEPTIONMODELS4]", "fallback_length": 19, "reason": "Long fallback"}, {"id": "NG1954", "symbol": "⬺", "fallback": "[PERCEPTIONMODELS5]", "fallback_length": 19, "reason": "Long fallback"}, {"id": "NG1955", "symbol": "⠚", "fallback": "[PERCEPTIONMODELS6]", "fallback_length": 19, "reason": "Long fallback"}, {"id": "NG1956", "symbol": "⣭", "fallback": "[PERCEPTIONMODELS7]", "fallback_length": 19, "reason": "Long fallback"}, {"id": "NG1957", "symbol": "⋅", "fallback": "[PERCEPTIONMODELS8]", "fallback_length": 19, "reason": "Long fallback"}, {"id": "NG1958", "symbol": "⏅", "fallback": "[PERCEPTIONMODELS9]", "fallback_length": 19, "reason": "Long fallback"}, {"id": "NG1959", "symbol": "❁", "fallback": "[PERCEPTIONMODELS10]", "fallback_length": 20, "reason": "Long fallback"}, {"id": "NG1960", "symbol": "⪖", "fallback": "[FUTURE..]", "fallback_length": 10, "reason": "Long fallback"}, {"id": "NG1961", "symbol": "⭝", "fallback": "[EXPERI..]", "fallback_length": 10, "reason": "Long fallback"}, {"id": "NG1962", "symbol": "⠑", "fallback": "[EXPERI..]", "fallback_length": 10, "reason": "Long fallback"}, {"id": "NG1963", "symbol": "⨮", "fallback": "[RESEAR..]", "fallback_length": 10, "reason": "Long fallback"}, {"id": "NG1964", "symbol": "⏆", "fallback": "[FUTURESYS1]", "fallback_length": 12, "reason": "Long fallback"}, {"id": "NG1966", "symbol": "⫈", "fallback": "[EXPERI..]", "fallback_length": 10, "reason": "Long fallback"}, {"id": "NG1967", "symbol": "⫆", "fallback": "[FUTURECTRL]", "fallback_length": 12, "reason": "Long fallback"}, {"id": "NG1968", "symbol": "⋣", "fallback": "[FUTUREOP]", "fallback_length": 10, "reason": "Long fallback"}, {"id": "NG1969", "symbol": "⠙", "fallback": "[NOVELC..]", "fallback_length": 10, "reason": "Long fallback"}, {"id": "NG1970", "symbol": "⣧", "fallback": "[NOVELM..]", "fallback_length": 10, "reason": "Long fallback"}, {"id": "NG1971", "symbol": "⯆", "fallback": "[NOVELCTRL]", "fallback_length": 11, "reason": "Long fallback"}, {"id": "NG1972", "symbol": "⤟", "fallback": "[FUTURE..]", "fallback_length": 10, "reason": "Long fallback"}, {"id": "NG1973", "symbol": "✗", "fallback": "[RESEARCH]", "fallback_length": 10, "reason": "Long fallback"}, {"id": "NG1974", "symbol": "⪦", "fallback": "[RESEAR..]", "fallback_length": 10, "reason": "Long fallback"}, {"id": "NG1975", "symbol": "⏇", "fallback": "[RESEARCHAREASCTRL]", "fallback_length": 19, "reason": "Long fallback"}, {"id": "NG1976", "symbol": "✜", "fallback": "[RESEAR..]", "fallback_length": 10, "reason": "Long fallback"}, {"id": "NG1977", "symbol": "⬩", "fallback": "[RESEAR..]", "fallback_length": 10, "reason": "Long fallback"}, {"id": "NG1978", "symbol": "⧂", "fallback": "[RESEAR..]", "fallback_length": 10, "reason": "Long fallback"}, {"id": "NG1979", "symbol": "✯", "fallback": "[RESEAR..]", "fallback_length": 10, "reason": "Long fallback"}, {"id": "NG1980", "symbol": "⫇", "fallback": "[RESEARCHAREAS1]", "fallback_length": 16, "reason": "Long fallback"}, {"id": "NG1981", "symbol": "⯇", "fallback": "[RESEARCHAREASCORE1]", "fallback_length": 20, "reason": "Long fallback"}, {"id": "NG1982", "symbol": "⠻", "fallback": "[RESEARCHAREASCTRL1]", "fallback_length": 20, "reason": "Long fallback"}, {"id": "NG1983", "symbol": "⭯", "fallback": "[RESEARCHAREASOP1]", "fallback_length": 18, "reason": "Long fallback"}, {"id": "NG1984", "symbol": "⏈", "fallback": "[RESEARCHAREASFN]", "fallback_length": 17, "reason": "Long fallback"}, {"id": "NG1985", "symbol": "⯶", "fallback": "[RESEARCHAREASFN1]", "fallback_length": 18, "reason": "Long fallback"}, {"id": "NG1986", "symbol": "⯈", "fallback": "[RESEARCHAREASCTRL2]", "fallback_length": 20, "reason": "Long fallback"}, {"id": "NG1987", "symbol": "⏉", "fallback": "[RESEARCHAREASFN2]", "fallback_length": 18, "reason": "Long fallback"}, {"id": "NG1988", "symbol": "➵", "fallback": "[EMERGI..]", "fallback_length": 10, "reason": "Long fallback"}, {"id": "NG1989", "symbol": "➮", "fallback": "[EMERGINGPARADIGMS1]", "fallback_length": 20, "reason": "Long fallback"}, {"id": "NG1990", "symbol": "⏊", "fallback": "[EMERGINGPARADIGMS2]", "fallback_length": 20, "reason": "Long fallback"}, {"id": "NG1991", "symbol": "⫊", "fallback": "[EMERGINGPARADIGMS3]", "fallback_length": 20, "reason": "Long fallback"}, {"id": "NG1992", "symbol": "⏋", "fallback": "[EMERGINGPARADIGMS4]", "fallback_length": 20, "reason": "Long fallback"}, {"id": "NG1993", "symbol": "✊", "fallback": "[EMERGINGPARADIGMS5]", "fallback_length": 20, "reason": "Long fallback"}, {"id": "NG1994", "symbol": "⭱", "fallback": "[NOVELA..]", "fallback_length": 10, "reason": "Long fallback"}, {"id": "NG1995", "symbol": "⫋", "fallback": "[NOVELABSTRACTIONS1]", "fallback_length": 20, "reason": "Long fallback"}, {"id": "NG1996", "symbol": "⯋", "fallback": "[NOVELABSTRACTIONS2]", "fallback_length": 20, "reason": "Long fallback"}, {"id": "NG1997", "symbol": "⋌", "fallback": "[NOVELABSTRACTIONS3]", "fallback_length": 20, "reason": "Long fallback"}, {"id": "NG1998", "symbol": "⮣", "fallback": "[NOVELABSTRACTIONS4]", "fallback_length": 20, "reason": "Long fallback"}, {"id": "NG1999", "symbol": "⏌", "fallback": "[NOVELABSTRACTIONS5]", "fallback_length": 20, "reason": "Long fallback"}, {"id": "NG2000", "symbol": "⯌", "fallback": "[NOVELABSTRACTIONS6]", "fallback_length": 20, "reason": "Long fallback"}, {"id": "NG2001", "symbol": "⋍", "fallback": "[NOVELABSTRACTIONS7]", "fallback_length": 20, "reason": "Long fallback"}, {"id": "NG2002", "symbol": "⏍", "fallback": "[NOVELABSTRACTIONS8]", "fallback_length": 20, "reason": "Long fallback"}, {"id": "NG2003", "symbol": "⡳", "fallback": "[EXTENS..]", "fallback_length": 10, "reason": "Long fallback"}, {"id": "NG2004", "symbol": "⤐", "fallback": "[EXTENS..]", "fallback_length": 10, "reason": "Long fallback"}, {"id": "NG2005", "symbol": "⯍", "fallback": "[EXTENSIONPOINTSFN1]", "fallback_length": 20, "reason": "Long fallback"}, {"id": "NG2006", "symbol": "⏎", "fallback": "[EXTENSIONPOINTSOP1]", "fallback_length": 20, "reason": "Long fallback"}, {"id": "NG2007", "symbol": "⤤", "fallback": "[EXTENSIONPOINTSOP2]", "fallback_length": 20, "reason": "Long fallback"}, {"id": "NG2008", "symbol": "⫎", "fallback": "[EXTENSIONPOINTSOP3]", "fallback_length": 20, "reason": "Long fallback"}, {"id": "NG2009", "symbol": "⬤", "fallback": "[EXTENS..]", "fallback_length": 10, "reason": "Long fallback"}, {"id": "NG2010", "symbol": "⏏", "fallback": "[EXTENSIONPOINTS]", "fallback_length": 17, "reason": "Long fallback"}, {"id": "NG2011", "symbol": "⭢", "fallback": "[EXTENSIONPOINTS1]", "fallback_length": 18, "reason": "Long fallback"}, {"id": "NG2012", "symbol": "⯏", "fallback": "[EXTENSIONPOINTSFN2]", "fallback_length": 20, "reason": "Long fallback"}, {"id": "NG2013", "symbol": "⋐", "fallback": "[EXTENSIONPOINTS2]", "fallback_length": 18, "reason": "Long fallback"}, {"id": "NG2014", "symbol": "⫐", "fallback": "[EXTENSIONPOINTSOP4]", "fallback_length": 20, "reason": "Long fallback"}, {"id": "NG2015", "symbol": "⢋", "fallback": "[EXTENSIONPOINTSFN3]", "fallback_length": 20, "reason": "Long fallback"}, {"id": "NG2016", "symbol": "⋑", "fallback": "[EXTENSIONPOINTSFN4]", "fallback_length": 20, "reason": "Long fallback"}, {"id": "NG2017", "symbol": "𝜕", "fallback": "[EXTENSIONPOINTS3]", "fallback_length": 18, "reason": "Long fallback"}, {"id": "NG2018", "symbol": "⏑", "fallback": "[EXTENSIONPOINTS4]", "fallback_length": 18, "reason": "Long fallback"}, {"id": "NG2019", "symbol": "⬚", "fallback": "[EXTENSIONPOINTSFN5]", "fallback_length": 20, "reason": "Long fallback"}, {"id": "NG2020", "symbol": "⦦", "fallback": "[EXTENSIONPOINTSFN6]", "fallback_length": 20, "reason": "Long fallback"}, {"id": "NG2021", "symbol": "⫑", "fallback": "[EXTENSIONPOINTSFN7]", "fallback_length": 20, "reason": "Long fallback"}, {"id": "NG2022", "symbol": "⯑", "fallback": "[EXTENSIONPOINTS5]", "fallback_length": 18, "reason": "Long fallback"}, {"id": "NG2023", "symbol": "⋒", "fallback": "[EXTENSIONPOINTSOP5]", "fallback_length": 20, "reason": "Long fallback"}, {"id": "NG2024", "symbol": "✰", "fallback": "[EXTENSIONPOINTSOP6]", "fallback_length": 20, "reason": "Long fallback"}, {"id": "NG2025", "symbol": "⏒", "fallback": "[EXTENSIONPOINTSOP7]", "fallback_length": 20, "reason": "Long fallback"}, {"id": "NG2026", "symbol": "➭", "fallback": "[EXTENSIONPOINTSOP8]", "fallback_length": 20, "reason": "Long fallback"}, {"id": "NG2027", "symbol": "⥨", "fallback": "[EXTENSIONPOINTSFN8]", "fallback_length": 20, "reason": "Long fallback"}, {"id": "NG2028", "symbol": "⫒", "fallback": "[EXTENSIONPOINTS6]", "fallback_length": 18, "reason": "Long fallback"}, {"id": "NG2029", "symbol": "⦟", "fallback": "[EXTENSIONPOINTS7]", "fallback_length": 18, "reason": "Long fallback"}, {"id": "NG2030", "symbol": "⯒", "fallback": "[EXTENSIONPOINTSOP9]", "fallback_length": 20, "reason": "Long fallback"}, {"id": "NG2031", "symbol": "⫓", "fallback": "[EXTENSIONPOINTSFN9]", "fallback_length": 20, "reason": "Long fallback"}, {"id": "NG2032", "symbol": "⠷", "fallback": "[EXTENSIONPOINTS8]", "fallback_length": 18, "reason": "Long fallback"}, {"id": "NG2033", "symbol": "⏔", "fallback": "[EXTENSIONPOINTS9]", "fallback_length": 18, "reason": "Long fallback"}, {"id": "NG2034", "symbol": "⮏", "fallback": "[EXTENSIONPOINTS10]", "fallback_length": 19, "reason": "Long fallback"}, {"id": "NG2035", "symbol": "⯔", "fallback": "[EXTENSIONPOINTS11]", "fallback_length": 19, "reason": "Long fallback"}, {"id": "NG2036", "symbol": "⋕", "fallback": "[EXTENSIONPOINTS12]", "fallback_length": 19, "reason": "Long fallback"}, {"id": "NG2037", "symbol": "⏕", "fallback": "[EXTENSIONPOINTS13]", "fallback_length": 19, "reason": "Long fallback"}, {"id": "NG2038", "symbol": "⋖", "fallback": "[EXTENSIONPOINTS14]", "fallback_length": 19, "reason": "Long fallback"}, {"id": "NG2039", "symbol": "⏖", "fallback": "[EXTENSIONPOINTS15]", "fallback_length": 19, "reason": "Long fallback"}, {"id": "NG2040", "symbol": "⬄", "fallback": "[EXTENSIONPOINTS16]", "fallback_length": 19, "reason": "Long fallback"}, {"id": "NG2041", "symbol": "⫖", "fallback": "[EXTENSIONPOINTS17]", "fallback_length": 19, "reason": "Long fallback"}, {"id": "NG2042", "symbol": "⨽", "fallback": "[EXTENSIONPOINTS18]", "fallback_length": 19, "reason": "Long fallback"}, {"id": "NG2043", "symbol": "⩌", "fallback": "[EXTENSIONPOINTS19]", "fallback_length": 19, "reason": "Long fallback"}, {"id": "NG2044", "symbol": "⣷", "fallback": "[EXTENSIONPOINTS20]", "fallback_length": 19, "reason": "Long fallback"}, {"id": "NG2045", "symbol": "⯃", "fallback": "[EXTENSIONPOINTS21]", "fallback_length": 19, "reason": "Long fallback"}, {"id": "NG2046", "symbol": "⋗", "fallback": "[EXTENSIONPOINTS22]", "fallback_length": 19, "reason": "Long fallback"}, {"id": "NG2047", "symbol": "⏗", "fallback": "[EXTENSIONPOINTS23]", "fallback_length": 19, "reason": "Long fallback"}, {"id": "NG2048", "symbol": "⫾", "fallback": "[EXTENSIONPOINTS24]", "fallback_length": 19, "reason": "Long fallback"}, {"id": "NG2059", "symbol": "⫦", "fallback": "[BATCHNOR]", "fallback_length": 10, "reason": "Long fallback"}, {"id": "NG2079", "symbol": "⏮", "fallback": "[BATCHNOR]", "fallback_length": 10, "reason": "Long fallback"}, {"id": "NG2099", "symbol": "⯷", "fallback": "[BATCHNOR]", "fallback_length": 10, "reason": "Long fallback"}], "token_cost_distribution": {"1": 1735, "3": 55}, "fallback_analysis": {"total_fallbacks": 1790, "compact_fallbacks": 418, "long_fallbacks": 1372, "avg_fallback_length": 13.046927374301676}}, "embedding_stats": {"total_symbols": 1790, "unicode_ranges": {"2500-25FF": 94, "2A00-2AFF": 252, "2200-22FF": 242, "2300-23FF": 249, "2900-29FF": 255, "2100-21FF": 101, "2600-26FF": 24, "2B00-2BFF": 253, "2700-27FF": 192, "2800-28FF": 104, "1D700-1D7FF": 4}, "symbol_density": {}, "potential_conflicts": [], "unicode_coverage": {"mathematical_operators": 242, "miscellaneous_technical": 249, "geometric_shapes": 94, "dingbats": 192, "supplemental_arrows": 255, "other_ranges": 738}}, "compression_stats": {"total_symbols": 1790, "domain_distribution": {"unknown": 20, "operator": 95, "memory": 94, "logic": 112, "structure": 89, "flow": 102, "reasoning": 55, "advanced_coding": 306, "meta_programming": 62, "distributed_systems": 108, "quantum_computing": 29, "symbolic_ai": 44, "neural_architectures": 72, "formal_verification": 49, "category_theory": 43, "type_theory": 37, "concurrency_advanced": 79, "machine_learning": 123, "mathematical_structures": 64, "philosophical_concepts": 64, "cognitive_modeling": 54, "reserved_expansion": 89}, "semantic_density": {}, "code_patterns": {"ng:operator": 99, "ng:logic": 120, "ng:reasoning": 56, "ng:structure": 91, "ng:flow": 107, "ng:memory": 94, "ng:advanced_coding": 306, "ng:meta_programming": 62, "ng:distributed_systems": 108, "ng:quantum_computing": 29, "ng:symbolic_ai": 44, "ng:neural_architectures": 72, "ng:formal_verification": 49, "ng:category_theory": 43, "ng:type_theory": 37, "ng:concurrency_advanced": 79, "ng:machine_learning": 123, "ng:mathematical_structures": 64, "ng:philosophical_concepts": 64, "ng:cognitive_modeling": 54, "ng:reserved_expansion": 89}, "naming_consistency": {"consistent_naming": 1250, "inconsistent_naming": 540, "naming_issues": [{"id": "NG0025", "name": "or_1", "reason": "Generic numbered name"}, {"id": "NG0026", "name": "alloc_1", "reason": "Generic numbered name"}, {"id": "NG0028", "name": "function_1", "reason": "Generic numbered name"}, {"id": "NG0035", "name": "return_1", "reason": "Generic numbered name"}, {"id": "NG0041", "name": "and_1", "reason": "Generic numbered name"}, {"id": "NG0042", "name": "function_2", "reason": "Generic numbered name"}, {"id": "NG0043", "name": "property_1", "reason": "Generic numbered name"}, {"id": "NG0044", "name": "pointer_1", "reason": "Generic numbered name"}, {"id": "NG0047", "name": "implies_1", "reason": "Generic numbered name"}, {"id": "NG0048", "name": "break_1", "reason": "Generic numbered name"}, {"id": "NG0053", "name": "deref_1", "reason": "Generic numbered name"}, {"id": "NG0054", "name": "class_1", "reason": "Generic numbered name"}, {"id": "NG0056", "name": "pointer_2", "reason": "Generic numbered name"}, {"id": "NG0057", "name": "break_2", "reason": "Generic numbered name"}, {"id": "NG0058", "name": "deref_2", "reason": "Generic numbered name"}, {"id": "NG0061", "name": "for_2", "reason": "Generic numbered name"}, {"id": "NG0064", "name": "property_2", "reason": "Generic numbered name"}, {"id": "NG0091", "name": "else_1", "reason": "Generic numbered name"}, {"id": "NG0092", "name": "method_1", "reason": "Generic numbered name"}, {"id": "NG0094", "name": "else_2", "reason": "Generic numbered name"}, {"id": "NG0096", "name": "sub_1", "reason": "Generic numbered name"}, {"id": "NG0099", "name": "class_2", "reason": "Generic numbered name"}, {"id": "NG0115", "name": "method_2", "reason": "Generic numbered name"}, {"id": "NG0116", "name": "free_1", "reason": "Generic numbered name"}, {"id": "NG0121", "name": "pow_1", "reason": "Generic numbered name"}, {"id": "NG0131", "name": "pow_2", "reason": "Generic numbered name"}, {"id": "NG0134", "name": "xor_1", "reason": "Generic numbered name"}, {"id": "NG0143", "name": "return_2", "reason": "Generic numbered name"}, {"id": "NG0152", "name": "ref_1", "reason": "Generic numbered name"}, {"id": "NG0156", "name": "ref_2", "reason": "Generic numbered name"}, {"id": "NG0218", "name": "iff_1", "reason": "Generic numbered name"}, {"id": "NG0225", "name": "xor_2", "reason": "Generic numbered name"}, {"id": "NG0234", "name": "iff_2", "reason": "Generic numbered name"}, {"id": "NG0891", "name": "dynamicdispatch_fn_1", "reason": "Generic numbered name"}, {"id": "NG0899", "name": "garbagecollection_1", "reason": "Generic numbered name"}, {"id": "NG0923", "name": "metaclasses_sys_1", "reason": "Generic numbered name"}, {"id": "NG0928", "name": "metaclasses_1", "reason": "Generic numbered name"}, {"id": "NG0929", "name": "metaclasses_sys_2", "reason": "Generic numbered name"}, {"id": "NG0930", "name": "metaclasses_ctrl_1", "reason": "Generic numbered name"}, {"id": "NG0945", "name": "codegen_op_1", "reason": "Generic numbered name"}, {"id": "NG0948", "name": "emit_ctrl_1", "reason": "Generic numbered name"}, {"id": "NG0953", "name": "generate_sys_1", "reason": "Generic numbered name"}, {"id": "NG0961", "name": "reflect_meta_1", "reason": "Generic numbered name"}, {"id": "NG0962", "name": "meta_meta_1", "reason": "Generic numbered name"}, {"id": "NG0964", "name": "introspect_1", "reason": "Generic numbered name"}, {"id": "NG0965", "name": "meta_meta_2", "reason": "Generic numbered name"}, {"id": "NG0970", "name": "introspection_meta_1", "reason": "Generic numbered name"}, {"id": "NG0971", "name": "introspection_core_1", "reason": "Generic numbered name"}, {"id": "NG0972", "name": "introspection_1", "reason": "Generic numbered name"}, {"id": "NG0974", "name": "introspection_core_2", "reason": "Generic numbered name"}, {"id": "NG0976", "name": "introspection_fn_1", "reason": "Generic numbered name"}, {"id": "NG0983", "name": "dynamicdispatch_1", "reason": "Generic numbered name"}, {"id": "NG0984", "name": "dynamicdispatch_fn_2", "reason": "Generic numbered name"}, {"id": "NG0986", "name": "dynamicdispatch_2", "reason": "Generic numbered name"}, {"id": "NG0989", "name": "dynamicdispatch_op_1", "reason": "Generic numbered name"}, {"id": "NG0992", "name": "metaobjects_fn_1", "reason": "Generic numbered name"}, {"id": "NG0994", "name": "metaobjects_1", "reason": "Generic numbered name"}, {"id": "NG0996", "name": "metaobjects_2", "reason": "Generic numbered name"}, {"id": "NG0997", "name": "metaobjects_fn_2", "reason": "Generic numbered name"}, {"id": "NG0999", "name": "metaobjects_op_1", "reason": "Generic numbered name"}, {"id": "NG1001", "name": "metaobjects_op_2", "reason": "Generic numbered name"}, {"id": "NG1002", "name": "metaobjects_sys_1", "reason": "Generic numbered name"}, {"id": "NG1003", "name": "metaobjects_sys_2", "reason": "Generic numbered name"}, {"id": "NG1009", "name": "bytecode_sys_1", "reason": "Generic numbered name"}, {"id": "NG1011", "name": "bytecode_1", "reason": "Generic numbered name"}, {"id": "NG1012", "name": "bytecode_sys_2", "reason": "Generic numbered name"}, {"id": "NG1013", "name": "bytecode_2", "reason": "Generic numbered name"}, {"id": "NG1014", "name": "bytecode_ctrl_1", "reason": "Generic numbered name"}, {"id": "NG1017", "name": "jitcompilation_sys_1", "reason": "Generic numbered name"}, {"id": "NG1020", "name": "jitcompilation_fn_1", "reason": "Generic numbered name"}, {"id": "NG1023", "name": "jitcompilation_sys_2", "reason": "Generic numbered name"}, {"id": "NG1024", "name": "jitcompilation_op_1", "reason": "Generic numbered name"}, {"id": "NG1026", "name": "jitcompilation_op_2", "reason": "Generic numbered name"}, {"id": "NG1028", "name": "garbagecollection_2", "reason": "Generic numbered name"}, {"id": "NG1038", "name": "memorypools_1", "reason": "Generic numbered name"}, {"id": "NG1042", "name": "memorypools_sys_1", "reason": "Generic numbered name"}, {"id": "NG1043", "name": "memorypools_proc_1", "reason": "Generic numbered name"}, {"id": "NG1044", "name": "memorypools_meta_1", "reason": "Generic numbered name"}, {"id": "NG1045", "name": "memorypools_core_1", "reason": "Generic numbered name"}, {"id": "NG1046", "name": "memorypools_sys_2", "reason": "Generic numbered name"}, {"id": "NG1049", "name": "stackframes_fn_1", "reason": "Generic numbered name"}, {"id": "NG1052", "name": "stackframes_sys_1", "reason": "Generic numbered name"}, {"id": "NG1054", "name": "stackframes_1", "reason": "Generic numbered name"}, {"id": "NG1055", "name": "stackframes_core_1", "reason": "Generic numbered name"}, {"id": "NG1057", "name": "stackframes_meta_1", "reason": "Generic numbered name"}, {"id": "NG1058", "name": "stackframes_fn_2", "reason": "Generic numbered name"}, {"id": "NG1059", "name": "stackframes_core_2", "reason": "Generic numbered name"}, {"id": "NG1060", "name": "heapmanagement_sys_1", "reason": "Generic numbered name"}, {"id": "NG1062", "name": "heapmanagement_sys_2", "reason": "Generic numbered name"}, {"id": "NG1065", "name": "heapmanagement_fn_1", "reason": "Generic numbered name"}, {"id": "NG1068", "name": "heapmanagement_1", "reason": "Generic numbered name"}, {"id": "NG1070", "name": "heapmanagement_fn_2", "reason": "Generic numbered name"}, {"id": "NG1071", "name": "heapmanagement_2", "reason": "Generic numbered name"}, {"id": "NG1073", "name": "coroutines_op_1", "reason": "Generic numbered name"}, {"id": "NG1075", "name": "coroutines_ctrl_1", "reason": "Generic numbered name"}, {"id": "NG1077", "name": "coroutines_fn_1", "reason": "Generic numbered name"}, {"id": "NG1078", "name": "coroutines_op_2", "reason": "Generic numbered name"}, {"id": "NG1079", "name": "coroutines_1", "reason": "Generic numbered name"}, {"id": "NG1080", "name": "coroutines_2", "reason": "Generic numbered name"}, {"id": "NG1081", "name": "coroutines_meta_1", "reason": "Generic numbered name"}, {"id": "NG1086", "name": "generators_sys_1", "reason": "Generic numbered name"}, {"id": "NG1088", "name": "generators_meta_1", "reason": "Generic numbered name"}, {"id": "NG1090", "name": "generators_ctrl_1", "reason": "Generic numbered name"}, {"id": "NG1091", "name": "generators_meta_2", "reason": "Generic numbered name"}, {"id": "NG1092", "name": "generators_ctrl_2", "reason": "Generic numbered name"}, {"id": "NG1094", "name": "generators_proc_1", "reason": "Generic numbered name"}, {"id": "NG1098", "name": "iterators_fn_1", "reason": "Generic numbered name"}, {"id": "NG1100", "name": "iterators_core_1", "reason": "Generic numbered name"}, {"id": "NG1101", "name": "iterators_sys_1", "reason": "Generic numbered name"}, {"id": "NG1102", "name": "iterators_ctrl_1", "reason": "Generic numbered name"}, {"id": "NG1103", "name": "iterators_1", "reason": "Generic numbered name"}, {"id": "NG1104", "name": "iterators_ctrl_2", "reason": "Generic numbered name"}, {"id": "NG1105", "name": "iterators_core_2", "reason": "Generic numbered name"}, {"id": "NG1106", "name": "iterators_fn_2", "reason": "Generic numbered name"}, {"id": "NG1110", "name": "comprehensions_op_1", "reason": "Generic numbered name"}, {"id": "NG1111", "name": "comprehensions_sys_1", "reason": "Generic numbered name"}, {"id": "NG1112", "name": "comprehensions_fn_1", "reason": "Generic numbered name"}, {"id": "NG1115", "name": "comprehensions_op_2", "reason": "Generic numbered name"}, {"id": "NG1116", "name": "comprehensions_sys_2", "reason": "Generic numbered name"}, {"id": "NG1117", "name": "comprehensions_1", "reason": "Generic numbered name"}, {"id": "NG1122", "name": "decorators_proc_1", "reason": "Generic numbered name"}, {"id": "NG1123", "name": "decorators_proc_2", "reason": "Generic numbered name"}, {"id": "NG1124", "name": "decorators_ctrl_1", "reason": "Generic numbered name"}, {"id": "NG1131", "name": "decorators_sys_1", "reason": "Generic numbered name"}, {"id": "NG1133", "name": "contextmanagers_op_1", "reason": "Generic numbered name"}, {"id": "NG1134", "name": "contextmanagers_1", "reason": "Generic numbered name"}, {"id": "NG1135", "name": "contextmanagers_op_2", "reason": "Generic numbered name"}, {"id": "NG1137", "name": "contextmanagers_2", "reason": "Generic numbered name"}, {"id": "NG1143", "name": "contextmanagers_fn_1", "reason": "Generic numbered name"}, {"id": "NG1146", "name": "descriptors_op_1", "reason": "Generic numbered name"}, {"id": "NG1147", "name": "descriptors_proc_1", "reason": "Generic numbered name"}, {"id": "NG1148", "name": "descriptors_proc_2", "reason": "Generic numbered name"}, {"id": "NG1149", "name": "descriptors_op_2", "reason": "Generic numbered name"}, {"id": "NG1151", "name": "descriptors_meta_1", "reason": "Generic numbered name"}, {"id": "NG1153", "name": "descriptors_fn_1", "reason": "Generic numbered name"}, {"id": "NG1154", "name": "descriptors_core_1", "reason": "Generic numbered name"}, {"id": "NG1155", "name": "descriptors_meta_2", "reason": "Generic numbered name"}, {"id": "NG1157", "name": "metaclasses_meta_1", "reason": "Generic numbered name"}, {"id": "NG1159", "name": "metaclasses_2", "reason": "Generic numbered name"}, {"id": "NG1160", "name": "metaclasses_ctrl_2", "reason": "Generic numbered name"}, {"id": "NG1163", "name": "metaclasses_op_1", "reason": "Generic numbered name"}, {"id": "NG1164", "name": "metaclasses_op_2", "reason": "Generic numbered name"}, {"id": "NG1165", "name": "metaclasses_meta_2", "reason": "Generic numbered name"}, {"id": "NG1168", "name": "metaclasses_proc_1", "reason": "Generic numbered name"}, {"id": "NG1171", "name": "metaclasses_fn_1", "reason": "Generic numbered name"}, {"id": "NG1175", "name": "metaclasses_core_1", "reason": "Generic numbered name"}, {"id": "NG1179", "name": "metaclasses_core_2", "reason": "Generic numbered name"}, {"id": "NG1181", "name": "metaclasses_fn_2", "reason": "Generic numbered name"}, {"id": "NG1185", "name": "metaclasses_proc_2", "reason": "Generic numbered name"}, {"id": "NG1194", "name": "codeasdata_1", "reason": "Generic numbered name"}, {"id": "NG1195", "name": "codeasdata_2", "reason": "Generic numbered name"}, {"id": "NG1198", "name": "codeasdata_core_1", "reason": "Generic numbered name"}, {"id": "NG1199", "name": "codeasdata_op_1", "reason": "Generic numbered name"}, {"id": "NG1201", "name": "codeasdata_sys_1", "reason": "Generic numbered name"}, {"id": "NG1203", "name": "macrosystems_ctrl_1", "reason": "Generic numbered name"}, {"id": "NG1206", "name": "macrosystems_ctrl_2", "reason": "Generic numbered name"}, {"id": "NG1209", "name": "macrosystems_1", "reason": "Generic numbered name"}, {"id": "NG1210", "name": "macrosystems_2", "reason": "Generic numbered name"}, {"id": "NG1212", "name": "macrosystems_op_1", "reason": "Generic numbered name"}, {"id": "NG1214", "name": "macrosystems_op_2", "reason": "Generic numbered name"}, {"id": "NG1215", "name": "macrosystems_proc_1", "reason": "Generic numbered name"}, {"id": "NG1217", "name": "stagedcomputation_1", "reason": "Generic numbered name"}, {"id": "NG1218", "name": "stagedcomputation_2", "reason": "Generic numbered name"}, {"id": "NG1226", "name": "partialevaluation_1", "reason": "Generic numbered name"}, {"id": "NG1227", "name": "partialevaluation_2", "reason": "Generic numbered name"}, {"id": "NG1238", "name": "programsynthesis_1", "reason": "Generic numbered name"}, {"id": "NG1239", "name": "programsynthesis_2", "reason": "Generic numbered name"}, {"id": "NG1256", "name": "consensus_op_1", "reason": "Generic numbered name"}, {"id": "NG1259", "name": "distributedlocks_1", "reason": "Generic numbered name"}, {"id": "NG1266", "name": "vectorclocks_op_1", "reason": "Generic numbered name"}, {"id": "NG1270", "name": "captheorem_core_2", "reason": "Generic numbered name"}, {"id": "NG1274", "name": "captheorem_proc_1", "reason": "Generic numbered name"}, {"id": "NG1276", "name": "gossipprotocols_1", "reason": "Generic numbered name"}, {"id": "NG1278", "name": "gossipprotocols_op_2", "reason": "Generic numbered name"}, {"id": "NG1280", "name": "gossipprotocols_fn_1", "reason": "Generic numbered name"}, {"id": "NG1281", "name": "gossipprotocols_fn_2", "reason": "Generic numbered name"}, {"id": "NG1288", "name": "leaderelection_op_1", "reason": "Generic numbered name"}, {"id": "NG1294", "name": "sharding_meta_1", "reason": "Generic numbered name"}, {"id": "NG1295", "name": "sharding_op_1", "reason": "Generic numbered name"}, {"id": "NG1296", "name": "sharding_op_2", "reason": "Generic numbered name"}, {"id": "NG1297", "name": "sharding_sys_1", "reason": "Generic numbered name"}, {"id": "NG1303", "name": "replication_1", "reason": "Generic numbered name"}, {"id": "NG1304", "name": "replication_sys_1", "reason": "Generic numbered name"}, {"id": "NG1306", "name": "loadbalancing_1", "reason": "Generic numbered name"}, {"id": "NG1312", "name": "loadbalancing_proc_1", "reason": "Generic numbered name"}, {"id": "NG1318", "name": "circuitbreakers_1", "reason": "Generic numbered name"}, {"id": "NG1319", "name": "circuitbreakers_2", "reason": "Generic numbered name"}, {"id": "NG1320", "name": "circuitbreakers_fn_1", "reason": "Generic numbered name"}, {"id": "NG1321", "name": "circuitbreakers_fn_2", "reason": "Generic numbered name"}, {"id": "NG1322", "name": "circuitbreakers_op_1", "reason": "Generic numbered name"}, {"id": "NG1362", "name": "pauli_op_1", "reason": "Generic numbered name"}, {"id": "NG1366", "name": "quantumcircuits_fn_1", "reason": "Generic numbered name"}, {"id": "NG1367", "name": "quantumcircuits_fn_2", "reason": "Generic numbered name"}, {"id": "NG1368", "name": "quantumcircuits_op_1", "reason": "Generic numbered name"}, {"id": "NG1372", "name": "superposition_1", "reason": "Generic numbered name"}, {"id": "NG1374", "name": "superposition_2", "reason": "Generic numbered name"}, {"id": "NG1379", "name": "entanglement_fn_1", "reason": "Generic numbered name"}, {"id": "NG1382", "name": "quantumalgorithms_2", "reason": "Generic numbered name"}, {"id": "NG1395", "name": "theoremproving_sys_1", "reason": "Generic numbered name"}, {"id": "NG1397", "name": "theoremproving_sys_2", "reason": "Generic numbered name"}, {"id": "NG1398", "name": "theoremproving_1", "reason": "Generic numbered name"}, {"id": "NG1403", "name": "expertsystems_core_1", "reason": "Generic numbered name"}, {"id": "NG1404", "name": "expertsystems_meta_1", "reason": "Generic numbered name"}, {"id": "NG1405", "name": "expertsystems_proc_2", "reason": "Generic numbered name"}, {"id": "NG1408", "name": "expertsystems_meta_2", "reason": "Generic numbered name"}, {"id": "NG1417", "name": "ontologies_sys_1", "reason": "Generic numbered name"}, {"id": "NG1419", "name": "ontologies_sys_2", "reason": "Generic numbered name"}, {"id": "NG1421", "name": "ontologies_core_2", "reason": "Generic numbered name"}, {"id": "NG1422", "name": "descriptionlogics_1", "reason": "Generic numbered name"}, {"id": "NG1423", "name": "descriptionlogics_2", "reason": "Generic numbered name"}, {"id": "NG1436", "name": "attention_op_1", "reason": "Generic numbered name"}, {"id": "NG1438", "name": "transformerblocks_1", "reason": "Generic numbered name"}, {"id": "NG1446", "name": "lossfunctions_meta_1", "reason": "Generic numbered name"}, {"id": "NG1457", "name": "theoremproving_fn_1", "reason": "Generic numbered name"}, {"id": "NG1461", "name": "staticanalysis_1", "reason": "Generic numbered name"}, {"id": "NG1464", "name": "symbolicexecution_1", "reason": "Generic numbered name"}, {"id": "NG1469", "name": "temporallogic_ctrl_1", "reason": "Generic numbered name"}, {"id": "NG1473", "name": "hoarelogic_meta_1", "reason": "Generic numbered name"}, {"id": "NG1474", "name": "hoarelogic_meta_2", "reason": "Generic numbered name"}, {"id": "NG1477", "name": "separationlogic_fn_1", "reason": "Generic numbered name"}, {"id": "NG1479", "name": "separationlogic_1", "reason": "Generic numbered name"}, {"id": "NG1483", "name": "separationlogic_fn_2", "reason": "Generic numbered name"}, {"id": "NG1490", "name": "separationlogic_op_1", "reason": "Generic numbered name"}, {"id": "NG1504", "name": "monads_op_1", "reason": "Generic numbered name"}, {"id": "NG1506", "name": "comonads_core_1", "reason": "Generic numbered name"}, {"id": "NG1509", "name": "comonads_meta_1", "reason": "Generic numbered name"}, {"id": "NG1510", "name": "comonads_core_2", "reason": "Generic numbered name"}, {"id": "NG1512", "name": "adjunctions_1", "reason": "Generic numbered name"}, {"id": "NG1513", "name": "adjunctions_2", "reason": "Generic numbered name"}, {"id": "NG1518", "name": "limits_meta_1", "reason": "Generic numbered name"}, {"id": "NG1519", "name": "limits_sys_1", "reason": "Generic numbered name"}, {"id": "NG1522", "name": "colimits_meta_1", "reason": "Generic numbered name"}, {"id": "NG1527", "name": "topoi_core_1", "reason": "Generic numbered name"}, {"id": "NG1531", "name": "sheaves_ctrl_1", "reason": "Generic numbered name"}, {"id": "NG1534", "name": "sheaves_ctrl_2", "reason": "Generic numbered name"}, {"id": "NG1535", "name": "sheaves_op_2", "reason": "Generic numbered name"}, {"id": "NG1536", "name": "sheaves_fn_1", "reason": "Generic numbered name"}, {"id": "NG1548", "name": "lineartypes_meta_1", "reason": "Generic numbered name"}, {"id": "NG1552", "name": "sessiontypes_core_1", "reason": "Generic numbered name"}, {"id": "NG1553", "name": "sessiontypes_op_1", "reason": "Generic numbered name"}, {"id": "NG1555", "name": "effecttypes_sys_1", "reason": "Generic numbered name"}, {"id": "NG1556", "name": "effecttypes_sys_2", "reason": "Generic numbered name"}, {"id": "NG1560", "name": "refinementtypes_1", "reason": "Generic numbered name"}, {"id": "NG1562", "name": "refinementtypes_fn_1", "reason": "Generic numbered name"}, {"id": "NG1563", "name": "refinementtypes_fn_2", "reason": "Generic numbered name"}, {"id": "NG1564", "name": "intersectiontypes_1", "reason": "Generic numbered name"}, {"id": "NG1565", "name": "intersectiontypes_2", "reason": "Generic numbered name"}, {"id": "NG1569", "name": "uniontypes_1", "reason": "Generic numbered name"}, {"id": "NG1570", "name": "uniontypes_meta_1", "reason": "Generic numbered name"}, {"id": "NG1571", "name": "gradualtyping_core_1", "reason": "Generic numbered name"}, {"id": "NG1573", "name": "gradualtyping_proc_1", "reason": "Generic numbered name"}, {"id": "NG1580", "name": "actor_meta_1", "reason": "Generic numbered name"}, {"id": "NG1587", "name": "cspchannels_proc_1", "reason": "Generic numbered name"}, {"id": "NG1588", "name": "cspchannels_fn_2", "reason": "Generic numbered name"}, {"id": "NG1590", "name": "cspchannels_op_1", "reason": "Generic numbered name"}, {"id": "NG1591", "name": "cspchannels_sys_1", "reason": "Generic numbered name"}, {"id": "NG1597", "name": "memoryordering_op_1", "reason": "Generic numbered name"}, {"id": "NG1598", "name": "memoryordering_op_2", "reason": "Generic numbered name"}, {"id": "NG1600", "name": "memoryordering_fn_1", "reason": "Generic numbered name"}, {"id": "NG1604", "name": "memoryordering_sys_1", "reason": "Generic numbered name"}, {"id": "NG1607", "name": "atomicoperations_1", "reason": "Generic numbered name"}, {"id": "NG1615", "name": "compareandswap_sys_1", "reason": "Generic numbered name"}, {"id": "NG1617", "name": "compareandswap_sys_2", "reason": "Generic numbered name"}, {"id": "NG1622", "name": "hazardpointers_fn_1", "reason": "Generic numbered name"}, {"id": "NG1623", "name": "hazardpointers_op_2", "reason": "Generic numbered name"}, {"id": "NG1626", "name": "hazardpointers_sys_1", "reason": "Generic numbered name"}, {"id": "NG1628", "name": "hazardpointers_sys_2", "reason": "Generic numbered name"}, {"id": "NG1635", "name": "hazardpointers_1", "reason": "Generic numbered name"}, {"id": "NG1659", "name": "training_proc_1", "reason": "Generic numbered name"}, {"id": "NG1662", "name": "prediction_proc_1", "reason": "Generic numbered name"}, {"id": "NG1666", "name": "training_proc_2", "reason": "Generic numbered name"}, {"id": "NG1668", "name": "training_ctrl_1", "reason": "Generic numbered name"}, {"id": "NG1670", "name": "training_op_1", "reason": "Generic numbered name"}, {"id": "NG1673", "name": "deeplearning_1", "reason": "Generic numbered name"}, {"id": "NG1676", "name": "deeplearning_2", "reason": "Generic numbered name"}, {"id": "NG1679", "name": "deeplearning_sys_1", "reason": "Generic numbered name"}, {"id": "NG1680", "name": "deeplearning_ctrl_1", "reason": "Generic numbered name"}, {"id": "NG1681", "name": "deeplearning_sys_2", "reason": "Generic numbered name"}, {"id": "NG1682", "name": "deeplearning_core_1", "reason": "Generic numbered name"}, {"id": "NG1683", "name": "deeplearning_ctrl_2", "reason": "Generic numbered name"}, {"id": "NG1684", "name": "deeplearning_op_1", "reason": "Generic numbered name"}, {"id": "NG1685", "name": "deeplearning_meta_1", "reason": "Generic numbered name"}, {"id": "NG1691", "name": "modelevaluation_1", "reason": "Generic numbered name"}, {"id": "NG1692", "name": "modelevaluation_2", "reason": "Generic numbered name"}, {"id": "NG1693", "name": "modelevaluation_op_1", "reason": "Generic numbered name"}, {"id": "NG1694", "name": "modelevaluation_fn_1", "reason": "Generic numbered name"}, {"id": "NG1696", "name": "modelevaluation_fn_2", "reason": "Generic numbered name"}, {"id": "NG1697", "name": "modelevaluation_op_2", "reason": "Generic numbered name"}, {"id": "NG1705", "name": "ensemblemethods_op_1", "reason": "Generic numbered name"}, {"id": "NG1708", "name": "ensemblemethods_fn_1", "reason": "Generic numbered name"}, {"id": "NG1709", "name": "ensemblemethods_1", "reason": "Generic numbered name"}, {"id": "NG1710", "name": "ensemblemethods_fn_2", "reason": "Generic numbered name"}, {"id": "NG1712", "name": "ensemblemethods_2", "reason": "Generic numbered name"}, {"id": "NG1715", "name": "ensemblemethods_op_2", "reason": "Generic numbered name"}, {"id": "NG1779", "name": "ring_op_1", "reason": "Generic numbered name"}, {"id": "NG1785", "name": "algebra_1", "reason": "Generic numbered name"}, {"id": "NG1787", "name": "group_ctrl_1", "reason": "Generic numbered name"}, {"id": "NG1789", "name": "topologicalspaces_1", "reason": "Generic numbered name"}, {"id": "NG1790", "name": "topologicalspaces_2", "reason": "Generic numbered name"}, {"id": "NG1796", "name": "measuretheory_op_1", "reason": "Generic numbered name"}, {"id": "NG1800", "name": "measuretheory_fn_1", "reason": "Generic numbered name"}, {"id": "NG1801", "name": "measuretheory_proc_1", "reason": "Generic numbered name"}, {"id": "NG1805", "name": "numbertheory_ctrl_1", "reason": "Generic numbered name"}, {"id": "NG1809", "name": "numbertheory_meta_1", "reason": "Generic numbered name"}, {"id": "NG1811", "name": "numbertheory_proc_1", "reason": "Generic numbered name"}, {"id": "NG1812", "name": "numbertheory_proc_2", "reason": "Generic numbered name"}, {"id": "NG1817", "name": "combinatorics_sys_1", "reason": "Generic numbered name"}, {"id": "NG1821", "name": "combinatorics_proc_1", "reason": "Generic numbered name"}, {"id": "NG1822", "name": "combinatorics_op_1", "reason": "Generic numbered name"}, {"id": "NG1823", "name": "combinatorics_core_1", "reason": "Generic numbered name"}, {"id": "NG1824", "name": "combinatorics_op_2", "reason": "Generic numbered name"}, {"id": "NG1825", "name": "combinatorics_core_2", "reason": "Generic numbered name"}, {"id": "NG1826", "name": "combinatorics_meta_1", "reason": "Generic numbered name"}, {"id": "NG1829", "name": "combinatorics_fn_1", "reason": "Generic numbered name"}, {"id": "NG1831", "name": "combinatorics_meta_2", "reason": "Generic numbered name"}, {"id": "NG1832", "name": "combinatorics_ctrl_1", "reason": "Generic numbered name"}, {"id": "NG1833", "name": "combinatorics_fn_2", "reason": "Generic numbered name"}, {"id": "NG1840", "name": "combinatorics_ctrl_2", "reason": "Generic numbered name"}, {"id": "NG1845", "name": "knowledge_ctrl_1", "reason": "Generic numbered name"}, {"id": "NG1854", "name": "ontology_sys_1", "reason": "Generic numbered name"}, {"id": "NG1855", "name": "ontology_op_1", "reason": "Generic numbered name"}, {"id": "NG1856", "name": "ontology_sys_2", "reason": "Generic numbered name"}, {"id": "NG1857", "name": "ontology_op_2", "reason": "Generic numbered name"}, {"id": "NG1865", "name": "logicphilosophy_fn_1", "reason": "Generic numbered name"}, {"id": "NG1866", "name": "logicphilosophy_op_1", "reason": "Generic numbered name"}, {"id": "NG1867", "name": "logicphilosophy_fn_2", "reason": "Generic numbered name"}, {"id": "NG1869", "name": "logicphilosophy_op_2", "reason": "Generic numbered name"}, {"id": "NG1874", "name": "philosophyofmind_1", "reason": "Generic numbered name"}, {"id": "NG1876", "name": "philosophyofmind_2", "reason": "Generic numbered name"}, {"id": "NG1880", "name": "ethics_op_1", "reason": "Generic numbered name"}, {"id": "NG1884", "name": "ethics_1", "reason": "Generic numbered name"}, {"id": "NG1885", "name": "ethics_core_1", "reason": "Generic numbered name"}, {"id": "NG1887", "name": "ethics_2", "reason": "Generic numbered name"}, {"id": "NG1891", "name": "metaphysics_ctrl_1", "reason": "Generic numbered name"}, {"id": "NG1892", "name": "metaphysics_ctrl_2", "reason": "Generic numbered name"}, {"id": "NG1897", "name": "metaphysics_meta_1", "reason": "Generic numbered name"}, {"id": "NG1899", "name": "metaphysics_1", "reason": "Generic numbered name"}, {"id": "NG1900", "name": "metaphysics_fn_1", "reason": "Generic numbered name"}, {"id": "NG1902", "name": "metaphysics_fn_2", "reason": "Generic numbered name"}, {"id": "NG1909", "name": "attention_proc_1", "reason": "Generic numbered name"}, {"id": "NG1919", "name": "memorymodels_1", "reason": "Generic numbered name"}, {"id": "NG1920", "name": "memorymodels_op_1", "reason": "Generic numbered name"}, {"id": "NG1922", "name": "memorymodels_2", "reason": "Generic numbered name"}, {"id": "NG1923", "name": "memorymodels_proc_1", "reason": "Generic numbered name"}, {"id": "NG1924", "name": "memorymodels_ctrl_1", "reason": "Generic numbered name"}, {"id": "NG1927", "name": "attentionmodels_1", "reason": "Generic numbered name"}, {"id": "NG1931", "name": "attentionmodels_op_1", "reason": "Generic numbered name"}, {"id": "NG1932", "name": "attentionmodels_fn_1", "reason": "Generic numbered name"}, {"id": "NG1933", "name": "attentionmodels_2", "reason": "Generic numbered name"}, {"id": "NG1934", "name": "attentionmodels_op_2", "reason": "Generic numbered name"}, {"id": "NG1942", "name": "decisionmaking_fn_1", "reason": "Generic numbered name"}, {"id": "NG1943", "name": "decisionmaking_op_1", "reason": "Generic numbered name"}, {"id": "NG1945", "name": "decisionmaking_1", "reason": "Generic numbered name"}, {"id": "NG1950", "name": "perceptionmodels_1", "reason": "Generic numbered name"}, {"id": "NG1951", "name": "perceptionmodels_2", "reason": "Generic numbered name"}, {"id": "NG1964", "name": "future_sys_1", "reason": "Generic numbered name"}, {"id": "NG1980", "name": "researchareas_1", "reason": "Generic numbered name"}, {"id": "NG1981", "name": "researchareas_core_1", "reason": "Generic numbered name"}, {"id": "NG1982", "name": "researchareas_ctrl_1", "reason": "Generic numbered name"}, {"id": "NG1983", "name": "researchareas_op_1", "reason": "Generic numbered name"}, {"id": "NG1985", "name": "researchareas_fn_1", "reason": "Generic numbered name"}, {"id": "NG1986", "name": "researchareas_ctrl_2", "reason": "Generic numbered name"}, {"id": "NG1987", "name": "researchareas_fn_2", "reason": "Generic numbered name"}, {"id": "NG1989", "name": "emergingparadigms_1", "reason": "Generic numbered name"}, {"id": "NG1990", "name": "emergingparadigms_2", "reason": "Generic numbered name"}, {"id": "NG1995", "name": "novelabstractions_1", "reason": "Generic numbered name"}, {"id": "NG1996", "name": "novelabstractions_2", "reason": "Generic numbered name"}, {"id": "NG2005", "name": "extensionpoints_fn_1", "reason": "Generic numbered name"}, {"id": "NG2006", "name": "extensionpoints_op_1", "reason": "Generic numbered name"}, {"id": "NG2007", "name": "extensionpoints_op_2", "reason": "Generic numbered name"}, {"id": "NG2011", "name": "extensionpoints_1", "reason": "Generic numbered name"}, {"id": "NG2012", "name": "extensionpoints_fn_2", "reason": "Generic numbered name"}, {"id": "NG2013", "name": "extensionpoints_2", "reason": "Generic numbered name"}]}}, "readiness_assessment": {"tokenization_ready": true, "embedding_ready": true, "compression_ready": false, "overall_ready": false}, "recommendations": ["Abbreviare fallback lunghi per compatibilità terminale", "Migliorare consistenza naming per compressione semantica"]}