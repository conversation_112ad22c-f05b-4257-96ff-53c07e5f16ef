{"audit_summary": {"audit_timestamp": "20250526_084147", "registry_version": "9.0.0", "registry_status": "GOD_MODE_2048_COMPLETE", "total_symbols": 2048, "audit_scores": {"duplicate_check": 0, "integrity_check": 100.0, "unicode_safety": 76.1, "quality_metrics": 97.2, "overall_score": 68.3}, "critical_issues": {"has_duplicates": true, "integrity_issues": 0, "unsafe_unicode": 489, "low_quality_symbols": 0}, "registry_health": {"excellent": false, "good": false, "acceptable": false, "needs_improvement": true}, "recommendations": ["CRITICO: Rimuovere duplicati dal registry", "Sostituire Unicode non sicuri con range approvati"]}, "duplicate_results": {"total_symbols": 2048, "duplicate_ids": [], "duplicate_unicode": [], "duplicate_symbols": [], "duplicate_codes": [], "duplicate_names": [{"name": "self_awareness", "count": 2}, {"name": "meta_meta", "count": 2}, {"name": "introspection", "count": 2}, {"name": "temporal_reasoning", "count": 2}, {"name": "downwards_arrow_with_tip_", "count": 2}, {"name": "leftwards_harpoon_with_ba", "count": 2}, {"name": "upwards_harpoon_with_barb", "count": 2}, {"name": "rightwards_harpoon_with_b", "count": 2}, {"name": "downwards_harpoon_with_ba", "count": 2}, {"name": "upwards_white_arrow_on_pe", "count": 3}, {"name": "upwards_white_double_arro", "count": 2}, {"name": "turned_sans_serif_capital", "count": 3}, {"name": "double_struck_italic_smal", "count": 4}], "has_duplicates": true}, "integrity_results": {"total_symbols": 2048, "missing_fields": [], "invalid_formats": [], "quality_issues": [], "integrity_score": 100.0}, "unicode_results": {"total_symbols": 2048, "safe_unicode": 1559, "unsafe_unicode": 489, "range_distribution": {"Geometric Shapes": 96, "Supplemental Mathematical Operators": 255, "Mathematical Operators": 256, "Miscellaneous Technical": 256, "Supplemental Arrows-B": 128, "Miscellaneous Mathematical Symbols-B": 128, "Dingbats": 192, "Miscellaneous Symbols and Arrows": 248}, "unsafe_symbols": [{"id": "NG2142", "symbol": "🜔", "unicode_point": "U+1F714", "code_point": 128788}, {"id": "NG2147", "symbol": "🟈", "unicode_point": "U+1F7C8", "code_point": 128968}, {"id": "NG2151", "symbol": "🣂", "unicode_point": "U+1F8C2", "code_point": 129218}, {"id": "NG2154", "symbol": "🠷", "unicode_point": "U+1F837", "code_point": 129079}, {"id": "NG2157", "symbol": "🞱", "unicode_point": "U+1F7B1", "code_point": 128945}, {"id": "NG2159", "symbol": "🢳", "unicode_point": "U+1F8B3", "code_point": 129203}, {"id": "NG2161", "symbol": "🢁", "unicode_point": "U+1F881", "code_point": 129153}, {"id": "NG2162", "symbol": "🠚", "unicode_point": "U+1F81A", "code_point": 129050}, {"id": "NG2165", "symbol": "🣃", "unicode_point": "U+1F8C3", "code_point": 129219}, {"id": "NG2168", "symbol": "🜛", "unicode_point": "U+1F71B", "code_point": 128795}, {"id": "NG2175", "symbol": "🢺", "unicode_point": "U+1F8BA", "code_point": 129210}, {"id": "NG2177", "symbol": "🜈", "unicode_point": "U+1F708", "code_point": 128776}, {"id": "NG2182", "symbol": "🞥", "unicode_point": "U+1F7A5", "code_point": 128933}, {"id": "NG2183", "symbol": "🟹", "unicode_point": "U+1F7F9", "code_point": 129017}, {"id": "NG2184", "symbol": "🝅", "unicode_point": "U+1F745", "code_point": 128837}, {"id": "NG2185", "symbol": "🟻", "unicode_point": "U+1F7FB", "code_point": 129019}, {"id": "NG2187", "symbol": "🟌", "unicode_point": "U+1F7CC", "code_point": 128972}, {"id": "NG2190", "symbol": "🞂", "unicode_point": "U+1F782", "code_point": 128898}, {"id": "NG2194", "symbol": "🝏", "unicode_point": "U+1F74F", "code_point": 128847}, {"id": "NG2201", "symbol": "🞾", "unicode_point": "U+1F7BE", "code_point": 128958}, {"id": "NG2207", "symbol": "🠢", "unicode_point": "U+1F822", "code_point": 129058}, {"id": "NG2211", "symbol": "🟢", "unicode_point": "U+1F7E2", "code_point": 128994}, {"id": "NG2214", "symbol": "🜱", "unicode_point": "U+1F731", "code_point": 128817}, {"id": "NG2222", "symbol": "🡅", "unicode_point": "U+1F845", "code_point": 129093}, {"id": "NG2224", "symbol": "🟒", "unicode_point": "U+1F7D2", "code_point": 128978}, {"id": "NG2229", "symbol": "🝭", "unicode_point": "U+1F76D", "code_point": 128877}, {"id": "NG2230", "symbol": "🝙", "unicode_point": "U+1F759", "code_point": 128857}, {"id": "NG2231", "symbol": "🞗", "unicode_point": "U+1F797", "code_point": 128919}, {"id": "NG2233", "symbol": "🟯", "unicode_point": "U+1F7EF", "code_point": 129007}, {"id": "NG2234", "symbol": "🜉", "unicode_point": "U+1F709", "code_point": 128777}, {"id": "NG2235", "symbol": "🞬", "unicode_point": "U+1F7AC", "code_point": 128940}, {"id": "NG2238", "symbol": "🞇", "unicode_point": "U+1F787", "code_point": 128903}, {"id": "NG2247", "symbol": "🝃", "unicode_point": "U+1F743", "code_point": 128835}, {"id": "NG2253", "symbol": "🞏", "unicode_point": "U+1F78F", "code_point": 128911}, {"id": "NG2254", "symbol": "🞮", "unicode_point": "U+1F7AE", "code_point": 128942}, {"id": "NG2257", "symbol": "🠴", "unicode_point": "U+1F834", "code_point": 129076}, {"id": "NG2259", "symbol": "🢥", "unicode_point": "U+1F8A5", "code_point": 129189}, {"id": "NG2267", "symbol": "🟚", "unicode_point": "U+1F7DA", "code_point": 128986}, {"id": "NG2268", "symbol": "🢔", "unicode_point": "U+1F894", "code_point": 129172}, {"id": "NG2269", "symbol": "🜗", "unicode_point": "U+1F717", "code_point": 128791}, {"id": "NG2271", "symbol": "🞕", "unicode_point": "U+1F795", "code_point": 128917}, {"id": "NG2273", "symbol": "🝊", "unicode_point": "U+1F74A", "code_point": 128842}, {"id": "NG2279", "symbol": "🠫", "unicode_point": "U+1F82B", "code_point": 129067}, {"id": "NG2281", "symbol": "🢕", "unicode_point": "U+1F895", "code_point": 129173}, {"id": "NG2282", "symbol": "🡵", "unicode_point": "U+1F875", "code_point": 129141}, {"id": "NG2289", "symbol": "🢣", "unicode_point": "U+1F8A3", "code_point": 129187}, {"id": "NG2295", "symbol": "🢚", "unicode_point": "U+1F89A", "code_point": 129178}, {"id": "NG2297", "symbol": "🝗", "unicode_point": "U+1F757", "code_point": 128855}, {"id": "NG2303", "symbol": "🟖", "unicode_point": "U+1F7D6", "code_point": 128982}, {"id": "NG2306", "symbol": "🟫", "unicode_point": "U+1F7EB", "code_point": 129003}, {"id": "NG2311", "symbol": "🢛", "unicode_point": "U+1F89B", "code_point": 129179}, {"id": "NG2313", "symbol": "🞘", "unicode_point": "U+1F798", "code_point": 128920}, {"id": "NG2317", "symbol": "🞫", "unicode_point": "U+1F7AB", "code_point": 128939}, {"id": "NG2319", "symbol": "🟵", "unicode_point": "U+1F7F5", "code_point": 129013}, {"id": "NG2320", "symbol": "🢙", "unicode_point": "U+1F899", "code_point": 129177}, {"id": "NG2322", "symbol": "🞼", "unicode_point": "U+1F7BC", "code_point": 128956}, {"id": "NG2326", "symbol": "🢓", "unicode_point": "U+1F893", "code_point": 129171}, {"id": "NG2327", "symbol": "🟆", "unicode_point": "U+1F7C6", "code_point": 128966}, {"id": "NG2328", "symbol": "🜷", "unicode_point": "U+1F737", "code_point": 128823}, {"id": "NG2329", "symbol": "🠾", "unicode_point": "U+1F83E", "code_point": 129086}, {"id": "NG2332", "symbol": "🝔", "unicode_point": "U+1F754", "code_point": 128852}, {"id": "NG2334", "symbol": "🠿", "unicode_point": "U+1F83F", "code_point": 129087}, {"id": "NG2338", "symbol": "🝇", "unicode_point": "U+1F747", "code_point": 128839}, {"id": "NG2342", "symbol": "🠣", "unicode_point": "U+1F823", "code_point": 129059}, {"id": "NG2343", "symbol": "🠽", "unicode_point": "U+1F83D", "code_point": 129085}, {"id": "NG2349", "symbol": "🝓", "unicode_point": "U+1F753", "code_point": 128851}, {"id": "NG2351", "symbol": "🢯", "unicode_point": "U+1F8AF", "code_point": 129199}, {"id": "NG2353", "symbol": "🞳", "unicode_point": "U+1F7B3", "code_point": 128947}, {"id": "NG2354", "symbol": "🞰", "unicode_point": "U+1F7B0", "code_point": 128944}, {"id": "NG2356", "symbol": "🟎", "unicode_point": "U+1F7CE", "code_point": 128974}, {"id": "NG2361", "symbol": "🝁", "unicode_point": "U+1F741", "code_point": 128833}, {"id": "NG2362", "symbol": "🝲", "unicode_point": "U+1F772", "code_point": 128882}, {"id": "NG2363", "symbol": "🡇", "unicode_point": "U+1F847", "code_point": 129095}, {"id": "NG2367", "symbol": "🡼", "unicode_point": "U+1F87C", "code_point": 129148}, {"id": "NG2370", "symbol": "🢃", "unicode_point": "U+1F883", "code_point": 129155}, {"id": "NG2373", "symbol": "🞀", "unicode_point": "U+1F780", "code_point": 128896}, {"id": "NG2375", "symbol": "🠂", "unicode_point": "U+1F802", "code_point": 129026}, {"id": "NG2378", "symbol": "🡘", "unicode_point": "U+1F858", "code_point": 129112}, {"id": "NG2380", "symbol": "🜼", "unicode_point": "U+1F73C", "code_point": 128828}, {"id": "NG2382", "symbol": "🢂", "unicode_point": "U+1F882", "code_point": 129154}, {"id": "NG2386", "symbol": "🡈", "unicode_point": "U+1F848", "code_point": 129096}, {"id": "NG2387", "symbol": "🜃", "unicode_point": "U+1F703", "code_point": 128771}, {"id": "NG2388", "symbol": "🝸", "unicode_point": "U+1F778", "code_point": 128888}, {"id": "NG2396", "symbol": "🡌", "unicode_point": "U+1F84C", "code_point": 129100}, {"id": "NG2399", "symbol": "🢧", "unicode_point": "U+1F8A7", "code_point": 129191}, {"id": "NG2401", "symbol": "🞲", "unicode_point": "U+1F7B2", "code_point": 128946}, {"id": "NG2404", "symbol": "🜾", "unicode_point": "U+1F73E", "code_point": 128830}, {"id": "NG2405", "symbol": "🝪", "unicode_point": "U+1F76A", "code_point": 128874}, {"id": "NG2409", "symbol": "🠳", "unicode_point": "U+1F833", "code_point": 129075}, {"id": "NG2418", "symbol": "🜁", "unicode_point": "U+1F701", "code_point": 128769}, {"id": "NG2419", "symbol": "🝢", "unicode_point": "U+1F762", "code_point": 128866}, {"id": "NG2428", "symbol": "🢱", "unicode_point": "U+1F8B1", "code_point": 129201}, {"id": "NG2430", "symbol": "🞿", "unicode_point": "U+1F7BF", "code_point": 128959}, {"id": "NG2433", "symbol": "🜵", "unicode_point": "U+1F735", "code_point": 128821}, {"id": "NG2436", "symbol": "🢍", "unicode_point": "U+1F88D", "code_point": 129165}, {"id": "NG2438", "symbol": "🣆", "unicode_point": "U+1F8C6", "code_point": 129222}, {"id": "NG2439", "symbol": "🜢", "unicode_point": "U+1F722", "code_point": 128802}, {"id": "NG2440", "symbol": "🝐", "unicode_point": "U+1F750", "code_point": 128848}, {"id": "NG2441", "symbol": "🞡", "unicode_point": "U+1F7A1", "code_point": 128929}, {"id": "NG2444", "symbol": "🡩", "unicode_point": "U+1F869", "code_point": 129129}, {"id": "NG2445", "symbol": "🞎", "unicode_point": "U+1F78E", "code_point": 128910}, {"id": "NG2447", "symbol": "🞒", "unicode_point": "U+1F792", "code_point": 128914}, {"id": "NG2451", "symbol": "🝋", "unicode_point": "U+1F74B", "code_point": 128843}, {"id": "NG2455", "symbol": "🜏", "unicode_point": "U+1F70F", "code_point": 128783}, {"id": "NG2456", "symbol": "🝠", "unicode_point": "U+1F760", "code_point": 128864}, {"id": "NG2458", "symbol": "🝎", "unicode_point": "U+1F74E", "code_point": 128846}, {"id": "NG2462", "symbol": "🝼", "unicode_point": "U+1F77C", "code_point": 128892}, {"id": "NG2463", "symbol": "🠭", "unicode_point": "U+1F82D", "code_point": 129069}, {"id": "NG2465", "symbol": "🟱", "unicode_point": "U+1F7F1", "code_point": 129009}, {"id": "NG2467", "symbol": "🟷", "unicode_point": "U+1F7F7", "code_point": 129015}, {"id": "NG2468", "symbol": "🠲", "unicode_point": "U+1F832", "code_point": 129074}, {"id": "NG2469", "symbol": "🟃", "unicode_point": "U+1F7C3", "code_point": 128963}, {"id": "NG2474", "symbol": "🟅", "unicode_point": "U+1F7C5", "code_point": 128965}, {"id": "NG2476", "symbol": "🜲", "unicode_point": "U+1F732", "code_point": 128818}, {"id": "NG2479", "symbol": "🠝", "unicode_point": "U+1F81D", "code_point": 129053}, {"id": "NG2481", "symbol": "🠇", "unicode_point": "U+1F807", "code_point": 129031}, {"id": "NG2482", "symbol": "🜝", "unicode_point": "U+1F71D", "code_point": 128797}, {"id": "NG2484", "symbol": "🜂", "unicode_point": "U+1F702", "code_point": 128770}, {"id": "NG2486", "symbol": "🞴", "unicode_point": "U+1F7B4", "code_point": 128948}, {"id": "NG2488", "symbol": "🜍", "unicode_point": "U+1F70D", "code_point": 128781}, {"id": "NG2489", "symbol": "🞹", "unicode_point": "U+1F7B9", "code_point": 128953}, {"id": "NG2491", "symbol": "🟳", "unicode_point": "U+1F7F3", "code_point": 129011}, {"id": "NG2492", "symbol": "🡔", "unicode_point": "U+1F854", "code_point": 129108}, {"id": "NG2493", "symbol": "🠪", "unicode_point": "U+1F82A", "code_point": 129066}, {"id": "NG2496", "symbol": "🠦", "unicode_point": "U+1F826", "code_point": 129062}, {"id": "NG2497", "symbol": "🠃", "unicode_point": "U+1F803", "code_point": 129027}, {"id": "NG2498", "symbol": "🢎", "unicode_point": "U+1F88E", "code_point": 129166}, {"id": "NG2500", "symbol": "🟮", "unicode_point": "U+1F7EE", "code_point": 129006}, {"id": "NG2501", "symbol": "🟄", "unicode_point": "U+1F7C4", "code_point": 128964}, {"id": "NG2503", "symbol": "🝕", "unicode_point": "U+1F755", "code_point": 128853}, {"id": "NG2505", "symbol": "🝂", "unicode_point": "U+1F742", "code_point": 128834}, {"id": "NG2507", "symbol": "🢘", "unicode_point": "U+1F898", "code_point": 129176}, {"id": "NG2511", "symbol": "🡨", "unicode_point": "U+1F868", "code_point": 129128}, {"id": "NG2513", "symbol": "🞤", "unicode_point": "U+1F7A4", "code_point": 128932}, {"id": "NG2516", "symbol": "🜊", "unicode_point": "U+1F70A", "code_point": 128778}, {"id": "NG2521", "symbol": "🝩", "unicode_point": "U+1F769", "code_point": 128873}, {"id": "NG2524", "symbol": "🠆", "unicode_point": "U+1F806", "code_point": 129030}, {"id": "NG2525", "symbol": "🝘", "unicode_point": "U+1F758", "code_point": 128856}, {"id": "NG2527", "symbol": "🠈", "unicode_point": "U+1F808", "code_point": 129032}, {"id": "NG2529", "symbol": "🟶", "unicode_point": "U+1F7F6", "code_point": 129014}, {"id": "NG2534", "symbol": "🠉", "unicode_point": "U+1F809", "code_point": 129033}, {"id": "NG2536", "symbol": "🞌", "unicode_point": "U+1F78C", "code_point": 128908}, {"id": "NG2537", "symbol": "🟝", "unicode_point": "U+1F7DD", "code_point": 128989}, {"id": "NG2543", "symbol": "🜄", "unicode_point": "U+1F704", "code_point": 128772}, {"id": "NG2544", "symbol": "🞠", "unicode_point": "U+1F7A0", "code_point": 128928}, {"id": "NG2550", "symbol": "🢀", "unicode_point": "U+1F880", "code_point": 129152}, {"id": "NG2551", "symbol": "🞚", "unicode_point": "U+1F79A", "code_point": 128922}, {"id": "NG2553", "symbol": "🜭", "unicode_point": "U+1F72D", "code_point": 128813}, {"id": "NG2554", "symbol": "🞛", "unicode_point": "U+1F79B", "code_point": 128923}, {"id": "NG2559", "symbol": "🠛", "unicode_point": "U+1F81B", "code_point": 129051}, {"id": "NG2560", "symbol": "🞞", "unicode_point": "U+1F79E", "code_point": 128926}, {"id": "NG2561", "symbol": "🡎", "unicode_point": "U+1F84E", "code_point": 129102}, {"id": "NG2567", "symbol": "🞟", "unicode_point": "U+1F79F", "code_point": 128927}, {"id": "NG2568", "symbol": "🠘", "unicode_point": "U+1F818", "code_point": 129048}, {"id": "NG2569", "symbol": "🞨", "unicode_point": "U+1F7A8", "code_point": 128936}, {"id": "NG2571", "symbol": "🝉", "unicode_point": "U+1F749", "code_point": 128841}, {"id": "NG2575", "symbol": "🜘", "unicode_point": "U+1F718", "code_point": 128792}, {"id": "NG2577", "symbol": "🟔", "unicode_point": "U+1F7D4", "code_point": 128980}, {"id": "NG2578", "symbol": "🟰", "unicode_point": "U+1F7F0", "code_point": 129008}, {"id": "NG2581", "symbol": "🡠", "unicode_point": "U+1F860", "code_point": 129120}, {"id": "NG2584", "symbol": "🞧", "unicode_point": "U+1F7A7", "code_point": 128935}, {"id": "NG2586", "symbol": "🢋", "unicode_point": "U+1F88B", "code_point": 129163}, {"id": "NG2588", "symbol": "🜿", "unicode_point": "U+1F73F", "code_point": 128831}, {"id": "NG2591", "symbol": "🝵", "unicode_point": "U+1F775", "code_point": 128885}, {"id": "NG2592", "symbol": "🜎", "unicode_point": "U+1F70E", "code_point": 128782}, {"id": "NG2596", "symbol": "🡴", "unicode_point": "U+1F874", "code_point": 129140}, {"id": "NG2598", "symbol": "🡞", "unicode_point": "U+1F85E", "code_point": 129118}, {"id": "NG2601", "symbol": "🜳", "unicode_point": "U+1F733", "code_point": 128819}, {"id": "NG2606", "symbol": "🡛", "unicode_point": "U+1F85B", "code_point": 129115}, {"id": "NG2608", "symbol": "←", "unicode_point": "U+2190", "code_point": 8592}, {"id": "NG2609", "symbol": "↑", "unicode_point": "U+2191", "code_point": 8593}, {"id": "NG2610", "symbol": "→", "unicode_point": "U+2192", "code_point": 8594}, {"id": "NG2611", "symbol": "↓", "unicode_point": "U+2193", "code_point": 8595}, {"id": "NG2612", "symbol": "↔", "unicode_point": "U+2194", "code_point": 8596}, {"id": "NG2613", "symbol": "↕", "unicode_point": "U+2195", "code_point": 8597}, {"id": "NG2614", "symbol": "↖", "unicode_point": "U+2196", "code_point": 8598}, {"id": "NG2615", "symbol": "↗", "unicode_point": "U+2197", "code_point": 8599}, {"id": "NG2616", "symbol": "↘", "unicode_point": "U+2198", "code_point": 8600}, {"id": "NG2617", "symbol": "↙", "unicode_point": "U+2199", "code_point": 8601}, {"id": "NG2618", "symbol": "↚", "unicode_point": "U+219A", "code_point": 8602}, {"id": "NG2619", "symbol": "↛", "unicode_point": "U+219B", "code_point": 8603}, {"id": "NG2620", "symbol": "↜", "unicode_point": "U+219C", "code_point": 8604}, {"id": "NG2621", "symbol": "↝", "unicode_point": "U+219D", "code_point": 8605}, {"id": "NG2622", "symbol": "↞", "unicode_point": "U+219E", "code_point": 8606}, {"id": "NG2623", "symbol": "↟", "unicode_point": "U+219F", "code_point": 8607}, {"id": "NG2624", "symbol": "↠", "unicode_point": "U+21A0", "code_point": 8608}, {"id": "NG2625", "symbol": "↡", "unicode_point": "U+21A1", "code_point": 8609}, {"id": "NG2626", "symbol": "↢", "unicode_point": "U+21A2", "code_point": 8610}, {"id": "NG2627", "symbol": "↣", "unicode_point": "U+21A3", "code_point": 8611}, {"id": "NG2628", "symbol": "↤", "unicode_point": "U+21A4", "code_point": 8612}, {"id": "NG2629", "symbol": "↥", "unicode_point": "U+21A5", "code_point": 8613}, {"id": "NG2630", "symbol": "↦", "unicode_point": "U+21A6", "code_point": 8614}, {"id": "NG2631", "symbol": "↧", "unicode_point": "U+21A7", "code_point": 8615}, {"id": "NG2632", "symbol": "↨", "unicode_point": "U+21A8", "code_point": 8616}, {"id": "NG2633", "symbol": "↩", "unicode_point": "U+21A9", "code_point": 8617}, {"id": "NG2634", "symbol": "↪", "unicode_point": "U+21AA", "code_point": 8618}, {"id": "NG2635", "symbol": "↫", "unicode_point": "U+21AB", "code_point": 8619}, {"id": "NG2636", "symbol": "↬", "unicode_point": "U+21AC", "code_point": 8620}, {"id": "NG2637", "symbol": "↭", "unicode_point": "U+21AD", "code_point": 8621}, {"id": "NG2638", "symbol": "↮", "unicode_point": "U+21AE", "code_point": 8622}, {"id": "NG2639", "symbol": "↯", "unicode_point": "U+21AF", "code_point": 8623}, {"id": "NG2640", "symbol": "↰", "unicode_point": "U+21B0", "code_point": 8624}, {"id": "NG2641", "symbol": "↱", "unicode_point": "U+21B1", "code_point": 8625}, {"id": "NG2642", "symbol": "↲", "unicode_point": "U+21B2", "code_point": 8626}, {"id": "NG2643", "symbol": "↳", "unicode_point": "U+21B3", "code_point": 8627}, {"id": "NG2644", "symbol": "↴", "unicode_point": "U+21B4", "code_point": 8628}, {"id": "NG2645", "symbol": "↵", "unicode_point": "U+21B5", "code_point": 8629}, {"id": "NG2646", "symbol": "↶", "unicode_point": "U+21B6", "code_point": 8630}, {"id": "NG2647", "symbol": "↷", "unicode_point": "U+21B7", "code_point": 8631}, {"id": "NG2648", "symbol": "↸", "unicode_point": "U+21B8", "code_point": 8632}, {"id": "NG2649", "symbol": "↹", "unicode_point": "U+21B9", "code_point": 8633}, {"id": "NG2650", "symbol": "↺", "unicode_point": "U+21BA", "code_point": 8634}, {"id": "NG2651", "symbol": "↻", "unicode_point": "U+21BB", "code_point": 8635}, {"id": "NG2652", "symbol": "↼", "unicode_point": "U+21BC", "code_point": 8636}, {"id": "NG2653", "symbol": "↽", "unicode_point": "U+21BD", "code_point": 8637}, {"id": "NG2654", "symbol": "↾", "unicode_point": "U+21BE", "code_point": 8638}, {"id": "NG2655", "symbol": "↿", "unicode_point": "U+21BF", "code_point": 8639}, {"id": "NG2656", "symbol": "⇀", "unicode_point": "U+21C0", "code_point": 8640}, {"id": "NG2657", "symbol": "⇁", "unicode_point": "U+21C1", "code_point": 8641}, {"id": "NG2658", "symbol": "⇂", "unicode_point": "U+21C2", "code_point": 8642}, {"id": "NG2659", "symbol": "⇃", "unicode_point": "U+21C3", "code_point": 8643}, {"id": "NG2660", "symbol": "⇄", "unicode_point": "U+21C4", "code_point": 8644}, {"id": "NG2661", "symbol": "⇅", "unicode_point": "U+21C5", "code_point": 8645}, {"id": "NG2662", "symbol": "⇆", "unicode_point": "U+21C6", "code_point": 8646}, {"id": "NG2663", "symbol": "⇇", "unicode_point": "U+21C7", "code_point": 8647}, {"id": "NG2664", "symbol": "⇈", "unicode_point": "U+21C8", "code_point": 8648}, {"id": "NG2665", "symbol": "⇉", "unicode_point": "U+21C9", "code_point": 8649}, {"id": "NG2666", "symbol": "⇊", "unicode_point": "U+21CA", "code_point": 8650}, {"id": "NG2667", "symbol": "⇋", "unicode_point": "U+21CB", "code_point": 8651}, {"id": "NG2668", "symbol": "⇌", "unicode_point": "U+21CC", "code_point": 8652}, {"id": "NG2669", "symbol": "⇍", "unicode_point": "U+21CD", "code_point": 8653}, {"id": "NG2670", "symbol": "⇎", "unicode_point": "U+21CE", "code_point": 8654}, {"id": "NG2671", "symbol": "⇏", "unicode_point": "U+21CF", "code_point": 8655}, {"id": "NG2672", "symbol": "⇐", "unicode_point": "U+21D0", "code_point": 8656}, {"id": "NG2673", "symbol": "⇑", "unicode_point": "U+21D1", "code_point": 8657}, {"id": "NG2674", "symbol": "⇒", "unicode_point": "U+21D2", "code_point": 8658}, {"id": "NG2675", "symbol": "⇓", "unicode_point": "U+21D3", "code_point": 8659}, {"id": "NG2676", "symbol": "⇔", "unicode_point": "U+21D4", "code_point": 8660}, {"id": "NG2677", "symbol": "⇕", "unicode_point": "U+21D5", "code_point": 8661}, {"id": "NG2678", "symbol": "⇖", "unicode_point": "U+21D6", "code_point": 8662}, {"id": "NG2679", "symbol": "⇗", "unicode_point": "U+21D7", "code_point": 8663}, {"id": "NG2680", "symbol": "⇘", "unicode_point": "U+21D8", "code_point": 8664}, {"id": "NG2681", "symbol": "⇙", "unicode_point": "U+21D9", "code_point": 8665}, {"id": "NG2682", "symbol": "⇚", "unicode_point": "U+21DA", "code_point": 8666}, {"id": "NG2683", "symbol": "⇛", "unicode_point": "U+21DB", "code_point": 8667}, {"id": "NG2684", "symbol": "⇜", "unicode_point": "U+21DC", "code_point": 8668}, {"id": "NG2685", "symbol": "⇝", "unicode_point": "U+21DD", "code_point": 8669}, {"id": "NG2686", "symbol": "⇞", "unicode_point": "U+21DE", "code_point": 8670}, {"id": "NG2687", "symbol": "⇟", "unicode_point": "U+21DF", "code_point": 8671}, {"id": "NG2688", "symbol": "⇠", "unicode_point": "U+21E0", "code_point": 8672}, {"id": "NG2689", "symbol": "⇡", "unicode_point": "U+21E1", "code_point": 8673}, {"id": "NG2690", "symbol": "⇢", "unicode_point": "U+21E2", "code_point": 8674}, {"id": "NG2691", "symbol": "⇣", "unicode_point": "U+21E3", "code_point": 8675}, {"id": "NG2692", "symbol": "⇤", "unicode_point": "U+21E4", "code_point": 8676}, {"id": "NG2693", "symbol": "⇥", "unicode_point": "U+21E5", "code_point": 8677}, {"id": "NG2694", "symbol": "⇦", "unicode_point": "U+21E6", "code_point": 8678}, {"id": "NG2695", "symbol": "⇧", "unicode_point": "U+21E7", "code_point": 8679}, {"id": "NG2696", "symbol": "⇨", "unicode_point": "U+21E8", "code_point": 8680}, {"id": "NG2697", "symbol": "⇩", "unicode_point": "U+21E9", "code_point": 8681}, {"id": "NG2698", "symbol": "⇪", "unicode_point": "U+21EA", "code_point": 8682}, {"id": "NG2699", "symbol": "⇫", "unicode_point": "U+21EB", "code_point": 8683}, {"id": "NG2700", "symbol": "⇬", "unicode_point": "U+21EC", "code_point": 8684}, {"id": "NG2701", "symbol": "⇭", "unicode_point": "U+21ED", "code_point": 8685}, {"id": "NG2702", "symbol": "⇮", "unicode_point": "U+21EE", "code_point": 8686}, {"id": "NG2703", "symbol": "⇯", "unicode_point": "U+21EF", "code_point": 8687}, {"id": "NG2704", "symbol": "⇰", "unicode_point": "U+21F0", "code_point": 8688}, {"id": "NG2705", "symbol": "⇱", "unicode_point": "U+21F1", "code_point": 8689}, {"id": "NG2706", "symbol": "⇲", "unicode_point": "U+21F2", "code_point": 8690}, {"id": "NG2707", "symbol": "⇳", "unicode_point": "U+21F3", "code_point": 8691}, {"id": "NG2708", "symbol": "⇴", "unicode_point": "U+21F4", "code_point": 8692}, {"id": "NG2709", "symbol": "⇵", "unicode_point": "U+21F5", "code_point": 8693}, {"id": "NG2710", "symbol": "⇶", "unicode_point": "U+21F6", "code_point": 8694}, {"id": "NG2711", "symbol": "⇷", "unicode_point": "U+21F7", "code_point": 8695}, {"id": "NG2712", "symbol": "⇸", "unicode_point": "U+21F8", "code_point": 8696}, {"id": "NG2713", "symbol": "⇹", "unicode_point": "U+21F9", "code_point": 8697}, {"id": "NG2714", "symbol": "⇺", "unicode_point": "U+21FA", "code_point": 8698}, {"id": "NG2715", "symbol": "⇻", "unicode_point": "U+21FB", "code_point": 8699}, {"id": "NG2716", "symbol": "⇼", "unicode_point": "U+21FC", "code_point": 8700}, {"id": "NG2717", "symbol": "⇽", "unicode_point": "U+21FD", "code_point": 8701}, {"id": "NG2718", "symbol": "⇾", "unicode_point": "U+21FE", "code_point": 8702}, {"id": "NG2719", "symbol": "⇿", "unicode_point": "U+21FF", "code_point": 8703}, {"id": "NG2720", "symbol": "⅀", "unicode_point": "U+2140", "code_point": 8512}, {"id": "NG2721", "symbol": "℀", "unicode_point": "U+2100", "code_point": 8448}, {"id": "NG2722", "symbol": "℁", "unicode_point": "U+2101", "code_point": 8449}, {"id": "NG2723", "symbol": "ℂ", "unicode_point": "U+2102", "code_point": 8450}, {"id": "NG2724", "symbol": "℃", "unicode_point": "U+2103", "code_point": 8451}, {"id": "NG2725", "symbol": "℄", "unicode_point": "U+2104", "code_point": 8452}, {"id": "NG2726", "symbol": "℅", "unicode_point": "U+2105", "code_point": 8453}, {"id": "NG2727", "symbol": "℆", "unicode_point": "U+2106", "code_point": 8454}, {"id": "NG2728", "symbol": "ℇ", "unicode_point": "U+2107", "code_point": 8455}, {"id": "NG2729", "symbol": "℈", "unicode_point": "U+2108", "code_point": 8456}, {"id": "NG2730", "symbol": "℉", "unicode_point": "U+2109", "code_point": 8457}, {"id": "NG2731", "symbol": "ℊ", "unicode_point": "U+210A", "code_point": 8458}, {"id": "NG2732", "symbol": "ℋ", "unicode_point": "U+210B", "code_point": 8459}, {"id": "NG2733", "symbol": "ℌ", "unicode_point": "U+210C", "code_point": 8460}, {"id": "NG2734", "symbol": "ℍ", "unicode_point": "U+210D", "code_point": 8461}, {"id": "NG2735", "symbol": "ℎ", "unicode_point": "U+210E", "code_point": 8462}, {"id": "NG2736", "symbol": "ℏ", "unicode_point": "U+210F", "code_point": 8463}, {"id": "NG2737", "symbol": "ℐ", "unicode_point": "U+2110", "code_point": 8464}, {"id": "NG2738", "symbol": "ℑ", "unicode_point": "U+2111", "code_point": 8465}, {"id": "NG2739", "symbol": "ℒ", "unicode_point": "U+2112", "code_point": 8466}, {"id": "NG2740", "symbol": "ℓ", "unicode_point": "U+2113", "code_point": 8467}, {"id": "NG2741", "symbol": "℔", "unicode_point": "U+2114", "code_point": 8468}, {"id": "NG2742", "symbol": "ℕ", "unicode_point": "U+2115", "code_point": 8469}, {"id": "NG2743", "symbol": "№", "unicode_point": "U+2116", "code_point": 8470}, {"id": "NG2744", "symbol": "℗", "unicode_point": "U+2117", "code_point": 8471}, {"id": "NG2745", "symbol": "℘", "unicode_point": "U+2118", "code_point": 8472}, {"id": "NG2746", "symbol": "ℙ", "unicode_point": "U+2119", "code_point": 8473}, {"id": "NG2747", "symbol": "ℚ", "unicode_point": "U+211A", "code_point": 8474}, {"id": "NG2748", "symbol": "ℛ", "unicode_point": "U+211B", "code_point": 8475}, {"id": "NG2749", "symbol": "ℜ", "unicode_point": "U+211C", "code_point": 8476}, {"id": "NG2750", "symbol": "ℝ", "unicode_point": "U+211D", "code_point": 8477}, {"id": "NG2751", "symbol": "℞", "unicode_point": "U+211E", "code_point": 8478}, {"id": "NG2752", "symbol": "℟", "unicode_point": "U+211F", "code_point": 8479}, {"id": "NG2753", "symbol": "℠", "unicode_point": "U+2120", "code_point": 8480}, {"id": "NG2754", "symbol": "℡", "unicode_point": "U+2121", "code_point": 8481}, {"id": "NG2755", "symbol": "™", "unicode_point": "U+2122", "code_point": 8482}, {"id": "NG2756", "symbol": "℣", "unicode_point": "U+2123", "code_point": 8483}, {"id": "NG2757", "symbol": "ℤ", "unicode_point": "U+2124", "code_point": 8484}, {"id": "NG2758", "symbol": "℥", "unicode_point": "U+2125", "code_point": 8485}, {"id": "NG2759", "symbol": "Ω", "unicode_point": "U+2126", "code_point": 8486}, {"id": "NG2760", "symbol": "℧", "unicode_point": "U+2127", "code_point": 8487}, {"id": "NG2761", "symbol": "ℨ", "unicode_point": "U+2128", "code_point": 8488}, {"id": "NG2762", "symbol": "℩", "unicode_point": "U+2129", "code_point": 8489}, {"id": "NG2763", "symbol": "K", "unicode_point": "U+212A", "code_point": 8490}, {"id": "NG2764", "symbol": "Å", "unicode_point": "U+212B", "code_point": 8491}, {"id": "NG2765", "symbol": "ℬ", "unicode_point": "U+212C", "code_point": 8492}, {"id": "NG2766", "symbol": "ℭ", "unicode_point": "U+212D", "code_point": 8493}, {"id": "NG2767", "symbol": "℮", "unicode_point": "U+212E", "code_point": 8494}, {"id": "NG2768", "symbol": "ℯ", "unicode_point": "U+212F", "code_point": 8495}, {"id": "NG2769", "symbol": "ℰ", "unicode_point": "U+2130", "code_point": 8496}, {"id": "NG2770", "symbol": "ℱ", "unicode_point": "U+2131", "code_point": 8497}, {"id": "NG2771", "symbol": "Ⅎ", "unicode_point": "U+2132", "code_point": 8498}, {"id": "NG2772", "symbol": "ℳ", "unicode_point": "U+2133", "code_point": 8499}, {"id": "NG2773", "symbol": "ℴ", "unicode_point": "U+2134", "code_point": 8500}, {"id": "NG2774", "symbol": "ℵ", "unicode_point": "U+2135", "code_point": 8501}, {"id": "NG2775", "symbol": "ℶ", "unicode_point": "U+2136", "code_point": 8502}, {"id": "NG2776", "symbol": "ℷ", "unicode_point": "U+2137", "code_point": 8503}, {"id": "NG2777", "symbol": "ℸ", "unicode_point": "U+2138", "code_point": 8504}, {"id": "NG2778", "symbol": "ℹ", "unicode_point": "U+2139", "code_point": 8505}, {"id": "NG2779", "symbol": "℺", "unicode_point": "U+213A", "code_point": 8506}, {"id": "NG2780", "symbol": "℻", "unicode_point": "U+213B", "code_point": 8507}, {"id": "NG2781", "symbol": "ℼ", "unicode_point": "U+213C", "code_point": 8508}, {"id": "NG2782", "symbol": "ℽ", "unicode_point": "U+213D", "code_point": 8509}, {"id": "NG2783", "symbol": "ℾ", "unicode_point": "U+213E", "code_point": 8510}, {"id": "NG2784", "symbol": "ℿ", "unicode_point": "U+213F", "code_point": 8511}, {"id": "NG2785", "symbol": "⅁", "unicode_point": "U+2141", "code_point": 8513}, {"id": "NG2786", "symbol": "⅂", "unicode_point": "U+2142", "code_point": 8514}, {"id": "NG2787", "symbol": "⅃", "unicode_point": "U+2143", "code_point": 8515}, {"id": "NG2788", "symbol": "⅄", "unicode_point": "U+2144", "code_point": 8516}, {"id": "NG2789", "symbol": "ⅅ", "unicode_point": "U+2145", "code_point": 8517}, {"id": "NG2790", "symbol": "ⅆ", "unicode_point": "U+2146", "code_point": 8518}, {"id": "NG2791", "symbol": "ⅇ", "unicode_point": "U+2147", "code_point": 8519}, {"id": "NG2792", "symbol": "ⅈ", "unicode_point": "U+2148", "code_point": 8520}, {"id": "NG2793", "symbol": "ⅉ", "unicode_point": "U+2149", "code_point": 8521}, {"id": "NG2794", "symbol": "⅊", "unicode_point": "U+214A", "code_point": 8522}, {"id": "NG2795", "symbol": "⅋", "unicode_point": "U+214B", "code_point": 8523}, {"id": "NG2796", "symbol": "⅌", "unicode_point": "U+214C", "code_point": 8524}, {"id": "NG2797", "symbol": "⅍", "unicode_point": "U+214D", "code_point": 8525}, {"id": "NG2798", "symbol": "ⅎ", "unicode_point": "U+214E", "code_point": 8526}, {"id": "NG2799", "symbol": "⅏", "unicode_point": "U+214F", "code_point": 8527}, {"id": "NG2800", "symbol": "Ꜳ", "unicode_point": "U+A732", "code_point": 42802}, {"id": "NG2801", "symbol": "𝛺", "unicode_point": "U+1D6FA", "code_point": 120570}, {"id": "NG2802", "symbol": "𝛂", "unicode_point": "U+1D6C2", "code_point": 120514}, {"id": "NG2803", "symbol": "𝕒", "unicode_point": "U+1D552", "code_point": 120146}, {"id": "NG2804", "symbol": "ꭞ", "unicode_point": "U+AB5E", "code_point": 43870}, {"id": "NG2805", "symbol": "𝐺", "unicode_point": "U+1D43A", "code_point": 119866}, {"id": "NG2806", "symbol": "ⅼ", "unicode_point": "U+217C", "code_point": 8572}, {"id": "NG2807", "symbol": "𝜁", "unicode_point": "U+1D701", "code_point": 120577}, {"id": "NG2808", "symbol": "𝒒", "unicode_point": "U+1D492", "code_point": 119954}, {"id": "NG2809", "symbol": "𝟈", "unicode_point": "U+1D7C8", "code_point": 120776}, {"id": "NG2810", "symbol": "𝜬", "unicode_point": "U+1D72C", "code_point": 120620}, {"id": "NG2811", "symbol": "𝗗", "unicode_point": "U+1D5D7", "code_point": 120279}, {"id": "NG2812", "symbol": "⣀", "unicode_point": "U+28C0", "code_point": 10432}, {"id": "NG2813", "symbol": "⸩", "unicode_point": "U+2E29", "code_point": 11817}, {"id": "NG2814", "symbol": "⠳", "unicode_point": "U+2833", "code_point": 10291}, {"id": "NG2815", "symbol": "ꟳ", "unicode_point": "U+A7F3", "code_point": 42995}, {"id": "NG2816", "symbol": "𝞤", "unicode_point": "U+1D7A4", "code_point": 120740}, {"id": "NG2817", "symbol": "𝚎", "unicode_point": "U+1D68E", "code_point": 120462}, {"id": "NG2818", "symbol": "ꬾ", "unicode_point": "U+AB3E", "code_point": 43838}, {"id": "NG2819", "symbol": "ⅽ", "unicode_point": "U+217D", "code_point": 8573}, {"id": "NG2820", "symbol": "𝟆", "unicode_point": "U+1D7C6", "code_point": 120774}, {"id": "NG2821", "symbol": "⡩", "unicode_point": "U+2869", "code_point": 10345}, {"id": "NG2822", "symbol": "𝑎", "unicode_point": "U+1D44E", "code_point": 119886}, {"id": "NG2823", "symbol": "Ⅰ", "unicode_point": "U+2160", "code_point": 8544}, {"id": "NG2824", "symbol": "⢅", "unicode_point": "U+2885", "code_point": 10373}, {"id": "NG2825", "symbol": "𝗰", "unicode_point": "U+1D5F0", "code_point": 120304}, {"id": "NG2826", "symbol": "𝙺", "unicode_point": "U+1D67A", "code_point": 120442}, {"id": "NG2827", "symbol": "ꭖ", "unicode_point": "U+AB56", "code_point": 43862}, {"id": "NG2828", "symbol": "⢉", "unicode_point": "U+2889", "code_point": 10377}, {"id": "NG2829", "symbol": "𝘆", "unicode_point": "U+1D606", "code_point": 120326}, {"id": "NG2830", "symbol": "𝝘", "unicode_point": "U+1D758", "code_point": 120664}, {"id": "NG2831", "symbol": "𝞽", "unicode_point": "U+1D7BD", "code_point": 120765}, {"id": "NG2832", "symbol": "⡨", "unicode_point": "U+2868", "code_point": 10344}, {"id": "NG2833", "symbol": "Ꞔ", "unicode_point": "U+A7C4", "code_point": 42948}, {"id": "NG2834", "symbol": "𝕚", "unicode_point": "U+1D55A", "code_point": 120154}, {"id": "NG2835", "symbol": "𝘓", "unicode_point": "U+1D613", "code_point": 120339}, {"id": "NG2836", "symbol": "⟷", "unicode_point": "U+27F7", "code_point": 10231}, {"id": "NG2837", "symbol": "𝛘", "unicode_point": "U+1D6D8", "code_point": 120536}, {"id": "NG2838", "symbol": "𝚢", "unicode_point": "U+1D6A2", "code_point": 120482}, {"id": "NG2839", "symbol": "𝘉", "unicode_point": "U+1D609", "code_point": 120329}, {"id": "NG2840", "symbol": "𝟫", "unicode_point": "U+1D7EB", "code_point": 120811}, {"id": "NG2841", "symbol": "𝕃", "unicode_point": "U+1D543", "code_point": 120131}, {"id": "NG2842", "symbol": "𝚳", "unicode_point": "U+1D6B3", "code_point": 120499}, {"id": "NG2843", "symbol": "𝛽", "unicode_point": "U+1D6FD", "code_point": 120573}, {"id": "NG2844", "symbol": "𝐡", "unicode_point": "U+1D421", "code_point": 119841}, {"id": "NG2845", "symbol": "ꜥ", "unicode_point": "U+A725", "code_point": 42789}, {"id": "NG2846", "symbol": "ⷸ", "unicode_point": "U+2DF8", "code_point": 11768}, {"id": "NG2847", "symbol": "⟖", "unicode_point": "U+27D6", "code_point": 10198}, {"id": "NG2848", "symbol": "𝓔", "unicode_point": "U+1D4D4", "code_point": 120020}, {"id": "NG2849", "symbol": "ꭐ", "unicode_point": "U+AB50", "code_point": 43856}, {"id": "NG2850", "symbol": "𝒪", "unicode_point": "U+1D4AA", "code_point": 119978}, {"id": "NG2851", "symbol": "𝗀", "unicode_point": "U+1D5C0", "code_point": 120256}, {"id": "NG2852", "symbol": "𝝭", "unicode_point": "U+1D76D", "code_point": 120685}, {"id": "NG2853", "symbol": "𝒍", "unicode_point": "U+1D48D", "code_point": 119949}, {"id": "NG2854", "symbol": "𝑜", "unicode_point": "U+1D45C", "code_point": 119900}, {"id": "NG2855", "symbol": "⢟", "unicode_point": "U+289F", "code_point": 10399}, {"id": "NG2856", "symbol": "𝒴", "unicode_point": "U+1D4B4", "code_point": 119988}, {"id": "NG2857", "symbol": "⸬", "unicode_point": "U+2E2C", "code_point": 11820}, {"id": "NG2858", "symbol": "𝑷", "unicode_point": "U+1D477", "code_point": 119927}, {"id": "NG2859", "symbol": "𝔅", "unicode_point": "U+1D505", "code_point": 120069}, {"id": "NG2860", "symbol": "⣸", "unicode_point": "U+28F8", "code_point": 10488}, {"id": "NG2861", "symbol": "𝒞", "unicode_point": "U+1D49E", "code_point": 119966}, {"id": "NG2862", "symbol": "ꟃ", "unicode_point": "U+A7C3", "code_point": 42947}, {"id": "NG2863", "symbol": "⣲", "unicode_point": "U+28F2", "code_point": 10482}, {"id": "NG2864", "symbol": "ⷩ", "unicode_point": "U+2DE9", "code_point": 11753}, {"id": "NG2865", "symbol": "𝕫", "unicode_point": "U+1D56B", "code_point": 120171}, {"id": "NG2866", "symbol": "𝖦", "unicode_point": "U+1D5A6", "code_point": 120230}, {"id": "NG2867", "symbol": "𝔙", "unicode_point": "U+1D519", "code_point": 120089}, {"id": "NG2868", "symbol": "↋", "unicode_point": "U+218B", "code_point": 8587}, {"id": "NG2869", "symbol": "𝘥", "unicode_point": "U+1D625", "code_point": 120357}, {"id": "NG2870", "symbol": "ⷡ", "unicode_point": "U+2DE1", "code_point": 11745}, {"id": "NG2871", "symbol": "⠨", "unicode_point": "U+2828", "code_point": 10280}, {"id": "NG2872", "symbol": "Ɡ", "unicode_point": "U+A7AC", "code_point": 42924}, {"id": "NG2873", "symbol": "ꞏ", "unicode_point": "U+A78F", "code_point": 42895}, {"id": "NG2874", "symbol": "⸨", "unicode_point": "U+2E28", "code_point": 11816}, {"id": "NG2875", "symbol": "𝟷", "unicode_point": "U+1D7F7", "code_point": 120823}, {"id": "NG2876", "symbol": "𝗙", "unicode_point": "U+1D5D9", "code_point": 120281}, {"id": "NG2877", "symbol": "𝔪", "unicode_point": "U+1D52A", "code_point": 120106}, {"id": "NG2878", "symbol": "𝝟", "unicode_point": "U+1D75F", "code_point": 120671}, {"id": "NG2879", "symbol": "ⱻ", "unicode_point": "U+2C7B", "code_point": 11387}, {"id": "NG2880", "symbol": "𝟜", "unicode_point": "U+1D7DC", "code_point": 120796}, {"id": "NG2881", "symbol": "⢮", "unicode_point": "U+28AE", "code_point": 10414}, {"id": "NG2882", "symbol": "⠃", "unicode_point": "U+2803", "code_point": 10243}, {"id": "NG2883", "symbol": "𝑍", "unicode_point": "U+1D44D", "code_point": 119885}, {"id": "NG2884", "symbol": "ꝙ", "unicode_point": "U+A759", "code_point": 42841}, {"id": "NG2885", "symbol": "𝙯", "unicode_point": "U+1D66F", "code_point": 120431}, {"id": "NG2886", "symbol": "Ɫ", "unicode_point": "U+2C62", "code_point": 11362}, {"id": "NG2887", "symbol": "ꝿ", "unicode_point": "U+A77F", "code_point": 42879}, {"id": "NG2888", "symbol": "⸊", "unicode_point": "U+2E0A", "code_point": 11786}, {"id": "NG2889", "symbol": "⡕", "unicode_point": "U+2855", "code_point": 10325}, {"id": "NG2890", "symbol": "𝜱", "unicode_point": "U+1D731", "code_point": 120625}, {"id": "NG2891", "symbol": "𝑶", "unicode_point": "U+1D476", "code_point": 119926}, {"id": "NG2892", "symbol": "𝗇", "unicode_point": "U+1D5C7", "code_point": 120263}, {"id": "NG2893", "symbol": "𝟬", "unicode_point": "U+1D7EC", "code_point": 120812}, {"id": "NG2894", "symbol": "𝔻", "unicode_point": "U+1D53B", "code_point": 120123}, {"id": "NG2895", "symbol": "⸪", "unicode_point": "U+2E2A", "code_point": 11818}, {"id": "NG2896", "symbol": "𝞝", "unicode_point": "U+1D79D", "code_point": 120733}, {"id": "NG2897", "symbol": "𝒀", "unicode_point": "U+1D480", "code_point": 119936}, {"id": "NG2898", "symbol": "⣔", "unicode_point": "U+28D4", "code_point": 10452}, {"id": "NG2899", "symbol": "𝕱", "unicode_point": "U+1D571", "code_point": 120177}, {"id": "NG2900", "symbol": "𝖸", "unicode_point": "U+1D5B8", "code_point": 120248}, {"id": "NG2901", "symbol": "Ꝗ", "unicode_point": "U+A756", "code_point": 42838}, {"id": "NG2902", "symbol": "𝘶", "unicode_point": "U+1D636", "code_point": 120374}, {"id": "NG2903", "symbol": "𝐋", "unicode_point": "U+1D40B", "code_point": 119819}, {"id": "NG2904", "symbol": "⹁", "unicode_point": "U+2E41", "code_point": 11841}, {"id": "NG2905", "symbol": "𝓏", "unicode_point": "U+1D4CF", "code_point": 120015}, {"id": "NG2906", "symbol": "𝒹", "unicode_point": "U+1D4B9", "code_point": 119993}, {"id": "NG2907", "symbol": "⟢", "unicode_point": "U+27E2", "code_point": 10210}, {"id": "NG2908", "symbol": "𝓝", "unicode_point": "U+1D4DD", "code_point": 120029}, {"id": "NG2909", "symbol": "Ⅳ", "unicode_point": "U+2163", "code_point": 8547}, {"id": "NG2910", "symbol": "⡀", "unicode_point": "U+2840", "code_point": 10304}, {"id": "NG2911", "symbol": "𝞗", "unicode_point": "U+1D797", "code_point": 120727}, {"id": "NG2912", "symbol": "𝞼", "unicode_point": "U+1D7BC", "code_point": 120764}, {"id": "NG2913", "symbol": "𝑨", "unicode_point": "U+1D468", "code_point": 119912}, {"id": "NG2914", "symbol": "𝓹", "unicode_point": "U+1D4F9", "code_point": 120057}, {"id": "NG2915", "symbol": "⠐", "unicode_point": "U+2810", "code_point": 10256}, {"id": "NG2916", "symbol": "𝞚", "unicode_point": "U+1D79A", "code_point": 120730}, {"id": "NG2917", "symbol": "ⷲ", "unicode_point": "U+2DF2", "code_point": 11762}, {"id": "NG2918", "symbol": "⢈", "unicode_point": "U+2888", "code_point": 10376}, {"id": "NG2919", "symbol": "𝟏", "unicode_point": "U+1D7CF", "code_point": 120783}, {"id": "NG2920", "symbol": "ꬵ", "unicode_point": "U+AB35", "code_point": 43829}, {"id": "NG2921", "symbol": "ⱴ", "unicode_point": "U+2C74", "code_point": 11380}, {"id": "NG2922", "symbol": "⡃", "unicode_point": "U+2843", "code_point": 10307}, {"id": "NG2923", "symbol": "𝞂", "unicode_point": "U+1D782", "code_point": 120706}, {"id": "NG2924", "symbol": "⡮", "unicode_point": "U+286E", "code_point": 10350}, {"id": "NG2925", "symbol": "𝝚", "unicode_point": "U+1D75A", "code_point": 120666}, {"id": "NG2926", "symbol": "𝚶", "unicode_point": "U+1D6B6", "code_point": 120502}, {"id": "NG2927", "symbol": "𝒊", "unicode_point": "U+1D48A", "code_point": 119946}]}, "domain_results": {"total_symbols": 2048, "domain_counts": {"operator": 95, "memory": 94, "logic": 111, "structure": 89, "flow": 102, "reasoning": 55, "advanced_coding": 303, "meta_programming": 62, "distributed_systems": 92, "quantum_computing": 18, "symbolic_ai": 31, "neural_architectures": 64, "formal_verification": 35, "category_theory": 35, "type_theory": 19, "concurrency_advanced": 56, "machine_learning": 89, "mathematical_structures": 46, "philosophical_concepts": 46, "cognitive_modeling": 33, "reserved_expansion": 58, "extension": 194, "mathematical_operators": 11, "geometric_shapes": 11, "arrows": 11, "technical_symbols": 11, "letterlike_symbols": 11, "number_forms": 11, "supplemental_math": 11, "misc_technical": 11, "logical_operators": 11, "set_theory": 11, "calculus": 11, "algebra": 11, "topology": 11, "analysis": 10, "geometry": 10, "combinatorics": 10, "statistics": 10, "physics_notation": 10, "meta": 32, "agents": 24, "architecture": 20, "cognition": 16, "self": 20, "temporal": 16}, "category_counts": {"operator": 95, "memory": 94, "logic": 111, "structure": 89, "flow": 102, "reasoning": 55, "advanced_coding": 303, "meta_programming": 62, "distributed_systems": 92, "quantum_computing": 18, "symbolic_ai": 31, "neural_architectures": 64, "formal_verification": 35, "category_theory": 35, "type_theory": 19, "concurrency_advanced": 56, "machine_learning": 89, "mathematical_structures": 46, "philosophical_concepts": 46, "cognitive_modeling": 33, "reserved_expansion": 58, "extension": 194, "mathematical_operators": 11, "geometric_shapes": 11, "arrows": 11, "technical_symbols": 11, "letterlike_symbols": 11, "number_forms": 11, "supplemental_math": 11, "misc_technical": 11, "logical_operators": 11, "set_theory": 11, "calculus": 11, "algebra": 11, "topology": 11, "analysis": 10, "geometry": 10, "combinatorics": 10, "statistics": 10, "physics_notation": 10, "meta": 32, "agents": 24, "architecture": 20, "cognition": 16, "self": 20, "temporal": 16}, "tier_distribution": {"god": 2048}, "generator_distribution": {"simple": 491, "reasoning_specialized": 55, "god_tier_v1": 936, "ultra_pipeline": 51, "complete_to_2048": 194, "whitelist_pipeline": 193, "god_mode_2048_pipeline": 128}}, "quality_results": {"total_symbols": 2048, "score_stats": {"min": 95.0, "max": 100.0, "avg": 97.216796875, "below_95": 0}, "token_cost_stats": {"min": 1, "max": 1, "avg": 1.0, "above_2": 0}, "token_density_stats": {"min": 0.9, "max": 1.0, "avg": 0.9514453125, "below_09": 0}}}