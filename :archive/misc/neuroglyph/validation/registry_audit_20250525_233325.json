{"audit_summary": {"audit_timestamp": "20250525_233325", "registry_version": "6.0.0", "registry_status": "WHITELIST_ENHANCED_1920+0", "total_symbols": 1920, "audit_scores": {"duplicate_check": 0, "integrity_check": 100.0, "unicode_safety": 80.9, "quality_metrics": 97.2, "overall_score": 69.5}, "critical_issues": {"has_duplicates": true, "integrity_issues": 0, "unsafe_unicode": 367, "low_quality_symbols": 0}, "registry_health": {"excellent": false, "good": false, "acceptable": false, "needs_improvement": true}, "recommendations": ["CRITICO: Rimuovere duplicati dal registry", "Sostituire Unicode non sicuri con range approvati"]}, "duplicate_results": {"total_symbols": 1920, "duplicate_ids": [], "duplicate_unicode": [], "duplicate_symbols": [], "duplicate_codes": [], "duplicate_names": [{"name": "downwards_arrow_with_tip_", "count": 2}, {"name": "leftwards_harpoon_with_ba", "count": 2}, {"name": "upwards_harpoon_with_barb", "count": 2}, {"name": "rightwards_harpoon_with_b", "count": 2}, {"name": "downwards_harpoon_with_ba", "count": 2}, {"name": "upwards_white_arrow_on_pe", "count": 3}, {"name": "upwards_white_double_arro", "count": 2}, {"name": "turned_sans_serif_capital", "count": 3}, {"name": "double_struck_italic_smal", "count": 4}], "has_duplicates": true}, "integrity_results": {"total_symbols": 1920, "missing_fields": [], "invalid_formats": [], "quality_issues": [], "integrity_score": 100.0}, "unicode_results": {"total_symbols": 1920, "safe_unicode": 1553, "unsafe_unicode": 367, "range_distribution": {"Geometric Shapes": 96, "Supplemental Mathematical Operators": 255, "Mathematical Operators": 256, "Miscellaneous Technical": 256, "Supplemental Arrows-B": 128, "Miscellaneous Mathematical Symbols-B": 128, "Dingbats": 192, "Miscellaneous Symbols and Arrows": 242}, "unsafe_symbols": [{"id": "NG2124", "symbol": "🟥", "unicode_point": "U+1F7E5", "code_point": 128997}, {"id": "NG2127", "symbol": "🝻", "unicode_point": "U+1F77B", "code_point": 128891}, {"id": "NG2128", "symbol": "🝷", "unicode_point": "U+1F777", "code_point": 128887}, {"id": "NG2133", "symbol": "🜐", "unicode_point": "U+1F710", "code_point": 128784}, {"id": "NG2136", "symbol": "🢐", "unicode_point": "U+1F890", "code_point": 129168}, {"id": "NG2137", "symbol": "🜨", "unicode_point": "U+1F728", "code_point": 128808}, {"id": "NG2142", "symbol": "🜔", "unicode_point": "U+1F714", "code_point": 128788}, {"id": "NG2147", "symbol": "🟈", "unicode_point": "U+1F7C8", "code_point": 128968}, {"id": "NG2151", "symbol": "🣂", "unicode_point": "U+1F8C2", "code_point": 129218}, {"id": "NG2154", "symbol": "🠷", "unicode_point": "U+1F837", "code_point": 129079}, {"id": "NG2157", "symbol": "🞱", "unicode_point": "U+1F7B1", "code_point": 128945}, {"id": "NG2159", "symbol": "🢳", "unicode_point": "U+1F8B3", "code_point": 129203}, {"id": "NG2161", "symbol": "🢁", "unicode_point": "U+1F881", "code_point": 129153}, {"id": "NG2162", "symbol": "🠚", "unicode_point": "U+1F81A", "code_point": 129050}, {"id": "NG2165", "symbol": "🣃", "unicode_point": "U+1F8C3", "code_point": 129219}, {"id": "NG2168", "symbol": "🜛", "unicode_point": "U+1F71B", "code_point": 128795}, {"id": "NG2175", "symbol": "🢺", "unicode_point": "U+1F8BA", "code_point": 129210}, {"id": "NG2177", "symbol": "🜈", "unicode_point": "U+1F708", "code_point": 128776}, {"id": "NG2182", "symbol": "🞥", "unicode_point": "U+1F7A5", "code_point": 128933}, {"id": "NG2183", "symbol": "🟹", "unicode_point": "U+1F7F9", "code_point": 129017}, {"id": "NG2184", "symbol": "🝅", "unicode_point": "U+1F745", "code_point": 128837}, {"id": "NG2185", "symbol": "🟻", "unicode_point": "U+1F7FB", "code_point": 129019}, {"id": "NG2187", "symbol": "🟌", "unicode_point": "U+1F7CC", "code_point": 128972}, {"id": "NG2190", "symbol": "🞂", "unicode_point": "U+1F782", "code_point": 128898}, {"id": "NG2194", "symbol": "🝏", "unicode_point": "U+1F74F", "code_point": 128847}, {"id": "NG2201", "symbol": "🞾", "unicode_point": "U+1F7BE", "code_point": 128958}, {"id": "NG2207", "symbol": "🠢", "unicode_point": "U+1F822", "code_point": 129058}, {"id": "NG2211", "symbol": "🟢", "unicode_point": "U+1F7E2", "code_point": 128994}, {"id": "NG2214", "symbol": "🜱", "unicode_point": "U+1F731", "code_point": 128817}, {"id": "NG2222", "symbol": "🡅", "unicode_point": "U+1F845", "code_point": 129093}, {"id": "NG2224", "symbol": "🟒", "unicode_point": "U+1F7D2", "code_point": 128978}, {"id": "NG2229", "symbol": "🝭", "unicode_point": "U+1F76D", "code_point": 128877}, {"id": "NG2230", "symbol": "🝙", "unicode_point": "U+1F759", "code_point": 128857}, {"id": "NG2231", "symbol": "🞗", "unicode_point": "U+1F797", "code_point": 128919}, {"id": "NG2233", "symbol": "🟯", "unicode_point": "U+1F7EF", "code_point": 129007}, {"id": "NG2234", "symbol": "🜉", "unicode_point": "U+1F709", "code_point": 128777}, {"id": "NG2235", "symbol": "🞬", "unicode_point": "U+1F7AC", "code_point": 128940}, {"id": "NG2238", "symbol": "🞇", "unicode_point": "U+1F787", "code_point": 128903}, {"id": "NG2247", "symbol": "🝃", "unicode_point": "U+1F743", "code_point": 128835}, {"id": "NG2253", "symbol": "🞏", "unicode_point": "U+1F78F", "code_point": 128911}, {"id": "NG2254", "symbol": "🞮", "unicode_point": "U+1F7AE", "code_point": 128942}, {"id": "NG2257", "symbol": "🠴", "unicode_point": "U+1F834", "code_point": 129076}, {"id": "NG2259", "symbol": "🢥", "unicode_point": "U+1F8A5", "code_point": 129189}, {"id": "NG2267", "symbol": "🟚", "unicode_point": "U+1F7DA", "code_point": 128986}, {"id": "NG2268", "symbol": "🢔", "unicode_point": "U+1F894", "code_point": 129172}, {"id": "NG2269", "symbol": "🜗", "unicode_point": "U+1F717", "code_point": 128791}, {"id": "NG2271", "symbol": "🞕", "unicode_point": "U+1F795", "code_point": 128917}, {"id": "NG2273", "symbol": "🝊", "unicode_point": "U+1F74A", "code_point": 128842}, {"id": "NG2279", "symbol": "🠫", "unicode_point": "U+1F82B", "code_point": 129067}, {"id": "NG2281", "symbol": "🢕", "unicode_point": "U+1F895", "code_point": 129173}, {"id": "NG2282", "symbol": "🡵", "unicode_point": "U+1F875", "code_point": 129141}, {"id": "NG2289", "symbol": "🢣", "unicode_point": "U+1F8A3", "code_point": 129187}, {"id": "NG2295", "symbol": "🢚", "unicode_point": "U+1F89A", "code_point": 129178}, {"id": "NG2297", "symbol": "🝗", "unicode_point": "U+1F757", "code_point": 128855}, {"id": "NG2303", "symbol": "🟖", "unicode_point": "U+1F7D6", "code_point": 128982}, {"id": "NG2306", "symbol": "🟫", "unicode_point": "U+1F7EB", "code_point": 129003}, {"id": "NG2311", "symbol": "🢛", "unicode_point": "U+1F89B", "code_point": 129179}, {"id": "NG2313", "symbol": "🞘", "unicode_point": "U+1F798", "code_point": 128920}, {"id": "NG2317", "symbol": "🞫", "unicode_point": "U+1F7AB", "code_point": 128939}, {"id": "NG2319", "symbol": "🟵", "unicode_point": "U+1F7F5", "code_point": 129013}, {"id": "NG2320", "symbol": "🢙", "unicode_point": "U+1F899", "code_point": 129177}, {"id": "NG2322", "symbol": "🞼", "unicode_point": "U+1F7BC", "code_point": 128956}, {"id": "NG2326", "symbol": "🢓", "unicode_point": "U+1F893", "code_point": 129171}, {"id": "NG2327", "symbol": "🟆", "unicode_point": "U+1F7C6", "code_point": 128966}, {"id": "NG2328", "symbol": "🜷", "unicode_point": "U+1F737", "code_point": 128823}, {"id": "NG2329", "symbol": "🠾", "unicode_point": "U+1F83E", "code_point": 129086}, {"id": "NG2332", "symbol": "🝔", "unicode_point": "U+1F754", "code_point": 128852}, {"id": "NG2334", "symbol": "🠿", "unicode_point": "U+1F83F", "code_point": 129087}, {"id": "NG2338", "symbol": "🝇", "unicode_point": "U+1F747", "code_point": 128839}, {"id": "NG2342", "symbol": "🠣", "unicode_point": "U+1F823", "code_point": 129059}, {"id": "NG2343", "symbol": "🠽", "unicode_point": "U+1F83D", "code_point": 129085}, {"id": "NG2349", "symbol": "🝓", "unicode_point": "U+1F753", "code_point": 128851}, {"id": "NG2351", "symbol": "🢯", "unicode_point": "U+1F8AF", "code_point": 129199}, {"id": "NG2353", "symbol": "🞳", "unicode_point": "U+1F7B3", "code_point": 128947}, {"id": "NG2354", "symbol": "🞰", "unicode_point": "U+1F7B0", "code_point": 128944}, {"id": "NG2356", "symbol": "🟎", "unicode_point": "U+1F7CE", "code_point": 128974}, {"id": "NG2361", "symbol": "🝁", "unicode_point": "U+1F741", "code_point": 128833}, {"id": "NG2362", "symbol": "🝲", "unicode_point": "U+1F772", "code_point": 128882}, {"id": "NG2363", "symbol": "🡇", "unicode_point": "U+1F847", "code_point": 129095}, {"id": "NG2367", "symbol": "🡼", "unicode_point": "U+1F87C", "code_point": 129148}, {"id": "NG2370", "symbol": "🢃", "unicode_point": "U+1F883", "code_point": 129155}, {"id": "NG2373", "symbol": "🞀", "unicode_point": "U+1F780", "code_point": 128896}, {"id": "NG2375", "symbol": "🠂", "unicode_point": "U+1F802", "code_point": 129026}, {"id": "NG2378", "symbol": "🡘", "unicode_point": "U+1F858", "code_point": 129112}, {"id": "NG2380", "symbol": "🜼", "unicode_point": "U+1F73C", "code_point": 128828}, {"id": "NG2382", "symbol": "🢂", "unicode_point": "U+1F882", "code_point": 129154}, {"id": "NG2386", "symbol": "🡈", "unicode_point": "U+1F848", "code_point": 129096}, {"id": "NG2387", "symbol": "🜃", "unicode_point": "U+1F703", "code_point": 128771}, {"id": "NG2388", "symbol": "🝸", "unicode_point": "U+1F778", "code_point": 128888}, {"id": "NG2396", "symbol": "🡌", "unicode_point": "U+1F84C", "code_point": 129100}, {"id": "NG2399", "symbol": "🢧", "unicode_point": "U+1F8A7", "code_point": 129191}, {"id": "NG2401", "symbol": "🞲", "unicode_point": "U+1F7B2", "code_point": 128946}, {"id": "NG2404", "symbol": "🜾", "unicode_point": "U+1F73E", "code_point": 128830}, {"id": "NG2405", "symbol": "🝪", "unicode_point": "U+1F76A", "code_point": 128874}, {"id": "NG2409", "symbol": "🠳", "unicode_point": "U+1F833", "code_point": 129075}, {"id": "NG2418", "symbol": "🜁", "unicode_point": "U+1F701", "code_point": 128769}, {"id": "NG2419", "symbol": "🝢", "unicode_point": "U+1F762", "code_point": 128866}, {"id": "NG2428", "symbol": "🢱", "unicode_point": "U+1F8B1", "code_point": 129201}, {"id": "NG2430", "symbol": "🞿", "unicode_point": "U+1F7BF", "code_point": 128959}, {"id": "NG2433", "symbol": "🜵", "unicode_point": "U+1F735", "code_point": 128821}, {"id": "NG2436", "symbol": "🢍", "unicode_point": "U+1F88D", "code_point": 129165}, {"id": "NG2438", "symbol": "🣆", "unicode_point": "U+1F8C6", "code_point": 129222}, {"id": "NG2439", "symbol": "🜢", "unicode_point": "U+1F722", "code_point": 128802}, {"id": "NG2440", "symbol": "🝐", "unicode_point": "U+1F750", "code_point": 128848}, {"id": "NG2441", "symbol": "🞡", "unicode_point": "U+1F7A1", "code_point": 128929}, {"id": "NG2444", "symbol": "🡩", "unicode_point": "U+1F869", "code_point": 129129}, {"id": "NG2445", "symbol": "🞎", "unicode_point": "U+1F78E", "code_point": 128910}, {"id": "NG2447", "symbol": "🞒", "unicode_point": "U+1F792", "code_point": 128914}, {"id": "NG2451", "symbol": "🝋", "unicode_point": "U+1F74B", "code_point": 128843}, {"id": "NG2455", "symbol": "🜏", "unicode_point": "U+1F70F", "code_point": 128783}, {"id": "NG2456", "symbol": "🝠", "unicode_point": "U+1F760", "code_point": 128864}, {"id": "NG2458", "symbol": "🝎", "unicode_point": "U+1F74E", "code_point": 128846}, {"id": "NG2462", "symbol": "🝼", "unicode_point": "U+1F77C", "code_point": 128892}, {"id": "NG2463", "symbol": "🠭", "unicode_point": "U+1F82D", "code_point": 129069}, {"id": "NG2465", "symbol": "🟱", "unicode_point": "U+1F7F1", "code_point": 129009}, {"id": "NG2467", "symbol": "🟷", "unicode_point": "U+1F7F7", "code_point": 129015}, {"id": "NG2468", "symbol": "🠲", "unicode_point": "U+1F832", "code_point": 129074}, {"id": "NG2469", "symbol": "🟃", "unicode_point": "U+1F7C3", "code_point": 128963}, {"id": "NG2474", "symbol": "🟅", "unicode_point": "U+1F7C5", "code_point": 128965}, {"id": "NG2476", "symbol": "🜲", "unicode_point": "U+1F732", "code_point": 128818}, {"id": "NG2479", "symbol": "🠝", "unicode_point": "U+1F81D", "code_point": 129053}, {"id": "NG2481", "symbol": "🠇", "unicode_point": "U+1F807", "code_point": 129031}, {"id": "NG2482", "symbol": "🜝", "unicode_point": "U+1F71D", "code_point": 128797}, {"id": "NG2484", "symbol": "🜂", "unicode_point": "U+1F702", "code_point": 128770}, {"id": "NG2486", "symbol": "🞴", "unicode_point": "U+1F7B4", "code_point": 128948}, {"id": "NG2488", "symbol": "🜍", "unicode_point": "U+1F70D", "code_point": 128781}, {"id": "NG2489", "symbol": "🞹", "unicode_point": "U+1F7B9", "code_point": 128953}, {"id": "NG2491", "symbol": "🟳", "unicode_point": "U+1F7F3", "code_point": 129011}, {"id": "NG2492", "symbol": "🡔", "unicode_point": "U+1F854", "code_point": 129108}, {"id": "NG2493", "symbol": "🠪", "unicode_point": "U+1F82A", "code_point": 129066}, {"id": "NG2496", "symbol": "🠦", "unicode_point": "U+1F826", "code_point": 129062}, {"id": "NG2497", "symbol": "🠃", "unicode_point": "U+1F803", "code_point": 129027}, {"id": "NG2498", "symbol": "🢎", "unicode_point": "U+1F88E", "code_point": 129166}, {"id": "NG2500", "symbol": "🟮", "unicode_point": "U+1F7EE", "code_point": 129006}, {"id": "NG2501", "symbol": "🟄", "unicode_point": "U+1F7C4", "code_point": 128964}, {"id": "NG2503", "symbol": "🝕", "unicode_point": "U+1F755", "code_point": 128853}, {"id": "NG2505", "symbol": "🝂", "unicode_point": "U+1F742", "code_point": 128834}, {"id": "NG2507", "symbol": "🢘", "unicode_point": "U+1F898", "code_point": 129176}, {"id": "NG2511", "symbol": "🡨", "unicode_point": "U+1F868", "code_point": 129128}, {"id": "NG2513", "symbol": "🞤", "unicode_point": "U+1F7A4", "code_point": 128932}, {"id": "NG2516", "symbol": "🜊", "unicode_point": "U+1F70A", "code_point": 128778}, {"id": "NG2521", "symbol": "🝩", "unicode_point": "U+1F769", "code_point": 128873}, {"id": "NG2524", "symbol": "🠆", "unicode_point": "U+1F806", "code_point": 129030}, {"id": "NG2525", "symbol": "🝘", "unicode_point": "U+1F758", "code_point": 128856}, {"id": "NG2527", "symbol": "🠈", "unicode_point": "U+1F808", "code_point": 129032}, {"id": "NG2529", "symbol": "🟶", "unicode_point": "U+1F7F6", "code_point": 129014}, {"id": "NG2534", "symbol": "🠉", "unicode_point": "U+1F809", "code_point": 129033}, {"id": "NG2536", "symbol": "🞌", "unicode_point": "U+1F78C", "code_point": 128908}, {"id": "NG2537", "symbol": "🟝", "unicode_point": "U+1F7DD", "code_point": 128989}, {"id": "NG2543", "symbol": "🜄", "unicode_point": "U+1F704", "code_point": 128772}, {"id": "NG2544", "symbol": "🞠", "unicode_point": "U+1F7A0", "code_point": 128928}, {"id": "NG2550", "symbol": "🢀", "unicode_point": "U+1F880", "code_point": 129152}, {"id": "NG2551", "symbol": "🞚", "unicode_point": "U+1F79A", "code_point": 128922}, {"id": "NG2553", "symbol": "🜭", "unicode_point": "U+1F72D", "code_point": 128813}, {"id": "NG2554", "symbol": "🞛", "unicode_point": "U+1F79B", "code_point": 128923}, {"id": "NG2559", "symbol": "🠛", "unicode_point": "U+1F81B", "code_point": 129051}, {"id": "NG2560", "symbol": "🞞", "unicode_point": "U+1F79E", "code_point": 128926}, {"id": "NG2561", "symbol": "🡎", "unicode_point": "U+1F84E", "code_point": 129102}, {"id": "NG2567", "symbol": "🞟", "unicode_point": "U+1F79F", "code_point": 128927}, {"id": "NG2568", "symbol": "🠘", "unicode_point": "U+1F818", "code_point": 129048}, {"id": "NG2569", "symbol": "🞨", "unicode_point": "U+1F7A8", "code_point": 128936}, {"id": "NG2571", "symbol": "🝉", "unicode_point": "U+1F749", "code_point": 128841}, {"id": "NG2575", "symbol": "🜘", "unicode_point": "U+1F718", "code_point": 128792}, {"id": "NG2577", "symbol": "🟔", "unicode_point": "U+1F7D4", "code_point": 128980}, {"id": "NG2578", "symbol": "🟰", "unicode_point": "U+1F7F0", "code_point": 129008}, {"id": "NG2581", "symbol": "🡠", "unicode_point": "U+1F860", "code_point": 129120}, {"id": "NG2584", "symbol": "🞧", "unicode_point": "U+1F7A7", "code_point": 128935}, {"id": "NG2586", "symbol": "🢋", "unicode_point": "U+1F88B", "code_point": 129163}, {"id": "NG2588", "symbol": "🜿", "unicode_point": "U+1F73F", "code_point": 128831}, {"id": "NG2591", "symbol": "🝵", "unicode_point": "U+1F775", "code_point": 128885}, {"id": "NG2592", "symbol": "🜎", "unicode_point": "U+1F70E", "code_point": 128782}, {"id": "NG2596", "symbol": "🡴", "unicode_point": "U+1F874", "code_point": 129140}, {"id": "NG2598", "symbol": "🡞", "unicode_point": "U+1F85E", "code_point": 129118}, {"id": "NG2601", "symbol": "🜳", "unicode_point": "U+1F733", "code_point": 128819}, {"id": "NG2606", "symbol": "🡛", "unicode_point": "U+1F85B", "code_point": 129115}, {"id": "NG2608", "symbol": "←", "unicode_point": "U+2190", "code_point": 8592}, {"id": "NG2609", "symbol": "↑", "unicode_point": "U+2191", "code_point": 8593}, {"id": "NG2610", "symbol": "→", "unicode_point": "U+2192", "code_point": 8594}, {"id": "NG2611", "symbol": "↓", "unicode_point": "U+2193", "code_point": 8595}, {"id": "NG2612", "symbol": "↔", "unicode_point": "U+2194", "code_point": 8596}, {"id": "NG2613", "symbol": "↕", "unicode_point": "U+2195", "code_point": 8597}, {"id": "NG2614", "symbol": "↖", "unicode_point": "U+2196", "code_point": 8598}, {"id": "NG2615", "symbol": "↗", "unicode_point": "U+2197", "code_point": 8599}, {"id": "NG2616", "symbol": "↘", "unicode_point": "U+2198", "code_point": 8600}, {"id": "NG2617", "symbol": "↙", "unicode_point": "U+2199", "code_point": 8601}, {"id": "NG2618", "symbol": "↚", "unicode_point": "U+219A", "code_point": 8602}, {"id": "NG2619", "symbol": "↛", "unicode_point": "U+219B", "code_point": 8603}, {"id": "NG2620", "symbol": "↜", "unicode_point": "U+219C", "code_point": 8604}, {"id": "NG2621", "symbol": "↝", "unicode_point": "U+219D", "code_point": 8605}, {"id": "NG2622", "symbol": "↞", "unicode_point": "U+219E", "code_point": 8606}, {"id": "NG2623", "symbol": "↟", "unicode_point": "U+219F", "code_point": 8607}, {"id": "NG2624", "symbol": "↠", "unicode_point": "U+21A0", "code_point": 8608}, {"id": "NG2625", "symbol": "↡", "unicode_point": "U+21A1", "code_point": 8609}, {"id": "NG2626", "symbol": "↢", "unicode_point": "U+21A2", "code_point": 8610}, {"id": "NG2627", "symbol": "↣", "unicode_point": "U+21A3", "code_point": 8611}, {"id": "NG2628", "symbol": "↤", "unicode_point": "U+21A4", "code_point": 8612}, {"id": "NG2629", "symbol": "↥", "unicode_point": "U+21A5", "code_point": 8613}, {"id": "NG2630", "symbol": "↦", "unicode_point": "U+21A6", "code_point": 8614}, {"id": "NG2631", "symbol": "↧", "unicode_point": "U+21A7", "code_point": 8615}, {"id": "NG2632", "symbol": "↨", "unicode_point": "U+21A8", "code_point": 8616}, {"id": "NG2633", "symbol": "↩", "unicode_point": "U+21A9", "code_point": 8617}, {"id": "NG2634", "symbol": "↪", "unicode_point": "U+21AA", "code_point": 8618}, {"id": "NG2635", "symbol": "↫", "unicode_point": "U+21AB", "code_point": 8619}, {"id": "NG2636", "symbol": "↬", "unicode_point": "U+21AC", "code_point": 8620}, {"id": "NG2637", "symbol": "↭", "unicode_point": "U+21AD", "code_point": 8621}, {"id": "NG2638", "symbol": "↮", "unicode_point": "U+21AE", "code_point": 8622}, {"id": "NG2639", "symbol": "↯", "unicode_point": "U+21AF", "code_point": 8623}, {"id": "NG2640", "symbol": "↰", "unicode_point": "U+21B0", "code_point": 8624}, {"id": "NG2641", "symbol": "↱", "unicode_point": "U+21B1", "code_point": 8625}, {"id": "NG2642", "symbol": "↲", "unicode_point": "U+21B2", "code_point": 8626}, {"id": "NG2643", "symbol": "↳", "unicode_point": "U+21B3", "code_point": 8627}, {"id": "NG2644", "symbol": "↴", "unicode_point": "U+21B4", "code_point": 8628}, {"id": "NG2645", "symbol": "↵", "unicode_point": "U+21B5", "code_point": 8629}, {"id": "NG2646", "symbol": "↶", "unicode_point": "U+21B6", "code_point": 8630}, {"id": "NG2647", "symbol": "↷", "unicode_point": "U+21B7", "code_point": 8631}, {"id": "NG2648", "symbol": "↸", "unicode_point": "U+21B8", "code_point": 8632}, {"id": "NG2649", "symbol": "↹", "unicode_point": "U+21B9", "code_point": 8633}, {"id": "NG2650", "symbol": "↺", "unicode_point": "U+21BA", "code_point": 8634}, {"id": "NG2651", "symbol": "↻", "unicode_point": "U+21BB", "code_point": 8635}, {"id": "NG2652", "symbol": "↼", "unicode_point": "U+21BC", "code_point": 8636}, {"id": "NG2653", "symbol": "↽", "unicode_point": "U+21BD", "code_point": 8637}, {"id": "NG2654", "symbol": "↾", "unicode_point": "U+21BE", "code_point": 8638}, {"id": "NG2655", "symbol": "↿", "unicode_point": "U+21BF", "code_point": 8639}, {"id": "NG2656", "symbol": "⇀", "unicode_point": "U+21C0", "code_point": 8640}, {"id": "NG2657", "symbol": "⇁", "unicode_point": "U+21C1", "code_point": 8641}, {"id": "NG2658", "symbol": "⇂", "unicode_point": "U+21C2", "code_point": 8642}, {"id": "NG2659", "symbol": "⇃", "unicode_point": "U+21C3", "code_point": 8643}, {"id": "NG2660", "symbol": "⇄", "unicode_point": "U+21C4", "code_point": 8644}, {"id": "NG2661", "symbol": "⇅", "unicode_point": "U+21C5", "code_point": 8645}, {"id": "NG2662", "symbol": "⇆", "unicode_point": "U+21C6", "code_point": 8646}, {"id": "NG2663", "symbol": "⇇", "unicode_point": "U+21C7", "code_point": 8647}, {"id": "NG2664", "symbol": "⇈", "unicode_point": "U+21C8", "code_point": 8648}, {"id": "NG2665", "symbol": "⇉", "unicode_point": "U+21C9", "code_point": 8649}, {"id": "NG2666", "symbol": "⇊", "unicode_point": "U+21CA", "code_point": 8650}, {"id": "NG2667", "symbol": "⇋", "unicode_point": "U+21CB", "code_point": 8651}, {"id": "NG2668", "symbol": "⇌", "unicode_point": "U+21CC", "code_point": 8652}, {"id": "NG2669", "symbol": "⇍", "unicode_point": "U+21CD", "code_point": 8653}, {"id": "NG2670", "symbol": "⇎", "unicode_point": "U+21CE", "code_point": 8654}, {"id": "NG2671", "symbol": "⇏", "unicode_point": "U+21CF", "code_point": 8655}, {"id": "NG2672", "symbol": "⇐", "unicode_point": "U+21D0", "code_point": 8656}, {"id": "NG2673", "symbol": "⇑", "unicode_point": "U+21D1", "code_point": 8657}, {"id": "NG2674", "symbol": "⇒", "unicode_point": "U+21D2", "code_point": 8658}, {"id": "NG2675", "symbol": "⇓", "unicode_point": "U+21D3", "code_point": 8659}, {"id": "NG2676", "symbol": "⇔", "unicode_point": "U+21D4", "code_point": 8660}, {"id": "NG2677", "symbol": "⇕", "unicode_point": "U+21D5", "code_point": 8661}, {"id": "NG2678", "symbol": "⇖", "unicode_point": "U+21D6", "code_point": 8662}, {"id": "NG2679", "symbol": "⇗", "unicode_point": "U+21D7", "code_point": 8663}, {"id": "NG2680", "symbol": "⇘", "unicode_point": "U+21D8", "code_point": 8664}, {"id": "NG2681", "symbol": "⇙", "unicode_point": "U+21D9", "code_point": 8665}, {"id": "NG2682", "symbol": "⇚", "unicode_point": "U+21DA", "code_point": 8666}, {"id": "NG2683", "symbol": "⇛", "unicode_point": "U+21DB", "code_point": 8667}, {"id": "NG2684", "symbol": "⇜", "unicode_point": "U+21DC", "code_point": 8668}, {"id": "NG2685", "symbol": "⇝", "unicode_point": "U+21DD", "code_point": 8669}, {"id": "NG2686", "symbol": "⇞", "unicode_point": "U+21DE", "code_point": 8670}, {"id": "NG2687", "symbol": "⇟", "unicode_point": "U+21DF", "code_point": 8671}, {"id": "NG2688", "symbol": "⇠", "unicode_point": "U+21E0", "code_point": 8672}, {"id": "NG2689", "symbol": "⇡", "unicode_point": "U+21E1", "code_point": 8673}, {"id": "NG2690", "symbol": "⇢", "unicode_point": "U+21E2", "code_point": 8674}, {"id": "NG2691", "symbol": "⇣", "unicode_point": "U+21E3", "code_point": 8675}, {"id": "NG2692", "symbol": "⇤", "unicode_point": "U+21E4", "code_point": 8676}, {"id": "NG2693", "symbol": "⇥", "unicode_point": "U+21E5", "code_point": 8677}, {"id": "NG2694", "symbol": "⇦", "unicode_point": "U+21E6", "code_point": 8678}, {"id": "NG2695", "symbol": "⇧", "unicode_point": "U+21E7", "code_point": 8679}, {"id": "NG2696", "symbol": "⇨", "unicode_point": "U+21E8", "code_point": 8680}, {"id": "NG2697", "symbol": "⇩", "unicode_point": "U+21E9", "code_point": 8681}, {"id": "NG2698", "symbol": "⇪", "unicode_point": "U+21EA", "code_point": 8682}, {"id": "NG2699", "symbol": "⇫", "unicode_point": "U+21EB", "code_point": 8683}, {"id": "NG2700", "symbol": "⇬", "unicode_point": "U+21EC", "code_point": 8684}, {"id": "NG2701", "symbol": "⇭", "unicode_point": "U+21ED", "code_point": 8685}, {"id": "NG2702", "symbol": "⇮", "unicode_point": "U+21EE", "code_point": 8686}, {"id": "NG2703", "symbol": "⇯", "unicode_point": "U+21EF", "code_point": 8687}, {"id": "NG2704", "symbol": "⇰", "unicode_point": "U+21F0", "code_point": 8688}, {"id": "NG2705", "symbol": "⇱", "unicode_point": "U+21F1", "code_point": 8689}, {"id": "NG2706", "symbol": "⇲", "unicode_point": "U+21F2", "code_point": 8690}, {"id": "NG2707", "symbol": "⇳", "unicode_point": "U+21F3", "code_point": 8691}, {"id": "NG2708", "symbol": "⇴", "unicode_point": "U+21F4", "code_point": 8692}, {"id": "NG2709", "symbol": "⇵", "unicode_point": "U+21F5", "code_point": 8693}, {"id": "NG2710", "symbol": "⇶", "unicode_point": "U+21F6", "code_point": 8694}, {"id": "NG2711", "symbol": "⇷", "unicode_point": "U+21F7", "code_point": 8695}, {"id": "NG2712", "symbol": "⇸", "unicode_point": "U+21F8", "code_point": 8696}, {"id": "NG2713", "symbol": "⇹", "unicode_point": "U+21F9", "code_point": 8697}, {"id": "NG2714", "symbol": "⇺", "unicode_point": "U+21FA", "code_point": 8698}, {"id": "NG2715", "symbol": "⇻", "unicode_point": "U+21FB", "code_point": 8699}, {"id": "NG2716", "symbol": "⇼", "unicode_point": "U+21FC", "code_point": 8700}, {"id": "NG2717", "symbol": "⇽", "unicode_point": "U+21FD", "code_point": 8701}, {"id": "NG2718", "symbol": "⇾", "unicode_point": "U+21FE", "code_point": 8702}, {"id": "NG2719", "symbol": "⇿", "unicode_point": "U+21FF", "code_point": 8703}, {"id": "NG2720", "symbol": "⅀", "unicode_point": "U+2140", "code_point": 8512}, {"id": "NG2721", "symbol": "℀", "unicode_point": "U+2100", "code_point": 8448}, {"id": "NG2722", "symbol": "℁", "unicode_point": "U+2101", "code_point": 8449}, {"id": "NG2723", "symbol": "ℂ", "unicode_point": "U+2102", "code_point": 8450}, {"id": "NG2724", "symbol": "℃", "unicode_point": "U+2103", "code_point": 8451}, {"id": "NG2725", "symbol": "℄", "unicode_point": "U+2104", "code_point": 8452}, {"id": "NG2726", "symbol": "℅", "unicode_point": "U+2105", "code_point": 8453}, {"id": "NG2727", "symbol": "℆", "unicode_point": "U+2106", "code_point": 8454}, {"id": "NG2728", "symbol": "ℇ", "unicode_point": "U+2107", "code_point": 8455}, {"id": "NG2729", "symbol": "℈", "unicode_point": "U+2108", "code_point": 8456}, {"id": "NG2730", "symbol": "℉", "unicode_point": "U+2109", "code_point": 8457}, {"id": "NG2731", "symbol": "ℊ", "unicode_point": "U+210A", "code_point": 8458}, {"id": "NG2732", "symbol": "ℋ", "unicode_point": "U+210B", "code_point": 8459}, {"id": "NG2733", "symbol": "ℌ", "unicode_point": "U+210C", "code_point": 8460}, {"id": "NG2734", "symbol": "ℍ", "unicode_point": "U+210D", "code_point": 8461}, {"id": "NG2735", "symbol": "ℎ", "unicode_point": "U+210E", "code_point": 8462}, {"id": "NG2736", "symbol": "ℏ", "unicode_point": "U+210F", "code_point": 8463}, {"id": "NG2737", "symbol": "ℐ", "unicode_point": "U+2110", "code_point": 8464}, {"id": "NG2738", "symbol": "ℑ", "unicode_point": "U+2111", "code_point": 8465}, {"id": "NG2739", "symbol": "ℒ", "unicode_point": "U+2112", "code_point": 8466}, {"id": "NG2740", "symbol": "ℓ", "unicode_point": "U+2113", "code_point": 8467}, {"id": "NG2741", "symbol": "℔", "unicode_point": "U+2114", "code_point": 8468}, {"id": "NG2742", "symbol": "ℕ", "unicode_point": "U+2115", "code_point": 8469}, {"id": "NG2743", "symbol": "№", "unicode_point": "U+2116", "code_point": 8470}, {"id": "NG2744", "symbol": "℗", "unicode_point": "U+2117", "code_point": 8471}, {"id": "NG2745", "symbol": "℘", "unicode_point": "U+2118", "code_point": 8472}, {"id": "NG2746", "symbol": "ℙ", "unicode_point": "U+2119", "code_point": 8473}, {"id": "NG2747", "symbol": "ℚ", "unicode_point": "U+211A", "code_point": 8474}, {"id": "NG2748", "symbol": "ℛ", "unicode_point": "U+211B", "code_point": 8475}, {"id": "NG2749", "symbol": "ℜ", "unicode_point": "U+211C", "code_point": 8476}, {"id": "NG2750", "symbol": "ℝ", "unicode_point": "U+211D", "code_point": 8477}, {"id": "NG2751", "symbol": "℞", "unicode_point": "U+211E", "code_point": 8478}, {"id": "NG2752", "symbol": "℟", "unicode_point": "U+211F", "code_point": 8479}, {"id": "NG2753", "symbol": "℠", "unicode_point": "U+2120", "code_point": 8480}, {"id": "NG2754", "symbol": "℡", "unicode_point": "U+2121", "code_point": 8481}, {"id": "NG2755", "symbol": "™", "unicode_point": "U+2122", "code_point": 8482}, {"id": "NG2756", "symbol": "℣", "unicode_point": "U+2123", "code_point": 8483}, {"id": "NG2757", "symbol": "ℤ", "unicode_point": "U+2124", "code_point": 8484}, {"id": "NG2758", "symbol": "℥", "unicode_point": "U+2125", "code_point": 8485}, {"id": "NG2759", "symbol": "Ω", "unicode_point": "U+2126", "code_point": 8486}, {"id": "NG2760", "symbol": "℧", "unicode_point": "U+2127", "code_point": 8487}, {"id": "NG2761", "symbol": "ℨ", "unicode_point": "U+2128", "code_point": 8488}, {"id": "NG2762", "symbol": "℩", "unicode_point": "U+2129", "code_point": 8489}, {"id": "NG2763", "symbol": "K", "unicode_point": "U+212A", "code_point": 8490}, {"id": "NG2764", "symbol": "Å", "unicode_point": "U+212B", "code_point": 8491}, {"id": "NG2765", "symbol": "ℬ", "unicode_point": "U+212C", "code_point": 8492}, {"id": "NG2766", "symbol": "ℭ", "unicode_point": "U+212D", "code_point": 8493}, {"id": "NG2767", "symbol": "℮", "unicode_point": "U+212E", "code_point": 8494}, {"id": "NG2768", "symbol": "ℯ", "unicode_point": "U+212F", "code_point": 8495}, {"id": "NG2769", "symbol": "ℰ", "unicode_point": "U+2130", "code_point": 8496}, {"id": "NG2770", "symbol": "ℱ", "unicode_point": "U+2131", "code_point": 8497}, {"id": "NG2771", "symbol": "Ⅎ", "unicode_point": "U+2132", "code_point": 8498}, {"id": "NG2772", "symbol": "ℳ", "unicode_point": "U+2133", "code_point": 8499}, {"id": "NG2773", "symbol": "ℴ", "unicode_point": "U+2134", "code_point": 8500}, {"id": "NG2774", "symbol": "ℵ", "unicode_point": "U+2135", "code_point": 8501}, {"id": "NG2775", "symbol": "ℶ", "unicode_point": "U+2136", "code_point": 8502}, {"id": "NG2776", "symbol": "ℷ", "unicode_point": "U+2137", "code_point": 8503}, {"id": "NG2777", "symbol": "ℸ", "unicode_point": "U+2138", "code_point": 8504}, {"id": "NG2778", "symbol": "ℹ", "unicode_point": "U+2139", "code_point": 8505}, {"id": "NG2779", "symbol": "℺", "unicode_point": "U+213A", "code_point": 8506}, {"id": "NG2780", "symbol": "℻", "unicode_point": "U+213B", "code_point": 8507}, {"id": "NG2781", "symbol": "ℼ", "unicode_point": "U+213C", "code_point": 8508}, {"id": "NG2782", "symbol": "ℽ", "unicode_point": "U+213D", "code_point": 8509}, {"id": "NG2783", "symbol": "ℾ", "unicode_point": "U+213E", "code_point": 8510}, {"id": "NG2784", "symbol": "ℿ", "unicode_point": "U+213F", "code_point": 8511}, {"id": "NG2785", "symbol": "⅁", "unicode_point": "U+2141", "code_point": 8513}, {"id": "NG2786", "symbol": "⅂", "unicode_point": "U+2142", "code_point": 8514}, {"id": "NG2787", "symbol": "⅃", "unicode_point": "U+2143", "code_point": 8515}, {"id": "NG2788", "symbol": "⅄", "unicode_point": "U+2144", "code_point": 8516}, {"id": "NG2789", "symbol": "ⅅ", "unicode_point": "U+2145", "code_point": 8517}, {"id": "NG2790", "symbol": "ⅆ", "unicode_point": "U+2146", "code_point": 8518}, {"id": "NG2791", "symbol": "ⅇ", "unicode_point": "U+2147", "code_point": 8519}, {"id": "NG2792", "symbol": "ⅈ", "unicode_point": "U+2148", "code_point": 8520}, {"id": "NG2793", "symbol": "ⅉ", "unicode_point": "U+2149", "code_point": 8521}, {"id": "NG2794", "symbol": "⅊", "unicode_point": "U+214A", "code_point": 8522}, {"id": "NG2795", "symbol": "⅋", "unicode_point": "U+214B", "code_point": 8523}, {"id": "NG2796", "symbol": "⅌", "unicode_point": "U+214C", "code_point": 8524}, {"id": "NG2797", "symbol": "⅍", "unicode_point": "U+214D", "code_point": 8525}, {"id": "NG2798", "symbol": "ⅎ", "unicode_point": "U+214E", "code_point": 8526}, {"id": "NG2799", "symbol": "⅏", "unicode_point": "U+214F", "code_point": 8527}]}, "domain_results": {"total_symbols": 1920, "domain_counts": {"operator": 95, "memory": 94, "logic": 111, "structure": 89, "flow": 102, "reasoning": 55, "advanced_coding": 303, "meta_programming": 62, "distributed_systems": 92, "quantum_computing": 18, "symbolic_ai": 31, "neural_architectures": 64, "formal_verification": 35, "category_theory": 35, "type_theory": 19, "concurrency_advanced": 56, "machine_learning": 89, "mathematical_structures": 46, "philosophical_concepts": 46, "cognitive_modeling": 33, "reserved_expansion": 58, "extension": 194, "mathematical_operators": 11, "geometric_shapes": 11, "arrows": 11, "technical_symbols": 11, "letterlike_symbols": 11, "number_forms": 11, "supplemental_math": 11, "misc_technical": 11, "logical_operators": 11, "set_theory": 11, "calculus": 11, "algebra": 11, "topology": 11, "analysis": 10, "geometry": 10, "combinatorics": 10, "statistics": 10, "physics_notation": 10}, "category_counts": {"operator": 95, "memory": 94, "logic": 111, "structure": 89, "flow": 102, "reasoning": 55, "advanced_coding": 303, "meta_programming": 62, "distributed_systems": 92, "quantum_computing": 18, "symbolic_ai": 31, "neural_architectures": 64, "formal_verification": 35, "category_theory": 35, "type_theory": 19, "concurrency_advanced": 56, "machine_learning": 89, "mathematical_structures": 46, "philosophical_concepts": 46, "cognitive_modeling": 33, "reserved_expansion": 58, "extension": 194, "mathematical_operators": 11, "geometric_shapes": 11, "arrows": 11, "technical_symbols": 11, "letterlike_symbols": 11, "number_forms": 11, "supplemental_math": 11, "misc_technical": 11, "logical_operators": 11, "set_theory": 11, "calculus": 11, "algebra": 11, "topology": 11, "analysis": 10, "geometry": 10, "combinatorics": 10, "statistics": 10, "physics_notation": 10}, "tier_distribution": {"god": 1920}, "generator_distribution": {"simple": 491, "reasoning_specialized": 55, "god_tier_v1": 936, "ultra_pipeline": 51, "complete_to_2048": 194, "whitelist_pipeline": 193}}, "quality_results": {"total_symbols": 1920, "score_stats": {"min": 95.0, "max": 100.0, "avg": 97.16661458333333, "below_95": 0}, "token_cost_stats": {"min": 1, "max": 1, "avg": 1.0, "above_2": 0}, "token_density_stats": {"min": 0.9, "max": 1.0, "avg": 0.9507083333333333, "below_09": 0}}}