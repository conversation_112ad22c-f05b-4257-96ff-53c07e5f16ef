{"audit_summary": {"audit_timestamp": "20250525_212529", "registry_version": "2.1.0", "registry_status": "GOD_TIER_VALIDATED", "total_symbols": 1871, "audit_scores": {"duplicate_check": 0, "integrity_check": 96.0, "unicode_safety": 86.5, "quality_metrics": 97.0, "overall_score": 69.9}, "critical_issues": {"has_duplicates": true, "integrity_issues": 75, "unsafe_unicode": 233, "low_quality_symbols": 16}, "registry_health": {"excellent": false, "good": false, "acceptable": false, "needs_improvement": true}, "recommendations": ["CRITICO: Rimuovere duplicati dal registry", "Sostituire Unicode non sicuri con range approvati", "Migliorare simboli con validation score < 95.0"]}, "duplicate_results": {"total_symbols": 1871, "duplicate_ids": [], "duplicate_unicode": [], "duplicate_symbols": [], "duplicate_codes": [], "duplicate_names": [{"name": "theoremproving", "count": 2}, {"name": "theoremproving_op", "count": 2}, {"name": "gradient_flow", "count": 2}, {"name": "layer_normalization", "count": 2}, {"name": "weight_initialization", "count": 2}], "has_duplicates": true}, "integrity_results": {"total_symbols": 1871, "missing_fields": [], "invalid_formats": [], "quality_issues": [{"index": 0, "id": "NG0001", "symbol": "⊕", "issues": ["Missing unicode_point", "Missing name"]}, {"index": 1, "id": "NG0002", "symbol": "⊖", "issues": ["Missing unicode_point", "Missing name"]}, {"index": 2, "id": "NG0003", "symbol": "⊗", "issues": ["Missing unicode_point", "Missing name"]}, {"index": 3, "id": "NG0004", "symbol": "⊘", "issues": ["Missing unicode_point", "Missing name"]}, {"index": 4, "id": "NG0005", "symbol": "≡", "issues": ["Missing unicode_point", "Missing name"]}, {"index": 5, "id": "NG0006", "symbol": "≢", "issues": ["Missing unicode_point", "Missing name"]}, {"index": 6, "id": "NG0007", "symbol": "∧", "issues": ["Missing unicode_point", "Missing name"]}, {"index": 7, "id": "NG0008", "symbol": "∨", "issues": ["Missing unicode_point", "Missing name"]}, {"index": 8, "id": "NG0009", "symbol": "¬", "issues": ["Missing unicode_point", "Missing name"]}, {"index": 9, "id": "NG0010", "symbol": "∈", "issues": ["Missing unicode_point", "Missing name"]}, {"index": 10, "id": "NG0011", "symbol": "∉", "issues": ["Missing unicode_point", "Missing name"]}, {"index": 11, "id": "NG0012", "symbol": "⊨", "issues": ["Missing unicode_point", "Missing name"]}, {"index": 12, "id": "NG0013", "symbol": "⇌", "issues": ["Missing unicode_point", "Missing name"]}, {"index": 13, "id": "NG0014", "symbol": "⟨⟩", "issues": ["Missing unicode_point", "Missing name"]}, {"index": 14, "id": "NG0015", "symbol": "⟪⟫", "issues": ["Missing unicode_point", "Missing name"]}, {"index": 15, "id": "NG0016", "symbol": "◊", "issues": ["Missing unicode_point", "Missing name"]}, {"index": 16, "id": "NG0017", "symbol": "◈", "issues": ["Missing unicode_point", "Missing name"]}, {"index": 17, "id": "NG0018", "symbol": "⟲", "issues": ["Missing unicode_point", "Missing name"]}, {"index": 18, "id": "NG0019", "symbol": "⟳", "issues": ["Missing unicode_point", "Missing name"]}, {"index": 19, "id": "NG0020", "symbol": "⤴", "issues": ["Missing unicode_point", "Missing name"]}, {"index": 512, "id": "NG0513", "symbol": "⎢", "issues": ["High token cost: 3"]}, {"index": 513, "id": "NG0514", "symbol": "⏐", "issues": ["Low validation score: 93.0", "High token cost: 3"]}, {"index": 514, "id": "NG0515", "symbol": "⎆", "issues": ["High token cost: 3"]}, {"index": 515, "id": "NG0516", "symbol": "⌔", "issues": ["High token cost: 3"]}, {"index": 516, "id": "NG0517", "symbol": "⍸", "issues": ["High token cost: 3"]}, {"index": 517, "id": "NG0518", "symbol": "⧮", "issues": ["High token cost: 3"]}, {"index": 518, "id": "NG0519", "symbol": "⪜", "issues": ["Low validation score: 93.0", "High token cost: 3"]}, {"index": 519, "id": "NG0520", "symbol": "⇡", "issues": ["High token cost: 3"]}, {"index": 520, "id": "NG0521", "symbol": "⊍", "issues": ["Low validation score: 93.0", "High token cost: 3"]}, {"index": 521, "id": "NG0522", "symbol": "⧬", "issues": ["High token cost: 3"]}, {"index": 522, "id": "NG0523", "symbol": "⍛", "issues": ["High token cost: 3"]}, {"index": 523, "id": "NG0524", "symbol": "⏃", "issues": ["Low validation score: 93.0", "High token cost: 3"]}, {"index": 524, "id": "NG0525", "symbol": "⪪", "issues": ["High token cost: 3"]}, {"index": 525, "id": "NG0526", "symbol": "⦵", "issues": ["Low validation score: 93.0", "High token cost: 3"]}, {"index": 526, "id": "NG0527", "symbol": "⧄", "issues": ["High token cost: 3"]}, {"index": 527, "id": "NG0528", "symbol": "⩓", "issues": ["High token cost: 3"]}, {"index": 528, "id": "NG0529", "symbol": "⎭", "issues": ["Low validation score: 93.0", "High token cost: 3"]}, {"index": 529, "id": "NG0530", "symbol": "⪫", "issues": ["High token cost: 3"]}, {"index": 530, "id": "NG0531", "symbol": "⊲", "issues": ["High token cost: 3"]}, {"index": 531, "id": "NG0532", "symbol": "⩢", "issues": ["High token cost: 3"]}, {"index": 532, "id": "NG0533", "symbol": "⪣", "issues": ["Low validation score: 93.0", "High token cost: 3"]}, {"index": 533, "id": "NG0534", "symbol": "⏓", "issues": ["High token cost: 3"]}, {"index": 534, "id": "NG0535", "symbol": "∟", "issues": ["High token cost: 3"]}, {"index": 535, "id": "NG0536", "symbol": "⌾", "issues": ["High token cost: 3"]}, {"index": 536, "id": "NG0537", "symbol": "⎥", "issues": ["High token cost: 3"]}, {"index": 537, "id": "NG0538", "symbol": "⩷", "issues": ["High token cost: 3"]}, {"index": 538, "id": "NG0539", "symbol": "▪", "issues": ["Low validation score: 93.0", "High token cost: 3"]}, {"index": 539, "id": "NG0540", "symbol": "⍝", "issues": ["High token cost: 3"]}, {"index": 540, "id": "NG0541", "symbol": "⪀", "issues": ["High token cost: 3"]}, {"index": 541, "id": "NG0542", "symbol": "⪲", "issues": ["High token cost: 3"]}, {"index": 542, "id": "NG0543", "symbol": "⏟", "issues": ["High token cost: 3"]}, {"index": 543, "id": "NG0544", "symbol": "⎓", "issues": ["High token cost: 3"]}, {"index": 544, "id": "NG0545", "symbol": "≘", "issues": ["High token cost: 3"]}, {"index": 545, "id": "NG0546", "symbol": "⧗", "issues": ["High token cost: 3"]}, {"index": 546, "id": "NG0547", "symbol": "⧓", "issues": ["High token cost: 3"]}, {"index": 547, "id": "NG0548", "symbol": "⎑", "issues": ["Low validation score: 93.0", "High token cost: 3"]}, {"index": 548, "id": "NG0549", "symbol": "⏰", "issues": ["Low validation score: 93.0", "High token cost: 3"]}, {"index": 549, "id": "NG0550", "symbol": "⧯", "issues": ["High token cost: 3"]}, {"index": 550, "id": "NG0551", "symbol": "⎚", "issues": ["High token cost: 3"]}, {"index": 551, "id": "NG0552", "symbol": "⧱", "issues": ["High token cost: 3"]}, {"index": 552, "id": "NG0553", "symbol": "≊", "issues": ["Low validation score: 93.0", "High token cost: 3"]}, {"index": 553, "id": "NG0554", "symbol": "⦸", "issues": ["High token cost: 3"]}, {"index": 554, "id": "NG0555", "symbol": "⎁", "issues": ["Low validation score: 93.0", "High token cost: 3"]}, {"index": 555, "id": "NG0556", "symbol": "⎯", "issues": ["High token cost: 3"]}, {"index": 556, "id": "NG0557", "symbol": "≵", "issues": ["Low validation score: 93.0", "High token cost: 3"]}, {"index": 557, "id": "NG0558", "symbol": "⎧", "issues": ["Low validation score: 93.0", "High token cost: 3"]}, {"index": 558, "id": "NG0559", "symbol": "⎬", "issues": ["High token cost: 3"]}, {"index": 559, "id": "NG0560", "symbol": "⌏", "issues": ["High token cost: 3"]}, {"index": 560, "id": "NG0561", "symbol": "⦗", "issues": ["Low validation score: 93.0", "High token cost: 3"]}, {"index": 561, "id": "NG0562", "symbol": "∏", "issues": ["High token cost: 3"]}, {"index": 562, "id": "NG0563", "symbol": "⫳", "issues": ["High token cost: 3"]}, {"index": 563, "id": "NG0564", "symbol": "⧔", "issues": ["High token cost: 3"]}, {"index": 564, "id": "NG0565", "symbol": "⊮", "issues": ["Low validation score: 93.0", "High token cost: 3"]}, {"index": 565, "id": "NG0566", "symbol": "⫩", "issues": ["High token cost: 3"]}, {"index": 566, "id": "NG0567", "symbol": "⦿", "issues": ["High token cost: 3"]}], "integrity_score": 95.99144842330304}, "unicode_results": {"total_symbols": 1871, "safe_unicode": 1618, "unsafe_unicode": 233, "range_distribution": {"Geometric Shapes": 94, "Supplemental Mathematical Operators": 252, "Mathematical Operators": 242, "Miscellaneous Technical": 249, "Supplemental Arrows-B": 127, "Miscellaneous Mathematical Symbols-B": 128, "Miscellaneous Symbols and Arrows": 253, "Dingbats": 192, "Miscellaneous Symbols and Pictographs": 81}, "unsafe_symbols": [{"id": "NG0030", "symbol": "↢", "unicode_point": "U+21A2", "code_point": 8610}, {"id": "NG0033", "symbol": "⇵", "unicode_point": "U+21F5", "code_point": 8693}, {"id": "NG0034", "symbol": "⇣", "unicode_point": "U+21E3", "code_point": 8675}, {"id": "NG0042", "symbol": "↤", "unicode_point": "U+21A4", "code_point": 8612}, {"id": "NG0043", "symbol": "↰", "unicode_point": "U+21B0", "code_point": 8624}, {"id": "NG0045", "symbol": "↘", "unicode_point": "U+2198", "code_point": 8600}, {"id": "NG0055", "symbol": "←", "unicode_point": "U+2190", "code_point": 8592}, {"id": "NG0057", "symbol": "⇃", "unicode_point": "U+21C3", "code_point": 8643}, {"id": "NG0059", "symbol": "⇗", "unicode_point": "U+21D7", "code_point": 8663}, {"id": "NG0064", "symbol": "⚮", "unicode_point": "U+26AE", "code_point": 9902}, {"id": "NG0065", "symbol": "⇻", "unicode_point": "U+21FB", "code_point": 8699}, {"id": "NG0072", "symbol": "⇈", "unicode_point": "U+21C8", "code_point": 8648}, {"id": "NG0073", "symbol": "⚳", "unicode_point": "U+26B3", "code_point": 9907}, {"id": "NG0074", "symbol": "↨", "unicode_point": "U+21A8", "code_point": 8616}, {"id": "NG0079", "symbol": "⇦", "unicode_point": "U+21E6", "code_point": 8678}, {"id": "NG0080", "symbol": "⚼", "unicode_point": "U+26BC", "code_point": 9916}, {"id": "NG0084", "symbol": "⇆", "unicode_point": "U+21C6", "code_point": 8646}, {"id": "NG0087", "symbol": "⇟", "unicode_point": "U+21DF", "code_point": 8671}, {"id": "NG0103", "symbol": "⇑", "unicode_point": "U+21D1", "code_point": 8657}, {"id": "NG0107", "symbol": "⇅", "unicode_point": "U+21C5", "code_point": 8645}, {"id": "NG0111", "symbol": "⇎", "unicode_point": "U+21CE", "code_point": 8654}, {"id": "NG0114", "symbol": "↹", "unicode_point": "U+21B9", "code_point": 8633}, {"id": "NG0118", "symbol": "⇷", "unicode_point": "U+21F7", "code_point": 8695}, {"id": "NG0121", "symbol": "↥", "unicode_point": "U+21A5", "code_point": 8613}, {"id": "NG0128", "symbol": "⇪", "unicode_point": "U+21EA", "code_point": 8682}, {"id": "NG0131", "symbol": "↭", "unicode_point": "U+21AD", "code_point": 8621}, {"id": "NG0133", "symbol": "⇴", "unicode_point": "U+21F4", "code_point": 8692}, {"id": "NG0135", "symbol": "⇒", "unicode_point": "U+21D2", "code_point": 8658}, {"id": "NG0137", "symbol": "⇿", "unicode_point": "U+21FF", "code_point": 8703}, {"id": "NG0146", "symbol": "⇰", "unicode_point": "U+21F0", "code_point": 8688}, {"id": "NG0149", "symbol": "↪", "unicode_point": "U+21AA", "code_point": 8618}, {"id": "NG0150", "symbol": "↷", "unicode_point": "U+21B7", "code_point": 8631}, {"id": "NG0151", "symbol": "⚰", "unicode_point": "U+26B0", "code_point": 9904}, {"id": "NG0153", "symbol": "⇕", "unicode_point": "U+21D5", "code_point": 8661}, {"id": "NG0158", "symbol": "↽", "unicode_point": "U+21BD", "code_point": 8637}, {"id": "NG0162", "symbol": "⚢", "unicode_point": "U+26A2", "code_point": 9890}, {"id": "NG0166", "symbol": "⇂", "unicode_point": "U+21C2", "code_point": 8642}, {"id": "NG0168", "symbol": "⇠", "unicode_point": "U+21E0", "code_point": 8672}, {"id": "NG0171", "symbol": "↧", "unicode_point": "U+21A7", "code_point": 8615}, {"id": "NG0172", "symbol": "↮", "unicode_point": "U+21AE", "code_point": 8622}, {"id": "NG0178", "symbol": "↩", "unicode_point": "U+21A9", "code_point": 8617}, {"id": "NG0179", "symbol": "↗", "unicode_point": "U+2197", "code_point": 8599}, {"id": "NG0188", "symbol": "⇱", "unicode_point": "U+21F1", "code_point": 8689}, {"id": "NG0195", "symbol": "⇚", "unicode_point": "U+21DA", "code_point": 8666}, {"id": "NG0196", "symbol": "⇲", "unicode_point": "U+21F2", "code_point": 8690}, {"id": "NG0199", "symbol": "↾", "unicode_point": "U+21BE", "code_point": 8638}, {"id": "NG0208", "symbol": "⇢", "unicode_point": "U+21E2", "code_point": 8674}, {"id": "NG0209", "symbol": "↦", "unicode_point": "U+21A6", "code_point": 8614}, {"id": "NG0211", "symbol": "⇋", "unicode_point": "U+21CB", "code_point": 8651}, {"id": "NG0213", "symbol": "⇖", "unicode_point": "U+21D6", "code_point": 8662}, {"id": "NG0216", "symbol": "↖", "unicode_point": "U+2196", "code_point": 8598}, {"id": "NG0219", "symbol": "⇙", "unicode_point": "U+21D9", "code_point": 8665}, {"id": "NG0222", "symbol": "⇁", "unicode_point": "U+21C1", "code_point": 8641}, {"id": "NG0225", "symbol": "↿", "unicode_point": "U+21BF", "code_point": 8639}, {"id": "NG0231", "symbol": "⇽", "unicode_point": "U+21FD", "code_point": 8701}, {"id": "NG0232", "symbol": "⇤", "unicode_point": "U+21E4", "code_point": 8676}, {"id": "NG0236", "symbol": "⇀", "unicode_point": "U+21C0", "code_point": 8640}, {"id": "NG0240", "symbol": "⚗", "unicode_point": "U+2697", "code_point": 9879}, {"id": "NG0243", "symbol": "↜", "unicode_point": "U+219C", "code_point": 8604}, {"id": "NG0252", "symbol": "↝", "unicode_point": "U+219D", "code_point": 8605}, {"id": "NG0259", "symbol": "↑", "unicode_point": "U+2191", "code_point": 8593}, {"id": "NG0260", "symbol": "⇘", "unicode_point": "U+21D8", "code_point": 8664}, {"id": "NG0262", "symbol": "↛", "unicode_point": "U+219B", "code_point": 8603}, {"id": "NG0265", "symbol": "↠", "unicode_point": "U+21A0", "code_point": 8608}, {"id": "NG0269", "symbol": "↲", "unicode_point": "U+21B2", "code_point": 8626}, {"id": "NG0271", "symbol": "⇄", "unicode_point": "U+21C4", "code_point": 8644}, {"id": "NG0273", "symbol": "↯", "unicode_point": "U+21AF", "code_point": 8623}, {"id": "NG0274", "symbol": "⇇", "unicode_point": "U+21C7", "code_point": 8647}, {"id": "NG0278", "symbol": "⇝", "unicode_point": "U+21DD", "code_point": 8669}, {"id": "NG0282", "symbol": "↔", "unicode_point": "U+2194", "code_point": 8596}, {"id": "NG0285", "symbol": "⇳", "unicode_point": "U+21F3", "code_point": 8691}, {"id": "NG0287", "symbol": "↡", "unicode_point": "U+21A1", "code_point": 8609}, {"id": "NG0290", "symbol": "⇏", "unicode_point": "U+21CF", "code_point": 8655}, {"id": "NG0292", "symbol": "⇓", "unicode_point": "U+21D3", "code_point": 8659}, {"id": "NG0294", "symbol": "⇶", "unicode_point": "U+21F6", "code_point": 8694}, {"id": "NG0297", "symbol": "⇬", "unicode_point": "U+21EC", "code_point": 8684}, {"id": "NG0299", "symbol": "⚥", "unicode_point": "U+26A5", "code_point": 9893}, {"id": "NG0300", "symbol": "→", "unicode_point": "U+2192", "code_point": 8594}, {"id": "NG0301", "symbol": "⇩", "unicode_point": "U+21E9", "code_point": 8681}, {"id": "NG0304", "symbol": "⇼", "unicode_point": "U+21FC", "code_point": 8700}, {"id": "NG0307", "symbol": "⇸", "unicode_point": "U+21F8", "code_point": 8696}, {"id": "NG0308", "symbol": "⚛", "unicode_point": "U+269B", "code_point": 9883}, {"id": "NG0310", "symbol": "↶", "unicode_point": "U+21B6", "code_point": 8630}, {"id": "NG0314", "symbol": "⇺", "unicode_point": "U+21FA", "code_point": 8698}, {"id": "NG0317", "symbol": "↻", "unicode_point": "U+21BB", "code_point": 8635}, {"id": "NG0321", "symbol": "↸", "unicode_point": "U+21B8", "code_point": 8632}, {"id": "NG0335", "symbol": "⇉", "unicode_point": "U+21C9", "code_point": 8649}, {"id": "NG0340", "symbol": "⚖", "unicode_point": "U+2696", "code_point": 9878}, {"id": "NG0348", "symbol": "↳", "unicode_point": "U+21B3", "code_point": 8627}, {"id": "NG0351", "symbol": "↕", "unicode_point": "U+2195", "code_point": 8597}, {"id": "NG0373", "symbol": "↫", "unicode_point": "U+21AB", "code_point": 8619}, {"id": "NG0385", "symbol": "⚯", "unicode_point": "U+26AF", "code_point": 9903}, {"id": "NG0389", "symbol": "⚫", "unicode_point": "U+26AB", "code_point": 9899}, {"id": "NG0391", "symbol": "⚻", "unicode_point": "U+26BB", "code_point": 9915}, {"id": "NG0394", "symbol": "⇞", "unicode_point": "U+21DE", "code_point": 8670}, {"id": "NG0396", "symbol": "↣", "unicode_point": "U+21A3", "code_point": 8611}, {"id": "NG0397", "symbol": "⚝", "unicode_point": "U+269D", "code_point": 9885}, {"id": "NG0399", "symbol": "⇹", "unicode_point": "U+21F9", "code_point": 8697}, {"id": "NG0404", "symbol": "⚧", "unicode_point": "U+26A7", "code_point": 9895}, {"id": "NG0413", "symbol": "⇯", "unicode_point": "U+21EF", "code_point": 8687}, {"id": "NG0415", "symbol": "⇧", "unicode_point": "U+21E7", "code_point": 8679}, {"id": "NG0417", "symbol": "↵", "unicode_point": "U+21B5", "code_point": 8629}, {"id": "NG0423", "symbol": "↬", "unicode_point": "U+21AC", "code_point": 8620}, {"id": "NG0425", "symbol": "⚣", "unicode_point": "U+26A3", "code_point": 9891}, {"id": "NG0427", "symbol": "⚬", "unicode_point": "U+26AC", "code_point": 9900}, {"id": "NG0431", "symbol": "↺", "unicode_point": "U+21BA", "code_point": 8634}, {"id": "NG0434", "symbol": "⇜", "unicode_point": "U+21DC", "code_point": 8668}, {"id": "NG0437", "symbol": "⚜", "unicode_point": "U+269C", "code_point": 9884}, {"id": "NG0440", "symbol": "↓", "unicode_point": "U+2193", "code_point": 8595}, {"id": "NG0451", "symbol": "⇊", "unicode_point": "U+21CA", "code_point": 8650}, {"id": "NG0452", "symbol": "⇥", "unicode_point": "U+21E5", "code_point": 8677}, {"id": "NG0476", "symbol": "⚹", "unicode_point": "U+26B9", "code_point": 9913}, {"id": "NG0477", "symbol": "⚠", "unicode_point": "U+26A0", "code_point": 9888}, {"id": "NG0478", "symbol": "⇍", "unicode_point": "U+21CD", "code_point": 8653}, {"id": "NG0480", "symbol": "⚟", "unicode_point": "U+269F", "code_point": 9887}, {"id": "NG0484", "symbol": "⚶", "unicode_point": "U+26B6", "code_point": 9910}, {"id": "NG0485", "symbol": "⇾", "unicode_point": "U+21FE", "code_point": 8702}, {"id": "NG0488", "symbol": "⇐", "unicode_point": "U+21D0", "code_point": 8656}, {"id": "NG0492", "symbol": "⇮", "unicode_point": "U+21EE", "code_point": 8686}, {"id": "NG0493", "symbol": "⚩", "unicode_point": "U+26A9", "code_point": 9897}, {"id": "NG0501", "symbol": "⇫", "unicode_point": "U+21EB", "code_point": 8683}, {"id": "NG0507", "symbol": "⚤", "unicode_point": "U+26A4", "code_point": 9892}, {"id": "NG0510", "symbol": "⚪", "unicode_point": "U+26AA", "code_point": 9898}, {"id": "NG0512", "symbol": "↼", "unicode_point": "U+21BC", "code_point": 8636}, {"id": "NG0520", "symbol": "⇡", "unicode_point": "U+21E1", "code_point": 8673}, {"id": "NG0885", "symbol": "⣖", "unicode_point": "U+28D6", "code_point": 10454}, {"id": "NG0888", "symbol": "⢠", "unicode_point": "U+28A0", "code_point": 10400}, {"id": "NG0893", "symbol": "⠏", "unicode_point": "U+280F", "code_point": 10255}, {"id": "NG0907", "symbol": "⠌", "unicode_point": "U+280C", "code_point": 10252}, {"id": "NG0910", "symbol": "⢓", "unicode_point": "U+2893", "code_point": 10387}, {"id": "NG0920", "symbol": "⠣", "unicode_point": "U+2823", "code_point": 10275}, {"id": "NG0937", "symbol": "⣋", "unicode_point": "U+28CB", "code_point": 10443}, {"id": "NG0941", "symbol": "⢐", "unicode_point": "U+2890", "code_point": 10384}, {"id": "NG0949", "symbol": "⡗", "unicode_point": "U+2857", "code_point": 10327}, {"id": "NG0974", "symbol": "⣠", "unicode_point": "U+28E0", "code_point": 10464}, {"id": "NG0983", "symbol": "⢖", "unicode_point": "U+2896", "code_point": 10390}, {"id": "NG0985", "symbol": "𝝏", "unicode_point": "U+1D74F", "code_point": 120655}, {"id": "NG1003", "symbol": "⠁", "unicode_point": "U+2801", "code_point": 10241}, {"id": "NG1007", "symbol": "⣐", "unicode_point": "U+28D0", "code_point": 10448}, {"id": "NG1014", "symbol": "⠮", "unicode_point": "U+282E", "code_point": 10286}, {"id": "NG1019", "symbol": "⠀", "unicode_point": "U+2800", "code_point": 10240}, {"id": "NG1028", "symbol": "⠂", "unicode_point": "U+2802", "code_point": 10242}, {"id": "NG1038", "symbol": "⢈", "unicode_point": "U+2888", "code_point": 10376}, {"id": "NG1052", "symbol": "⡘", "unicode_point": "U+2858", "code_point": 10328}, {"id": "NG1066", "symbol": "⡔", "unicode_point": "U+2854", "code_point": 10324}, {"id": "NG1074", "symbol": "⠎", "unicode_point": "U+280E", "code_point": 10254}, {"id": "NG1082", "symbol": "⠹", "unicode_point": "U+2839", "code_point": 10297}, {"id": "NG1097", "symbol": "⣛", "unicode_point": "U+28DB", "code_point": 10459}, {"id": "NG1112", "symbol": "⢧", "unicode_point": "U+28A7", "code_point": 10407}, {"id": "NG1130", "symbol": "⡣", "unicode_point": "U+2863", "code_point": 10339}, {"id": "NG1145", "symbol": "⡑", "unicode_point": "U+2851", "code_point": 10321}, {"id": "NG1146", "symbol": "⢣", "unicode_point": "U+28A3", "code_point": 10403}, {"id": "NG1153", "symbol": "⠿", "unicode_point": "U+283F", "code_point": 10303}, {"id": "NG1160", "symbol": "⣃", "unicode_point": "U+28C3", "code_point": 10435}, {"id": "NG1187", "symbol": "⠒", "unicode_point": "U+2812", "code_point": 10258}, {"id": "NG1195", "symbol": "⣹", "unicode_point": "U+28F9", "code_point": 10489}, {"id": "NG1208", "symbol": "⢁", "unicode_point": "U+2881", "code_point": 10369}, {"id": "NG1211", "symbol": "⡶", "unicode_point": "U+2876", "code_point": 10358}, {"id": "NG1214", "symbol": "⢹", "unicode_point": "U+28B9", "code_point": 10425}, {"id": "NG1226", "symbol": "⣘", "unicode_point": "U+28D8", "code_point": 10456}, {"id": "NG1232", "symbol": "⣂", "unicode_point": "U+28C2", "code_point": 10434}, {"id": "NG1233", "symbol": "⡆", "unicode_point": "U+2846", "code_point": 10310}, {"id": "NG1236", "symbol": "⡭", "unicode_point": "U+286D", "code_point": 10349}, {"id": "NG1244", "symbol": "⣩", "unicode_point": "U+28E9", "code_point": 10473}, {"id": "NG1258", "symbol": "⢵", "unicode_point": "U+28B5", "code_point": 10421}, {"id": "NG1267", "symbol": "⣔", "unicode_point": "U+28D4", "code_point": 10452}, {"id": "NG1274", "symbol": "⡪", "unicode_point": "U+286A", "code_point": 10346}, {"id": "NG1276", "symbol": "⡡", "unicode_point": "U+2861", "code_point": 10337}, {"id": "NG1278", "symbol": "⣙", "unicode_point": "U+28D9", "code_point": 10457}, {"id": "NG1304", "symbol": "⠋", "unicode_point": "U+280B", "code_point": 10251}, {"id": "NG1314", "symbol": "⠗", "unicode_point": "U+2817", "code_point": 10263}, {"id": "NG1318", "symbol": "⢳", "unicode_point": "U+28B3", "code_point": 10419}, {"id": "NG1329", "symbol": "⢩", "unicode_point": "U+28A9", "code_point": 10409}, {"id": "NG1332", "symbol": "⡾", "unicode_point": "U+287E", "code_point": 10366}, {"id": "NG1360", "symbol": "⠰", "unicode_point": "U+2830", "code_point": 10288}, {"id": "NG1371", "symbol": "⡄", "unicode_point": "U+2844", "code_point": 10308}, {"id": "NG1396", "symbol": "⣥", "unicode_point": "U+28E5", "code_point": 10469}, {"id": "NG1451", "symbol": "⣫", "unicode_point": "U+28EB", "code_point": 10475}, {"id": "NG1464", "symbol": "⡧", "unicode_point": "U+2867", "code_point": 10343}, {"id": "NG1474", "symbol": "⡠", "unicode_point": "U+2860", "code_point": 10336}, {"id": "NG1513", "symbol": "⢉", "unicode_point": "U+2889", "code_point": 10377}, {"id": "NG1534", "symbol": "⣳", "unicode_point": "U+28F3", "code_point": 10483}, {"id": "NG1537", "symbol": "⡦", "unicode_point": "U+2866", "code_point": 10342}, {"id": "NG1547", "symbol": "⠴", "unicode_point": "U+2834", "code_point": 10292}, {"id": "NG1548", "symbol": "⡍", "unicode_point": "U+284D", "code_point": 10317}, {"id": "NG1549", "symbol": "⠾", "unicode_point": "U+283E", "code_point": 10302}, {"id": "NG1555", "symbol": "𝝯", "unicode_point": "U+1D76F", "code_point": 120687}, {"id": "NG1561", "symbol": "⡟", "unicode_point": "U+285F", "code_point": 10335}, {"id": "NG1562", "symbol": "⣉", "unicode_point": "U+28C9", "code_point": 10441}, {"id": "NG1566", "symbol": "⣿", "unicode_point": "U+28FF", "code_point": 10495}, {"id": "NG1572", "symbol": "⢲", "unicode_point": "U+28B2", "code_point": 10418}, {"id": "NG1573", "symbol": "⡲", "unicode_point": "U+2872", "code_point": 10354}, {"id": "NG1574", "symbol": "⣱", "unicode_point": "U+28F1", "code_point": 10481}, {"id": "NG1594", "symbol": "⡬", "unicode_point": "U+286C", "code_point": 10348}, {"id": "NG1603", "symbol": "⡃", "unicode_point": "U+2843", "code_point": 10307}, {"id": "NG1623", "symbol": "⠥", "unicode_point": "U+2825", "code_point": 10277}, {"id": "NG1627", "symbol": "⠆", "unicode_point": "U+2806", "code_point": 10246}, {"id": "NG1642", "symbol": "⡢", "unicode_point": "U+2862", "code_point": 10338}, {"id": "NG1668", "symbol": "⠼", "unicode_point": "U+283C", "code_point": 10300}, {"id": "NG1671", "symbol": "⢗", "unicode_point": "U+2897", "code_point": 10391}, {"id": "NG1696", "symbol": "⣶", "unicode_point": "U+28F6", "code_point": 10486}, {"id": "NG1712", "symbol": "⢿", "unicode_point": "U+28BF", "code_point": 10431}, {"id": "NG1730", "symbol": "⠧", "unicode_point": "U+2827", "code_point": 10279}, {"id": "NG1779", "symbol": "⢂", "unicode_point": "U+2882", "code_point": 10370}, {"id": "NG1782", "symbol": "⢼", "unicode_point": "U+28BC", "code_point": 10428}, {"id": "NG1791", "symbol": "⢬", "unicode_point": "U+28AC", "code_point": 10412}, {"id": "NG1805", "symbol": "⣁", "unicode_point": "U+28C1", "code_point": 10433}, {"id": "NG1827", "symbol": "⠲", "unicode_point": "U+2832", "code_point": 10290}, {"id": "NG1830", "symbol": "⣕", "unicode_point": "U+28D5", "code_point": 10453}, {"id": "NG1842", "symbol": "𝞉", "unicode_point": "U+1D789", "code_point": 120713}, {"id": "NG1844", "symbol": "⣡", "unicode_point": "U+28E1", "code_point": 10465}, {"id": "NG1846", "symbol": "⢢", "unicode_point": "U+28A2", "code_point": 10402}, {"id": "NG1850", "symbol": "⢞", "unicode_point": "U+289E", "code_point": 10398}, {"id": "NG1861", "symbol": "⢡", "unicode_point": "U+28A1", "code_point": 10401}, {"id": "NG1866", "symbol": "⢫", "unicode_point": "U+28AB", "code_point": 10411}, {"id": "NG1890", "symbol": "⠕", "unicode_point": "U+2815", "code_point": 10261}, {"id": "NG1928", "symbol": "⠉", "unicode_point": "U+2809", "code_point": 10249}, {"id": "NG1931", "symbol": "⢸", "unicode_point": "U+28B8", "code_point": 10424}, {"id": "NG1933", "symbol": "⢮", "unicode_point": "U+28AE", "code_point": 10414}, {"id": "NG1940", "symbol": "⣽", "unicode_point": "U+28FD", "code_point": 10493}, {"id": "NG1945", "symbol": "⠠", "unicode_point": "U+2820", "code_point": 10272}, {"id": "NG1950", "symbol": "⡹", "unicode_point": "U+2879", "code_point": 10361}, {"id": "NG1955", "symbol": "⠚", "unicode_point": "U+281A", "code_point": 10266}, {"id": "NG1956", "symbol": "⣭", "unicode_point": "U+28ED", "code_point": 10477}, {"id": "NG1962", "symbol": "⠑", "unicode_point": "U+2811", "code_point": 10257}, {"id": "NG1969", "symbol": "⠙", "unicode_point": "U+2819", "code_point": 10265}, {"id": "NG1970", "symbol": "⣧", "unicode_point": "U+28E7", "code_point": 10471}, {"id": "NG1982", "symbol": "⠻", "unicode_point": "U+283B", "code_point": 10299}, {"id": "NG2003", "symbol": "⡳", "unicode_point": "U+2873", "code_point": 10355}, {"id": "NG2015", "symbol": "⢋", "unicode_point": "U+288B", "code_point": 10379}, {"id": "NG2017", "symbol": "𝜕", "unicode_point": "U+1D715", "code_point": 120597}, {"id": "NG2032", "symbol": "⠷", "unicode_point": "U+2837", "code_point": 10295}, {"id": "NG2044", "symbol": "⣷", "unicode_point": "U+28F7", "code_point": 10487}]}, "domain_results": {"total_symbols": 1871, "domain_counts": {"operator": 99, "logic": 120, "reasoning": 56, "structure": 91, "flow": 107, "memory": 94, "advanced_coding": 306, "meta_programming": 62, "distributed_systems": 108, "quantum_computing": 29, "symbolic_ai": 44, "neural_architectures": 72, "formal_verification": 49, "category_theory": 43, "type_theory": 37, "concurrency_advanced": 79, "machine_learning": 123, "mathematical_structures": 64, "philosophical_concepts": 64, "cognitive_modeling": 54, "reserved_expansion": 89, "final_domains": 81}, "category_counts": {"operator": 99, "logic": 120, "reasoning": 56, "structure": 91, "flow": 107, "memory": 94, "advanced_coding": 306, "meta_programming": 62, "distributed_systems": 108, "quantum_computing": 29, "symbolic_ai": 44, "neural_architectures": 72, "formal_verification": 49, "category_theory": 43, "type_theory": 37, "concurrency_advanced": 79, "machine_learning": 123, "mathematical_structures": 64, "philosophical_concepts": 64, "cognitive_modeling": 54, "reserved_expansion": 89, "final_domains": 81}, "tier_distribution": {"god": 1871}, "generator_distribution": {"unknown": 20, "simple": 492, "reasoning_specialized": 55, "god_tier_v1": 1167, "ultra_pipeline": 56, "final_completion": 81}}, "quality_results": {"total_symbols": 1871, "score_stats": {"min": 93.0, "max": 100.0, "avg": 96.95815072153928, "below_95": 16}, "token_cost_stats": {"min": 1, "max": 3, "avg": 1.0587920897915553, "above_2": 55}, "token_density_stats": {"min": 0.9, "max": 1.0, "avg": 0.9499518973810795, "below_09": 0}}}