{"audit_summary": {"audit_timestamp": "20250525_224210", "registry_version": "4.2.0", "registry_status": "SAFE_ONLY_1727", "total_symbols": 1727, "audit_scores": {"duplicate_check": 100, "integrity_check": 100.0, "unicode_safety": 89.9, "quality_metrics": 97.1, "overall_score": 96.7}, "critical_issues": {"has_duplicates": false, "integrity_issues": 0, "unsafe_unicode": 175, "low_quality_symbols": 0}, "registry_health": {"excellent": true, "good": false, "acceptable": false, "needs_improvement": false}, "recommendations": ["Sostituire Unicode non sicuri con range approvati"]}, "duplicate_results": {"total_symbols": 1727, "duplicate_ids": [], "duplicate_unicode": [], "duplicate_symbols": [], "duplicate_codes": [], "duplicate_names": [], "has_duplicates": false}, "integrity_results": {"total_symbols": 1727, "missing_fields": [], "invalid_formats": [], "quality_issues": [], "integrity_score": 100.0}, "unicode_results": {"total_symbols": 1727, "safe_unicode": 1552, "unsafe_unicode": 175, "range_distribution": {"Geometric Shapes": 96, "Supplemental Mathematical Operators": 255, "Mathematical Operators": 256, "Miscellaneous Technical": 255, "Supplemental Arrows-B": 128, "Miscellaneous Mathematical Symbols-B": 128, "Dingbats": 192, "Miscellaneous Symbols and Arrows": 242}, "unsafe_symbols": [{"id": "NG2124", "symbol": "🟥", "unicode_point": "U+1F7E5", "code_point": 128997}, {"id": "NG2127", "symbol": "🝻", "unicode_point": "U+1F77B", "code_point": 128891}, {"id": "NG2128", "symbol": "🝷", "unicode_point": "U+1F777", "code_point": 128887}, {"id": "NG2133", "symbol": "🜐", "unicode_point": "U+1F710", "code_point": 128784}, {"id": "NG2136", "symbol": "🢐", "unicode_point": "U+1F890", "code_point": 129168}, {"id": "NG2137", "symbol": "🜨", "unicode_point": "U+1F728", "code_point": 128808}, {"id": "NG2142", "symbol": "🜔", "unicode_point": "U+1F714", "code_point": 128788}, {"id": "NG2147", "symbol": "🟈", "unicode_point": "U+1F7C8", "code_point": 128968}, {"id": "NG2151", "symbol": "🣂", "unicode_point": "U+1F8C2", "code_point": 129218}, {"id": "NG2154", "symbol": "🠷", "unicode_point": "U+1F837", "code_point": 129079}, {"id": "NG2157", "symbol": "🞱", "unicode_point": "U+1F7B1", "code_point": 128945}, {"id": "NG2159", "symbol": "🢳", "unicode_point": "U+1F8B3", "code_point": 129203}, {"id": "NG2161", "symbol": "🢁", "unicode_point": "U+1F881", "code_point": 129153}, {"id": "NG2162", "symbol": "🠚", "unicode_point": "U+1F81A", "code_point": 129050}, {"id": "NG2165", "symbol": "🣃", "unicode_point": "U+1F8C3", "code_point": 129219}, {"id": "NG2168", "symbol": "🜛", "unicode_point": "U+1F71B", "code_point": 128795}, {"id": "NG2175", "symbol": "🢺", "unicode_point": "U+1F8BA", "code_point": 129210}, {"id": "NG2177", "symbol": "🜈", "unicode_point": "U+1F708", "code_point": 128776}, {"id": "NG2182", "symbol": "🞥", "unicode_point": "U+1F7A5", "code_point": 128933}, {"id": "NG2183", "symbol": "🟹", "unicode_point": "U+1F7F9", "code_point": 129017}, {"id": "NG2184", "symbol": "🝅", "unicode_point": "U+1F745", "code_point": 128837}, {"id": "NG2185", "symbol": "🟻", "unicode_point": "U+1F7FB", "code_point": 129019}, {"id": "NG2187", "symbol": "🟌", "unicode_point": "U+1F7CC", "code_point": 128972}, {"id": "NG2190", "symbol": "🞂", "unicode_point": "U+1F782", "code_point": 128898}, {"id": "NG2194", "symbol": "🝏", "unicode_point": "U+1F74F", "code_point": 128847}, {"id": "NG2201", "symbol": "🞾", "unicode_point": "U+1F7BE", "code_point": 128958}, {"id": "NG2207", "symbol": "🠢", "unicode_point": "U+1F822", "code_point": 129058}, {"id": "NG2211", "symbol": "🟢", "unicode_point": "U+1F7E2", "code_point": 128994}, {"id": "NG2214", "symbol": "🜱", "unicode_point": "U+1F731", "code_point": 128817}, {"id": "NG2222", "symbol": "🡅", "unicode_point": "U+1F845", "code_point": 129093}, {"id": "NG2224", "symbol": "🟒", "unicode_point": "U+1F7D2", "code_point": 128978}, {"id": "NG2229", "symbol": "🝭", "unicode_point": "U+1F76D", "code_point": 128877}, {"id": "NG2230", "symbol": "🝙", "unicode_point": "U+1F759", "code_point": 128857}, {"id": "NG2231", "symbol": "🞗", "unicode_point": "U+1F797", "code_point": 128919}, {"id": "NG2233", "symbol": "🟯", "unicode_point": "U+1F7EF", "code_point": 129007}, {"id": "NG2234", "symbol": "🜉", "unicode_point": "U+1F709", "code_point": 128777}, {"id": "NG2235", "symbol": "🞬", "unicode_point": "U+1F7AC", "code_point": 128940}, {"id": "NG2238", "symbol": "🞇", "unicode_point": "U+1F787", "code_point": 128903}, {"id": "NG2247", "symbol": "🝃", "unicode_point": "U+1F743", "code_point": 128835}, {"id": "NG2253", "symbol": "🞏", "unicode_point": "U+1F78F", "code_point": 128911}, {"id": "NG2254", "symbol": "🞮", "unicode_point": "U+1F7AE", "code_point": 128942}, {"id": "NG2257", "symbol": "🠴", "unicode_point": "U+1F834", "code_point": 129076}, {"id": "NG2259", "symbol": "🢥", "unicode_point": "U+1F8A5", "code_point": 129189}, {"id": "NG2267", "symbol": "🟚", "unicode_point": "U+1F7DA", "code_point": 128986}, {"id": "NG2268", "symbol": "🢔", "unicode_point": "U+1F894", "code_point": 129172}, {"id": "NG2269", "symbol": "🜗", "unicode_point": "U+1F717", "code_point": 128791}, {"id": "NG2271", "symbol": "🞕", "unicode_point": "U+1F795", "code_point": 128917}, {"id": "NG2273", "symbol": "🝊", "unicode_point": "U+1F74A", "code_point": 128842}, {"id": "NG2279", "symbol": "🠫", "unicode_point": "U+1F82B", "code_point": 129067}, {"id": "NG2281", "symbol": "🢕", "unicode_point": "U+1F895", "code_point": 129173}, {"id": "NG2282", "symbol": "🡵", "unicode_point": "U+1F875", "code_point": 129141}, {"id": "NG2289", "symbol": "🢣", "unicode_point": "U+1F8A3", "code_point": 129187}, {"id": "NG2295", "symbol": "🢚", "unicode_point": "U+1F89A", "code_point": 129178}, {"id": "NG2297", "symbol": "🝗", "unicode_point": "U+1F757", "code_point": 128855}, {"id": "NG2303", "symbol": "🟖", "unicode_point": "U+1F7D6", "code_point": 128982}, {"id": "NG2306", "symbol": "🟫", "unicode_point": "U+1F7EB", "code_point": 129003}, {"id": "NG2311", "symbol": "🢛", "unicode_point": "U+1F89B", "code_point": 129179}, {"id": "NG2313", "symbol": "🞘", "unicode_point": "U+1F798", "code_point": 128920}, {"id": "NG2317", "symbol": "🞫", "unicode_point": "U+1F7AB", "code_point": 128939}, {"id": "NG2319", "symbol": "🟵", "unicode_point": "U+1F7F5", "code_point": 129013}, {"id": "NG2320", "symbol": "🢙", "unicode_point": "U+1F899", "code_point": 129177}, {"id": "NG2322", "symbol": "🞼", "unicode_point": "U+1F7BC", "code_point": 128956}, {"id": "NG2326", "symbol": "🢓", "unicode_point": "U+1F893", "code_point": 129171}, {"id": "NG2327", "symbol": "🟆", "unicode_point": "U+1F7C6", "code_point": 128966}, {"id": "NG2328", "symbol": "🜷", "unicode_point": "U+1F737", "code_point": 128823}, {"id": "NG2329", "symbol": "🠾", "unicode_point": "U+1F83E", "code_point": 129086}, {"id": "NG2332", "symbol": "🝔", "unicode_point": "U+1F754", "code_point": 128852}, {"id": "NG2334", "symbol": "🠿", "unicode_point": "U+1F83F", "code_point": 129087}, {"id": "NG2338", "symbol": "🝇", "unicode_point": "U+1F747", "code_point": 128839}, {"id": "NG2342", "symbol": "🠣", "unicode_point": "U+1F823", "code_point": 129059}, {"id": "NG2343", "symbol": "🠽", "unicode_point": "U+1F83D", "code_point": 129085}, {"id": "NG2349", "symbol": "🝓", "unicode_point": "U+1F753", "code_point": 128851}, {"id": "NG2351", "symbol": "🢯", "unicode_point": "U+1F8AF", "code_point": 129199}, {"id": "NG2353", "symbol": "🞳", "unicode_point": "U+1F7B3", "code_point": 128947}, {"id": "NG2354", "symbol": "🞰", "unicode_point": "U+1F7B0", "code_point": 128944}, {"id": "NG2356", "symbol": "🟎", "unicode_point": "U+1F7CE", "code_point": 128974}, {"id": "NG2361", "symbol": "🝁", "unicode_point": "U+1F741", "code_point": 128833}, {"id": "NG2362", "symbol": "🝲", "unicode_point": "U+1F772", "code_point": 128882}, {"id": "NG2363", "symbol": "🡇", "unicode_point": "U+1F847", "code_point": 129095}, {"id": "NG2367", "symbol": "🡼", "unicode_point": "U+1F87C", "code_point": 129148}, {"id": "NG2370", "symbol": "🢃", "unicode_point": "U+1F883", "code_point": 129155}, {"id": "NG2373", "symbol": "🞀", "unicode_point": "U+1F780", "code_point": 128896}, {"id": "NG2375", "symbol": "🠂", "unicode_point": "U+1F802", "code_point": 129026}, {"id": "NG2378", "symbol": "🡘", "unicode_point": "U+1F858", "code_point": 129112}, {"id": "NG2380", "symbol": "🜼", "unicode_point": "U+1F73C", "code_point": 128828}, {"id": "NG2382", "symbol": "🢂", "unicode_point": "U+1F882", "code_point": 129154}, {"id": "NG2386", "symbol": "🡈", "unicode_point": "U+1F848", "code_point": 129096}, {"id": "NG2387", "symbol": "🜃", "unicode_point": "U+1F703", "code_point": 128771}, {"id": "NG2388", "symbol": "🝸", "unicode_point": "U+1F778", "code_point": 128888}, {"id": "NG2396", "symbol": "🡌", "unicode_point": "U+1F84C", "code_point": 129100}, {"id": "NG2399", "symbol": "🢧", "unicode_point": "U+1F8A7", "code_point": 129191}, {"id": "NG2401", "symbol": "🞲", "unicode_point": "U+1F7B2", "code_point": 128946}, {"id": "NG2404", "symbol": "🜾", "unicode_point": "U+1F73E", "code_point": 128830}, {"id": "NG2405", "symbol": "🝪", "unicode_point": "U+1F76A", "code_point": 128874}, {"id": "NG2409", "symbol": "🠳", "unicode_point": "U+1F833", "code_point": 129075}, {"id": "NG2418", "symbol": "🜁", "unicode_point": "U+1F701", "code_point": 128769}, {"id": "NG2419", "symbol": "🝢", "unicode_point": "U+1F762", "code_point": 128866}, {"id": "NG2428", "symbol": "🢱", "unicode_point": "U+1F8B1", "code_point": 129201}, {"id": "NG2430", "symbol": "🞿", "unicode_point": "U+1F7BF", "code_point": 128959}, {"id": "NG2433", "symbol": "🜵", "unicode_point": "U+1F735", "code_point": 128821}, {"id": "NG2436", "symbol": "🢍", "unicode_point": "U+1F88D", "code_point": 129165}, {"id": "NG2438", "symbol": "🣆", "unicode_point": "U+1F8C6", "code_point": 129222}, {"id": "NG2439", "symbol": "🜢", "unicode_point": "U+1F722", "code_point": 128802}, {"id": "NG2440", "symbol": "🝐", "unicode_point": "U+1F750", "code_point": 128848}, {"id": "NG2441", "symbol": "🞡", "unicode_point": "U+1F7A1", "code_point": 128929}, {"id": "NG2444", "symbol": "🡩", "unicode_point": "U+1F869", "code_point": 129129}, {"id": "NG2445", "symbol": "🞎", "unicode_point": "U+1F78E", "code_point": 128910}, {"id": "NG2447", "symbol": "🞒", "unicode_point": "U+1F792", "code_point": 128914}, {"id": "NG2451", "symbol": "🝋", "unicode_point": "U+1F74B", "code_point": 128843}, {"id": "NG2455", "symbol": "🜏", "unicode_point": "U+1F70F", "code_point": 128783}, {"id": "NG2456", "symbol": "🝠", "unicode_point": "U+1F760", "code_point": 128864}, {"id": "NG2458", "symbol": "🝎", "unicode_point": "U+1F74E", "code_point": 128846}, {"id": "NG2462", "symbol": "🝼", "unicode_point": "U+1F77C", "code_point": 128892}, {"id": "NG2463", "symbol": "🠭", "unicode_point": "U+1F82D", "code_point": 129069}, {"id": "NG2465", "symbol": "🟱", "unicode_point": "U+1F7F1", "code_point": 129009}, {"id": "NG2467", "symbol": "🟷", "unicode_point": "U+1F7F7", "code_point": 129015}, {"id": "NG2468", "symbol": "🠲", "unicode_point": "U+1F832", "code_point": 129074}, {"id": "NG2469", "symbol": "🟃", "unicode_point": "U+1F7C3", "code_point": 128963}, {"id": "NG2474", "symbol": "🟅", "unicode_point": "U+1F7C5", "code_point": 128965}, {"id": "NG2476", "symbol": "🜲", "unicode_point": "U+1F732", "code_point": 128818}, {"id": "NG2479", "symbol": "🠝", "unicode_point": "U+1F81D", "code_point": 129053}, {"id": "NG2481", "symbol": "🠇", "unicode_point": "U+1F807", "code_point": 129031}, {"id": "NG2482", "symbol": "🜝", "unicode_point": "U+1F71D", "code_point": 128797}, {"id": "NG2484", "symbol": "🜂", "unicode_point": "U+1F702", "code_point": 128770}, {"id": "NG2486", "symbol": "🞴", "unicode_point": "U+1F7B4", "code_point": 128948}, {"id": "NG2488", "symbol": "🜍", "unicode_point": "U+1F70D", "code_point": 128781}, {"id": "NG2489", "symbol": "🞹", "unicode_point": "U+1F7B9", "code_point": 128953}, {"id": "NG2491", "symbol": "🟳", "unicode_point": "U+1F7F3", "code_point": 129011}, {"id": "NG2492", "symbol": "🡔", "unicode_point": "U+1F854", "code_point": 129108}, {"id": "NG2493", "symbol": "🠪", "unicode_point": "U+1F82A", "code_point": 129066}, {"id": "NG2496", "symbol": "🠦", "unicode_point": "U+1F826", "code_point": 129062}, {"id": "NG2497", "symbol": "🠃", "unicode_point": "U+1F803", "code_point": 129027}, {"id": "NG2498", "symbol": "🢎", "unicode_point": "U+1F88E", "code_point": 129166}, {"id": "NG2500", "symbol": "🟮", "unicode_point": "U+1F7EE", "code_point": 129006}, {"id": "NG2501", "symbol": "🟄", "unicode_point": "U+1F7C4", "code_point": 128964}, {"id": "NG2503", "symbol": "🝕", "unicode_point": "U+1F755", "code_point": 128853}, {"id": "NG2505", "symbol": "🝂", "unicode_point": "U+1F742", "code_point": 128834}, {"id": "NG2507", "symbol": "🢘", "unicode_point": "U+1F898", "code_point": 129176}, {"id": "NG2511", "symbol": "🡨", "unicode_point": "U+1F868", "code_point": 129128}, {"id": "NG2513", "symbol": "🞤", "unicode_point": "U+1F7A4", "code_point": 128932}, {"id": "NG2516", "symbol": "🜊", "unicode_point": "U+1F70A", "code_point": 128778}, {"id": "NG2521", "symbol": "🝩", "unicode_point": "U+1F769", "code_point": 128873}, {"id": "NG2524", "symbol": "🠆", "unicode_point": "U+1F806", "code_point": 129030}, {"id": "NG2525", "symbol": "🝘", "unicode_point": "U+1F758", "code_point": 128856}, {"id": "NG2527", "symbol": "🠈", "unicode_point": "U+1F808", "code_point": 129032}, {"id": "NG2529", "symbol": "🟶", "unicode_point": "U+1F7F6", "code_point": 129014}, {"id": "NG2534", "symbol": "🠉", "unicode_point": "U+1F809", "code_point": 129033}, {"id": "NG2536", "symbol": "🞌", "unicode_point": "U+1F78C", "code_point": 128908}, {"id": "NG2537", "symbol": "🟝", "unicode_point": "U+1F7DD", "code_point": 128989}, {"id": "NG2543", "symbol": "🜄", "unicode_point": "U+1F704", "code_point": 128772}, {"id": "NG2544", "symbol": "🞠", "unicode_point": "U+1F7A0", "code_point": 128928}, {"id": "NG2550", "symbol": "🢀", "unicode_point": "U+1F880", "code_point": 129152}, {"id": "NG2551", "symbol": "🞚", "unicode_point": "U+1F79A", "code_point": 128922}, {"id": "NG2553", "symbol": "🜭", "unicode_point": "U+1F72D", "code_point": 128813}, {"id": "NG2554", "symbol": "🞛", "unicode_point": "U+1F79B", "code_point": 128923}, {"id": "NG2559", "symbol": "🠛", "unicode_point": "U+1F81B", "code_point": 129051}, {"id": "NG2560", "symbol": "🞞", "unicode_point": "U+1F79E", "code_point": 128926}, {"id": "NG2561", "symbol": "🡎", "unicode_point": "U+1F84E", "code_point": 129102}, {"id": "NG2567", "symbol": "🞟", "unicode_point": "U+1F79F", "code_point": 128927}, {"id": "NG2568", "symbol": "🠘", "unicode_point": "U+1F818", "code_point": 129048}, {"id": "NG2569", "symbol": "🞨", "unicode_point": "U+1F7A8", "code_point": 128936}, {"id": "NG2571", "symbol": "🝉", "unicode_point": "U+1F749", "code_point": 128841}, {"id": "NG2575", "symbol": "🜘", "unicode_point": "U+1F718", "code_point": 128792}, {"id": "NG2577", "symbol": "🟔", "unicode_point": "U+1F7D4", "code_point": 128980}, {"id": "NG2578", "symbol": "🟰", "unicode_point": "U+1F7F0", "code_point": 129008}, {"id": "NG2581", "symbol": "🡠", "unicode_point": "U+1F860", "code_point": 129120}, {"id": "NG2584", "symbol": "🞧", "unicode_point": "U+1F7A7", "code_point": 128935}, {"id": "NG2586", "symbol": "🢋", "unicode_point": "U+1F88B", "code_point": 129163}, {"id": "NG2588", "symbol": "🜿", "unicode_point": "U+1F73F", "code_point": 128831}, {"id": "NG2591", "symbol": "🝵", "unicode_point": "U+1F775", "code_point": 128885}, {"id": "NG2592", "symbol": "🜎", "unicode_point": "U+1F70E", "code_point": 128782}, {"id": "NG2596", "symbol": "🡴", "unicode_point": "U+1F874", "code_point": 129140}, {"id": "NG2598", "symbol": "🡞", "unicode_point": "U+1F85E", "code_point": 129118}, {"id": "NG2601", "symbol": "🜳", "unicode_point": "U+1F733", "code_point": 128819}, {"id": "NG2606", "symbol": "🡛", "unicode_point": "U+1F85B", "code_point": 129115}]}, "domain_results": {"total_symbols": 1727, "domain_counts": {"operator": 95, "memory": 94, "logic": 111, "structure": 89, "flow": 102, "reasoning": 55, "advanced_coding": 303, "meta_programming": 62, "distributed_systems": 92, "quantum_computing": 18, "symbolic_ai": 31, "neural_architectures": 64, "formal_verification": 35, "category_theory": 35, "type_theory": 19, "concurrency_advanced": 56, "machine_learning": 89, "mathematical_structures": 46, "philosophical_concepts": 46, "cognitive_modeling": 33, "reserved_expansion": 58, "extension": 194}, "category_counts": {"operator": 95, "memory": 94, "logic": 111, "structure": 89, "flow": 102, "reasoning": 55, "advanced_coding": 303, "meta_programming": 62, "distributed_systems": 92, "quantum_computing": 18, "symbolic_ai": 31, "neural_architectures": 64, "formal_verification": 35, "category_theory": 35, "type_theory": 19, "concurrency_advanced": 56, "machine_learning": 89, "mathematical_structures": 46, "philosophical_concepts": 46, "cognitive_modeling": 33, "reserved_expansion": 58, "extension": 194}, "tier_distribution": {"god": 1727}, "generator_distribution": {"simple": 491, "reasoning_specialized": 55, "god_tier_v1": 936, "ultra_pipeline": 51, "complete_to_2048": 194}}, "quality_results": {"total_symbols": 1727, "score_stats": {"min": 95.0, "max": 100.0, "avg": 97.07596988998263, "below_95": 0}, "token_cost_stats": {"min": 1, "max": 1, "avg": 1.0, "above_2": 0}, "token_density_stats": {"min": 0.9, "max": 1.0, "avg": 0.9494557035321367, "below_09": 0}}}