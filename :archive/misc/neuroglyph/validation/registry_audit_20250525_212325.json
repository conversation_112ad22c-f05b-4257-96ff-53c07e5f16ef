{"audit_summary": {"audit_timestamp": "20250525_212325", "registry_version": "2.0.0", "registry_status": "GOD_TIER_COMPLETE", "total_symbols": 2048, "audit_scores": {"duplicate_check": 0, "integrity_check": 48.7, "unicode_safety": 87.6, "quality_metrics": 97.0, "overall_score": 58.3}, "critical_issues": {"has_duplicates": true, "integrity_issues": 1051, "unsafe_unicode": 233, "low_quality_symbols": 16}, "registry_health": {"excellent": false, "good": false, "acceptable": false, "needs_improvement": true}, "recommendations": ["CRITICO: Rimuovere duplicati dal registry", "Correggere problemi di integrità dati", "Sostituire Unicode non sicuri con range approvati", "Migliorare simboli con validation score < 95.0"]}, "duplicate_results": {"total_symbols": 2048, "duplicate_ids": [], "duplicate_unicode": [{"unicode": "U+25A0", "count": 178}], "duplicate_symbols": [{"symbol": "■", "count": 178}], "duplicate_codes": [], "duplicate_names": [{"name": "error_correction", "count": 2}, {"name": "introspection", "count": 2}, {"name": "superposition", "count": 2}, {"name": "entanglement", "count": 2}, {"name": "theoremproving", "count": 2}, {"name": "theoremproving_op", "count": 2}, {"name": "gradient_flow", "count": 2}, {"name": "layer_normalization", "count": 2}, {"name": "weight_initialization", "count": 2}, {"name": "knowledge_graph", "count": 2}, {"name": "rule_system", "count": 2}, {"name": "description_logic", "count": 2}, {"name": "knowledge_base", "count": 2}, {"name": "production_rule", "count": 2}], "has_duplicates": true}, "integrity_results": {"total_symbols": 2048, "missing_fields": [], "invalid_formats": [], "quality_issues": [{"index": 0, "id": "NG0001", "symbol": "⊕", "issues": ["Missing unicode_point", "Missing name", "Missing category", "High token cost: 999"]}, {"index": 1, "id": "NG0002", "symbol": "⊖", "issues": ["Missing unicode_point", "Missing name", "Missing category", "High token cost: 999"]}, {"index": 2, "id": "NG0003", "symbol": "⊗", "issues": ["Missing unicode_point", "Missing name", "Missing category", "High token cost: 999"]}, {"index": 3, "id": "NG0004", "symbol": "⊘", "issues": ["Missing unicode_point", "Missing name", "Missing category", "High token cost: 999"]}, {"index": 4, "id": "NG0005", "symbol": "≡", "issues": ["Missing unicode_point", "Missing name", "Missing category", "High token cost: 999"]}, {"index": 5, "id": "NG0006", "symbol": "≢", "issues": ["Missing unicode_point", "Missing name", "Missing category", "High token cost: 999"]}, {"index": 6, "id": "NG0007", "symbol": "∧", "issues": ["Missing unicode_point", "Missing name", "Missing category", "High token cost: 999"]}, {"index": 7, "id": "NG0008", "symbol": "∨", "issues": ["Missing unicode_point", "Missing name", "Missing category", "High token cost: 999"]}, {"index": 8, "id": "NG0009", "symbol": "¬", "issues": ["Missing unicode_point", "Missing name", "Missing category", "High token cost: 999"]}, {"index": 9, "id": "NG0010", "symbol": "∈", "issues": ["Missing unicode_point", "Missing name", "Missing category", "High token cost: 999"]}, {"index": 10, "id": "NG0011", "symbol": "∉", "issues": ["Missing unicode_point", "Missing name", "Missing category", "High token cost: 999"]}, {"index": 11, "id": "NG0012", "symbol": "⊨", "issues": ["Missing unicode_point", "Missing name", "Missing category", "High token cost: 999"]}, {"index": 12, "id": "NG0013", "symbol": "⇌", "issues": ["Missing unicode_point", "Missing name", "Missing category", "High token cost: 999"]}, {"index": 13, "id": "NG0014", "symbol": "⟨⟩", "issues": ["Missing unicode_point", "Missing name", "Missing category", "High token cost: 999"]}, {"index": 14, "id": "NG0015", "symbol": "⟪⟫", "issues": ["Missing unicode_point", "Missing name", "Missing category", "High token cost: 999"]}, {"index": 15, "id": "NG0016", "symbol": "◊", "issues": ["Missing unicode_point", "Missing name", "Missing category", "High token cost: 999"]}, {"index": 16, "id": "NG0017", "symbol": "◈", "issues": ["Missing unicode_point", "Missing name", "Missing category", "High token cost: 999"]}, {"index": 17, "id": "NG0018", "symbol": "⟲", "issues": ["Missing unicode_point", "Missing name", "Missing category", "High token cost: 999"]}, {"index": 18, "id": "NG0019", "symbol": "⟳", "issues": ["Missing unicode_point", "Missing name", "Missing category", "High token cost: 999"]}, {"index": 19, "id": "NG0020", "symbol": "⤴", "issues": ["Missing unicode_point", "Missing name", "Missing category", "High token cost: 999"]}, {"index": 27, "id": "NG0028", "symbol": "⊬", "issues": ["Long fallback: 11 chars"]}, {"index": 41, "id": "NG0042", "symbol": "↤", "issues": ["Long fallback: 11 chars"]}, {"index": 42, "id": "NG0043", "symbol": "↰", "issues": ["Long fallback: 11 chars"]}, {"index": 63, "id": "NG0064", "symbol": "⚮", "issues": ["Long fallback: 11 chars"]}, {"index": 77, "id": "NG0078", "symbol": "⬂", "issues": ["Long fallback: 11 chars"]}, {"index": 78, "id": "NG0079", "symbol": "⇦", "issues": ["Long fallback: 11 chars"]}, {"index": 86, "id": "NG0087", "symbol": "⇟", "issues": ["Long fallback: 11 chars"]}, {"index": 92, "id": "NG0093", "symbol": "⦃", "issues": ["Long fallback: 11 chars"]}, {"index": 106, "id": "NG0107", "symbol": "⇅", "issues": ["Long fallback: 11 chars"]}, {"index": 113, "id": "NG0114", "symbol": "↹", "issues": ["Long fallback: 11 chars"]}, {"index": 122, "id": "NG0123", "symbol": "▫", "issues": ["Long fallback: 11 chars"]}, {"index": 128, "id": "NG0129", "symbol": "∰", "issues": ["Long fallback: 11 chars"]}, {"index": 140, "id": "NG0141", "symbol": "⋊", "issues": ["Long fallback: 11 chars"]}, {"index": 169, "id": "NG0170", "symbol": "⬆", "issues": ["Long fallback: 11 chars"]}, {"index": 172, "id": "NG0173", "symbol": "⊀", "issues": ["Long fallback: 11 chars"]}, {"index": 177, "id": "NG0178", "symbol": "↩", "issues": ["Long fallback: 11 chars"]}, {"index": 179, "id": "NG0180", "symbol": "⌇", "issues": ["Long fallback: 11 chars"]}, {"index": 184, "id": "NG0185", "symbol": "⦇", "issues": ["Long fallback: 11 chars"]}, {"index": 185, "id": "NG0186", "symbol": "◐", "issues": ["Long fallback: 11 chars"]}, {"index": 191, "id": "NG0192", "symbol": "≏", "issues": ["Long fallback: 11 chars"]}, {"index": 194, "id": "NG0195", "symbol": "⇚", "issues": ["Long fallback: 12 chars"]}, {"index": 200, "id": "NG0201", "symbol": "∶", "issues": ["Long fallback: 11 chars"]}, {"index": 207, "id": "NG0208", "symbol": "⇢", "issues": ["Long fallback: 11 chars"]}, {"index": 211, "id": "NG0212", "symbol": "◚", "issues": ["Long fallback: 12 chars"]}, {"index": 215, "id": "NG0216", "symbol": "↖", "issues": ["Long fallback: 11 chars"]}, {"index": 216, "id": "NG0217", "symbol": "⋎", "issues": ["Long fallback: 12 chars"]}, {"index": 219, "id": "NG0220", "symbol": "▧", "issues": ["Long fallback: 12 chars"]}, {"index": 222, "id": "NG0223", "symbol": "◽", "issues": ["Long fallback: 11 chars"]}, {"index": 225, "id": "NG0226", "symbol": "▿", "issues": ["Long fallback: 12 chars"]}, {"index": 227, "id": "NG0228", "symbol": "⦉", "issues": ["Long fallback: 12 chars"]}, {"index": 245, "id": "NG0246", "symbol": "◑", "issues": ["Long fallback: 12 chars"]}, {"index": 248, "id": "NG0249", "symbol": "⋜", "issues": ["Long fallback: 11 chars"]}, {"index": 249, "id": "NG0250", "symbol": "∋", "issues": ["Long fallback: 12 chars"]}, {"index": 251, "id": "NG0252", "symbol": "↝", "issues": ["Long fallback: 11 chars"]}, {"index": 267, "id": "NG0268", "symbol": "≄", "issues": ["Long fallback: 12 chars"]}, {"index": 277, "id": "NG0278", "symbol": "⇝", "issues": ["Long fallback: 12 chars"]}, {"index": 282, "id": "NG0283", "symbol": "◍", "issues": ["Long fallback: 12 chars"]}, {"index": 288, "id": "NG0289", "symbol": "⊻", "issues": ["Long fallback: 12 chars"]}, {"index": 299, "id": "NG0300", "symbol": "→", "issues": ["Long fallback: 11 chars"]}, {"index": 309, "id": "NG0310", "symbol": "↶", "issues": ["Long fallback: 12 chars"]}, {"index": 312, "id": "NG0313", "symbol": "⬌", "issues": ["Long fallback: 12 chars"]}, {"index": 317, "id": "NG0318", "symbol": "⋈", "issues": ["Long fallback: 12 chars"]}, {"index": 318, "id": "NG0319", "symbol": "⊾", "issues": ["Long fallback: 12 chars"]}, {"index": 324, "id": "NG0325", "symbol": "∑", "issues": ["Long fallback: 12 chars"]}, {"index": 330, "id": "NG0331", "symbol": "⊆", "issues": ["Long fallback: 12 chars"]}, {"index": 345, "id": "NG0346", "symbol": "⬏", "issues": ["Long fallback: 11 chars"]}, {"index": 351, "id": "NG0352", "symbol": "✐", "issues": ["Long fallback: 11 chars"]}, {"index": 353, "id": "NG0354", "symbol": "⌑", "issues": ["Long fallback: 11 chars"]}, {"index": 360, "id": "NG0361", "symbol": "∐", "issues": ["Long fallback: 11 chars"]}, {"index": 367, "id": "NG0368", "symbol": "◝", "issues": ["Long fallback: 11 chars"]}, {"index": 378, "id": "NG0379", "symbol": "⊝", "issues": ["Long fallback: 11 chars"]}, {"index": 386, "id": "NG0387", "symbol": "⊷", "issues": ["Long fallback: 12 chars"]}, {"index": 406, "id": "NG0407", "symbol": "⬖", "issues": ["Long fallback: 11 chars"]}, {"index": 421, "id": "NG0422", "symbol": "▶", "issues": ["Long fallback: 11 chars"]}, {"index": 431, "id": "NG0432", "symbol": "⨙", "issues": ["Long fallback: 11 chars"]}, {"index": 432, "id": "NG0433", "symbol": "∆", "issues": ["Long fallback: 12 chars"]}, {"index": 437, "id": "NG0438", "symbol": "⋦", "issues": ["Long fallback: 11 chars"]}, {"index": 442, "id": "NG0443", "symbol": "≜", "issues": ["Long fallback: 12 chars"]}, {"index": 445, "id": "NG0446", "symbol": "⊪", "issues": ["Long fallback: 11 chars"]}, {"index": 453, "id": "NG0454", "symbol": "∦", "issues": ["Long fallback: 12 chars"]}, {"index": 454, "id": "NG0455", "symbol": "✚", "issues": ["Long fallback: 12 chars"]}, {"index": 469, "id": "NG0470", "symbol": "▣", "issues": ["Long fallback: 12 chars"]}, {"index": 475, "id": "NG0476", "symbol": "⚹", "issues": ["Long fallback: 12 chars"]}, {"index": 483, "id": "NG0484", "symbol": "⚶", "issues": ["Long fallback: 11 chars"]}, {"index": 488, "id": "NG0489", "symbol": "⊤", "issues": ["Long fallback: 12 chars"]}, {"index": 498, "id": "NG0499", "symbol": "⌞", "issues": ["Long fallback: 12 chars"]}, {"index": 499, "id": "NG0500", "symbol": "≭", "issues": ["Long fallback: 11 chars"]}, {"index": 502, "id": "NG0503", "symbol": "⤞", "issues": ["Long fallback: 11 chars"]}, {"index": 504, "id": "NG0505", "symbol": "⊚", "issues": ["Long fallback: 12 chars"]}, {"index": 511, "id": "NG0512", "symbol": "↼", "issues": ["Long fallback: 12 chars"]}, {"index": 512, "id": "NG0513", "symbol": "⎢", "issues": ["High token cost: 3"]}, {"index": 513, "id": "NG0514", "symbol": "⏐", "issues": ["Low validation score: 93.0", "High token cost: 3"]}, {"index": 514, "id": "NG0515", "symbol": "⎆", "issues": ["High token cost: 3"]}, {"index": 515, "id": "NG0516", "symbol": "⌔", "issues": ["High token cost: 3"]}, {"index": 516, "id": "NG0517", "symbol": "⍸", "issues": ["High token cost: 3"]}, {"index": 517, "id": "NG0518", "symbol": "⧮", "issues": ["High token cost: 3"]}, {"index": 518, "id": "NG0519", "symbol": "⪜", "issues": ["Low validation score: 93.0", "High token cost: 3"]}, {"index": 519, "id": "NG0520", "symbol": "⇡", "issues": ["High token cost: 3"]}, {"index": 520, "id": "NG0521", "symbol": "⊍", "issues": ["Low validation score: 93.0", "High token cost: 3"]}, {"index": 521, "id": "NG0522", "symbol": "⧬", "issues": ["High token cost: 3"]}, {"index": 522, "id": "NG0523", "symbol": "⍛", "issues": ["High token cost: 3"]}, {"index": 523, "id": "NG0524", "symbol": "⏃", "issues": ["Low validation score: 93.0", "High token cost: 3"]}, {"index": 524, "id": "NG0525", "symbol": "⪪", "issues": ["High token cost: 3"]}, {"index": 525, "id": "NG0526", "symbol": "⦵", "issues": ["Low validation score: 93.0", "High token cost: 3"]}, {"index": 526, "id": "NG0527", "symbol": "⧄", "issues": ["High token cost: 3"]}, {"index": 527, "id": "NG0528", "symbol": "⩓", "issues": ["High token cost: 3"]}, {"index": 528, "id": "NG0529", "symbol": "⎭", "issues": ["Low validation score: 93.0", "High token cost: 3"]}, {"index": 529, "id": "NG0530", "symbol": "⪫", "issues": ["High token cost: 3"]}, {"index": 530, "id": "NG0531", "symbol": "⊲", "issues": ["High token cost: 3"]}, {"index": 531, "id": "NG0532", "symbol": "⩢", "issues": ["High token cost: 3"]}, {"index": 532, "id": "NG0533", "symbol": "⪣", "issues": ["Low validation score: 93.0", "High token cost: 3"]}, {"index": 533, "id": "NG0534", "symbol": "⏓", "issues": ["High token cost: 3"]}, {"index": 534, "id": "NG0535", "symbol": "∟", "issues": ["High token cost: 3"]}, {"index": 535, "id": "NG0536", "symbol": "⌾", "issues": ["High token cost: 3"]}, {"index": 536, "id": "NG0537", "symbol": "⎥", "issues": ["High token cost: 3"]}, {"index": 537, "id": "NG0538", "symbol": "⩷", "issues": ["High token cost: 3"]}, {"index": 538, "id": "NG0539", "symbol": "▪", "issues": ["Low validation score: 93.0", "High token cost: 3"]}, {"index": 539, "id": "NG0540", "symbol": "⍝", "issues": ["High token cost: 3"]}, {"index": 540, "id": "NG0541", "symbol": "⪀", "issues": ["High token cost: 3"]}, {"index": 541, "id": "NG0542", "symbol": "⪲", "issues": ["High token cost: 3"]}, {"index": 542, "id": "NG0543", "symbol": "⏟", "issues": ["High token cost: 3"]}, {"index": 543, "id": "NG0544", "symbol": "⎓", "issues": ["High token cost: 3"]}, {"index": 544, "id": "NG0545", "symbol": "≘", "issues": ["High token cost: 3"]}, {"index": 545, "id": "NG0546", "symbol": "⧗", "issues": ["High token cost: 3"]}, {"index": 546, "id": "NG0547", "symbol": "⧓", "issues": ["High token cost: 3"]}, {"index": 547, "id": "NG0548", "symbol": "⎑", "issues": ["Low validation score: 93.0", "High token cost: 3"]}, {"index": 548, "id": "NG0549", "symbol": "⏰", "issues": ["Low validation score: 93.0", "High token cost: 3"]}, {"index": 549, "id": "NG0550", "symbol": "⧯", "issues": ["High token cost: 3"]}, {"index": 550, "id": "NG0551", "symbol": "⎚", "issues": ["High token cost: 3"]}, {"index": 551, "id": "NG0552", "symbol": "⧱", "issues": ["High token cost: 3"]}, {"index": 552, "id": "NG0553", "symbol": "≊", "issues": ["Low validation score: 93.0", "High token cost: 3"]}, {"index": 553, "id": "NG0554", "symbol": "⦸", "issues": ["High token cost: 3"]}, {"index": 554, "id": "NG0555", "symbol": "⎁", "issues": ["Low validation score: 93.0", "High token cost: 3"]}, {"index": 555, "id": "NG0556", "symbol": "⎯", "issues": ["High token cost: 3"]}, {"index": 556, "id": "NG0557", "symbol": "≵", "issues": ["Low validation score: 93.0", "High token cost: 3"]}, {"index": 557, "id": "NG0558", "symbol": "⎧", "issues": ["Low validation score: 93.0", "High token cost: 3"]}, {"index": 558, "id": "NG0559", "symbol": "⎬", "issues": ["High token cost: 3"]}, {"index": 559, "id": "NG0560", "symbol": "⌏", "issues": ["High token cost: 3"]}, {"index": 560, "id": "NG0561", "symbol": "⦗", "issues": ["Low validation score: 93.0", "High token cost: 3"]}, {"index": 561, "id": "NG0562", "symbol": "∏", "issues": ["High token cost: 3"]}, {"index": 562, "id": "NG0563", "symbol": "⫳", "issues": ["High token cost: 3"]}, {"index": 563, "id": "NG0564", "symbol": "⧔", "issues": ["High token cost: 3"]}, {"index": 564, "id": "NG0565", "symbol": "⊮", "issues": ["Low validation score: 93.0", "High token cost: 3"]}, {"index": 565, "id": "NG0566", "symbol": "⫩", "issues": ["High token cost: 3"]}, {"index": 566, "id": "NG0567", "symbol": "⦿", "issues": ["High token cost: 3"]}, {"index": 567, "id": "NG0882", "symbol": "⬟", "issues": ["Long fallback: 16 chars"]}, {"index": 572, "id": "NG0887", "symbol": "⌠", "issues": ["Long fallback: 16 chars"]}, {"index": 575, "id": "NG0890", "symbol": "⦠", "issues": ["Long fallback: 19 chars"]}, {"index": 576, "id": "NG0891", "symbol": "✒", "issues": ["Long fallback: 20 chars"]}, {"index": 577, "id": "NG0892", "symbol": "⨠", "issues": ["Long fallback: 15 chars"]}, {"index": 580, "id": "NG0895", "symbol": "⬠", "issues": ["Long fallback: 13 chars"]}, {"index": 583, "id": "NG0898", "symbol": "⌡", "issues": ["Long fallback: 19 chars"]}, {"index": 584, "id": "NG0899", "symbol": "◁", "issues": ["Long fallback: 20 chars"]}, {"index": 585, "id": "NG0900", "symbol": "✡", "issues": ["Long fallback: 17 chars"]}, {"index": 587, "id": "NG0902", "symbol": "⦡", "issues": ["Long fallback: 13 chars"]}, {"index": 588, "id": "NG0903", "symbol": "⨡", "issues": ["Long fallback: 15 chars"]}, {"index": 589, "id": "NG0904", "symbol": "⬡", "issues": ["Long fallback: 19 chars"]}, {"index": 590, "id": "NG0905", "symbol": "⌢", "issues": ["Long fallback: 20 chars"]}, {"index": 593, "id": "NG0908", "symbol": "✢", "issues": ["Long fallback: 14 chars"]}, {"index": 594, "id": "NG0909", "symbol": "⬢", "issues": ["Long fallback: 15 chars"]}, {"index": 598, "id": "NG0913", "symbol": "⌣", "issues": ["Long fallback: 19 chars"]}, {"index": 599, "id": "NG0914", "symbol": "⨣", "issues": ["Long fallback: 14 chars"]}, {"index": 603, "id": "NG0918", "symbol": "⬣", "issues": ["Long fallback: 15 chars"]}, {"index": 604, "id": "NG0919", "symbol": "⌤", "issues": ["Long fallback: 17 chars"]}, {"index": 606, "id": "NG0921", "symbol": "∥", "issues": ["Long fallback: 17 chars"]}, {"index": 608, "id": "NG0923", "symbol": "⯮", "issues": ["Long fallback: 17 chars"]}, {"index": 609, "id": "NG0924", "symbol": "⌥", "issues": ["Long fallback: 17 chars"]}, {"index": 610, "id": "NG0925", "symbol": "✥", "issues": ["Long fallback: 17 chars"]}, {"index": 613, "id": "NG0928", "symbol": "⬥", "issues": ["Long fallback: 14 chars"]}, {"index": 614, "id": "NG0929", "symbol": "⫏", "issues": ["Long fallback: 17 chars"]}, {"index": 615, "id": "NG0930", "symbol": "⌦", "issues": ["Long fallback: 18 chars"]}, {"index": 616, "id": "NG0931", "symbol": "⨦", "issues": ["Long fallback: 17 chars"]}, {"index": 618, "id": "NG0933", "symbol": "⬦", "issues": ["Long fallback: 15 chars"]}, {"index": 619, "id": "NG0934", "symbol": "⌧", "issues": ["Long fallback: 13 chars"]}, {"index": 620, "id": "NG0935", "symbol": "✧", "issues": ["Long fallback: 14 chars"]}, {"index": 621, "id": "NG0936", "symbol": "⬧", "issues": ["Long fallback: 14 chars"]}, {"index": 624, "id": "NG0939", "symbol": "⌨", "issues": ["Long fallback: 14 chars"]}, {"index": 625, "id": "NG0940", "symbol": "✨", "issues": ["Long fallback: 15 chars"]}, {"index": 627, "id": "NG0942", "symbol": "⤨", "issues": ["Long fallback: 18 chars"]}, {"index": 629, "id": "NG0944", "symbol": "⨨", "issues": ["Long fallback: 13 chars"]}, {"index": 630, "id": "NG0945", "symbol": "⬨", "issues": ["Long fallback: 12 chars"]}, {"index": 633, "id": "NG0948", "symbol": "⦛", "issues": ["Long fallback: 11 chars"]}, {"index": 635, "id": "NG0950", "symbol": "〈", "issues": ["Long fallback: 14 chars"]}, {"index": 636, "id": "NG0951", "symbol": "⨩", "issues": ["Long fallback: 13 chars"]}, {"index": 638, "id": "NG0953", "symbol": "〉", "issues": ["Long fallback: 14 chars"]}, {"index": 641, "id": "NG0956", "symbol": "⤪", "issues": ["Long fallback: 16 chars"]}, {"index": 642, "id": "NG0957", "symbol": "⦪", "issues": ["Long fallback: 15 chars"]}, {"index": 643, "id": "NG0958", "symbol": "⌫", "issues": ["Long fallback: 13 chars"]}, {"index": 644, "id": "NG0959", "symbol": "○", "issues": ["Long fallback: 16 chars"]}, {"index": 646, "id": "NG0961", "symbol": "✫", "issues": ["Long fallback: 14 chars"]}, {"index": 647, "id": "NG0962", "symbol": "⦫", "issues": ["Long fallback: 11 chars"]}, {"index": 648, "id": "NG0963", "symbol": "⨫", "issues": ["Long fallback: 13 chars"]}, {"index": 649, "id": "NG0964", "symbol": "⯜", "issues": ["Long fallback: 13 chars"]}, {"index": 650, "id": "NG0965", "symbol": "∬", "issues": ["Long fallback: 11 chars"]}, {"index": 651, "id": "NG0966", "symbol": "⌬", "issues": ["Long fallback: 11 chars"]}, {"index": 652, "id": "NG0967", "symbol": "⨬", "issues": ["Long fallback: 11 chars"]}, {"index": 653, "id": "NG0968", "symbol": "∭", "issues": ["Long fallback: 19 chars"]}, {"index": 655, "id": "NG0970", "symbol": "❑", "issues": ["Long fallback: 20 chars"]}, {"index": 656, "id": "NG0971", "symbol": "⮷", "issues": ["Long fallback: 20 chars"]}, {"index": 657, "id": "NG0972", "symbol": "⌭", "issues": ["Long fallback: 16 chars"]}, {"index": 659, "id": "NG0974", "symbol": "⣠", "issues": ["Long fallback: 20 chars"]}, {"index": 660, "id": "NG0975", "symbol": "⨭", "issues": ["Long fallback: 17 chars"]}, {"index": 661, "id": "NG0976", "symbol": "⬭", "issues": ["Long fallback: 18 chars"]}, {"index": 663, "id": "NG0978", "symbol": "⨂", "issues": ["Long fallback: 20 chars"]}, {"index": 664, "id": "NG0979", "symbol": "∮", "issues": ["Long fallback: 19 chars"]}, {"index": 665, "id": "NG0980", "symbol": "⌮", "issues": ["Long fallback: 20 chars"]}, {"index": 668, "id": "NG0983", "symbol": "⢖", "issues": ["Long fallback: 18 chars"]}, {"index": 669, "id": "NG0984", "symbol": "⤮", "issues": ["Long fallback: 20 chars"]}, {"index": 670, "id": "NG0985", "symbol": "𝝏", "issues": ["Long fallback: 20 chars"]}, {"index": 671, "id": "NG0986", "symbol": "⤗", "issues": ["Long fallback: 18 chars"]}, {"index": 672, "id": "NG0987", "symbol": "⮐", "issues": ["Long fallback: 18 chars"]}, {"index": 673, "id": "NG0988", "symbol": "✼", "issues": ["Long fallback: 18 chars"]}, {"index": 674, "id": "NG0989", "symbol": "⬮", "issues": ["Long fallback: 20 chars"]}, {"index": 675, "id": "NG0990", "symbol": "⫟", "issues": ["Long fallback: 20 chars"]}, {"index": 676, "id": "NG0991", "symbol": "⮶", "issues": ["Long fallback: 18 chars"]}, {"index": 677, "id": "NG0992", "symbol": "⫄", "issues": ["Long fallback: 16 chars"]}, {"index": 679, "id": "NG0994", "symbol": "⌯", "issues": ["Long fallback: 14 chars"]}, {"index": 681, "id": "NG0996", "symbol": "⦯", "issues": ["Long fallback: 14 chars"]}, {"index": 682, "id": "NG0997", "symbol": "⬯", "issues": ["Long fallback: 16 chars"]}, {"index": 683, "id": "NG0998", "symbol": "⌰", "issues": ["Long fallback: 16 chars"]}, {"index": 684, "id": "NG0999", "symbol": "⤰", "issues": ["Long fallback: 16 chars"]}, {"index": 685, "id": "NG1000", "symbol": "❘", "issues": ["Long fallback: 16 chars"]}, {"index": 686, "id": "NG1001", "symbol": "⬰", "issues": ["Long fallback: 16 chars"]}, {"index": 687, "id": "NG1002", "symbol": "⥸", "issues": ["Long fallback: 17 chars"]}, {"index": 688, "id": "NG1003", "symbol": "⠁", "issues": ["Long fallback: 17 chars"]}, {"index": 689, "id": "NG1004", "symbol": "⌱", "issues": ["Long fallback: 14 chars"]}, {"index": 690, "id": "NG1005", "symbol": "⨱", "issues": ["Long fallback: 12 chars"]}, {"index": 694, "id": "NG1009", "symbol": "❫", "issues": ["Long fallback: 14 chars"]}, {"index": 695, "id": "NG1010", "symbol": "⬱", "issues": ["Long fallback: 12 chars"]}, {"index": 696, "id": "NG1011", "symbol": "❤", "issues": ["Long fallback: 11 chars"]}, {"index": 697, "id": "NG1012", "symbol": "❗", "issues": ["Long fallback: 14 chars"]}, {"index": 698, "id": "NG1013", "symbol": "⌲", "issues": ["Long fallback: 11 chars"]}, {"index": 699, "id": "NG1014", "symbol": "⠮", "issues": ["Long fallback: 15 chars"]}, {"index": 700, "id": "NG1015", "symbol": "✲", "issues": ["Long fallback: 11 chars"]}, {"index": 702, "id": "NG1017", "symbol": "⦲", "issues": ["Long fallback: 20 chars"]}, {"index": 703, "id": "NG1018", "symbol": "⌳", "issues": ["Long fallback: 18 chars"]}, {"index": 705, "id": "NG1020", "symbol": "⥫", "issues": ["Long fallback: 19 chars"]}, {"index": 706, "id": "NG1021", "symbol": "⤳", "issues": ["Long fallback: 18 chars"]}, {"index": 707, "id": "NG1022", "symbol": "⨳", "issues": ["Long fallback: 20 chars"]}, {"index": 708, "id": "NG1023", "symbol": "⫉", "issues": ["Long fallback: 20 chars"]}, {"index": 709, "id": "NG1024", "symbol": "⭙", "issues": ["Long fallback: 19 chars"]}, {"index": 710, "id": "NG1025", "symbol": "⬳", "issues": ["Long fallback: 20 chars"]}, {"index": 711, "id": "NG1026", "symbol": "⫬", "issues": ["Long fallback: 19 chars"]}, {"index": 712, "id": "NG1027", "symbol": "∴", "issues": ["Long fallback: 16 chars"]}, {"index": 713, "id": "NG1028", "symbol": "⠂", "issues": ["Long fallback: 20 chars"]}, {"index": 714, "id": "NG1029", "symbol": "⦙", "issues": ["Long fallback: 20 chars"]}, {"index": 715, "id": "NG1030", "symbol": "⭌", "issues": ["Long fallback: 20 chars"]}, {"index": 716, "id": "NG1031", "symbol": "⌴", "issues": ["Long fallback: 20 chars"]}, {"index": 717, "id": "NG1032", "symbol": "⨚", "issues": ["Long fallback: 20 chars"]}, {"index": 718, "id": "NG1033", "symbol": "⮟", "issues": ["Long fallback: 20 chars"]}, {"index": 719, "id": "NG1034", "symbol": "✳", "issues": ["Long fallback: 20 chars"]}, {"index": 720, "id": "NG1035", "symbol": "⥇", "issues": ["Long fallback: 20 chars"]}, {"index": 721, "id": "NG1036", "symbol": "✴", "issues": ["Long fallback: 15 chars"]}, {"index": 722, "id": "NG1037", "symbol": "⨴", "issues": ["Long fallback: 16 chars"]}, {"index": 723, "id": "NG1038", "symbol": "⢈", "issues": ["Long fallback: 14 chars"]}, {"index": 724, "id": "NG1039", "symbol": "⬴", "issues": ["Long fallback: 17 chars"]}, {"index": 726, "id": "NG1041", "symbol": "∵", "issues": ["Long fallback: 17 chars"]}, {"index": 727, "id": "NG1042", "symbol": "⧹", "issues": ["Long fallback: 17 chars"]}, {"index": 728, "id": "NG1043", "symbol": "⌵", "issues": ["Long fallback: 18 chars"]}, {"index": 729, "id": "NG1044", "symbol": "✵", "issues": ["Long fallback: 18 chars"]}, {"index": 730, "id": "NG1045", "symbol": "⤵", "issues": ["Long fallback: 18 chars"]}, {"index": 731, "id": "NG1046", "symbol": "⨥", "issues": ["Long fallback: 17 chars"]}, {"index": 732, "id": "NG1047", "symbol": "⬵", "issues": ["Long fallback: 17 chars"]}, {"index": 733, "id": "NG1048", "symbol": "⌶", "issues": ["Long fallback: 17 chars"]}, {"index": 734, "id": "NG1049", "symbol": "⯦", "issues": ["Long fallback: 16 chars"]}, {"index": 737, "id": "NG1052", "symbol": "⡘", "issues": ["Long fallback: 17 chars"]}, {"index": 738, "id": "NG1053", "symbol": "✶", "issues": ["Long fallback: 17 chars"]}, {"index": 739, "id": "NG1054", "symbol": "⯨", "issues": ["Long fallback: 14 chars"]}, {"index": 740, "id": "NG1055", "symbol": "⤶", "issues": ["Long fallback: 18 chars"]}, {"index": 741, "id": "NG1056", "symbol": "⦶", "issues": ["Long fallback: 17 chars"]}, {"index": 742, "id": "NG1057", "symbol": "⬶", "issues": ["Long fallback: 18 chars"]}, {"index": 743, "id": "NG1058", "symbol": "⌷", "issues": ["Long fallback: 16 chars"]}, {"index": 744, "id": "NG1059", "symbol": "✷", "issues": ["Long fallback: 18 chars"]}, {"index": 745, "id": "NG1060", "symbol": "❄", "issues": ["Long fallback: 20 chars"]}, {"index": 747, "id": "NG1062", "symbol": "⤷", "issues": ["Long fallback: 20 chars"]}, {"index": 748, "id": "NG1063", "symbol": "⬷", "issues": ["Long fallback: 18 chars"]}, {"index": 750, "id": "NG1065", "symbol": "∸", "issues": ["Long fallback: 19 chars"]}, {"index": 752, "id": "NG1067", "symbol": "⌸", "issues": ["Long fallback: 20 chars"]}, {"index": 753, "id": "NG1068", "symbol": "✞", "issues": ["Long fallback: 17 chars"]}, {"index": 754, "id": "NG1069", "symbol": "✸", "issues": ["Long fallback: 20 chars"]}, {"index": 755, "id": "NG1070", "symbol": "⪒", "issues": ["Long fallback: 19 chars"]}, {"index": 756, "id": "NG1071", "symbol": "⮙", "issues": ["Long fallback: 17 chars"]}, {"index": 758, "id": "NG1073", "symbol": "⤸", "issues": ["Long fallback: 15 chars"]}, {"index": 760, "id": "NG1075", "symbol": "⨸", "issues": ["Long fallback: 17 chars"]}, {"index": 762, "id": "NG1077", "symbol": "⬸", "issues": ["Long fallback: 15 chars"]}, {"index": 763, "id": "NG1078", "symbol": "⌹", "issues": ["Long fallback: 15 chars"]}, {"index": 764, "id": "NG1079", "symbol": "⤹", "issues": ["Long fallback: 13 chars"]}, {"index": 765, "id": "NG1080", "symbol": "⦹", "issues": ["Long fallback: 13 chars"]}, {"index": 766, "id": "NG1081", "symbol": "⦓", "issues": ["Long fallback: 17 chars"]}, {"index": 768, "id": "NG1083", "symbol": "⌺", "issues": ["Long fallback: 15 chars"]}, {"index": 769, "id": "NG1084", "symbol": "✺", "issues": ["Long fallback: 16 chars"]}, {"index": 770, "id": "NG1085", "symbol": "⤺", "issues": ["Long fallback: 14 chars"]}, {"index": 771, "id": "NG1086", "symbol": "⨺", "issues": ["Long fallback: 16 chars"]}, {"index": 772, "id": "NG1087", "symbol": "∻", "issues": ["Long fallback: 16 chars"]}, {"index": 773, "id": "NG1088", "symbol": "⌻", "issues": ["Long fallback: 17 chars"]}, {"index": 774, "id": "NG1089", "symbol": "⤻", "issues": ["Long fallback: 16 chars"]}, {"index": 775, "id": "NG1090", "symbol": "⨻", "issues": ["Long fallback: 17 chars"]}, {"index": 776, "id": "NG1091", "symbol": "⪥", "issues": ["Long fallback: 17 chars"]}, {"index": 777, "id": "NG1092", "symbol": "⬻", "issues": ["Long fallback: 17 chars"]}, {"index": 778, "id": "NG1093", "symbol": "⌼", "issues": ["Long fallback: 12 chars"]}, {"index": 779, "id": "NG1094", "symbol": "⤼", "issues": ["Long fallback: 17 chars"]}, {"index": 780, "id": "NG1095", "symbol": "⨼", "issues": ["Long fallback: 17 chars"]}, {"index": 781, "id": "NG1096", "symbol": "⬼", "issues": ["Long fallback: 15 chars"]}, {"index": 783, "id": "NG1098", "symbol": "∽", "issues": ["Long fallback: 14 chars"]}, {"index": 784, "id": "NG1099", "symbol": "⌽", "issues": ["Long fallback: 11 chars"]}, {"index": 785, "id": "NG1100", "symbol": "⫍", "issues": ["Long fallback: 16 chars"]}, {"index": 786, "id": "NG1101", "symbol": "⭼", "issues": ["Long fallback: 15 chars"]}, {"index": 787, "id": "NG1102", "symbol": "⤽", "issues": ["Long fallback: 16 chars"]}, {"index": 788, "id": "NG1103", "symbol": "⪨", "issues": ["Long fallback: 12 chars"]}, {"index": 789, "id": "NG1104", "symbol": "✏", "issues": ["Long fallback: 16 chars"]}, {"index": 790, "id": "NG1105", "symbol": "⭃", "issues": ["Long fallback: 16 chars"]}, {"index": 791, "id": "NG1106", "symbol": "⥘", "issues": ["Long fallback: 14 chars"]}, {"index": 792, "id": "NG1107", "symbol": "⦽", "issues": ["Long fallback: 15 chars"]}, {"index": 794, "id": "NG1109", "symbol": "⬽", "issues": ["Long fallback: 20 chars"]}, {"index": 795, "id": "NG1110", "symbol": "✾", "issues": ["Long fallback: 19 chars"]}, {"index": 796, "id": "NG1111", "symbol": "⤾", "issues": ["Long fallback: 20 chars"]}, {"index": 797, "id": "NG1112", "symbol": "⢧", "issues": ["Long fallback: 19 chars"]}, {"index": 799, "id": "NG1114", "symbol": "⨾", "issues": ["Long fallback: 16 chars"]}, {"index": 800, "id": "NG1115", "symbol": "⬾", "issues": ["Long fallback: 19 chars"]}, {"index": 801, "id": "NG1116", "symbol": "⌿", "issues": ["Long fallback: 20 chars"]}, {"index": 802, "id": "NG1117", "symbol": "✿", "issues": ["Long fallback: 17 chars"]}, {"index": 803, "id": "NG1118", "symbol": "⤿", "issues": ["Long fallback: 20 chars"]}, {"index": 804, "id": "NG1119", "symbol": "≀", "issues": ["Long fallback: 20 chars"]}, {"index": 805, "id": "NG1120", "symbol": "⍀", "issues": ["Long fallback: 16 chars"]}, {"index": 806, "id": "NG1121", "symbol": "⧀", "issues": ["Long fallback: 14 chars"]}, {"index": 807, "id": "NG1122", "symbol": "⩀", "issues": ["Long fallback: 17 chars"]}, {"index": 808, "id": "NG1123", "symbol": "⧳", "issues": ["Long fallback: 17 chars"]}, {"index": 809, "id": "NG1124", "symbol": "⭀", "issues": ["Long fallback: 17 chars"]}, {"index": 810, "id": "NG1125", "symbol": "⍁", "issues": ["Long fallback: 17 chars"]}, {"index": 811, "id": "NG1126", "symbol": "⦢", "issues": ["Long fallback: 17 chars"]}, {"index": 813, "id": "NG1128", "symbol": "⧁", "issues": ["Long fallback: 17 chars"]}, {"index": 814, "id": "NG1129", "symbol": "⩁", "issues": ["Long fallback: 16 chars"]}, {"index": 816, "id": "NG1131", "symbol": "➬", "issues": ["Long fallback: 16 chars"]}, {"index": 817, "id": "NG1132", "symbol": "⭁", "issues": ["Long fallback: 19 chars"]}, {"index": 818, "id": "NG1133", "symbol": "≂", "issues": ["Long fallback: 20 chars"]}, {"index": 819, "id": "NG1134", "symbol": "⯄", "issues": ["Long fallback: 18 chars"]}, {"index": 820, "id": "NG1135", "symbol": "⍂", "issues": ["Long fallback: 20 chars"]}, {"index": 821, "id": "NG1136", "symbol": "⥂", "issues": ["Long fallback: 20 chars"]}, {"index": 822, "id": "NG1137", "symbol": "⭂", "issues": ["Long fallback: 18 chars"]}, {"index": 824, "id": "NG1139", "symbol": "⍃", "issues": ["Long fallback: 18 chars"]}, {"index": 825, "id": "NG1140", "symbol": "⧃", "issues": ["Long fallback: 20 chars"]}, {"index": 826, "id": "NG1141", "symbol": "⩃", "issues": ["Long fallback: 20 chars"]}, {"index": 827, "id": "NG1142", "symbol": "⍄", "issues": ["Long fallback: 20 chars"]}, {"index": 828, "id": "NG1143", "symbol": "✩", "issues": ["Long fallback: 20 chars"]}, {"index": 829, "id": "NG1144", "symbol": "⩄", "issues": ["Long fallback: 17 chars"]}, {"index": 831, "id": "NG1146", "symbol": "⢣", "issues": ["Long fallback: 16 chars"]}, {"index": 832, "id": "NG1147", "symbol": "⭄", "issues": ["Long fallback: 18 chars"]}, {"index": 833, "id": "NG1148", "symbol": "≅", "issues": ["Long fallback: 18 chars"]}, {"index": 834, "id": "NG1149", "symbol": "⥣", "issues": ["Long fallback: 16 chars"]}, {"index": 835, "id": "NG1150", "symbol": "⍅", "issues": ["Long fallback: 15 chars"]}, {"index": 836, "id": "NG1151", "symbol": "❅", "issues": ["Long fallback: 18 chars"]}, {"index": 838, "id": "NG1153", "symbol": "⠿", "issues": ["Long fallback: 16 chars"]}, {"index": 839, "id": "NG1154", "symbol": "⩅", "issues": ["Long fallback: 18 chars"]}, {"index": 840, "id": "NG1155", "symbol": "⦣", "issues": ["Long fallback: 18 chars"]}, {"index": 842, "id": "NG1157", "symbol": "⯛", "issues": ["Long fallback: 18 chars"]}, {"index": 843, "id": "NG1158", "symbol": "≆", "issues": ["Long fallback: 17 chars"]}, {"index": 844, "id": "NG1159", "symbol": "⍆", "issues": ["Long fallback: 14 chars"]}, {"index": 845, "id": "NG1160", "symbol": "⣃", "issues": ["Long fallback: 18 chars"]}, {"index": 846, "id": "NG1161", "symbol": "⥆", "issues": ["Long fallback: 17 chars"]}, {"index": 847, "id": "NG1162", "symbol": "⨰", "issues": ["Long fallback: 14 chars"]}, {"index": 848, "id": "NG1163", "symbol": "❍", "issues": ["Long fallback: 16 chars"]}, {"index": 849, "id": "NG1164", "symbol": "⯅", "issues": ["Long fallback: 16 chars"]}, {"index": 850, "id": "NG1165", "symbol": "⧆", "issues": ["Long fallback: 18 chars"]}, {"index": 851, "id": "NG1166", "symbol": "⭆", "issues": ["Long fallback: 18 chars"]}, {"index": 852, "id": "NG1167", "symbol": "⥙", "issues": ["Long fallback: 17 chars"]}, {"index": 853, "id": "NG1168", "symbol": "⥝", "issues": ["Long fallback: 18 chars"]}, {"index": 854, "id": "NG1169", "symbol": "⤢", "issues": ["Long fallback: 14 chars"]}, {"index": 855, "id": "NG1170", "symbol": "⍇", "issues": ["Long fallback: 16 chars"]}, {"index": 856, "id": "NG1171", "symbol": "⮈", "issues": ["Long fallback: 16 chars"]}, {"index": 857, "id": "NG1172", "symbol": "❇", "issues": ["Long fallback: 17 chars"]}, {"index": 858, "id": "NG1173", "symbol": "⧩", "issues": ["Long fallback: 18 chars"]}, {"index": 859, "id": "NG1174", "symbol": "⧇", "issues": ["Long fallback: 18 chars"]}, {"index": 860, "id": "NG1175", "symbol": "⭇", "issues": ["Long fallback: 18 chars"]}, {"index": 861, "id": "NG1176", "symbol": "≈", "issues": ["Long fallback: 18 chars"]}, {"index": 862, "id": "NG1177", "symbol": "⍈", "issues": ["Long fallback: 17 chars"]}, {"index": 863, "id": "NG1178", "symbol": "❠", "issues": ["Long fallback: 17 chars"]}, {"index": 864, "id": "NG1179", "symbol": "⭈", "issues": ["Long fallback: 18 chars"]}, {"index": 865, "id": "NG1180", "symbol": "⍉", "issues": ["Long fallback: 18 chars"]}, {"index": 866, "id": "NG1181", "symbol": "⧷", "issues": ["Long fallback: 16 chars"]}, {"index": 867, "id": "NG1182", "symbol": "⩉", "issues": ["Long fallback: 16 chars"]}, {"index": 868, "id": "NG1183", "symbol": "⭉", "issues": ["Long fallback: 18 chars"]}, {"index": 869, "id": "NG1184", "symbol": "⍊", "issues": ["Long fallback: 18 chars"]}, {"index": 870, "id": "NG1185", "symbol": "❊", "issues": ["Long fallback: 18 chars"]}, {"index": 871, "id": "NG1186", "symbol": "⥊", "issues": ["Long fallback: 18 chars"]}, {"index": 872, "id": "NG1187", "symbol": "⠒", "issues": ["Long fallback: 18 chars"]}, {"index": 876, "id": "NG1191", "symbol": "⧊", "issues": ["Long fallback: 16 chars"]}, {"index": 878, "id": "NG1193", "symbol": "⩊", "issues": ["Long fallback: 14 chars"]}, {"index": 879, "id": "NG1194", "symbol": "⯚", "issues": ["Long fallback: 13 chars"]}, {"index": 880, "id": "NG1195", "symbol": "⣹", "issues": ["Long fallback: 13 chars"]}, {"index": 881, "id": "NG1196", "symbol": "⭊", "issues": ["Long fallback: 13 chars"]}, {"index": 883, "id": "NG1198", "symbol": "⥍", "issues": ["Long fallback: 17 chars"]}, {"index": 884, "id": "NG1199", "symbol": "⍋", "issues": ["Long fallback: 15 chars"]}, {"index": 885, "id": "NG1200", "symbol": "⥋", "issues": ["Long fallback: 13 chars"]}, {"index": 886, "id": "NG1201", "symbol": "⧋", "issues": ["Long fallback: 16 chars"]}, {"index": 887, "id": "NG1202", "symbol": "⩋", "issues": ["Long fallback: 18 chars"]}, {"index": 888, "id": "NG1203", "symbol": "⭋", "issues": ["Long fallback: 19 chars"]}, {"index": 889, "id": "NG1204", "symbol": "≌", "issues": ["Long fallback: 18 chars"]}, {"index": 890, "id": "NG1205", "symbol": "⍌", "issues": ["Long fallback: 16 chars"]}, {"index": 891, "id": "NG1206", "symbol": "✽", "issues": ["Long fallback: 19 chars"]}, {"index": 892, "id": "NG1207", "symbol": "❌", "issues": ["Long fallback: 14 chars"]}, {"index": 894, "id": "NG1209", "symbol": "⥌", "issues": ["Long fallback: 15 chars"]}, {"index": 895, "id": "NG1210", "symbol": "≍", "issues": ["Long fallback: 15 chars"]}, {"index": 897, "id": "NG1212", "symbol": "⦭", "issues": ["Long fallback: 17 chars"]}, {"index": 898, "id": "NG1213", "symbol": "⥅", "issues": ["Long fallback: 15 chars"]}, {"index": 899, "id": "NG1214", "symbol": "⢹", "issues": ["Long fallback: 17 chars"]}, {"index": 900, "id": "NG1215", "symbol": "⧛", "issues": ["Long fallback: 19 chars"]}, {"index": 901, "id": "NG1216", "symbol": "⍍", "issues": ["Long fallback: 19 chars"]}, {"index": 902, "id": "NG1217", "symbol": "⧍", "issues": ["Long fallback: 20 chars"]}, {"index": 903, "id": "NG1218", "symbol": "⮜", "issues": ["Long fallback: 20 chars"]}, {"index": 904, "id": "NG1219", "symbol": "⩍", "issues": ["Long fallback: 20 chars"]}, {"index": 905, "id": "NG1220", "symbol": "⭍", "issues": ["Long fallback: 20 chars"]}, {"index": 906, "id": "NG1221", "symbol": "⬬", "issues": ["Long fallback: 20 chars"]}, {"index": 907, "id": "NG1222", "symbol": "⍎", "issues": ["Long fallback: 20 chars"]}, {"index": 908, "id": "NG1223", "symbol": "❎", "issues": ["Long fallback: 20 chars"]}, {"index": 909, "id": "NG1224", "symbol": "⮘", "issues": ["Long fallback: 20 chars"]}, {"index": 910, "id": "NG1225", "symbol": "⭎", "issues": ["Long fallback: 20 chars"]}, {"index": 911, "id": "NG1226", "symbol": "⣘", "issues": ["Long fallback: 20 chars"]}, {"index": 912, "id": "NG1227", "symbol": "⍏", "issues": ["Long fallback: 20 chars"]}, {"index": 913, "id": "NG1228", "symbol": "❏", "issues": ["Long fallback: 20 chars"]}, {"index": 914, "id": "NG1229", "symbol": "⧏", "issues": ["Long fallback: 20 chars"]}, {"index": 915, "id": "NG1230", "symbol": "⭏", "issues": ["Long fallback: 20 chars"]}, {"index": 916, "id": "NG1231", "symbol": "⍐", "issues": ["Long fallback: 20 chars"]}, {"index": 917, "id": "NG1232", "symbol": "⣂", "issues": ["Long fallback: 20 chars"]}, {"index": 918, "id": "NG1233", "symbol": "⡆", "issues": ["Long fallback: 20 chars"]}, {"index": 919, "id": "NG1234", "symbol": "❐", "issues": ["Long fallback: 20 chars"]}, {"index": 923, "id": "NG1238", "symbol": "⥐", "issues": ["Long fallback: 19 chars"]}, {"index": 924, "id": "NG1239", "symbol": "⧐", "issues": ["Long fallback: 19 chars"]}, {"index": 925, "id": "NG1240", "symbol": "⍑", "issues": ["Long fallback: 19 chars"]}, {"index": 926, "id": "NG1241", "symbol": "⦴", "issues": ["Long fallback: 19 chars"]}, {"index": 927, "id": "NG1242", "symbol": "⥑", "issues": ["Long fallback: 19 chars"]}, {"index": 928, "id": "NG1243", "symbol": "⩑", "issues": ["Long fallback: 19 chars"]}, {"index": 929, "id": "NG1244", "symbol": "⣩", "issues": ["Long fallback: 19 chars"]}, {"index": 930, "id": "NG1245", "symbol": "≒", "issues": ["Long fallback: 19 chars"]}, {"index": 931, "id": "NG1246", "symbol": "❥", "issues": ["Long fallback: 19 chars"]}, {"index": 932, "id": "NG1247", "symbol": "⍒", "issues": ["Long fallback: 20 chars"]}, {"index": 933, "id": "NG1248", "symbol": "⧥", "issues": ["Long fallback: 20 chars"]}, {"index": 937, "id": "NG1252", "symbol": "❒", "issues": ["Long fallback: 11 chars"]}, {"index": 941, "id": "NG1256", "symbol": "⧒", "issues": ["Long fallback: 14 chars"]}, {"index": 942, "id": "NG1257", "symbol": "⭒", "issues": ["Long fallback: 20 chars"]}, {"index": 944, "id": "NG1259", "symbol": "⧴", "issues": ["Long fallback: 19 chars"]}, {"index": 945, "id": "NG1260", "symbol": "⍓", "issues": ["Long fallback: 19 chars"]}, {"index": 946, "id": "NG1261", "symbol": "⤡", "issues": ["Long fallback: 19 chars"]}, {"index": 947, "id": "NG1262", "symbol": "⨋", "issues": ["Long fallback: 19 chars"]}, {"index": 949, "id": "NG1264", "symbol": "⥓", "issues": ["Long fallback: 18 chars"]}, {"index": 950, "id": "NG1265", "symbol": "⭓", "issues": ["Long fallback: 17 chars"]}, {"index": 951, "id": "NG1266", "symbol": "⮊", "issues": ["Long fallback: 17 chars"]}, {"index": 953, "id": "NG1268", "symbol": "⍔", "issues": ["Long fallback: 18 chars"]}, {"index": 955, "id": "NG1270", "symbol": "⥬", "issues": ["Long fallback: 17 chars"]}, {"index": 956, "id": "NG1271", "symbol": "❔", "issues": ["Long fallback: 14 chars"]}, {"index": 959, "id": "NG1274", "symbol": "⡪", "issues": ["Long fallback: 17 chars"]}, {"index": 960, "id": "NG1275", "symbol": "⩔", "issues": ["Long fallback: 19 chars"]}, {"index": 961, "id": "NG1276", "symbol": "⡡", "issues": ["Long fallback: 18 chars"]}, {"index": 963, "id": "NG1278", "symbol": "⣙", "issues": ["Long fallback: 20 chars"]}, {"index": 965, "id": "NG1280", "symbol": "⭔", "issues": ["Long fallback: 20 chars"]}, {"index": 966, "id": "NG1281", "symbol": "⪾", "issues": ["Long fallback: 20 chars"]}, {"index": 968, "id": "NG1283", "symbol": "⍕", "issues": ["Long fallback: 20 chars"]}, {"index": 969, "id": "NG1284", "symbol": "❕", "issues": ["Long fallback: 20 chars"]}, {"index": 971, "id": "NG1286", "symbol": "⩕", "issues": ["Long fallback: 18 chars"]}, {"index": 972, "id": "NG1287", "symbol": "≖", "issues": ["Long fallback: 20 chars"]}, {"index": 973, "id": "NG1288", "symbol": "⦷", "issues": ["Long fallback: 19 chars"]}, {"index": 975, "id": "NG1290", "symbol": "⍖", "issues": ["Long fallback: 14 chars"]}, {"index": 976, "id": "NG1291", "symbol": "❖", "issues": ["Long fallback: 12 chars"]}, {"index": 977, "id": "NG1292", "symbol": "⥖", "issues": ["Long fallback: 13 chars"]}, {"index": 979, "id": "NG1294", "symbol": "⭖", "issues": ["Long fallback: 15 chars"]}, {"index": 980, "id": "NG1295", "symbol": "⯊", "issues": ["Long fallback: 13 chars"]}, {"index": 981, "id": "NG1296", "symbol": "⍗", "issues": ["Long fallback: 13 chars"]}, {"index": 982, "id": "NG1297", "symbol": "⭗", "issues": ["Long fallback: 14 chars"]}, {"index": 984, "id": "NG1299", "symbol": "⍘", "issues": ["Long fallback: 16 chars"]}, {"index": 985, "id": "NG1300", "symbol": "⩘", "issues": ["Long fallback: 17 chars"]}, {"index": 988, "id": "NG1303", "symbol": "⭘", "issues": ["Long fallback: 14 chars"]}, {"index": 989, "id": "NG1304", "symbol": "⠋", "issues": ["Long fallback: 17 chars"]}, {"index": 990, "id": "NG1305", "symbol": "⍙", "issues": ["Long fallback: 15 chars"]}, {"index": 991, "id": "NG1306", "symbol": "⩙", "issues": ["Long fallback: 16 chars"]}, {"index": 992, "id": "NG1307", "symbol": "⍚", "issues": ["Long fallback: 18 chars"]}, {"index": 993, "id": "NG1308", "symbol": "⥚", "issues": ["Long fallback: 17 chars"]}, {"index": 997, "id": "NG1312", "symbol": "⦏", "issues": ["Long fallback: 20 chars"]}, {"index": 998, "id": "NG1313", "symbol": "⩚", "issues": ["Long fallback: 19 chars"]}, {"index": 1001, "id": "NG1316", "symbol": "≛", "issues": ["Long fallback: 17 chars"]}, {"index": 1003, "id": "NG1318", "symbol": "⢳", "issues": ["Long fallback: 18 chars"]}, {"index": 1004, "id": "NG1319", "symbol": "⭛", "issues": ["Long fallback: 18 chars"]}, {"index": 1005, "id": "NG1320", "symbol": "⍜", "issues": ["Long fallback: 20 chars"]}, {"index": 1006, "id": "NG1321", "symbol": "⫵", "issues": ["Long fallback: 20 chars"]}, {"index": 1007, "id": "NG1322", "symbol": "❡", "issues": ["Long fallback: 20 chars"]}, {"index": 1008, "id": "NG1323", "symbol": "⥜", "issues": ["Long fallback: 18 chars"]}, {"index": 1009, "id": "NG1324", "symbol": "⩜", "issues": ["Long fallback: 20 chars"]}, {"index": 1010, "id": "NG1325", "symbol": "⩆", "issues": ["Long fallback: 20 chars"]}, {"index": 1011, "id": "NG1326", "symbol": "❃", "issues": ["Long fallback: 20 chars"]}, {"index": 1012, "id": "NG1327", "symbol": "❀", "issues": ["Long fallback: 20 chars"]}, {"index": 1013, "id": "NG1328", "symbol": "⧝", "issues": ["Long fallback: 20 chars"]}, {"index": 1014, "id": "NG1329", "symbol": "⢩", "issues": ["Long fallback: 20 chars"]}, {"index": 1015, "id": "NG1330", "symbol": "≞", "issues": ["Long fallback: 18 chars"]}, {"index": 1016, "id": "NG1331", "symbol": "⮢", "issues": ["Long fallback: 20 chars"]}, {"index": 1017, "id": "NG1332", "symbol": "⡾", "issues": ["Long fallback: 20 chars"]}, {"index": 1018, "id": "NG1333", "symbol": "⍞", "issues": ["Long fallback: 20 chars"]}, {"index": 1019, "id": "NG1334", "symbol": "⤦", "issues": ["Long fallback: 20 chars"]}, {"index": 1020, "id": "NG1335", "symbol": "◾", "issues": ["Long fallback: 20 chars"]}, {"index": 1021, "id": "NG1336", "symbol": "✦", "issues": ["Long fallback: 20 chars"]}, {"index": 1022, "id": "NG1337", "symbol": "⭞", "issues": ["Long fallback: 18 chars"]}, {"index": 1023, "id": "NG1338", "symbol": "≟", "issues": ["Long fallback: 18 chars"]}, {"index": 1024, "id": "NG1339", "symbol": "⍟", "issues": ["Long fallback: 18 chars"]}, {"index": 1025, "id": "NG1340", "symbol": "❟", "issues": ["Long fallback: 18 chars"]}, {"index": 1026, "id": "NG1341", "symbol": "⥎", "issues": ["Long fallback: 19 chars"]}, {"index": 1027, "id": "NG1342", "symbol": "⍠", "issues": ["Long fallback: 19 chars"]}, {"index": 1028, "id": "NG1343", "symbol": "⩠", "issues": ["Long fallback: 19 chars"]}, {"index": 1029, "id": "NG1344", "symbol": "⦑", "issues": ["Long fallback: 19 chars"]}, {"index": 1030, "id": "NG1345", "symbol": "⭠", "issues": ["Long fallback: 20 chars"]}, {"index": 1031, "id": "NG1346", "symbol": "⍡", "issues": ["Long fallback: 19 chars"]}, {"index": 1032, "id": "NG1347", "symbol": "⨿", "issues": ["Long fallback: 20 chars"]}, {"index": 1033, "id": "NG1348", "symbol": "❓", "issues": ["Long fallback: 19 chars"]}, {"index": 1034, "id": "NG1349", "symbol": "⩡", "issues": ["Long fallback: 19 chars"]}, {"index": 1035, "id": "NG1350", "symbol": "⭡", "issues": ["Long fallback: 19 chars"]}, {"index": 1036, "id": "NG1351", "symbol": "⍢", "issues": ["Long fallback: 19 chars"]}, {"index": 1037, "id": "NG1352", "symbol": "⮕", "issues": ["Long fallback: 19 chars"]}, {"index": 1038, "id": "NG1353", "symbol": "❢", "issues": ["Long fallback: 19 chars"]}, {"index": 1039, "id": "NG1354", "symbol": "⪧", "issues": ["Long fallback: 19 chars"]}, {"index": 1040, "id": "NG1355", "symbol": "≣", "issues": ["Long fallback: 19 chars"]}, {"index": 1041, "id": "NG1356", "symbol": "⭑", "issues": ["Long fallback: 19 chars"]}, {"index": 1042, "id": "NG1357", "symbol": "⍣", "issues": ["Long fallback: 19 chars"]}, {"index": 1051, "id": "NG1366", "symbol": "⩣", "issues": ["Long fallback: 20 chars"]}, {"index": 1052, "id": "NG1367", "symbol": "⥰", "issues": ["Long fallback: 20 chars"]}, {"index": 1053, "id": "NG1368", "symbol": "⭣", "issues": ["Long fallback: 20 chars"]}, {"index": 1054, "id": "NG1369", "symbol": "≤", "issues": ["Long fallback: 20 chars"]}, {"index": 1057, "id": "NG1372", "symbol": "⍤", "issues": ["Long fallback: 16 chars"]}, {"index": 1058, "id": "NG1373", "symbol": "⥤", "issues": ["Long fallback: 17 chars"]}, {"index": 1059, "id": "NG1374", "symbol": "⦨", "issues": ["Long fallback: 16 chars"]}, {"index": 1061, "id": "NG1376", "symbol": "⩤", "issues": ["Long fallback: 16 chars"]}, {"index": 1064, "id": "NG1379", "symbol": "✃", "issues": ["Long fallback: 17 chars"]}, {"index": 1067, "id": "NG1382", "symbol": "⍥", "issues": ["Long fallback: 20 chars"]}, {"index": 1068, "id": "NG1383", "symbol": "⥥", "issues": ["Long fallback: 20 chars"]}, {"index": 1069, "id": "NG1384", "symbol": "⭸", "issues": ["Long fallback: 20 chars"]}, {"index": 1070, "id": "NG1385", "symbol": "⭤", "issues": ["Long fallback: 20 chars"]}, {"index": 1071, "id": "NG1386", "symbol": "⩥", "issues": ["Long fallback: 20 chars"]}, {"index": 1072, "id": "NG1387", "symbol": "⭥", "issues": ["Long fallback: 20 chars"]}, {"index": 1073, "id": "NG1388", "symbol": "≦", "issues": ["Long fallback: 18 chars"]}, {"index": 1074, "id": "NG1389", "symbol": "⍦", "issues": ["Long fallback: 19 chars"]}, {"index": 1075, "id": "NG1390", "symbol": "✹", "issues": ["Long fallback: 19 chars"]}, {"index": 1076, "id": "NG1391", "symbol": "⤓", "issues": ["Long fallback: 19 chars"]}, {"index": 1077, "id": "NG1392", "symbol": "❦", "issues": ["Long fallback: 19 chars"]}, {"index": 1080, "id": "NG1395", "symbol": "⫴", "issues": ["Long fallback: 20 chars"]}, {"index": 1082, "id": "NG1397", "symbol": "⩖", "issues": ["Long fallback: 20 chars"]}, {"index": 1083, "id": "NG1398", "symbol": "⥛", "issues": ["Long fallback: 17 chars"]}, {"index": 1084, "id": "NG1399", "symbol": "⥦", "issues": ["Long fallback: 20 chars"]}, {"index": 1085, "id": "NG1400", "symbol": "⧦", "issues": ["Long fallback: 20 chars"]}, {"index": 1086, "id": "NG1401", "symbol": "≧", "issues": ["Long fallback: 19 chars"]}, {"index": 1087, "id": "NG1402", "symbol": "⍧", "issues": ["Long fallback: 19 chars"]}, {"index": 1088, "id": "NG1403", "symbol": "❧", "issues": ["Long fallback: 20 chars"]}, {"index": 1089, "id": "NG1404", "symbol": "⥀", "issues": ["Long fallback: 20 chars"]}, {"index": 1090, "id": "NG1405", "symbol": "⧎", "issues": ["Long fallback: 20 chars"]}, {"index": 1091, "id": "NG1406", "symbol": "⥧", "issues": ["Long fallback: 18 chars"]}, {"index": 1092, "id": "NG1407", "symbol": "⧧", "issues": ["Long fallback: 17 chars"]}, {"index": 1093, "id": "NG1408", "symbol": "⥶", "issues": ["Long fallback: 20 chars"]}, {"index": 1094, "id": "NG1409", "symbol": "⭧", "issues": ["Long fallback: 20 chars"]}, {"index": 1095, "id": "NG1410", "symbol": "≨", "issues": ["Long fallback: 19 chars"]}, {"index": 1096, "id": "NG1411", "symbol": "⍨", "issues": ["Long fallback: 19 chars"]}, {"index": 1097, "id": "NG1412", "symbol": "❨", "issues": ["Long fallback: 19 chars"]}, {"index": 1098, "id": "NG1413", "symbol": "✌", "issues": ["Long fallback: 19 chars"]}, {"index": 1099, "id": "NG1414", "symbol": "⭨", "issues": ["Long fallback: 19 chars"]}, {"index": 1100, "id": "NG1415", "symbol": "⥒", "issues": ["Long fallback: 19 chars"]}, {"index": 1102, "id": "NG1417", "symbol": "⬛", "issues": ["Long fallback: 16 chars"]}, {"index": 1104, "id": "NG1419", "symbol": "➪", "issues": ["Long fallback: 16 chars"]}, {"index": 1105, "id": "NG1420", "symbol": "≩", "issues": ["Long fallback: 16 chars"]}, {"index": 1106, "id": "NG1421", "symbol": "⍩", "issues": ["Long fallback: 17 chars"]}, {"index": 1107, "id": "NG1422", "symbol": "⭩", "issues": ["Long fallback: 20 chars"]}, {"index": 1108, "id": "NG1423", "symbol": "⍪", "issues": ["Long fallback: 20 chars"]}, {"index": 1109, "id": "NG1424", "symbol": "❪", "issues": ["Long fallback: 20 chars"]}, {"index": 1110, "id": "NG1425", "symbol": "⧪", "issues": ["Long fallback: 20 chars"]}, {"index": 1111, "id": "NG1426", "symbol": "⩪", "issues": ["Long fallback: 20 chars"]}, {"index": 1112, "id": "NG1427", "symbol": "⥡", "issues": ["Long fallback: 20 chars"]}, {"index": 1113, "id": "NG1428", "symbol": "⍫", "issues": ["Long fallback: 20 chars"]}, {"index": 1115, "id": "NG1430", "symbol": "⩫", "issues": ["Long fallback: 14 chars"]}, {"index": 1116, "id": "NG1431", "symbol": "⭫", "issues": ["Long fallback: 15 chars"]}, {"index": 1118, "id": "NG1433", "symbol": "⍬", "issues": ["Long fallback: 14 chars"]}, {"index": 1120, "id": "NG1435", "symbol": "⩬", "issues": ["Long fallback: 15 chars"]}, {"index": 1121, "id": "NG1436", "symbol": "⮺", "issues": ["Long fallback: 14 chars"]}, {"index": 1123, "id": "NG1438", "symbol": "⦍", "issues": ["Long fallback: 20 chars"]}, {"index": 1124, "id": "NG1439", "symbol": "⭬", "issues": ["Long fallback: 20 chars"]}, {"index": 1125, "id": "NG1440", "symbol": "⥕", "issues": ["Long fallback: 20 chars"]}, {"index": 1126, "id": "NG1441", "symbol": "⍭", "issues": ["Long fallback: 20 chars"]}, {"index": 1127, "id": "NG1442", "symbol": "⭪", "issues": ["Long fallback: 20 chars"]}, {"index": 1129, "id": "NG1444", "symbol": "❭", "issues": ["Long fallback: 19 chars"]}, {"index": 1130, "id": "NG1445", "symbol": "⥭", "issues": ["Long fallback: 19 chars"]}, {"index": 1131, "id": "NG1446", "symbol": "⧭", "issues": ["Long fallback: 20 chars"]}, {"index": 1134, "id": "NG1449", "symbol": "≮", "issues": ["Long fallback: 14 chars"]}, {"index": 1138, "id": "NG1453", "symbol": "⍮", "issues": ["Long fallback: 18 chars"]}, {"index": 1140, "id": "NG1455", "symbol": "⩮", "issues": ["Long fallback: 16 chars"]}, {"index": 1141, "id": "NG1456", "symbol": "⭮", "issues": ["Long fallback: 18 chars"]}, {"index": 1142, "id": "NG1457", "symbol": "⩞", "issues": ["Long fallback: 19 chars"]}, {"index": 1143, "id": "NG1458", "symbol": "⍯", "issues": ["Long fallback: 16 chars"]}, {"index": 1146, "id": "NG1461", "symbol": "❯", "issues": ["Long fallback: 17 chars"]}, {"index": 1147, "id": "NG1462", "symbol": "⩯", "issues": ["Long fallback: 20 chars"]}, {"index": 1148, "id": "NG1463", "symbol": "≰", "issues": ["Long fallback: 19 chars"]}, {"index": 1149, "id": "NG1464", "symbol": "⡧", "issues": ["Long fallback: 20 chars"]}, {"index": 1150, "id": "NG1465", "symbol": "⪁", "issues": ["Long fallback: 20 chars"]}, {"index": 1151, "id": "NG1466", "symbol": "⍰", "issues": ["Long fallback: 20 chars"]}, {"index": 1152, "id": "NG1467", "symbol": "⍱", "issues": ["Long fallback: 17 chars"]}, {"index": 1153, "id": "NG1468", "symbol": "⥱", "issues": ["Long fallback: 19 chars"]}, {"index": 1154, "id": "NG1469", "symbol": "✔", "issues": ["Long fallback: 20 chars"]}, {"index": 1155, "id": "NG1470", "symbol": "⩱", "issues": ["Long fallback: 16 chars"]}, {"index": 1158, "id": "NG1473", "symbol": "⍲", "issues": ["Long fallback: 17 chars"]}, {"index": 1159, "id": "NG1474", "symbol": "⡠", "issues": ["Long fallback: 17 chars"]}, {"index": 1160, "id": "NG1475", "symbol": "❲", "issues": ["Long fallback: 15 chars"]}, {"index": 1161, "id": "NG1476", "symbol": "⩲", "issues": ["Long fallback: 19 chars"]}, {"index": 1162, "id": "NG1477", "symbol": "≳", "issues": ["Long fallback: 20 chars"]}, {"index": 1163, "id": "NG1478", "symbol": "⍳", "issues": ["Long fallback: 17 chars"]}, {"index": 1164, "id": "NG1479", "symbol": "❳", "issues": ["Long fallback: 18 chars"]}, {"index": 1165, "id": "NG1480", "symbol": "⦰", "issues": ["Long fallback: 18 chars"]}, {"index": 1166, "id": "NG1481", "symbol": "⩳", "issues": ["Long fallback: 18 chars"]}, {"index": 1167, "id": "NG1482", "symbol": "⭳", "issues": ["Long fallback: 18 chars"]}, {"index": 1168, "id": "NG1483", "symbol": "≴", "issues": ["Long fallback: 20 chars"]}, {"index": 1169, "id": "NG1484", "symbol": "⨐", "issues": ["Long fallback: 18 chars"]}, {"index": 1170, "id": "NG1485", "symbol": "⍴", "issues": ["Long fallback: 19 chars"]}, {"index": 1171, "id": "NG1486", "symbol": "⨲", "issues": ["Long fallback: 20 chars"]}, {"index": 1172, "id": "NG1487", "symbol": "⩴", "issues": ["Long fallback: 18 chars"]}, {"index": 1173, "id": "NG1488", "symbol": "⭴", "issues": ["Long fallback: 20 chars"]}, {"index": 1174, "id": "NG1489", "symbol": "⍵", "issues": ["Long fallback: 18 chars"]}, {"index": 1175, "id": "NG1490", "symbol": "✭", "issues": ["Long fallback: 20 chars"]}, {"index": 1176, "id": "NG1491", "symbol": "❝", "issues": ["Long fallback: 18 chars"]}, {"index": 1177, "id": "NG1492", "symbol": "⥪", "issues": ["Long fallback: 20 chars"]}, {"index": 1178, "id": "NG1493", "symbol": "❵", "issues": ["Long fallback: 20 chars"]}, {"index": 1179, "id": "NG1494", "symbol": "⧵", "issues": ["Long fallback: 19 chars"]}, {"index": 1180, "id": "NG1495", "symbol": "⩵", "issues": ["Long fallback: 20 chars"]}, {"index": 1184, "id": "NG1499", "symbol": "⭵", "issues": ["Long fallback: 11 chars"]}, {"index": 1186, "id": "NG1501", "symbol": "≶", "issues": ["Long fallback: 11 chars"]}, {"index": 1189, "id": "NG1504", "symbol": "⤖", "issues": ["Long fallback: 11 chars"]}, {"index": 1190, "id": "NG1505", "symbol": "⍶", "issues": ["Long fallback: 14 chars"]}, {"index": 1191, "id": "NG1506", "symbol": "❶", "issues": ["Long fallback: 15 chars"]}, {"index": 1193, "id": "NG1508", "symbol": "⧶", "issues": ["Long fallback: 13 chars"]}, {"index": 1194, "id": "NG1509", "symbol": "⦜", "issues": ["Long fallback: 15 chars"]}, {"index": 1195, "id": "NG1510", "symbol": "➚", "issues": ["Long fallback: 15 chars"]}, {"index": 1196, "id": "NG1511", "symbol": "⭶", "issues": ["Long fallback: 17 chars"]}, {"index": 1197, "id": "NG1512", "symbol": "≷", "issues": ["Long fallback: 14 chars"]}, {"index": 1198, "id": "NG1513", "symbol": "⢉", "issues": ["Long fallback: 14 chars"]}, {"index": 1199, "id": "NG1514", "symbol": "⍷", "issues": ["Long fallback: 17 chars"]}, {"index": 1201, "id": "NG1516", "symbol": "❷", "issues": ["Long fallback: 12 chars"]}, {"index": 1203, "id": "NG1518", "symbol": "⫲", "issues": ["Long fallback: 13 chars"]}, {"index": 1204, "id": "NG1519", "symbol": "⥷", "issues": ["Long fallback: 12 chars"]}, {"index": 1205, "id": "NG1520", "symbol": "≸", "issues": ["Long fallback: 14 chars"]}, {"index": 1206, "id": "NG1521", "symbol": "❸", "issues": ["Long fallback: 12 chars"]}, {"index": 1207, "id": "NG1522", "symbol": "➿", "issues": ["Long fallback: 15 chars"]}, {"index": 1208, "id": "NG1523", "symbol": "⧸", "issues": ["Long fallback: 14 chars"]}, {"index": 1209, "id": "NG1524", "symbol": "⩸", "issues": ["Long fallback: 11 chars"]}, {"index": 1212, "id": "NG1527", "symbol": "⍹", "issues": ["Long fallback: 12 chars"]}, {"index": 1214, "id": "NG1529", "symbol": "❹", "issues": ["Long fallback: 11 chars"]}, {"index": 1215, "id": "NG1530", "symbol": "⥹", "issues": ["Long fallback: 13 chars"]}, {"index": 1216, "id": "NG1531", "symbol": "⩹", "issues": ["Long fallback: 14 chars"]}, {"index": 1217, "id": "NG1532", "symbol": "⭹", "issues": ["Long fallback: 11 chars"]}, {"index": 1218, "id": "NG1533", "symbol": "⍺", "issues": ["Long fallback: 12 chars"]}, {"index": 1219, "id": "NG1534", "symbol": "⣳", "issues": ["Long fallback: 14 chars"]}, {"index": 1220, "id": "NG1535", "symbol": "❺", "issues": ["Long fallback: 12 chars"]}, {"index": 1221, "id": "NG1536", "symbol": "✻", "issues": ["Long fallback: 12 chars"]}, {"index": 1222, "id": "NG1537", "symbol": "⡦", "issues": ["Long fallback: 12 chars"]}, {"index": 1223, "id": "NG1538", "symbol": "⥺", "issues": ["Long fallback: 13 chars"]}, {"index": 1224, "id": "NG1539", "symbol": "⩺", "issues": ["Long fallback: 15 chars"]}, {"index": 1225, "id": "NG1540", "symbol": "⭺", "issues": ["Long fallback: 15 chars"]}, {"index": 1226, "id": "NG1541", "symbol": "≻", "issues": ["Long fallback: 11 chars"]}, {"index": 1229, "id": "NG1544", "symbol": "⍻", "issues": ["Long fallback: 17 chars"]}, {"index": 1233, "id": "NG1548", "symbol": "⡍", "issues": ["Long fallback: 18 chars"]}, {"index": 1237, "id": "NG1552", "symbol": "⦾", "issues": ["Long fallback: 19 chars"]}, {"index": 1238, "id": "NG1553", "symbol": "❻", "issues": ["Long fallback: 17 chars"]}, {"index": 1239, "id": "NG1554", "symbol": "⥻", "issues": ["Long fallback: 14 chars"]}, {"index": 1240, "id": "NG1555", "symbol": "𝝯", "issues": ["Long fallback: 17 chars"]}, {"index": 1241, "id": "NG1556", "symbol": "⯓", "issues": ["Long fallback: 17 chars"]}, {"index": 1242, "id": "NG1557", "symbol": "⩻", "issues": ["Long fallback: 17 chars"]}, {"index": 1243, "id": "NG1558", "symbol": "⭻", "issues": ["Long fallback: 17 chars"]}, {"index": 1244, "id": "NG1559", "symbol": "≼", "issues": ["Long fallback: 19 chars"]}, {"index": 1245, "id": "NG1560", "symbol": "⍼", "issues": ["Long fallback: 18 chars"]}, {"index": 1247, "id": "NG1562", "symbol": "⣉", "issues": ["Long fallback: 20 chars"]}, {"index": 1248, "id": "NG1563", "symbol": "❼", "issues": ["Long fallback: 20 chars"]}, {"index": 1249, "id": "NG1564", "symbol": "⩼", "issues": ["Long fallback: 20 chars"]}, {"index": 1250, "id": "NG1565", "symbol": "⥟", "issues": ["Long fallback: 20 chars"]}, {"index": 1251, "id": "NG1566", "symbol": "⣿", "issues": ["Long fallback: 20 chars"]}, {"index": 1254, "id": "NG1569", "symbol": "⍽", "issues": ["Long fallback: 13 chars"]}, {"index": 1255, "id": "NG1570", "symbol": "❽", "issues": ["Long fallback: 17 chars"]}, {"index": 1256, "id": "NG1571", "symbol": "⩈", "issues": ["Long fallback: 20 chars"]}, {"index": 1258, "id": "NG1573", "symbol": "⡲", "issues": ["Long fallback: 20 chars"]}, {"index": 1260, "id": "NG1575", "symbol": "⩽", "issues": ["Long fallback: 17 chars"]}, {"index": 1263, "id": "NG1578", "symbol": "⍾", "issues": ["Long fallback: 13 chars"]}, {"index": 1264, "id": "NG1579", "symbol": "❾", "issues": ["Long fallback: 11 chars"]}, {"index": 1265, "id": "NG1580", "symbol": "⭟", "issues": ["Long fallback: 12 chars"]}, {"index": 1266, "id": "NG1581", "symbol": "⥾", "issues": ["Long fallback: 12 chars"]}, {"index": 1267, "id": "NG1582", "symbol": "⧾", "issues": ["Long fallback: 11 chars"]}, {"index": 1270, "id": "NG1585", "symbol": "⩾", "issues": ["Long fallback: 17 chars"]}, {"index": 1272, "id": "NG1587", "symbol": "⧌", "issues": ["Long fallback: 18 chars"]}, {"index": 1273, "id": "NG1588", "symbol": "⭾", "issues": ["Long fallback: 16 chars"]}, {"index": 1274, "id": "NG1589", "symbol": "⍿", "issues": ["Long fallback: 17 chars"]}, {"index": 1275, "id": "NG1590", "symbol": "❿", "issues": ["Long fallback: 16 chars"]}, {"index": 1276, "id": "NG1591", "symbol": "⎀", "issues": ["Long fallback: 17 chars"]}, {"index": 1277, "id": "NG1592", "symbol": "➀", "issues": ["Long fallback: 20 chars"]}, {"index": 1278, "id": "NG1593", "symbol": "➁", "issues": ["Long fallback: 20 chars"]}, {"index": 1280, "id": "NG1595", "symbol": "⮁", "issues": ["Long fallback: 20 chars"]}, {"index": 1281, "id": "NG1596", "symbol": "⊂", "issues": ["Long fallback: 18 chars"]}, {"index": 1282, "id": "NG1597", "symbol": "⎂", "issues": ["Long fallback: 19 chars"]}, {"index": 1283, "id": "NG1598", "symbol": "⦧", "issues": ["Long fallback: 19 chars"]}, {"index": 1285, "id": "NG1600", "symbol": "➂", "issues": ["Long fallback: 19 chars"]}, {"index": 1286, "id": "NG1601", "symbol": "⮂", "issues": ["Long fallback: 16 chars"]}, {"index": 1288, "id": "NG1603", "symbol": "⡃", "issues": ["Long fallback: 19 chars"]}, {"index": 1289, "id": "NG1604", "symbol": "⯎", "issues": ["Long fallback: 20 chars"]}, {"index": 1290, "id": "NG1605", "symbol": "⎃", "issues": ["Long fallback: 20 chars"]}, {"index": 1292, "id": "NG1607", "symbol": "⫨", "issues": ["Long fallback: 19 chars"]}, {"index": 1293, "id": "NG1608", "symbol": "➃", "issues": ["Long fallback: 19 chars"]}, {"index": 1294, "id": "NG1609", "symbol": "❙", "issues": ["Long fallback: 19 chars"]}, {"index": 1295, "id": "NG1610", "symbol": "⯺", "issues": ["Long fallback: 19 chars"]}, {"index": 1296, "id": "NG1611", "symbol": "⪃", "issues": ["Long fallback: 20 chars"]}, {"index": 1297, "id": "NG1612", "symbol": "⊄", "issues": ["Long fallback: 18 chars"]}, {"index": 1298, "id": "NG1613", "symbol": "⎄", "issues": ["Long fallback: 20 chars"]}, {"index": 1299, "id": "NG1614", "symbol": "➄", "issues": ["Long fallback: 20 chars"]}, {"index": 1300, "id": "NG1615", "symbol": "⩨", "issues": ["Long fallback: 20 chars"]}, {"index": 1301, "id": "NG1616", "symbol": "⮄", "issues": ["Long fallback: 18 chars"]}, {"index": 1302, "id": "NG1617", "symbol": "⫝", "issues": ["Long fallback: 20 chars"]}, {"index": 1303, "id": "NG1618", "symbol": "⎅", "issues": ["Long fallback: 20 chars"]}, {"index": 1304, "id": "NG1619", "symbol": "➅", "issues": ["Long fallback: 20 chars"]}, {"index": 1305, "id": "NG1620", "symbol": "⪅", "issues": ["Long fallback: 19 chars"]}, {"index": 1307, "id": "NG1622", "symbol": "⫯", "issues": ["Long fallback: 19 chars"]}, {"index": 1308, "id": "NG1623", "symbol": "⠥", "issues": ["Long fallback: 19 chars"]}, {"index": 1310, "id": "NG1625", "symbol": "⮅", "issues": ["Long fallback: 19 chars"]}, {"index": 1311, "id": "NG1626", "symbol": "➆", "issues": ["Long fallback: 20 chars"]}, {"index": 1312, "id": "NG1627", "symbol": "⠆", "issues": ["Long fallback: 19 chars"]}, {"index": 1313, "id": "NG1628", "symbol": "⪆", "issues": ["Long fallback: 20 chars"]}, {"index": 1314, "id": "NG1629", "symbol": "⮡", "issues": ["Long fallback: 20 chars"]}, {"index": 1315, "id": "NG1630", "symbol": "⨏", "issues": ["Long fallback: 19 chars"]}, {"index": 1316, "id": "NG1631", "symbol": "⮆", "issues": ["Long fallback: 20 chars"]}, {"index": 1317, "id": "NG1632", "symbol": "⩎", "issues": ["Long fallback: 19 chars"]}, {"index": 1318, "id": "NG1633", "symbol": "⊇", "issues": ["Long fallback: 19 chars"]}, {"index": 1319, "id": "NG1634", "symbol": "⎇", "issues": ["Long fallback: 20 chars"]}, {"index": 1320, "id": "NG1635", "symbol": "➇", "issues": ["Long fallback: 17 chars"]}, {"index": 1321, "id": "NG1636", "symbol": "⪇", "issues": ["Long fallback: 20 chars"]}, {"index": 1322, "id": "NG1637", "symbol": "⮇", "issues": ["Long fallback: 17 chars"]}, {"index": 1323, "id": "NG1638", "symbol": "⎈", "issues": ["Long fallback: 19 chars"]}, {"index": 1324, "id": "NG1639", "symbol": "➈", "issues": ["Long fallback: 17 chars"]}, {"index": 1325, "id": "NG1640", "symbol": "⭷", "issues": ["Long fallback: 19 chars"]}, {"index": 1326, "id": "NG1641", "symbol": "⪈", "issues": ["Long fallback: 19 chars"]}, {"index": 1327, "id": "NG1642", "symbol": "⡢", "issues": ["Long fallback: 17 chars"]}, {"index": 1328, "id": "NG1643", "symbol": "❉", "issues": ["Long fallback: 19 chars"]}, {"index": 1329, "id": "NG1644", "symbol": "⎉", "issues": ["Long fallback: 20 chars"]}, {"index": 1330, "id": "NG1645", "symbol": "➉", "issues": ["Long fallback: 17 chars"]}, {"index": 1331, "id": "NG1646", "symbol": "⪉", "issues": ["Long fallback: 17 chars"]}, {"index": 1332, "id": "NG1647", "symbol": "⬀", "issues": ["Long fallback: 19 chars"]}, {"index": 1333, "id": "NG1648", "symbol": "⎊", "issues": ["Long fallback: 18 chars"]}, {"index": 1334, "id": "NG1649", "symbol": "➊", "issues": ["Long fallback: 20 chars"]}, {"index": 1335, "id": "NG1650", "symbol": "⪊", "issues": ["Long fallback: 20 chars"]}, {"index": 1336, "id": "NG1651", "symbol": "⬒", "issues": ["Long fallback: 20 chars"]}, {"index": 1337, "id": "NG1652", "symbol": "⯥", "issues": ["Long fallback: 20 chars"]}, {"index": 1338, "id": "NG1653", "symbol": "❆", "issues": ["Long fallback: 18 chars"]}, {"index": 1339, "id": "NG1654", "symbol": "⎋", "issues": ["Long fallback: 18 chars"]}, {"index": 1340, "id": "NG1655", "symbol": "➋", "issues": ["Long fallback: 14 chars"]}, {"index": 1344, "id": "NG1659", "symbol": "⬕", "issues": ["Long fallback: 15 chars"]}, {"index": 1347, "id": "NG1662", "symbol": "⮋", "issues": ["Long fallback: 17 chars"]}, {"index": 1349, "id": "NG1664", "symbol": "⊌", "issues": ["Long fallback: 12 chars"]}, {"index": 1350, "id": "NG1665", "symbol": "⎌", "issues": ["Long fallback: 14 chars"]}, {"index": 1351, "id": "NG1666", "symbol": "⤩", "issues": ["Long fallback: 15 chars"]}, {"index": 1353, "id": "NG1668", "symbol": "⠼", "issues": ["Long fallback: 15 chars"]}, {"index": 1354, "id": "NG1669", "symbol": "➌", "issues": ["Long fallback: 14 chars"]}, {"index": 1355, "id": "NG1670", "symbol": "⎍", "issues": ["Long fallback: 13 chars"]}, {"index": 1358, "id": "NG1673", "symbol": "➍", "issues": ["Long fallback: 15 chars"]}, {"index": 1361, "id": "NG1676", "symbol": "⪍", "issues": ["Long fallback: 15 chars"]}, {"index": 1362, "id": "NG1677", "symbol": "⮍", "issues": ["Long fallback: 18 chars"]}, {"index": 1363, "id": "NG1678", "symbol": "⎎", "issues": ["Long fallback: 18 chars"]}, {"index": 1364, "id": "NG1679", "symbol": "➎", "issues": ["Long fallback: 18 chars"]}, {"index": 1365, "id": "NG1680", "symbol": "⪎", "issues": ["Long fallback: 19 chars"]}, {"index": 1366, "id": "NG1681", "symbol": "✀", "issues": ["Long fallback: 18 chars"]}, {"index": 1367, "id": "NG1682", "symbol": "⩛", "issues": ["Long fallback: 19 chars"]}, {"index": 1368, "id": "NG1683", "symbol": "⎏", "issues": ["Long fallback: 19 chars"]}, {"index": 1369, "id": "NG1684", "symbol": "⪌", "issues": ["Long fallback: 17 chars"]}, {"index": 1370, "id": "NG1685", "symbol": "➏", "issues": ["Long fallback: 19 chars"]}, {"index": 1373, "id": "NG1688", "symbol": "⪏", "issues": ["Long fallback: 17 chars"]}, {"index": 1375, "id": "NG1690", "symbol": "⎐", "issues": ["Long fallback: 19 chars"]}, {"index": 1376, "id": "NG1691", "symbol": "⦥", "issues": ["Long fallback: 18 chars"]}, {"index": 1377, "id": "NG1692", "symbol": "➐", "issues": ["Long fallback: 18 chars"]}, {"index": 1378, "id": "NG1693", "symbol": "⪐", "issues": ["Long fallback: 20 chars"]}, {"index": 1379, "id": "NG1694", "symbol": "⩰", "issues": ["Long fallback: 20 chars"]}, {"index": 1380, "id": "NG1695", "symbol": "⦘", "issues": ["Long fallback: 18 chars"]}, {"index": 1381, "id": "NG1696", "symbol": "⣶", "issues": ["Long fallback: 20 chars"]}, {"index": 1382, "id": "NG1697", "symbol": "⊑", "issues": ["Long fallback: 20 chars"]}, {"index": 1383, "id": "NG1698", "symbol": "⥄", "issues": ["Long fallback: 18 chars"]}, {"index": 1384, "id": "NG1699", "symbol": "➑", "issues": ["Long fallback: 18 chars"]}, {"index": 1385, "id": "NG1700", "symbol": "⪑", "issues": ["Long fallback: 18 chars"]}, {"index": 1386, "id": "NG1701", "symbol": "❂", "issues": ["Long fallback: 18 chars"]}, {"index": 1387, "id": "NG1702", "symbol": "⮑", "issues": ["Long fallback: 20 chars"]}, {"index": 1388, "id": "NG1703", "symbol": "⎒", "issues": ["Long fallback: 20 chars"]}, {"index": 1390, "id": "NG1705", "symbol": "➒", "issues": ["Long fallback: 20 chars"]}, {"index": 1393, "id": "NG1708", "symbol": "⮒", "issues": ["Long fallback: 20 chars"]}, {"index": 1394, "id": "NG1709", "symbol": "⊓", "issues": ["Long fallback: 18 chars"]}, {"index": 1395, "id": "NG1710", "symbol": "➓", "issues": ["Long fallback: 20 chars"]}, {"index": 1396, "id": "NG1711", "symbol": "⪓", "issues": ["Long fallback: 20 chars"]}, {"index": 1397, "id": "NG1712", "symbol": "⢿", "issues": ["Long fallback: 18 chars"]}, {"index": 1398, "id": "NG1713", "symbol": "⮓", "issues": ["Long fallback: 20 chars"]}, {"index": 1399, "id": "NG1714", "symbol": "⪋", "issues": ["Long fallback: 20 chars"]}, {"index": 1400, "id": "NG1715", "symbol": "⊔", "issues": ["Long fallback: 20 chars"]}, {"index": 1401, "id": "NG1716", "symbol": "✙", "issues": ["Long fallback: 20 chars"]}, {"index": 1402, "id": "NG1717", "symbol": "⤱", "issues": ["Long fallback: 20 chars"]}, {"index": 1403, "id": "NG1718", "symbol": "⎔", "issues": ["Long fallback: 20 chars"]}, {"index": 1404, "id": "NG1719", "symbol": "➽", "issues": ["Long fallback: 18 chars"]}, {"index": 1405, "id": "NG1720", "symbol": "➔", "issues": ["Long fallback: 20 chars"]}, {"index": 1406, "id": "NG1721", "symbol": "⪄", "issues": ["Long fallback: 20 chars"]}, {"index": 1407, "id": "NG1722", "symbol": "⮳", "issues": ["Long fallback: 18 chars"]}, {"index": 1408, "id": "NG1723", "symbol": "⪔", "issues": ["Long fallback: 18 chars"]}, {"index": 1409, "id": "NG1724", "symbol": "⯕", "issues": ["Long fallback: 20 chars"]}, {"index": 1410, "id": "NG1725", "symbol": "⎕", "issues": ["Long fallback: 20 chars"]}, {"index": 1411, "id": "NG1726", "symbol": "⪕", "issues": ["Long fallback: 20 chars"]}, {"index": 1412, "id": "NG1727", "symbol": "⤏", "issues": ["Long fallback: 20 chars"]}, {"index": 1413, "id": "NG1728", "symbol": "⫮", "issues": ["Long fallback: 20 chars"]}, {"index": 1414, "id": "NG1729", "symbol": "⎖", "issues": ["Long fallback: 20 chars"]}, {"index": 1415, "id": "NG1730", "symbol": "⠧", "issues": ["Long fallback: 18 chars"]}, {"index": 1416, "id": "NG1731", "symbol": "➖", "issues": ["Long fallback: 18 chars"]}, {"index": 1417, "id": "NG1732", "symbol": "⮖", "issues": ["Long fallback: 20 chars"]}, {"index": 1418, "id": "NG1733", "symbol": "⎗", "issues": ["Long fallback: 18 chars"]}, {"index": 1419, "id": "NG1734", "symbol": "➗", "issues": ["Long fallback: 18 chars"]}, {"index": 1420, "id": "NG1735", "symbol": "⪗", "issues": ["Long fallback: 19 chars"]}, {"index": 1421, "id": "NG1736", "symbol": "⮗", "issues": ["Long fallback: 19 chars"]}, {"index": 1422, "id": "NG1737", "symbol": "⧑", "issues": ["Long fallback: 19 chars"]}, {"index": 1423, "id": "NG1738", "symbol": "⎘", "issues": ["Long fallback: 19 chars"]}, {"index": 1424, "id": "NG1739", "symbol": "➘", "issues": ["Long fallback: 19 chars"]}, {"index": 1425, "id": "NG1740", "symbol": "⬞", "issues": ["Long fallback: 19 chars"]}, {"index": 1426, "id": "NG1741", "symbol": "⎙", "issues": ["Long fallback: 19 chars"]}, {"index": 1427, "id": "NG1742", "symbol": "➙", "issues": ["Long fallback: 19 chars"]}, {"index": 1428, "id": "NG1743", "symbol": "⮚", "issues": ["Long fallback: 19 chars"]}, {"index": 1429, "id": "NG1744", "symbol": "⎛", "issues": ["Long fallback: 19 chars"]}, {"index": 1430, "id": "NG1745", "symbol": "⪛", "issues": ["Long fallback: 19 chars"]}, {"index": 1431, "id": "NG1746", "symbol": "⥵", "issues": ["Long fallback: 19 chars"]}, {"index": 1432, "id": "NG1747", "symbol": "⮛", "issues": ["Long fallback: 19 chars"]}, {"index": 1433, "id": "NG1748", "symbol": "⊜", "issues": ["Long fallback: 19 chars"]}, {"index": 1434, "id": "NG1749", "symbol": "⩝", "issues": ["Long fallback: 19 chars"]}, {"index": 1435, "id": "NG1750", "symbol": "⎜", "issues": ["Long fallback: 19 chars"]}, {"index": 1436, "id": "NG1751", "symbol": "⎝", "issues": ["Long fallback: 19 chars"]}, {"index": 1437, "id": "NG1752", "symbol": "➝", "issues": ["Long fallback: 19 chars"]}, {"index": 1438, "id": "NG1753", "symbol": "⪝", "issues": ["Long fallback: 19 chars"]}, {"index": 1439, "id": "NG1754", "symbol": "⮝", "issues": ["Long fallback: 19 chars"]}, {"index": 1440, "id": "NG1755", "symbol": "⎞", "issues": ["Long fallback: 19 chars"]}, {"index": 1441, "id": "NG1756", "symbol": "⪞", "issues": ["Long fallback: 19 chars"]}, {"index": 1442, "id": "NG1757", "symbol": "⊟", "issues": ["Long fallback: 19 chars"]}, {"index": 1443, "id": "NG1758", "symbol": "⎟", "issues": ["Long fallback: 19 chars"]}, {"index": 1444, "id": "NG1759", "symbol": "➟", "issues": ["Long fallback: 19 chars"]}, {"index": 1445, "id": "NG1760", "symbol": "⎠", "issues": ["Long fallback: 19 chars"]}, {"index": 1446, "id": "NG1761", "symbol": "⮠", "issues": ["Long fallback: 19 chars"]}, {"index": 1447, "id": "NG1762", "symbol": "⤠", "issues": ["Long fallback: 19 chars"]}, {"index": 1448, "id": "NG1763", "symbol": "⊡", "issues": ["Long fallback: 19 chars"]}, {"index": 1449, "id": "NG1764", "symbol": "⮪", "issues": ["Long fallback: 19 chars"]}, {"index": 1450, "id": "NG1765", "symbol": "⎡", "issues": ["Long fallback: 19 chars"]}, {"index": 1451, "id": "NG1766", "symbol": "⪡", "issues": ["Long fallback: 19 chars"]}, {"index": 1452, "id": "NG1767", "symbol": "⭰", "issues": ["Long fallback: 19 chars"]}, {"index": 1453, "id": "NG1768", "symbol": "⥁", "issues": ["Long fallback: 19 chars"]}, {"index": 1454, "id": "NG1769", "symbol": "⩭", "issues": ["Long fallback: 19 chars"]}, {"index": 1455, "id": "NG1770", "symbol": "⦻", "issues": ["Long fallback: 19 chars"]}, {"index": 1456, "id": "NG1771", "symbol": "⨓", "issues": ["Long fallback: 19 chars"]}, {"index": 1457, "id": "NG1772", "symbol": "➢", "issues": ["Long fallback: 19 chars"]}, {"index": 1458, "id": "NG1773", "symbol": "⭲", "issues": ["Long fallback: 19 chars"]}, {"index": 1459, "id": "NG1774", "symbol": "⪢", "issues": ["Long fallback: 19 chars"]}, {"index": 1460, "id": "NG1775", "symbol": "✮", "issues": ["Long fallback: 19 chars"]}, {"index": 1461, "id": "NG1776", "symbol": "⬁", "issues": ["Long fallback: 19 chars"]}, {"index": 1462, "id": "NG1777", "symbol": "⥽", "issues": ["Long fallback: 19 chars"]}, {"index": 1465, "id": "NG1780", "symbol": "⊣", "issues": ["Long fallback: 11 chars"]}, {"index": 1466, "id": "NG1781", "symbol": "⎣", "issues": ["Long fallback: 13 chars"]}, {"index": 1471, "id": "NG1786", "symbol": "➣", "issues": ["Long fallback: 11 chars"]}, {"index": 1472, "id": "NG1787", "symbol": "⎤", "issues": ["Long fallback: 12 chars"]}, {"index": 1473, "id": "NG1788", "symbol": "⮤", "issues": ["Long fallback: 19 chars"]}, {"index": 1474, "id": "NG1789", "symbol": "⩩", "issues": ["Long fallback: 20 chars"]}, {"index": 1475, "id": "NG1790", "symbol": "⧼", "issues": ["Long fallback: 20 chars"]}, {"index": 1476, "id": "NG1791", "symbol": "⢬", "issues": ["Long fallback: 20 chars"]}, {"index": 1477, "id": "NG1792", "symbol": "⧕", "issues": ["Long fallback: 20 chars"]}, {"index": 1479, "id": "NG1794", "symbol": "⊥", "issues": ["Long fallback: 17 chars"]}, {"index": 1480, "id": "NG1795", "symbol": "➥", "issues": ["Long fallback: 17 chars"]}, {"index": 1481, "id": "NG1796", "symbol": "⮥", "issues": ["Long fallback: 18 chars"]}, {"index": 1482, "id": "NG1797", "symbol": "⎦", "issues": ["Long fallback: 19 chars"]}, {"index": 1483, "id": "NG1798", "symbol": "➦", "issues": ["Long fallback: 19 chars"]}, {"index": 1484, "id": "NG1799", "symbol": "⮦", "issues": ["Long fallback: 15 chars"]}, {"index": 1485, "id": "NG1800", "symbol": "➧", "issues": ["Long fallback: 18 chars"]}, {"index": 1486, "id": "NG1801", "symbol": "❮", "issues": ["Long fallback: 20 chars"]}, {"index": 1488, "id": "NG1803", "symbol": "⮧", "issues": ["Long fallback: 18 chars"]}, {"index": 1490, "id": "NG1805", "symbol": "⣁", "issues": ["Long fallback: 19 chars"]}, {"index": 1493, "id": "NG1808", "symbol": "⎨", "issues": ["Long fallback: 18 chars"]}, {"index": 1494, "id": "NG1809", "symbol": "➨", "issues": ["Long fallback: 19 chars"]}, {"index": 1495, "id": "NG1810", "symbol": "⊩", "issues": ["Long fallback: 18 chars"]}, {"index": 1496, "id": "NG1811", "symbol": "❰", "issues": ["Long fallback: 19 chars"]}, {"index": 1497, "id": "NG1812", "symbol": "⩐", "issues": ["Long fallback: 19 chars"]}, {"index": 1498, "id": "NG1813", "symbol": "⎩", "issues": ["Long fallback: 15 chars"]}, {"index": 1502, "id": "NG1817", "symbol": "⨑", "issues": ["Long fallback: 19 chars"]}, {"index": 1503, "id": "NG1818", "symbol": "➩", "issues": ["Long fallback: 19 chars"]}, {"index": 1505, "id": "NG1820", "symbol": "⮩", "issues": ["Long fallback: 19 chars"]}, {"index": 1506, "id": "NG1821", "symbol": "⭅", "issues": ["Long fallback: 20 chars"]}, {"index": 1507, "id": "NG1822", "symbol": "⤇", "issues": ["Long fallback: 18 chars"]}, {"index": 1508, "id": "NG1823", "symbol": "⎪", "issues": ["Long fallback: 20 chars"]}, {"index": 1509, "id": "NG1824", "symbol": "⊫", "issues": ["Long fallback: 18 chars"]}, {"index": 1510, "id": "NG1825", "symbol": "⎫", "issues": ["Long fallback: 20 chars"]}, {"index": 1511, "id": "NG1826", "symbol": "⮫", "issues": ["Long fallback: 20 chars"]}, {"index": 1512, "id": "NG1827", "symbol": "⠲", "issues": ["Long fallback: 20 chars"]}, {"index": 1513, "id": "NG1828", "symbol": "⪬", "issues": ["Long fallback: 19 chars"]}, {"index": 1514, "id": "NG1829", "symbol": "⊭", "issues": ["Long fallback: 18 chars"]}, {"index": 1515, "id": "NG1830", "symbol": "⣕", "issues": ["Long fallback: 20 chars"]}, {"index": 1516, "id": "NG1831", "symbol": "⥲", "issues": ["Long fallback: 20 chars"]}, {"index": 1517, "id": "NG1832", "symbol": "⥉", "issues": ["Long fallback: 20 chars"]}, {"index": 1518, "id": "NG1833", "symbol": "⪭", "issues": ["Long fallback: 18 chars"]}, {"index": 1519, "id": "NG1834", "symbol": "⮌", "issues": ["Long fallback: 20 chars"]}, {"index": 1520, "id": "NG1835", "symbol": "⎮", "issues": ["Long fallback: 18 chars"]}, {"index": 1521, "id": "NG1836", "symbol": "⪮", "issues": ["Long fallback: 20 chars"]}, {"index": 1522, "id": "NG1837", "symbol": "⮮", "issues": ["Long fallback: 18 chars"]}, {"index": 1523, "id": "NG1838", "symbol": "⫶", "issues": ["Long fallback: 18 chars"]}, {"index": 1524, "id": "NG1839", "symbol": "⤲", "issues": ["Long fallback: 18 chars"]}, {"index": 1525, "id": "NG1840", "symbol": "➯", "issues": ["Long fallback: 20 chars"]}, {"index": 1526, "id": "NG1841", "symbol": "⮰", "issues": ["Long fallback: 20 chars"]}, {"index": 1530, "id": "NG1845", "symbol": "⪯", "issues": ["Long fallback: 16 chars"]}, {"index": 1533, "id": "NG1848", "symbol": "⎰", "issues": ["Long fallback: 15 chars"]}, {"index": 1536, "id": "NG1851", "symbol": "⪰", "issues": ["Long fallback: 11 chars"]}, {"index": 1537, "id": "NG1852", "symbol": "⎱", "issues": ["Long fallback: 12 chars"]}, {"index": 1538, "id": "NG1853", "symbol": "⪱", "issues": ["Long fallback: 13 chars"]}, {"index": 1539, "id": "NG1854", "symbol": "⮱", "issues": ["Long fallback: 14 chars"]}, {"index": 1540, "id": "NG1855", "symbol": "⎲", "issues": ["Long fallback: 13 chars"]}, {"index": 1541, "id": "NG1856", "symbol": "⤯", "issues": ["Long fallback: 14 chars"]}, {"index": 1542, "id": "NG1857", "symbol": "➲", "issues": ["Long fallback: 13 chars"]}, {"index": 1543, "id": "NG1858", "symbol": "⎳", "issues": ["Long fallback: 14 chars"]}, {"index": 1544, "id": "NG1859", "symbol": "➳", "issues": ["Long fallback: 13 chars"]}, {"index": 1545, "id": "NG1860", "symbol": "⪳", "issues": ["Long fallback: 13 chars"]}, {"index": 1547, "id": "NG1862", "symbol": "⊴", "issues": ["Long fallback: 19 chars"]}, {"index": 1548, "id": "NG1863", "symbol": "⎴", "issues": ["Long fallback: 17 chars"]}, {"index": 1549, "id": "NG1864", "symbol": "⮴", "issues": ["Long fallback: 19 chars"]}, {"index": 1550, "id": "NG1865", "symbol": "✬", "issues": ["Long fallback: 20 chars"]}, {"index": 1551, "id": "NG1866", "symbol": "⢫", "issues": ["Long fallback: 20 chars"]}, {"index": 1552, "id": "NG1867", "symbol": "⎵", "issues": ["Long fallback: 20 chars"]}, {"index": 1554, "id": "NG1869", "symbol": "➱", "issues": ["Long fallback: 20 chars"]}, {"index": 1555, "id": "NG1870", "symbol": "⪙", "issues": ["Long fallback: 20 chars"]}, {"index": 1556, "id": "NG1871", "symbol": "⮵", "issues": ["Long fallback: 20 chars"]}, {"index": 1557, "id": "NG1872", "symbol": "⊶", "issues": ["Long fallback: 20 chars"]}, {"index": 1559, "id": "NG1874", "symbol": "⎶", "issues": ["Long fallback: 19 chars"]}, {"index": 1560, "id": "NG1875", "symbol": "➶", "issues": ["Long fallback: 20 chars"]}, {"index": 1561, "id": "NG1876", "symbol": "⪶", "issues": ["Long fallback: 19 chars"]}, {"index": 1562, "id": "NG1877", "symbol": "⧈", "issues": ["Long fallback: 19 chars"]}, {"index": 1563, "id": "NG1878", "symbol": "⮉", "issues": ["Long fallback: 19 chars"]}, {"index": 1565, "id": "NG1880", "symbol": "⎷", "issues": ["Long fallback: 11 chars"]}, {"index": 1566, "id": "NG1881", "symbol": "⪷", "issues": ["Long fallback: 12 chars"]}, {"index": 1567, "id": "NG1882", "symbol": "⊸", "issues": ["Long fallback: 12 chars"]}, {"index": 1570, "id": "NG1885", "symbol": "➸", "issues": ["Long fallback: 13 chars"]}, {"index": 1576, "id": "NG1891", "symbol": "➰", "issues": ["Long fallback: 18 chars"]}, {"index": 1577, "id": "NG1892", "symbol": "⎹", "issues": ["Long fallback: 18 chars"]}, {"index": 1580, "id": "NG1895", "symbol": "⦬", "issues": ["Long fallback: 18 chars"]}, {"index": 1581, "id": "NG1896", "symbol": "➹", "issues": ["Long fallback: 18 chars"]}, {"index": 1582, "id": "NG1897", "symbol": "⪹", "issues": ["Long fallback: 18 chars"]}, {"index": 1583, "id": "NG1898", "symbol": "⎺", "issues": ["Long fallback: 17 chars"]}, {"index": 1584, "id": "NG1899", "symbol": "➺", "issues": ["Long fallback: 14 chars"]}, {"index": 1585, "id": "NG1900", "symbol": "⪺", "issues": ["Long fallback: 16 chars"]}, {"index": 1586, "id": "NG1901", "symbol": "⎻", "issues": ["Long fallback: 18 chars"]}, {"index": 1587, "id": "NG1902", "symbol": "➻", "issues": ["Long fallback: 16 chars"]}, {"index": 1588, "id": "NG1903", "symbol": "❜", "issues": ["Long fallback: 16 chars"]}, {"index": 1589, "id": "NG1904", "symbol": "⪻", "issues": ["Long fallback: 17 chars"]}, {"index": 1590, "id": "NG1905", "symbol": "⮻", "issues": ["Long fallback: 18 chars"]}, {"index": 1593, "id": "NG1908", "symbol": "⊼", "issues": ["Long fallback: 13 chars"]}, {"index": 1594, "id": "NG1909", "symbol": "⨧", "issues": ["Long fallback: 16 chars"]}, {"index": 1597, "id": "NG1912", "symbol": "⎼", "issues": ["Long fallback: 11 chars"]}, {"index": 1598, "id": "NG1913", "symbol": "➼", "issues": ["Long fallback: 12 chars"]}, {"index": 1599, "id": "NG1914", "symbol": "⪼", "issues": ["Long fallback: 11 chars"]}, {"index": 1602, "id": "NG1917", "symbol": "⎽", "issues": ["Long fallback: 14 chars"]}, {"index": 1604, "id": "NG1919", "symbol": "➫", "issues": ["Long fallback: 15 chars"]}, {"index": 1605, "id": "NG1920", "symbol": "⪽", "issues": ["Long fallback: 17 chars"]}, {"index": 1606, "id": "NG1921", "symbol": "⮽", "issues": ["Long fallback: 18 chars"]}, {"index": 1607, "id": "NG1922", "symbol": "⎾", "issues": ["Long fallback: 15 chars"]}, {"index": 1608, "id": "NG1923", "symbol": "✤", "issues": ["Long fallback: 19 chars"]}, {"index": 1609, "id": "NG1924", "symbol": "➾", "issues": ["Long fallback: 19 chars"]}, {"index": 1610, "id": "NG1925", "symbol": "⮾", "issues": ["Long fallback: 17 chars"]}, {"index": 1612, "id": "NG1927", "symbol": "⎿", "issues": ["Long fallback: 18 chars"]}, {"index": 1614, "id": "NG1929", "symbol": "⪿", "issues": ["Long fallback: 19 chars"]}, {"index": 1616, "id": "NG1931", "symbol": "⢸", "issues": ["Long fallback: 20 chars"]}, {"index": 1617, "id": "NG1932", "symbol": "⨯", "issues": ["Long fallback: 20 chars"]}, {"index": 1618, "id": "NG1933", "symbol": "⢮", "issues": ["Long fallback: 18 chars"]}, {"index": 1619, "id": "NG1934", "symbol": "⮲", "issues": ["Long fallback: 20 chars"]}, {"index": 1620, "id": "NG1935", "symbol": "⮿", "issues": ["Long fallback: 18 chars"]}, {"index": 1621, "id": "NG1936", "symbol": "⏀", "issues": ["Long fallback: 20 chars"]}, {"index": 1623, "id": "NG1938", "symbol": "⫀", "issues": ["Long fallback: 20 chars"]}, {"index": 1624, "id": "NG1939", "symbol": "⯀", "issues": ["Long fallback: 18 chars"]}, {"index": 1627, "id": "NG1942", "symbol": "⏁", "issues": ["Long fallback: 19 chars"]}, {"index": 1628, "id": "NG1943", "symbol": "❋", "issues": ["Long fallback: 19 chars"]}, {"index": 1629, "id": "NG1944", "symbol": "⫁", "issues": ["Long fallback: 16 chars"]}, {"index": 1630, "id": "NG1945", "symbol": "⠠", "issues": ["Long fallback: 17 chars"]}, {"index": 1631, "id": "NG1946", "symbol": "⯁", "issues": ["Long fallback: 20 chars"]}, {"index": 1633, "id": "NG1948", "symbol": "⏂", "issues": ["Long fallback: 20 chars"]}, {"index": 1634, "id": "NG1949", "symbol": "⫂", "issues": ["Long fallback: 20 chars"]}, {"index": 1635, "id": "NG1950", "symbol": "⡹", "issues": ["Long fallback: 19 chars"]}, {"index": 1636, "id": "NG1951", "symbol": "⯂", "issues": ["Long fallback: 19 chars"]}, {"index": 1637, "id": "NG1952", "symbol": "⫃", "issues": ["Long fallback: 19 chars"]}, {"index": 1638, "id": "NG1953", "symbol": "⏄", "issues": ["Long fallback: 19 chars"]}, {"index": 1639, "id": "NG1954", "symbol": "⬺", "issues": ["Long fallback: 19 chars"]}, {"index": 1640, "id": "NG1955", "symbol": "⠚", "issues": ["Long fallback: 19 chars"]}, {"index": 1641, "id": "NG1956", "symbol": "⣭", "issues": ["Long fallback: 19 chars"]}, {"index": 1642, "id": "NG1957", "symbol": "⋅", "issues": ["Long fallback: 19 chars"]}, {"index": 1643, "id": "NG1958", "symbol": "⏅", "issues": ["Long fallback: 19 chars"]}, {"index": 1644, "id": "NG1959", "symbol": "❁", "issues": ["Long fallback: 20 chars"]}, {"index": 1649, "id": "NG1964", "symbol": "⏆", "issues": ["Long fallback: 12 chars"]}, {"index": 1652, "id": "NG1967", "symbol": "⫆", "issues": ["Long fallback: 12 chars"]}, {"index": 1656, "id": "NG1971", "symbol": "⯆", "issues": ["Long fallback: 11 chars"]}, {"index": 1660, "id": "NG1975", "symbol": "⏇", "issues": ["Long fallback: 19 chars"]}, {"index": 1665, "id": "NG1980", "symbol": "⫇", "issues": ["Long fallback: 16 chars"]}, {"index": 1666, "id": "NG1981", "symbol": "⯇", "issues": ["Long fallback: 20 chars"]}, {"index": 1667, "id": "NG1982", "symbol": "⠻", "issues": ["Long fallback: 20 chars"]}, {"index": 1668, "id": "NG1983", "symbol": "⭯", "issues": ["Long fallback: 18 chars"]}, {"index": 1669, "id": "NG1984", "symbol": "⏈", "issues": ["Long fallback: 17 chars"]}, {"index": 1670, "id": "NG1985", "symbol": "⯶", "issues": ["Long fallback: 18 chars"]}, {"index": 1671, "id": "NG1986", "symbol": "⯈", "issues": ["Long fallback: 20 chars"]}, {"index": 1672, "id": "NG1987", "symbol": "⏉", "issues": ["Long fallback: 18 chars"]}, {"index": 1674, "id": "NG1989", "symbol": "➮", "issues": ["Long fallback: 20 chars"]}, {"index": 1675, "id": "NG1990", "symbol": "⏊", "issues": ["Long fallback: 20 chars"]}, {"index": 1676, "id": "NG1991", "symbol": "⫊", "issues": ["Long fallback: 20 chars"]}, {"index": 1677, "id": "NG1992", "symbol": "⏋", "issues": ["Long fallback: 20 chars"]}, {"index": 1678, "id": "NG1993", "symbol": "✊", "issues": ["Long fallback: 20 chars"]}, {"index": 1680, "id": "NG1995", "symbol": "⫋", "issues": ["Long fallback: 20 chars"]}, {"index": 1681, "id": "NG1996", "symbol": "⯋", "issues": ["Long fallback: 20 chars"]}, {"index": 1682, "id": "NG1997", "symbol": "⋌", "issues": ["Long fallback: 20 chars"]}, {"index": 1683, "id": "NG1998", "symbol": "⮣", "issues": ["Long fallback: 20 chars"]}, {"index": 1684, "id": "NG1999", "symbol": "⏌", "issues": ["Long fallback: 20 chars"]}, {"index": 1685, "id": "NG2000", "symbol": "⯌", "issues": ["Long fallback: 20 chars"]}, {"index": 1686, "id": "NG2001", "symbol": "⋍", "issues": ["Long fallback: 20 chars"]}, {"index": 1687, "id": "NG2002", "symbol": "⏍", "issues": ["Long fallback: 20 chars"]}, {"index": 1690, "id": "NG2005", "symbol": "⯍", "issues": ["Long fallback: 20 chars"]}, {"index": 1691, "id": "NG2006", "symbol": "⏎", "issues": ["Long fallback: 20 chars"]}, {"index": 1692, "id": "NG2007", "symbol": "⤤", "issues": ["Long fallback: 20 chars"]}, {"index": 1693, "id": "NG2008", "symbol": "⫎", "issues": ["Long fallback: 20 chars"]}, {"index": 1695, "id": "NG2010", "symbol": "⏏", "issues": ["Long fallback: 17 chars"]}, {"index": 1696, "id": "NG2011", "symbol": "⭢", "issues": ["Long fallback: 18 chars"]}, {"index": 1697, "id": "NG2012", "symbol": "⯏", "issues": ["Long fallback: 20 chars"]}, {"index": 1698, "id": "NG2013", "symbol": "⋐", "issues": ["Long fallback: 18 chars"]}, {"index": 1699, "id": "NG2014", "symbol": "⫐", "issues": ["Long fallback: 20 chars"]}, {"index": 1700, "id": "NG2015", "symbol": "⢋", "issues": ["Long fallback: 20 chars"]}, {"index": 1701, "id": "NG2016", "symbol": "⋑", "issues": ["Long fallback: 20 chars"]}, {"index": 1702, "id": "NG2017", "symbol": "𝜕", "issues": ["Long fallback: 18 chars"]}, {"index": 1703, "id": "NG2018", "symbol": "⏑", "issues": ["Long fallback: 18 chars"]}, {"index": 1704, "id": "NG2019", "symbol": "⬚", "issues": ["Long fallback: 20 chars"]}, {"index": 1705, "id": "NG2020", "symbol": "⦦", "issues": ["Long fallback: 20 chars"]}, {"index": 1706, "id": "NG2021", "symbol": "⫑", "issues": ["Long fallback: 20 chars"]}, {"index": 1707, "id": "NG2022", "symbol": "⯑", "issues": ["Long fallback: 18 chars"]}, {"index": 1708, "id": "NG2023", "symbol": "⋒", "issues": ["Long fallback: 20 chars"]}, {"index": 1709, "id": "NG2024", "symbol": "✰", "issues": ["Long fallback: 20 chars"]}, {"index": 1710, "id": "NG2025", "symbol": "⏒", "issues": ["Long fallback: 20 chars"]}, {"index": 1711, "id": "NG2026", "symbol": "➭", "issues": ["Long fallback: 20 chars"]}, {"index": 1712, "id": "NG2027", "symbol": "⥨", "issues": ["Long fallback: 20 chars"]}, {"index": 1713, "id": "NG2028", "symbol": "⫒", "issues": ["Long fallback: 18 chars"]}, {"index": 1714, "id": "NG2029", "symbol": "⦟", "issues": ["Long fallback: 18 chars"]}, {"index": 1715, "id": "NG2030", "symbol": "⯒", "issues": ["Long fallback: 20 chars"]}, {"index": 1716, "id": "NG2031", "symbol": "⫓", "issues": ["Long fallback: 20 chars"]}, {"index": 1717, "id": "NG2032", "symbol": "⠷", "issues": ["Long fallback: 18 chars"]}, {"index": 1718, "id": "NG2033", "symbol": "⏔", "issues": ["Long fallback: 18 chars"]}, {"index": 1719, "id": "NG2034", "symbol": "⮏", "issues": ["Long fallback: 19 chars"]}, {"index": 1720, "id": "NG2035", "symbol": "⯔", "issues": ["Long fallback: 19 chars"]}, {"index": 1721, "id": "NG2036", "symbol": "⋕", "issues": ["Long fallback: 19 chars"]}, {"index": 1722, "id": "NG2037", "symbol": "⏕", "issues": ["Long fallback: 19 chars"]}, {"index": 1723, "id": "NG2038", "symbol": "⋖", "issues": ["Long fallback: 19 chars"]}, {"index": 1724, "id": "NG2039", "symbol": "⏖", "issues": ["Long fallback: 19 chars"]}, {"index": 1725, "id": "NG2040", "symbol": "⬄", "issues": ["Long fallback: 19 chars"]}, {"index": 1726, "id": "NG2041", "symbol": "⫖", "issues": ["Long fallback: 19 chars"]}, {"index": 1727, "id": "NG2042", "symbol": "⨽", "issues": ["Long fallback: 19 chars"]}, {"index": 1728, "id": "NG2043", "symbol": "⩌", "issues": ["Long fallback: 19 chars"]}, {"index": 1729, "id": "NG2044", "symbol": "⣷", "issues": ["Long fallback: 19 chars"]}, {"index": 1730, "id": "NG2045", "symbol": "⯃", "issues": ["Long fallback: 19 chars"]}, {"index": 1731, "id": "NG2046", "symbol": "⋗", "issues": ["Long fallback: 19 chars"]}, {"index": 1732, "id": "NG2047", "symbol": "⏗", "issues": ["Long fallback: 19 chars"]}, {"index": 1733, "id": "NG2048", "symbol": "⫾", "issues": ["Long fallback: 19 chars"]}], "integrity_score": 48.681640625}, "unicode_results": {"total_symbols": 2048, "safe_unicode": 1795, "unsafe_unicode": 233, "range_distribution": {"Geometric Shapes": 271, "Supplemental Mathematical Operators": 252, "Mathematical Operators": 242, "Miscellaneous Technical": 249, "Supplemental Arrows-B": 127, "Miscellaneous Mathematical Symbols-B": 128, "Miscellaneous Symbols and Arrows": 253, "Dingbats": 192, "Miscellaneous Symbols and Pictographs": 81}, "unsafe_symbols": [{"id": "NG0030", "symbol": "↢", "unicode_point": "U+21A2", "code_point": 8610}, {"id": "NG0033", "symbol": "⇵", "unicode_point": "U+21F5", "code_point": 8693}, {"id": "NG0034", "symbol": "⇣", "unicode_point": "U+21E3", "code_point": 8675}, {"id": "NG0042", "symbol": "↤", "unicode_point": "U+21A4", "code_point": 8612}, {"id": "NG0043", "symbol": "↰", "unicode_point": "U+21B0", "code_point": 8624}, {"id": "NG0045", "symbol": "↘", "unicode_point": "U+2198", "code_point": 8600}, {"id": "NG0055", "symbol": "←", "unicode_point": "U+2190", "code_point": 8592}, {"id": "NG0057", "symbol": "⇃", "unicode_point": "U+21C3", "code_point": 8643}, {"id": "NG0059", "symbol": "⇗", "unicode_point": "U+21D7", "code_point": 8663}, {"id": "NG0064", "symbol": "⚮", "unicode_point": "U+26AE", "code_point": 9902}, {"id": "NG0065", "symbol": "⇻", "unicode_point": "U+21FB", "code_point": 8699}, {"id": "NG0072", "symbol": "⇈", "unicode_point": "U+21C8", "code_point": 8648}, {"id": "NG0073", "symbol": "⚳", "unicode_point": "U+26B3", "code_point": 9907}, {"id": "NG0074", "symbol": "↨", "unicode_point": "U+21A8", "code_point": 8616}, {"id": "NG0079", "symbol": "⇦", "unicode_point": "U+21E6", "code_point": 8678}, {"id": "NG0080", "symbol": "⚼", "unicode_point": "U+26BC", "code_point": 9916}, {"id": "NG0084", "symbol": "⇆", "unicode_point": "U+21C6", "code_point": 8646}, {"id": "NG0087", "symbol": "⇟", "unicode_point": "U+21DF", "code_point": 8671}, {"id": "NG0103", "symbol": "⇑", "unicode_point": "U+21D1", "code_point": 8657}, {"id": "NG0107", "symbol": "⇅", "unicode_point": "U+21C5", "code_point": 8645}, {"id": "NG0111", "symbol": "⇎", "unicode_point": "U+21CE", "code_point": 8654}, {"id": "NG0114", "symbol": "↹", "unicode_point": "U+21B9", "code_point": 8633}, {"id": "NG0118", "symbol": "⇷", "unicode_point": "U+21F7", "code_point": 8695}, {"id": "NG0121", "symbol": "↥", "unicode_point": "U+21A5", "code_point": 8613}, {"id": "NG0128", "symbol": "⇪", "unicode_point": "U+21EA", "code_point": 8682}, {"id": "NG0131", "symbol": "↭", "unicode_point": "U+21AD", "code_point": 8621}, {"id": "NG0133", "symbol": "⇴", "unicode_point": "U+21F4", "code_point": 8692}, {"id": "NG0135", "symbol": "⇒", "unicode_point": "U+21D2", "code_point": 8658}, {"id": "NG0137", "symbol": "⇿", "unicode_point": "U+21FF", "code_point": 8703}, {"id": "NG0146", "symbol": "⇰", "unicode_point": "U+21F0", "code_point": 8688}, {"id": "NG0149", "symbol": "↪", "unicode_point": "U+21AA", "code_point": 8618}, {"id": "NG0150", "symbol": "↷", "unicode_point": "U+21B7", "code_point": 8631}, {"id": "NG0151", "symbol": "⚰", "unicode_point": "U+26B0", "code_point": 9904}, {"id": "NG0153", "symbol": "⇕", "unicode_point": "U+21D5", "code_point": 8661}, {"id": "NG0158", "symbol": "↽", "unicode_point": "U+21BD", "code_point": 8637}, {"id": "NG0162", "symbol": "⚢", "unicode_point": "U+26A2", "code_point": 9890}, {"id": "NG0166", "symbol": "⇂", "unicode_point": "U+21C2", "code_point": 8642}, {"id": "NG0168", "symbol": "⇠", "unicode_point": "U+21E0", "code_point": 8672}, {"id": "NG0171", "symbol": "↧", "unicode_point": "U+21A7", "code_point": 8615}, {"id": "NG0172", "symbol": "↮", "unicode_point": "U+21AE", "code_point": 8622}, {"id": "NG0178", "symbol": "↩", "unicode_point": "U+21A9", "code_point": 8617}, {"id": "NG0179", "symbol": "↗", "unicode_point": "U+2197", "code_point": 8599}, {"id": "NG0188", "symbol": "⇱", "unicode_point": "U+21F1", "code_point": 8689}, {"id": "NG0195", "symbol": "⇚", "unicode_point": "U+21DA", "code_point": 8666}, {"id": "NG0196", "symbol": "⇲", "unicode_point": "U+21F2", "code_point": 8690}, {"id": "NG0199", "symbol": "↾", "unicode_point": "U+21BE", "code_point": 8638}, {"id": "NG0208", "symbol": "⇢", "unicode_point": "U+21E2", "code_point": 8674}, {"id": "NG0209", "symbol": "↦", "unicode_point": "U+21A6", "code_point": 8614}, {"id": "NG0211", "symbol": "⇋", "unicode_point": "U+21CB", "code_point": 8651}, {"id": "NG0213", "symbol": "⇖", "unicode_point": "U+21D6", "code_point": 8662}, {"id": "NG0216", "symbol": "↖", "unicode_point": "U+2196", "code_point": 8598}, {"id": "NG0219", "symbol": "⇙", "unicode_point": "U+21D9", "code_point": 8665}, {"id": "NG0222", "symbol": "⇁", "unicode_point": "U+21C1", "code_point": 8641}, {"id": "NG0225", "symbol": "↿", "unicode_point": "U+21BF", "code_point": 8639}, {"id": "NG0231", "symbol": "⇽", "unicode_point": "U+21FD", "code_point": 8701}, {"id": "NG0232", "symbol": "⇤", "unicode_point": "U+21E4", "code_point": 8676}, {"id": "NG0236", "symbol": "⇀", "unicode_point": "U+21C0", "code_point": 8640}, {"id": "NG0240", "symbol": "⚗", "unicode_point": "U+2697", "code_point": 9879}, {"id": "NG0243", "symbol": "↜", "unicode_point": "U+219C", "code_point": 8604}, {"id": "NG0252", "symbol": "↝", "unicode_point": "U+219D", "code_point": 8605}, {"id": "NG0259", "symbol": "↑", "unicode_point": "U+2191", "code_point": 8593}, {"id": "NG0260", "symbol": "⇘", "unicode_point": "U+21D8", "code_point": 8664}, {"id": "NG0262", "symbol": "↛", "unicode_point": "U+219B", "code_point": 8603}, {"id": "NG0265", "symbol": "↠", "unicode_point": "U+21A0", "code_point": 8608}, {"id": "NG0269", "symbol": "↲", "unicode_point": "U+21B2", "code_point": 8626}, {"id": "NG0271", "symbol": "⇄", "unicode_point": "U+21C4", "code_point": 8644}, {"id": "NG0273", "symbol": "↯", "unicode_point": "U+21AF", "code_point": 8623}, {"id": "NG0274", "symbol": "⇇", "unicode_point": "U+21C7", "code_point": 8647}, {"id": "NG0278", "symbol": "⇝", "unicode_point": "U+21DD", "code_point": 8669}, {"id": "NG0282", "symbol": "↔", "unicode_point": "U+2194", "code_point": 8596}, {"id": "NG0285", "symbol": "⇳", "unicode_point": "U+21F3", "code_point": 8691}, {"id": "NG0287", "symbol": "↡", "unicode_point": "U+21A1", "code_point": 8609}, {"id": "NG0290", "symbol": "⇏", "unicode_point": "U+21CF", "code_point": 8655}, {"id": "NG0292", "symbol": "⇓", "unicode_point": "U+21D3", "code_point": 8659}, {"id": "NG0294", "symbol": "⇶", "unicode_point": "U+21F6", "code_point": 8694}, {"id": "NG0297", "symbol": "⇬", "unicode_point": "U+21EC", "code_point": 8684}, {"id": "NG0299", "symbol": "⚥", "unicode_point": "U+26A5", "code_point": 9893}, {"id": "NG0300", "symbol": "→", "unicode_point": "U+2192", "code_point": 8594}, {"id": "NG0301", "symbol": "⇩", "unicode_point": "U+21E9", "code_point": 8681}, {"id": "NG0304", "symbol": "⇼", "unicode_point": "U+21FC", "code_point": 8700}, {"id": "NG0307", "symbol": "⇸", "unicode_point": "U+21F8", "code_point": 8696}, {"id": "NG0308", "symbol": "⚛", "unicode_point": "U+269B", "code_point": 9883}, {"id": "NG0310", "symbol": "↶", "unicode_point": "U+21B6", "code_point": 8630}, {"id": "NG0314", "symbol": "⇺", "unicode_point": "U+21FA", "code_point": 8698}, {"id": "NG0317", "symbol": "↻", "unicode_point": "U+21BB", "code_point": 8635}, {"id": "NG0321", "symbol": "↸", "unicode_point": "U+21B8", "code_point": 8632}, {"id": "NG0335", "symbol": "⇉", "unicode_point": "U+21C9", "code_point": 8649}, {"id": "NG0340", "symbol": "⚖", "unicode_point": "U+2696", "code_point": 9878}, {"id": "NG0348", "symbol": "↳", "unicode_point": "U+21B3", "code_point": 8627}, {"id": "NG0351", "symbol": "↕", "unicode_point": "U+2195", "code_point": 8597}, {"id": "NG0373", "symbol": "↫", "unicode_point": "U+21AB", "code_point": 8619}, {"id": "NG0385", "symbol": "⚯", "unicode_point": "U+26AF", "code_point": 9903}, {"id": "NG0389", "symbol": "⚫", "unicode_point": "U+26AB", "code_point": 9899}, {"id": "NG0391", "symbol": "⚻", "unicode_point": "U+26BB", "code_point": 9915}, {"id": "NG0394", "symbol": "⇞", "unicode_point": "U+21DE", "code_point": 8670}, {"id": "NG0396", "symbol": "↣", "unicode_point": "U+21A3", "code_point": 8611}, {"id": "NG0397", "symbol": "⚝", "unicode_point": "U+269D", "code_point": 9885}, {"id": "NG0399", "symbol": "⇹", "unicode_point": "U+21F9", "code_point": 8697}, {"id": "NG0404", "symbol": "⚧", "unicode_point": "U+26A7", "code_point": 9895}, {"id": "NG0413", "symbol": "⇯", "unicode_point": "U+21EF", "code_point": 8687}, {"id": "NG0415", "symbol": "⇧", "unicode_point": "U+21E7", "code_point": 8679}, {"id": "NG0417", "symbol": "↵", "unicode_point": "U+21B5", "code_point": 8629}, {"id": "NG0423", "symbol": "↬", "unicode_point": "U+21AC", "code_point": 8620}, {"id": "NG0425", "symbol": "⚣", "unicode_point": "U+26A3", "code_point": 9891}, {"id": "NG0427", "symbol": "⚬", "unicode_point": "U+26AC", "code_point": 9900}, {"id": "NG0431", "symbol": "↺", "unicode_point": "U+21BA", "code_point": 8634}, {"id": "NG0434", "symbol": "⇜", "unicode_point": "U+21DC", "code_point": 8668}, {"id": "NG0437", "symbol": "⚜", "unicode_point": "U+269C", "code_point": 9884}, {"id": "NG0440", "symbol": "↓", "unicode_point": "U+2193", "code_point": 8595}, {"id": "NG0451", "symbol": "⇊", "unicode_point": "U+21CA", "code_point": 8650}, {"id": "NG0452", "symbol": "⇥", "unicode_point": "U+21E5", "code_point": 8677}, {"id": "NG0476", "symbol": "⚹", "unicode_point": "U+26B9", "code_point": 9913}, {"id": "NG0477", "symbol": "⚠", "unicode_point": "U+26A0", "code_point": 9888}, {"id": "NG0478", "symbol": "⇍", "unicode_point": "U+21CD", "code_point": 8653}, {"id": "NG0480", "symbol": "⚟", "unicode_point": "U+269F", "code_point": 9887}, {"id": "NG0484", "symbol": "⚶", "unicode_point": "U+26B6", "code_point": 9910}, {"id": "NG0485", "symbol": "⇾", "unicode_point": "U+21FE", "code_point": 8702}, {"id": "NG0488", "symbol": "⇐", "unicode_point": "U+21D0", "code_point": 8656}, {"id": "NG0492", "symbol": "⇮", "unicode_point": "U+21EE", "code_point": 8686}, {"id": "NG0493", "symbol": "⚩", "unicode_point": "U+26A9", "code_point": 9897}, {"id": "NG0501", "symbol": "⇫", "unicode_point": "U+21EB", "code_point": 8683}, {"id": "NG0507", "symbol": "⚤", "unicode_point": "U+26A4", "code_point": 9892}, {"id": "NG0510", "symbol": "⚪", "unicode_point": "U+26AA", "code_point": 9898}, {"id": "NG0512", "symbol": "↼", "unicode_point": "U+21BC", "code_point": 8636}, {"id": "NG0520", "symbol": "⇡", "unicode_point": "U+21E1", "code_point": 8673}, {"id": "NG0885", "symbol": "⣖", "unicode_point": "U+28D6", "code_point": 10454}, {"id": "NG0888", "symbol": "⢠", "unicode_point": "U+28A0", "code_point": 10400}, {"id": "NG0893", "symbol": "⠏", "unicode_point": "U+280F", "code_point": 10255}, {"id": "NG0907", "symbol": "⠌", "unicode_point": "U+280C", "code_point": 10252}, {"id": "NG0910", "symbol": "⢓", "unicode_point": "U+2893", "code_point": 10387}, {"id": "NG0920", "symbol": "⠣", "unicode_point": "U+2823", "code_point": 10275}, {"id": "NG0937", "symbol": "⣋", "unicode_point": "U+28CB", "code_point": 10443}, {"id": "NG0941", "symbol": "⢐", "unicode_point": "U+2890", "code_point": 10384}, {"id": "NG0949", "symbol": "⡗", "unicode_point": "U+2857", "code_point": 10327}, {"id": "NG0974", "symbol": "⣠", "unicode_point": "U+28E0", "code_point": 10464}, {"id": "NG0983", "symbol": "⢖", "unicode_point": "U+2896", "code_point": 10390}, {"id": "NG0985", "symbol": "𝝏", "unicode_point": "U+1D74F", "code_point": 120655}, {"id": "NG1003", "symbol": "⠁", "unicode_point": "U+2801", "code_point": 10241}, {"id": "NG1007", "symbol": "⣐", "unicode_point": "U+28D0", "code_point": 10448}, {"id": "NG1014", "symbol": "⠮", "unicode_point": "U+282E", "code_point": 10286}, {"id": "NG1019", "symbol": "⠀", "unicode_point": "U+2800", "code_point": 10240}, {"id": "NG1028", "symbol": "⠂", "unicode_point": "U+2802", "code_point": 10242}, {"id": "NG1038", "symbol": "⢈", "unicode_point": "U+2888", "code_point": 10376}, {"id": "NG1052", "symbol": "⡘", "unicode_point": "U+2858", "code_point": 10328}, {"id": "NG1066", "symbol": "⡔", "unicode_point": "U+2854", "code_point": 10324}, {"id": "NG1074", "symbol": "⠎", "unicode_point": "U+280E", "code_point": 10254}, {"id": "NG1082", "symbol": "⠹", "unicode_point": "U+2839", "code_point": 10297}, {"id": "NG1097", "symbol": "⣛", "unicode_point": "U+28DB", "code_point": 10459}, {"id": "NG1112", "symbol": "⢧", "unicode_point": "U+28A7", "code_point": 10407}, {"id": "NG1130", "symbol": "⡣", "unicode_point": "U+2863", "code_point": 10339}, {"id": "NG1145", "symbol": "⡑", "unicode_point": "U+2851", "code_point": 10321}, {"id": "NG1146", "symbol": "⢣", "unicode_point": "U+28A3", "code_point": 10403}, {"id": "NG1153", "symbol": "⠿", "unicode_point": "U+283F", "code_point": 10303}, {"id": "NG1160", "symbol": "⣃", "unicode_point": "U+28C3", "code_point": 10435}, {"id": "NG1187", "symbol": "⠒", "unicode_point": "U+2812", "code_point": 10258}, {"id": "NG1195", "symbol": "⣹", "unicode_point": "U+28F9", "code_point": 10489}, {"id": "NG1208", "symbol": "⢁", "unicode_point": "U+2881", "code_point": 10369}, {"id": "NG1211", "symbol": "⡶", "unicode_point": "U+2876", "code_point": 10358}, {"id": "NG1214", "symbol": "⢹", "unicode_point": "U+28B9", "code_point": 10425}, {"id": "NG1226", "symbol": "⣘", "unicode_point": "U+28D8", "code_point": 10456}, {"id": "NG1232", "symbol": "⣂", "unicode_point": "U+28C2", "code_point": 10434}, {"id": "NG1233", "symbol": "⡆", "unicode_point": "U+2846", "code_point": 10310}, {"id": "NG1236", "symbol": "⡭", "unicode_point": "U+286D", "code_point": 10349}, {"id": "NG1244", "symbol": "⣩", "unicode_point": "U+28E9", "code_point": 10473}, {"id": "NG1258", "symbol": "⢵", "unicode_point": "U+28B5", "code_point": 10421}, {"id": "NG1267", "symbol": "⣔", "unicode_point": "U+28D4", "code_point": 10452}, {"id": "NG1274", "symbol": "⡪", "unicode_point": "U+286A", "code_point": 10346}, {"id": "NG1276", "symbol": "⡡", "unicode_point": "U+2861", "code_point": 10337}, {"id": "NG1278", "symbol": "⣙", "unicode_point": "U+28D9", "code_point": 10457}, {"id": "NG1304", "symbol": "⠋", "unicode_point": "U+280B", "code_point": 10251}, {"id": "NG1314", "symbol": "⠗", "unicode_point": "U+2817", "code_point": 10263}, {"id": "NG1318", "symbol": "⢳", "unicode_point": "U+28B3", "code_point": 10419}, {"id": "NG1329", "symbol": "⢩", "unicode_point": "U+28A9", "code_point": 10409}, {"id": "NG1332", "symbol": "⡾", "unicode_point": "U+287E", "code_point": 10366}, {"id": "NG1360", "symbol": "⠰", "unicode_point": "U+2830", "code_point": 10288}, {"id": "NG1371", "symbol": "⡄", "unicode_point": "U+2844", "code_point": 10308}, {"id": "NG1396", "symbol": "⣥", "unicode_point": "U+28E5", "code_point": 10469}, {"id": "NG1451", "symbol": "⣫", "unicode_point": "U+28EB", "code_point": 10475}, {"id": "NG1464", "symbol": "⡧", "unicode_point": "U+2867", "code_point": 10343}, {"id": "NG1474", "symbol": "⡠", "unicode_point": "U+2860", "code_point": 10336}, {"id": "NG1513", "symbol": "⢉", "unicode_point": "U+2889", "code_point": 10377}, {"id": "NG1534", "symbol": "⣳", "unicode_point": "U+28F3", "code_point": 10483}, {"id": "NG1537", "symbol": "⡦", "unicode_point": "U+2866", "code_point": 10342}, {"id": "NG1547", "symbol": "⠴", "unicode_point": "U+2834", "code_point": 10292}, {"id": "NG1548", "symbol": "⡍", "unicode_point": "U+284D", "code_point": 10317}, {"id": "NG1549", "symbol": "⠾", "unicode_point": "U+283E", "code_point": 10302}, {"id": "NG1555", "symbol": "𝝯", "unicode_point": "U+1D76F", "code_point": 120687}, {"id": "NG1561", "symbol": "⡟", "unicode_point": "U+285F", "code_point": 10335}, {"id": "NG1562", "symbol": "⣉", "unicode_point": "U+28C9", "code_point": 10441}, {"id": "NG1566", "symbol": "⣿", "unicode_point": "U+28FF", "code_point": 10495}, {"id": "NG1572", "symbol": "⢲", "unicode_point": "U+28B2", "code_point": 10418}, {"id": "NG1573", "symbol": "⡲", "unicode_point": "U+2872", "code_point": 10354}, {"id": "NG1574", "symbol": "⣱", "unicode_point": "U+28F1", "code_point": 10481}, {"id": "NG1594", "symbol": "⡬", "unicode_point": "U+286C", "code_point": 10348}, {"id": "NG1603", "symbol": "⡃", "unicode_point": "U+2843", "code_point": 10307}, {"id": "NG1623", "symbol": "⠥", "unicode_point": "U+2825", "code_point": 10277}, {"id": "NG1627", "symbol": "⠆", "unicode_point": "U+2806", "code_point": 10246}, {"id": "NG1642", "symbol": "⡢", "unicode_point": "U+2862", "code_point": 10338}, {"id": "NG1668", "symbol": "⠼", "unicode_point": "U+283C", "code_point": 10300}, {"id": "NG1671", "symbol": "⢗", "unicode_point": "U+2897", "code_point": 10391}, {"id": "NG1696", "symbol": "⣶", "unicode_point": "U+28F6", "code_point": 10486}, {"id": "NG1712", "symbol": "⢿", "unicode_point": "U+28BF", "code_point": 10431}, {"id": "NG1730", "symbol": "⠧", "unicode_point": "U+2827", "code_point": 10279}, {"id": "NG1779", "symbol": "⢂", "unicode_point": "U+2882", "code_point": 10370}, {"id": "NG1782", "symbol": "⢼", "unicode_point": "U+28BC", "code_point": 10428}, {"id": "NG1791", "symbol": "⢬", "unicode_point": "U+28AC", "code_point": 10412}, {"id": "NG1805", "symbol": "⣁", "unicode_point": "U+28C1", "code_point": 10433}, {"id": "NG1827", "symbol": "⠲", "unicode_point": "U+2832", "code_point": 10290}, {"id": "NG1830", "symbol": "⣕", "unicode_point": "U+28D5", "code_point": 10453}, {"id": "NG1842", "symbol": "𝞉", "unicode_point": "U+1D789", "code_point": 120713}, {"id": "NG1844", "symbol": "⣡", "unicode_point": "U+28E1", "code_point": 10465}, {"id": "NG1846", "symbol": "⢢", "unicode_point": "U+28A2", "code_point": 10402}, {"id": "NG1850", "symbol": "⢞", "unicode_point": "U+289E", "code_point": 10398}, {"id": "NG1861", "symbol": "⢡", "unicode_point": "U+28A1", "code_point": 10401}, {"id": "NG1866", "symbol": "⢫", "unicode_point": "U+28AB", "code_point": 10411}, {"id": "NG1890", "symbol": "⠕", "unicode_point": "U+2815", "code_point": 10261}, {"id": "NG1928", "symbol": "⠉", "unicode_point": "U+2809", "code_point": 10249}, {"id": "NG1931", "symbol": "⢸", "unicode_point": "U+28B8", "code_point": 10424}, {"id": "NG1933", "symbol": "⢮", "unicode_point": "U+28AE", "code_point": 10414}, {"id": "NG1940", "symbol": "⣽", "unicode_point": "U+28FD", "code_point": 10493}, {"id": "NG1945", "symbol": "⠠", "unicode_point": "U+2820", "code_point": 10272}, {"id": "NG1950", "symbol": "⡹", "unicode_point": "U+2879", "code_point": 10361}, {"id": "NG1955", "symbol": "⠚", "unicode_point": "U+281A", "code_point": 10266}, {"id": "NG1956", "symbol": "⣭", "unicode_point": "U+28ED", "code_point": 10477}, {"id": "NG1962", "symbol": "⠑", "unicode_point": "U+2811", "code_point": 10257}, {"id": "NG1969", "symbol": "⠙", "unicode_point": "U+2819", "code_point": 10265}, {"id": "NG1970", "symbol": "⣧", "unicode_point": "U+28E7", "code_point": 10471}, {"id": "NG1982", "symbol": "⠻", "unicode_point": "U+283B", "code_point": 10299}, {"id": "NG2003", "symbol": "⡳", "unicode_point": "U+2873", "code_point": 10355}, {"id": "NG2015", "symbol": "⢋", "unicode_point": "U+288B", "code_point": 10379}, {"id": "NG2017", "symbol": "𝜕", "unicode_point": "U+1D715", "code_point": 120597}, {"id": "NG2032", "symbol": "⠷", "unicode_point": "U+2837", "code_point": 10295}, {"id": "NG2044", "symbol": "⣷", "unicode_point": "U+28F7", "code_point": 10487}]}, "domain_results": {"total_symbols": 2048, "domain_counts": {"operator": 99, "logic": 120, "reasoning": 56, "structure": 91, "flow": 107, "memory": 94, "advanced_coding": 306, "meta_programming": 128, "distributed_systems": 108, "quantum_computing": 76, "symbolic_ai": 76, "neural_architectures": 72, "formal_verification": 49, "category_theory": 43, "type_theory": 37, "concurrency_advanced": 79, "machine_learning": 123, "mathematical_structures": 64, "philosophical_concepts": 64, "cognitive_modeling": 54, "reserved_expansion": 89, "cognitive_maps": 32, "final_domains": 81}, "category_counts": {"unknown": 20, "operator": 95, "memory": 94, "logic": 112, "structure": 89, "flow": 102, "reasoning": 55, "advanced_coding": 306, "meta_programming": 128, "distributed_systems": 108, "quantum_computing": 76, "symbolic_ai": 76, "neural_architectures": 72, "formal_verification": 49, "category_theory": 43, "type_theory": 37, "concurrency_advanced": 79, "machine_learning": 123, "mathematical_structures": 64, "philosophical_concepts": 64, "cognitive_modeling": 54, "reserved_expansion": 89, "cognitive_maps": 32, "final_domains": 81}, "tier_distribution": {"unknown": 567, "god": 1481}, "generator_distribution": {"unknown": 20, "simple": 492, "reasoning_specialized": 55, "god_tier_v1": 1167, "ultra_pipeline": 56, "direct_completer": 177, "final_completion": 81}}, "quality_results": {"total_symbols": 2048, "score_stats": {"min": 93.0, "max": 100.0, "avg": 96.970361328125, "below_95": 16}, "token_cost_stats": {"min": 1, "max": 3, "avg": 1.054240631163708, "above_2": 55}, "token_density_stats": {"min": 0.9, "max": 1.0, "avg": 0.94828025477707, "below_09": 0}}}