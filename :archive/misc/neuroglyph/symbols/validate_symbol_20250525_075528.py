"""
Validatore simboli NEUROGLYPH - verifica unicità, struttura, fallback, token cost
"""
import json
import unicodedata
from pathlib import Path

symbol_path = Path("core/symbols_ultra.json")
registry_path = Path("symbol_validation/symbols_registry.json")

def validate_symbol(symbol_entry):
    errors = []
    code = symbol_entry.get("code")
    unicode_char = symbol_entry.get("symbol")
    fallback = symbol_entry.get("fallback")

    if len(unicode_char) != 1:
        errors.append("Symbol must be a single Unicode character.")
    try:
        unicodedata.name(unicode_char)
    except ValueError:
        errors.append("Symbol has invalid Unicode codepoint.")
    if not code.startswith("ng:"):
        errors.append("Code must start with 'ng:'.")
    if not fallback:
        errors.append("Missing ASCII fallback.")
    return errors

def main():
    with open(symbol_path, "r", encoding="utf-8") as f:
        symbols = json.load(f)["symbols"]

    seen_codes = set()
    seen_symbols = set()
    errors_total = 0

    for sym in symbols:
        errs = validate_symbol(sym)
        if sym["code"] in seen_codes:
            errs.append("Duplicate code")
        if sym["symbol"] in seen_symbols:
            errs.append("Duplicate symbol")

        if errs:
            print(f"❌ {sym['symbol']} ({sym['code']}):")
            for e in errs:
                print(f"   - {e}")
            errors_total += 1

        seen_codes.add(sym["code"])
        seen_symbols.add(sym["symbol"])

    print(f"✅ Validation complete. {errors_total} errors found.")

if __name__ == "__main__":
    main()
