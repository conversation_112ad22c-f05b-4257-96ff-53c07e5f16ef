{"review_timestamp": "20250525_190243", "summary": {"severity_a_count": 1001, "severity_b_count": 388, "total_critical": 1389}, "patterns": {"severity_a": {"low_score_count": 267, "unicode_risky_count": 734, "both_issues_count": 0, "score_distribution": {"90-99": 734, "0-9": 267}, "unicode_ranges": {"miscellaneous_symbols": 138, "unknown": 267, "emoji_extended": 596}, "categories": {"logic": 30, "memory": 23, "flow": 27, "operator": 28, "structure": 22, "async_concurrency": 70, "classes_oop": 71, "data_structures": 60, "final_completion": 66, "advanced_coding": 164, "meta_programming": 33, "distributed_systems": 51, "quantum_computing": 8, "symbolic_ai": 25, "neural_architectures": 9, "formal_verification": 28, "category_theory": 22, "type_theory": 15, "concurrency_advanced": 45, "machine_learning": 68, "mathematical_structures": 30, "philosophical_concepts": 36, "cognitive_modeling": 26, "reserved_expansion": 44}, "auto_generated_count": 734}, "severity_b": {"low_score_count": 0, "unicode_risky_count": 29, "both_issues_count": 0, "score_distribution": {"90-99": 388}, "unicode_ranges": {"miscellaneous_symbols": 4, "safe": 356, "emoji_extended": 28}, "categories": {"memory": 7, "structure": 39, "logic": 10, "advanced_coding": 83, "meta_programming": 18, "distributed_systems": 35, "quantum_computing": 8, "symbolic_ai": 13, "neural_architectures": 4, "formal_verification": 13, "category_theory": 16, "type_theory": 9, "concurrency_advanced": 24, "machine_learning": 38, "mathematical_structures": 20, "philosophical_concepts": 11, "cognitive_modeling": 14, "reserved_expansion": 26}, "auto_generated_count": 388}}, "recommendations": {"severity_a_actions": {"NG0025": {"action": "REPLACE_UNICODE", "reason": "Unicode problematico: miscellaneous_symbols; Auto-generato", "symbol": "☬", "score": "95.0", "issues": "Risky Unicode range: miscellaneous_symbols; Generic numbered name"}, "NG0026": {"action": "REPLACE_UNICODE", "reason": "Unicode problematico: miscellaneous_symbols; Auto-generato", "symbol": "⛑", "score": "95.0", "issues": "Risky Unicode range: miscellaneous_symbols; Generic numbered name"}, "NG0041": {"action": "REPLACE_UNICODE", "reason": "Unicode problematico: miscellaneous_symbols; Auto-generato", "symbol": "♄", "score": "95.0", "issues": "Risky Unicode range: miscellaneous_symbols; Generic numbered name"}, "NG0044": {"action": "REPLACE_UNICODE", "reason": "Unicode problematico: miscellaneous_symbols; Auto-generato", "symbol": "⚌", "score": "95.0", "issues": "Risky Unicode range: miscellaneous_symbols; Generic numbered name"}, "NG0047": {"action": "REPLACE_UNICODE", "reason": "Unicode problematico: miscellaneous_symbols; Auto-generato", "symbol": "☤", "score": "95.0", "issues": "Risky Unicode range: miscellaneous_symbols; Generic numbered name"}, "NG0048": {"action": "REPLACE_UNICODE", "reason": "Unicode problematico: miscellaneous_symbols; Auto-generato", "symbol": "⚓", "score": "95.0", "issues": "Risky Unicode range: miscellaneous_symbols; Generic numbered name"}, "NG0053": {"action": "REPLACE_UNICODE", "reason": "Unicode problematico: miscellaneous_symbols; Auto-generato", "symbol": "☴", "score": "95.0", "issues": "Risky Unicode range: miscellaneous_symbols; Generic numbered name"}, "NG0058": {"action": "REPLACE_UNICODE", "reason": "Unicode problematico: miscellaneous_symbols; Auto-generato", "symbol": "♑", "score": "95.0", "issues": "Risky Unicode range: miscellaneous_symbols; Generic numbered name"}, "NG0061": {"action": "REPLACE_UNICODE", "reason": "Unicode problematico: miscellaneous_symbols; Auto-generato", "symbol": "⚁", "score": "95.0", "issues": "Risky Unicode range: miscellaneous_symbols; Generic numbered name"}, "NG0075": {"action": "REPLACE_UNICODE", "reason": "Unicode problematico: miscellaneous_symbols; Auto-generato", "symbol": "♃", "score": "95.0", "issues": "Risky Unicode range: miscellaneous_symbols; Generic numbered name"}, "NG0077": {"action": "REPLACE_UNICODE", "reason": "Unicode problematico: miscellaneous_symbols; Auto-generato", "symbol": "⛉", "score": "95.0", "issues": "Risky Unicode range: miscellaneous_symbols; Generic numbered name"}, "NG0078": {"action": "REPLACE_UNICODE", "reason": "Unicode problematico: miscellaneous_symbols; Auto-generato", "symbol": "♒", "score": "95.0", "issues": "Risky Unicode range: miscellaneous_symbols; Long fallback: 11 chars; Generic numbered name"}, "NG0091": {"action": "REPLACE_UNICODE", "reason": "Unicode problematico: miscellaneous_symbols; Auto-generato", "symbol": "♶", "score": "95.0", "issues": "Risky Unicode range: miscellaneous_symbols; Generic numbered name"}, "NG0092": {"action": "REPLACE_UNICODE", "reason": "Unicode problematico: miscellaneous_symbols; Auto-generato", "symbol": "♋", "score": "95.0", "issues": "Risky Unicode range: miscellaneous_symbols; Generic numbered name"}, "NG0093": {"action": "REPLACE_UNICODE", "reason": "Unicode problematico: miscellaneous_symbols; Auto-generato", "symbol": "☏", "score": "95.0", "issues": "Risky Unicode range: miscellaneous_symbols; Long fallback: 11 chars; Generic numbered name"}, "NG0096": {"action": "REPLACE_UNICODE", "reason": "Unicode problematico: miscellaneous_symbols; Auto-generato", "symbol": "⚆", "score": "95.0", "issues": "Risky Unicode range: miscellaneous_symbols; Generic numbered name"}, "NG0098": {"action": "REPLACE_UNICODE", "reason": "Unicode problematico: miscellaneous_symbols; Auto-generato", "symbol": "☘", "score": "95.0", "issues": "Risky Unicode range: miscellaneous_symbols; Generic numbered name"}, "NG0099": {"action": "REPLACE_UNICODE", "reason": "Unicode problematico: miscellaneous_symbols; Auto-generato", "symbol": "♬", "score": "95.0", "issues": "Risky Unicode range: miscellaneous_symbols; Generic numbered name"}, "NG0106": {"action": "REPLACE_UNICODE", "reason": "Unicode problematico: miscellaneous_symbols; Auto-generato", "symbol": "♨", "score": "95.0", "issues": "Risky Unicode range: miscellaneous_symbols; Generic numbered name"}, "NG0115": {"action": "REPLACE_UNICODE", "reason": "Unicode problematico: miscellaneous_symbols; Auto-generato", "symbol": "⛧", "score": "95.0", "issues": "Risky Unicode range: miscellaneous_symbols; Generic numbered name"}, "NG0116": {"action": "REPLACE_UNICODE", "reason": "Unicode problematico: miscellaneous_symbols; Auto-generato", "symbol": "☯", "score": "95.0", "issues": "Risky Unicode range: miscellaneous_symbols; Generic numbered name"}, "NG0122": {"action": "REPLACE_UNICODE", "reason": "Unicode problematico: miscellaneous_symbols; Auto-generato", "symbol": "♉", "score": "95.0", "issues": "Risky Unicode range: miscellaneous_symbols; Generic numbered name"}, "NG0124": {"action": "REPLACE_UNICODE", "reason": "Unicode problematico: miscellaneous_symbols; Auto-generato", "symbol": "⚒", "score": "95.0", "issues": "Risky Unicode range: miscellaneous_symbols; Generic numbered name"}, "NG0127": {"action": "REPLACE_UNICODE", "reason": "Unicode problematico: miscellaneous_symbols; Auto-generato", "symbol": "☙", "score": "95.0", "issues": "Risky Unicode range: miscellaneous_symbols; Generic numbered name"}, "NG0130": {"action": "REPLACE_UNICODE", "reason": "Unicode problematico: miscellaneous_symbols; Auto-generato", "symbol": "♊", "score": "95.0", "issues": "Risky Unicode range: miscellaneous_symbols; Generic numbered name"}, "NG0139": {"action": "REPLACE_UNICODE", "reason": "Unicode problematico: miscellaneous_symbols; Auto-generato", "symbol": "☕", "score": "95.0", "issues": "Risky Unicode range: miscellaneous_symbols; Generic numbered name"}, "NG0159": {"action": "REPLACE_UNICODE", "reason": "Unicode problematico: miscellaneous_symbols; Auto-generato", "symbol": "♅", "score": "95.0", "issues": "Risky Unicode range: miscellaneous_symbols; Generic numbered name"}, "NG0160": {"action": "REPLACE_UNICODE", "reason": "Unicode problematico: miscellaneous_symbols; Auto-generato", "symbol": "♽", "score": "95.0", "issues": "Risky Unicode range: miscellaneous_symbols; Generic numbered name"}, "NG0161": {"action": "REPLACE_UNICODE", "reason": "Unicode problematico: miscellaneous_symbols; Auto-generato", "symbol": "⛢", "score": "95.0", "issues": "Risky Unicode range: miscellaneous_symbols; Generic numbered name"}, "NG0167": {"action": "REPLACE_UNICODE", "reason": "Unicode problematico: miscellaneous_symbols; Auto-generato", "symbol": "⚑", "score": "95.0", "issues": "Risky Unicode range: miscellaneous_symbols; Generic numbered name"}, "NG0169": {"action": "REPLACE_UNICODE", "reason": "Unicode problematico: miscellaneous_symbols; Auto-generato", "symbol": "♷", "score": "95.0", "issues": "Risky Unicode range: miscellaneous_symbols; Generic numbered name"}, "NG0170": {"action": "REPLACE_UNICODE", "reason": "Unicode problematico: miscellaneous_symbols; Auto-generato", "symbol": "⚈", "score": "95.0", "issues": "Risky Unicode range: miscellaneous_symbols; Long fallback: 11 chars; Generic numbered name"}, "NG0180": {"action": "REPLACE_UNICODE", "reason": "Unicode problematico: miscellaneous_symbols; Auto-generato", "symbol": "⚘", "score": "95.0", "issues": "Risky Unicode range: miscellaneous_symbols; Long fallback: 11 chars; Generic numbered name"}, "NG0181": {"action": "REPLACE_UNICODE", "reason": "Unicode problematico: miscellaneous_symbols; Auto-generato", "symbol": "☛", "score": "95.0", "issues": "Risky Unicode range: miscellaneous_symbols; Generic numbered name"}, "NG0185": {"action": "REPLACE_UNICODE", "reason": "Unicode problematico: miscellaneous_symbols; Auto-generato", "symbol": "⛡", "score": "95.0", "issues": "Risky Unicode range: miscellaneous_symbols; Long fallback: 11 chars; Generic numbered name"}, "NG0187": {"action": "REPLACE_UNICODE", "reason": "Unicode problematico: miscellaneous_symbols; Auto-generato", "symbol": "⛽", "score": "95.0", "issues": "Risky Unicode range: miscellaneous_symbols; Generic numbered name"}, "NG0203": {"action": "REPLACE_UNICODE", "reason": "Unicode problematico: miscellaneous_symbols; Auto-generato", "symbol": "⛁", "score": "95.0", "issues": "Risky Unicode range: miscellaneous_symbols; Generic numbered name"}, "NG0205": {"action": "REPLACE_UNICODE", "reason": "Unicode problematico: miscellaneous_symbols; Auto-generato", "symbol": "♫", "score": "95.0", "issues": "Risky Unicode range: miscellaneous_symbols; Generic numbered name"}, "NG0214": {"action": "REPLACE_UNICODE", "reason": "Unicode problematico: miscellaneous_symbols; Auto-generato", "symbol": "☸", "score": "95.0", "issues": "Risky Unicode range: miscellaneous_symbols; Generic numbered name"}, "NG0215": {"action": "REPLACE_UNICODE", "reason": "Unicode problematico: miscellaneous_symbols; Auto-generato", "symbol": "⛕", "score": "95.0", "issues": "Risky Unicode range: miscellaneous_symbols; Generic numbered name"}, "NG0218": {"action": "REPLACE_UNICODE", "reason": "Unicode problematico: miscellaneous_symbols; Auto-generato", "symbol": "⛨", "score": "95.0", "issues": "Risky Unicode range: miscellaneous_symbols; Generic numbered name"}, "NG0221": {"action": "REPLACE_UNICODE", "reason": "Unicode problematico: miscellaneous_symbols; Auto-generato", "symbol": "⚉", "score": "95.0", "issues": "Risky Unicode range: miscellaneous_symbols; Generic numbered name"}, "NG0227": {"action": "REPLACE_UNICODE", "reason": "Unicode problematico: miscellaneous_symbols; Auto-generato", "symbol": "☑", "score": "95.0", "issues": "Risky Unicode range: miscellaneous_symbols; Generic numbered name"}, "NG0228": {"action": "REPLACE_UNICODE", "reason": "Unicode problematico: miscellaneous_symbols; Auto-generato", "symbol": "☿", "score": "95.0", "issues": "Risky Unicode range: miscellaneous_symbols; Long fallback: 12 chars; Generic numbered name"}, "NG0229": {"action": "REPLACE_UNICODE", "reason": "Unicode problematico: miscellaneous_symbols; Auto-generato", "symbol": "♮", "score": "95.0", "issues": "Risky Unicode range: miscellaneous_symbols; Generic numbered name"}, "NG0233": {"action": "REPLACE_UNICODE", "reason": "Unicode problematico: miscellaneous_symbols; Auto-generato", "symbol": "☧", "score": "95.0", "issues": "Risky Unicode range: miscellaneous_symbols; Generic numbered name"}, "NG0234": {"action": "REPLACE_UNICODE", "reason": "Unicode problematico: miscellaneous_symbols; Auto-generato", "symbol": "☮", "score": "95.0", "issues": "Risky Unicode range: miscellaneous_symbols; Generic numbered name"}, "NG0235": {"action": "REPLACE_UNICODE", "reason": "Unicode problematico: miscellaneous_symbols; Auto-generato", "symbol": "⚋", "score": "95.0", "issues": "Risky Unicode range: miscellaneous_symbols; Generic numbered name"}, "NG0238": {"action": "REPLACE_UNICODE", "reason": "Unicode problematico: miscellaneous_symbols; Auto-generato", "symbol": "⛣", "score": "95.0", "issues": "Risky Unicode range: miscellaneous_symbols; Generic numbered name"}, "NG0253": {"action": "REPLACE_UNICODE", "reason": "Unicode problematico: miscellaneous_symbols; Auto-generato", "symbol": "⛐", "score": "95.0", "issues": "Risky Unicode range: miscellaneous_symbols; Generic numbered name"}, "NG0258": {"action": "REPLACE_UNICODE", "reason": "Unicode problematico: miscellaneous_symbols; Auto-generato", "symbol": "☝", "score": "95.0", "issues": "Risky Unicode range: miscellaneous_symbols; Generic numbered name"}, "NG0264": {"action": "REPLACE_UNICODE", "reason": "Unicode problematico: miscellaneous_symbols; Auto-generato", "symbol": "⚂", "score": "95.0", "issues": "Risky Unicode range: miscellaneous_symbols; Generic numbered name"}, "NG0270": {"action": "REPLACE_UNICODE", "reason": "Unicode problematico: miscellaneous_symbols; Auto-generato", "symbol": "☻", "score": "95.0", "issues": "Risky Unicode range: miscellaneous_symbols; Generic numbered name"}, "NG0275": {"action": "REPLACE_UNICODE", "reason": "Unicode problematico: miscellaneous_symbols; Auto-generato", "symbol": "♙", "score": "95.0", "issues": "Risky Unicode range: miscellaneous_symbols; Generic numbered name"}, "NG0291": {"action": "REPLACE_UNICODE", "reason": "Unicode problematico: miscellaneous_symbols; Auto-generato", "symbol": "☊", "score": "95.0", "issues": "Risky Unicode range: miscellaneous_symbols; Generic numbered name"}, "NG0293": {"action": "REPLACE_UNICODE", "reason": "Unicode problematico: miscellaneous_symbols; Auto-generato", "symbol": "☶", "score": "95.0", "issues": "Risky Unicode range: miscellaneous_symbols; Generic numbered name"}, "NG0295": {"action": "REPLACE_UNICODE", "reason": "Unicode problematico: miscellaneous_symbols; Auto-generato", "symbol": "☦", "score": "95.0", "issues": "Risky Unicode range: miscellaneous_symbols; Generic numbered name"}, "NG0302": {"action": "REPLACE_UNICODE", "reason": "Unicode problematico: miscellaneous_symbols; Auto-generato", "symbol": "♭", "score": "95.0", "issues": "Risky Unicode range: miscellaneous_symbols; Generic numbered name"}, "NG0306": {"action": "REPLACE_UNICODE", "reason": "Unicode problematico: miscellaneous_symbols; Auto-generato", "symbol": "♥", "score": "95.0", "issues": "Risky Unicode range: miscellaneous_symbols; Generic numbered name"}, "NG0313": {"action": "REPLACE_UNICODE", "reason": "Unicode problematico: miscellaneous_symbols; Auto-generato", "symbol": "⛍", "score": "95.0", "issues": "Risky Unicode range: miscellaneous_symbols; Long fallback: 12 chars; Generic numbered name"}, "NG0315": {"action": "REPLACE_UNICODE", "reason": "Unicode problematico: miscellaneous_symbols; Auto-generato", "symbol": "☽", "score": "95.0", "issues": "Risky Unicode range: miscellaneous_symbols; Generic numbered name"}, "NG0320": {"action": "REPLACE_UNICODE", "reason": "Unicode problematico: miscellaneous_symbols; Auto-generato", "symbol": "☀", "score": "95.0", "issues": "Risky Unicode range: miscellaneous_symbols; Generic numbered name"}, "NG0323": {"action": "REPLACE_UNICODE", "reason": "Unicode problematico: miscellaneous_symbols; Auto-generato", "symbol": "♡", "score": "95.0", "issues": "Risky Unicode range: miscellaneous_symbols; Generic numbered name"}, "NG0324": {"action": "REPLACE_UNICODE", "reason": "Unicode problematico: miscellaneous_symbols; Auto-generato", "symbol": "☈", "score": "95.0", "issues": "Risky Unicode range: miscellaneous_symbols; Generic numbered name"}, "NG0326": {"action": "REPLACE_UNICODE", "reason": "Unicode problematico: miscellaneous_symbols; Auto-generato", "symbol": "♧", "score": "95.0", "issues": "Risky Unicode range: miscellaneous_symbols; Generic numbered name"}, "NG0329": {"action": "REPLACE_UNICODE", "reason": "Unicode problematico: miscellaneous_symbols; Auto-generato", "symbol": "⛘", "score": "95.0", "issues": "Risky Unicode range: miscellaneous_symbols; Generic numbered name"}, "NG0330": {"action": "REPLACE_UNICODE", "reason": "Unicode problematico: miscellaneous_symbols; Auto-generato", "symbol": "☡", "score": "95.0", "issues": "Risky Unicode range: miscellaneous_symbols; Generic numbered name"}, "NG0333": {"action": "REPLACE_UNICODE", "reason": "Unicode problematico: miscellaneous_symbols; Auto-generato", "symbol": "⛯", "score": "95.0", "issues": "Risky Unicode range: miscellaneous_symbols; Generic numbered name"}, "NG0337": {"action": "REPLACE_UNICODE", "reason": "Unicode problematico: miscellaneous_symbols; Auto-generato", "symbol": "⚅", "score": "95.0", "issues": "Risky Unicode range: miscellaneous_symbols; Generic numbered name"}, "NG0338": {"action": "REPLACE_UNICODE", "reason": "Unicode problematico: miscellaneous_symbols; Auto-generato", "symbol": "♿", "score": "95.0", "issues": "Risky Unicode range: miscellaneous_symbols; Generic numbered name"}, "NG0346": {"action": "REPLACE_UNICODE", "reason": "Unicode problematico: miscellaneous_symbols; Auto-generato", "symbol": "☵", "score": "95.0", "issues": "Risky Unicode range: miscellaneous_symbols; Long fallback: 11 chars; Generic numbered name"}, "NG0350": {"action": "REPLACE_UNICODE", "reason": "Unicode problematico: miscellaneous_symbols; Auto-generato", "symbol": "⛟", "score": "95.0", "issues": "Risky Unicode range: miscellaneous_symbols; Generic numbered name"}, "NG0352": {"action": "REPLACE_UNICODE", "reason": "Unicode problematico: miscellaneous_symbols; Auto-generato", "symbol": "⛂", "score": "95.0", "issues": "Risky Unicode range: miscellaneous_symbols; Long fallback: 11 chars; Generic numbered name"}, "NG0353": {"action": "REPLACE_UNICODE", "reason": "Unicode problematico: miscellaneous_symbols; Auto-generato", "symbol": "⛇", "score": "95.0", "issues": "Risky Unicode range: miscellaneous_symbols; Generic numbered name"}, "NG0354": {"action": "REPLACE_UNICODE", "reason": "Unicode problematico: miscellaneous_symbols; Auto-generato", "symbol": "⛺", "score": "95.0", "issues": "Risky Unicode range: miscellaneous_symbols; Long fallback: 11 chars; Generic numbered name"}, "NG0355": {"action": "REPLACE_UNICODE", "reason": "Unicode problematico: miscellaneous_symbols; Auto-generato", "symbol": "☢", "score": "95.0", "issues": "Risky Unicode range: miscellaneous_symbols; Generic numbered name"}, "NG0358": {"action": "REPLACE_UNICODE", "reason": "Unicode problematico: miscellaneous_symbols; Auto-generato", "symbol": "♏", "score": "95.0", "issues": "Risky Unicode range: miscellaneous_symbols; Generic numbered name"}, "NG0363": {"action": "REPLACE_UNICODE", "reason": "Unicode problematico: miscellaneous_symbols; Auto-generato", "symbol": "⛲", "score": "95.0", "issues": "Risky Unicode range: miscellaneous_symbols; Generic numbered name"}, "NG0364": {"action": "REPLACE_UNICODE", "reason": "Unicode problematico: miscellaneous_symbols; Auto-generato", "symbol": "⚀", "score": "95.0", "issues": "Risky Unicode range: miscellaneous_symbols; Generic numbered name"}, "NG0365": {"action": "REPLACE_UNICODE", "reason": "Unicode problematico: miscellaneous_symbols; Auto-generato", "symbol": "⛊", "score": "95.0", "issues": "Risky Unicode range: miscellaneous_symbols; Generic numbered name"}, "NG0366": {"action": "REPLACE_UNICODE", "reason": "Unicode problematico: miscellaneous_symbols; Auto-generato", "symbol": "☥", "score": "95.0", "issues": "Risky Unicode range: miscellaneous_symbols; Generic numbered name"}, "NG0369": {"action": "REPLACE_UNICODE", "reason": "Unicode problematico: miscellaneous_symbols; Auto-generato", "symbol": "☩", "score": "95.0", "issues": "Risky Unicode range: miscellaneous_symbols; Generic numbered name"}, "NG0370": {"action": "REPLACE_UNICODE", "reason": "Unicode problematico: miscellaneous_symbols; Auto-generato", "symbol": "♁", "score": "95.0", "issues": "Risky Unicode range: miscellaneous_symbols; Generic numbered name"}, "NG0371": {"action": "REPLACE_UNICODE", "reason": "Unicode problematico: miscellaneous_symbols; Auto-generato", "symbol": "⛋", "score": "95.0", "issues": "Risky Unicode range: miscellaneous_symbols; Generic numbered name"}, "NG0374": {"action": "REPLACE_UNICODE", "reason": "Unicode problematico: miscellaneous_symbols; Auto-generato", "symbol": "☰", "score": "95.0", "issues": "Risky Unicode range: miscellaneous_symbols; Generic numbered name"}, "NG0376": {"action": "REPLACE_UNICODE", "reason": "Unicode problematico: miscellaneous_symbols; Auto-generato", "symbol": "♻", "score": "95.0", "issues": "Risky Unicode range: miscellaneous_symbols; Generic numbered name"}, "NG0382": {"action": "REPLACE_UNICODE", "reason": "Unicode problematico: miscellaneous_symbols; Auto-generato", "symbol": "⛭", "score": "95.0", "issues": "Risky Unicode range: miscellaneous_symbols; Generic numbered name"}, "NG0386": {"action": "REPLACE_UNICODE", "reason": "Unicode problematico: miscellaneous_symbols; Auto-generato", "symbol": "⛱", "score": "95.0", "issues": "Risky Unicode range: miscellaneous_symbols; Generic numbered name"}, "NG0388": {"action": "REPLACE_UNICODE", "reason": "Unicode problematico: miscellaneous_symbols; Auto-generato", "symbol": "♛", "score": "95.0", "issues": "Risky Unicode range: miscellaneous_symbols; Generic numbered name"}, "NG0393": {"action": "REPLACE_UNICODE", "reason": "Unicode problematico: miscellaneous_symbols; Auto-generato", "symbol": "☟", "score": "95.0", "issues": "Risky Unicode range: miscellaneous_symbols; Generic numbered name"}, "NG0395": {"action": "REPLACE_UNICODE", "reason": "Unicode problematico: miscellaneous_symbols; Auto-generato", "symbol": "♂", "score": "95.0", "issues": "Risky Unicode range: miscellaneous_symbols; Generic numbered name"}, "NG0398": {"action": "REPLACE_UNICODE", "reason": "Unicode problematico: miscellaneous_symbols; Auto-generato", "symbol": "♤", "score": "95.0", "issues": "Risky Unicode range: miscellaneous_symbols; Generic numbered name"}, "NG0402": {"action": "REPLACE_UNICODE", "reason": "Unicode problematico: miscellaneous_symbols; Auto-generato", "symbol": "⛞", "score": "95.0", "issues": "Risky Unicode range: miscellaneous_symbols; Generic numbered name"}, "NG0403": {"action": "REPLACE_UNICODE", "reason": "Unicode problematico: miscellaneous_symbols; Auto-generato", "symbol": "⛚", "score": "95.0", "issues": "Risky Unicode range: miscellaneous_symbols; Generic numbered name"}, "NG0407": {"action": "REPLACE_UNICODE", "reason": "Unicode problematico: miscellaneous_symbols; Auto-generato", "symbol": "⛥", "score": "95.0", "issues": "Risky Unicode range: miscellaneous_symbols; Long fallback: 11 chars; Generic numbered name"}, "NG0408": {"action": "REPLACE_UNICODE", "reason": "Unicode problematico: miscellaneous_symbols; Auto-generato", "symbol": "♝", "score": "95.0", "issues": "Risky Unicode range: miscellaneous_symbols; Generic numbered name"}, "NG0409": {"action": "REPLACE_UNICODE", "reason": "Unicode problematico: miscellaneous_symbols; Auto-generato", "symbol": "☠", "score": "95.0", "issues": "Risky Unicode range: miscellaneous_symbols; Generic numbered name"}, "NG0411": {"action": "REPLACE_UNICODE", "reason": "Unicode problematico: miscellaneous_symbols; Auto-generato", "symbol": "☇", "score": "95.0", "issues": "Risky Unicode range: miscellaneous_symbols; Generic numbered name"}, "NG0414": {"action": "REPLACE_UNICODE", "reason": "Unicode problematico: miscellaneous_symbols; Auto-generato", "symbol": "☞", "score": "95.0", "issues": "Risky Unicode range: miscellaneous_symbols; Generic numbered name"}, "NG0416": {"action": "REPLACE_UNICODE", "reason": "Unicode problematico: miscellaneous_symbols; Auto-generato", "symbol": "⚍", "score": "95.0", "issues": "Risky Unicode range: miscellaneous_symbols; Generic numbered name"}, "NG0418": {"action": "REPLACE_UNICODE", "reason": "Unicode problematico: miscellaneous_symbols; Auto-generato", "symbol": "☭", "score": "95.0", "issues": "Risky Unicode range: miscellaneous_symbols; Generic numbered name"}, "NG0426": {"action": "REPLACE_UNICODE", "reason": "Unicode problematico: miscellaneous_symbols; Auto-generato", "symbol": "♘", "score": "95.0", "issues": "Risky Unicode range: miscellaneous_symbols; Generic numbered name"}, "NG0430": {"action": "REPLACE_UNICODE", "reason": "Unicode problematico: miscellaneous_symbols; Auto-generato", "symbol": "♾", "score": "95.0", "issues": "Risky Unicode range: miscellaneous_symbols; Generic numbered name"}, "NG0432": {"action": "REPLACE_UNICODE", "reason": "Unicode problematico: miscellaneous_symbols; Auto-generato", "symbol": "⚽", "score": "95.0", "issues": "Risky Unicode range: miscellaneous_symbols; Long fallback: 11 chars; Generic numbered name"}, "NG0435": {"action": "REPLACE_UNICODE", "reason": "Unicode problematico: miscellaneous_symbols; Auto-generato", "symbol": "⛒", "score": "95.0", "issues": "Risky Unicode range: miscellaneous_symbols; Generic numbered name"}, "NG0439": {"action": "REPLACE_UNICODE", "reason": "Unicode problematico: miscellaneous_symbols; Auto-generato", "symbol": "♈", "score": "95.0", "issues": "Risky Unicode range: miscellaneous_symbols; Generic numbered name"}, "NG0444": {"action": "REPLACE_UNICODE", "reason": "Unicode problematico: miscellaneous_symbols; Auto-generato", "symbol": "⛵", "score": "95.0", "issues": "Risky Unicode range: miscellaneous_symbols; Generic numbered name"}, "NG0449": {"action": "REPLACE_UNICODE", "reason": "Unicode problematico: miscellaneous_symbols; Auto-generato", "symbol": "♣", "score": "95.0", "issues": "Risky Unicode range: miscellaneous_symbols; Generic numbered name"}, "NG0455": {"action": "REPLACE_UNICODE", "reason": "Unicode problematico: miscellaneous_symbols; Auto-generato", "symbol": "♗", "score": "95.0", "issues": "Risky Unicode range: miscellaneous_symbols; Long fallback: 12 chars; Generic numbered name"}, "NG0456": {"action": "REPLACE_UNICODE", "reason": "Unicode problematico: miscellaneous_symbols; Auto-generato", "symbol": "☜", "score": "95.0", "issues": "Risky Unicode range: miscellaneous_symbols; Generic numbered name"}, "NG0458": {"action": "REPLACE_UNICODE", "reason": "Unicode problematico: miscellaneous_symbols; Auto-generato", "symbol": "⛝", "score": "95.0", "issues": "Risky Unicode range: miscellaneous_symbols; Generic numbered name"}, "NG0459": {"action": "REPLACE_UNICODE", "reason": "Unicode problematico: miscellaneous_symbols; Auto-generato", "symbol": "⛔", "score": "95.0", "issues": "Risky Unicode range: miscellaneous_symbols; Generic numbered name"}, "NG0463": {"action": "REPLACE_UNICODE", "reason": "Unicode problematico: miscellaneous_symbols; Auto-generato", "symbol": "⛖", "score": "95.0", "issues": "Risky Unicode range: miscellaneous_symbols; Generic numbered name"}, "NG0464": {"action": "REPLACE_UNICODE", "reason": "Unicode problematico: miscellaneous_symbols; Auto-generato", "symbol": "☄", "score": "95.0", "issues": "Risky Unicode range: miscellaneous_symbols; Generic numbered name"}, "NG0465": {"action": "REPLACE_UNICODE", "reason": "Unicode problematico: miscellaneous_symbols; Auto-generato", "symbol": "⛸", "score": "95.0", "issues": "Risky Unicode range: miscellaneous_symbols; Generic numbered name"}, "NG0467": {"action": "REPLACE_UNICODE", "reason": "Unicode problematico: miscellaneous_symbols; Auto-generato", "symbol": "♦", "score": "95.0", "issues": "Risky Unicode range: miscellaneous_symbols; Generic numbered name"}, "NG0469": {"action": "REPLACE_UNICODE", "reason": "Unicode problematico: miscellaneous_symbols; Auto-generato", "symbol": "☾", "score": "95.0", "issues": "Risky Unicode range: miscellaneous_symbols; Generic numbered name"}, "NG0472": {"action": "REPLACE_UNICODE", "reason": "Unicode problematico: miscellaneous_symbols; Auto-generato", "symbol": "⛆", "score": "95.0", "issues": "Risky Unicode range: miscellaneous_symbols; Generic numbered name"}, "NG0473": {"action": "REPLACE_UNICODE", "reason": "Unicode problematico: miscellaneous_symbols; Auto-generato", "symbol": "⛗", "score": "95.0", "issues": "Risky Unicode range: miscellaneous_symbols; Generic numbered name"}, "NG0474": {"action": "REPLACE_UNICODE", "reason": "Unicode problematico: miscellaneous_symbols; Auto-generato", "symbol": "♔", "score": "95.0", "issues": "Risky Unicode range: miscellaneous_symbols; Generic numbered name"}, "NG0475": {"action": "REPLACE_UNICODE", "reason": "Unicode problematico: miscellaneous_symbols; Auto-generato", "symbol": "♞", "score": "95.0", "issues": "Risky Unicode range: miscellaneous_symbols; Generic numbered name"}, "NG0487": {"action": "REPLACE_UNICODE", "reason": "Unicode problematico: miscellaneous_symbols; Auto-generato", "symbol": "☺", "score": "95.0", "issues": "Risky Unicode range: miscellaneous_symbols; Generic numbered name"}, "NG0490": {"action": "REPLACE_UNICODE", "reason": "Unicode problematico: miscellaneous_symbols; Auto-generato", "symbol": "⛿", "score": "95.0", "issues": "Risky Unicode range: miscellaneous_symbols; Generic numbered name"}, "NG0491": {"action": "REPLACE_UNICODE", "reason": "Unicode problematico: miscellaneous_symbols; Auto-generato", "symbol": "☂", "score": "95.0", "issues": "Risky Unicode range: miscellaneous_symbols; Generic numbered name"}, "NG0497": {"action": "REPLACE_UNICODE", "reason": "Unicode problematico: miscellaneous_symbols; Auto-generato", "symbol": "☨", "score": "95.0", "issues": "Risky Unicode range: miscellaneous_symbols; Generic numbered name"}, "NG0499": {"action": "REPLACE_UNICODE", "reason": "Unicode problematico: miscellaneous_symbols; Auto-generato", "symbol": "☗", "score": "95.0", "issues": "Risky Unicode range: miscellaneous_symbols; Long fallback: 12 chars; Generic numbered name"}, "NG0503": {"action": "REPLACE_UNICODE", "reason": "Unicode problematico: miscellaneous_symbols; Auto-generato", "symbol": "♯", "score": "95.0", "issues": "Risky Unicode range: miscellaneous_symbols; Long fallback: 11 chars; Generic numbered name"}, "NG0506": {"action": "REPLACE_UNICODE", "reason": "Unicode problematico: miscellaneous_symbols; Auto-generato", "symbol": "♰", "score": "95.0", "issues": "Risky Unicode range: miscellaneous_symbols; Generic numbered name"}, "NG0508": {"action": "REPLACE_UNICODE", "reason": "Unicode problematico: miscellaneous_symbols; Auto-generato", "symbol": "☍", "score": "95.0", "issues": "Risky Unicode range: miscellaneous_symbols; Generic numbered name"}, "NG0511": {"action": "REPLACE_UNICODE", "reason": "Unicode problematico: miscellaneous_symbols; Auto-generato", "symbol": "♠", "score": "95.0", "issues": "Risky Unicode range: miscellaneous_symbols; Generic numbered name"}, "NG0568": {"action": "REMOVE", "reason": "Score basso: 1.4000000000000001", "symbol": "⚡", "score": "1.4000000000000001", "issues": "Low validation score: 1.4000000000000001"}, "NG0569": {"action": "REMOVE", "reason": "Score basso: 1.4000000000000001", "symbol": "⏳", "score": "1.4000000000000001", "issues": "Low validation score: 1.4000000000000001"}, "NG0570": {"action": "REMOVE", "reason": "Score basso: 1.4000000000000001", "symbol": "⏸", "score": "1.4000000000000001", "issues": "Low validation score: 1.4000000000000001"}, "NG0571": {"action": "REMOVE", "reason": "Score basso: 1.4000000000000001", "symbol": "⏯", "score": "1.4000000000000001", "issues": "Low validation score: 1.4000000000000001"}, "NG0572": {"action": "REMOVE", "reason": "Score basso: 1.4000000000000001", "symbol": "⏹", "score": "1.4000000000000001", "issues": "Low validation score: 1.4000000000000001"}, "NG0573": {"action": "REMOVE", "reason": "Score basso: 1.4000000000000001", "symbol": "⏱", "score": "1.4000000000000001", "issues": "Low validation score: 1.4000000000000001"}, "NG0574": {"action": "REMOVE", "reason": "Score basso: 1.4000000000000001", "symbol": "⌛", "score": "1.4000000000000001", "issues": "Low validation score: 1.4000000000000001"}, "NG0575": {"action": "REMOVE", "reason": "Score basso: 1.4000000000000001", "symbol": "🔮", "score": "1.4000000000000001", "issues": "Low validation score: 1.4000000000000001"}, "NG0576": {"action": "REMOVE", "reason": "Score basso: 1.4000000000000001", "symbol": "🔄", "score": "1.4000000000000001", "issues": "Low validation score: 1.4000000000000001"}, "NG0577": {"action": "REMOVE", "reason": "Score basso: 1.4000000000000001", "symbol": "🎯", "score": "1.4000000000000001", "issues": "Low validation score: 1.4000000000000001"}, "NG0578": {"action": "REMOVE", "reason": "Score basso: 1.4000000000000001", "symbol": "🌊", "score": "1.4000000000000001", "issues": "Low validation score: 1.4000000000000001"}, "NG0580": {"action": "REMOVE", "reason": "Score basso: 1.4000000000000001", "symbol": "🔁", "score": "1.4000000000000001", "issues": "Low validation score: 1.4000000000000001"}, "NG0581": {"action": "REMOVE", "reason": "Score basso: 1.4000000000000001", "symbol": "🔃", "score": "1.4000000000000001", "issues": "Low validation score: 1.4000000000000001"}, "NG0583": {"action": "REMOVE", "reason": "Score basso: 1.4000000000000001", "symbol": "🏭", "score": "1.4000000000000001", "issues": "Low validation score: 1.4000000000000001"}, "NG0584": {"action": "REMOVE", "reason": "Score basso: 1.4000000000000001", "symbol": "⚙", "score": "1.4000000000000001", "issues": "Low validation score: 1.4000000000000001"}, "NG0585": {"action": "REMOVE", "reason": "Score basso: 1.4000000000000001", "symbol": "🔧", "score": "1.4000000000000001", "issues": "Low validation score: 1.4000000000000001"}, "NG0587": {"action": "REMOVE", "reason": "Score basso: 1.4000000000000001", "symbol": "🔩", "score": "1.4000000000000001", "issues": "Low validation score: 1.4000000000000001"}, "NG0588": {"action": "REMOVE", "reason": "Score basso: 1.4000000000000001", "symbol": "🔐", "score": "1.4000000000000001", "issues": "Low validation score: 1.4000000000000001"}, "NG0589": {"action": "REMOVE", "reason": "Score basso: 1.4000000000000001", "symbol": "🔓", "score": "1.4000000000000001", "issues": "Low validation score: 1.4000000000000001"}, "NG0590": {"action": "REMOVE", "reason": "Score basso: 1.4000000000000001", "symbol": "🛡", "score": "1.4000000000000001", "issues": "Low validation score: 1.4000000000000001"}, "NG0591": {"action": "REMOVE", "reason": "Score basso: 1.4000000000000001", "symbol": "🎭", "score": "1.4000000000000001", "issues": "Low validation score: 1.4000000000000001"}, "NG0592": {"action": "REMOVE", "reason": "Score basso: 1.4000000000000001", "symbol": "🚨", "score": "1.4000000000000001", "issues": "Low validation score: 1.4000000000000001"}, "NG0593": {"action": "REMOVE", "reason": "Score basso: 1.4000000000000001", "symbol": "🛟", "score": "1.4000000000000001", "issues": "Low validation score: 1.4000000000000001"}, "NG0594": {"action": "REMOVE", "reason": "Score basso: 1.4000000000000001", "symbol": "🧵", "score": "1.4000000000000001", "issues": "Low validation score: 1.4000000000000001"}, "NG0596": {"action": "REMOVE", "reason": "Score basso: 1.4000000000000001", "symbol": "👥", "score": "1.4000000000000001", "issues": "Low validation score: 1.4000000000000001"}, "NG0598": {"action": "REMOVE", "reason": "Score basso: 1.4000000000000001", "symbol": "🔚", "score": "1.4000000000000001", "issues": "Low validation score: 1.4000000000000001"}, "NG0599": {"action": "REMOVE", "reason": "Score basso: 1.4000000000000001", "symbol": "💀", "score": "1.4000000000000001", "issues": "Low validation score: 1.4000000000000001"}, "NG0600": {"action": "REMOVE", "reason": "Score basso: 1.4000000000000001", "symbol": "😴", "score": "1.4000000000000001", "issues": "Low validation score: 1.4000000000000001"}, "NG0603": {"action": "REMOVE", "reason": "Score basso: 1.4000000000000001", "symbol": "🍴", "score": "1.4000000000000001", "issues": "Low validation score: 1.4000000000000001"}, "NG0605": {"action": "REMOVE", "reason": "Score basso: 1.4000000000000001", "symbol": "📤", "score": "1.4000000000000001", "issues": "Low validation score: 1.4000000000000001"}, "NG0606": {"action": "REMOVE", "reason": "Score basso: 1.4000000000000001", "symbol": "📥", "score": "1.4000000000000001", "issues": "Low validation score: 1.4000000000000001"}, "NG0607": {"action": "REMOVE", "reason": "Score basso: 1.4000000000000001", "symbol": "🚇", "score": "1.4000000000000001", "issues": "Low validation score: 1.4000000000000001"}, "NG0608": {"action": "REMOVE", "reason": "Score basso: 1.4000000000000001", "symbol": "📡", "score": "1.4000000000000001", "issues": "Low validation score: 1.4000000000000001"}, "NG0609": {"action": "REMOVE", "reason": "Score basso: 1.4000000000000001", "symbol": "👷", "score": "1.4000000000000001", "issues": "Low validation score: 1.4000000000000001"}, "NG0610": {"action": "REMOVE", "reason": "Score basso: 1.4000000000000001", "symbol": "📋", "score": "1.4000000000000001", "issues": "Low validation score: 1.4000000000000001"}, "NG0613": {"action": "REMOVE", "reason": "Score basso: 1.4000000000000001", "symbol": "📊", "score": "1.4000000000000001", "issues": "Low validation score: 1.4000000000000001"}, "NG0615": {"action": "REMOVE", "reason": "Score basso: 1.4000000000000001", "symbol": "❌", "score": "1.4000000000000001", "issues": "Low validation score: 1.4000000000000001"}, "NG0616": {"action": "REMOVE", "reason": "Score basso: 1.4000000000000001", "symbol": "📅", "score": "1.4000000000000001", "issues": "Low validation score: 1.4000000000000001"}, "NG0617": {"action": "REMOVE", "reason": "Score basso: 1.4000000000000001", "symbol": "📆", "score": "1.4000000000000001", "issues": "Low validation score: 1.4000000000000001"}, "NG0621": {"action": "REMOVE", "reason": "Score basso: 1.4000000000000001", "symbol": "🛑", "score": "1.4000000000000001", "issues": "Low validation score: 1.4000000000000001"}, "NG0623": {"action": "REMOVE", "reason": "Score basso: 1.4000000000000001", "symbol": "🗝", "score": "1.4000000000000001", "issues": "Low validation score: 1.4000000000000001"}, "NG0624": {"action": "REMOVE", "reason": "Score basso: 1.4000000000000001", "symbol": "🚪", "score": "1.4000000000000001", "issues": "Low validation score: 1.4000000000000001"}, "NG0627": {"action": "REMOVE", "reason": "Score basso: 1.4000000000000001", "symbol": "📖", "score": "1.4000000000000001", "issues": "Low validation score: 1.4000000000000001"}, "NG0628": {"action": "REMOVE", "reason": "Score basso: 1.4000000000000001", "symbol": "📝", "score": "1.4000000000000001", "issues": "Low validation score: 1.4000000000000001"}, "NG0630": {"action": "REMOVE", "reason": "Score basso: 1.4000000000000001", "symbol": "👑", "score": "1.4000000000000001", "issues": "Low validation score: 1.4000000000000001"}, "NG0631": {"action": "REMOVE", "reason": "Score basso: 1.4000000000000001", "symbol": "🎲", "score": "1.4000000000000001", "issues": "Low validation score: 1.4000000000000001"}, "NG0632": {"action": "REMOVE", "reason": "Score basso: 1.4000000000000001", "symbol": "🔢", "score": "1.4000000000000001", "issues": "Low validation score: 1.4000000000000001"}, "NG0633": {"action": "REMOVE", "reason": "Score basso: 1.4000000000000001", "symbol": "🚦", "score": "1.4000000000000001", "issues": "Low validation score: 1.4000000000000001"}, "NG0638": {"action": "REMOVE", "reason": "Score basso: 1.4000000000000001", "symbol": "🔔", "score": "1.4000000000000001", "issues": "Low validation score: 1.4000000000000001"}, "NG0639": {"action": "REMOVE", "reason": "Score basso: 1.4000000000000001", "symbol": "🎺", "score": "1.4000000000000001", "issues": "Low validation score: 1.4000000000000001"}, "NG0640": {"action": "REMOVE", "reason": "Score basso: 1.4000000000000001", "symbol": "🔇", "score": "1.4000000000000001", "issues": "Low validation score: 1.4000000000000001"}, "NG0642": {"action": "REMOVE", "reason": "Score basso: 1.4000000000000001", "symbol": "📢", "score": "1.4000000000000001", "issues": "Low validation score: 1.4000000000000001"}, "NG0645": {"action": "REMOVE", "reason": "Score basso: 1.4000000000000001", "symbol": "✅", "score": "1.4000000000000001", "issues": "Low validation score: 1.4000000000000001"}, "NG0649": {"action": "REMOVE", "reason": "Score basso: 1.4000000000000001", "symbol": "🔗", "score": "1.4000000000000001", "issues": "Low validation score: 1.4000000000000001"}, "NG0653": {"action": "REMOVE", "reason": "Score basso: 1.4000000000000001", "symbol": "🤝", "score": "1.4000000000000001", "issues": "Low validation score: 1.4000000000000001"}, "NG0654": {"action": "REMOVE", "reason": "Score basso: 1.4000000000000001", "symbol": "🏃", "score": "1.4000000000000001", "issues": "Low validation score: 1.4000000000000001"}, "NG0655": {"action": "REMOVE", "reason": "Score basso: 1.4000000000000001", "symbol": "🏆", "score": "1.4000000000000001", "issues": "Low validation score: 1.4000000000000001"}, "NG0661": {"action": "REMOVE", "reason": "Score basso: 1.4000000000000001", "symbol": "✨", "score": "1.4000000000000001", "issues": "Low validation score: 1.4000000000000001"}, "NG0662": {"action": "REMOVE", "reason": "Score basso: 1.4000000000000001", "symbol": "💥", "score": "1.4000000000000001", "issues": "Low validation score: 1.4000000000000001"}, "NG0671": {"action": "REMOVE", "reason": "Score basso: 1.3", "symbol": "🔒", "score": "1.3", "issues": "Low validation score: 1.3"}, "NG0678": {"action": "REMOVE", "reason": "Score basso: 1.3", "symbol": "📣", "score": "1.3", "issues": "Low validation score: 1.3; Long fallback: 11 chars"}, "NG0683": {"action": "REMOVE", "reason": "Score basso: 1.3", "symbol": "🏁", "score": "1.3", "issues": "Low validation score: 1.3"}, "NG0685": {"action": "REMOVE", "reason": "Score basso: 1.3", "symbol": "🎉", "score": "1.3", "issues": "Low validation score: 1.3"}, "NG0579": {"action": "REMOVE", "reason": "Score basso: 1.4000000000000001", "symbol": "🔀", "score": "1.4000000000000001", "issues": "Low validation score: 1.4000000000000001"}, "NG0582": {"action": "REMOVE", "reason": "Score basso: 1.4000000000000001", "symbol": "⤵", "score": "1.4000000000000001", "issues": "Low validation score: 1.4000000000000001"}, "NG0586": {"action": "REMOVE", "reason": "Score basso: 1.4000000000000001", "symbol": "🛠", "score": "1.4000000000000001", "issues": "Low validation score: 1.4000000000000001"}, "NG0674": {"action": "REMOVE", "reason": "Score basso: 1.3", "symbol": "🔽", "score": "1.3", "issues": "Low validation score: 1.3; Long fallback: 11 chars"}, "NG0675": {"action": "REMOVE", "reason": "Score basso: 1.3", "symbol": "⬆", "score": "1.3", "issues": "Low validation score: 1.3"}, "NG0676": {"action": "REMOVE", "reason": "Score basso: 1.3", "symbol": "⬇", "score": "1.3", "issues": "Low validation score: 1.3"}, "NG0677": {"action": "REMOVE", "reason": "Score basso: 1.3", "symbol": "🚫", "score": "1.3", "issues": "Low validation score: 1.3"}, "NG0595": {"action": "REMOVE", "reason": "Score basso: 1.4000000000000001", "symbol": "⟴", "score": "1.4000000000000001", "issues": "Low validation score: 1.4000000000000001"}, "NG0686": {"action": "REMOVE", "reason": "Score basso: 1.4000000000000001", "symbol": "⟨", "score": "1.4000000000000001", "issues": "Low validation score: 1.4000000000000001"}, "NG0687": {"action": "REMOVE", "reason": "Score basso: 1.4000000000000001", "symbol": "⟩", "score": "1.4000000000000001", "issues": "Low validation score: 1.4000000000000001"}, "NG0688": {"action": "REMOVE", "reason": "Score basso: 1.4000000000000001", "symbol": "⟪", "score": "1.4000000000000001", "issues": "Low validation score: 1.4000000000000001"}, "NG0689": {"action": "REMOVE", "reason": "Score basso: 1.4000000000000001", "symbol": "⟫", "score": "1.4000000000000001", "issues": "Low validation score: 1.4000000000000001"}, "NG0690": {"action": "REMOVE", "reason": "Score basso: 1.4000000000000001", "symbol": "⟬", "score": "1.4000000000000001", "issues": "Low validation score: 1.4000000000000001"}, "NG0691": {"action": "REMOVE", "reason": "Score basso: 1.4000000000000001", "symbol": "⟭", "score": "1.4000000000000001", "issues": "Low validation score: 1.4000000000000001"}, "NG0692": {"action": "REMOVE", "reason": "Score basso: 1.4000000000000001", "symbol": "⟮", "score": "1.4000000000000001", "issues": "Low validation score: 1.4000000000000001"}, "NG0693": {"action": "REMOVE", "reason": "Score basso: 1.4000000000000001", "symbol": "⟯", "score": "1.4000000000000001", "issues": "Low validation score: 1.4000000000000001"}, "NG0694": {"action": "REMOVE", "reason": "Score basso: 1.3", "symbol": "⟰", "score": "1.3", "issues": "Low validation score: 1.3"}, "NG0695": {"action": "REMOVE", "reason": "Score basso: 1.3", "symbol": "⟱", "score": "1.3", "issues": "Low validation score: 1.3"}, "NG0696": {"action": "REMOVE", "reason": "Score basso: 1.3", "symbol": "⤊", "score": "1.3", "issues": "Low validation score: 1.3"}, "NG0697": {"action": "REMOVE", "reason": "Score basso: 1.3", "symbol": "⤋", "score": "1.3", "issues": "Low validation score: 1.3"}, "NG0698": {"action": "REMOVE", "reason": "Score basso: 1.3", "symbol": "⤷", "score": "1.3", "issues": "Low validation score: 1.3"}, "NG0699": {"action": "REMOVE", "reason": "Score basso: 1.3", "symbol": "⤶", "score": "1.3", "issues": "Low validation score: 1.3"}, "NG0700": {"action": "REMOVE", "reason": "Score basso: 1.3", "symbol": "⤸", "score": "1.3", "issues": "Low validation score: 1.3"}, "NG0701": {"action": "REMOVE", "reason": "Score basso: 1.3", "symbol": "⤹", "score": "1.3", "issues": "Low validation score: 1.3"}, "NG0702": {"action": "REMOVE", "reason": "Score basso: 1.3", "symbol": "⤺", "score": "1.3", "issues": "Low validation score: 1.3"}, "NG0703": {"action": "REMOVE", "reason": "Score basso: 1.3", "symbol": "⤼", "score": "1.3", "issues": "Low validation score: 1.3"}, "NG0704": {"action": "REMOVE", "reason": "Score basso: 1.3", "symbol": "⤽", "score": "1.3", "issues": "Low validation score: 1.3"}, "NG0705": {"action": "REMOVE", "reason": "Score basso: 1.3", "symbol": "⟵", "score": "1.3", "issues": "Low validation score: 1.3"}, "NG0706": {"action": "REMOVE", "reason": "Score basso: 1.3", "symbol": "⟶", "score": "1.3", "issues": "Low validation score: 1.3"}, "NG0707": {"action": "REMOVE", "reason": "Score basso: 1.3", "symbol": "⟷", "score": "1.3", "issues": "Low validation score: 1.3"}, "NG0708": {"action": "REMOVE", "reason": "Score basso: 1.3", "symbol": "⟸", "score": "1.3", "issues": "Low validation score: 1.3"}, "NG0709": {"action": "REMOVE", "reason": "Score basso: 1.3", "symbol": "⟹", "score": "1.3", "issues": "Low validation score: 1.3"}, "NG0710": {"action": "REMOVE", "reason": "Score basso: 1.3", "symbol": "⇔", "score": "1.3", "issues": "Low validation score: 1.3"}, "NG0711": {"action": "REMOVE", "reason": "Score basso: 1.3", "symbol": "⟾", "score": "1.3", "issues": "Low validation score: 1.3"}, "NG0712": {"action": "REMOVE", "reason": "Score basso: 1.3", "symbol": "⟿", "score": "1.3", "issues": "Low validation score: 1.3"}, "NG0713": {"action": "REMOVE", "reason": "Score basso: 1.3", "symbol": "⤀", "score": "1.3", "issues": "Low validation score: 1.3"}, "NG0714": {"action": "REMOVE", "reason": "Score basso: 1.3", "symbol": "⤁", "score": "1.3", "issues": "Low validation score: 1.3"}, "NG0715": {"action": "REMOVE", "reason": "Score basso: 1.3", "symbol": "⤂", "score": "1.3", "issues": "Low validation score: 1.3"}, "NG0716": {"action": "REMOVE", "reason": "Score basso: 1.3", "symbol": "⤃", "score": "1.3", "issues": "Low validation score: 1.3"}, "NG0717": {"action": "REMOVE", "reason": "Score basso: 1.3", "symbol": "⤄", "score": "1.3", "issues": "Low validation score: 1.3"}, "NG0718": {"action": "REMOVE", "reason": "Score basso: 1.3", "symbol": "⤅", "score": "1.3", "issues": "Low validation score: 1.3"}, "NG0719": {"action": "REMOVE", "reason": "Score basso: 1.3", "symbol": "⚔", "score": "1.3", "issues": "Low validation score: 1.3"}, "NG0720": {"action": "REMOVE", "reason": "Score basso: 1.3", "symbol": "⚚", "score": "1.3", "issues": "Low validation score: 1.3"}, "NG0721": {"action": "REMOVE", "reason": "Score basso: 1.3", "symbol": "⚞", "score": "1.3", "issues": "Low validation score: 1.3"}, "NG0722": {"action": "REMOVE", "reason": "Score basso: 1.3", "symbol": "⚨", "score": "1.3", "issues": "Low validation score: 1.3"}, "NG0723": {"action": "REMOVE", "reason": "Score basso: 1.3", "symbol": "⚭", "score": "1.3", "issues": "Low validation score: 1.3"}, "NG0724": {"action": "REMOVE", "reason": "Score basso: 1.3", "symbol": "⚱", "score": "1.3", "issues": "Low validation score: 1.3"}, "NG0725": {"action": "REMOVE", "reason": "Score basso: 1.2000000000000002", "symbol": "⊜", "score": "1.2000000000000002", "issues": "Low validation score: 1.2000000000000002"}, "NG0726": {"action": "REMOVE", "reason": "Score basso: 1.2", "symbol": "⤻", "score": "1.2", "issues": "Low validation score: 1.2; Long fallback: 11 chars"}, "NG0727": {"action": "REMOVE", "reason": "Score basso: 1.2", "symbol": "⚕", "score": "1.2", "issues": "Low validation score: 1.2; Long fallback: 11 chars"}, "NG0728": {"action": "REMOVE", "reason": "Score basso: 1.1", "symbol": "⊂", "score": "1.1", "issues": "Low validation score: 1.1"}, "NG0729": {"action": "REMOVE", "reason": "Score basso: 1.1", "symbol": "⊇", "score": "1.1", "issues": "Low validation score: 1.1"}, "NG0730": {"action": "REMOVE", "reason": "Score basso: 1.1", "symbol": "⬟", "score": "1.1", "issues": "Low validation score: 1.1"}, "NG0731": {"action": "REMOVE", "reason": "Score basso: 1.1", "symbol": "⬠", "score": "1.1", "issues": "Low validation score: 1.1"}, "NG0732": {"action": "REMOVE", "reason": "Score basso: 1.1", "symbol": "⬢", "score": "1.1", "issues": "Low validation score: 1.1"}, "NG0733": {"action": "REMOVE", "reason": "Score basso: 1.1", "symbol": "⟐", "score": "1.1", "issues": "Low validation score: 1.1"}, "NG0734": {"action": "REMOVE", "reason": "Score basso: 1.1", "symbol": "⟑", "score": "1.1", "issues": "Low validation score: 1.1"}, "NG0735": {"action": "REMOVE", "reason": "Score basso: 1.1", "symbol": "⟒", "score": "1.1", "issues": "Low validation score: 1.1"}, "NG0736": {"action": "REMOVE", "reason": "Score basso: 1.1", "symbol": "⟕", "score": "1.1", "issues": "Low validation score: 1.1"}, "NG0737": {"action": "REMOVE", "reason": "Score basso: 1.1", "symbol": "⟖", "score": "1.1", "issues": "Low validation score: 1.1"}, "NG0738": {"action": "REMOVE", "reason": "Score basso: 1.1", "symbol": "⊟", "score": "1.1", "issues": "Low validation score: 1.1"}, "NG0739": {"action": "REMOVE", "reason": "Score basso: 1.1", "symbol": "⊡", "score": "1.1", "issues": "Low validation score: 1.1"}, "NG0740": {"action": "REMOVE", "reason": "Score basso: 1.1", "symbol": "⊣", "score": "1.1", "issues": "Low validation score: 1.1"}, "NG0741": {"action": "REMOVE", "reason": "Score basso: 1.1", "symbol": "⊥", "score": "1.1", "issues": "Low validation score: 1.1"}, "NG0742": {"action": "REMOVE", "reason": "Score basso: 1.1", "symbol": "⦀", "score": "1.1", "issues": "Low validation score: 1.1"}, "NG0743": {"action": "REMOVE", "reason": "Score basso: 1.1", "symbol": "⦁", "score": "1.1", "issues": "Low validation score: 1.1"}, "NG0744": {"action": "REMOVE", "reason": "Score basso: 1.1", "symbol": "⦂", "score": "1.1", "issues": "Low validation score: 1.1"}, "NG0745": {"action": "REMOVE", "reason": "Score basso: 1.1", "symbol": "⦃", "score": "1.1", "issues": "Low validation score: 1.1"}, "NG0746": {"action": "REMOVE", "reason": "Score basso: 1.1", "symbol": "⦄", "score": "1.1", "issues": "Low validation score: 1.1"}, "NG0747": {"action": "REMOVE", "reason": "Score basso: 1.1", "symbol": "⦅", "score": "1.1", "issues": "Low validation score: 1.1"}, "NG0748": {"action": "REMOVE", "reason": "Score basso: 1.1", "symbol": "⦆", "score": "1.1", "issues": "Low validation score: 1.1"}, "NG0749": {"action": "REMOVE", "reason": "Score basso: 1.1", "symbol": "⦇", "score": "1.1", "issues": "Low validation score: 1.1"}, "NG0750": {"action": "REMOVE", "reason": "Score basso: 1.1", "symbol": "⚦", "score": "1.1", "issues": "Low validation score: 1.1"}, "NG0751": {"action": "REMOVE", "reason": "Score basso: 1.0", "symbol": "∌", "score": "1.0", "issues": "Low validation score: 1.0; Long fallback: 11 chars"}, "NG0752": {"action": "REMOVE", "reason": "Score basso: 1.0", "symbol": "⬡", "score": "1.0", "issues": "Low validation score: 1.0; Long fallback: 11 chars"}, "NG0753": {"action": "REMOVE", "reason": "Score basso: 1.0", "symbol": "⟓", "score": "1.0", "issues": "Low validation score: 1.0; Long fallback: 12 chars"}, "NG0754": {"action": "REMOVE", "reason": "Score basso: 1.0", "symbol": "⟔", "score": "1.0", "issues": "Low validation score: 1.0; Long fallback: 13 chars"}, "NG0755": {"action": "REMOVE", "reason": "Score basso: 1.0", "symbol": "⟗", "score": "1.0", "issues": "Low validation score: 1.0; Long fallback: 11 chars"}, "NG0756": {"action": "REMOVE", "reason": "Score basso: 1.5000000000000002", "symbol": "⟦", "score": "1.5000000000000002", "issues": "Low validation score: 1.5000000000000002"}, "NG0757": {"action": "REMOVE", "reason": "Score basso: 1.5000000000000002", "symbol": "⟧", "score": "1.5000000000000002", "issues": "Low validation score: 1.5000000000000002"}, "NG0758": {"action": "REMOVE", "reason": "Score basso: 1.3", "symbol": "⋅", "score": "1.3", "issues": "Low validation score: 1.3"}, "NG0759": {"action": "REMOVE", "reason": "Score basso: 1.3", "symbol": "⋖", "score": "1.3", "issues": "Low validation score: 1.3"}, "NG0760": {"action": "REMOVE", "reason": "Score basso: 1.3", "symbol": "⋗", "score": "1.3", "issues": "Low validation score: 1.3"}, "NG0761": {"action": "REMOVE", "reason": "Score basso: 1.3", "symbol": "⌀", "score": "1.3", "issues": "Low validation score: 1.3"}, "NG0762": {"action": "REMOVE", "reason": "Score basso: 1.3", "symbol": "⌁", "score": "1.3", "issues": "Low validation score: 1.3"}, "NG0763": {"action": "REMOVE", "reason": "Score basso: 1.3", "symbol": "⌅", "score": "1.3", "issues": "Low validation score: 1.3"}, "NG0764": {"action": "REMOVE", "reason": "Score basso: 1.3", "symbol": "⌌", "score": "1.3", "issues": "Low validation score: 1.3"}, "NG0765": {"action": "REMOVE", "reason": "Score basso: 1.2000000000000002", "symbol": "⦈", "score": "1.2000000000000002", "issues": "Low validation score: 1.2000000000000002"}, "NG0766": {"action": "REMOVE", "reason": "Score basso: 1.2000000000000002", "symbol": "⦉", "score": "1.2000000000000002", "issues": "Low validation score: 1.2000000000000002"}, "NG0767": {"action": "REMOVE", "reason": "Score basso: 1.2000000000000002", "symbol": "⦊", "score": "1.2000000000000002", "issues": "Low validation score: 1.2000000000000002"}, "NG0768": {"action": "REMOVE", "reason": "Score basso: 1.2000000000000002", "symbol": "⦋", "score": "1.2000000000000002", "issues": "Low validation score: 1.2000000000000002"}, "NG0769": {"action": "REMOVE", "reason": "Score basso: 1.2000000000000002", "symbol": "⦌", "score": "1.2000000000000002", "issues": "Low validation score: 1.2000000000000002"}, "NG0770": {"action": "REMOVE", "reason": "Score basso: 1.2", "symbol": "⌂", "score": "1.2", "issues": "Low validation score: 1.2; Long fallback: 11 chars"}, "NG0771": {"action": "REMOVE", "reason": "Score basso: 1.2", "symbol": "⌃", "score": "1.2", "issues": "Low validation score: 1.2; Long fallback: 11 chars"}, "NG0772": {"action": "REMOVE", "reason": "Score basso: 1.2", "symbol": "⌄", "score": "1.2", "issues": "Low validation score: 1.2; Long fallback: 11 chars"}, "NG0773": {"action": "REMOVE", "reason": "Score basso: 1.1", "symbol": "⊩", "score": "1.1", "issues": "Low validation score: 1.1"}, "NG0774": {"action": "REMOVE", "reason": "Score basso: 1.1", "symbol": "⊫", "score": "1.1", "issues": "Low validation score: 1.1"}, "NG0775": {"action": "REMOVE", "reason": "Score basso: 1.1", "symbol": "⊭", "score": "1.1", "issues": "Low validation score: 1.1"}, "NG0776": {"action": "REMOVE", "reason": "Score basso: 1.1", "symbol": "⋌", "score": "1.1", "issues": "Low validation score: 1.1"}, "NG0777": {"action": "REMOVE", "reason": "Score basso: 1.1", "symbol": "⋍", "score": "1.1", "issues": "Low validation score: 1.1"}, "NG0778": {"action": "REMOVE", "reason": "Score basso: 1.1", "symbol": "⋐", "score": "1.1", "issues": "Low validation score: 1.1"}, "NG0779": {"action": "REMOVE", "reason": "Score basso: 1.1", "symbol": "⋑", "score": "1.1", "issues": "Low validation score: 1.1"}, "NG0780": {"action": "REMOVE", "reason": "Score basso: 1.1", "symbol": "⋒", "score": "1.1", "issues": "Low validation score: 1.1"}, "NG0781": {"action": "REMOVE", "reason": "Score basso: 1.1", "symbol": "⋚", "score": "1.1", "issues": "Low validation score: 1.1"}, "NG0782": {"action": "REMOVE", "reason": "Score basso: 1.1", "symbol": "⋠", "score": "1.1", "issues": "Low validation score: 1.1"}, "NG0783": {"action": "REMOVE", "reason": "Score basso: 1.1", "symbol": "⋡", "score": "1.1", "issues": "Low validation score: 1.1"}, "NG0784": {"action": "REMOVE", "reason": "Score basso: 1.1", "symbol": "⋤", "score": "1.1", "issues": "Low validation score: 1.1"}, "NG0785": {"action": "REMOVE", "reason": "Score basso: 1.1", "symbol": "⋪", "score": "1.1", "issues": "Low validation score: 1.1"}, "NG0786": {"action": "REMOVE", "reason": "Score basso: 1.1", "symbol": "⋭", "score": "1.1", "issues": "Low validation score: 1.1"}, "NG0787": {"action": "REMOVE", "reason": "Score basso: 1.1", "symbol": "⋶", "score": "1.1", "issues": "Low validation score: 1.1"}, "NG0788": {"action": "REMOVE", "reason": "Score basso: 1.1", "symbol": "⋻", "score": "1.1", "issues": "Low validation score: 1.1"}, "NG0789": {"action": "REMOVE", "reason": "Score basso: 1.1", "symbol": "⋽", "score": "1.1", "issues": "Low validation score: 1.1"}, "NG0790": {"action": "REMOVE", "reason": "Score basso: 1.1", "symbol": "⌆", "score": "1.1", "issues": "Low validation score: 1.1"}, "NG0791": {"action": "REMOVE", "reason": "Score basso: 1.1", "symbol": "⌇", "score": "1.1", "issues": "Low validation score: 1.1"}, "NG0792": {"action": "REMOVE", "reason": "Score basso: 1.1", "symbol": "⌋", "score": "1.1", "issues": "Low validation score: 1.1"}, "NG0793": {"action": "REMOVE", "reason": "Score basso: 1.1", "symbol": "⌍", "score": "1.1", "issues": "Low validation score: 1.1"}, "NG0794": {"action": "REMOVE", "reason": "Score basso: 1.1", "symbol": "⌎", "score": "1.1", "issues": "Low validation score: 1.1"}, "NG0795": {"action": "REMOVE", "reason": "Score basso: 1.1", "symbol": "⌐", "score": "1.1", "issues": "Low validation score: 1.1"}, "NG0796": {"action": "REMOVE", "reason": "Score basso: 1.1", "symbol": "⌑", "score": "1.1", "issues": "Low validation score: 1.1"}, "NG0797": {"action": "REMOVE", "reason": "Score basso: 1.1", "symbol": "⌒", "score": "1.1", "issues": "Low validation score: 1.1"}, "NG0798": {"action": "REMOVE", "reason": "Score basso: 1.1", "symbol": "⌓", "score": "1.1", "issues": "Low validation score: 1.1"}, "NG0799": {"action": "REMOVE", "reason": "Score basso: 1.1", "symbol": "⌕", "score": "1.1", "issues": "Low validation score: 1.1"}, "NG0800": {"action": "REMOVE", "reason": "Score basso: 1.1", "symbol": "⌖", "score": "1.1", "issues": "Low validation score: 1.1"}, "NG0801": {"action": "REMOVE", "reason": "Score basso: 1.1", "symbol": "⌗", "score": "1.1", "issues": "Low validation score: 1.1"}, "NG0802": {"action": "REMOVE", "reason": "Score basso: 1.1", "symbol": "⌙", "score": "1.1", "issues": "Low validation score: 1.1"}, "NG0803": {"action": "REMOVE", "reason": "Score basso: 1.1", "symbol": "⌚", "score": "1.1", "issues": "Low validation score: 1.1"}, "NG0804": {"action": "REMOVE", "reason": "Score basso: 1.1", "symbol": "⌜", "score": "1.1", "issues": "Low validation score: 1.1"}, "NG0805": {"action": "REMOVE", "reason": "Score basso: 1.1", "symbol": "⌝", "score": "1.1", "issues": "Low validation score: 1.1"}, "NG0806": {"action": "REMOVE", "reason": "Score basso: 1.0", "symbol": "⋕", "score": "1.0", "issues": "Low validation score: 1.0; Long fallback: 11 chars"}, "NG0807": {"action": "REMOVE", "reason": "Score basso: 1.0", "symbol": "⋣", "score": "1.0", "issues": "Low validation score: 1.0; Long fallback: 12 chars"}, "NG0808": {"action": "REMOVE", "reason": "Score basso: 1.0", "symbol": "⋥", "score": "1.0", "issues": "Low validation score: 1.0; Long fallback: 12 chars"}, "NG0809": {"action": "REMOVE", "reason": "Score basso: 1.0", "symbol": "⋷", "score": "1.0", "issues": "Low validation score: 1.0; Long fallback: 11 chars"}, "NG0810": {"action": "REMOVE", "reason": "Score basso: 1.0", "symbol": "⋸", "score": "1.0", "issues": "Low validation score: 1.0; Long fallback: 12 chars"}, "NG0811": {"action": "REMOVE", "reason": "Score basso: 1.0", "symbol": "⋼", "score": "1.0", "issues": "Low validation score: 1.0; Long fallback: 13 chars"}, "NG0812": {"action": "REMOVE", "reason": "Score basso: 1.0", "symbol": "⌈", "score": "1.0", "issues": "Low validation score: 1.0; Long fallback: 15 chars"}, "NG0813": {"action": "REMOVE", "reason": "Score basso: 1.0", "symbol": "⌉", "score": "1.0", "issues": "Low validation score: 1.0; Long fallback: 13 chars"}, "NG0814": {"action": "REMOVE", "reason": "Score basso: 1.0", "symbol": "⌊", "score": "1.0", "issues": "Low validation score: 1.0; Long fallback: 11 chars"}, "NG0815": {"action": "REMOVE", "reason": "Score basso: 1.0", "symbol": "⌘", "score": "1.0", "issues": "Low validation score: 1.0; Long fallback: 15 chars"}, "NG0816": {"action": "REMOVE", "reason": "Score basso: 1.0", "symbol": "☁", "score": "1.0", "issues": "Low validation score: 1.0"}, "NG0817": {"action": "REMOVE", "reason": "Score basso: 1.0", "symbol": "☃", "score": "1.0", "issues": "Low validation score: 1.0"}, "NG0818": {"action": "REMOVE", "reason": "Score basso: 1.0", "symbol": "★", "score": "1.0", "issues": "Low validation score: 1.0"}, "NG0819": {"action": "REMOVE", "reason": "Score basso: 1.0", "symbol": "☆", "score": "1.0", "issues": "Low validation score: 1.0; Long fallback: 14 chars"}, "NG0820": {"action": "REMOVE", "reason": "Score basso: 1.0", "symbol": "☉", "score": "1.0", "issues": "Low validation score: 1.0"}, "NG0821": {"action": "REMOVE", "reason": "Score basso: 1.0", "symbol": "☋", "score": "1.0", "issues": "Low validation score: 1.0"}, "NG0822": {"action": "REMOVE", "reason": "Score basso: 1.0", "symbol": "☌", "score": "1.0", "issues": "Low validation score: 1.0"}, "NG0823": {"action": "REMOVE", "reason": "Score basso: 1.0", "symbol": "☐", "score": "1.0", "issues": "Low validation score: 1.0"}, "NG0824": {"action": "REMOVE", "reason": "Score basso: 1.0", "symbol": "☒", "score": "1.0", "issues": "Low validation score: 1.0"}, "NG0825": {"action": "REMOVE", "reason": "Score basso: 1.0", "symbol": "☓", "score": "1.0", "issues": "Low validation score: 1.0"}, "NG0826": {"action": "REMOVE", "reason": "Score basso: 1.0", "symbol": "☔", "score": "1.0", "issues": "Low validation score: 1.0"}, "NG0827": {"action": "REMOVE", "reason": "Score basso: 1.0", "symbol": "☖", "score": "1.0", "issues": "Low validation score: 1.0"}, "NG0828": {"action": "REMOVE", "reason": "Score basso: 1.0", "symbol": "☚", "score": "1.0", "issues": "Low validation score: 1.0"}, "NG0829": {"action": "REMOVE", "reason": "Score basso: 1.0", "symbol": "☣", "score": "1.0", "issues": "Low validation score: 1.0"}, "NG0830": {"action": "REMOVE", "reason": "Score basso: 1.0", "symbol": "☪", "score": "1.0", "issues": "Low validation score: 1.0"}, "NG0831": {"action": "REMOVE", "reason": "Score basso: 1.0", "symbol": "☫", "score": "1.0", "issues": "Low validation score: 1.0"}, "NG0832": {"action": "REMOVE", "reason": "Score basso: 1.0", "symbol": "☱", "score": "1.0", "issues": "Low validation score: 1.0"}, "NG0833": {"action": "REMOVE", "reason": "Score basso: 1.0", "symbol": "☲", "score": "1.0", "issues": "Low validation score: 1.0"}, "NG0834": {"action": "REMOVE", "reason": "Score basso: 1.0", "symbol": "☳", "score": "1.0", "issues": "Low validation score: 1.0"}, "NG0835": {"action": "REMOVE", "reason": "Score basso: 1.0", "symbol": "☷", "score": "1.0", "issues": "Low validation score: 1.0"}, "NG0836": {"action": "REMOVE", "reason": "Score basso: 1.0", "symbol": "☹", "score": "1.0", "issues": "Low validation score: 1.0"}, "NG0837": {"action": "REMOVE", "reason": "Score basso: 1.0", "symbol": "☼", "score": "1.0", "issues": "Low validation score: 1.0"}, "NG0838": {"action": "REMOVE", "reason": "Score basso: 1.0", "symbol": "♀", "score": "1.0", "issues": "Low validation score: 1.0"}, "NG0839": {"action": "REMOVE", "reason": "Score basso: 1.0", "symbol": "♆", "score": "1.0", "issues": "Low validation score: 1.0"}, "NG0840": {"action": "REMOVE", "reason": "Score basso: 1.0", "symbol": "♇", "score": "1.0", "issues": "Low validation score: 1.0"}, "NG0841": {"action": "REMOVE", "reason": "Score basso: 1.0", "symbol": "♌", "score": "1.0", "issues": "Low validation score: 1.0"}, "NG0842": {"action": "REMOVE", "reason": "Score basso: 1.0", "symbol": "♍", "score": "1.0", "issues": "Low validation score: 1.0"}, "NG0843": {"action": "REMOVE", "reason": "Score basso: 1.0", "symbol": "♎", "score": "1.0", "issues": "Low validation score: 1.0"}, "NG0844": {"action": "REMOVE", "reason": "Score basso: 1.0", "symbol": "♐", "score": "1.0", "issues": "Low validation score: 1.0"}, "NG0845": {"action": "REMOVE", "reason": "Score basso: 1.0", "symbol": "♓", "score": "1.0", "issues": "Low validation score: 1.0"}, "NG0846": {"action": "REMOVE", "reason": "Score basso: 1.0", "symbol": "♕", "score": "1.0", "issues": "Low validation score: 1.0"}, "NG0847": {"action": "REMOVE", "reason": "Score basso: 1.0", "symbol": "♖", "score": "1.0", "issues": "Low validation score: 1.0"}, "NG0848": {"action": "REMOVE", "reason": "Score basso: 1.0", "symbol": "♚", "score": "1.0", "issues": "Low validation score: 1.0"}, "NG0849": {"action": "REMOVE", "reason": "Score basso: 1.0", "symbol": "♜", "score": "1.0", "issues": "Low validation score: 1.0"}, "NG0850": {"action": "REMOVE", "reason": "Score basso: 1.0", "symbol": "♟", "score": "1.0", "issues": "Low validation score: 1.0"}, "NG0851": {"action": "REMOVE", "reason": "Score basso: 1.0", "symbol": "♢", "score": "1.0", "issues": "Low validation score: 1.0"}, "NG0852": {"action": "REMOVE", "reason": "Score basso: 1.0", "symbol": "♩", "score": "1.0", "issues": "Low validation score: 1.0"}, "NG0853": {"action": "REMOVE", "reason": "Score basso: 1.0", "symbol": "♪", "score": "1.0", "issues": "Low validation score: 1.0"}, "NG0854": {"action": "REMOVE", "reason": "Score basso: 1.0", "symbol": "♱", "score": "1.0", "issues": "Low validation score: 1.0"}, "NG0855": {"action": "REMOVE", "reason": "Score basso: 1.0", "symbol": "♲", "score": "1.0", "issues": "Low validation score: 1.0"}, "NG0856": {"action": "REMOVE", "reason": "Score basso: 1.0", "symbol": "♳", "score": "1.0", "issues": "Low validation score: 1.0"}, "NG0857": {"action": "REMOVE", "reason": "Score basso: 1.0", "symbol": "♴", "score": "1.0", "issues": "Low validation score: 1.0"}, "NG0858": {"action": "REMOVE", "reason": "Score basso: 1.0", "symbol": "♵", "score": "1.0", "issues": "Low validation score: 1.0"}, "NG0859": {"action": "REMOVE", "reason": "Score basso: 1.0", "symbol": "♸", "score": "1.0", "issues": "Low validation score: 1.0"}, "NG0860": {"action": "REMOVE", "reason": "Score basso: 1.0", "symbol": "♹", "score": "1.0", "issues": "Low validation score: 1.0"}, "NG0861": {"action": "REMOVE", "reason": "Score basso: 1.0", "symbol": "♺", "score": "1.0", "issues": "Low validation score: 1.0"}, "NG0862": {"action": "REMOVE", "reason": "Score basso: 1.0", "symbol": "♼", "score": "1.0", "issues": "Low validation score: 1.0"}, "NG0863": {"action": "REMOVE", "reason": "Score basso: 1.0", "symbol": "⚃", "score": "1.0", "issues": "Low validation score: 1.0"}, "NG0864": {"action": "REMOVE", "reason": "Score basso: 1.0", "symbol": "⚄", "score": "1.0", "issues": "Low validation score: 1.0"}, "NG0865": {"action": "REMOVE", "reason": "Score basso: 1.0", "symbol": "⚇", "score": "1.0", "issues": "Low validation score: 1.0"}, "NG0866": {"action": "REMOVE", "reason": "Score basso: 1.0", "symbol": "⚊", "score": "1.0", "issues": "Low validation score: 1.0"}, "NG0867": {"action": "REMOVE", "reason": "Score basso: 1.0", "symbol": "⚎", "score": "1.0", "issues": "Low validation score: 1.0"}, "NG0868": {"action": "REMOVE", "reason": "Score basso: 1.0", "symbol": "⚏", "score": "1.0", "issues": "Low validation score: 1.0"}, "NG0869": {"action": "REMOVE", "reason": "Score basso: 1.0", "symbol": "⚐", "score": "1.0", "issues": "Low validation score: 1.0"}, "NG0870": {"action": "REMOVE", "reason": "Score basso: 1.0", "symbol": "⚲", "score": "1.0", "issues": "Low validation score: 1.0"}, "NG0871": {"action": "REMOVE", "reason": "Score basso: 1.0", "symbol": "⚴", "score": "1.0", "issues": "Low validation score: 1.0"}, "NG0872": {"action": "REMOVE", "reason": "Score basso: 1.0", "symbol": "⚵", "score": "1.0", "issues": "Low validation score: 1.0"}, "NG0873": {"action": "REMOVE", "reason": "Score basso: 1.0", "symbol": "⚷", "score": "1.0", "issues": "Low validation score: 1.0"}, "NG0874": {"action": "REMOVE", "reason": "Score basso: 1.0", "symbol": "⚸", "score": "1.0", "issues": "Low validation score: 1.0"}, "NG0875": {"action": "REMOVE", "reason": "Score basso: 1.0", "symbol": "⚺", "score": "1.0", "issues": "Low validation score: 1.0"}, "NG0876": {"action": "REMOVE", "reason": "Score basso: 1.0", "symbol": "⚾", "score": "1.0", "issues": "Low validation score: 1.0"}, "NG0877": {"action": "REMOVE", "reason": "Score basso: 1.0", "symbol": "⚿", "score": "1.0", "issues": "Low validation score: 1.0"}, "NG0878": {"action": "REMOVE", "reason": "Score basso: 1.0", "symbol": "⛀", "score": "1.0", "issues": "Low validation score: 1.0"}, "NG0879": {"action": "REMOVE", "reason": "Score basso: 1.0", "symbol": "⛃", "score": "1.0", "issues": "Low validation score: 1.0"}, "NG0880": {"action": "REMOVE", "reason": "Score basso: 1.0", "symbol": "⛄", "score": "1.0", "issues": "Low validation score: 1.0"}, "NG0881": {"action": "REMOVE", "reason": "Score basso: 1.0", "symbol": "⛅", "score": "1.0", "issues": "Low validation score: 1.0"}, "NG0882": {"action": "REPLACE_UNICODE", "reason": "Unicode problematico: emoji_extended; Auto-generato", "symbol": "🟖", "score": "95.0", "issues": "Risky Unicode range: emoji_extended; Long fallback: 16 chars"}, "NG0887": {"action": "REPLACE_UNICODE", "reason": "Unicode problematico: emoji_extended; Auto-generato", "symbol": "😏", "score": "95.0", "issues": "Risky Unicode range: emoji_extended; Long fallback: 16 chars"}, "NG0890": {"action": "REPLACE_UNICODE", "reason": "Unicode problematico: emoji_extended; Auto-generato", "symbol": "🠲", "score": "95.0", "issues": "Risky Unicode range: emoji_extended; Long fallback: 19 chars"}, "NG0892": {"action": "REPLACE_UNICODE", "reason": "Unicode problematico: emoji_extended; Auto-generato", "symbol": "🟙", "score": "95.0", "issues": "Risky Unicode range: emoji_extended; Long fallback: 15 chars"}, "NG0895": {"action": "REPLACE_UNICODE", "reason": "Unicode problematico: emoji_extended; Auto-generato", "symbol": "🜿", "score": "95.0", "issues": "Risky Unicode range: emoji_extended; Long fallback: 13 chars"}, "NG0898": {"action": "REPLACE_UNICODE", "reason": "Unicode problematico: emoji_extended; Auto-generato", "symbol": "🟈", "score": "95.0", "issues": "Risky Unicode range: emoji_extended; Long fallback: 19 chars"}, "NG0899": {"action": "REPLACE_UNICODE", "reason": "Unicode problematico: emoji_extended; Auto-generato", "symbol": "🞊", "score": "95.0", "issues": "Risky Unicode range: emoji_extended; Long fallback: 20 chars; Generic numbered name"}, "NG0900": {"action": "REPLACE_UNICODE", "reason": "Unicode problematico: emoji_extended; Auto-generato", "symbol": "🥻", "score": "95.0", "issues": "Risky Unicode range: emoji_extended; Long fallback: 17 chars"}, "NG0902": {"action": "REPLACE_UNICODE", "reason": "Unicode problematico: emoji_extended; Auto-generato", "symbol": "🢝", "score": "95.0", "issues": "Risky Unicode range: emoji_extended; Long fallback: 13 chars"}, "NG0903": {"action": "REPLACE_UNICODE", "reason": "Unicode problematico: emoji_extended; Auto-generato", "symbol": "🤃", "score": "95.0", "issues": "Risky Unicode range: emoji_extended; Long fallback: 15 chars"}, "NG0904": {"action": "REPLACE_UNICODE", "reason": "Unicode problematico: emoji_extended; Auto-generato", "symbol": "🜼", "score": "95.0", "issues": "Risky Unicode range: emoji_extended; Long fallback: 19 chars"}, "NG0905": {"action": "REPLACE_UNICODE", "reason": "Unicode problematico: emoji_extended; Auto-generato", "symbol": "🖼", "score": "95.0", "issues": "Risky Unicode range: emoji_extended; Long fallback: 20 chars"}, "NG0908": {"action": "REPLACE_UNICODE", "reason": "Unicode problematico: emoji_extended; Auto-generato", "symbol": "🞞", "score": "95.0", "issues": "Risky Unicode range: emoji_extended; Long fallback: 14 chars"}, "NG0909": {"action": "REPLACE_UNICODE", "reason": "Unicode problematico: emoji_extended; Auto-generato", "symbol": "🡵", "score": "95.0", "issues": "Risky Unicode range: emoji_extended; Long fallback: 15 chars"}, "NG0913": {"action": "REPLACE_UNICODE", "reason": "Unicode problematico: emoji_extended; Auto-generato", "symbol": "🛩", "score": "95.0", "issues": "Risky Unicode range: emoji_extended; Long fallback: 19 chars"}, "NG0914": {"action": "REPLACE_UNICODE", "reason": "Unicode problematico: emoji_extended; Auto-generato", "symbol": "🞴", "score": "95.0", "issues": "Risky Unicode range: emoji_extended; Long fallback: 14 chars"}, "NG0918": {"action": "REPLACE_UNICODE", "reason": "Unicode problematico: emoji_extended; Auto-generato", "symbol": "🜏", "score": "95.0", "issues": "Risky Unicode range: emoji_extended; Long fallback: 15 chars"}, "NG0919": {"action": "REPLACE_UNICODE", "reason": "Unicode problematico: emoji_extended; Auto-generato", "symbol": "🜦", "score": "95.0", "issues": "Risky Unicode range: emoji_extended; Long fallback: 17 chars"}, "NG0921": {"action": "REPLACE_UNICODE", "reason": "Unicode problematico: emoji_extended; Auto-generato", "symbol": "📕", "score": "95.0", "issues": "Risky Unicode range: emoji_extended; Long fallback: 17 chars"}, "NG0924": {"action": "REPLACE_UNICODE", "reason": "Unicode problematico: emoji_extended; Auto-generato", "symbol": "🎣", "score": "95.0", "issues": "Risky Unicode range: emoji_extended; Long fallback: 17 chars"}, "NG0925": {"action": "REPLACE_UNICODE", "reason": "Unicode problematico: emoji_extended; Auto-generato", "symbol": "🎫", "score": "95.0", "issues": "Risky Unicode range: emoji_extended; Long fallback: 17 chars"}, "NG0928": {"action": "REPLACE_UNICODE", "reason": "Unicode problematico: emoji_extended; Auto-generato", "symbol": "🝆", "score": "95.0", "issues": "Risky Unicode range: emoji_extended; Long fallback: 14 chars; Generic numbered name"}, "NG0930": {"action": "REPLACE_UNICODE", "reason": "Unicode problematico: emoji_extended; Auto-generato", "symbol": "🥅", "score": "95.0", "issues": "Risky Unicode range: emoji_extended; Long fallback: 18 chars; Generic numbered name"}, "NG0931": {"action": "REPLACE_UNICODE", "reason": "Unicode problematico: emoji_extended; Auto-generato", "symbol": "😇", "score": "95.0", "issues": "Risky Unicode range: emoji_extended; Long fallback: 17 chars; Generic numbered name"}, "NG0933": {"action": "REPLACE_UNICODE", "reason": "Unicode problematico: emoji_extended; Auto-generato", "symbol": "🜲", "score": "95.0", "issues": "Risky Unicode range: emoji_extended; Long fallback: 15 chars"}, "NG0934": {"action": "REPLACE_UNICODE", "reason": "Unicode problematico: emoji_extended; Auto-generato", "symbol": "🥓", "score": "95.0", "issues": "Risky Unicode range: emoji_extended; Long fallback: 13 chars"}, "NG0935": {"action": "REPLACE_UNICODE", "reason": "Unicode problematico: emoji_extended; Auto-generato", "symbol": "🝼", "score": "95.0", "issues": "Risky Unicode range: emoji_extended; Long fallback: 14 chars"}, "NG0936": {"action": "REPLACE_UNICODE", "reason": "Unicode problematico: emoji_extended; Auto-generato", "symbol": "🌒", "score": "95.0", "issues": "Risky Unicode range: emoji_extended; Long fallback: 14 chars"}, "NG0939": {"action": "REPLACE_UNICODE", "reason": "Unicode problematico: emoji_extended; Auto-generato", "symbol": "🜧", "score": "95.0", "issues": "Risky Unicode range: emoji_extended; Long fallback: 14 chars"}, "NG0940": {"action": "REPLACE_UNICODE", "reason": "Unicode problematico: emoji_extended; Auto-generato", "symbol": "🎁", "score": "95.0", "issues": "Risky Unicode range: emoji_extended; Long fallback: 15 chars"}, "NG0942": {"action": "REPLACE_UNICODE", "reason": "Unicode problematico: emoji_extended; Auto-generato", "symbol": "🚵", "score": "95.0", "issues": "Risky Unicode range: emoji_extended; Long fallback: 18 chars"}, "NG0944": {"action": "REPLACE_UNICODE", "reason": "Unicode problematico: emoji_extended; Auto-generato", "symbol": "🚡", "score": "95.0", "issues": "Risky Unicode range: emoji_extended; Long fallback: 13 chars"}, "NG0945": {"action": "REPLACE_UNICODE", "reason": "Unicode problematico: emoji_extended; Auto-generato", "symbol": "🛸", "score": "95.0", "issues": "Risky Unicode range: emoji_extended; Long fallback: 12 chars; Generic numbered name"}, "NG0950": {"action": "REPLACE_UNICODE", "reason": "Unicode problematico: emoji_extended; Auto-generato", "symbol": "🝝", "score": "95.0", "issues": "Risky Unicode range: emoji_extended; Long fallback: 14 chars"}, "NG0951": {"action": "REPLACE_UNICODE", "reason": "Unicode problematico: emoji_extended; Auto-generato", "symbol": "🠮", "score": "95.0", "issues": "Risky Unicode range: emoji_extended; Long fallback: 13 chars"}, "NG0953": {"action": "REPLACE_UNICODE", "reason": "Unicode problematico: emoji_extended; Auto-generato", "symbol": "🢠", "score": "95.0", "issues": "Risky Unicode range: emoji_extended; Long fallback: 14 chars; Generic numbered name"}, "NG0956": {"action": "REPLACE_UNICODE", "reason": "Unicode problematico: emoji_extended; Auto-generato", "symbol": "😉", "score": "95.0", "issues": "Risky Unicode range: emoji_extended; Long fallback: 16 chars"}, "NG0957": {"action": "REPLACE_UNICODE", "reason": "Unicode problematico: emoji_extended; Auto-generato", "symbol": "🙇", "score": "95.0", "issues": "Risky Unicode range: emoji_extended; Long fallback: 15 chars"}, "NG0958": {"action": "REPLACE_UNICODE", "reason": "Unicode problematico: emoji_extended; Auto-generato", "symbol": "📔", "score": "95.0", "issues": "Risky Unicode range: emoji_extended; Long fallback: 13 chars"}, "NG0959": {"action": "REPLACE_UNICODE", "reason": "Unicode problematico: emoji_extended; Auto-generato", "symbol": "🞿", "score": "95.0", "issues": "Risky Unicode range: emoji_extended; Long fallback: 16 chars"}, "NG0961": {"action": "REPLACE_UNICODE", "reason": "Unicode problematico: emoji_extended; Auto-generato", "symbol": "🞦", "score": "95.0", "issues": "Risky Unicode range: emoji_extended; Long fallback: 14 chars; Generic numbered name"}, "NG0962": {"action": "REPLACE_UNICODE", "reason": "Unicode problematico: emoji_extended; Auto-generato", "symbol": "🝫", "score": "95.0", "issues": "Risky Unicode range: emoji_extended; Long fallback: 11 chars; Generic numbered name"}, "NG0963": {"action": "REPLACE_UNICODE", "reason": "Unicode problematico: emoji_extended; Auto-generato", "symbol": "😄", "score": "95.0", "issues": "Risky Unicode range: emoji_extended; Long fallback: 13 chars"}, "NG0965": {"action": "REPLACE_UNICODE", "reason": "Unicode problematico: emoji_extended; Auto-generato", "symbol": "🦽", "score": "95.0", "issues": "Risky Unicode range: emoji_extended; Long fallback: 11 chars; Generic numbered name"}, "NG0966": {"action": "REPLACE_UNICODE", "reason": "Unicode problematico: emoji_extended; Auto-generato", "symbol": "🞼", "score": "95.0", "issues": "Risky Unicode range: emoji_extended; Long fallback: 11 chars"}, "NG0967": {"action": "REPLACE_UNICODE", "reason": "Unicode problematico: emoji_extended; Auto-generato", "symbol": "🝠", "score": "95.0", "issues": "Risky Unicode range: emoji_extended; Long fallback: 11 chars"}, "NG0968": {"action": "REPLACE_UNICODE", "reason": "Unicode problematico: emoji_extended; Auto-generato", "symbol": "😳", "score": "95.0", "issues": "Risky Unicode range: emoji_extended; Long fallback: 19 chars"}, "NG0972": {"action": "REPLACE_UNICODE", "reason": "Unicode problematico: emoji_extended; Auto-generato", "symbol": "😨", "score": "95.0", "issues": "Risky Unicode range: emoji_extended; Long fallback: 16 chars; Generic numbered name"}, "NG0975": {"action": "REPLACE_UNICODE", "reason": "Unicode problematico: emoji_extended; Auto-generato", "symbol": "🤅", "score": "95.0", "issues": "Risky Unicode range: emoji_extended; Long fallback: 17 chars"}, "NG0976": {"action": "REPLACE_UNICODE", "reason": "Unicode problematico: emoji_extended; Auto-generato", "symbol": "😮", "score": "95.0", "issues": "Risky Unicode range: emoji_extended; Long fallback: 18 chars; Generic numbered name"}, "NG0979": {"action": "REPLACE_UNICODE", "reason": "Unicode problematico: emoji_extended; Auto-generato", "symbol": "🞍", "score": "95.0", "issues": "Risky Unicode range: emoji_extended; Long fallback: 19 chars"}, "NG0980": {"action": "REPLACE_UNICODE", "reason": "Unicode problematico: emoji_extended; Auto-generato", "symbol": "😵", "score": "95.0", "issues": "Risky Unicode range: emoji_extended; Long fallback: 20 chars"}, "NG0984": {"action": "REPLACE_UNICODE", "reason": "Unicode problematico: emoji_extended; Auto-generato", "symbol": "🜂", "score": "95.0", "issues": "Risky Unicode range: emoji_extended; Long fallback: 20 chars; Generic numbered name"}, "NG0989": {"action": "REPLACE_UNICODE", "reason": "Unicode problematico: emoji_extended; Auto-generato", "symbol": "🤶", "score": "95.0", "issues": "Risky Unicode range: emoji_extended; Long fallback: 20 chars; Generic numbered name"}, "NG0994": {"action": "REPLACE_UNICODE", "reason": "Unicode problematico: emoji_extended; Auto-generato", "symbol": "🚄", "score": "95.0", "issues": "Risky Unicode range: emoji_extended; Long fallback: 14 chars; Generic numbered name"}, "NG0996": {"action": "REPLACE_UNICODE", "reason": "Unicode problematico: emoji_extended; Auto-generato", "symbol": "🡆", "score": "95.0", "issues": "Risky Unicode range: emoji_extended; Long fallback: 14 chars; Generic numbered name"}, "NG0997": {"action": "REPLACE_UNICODE", "reason": "Unicode problematico: emoji_extended; Auto-generato", "symbol": "😱", "score": "95.0", "issues": "Risky Unicode range: emoji_extended; Long fallback: 16 chars; Generic numbered name"}, "NG0998": {"action": "REPLACE_UNICODE", "reason": "Unicode problematico: emoji_extended; Auto-generato", "symbol": "😈", "score": "95.0", "issues": "Risky Unicode range: emoji_extended; Long fallback: 16 chars"}, "NG0999": {"action": "REPLACE_UNICODE", "reason": "Unicode problematico: emoji_extended; Auto-generato", "symbol": "🠬", "score": "95.0", "issues": "Risky Unicode range: emoji_extended; Long fallback: 16 chars; Generic numbered name"}, "NG1001": {"action": "REPLACE_UNICODE", "reason": "Unicode problematico: emoji_extended; Auto-generato", "symbol": "🜒", "score": "95.0", "issues": "Risky Unicode range: emoji_extended; Long fallback: 16 chars; Generic numbered name"}, "NG1004": {"action": "REPLACE_UNICODE", "reason": "Unicode problematico: emoji_extended; Auto-generato", "symbol": "🤨", "score": "95.0", "issues": "Risky Unicode range: emoji_extended; Long fallback: 14 chars"}, "NG1005": {"action": "REPLACE_UNICODE", "reason": "Unicode problematico: emoji_extended; Auto-generato", "symbol": "🚬", "score": "95.0", "issues": "Risky Unicode range: emoji_extended; Long fallback: 12 chars"}, "NG1010": {"action": "REPLACE_UNICODE", "reason": "Unicode problematico: emoji_extended; Auto-generato", "symbol": "💊", "score": "95.0", "issues": "Risky Unicode range: emoji_extended; Long fallback: 12 chars"}, "NG1013": {"action": "REPLACE_UNICODE", "reason": "Unicode problematico: emoji_extended; Auto-generato", "symbol": "🛄", "score": "95.0", "issues": "Risky Unicode range: emoji_extended; Long fallback: 11 chars; Generic numbered name"}, "NG1015": {"action": "REPLACE_UNICODE", "reason": "Unicode problematico: emoji_extended; Auto-generato", "symbol": "🞳", "score": "95.0", "issues": "Risky Unicode range: emoji_extended; Long fallback: 11 chars; Generic numbered name"}, "NG1017": {"action": "REPLACE_UNICODE", "reason": "Unicode problematico: emoji_extended; Auto-generato", "symbol": "🚆", "score": "95.0", "issues": "Risky Unicode range: emoji_extended; Long fallback: 20 chars; Generic numbered name"}, "NG1018": {"action": "REPLACE_UNICODE", "reason": "Unicode problematico: emoji_extended; Auto-generato", "symbol": "🥘", "score": "95.0", "issues": "Risky Unicode range: emoji_extended; Long fallback: 18 chars"}, "NG1021": {"action": "REPLACE_UNICODE", "reason": "Unicode problematico: emoji_extended; Auto-generato", "symbol": "🜢", "score": "95.0", "issues": "Risky Unicode range: emoji_extended; Long fallback: 18 chars"}, "NG1022": {"action": "REPLACE_UNICODE", "reason": "Unicode problematico: emoji_extended; Auto-generato", "symbol": "🕓", "score": "95.0", "issues": "Risky Unicode range: emoji_extended; Long fallback: 20 chars"}, "NG1025": {"action": "REPLACE_UNICODE", "reason": "Unicode problematico: emoji_extended; Auto-generato", "symbol": "🜹", "score": "95.0", "issues": "Risky Unicode range: emoji_extended; Long fallback: 20 chars; Generic numbered name"}, "NG1027": {"action": "REPLACE_UNICODE", "reason": "Unicode problematico: emoji_extended; Auto-generato", "symbol": "🠫", "score": "95.0", "issues": "Risky Unicode range: emoji_extended; Long fallback: 16 chars"}, "NG1031": {"action": "REPLACE_UNICODE", "reason": "Unicode problematico: emoji_extended; Auto-generato", "symbol": "🞥", "score": "95.0", "issues": "Risky Unicode range: emoji_extended; Long fallback: 20 chars; Generic numbered name"}, "NG1036": {"action": "REPLACE_UNICODE", "reason": "Unicode problematico: emoji_extended; Auto-generato", "symbol": "🝲", "score": "95.0", "issues": "Risky Unicode range: emoji_extended; Long fallback: 15 chars"}, "NG1037": {"action": "REPLACE_UNICODE", "reason": "Unicode problematico: emoji_extended; Auto-generato", "symbol": "🝮", "score": "95.0", "issues": "Risky Unicode range: emoji_extended; Long fallback: 16 chars"}, "NG1039": {"action": "REPLACE_UNICODE", "reason": "Unicode problematico: emoji_extended; Auto-generato", "symbol": "😤", "score": "95.0", "issues": "Risky Unicode range: emoji_extended; Long fallback: 17 chars"}, "NG1041": {"action": "REPLACE_UNICODE", "reason": "Unicode problematico: emoji_extended; Auto-generato", "symbol": "🞃", "score": "95.0", "issues": "Risky Unicode range: emoji_extended; Long fallback: 17 chars"}, "NG1043": {"action": "REPLACE_UNICODE", "reason": "Unicode problematico: emoji_extended; Auto-generato", "symbol": "🤉", "score": "95.0", "issues": "Risky Unicode range: emoji_extended; Long fallback: 18 chars; Generic numbered name"}, "NG1044": {"action": "REPLACE_UNICODE", "reason": "Unicode problematico: emoji_extended; Auto-generato", "symbol": "🕮", "score": "95.0", "issues": "Risky Unicode range: emoji_extended; Long fallback: 18 chars; Generic numbered name"}, "NG1045": {"action": "REPLACE_UNICODE", "reason": "Unicode problematico: emoji_extended; Auto-generato", "symbol": "🗜", "score": "95.0", "issues": "Risky Unicode range: emoji_extended; Long fallback: 18 chars; Generic numbered name"}, "NG1047": {"action": "REPLACE_UNICODE", "reason": "Unicode problematico: emoji_extended; Auto-generato", "symbol": "😺", "score": "95.0", "issues": "Risky Unicode range: emoji_extended; Long fallback: 17 chars"}, "NG1048": {"action": "REPLACE_UNICODE", "reason": "Unicode problematico: emoji_extended; Auto-generato", "symbol": "🠆", "score": "95.0", "issues": "Risky Unicode range: emoji_extended; Long fallback: 17 chars"}, "NG1053": {"action": "REPLACE_UNICODE", "reason": "Unicode problematico: emoji_extended; Auto-generato", "symbol": "🕐", "score": "95.0", "issues": "Risky Unicode range: emoji_extended; Long fallback: 17 chars"}, "NG1055": {"action": "REPLACE_UNICODE", "reason": "Unicode problematico: emoji_extended; Auto-generato", "symbol": "🝉", "score": "95.0", "issues": "Risky Unicode range: emoji_extended; Long fallback: 18 chars; Generic numbered name"}, "NG1056": {"action": "REPLACE_UNICODE", "reason": "Unicode problematico: emoji_extended; Auto-generato", "symbol": "🠸", "score": "95.0", "issues": "Risky Unicode range: emoji_extended; Long fallback: 17 chars"}, "NG1057": {"action": "REPLACE_UNICODE", "reason": "Unicode problematico: emoji_extended; Auto-generato", "symbol": "🡢", "score": "95.0", "issues": "Risky Unicode range: emoji_extended; Long fallback: 18 chars; Generic numbered name"}, "NG1058": {"action": "REPLACE_UNICODE", "reason": "Unicode problematico: emoji_extended; Auto-generato", "symbol": "🡗", "score": "95.0", "issues": "Risky Unicode range: emoji_extended; Long fallback: 16 chars; Generic numbered name"}, "NG1059": {"action": "REPLACE_UNICODE", "reason": "Unicode problematico: emoji_extended; Auto-generato", "symbol": "🕜", "score": "95.0", "issues": "Risky Unicode range: emoji_extended; Long fallback: 18 chars; Generic numbered name"}, "NG1062": {"action": "REPLACE_UNICODE", "reason": "Unicode problematico: emoji_extended; Auto-generato", "symbol": "🌮", "score": "95.0", "issues": "Risky Unicode range: emoji_extended; Long fallback: 20 chars; Generic numbered name"}, "NG1063": {"action": "REPLACE_UNICODE", "reason": "Unicode problematico: emoji_extended; Auto-generato", "symbol": "🕀", "score": "95.0", "issues": "Risky Unicode range: emoji_extended; Long fallback: 18 chars"}, "NG1065": {"action": "REPLACE_UNICODE", "reason": "Unicode problematico: emoji_extended; Auto-generato", "symbol": "🟇", "score": "95.0", "issues": "Risky Unicode range: emoji_extended; Long fallback: 19 chars; Generic numbered name"}, "NG1067": {"action": "REPLACE_UNICODE", "reason": "Unicode problematico: emoji_extended; Auto-generato", "symbol": "🛲", "score": "95.0", "issues": "Risky Unicode range: emoji_extended; Long fallback: 20 chars"}, "NG1069": {"action": "REPLACE_UNICODE", "reason": "Unicode problematico: emoji_extended; Auto-generato", "symbol": "🛉", "score": "95.0", "issues": "Risky Unicode range: emoji_extended; Long fallback: 20 chars"}, "NG1073": {"action": "REPLACE_UNICODE", "reason": "Unicode problematico: emoji_extended; Auto-generato", "symbol": "🧒", "score": "95.0", "issues": "Risky Unicode range: emoji_extended; Long fallback: 15 chars; Generic numbered name"}, "NG1075": {"action": "REPLACE_UNICODE", "reason": "Unicode problematico: emoji_extended; Auto-generato", "symbol": "🛝", "score": "95.0", "issues": "Risky Unicode range: emoji_extended; Long fallback: 17 chars; Generic numbered name"}, "NG1077": {"action": "REPLACE_UNICODE", "reason": "Unicode problematico: emoji_extended; Auto-generato", "symbol": "🛖", "score": "95.0", "issues": "Risky Unicode range: emoji_extended; Long fallback: 15 chars; Generic numbered name"}, "NG1078": {"action": "REPLACE_UNICODE", "reason": "Unicode problematico: emoji_extended; Auto-generato", "symbol": "🞒", "score": "95.0", "issues": "Risky Unicode range: emoji_extended; Long fallback: 15 chars; Generic numbered name"}, "NG1079": {"action": "REPLACE_UNICODE", "reason": "Unicode problematico: emoji_extended; Auto-generato", "symbol": "👔", "score": "95.0", "issues": "Risky Unicode range: emoji_extended; Long fallback: 13 chars; Generic numbered name"}, "NG1080": {"action": "REPLACE_UNICODE", "reason": "Unicode problematico: emoji_extended; Auto-generato", "symbol": "🟆", "score": "95.0", "issues": "Risky Unicode range: emoji_extended; Long fallback: 13 chars; Generic numbered name"}, "NG1083": {"action": "REPLACE_UNICODE", "reason": "Unicode problematico: emoji_extended; Auto-generato", "symbol": "🠀", "score": "95.0", "issues": "Risky Unicode range: emoji_extended; Long fallback: 15 chars"}, "NG1084": {"action": "REPLACE_UNICODE", "reason": "Unicode problematico: emoji_extended; Auto-generato", "symbol": "🧤", "score": "95.0", "issues": "Risky Unicode range: emoji_extended; Long fallback: 16 chars"}, "NG1085": {"action": "REPLACE_UNICODE", "reason": "Unicode problematico: emoji_extended; Auto-generato", "symbol": "🟩", "score": "95.0", "issues": "Risky Unicode range: emoji_extended; Long fallback: 14 chars"}, "NG1086": {"action": "REPLACE_UNICODE", "reason": "Unicode problematico: emoji_extended; Auto-generato", "symbol": "🚻", "score": "95.0", "issues": "Risky Unicode range: emoji_extended; Long fallback: 16 chars; Generic numbered name"}, "NG1087": {"action": "REPLACE_UNICODE", "reason": "Unicode problematico: emoji_extended; Auto-generato", "symbol": "🧥", "score": "95.0", "issues": "Risky Unicode range: emoji_extended; Long fallback: 16 chars"}, "NG1088": {"action": "REPLACE_UNICODE", "reason": "Unicode problematico: emoji_extended; Auto-generato", "symbol": "😅", "score": "95.0", "issues": "Risky Unicode range: emoji_extended; Long fallback: 17 chars; Generic numbered name"}, "NG1089": {"action": "REPLACE_UNICODE", "reason": "Unicode problematico: emoji_extended; Auto-generato", "symbol": "🠽", "score": "95.0", "issues": "Risky Unicode range: emoji_extended; Long fallback: 16 chars"}, "NG1090": {"action": "REPLACE_UNICODE", "reason": "Unicode problematico: emoji_extended; Auto-generato", "symbol": "😸", "score": "95.0", "issues": "Risky Unicode range: emoji_extended; Long fallback: 17 chars; Generic numbered name"}, "NG1092": {"action": "REPLACE_UNICODE", "reason": "Unicode problematico: emoji_extended; Auto-generato", "symbol": "😗", "score": "95.0", "issues": "Risky Unicode range: emoji_extended; Long fallback: 17 chars; Generic numbered name"}, "NG1093": {"action": "REPLACE_UNICODE", "reason": "Unicode problematico: emoji_extended; Auto-generato", "symbol": "🜥", "score": "95.0", "issues": "Risky Unicode range: emoji_extended; Long fallback: 12 chars"}, "NG1094": {"action": "REPLACE_UNICODE", "reason": "Unicode problematico: emoji_extended; Auto-generato", "symbol": "🎽", "score": "95.0", "issues": "Risky Unicode range: emoji_extended; Long fallback: 17 chars; Generic numbered name"}, "NG1095": {"action": "REPLACE_UNICODE", "reason": "Unicode problematico: emoji_extended; Auto-generato", "symbol": "📑", "score": "95.0", "issues": "Risky Unicode range: emoji_extended; Long fallback: 17 chars; Generic numbered name"}, "NG1096": {"action": "REPLACE_UNICODE", "reason": "Unicode problematico: emoji_extended; Auto-generato", "symbol": "🏻", "score": "95.0", "issues": "Risky Unicode range: emoji_extended; Long fallback: 15 chars"}, "NG1098": {"action": "REPLACE_UNICODE", "reason": "Unicode problematico: emoji_extended; Auto-generato", "symbol": "🝵", "score": "95.0", "issues": "Risky Unicode range: emoji_extended; Long fallback: 14 chars; Generic numbered name"}, "NG1099": {"action": "REPLACE_UNICODE", "reason": "Unicode problematico: emoji_extended; Auto-generato", "symbol": "🤤", "score": "95.0", "issues": "Risky Unicode range: emoji_extended; Long fallback: 11 chars"}, "NG1102": {"action": "REPLACE_UNICODE", "reason": "Unicode problematico: emoji_extended; Auto-generato", "symbol": "🜕", "score": "95.0", "issues": "Risky Unicode range: emoji_extended; Long fallback: 16 chars; Generic numbered name"}, "NG1107": {"action": "REPLACE_UNICODE", "reason": "Unicode problematico: emoji_extended; Auto-generato", "symbol": "🛀", "score": "95.0", "issues": "Risky Unicode range: emoji_extended; Long fallback: 15 chars"}, "NG1109": {"action": "REPLACE_UNICODE", "reason": "Unicode problematico: emoji_extended; Auto-generato", "symbol": "🡯", "score": "95.0", "issues": "Risky Unicode range: emoji_extended; Long fallback: 20 chars"}, "NG1110": {"action": "REPLACE_UNICODE", "reason": "Unicode problematico: emoji_extended; Auto-generato", "symbol": "🚹", "score": "95.0", "issues": "Risky Unicode range: emoji_extended; Long fallback: 19 chars; Generic numbered name"}, "NG1111": {"action": "REPLACE_UNICODE", "reason": "Unicode problematico: emoji_extended; Auto-generato", "symbol": "🌧", "score": "95.0", "issues": "Risky Unicode range: emoji_extended; Long fallback: 20 chars; Generic numbered name"}, "NG1114": {"action": "REPLACE_UNICODE", "reason": "Unicode problematico: emoji_extended; Auto-generato", "symbol": "🍥", "score": "95.0", "issues": "Risky Unicode range: emoji_extended; Long fallback: 16 chars"}, "NG1115": {"action": "REPLACE_UNICODE", "reason": "Unicode problematico: miscellaneous_symbols; Auto-generato", "symbol": "⛾", "score": "95.0", "issues": "Risky Unicode range: miscellaneous_symbols; Long fallback: 19 chars; Generic numbered name"}, "NG1116": {"action": "REPLACE_UNICODE", "reason": "Unicode problematico: emoji_extended; Auto-generato", "symbol": "🞫", "score": "95.0", "issues": "Risky Unicode range: emoji_extended; Long fallback: 20 chars; Generic numbered name"}, "NG1117": {"action": "REPLACE_UNICODE", "reason": "Unicode problematico: emoji_extended; Auto-generato", "symbol": "🟣", "score": "95.0", "issues": "Risky Unicode range: emoji_extended; Long fallback: 17 chars; Generic numbered name"}, "NG1118": {"action": "REPLACE_UNICODE", "reason": "Unicode problematico: emoji_extended; Auto-generato", "symbol": "🡠", "score": "95.0", "issues": "Risky Unicode range: emoji_extended; Long fallback: 20 chars"}, "NG1119": {"action": "REPLACE_UNICODE", "reason": "Unicode problematico: emoji_extended; Auto-generato", "symbol": "😶", "score": "95.0", "issues": "Risky Unicode range: emoji_extended; Long fallback: 20 chars; Generic numbered name"}, "NG1120": {"action": "REPLACE_UNICODE", "reason": "Unicode problematico: emoji_extended; Auto-generato", "symbol": "🝗", "score": "95.0", "issues": "Risky Unicode range: emoji_extended; Long fallback: 16 chars"}, "NG1121": {"action": "REPLACE_UNICODE", "reason": "Unicode problematico: emoji_extended; Auto-generato", "symbol": "🞶", "score": "95.0", "issues": "Risky Unicode range: emoji_extended; Long fallback: 14 chars"}, "NG1122": {"action": "REPLACE_UNICODE", "reason": "Unicode problematico: emoji_extended; Auto-generato", "symbol": "🝃", "score": "95.0", "issues": "Risky Unicode range: emoji_extended; Long fallback: 17 chars; Generic numbered name"}, "NG1124": {"action": "REPLACE_UNICODE", "reason": "Unicode problematico: emoji_extended; Auto-generato", "symbol": "🟤", "score": "95.0", "issues": "Risky Unicode range: emoji_extended; Long fallback: 17 chars; Generic numbered name"}, "NG1125": {"action": "REPLACE_UNICODE", "reason": "Unicode problematico: emoji_extended; Auto-generato", "symbol": "😆", "score": "95.0", "issues": "Risky Unicode range: emoji_extended; Long fallback: 17 chars; Generic numbered name"}, "NG1128": {"action": "REPLACE_UNICODE", "reason": "Unicode problematico: emoji_extended; Auto-generato", "symbol": "🞁", "score": "95.0", "issues": "Risky Unicode range: emoji_extended; Long fallback: 17 chars; Generic numbered name"}, "NG1129": {"action": "REPLACE_UNICODE", "reason": "Unicode problematico: emoji_extended; Auto-generato", "symbol": "🟒", "score": "95.0", "issues": "Risky Unicode range: emoji_extended; Long fallback: 16 chars"}, "NG1132": {"action": "REPLACE_UNICODE", "reason": "Unicode problematico: emoji_extended; Auto-generato", "symbol": "🚅", "score": "95.0", "issues": "Risky Unicode range: emoji_extended; Long fallback: 19 chars"}, "NG1133": {"action": "REPLACE_UNICODE", "reason": "Unicode problematico: emoji_extended; Auto-generato", "symbol": "🞪", "score": "95.0", "issues": "Risky Unicode range: emoji_extended; Long fallback: 20 chars; Generic numbered name"}, "NG1135": {"action": "REPLACE_UNICODE", "reason": "Unicode problematico: emoji_extended; Auto-generato", "symbol": "🚖", "score": "95.0", "issues": "Risky Unicode range: emoji_extended; Long fallback: 20 chars; Generic numbered name"}, "NG1136": {"action": "REPLACE_UNICODE", "reason": "Unicode problematico: emoji_extended; Auto-generato", "symbol": "😜", "score": "95.0", "issues": "Risky Unicode range: emoji_extended; Long fallback: 20 chars; Generic numbered name"}, "NG1137": {"action": "REPLACE_UNICODE", "reason": "Unicode problematico: emoji_extended; Auto-generato", "symbol": "🝍", "score": "95.0", "issues": "Risky Unicode range: emoji_extended; Long fallback: 18 chars; Generic numbered name"}, "NG1139": {"action": "REPLACE_UNICODE", "reason": "Unicode problematico: emoji_extended; Auto-generato", "symbol": "🜅", "score": "95.0", "issues": "Risky Unicode range: emoji_extended; Long fallback: 18 chars; Generic numbered name"}, "NG1140": {"action": "REPLACE_UNICODE", "reason": "Unicode problematico: emoji_extended; Auto-generato", "symbol": "🦇", "score": "95.0", "issues": "Risky Unicode range: emoji_extended; Long fallback: 20 chars; Generic numbered name"}, "NG1141": {"action": "REPLACE_UNICODE", "reason": "Unicode problematico: emoji_extended; Auto-generato", "symbol": "😰", "score": "95.0", "issues": "Risky Unicode range: emoji_extended; Long fallback: 20 chars; Generic numbered name"}, "NG1142": {"action": "REPLACE_UNICODE", "reason": "Unicode problematico: emoji_extended; Auto-generato", "symbol": "😊", "score": "95.0", "issues": "Risky Unicode range: emoji_extended; Long fallback: 20 chars; Generic numbered name"}, "NG1144": {"action": "REPLACE_UNICODE", "reason": "Unicode problematico: emoji_extended; Auto-generato", "symbol": "🧼", "score": "95.0", "issues": "Risky Unicode range: emoji_extended; Long fallback: 17 chars"}, "NG1147": {"action": "REPLACE_UNICODE", "reason": "Unicode problematico: emoji_extended; Auto-generato", "symbol": "🟕", "score": "95.0", "issues": "Risky Unicode range: emoji_extended; Long fallback: 18 chars; Generic numbered name"}, "NG1148": {"action": "REPLACE_UNICODE", "reason": "Unicode problematico: emoji_extended; Auto-generato", "symbol": "🗖", "score": "95.0", "issues": "Risky Unicode range: emoji_extended; Long fallback: 18 chars; Generic numbered name"}, "NG1150": {"action": "REPLACE_UNICODE", "reason": "Unicode problematico: emoji_extended; Auto-generato", "symbol": "🜍", "score": "95.0", "issues": "Risky Unicode range: emoji_extended; Long fallback: 15 chars"}, "NG1151": {"action": "REPLACE_UNICODE", "reason": "Unicode problematico: emoji_extended; Auto-generato", "symbol": "🜤", "score": "95.0", "issues": "Risky Unicode range: emoji_extended; Long fallback: 18 chars; Generic numbered name"}, "NG1154": {"action": "REPLACE_UNICODE", "reason": "Unicode problematico: emoji_extended; Auto-generato", "symbol": "🝣", "score": "95.0", "issues": "Risky Unicode range: emoji_extended; Long fallback: 18 chars; Generic numbered name"}, "NG1158": {"action": "REPLACE_UNICODE", "reason": "Unicode problematico: emoji_extended; Auto-generato", "symbol": "🝓", "score": "95.0", "issues": "Risky Unicode range: emoji_extended; Long fallback: 17 chars; Generic numbered name"}, "NG1159": {"action": "REPLACE_UNICODE", "reason": "Unicode problematico: emoji_extended; Auto-generato", "symbol": "🝚", "score": "95.0", "issues": "Risky Unicode range: emoji_extended; Long fallback: 14 chars; Generic numbered name"}, "NG1161": {"action": "REPLACE_UNICODE", "reason": "Unicode problematico: emoji_extended; Auto-generato", "symbol": "🛅", "score": "95.0", "issues": "Risky Unicode range: emoji_extended; Long fallback: 17 chars; Generic numbered name"}, "NG1165": {"action": "REPLACE_UNICODE", "reason": "Unicode problematico: emoji_extended; Auto-generato", "symbol": "🡪", "score": "95.0", "issues": "Risky Unicode range: emoji_extended; Long fallback: 18 chars; Generic numbered name"}, "NG1166": {"action": "REPLACE_UNICODE", "reason": "Unicode problematico: emoji_extended; Auto-generato", "symbol": "🙎", "score": "95.0", "issues": "Risky Unicode range: emoji_extended; Long fallback: 18 chars; Generic numbered name"}, "NG1170": {"action": "REPLACE_UNICODE", "reason": "Unicode problematico: emoji_extended; Auto-generato", "symbol": "🝋", "score": "95.0", "issues": "Risky Unicode range: emoji_extended; Long fallback: 16 chars; Generic numbered name"}, "NG1172": {"action": "REPLACE_UNICODE", "reason": "Unicode problematico: emoji_extended; Auto-generato", "symbol": "🟪", "score": "95.0", "issues": "Risky Unicode range: emoji_extended; Long fallback: 17 chars; Generic numbered name"}, "NG1174": {"action": "REPLACE_UNICODE", "reason": "Unicode problematico: emoji_extended; Auto-generato", "symbol": "🛆", "score": "95.0", "issues": "Risky Unicode range: emoji_extended; Long fallback: 18 chars; Generic numbered name"}, "NG1175": {"action": "REPLACE_UNICODE", "reason": "Unicode problematico: emoji_extended; Auto-generato", "symbol": "🛋", "score": "95.0", "issues": "Risky Unicode range: emoji_extended; Long fallback: 18 chars; Generic numbered name"}, "NG1176": {"action": "REPLACE_UNICODE", "reason": "Unicode problematico: emoji_extended; Auto-generato", "symbol": "🔰", "score": "95.0", "issues": "Risky Unicode range: emoji_extended; Long fallback: 18 chars; Generic numbered name"}, "NG1177": {"action": "REPLACE_UNICODE", "reason": "Unicode problematico: emoji_extended; Auto-generato", "symbol": "📼", "score": "95.0", "issues": "Risky Unicode range: emoji_extended; Long fallback: 17 chars; Generic numbered name"}, "NG1179": {"action": "REPLACE_UNICODE", "reason": "Unicode problematico: emoji_extended; Auto-generato", "symbol": "🝞", "score": "95.0", "issues": "Risky Unicode range: emoji_extended; Long fallback: 18 chars; Generic numbered name"}, "NG1180": {"action": "REPLACE_UNICODE", "reason": "Unicode problematico: emoji_extended; Auto-generato", "symbol": "🖞", "score": "95.0", "issues": "Risky Unicode range: emoji_extended; Long fallback: 18 chars; Generic numbered name"}, "NG1182": {"action": "REPLACE_UNICODE", "reason": "Unicode problematico: emoji_extended; Auto-generato", "symbol": "🦀", "score": "95.0", "issues": "Risky Unicode range: emoji_extended; Long fallback: 16 chars; Generic numbered name"}, "NG1183": {"action": "REPLACE_UNICODE", "reason": "Unicode problematico: emoji_extended; Auto-generato", "symbol": "🌇", "score": "95.0", "issues": "Risky Unicode range: emoji_extended; Long fallback: 18 chars; Generic numbered name"}, "NG1184": {"action": "REPLACE_UNICODE", "reason": "Unicode problematico: emoji_extended; Auto-generato", "symbol": "🛹", "score": "95.0", "issues": "Risky Unicode range: emoji_extended; Long fallback: 18 chars; Generic numbered name"}, "NG1185": {"action": "REPLACE_UNICODE", "reason": "Unicode problematico: emoji_extended; Auto-generato", "symbol": "🚩", "score": "95.0", "issues": "Risky Unicode range: emoji_extended; Long fallback: 18 chars; Generic numbered name"}, "NG1186": {"action": "REPLACE_UNICODE", "reason": "Unicode problematico: emoji_extended; Auto-generato", "symbol": "🙌", "score": "95.0", "issues": "Risky Unicode range: emoji_extended; Long fallback: 18 chars; Generic numbered name"}, "NG1191": {"action": "REPLACE_UNICODE", "reason": "Unicode problematico: emoji_extended; Auto-generato", "symbol": "🗟", "score": "95.0", "issues": "Risky Unicode range: emoji_extended; Long fallback: 16 chars"}, "NG1193": {"action": "REPLACE_UNICODE", "reason": "Unicode problematico: emoji_extended; Auto-generato", "symbol": "🙅", "score": "95.0", "issues": "Risky Unicode range: emoji_extended; Long fallback: 14 chars"}, "NG1196": {"action": "REPLACE_UNICODE", "reason": "Unicode problematico: emoji_extended; Auto-generato", "symbol": "🚂", "score": "95.0", "issues": "Risky Unicode range: emoji_extended; Long fallback: 13 chars; Generic numbered name"}, "NG1199": {"action": "REPLACE_UNICODE", "reason": "Unicode problematico: emoji_extended; Auto-generato", "symbol": "🠟", "score": "95.0", "issues": "Risky Unicode range: emoji_extended; Long fallback: 15 chars; Generic numbered name"}, "NG1200": {"action": "REPLACE_UNICODE", "reason": "Unicode problematico: emoji_extended; Auto-generato", "symbol": "🦢", "score": "95.0", "issues": "Risky Unicode range: emoji_extended; Long fallback: 13 chars; Generic numbered name"}, "NG1201": {"action": "REPLACE_UNICODE", "reason": "Unicode problematico: emoji_extended; Auto-generato", "symbol": "🥤", "score": "95.0", "issues": "Risky Unicode range: emoji_extended; Long fallback: 16 chars; Generic numbered name"}, "NG1202": {"action": "REPLACE_UNICODE", "reason": "Unicode problematico: emoji_extended; Auto-generato", "symbol": "🝻", "score": "95.0", "issues": "Risky Unicode range: emoji_extended; Long fallback: 18 chars"}, "NG1203": {"action": "REPLACE_UNICODE", "reason": "Unicode problematico: emoji_extended; Auto-generato", "symbol": "🥆", "score": "95.0", "issues": "Risky Unicode range: emoji_extended; Long fallback: 19 chars; Generic numbered name"}, "NG1204": {"action": "REPLACE_UNICODE", "reason": "Unicode problematico: emoji_extended; Auto-generato", "symbol": "🢩", "score": "95.0", "issues": "Risky Unicode range: emoji_extended; Long fallback: 18 chars"}, "NG1205": {"action": "REPLACE_UNICODE", "reason": "Unicode problematico: emoji_extended; Auto-generato", "symbol": "🟘", "score": "95.0", "issues": "Risky Unicode range: emoji_extended; Long fallback: 16 chars"}, "NG1207": {"action": "REPLACE_UNICODE", "reason": "Unicode problematico: emoji_extended; Auto-generato", "symbol": "🝔", "score": "95.0", "issues": "Risky Unicode range: emoji_extended; Long fallback: 14 chars"}, "NG1209": {"action": "REPLACE_UNICODE", "reason": "Unicode problematico: emoji_extended; Auto-generato", "symbol": "😣", "score": "95.0", "issues": "Risky Unicode range: emoji_extended; Long fallback: 15 chars; Generic numbered name"}, "NG1210": {"action": "REPLACE_UNICODE", "reason": "Unicode problematico: emoji_extended; Auto-generato", "symbol": "🜆", "score": "95.0", "issues": "Risky Unicode range: emoji_extended; Long fallback: 15 chars; Generic numbered name"}, "NG1216": {"action": "REPLACE_UNICODE", "reason": "Unicode problematico: emoji_extended; Auto-generato", "symbol": "🞂", "score": "95.0", "issues": "Risky Unicode range: emoji_extended; Long fallback: 19 chars"}, "NG1217": {"action": "REPLACE_UNICODE", "reason": "Unicode problematico: emoji_extended; Auto-generato", "symbol": "🥾", "score": "95.0", "issues": "Risky Unicode range: emoji_extended; Long fallback: 20 chars; Generic numbered name"}, "NG1219": {"action": "REPLACE_UNICODE", "reason": "Unicode problematico: emoji_extended; Auto-generato", "symbol": "🠙", "score": "95.0", "issues": "Risky Unicode range: emoji_extended; Long fallback: 20 chars; Generic numbered name"}, "NG1220": {"action": "REPLACE_UNICODE", "reason": "Unicode problematico: emoji_extended; Auto-generato", "symbol": "🟍", "score": "95.0", "issues": "Risky Unicode range: emoji_extended; Long fallback: 20 chars; Generic numbered name"}, "NG1222": {"action": "REPLACE_UNICODE", "reason": "Unicode problematico: miscellaneous_symbols; Auto-generato", "symbol": "⛰", "score": "95.0", "issues": "Risky Unicode range: miscellaneous_symbols; Long fallback: 20 chars; Generic numbered name"}, "NG1223": {"action": "REPLACE_UNICODE", "reason": "Unicode problematico: emoji_extended; Auto-generato", "symbol": "😛", "score": "95.0", "issues": "Risky Unicode range: emoji_extended; Long fallback: 20 chars; Generic numbered name"}, "NG1225": {"action": "REPLACE_UNICODE", "reason": "Unicode problematico: emoji_extended; Auto-generato", "symbol": "🔤", "score": "95.0", "issues": "Risky Unicode range: emoji_extended; Long fallback: 20 chars; Generic numbered name"}, "NG1227": {"action": "REPLACE_UNICODE", "reason": "Unicode problematico: emoji_extended; Auto-generato", "symbol": "👢", "score": "95.0", "issues": "Risky Unicode range: emoji_extended; Long fallback: 20 chars; Generic numbered name"}, "NG1228": {"action": "REPLACE_UNICODE", "reason": "Unicode problematico: emoji_extended; Auto-generato", "symbol": "🛈", "score": "95.0", "issues": "Risky Unicode range: emoji_extended; Long fallback: 20 chars; Generic numbered name"}, "NG1229": {"action": "REPLACE_UNICODE", "reason": "Unicode problematico: emoji_extended; Auto-generato", "symbol": "🛰", "score": "95.0", "issues": "Risky Unicode range: emoji_extended; Long fallback: 20 chars; Generic numbered name"}, "NG1230": {"action": "REPLACE_UNICODE", "reason": "Unicode problematico: miscellaneous_symbols; Auto-generato", "symbol": "⛜", "score": "95.0", "issues": "Risky Unicode range: miscellaneous_symbols; Long fallback: 20 chars; Generic numbered name"}, "NG1231": {"action": "REPLACE_UNICODE", "reason": "Unicode problematico: emoji_extended; Auto-generato", "symbol": "🦅", "score": "95.0", "issues": "Risky Unicode range: emoji_extended; Long fallback: 20 chars; Generic numbered name"}, "NG1234": {"action": "REPLACE_UNICODE", "reason": "Unicode problematico: emoji_extended; Auto-generato", "symbol": "🐽", "score": "95.0", "issues": "Risky Unicode range: emoji_extended; Long fallback: 20 chars; Generic numbered name"}, "NG1238": {"action": "REPLACE_UNICODE", "reason": "Unicode problematico: emoji_extended; Auto-generato", "symbol": "🟥", "score": "95.0", "issues": "Risky Unicode range: emoji_extended; Long fallback: 19 chars; Generic numbered name"}, "NG1239": {"action": "REPLACE_UNICODE", "reason": "Unicode problematico: emoji_extended; Auto-generato", "symbol": "😢", "score": "95.0", "issues": "Risky Unicode range: emoji_extended; Long fallback: 19 chars; Generic numbered name"}, "NG1240": {"action": "REPLACE_UNICODE", "reason": "Unicode problematico: emoji_extended; Auto-generato", "symbol": "🚁", "score": "95.0", "issues": "Risky Unicode range: emoji_extended; Long fallback: 19 chars; Generic numbered name"}, "NG1242": {"action": "REPLACE_UNICODE", "reason": "Unicode problematico: emoji_extended; Auto-generato", "symbol": "🠭", "score": "95.0", "issues": "Risky Unicode range: emoji_extended; Long fallback: 19 chars; Generic numbered name"}, "NG1243": {"action": "REPLACE_UNICODE", "reason": "Unicode problematico: emoji_extended; Auto-generato", "symbol": "👿", "score": "95.0", "issues": "Risky Unicode range: emoji_extended; Long fallback: 19 chars; Generic numbered name"}, "NG1245": {"action": "REPLACE_UNICODE", "reason": "Unicode problematico: emoji_extended; Auto-generato", "symbol": "🤕", "score": "95.0", "issues": "Risky Unicode range: emoji_extended; Long fallback: 19 chars; Generic numbered name"}, "NG1247": {"action": "REPLACE_UNICODE", "reason": "Unicode problematico: emoji_extended; Auto-generato", "symbol": "🍓", "score": "95.0", "issues": "Risky Unicode range: emoji_extended; Long fallback: 20 chars; Generic numbered name"}, "NG1252": {"action": "REPLACE_UNICODE", "reason": "Unicode problematico: emoji_extended; Auto-generato", "symbol": "🠰", "score": "95.0", "issues": "Risky Unicode range: emoji_extended; Long fallback: 11 chars"}, "NG1256": {"action": "REPLACE_UNICODE", "reason": "Unicode problematico: emoji_extended; Auto-generato", "symbol": "🏦", "score": "95.0", "issues": "Risky Unicode range: emoji_extended; Long fallback: 14 chars; Generic numbered name"}, "NG1257": {"action": "REPLACE_UNICODE", "reason": "Unicode problematico: emoji_extended; Auto-generato", "symbol": "📭", "score": "95.0", "issues": "Risky Unicode range: emoji_extended; Long fallback: 20 chars"}, "NG1260": {"action": "REPLACE_UNICODE", "reason": "Unicode problematico: emoji_extended; Auto-generato", "symbol": "🝇", "score": "95.0", "issues": "Risky Unicode range: emoji_extended; Long fallback: 19 chars; Generic numbered name"}, "NG1264": {"action": "REPLACE_UNICODE", "reason": "Unicode problematico: emoji_extended; Auto-generato", "symbol": "🧶", "score": "95.0", "issues": "Risky Unicode range: emoji_extended; Long fallback: 18 chars"}, "NG1265": {"action": "REPLACE_UNICODE", "reason": "Unicode problematico: emoji_extended; Auto-generato", "symbol": "🠔", "score": "95.0", "issues": "Risky Unicode range: emoji_extended; Long fallback: 17 chars"}, "NG1268": {"action": "REPLACE_UNICODE", "reason": "Unicode problematico: emoji_extended; Auto-generato", "symbol": "🥿", "score": "95.0", "issues": "Risky Unicode range: emoji_extended; Long fallback: 18 chars"}, "NG1271": {"action": "REPLACE_UNICODE", "reason": "Unicode problematico: emoji_extended; Auto-generato", "symbol": "🚧", "score": "95.0", "issues": "Risky Unicode range: emoji_extended; Long fallback: 14 chars"}, "NG1275": {"action": "REPLACE_UNICODE", "reason": "Unicode problematico: emoji_extended; Auto-generato", "symbol": "🦭", "score": "95.0", "issues": "Risky Unicode range: emoji_extended; Long fallback: 19 chars"}, "NG1280": {"action": "REPLACE_UNICODE", "reason": "Unicode problematico: emoji_extended; Auto-generato", "symbol": "🙂", "score": "95.0", "issues": "Risky Unicode range: emoji_extended; Long fallback: 20 chars; Generic numbered name"}, "NG1283": {"action": "REPLACE_UNICODE", "reason": "Unicode problematico: emoji_extended; Auto-generato", "symbol": "🤭", "score": "95.0", "issues": "Risky Unicode range: emoji_extended; Long fallback: 20 chars"}, "NG1284": {"action": "REPLACE_UNICODE", "reason": "Unicode problematico: emoji_extended; Auto-generato", "symbol": "🜰", "score": "95.0", "issues": "Risky Unicode range: emoji_extended; Long fallback: 20 chars"}, "NG1286": {"action": "REPLACE_UNICODE", "reason": "Unicode problematico: emoji_extended; Auto-generato", "symbol": "🟅", "score": "95.0", "issues": "Risky Unicode range: emoji_extended; Long fallback: 18 chars"}, "NG1287": {"action": "REPLACE_UNICODE", "reason": "Unicode problematico: emoji_extended; Auto-generato", "symbol": "🝬", "score": "95.0", "issues": "Risky Unicode range: emoji_extended; Long fallback: 20 chars"}, "NG1290": {"action": "REPLACE_UNICODE", "reason": "Unicode problematico: emoji_extended; Auto-generato", "symbol": "🧆", "score": "95.0", "issues": "Risky Unicode range: emoji_extended; Long fallback: 14 chars"}, "NG1291": {"action": "REPLACE_UNICODE", "reason": "Unicode problematico: emoji_extended; Auto-generato", "symbol": "🧧", "score": "95.0", "issues": "Risky Unicode range: emoji_extended; Long fallback: 12 chars"}, "NG1292": {"action": "REPLACE_UNICODE", "reason": "Unicode problematico: emoji_extended; Auto-generato", "symbol": "🝟", "score": "95.0", "issues": "Risky Unicode range: emoji_extended; Long fallback: 13 chars"}, "NG1294": {"action": "REPLACE_UNICODE", "reason": "Unicode problematico: emoji_extended; Auto-generato", "symbol": "🦃", "score": "95.0", "issues": "Risky Unicode range: emoji_extended; Long fallback: 15 chars; Generic numbered name"}, "NG1296": {"action": "REPLACE_UNICODE", "reason": "Unicode problematico: emoji_extended; Auto-generato", "symbol": "🧫", "score": "95.0", "issues": "Risky Unicode range: emoji_extended; Long fallback: 13 chars; Generic numbered name"}, "NG1297": {"action": "REPLACE_UNICODE", "reason": "Unicode problematico: miscellaneous_symbols; Auto-generato", "symbol": "⛈", "score": "95.0", "issues": "Risky Unicode range: miscellaneous_symbols; Long fallback: 14 chars; Generic numbered name"}, "NG1299": {"action": "REPLACE_UNICODE", "reason": "Unicode problematico: emoji_extended; Auto-generato", "symbol": "🡧", "score": "95.0", "issues": "Risky Unicode range: emoji_extended; Long fallback: 16 chars"}, "NG1300": {"action": "REPLACE_UNICODE", "reason": "Unicode problematico: emoji_extended; Auto-generato", "symbol": "💯", "score": "95.0", "issues": "Risky Unicode range: emoji_extended; Long fallback: 17 chars"}, "NG1303": {"action": "REPLACE_UNICODE", "reason": "Unicode problematico: emoji_extended; Auto-generato", "symbol": "🥉", "score": "95.0", "issues": "Risky Unicode range: emoji_extended; Long fallback: 14 chars; Generic numbered name"}, "NG1305": {"action": "REPLACE_UNICODE", "reason": "Unicode problematico: emoji_extended; Auto-generato", "symbol": "🛒", "score": "95.0", "issues": "Risky Unicode range: emoji_extended; Long fallback: 15 chars"}, "NG1306": {"action": "REPLACE_UNICODE", "reason": "Unicode problematico: emoji_extended; Auto-generato", "symbol": "🧁", "score": "95.0", "issues": "Risky Unicode range: emoji_extended; Long fallback: 16 chars; Generic numbered name"}, "NG1307": {"action": "REPLACE_UNICODE", "reason": "Unicode problematico: emoji_extended; Auto-generato", "symbol": "🚛", "score": "95.0", "issues": "Risky Unicode range: emoji_extended; Long fallback: 18 chars"}, "NG1308": {"action": "REPLACE_UNICODE", "reason": "Unicode problematico: emoji_extended; Auto-generato", "symbol": "💰", "score": "95.0", "issues": "Risky Unicode range: emoji_extended; Long fallback: 17 chars"}, "NG1313": {"action": "REPLACE_UNICODE", "reason": "Unicode problematico: emoji_extended; Auto-generato", "symbol": "🜬", "score": "95.0", "issues": "Risky Unicode range: emoji_extended; Long fallback: 19 chars"}, "NG1316": {"action": "REPLACE_UNICODE", "reason": "Unicode problematico: emoji_extended; Auto-generato", "symbol": "🜓", "score": "95.0", "issues": "Risky Unicode range: emoji_extended; Long fallback: 17 chars"}, "NG1319": {"action": "REPLACE_UNICODE", "reason": "Unicode problematico: emoji_extended; Auto-generato", "symbol": "🙊", "score": "95.0", "issues": "Risky Unicode range: emoji_extended; Long fallback: 18 chars; Generic numbered name"}, "NG1320": {"action": "REPLACE_UNICODE", "reason": "Unicode problematico: emoji_extended; Auto-generato", "symbol": "🞯", "score": "95.0", "issues": "Risky Unicode range: emoji_extended; Long fallback: 20 chars; Generic numbered name"}, "NG1323": {"action": "REPLACE_UNICODE", "reason": "Unicode problematico: emoji_extended; Auto-generato", "symbol": "🜔", "score": "95.0", "issues": "Risky Unicode range: emoji_extended; Long fallback: 18 chars; Generic numbered name"}, "NG1324": {"action": "REPLACE_UNICODE", "reason": "Unicode problematico: emoji_extended; Auto-generato", "symbol": "🞐", "score": "95.0", "issues": "Risky Unicode range: emoji_extended; Long fallback: 20 chars; Generic numbered name"}, "NG1328": {"action": "REPLACE_UNICODE", "reason": "Unicode problematico: emoji_extended; Auto-generato", "symbol": "🦜", "score": "95.0", "issues": "Risky Unicode range: emoji_extended; Long fallback: 20 chars; Generic numbered name"}, "NG1330": {"action": "REPLACE_UNICODE", "reason": "Unicode problematico: emoji_extended; Auto-generato", "symbol": "🢪", "score": "95.0", "issues": "Risky Unicode range: emoji_extended; Long fallback: 18 chars; Generic numbered name"}, "NG1333": {"action": "REPLACE_UNICODE", "reason": "Unicode problematico: emoji_extended; Auto-generato", "symbol": "🟉", "score": "95.0", "issues": "Risky Unicode range: emoji_extended; Long fallback: 20 chars; Generic numbered name"}, "NG1335": {"action": "REPLACE_UNICODE", "reason": "Unicode problematico: emoji_extended; Auto-generato", "symbol": "🝨", "score": "95.0", "issues": "Risky Unicode range: emoji_extended; Long fallback: 20 chars; Generic numbered name"}, "NG1337": {"action": "REPLACE_UNICODE", "reason": "Unicode problematico: emoji_extended; Auto-generato", "symbol": "🤔", "score": "95.0", "issues": "Risky Unicode range: emoji_extended; Long fallback: 18 chars; Generic numbered name"}, "NG1338": {"action": "REPLACE_UNICODE", "reason": "Unicode problematico: emoji_extended; Auto-generato", "symbol": "🤊", "score": "95.0", "issues": "Risky Unicode range: emoji_extended; Long fallback: 18 chars; Generic numbered name"}, "NG1339": {"action": "REPLACE_UNICODE", "reason": "Unicode problematico: emoji_extended; Auto-generato", "symbol": "😝", "score": "95.0", "issues": "Risky Unicode range: emoji_extended; Long fallback: 18 chars; Generic numbered name"}, "NG1340": {"action": "REPLACE_UNICODE", "reason": "Unicode problematico: emoji_extended; Auto-generato", "symbol": "📐", "score": "95.0", "issues": "Risky Unicode range: emoji_extended; Long fallback: 18 chars; Generic numbered name"}, "NG1342": {"action": "REPLACE_UNICODE", "reason": "Unicode problematico: emoji_extended; Auto-generato", "symbol": "🝳", "score": "95.0", "issues": "Risky Unicode range: emoji_extended; Long fallback: 19 chars; Generic numbered name"}, "NG1343": {"action": "REPLACE_UNICODE", "reason": "Unicode problematico: emoji_extended; Auto-generato", "symbol": "🛻", "score": "95.0", "issues": "Risky Unicode range: emoji_extended; Long fallback: 19 chars; Generic numbered name"}, "NG1345": {"action": "REPLACE_UNICODE", "reason": "Unicode problematico: emoji_extended; Auto-generato", "symbol": "🙃", "score": "95.0", "issues": "Risky Unicode range: emoji_extended; Long fallback: 20 chars; Generic numbered name"}, "NG1346": {"action": "REPLACE_UNICODE", "reason": "Unicode problematico: emoji_extended; Auto-generato", "symbol": "🖆", "score": "95.0", "issues": "Risky Unicode range: emoji_extended; Long fallback: 19 chars; Generic numbered name"}, "NG1349": {"action": "REPLACE_UNICODE", "reason": "Unicode problematico: emoji_extended; Auto-generato", "symbol": "😋", "score": "95.0", "issues": "Risky Unicode range: emoji_extended; Long fallback: 19 chars; Generic numbered name"}, "NG1350": {"action": "REPLACE_UNICODE", "reason": "Unicode problematico: emoji_extended; Auto-generato", "symbol": "🐜", "score": "95.0", "issues": "Risky Unicode range: emoji_extended; Long fallback: 19 chars; Generic numbered name"}, "NG1351": {"action": "REPLACE_UNICODE", "reason": "Unicode problematico: emoji_extended; Auto-generato", "symbol": "🏊", "score": "95.0", "issues": "Risky Unicode range: emoji_extended; Long fallback: 19 chars; Generic numbered name"}, "NG1353": {"action": "REPLACE_UNICODE", "reason": "Unicode problematico: emoji_extended; Auto-generato", "symbol": "🕵", "score": "95.0", "issues": "Risky Unicode range: emoji_extended; Long fallback: 19 chars; Generic numbered name"}, "NG1355": {"action": "REPLACE_UNICODE", "reason": "Unicode problematico: emoji_extended; Auto-generato", "symbol": "🛫", "score": "95.0", "issues": "Risky Unicode range: emoji_extended; Long fallback: 19 chars; Generic numbered name"}, "NG1357": {"action": "REPLACE_UNICODE", "reason": "Unicode problematico: emoji_extended; Auto-generato", "symbol": "🞡", "score": "95.0", "issues": "Risky Unicode range: emoji_extended; Long fallback: 19 chars; Generic numbered name"}, "NG1366": {"action": "REPLACE_UNICODE", "reason": "Unicode problematico: emoji_extended; Auto-generato", "symbol": "🝙", "score": "95.0", "issues": "Risky Unicode range: emoji_extended; Long fallback: 20 chars; Generic numbered name"}, "NG1368": {"action": "REPLACE_UNICODE", "reason": "Unicode problematico: emoji_extended; Auto-generato", "symbol": "🐵", "score": "95.0", "issues": "Risky Unicode range: emoji_extended; Long fallback: 20 chars; Generic numbered name"}, "NG1369": {"action": "REPLACE_UNICODE", "reason": "Unicode problematico: emoji_extended; Auto-generato", "symbol": "😘", "score": "95.0", "issues": "Risky Unicode range: emoji_extended; Long fallback: 20 chars; Generic numbered name"}, "NG1372": {"action": "REPLACE_UNICODE", "reason": "Unicode problematico: emoji_extended; Auto-generato", "symbol": "🧳", "score": "95.0", "issues": "Risky Unicode range: emoji_extended; Long fallback: 16 chars; Generic numbered name"}, "NG1373": {"action": "REPLACE_UNICODE", "reason": "Unicode problematico: emoji_extended; Auto-generato", "symbol": "🚮", "score": "95.0", "issues": "Risky Unicode range: emoji_extended; Long fallback: 17 chars"}, "NG1376": {"action": "REPLACE_UNICODE", "reason": "Unicode problematico: emoji_extended; Auto-generato", "symbol": "🕡", "score": "95.0", "issues": "Risky Unicode range: emoji_extended; Long fallback: 16 chars"}, "NG1382": {"action": "REPLACE_UNICODE", "reason": "Unicode problematico: emoji_extended; Auto-generato", "symbol": "😕", "score": "95.0", "issues": "Risky Unicode range: emoji_extended; Long fallback: 20 chars; Generic numbered name"}, "NG1383": {"action": "REPLACE_UNICODE", "reason": "Unicode problematico: emoji_extended; Auto-generato", "symbol": "🦬", "score": "95.0", "issues": "Risky Unicode range: emoji_extended; Long fallback: 20 chars; Generic numbered name"}, "NG1386": {"action": "REPLACE_UNICODE", "reason": "Unicode problematico: emoji_extended; Auto-generato", "symbol": "🜴", "score": "95.0", "issues": "Risky Unicode range: emoji_extended; Long fallback: 20 chars"}, "NG1387": {"action": "REPLACE_UNICODE", "reason": "Unicode problematico: emoji_extended; Auto-generato", "symbol": "😷", "score": "95.0", "issues": "Risky Unicode range: emoji_extended; Long fallback: 20 chars"}, "NG1388": {"action": "REPLACE_UNICODE", "reason": "Unicode problematico: emoji_extended; Auto-generato", "symbol": "🤗", "score": "95.0", "issues": "Risky Unicode range: emoji_extended; Long fallback: 18 chars"}, "NG1389": {"action": "REPLACE_UNICODE", "reason": "Unicode problematico: emoji_extended; Auto-generato", "symbol": "😼", "score": "95.0", "issues": "Risky Unicode range: emoji_extended; Long fallback: 19 chars; Generic numbered name"}, "NG1392": {"action": "REPLACE_UNICODE", "reason": "Unicode problematico: emoji_extended; Auto-generato", "symbol": "😙", "score": "95.0", "issues": "Risky Unicode range: emoji_extended; Long fallback: 19 chars; Generic numbered name"}, "NG1399": {"action": "REPLACE_UNICODE", "reason": "Unicode problematico: emoji_extended; Auto-generato", "symbol": "😒", "score": "95.0", "issues": "Risky Unicode range: emoji_extended; Long fallback: 20 chars"}, "NG1400": {"action": "REPLACE_UNICODE", "reason": "Unicode problematico: emoji_extended; Auto-generato", "symbol": "🛏", "score": "95.0", "issues": "Risky Unicode range: emoji_extended; Long fallback: 20 chars; Generic numbered name"}, "NG1401": {"action": "REPLACE_UNICODE", "reason": "Unicode problematico: emoji_extended; Auto-generato", "symbol": "🎤", "score": "95.0", "issues": "Risky Unicode range: emoji_extended; Long fallback: 19 chars"}, "NG1402": {"action": "REPLACE_UNICODE", "reason": "Unicode problematico: emoji_extended; Auto-generato", "symbol": "🛔", "score": "95.0", "issues": "Risky Unicode range: emoji_extended; Long fallback: 19 chars"}, "NG1403": {"action": "REPLACE_UNICODE", "reason": "Unicode problematico: emoji_extended; Auto-generato", "symbol": "🧹", "score": "95.0", "issues": "Risky Unicode range: emoji_extended; Long fallback: 20 chars; Generic numbered name"}, "NG1406": {"action": "REPLACE_UNICODE", "reason": "Unicode problematico: emoji_extended; Auto-generato", "symbol": "🥋", "score": "95.0", "issues": "Risky Unicode range: emoji_extended; Long fallback: 18 chars"}, "NG1407": {"action": "REPLACE_UNICODE", "reason": "Unicode problematico: emoji_extended; Auto-generato", "symbol": "😍", "score": "95.0", "issues": "Risky Unicode range: emoji_extended; Long fallback: 17 chars"}, "NG1409": {"action": "REPLACE_UNICODE", "reason": "Unicode problematico: emoji_extended; Auto-generato", "symbol": "🡖", "score": "95.0", "issues": "Risky Unicode range: emoji_extended; Long fallback: 20 chars"}, "NG1410": {"action": "REPLACE_UNICODE", "reason": "Unicode problematico: emoji_extended; Auto-generato", "symbol": "🥺", "score": "95.0", "issues": "Risky Unicode range: emoji_extended; Long fallback: 19 chars; Generic numbered name"}, "NG1411": {"action": "REPLACE_UNICODE", "reason": "Unicode problematico: emoji_extended; Auto-generato", "symbol": "🐆", "score": "95.0", "issues": "Risky Unicode range: emoji_extended; Long fallback: 19 chars; Generic numbered name"}, "NG1412": {"action": "REPLACE_UNICODE", "reason": "Unicode problematico: emoji_extended; Auto-generato", "symbol": "🠒", "score": "95.0", "issues": "Risky Unicode range: emoji_extended; Long fallback: 19 chars; Generic numbered name"}, "NG1414": {"action": "REPLACE_UNICODE", "reason": "Unicode problematico: emoji_extended; Auto-generato", "symbol": "😔", "score": "95.0", "issues": "Risky Unicode range: emoji_extended; Long fallback: 19 chars; Generic numbered name"}, "NG1420": {"action": "REPLACE_UNICODE", "reason": "Unicode problematico: emoji_extended; Auto-generato", "symbol": "🞻", "score": "95.0", "issues": "Risky Unicode range: emoji_extended; Long fallback: 16 chars; Generic numbered name"}, "NG1421": {"action": "REPLACE_UNICODE", "reason": "Unicode problematico: emoji_extended; Auto-generato", "symbol": "🗹", "score": "95.0", "issues": "Risky Unicode range: emoji_extended; Long fallback: 17 chars; Generic numbered name"}, "NG1422": {"action": "REPLACE_UNICODE", "reason": "Unicode problematico: emoji_extended; Auto-generato", "symbol": "🞹", "score": "95.0", "issues": "Risky Unicode range: emoji_extended; Long fallback: 20 chars; Generic numbered name"}, "NG1423": {"action": "REPLACE_UNICODE", "reason": "Unicode problematico: emoji_extended; Auto-generato", "symbol": "📱", "score": "95.0", "issues": "Risky Unicode range: emoji_extended; Long fallback: 20 chars; Generic numbered name"}, "NG1424": {"action": "REPLACE_UNICODE", "reason": "Unicode problematico: emoji_extended; Auto-generato", "symbol": "🍄", "score": "95.0", "issues": "Risky Unicode range: emoji_extended; Long fallback: 20 chars; Generic numbered name"}, "NG1425": {"action": "REPLACE_UNICODE", "reason": "Unicode problematico: emoji_extended; Auto-generato", "symbol": "🏗", "score": "95.0", "issues": "Risky Unicode range: emoji_extended; Long fallback: 20 chars; Generic numbered name"}, "NG1426": {"action": "REPLACE_UNICODE", "reason": "Unicode problematico: emoji_extended; Auto-generato", "symbol": "🜟", "score": "95.0", "issues": "Risky Unicode range: emoji_extended; Long fallback: 20 chars; Generic numbered name"}, "NG1428": {"action": "REPLACE_UNICODE", "reason": "Unicode problematico: emoji_extended; Auto-generato", "symbol": "🦹", "score": "95.0", "issues": "Risky Unicode range: emoji_extended; Long fallback: 20 chars; Generic numbered name"}, "NG1430": {"action": "REPLACE_UNICODE", "reason": "Unicode problematico: emoji_extended; Auto-generato", "symbol": "🕎", "score": "95.0", "issues": "Risky Unicode range: emoji_extended; Long fallback: 14 chars"}, "NG1431": {"action": "REPLACE_UNICODE", "reason": "Unicode problematico: emoji_extended; Auto-generato", "symbol": "🕭", "score": "95.0", "issues": "Risky Unicode range: emoji_extended; Long fallback: 15 chars"}, "NG1433": {"action": "REPLACE_UNICODE", "reason": "Unicode problematico: emoji_extended; Auto-generato", "symbol": "🡑", "score": "95.0", "issues": "Risky Unicode range: emoji_extended; Long fallback: 14 chars"}, "NG1435": {"action": "REPLACE_UNICODE", "reason": "Unicode problematico: emoji_extended; Auto-generato", "symbol": "🜽", "score": "95.0", "issues": "Risky Unicode range: emoji_extended; Long fallback: 15 chars"}, "NG1439": {"action": "REPLACE_UNICODE", "reason": "Unicode problematico: emoji_extended; Auto-generato", "symbol": "😃", "score": "95.0", "issues": "Risky Unicode range: emoji_extended; Long fallback: 20 chars; Generic numbered name"}, "NG1441": {"action": "REPLACE_UNICODE", "reason": "Unicode problematico: emoji_extended; Auto-generato", "symbol": "🏩", "score": "95.0", "issues": "Risky Unicode range: emoji_extended; Long fallback: 20 chars; Generic numbered name"}, "NG1444": {"action": "REPLACE_UNICODE", "reason": "Unicode problematico: emoji_extended; Auto-generato", "symbol": "🚘", "score": "95.0", "issues": "Risky Unicode range: emoji_extended; Long fallback: 19 chars"}, "NG1445": {"action": "REPLACE_UNICODE", "reason": "Unicode problematico: emoji_extended; Auto-generato", "symbol": "🚭", "score": "95.0", "issues": "Risky Unicode range: emoji_extended; Long fallback: 19 chars"}, "NG1446": {"action": "REPLACE_UNICODE", "reason": "Unicode problematico: emoji_extended; Auto-generato", "symbol": "🚳", "score": "95.0", "issues": "Risky Unicode range: emoji_extended; Long fallback: 20 chars; Generic numbered name"}, "NG1449": {"action": "REPLACE_UNICODE", "reason": "Unicode problematico: emoji_extended; Auto-generato", "symbol": "🥰", "score": "95.0", "issues": "Risky Unicode range: emoji_extended; Long fallback: 14 chars"}, "NG1453": {"action": "REPLACE_UNICODE", "reason": "Unicode problematico: emoji_extended; Auto-generato", "symbol": "🝧", "score": "95.0", "issues": "Risky Unicode range: emoji_extended; Long fallback: 18 chars"}, "NG1455": {"action": "REPLACE_UNICODE", "reason": "Unicode problematico: emoji_extended; Auto-generato", "symbol": "🜈", "score": "95.0", "issues": "Risky Unicode range: emoji_extended; Long fallback: 16 chars"}, "NG1456": {"action": "REPLACE_UNICODE", "reason": "Unicode problematico: emoji_extended; Auto-generato", "symbol": "😡", "score": "95.0", "issues": "Risky Unicode range: emoji_extended; Long fallback: 18 chars"}, "NG1458": {"action": "REPLACE_UNICODE", "reason": "Unicode problematico: emoji_extended; Auto-generato", "symbol": "🙉", "score": "95.0", "issues": "Risky Unicode range: emoji_extended; Long fallback: 16 chars"}, "NG1461": {"action": "REPLACE_UNICODE", "reason": "Unicode problematico: emoji_extended; Auto-generato", "symbol": "🤧", "score": "95.0", "issues": "Risky Unicode range: emoji_extended; Long fallback: 17 chars; Generic numbered name"}, "NG1462": {"action": "REPLACE_UNICODE", "reason": "Unicode problematico: miscellaneous_symbols; Auto-generato", "symbol": "⛷", "score": "95.0", "issues": "Risky Unicode range: miscellaneous_symbols; Long fallback: 20 chars"}, "NG1463": {"action": "REPLACE_UNICODE", "reason": "Unicode problematico: emoji_extended; Auto-generato", "symbol": "🛍", "score": "95.0", "issues": "Risky Unicode range: emoji_extended; Long fallback: 19 chars"}, "NG1466": {"action": "REPLACE_UNICODE", "reason": "Unicode problematico: emoji_extended; Auto-generato", "symbol": "😯", "score": "95.0", "issues": "Risky Unicode range: emoji_extended; Long fallback: 20 chars; Generic numbered name"}, "NG1467": {"action": "REPLACE_UNICODE", "reason": "Unicode problematico: emoji_extended; Auto-generato", "symbol": "🥁", "score": "95.0", "issues": "Risky Unicode range: emoji_extended; Long fallback: 17 chars"}, "NG1468": {"action": "REPLACE_UNICODE", "reason": "Unicode problematico: emoji_extended; Auto-generato", "symbol": "🌫", "score": "95.0", "issues": "Risky Unicode range: emoji_extended; Long fallback: 19 chars"}, "NG1470": {"action": "REPLACE_UNICODE", "reason": "Unicode problematico: emoji_extended; Auto-generato", "symbol": "🖒", "score": "95.0", "issues": "Risky Unicode range: emoji_extended; Long fallback: 16 chars"}, "NG1473": {"action": "REPLACE_UNICODE", "reason": "Unicode problematico: emoji_extended; Auto-generato", "symbol": "🠱", "score": "95.0", "issues": "Risky Unicode range: emoji_extended; Long fallback: 17 chars; Generic numbered name"}, "NG1475": {"action": "REPLACE_UNICODE", "reason": "Unicode problematico: emoji_extended; Auto-generato", "symbol": "🠑", "score": "95.0", "issues": "Risky Unicode range: emoji_extended; Long fallback: 15 chars"}, "NG1476": {"action": "REPLACE_UNICODE", "reason": "Unicode problematico: emoji_extended; Auto-generato", "symbol": "🝐", "score": "95.0", "issues": "Risky Unicode range: emoji_extended; Long fallback: 19 chars"}, "NG1477": {"action": "REPLACE_UNICODE", "reason": "Unicode problematico: emoji_extended; Auto-generato", "symbol": "🚶", "score": "95.0", "issues": "Risky Unicode range: emoji_extended; Long fallback: 20 chars; Generic numbered name"}, "NG1478": {"action": "REPLACE_UNICODE", "reason": "Unicode problematico: emoji_extended; Auto-generato", "symbol": "🙋", "score": "95.0", "issues": "Risky Unicode range: emoji_extended; Long fallback: 17 chars"}, "NG1479": {"action": "REPLACE_UNICODE", "reason": "Unicode problematico: emoji_extended; Auto-generato", "symbol": "📷", "score": "95.0", "issues": "Risky Unicode range: emoji_extended; Long fallback: 18 chars; Generic numbered name"}, "NG1481": {"action": "REPLACE_UNICODE", "reason": "Unicode problematico: emoji_extended; Auto-generato", "symbol": "🞰", "score": "95.0", "issues": "Risky Unicode range: emoji_extended; Long fallback: 18 chars; Generic numbered name"}, "NG1482": {"action": "REPLACE_UNICODE", "reason": "Unicode problematico: emoji_extended; Auto-generato", "symbol": "😿", "score": "95.0", "issues": "Risky Unicode range: emoji_extended; Long fallback: 18 chars; Generic numbered name"}, "NG1483": {"action": "REPLACE_UNICODE", "reason": "Unicode problematico: emoji_extended; Auto-generato", "symbol": "🐐", "score": "95.0", "issues": "Risky Unicode range: emoji_extended; Long fallback: 20 chars; Generic numbered name"}, "NG1485": {"action": "REPLACE_UNICODE", "reason": "Unicode problematico: emoji_extended; Auto-generato", "symbol": "🟏", "score": "95.0", "issues": "Risky Unicode range: emoji_extended; Long fallback: 19 chars"}, "NG1487": {"action": "REPLACE_UNICODE", "reason": "Unicode problematico: emoji_extended; Auto-generato", "symbol": "🦈", "score": "95.0", "issues": "Risky Unicode range: emoji_extended; Long fallback: 18 chars; Generic numbered name"}, "NG1488": {"action": "REPLACE_UNICODE", "reason": "Unicode problematico: emoji_extended; Auto-generato", "symbol": "🢂", "score": "95.0", "issues": "Risky Unicode range: emoji_extended; Long fallback: 20 chars; Generic numbered name"}, "NG1489": {"action": "REPLACE_UNICODE", "reason": "Unicode problematico: emoji_extended; Auto-generato", "symbol": "🜠", "score": "95.0", "issues": "Risky Unicode range: emoji_extended; Long fallback: 18 chars; Generic numbered name"}, "NG1493": {"action": "REPLACE_UNICODE", "reason": "Unicode problematico: emoji_extended; Auto-generato", "symbol": "🧮", "score": "95.0", "issues": "Risky Unicode range: emoji_extended; Long fallback: 20 chars; Generic numbered name"}, "NG1494": {"action": "REPLACE_UNICODE", "reason": "Unicode problematico: emoji_extended; Auto-generato", "symbol": "🛕", "score": "95.0", "issues": "Risky Unicode range: emoji_extended; Long fallback: 19 chars; Generic numbered name"}, "NG1495": {"action": "REPLACE_UNICODE", "reason": "Unicode problematico: emoji_extended; Auto-generato", "symbol": "🚱", "score": "95.0", "issues": "Risky Unicode range: emoji_extended; Long fallback: 20 chars; Generic numbered name"}, "NG1499": {"action": "REPLACE_UNICODE", "reason": "Unicode problematico: emoji_extended; Auto-generato", "symbol": "🏉", "score": "95.0", "issues": "Risky Unicode range: emoji_extended; Long fallback: 11 chars"}, "NG1501": {"action": "REPLACE_UNICODE", "reason": "Unicode problematico: emoji_extended; Auto-generato", "symbol": "🝘", "score": "95.0", "issues": "Risky Unicode range: emoji_extended; Long fallback: 11 chars"}, "NG1505": {"action": "REPLACE_UNICODE", "reason": "Unicode problematico: emoji_extended; Auto-generato", "symbol": "🧨", "score": "95.0", "issues": "Risky Unicode range: emoji_extended; Long fallback: 14 chars"}, "NG1506": {"action": "REPLACE_UNICODE", "reason": "Unicode problematico: emoji_extended; Auto-generato", "symbol": "🤼", "score": "95.0", "issues": "Risky Unicode range: emoji_extended; Long fallback: 15 chars; Generic numbered name"}, "NG1508": {"action": "REPLACE_UNICODE", "reason": "Unicode problematico: emoji_extended; Auto-generato", "symbol": "🍗", "score": "95.0", "issues": "Risky Unicode range: emoji_extended; Long fallback: 13 chars"}, "NG1511": {"action": "REPLACE_UNICODE", "reason": "Unicode problematico: emoji_extended; Auto-generato", "symbol": "🝡", "score": "95.0", "issues": "Risky Unicode range: emoji_extended; Long fallback: 17 chars"}, "NG1512": {"action": "REPLACE_UNICODE", "reason": "Unicode problematico: emoji_extended; Auto-generato", "symbol": "🛂", "score": "95.0", "issues": "Risky Unicode range: emoji_extended; Long fallback: 14 chars; Generic numbered name"}, "NG1514": {"action": "REPLACE_UNICODE", "reason": "Unicode problematico: emoji_extended; Auto-generato", "symbol": "🠪", "score": "95.0", "issues": "Risky Unicode range: emoji_extended; Long fallback: 17 chars"}, "NG1516": {"action": "REPLACE_UNICODE", "reason": "Unicode problematico: emoji_extended; Auto-generato", "symbol": "🞖", "score": "95.0", "issues": "Risky Unicode range: emoji_extended; Long fallback: 12 chars"}, "NG1519": {"action": "REPLACE_UNICODE", "reason": "Unicode problematico: emoji_extended; Auto-generato", "symbol": "🧅", "score": "95.0", "issues": "Risky Unicode range: emoji_extended; Long fallback: 12 chars; Generic numbered name"}, "NG1520": {"action": "REPLACE_UNICODE", "reason": "Unicode problematico: emoji_extended; Auto-generato", "symbol": "😀", "score": "95.0", "issues": "Risky Unicode range: emoji_extended; Long fallback: 14 chars"}, "NG1521": {"action": "REPLACE_UNICODE", "reason": "Unicode problematico: emoji_extended; Auto-generato", "symbol": "🝴", "score": "95.0", "issues": "Risky Unicode range: emoji_extended; Long fallback: 12 chars"}, "NG1523": {"action": "REPLACE_UNICODE", "reason": "Unicode problematico: emoji_extended; Auto-generato", "symbol": "🝊", "score": "95.0", "issues": "Risky Unicode range: emoji_extended; Long fallback: 14 chars"}, "NG1524": {"action": "REPLACE_UNICODE", "reason": "Unicode problematico: emoji_extended; Auto-generato", "symbol": "👏", "score": "95.0", "issues": "Risky Unicode range: emoji_extended; Long fallback: 11 chars"}, "NG1527": {"action": "REPLACE_UNICODE", "reason": "Unicode problematico: emoji_extended; Auto-generato", "symbol": "🗰", "score": "95.0", "issues": "Risky Unicode range: emoji_extended; Long fallback: 12 chars; Generic numbered name"}, "NG1529": {"action": "REPLACE_UNICODE", "reason": "Unicode problematico: emoji_extended; Auto-generato", "symbol": "🥖", "score": "95.0", "issues": "Risky Unicode range: emoji_extended; Long fallback: 11 chars"}, "NG1530": {"action": "REPLACE_UNICODE", "reason": "Unicode problematico: emoji_extended; Auto-generato", "symbol": "🟋", "score": "95.0", "issues": "Risky Unicode range: emoji_extended; Long fallback: 13 chars"}, "NG1531": {"action": "REPLACE_UNICODE", "reason": "Unicode problematico: emoji_extended; Auto-generato", "symbol": "🡭", "score": "95.0", "issues": "Risky Unicode range: emoji_extended; Long fallback: 14 chars; Generic numbered name"}, "NG1532": {"action": "REPLACE_UNICODE", "reason": "Unicode problematico: emoji_extended; Auto-generato", "symbol": "😓", "score": "95.0", "issues": "Risky Unicode range: emoji_extended; Long fallback: 11 chars"}, "NG1533": {"action": "REPLACE_UNICODE", "reason": "Unicode problematico: emoji_extended; Auto-generato", "symbol": "😞", "score": "95.0", "issues": "Risky Unicode range: emoji_extended; Long fallback: 12 chars"}, "NG1535": {"action": "REPLACE_UNICODE", "reason": "Unicode problematico: emoji_extended; Auto-generato", "symbol": "🟦", "score": "95.0", "issues": "Risky Unicode range: emoji_extended; Long fallback: 12 chars; Generic numbered name"}, "NG1538": {"action": "REPLACE_UNICODE", "reason": "Unicode problematico: emoji_extended; Auto-generato", "symbol": "🛺", "score": "95.0", "issues": "Risky Unicode range: emoji_extended; Long fallback: 13 chars"}, "NG1539": {"action": "REPLACE_UNICODE", "reason": "Unicode problematico: emoji_extended; Auto-generato", "symbol": "🡫", "score": "95.0", "issues": "Risky Unicode range: emoji_extended; Long fallback: 15 chars"}, "NG1540": {"action": "REPLACE_UNICODE", "reason": "Unicode problematico: emoji_extended; Auto-generato", "symbol": "🠡", "score": "95.0", "issues": "Risky Unicode range: emoji_extended; Long fallback: 15 chars"}, "NG1541": {"action": "REPLACE_UNICODE", "reason": "Unicode problematico: emoji_extended; Auto-generato", "symbol": "🍭", "score": "95.0", "issues": "Risky Unicode range: emoji_extended; Long fallback: 11 chars"}, "NG1544": {"action": "REPLACE_UNICODE", "reason": "Unicode problematico: emoji_extended; Auto-generato", "symbol": "🔥", "score": "95.0", "issues": "Risky Unicode range: emoji_extended; Long fallback: 17 chars"}, "NG1553": {"action": "REPLACE_UNICODE", "reason": "Unicode problematico: emoji_extended; Auto-generato", "symbol": "🜣", "score": "95.0", "issues": "Risky Unicode range: emoji_extended; Long fallback: 17 chars; Generic numbered name"}, "NG1554": {"action": "REPLACE_UNICODE", "reason": "Unicode problematico: emoji_extended; Auto-generato", "symbol": "👲", "score": "95.0", "issues": "Risky Unicode range: emoji_extended; Long fallback: 14 chars"}, "NG1557": {"action": "REPLACE_UNICODE", "reason": "Unicode problematico: emoji_extended; Auto-generato", "symbol": "🔖", "score": "95.0", "issues": "Risky Unicode range: emoji_extended; Long fallback: 17 chars"}, "NG1558": {"action": "REPLACE_UNICODE", "reason": "Unicode problematico: emoji_extended; Auto-generato", "symbol": "🝦", "score": "95.0", "issues": "Risky Unicode range: emoji_extended; Long fallback: 17 chars"}, "NG1559": {"action": "REPLACE_UNICODE", "reason": "Unicode problematico: emoji_extended; Auto-generato", "symbol": "🚔", "score": "95.0", "issues": "Risky Unicode range: emoji_extended; Long fallback: 19 chars"}, "NG1560": {"action": "REPLACE_UNICODE", "reason": "Unicode problematico: emoji_extended; Auto-generato", "symbol": "🢣", "score": "95.0", "issues": "Risky Unicode range: emoji_extended; Long fallback: 18 chars; Generic numbered name"}, "NG1563": {"action": "REPLACE_UNICODE", "reason": "Unicode problematico: emoji_extended; Auto-generato", "symbol": "🠦", "score": "95.0", "issues": "Risky Unicode range: emoji_extended; Long fallback: 20 chars; Generic numbered name"}, "NG1564": {"action": "REPLACE_UNICODE", "reason": "Unicode problematico: emoji_extended; Auto-generato", "symbol": "🧉", "score": "95.0", "issues": "Risky Unicode range: emoji_extended; Long fallback: 20 chars; Generic numbered name"}, "NG1569": {"action": "REPLACE_UNICODE", "reason": "Unicode problematico: emoji_extended; Auto-generato", "symbol": "💕", "score": "95.0", "issues": "Risky Unicode range: emoji_extended; Long fallback: 13 chars; Generic numbered name"}, "NG1570": {"action": "REPLACE_UNICODE", "reason": "Unicode problematico: emoji_extended; Auto-generato", "symbol": "🢕", "score": "95.0", "issues": "Risky Unicode range: emoji_extended; Long fallback: 17 chars; Generic numbered name"}, "NG1575": {"action": "REPLACE_UNICODE", "reason": "Unicode problematico: emoji_extended; Auto-generato", "symbol": "🕦", "score": "95.0", "issues": "Risky Unicode range: emoji_extended; Long fallback: 17 chars"}, "NG1578": {"action": "REPLACE_UNICODE", "reason": "Unicode problematico: emoji_extended; Auto-generato", "symbol": "🞺", "score": "95.0", "issues": "Risky Unicode range: emoji_extended; Long fallback: 13 chars"}, "NG1579": {"action": "REPLACE_UNICODE", "reason": "Unicode problematico: emoji_extended; Auto-generato", "symbol": "🜛", "score": "95.0", "issues": "Risky Unicode range: emoji_extended; Long fallback: 11 chars"}, "NG1581": {"action": "REPLACE_UNICODE", "reason": "Unicode problematico: emoji_extended; Auto-generato", "symbol": "🥵", "score": "95.0", "issues": "Risky Unicode range: emoji_extended; Long fallback: 12 chars"}, "NG1582": {"action": "REPLACE_UNICODE", "reason": "Unicode problematico: emoji_extended; Auto-generato", "symbol": "🖝", "score": "95.0", "issues": "Risky Unicode range: emoji_extended; Long fallback: 11 chars"}, "NG1585": {"action": "REPLACE_UNICODE", "reason": "Unicode problematico: emoji_extended; Auto-generato", "symbol": "👾", "score": "95.0", "issues": "Risky Unicode range: emoji_extended; Long fallback: 17 chars"}, "NG1588": {"action": "REPLACE_UNICODE", "reason": "Unicode problematico: emoji_extended; Auto-generato", "symbol": "😟", "score": "95.0", "issues": "Risky Unicode range: emoji_extended; Long fallback: 16 chars; Generic numbered name"}, "NG1589": {"action": "REPLACE_UNICODE", "reason": "Unicode problematico: emoji_extended; Auto-generato", "symbol": "🙏", "score": "95.0", "issues": "Risky Unicode range: emoji_extended; Long fallback: 17 chars"}, "NG1590": {"action": "REPLACE_UNICODE", "reason": "Unicode problematico: emoji_extended; Auto-generato", "symbol": "🟓", "score": "95.0", "issues": "Risky Unicode range: emoji_extended; Long fallback: 16 chars; Generic numbered name"}, "NG1591": {"action": "REPLACE_UNICODE", "reason": "Unicode problematico: emoji_extended; Auto-generato", "symbol": "🏋", "score": "95.0", "issues": "Risky Unicode range: emoji_extended; Long fallback: 17 chars; Generic numbered name"}, "NG1592": {"action": "REPLACE_UNICODE", "reason": "Unicode problematico: emoji_extended; Auto-generato", "symbol": "🥹", "score": "95.0", "issues": "Risky Unicode range: emoji_extended; Long fallback: 20 chars"}, "NG1593": {"action": "REPLACE_UNICODE", "reason": "Unicode problematico: emoji_extended; Auto-generato", "symbol": "🤦", "score": "95.0", "issues": "Risky Unicode range: emoji_extended; Long fallback: 20 chars"}, "NG1595": {"action": "REPLACE_UNICODE", "reason": "Unicode problematico: emoji_extended; Auto-generato", "symbol": "🦥", "score": "95.0", "issues": "Risky Unicode range: emoji_extended; Long fallback: 20 chars"}, "NG1596": {"action": "REPLACE_UNICODE", "reason": "Unicode problematico: emoji_extended; Auto-generato", "symbol": "🞵", "score": "95.0", "issues": "Risky Unicode range: emoji_extended; Long fallback: 18 chars"}, "NG1597": {"action": "REPLACE_UNICODE", "reason": "Unicode problematico: emoji_extended; Auto-generato", "symbol": "🝏", "score": "95.0", "issues": "Risky Unicode range: emoji_extended; Long fallback: 19 chars; Generic numbered name"}, "NG1600": {"action": "REPLACE_UNICODE", "reason": "Unicode problematico: emoji_extended; Auto-generato", "symbol": "🡒", "score": "95.0", "issues": "Risky Unicode range: emoji_extended; Long fallback: 19 chars; Generic numbered name"}, "NG1601": {"action": "REPLACE_UNICODE", "reason": "Unicode problematico: emoji_extended; Auto-generato", "symbol": "🚺", "score": "95.0", "issues": "Risky Unicode range: emoji_extended; Long fallback: 16 chars"}, "NG1605": {"action": "REPLACE_UNICODE", "reason": "Unicode problematico: emoji_extended; Auto-generato", "symbol": "🛓", "score": "95.0", "issues": "Risky Unicode range: emoji_extended; Long fallback: 20 chars"}, "NG1608": {"action": "REPLACE_UNICODE", "reason": "Unicode problematico: emoji_extended; Auto-generato", "symbol": "🥠", "score": "95.0", "issues": "Risky Unicode range: emoji_extended; Long fallback: 19 chars; Generic numbered name"}, "NG1611": {"action": "REPLACE_UNICODE", "reason": "Unicode problematico: emoji_extended; Auto-generato", "symbol": "🎷", "score": "95.0", "issues": "Risky Unicode range: emoji_extended; Long fallback: 20 chars; Generic numbered name"}, "NG1612": {"action": "REPLACE_UNICODE", "reason": "Unicode problematico: emoji_extended; Auto-generato", "symbol": "🞔", "score": "95.0", "issues": "Risky Unicode range: emoji_extended; Long fallback: 18 chars"}, "NG1613": {"action": "REPLACE_UNICODE", "reason": "Unicode problematico: emoji_extended; Auto-generato", "symbol": "🖡", "score": "95.0", "issues": "Risky Unicode range: emoji_extended; Long fallback: 20 chars"}, "NG1614": {"action": "REPLACE_UNICODE", "reason": "Unicode problematico: emoji_extended; Auto-generato", "symbol": "🧖", "score": "95.0", "issues": "Risky Unicode range: emoji_extended; Long fallback: 20 chars"}, "NG1616": {"action": "REPLACE_UNICODE", "reason": "Unicode problematico: emoji_extended; Auto-generato", "symbol": "🜡", "score": "95.0", "issues": "Risky Unicode range: emoji_extended; Long fallback: 18 chars"}, "NG1618": {"action": "REPLACE_UNICODE", "reason": "Unicode problematico: emoji_extended; Auto-generato", "symbol": "🧍", "score": "95.0", "issues": "Risky Unicode range: emoji_extended; Long fallback: 20 chars; Generic numbered name"}, "NG1619": {"action": "REPLACE_UNICODE", "reason": "Unicode problematico: emoji_extended; Auto-generato", "symbol": "🟊", "score": "95.0", "issues": "Risky Unicode range: emoji_extended; Long fallback: 20 chars"}, "NG1620": {"action": "REPLACE_UNICODE", "reason": "Unicode problematico: emoji_extended; Auto-generato", "symbol": "🢚", "score": "95.0", "issues": "Risky Unicode range: emoji_extended; Long fallback: 19 chars"}, "NG1625": {"action": "REPLACE_UNICODE", "reason": "Unicode problematico: emoji_extended; Auto-generato", "symbol": "🍙", "score": "95.0", "issues": "Risky Unicode range: emoji_extended; Long fallback: 19 chars; Generic numbered name"}, "NG1626": {"action": "REPLACE_UNICODE", "reason": "Unicode problematico: emoji_extended; Auto-generato", "symbol": "💄", "score": "95.0", "issues": "Risky Unicode range: emoji_extended; Long fallback: 20 chars; Generic numbered name"}, "NG1628": {"action": "REPLACE_UNICODE", "reason": "Unicode problematico: emoji_extended; Auto-generato", "symbol": "🚣", "score": "95.0", "issues": "Risky Unicode range: emoji_extended; Long fallback: 20 chars; Generic numbered name"}, "NG1631": {"action": "REPLACE_UNICODE", "reason": "Unicode problematico: emoji_extended; Auto-generato", "symbol": "🜀", "score": "95.0", "issues": "Risky Unicode range: emoji_extended; Long fallback: 20 chars; Generic numbered name"}, "NG1633": {"action": "REPLACE_UNICODE", "reason": "Unicode problematico: emoji_extended; Auto-generato", "symbol": "🜋", "score": "95.0", "issues": "Risky Unicode range: emoji_extended; Long fallback: 19 chars; Generic numbered name"}, "NG1634": {"action": "REPLACE_UNICODE", "reason": "Unicode problematico: emoji_extended; Auto-generato", "symbol": "🜇", "score": "95.0", "issues": "Risky Unicode range: emoji_extended; Long fallback: 20 chars; Generic numbered name"}, "NG1635": {"action": "REPLACE_UNICODE", "reason": "Unicode problematico: emoji_extended; Auto-generato", "symbol": "🞢", "score": "95.0", "issues": "Risky Unicode range: emoji_extended; Long fallback: 17 chars; Generic numbered name"}, "NG1636": {"action": "REPLACE_UNICODE", "reason": "Unicode problematico: emoji_extended; Auto-generato", "symbol": "🥒", "score": "95.0", "issues": "Risky Unicode range: emoji_extended; Long fallback: 20 chars; Generic numbered name"}, "NG1637": {"action": "REPLACE_UNICODE", "reason": "Unicode problematico: emoji_extended; Auto-generato", "symbol": "👯", "score": "95.0", "issues": "Risky Unicode range: emoji_extended; Long fallback: 17 chars; Generic numbered name"}, "NG1638": {"action": "REPLACE_UNICODE", "reason": "Unicode problematico: emoji_extended; Auto-generato", "symbol": "🡲", "score": "95.0", "issues": "Risky Unicode range: emoji_extended; Long fallback: 19 chars; Generic numbered name"}, "NG1639": {"action": "REPLACE_UNICODE", "reason": "Unicode problematico: emoji_extended; Auto-generato", "symbol": "😩", "score": "95.0", "issues": "Risky Unicode range: emoji_extended; Long fallback: 17 chars; Generic numbered name"}, "NG1641": {"action": "REPLACE_UNICODE", "reason": "Unicode problematico: emoji_extended; Auto-generato", "symbol": "🛤", "score": "95.0", "issues": "Risky Unicode range: emoji_extended; Long fallback: 19 chars; Generic numbered name"}, "NG1644": {"action": "REPLACE_UNICODE", "reason": "Unicode problematico: emoji_extended; Auto-generato", "symbol": "🐝", "score": "95.0", "issues": "Risky Unicode range: emoji_extended; Long fallback: 20 chars; Generic numbered name"}, "NG1645": {"action": "REPLACE_UNICODE", "reason": "Unicode problematico: emoji_extended; Auto-generato", "symbol": "🜝", "score": "95.0", "issues": "Risky Unicode range: emoji_extended; Long fallback: 17 chars; Generic numbered name"}, "NG1646": {"action": "REPLACE_UNICODE", "reason": "Unicode problematico: emoji_extended; Auto-generato", "symbol": "🠜", "score": "95.0", "issues": "Risky Unicode range: emoji_extended; Long fallback: 17 chars; Generic numbered name"}, "NG1648": {"action": "REPLACE_UNICODE", "reason": "Unicode problematico: emoji_extended; Auto-generato", "symbol": "🟨", "score": "95.0", "issues": "Risky Unicode range: emoji_extended; Long fallback: 18 chars; Generic numbered name"}, "NG1649": {"action": "REPLACE_UNICODE", "reason": "Unicode problematico: emoji_extended; Auto-generato", "symbol": "📬", "score": "95.0", "issues": "Risky Unicode range: emoji_extended; Long fallback: 20 chars; Generic numbered name"}, "NG1650": {"action": "REPLACE_UNICODE", "reason": "Unicode problematico: emoji_extended; Auto-generato", "symbol": "🖲", "score": "95.0", "issues": "Risky Unicode range: emoji_extended; Long fallback: 20 chars; Generic numbered name"}, "NG1654": {"action": "REPLACE_UNICODE", "reason": "Unicode problematico: emoji_extended; Auto-generato", "symbol": "🠻", "score": "95.0", "issues": "Risky Unicode range: emoji_extended; Long fallback: 18 chars; Generic numbered name"}, "NG1655": {"action": "REPLACE_UNICODE", "reason": "Unicode problematico: emoji_extended; Auto-generato", "symbol": "💴", "score": "95.0", "issues": "Risky Unicode range: emoji_extended; Long fallback: 14 chars"}, "NG1662": {"action": "REPLACE_UNICODE", "reason": "Unicode problematico: emoji_extended; Auto-generato", "symbol": "🗱", "score": "95.0", "issues": "Risky Unicode range: emoji_extended; Long fallback: 17 chars; Generic numbered name"}, "NG1664": {"action": "REPLACE_UNICODE", "reason": "Unicode problematico: emoji_extended; Auto-generato", "symbol": "🢅", "score": "95.0", "issues": "Risky Unicode range: emoji_extended; Long fallback: 12 chars"}, "NG1665": {"action": "REPLACE_UNICODE", "reason": "Unicode problematico: emoji_extended; Auto-generato", "symbol": "🛃", "score": "95.0", "issues": "Risky Unicode range: emoji_extended; Long fallback: 14 chars"}, "NG1669": {"action": "REPLACE_UNICODE", "reason": "Unicode problematico: emoji_extended; Auto-generato", "symbol": "🏍", "score": "95.0", "issues": "Risky Unicode range: emoji_extended; Long fallback: 14 chars"}, "NG1670": {"action": "REPLACE_UNICODE", "reason": "Unicode problematico: emoji_extended; Auto-generato", "symbol": "🏳", "score": "95.0", "issues": "Risky Unicode range: emoji_extended; Long fallback: 13 chars; Generic numbered name"}, "NG1673": {"action": "REPLACE_UNICODE", "reason": "Unicode problematico: emoji_extended; Auto-generato", "symbol": "😻", "score": "95.0", "issues": "Risky Unicode range: emoji_extended; Long fallback: 15 chars; Generic numbered name"}, "NG1676": {"action": "REPLACE_UNICODE", "reason": "Unicode problematico: emoji_extended; Auto-generato", "symbol": "🞑", "score": "95.0", "issues": "Risky Unicode range: emoji_extended; Long fallback: 15 chars; Generic numbered name"}, "NG1677": {"action": "REPLACE_UNICODE", "reason": "Unicode problematico: emoji_extended; Auto-generato", "symbol": "🚤", "score": "95.0", "issues": "Risky Unicode range: emoji_extended; Long fallback: 18 chars"}, "NG1678": {"action": "REPLACE_UNICODE", "reason": "Unicode problematico: emoji_extended; Auto-generato", "symbol": "🧾", "score": "95.0", "issues": "Risky Unicode range: emoji_extended; Long fallback: 18 chars"}, "NG1679": {"action": "REPLACE_UNICODE", "reason": "Unicode problematico: emoji_extended; Auto-generato", "symbol": "😑", "score": "95.0", "issues": "Risky Unicode range: emoji_extended; Long fallback: 18 chars; Generic numbered name"}, "NG1680": {"action": "REPLACE_UNICODE", "reason": "Unicode problematico: emoji_extended; Auto-generato", "symbol": "🟃", "score": "95.0", "issues": "Risky Unicode range: emoji_extended; Long fallback: 19 chars; Generic numbered name"}, "NG1683": {"action": "REPLACE_UNICODE", "reason": "Unicode problematico: emoji_extended; Auto-generato", "symbol": "🥇", "score": "95.0", "issues": "Risky Unicode range: emoji_extended; Long fallback: 19 chars; Generic numbered name"}, "NG1685": {"action": "REPLACE_UNICODE", "reason": "Unicode problematico: emoji_extended; Auto-generato", "symbol": "🚸", "score": "95.0", "issues": "Risky Unicode range: emoji_extended; Long fallback: 19 chars; Generic numbered name"}, "NG1688": {"action": "REPLACE_UNICODE", "reason": "Unicode problematico: emoji_extended; Auto-generato", "symbol": "😹", "score": "95.0", "issues": "Risky Unicode range: emoji_extended; Long fallback: 17 chars"}, "NG1690": {"action": "REPLACE_UNICODE", "reason": "Unicode problematico: emoji_extended; Auto-generato", "symbol": "🝢", "score": "95.0", "issues": "Risky Unicode range: emoji_extended; Long fallback: 19 chars"}, "NG1692": {"action": "REPLACE_UNICODE", "reason": "Unicode problematico: emoji_extended; Auto-generato", "symbol": "🠖", "score": "95.0", "issues": "Risky Unicode range: emoji_extended; Long fallback: 18 chars; Generic numbered name"}, "NG1693": {"action": "REPLACE_UNICODE", "reason": "Unicode problematico: emoji_extended; Auto-generato", "symbol": "🤪", "score": "95.0", "issues": "Risky Unicode range: emoji_extended; Long fallback: 20 chars; Generic numbered name"}, "NG1697": {"action": "REPLACE_UNICODE", "reason": "Unicode problematico: emoji_extended; Auto-generato", "symbol": "🝑", "score": "95.0", "issues": "Risky Unicode range: emoji_extended; Long fallback: 20 chars; Generic numbered name"}, "NG1699": {"action": "REPLACE_UNICODE", "reason": "Unicode problematico: emoji_extended; Auto-generato", "symbol": "🜌", "score": "95.0", "issues": "Risky Unicode range: emoji_extended; Long fallback: 18 chars; Generic numbered name"}, "NG1700": {"action": "REPLACE_UNICODE", "reason": "Unicode problematico: emoji_extended; Auto-generato", "symbol": "🞮", "score": "95.0", "issues": "Risky Unicode range: emoji_extended; Long fallback: 18 chars; Generic numbered name"}, "NG1702": {"action": "REPLACE_UNICODE", "reason": "Unicode problematico: emoji_extended; Auto-generato", "symbol": "🖛", "score": "95.0", "issues": "Risky Unicode range: emoji_extended; Long fallback: 20 chars; Generic numbered name"}, "NG1703": {"action": "REPLACE_UNICODE", "reason": "Unicode problematico: emoji_extended; Auto-generato", "symbol": "🢗", "score": "95.0", "issues": "Risky Unicode range: emoji_extended; Long fallback: 20 chars"}, "NG1705": {"action": "REPLACE_UNICODE", "reason": "Unicode problematico: emoji_extended; Auto-generato", "symbol": "🛞", "score": "95.0", "issues": "Risky Unicode range: emoji_extended; Long fallback: 20 chars; Generic numbered name"}, "NG1708": {"action": "REPLACE_UNICODE", "reason": "Unicode problematico: emoji_extended; Auto-generato", "symbol": "🍖", "score": "95.0", "issues": "Risky Unicode range: emoji_extended; Long fallback: 20 chars; Generic numbered name"}, "NG1709": {"action": "REPLACE_UNICODE", "reason": "Unicode problematico: emoji_extended; Auto-generato", "symbol": "🠚", "score": "95.0", "issues": "Risky Unicode range: emoji_extended; Long fallback: 18 chars; Generic numbered name"}, "NG1710": {"action": "REPLACE_UNICODE", "reason": "Unicode problematico: emoji_extended; Auto-generato", "symbol": "😦", "score": "95.0", "issues": "Risky Unicode range: emoji_extended; Long fallback: 20 chars; Generic numbered name"}, "NG1711": {"action": "REPLACE_UNICODE", "reason": "Unicode problematico: emoji_extended; Auto-generato", "symbol": "📮", "score": "95.0", "issues": "Risky Unicode range: emoji_extended; Long fallback: 20 chars"}, "NG1713": {"action": "REPLACE_UNICODE", "reason": "Unicode problematico: emoji_extended; Auto-generato", "symbol": "🠶", "score": "95.0", "issues": "Risky Unicode range: emoji_extended; Long fallback: 20 chars; Generic numbered name"}, "NG1715": {"action": "REPLACE_UNICODE", "reason": "Unicode problematico: emoji_extended; Auto-generato", "symbol": "🛊", "score": "95.0", "issues": "Risky Unicode range: emoji_extended; Long fallback: 20 chars; Generic numbered name"}, "NG1718": {"action": "REPLACE_UNICODE", "reason": "Unicode problematico: emoji_extended; Auto-generato", "symbol": "🏥", "score": "95.0", "issues": "Risky Unicode range: emoji_extended; Long fallback: 20 chars; Generic numbered name"}, "NG1720": {"action": "REPLACE_UNICODE", "reason": "Unicode problematico: emoji_extended; Auto-generato", "symbol": "🜎", "score": "95.0", "issues": "Risky Unicode range: emoji_extended; Long fallback: 20 chars; Generic numbered name"}, "NG1723": {"action": "REPLACE_UNICODE", "reason": "Unicode problematico: emoji_extended; Auto-generato", "symbol": "🝌", "score": "95.0", "issues": "Risky Unicode range: emoji_extended; Long fallback: 18 chars; Generic numbered name"}, "NG1725": {"action": "REPLACE_UNICODE", "reason": "Unicode problematico: emoji_extended; Auto-generato", "symbol": "🝛", "score": "95.0", "issues": "Risky Unicode range: emoji_extended; Long fallback: 20 chars; Generic numbered name"}, "NG1726": {"action": "REPLACE_UNICODE", "reason": "Unicode problematico: emoji_extended; Auto-generato", "symbol": "🝖", "score": "95.0", "issues": "Risky Unicode range: emoji_extended; Long fallback: 20 chars; Generic numbered name"}, "NG1729": {"action": "REPLACE_UNICODE", "reason": "Unicode problematico: emoji_extended; Auto-generato", "symbol": "🤚", "score": "95.0", "issues": "Risky Unicode range: emoji_extended; Long fallback: 20 chars; Generic numbered name"}, "NG1731": {"action": "REPLACE_UNICODE", "reason": "Unicode problematico: emoji_extended; Auto-generato", "symbol": "🔻", "score": "95.0", "issues": "Risky Unicode range: emoji_extended; Long fallback: 18 chars; Generic numbered name"}, "NG1732": {"action": "REPLACE_UNICODE", "reason": "Unicode problematico: emoji_extended; Auto-generato", "symbol": "🍠", "score": "95.0", "issues": "Risky Unicode range: emoji_extended; Long fallback: 20 chars; Generic numbered name"}, "NG1733": {"action": "REPLACE_UNICODE", "reason": "Unicode problematico: emoji_extended; Auto-generato", "symbol": "🤌", "score": "95.0", "issues": "Risky Unicode range: emoji_extended; Long fallback: 18 chars; Generic numbered name"}, "NG1734": {"action": "REPLACE_UNICODE", "reason": "Unicode problematico: emoji_extended; Auto-generato", "symbol": "🝤", "score": "95.0", "issues": "Risky Unicode range: emoji_extended; Long fallback: 18 chars; Generic numbered name"}, "NG1735": {"action": "REPLACE_UNICODE", "reason": "Unicode problematico: emoji_extended; Auto-generato", "symbol": "🠧", "score": "95.0", "issues": "Risky Unicode range: emoji_extended; Long fallback: 19 chars; Generic numbered name"}, "NG1736": {"action": "REPLACE_UNICODE", "reason": "Unicode problematico: emoji_extended; Auto-generato", "symbol": "🎵", "score": "95.0", "issues": "Risky Unicode range: emoji_extended; Long fallback: 19 chars; Generic numbered name"}, "NG1738": {"action": "REPLACE_UNICODE", "reason": "Unicode problematico: emoji_extended; Auto-generato", "symbol": "🜖", "score": "95.0", "issues": "Risky Unicode range: emoji_extended; Long fallback: 19 chars; Generic numbered name"}, "NG1739": {"action": "REPLACE_UNICODE", "reason": "Unicode problematico: miscellaneous_symbols; Auto-generato", "symbol": "⛩", "score": "95.0", "issues": "Risky Unicode range: miscellaneous_symbols; Long fallback: 19 chars; Generic numbered name"}, "NG1741": {"action": "REPLACE_UNICODE", "reason": "Unicode problematico: emoji_extended; Auto-generato", "symbol": "🐊", "score": "95.0", "issues": "Risky Unicode range: emoji_extended; Long fallback: 19 chars; Generic numbered name"}, "NG1742": {"action": "REPLACE_UNICODE", "reason": "Unicode problematico: emoji_extended; Auto-generato", "symbol": "🤀", "score": "95.0", "issues": "Risky Unicode range: emoji_extended; Long fallback: 19 chars; Generic numbered name"}, "NG1743": {"action": "REPLACE_UNICODE", "reason": "Unicode problematico: emoji_extended; Auto-generato", "symbol": "🟑", "score": "95.0", "issues": "Risky Unicode range: emoji_extended; Long fallback: 19 chars; Generic numbered name"}, "NG1744": {"action": "REPLACE_UNICODE", "reason": "Unicode problematico: emoji_extended; Auto-generato", "symbol": "🤺", "score": "95.0", "issues": "Risky Unicode range: emoji_extended; Long fallback: 19 chars; Generic numbered name"}, "NG1745": {"action": "REPLACE_UNICODE", "reason": "Unicode problematico: emoji_extended; Auto-generato", "symbol": "🚉", "score": "95.0", "issues": "Risky Unicode range: emoji_extended; Long fallback: 19 chars; Generic numbered name"}, "NG1747": {"action": "REPLACE_UNICODE", "reason": "Unicode problematico: emoji_extended; Auto-generato", "symbol": "🜾", "score": "95.0", "issues": "Risky Unicode range: emoji_extended; Long fallback: 19 chars; Generic numbered name"}, "NG1748": {"action": "REPLACE_UNICODE", "reason": "Unicode problematico: emoji_extended; Auto-generato", "symbol": "🞟", "score": "95.0", "issues": "Risky Unicode range: emoji_extended; Long fallback: 19 chars; Generic numbered name"}, "NG1750": {"action": "REPLACE_UNICODE", "reason": "Unicode problematico: emoji_extended; Auto-generato", "symbol": "🛎", "score": "95.0", "issues": "Risky Unicode range: emoji_extended; Long fallback: 19 chars; Generic numbered name"}, "NG1751": {"action": "REPLACE_UNICODE", "reason": "Unicode problematico: emoji_extended; Auto-generato", "symbol": "😖", "score": "95.0", "issues": "Risky Unicode range: emoji_extended; Long fallback: 19 chars; Generic numbered name"}, "NG1752": {"action": "REPLACE_UNICODE", "reason": "Unicode problematico: emoji_extended; Auto-generato", "symbol": "🐚", "score": "95.0", "issues": "Risky Unicode range: emoji_extended; Long fallback: 19 chars; Generic numbered name"}, "NG1753": {"action": "REPLACE_UNICODE", "reason": "Unicode problematico: miscellaneous_symbols; Auto-generato", "symbol": "⛮", "score": "95.0", "issues": "Risky Unicode range: miscellaneous_symbols; Long fallback: 19 chars; Generic numbered name"}, "NG1754": {"action": "REPLACE_UNICODE", "reason": "Unicode problematico: emoji_extended; Auto-generato", "symbol": "💠", "score": "95.0", "issues": "Risky Unicode range: emoji_extended; Long fallback: 19 chars; Generic numbered name"}, "NG1755": {"action": "REPLACE_UNICODE", "reason": "Unicode problematico: emoji_extended; Auto-generato", "symbol": "🖅", "score": "95.0", "issues": "Risky Unicode range: emoji_extended; Long fallback: 19 chars; Generic numbered name"}, "NG1756": {"action": "REPLACE_UNICODE", "reason": "Unicode problematico: emoji_extended; Auto-generato", "symbol": "🥚", "score": "95.0", "issues": "Risky Unicode range: emoji_extended; Long fallback: 19 chars; Generic numbered name"}, "NG1757": {"action": "REPLACE_UNICODE", "reason": "Unicode problematico: emoji_extended; Auto-generato", "symbol": "😂", "score": "95.0", "issues": "Risky Unicode range: emoji_extended; Long fallback: 19 chars; Generic numbered name"}, "NG1758": {"action": "REPLACE_UNICODE", "reason": "Unicode problematico: emoji_extended; Auto-generato", "symbol": "🌯", "score": "95.0", "issues": "Risky Unicode range: emoji_extended; Long fallback: 19 chars; Generic numbered name"}, "NG1759": {"action": "REPLACE_UNICODE", "reason": "Unicode problematico: emoji_extended; Auto-generato", "symbol": "🡦", "score": "95.0", "issues": "Risky Unicode range: emoji_extended; Long fallback: 19 chars; Generic numbered name"}, "NG1760": {"action": "REPLACE_UNICODE", "reason": "Unicode problematico: emoji_extended; Auto-generato", "symbol": "🞽", "score": "95.0", "issues": "Risky Unicode range: emoji_extended; Long fallback: 19 chars; Generic numbered name"}, "NG1761": {"action": "REPLACE_UNICODE", "reason": "Unicode problematico: emoji_extended; Auto-generato", "symbol": "🢟", "score": "95.0", "issues": "Risky Unicode range: emoji_extended; Long fallback: 19 chars; Generic numbered name"}, "NG1763": {"action": "REPLACE_UNICODE", "reason": "Unicode problematico: emoji_extended; Auto-generato", "symbol": "🢜", "score": "95.0", "issues": "Risky Unicode range: emoji_extended; Long fallback: 19 chars; Generic numbered name"}, "NG1765": {"action": "REPLACE_UNICODE", "reason": "Unicode problematico: emoji_extended; Auto-generato", "symbol": "🧻", "score": "95.0", "issues": "Risky Unicode range: emoji_extended; Long fallback: 19 chars; Generic numbered name"}, "NG1766": {"action": "REPLACE_UNICODE", "reason": "Unicode problematico: emoji_extended; Auto-generato", "symbol": "🦶", "score": "95.0", "issues": "Risky Unicode range: emoji_extended; Long fallback: 19 chars; Generic numbered name"}, "NG1772": {"action": "REPLACE_UNICODE", "reason": "Unicode problematico: emoji_extended; Auto-generato", "symbol": "🧷", "score": "95.0", "issues": "Risky Unicode range: emoji_extended; Long fallback: 19 chars; Generic numbered name"}, "NG1774": {"action": "REPLACE_UNICODE", "reason": "Unicode problematico: emoji_extended; Auto-generato", "symbol": "🏐", "score": "95.0", "issues": "Risky Unicode range: emoji_extended; Long fallback: 19 chars; Generic numbered name"}, "NG1780": {"action": "REPLACE_UNICODE", "reason": "Unicode problematico: emoji_extended; Auto-generato", "symbol": "🢐", "score": "95.0", "issues": "Risky Unicode range: emoji_extended; Long fallback: 11 chars"}, "NG1781": {"action": "REPLACE_UNICODE", "reason": "Unicode problematico: emoji_extended; Auto-generato", "symbol": "😽", "score": "95.0", "issues": "Risky Unicode range: emoji_extended; Long fallback: 13 chars"}, "NG1786": {"action": "REPLACE_UNICODE", "reason": "Unicode problematico: emoji_extended; Auto-generato", "symbol": "👈", "score": "95.0", "issues": "Risky Unicode range: emoji_extended; Long fallback: 11 chars"}, "NG1787": {"action": "REPLACE_UNICODE", "reason": "Unicode problematico: emoji_extended; Auto-generato", "symbol": "🜷", "score": "95.0", "issues": "Risky Unicode range: emoji_extended; Long fallback: 12 chars; Generic numbered name"}, "NG1788": {"action": "REPLACE_UNICODE", "reason": "Unicode problematico: emoji_extended; Auto-generato", "symbol": "🧈", "score": "95.0", "issues": "Risky Unicode range: emoji_extended; Long fallback: 19 chars"}, "NG1794": {"action": "REPLACE_UNICODE", "reason": "Unicode problematico: emoji_extended; Auto-generato", "symbol": "🠩", "score": "95.0", "issues": "Risky Unicode range: emoji_extended; Long fallback: 17 chars"}, "NG1795": {"action": "REPLACE_UNICODE", "reason": "Unicode problematico: emoji_extended; Auto-generato", "symbol": "🞨", "score": "95.0", "issues": "Risky Unicode range: emoji_extended; Long fallback: 17 chars"}, "NG1796": {"action": "REPLACE_UNICODE", "reason": "Unicode problematico: emoji_extended; Auto-generato", "symbol": "🐦", "score": "95.0", "issues": "Risky Unicode range: emoji_extended; Long fallback: 18 chars; Generic numbered name"}, "NG1797": {"action": "REPLACE_UNICODE", "reason": "Unicode problematico: emoji_extended; Auto-generato", "symbol": "🡩", "score": "95.0", "issues": "Risky Unicode range: emoji_extended; Long fallback: 19 chars"}, "NG1798": {"action": "REPLACE_UNICODE", "reason": "Unicode problematico: emoji_extended; Auto-generato", "symbol": "🡸", "score": "95.0", "issues": "Risky Unicode range: emoji_extended; Long fallback: 19 chars"}, "NG1799": {"action": "REPLACE_UNICODE", "reason": "Unicode problematico: emoji_extended; Auto-generato", "symbol": "😥", "score": "95.0", "issues": "Risky Unicode range: emoji_extended; Long fallback: 15 chars"}, "NG1800": {"action": "REPLACE_UNICODE", "reason": "Unicode problematico: emoji_extended; Auto-generato", "symbol": "😫", "score": "95.0", "issues": "Risky Unicode range: emoji_extended; Long fallback: 18 chars; Generic numbered name"}, "NG1803": {"action": "REPLACE_UNICODE", "reason": "Unicode problematico: emoji_extended; Auto-generato", "symbol": "🠥", "score": "95.0", "issues": "Risky Unicode range: emoji_extended; Long fallback: 18 chars"}, "NG1808": {"action": "REPLACE_UNICODE", "reason": "Unicode problematico: miscellaneous_symbols; Auto-generato", "symbol": "⛌", "score": "95.0", "issues": "Risky Unicode range: miscellaneous_symbols; Long fallback: 18 chars"}, "NG1809": {"action": "REPLACE_UNICODE", "reason": "Unicode problematico: emoji_extended; Auto-generato", "symbol": "🚲", "score": "95.0", "issues": "Risky Unicode range: emoji_extended; Long fallback: 19 chars; Generic numbered name"}, "NG1810": {"action": "REPLACE_UNICODE", "reason": "Unicode problematico: emoji_extended; Auto-generato", "symbol": "🠉", "score": "95.0", "issues": "Risky Unicode range: emoji_extended; Long fallback: 18 chars"}, "NG1813": {"action": "REPLACE_UNICODE", "reason": "Unicode problematico: emoji_extended; Auto-generato", "symbol": "🚷", "score": "95.0", "issues": "Risky Unicode range: emoji_extended; Long fallback: 15 chars"}, "NG1818": {"action": "REPLACE_UNICODE", "reason": "Unicode problematico: emoji_extended; Auto-generato", "symbol": "👩", "score": "95.0", "issues": "Risky Unicode range: emoji_extended; Long fallback: 19 chars"}, "NG1820": {"action": "REPLACE_UNICODE", "reason": "Unicode problematico: emoji_extended; Auto-generato", "symbol": "🞤", "score": "95.0", "issues": "Risky Unicode range: emoji_extended; Long fallback: 19 chars"}, "NG1823": {"action": "REPLACE_UNICODE", "reason": "Unicode problematico: emoji_extended; Auto-generato", "symbol": "🧙", "score": "95.0", "issues": "Risky Unicode range: emoji_extended; Long fallback: 20 chars; Generic numbered name"}, "NG1824": {"action": "REPLACE_UNICODE", "reason": "Unicode problematico: emoji_extended; Auto-generato", "symbol": "🛴", "score": "95.0", "issues": "Risky Unicode range: emoji_extended; Long fallback: 18 chars; Generic numbered name"}, "NG1825": {"action": "REPLACE_UNICODE", "reason": "Unicode problematico: emoji_extended; Auto-generato", "symbol": "🡤", "score": "95.0", "issues": "Risky Unicode range: emoji_extended; Long fallback: 20 chars; Generic numbered name"}, "NG1826": {"action": "REPLACE_UNICODE", "reason": "Unicode problematico: emoji_extended; Auto-generato", "symbol": "🗶", "score": "95.0", "issues": "Risky Unicode range: emoji_extended; Long fallback: 20 chars; Generic numbered name"}, "NG1828": {"action": "REPLACE_UNICODE", "reason": "Unicode problematico: emoji_extended; Auto-generato", "symbol": "🟗", "score": "95.0", "issues": "Risky Unicode range: emoji_extended; Long fallback: 19 chars"}, "NG1829": {"action": "REPLACE_UNICODE", "reason": "Unicode problematico: emoji_extended; Auto-generato", "symbol": "🥪", "score": "95.0", "issues": "Risky Unicode range: emoji_extended; Long fallback: 18 chars; Generic numbered name"}, "NG1833": {"action": "REPLACE_UNICODE", "reason": "Unicode problematico: emoji_extended; Auto-generato", "symbol": "🚴", "score": "95.0", "issues": "Risky Unicode range: emoji_extended; Long fallback: 18 chars; Generic numbered name"}, "NG1835": {"action": "REPLACE_UNICODE", "reason": "Unicode problematico: emoji_extended; Auto-generato", "symbol": "🝿", "score": "95.0", "issues": "Risky Unicode range: emoji_extended; Long fallback: 18 chars; Generic numbered name"}, "NG1836": {"action": "REPLACE_UNICODE", "reason": "Unicode problematico: emoji_extended; Auto-generato", "symbol": "🜵", "score": "95.0", "issues": "Risky Unicode range: emoji_extended; Long fallback: 20 chars; Generic numbered name"}, "NG1837": {"action": "REPLACE_UNICODE", "reason": "Unicode problematico: emoji_extended; Auto-generato", "symbol": "🥦", "score": "95.0", "issues": "Risky Unicode range: emoji_extended; Long fallback: 18 chars; Generic numbered name"}, "NG1840": {"action": "REPLACE_UNICODE", "reason": "Unicode problematico: emoji_extended; Auto-generato", "symbol": "🦩", "score": "95.0", "issues": "Risky Unicode range: emoji_extended; Long fallback: 20 chars; Generic numbered name"}, "NG1845": {"action": "REPLACE_UNICODE", "reason": "Unicode problematico: emoji_extended; Auto-generato", "symbol": "😪", "score": "95.0", "issues": "Risky Unicode range: emoji_extended; Long fallback: 16 chars; Generic numbered name"}, "NG1848": {"action": "REPLACE_UNICODE", "reason": "Unicode problematico: emoji_extended; Auto-generato", "symbol": "🞓", "score": "95.0", "issues": "Risky Unicode range: emoji_extended; Long fallback: 15 chars"}, "NG1851": {"action": "REPLACE_UNICODE", "reason": "Unicode problematico: emoji_extended; Auto-generato", "symbol": "🎊", "score": "95.0", "issues": "Risky Unicode range: emoji_extended; Long fallback: 11 chars"}, "NG1852": {"action": "REPLACE_UNICODE", "reason": "Unicode problematico: emoji_extended; Auto-generato", "symbol": "🜪", "score": "95.0", "issues": "Risky Unicode range: emoji_extended; Long fallback: 12 chars"}, "NG1853": {"action": "REPLACE_UNICODE", "reason": "Unicode problematico: emoji_extended; Auto-generato", "symbol": "🤵", "score": "95.0", "issues": "Risky Unicode range: emoji_extended; Long fallback: 13 chars"}, "NG1854": {"action": "REPLACE_UNICODE", "reason": "Unicode problematico: emoji_extended; Auto-generato", "symbol": "🔕", "score": "95.0", "issues": "Risky Unicode range: emoji_extended; Long fallback: 14 chars; Generic numbered name"}, "NG1855": {"action": "REPLACE_UNICODE", "reason": "Unicode problematico: emoji_extended; Auto-generato", "symbol": "🡄", "score": "95.0", "issues": "Risky Unicode range: emoji_extended; Long fallback: 13 chars; Generic numbered name"}, "NG1857": {"action": "REPLACE_UNICODE", "reason": "Unicode problematico: emoji_extended; Auto-generato", "symbol": "🔊", "score": "95.0", "issues": "Risky Unicode range: emoji_extended; Long fallback: 13 chars; Generic numbered name"}, "NG1858": {"action": "REPLACE_UNICODE", "reason": "Unicode problematico: emoji_extended; Auto-generato", "symbol": "💔", "score": "95.0", "issues": "Risky Unicode range: emoji_extended; Long fallback: 14 chars"}, "NG1859": {"action": "REPLACE_UNICODE", "reason": "Unicode problematico: emoji_extended; Auto-generato", "symbol": "🞗", "score": "95.0", "issues": "Risky Unicode range: emoji_extended; Long fallback: 13 chars; Generic numbered name"}, "NG1860": {"action": "REPLACE_UNICODE", "reason": "Unicode problematico: emoji_extended; Auto-generato", "symbol": "🡇", "score": "95.0", "issues": "Risky Unicode range: emoji_extended; Long fallback: 13 chars; Generic numbered name"}, "NG1862": {"action": "REPLACE_UNICODE", "reason": "Unicode problematico: emoji_extended; Auto-generato", "symbol": "🐤", "score": "95.0", "issues": "Risky Unicode range: emoji_extended; Long fallback: 19 chars"}, "NG1863": {"action": "REPLACE_UNICODE", "reason": "Unicode problematico: emoji_extended; Auto-generato", "symbol": "🞣", "score": "95.0", "issues": "Risky Unicode range: emoji_extended; Long fallback: 17 chars"}, "NG1864": {"action": "REPLACE_UNICODE", "reason": "Unicode problematico: emoji_extended; Auto-generato", "symbol": "📻", "score": "95.0", "issues": "Risky Unicode range: emoji_extended; Long fallback: 19 chars"}, "NG1867": {"action": "REPLACE_UNICODE", "reason": "Unicode problematico: emoji_extended; Auto-generato", "symbol": "🥷", "score": "95.0", "issues": "Risky Unicode range: emoji_extended; Long fallback: 20 chars; Generic numbered name"}, "NG1871": {"action": "REPLACE_UNICODE", "reason": "Unicode problematico: emoji_extended; Auto-generato", "symbol": "💽", "score": "95.0", "issues": "Risky Unicode range: emoji_extended; Long fallback: 20 chars; Generic numbered name"}, "NG1872": {"action": "REPLACE_UNICODE", "reason": "Unicode problematico: emoji_extended; Auto-generato", "symbol": "🍊", "score": "95.0", "issues": "Risky Unicode range: emoji_extended; Long fallback: 20 chars"}, "NG1874": {"action": "REPLACE_UNICODE", "reason": "Unicode problematico: emoji_extended; Auto-generato", "symbol": "🤂", "score": "95.0", "issues": "Risky Unicode range: emoji_extended; Long fallback: 19 chars; Generic numbered name"}, "NG1875": {"action": "REPLACE_UNICODE", "reason": "Unicode problematico: emoji_extended; Auto-generato", "symbol": "😐", "score": "95.0", "issues": "Risky Unicode range: emoji_extended; Long fallback: 20 chars"}, "NG1876": {"action": "REPLACE_UNICODE", "reason": "Unicode problematico: emoji_extended; Auto-generato", "symbol": "🖜", "score": "95.0", "issues": "Risky Unicode range: emoji_extended; Long fallback: 19 chars; Generic numbered name"}, "NG1880": {"action": "REPLACE_UNICODE", "reason": "Unicode problematico: emoji_extended; Auto-generato", "symbol": "🤻", "score": "95.0", "issues": "Risky Unicode range: emoji_extended; Long fallback: 11 chars; Generic numbered name"}, "NG1881": {"action": "REPLACE_UNICODE", "reason": "Unicode problematico: emoji_extended; Auto-generato", "symbol": "🤽", "score": "95.0", "issues": "Risky Unicode range: emoji_extended; Long fallback: 12 chars"}, "NG1882": {"action": "REPLACE_UNICODE", "reason": "Unicode problematico: emoji_extended; Auto-generato", "symbol": "🜑", "score": "95.0", "issues": "Risky Unicode range: emoji_extended; Long fallback: 12 chars"}, "NG1884": {"action": "REPLACE_UNICODE", "reason": "Unicode problematico: emoji_extended; Auto-generato", "symbol": "🧰", "score": "95.0", "issues": "Risky Unicode range: emoji_extended; Generic numbered name"}, "NG1885": {"action": "REPLACE_UNICODE", "reason": "Unicode problematico: emoji_extended; Auto-generato", "symbol": "🤡", "score": "95.0", "issues": "Risky Unicode range: emoji_extended; Long fallback: 13 chars; Generic numbered name"}, "NG1888": {"action": "REPLACE_UNICODE", "reason": "Unicode problematico: emoji_extended; Auto-generato", "symbol": "🥔", "score": "95.0", "issues": "Risky Unicode range: emoji_extended; Generic numbered name"}, "NG1892": {"action": "REPLACE_UNICODE", "reason": "Unicode problematico: emoji_extended; Auto-generato", "symbol": "🧇", "score": "95.0", "issues": "Risky Unicode range: emoji_extended; Long fallback: 18 chars; Generic numbered name"}, "NG1896": {"action": "REPLACE_UNICODE", "reason": "Unicode problematico: emoji_extended; Auto-generato", "symbol": "🦚", "score": "95.0", "issues": "Risky Unicode range: emoji_extended; Long fallback: 18 chars; Generic numbered name"}, "NG1897": {"action": "REPLACE_UNICODE", "reason": "Unicode problematico: emoji_extended; Auto-generato", "symbol": "💩", "score": "95.0", "issues": "Risky Unicode range: emoji_extended; Long fallback: 18 chars; Generic numbered name"}, "NG1898": {"action": "REPLACE_UNICODE", "reason": "Unicode problematico: emoji_extended; Auto-generato", "symbol": "🞩", "score": "95.0", "issues": "Risky Unicode range: emoji_extended; Long fallback: 17 chars"}, "NG1899": {"action": "REPLACE_UNICODE", "reason": "Unicode problematico: emoji_extended; Auto-generato", "symbol": "🚞", "score": "95.0", "issues": "Risky Unicode range: emoji_extended; Long fallback: 14 chars; Generic numbered name"}, "NG1900": {"action": "REPLACE_UNICODE", "reason": "Unicode problematico: emoji_extended; Auto-generato", "symbol": "🕯", "score": "95.0", "issues": "Risky Unicode range: emoji_extended; Long fallback: 16 chars; Generic numbered name"}, "NG1901": {"action": "REPLACE_UNICODE", "reason": "Unicode problematico: emoji_extended; Auto-generato", "symbol": "🝾", "score": "95.0", "issues": "Risky Unicode range: emoji_extended; Long fallback: 18 chars; Generic numbered name"}, "NG1902": {"action": "REPLACE_UNICODE", "reason": "Unicode problematico: emoji_extended; Auto-generato", "symbol": "🛣", "score": "95.0", "issues": "Risky Unicode range: emoji_extended; Long fallback: 16 chars; Generic numbered name"}, "NG1904": {"action": "REPLACE_UNICODE", "reason": "Unicode problematico: emoji_extended; Auto-generato", "symbol": "🦞", "score": "95.0", "issues": "Risky Unicode range: emoji_extended; Long fallback: 17 chars"}, "NG1905": {"action": "REPLACE_UNICODE", "reason": "Unicode problematico: emoji_extended; Auto-generato", "symbol": "🦔", "score": "95.0", "issues": "Risky Unicode range: emoji_extended; Long fallback: 18 chars; Generic numbered name"}, "NG1908": {"action": "REPLACE_UNICODE", "reason": "Unicode problematico: emoji_extended; Auto-generato", "symbol": "🤛", "score": "95.0", "issues": "Risky Unicode range: emoji_extended; Long fallback: 13 chars"}, "NG1912": {"action": "REPLACE_UNICODE", "reason": "Unicode problematico: emoji_extended; Auto-generato", "symbol": "🚍", "score": "95.0", "issues": "Risky Unicode range: emoji_extended; Long fallback: 11 chars"}, "NG1913": {"action": "REPLACE_UNICODE", "reason": "Unicode problematico: emoji_extended; Auto-generato", "symbol": "🍉", "score": "95.0", "issues": "Risky Unicode range: emoji_extended; Long fallback: 12 chars"}, "NG1914": {"action": "REPLACE_UNICODE", "reason": "Unicode problematico: emoji_extended; Auto-generato", "symbol": "😧", "score": "95.0", "issues": "Risky Unicode range: emoji_extended; Long fallback: 11 chars"}, "NG1917": {"action": "REPLACE_UNICODE", "reason": "Unicode problematico: emoji_extended; Auto-generato", "symbol": "🕾", "score": "95.0", "issues": "Risky Unicode range: emoji_extended; Long fallback: 14 chars"}, "NG1920": {"action": "REPLACE_UNICODE", "reason": "Unicode problematico: emoji_extended; Auto-generato", "symbol": "🧜", "score": "95.0", "issues": "Risky Unicode range: emoji_extended; Long fallback: 17 chars; Generic numbered name"}, "NG1921": {"action": "REPLACE_UNICODE", "reason": "Unicode problematico: emoji_extended; Auto-generato", "symbol": "🛌", "score": "95.0", "issues": "Risky Unicode range: emoji_extended; Long fallback: 18 chars"}, "NG1922": {"action": "REPLACE_UNICODE", "reason": "Unicode problematico: emoji_extended; Auto-generato", "symbol": "📀", "score": "95.0", "issues": "Risky Unicode range: emoji_extended; Long fallback: 15 chars; Generic numbered name"}, "NG1924": {"action": "REPLACE_UNICODE", "reason": "Unicode problematico: emoji_extended; Auto-generato", "symbol": "🐮", "score": "95.0", "issues": "Risky Unicode range: emoji_extended; Long fallback: 19 chars; Generic numbered name"}, "NG1925": {"action": "REPLACE_UNICODE", "reason": "Unicode problematico: emoji_extended; Auto-generato", "symbol": "🛢", "score": "95.0", "issues": "Risky Unicode range: emoji_extended; Long fallback: 17 chars"}, "NG1927": {"action": "REPLACE_UNICODE", "reason": "Unicode problematico: emoji_extended; Auto-generato", "symbol": "🔑", "score": "95.0", "issues": "Risky Unicode range: emoji_extended; Long fallback: 18 chars; Generic numbered name"}, "NG1929": {"action": "REPLACE_UNICODE", "reason": "Unicode problematico: emoji_extended; Auto-generato", "symbol": "🦫", "score": "95.0", "issues": "Risky Unicode range: emoji_extended; Long fallback: 19 chars"}, "NG1935": {"action": "REPLACE_UNICODE", "reason": "Unicode problematico: emoji_extended; Auto-generato", "symbol": "🥴", "score": "95.0", "issues": "Risky Unicode range: emoji_extended; Long fallback: 18 chars; Generic numbered name"}, "NG1936": {"action": "REPLACE_UNICODE", "reason": "Unicode problematico: emoji_extended; Auto-generato", "symbol": "🜫", "score": "95.0", "issues": "Risky Unicode range: emoji_extended; Long fallback: 20 chars"}, "NG1938": {"action": "REPLACE_UNICODE", "reason": "Unicode problematico: emoji_extended; Auto-generato", "symbol": "🥭", "score": "95.0", "issues": "Risky Unicode range: emoji_extended; Long fallback: 20 chars"}, "NG1939": {"action": "REPLACE_UNICODE", "reason": "Unicode problematico: emoji_extended; Auto-generato", "symbol": "🠤", "score": "95.0", "issues": "Risky Unicode range: emoji_extended; Long fallback: 18 chars"}, "NG1942": {"action": "REPLACE_UNICODE", "reason": "Unicode problematico: emoji_extended; Auto-generato", "symbol": "🜁", "score": "95.0", "issues": "Risky Unicode range: emoji_extended; Long fallback: 19 chars; Generic numbered name"}, "NG1944": {"action": "REPLACE_UNICODE", "reason": "Unicode problematico: emoji_extended; Auto-generato", "symbol": "🡨", "score": "95.0", "issues": "Risky Unicode range: emoji_extended; Long fallback: 16 chars"}, "NG1946": {"action": "REPLACE_UNICODE", "reason": "Unicode problematico: emoji_extended; Auto-generato", "symbol": "🔎", "score": "95.0", "issues": "Risky Unicode range: emoji_extended; Long fallback: 20 chars"}, "NG1948": {"action": "REPLACE_UNICODE", "reason": "Unicode problematico: emoji_extended; Auto-generato", "symbol": "🤞", "score": "95.0", "issues": "Risky Unicode range: emoji_extended; Long fallback: 20 chars"}, "NG1949": {"action": "REPLACE_UNICODE", "reason": "Unicode problematico: emoji_extended; Auto-generato", "symbol": "🤥", "score": "95.0", "issues": "Risky Unicode range: emoji_extended; Long fallback: 20 chars"}, "NG1951": {"action": "REPLACE_UNICODE", "reason": "Unicode problematico: emoji_extended; Auto-generato", "symbol": "🚟", "score": "95.0", "issues": "Risky Unicode range: emoji_extended; Long fallback: 19 chars; Generic numbered name"}, "NG1952": {"action": "REPLACE_UNICODE", "reason": "Unicode problematico: emoji_extended; Auto-generato", "symbol": "💨", "score": "95.0", "issues": "Risky Unicode range: emoji_extended; Long fallback: 19 chars; Generic numbered name"}, "NG1953": {"action": "REPLACE_UNICODE", "reason": "Unicode problematico: emoji_extended; Auto-generato", "symbol": "🚌", "score": "95.0", "issues": "Risky Unicode range: emoji_extended; Long fallback: 19 chars; Generic numbered name"}, "NG1957": {"action": "REPLACE_UNICODE", "reason": "Unicode problematico: emoji_extended; Auto-generato", "symbol": "🐗", "score": "95.0", "issues": "Risky Unicode range: emoji_extended; Long fallback: 19 chars; Generic numbered name"}, "NG1958": {"action": "REPLACE_UNICODE", "reason": "Unicode problematico: emoji_extended; Auto-generato", "symbol": "🠁", "score": "95.0", "issues": "Risky Unicode range: emoji_extended; Long fallback: 19 chars; Generic numbered name"}, "NG1964": {"action": "REPLACE_UNICODE", "reason": "Unicode problematico: emoji_extended; Auto-generato", "symbol": "🞾", "score": "95.0", "issues": "Risky Unicode range: emoji_extended; Long fallback: 12 chars; Generic numbered name"}, "NG1967": {"action": "REPLACE_UNICODE", "reason": "Unicode problematico: emoji_extended; Auto-generato", "symbol": "🡺", "score": "95.0", "issues": "Risky Unicode range: emoji_extended; Long fallback: 12 chars"}, "NG1971": {"action": "REPLACE_UNICODE", "reason": "Unicode problematico: emoji_extended; Auto-generato", "symbol": "🏘", "score": "95.0", "issues": "Risky Unicode range: emoji_extended; Long fallback: 11 chars"}, "NG1975": {"action": "REPLACE_UNICODE", "reason": "Unicode problematico: emoji_extended; Auto-generato", "symbol": "🕠", "score": "95.0", "issues": "Risky Unicode range: emoji_extended; Long fallback: 19 chars"}, "NG1980": {"action": "REPLACE_UNICODE", "reason": "Unicode problematico: emoji_extended; Auto-generato", "symbol": "🞚", "score": "95.0", "issues": "Risky Unicode range: emoji_extended; Long fallback: 16 chars; Generic numbered name"}, "NG1981": {"action": "REPLACE_UNICODE", "reason": "Unicode problematico: emoji_extended; Auto-generato", "symbol": "🚽", "score": "95.0", "issues": "Risky Unicode range: emoji_extended; Long fallback: 20 chars; Generic numbered name"}, "NG1984": {"action": "REPLACE_UNICODE", "reason": "Unicode problematico: emoji_extended; Auto-generato", "symbol": "🛗", "score": "95.0", "issues": "Risky Unicode range: emoji_extended; Long fallback: 17 chars"}, "NG1986": {"action": "REPLACE_UNICODE", "reason": "Unicode problematico: emoji_extended; Auto-generato", "symbol": "👆", "score": "95.0", "issues": "Risky Unicode range: emoji_extended; Long fallback: 20 chars; Generic numbered name"}, "NG1987": {"action": "REPLACE_UNICODE", "reason": "Unicode problematico: emoji_extended; Auto-generato", "symbol": "😁", "score": "95.0", "issues": "Risky Unicode range: emoji_extended; Long fallback: 18 chars; Generic numbered name"}, "NG1990": {"action": "REPLACE_UNICODE", "reason": "Unicode problematico: emoji_extended; Auto-generato", "symbol": "🡻", "score": "95.0", "issues": "Risky Unicode range: emoji_extended; Long fallback: 20 chars; Generic numbered name"}, "NG1991": {"action": "REPLACE_UNICODE", "reason": "Unicode problematico: emoji_extended; Auto-generato", "symbol": "🜘", "score": "95.0", "issues": "Risky Unicode range: emoji_extended; Long fallback: 20 chars; Generic numbered name"}, "NG1992": {"action": "REPLACE_UNICODE", "reason": "Unicode problematico: emoji_extended; Auto-generato", "symbol": "🞆", "score": "95.0", "issues": "Risky Unicode range: emoji_extended; Long fallback: 20 chars; Generic numbered name"}, "NG1995": {"action": "REPLACE_UNICODE", "reason": "Unicode problematico: emoji_extended; Auto-generato", "symbol": "🍰", "score": "95.0", "issues": "Risky Unicode range: emoji_extended; Long fallback: 20 chars; Generic numbered name"}, "NG1996": {"action": "REPLACE_UNICODE", "reason": "Unicode problematico: emoji_extended; Auto-generato", "symbol": "🤹", "score": "95.0", "issues": "Risky Unicode range: emoji_extended; Long fallback: 20 chars; Generic numbered name"}, "NG1997": {"action": "REPLACE_UNICODE", "reason": "Unicode problematico: emoji_extended; Auto-generato", "symbol": "🏚", "score": "95.0", "issues": "Risky Unicode range: emoji_extended; Long fallback: 20 chars; Generic numbered name"}, "NG1999": {"action": "REPLACE_UNICODE", "reason": "Unicode problematico: emoji_extended; Auto-generato", "symbol": "🡕", "score": "95.0", "issues": "Risky Unicode range: emoji_extended; Long fallback: 20 chars; Generic numbered name"}, "NG2000": {"action": "REPLACE_UNICODE", "reason": "Unicode problematico: emoji_extended; Auto-generato", "symbol": "🐣", "score": "95.0", "issues": "Risky Unicode range: emoji_extended; Long fallback: 20 chars; Generic numbered name"}, "NG2001": {"action": "REPLACE_UNICODE", "reason": "Unicode problematico: emoji_extended; Auto-generato", "symbol": "🝱", "score": "95.0", "issues": "Risky Unicode range: emoji_extended; Long fallback: 20 chars; Generic numbered name"}, "NG2002": {"action": "REPLACE_UNICODE", "reason": "Unicode problematico: emoji_extended; Auto-generato", "symbol": "🜺", "score": "95.0", "issues": "Risky Unicode range: emoji_extended; Long fallback: 20 chars; Generic numbered name"}, "NG2005": {"action": "REPLACE_UNICODE", "reason": "Unicode problematico: emoji_extended; Auto-generato", "symbol": "😌", "score": "95.0", "issues": "Risky Unicode range: emoji_extended; Long fallback: 20 chars; Generic numbered name"}, "NG2006": {"action": "REPLACE_UNICODE", "reason": "Unicode problematico: emoji_extended; Auto-generato", "symbol": "🜞", "score": "95.0", "issues": "Risky Unicode range: emoji_extended; Long fallback: 20 chars; Generic numbered name"}, "NG2008": {"action": "REPLACE_UNICODE", "reason": "Unicode problematico: emoji_extended; Auto-generato", "symbol": "🙍", "score": "95.0", "issues": "Risky Unicode range: emoji_extended; Long fallback: 20 chars; Generic numbered name"}, "NG2010": {"action": "REPLACE_UNICODE", "reason": "Unicode problematico: emoji_extended; Auto-generato", "symbol": "🧺", "score": "95.0", "issues": "Risky Unicode range: emoji_extended; Long fallback: 17 chars"}, "NG2012": {"action": "REPLACE_UNICODE", "reason": "Unicode problematico: emoji_extended; Auto-generato", "symbol": "🤏", "score": "95.0", "issues": "Risky Unicode range: emoji_extended; Long fallback: 20 chars; Generic numbered name"}, "NG2013": {"action": "REPLACE_UNICODE", "reason": "Unicode problematico: emoji_extended; Auto-generato", "symbol": "🥜", "score": "95.0", "issues": "Risky Unicode range: emoji_extended; Long fallback: 18 chars; Generic numbered name"}, "NG2014": {"action": "REPLACE_UNICODE", "reason": "Unicode problematico: emoji_extended; Auto-generato", "symbol": "🚓", "score": "95.0", "issues": "Risky Unicode range: emoji_extended; Long fallback: 20 chars; Generic numbered name"}, "NG2016": {"action": "REPLACE_UNICODE", "reason": "Unicode problematico: emoji_extended; Auto-generato", "symbol": "🟂", "score": "95.0", "issues": "Risky Unicode range: emoji_extended; Long fallback: 20 chars; Generic numbered name"}, "NG2018": {"action": "REPLACE_UNICODE", "reason": "Unicode problematico: emoji_extended; Auto-generato", "symbol": "🝰", "score": "95.0", "issues": "Risky Unicode range: emoji_extended; Long fallback: 18 chars; Generic numbered name"}, "NG2021": {"action": "REPLACE_UNICODE", "reason": "Unicode problematico: emoji_extended; Auto-generato", "symbol": "🟁", "score": "95.0", "issues": "Risky Unicode range: emoji_extended; Long fallback: 20 chars; Generic numbered name"}, "NG2022": {"action": "REPLACE_UNICODE", "reason": "Unicode problematico: emoji_extended; Auto-generato", "symbol": "🧃", "score": "95.0", "issues": "Risky Unicode range: emoji_extended; Long fallback: 18 chars; Generic numbered name"}, "NG2023": {"action": "REPLACE_UNICODE", "reason": "Unicode problematico: emoji_extended; Auto-generato", "symbol": "💙", "score": "95.0", "issues": "Risky Unicode range: emoji_extended; Long fallback: 20 chars; Generic numbered name"}, "NG2025": {"action": "REPLACE_UNICODE", "reason": "Unicode problematico: emoji_extended; Auto-generato", "symbol": "🦯", "score": "95.0", "issues": "Risky Unicode range: emoji_extended; Long fallback: 20 chars; Generic numbered name"}, "NG2028": {"action": "REPLACE_UNICODE", "reason": "Unicode problematico: emoji_extended; Auto-generato", "symbol": "🢢", "score": "95.0", "issues": "Risky Unicode range: emoji_extended; Long fallback: 18 chars; Generic numbered name"}, "NG2030": {"action": "REPLACE_UNICODE", "reason": "Unicode problematico: emoji_extended; Auto-generato", "symbol": "🝎", "score": "95.0", "issues": "Risky Unicode range: emoji_extended; Long fallback: 20 chars; Generic numbered name"}, "NG2031": {"action": "REPLACE_UNICODE", "reason": "Unicode problematico: emoji_extended; Auto-generato", "symbol": "🟧", "score": "95.0", "issues": "Risky Unicode range: emoji_extended; Long fallback: 20 chars; Generic numbered name"}, "NG2033": {"action": "REPLACE_UNICODE", "reason": "Unicode problematico: emoji_extended; Auto-generato", "symbol": "🠝", "score": "95.0", "issues": "Risky Unicode range: emoji_extended; Long fallback: 18 chars; Generic numbered name"}, "NG2035": {"action": "REPLACE_UNICODE", "reason": "Unicode problematico: emoji_extended; Auto-generato", "symbol": "🦛", "score": "95.0", "issues": "Risky Unicode range: emoji_extended; Long fallback: 19 chars; Generic numbered name"}, "NG2036": {"action": "REPLACE_UNICODE", "reason": "Unicode problematico: emoji_extended; Auto-generato", "symbol": "🥬", "score": "95.0", "issues": "Risky Unicode range: emoji_extended; Long fallback: 19 chars; Generic numbered name"}, "NG2037": {"action": "REPLACE_UNICODE", "reason": "Unicode problematico: emoji_extended; Auto-generato", "symbol": "🗏", "score": "95.0", "issues": "Risky Unicode range: emoji_extended; Long fallback: 19 chars; Generic numbered name"}, "NG2038": {"action": "REPLACE_UNICODE", "reason": "Unicode problematico: emoji_extended; Auto-generato", "symbol": "🏮", "score": "95.0", "issues": "Risky Unicode range: emoji_extended; Long fallback: 19 chars; Generic numbered name"}, "NG2039": {"action": "REPLACE_UNICODE", "reason": "Unicode problematico: emoji_extended; Auto-generato", "symbol": "🗠", "score": "95.0", "issues": "Risky Unicode range: emoji_extended; Long fallback: 19 chars; Generic numbered name"}, "NG2041": {"action": "REPLACE_UNICODE", "reason": "Unicode problematico: emoji_extended; Auto-generato", "symbol": "🠢", "score": "95.0", "issues": "Risky Unicode range: emoji_extended; Long fallback: 19 chars; Generic numbered name"}, "NG2046": {"action": "REPLACE_UNICODE", "reason": "Unicode problematico: emoji_extended; Auto-generato", "symbol": "🌶", "score": "95.0", "issues": "Risky Unicode range: emoji_extended; Long fallback: 19 chars; Generic numbered name"}, "NG2047": {"action": "REPLACE_UNICODE", "reason": "Unicode problematico: emoji_extended; Auto-generato", "symbol": "🦕", "score": "95.0", "issues": "Risky Unicode range: emoji_extended; Long fallback: 19 chars; Generic numbered name"}}, "severity_b_actions": {"NG0023": {"action": "REPLACE_UNICODE", "reason": "Unicode problematico: miscellaneous_symbols; Auto-generato", "symbol": "☎", "score": "95.0", "issues": "Risky Unicode range: miscellaneous_symbols"}, "NG0028": {"action": "DEFER", "reason": "Auto-generato", "symbol": "⊬", "score": "95.0", "issues": "Long fallback: 11 chars; Generic numbered name"}, "NG0042": {"action": "DEFER", "reason": "Auto-generato", "symbol": "↤", "score": "95.0", "issues": "Long fallback: 11 chars; Generic numbered name"}, "NG0043": {"action": "DEFER", "reason": "Auto-generato", "symbol": "↰", "score": "95.0", "issues": "Long fallback: 11 chars; Generic numbered name"}, "NG0064": {"action": "DEFER", "reason": "Auto-generato", "symbol": "⚮", "score": "95.0", "issues": "Long fallback: 11 chars; Generic numbered name"}, "NG0079": {"action": "DEFER", "reason": "Auto-generato", "symbol": "⇦", "score": "95.0", "issues": "Long fallback: 11 chars; Generic numbered name"}, "NG0087": {"action": "DEFER", "reason": "Auto-generato", "symbol": "⇟", "score": "95.0", "issues": "Long fallback: 11 chars; Generic numbered name"}, "NG0107": {"action": "DEFER", "reason": "Auto-generato", "symbol": "⇅", "score": "95.0", "issues": "Long fallback: 11 chars; Generic numbered name"}, "NG0114": {"action": "DEFER", "reason": "Auto-generato", "symbol": "↹", "score": "95.0", "issues": "Long fallback: 11 chars; Generic numbered name"}, "NG0123": {"action": "DEFER", "reason": "Auto-generato", "symbol": "▫", "score": "95.0", "issues": "Long fallback: 11 chars; Generic numbered name"}, "NG0129": {"action": "DEFER", "reason": "Auto-generato", "symbol": "∰", "score": "95.0", "issues": "Long fallback: 11 chars; Generic numbered name"}, "NG0141": {"action": "DEFER", "reason": "Auto-generato", "symbol": "⋊", "score": "95.0", "issues": "Long fallback: 11 chars; Generic numbered name"}, "NG0173": {"action": "DEFER", "reason": "Auto-generato", "symbol": "⊀", "score": "95.0", "issues": "Long fallback: 11 chars; Generic numbered name"}, "NG0178": {"action": "DEFER", "reason": "Auto-generato", "symbol": "↩", "score": "95.0", "issues": "Long fallback: 11 chars; Generic numbered name"}, "NG0186": {"action": "DEFER", "reason": "Auto-generato", "symbol": "◐", "score": "95.0", "issues": "Long fallback: 11 chars; Generic numbered name"}, "NG0192": {"action": "DEFER", "reason": "Auto-generato", "symbol": "≏", "score": "95.0", "issues": "Long fallback: 11 chars; Generic numbered name"}, "NG0195": {"action": "DEFER", "reason": "Auto-generato", "symbol": "⇚", "score": "95.0", "issues": "Long fallback: 12 chars; Generic numbered name"}, "NG0201": {"action": "DEFER", "reason": "Auto-generato", "symbol": "∶", "score": "95.0", "issues": "Long fallback: 11 chars; Generic numbered name"}, "NG0208": {"action": "DEFER", "reason": "Auto-generato", "symbol": "⇢", "score": "95.0", "issues": "Long fallback: 11 chars; Generic numbered name"}, "NG0212": {"action": "DEFER", "reason": "Auto-generato", "symbol": "◚", "score": "95.0", "issues": "Long fallback: 12 chars; Generic numbered name"}, "NG0216": {"action": "DEFER", "reason": "Auto-generato", "symbol": "↖", "score": "95.0", "issues": "Long fallback: 11 chars; Generic numbered name"}, "NG0217": {"action": "DEFER", "reason": "Auto-generato", "symbol": "⋎", "score": "95.0", "issues": "Long fallback: 12 chars; Generic numbered name"}, "NG0220": {"action": "DEFER", "reason": "Auto-generato", "symbol": "▧", "score": "95.0", "issues": "Long fallback: 12 chars; Generic numbered name"}, "NG0223": {"action": "DEFER", "reason": "Auto-generato", "symbol": "◽", "score": "95.0", "issues": "Long fallback: 11 chars; Generic numbered name"}, "NG0226": {"action": "DEFER", "reason": "Auto-generato", "symbol": "▿", "score": "95.0", "issues": "Long fallback: 12 chars; Generic numbered name"}, "NG0246": {"action": "DEFER", "reason": "Auto-generato", "symbol": "◑", "score": "95.0", "issues": "Long fallback: 12 chars; Generic numbered name"}, "NG0249": {"action": "DEFER", "reason": "Auto-generato", "symbol": "⋜", "score": "95.0", "issues": "Long fallback: 11 chars; Generic numbered name"}, "NG0250": {"action": "DEFER", "reason": "Auto-generato", "symbol": "∋", "score": "95.0", "issues": "Long fallback: 12 chars; Generic numbered name"}, "NG0252": {"action": "DEFER", "reason": "Auto-generato", "symbol": "↝", "score": "95.0", "issues": "Long fallback: 11 chars; Generic numbered name"}, "NG0268": {"action": "DEFER", "reason": "Auto-generato", "symbol": "≄", "score": "95.0", "issues": "Long fallback: 12 chars; Generic numbered name"}, "NG0278": {"action": "DEFER", "reason": "Auto-generato", "symbol": "⇝", "score": "95.0", "issues": "Long fallback: 12 chars; Generic numbered name"}, "NG0283": {"action": "DEFER", "reason": "Auto-generato", "symbol": "◍", "score": "95.0", "issues": "Long fallback: 12 chars; Generic numbered name"}, "NG0289": {"action": "DEFER", "reason": "Auto-generato", "symbol": "⊻", "score": "95.0", "issues": "Long fallback: 12 chars; Generic numbered name"}, "NG0300": {"action": "DEFER", "reason": "Auto-generato", "symbol": "→", "score": "95.0", "issues": "Long fallback: 11 chars; Generic numbered name"}, "NG0310": {"action": "DEFER", "reason": "Auto-generato", "symbol": "↶", "score": "95.0", "issues": "Long fallback: 12 chars; Generic numbered name"}, "NG0318": {"action": "DEFER", "reason": "Auto-generato", "symbol": "⋈", "score": "95.0", "issues": "Long fallback: 12 chars; Generic numbered name"}, "NG0319": {"action": "DEFER", "reason": "Auto-generato", "symbol": "⊾", "score": "95.0", "issues": "Long fallback: 12 chars; Generic numbered name"}, "NG0325": {"action": "DEFER", "reason": "Auto-generato", "symbol": "∑", "score": "95.0", "issues": "Long fallback: 12 chars; Generic numbered name"}, "NG0331": {"action": "DEFER", "reason": "Auto-generato", "symbol": "⊆", "score": "95.0", "issues": "Long fallback: 12 chars; Generic numbered name"}, "NG0361": {"action": "DEFER", "reason": "Auto-generato", "symbol": "∐", "score": "95.0", "issues": "Long fallback: 11 chars; Generic numbered name"}, "NG0368": {"action": "DEFER", "reason": "Auto-generato", "symbol": "◝", "score": "95.0", "issues": "Long fallback: 11 chars; Generic numbered name"}, "NG0379": {"action": "DEFER", "reason": "Auto-generato", "symbol": "⊝", "score": "95.0", "issues": "Long fallback: 11 chars; Generic numbered name"}, "NG0387": {"action": "DEFER", "reason": "Auto-generato", "symbol": "⊷", "score": "95.0", "issues": "Long fallback: 12 chars; Generic numbered name"}, "NG0422": {"action": "DEFER", "reason": "Auto-generato", "symbol": "▶", "score": "95.0", "issues": "Long fallback: 11 chars; Generic numbered name"}, "NG0433": {"action": "DEFER", "reason": "Auto-generato", "symbol": "∆", "score": "95.0", "issues": "Long fallback: 12 chars; Generic numbered name"}, "NG0438": {"action": "DEFER", "reason": "Auto-generato", "symbol": "⋦", "score": "95.0", "issues": "Long fallback: 11 chars; Generic numbered name"}, "NG0443": {"action": "DEFER", "reason": "Auto-generato", "symbol": "≜", "score": "95.0", "issues": "Long fallback: 12 chars; Generic numbered name"}, "NG0446": {"action": "DEFER", "reason": "Auto-generato", "symbol": "⊪", "score": "95.0", "issues": "Long fallback: 11 chars; Generic numbered name"}, "NG0454": {"action": "DEFER", "reason": "Auto-generato", "symbol": "∦", "score": "95.0", "issues": "Long fallback: 12 chars; Generic numbered name"}, "NG0470": {"action": "DEFER", "reason": "Auto-generato", "symbol": "▣", "score": "95.0", "issues": "Long fallback: 12 chars; Generic numbered name"}, "NG0476": {"action": "DEFER", "reason": "Auto-generato", "symbol": "⚹", "score": "95.0", "issues": "Long fallback: 12 chars; Generic numbered name"}, "NG0484": {"action": "DEFER", "reason": "Auto-generato", "symbol": "⚶", "score": "95.0", "issues": "Long fallback: 11 chars; Generic numbered name"}, "NG0489": {"action": "DEFER", "reason": "Auto-generato", "symbol": "⊤", "score": "95.0", "issues": "Long fallback: 12 chars; Generic numbered name"}, "NG0500": {"action": "DEFER", "reason": "Auto-generato", "symbol": "≭", "score": "95.0", "issues": "Long fallback: 11 chars; Generic numbered name"}, "NG0505": {"action": "DEFER", "reason": "Auto-generato", "symbol": "⊚", "score": "95.0", "issues": "Long fallback: 12 chars; Generic numbered name"}, "NG0512": {"action": "DEFER", "reason": "Auto-generato", "symbol": "↼", "score": "95.0", "issues": "Long fallback: 12 chars; Generic numbered name"}, "NG0884": {"action": "REPLACE_UNICODE", "reason": "Unicode problematico: emoji_extended; Auto-generato", "symbol": "🡡", "score": "95.0", "issues": "Risky Unicode range: emoji_extended"}, "NG0886": {"action": "REPLACE_UNICODE", "reason": "Unicode problematico: emoji_extended; Auto-generato", "symbol": "🌅", "score": "95.0", "issues": "Risky Unicode range: emoji_extended"}, "NG0891": {"action": "DEFER", "reason": "Auto-generato", "symbol": "✒", "score": "95.0", "issues": "Long fallback: 20 chars; Generic numbered name"}, "NG0894": {"action": "REPLACE_UNICODE", "reason": "Unicode problematico: emoji_extended; Auto-generato", "symbol": "🙈", "score": "95.0", "issues": "Risky Unicode range: emoji_extended"}, "NG0923": {"action": "DEFER", "reason": "Auto-generato", "symbol": "⯮", "score": "95.0", "issues": "Long fallback: 17 chars; Generic numbered name"}, "NG0929": {"action": "DEFER", "reason": "Auto-generato", "symbol": "⫏", "score": "95.0", "issues": "Long fallback: 17 chars; Generic numbered name"}, "NG0943": {"action": "REPLACE_UNICODE", "reason": "Unicode problematico: emoji_extended; Auto-generato", "symbol": "🚙", "score": "95.0", "issues": "Risky Unicode range: emoji_extended"}, "NG0947": {"action": "REPLACE_UNICODE", "reason": "Unicode problematico: emoji_extended; Auto-generato", "symbol": "🚏", "score": "95.0", "issues": "Risky Unicode range: emoji_extended"}, "NG0948": {"action": "DEFER", "reason": "Auto-generato", "symbol": "⦛", "score": "95.0", "issues": "Long fallback: 11 chars; Generic numbered name"}, "NG0954": {"action": "REPLACE_UNICODE", "reason": "Unicode problematico: emoji_extended; Auto-generato", "symbol": "🞧", "score": "95.0", "issues": "Risky Unicode range: emoji_extended"}, "NG0955": {"action": "REPLACE_UNICODE", "reason": "Unicode problematico: emoji_extended; Auto-generato", "symbol": "🟔", "score": "95.0", "issues": "Risky Unicode range: emoji_extended"}, "NG0964": {"action": "DEFER", "reason": "Auto-generato", "symbol": "⯜", "score": "95.0", "issues": "Long fallback: 13 chars; Generic numbered name"}, "NG0970": {"action": "DEFER", "reason": "Auto-generato", "symbol": "❑", "score": "95.0", "issues": "Long fallback: 20 chars; Generic numbered name"}, "NG0971": {"action": "DEFER", "reason": "Auto-generato", "symbol": "⮷", "score": "95.0", "issues": "Long fallback: 20 chars; Generic numbered name"}, "NG0974": {"action": "DEFER", "reason": "Auto-generato", "symbol": "⣠", "score": "95.0", "issues": "Long fallback: 20 chars; Generic numbered name"}, "NG0978": {"action": "DEFER", "reason": "Auto-generato", "symbol": "⨂", "score": "95.0", "issues": "Long fallback: 20 chars; Generic numbered name"}, "NG0983": {"action": "DEFER", "reason": "Auto-generato", "symbol": "⢖", "score": "95.0", "issues": "Long fallback: 18 chars; Generic numbered name"}, "NG0985": {"action": "DEFER", "reason": "Auto-generato", "symbol": "𝝏", "score": "95.0", "issues": "Long fallback: 20 chars; Generic numbered name"}, "NG0986": {"action": "DEFER", "reason": "Auto-generato", "symbol": "⤗", "score": "95.0", "issues": "Long fallback: 18 chars; Generic numbered name"}, "NG0987": {"action": "DEFER", "reason": "Auto-generato", "symbol": "⮐", "score": "95.0", "issues": "Long fallback: 18 chars; Generic numbered name"}, "NG0988": {"action": "DEFER", "reason": "Auto-generato", "symbol": "✼", "score": "95.0", "issues": "Long fallback: 18 chars; Generic numbered name"}, "NG0990": {"action": "DEFER", "reason": "Auto-generato", "symbol": "⫟", "score": "95.0", "issues": "Long fallback: 20 chars; Generic numbered name"}, "NG0991": {"action": "DEFER", "reason": "Auto-generato", "symbol": "⮶", "score": "95.0", "issues": "Long fallback: 18 chars; Generic numbered name"}, "NG0992": {"action": "DEFER", "reason": "Auto-generato", "symbol": "⫄", "score": "95.0", "issues": "Long fallback: 16 chars; Generic numbered name"}, "NG1000": {"action": "DEFER", "reason": "Auto-generato", "symbol": "❘", "score": "95.0", "issues": "Long fallback: 16 chars; Generic numbered name"}, "NG1002": {"action": "DEFER", "reason": "Auto-generato", "symbol": "⥸", "score": "95.0", "issues": "Long fallback: 17 chars; Generic numbered name"}, "NG1003": {"action": "DEFER", "reason": "Auto-generato", "symbol": "⠁", "score": "95.0", "issues": "Long fallback: 17 chars; Generic numbered name"}, "NG1009": {"action": "DEFER", "reason": "Auto-generato", "symbol": "❫", "score": "95.0", "issues": "Long fallback: 14 chars; Generic numbered name"}, "NG1011": {"action": "DEFER", "reason": "Auto-generato", "symbol": "❤", "score": "95.0", "issues": "Long fallback: 11 chars; Generic numbered name"}, "NG1012": {"action": "DEFER", "reason": "Auto-generato", "symbol": "❗", "score": "95.0", "issues": "Long fallback: 14 chars; Generic numbered name"}, "NG1014": {"action": "DEFER", "reason": "Auto-generato", "symbol": "⠮", "score": "95.0", "issues": "Long fallback: 15 chars; Generic numbered name"}, "NG1020": {"action": "DEFER", "reason": "Auto-generato", "symbol": "⥫", "score": "95.0", "issues": "Long fallback: 19 chars; Generic numbered name"}, "NG1023": {"action": "DEFER", "reason": "Auto-generato", "symbol": "⫉", "score": "95.0", "issues": "Long fallback: 20 chars; Generic numbered name"}, "NG1024": {"action": "DEFER", "reason": "Auto-generato", "symbol": "⭙", "score": "95.0", "issues": "Long fallback: 19 chars; Generic numbered name"}, "NG1026": {"action": "DEFER", "reason": "Auto-generato", "symbol": "⫬", "score": "95.0", "issues": "Long fallback: 19 chars; Generic numbered name"}, "NG1028": {"action": "DEFER", "reason": "Auto-generato", "symbol": "⠂", "score": "95.0", "issues": "Long fallback: 20 chars; Generic numbered name"}, "NG1029": {"action": "DEFER", "reason": "Auto-generato", "symbol": "⦙", "score": "95.0", "issues": "Long fallback: 20 chars; Generic numbered name"}, "NG1030": {"action": "DEFER", "reason": "Auto-generato", "symbol": "⭌", "score": "95.0", "issues": "Long fallback: 20 chars; Generic numbered name"}, "NG1032": {"action": "DEFER", "reason": "Auto-generato", "symbol": "⨚", "score": "95.0", "issues": "Long fallback: 20 chars; Generic numbered name"}, "NG1033": {"action": "DEFER", "reason": "Auto-generato", "symbol": "⮟", "score": "95.0", "issues": "Long fallback: 20 chars; Generic numbered name"}, "NG1034": {"action": "DEFER", "reason": "Auto-generato", "symbol": "✳", "score": "95.0", "issues": "Long fallback: 20 chars; Generic numbered name"}, "NG1035": {"action": "DEFER", "reason": "Auto-generato", "symbol": "⥇", "score": "95.0", "issues": "Long fallback: 20 chars; Generic numbered name"}, "NG1038": {"action": "DEFER", "reason": "Auto-generato", "symbol": "⢈", "score": "95.0", "issues": "Long fallback: 14 chars; Generic numbered name"}, "NG1042": {"action": "DEFER", "reason": "Auto-generato", "symbol": "⧹", "score": "95.0", "issues": "Long fallback: 17 chars; Generic numbered name"}, "NG1046": {"action": "DEFER", "reason": "Auto-generato", "symbol": "⨥", "score": "95.0", "issues": "Long fallback: 17 chars; Generic numbered name"}, "NG1049": {"action": "DEFER", "reason": "Auto-generato", "symbol": "⯦", "score": "95.0", "issues": "Long fallback: 16 chars; Generic numbered name"}, "NG1052": {"action": "DEFER", "reason": "Auto-generato", "symbol": "⡘", "score": "95.0", "issues": "Long fallback: 17 chars; Generic numbered name"}, "NG1054": {"action": "DEFER", "reason": "Auto-generato", "symbol": "⯨", "score": "95.0", "issues": "Long fallback: 14 chars; Generic numbered name"}, "NG1060": {"action": "DEFER", "reason": "Auto-generato", "symbol": "❄", "score": "95.0", "issues": "Long fallback: 20 chars; Generic numbered name"}, "NG1068": {"action": "DEFER", "reason": "Auto-generato", "symbol": "✞", "score": "95.0", "issues": "Long fallback: 17 chars; Generic numbered name"}, "NG1070": {"action": "DEFER", "reason": "Auto-generato", "symbol": "⪒", "score": "95.0", "issues": "Long fallback: 19 chars; Generic numbered name"}, "NG1071": {"action": "DEFER", "reason": "Auto-generato", "symbol": "⮙", "score": "95.0", "issues": "Long fallback: 17 chars; Generic numbered name"}, "NG1081": {"action": "DEFER", "reason": "Auto-generato", "symbol": "⦓", "score": "95.0", "issues": "Long fallback: 17 chars; Generic numbered name"}, "NG1091": {"action": "DEFER", "reason": "Auto-generato", "symbol": "⪥", "score": "95.0", "issues": "Long fallback: 17 chars; Generic numbered name"}, "NG1100": {"action": "DEFER", "reason": "Auto-generato", "symbol": "⫍", "score": "95.0", "issues": "Long fallback: 16 chars; Generic numbered name"}, "NG1101": {"action": "DEFER", "reason": "Auto-generato", "symbol": "⭼", "score": "95.0", "issues": "Long fallback: 15 chars; Generic numbered name"}, "NG1103": {"action": "DEFER", "reason": "Auto-generato", "symbol": "⪨", "score": "95.0", "issues": "Long fallback: 12 chars; Generic numbered name"}, "NG1104": {"action": "DEFER", "reason": "Auto-generato", "symbol": "✏", "score": "95.0", "issues": "Long fallback: 16 chars; Generic numbered name"}, "NG1105": {"action": "DEFER", "reason": "Auto-generato", "symbol": "⭃", "score": "95.0", "issues": "Long fallback: 16 chars; Generic numbered name"}, "NG1106": {"action": "DEFER", "reason": "Auto-generato", "symbol": "⥘", "score": "95.0", "issues": "Long fallback: 14 chars; Generic numbered name"}, "NG1112": {"action": "DEFER", "reason": "Auto-generato", "symbol": "⢧", "score": "95.0", "issues": "Long fallback: 19 chars; Generic numbered name"}, "NG1123": {"action": "DEFER", "reason": "Auto-generato", "symbol": "⧳", "score": "95.0", "issues": "Long fallback: 17 chars; Generic numbered name"}, "NG1126": {"action": "DEFER", "reason": "Auto-generato", "symbol": "⦢", "score": "95.0", "issues": "Long fallback: 17 chars; Generic numbered name"}, "NG1131": {"action": "DEFER", "reason": "Auto-generato", "symbol": "➬", "score": "95.0", "issues": "Long fallback: 16 chars; Generic numbered name"}, "NG1134": {"action": "DEFER", "reason": "Auto-generato", "symbol": "⯄", "score": "95.0", "issues": "Long fallback: 18 chars; Generic numbered name"}, "NG1143": {"action": "DEFER", "reason": "Auto-generato", "symbol": "✩", "score": "95.0", "issues": "Long fallback: 20 chars; Generic numbered name"}, "NG1146": {"action": "DEFER", "reason": "Auto-generato", "symbol": "⢣", "score": "95.0", "issues": "Long fallback: 16 chars; Generic numbered name"}, "NG1149": {"action": "DEFER", "reason": "Auto-generato", "symbol": "⥣", "score": "95.0", "issues": "Long fallback: 16 chars; Generic numbered name"}, "NG1153": {"action": "DEFER", "reason": "Auto-generato", "symbol": "⠿", "score": "95.0", "issues": "Long fallback: 16 chars; Generic numbered name"}, "NG1155": {"action": "DEFER", "reason": "Auto-generato", "symbol": "⦣", "score": "95.0", "issues": "Long fallback: 18 chars; Generic numbered name"}, "NG1157": {"action": "DEFER", "reason": "Auto-generato", "symbol": "⯛", "score": "95.0", "issues": "Long fallback: 18 chars; Generic numbered name"}, "NG1160": {"action": "DEFER", "reason": "Auto-generato", "symbol": "⣃", "score": "95.0", "issues": "Long fallback: 18 chars; Generic numbered name"}, "NG1162": {"action": "DEFER", "reason": "Auto-generato", "symbol": "⨰", "score": "95.0", "issues": "Long fallback: 14 chars; Generic numbered name"}, "NG1163": {"action": "DEFER", "reason": "Auto-generato", "symbol": "❍", "score": "95.0", "issues": "Long fallback: 16 chars; Generic numbered name"}, "NG1164": {"action": "DEFER", "reason": "Auto-generato", "symbol": "⯅", "score": "95.0", "issues": "Long fallback: 16 chars; Generic numbered name"}, "NG1167": {"action": "DEFER", "reason": "Auto-generato", "symbol": "⥙", "score": "95.0", "issues": "Long fallback: 17 chars; Generic numbered name"}, "NG1168": {"action": "DEFER", "reason": "Auto-generato", "symbol": "⥝", "score": "95.0", "issues": "Long fallback: 18 chars; Generic numbered name"}, "NG1169": {"action": "DEFER", "reason": "Auto-generato", "symbol": "⤢", "score": "95.0", "issues": "Long fallback: 14 chars; Generic numbered name"}, "NG1171": {"action": "DEFER", "reason": "Auto-generato", "symbol": "⮈", "score": "95.0", "issues": "Long fallback: 16 chars; Generic numbered name"}, "NG1173": {"action": "DEFER", "reason": "Auto-generato", "symbol": "⧩", "score": "95.0", "issues": "Long fallback: 18 chars; Generic numbered name"}, "NG1178": {"action": "DEFER", "reason": "Auto-generato", "symbol": "❠", "score": "95.0", "issues": "Long fallback: 17 chars; Generic numbered name"}, "NG1181": {"action": "DEFER", "reason": "Auto-generato", "symbol": "⧷", "score": "95.0", "issues": "Long fallback: 16 chars; Generic numbered name"}, "NG1187": {"action": "DEFER", "reason": "Auto-generato", "symbol": "⠒", "score": "95.0", "issues": "Long fallback: 18 chars; Generic numbered name"}, "NG1194": {"action": "DEFER", "reason": "Auto-generato", "symbol": "⯚", "score": "95.0", "issues": "Long fallback: 13 chars; Generic numbered name"}, "NG1195": {"action": "DEFER", "reason": "Auto-generato", "symbol": "⣹", "score": "95.0", "issues": "Long fallback: 13 chars; Generic numbered name"}, "NG1198": {"action": "DEFER", "reason": "Auto-generato", "symbol": "⥍", "score": "95.0", "issues": "Long fallback: 17 chars; Generic numbered name"}, "NG1206": {"action": "DEFER", "reason": "Auto-generato", "symbol": "✽", "score": "95.0", "issues": "Long fallback: 19 chars; Generic numbered name"}, "NG1212": {"action": "DEFER", "reason": "Auto-generato", "symbol": "⦭", "score": "95.0", "issues": "Long fallback: 17 chars; Generic numbered name"}, "NG1213": {"action": "DEFER", "reason": "Auto-generato", "symbol": "⥅", "score": "95.0", "issues": "Long fallback: 15 chars; Generic numbered name"}, "NG1214": {"action": "DEFER", "reason": "Auto-generato", "symbol": "⢹", "score": "95.0", "issues": "Long fallback: 17 chars; Generic numbered name"}, "NG1215": {"action": "DEFER", "reason": "Auto-generato", "symbol": "⧛", "score": "95.0", "issues": "Long fallback: 19 chars; Generic numbered name"}, "NG1218": {"action": "DEFER", "reason": "Auto-generato", "symbol": "⮜", "score": "95.0", "issues": "Long fallback: 20 chars; Generic numbered name"}, "NG1221": {"action": "DEFER", "reason": "Auto-generato", "symbol": "⬬", "score": "95.0", "issues": "Long fallback: 20 chars; Generic numbered name"}, "NG1224": {"action": "DEFER", "reason": "Auto-generato", "symbol": "⮘", "score": "95.0", "issues": "Long fallback: 20 chars; Generic numbered name"}, "NG1226": {"action": "DEFER", "reason": "Auto-generato", "symbol": "⣘", "score": "95.0", "issues": "Long fallback: 20 chars; Generic numbered name"}, "NG1232": {"action": "DEFER", "reason": "Auto-generato", "symbol": "⣂", "score": "95.0", "issues": "Long fallback: 20 chars; Generic numbered name"}, "NG1233": {"action": "DEFER", "reason": "Auto-generato", "symbol": "⡆", "score": "95.0", "issues": "Long fallback: 20 chars; Generic numbered name"}, "NG1241": {"action": "DEFER", "reason": "Auto-generato", "symbol": "⦴", "score": "95.0", "issues": "Long fallback: 19 chars; Generic numbered name"}, "NG1244": {"action": "DEFER", "reason": "Auto-generato", "symbol": "⣩", "score": "95.0", "issues": "Long fallback: 19 chars; Generic numbered name"}, "NG1246": {"action": "DEFER", "reason": "Auto-generato", "symbol": "❥", "score": "95.0", "issues": "Long fallback: 19 chars; Generic numbered name"}, "NG1248": {"action": "DEFER", "reason": "Auto-generato", "symbol": "⧥", "score": "95.0", "issues": "Long fallback: 20 chars; Generic numbered name"}, "NG1250": {"action": "REPLACE_UNICODE", "reason": "Unicode problematico: emoji_extended; Auto-generato", "symbol": "🤇", "score": "95.0", "issues": "Risky Unicode range: emoji_extended"}, "NG1251": {"action": "REPLACE_UNICODE", "reason": "Unicode problematico: emoji_extended; Auto-generato", "symbol": "🛨", "score": "95.0", "issues": "Risky Unicode range: emoji_extended"}, "NG1253": {"action": "REPLACE_UNICODE", "reason": "Unicode problematico: emoji_extended; Auto-generato", "symbol": "🜄", "score": "95.0", "issues": "Risky Unicode range: emoji_extended"}, "NG1254": {"action": "REPLACE_UNICODE", "reason": "Unicode problematico: emoji_extended; Auto-generato", "symbol": "🤾", "score": "95.0", "issues": "Risky Unicode range: emoji_extended"}, "NG1259": {"action": "DEFER", "reason": "Auto-generato", "symbol": "⧴", "score": "95.0", "issues": "Long fallback: 19 chars; Generic numbered name"}, "NG1261": {"action": "DEFER", "reason": "Auto-generato", "symbol": "⤡", "score": "95.0", "issues": "Long fallback: 19 chars; Generic numbered name"}, "NG1262": {"action": "DEFER", "reason": "Auto-generato", "symbol": "⨋", "score": "95.0", "issues": "Long fallback: 19 chars; Generic numbered name"}, "NG1266": {"action": "DEFER", "reason": "Auto-generato", "symbol": "⮊", "score": "95.0", "issues": "Long fallback: 17 chars; Generic numbered name"}, "NG1270": {"action": "DEFER", "reason": "Auto-generato", "symbol": "⥬", "score": "95.0", "issues": "Long fallback: 17 chars; Generic numbered name"}, "NG1274": {"action": "DEFER", "reason": "Auto-generato", "symbol": "⡪", "score": "95.0", "issues": "Long fallback: 17 chars; Generic numbered name"}, "NG1276": {"action": "DEFER", "reason": "Auto-generato", "symbol": "⡡", "score": "95.0", "issues": "Long fallback: 18 chars; Generic numbered name"}, "NG1278": {"action": "DEFER", "reason": "Auto-generato", "symbol": "⣙", "score": "95.0", "issues": "Long fallback: 20 chars; Generic numbered name"}, "NG1281": {"action": "DEFER", "reason": "Auto-generato", "symbol": "⪾", "score": "95.0", "issues": "Long fallback: 20 chars; Generic numbered name"}, "NG1288": {"action": "DEFER", "reason": "Auto-generato", "symbol": "⦷", "score": "95.0", "issues": "Long fallback: 19 chars; Generic numbered name"}, "NG1295": {"action": "DEFER", "reason": "Auto-generato", "symbol": "⯊", "score": "95.0", "issues": "Long fallback: 13 chars; Generic numbered name"}, "NG1304": {"action": "DEFER", "reason": "Auto-generato", "symbol": "⠋", "score": "95.0", "issues": "Long fallback: 17 chars; Generic numbered name"}, "NG1312": {"action": "DEFER", "reason": "Auto-generato", "symbol": "⦏", "score": "95.0", "issues": "Long fallback: 20 chars; Generic numbered name"}, "NG1318": {"action": "DEFER", "reason": "Auto-generato", "symbol": "⢳", "score": "95.0", "issues": "Long fallback: 18 chars; Generic numbered name"}, "NG1321": {"action": "DEFER", "reason": "Auto-generato", "symbol": "⫵", "score": "95.0", "issues": "Long fallback: 20 chars; Generic numbered name"}, "NG1322": {"action": "DEFER", "reason": "Auto-generato", "symbol": "❡", "score": "95.0", "issues": "Long fallback: 20 chars; Generic numbered name"}, "NG1325": {"action": "DEFER", "reason": "Auto-generato", "symbol": "⩆", "score": "95.0", "issues": "Long fallback: 20 chars; Generic numbered name"}, "NG1326": {"action": "DEFER", "reason": "Auto-generato", "symbol": "❃", "score": "95.0", "issues": "Long fallback: 20 chars; Generic numbered name"}, "NG1327": {"action": "DEFER", "reason": "Auto-generato", "symbol": "❀", "score": "95.0", "issues": "Long fallback: 20 chars; Generic numbered name"}, "NG1329": {"action": "DEFER", "reason": "Auto-generato", "symbol": "⢩", "score": "95.0", "issues": "Long fallback: 20 chars; Generic numbered name"}, "NG1331": {"action": "DEFER", "reason": "Auto-generato", "symbol": "⮢", "score": "95.0", "issues": "Long fallback: 20 chars; Generic numbered name"}, "NG1332": {"action": "DEFER", "reason": "Auto-generato", "symbol": "⡾", "score": "95.0", "issues": "Long fallback: 20 chars; Generic numbered name"}, "NG1334": {"action": "DEFER", "reason": "Auto-generato", "symbol": "⤦", "score": "95.0", "issues": "Long fallback: 20 chars; Generic numbered name"}, "NG1336": {"action": "DEFER", "reason": "Auto-generato", "symbol": "✦", "score": "95.0", "issues": "Long fallback: 20 chars; Generic numbered name"}, "NG1341": {"action": "DEFER", "reason": "Auto-generato", "symbol": "⥎", "score": "95.0", "issues": "Long fallback: 19 chars; Generic numbered name"}, "NG1344": {"action": "DEFER", "reason": "Auto-generato", "symbol": "⦑", "score": "95.0", "issues": "Long fallback: 19 chars; Generic numbered name"}, "NG1347": {"action": "DEFER", "reason": "Auto-generato", "symbol": "⨿", "score": "95.0", "issues": "Long fallback: 20 chars; Generic numbered name"}, "NG1348": {"action": "DEFER", "reason": "Auto-generato", "symbol": "❓", "score": "95.0", "issues": "Long fallback: 19 chars; Generic numbered name"}, "NG1352": {"action": "DEFER", "reason": "Auto-generato", "symbol": "⮕", "score": "95.0", "issues": "Long fallback: 19 chars; Generic numbered name"}, "NG1354": {"action": "DEFER", "reason": "Auto-generato", "symbol": "⪧", "score": "95.0", "issues": "Long fallback: 19 chars; Generic numbered name"}, "NG1356": {"action": "DEFER", "reason": "Auto-generato", "symbol": "⭑", "score": "95.0", "issues": "Long fallback: 19 chars; Generic numbered name"}, "NG1358": {"action": "REPLACE_UNICODE", "reason": "Unicode problematico: emoji_extended; Auto-generato", "symbol": "🢀", "score": "95.0", "issues": "Risky Unicode range: emoji_extended"}, "NG1361": {"action": "REPLACE_UNICODE", "reason": "Unicode problematico: emoji_extended; Auto-generato", "symbol": "🜚", "score": "95.0", "issues": "Risky Unicode range: emoji_extended"}, "NG1363": {"action": "REPLACE_UNICODE", "reason": "Unicode problematico: emoji_extended; Auto-generato", "symbol": "🜶", "score": "95.0", "issues": "Risky Unicode range: emoji_extended"}, "NG1367": {"action": "DEFER", "reason": "Auto-generato", "symbol": "⥰", "score": "95.0", "issues": "Long fallback: 20 chars; Generic numbered name"}, "NG1374": {"action": "DEFER", "reason": "Auto-generato", "symbol": "⦨", "score": "95.0", "issues": "Long fallback: 16 chars; Generic numbered name"}, "NG1379": {"action": "DEFER", "reason": "Auto-generato", "symbol": "✃", "score": "95.0", "issues": "Long fallback: 17 chars; Generic numbered name"}, "NG1384": {"action": "DEFER", "reason": "Auto-generato", "symbol": "⭸", "score": "95.0", "issues": "Long fallback: 20 chars; Generic numbered name"}, "NG1385": {"action": "DEFER", "reason": "Auto-generato", "symbol": "⭤", "score": "95.0", "issues": "Long fallback: 20 chars; Generic numbered name"}, "NG1390": {"action": "DEFER", "reason": "Auto-generato", "symbol": "✹", "score": "95.0", "issues": "Long fallback: 19 chars; Generic numbered name"}, "NG1391": {"action": "DEFER", "reason": "Auto-generato", "symbol": "⤓", "score": "95.0", "issues": "Long fallback: 19 chars; Generic numbered name"}, "NG1395": {"action": "DEFER", "reason": "Auto-generato", "symbol": "⫴", "score": "95.0", "issues": "Long fallback: 20 chars; Generic numbered name"}, "NG1397": {"action": "DEFER", "reason": "Auto-generato", "symbol": "⩖", "score": "95.0", "issues": "Long fallback: 20 chars; Generic numbered name"}, "NG1398": {"action": "DEFER", "reason": "Auto-generato", "symbol": "⥛", "score": "95.0", "issues": "Long fallback: 17 chars; Generic numbered name"}, "NG1404": {"action": "DEFER", "reason": "Auto-generato", "symbol": "⥀", "score": "95.0", "issues": "Long fallback: 20 chars; Generic numbered name"}, "NG1405": {"action": "DEFER", "reason": "Auto-generato", "symbol": "⧎", "score": "95.0", "issues": "Long fallback: 20 chars; Generic numbered name"}, "NG1408": {"action": "DEFER", "reason": "Auto-generato", "symbol": "⥶", "score": "95.0", "issues": "Long fallback: 20 chars; Generic numbered name"}, "NG1413": {"action": "DEFER", "reason": "Auto-generato", "symbol": "✌", "score": "95.0", "issues": "Long fallback: 19 chars; Generic numbered name"}, "NG1415": {"action": "DEFER", "reason": "Auto-generato", "symbol": "⥒", "score": "95.0", "issues": "Long fallback: 19 chars; Generic numbered name"}, "NG1417": {"action": "DEFER", "reason": "Auto-generato", "symbol": "⬛", "score": "95.0", "issues": "Long fallback: 16 chars; Generic numbered name"}, "NG1419": {"action": "DEFER", "reason": "Auto-generato", "symbol": "➪", "score": "95.0", "issues": "Long fallback: 16 chars; Generic numbered name"}, "NG1427": {"action": "DEFER", "reason": "Auto-generato", "symbol": "⥡", "score": "95.0", "issues": "Long fallback: 20 chars; Generic numbered name"}, "NG1436": {"action": "DEFER", "reason": "Auto-generato", "symbol": "⮺", "score": "95.0", "issues": "Long fallback: 14 chars; Generic numbered name"}, "NG1438": {"action": "DEFER", "reason": "Auto-generato", "symbol": "⦍", "score": "95.0", "issues": "Long fallback: 20 chars; Generic numbered name"}, "NG1440": {"action": "DEFER", "reason": "Auto-generato", "symbol": "⥕", "score": "95.0", "issues": "Long fallback: 20 chars; Generic numbered name"}, "NG1442": {"action": "DEFER", "reason": "Auto-generato", "symbol": "⭪", "score": "95.0", "issues": "Long fallback: 20 chars; Generic numbered name"}, "NG1447": {"action": "REPLACE_UNICODE", "reason": "Unicode problematico: emoji_extended; Auto-generato", "symbol": "😾", "score": "95.0", "issues": "Risky Unicode range: emoji_extended"}, "NG1448": {"action": "REPLACE_UNICODE", "reason": "Unicode problematico: emoji_extended; Auto-generato", "symbol": "🗙", "score": "95.0", "issues": "Risky Unicode range: emoji_extended"}, "NG1457": {"action": "DEFER", "reason": "Auto-generato", "symbol": "⩞", "score": "95.0", "issues": "Long fallback: 19 chars; Generic numbered name"}, "NG1464": {"action": "DEFER", "reason": "Auto-generato", "symbol": "⡧", "score": "95.0", "issues": "Long fallback: 20 chars; Generic numbered name"}, "NG1465": {"action": "DEFER", "reason": "Auto-generato", "symbol": "⪁", "score": "95.0", "issues": "Long fallback: 20 chars; Generic numbered name"}, "NG1469": {"action": "DEFER", "reason": "Auto-generato", "symbol": "✔", "score": "95.0", "issues": "Long fallback: 20 chars; Generic numbered name"}, "NG1474": {"action": "DEFER", "reason": "Auto-generato", "symbol": "⡠", "score": "95.0", "issues": "Long fallback: 17 chars; Generic numbered name"}, "NG1480": {"action": "DEFER", "reason": "Auto-generato", "symbol": "⦰", "score": "95.0", "issues": "Long fallback: 18 chars; Generic numbered name"}, "NG1484": {"action": "DEFER", "reason": "Auto-generato", "symbol": "⨐", "score": "95.0", "issues": "Long fallback: 18 chars; Generic numbered name"}, "NG1486": {"action": "DEFER", "reason": "Auto-generato", "symbol": "⨲", "score": "95.0", "issues": "Long fallback: 20 chars; Generic numbered name"}, "NG1490": {"action": "DEFER", "reason": "Auto-generato", "symbol": "✭", "score": "95.0", "issues": "Long fallback: 20 chars; Generic numbered name"}, "NG1491": {"action": "DEFER", "reason": "Auto-generato", "symbol": "❝", "score": "95.0", "issues": "Long fallback: 18 chars; Generic numbered name"}, "NG1492": {"action": "DEFER", "reason": "Auto-generato", "symbol": "⥪", "score": "95.0", "issues": "Long fallback: 20 chars; Generic numbered name"}, "NG1496": {"action": "REPLACE_UNICODE", "reason": "Unicode problematico: emoji_extended; Auto-generato", "symbol": "🛵", "score": "95.0", "issues": "Risky Unicode range: emoji_extended"}, "NG1497": {"action": "REPLACE_UNICODE", "reason": "Unicode problematico: emoji_extended; Auto-generato", "symbol": "🤬", "score": "95.0", "issues": "Risky Unicode range: emoji_extended"}, "NG1498": {"action": "REPLACE_UNICODE", "reason": "Unicode problematico: emoji_extended; Auto-generato", "symbol": "🞄", "score": "95.0", "issues": "Risky Unicode range: emoji_extended"}, "NG1500": {"action": "REPLACE_UNICODE", "reason": "Unicode problematico: emoji_extended; Auto-generato", "symbol": "🝭", "score": "95.0", "issues": "Risky Unicode range: emoji_extended"}, "NG1504": {"action": "DEFER", "reason": "Auto-generato", "symbol": "⤖", "score": "95.0", "issues": "Long fallback: 11 chars; Generic numbered name"}, "NG1509": {"action": "DEFER", "reason": "Auto-generato", "symbol": "⦜", "score": "95.0", "issues": "Long fallback: 15 chars; Generic numbered name"}, "NG1510": {"action": "DEFER", "reason": "Auto-generato", "symbol": "➚", "score": "95.0", "issues": "Long fallback: 15 chars; Generic numbered name"}, "NG1513": {"action": "DEFER", "reason": "Auto-generato", "symbol": "⢉", "score": "95.0", "issues": "Long fallback: 14 chars; Generic numbered name"}, "NG1517": {"action": "REPLACE_UNICODE", "reason": "Unicode problematico: emoji_extended; Auto-generato", "symbol": "🍳", "score": "95.0", "issues": "Risky Unicode range: emoji_extended"}, "NG1518": {"action": "DEFER", "reason": "Auto-generato", "symbol": "⫲", "score": "95.0", "issues": "Long fallback: 13 chars; Generic numbered name"}, "NG1522": {"action": "DEFER", "reason": "Auto-generato", "symbol": "➿", "score": "95.0", "issues": "Long fallback: 15 chars; Generic numbered name"}, "NG1525": {"action": "REPLACE_UNICODE", "reason": "Unicode problematico: emoji_extended; Auto-generato", "symbol": "🦌", "score": "95.0", "issues": "Risky Unicode range: emoji_extended"}, "NG1526": {"action": "REPLACE_UNICODE", "reason": "Unicode problematico: emoji_extended; Auto-generato", "symbol": "🙆", "score": "95.0", "issues": "Risky Unicode range: emoji_extended"}, "NG1534": {"action": "DEFER", "reason": "Auto-generato", "symbol": "⣳", "score": "95.0", "issues": "Long fallback: 14 chars; Generic numbered name"}, "NG1536": {"action": "DEFER", "reason": "Auto-generato", "symbol": "✻", "score": "95.0", "issues": "Long fallback: 12 chars; Generic numbered name"}, "NG1537": {"action": "DEFER", "reason": "Auto-generato", "symbol": "⡦", "score": "95.0", "issues": "Long fallback: 12 chars; Generic numbered name"}, "NG1548": {"action": "DEFER", "reason": "Auto-generato", "symbol": "⡍", "score": "95.0", "issues": "Long fallback: 18 chars; Generic numbered name"}, "NG1552": {"action": "DEFER", "reason": "Auto-generato", "symbol": "⦾", "score": "95.0", "issues": "Long fallback: 19 chars; Generic numbered name"}, "NG1555": {"action": "DEFER", "reason": "Auto-generato", "symbol": "𝝯", "score": "95.0", "issues": "Long fallback: 17 chars; Generic numbered name"}, "NG1556": {"action": "DEFER", "reason": "Auto-generato", "symbol": "⯓", "score": "95.0", "issues": "Long fallback: 17 chars; Generic numbered name"}, "NG1562": {"action": "DEFER", "reason": "Auto-generato", "symbol": "⣉", "score": "95.0", "issues": "Long fallback: 20 chars; Generic numbered name"}, "NG1565": {"action": "DEFER", "reason": "Auto-generato", "symbol": "⥟", "score": "95.0", "issues": "Long fallback: 20 chars; Generic numbered name"}, "NG1566": {"action": "DEFER", "reason": "Auto-generato", "symbol": "⣿", "score": "95.0", "issues": "Long fallback: 20 chars; Generic numbered name"}, "NG1571": {"action": "DEFER", "reason": "Auto-generato", "symbol": "⩈", "score": "95.0", "issues": "Long fallback: 20 chars; Generic numbered name"}, "NG1573": {"action": "DEFER", "reason": "Auto-generato", "symbol": "⡲", "score": "95.0", "issues": "Long fallback: 20 chars; Generic numbered name"}, "NG1577": {"action": "REPLACE_UNICODE", "reason": "Unicode problematico: emoji_extended; Auto-generato", "symbol": "🛪", "score": "95.0", "issues": "Risky Unicode range: emoji_extended"}, "NG1580": {"action": "DEFER", "reason": "Auto-generato", "symbol": "⭟", "score": "95.0", "issues": "Long fallback: 12 chars; Generic numbered name"}, "NG1587": {"action": "DEFER", "reason": "Auto-generato", "symbol": "⧌", "score": "95.0", "issues": "Long fallback: 18 chars; Generic numbered name"}, "NG1598": {"action": "DEFER", "reason": "Auto-generato", "symbol": "⦧", "score": "95.0", "issues": "Long fallback: 19 chars; Generic numbered name"}, "NG1603": {"action": "DEFER", "reason": "Auto-generato", "symbol": "⡃", "score": "95.0", "issues": "Long fallback: 19 chars; Generic numbered name"}, "NG1604": {"action": "DEFER", "reason": "Auto-generato", "symbol": "⯎", "score": "95.0", "issues": "Long fallback: 20 chars; Generic numbered name"}, "NG1607": {"action": "DEFER", "reason": "Auto-generato", "symbol": "⫨", "score": "95.0", "issues": "Long fallback: 19 chars; Generic numbered name"}, "NG1609": {"action": "DEFER", "reason": "Auto-generato", "symbol": "❙", "score": "95.0", "issues": "Long fallback: 19 chars; Generic numbered name"}, "NG1610": {"action": "DEFER", "reason": "Auto-generato", "symbol": "⯺", "score": "95.0", "issues": "Long fallback: 19 chars; Generic numbered name"}, "NG1615": {"action": "DEFER", "reason": "Auto-generato", "symbol": "⩨", "score": "95.0", "issues": "Long fallback: 20 chars; Generic numbered name"}, "NG1617": {"action": "DEFER", "reason": "Auto-generato", "symbol": "⫝", "score": "95.0", "issues": "Long fallback: 20 chars; Generic numbered name"}, "NG1622": {"action": "DEFER", "reason": "Auto-generato", "symbol": "⫯", "score": "95.0", "issues": "Long fallback: 19 chars; Generic numbered name"}, "NG1623": {"action": "DEFER", "reason": "Auto-generato", "symbol": "⠥", "score": "95.0", "issues": "Long fallback: 19 chars; Generic numbered name"}, "NG1627": {"action": "DEFER", "reason": "Auto-generato", "symbol": "⠆", "score": "95.0", "issues": "Long fallback: 19 chars; Generic numbered name"}, "NG1629": {"action": "DEFER", "reason": "Auto-generato", "symbol": "⮡", "score": "95.0", "issues": "Long fallback: 20 chars; Generic numbered name"}, "NG1630": {"action": "DEFER", "reason": "Auto-generato", "symbol": "⨏", "score": "95.0", "issues": "Long fallback: 19 chars; Generic numbered name"}, "NG1632": {"action": "DEFER", "reason": "Auto-generato", "symbol": "⩎", "score": "95.0", "issues": "Long fallback: 19 chars; Generic numbered name"}, "NG1640": {"action": "DEFER", "reason": "Auto-generato", "symbol": "⭷", "score": "95.0", "issues": "Long fallback: 19 chars; Generic numbered name"}, "NG1642": {"action": "DEFER", "reason": "Auto-generato", "symbol": "⡢", "score": "95.0", "issues": "Long fallback: 17 chars; Generic numbered name"}, "NG1643": {"action": "DEFER", "reason": "Auto-generato", "symbol": "❉", "score": "95.0", "issues": "Long fallback: 19 chars; Generic numbered name"}, "NG1647": {"action": "DEFER", "reason": "Auto-generato", "symbol": "⬀", "score": "95.0", "issues": "Long fallback: 19 chars; Generic numbered name"}, "NG1651": {"action": "DEFER", "reason": "Auto-generato", "symbol": "⬒", "score": "95.0", "issues": "Long fallback: 20 chars; Generic numbered name"}, "NG1652": {"action": "DEFER", "reason": "Auto-generato", "symbol": "⯥", "score": "95.0", "issues": "Long fallback: 20 chars; Generic numbered name"}, "NG1653": {"action": "DEFER", "reason": "Auto-generato", "symbol": "❆", "score": "95.0", "issues": "Long fallback: 18 chars; Generic numbered name"}, "NG1659": {"action": "DEFER", "reason": "Auto-generato", "symbol": "⬕", "score": "95.0", "issues": "Long fallback: 15 chars; Generic numbered name"}, "NG1666": {"action": "DEFER", "reason": "Auto-generato", "symbol": "⤩", "score": "95.0", "issues": "Long fallback: 15 chars; Generic numbered name"}, "NG1668": {"action": "DEFER", "reason": "Auto-generato", "symbol": "⠼", "score": "95.0", "issues": "Long fallback: 15 chars; Generic numbered name"}, "NG1681": {"action": "DEFER", "reason": "Auto-generato", "symbol": "✀", "score": "95.0", "issues": "Long fallback: 18 chars; Generic numbered name"}, "NG1682": {"action": "DEFER", "reason": "Auto-generato", "symbol": "⩛", "score": "95.0", "issues": "Long fallback: 19 chars; Generic numbered name"}, "NG1684": {"action": "DEFER", "reason": "Auto-generato", "symbol": "⪌", "score": "95.0", "issues": "Long fallback: 17 chars; Generic numbered name"}, "NG1691": {"action": "DEFER", "reason": "Auto-generato", "symbol": "⦥", "score": "95.0", "issues": "Long fallback: 18 chars; Generic numbered name"}, "NG1694": {"action": "DEFER", "reason": "Auto-generato", "symbol": "⩰", "score": "95.0", "issues": "Long fallback: 20 chars; Generic numbered name"}, "NG1695": {"action": "DEFER", "reason": "Auto-generato", "symbol": "⦘", "score": "95.0", "issues": "Long fallback: 18 chars; Generic numbered name"}, "NG1696": {"action": "DEFER", "reason": "Auto-generato", "symbol": "⣶", "score": "95.0", "issues": "Long fallback: 20 chars; Generic numbered name"}, "NG1698": {"action": "DEFER", "reason": "Auto-generato", "symbol": "⥄", "score": "95.0", "issues": "Long fallback: 18 chars; Generic numbered name"}, "NG1701": {"action": "DEFER", "reason": "Auto-generato", "symbol": "❂", "score": "95.0", "issues": "Long fallback: 18 chars; Generic numbered name"}, "NG1712": {"action": "DEFER", "reason": "Auto-generato", "symbol": "⢿", "score": "95.0", "issues": "Long fallback: 18 chars; Generic numbered name"}, "NG1714": {"action": "DEFER", "reason": "Auto-generato", "symbol": "⪋", "score": "95.0", "issues": "Long fallback: 20 chars; Generic numbered name"}, "NG1716": {"action": "DEFER", "reason": "Auto-generato", "symbol": "✙", "score": "95.0", "issues": "Long fallback: 20 chars; Generic numbered name"}, "NG1717": {"action": "DEFER", "reason": "Auto-generato", "symbol": "⤱", "score": "95.0", "issues": "Long fallback: 20 chars; Generic numbered name"}, "NG1719": {"action": "DEFER", "reason": "Auto-generato", "symbol": "➽", "score": "95.0", "issues": "Long fallback: 18 chars; Generic numbered name"}, "NG1721": {"action": "DEFER", "reason": "Auto-generato", "symbol": "⪄", "score": "95.0", "issues": "Long fallback: 20 chars; Generic numbered name"}, "NG1722": {"action": "DEFER", "reason": "Auto-generato", "symbol": "⮳", "score": "95.0", "issues": "Long fallback: 18 chars; Generic numbered name"}, "NG1724": {"action": "DEFER", "reason": "Auto-generato", "symbol": "⯕", "score": "95.0", "issues": "Long fallback: 20 chars; Generic numbered name"}, "NG1727": {"action": "DEFER", "reason": "Auto-generato", "symbol": "⤏", "score": "95.0", "issues": "Long fallback: 20 chars; Generic numbered name"}, "NG1728": {"action": "DEFER", "reason": "Auto-generato", "symbol": "⫮", "score": "95.0", "issues": "Long fallback: 20 chars; Generic numbered name"}, "NG1730": {"action": "DEFER", "reason": "Auto-generato", "symbol": "⠧", "score": "95.0", "issues": "Long fallback: 18 chars; Generic numbered name"}, "NG1737": {"action": "DEFER", "reason": "Auto-generato", "symbol": "⧑", "score": "95.0", "issues": "Long fallback: 19 chars; Generic numbered name"}, "NG1740": {"action": "DEFER", "reason": "Auto-generato", "symbol": "⬞", "score": "95.0", "issues": "Long fallback: 19 chars; Generic numbered name"}, "NG1746": {"action": "DEFER", "reason": "Auto-generato", "symbol": "⥵", "score": "95.0", "issues": "Long fallback: 19 chars; Generic numbered name"}, "NG1749": {"action": "DEFER", "reason": "Auto-generato", "symbol": "⩝", "score": "95.0", "issues": "Long fallback: 19 chars; Generic numbered name"}, "NG1762": {"action": "DEFER", "reason": "Auto-generato", "symbol": "⤠", "score": "95.0", "issues": "Long fallback: 19 chars; Generic numbered name"}, "NG1764": {"action": "DEFER", "reason": "Auto-generato", "symbol": "⮪", "score": "95.0", "issues": "Long fallback: 19 chars; Generic numbered name"}, "NG1767": {"action": "DEFER", "reason": "Auto-generato", "symbol": "⭰", "score": "95.0", "issues": "Long fallback: 19 chars; Generic numbered name"}, "NG1768": {"action": "DEFER", "reason": "Auto-generato", "symbol": "⥁", "score": "95.0", "issues": "Long fallback: 19 chars; Generic numbered name"}, "NG1769": {"action": "DEFER", "reason": "Auto-generato", "symbol": "⩭", "score": "95.0", "issues": "Long fallback: 19 chars; Generic numbered name"}, "NG1770": {"action": "DEFER", "reason": "Auto-generato", "symbol": "⦻", "score": "95.0", "issues": "Long fallback: 19 chars; Generic numbered name"}, "NG1771": {"action": "DEFER", "reason": "Auto-generato", "symbol": "⨓", "score": "95.0", "issues": "Long fallback: 19 chars; Generic numbered name"}, "NG1773": {"action": "DEFER", "reason": "Auto-generato", "symbol": "⭲", "score": "95.0", "issues": "Long fallback: 19 chars; Generic numbered name"}, "NG1775": {"action": "DEFER", "reason": "Auto-generato", "symbol": "✮", "score": "95.0", "issues": "Long fallback: 19 chars; Generic numbered name"}, "NG1776": {"action": "DEFER", "reason": "Auto-generato", "symbol": "⬁", "score": "95.0", "issues": "Long fallback: 19 chars; Generic numbered name"}, "NG1777": {"action": "DEFER", "reason": "Auto-generato", "symbol": "⥽", "score": "95.0", "issues": "Long fallback: 19 chars; Generic numbered name"}, "NG1784": {"action": "REPLACE_UNICODE", "reason": "Unicode problematico: emoji_extended; Auto-generato", "symbol": "🞉", "score": "95.0", "issues": "Risky Unicode range: emoji_extended"}, "NG1789": {"action": "DEFER", "reason": "Auto-generato", "symbol": "⩩", "score": "95.0", "issues": "Long fallback: 20 chars; Generic numbered name"}, "NG1790": {"action": "DEFER", "reason": "Auto-generato", "symbol": "⧼", "score": "95.0", "issues": "Long fallback: 20 chars; Generic numbered name"}, "NG1791": {"action": "DEFER", "reason": "Auto-generato", "symbol": "⢬", "score": "95.0", "issues": "Long fallback: 20 chars; Generic numbered name"}, "NG1792": {"action": "DEFER", "reason": "Auto-generato", "symbol": "⧕", "score": "95.0", "issues": "Long fallback: 20 chars; Generic numbered name"}, "NG1801": {"action": "DEFER", "reason": "Auto-generato", "symbol": "❮", "score": "95.0", "issues": "Long fallback: 20 chars; Generic numbered name"}, "NG1805": {"action": "DEFER", "reason": "Auto-generato", "symbol": "⣁", "score": "95.0", "issues": "Long fallback: 19 chars; Generic numbered name"}, "NG1811": {"action": "DEFER", "reason": "Auto-generato", "symbol": "❰", "score": "95.0", "issues": "Long fallback: 19 chars; Generic numbered name"}, "NG1812": {"action": "DEFER", "reason": "Auto-generato", "symbol": "⩐", "score": "95.0", "issues": "Long fallback: 19 chars; Generic numbered name"}, "NG1817": {"action": "DEFER", "reason": "Auto-generato", "symbol": "⨑", "score": "95.0", "issues": "Long fallback: 19 chars; Generic numbered name"}, "NG1821": {"action": "DEFER", "reason": "Auto-generato", "symbol": "⭅", "score": "95.0", "issues": "Long fallback: 20 chars; Generic numbered name"}, "NG1822": {"action": "DEFER", "reason": "Auto-generato", "symbol": "⤇", "score": "95.0", "issues": "Long fallback: 18 chars; Generic numbered name"}, "NG1827": {"action": "DEFER", "reason": "Auto-generato", "symbol": "⠲", "score": "95.0", "issues": "Long fallback: 20 chars; Generic numbered name"}, "NG1830": {"action": "DEFER", "reason": "Auto-generato", "symbol": "⣕", "score": "95.0", "issues": "Long fallback: 20 chars; Generic numbered name"}, "NG1831": {"action": "DEFER", "reason": "Auto-generato", "symbol": "⥲", "score": "95.0", "issues": "Long fallback: 20 chars; Generic numbered name"}, "NG1832": {"action": "DEFER", "reason": "Auto-generato", "symbol": "⥉", "score": "95.0", "issues": "Long fallback: 20 chars; Generic numbered name"}, "NG1834": {"action": "DEFER", "reason": "Auto-generato", "symbol": "⮌", "score": "95.0", "issues": "Long fallback: 20 chars; Generic numbered name"}, "NG1838": {"action": "DEFER", "reason": "Auto-generato", "symbol": "⫶", "score": "95.0", "issues": "Long fallback: 18 chars; Generic numbered name"}, "NG1839": {"action": "DEFER", "reason": "Auto-generato", "symbol": "⤲", "score": "95.0", "issues": "Long fallback: 18 chars; Generic numbered name"}, "NG1841": {"action": "DEFER", "reason": "Auto-generato", "symbol": "⮰", "score": "95.0", "issues": "Long fallback: 20 chars; Generic numbered name"}, "NG1856": {"action": "DEFER", "reason": "Auto-generato", "symbol": "⤯", "score": "95.0", "issues": "Long fallback: 14 chars; Generic numbered name"}, "NG1865": {"action": "DEFER", "reason": "Auto-generato", "symbol": "✬", "score": "95.0", "issues": "Long fallback: 20 chars; Generic numbered name"}, "NG1866": {"action": "DEFER", "reason": "Auto-generato", "symbol": "⢫", "score": "95.0", "issues": "Long fallback: 20 chars; Generic numbered name"}, "NG1869": {"action": "DEFER", "reason": "Auto-generato", "symbol": "➱", "score": "95.0", "issues": "Long fallback: 20 chars; Generic numbered name"}, "NG1870": {"action": "DEFER", "reason": "Auto-generato", "symbol": "⪙", "score": "95.0", "issues": "Long fallback: 20 chars; Generic numbered name"}, "NG1877": {"action": "DEFER", "reason": "Auto-generato", "symbol": "⧈", "score": "95.0", "issues": "Long fallback: 19 chars; Generic numbered name"}, "NG1878": {"action": "DEFER", "reason": "Auto-generato", "symbol": "⮉", "score": "95.0", "issues": "Long fallback: 19 chars; Generic numbered name"}, "NG1879": {"action": "REPLACE_UNICODE", "reason": "Unicode problematico: emoji_extended; Auto-generato", "symbol": "🢰", "score": "95.0", "issues": "Risky Unicode range: emoji_extended"}, "NG1891": {"action": "DEFER", "reason": "Auto-generato", "symbol": "➰", "score": "95.0", "issues": "Long fallback: 18 chars; Generic numbered name"}, "NG1895": {"action": "DEFER", "reason": "Auto-generato", "symbol": "⦬", "score": "95.0", "issues": "Long fallback: 18 chars; Generic numbered name"}, "NG1903": {"action": "DEFER", "reason": "Auto-generato", "symbol": "❜", "score": "95.0", "issues": "Long fallback: 16 chars; Generic numbered name"}, "NG1909": {"action": "DEFER", "reason": "Auto-generato", "symbol": "⨧", "score": "95.0", "issues": "Long fallback: 16 chars; Generic numbered name"}, "NG1919": {"action": "DEFER", "reason": "Auto-generato", "symbol": "➫", "score": "95.0", "issues": "Long fallback: 15 chars; Generic numbered name"}, "NG1923": {"action": "DEFER", "reason": "Auto-generato", "symbol": "✤", "score": "95.0", "issues": "Long fallback: 19 chars; Generic numbered name"}, "NG1931": {"action": "DEFER", "reason": "Auto-generato", "symbol": "⢸", "score": "95.0", "issues": "Long fallback: 20 chars; Generic numbered name"}, "NG1932": {"action": "DEFER", "reason": "Auto-generato", "symbol": "⨯", "score": "95.0", "issues": "Long fallback: 20 chars; Generic numbered name"}, "NG1933": {"action": "DEFER", "reason": "Auto-generato", "symbol": "⢮", "score": "95.0", "issues": "Long fallback: 18 chars; Generic numbered name"}, "NG1934": {"action": "DEFER", "reason": "Auto-generato", "symbol": "⮲", "score": "95.0", "issues": "Long fallback: 20 chars; Generic numbered name"}, "NG1943": {"action": "DEFER", "reason": "Auto-generato", "symbol": "❋", "score": "95.0", "issues": "Long fallback: 19 chars; Generic numbered name"}, "NG1945": {"action": "DEFER", "reason": "Auto-generato", "symbol": "⠠", "score": "95.0", "issues": "Long fallback: 17 chars; Generic numbered name"}, "NG1950": {"action": "DEFER", "reason": "Auto-generato", "symbol": "⡹", "score": "95.0", "issues": "Long fallback: 19 chars; Generic numbered name"}, "NG1954": {"action": "DEFER", "reason": "Auto-generato", "symbol": "⬺", "score": "95.0", "issues": "Long fallback: 19 chars; Generic numbered name"}, "NG1955": {"action": "DEFER", "reason": "Auto-generato", "symbol": "⠚", "score": "95.0", "issues": "Long fallback: 19 chars; Generic numbered name"}, "NG1956": {"action": "DEFER", "reason": "Auto-generato", "symbol": "⣭", "score": "95.0", "issues": "Long fallback: 19 chars; Generic numbered name"}, "NG1959": {"action": "DEFER", "reason": "Auto-generato", "symbol": "❁", "score": "95.0", "issues": "Long fallback: 20 chars; Generic numbered name"}, "NG1965": {"action": "REPLACE_UNICODE", "reason": "Unicode problematico: emoji_extended; Auto-generato", "symbol": "🍿", "score": "95.0", "issues": "Risky Unicode range: emoji_extended"}, "NG1968": {"action": "REPLACE_UNICODE", "reason": "Unicode problematico: emoji_extended; Auto-generato", "symbol": "🠛", "score": "95.0", "issues": "Risky Unicode range: emoji_extended"}, "NG1982": {"action": "DEFER", "reason": "Auto-generato", "symbol": "⠻", "score": "95.0", "issues": "Long fallback: 20 chars; Generic numbered name"}, "NG1983": {"action": "DEFER", "reason": "Auto-generato", "symbol": "⭯", "score": "95.0", "issues": "Long fallback: 18 chars; Generic numbered name"}, "NG1985": {"action": "DEFER", "reason": "Auto-generato", "symbol": "⯶", "score": "95.0", "issues": "Long fallback: 18 chars; Generic numbered name"}, "NG1989": {"action": "DEFER", "reason": "Auto-generato", "symbol": "➮", "score": "95.0", "issues": "Long fallback: 20 chars; Generic numbered name"}, "NG1993": {"action": "DEFER", "reason": "Auto-generato", "symbol": "✊", "score": "95.0", "issues": "Long fallback: 20 chars; Generic numbered name"}, "NG1998": {"action": "DEFER", "reason": "Auto-generato", "symbol": "⮣", "score": "95.0", "issues": "Long fallback: 20 chars; Generic numbered name"}, "NG2007": {"action": "DEFER", "reason": "Auto-generato", "symbol": "⤤", "score": "95.0", "issues": "Long fallback: 20 chars; Generic numbered name"}, "NG2011": {"action": "DEFER", "reason": "Auto-generato", "symbol": "⭢", "score": "95.0", "issues": "Long fallback: 18 chars; Generic numbered name"}, "NG2015": {"action": "DEFER", "reason": "Auto-generato", "symbol": "⢋", "score": "95.0", "issues": "Long fallback: 20 chars; Generic numbered name"}, "NG2017": {"action": "DEFER", "reason": "Auto-generato", "symbol": "𝜕", "score": "95.0", "issues": "Long fallback: 18 chars; Generic numbered name"}, "NG2019": {"action": "DEFER", "reason": "Auto-generato", "symbol": "⬚", "score": "95.0", "issues": "Long fallback: 20 chars; Generic numbered name"}, "NG2020": {"action": "DEFER", "reason": "Auto-generato", "symbol": "⦦", "score": "95.0", "issues": "Long fallback: 20 chars; Generic numbered name"}, "NG2024": {"action": "DEFER", "reason": "Auto-generato", "symbol": "✰", "score": "95.0", "issues": "Long fallback: 20 chars; Generic numbered name"}, "NG2026": {"action": "DEFER", "reason": "Auto-generato", "symbol": "➭", "score": "95.0", "issues": "Long fallback: 20 chars; Generic numbered name"}, "NG2027": {"action": "DEFER", "reason": "Auto-generato", "symbol": "⥨", "score": "95.0", "issues": "Long fallback: 20 chars; Generic numbered name"}, "NG2029": {"action": "DEFER", "reason": "Auto-generato", "symbol": "⦟", "score": "95.0", "issues": "Long fallback: 18 chars; Generic numbered name"}, "NG2032": {"action": "DEFER", "reason": "Auto-generato", "symbol": "⠷", "score": "95.0", "issues": "Long fallback: 18 chars; Generic numbered name"}, "NG2034": {"action": "DEFER", "reason": "Auto-generato", "symbol": "⮏", "score": "95.0", "issues": "Long fallback: 19 chars; Generic numbered name"}, "NG2040": {"action": "DEFER", "reason": "Auto-generato", "symbol": "⬄", "score": "95.0", "issues": "Long fallback: 19 chars; Generic numbered name"}, "NG2042": {"action": "DEFER", "reason": "Auto-generato", "symbol": "⨽", "score": "95.0", "issues": "Long fallback: 19 chars; Generic numbered name"}, "NG2043": {"action": "DEFER", "reason": "Auto-generato", "symbol": "⩌", "score": "95.0", "issues": "Long fallback: 19 chars; Generic numbered name"}, "NG2044": {"action": "DEFER", "reason": "Auto-generato", "symbol": "⣷", "score": "95.0", "issues": "Long fallback: 19 chars; Generic numbered name"}, "NG2045": {"action": "DEFER", "reason": "Auto-generato", "symbol": "⯃", "score": "95.0", "issues": "Long fallback: 19 chars; Generic numbered name"}, "NG2048": {"action": "DEFER", "reason": "Auto-generato", "symbol": "⫾", "score": "95.0", "issues": "Long fallback: 19 chars; Generic numbered name"}}, "priority_order": ["NG0751", "NG0752", "NG0753", "NG0754", "NG0755", "NG0806", "NG0807", "NG0808", "NG0809", "NG0810", "NG0811", "NG0812", "NG0813", "NG0814", "NG0815", "NG0816", "NG0817", "NG0818", "NG0819", "NG0820", "NG0821", "NG0822", "NG0823", "NG0824", "NG0825", "NG0826", "NG0827", "NG0828", "NG0829", "NG0830", "NG0831", "NG0832", "NG0833", "NG0834", "NG0835", "NG0836", "NG0837", "NG0838", "NG0839", "NG0840", "NG0841", "NG0842", "NG0843", "NG0844", "NG0845", "NG0846", "NG0847", "NG0848", "NG0849", "NG0850"], "batch_actions": {"REMOVE": ["NG0568", "NG0569", "NG0570", "NG0571", "NG0572", "NG0573", "NG0574", "NG0575", "NG0576", "NG0577", "NG0578", "NG0580", "NG0581", "NG0583", "NG0584", "NG0585", "NG0587", "NG0588", "NG0589", "NG0590", "NG0591", "NG0592", "NG0593", "NG0594", "NG0596", "NG0598", "NG0599", "NG0600", "NG0603", "NG0605", "NG0606", "NG0607", "NG0608", "NG0609", "NG0610", "NG0613", "NG0615", "NG0616", "NG0617", "NG0621", "NG0623", "NG0624", "NG0627", "NG0628", "NG0630", "NG0631", "NG0632", "NG0633", "NG0638", "NG0639", "NG0640", "NG0642", "NG0645", "NG0649", "NG0653", "NG0654", "NG0655", "NG0661", "NG0662", "NG0671", "NG0678", "NG0683", "NG0685", "NG0579", "NG0582", "NG0586", "NG0674", "NG0675", "NG0676", "NG0677", "NG0595", "NG0686", "NG0687", "NG0688", "NG0689", "NG0690", "NG0691", "NG0692", "NG0693", "NG0694", "NG0695", "NG0696", "NG0697", "NG0698", "NG0699", "NG0700", "NG0701", "NG0702", "NG0703", "NG0704", "NG0705", "NG0706", "NG0707", "NG0708", "NG0709", "NG0710", "NG0711", "NG0712", "NG0713", "NG0714", "NG0715", "NG0716", "NG0717", "NG0718", "NG0719", "NG0720", "NG0721", "NG0722", "NG0723", "NG0724", "NG0725", "NG0726", "NG0727", "NG0728", "NG0729", "NG0730", "NG0731", "NG0732", "NG0733", "NG0734", "NG0735", "NG0736", "NG0737", "NG0738", "NG0739", "NG0740", "NG0741", "NG0742", "NG0743", "NG0744", "NG0745", "NG0746", "NG0747", "NG0748", "NG0749", "NG0750", "NG0751", "NG0752", "NG0753", "NG0754", "NG0755", "NG0756", "NG0757", "NG0758", "NG0759", "NG0760", "NG0761", "NG0762", "NG0763", "NG0764", "NG0765", "NG0766", "NG0767", "NG0768", "NG0769", "NG0770", "NG0771", "NG0772", "NG0773", "NG0774", "NG0775", "NG0776", "NG0777", "NG0778", "NG0779", "NG0780", "NG0781", "NG0782", "NG0783", "NG0784", "NG0785", "NG0786", "NG0787", "NG0788", "NG0789", "NG0790", "NG0791", "NG0792", "NG0793", "NG0794", "NG0795", "NG0796", "NG0797", "NG0798", "NG0799", "NG0800", "NG0801", "NG0802", "NG0803", "NG0804", "NG0805", "NG0806", "NG0807", "NG0808", "NG0809", "NG0810", "NG0811", "NG0812", "NG0813", "NG0814", "NG0815", "NG0816", "NG0817", "NG0818", "NG0819", "NG0820", "NG0821", "NG0822", "NG0823", "NG0824", "NG0825", "NG0826", "NG0827", "NG0828", "NG0829", "NG0830", "NG0831", "NG0832", "NG0833", "NG0834", "NG0835", "NG0836", "NG0837", "NG0838", "NG0839", "NG0840", "NG0841", "NG0842", "NG0843", "NG0844", "NG0845", "NG0846", "NG0847", "NG0848", "NG0849", "NG0850", "NG0851", "NG0852", "NG0853", "NG0854", "NG0855", "NG0856", "NG0857", "NG0858", "NG0859", "NG0860", "NG0861", "NG0862", "NG0863", "NG0864", "NG0865", "NG0866", "NG0867", "NG0868", "NG0869", "NG0870", "NG0871", "NG0872", "NG0873", "NG0874", "NG0875", "NG0876", "NG0877", "NG0878", "NG0879", "NG0880", "NG0881"], "REPLACE_UNICODE": ["NG0025", "NG0026", "NG0041", "NG0044", "NG0047", "NG0048", "NG0053", "NG0058", "NG0061", "NG0075", "NG0077", "NG0078", "NG0091", "NG0092", "NG0093", "NG0096", "NG0098", "NG0099", "NG0106", "NG0115", "NG0116", "NG0122", "NG0124", "NG0127", "NG0130", "NG0139", "NG0159", "NG0160", "NG0161", "NG0167", "NG0169", "NG0170", "NG0180", "NG0181", "NG0185", "NG0187", "NG0203", "NG0205", "NG0214", "NG0215", "NG0218", "NG0221", "NG0227", "NG0228", "NG0229", "NG0233", "NG0234", "NG0235", "NG0238", "NG0253", "NG0258", "NG0264", "NG0270", "NG0275", "NG0291", "NG0293", "NG0295", "NG0302", "NG0306", "NG0313", "NG0315", "NG0320", "NG0323", "NG0324", "NG0326", "NG0329", "NG0330", "NG0333", "NG0337", "NG0338", "NG0346", "NG0350", "NG0352", "NG0353", "NG0354", "NG0355", "NG0358", "NG0363", "NG0364", "NG0365", "NG0366", "NG0369", "NG0370", "NG0371", "NG0374", "NG0376", "NG0382", "NG0386", "NG0388", "NG0393", "NG0395", "NG0398", "NG0402", "NG0403", "NG0407", "NG0408", "NG0409", "NG0411", "NG0414", "NG0416", "NG0418", "NG0426", "NG0430", "NG0432", "NG0435", "NG0439", "NG0444", "NG0449", "NG0455", "NG0456", "NG0458", "NG0459", "NG0463", "NG0464", "NG0465", "NG0467", "NG0469", "NG0472", "NG0473", "NG0474", "NG0475", "NG0487", "NG0490", "NG0491", "NG0497", "NG0499", "NG0503", "NG0506", "NG0508", "NG0511", "NG0882", "NG0887", "NG0890", "NG0892", "NG0895", "NG0898", "NG0899", "NG0900", "NG0902", "NG0903", "NG0904", "NG0905", "NG0908", "NG0909", "NG0913", "NG0914", "NG0918", "NG0919", "NG0921", "NG0924", "NG0925", "NG0928", "NG0930", "NG0931", "NG0933", "NG0934", "NG0935", "NG0936", "NG0939", "NG0940", "NG0942", "NG0944", "NG0945", "NG0950", "NG0951", "NG0953", "NG0956", "NG0957", "NG0958", "NG0959", "NG0961", "NG0962", "NG0963", "NG0965", "NG0966", "NG0967", "NG0968", "NG0972", "NG0975", "NG0976", "NG0979", "NG0980", "NG0984", "NG0989", "NG0994", "NG0996", "NG0997", "NG0998", "NG0999", "NG1001", "NG1004", "NG1005", "NG1010", "NG1013", "NG1015", "NG1017", "NG1018", "NG1021", "NG1022", "NG1025", "NG1027", "NG1031", "NG1036", "NG1037", "NG1039", "NG1041", "NG1043", "NG1044", "NG1045", "NG1047", "NG1048", "NG1053", "NG1055", "NG1056", "NG1057", "NG1058", "NG1059", "NG1062", "NG1063", "NG1065", "NG1067", "NG1069", "NG1073", "NG1075", "NG1077", "NG1078", "NG1079", "NG1080", "NG1083", "NG1084", "NG1085", "NG1086", "NG1087", "NG1088", "NG1089", "NG1090", "NG1092", "NG1093", "NG1094", "NG1095", "NG1096", "NG1098", "NG1099", "NG1102", "NG1107", "NG1109", "NG1110", "NG1111", "NG1114", "NG1115", "NG1116", "NG1117", "NG1118", "NG1119", "NG1120", "NG1121", "NG1122", "NG1124", "NG1125", "NG1128", "NG1129", "NG1132", "NG1133", "NG1135", "NG1136", "NG1137", "NG1139", "NG1140", "NG1141", "NG1142", "NG1144", "NG1147", "NG1148", "NG1150", "NG1151", "NG1154", "NG1158", "NG1159", "NG1161", "NG1165", "NG1166", "NG1170", "NG1172", "NG1174", "NG1175", "NG1176", "NG1177", "NG1179", "NG1180", "NG1182", "NG1183", "NG1184", "NG1185", "NG1186", "NG1191", "NG1193", "NG1196", "NG1199", "NG1200", "NG1201", "NG1202", "NG1203", "NG1204", "NG1205", "NG1207", "NG1209", "NG1210", "NG1216", "NG1217", "NG1219", "NG1220", "NG1222", "NG1223", "NG1225", "NG1227", "NG1228", "NG1229", "NG1230", "NG1231", "NG1234", "NG1238", "NG1239", "NG1240", "NG1242", "NG1243", "NG1245", "NG1247", "NG1252", "NG1256", "NG1257", "NG1260", "NG1264", "NG1265", "NG1268", "NG1271", "NG1275", "NG1280", "NG1283", "NG1284", "NG1286", "NG1287", "NG1290", "NG1291", "NG1292", "NG1294", "NG1296", "NG1297", "NG1299", "NG1300", "NG1303", "NG1305", "NG1306", "NG1307", "NG1308", "NG1313", "NG1316", "NG1319", "NG1320", "NG1323", "NG1324", "NG1328", "NG1330", "NG1333", "NG1335", "NG1337", "NG1338", "NG1339", "NG1340", "NG1342", "NG1343", "NG1345", "NG1346", "NG1349", "NG1350", "NG1351", "NG1353", "NG1355", "NG1357", "NG1366", "NG1368", "NG1369", "NG1372", "NG1373", "NG1376", "NG1382", "NG1383", "NG1386", "NG1387", "NG1388", "NG1389", "NG1392", "NG1399", "NG1400", "NG1401", "NG1402", "NG1403", "NG1406", "NG1407", "NG1409", "NG1410", "NG1411", "NG1412", "NG1414", "NG1420", "NG1421", "NG1422", "NG1423", "NG1424", "NG1425", "NG1426", "NG1428", "NG1430", "NG1431", "NG1433", "NG1435", "NG1439", "NG1441", "NG1444", "NG1445", "NG1446", "NG1449", "NG1453", "NG1455", "NG1456", "NG1458", "NG1461", "NG1462", "NG1463", "NG1466", "NG1467", "NG1468", "NG1470", "NG1473", "NG1475", "NG1476", "NG1477", "NG1478", "NG1479", "NG1481", "NG1482", "NG1483", "NG1485", "NG1487", "NG1488", "NG1489", "NG1493", "NG1494", "NG1495", "NG1499", "NG1501", "NG1505", "NG1506", "NG1508", "NG1511", "NG1512", "NG1514", "NG1516", "NG1519", "NG1520", "NG1521", "NG1523", "NG1524", "NG1527", "NG1529", "NG1530", "NG1531", "NG1532", "NG1533", "NG1535", "NG1538", "NG1539", "NG1540", "NG1541", "NG1544", "NG1553", "NG1554", "NG1557", "NG1558", "NG1559", "NG1560", "NG1563", "NG1564", "NG1569", "NG1570", "NG1575", "NG1578", "NG1579", "NG1581", "NG1582", "NG1585", "NG1588", "NG1589", "NG1590", "NG1591", "NG1592", "NG1593", "NG1595", "NG1596", "NG1597", "NG1600", "NG1601", "NG1605", "NG1608", "NG1611", "NG1612", "NG1613", "NG1614", "NG1616", "NG1618", "NG1619", "NG1620", "NG1625", "NG1626", "NG1628", "NG1631", "NG1633", "NG1634", "NG1635", "NG1636", "NG1637", "NG1638", "NG1639", "NG1641", "NG1644", "NG1645", "NG1646", "NG1648", "NG1649", "NG1650", "NG1654", "NG1655", "NG1662", "NG1664", "NG1665", "NG1669", "NG1670", "NG1673", "NG1676", "NG1677", "NG1678", "NG1679", "NG1680", "NG1683", "NG1685", "NG1688", "NG1690", "NG1692", "NG1693", "NG1697", "NG1699", "NG1700", "NG1702", "NG1703", "NG1705", "NG1708", "NG1709", "NG1710", "NG1711", "NG1713", "NG1715", "NG1718", "NG1720", "NG1723", "NG1725", "NG1726", "NG1729", "NG1731", "NG1732", "NG1733", "NG1734", "NG1735", "NG1736", "NG1738", "NG1739", "NG1741", "NG1742", "NG1743", "NG1744", "NG1745", "NG1747", "NG1748", "NG1750", "NG1751", "NG1752", "NG1753", "NG1754", "NG1755", "NG1756", "NG1757", "NG1758", "NG1759", "NG1760", "NG1761", "NG1763", "NG1765", "NG1766", "NG1772", "NG1774", "NG1780", "NG1781", "NG1786", "NG1787", "NG1788", "NG1794", "NG1795", "NG1796", "NG1797", "NG1798", "NG1799", "NG1800", "NG1803", "NG1808", "NG1809", "NG1810", "NG1813", "NG1818", "NG1820", "NG1823", "NG1824", "NG1825", "NG1826", "NG1828", "NG1829", "NG1833", "NG1835", "NG1836", "NG1837", "NG1840", "NG1845", "NG1848", "NG1851", "NG1852", "NG1853", "NG1854", "NG1855", "NG1857", "NG1858", "NG1859", "NG1860", "NG1862", "NG1863", "NG1864", "NG1867", "NG1871", "NG1872", "NG1874", "NG1875", "NG1876", "NG1880", "NG1881", "NG1882", "NG1884", "NG1885", "NG1888", "NG1892", "NG1896", "NG1897", "NG1898", "NG1899", "NG1900", "NG1901", "NG1902", "NG1904", "NG1905", "NG1908", "NG1912", "NG1913", "NG1914", "NG1917", "NG1920", "NG1921", "NG1922", "NG1924", "NG1925", "NG1927", "NG1929", "NG1935", "NG1936", "NG1938", "NG1939", "NG1942", "NG1944", "NG1946", "NG1948", "NG1949", "NG1951", "NG1952", "NG1953", "NG1957", "NG1958", "NG1964", "NG1967", "NG1971", "NG1975", "NG1980", "NG1981", "NG1984", "NG1986", "NG1987", "NG1990", "NG1991", "NG1992", "NG1995", "NG1996", "NG1997", "NG1999", "NG2000", "NG2001", "NG2002", "NG2005", "NG2006", "NG2008", "NG2010", "NG2012", "NG2013", "NG2014", "NG2016", "NG2018", "NG2021", "NG2022", "NG2023", "NG2025", "NG2028", "NG2030", "NG2031", "NG2033", "NG2035", "NG2036", "NG2037", "NG2038", "NG2039", "NG2041", "NG2046", "NG2047", "NG0023", "NG0884", "NG0886", "NG0894", "NG0943", "NG0947", "NG0954", "NG0955", "NG1250", "NG1251", "NG1253", "NG1254", "NG1358", "NG1361", "NG1363", "NG1447", "NG1448", "NG1496", "NG1497", "NG1498", "NG1500", "NG1517", "NG1525", "NG1526", "NG1577", "NG1784", "NG1879", "NG1965", "NG1968"], "REGENERATE": [], "APPROVE": [], "DEFER": ["NG0028", "NG0042", "NG0043", "NG0064", "NG0079", "NG0087", "NG0107", "NG0114", "NG0123", "NG0129", "NG0141", "NG0173", "NG0178", "NG0186", "NG0192", "NG0195", "NG0201", "NG0208", "NG0212", "NG0216", "NG0217", "NG0220", "NG0223", "NG0226", "NG0246", "NG0249", "NG0250", "NG0252", "NG0268", "NG0278", "NG0283", "NG0289", "NG0300", "NG0310", "NG0318", "NG0319", "NG0325", "NG0331", "NG0361", "NG0368", "NG0379", "NG0387", "NG0422", "NG0433", "NG0438", "NG0443", "NG0446", "NG0454", "NG0470", "NG0476", "NG0484", "NG0489", "NG0500", "NG0505", "NG0512", "NG0891", "NG0923", "NG0929", "NG0948", "NG0964", "NG0970", "NG0971", "NG0974", "NG0978", "NG0983", "NG0985", "NG0986", "NG0987", "NG0988", "NG0990", "NG0991", "NG0992", "NG1000", "NG1002", "NG1003", "NG1009", "NG1011", "NG1012", "NG1014", "NG1020", "NG1023", "NG1024", "NG1026", "NG1028", "NG1029", "NG1030", "NG1032", "NG1033", "NG1034", "NG1035", "NG1038", "NG1042", "NG1046", "NG1049", "NG1052", "NG1054", "NG1060", "NG1068", "NG1070", "NG1071", "NG1081", "NG1091", "NG1100", "NG1101", "NG1103", "NG1104", "NG1105", "NG1106", "NG1112", "NG1123", "NG1126", "NG1131", "NG1134", "NG1143", "NG1146", "NG1149", "NG1153", "NG1155", "NG1157", "NG1160", "NG1162", "NG1163", "NG1164", "NG1167", "NG1168", "NG1169", "NG1171", "NG1173", "NG1178", "NG1181", "NG1187", "NG1194", "NG1195", "NG1198", "NG1206", "NG1212", "NG1213", "NG1214", "NG1215", "NG1218", "NG1221", "NG1224", "NG1226", "NG1232", "NG1233", "NG1241", "NG1244", "NG1246", "NG1248", "NG1259", "NG1261", "NG1262", "NG1266", "NG1270", "NG1274", "NG1276", "NG1278", "NG1281", "NG1288", "NG1295", "NG1304", "NG1312", "NG1318", "NG1321", "NG1322", "NG1325", "NG1326", "NG1327", "NG1329", "NG1331", "NG1332", "NG1334", "NG1336", "NG1341", "NG1344", "NG1347", "NG1348", "NG1352", "NG1354", "NG1356", "NG1367", "NG1374", "NG1379", "NG1384", "NG1385", "NG1390", "NG1391", "NG1395", "NG1397", "NG1398", "NG1404", "NG1405", "NG1408", "NG1413", "NG1415", "NG1417", "NG1419", "NG1427", "NG1436", "NG1438", "NG1440", "NG1442", "NG1457", "NG1464", "NG1465", "NG1469", "NG1474", "NG1480", "NG1484", "NG1486", "NG1490", "NG1491", "NG1492", "NG1504", "NG1509", "NG1510", "NG1513", "NG1518", "NG1522", "NG1534", "NG1536", "NG1537", "NG1548", "NG1552", "NG1555", "NG1556", "NG1562", "NG1565", "NG1566", "NG1571", "NG1573", "NG1580", "NG1587", "NG1598", "NG1603", "NG1604", "NG1607", "NG1609", "NG1610", "NG1615", "NG1617", "NG1622", "NG1623", "NG1627", "NG1629", "NG1630", "NG1632", "NG1640", "NG1642", "NG1643", "NG1647", "NG1651", "NG1652", "NG1653", "NG1659", "NG1666", "NG1668", "NG1681", "NG1682", "NG1684", "NG1691", "NG1694", "NG1695", "NG1696", "NG1698", "NG1701", "NG1712", "NG1714", "NG1716", "NG1717", "NG1719", "NG1721", "NG1722", "NG1724", "NG1727", "NG1728", "NG1730", "NG1737", "NG1740", "NG1746", "NG1749", "NG1762", "NG1764", "NG1767", "NG1768", "NG1769", "NG1770", "NG1771", "NG1773", "NG1775", "NG1776", "NG1777", "NG1789", "NG1790", "NG1791", "NG1792", "NG1801", "NG1805", "NG1811", "NG1812", "NG1817", "NG1821", "NG1822", "NG1827", "NG1830", "NG1831", "NG1832", "NG1834", "NG1838", "NG1839", "NG1841", "NG1856", "NG1865", "NG1866", "NG1869", "NG1870", "NG1877", "NG1878", "NG1891", "NG1895", "NG1903", "NG1909", "NG1919", "NG1923", "NG1931", "NG1932", "NG1933", "NG1934", "NG1943", "NG1945", "NG1950", "NG1954", "NG1955", "NG1956", "NG1959", "NG1982", "NG1983", "NG1985", "NG1989", "NG1993", "NG1998", "NG2007", "NG2011", "NG2015", "NG2017", "NG2019", "NG2020", "NG2024", "NG2026", "NG2027", "NG2029", "NG2032", "NG2034", "NG2040", "NG2042", "NG2043", "NG2044", "NG2045", "NG2048"]}}, "next_steps": ["1. Eseguire azioni REMOVE in batch", "2. Sostituire Unicode problematici", "3. <PERSON><PERSON><PERSON><PERSON> simboli con score < 70", "4. Review manuale simboli DEFER", "5. <PERSON><PERSON><PERSON> con FASE 2: Completamento domini"]}