{"remediation_timestamp": "20250525_190108", "total_changes": 616, "changes_by_type": {"fallback_abbreviation": 251, "semantic_renaming": 57, "score_update": 308}, "changes": [{"type": "fallback_abbreviation", "symbol_id": "NG0514", "symbol": "⏐", "old_fallback": "[CORRELATION]", "new_fallback": "[CORR]", "timestamp": "2025-05-25T19:01:08.658683"}, {"type": "fallback_abbreviation", "symbol_id": "NG0515", "symbol": "⎆", "old_fallback": "[CONCLUSION]", "new_fallback": "[CONCLU..]", "timestamp": "2025-05-25T19:01:08.658699"}, {"type": "fallback_abbreviation", "symbol_id": "NG0518", "symbol": "⧮", "old_fallback": "[EVALUATION]", "new_fallback": "[EVALUA..]", "timestamp": "2025-05-25T19:01:08.658703"}, {"type": "fallback_abbreviation", "symbol_id": "NG0519", "symbol": "⪜", "old_fallback": "[SPECIALIZATION]", "new_fallback": "[SPEC]", "timestamp": "2025-05-25T19:01:08.658706"}, {"type": "fallback_abbreviation", "symbol_id": "NG0520", "symbol": "⇡", "old_fallback": "[ASSESSMENT]", "new_fallback": "[ASSESS..]", "timestamp": "2025-05-25T19:01:08.658707"}, {"type": "fallback_abbreviation", "symbol_id": "NG0521", "symbol": "⊍", "old_fallback": "[PROBLEMSOLVING]", "new_fallback": "[SOLVE]", "timestamp": "2025-05-25T19:01:08.658709"}, {"type": "fallback_abbreviation", "symbol_id": "NG0522", "symbol": "⧬", "old_fallback": "[CREATIVITY]", "new_fallback": "[CREATI..]", "timestamp": "2025-05-25T19:01:08.658711"}, {"type": "fallback_abbreviation", "symbol_id": "NG0523", "symbol": "⍛", "old_fallback": "[INTUITION]", "new_fallback": "[INTUIT..]", "timestamp": "2025-05-25T19:01:08.658713"}, {"type": "fallback_abbreviation", "symbol_id": "NG0524", "symbol": "⏃", "old_fallback": "[CONTRAPOSITIVE]", "new_fallback": "[CONTRA]", "timestamp": "2025-05-25T19:01:08.658715"}, {"type": "fallback_abbreviation", "symbol_id": "NG0526", "symbol": "⦵", "old_fallback": "[SELFAWARENESS]", "new_fallback": "[AWARE]", "timestamp": "2025-05-25T19:01:08.658717"}, {"type": "fallback_abbreviation", "symbol_id": "NG0527", "symbol": "⧄", "old_fallback": "[REFLECTION]", "new_fallback": "[REFLEC..]", "timestamp": "2025-05-25T19:01:08.658719"}, {"type": "fallback_abbreviation", "symbol_id": "NG0528", "symbol": "⩓", "old_fallback": "[DEDUCTION]", "new_fallback": "[DEDUCT..]", "timestamp": "2025-05-25T19:01:08.658721"}, {"type": "fallback_abbreviation", "symbol_id": "NG0529", "symbol": "⎭", "old_fallback": "[MODUSPONENS]", "new_fallback": "[MPONENS]", "timestamp": "2025-05-25T19:01:08.658723"}, {"type": "fallback_abbreviation", "symbol_id": "NG0530", "symbol": "⪫", "old_fallback": "[TAUTOLOGY]", "new_fallback": "[TAUTOL..]", "timestamp": "2025-05-25T19:01:08.658725"}, {"type": "fallback_abbreviation", "symbol_id": "NG0531", "symbol": "⊲", "old_fallback": "[HYPOTHESIS]", "new_fallback": "[HYPOTH..]", "timestamp": "2025-05-25T19:01:08.658726"}, {"type": "fallback_abbreviation", "symbol_id": "NG0532", "symbol": "⩢", "old_fallback": "[SOUNDNESS]", "new_fallback": "[SOUNDN..]", "timestamp": "2025-05-25T19:01:08.658728"}, {"type": "fallback_abbreviation", "symbol_id": "NG0533", "symbol": "⪣", "old_fallback": "[CONSISTENCY]", "new_fallback": "[CONSIST]", "timestamp": "2025-05-25T19:01:08.658730"}, {"type": "fallback_abbreviation", "symbol_id": "NG0536", "symbol": "⌾", "old_fallback": "[INFERENCE]", "new_fallback": "[INFERE..]", "timestamp": "2025-05-25T19:01:08.658732"}, {"type": "fallback_abbreviation", "symbol_id": "NG0538", "symbol": "⩷", "old_fallback": "[SIMILARITY]", "new_fallback": "[SIMILA..]", "timestamp": "2025-05-25T19:01:08.658734"}, {"type": "fallback_abbreviation", "symbol_id": "NG0539", "symbol": "▪", "old_fallback": "[CONTRADICTION]", "new_fallback": "[CONTRA]", "timestamp": "2025-05-25T19:01:08.658736"}, {"type": "fallback_abbreviation", "symbol_id": "NG0542", "symbol": "⪲", "old_fallback": "[CAUSALITY]", "new_fallback": "[CAUSAL..]", "timestamp": "2025-05-25T19:01:08.658738"}, {"type": "fallback_abbreviation", "symbol_id": "NG0543", "symbol": "⏟", "old_fallback": "[MONITORING]", "new_fallback": "[MONITO..]", "timestamp": "2025-05-25T19:01:08.658739"}, {"type": "fallback_abbreviation", "symbol_id": "NG0544", "symbol": "⎓", "old_fallback": "[SYLLOGISM]", "new_fallback": "[SYLLOG..]", "timestamp": "2025-05-25T19:01:08.658741"}, {"type": "fallback_abbreviation", "symbol_id": "NG0545", "symbol": "≘", "old_fallback": "[HEURISTIC]", "new_fallback": "[HEURIS..]", "timestamp": "2025-05-25T19:01:08.658743"}, {"type": "fallback_abbreviation", "symbol_id": "NG0546", "symbol": "⧗", "old_fallback": "[SYNTHESIS]", "new_fallback": "[SYNTHE..]", "timestamp": "2025-05-25T19:01:08.658745"}, {"type": "fallback_abbreviation", "symbol_id": "NG0548", "symbol": "⎑", "old_fallback": "[METACOGNITION]", "new_fallback": "[META]", "timestamp": "2025-05-25T19:01:08.658747"}, {"type": "fallback_abbreviation", "symbol_id": "NG0549", "symbol": "⏰", "old_fallback": "[COMPLETENESS]", "new_fallback": "[COMPLETE]", "timestamp": "2025-05-25T19:01:08.658748"}, {"type": "fallback_abbreviation", "symbol_id": "NG0551", "symbol": "⎚", "old_fallback": "[INDUCTION]", "new_fallback": "[INDUCT..]", "timestamp": "2025-05-25T19:01:08.658750"}, {"type": "fallback_abbreviation", "symbol_id": "NG0553", "symbol": "≊", "old_fallback": "[GENERALIZATION]", "new_fallback": "[GENERAL]", "timestamp": "2025-05-25T19:01:08.658752"}, {"type": "fallback_abbreviation", "symbol_id": "NG0555", "symbol": "⎁", "old_fallback": "[ERRORCORRECTION]", "new_fallback": "[ERRCORR]", "timestamp": "2025-05-25T19:01:08.658754"}, {"type": "fallback_abbreviation", "symbol_id": "NG0556", "symbol": "⎯", "old_fallback": "[ABDUCTION]", "new_fallback": "[ABDUCT..]", "timestamp": "2025-05-25T19:01:08.658756"}, {"type": "fallback_abbreviation", "symbol_id": "NG0557", "symbol": "≵", "old_fallback": "[CLASSIFICATION]", "new_fallback": "[CLASS]", "timestamp": "2025-05-25T19:01:08.658769"}, {"type": "fallback_abbreviation", "symbol_id": "NG0558", "symbol": "⎧", "old_fallback": "[MODUSTOLLENS]", "new_fallback": "[MTOLLENS]", "timestamp": "2025-05-25T19:01:08.658771"}, {"type": "fallback_abbreviation", "symbol_id": "NG0561", "symbol": "⦗", "old_fallback": "[ABSTRACTION]", "new_fallback": "[ABSTRACT]", "timestamp": "2025-05-25T19:01:08.658774"}, {"type": "fallback_abbreviation", "symbol_id": "NG0563", "symbol": "⫳", "old_fallback": "[DIFFERENCE]", "new_fallback": "[DIFFER..]", "timestamp": "2025-05-25T19:01:08.658776"}, {"type": "fallback_abbreviation", "symbol_id": "NG0565", "symbol": "⊮", "old_fallback": "[DECIDABILITY]", "new_fallback": "[DECIDE]", "timestamp": "2025-05-25T19:01:08.658778"}, {"type": "fallback_abbreviation", "symbol_id": "NG0883", "symbol": "⩒", "old_fallback": "[SYNTAXTREESYS]", "new_fallback": "[SYNTAX..]", "timestamp": "2025-05-25T19:01:08.658838"}, {"type": "fallback_abbreviation", "symbol_id": "NG0885", "symbol": "⣖", "old_fallback": "[CODEGENOP]", "new_fallback": "[CODEGE..]", "timestamp": "2025-05-25T19:01:08.658840"}, {"type": "fallback_abbreviation", "symbol_id": "NG0888", "symbol": "⢠", "old_fallback": "[INTROSPECTION]", "new_fallback": "[INTROS..]", "timestamp": "2025-05-25T19:01:08.658843"}, {"type": "fallback_abbreviation", "symbol_id": "NG0889", "symbol": "⥮", "old_fallback": "[INTROSPECTIONFN]", "new_fallback": "[INTROS..]", "timestamp": "2025-05-25T19:01:08.658845"}, {"type": "fallback_abbreviation", "symbol_id": "NG0893", "symbol": "⠏", "old_fallback": "[METAOBJECTS]", "new_fallback": "[METAOB..]", "timestamp": "2025-05-25T19:01:08.658847"}, {"type": "fallback_abbreviation", "symbol_id": "NG0896", "symbol": "⨪", "old_fallback": "[JITCOMPILATIONCORE]", "new_fallback": "[JITCOM..]", "timestamp": "2025-05-25T19:01:08.658850"}, {"type": "fallback_abbreviation", "symbol_id": "NG0897", "symbol": "⨄", "old_fallback": "[JITCOMPILATIONMETA]", "new_fallback": "[JITCOM..]", "timestamp": "2025-05-25T19:01:08.658852"}, {"type": "fallback_abbreviation", "symbol_id": "NG0901", "symbol": "⭭", "old_fallback": "[MEMORYPOOLS]", "new_fallback": "[MEMORY..]", "timestamp": "2025-05-25T19:01:08.658854"}, {"type": "fallback_abbreviation", "symbol_id": "NG0906", "symbol": "❣", "old_fallback": "[COROUTINESFN]", "new_fallback": "[COROUT..]", "timestamp": "2025-05-25T19:01:08.658857"}, {"type": "fallback_abbreviation", "symbol_id": "NG0907", "symbol": "⠌", "old_fallback": "[COROUTINESOP]", "new_fallback": "[COROUT..]", "timestamp": "2025-05-25T19:01:08.658858"}, {"type": "fallback_abbreviation", "symbol_id": "NG0910", "symbol": "⢓", "old_fallback": "[ITERATORSSYS]", "new_fallback": "[ITERAT..]", "timestamp": "2025-05-25T19:01:08.658861"}, {"type": "fallback_abbreviation", "symbol_id": "NG0911", "symbol": "⬹", "old_fallback": "[ITERATORSCTRL]", "new_fallback": "[ITERAT..]", "timestamp": "2025-05-25T19:01:08.658862"}, {"type": "fallback_abbreviation", "symbol_id": "NG0912", "symbol": "✝", "old_fallback": "[COMPREHENSIONSFN]", "new_fallback": "[COMPRE..]", "timestamp": "2025-05-25T19:01:08.658864"}, {"type": "fallback_abbreviation", "symbol_id": "NG0915", "symbol": "⧟", "old_fallback": "[DECORATORSCTRL]", "new_fallback": "[DECORA..]", "timestamp": "2025-05-25T19:01:08.658866"}, {"type": "fallback_abbreviation", "symbol_id": "NG0916", "symbol": "⥿", "old_fallback": "[CONTEXTMANAGERS]", "new_fallback": "[CONTEX..]", "timestamp": "2025-05-25T19:01:08.658868"}, {"type": "fallback_abbreviation", "symbol_id": "NG0917", "symbol": "⬫", "old_fallback": "[CONTEXTMANAGERSSYS]", "new_fallback": "[CONTEX..]", "timestamp": "2025-05-25T19:01:08.658870"}, {"type": "fallback_abbreviation", "symbol_id": "NG0920", "symbol": "⠣", "old_fallback": "[METACLASSESSYS]", "new_fallback": "[METACL..]", "timestamp": "2025-05-25T19:01:08.658873"}, {"type": "fallback_abbreviation", "symbol_id": "NG0922", "symbol": "⧡", "old_fallback": "[METACLASSES]", "new_fallback": "[METACL..]", "timestamp": "2025-05-25T19:01:08.658875"}, {"type": "fallback_abbreviation", "symbol_id": "NG0926", "symbol": "⨊", "old_fallback": "[METACLASSESOP]", "new_fallback": "[METACL..]", "timestamp": "2025-05-25T19:01:08.658877"}, {"type": "fallback_abbreviation", "symbol_id": "NG0927", "symbol": "⫙", "old_fallback": "[METACLASSESFN]", "new_fallback": "[METACL..]", "timestamp": "2025-05-25T19:01:08.658879"}, {"type": "fallback_abbreviation", "symbol_id": "NG0932", "symbol": "⦎", "old_fallback": "[PARSETREEFN]", "new_fallback": "[PARSET..]", "timestamp": "2025-05-25T19:01:08.658881"}, {"type": "fallback_abbreviation", "symbol_id": "NG0937", "symbol": "⣋", "old_fallback": "[ASTNODEFN]", "new_fallback": "[ASTNOD..]", "timestamp": "2025-05-25T19:01:08.658884"}, {"type": "fallback_abbreviation", "symbol_id": "NG0938", "symbol": "⬈", "old_fallback": "[PARSETREE]", "new_fallback": "[PARSET..]", "timestamp": "2025-05-25T19:01:08.658886"}, {"type": "fallback_abbreviation", "symbol_id": "NG0941", "symbol": "⢐", "old_fallback": "[ASTNODEOP]", "new_fallback": "[ASTNOD..]", "timestamp": "2025-05-25T19:01:08.658888"}, {"type": "fallback_abbreviation", "symbol_id": "NG0949", "symbol": "⡗", "old_fallback": "[GENERATESYS]", "new_fallback": "[GENERA..]", "timestamp": "2025-05-25T19:01:08.658891"}, {"type": "fallback_abbreviation", "symbol_id": "NG0952", "symbol": "⪠", "old_fallback": "[COMPILECORE]", "new_fallback": "[COMPIL..]", "timestamp": "2025-05-25T19:01:08.658893"}, {"type": "fallback_abbreviation", "symbol_id": "NG0960", "symbol": "⥳", "old_fallback": "[INTROSPECT]", "new_fallback": "[INTROS..]", "timestamp": "2025-05-25T19:01:08.658896"}, {"type": "fallback_abbreviation", "symbol_id": "NG0969", "symbol": "⮸", "old_fallback": "[INTROSPECTIONCORE]", "new_fallback": "[INTROS..]", "timestamp": "2025-05-25T19:01:08.658899"}, {"type": "fallback_abbreviation", "symbol_id": "NG0973", "symbol": "⪘", "old_fallback": "[INTROSPECTIONSYS]", "new_fallback": "[INTROS..]", "timestamp": "2025-05-25T19:01:08.658902"}, {"type": "fallback_abbreviation", "symbol_id": "NG0977", "symbol": "⨞", "old_fallback": "[INTROSPECTIONCTRL]", "new_fallback": "[INTROS..]", "timestamp": "2025-05-25T19:01:08.658905"}, {"type": "fallback_abbreviation", "symbol_id": "NG0981", "symbol": "⧚", "old_fallback": "[DYNAMICDISPATCHOP]", "new_fallback": "[DYNAMI..]", "timestamp": "2025-05-25T19:01:08.658908"}, {"type": "fallback_abbreviation", "symbol_id": "NG0982", "symbol": "⪟", "old_fallback": "[DYNAMICDISPATCH]", "new_fallback": "[DYNAMI..]", "timestamp": "2025-05-25T19:01:08.658910"}, {"type": "fallback_abbreviation", "symbol_id": "NG0993", "symbol": "⧠", "old_fallback": "[METAOBJECTSOP]", "new_fallback": "[METAOB..]", "timestamp": "2025-05-25T19:01:08.658914"}, {"type": "fallback_abbreviation", "symbol_id": "NG0995", "symbol": "⧲", "old_fallback": "[METAOBJECTSCTRL]", "new_fallback": "[METAOB..]", "timestamp": "2025-05-25T19:01:08.658916"}, {"type": "fallback_abbreviation", "symbol_id": "NG1006", "symbol": "⯐", "old_fallback": "[BYTECODECORE]", "new_fallback": "[BYTECO..]", "timestamp": "2025-05-25T19:01:08.658922"}, {"type": "fallback_abbreviation", "symbol_id": "NG1007", "symbol": "⣐", "old_fallback": "[BYTECODEMETA]", "new_fallback": "[BYTECO..]", "timestamp": "2025-05-25T19:01:08.658924"}, {"type": "fallback_abbreviation", "symbol_id": "NG1008", "symbol": "⬿", "old_fallback": "[BYTECODEPROC]", "new_fallback": "[BYTECO..]", "timestamp": "2025-05-25T19:01:08.658926"}, {"type": "fallback_abbreviation", "symbol_id": "NG1016", "symbol": "➴", "old_fallback": "[JITCOMPILATIONSYS]", "new_fallback": "[JITCOM..]", "timestamp": "2025-05-25T19:01:08.658931"}, {"type": "fallback_abbreviation", "symbol_id": "NG1019", "symbol": "⠀", "old_fallback": "[JITCOMPILATIONCTRL]", "new_fallback": "[JITCOM..]", "timestamp": "2025-05-25T19:01:08.658933"}, {"type": "fallback_abbreviation", "symbol_id": "NG1040", "symbol": "➞", "old_fallback": "[MEMORYPOOLSOP]", "new_fallback": "[MEMORY..]", "timestamp": "2025-05-25T19:01:08.658939"}, {"type": "fallback_abbreviation", "symbol_id": "NG1050", "symbol": "✱", "old_fallback": "[STACKFRAMESOP]", "new_fallback": "[STACKF..]", "timestamp": "2025-05-25T19:01:08.658943"}, {"type": "fallback_abbreviation", "symbol_id": "NG1051", "symbol": "⤭", "old_fallback": "[STACKFRAMESSYS]", "new_fallback": "[STACKF..]", "timestamp": "2025-05-25T19:01:08.658945"}, {"type": "fallback_abbreviation", "symbol_id": "NG1061", "symbol": "⪴", "old_fallback": "[HEAPMANAGEMENTOP]", "new_fallback": "[HEAPMA..]", "timestamp": "2025-05-25T19:01:08.658949"}, {"type": "fallback_abbreviation", "symbol_id": "NG1064", "symbol": "⥔", "old_fallback": "[HEAPMANAGEMENT]", "new_fallback": "[HEAPMA..]", "timestamp": "2025-05-25T19:01:08.658951"}, {"type": "fallback_abbreviation", "symbol_id": "NG1066", "symbol": "⡔", "old_fallback": "[HEAPMANAGEMENTCTRL]", "new_fallback": "[HEAPMA..]", "timestamp": "2025-05-25T19:01:08.658953"}, {"type": "fallback_abbreviation", "symbol_id": "NG1072", "symbol": "⦩", "old_fallback": "[COROUTINESCTRL]", "new_fallback": "[COROUT..]", "timestamp": "2025-05-25T19:01:08.658956"}, {"type": "fallback_abbreviation", "symbol_id": "NG1074", "symbol": "⠎", "old_fallback": "[COROUTINESMETA]", "new_fallback": "[COROUT..]", "timestamp": "2025-05-25T19:01:08.658958"}, {"type": "fallback_abbreviation", "symbol_id": "NG1076", "symbol": "❛", "old_fallback": "[COROUTINES]", "new_fallback": "[COROUT..]", "timestamp": "2025-05-25T19:01:08.658960"}, {"type": "fallback_abbreviation", "symbol_id": "NG1082", "symbol": "⠹", "old_fallback": "[COROUTINESCORE]", "new_fallback": "[COROUT..]", "timestamp": "2025-05-25T19:01:08.658963"}, {"type": "fallback_abbreviation", "symbol_id": "NG1097", "symbol": "⣛", "old_fallback": "[ITERATORSFN]", "new_fallback": "[ITERAT..]", "timestamp": "2025-05-25T19:01:08.658968"}, {"type": "fallback_abbreviation", "symbol_id": "NG1108", "symbol": "⫅", "old_fallback": "[COMPREHENSIONSOP]", "new_fallback": "[COMPRE..]", "timestamp": "2025-05-25T19:01:08.658972"}, {"type": "fallback_abbreviation", "symbol_id": "NG1113", "symbol": "⤙", "old_fallback": "[COMPREHENSIONSCTRL]", "new_fallback": "[COMPRE..]", "timestamp": "2025-05-25T19:01:08.658975"}, {"type": "fallback_abbreviation", "symbol_id": "NG1127", "symbol": "⥼", "old_fallback": "[DECORATORSMETA]", "new_fallback": "[DECORA..]", "timestamp": "2025-05-25T19:01:08.658980"}, {"type": "fallback_abbreviation", "symbol_id": "NG1130", "symbol": "⡣", "old_fallback": "[DECORATORSSYS]", "new_fallback": "[DECORA..]", "timestamp": "2025-05-25T19:01:08.658982"}, {"type": "fallback_abbreviation", "symbol_id": "NG1138", "symbol": "➤", "old_fallback": "[CONTEXTMANAGERSFN]", "new_fallback": "[CONTEX..]", "timestamp": "2025-05-25T19:01:08.658986"}, {"type": "fallback_abbreviation", "symbol_id": "NG1145", "symbol": "⡑", "old_fallback": "[DESCRIPTORSPROC]", "new_fallback": "[DESCRI..]", "timestamp": "2025-05-25T19:01:08.658989"}, {"type": "fallback_abbreviation", "symbol_id": "NG1152", "symbol": "⧅", "old_fallback": "[DESCRIPTORSSYS]", "new_fallback": "[DESCRI..]", "timestamp": "2025-05-25T19:01:08.658993"}, {"type": "fallback_abbreviation", "symbol_id": "NG1156", "symbol": "❱", "old_fallback": "[METACLASSESCORE]", "new_fallback": "[METACL..]", "timestamp": "2025-05-25T19:01:08.658995"}, {"type": "fallback_abbreviation", "symbol_id": "NG1188", "symbol": "⩶", "old_fallback": "[CODEASDATAPROC]", "new_fallback": "[CODEAS..]", "timestamp": "2025-05-25T19:01:08.659004"}, {"type": "fallback_abbreviation", "symbol_id": "NG1189", "symbol": "⧿", "old_fallback": "[CODEASDATA]", "new_fallback": "[CODEAS..]", "timestamp": "2025-05-25T19:01:08.659006"}, {"type": "fallback_abbreviation", "symbol_id": "NG1190", "symbol": "⦼", "old_fallback": "[CODEASDATASYS]", "new_fallback": "[CODEAS..]", "timestamp": "2025-05-25T19:01:08.659008"}, {"type": "fallback_abbreviation", "symbol_id": "NG1192", "symbol": "⥈", "old_fallback": "[CODEASDATAFN]", "new_fallback": "[CODEAS..]", "timestamp": "2025-05-25T19:01:08.659010"}, {"type": "fallback_abbreviation", "symbol_id": "NG1197", "symbol": "⥯", "old_fallback": "[CODEASDATAMETA]", "new_fallback": "[CODEAS..]", "timestamp": "2025-05-25T19:01:08.659012"}, {"type": "fallback_abbreviation", "symbol_id": "NG1208", "symbol": "⢁", "old_fallback": "[MACROSYSTEMSSYS]", "new_fallback": "[MACROS..]", "timestamp": "2025-05-25T19:01:08.659017"}, {"type": "fallback_abbreviation", "symbol_id": "NG1211", "symbol": "⡶", "old_fallback": "[MACROSYSTEMSOP]", "new_fallback": "[MACROS..]", "timestamp": "2025-05-25T19:01:08.659019"}, {"type": "fallback_abbreviation", "symbol_id": "NG1235", "symbol": "⨹", "old_fallback": "[PROGRAMSYNTHESISFN]", "new_fallback": "[PROGRA..]", "timestamp": "2025-05-25T19:01:08.659025"}, {"type": "fallback_abbreviation", "symbol_id": "NG1236", "symbol": "⡭", "old_fallback": "[PROGRAMSYNTHESISOP]", "new_fallback": "[PROGRA..]", "timestamp": "2025-05-25T19:01:08.659027"}, {"type": "fallback_abbreviation", "symbol_id": "NG1237", "symbol": "⩟", "old_fallback": "[PROGRAMSYNTHESIS]", "new_fallback": "[PROGRA..]", "timestamp": "2025-05-25T19:01:08.659029"}, {"type": "fallback_abbreviation", "symbol_id": "NG1249", "symbol": "⭐", "old_fallback": "[CODETRANSFORMATION]", "new_fallback": "[CODETR..]", "timestamp": "2025-05-25T19:01:08.659033"}, {"type": "fallback_abbreviation", "symbol_id": "NG1255", "symbol": "⮭", "old_fallback": "[CONSENSUSOP]", "new_fallback": "[CONSEN..]", "timestamp": "2025-05-25T19:01:08.659036"}, {"type": "fallback_abbreviation", "symbol_id": "NG1258", "symbol": "⢵", "old_fallback": "[DISTRIBUTEDLOCKS]", "new_fallback": "[DISTRI..]", "timestamp": "2025-05-25T19:01:08.659038"}, {"type": "fallback_abbreviation", "symbol_id": "NG1263", "symbol": "✟", "old_fallback": "[VECTORCLOCKSOP]", "new_fallback": "[VECTOR..]", "timestamp": "2025-05-25T19:01:08.659041"}, {"type": "fallback_abbreviation", "symbol_id": "NG1267", "symbol": "⣔", "old_fallback": "[VECTORCLOCKSMETA]", "new_fallback": "[VECTOR..]", "timestamp": "2025-05-25T19:01:08.659043"}, {"type": "fallback_abbreviation", "symbol_id": "NG1269", "symbol": "⨤", "old_fallback": "[CAPTHEOREMPROC]", "new_fallback": "[CAPTHE..]", "timestamp": "2025-05-25T19:01:08.659045"}, {"type": "fallback_abbreviation", "symbol_id": "NG1272", "symbol": "✪", "old_fallback": "[CAPTHEOREMOP]", "new_fallback": "[CAPTHE..]", "timestamp": "2025-05-25T19:01:08.659048"}, {"type": "fallback_abbreviation", "symbol_id": "NG1273", "symbol": "⮹", "old_fallback": "[CAPTHEOREM]", "new_fallback": "[CAPTHE..]", "timestamp": "2025-05-25T19:01:08.659050"}, {"type": "fallback_abbreviation", "symbol_id": "NG1277", "symbol": "⦤", "old_fallback": "[GOSSIPPROTOCOLSFN]", "new_fallback": "[GOSSIP..]", "timestamp": "2025-05-25T19:01:08.659052"}, {"type": "fallback_abbreviation", "symbol_id": "NG1279", "symbol": "⥠", "old_fallback": "[GOSSIPPROTOCOLSSYS]", "new_fallback": "[GOSSIP..]", "timestamp": "2025-05-25T19:01:08.659054"}, {"type": "fallback_abbreviation", "symbol_id": "NG1282", "symbol": "⪂", "old_fallback": "[LEADERELECTION]", "new_fallback": "[LEADER..]", "timestamp": "2025-05-25T19:01:08.659056"}, {"type": "fallback_abbreviation", "symbol_id": "NG1285", "symbol": "⪚", "old_fallback": "[LEADERELECTIONFN]", "new_fallback": "[LEADER..]", "timestamp": "2025-05-25T19:01:08.659058"}, {"type": "fallback_abbreviation", "symbol_id": "NG1289", "symbol": "⤥", "old_fallback": "[LEADERELECTIONPROC]", "new_fallback": "[LEADER..]", "timestamp": "2025-05-25T19:01:08.659061"}, {"type": "fallback_abbreviation", "symbol_id": "NG1293", "symbol": "⬪", "old_fallback": "[SHARDINGMETA]", "new_fallback": "[SHARDI..]", "timestamp": "2025-05-25T19:01:08.659063"}, {"type": "fallback_abbreviation", "symbol_id": "NG1298", "symbol": "⭦", "old_fallback": "[REPLICATION]", "new_fallback": "[REPLIC..]", "timestamp": "2025-05-25T19:01:08.659066"}, {"type": "fallback_abbreviation", "symbol_id": "NG1301", "symbol": "⯼", "old_fallback": "[REPLICATIONFN]", "new_fallback": "[REPLIC..]", "timestamp": "2025-05-25T19:01:08.659068"}, {"type": "fallback_abbreviation", "symbol_id": "NG1302", "symbol": "⧉", "old_fallback": "[REPLICATIONOP]", "new_fallback": "[REPLIC..]", "timestamp": "2025-05-25T19:01:08.659070"}, {"type": "fallback_abbreviation", "symbol_id": "NG1309", "symbol": "⤫", "old_fallback": "[LOADBALANCINGPROC]", "new_fallback": "[LOADBA..]", "timestamp": "2025-05-25T19:01:08.659073"}, {"type": "fallback_abbreviation", "symbol_id": "NG1310", "symbol": "⧽", "old_fallback": "[LOADBALANCINGCTRL]", "new_fallback": "[LOADBA..]", "timestamp": "2025-05-25T19:01:08.659075"}, {"type": "fallback_abbreviation", "symbol_id": "NG1311", "symbol": "⧫", "old_fallback": "[LOADBALANCINGFN]", "new_fallback": "[LOADBA..]", "timestamp": "2025-05-25T19:01:08.659077"}, {"type": "fallback_abbreviation", "symbol_id": "NG1314", "symbol": "⠗", "old_fallback": "[CIRCUITBREAKERSFN]", "new_fallback": "[CIRCUI..]", "timestamp": "2025-05-25T19:01:08.659079"}, {"type": "fallback_abbreviation", "symbol_id": "NG1315", "symbol": "⥏", "old_fallback": "[CIRCUITBREAKERSOP]", "new_fallback": "[CIRCUI..]", "timestamp": "2025-05-25T19:01:08.659081"}, {"type": "fallback_abbreviation", "symbol_id": "NG1317", "symbol": "❚", "old_fallback": "[CIRCUITBREAKERSSYS]", "new_fallback": "[CIRCUI..]", "timestamp": "2025-05-25T19:01:08.659083"}, {"type": "fallback_abbreviation", "symbol_id": "NG1360", "symbol": "⠰", "old_fallback": "[HADAMARDMETA]", "new_fallback": "[HADAMA..]", "timestamp": "2025-05-25T19:01:08.659092"}, {"type": "fallback_abbreviation", "symbol_id": "NG1364", "symbol": "⮨", "old_fallback": "[QUANTUMCIRCUITSOP]", "new_fallback": "[QUANTU..]", "timestamp": "2025-05-25T19:01:08.659109"}, {"type": "fallback_abbreviation", "symbol_id": "NG1365", "symbol": "⫔", "old_fallback": "[QUANTUMCIRCUITSSYS]", "new_fallback": "[QUANTU..]", "timestamp": "2025-05-25T19:01:08.659112"}, {"type": "fallback_abbreviation", "symbol_id": "NG1370", "symbol": "⭕", "old_fallback": "[SUPERPOSITIONPROC]", "new_fallback": "[SUPERP..]", "timestamp": "2025-05-25T19:01:08.659114"}, {"type": "fallback_abbreviation", "symbol_id": "NG1371", "symbol": "⡄", "old_fallback": "[SUPERPOSITION]", "new_fallback": "[SUPERP..]", "timestamp": "2025-05-25T19:01:08.659116"}, {"type": "fallback_abbreviation", "symbol_id": "NG1375", "symbol": "⮎", "old_fallback": "[ENTANGLEMENTPROC]", "new_fallback": "[ENTANG..]", "timestamp": "2025-05-25T19:01:08.659118"}, {"type": "fallback_abbreviation", "symbol_id": "NG1377", "symbol": "❞", "old_fallback": "[ENTANGLEMENT]", "new_fallback": "[ENTANG..]", "timestamp": "2025-05-25T19:01:08.659120"}, {"type": "fallback_abbreviation", "symbol_id": "NG1378", "symbol": "⪩", "old_fallback": "[ENTANGLEMENTMETA]", "new_fallback": "[ENTANG..]", "timestamp": "2025-05-25T19:01:08.659122"}, {"type": "fallback_abbreviation", "symbol_id": "NG1380", "symbol": "⮀", "old_fallback": "[ENTANGLEMENTOP]", "new_fallback": "[ENTANG..]", "timestamp": "2025-05-25T19:01:08.659124"}, {"type": "fallback_abbreviation", "symbol_id": "NG1381", "symbol": "⤉", "old_fallback": "[QUANTUMALGORITHMS]", "new_fallback": "[QUANTU..]", "timestamp": "2025-05-25T19:01:08.659126"}, {"type": "fallback_abbreviation", "symbol_id": "NG1393", "symbol": "❴", "old_fallback": "[THEOREMPROVING]", "new_fallback": "[THEORE..]", "timestamp": "2025-05-25T19:01:08.659131"}, {"type": "fallback_abbreviation", "symbol_id": "NG1394", "symbol": "⧜", "old_fallback": "[THEOREMPROVINGSYS]", "new_fallback": "[THEORE..]", "timestamp": "2025-05-25T19:01:08.659132"}, {"type": "fallback_abbreviation", "symbol_id": "NG1396", "symbol": "⣥", "old_fallback": "[THEOREMPROVINGOP]", "new_fallback": "[THEORE..]", "timestamp": "2025-05-25T19:01:08.659134"}, {"type": "fallback_abbreviation", "symbol_id": "NG1416", "symbol": "⨶", "old_fallback": "[ONTOLOGIESSYS]", "new_fallback": "[ONTOLO..]", "timestamp": "2025-05-25T19:01:08.659140"}, {"type": "fallback_abbreviation", "symbol_id": "NG1418", "symbol": "⮃", "old_fallback": "[ONTOLOGIESFN]", "new_fallback": "[ONTOLO..]", "timestamp": "2025-05-25T19:01:08.659142"}, {"type": "fallback_abbreviation", "symbol_id": "NG1429", "symbol": "⯯", "old_fallback": "[AUTOMATEDREASONING]", "new_fallback": "[AUTOMA..]", "timestamp": "2025-05-25T19:01:08.659146"}, {"type": "fallback_abbreviation", "symbol_id": "NG1432", "symbol": "⯉", "old_fallback": "[MULTIHEADFN]", "new_fallback": "[MULTIH..]", "timestamp": "2025-05-25T19:01:08.659149"}, {"type": "fallback_abbreviation", "symbol_id": "NG1434", "symbol": "⥞", "old_fallback": "[ATTENTIONOP]", "new_fallback": "[ATTENT..]", "timestamp": "2025-05-25T19:01:08.659151"}, {"type": "fallback_abbreviation", "symbol_id": "NG1437", "symbol": "⩿", "old_fallback": "[TRANSFORMERBLOCKS]", "new_fallback": "[TRANSF..]", "timestamp": "2025-05-25T19:01:08.659153"}, {"type": "fallback_abbreviation", "symbol_id": "NG1443", "symbol": "➷", "old_fallback": "[LOSSFUNCTIONSFN]", "new_fallback": "[LOSSFU..]", "timestamp": "2025-05-25T19:01:08.659156"}, {"type": "fallback_abbreviation", "symbol_id": "NG1450", "symbol": "➡", "old_fallback": "[MODELCHECK]", "new_fallback": "[MODELC..]", "timestamp": "2025-05-25T19:01:08.659158"}, {"type": "fallback_abbreviation", "symbol_id": "NG1451", "symbol": "⣫", "old_fallback": "[VERIFYCTRL]", "new_fallback": "[VERIFY..]", "timestamp": "2025-05-25T19:01:08.659160"}, {"type": "fallback_abbreviation", "symbol_id": "NG1452", "symbol": "⫡", "old_fallback": "[TEMPORALSYS]", "new_fallback": "[TEMPOR..]", "timestamp": "2025-05-25T19:01:08.659162"}, {"type": "fallback_abbreviation", "symbol_id": "NG1454", "symbol": "⥢", "old_fallback": "[THEOREMPROVINGCORE]", "new_fallback": "[THEORE..]", "timestamp": "2025-05-25T19:01:08.659164"}, {"type": "fallback_abbreviation", "symbol_id": "NG1459", "symbol": "➛", "old_fallback": "[STATICANALYSISCORE]", "new_fallback": "[STATIC..]", "timestamp": "2025-05-25T19:01:08.659167"}, {"type": "fallback_abbreviation", "symbol_id": "NG1460", "symbol": "⦱", "old_fallback": "[STATICANALYSISFN]", "new_fallback": "[STATIC..]", "timestamp": "2025-05-25T19:01:08.659169"}, {"type": "fallback_abbreviation", "symbol_id": "NG1471", "symbol": "⩗", "old_fallback": "[HOARELOGICPROC]", "new_fallback": "[HOAREL..]", "timestamp": "2025-05-25T19:01:08.659172"}, {"type": "fallback_abbreviation", "symbol_id": "NG1472", "symbol": "⤜", "old_fallback": "[HOARELOGICCTRL]", "new_fallback": "[HOAREL..]", "timestamp": "2025-05-25T19:01:08.659174"}, {"type": "fallback_abbreviation", "symbol_id": "NG1502", "symbol": "⦺", "old_fallback": "[MONADSMETA]", "new_fallback": "[MONADS..]", "timestamp": "2025-05-25T19:01:08.659182"}, {"type": "fallback_abbreviation", "symbol_id": "NG1503", "symbol": "⫛", "old_fallback": "[MONADSCORE]", "new_fallback": "[MONADS..]", "timestamp": "2025-05-25T19:01:08.659184"}, {"type": "fallback_abbreviation", "symbol_id": "NG1507", "symbol": "⧤", "old_fallback": "[COMONADSOP]", "new_fallback": "[COMONA..]", "timestamp": "2025-05-25T19:01:08.659186"}, {"type": "fallback_abbreviation", "symbol_id": "NG1515", "symbol": "⫕", "old_fallback": "[LIMITSCTRL]", "new_fallback": "[LIMITS..]", "timestamp": "2025-05-25T19:01:08.659190"}, {"type": "fallback_abbreviation", "symbol_id": "NG1528", "symbol": "⤬", "old_fallback": "[TOPOIMETA]", "new_fallback": "[TOPOIM..]", "timestamp": "2025-05-25T19:01:08.659194"}, {"type": "fallback_abbreviation", "symbol_id": "NG1542", "symbol": "⫌", "old_fallback": "[PITYPEMETA]", "new_fallback": "[PITYPE..]", "timestamp": "2025-05-25T19:01:08.659198"}, {"type": "fallback_abbreviation", "symbol_id": "NG1543", "symbol": "⦮", "old_fallback": "[INDEXEDSYS]", "new_fallback": "[INDEXE..]", "timestamp": "2025-05-25T19:01:08.659200"}, {"type": "fallback_abbreviation", "symbol_id": "NG1545", "symbol": "⧙", "old_fallback": "[LINEARTYPESOP]", "new_fallback": "[LINEAR..]", "timestamp": "2025-05-25T19:01:08.659202"}, {"type": "fallback_abbreviation", "symbol_id": "NG1546", "symbol": "⨈", "old_fallback": "[LINEARTYPES]", "new_fallback": "[LINEAR..]", "timestamp": "2025-05-25T19:01:08.659204"}, {"type": "fallback_abbreviation", "symbol_id": "NG1547", "symbol": "⠴", "old_fallback": "[LINEARTYPESFN]", "new_fallback": "[LINEAR..]", "timestamp": "2025-05-25T19:01:08.659205"}, {"type": "fallback_abbreviation", "symbol_id": "NG1549", "symbol": "⠾", "old_fallback": "[SESSIONTYPESFN]", "new_fallback": "[SESSIO..]", "timestamp": "2025-05-25T19:01:08.659207"}, {"type": "fallback_abbreviation", "symbol_id": "NG1550", "symbol": "⬘", "old_fallback": "[SESSIONTYPESCORE]", "new_fallback": "[SESSIO..]", "timestamp": "2025-05-25T19:01:08.659209"}, {"type": "fallback_abbreviation", "symbol_id": "NG1551", "symbol": "⥩", "old_fallback": "[SESSIONTYPESCTRL]", "new_fallback": "[SESSIO..]", "timestamp": "2025-05-25T19:01:08.659211"}, {"type": "fallback_abbreviation", "symbol_id": "NG1561", "symbol": "⡟", "old_fallback": "[REFINEMENTTYPESFN]", "new_fallback": "[REFINE..]", "timestamp": "2025-05-25T19:01:08.659214"}, {"type": "fallback_abbreviation", "symbol_id": "NG1567", "symbol": "⮯", "old_fallback": "[UNIONTYPESMETA]", "new_fallback": "[UNIONT..]", "timestamp": "2025-05-25T19:01:08.659217"}, {"type": "fallback_abbreviation", "symbol_id": "NG1568", "symbol": "✠", "old_fallback": "[UNIONTYPESCTRL]", "new_fallback": "[UNIONT..]", "timestamp": "2025-05-25T19:01:08.659219"}, {"type": "fallback_abbreviation", "symbol_id": "NG1572", "symbol": "⢲", "old_fallback": "[GRADUALTYPINGPROC]", "new_fallback": "[GRADUA..]", "timestamp": "2025-05-25T19:01:08.659229"}, {"type": "fallback_abbreviation", "symbol_id": "NG1574", "symbol": "⣱", "old_fallback": "[GRADUALTYPING]", "new_fallback": "[GRADUA..]", "timestamp": "2025-05-25T19:01:08.659231"}, {"type": "fallback_abbreviation", "symbol_id": "NG1583", "symbol": "⦖", "old_fallback": "[SPAWNCTRL]", "new_fallback": "[SPAWNC..]", "timestamp": "2025-05-25T19:01:08.659235"}, {"type": "fallback_abbreviation", "symbol_id": "NG1584", "symbol": "⭚", "old_fallback": "[CSPCHANNELSFN]", "new_fallback": "[CSPCHA..]", "timestamp": "2025-05-25T19:01:08.659237"}, {"type": "fallback_abbreviation", "symbol_id": "NG1586", "symbol": "⧖", "old_fallback": "[CSPCHANNELSOP]", "new_fallback": "[CSPCHA..]", "timestamp": "2025-05-25T19:01:08.659239"}, {"type": "fallback_abbreviation", "symbol_id": "NG1594", "symbol": "⡬", "old_fallback": "[MEMORYORDERINGPROC]", "new_fallback": "[MEMORY..]", "timestamp": "2025-05-25T19:01:08.659242"}, {"type": "fallback_abbreviation", "symbol_id": "NG1599", "symbol": "⨷", "old_fallback": "[MEMORYORDERINGFN]", "new_fallback": "[MEMORY..]", "timestamp": "2025-05-25T19:01:08.659244"}, {"type": "fallback_abbreviation", "symbol_id": "NG1602", "symbol": "✁", "old_fallback": "[MEMORYORDERINGCORE]", "new_fallback": "[MEMORY..]", "timestamp": "2025-05-25T19:01:08.659246"}, {"type": "fallback_abbreviation", "symbol_id": "NG1606", "symbol": "⦔", "old_fallback": "[ATOMICOPERATIONSFN]", "new_fallback": "[ATOMIC..]", "timestamp": "2025-05-25T19:01:08.659249"}, {"type": "fallback_abbreviation", "symbol_id": "NG1621", "symbol": "⦐", "old_fallback": "[HAZARDPOINTERS]", "new_fallback": "[HAZARD..]", "timestamp": "2025-05-25T19:01:08.659254"}, {"type": "fallback_abbreviation", "symbol_id": "NG1624", "symbol": "❬", "old_fallback": "[HAZARDPOINTERSCORE]", "new_fallback": "[HAZARD..]", "timestamp": "2025-05-25T19:01:08.659256"}, {"type": "fallback_abbreviation", "symbol_id": "NG1656", "symbol": "⤌", "old_fallback": "[PREDICTIONPROC]", "new_fallback": "[PREDIC..]", "timestamp": "2025-05-25T19:01:08.659264"}, {"type": "fallback_abbreviation", "symbol_id": "NG1658", "symbol": "⤕", "old_fallback": "[TRAININGOP]", "new_fallback": "[TRAINI..]", "timestamp": "2025-05-25T19:01:08.659266"}, {"type": "fallback_abbreviation", "symbol_id": "NG1660", "symbol": "⥃", "old_fallback": "[PREDICTIONMETA]", "new_fallback": "[PREDIC..]", "timestamp": "2025-05-25T19:01:08.659268"}, {"type": "fallback_abbreviation", "symbol_id": "NG1661", "symbol": "⫺", "old_fallback": "[REGRESSIONFN]", "new_fallback": "[REGRES..]", "timestamp": "2025-05-25T19:01:08.659270"}, {"type": "fallback_abbreviation", "symbol_id": "NG1663", "symbol": "⮬", "old_fallback": "[CLASSIFIEROP]", "new_fallback": "[CLASSI..]", "timestamp": "2025-05-25T19:01:08.659272"}, {"type": "fallback_abbreviation", "symbol_id": "NG1667", "symbol": "⪤", "old_fallback": "[PREDICTIONCTRL]", "new_fallback": "[PREDIC..]", "timestamp": "2025-05-25T19:01:08.659275"}, {"type": "fallback_abbreviation", "symbol_id": "NG1671", "symbol": "⢗", "old_fallback": "[DEEPLEARNINGCORE]", "new_fallback": "[DEEPLE..]", "timestamp": "2025-05-25T19:01:08.659277"}, {"type": "fallback_abbreviation", "symbol_id": "NG1672", "symbol": "⦳", "old_fallback": "[DEEPLEARNING]", "new_fallback": "[DEEPLE..]", "timestamp": "2025-05-25T19:01:08.659279"}, {"type": "fallback_abbreviation", "symbol_id": "NG1674", "symbol": "⧞", "old_fallback": "[DEEPLEARNINGSYS]", "new_fallback": "[DEEPLE..]", "timestamp": "2025-05-25T19:01:08.659281"}, {"type": "fallback_abbreviation", "symbol_id": "NG1675", "symbol": "⨘", "old_fallback": "[DEEPLEARNINGOP]", "new_fallback": "[DEEPLE..]", "timestamp": "2025-05-25T19:01:08.659282"}, {"type": "fallback_abbreviation", "symbol_id": "NG1686", "symbol": "⤔", "old_fallback": "[DEEPLEARNINGPROC]", "new_fallback": "[DEEPLE..]", "timestamp": "2025-05-25T19:01:08.659286"}, {"type": "fallback_abbreviation", "symbol_id": "NG1687", "symbol": "⫼", "old_fallback": "[MODELEVALUATIONSYS]", "new_fallback": "[MODELE..]", "timestamp": "2025-05-25T19:01:08.659288"}, {"type": "fallback_abbreviation", "symbol_id": "NG1689", "symbol": "⭜", "old_fallback": "[MODELEVALUATIONOP]", "new_fallback": "[MODELE..]", "timestamp": "2025-05-25T19:01:08.659290"}, {"type": "fallback_abbreviation", "symbol_id": "NG1704", "symbol": "⥴", "old_fallback": "[ENSEMBLEMETHODSOP]", "new_fallback": "[ENSEMB..]", "timestamp": "2025-05-25T19:01:08.659295"}, {"type": "fallback_abbreviation", "symbol_id": "NG1706", "symbol": "⧢", "old_fallback": "[ENSEMBLEMETHODSFN]", "new_fallback": "[ENSEMB..]", "timestamp": "2025-05-25T19:01:08.659297"}, {"type": "fallback_abbreviation", "symbol_id": "NG1707", "symbol": "⯾", "old_fallback": "[ENSEMBLEMETHODS]", "new_fallback": "[ENSEMB..]", "timestamp": "2025-05-25T19:01:08.659299"}, {"type": "fallback_abbreviation", "symbol_id": "NG1793", "symbol": "⮔", "old_fallback": "[MEASURETHEORYSYS]", "new_fallback": "[MEASUR..]", "timestamp": "2025-05-25T19:01:08.659325"}, {"type": "fallback_abbreviation", "symbol_id": "NG1802", "symbol": "✣", "old_fallback": "[MEASURETHEORYCTRL]", "new_fallback": "[MEASUR..]", "timestamp": "2025-05-25T19:01:08.659329"}, {"type": "fallback_abbreviation", "symbol_id": "NG1804", "symbol": "➠", "old_fallback": "[NUMBERTHEORYCTRL]", "new_fallback": "[NUMBER..]", "timestamp": "2025-05-25T19:01:08.659331"}, {"type": "fallback_abbreviation", "symbol_id": "NG1806", "symbol": "⤚", "old_fallback": "[NUMBERTHEORY]", "new_fallback": "[NUMBER..]", "timestamp": "2025-05-25T19:01:08.659333"}, {"type": "fallback_abbreviation", "symbol_id": "NG1807", "symbol": "⤆", "old_fallback": "[NUMBERTHEORYOP]", "new_fallback": "[NUMBER..]", "timestamp": "2025-05-25T19:01:08.659335"}, {"type": "fallback_abbreviation", "symbol_id": "NG1814", "symbol": "⯤", "old_fallback": "[COMBINATORICSMETA]", "new_fallback": "[COMBIN..]", "timestamp": "2025-05-25T19:01:08.659338"}, {"type": "fallback_abbreviation", "symbol_id": "NG1815", "symbol": "⩦", "old_fallback": "[COMBINATORICSSYS]", "new_fallback": "[COMBIN..]", "timestamp": "2025-05-25T19:01:08.659340"}, {"type": "fallback_abbreviation", "symbol_id": "NG1816", "symbol": "⭿", "old_fallback": "[COMBINATORICSOP]", "new_fallback": "[COMBIN..]", "timestamp": "2025-05-25T19:01:08.659342"}, {"type": "fallback_abbreviation", "symbol_id": "NG1819", "symbol": "⩏", "old_fallback": "[COMBINATORICSFN]", "new_fallback": "[COMBIN..]", "timestamp": "2025-05-25T19:01:08.659344"}, {"type": "fallback_abbreviation", "symbol_id": "NG1842", "symbol": "𝞉", "old_fallback": "[KNOWLEDGECTRL]", "new_fallback": "[KNOWLE..]", "timestamp": "2025-05-25T19:01:08.659351"}, {"type": "fallback_abbreviation", "symbol_id": "NG1843", "symbol": "⤣", "old_fallback": "[JUSTIFICATIONSYS]", "new_fallback": "[JUSTIF..]", "timestamp": "2025-05-25T19:01:08.659353"}, {"type": "fallback_abbreviation", "symbol_id": "NG1844", "symbol": "⣡", "old_fallback": "[JUSTIFICATIONPROC]", "new_fallback": "[JUSTIF..]", "timestamp": "2025-05-25T19:01:08.659355"}, {"type": "fallback_abbreviation", "symbol_id": "NG1846", "symbol": "⢢", "old_fallback": "[BELIEFCORE]", "new_fallback": "[BELIEF..]", "timestamp": "2025-05-25T19:01:08.659357"}, {"type": "fallback_abbreviation", "symbol_id": "NG1847", "symbol": "❈", "old_fallback": "[BELIEFCTRL]", "new_fallback": "[BELIEF..]", "timestamp": "2025-05-25T19:01:08.659359"}, {"type": "fallback_abbreviation", "symbol_id": "NG1850", "symbol": "⢞", "old_fallback": "[JUSTIFICATIONCTRL]", "new_fallback": "[JUSTIF..]", "timestamp": "2025-05-25T19:01:08.659361"}, {"type": "fallback_abbreviation", "symbol_id": "NG1861", "symbol": "⢡", "old_fallback": "[ONTOLOGYCORE]", "new_fallback": "[ONTOLO..]", "timestamp": "2025-05-25T19:01:08.659366"}, {"type": "fallback_abbreviation", "symbol_id": "NG1868", "symbol": "⯡", "old_fallback": "[LOGICPHILOSOPHYSYS]", "new_fallback": "[LOGICP..]", "timestamp": "2025-05-25T19:01:08.659369"}, {"type": "fallback_abbreviation", "symbol_id": "NG1873", "symbol": "⨵", "old_fallback": "[PHILOSOPHYOFMIND]", "new_fallback": "[PHILOS..]", "timestamp": "2025-05-25T19:01:08.659371"}, {"type": "fallback_abbreviation", "symbol_id": "NG1889", "symbol": "⮞", "old_fallback": "[METAPHYSICSMETA]", "new_fallback": "[METAPH..]", "timestamp": "2025-05-25T19:01:08.659376"}, {"type": "fallback_abbreviation", "symbol_id": "NG1890", "symbol": "⠕", "old_fallback": "[METAPHYSICSCTRL]", "new_fallback": "[METAPH..]", "timestamp": "2025-05-25T19:01:08.659378"}, {"type": "fallback_abbreviation", "symbol_id": "NG1893", "symbol": "✆", "old_fallback": "[METAPHYSICSFN]", "new_fallback": "[METAPH..]", "timestamp": "2025-05-25T19:01:08.659381"}, {"type": "fallback_abbreviation", "symbol_id": "NG1894", "symbol": "⧻", "old_fallback": "[METAPHYSICS]", "new_fallback": "[METAPH..]", "timestamp": "2025-05-25T19:01:08.659383"}, {"type": "fallback_abbreviation", "symbol_id": "NG1906", "symbol": "⬲", "old_fallback": "[ATTENTIONPROC]", "new_fallback": "[ATTENT..]", "timestamp": "2025-05-25T19:01:08.659387"}, {"type": "fallback_abbreviation", "symbol_id": "NG1907", "symbol": "⨢", "old_fallback": "[COGNITIONPROC]", "new_fallback": "[COGNIT..]", "timestamp": "2025-05-25T19:01:08.659389"}, {"type": "fallback_abbreviation", "symbol_id": "NG1910", "symbol": "⧨", "old_fallback": "[MEMORYCORE]", "new_fallback": "[MEMORY..]", "timestamp": "2025-05-25T19:01:08.659391"}, {"type": "fallback_abbreviation", "symbol_id": "NG1915", "symbol": "⩇", "old_fallback": "[ATTENTIONCTRL]", "new_fallback": "[ATTENT..]", "timestamp": "2025-05-25T19:01:08.659394"}, {"type": "fallback_abbreviation", "symbol_id": "NG1916", "symbol": "➜", "old_fallback": "[MEMORYMODELSOP]", "new_fallback": "[MEMORY..]", "timestamp": "2025-05-25T19:01:08.659395"}, {"type": "fallback_abbreviation", "symbol_id": "NG1918", "symbol": "⩧", "old_fallback": "[MEMORYMODELSPROC]", "new_fallback": "[MEMORY..]", "timestamp": "2025-05-25T19:01:08.659397"}, {"type": "fallback_abbreviation", "symbol_id": "NG1926", "symbol": "✂", "old_fallback": "[ATTENTIONMODELS]", "new_fallback": "[ATTENT..]", "timestamp": "2025-05-25T19:01:08.659401"}, {"type": "fallback_abbreviation", "symbol_id": "NG1928", "symbol": "⠉", "old_fallback": "[ATTENTIONMODELSOP]", "new_fallback": "[ATTENT..]", "timestamp": "2025-05-25T19:01:08.659403"}, {"type": "fallback_abbreviation", "symbol_id": "NG1930", "symbol": "⧺", "old_fallback": "[ATTENTIONMODELSSYS]", "new_fallback": "[ATTENT..]", "timestamp": "2025-05-25T19:01:08.659405"}, {"type": "fallback_abbreviation", "symbol_id": "NG1937", "symbol": "⧰", "old_fallback": "[DECISIONMAKINGCORE]", "new_fallback": "[DECISI..]", "timestamp": "2025-05-25T19:01:08.659409"}, {"type": "fallback_abbreviation", "symbol_id": "NG1940", "symbol": "⣽", "old_fallback": "[DECISIONMAKINGFN]", "new_fallback": "[DECISI..]", "timestamp": "2025-05-25T19:01:08.659411"}, {"type": "fallback_abbreviation", "symbol_id": "NG1941", "symbol": "⥗", "old_fallback": "[DECISIONMAKINGMETA]", "new_fallback": "[DECISI..]", "timestamp": "2025-05-25T19:01:08.659413"}, {"type": "fallback_abbreviation", "symbol_id": "NG1947", "symbol": "✘", "old_fallback": "[PERCEPTIONMODELS]", "new_fallback": "[PERCEP..]", "timestamp": "2025-05-25T19:01:08.659415"}, {"type": "fallback_abbreviation", "symbol_id": "NG1960", "symbol": "⪖", "old_fallback": "[FUTURESYS]", "new_fallback": "[FUTURE..]", "timestamp": "2025-05-25T19:01:08.659420"}, {"type": "fallback_abbreviation", "symbol_id": "NG1961", "symbol": "⭝", "old_fallback": "[EXPERIMENTALFN]", "new_fallback": "[EXPERI..]", "timestamp": "2025-05-25T19:01:08.659422"}, {"type": "fallback_abbreviation", "symbol_id": "NG1962", "symbol": "⠑", "old_fallback": "[EXPERIMENTALOP]", "new_fallback": "[EXPERI..]", "timestamp": "2025-05-25T19:01:08.659423"}, {"type": "fallback_abbreviation", "symbol_id": "NG1963", "symbol": "⨮", "old_fallback": "[RESEARCHCTRL]", "new_fallback": "[RESEAR..]", "timestamp": "2025-05-25T19:01:08.659425"}, {"type": "fallback_abbreviation", "symbol_id": "NG1966", "symbol": "⫈", "old_fallback": "[EXPERIMENTALMETA]", "new_fallback": "[EXPERI..]", "timestamp": "2025-05-25T19:01:08.659427"}, {"type": "fallback_abbreviation", "symbol_id": "NG1969", "symbol": "⠙", "old_fallback": "[NOVELCORE]", "new_fallback": "[NOVELC..]", "timestamp": "2025-05-25T19:01:08.659429"}, {"type": "fallback_abbreviation", "symbol_id": "NG1970", "symbol": "⣧", "old_fallback": "[NOVELMETA]", "new_fallback": "[NOVELM..]", "timestamp": "2025-05-25T19:01:08.659431"}, {"type": "fallback_abbreviation", "symbol_id": "NG1972", "symbol": "⤟", "old_fallback": "[FUTURECORE]", "new_fallback": "[FUTURE..]", "timestamp": "2025-05-25T19:01:08.659434"}, {"type": "fallback_abbreviation", "symbol_id": "NG1974", "symbol": "⪦", "old_fallback": "[RESEARCHAREASMETA]", "new_fallback": "[RESEAR..]", "timestamp": "2025-05-25T19:01:08.659436"}, {"type": "fallback_abbreviation", "symbol_id": "NG1976", "symbol": "✜", "old_fallback": "[RESEARCHAREASSYS]", "new_fallback": "[RESEAR..]", "timestamp": "2025-05-25T19:01:08.659438"}, {"type": "fallback_abbreviation", "symbol_id": "NG1977", "symbol": "⬩", "old_fallback": "[RESEARCHAREAS]", "new_fallback": "[RESEAR..]", "timestamp": "2025-05-25T19:01:08.659439"}, {"type": "fallback_abbreviation", "symbol_id": "NG1978", "symbol": "⧂", "old_fallback": "[RESEARCHAREASCORE]", "new_fallback": "[RESEAR..]", "timestamp": "2025-05-25T19:01:08.659441"}, {"type": "fallback_abbreviation", "symbol_id": "NG1979", "symbol": "✯", "old_fallback": "[RESEARCHAREASOP]", "new_fallback": "[RESEAR..]", "timestamp": "2025-05-25T19:01:08.659443"}, {"type": "fallback_abbreviation", "symbol_id": "NG1988", "symbol": "➵", "old_fallback": "[EMERGINGPARADIGMS]", "new_fallback": "[EMERGI..]", "timestamp": "2025-05-25T19:01:08.659447"}, {"type": "fallback_abbreviation", "symbol_id": "NG1994", "symbol": "⭱", "old_fallback": "[NOVELABSTRACTIONS]", "new_fallback": "[NOVELA..]", "timestamp": "2025-05-25T19:01:08.659450"}, {"type": "fallback_abbreviation", "symbol_id": "NG2003", "symbol": "⡳", "old_fallback": "[EXTENSIONPOINTSOP]", "new_fallback": "[EXTENS..]", "timestamp": "2025-05-25T19:01:08.659454"}, {"type": "fallback_abbreviation", "symbol_id": "NG2004", "symbol": "⤐", "old_fallback": "[EXTENSIONPOINTSFN]", "new_fallback": "[EXTENS..]", "timestamp": "2025-05-25T19:01:08.659455"}, {"type": "fallback_abbreviation", "symbol_id": "NG2009", "symbol": "⬤", "old_fallback": "[EXTENSIONPOINTSSYS]", "new_fallback": "[EXTENS..]", "timestamp": "2025-05-25T19:01:08.659458"}, {"type": "semantic_renaming", "symbol_id": "NG0031", "symbol": "◴", "old_name": "add_1", "new_name": "add_scalar", "old_code": "ng:operator:add_1", "new_code": "ng:operator:add_scalar", "timestamp": "2025-05-25T19:01:08.659494"}, {"type": "semantic_renaming", "symbol_id": "NG0033", "symbol": "⇵", "old_name": "add_2", "new_name": "add_vector", "old_code": "ng:operator:add_2", "new_code": "ng:operator:add_vector", "timestamp": "2025-05-25T19:01:08.659500"}, {"type": "semantic_renaming", "symbol_id": "NG0036", "symbol": "◼", "old_name": "alloc_2", "new_name": "alloc_stack", "old_code": "ng:memory:alloc_2", "new_code": "ng:memory:alloc_stack", "timestamp": "2025-05-25T19:01:08.659505"}, {"type": "semantic_renaming", "symbol_id": "NG0037", "symbol": "⊁", "old_name": "for_1", "new_name": "for_range", "old_code": "ng:flow:for_1", "new_code": "ng:flow:for_range", "timestamp": "2025-05-25T19:01:08.659507"}, {"type": "semantic_renaming", "symbol_id": "NG0038", "symbol": "⋲", "old_name": "add_3", "new_name": "add_matrix", "old_code": "ng:operator:add_3", "new_code": "ng:operator:add_matrix", "timestamp": "2025-05-25T19:01:08.659509"}, {"type": "semantic_renaming", "symbol_id": "NG0039", "symbol": "≙", "old_name": "if_1", "new_name": "if_conditional", "old_code": "ng:flow:if_1", "new_code": "ng:flow:if_conditional", "timestamp": "2025-05-25T19:01:08.659511"}, {"type": "semantic_renaming", "symbol_id": "NG0040", "symbol": "⋂", "old_name": "mul_1", "new_name": "mul_scalar", "old_code": "ng:operator:mul_1", "new_code": "ng:operator:mul_scalar", "timestamp": "2025-05-25T19:01:08.659513"}, {"type": "semantic_renaming", "symbol_id": "NG0046", "symbol": "◗", "old_name": "not_1", "new_name": "not_bitwise", "old_code": "ng:logic:not_1", "new_code": "ng:logic:not_bitwise", "timestamp": "2025-05-25T19:01:08.659516"}, {"type": "semantic_renaming", "symbol_id": "NG0050", "symbol": "◿", "old_name": "add_4", "new_name": "add_complex", "old_code": "ng:operator:add_4", "new_code": "ng:operator:add_complex", "timestamp": "2025-05-25T19:01:08.659519"}, {"type": "semantic_renaming", "symbol_id": "NG0051", "symbol": "∢", "old_name": "if_2", "new_name": "if_ternary", "old_code": "ng:flow:if_2", "new_code": "ng:flow:if_ternary", "timestamp": "2025-05-25T19:01:08.659521"}, {"type": "semantic_renaming", "symbol_id": "NG0052", "symbol": "◇", "old_name": "or_2", "new_name": "or_logical", "old_code": "ng:logic:or_2", "new_code": "ng:logic:or_logical", "timestamp": "2025-05-25T19:01:08.659524"}, {"type": "semantic_renaming", "symbol_id": "NG0055", "symbol": "←", "old_name": "div_1", "new_name": "div_scalar", "old_code": "ng:operator:div_1", "new_code": "ng:operator:div_scalar", "timestamp": "2025-05-25T19:01:08.659526"}, {"type": "semantic_renaming", "symbol_id": "NG0059", "symbol": "⇗", "old_name": "mod_1", "new_name": "mod_integer", "old_code": "ng:operator:mod_1", "new_code": "ng:operator:mod_integer", "timestamp": "2025-05-25T19:01:08.659529"}, {"type": "semantic_renaming", "symbol_id": "NG0060", "symbol": "⊠", "old_name": "implies_2", "new_name": "implies_strict", "old_code": "ng:logic:implies_2", "new_code": "ng:logic:implies_strict", "timestamp": "2025-05-25T19:01:08.659531"}, {"type": "semantic_renaming", "symbol_id": "NG0062", "symbol": "⋉", "old_name": "and_2", "new_name": "and_logical", "old_code": "ng:logic:and_2", "new_code": "ng:logic:and_logical", "timestamp": "2025-05-25T19:01:08.659533"}, {"type": "semantic_renaming", "symbol_id": "NG0063", "symbol": "⋨", "old_name": "implies_3", "new_name": "implies_relevant", "old_code": "ng:logic:implies_3", "new_code": "ng:logic:implies_relevant", "timestamp": "2025-05-25T19:01:08.659536"}, {"type": "semantic_renaming", "symbol_id": "NG0065", "symbol": "⇻", "old_name": "mod_2", "new_name": "mod_polynomial", "old_code": "ng:operator:mod_2", "new_code": "ng:operator:mod_polynomial", "timestamp": "2025-05-25T19:01:08.659538"}, {"type": "semantic_renaming", "symbol_id": "NG0066", "symbol": "≲", "old_name": "and_3", "new_name": "and_fuzzy", "old_code": "ng:logic:and_3", "new_code": "ng:logic:and_fuzzy", "timestamp": "2025-05-25T19:01:08.659540"}, {"type": "semantic_renaming", "symbol_id": "NG0070", "symbol": "◂", "old_name": "not_2", "new_name": "not_logical", "old_code": "ng:logic:not_2", "new_code": "ng:logic:not_logical", "timestamp": "2025-05-25T19:01:08.659543"}, {"type": "semantic_renaming", "symbol_id": "NG0071", "symbol": "◱", "old_name": "mod_3", "new_name": "mod_matrix", "old_code": "ng:operator:mod_3", "new_code": "ng:operator:mod_matrix", "timestamp": "2025-05-25T19:01:08.659545"}, {"type": "semantic_renaming", "symbol_id": "NG0073", "symbol": "⚳", "old_name": "while_1", "new_name": "while_condition", "old_code": "ng:flow:while_1", "new_code": "ng:flow:while_condition", "timestamp": "2025-05-25T19:01:08.659547"}, {"type": "semantic_renaming", "symbol_id": "NG0074", "symbol": "↨", "old_name": "if_3", "new_name": "if_switch", "old_code": "ng:flow:if_3", "new_code": "ng:flow:if_switch", "timestamp": "2025-05-25T19:01:08.659549"}, {"type": "semantic_renaming", "symbol_id": "NG0076", "symbol": "△", "old_name": "div_2", "new_name": "div_vector", "old_code": "ng:operator:div_2", "new_code": "ng:operator:div_vector", "timestamp": "2025-05-25T19:01:08.659553"}, {"type": "semantic_renaming", "symbol_id": "NG0080", "symbol": "⚼", "old_name": "for_3", "new_name": "for_parallel", "old_code": "ng:flow:for_3", "new_code": "ng:flow:for_parallel", "timestamp": "2025-05-25T19:01:08.659556"}, {"type": "semantic_renaming", "symbol_id": "NG0081", "symbol": "◢", "old_name": "deref_3", "new_name": "deref_atomic", "old_code": "ng:memory:deref_3", "new_code": "ng:memory:deref_atomic", "timestamp": "2025-05-25T19:01:08.659558"}, {"type": "semantic_renaming", "symbol_id": "NG0083", "symbol": "◨", "old_name": "add_5", "new_name": "add_modular", "old_code": "ng:operator:add_5", "new_code": "ng:operator:add_modular", "timestamp": "2025-05-25T19:01:08.659562"}, {"type": "semantic_renaming", "symbol_id": "NG0084", "symbol": "⇆", "old_name": "and_4", "new_name": "and_quantum", "old_code": "ng:logic:and_4", "new_code": "ng:logic:and_quantum", "timestamp": "2025-05-25T19:01:08.659565"}, {"type": "semantic_renaming", "symbol_id": "NG0085", "symbol": "⊃", "old_name": "while_2", "new_name": "while_infinite", "old_code": "ng:flow:while_2", "new_code": "ng:flow:while_infinite", "timestamp": "2025-05-25T19:01:08.659567"}, {"type": "semantic_renaming", "symbol_id": "NG0086", "symbol": "◄", "old_name": "while_3", "new_name": "while_bounded", "old_code": "ng:flow:while_3", "new_code": "ng:flow:while_bounded", "timestamp": "2025-05-25T19:01:08.659569"}, {"type": "semantic_renaming", "symbol_id": "NG0088", "symbol": "∗", "old_name": "mul_2", "new_name": "mul_vector", "old_code": "ng:operator:mul_2", "new_code": "ng:operator:mul_vector", "timestamp": "2025-05-25T19:01:08.659571"}, {"type": "semantic_renaming", "symbol_id": "NG0089", "symbol": "∿", "old_name": "implies_4", "new_name": "implies_modal", "old_code": "ng:logic:implies_4", "new_code": "ng:logic:implies_modal", "timestamp": "2025-05-25T19:01:08.659573"}, {"type": "semantic_renaming", "symbol_id": "NG0095", "symbol": "∱", "old_name": "alloc_3", "new_name": "alloc_pool", "old_code": "ng:memory:alloc_3", "new_code": "ng:memory:alloc_pool", "timestamp": "2025-05-25T19:01:08.659576"}, {"type": "semantic_renaming", "symbol_id": "NG0097", "symbol": "∀", "old_name": "div_4", "new_name": "div_integer", "old_code": "ng:operator:div_4", "new_code": "ng:operator:div_integer", "timestamp": "2025-05-25T19:01:08.659578"}, {"type": "semantic_renaming", "symbol_id": "NG0101", "symbol": "⋳", "old_name": "while_4", "new_name": "while_monitored", "old_code": "ng:flow:while_4", "new_code": "ng:flow:while_monitored", "timestamp": "2025-05-25T19:01:08.659581"}, {"type": "semantic_renaming", "symbol_id": "NG0102", "symbol": "◔", "old_name": "if_4", "new_name": "if_guard", "old_code": "ng:flow:if_4", "new_code": "ng:flow:if_guard", "timestamp": "2025-05-25T19:01:08.659583"}, {"type": "semantic_renaming", "symbol_id": "NG0103", "symbol": "⇑", "old_name": "alloc_4", "new_name": "alloc_aligned", "old_code": "ng:memory:alloc_4", "new_code": "ng:memory:alloc_aligned", "timestamp": "2025-05-25T19:01:08.659585"}, {"type": "semantic_renaming", "symbol_id": "NG0104", "symbol": "⋝", "old_name": "or_3", "new_name": "or_fuzzy", "old_code": "ng:logic:or_3", "new_code": "ng:logic:or_fuzzy", "timestamp": "2025-05-25T19:01:08.659587"}, {"type": "semantic_renaming", "symbol_id": "NG0105", "symbol": "⊉", "old_name": "while_5", "new_name": "while_async", "old_code": "ng:flow:while_5", "new_code": "ng:flow:while_async", "timestamp": "2025-05-25T19:01:08.659589"}, {"type": "semantic_renaming", "symbol_id": "NG0108", "symbol": "∯", "old_name": "implies_5", "new_name": "implies_quantum", "old_code": "ng:logic:implies_5", "new_code": "ng:logic:implies_quantum", "timestamp": "2025-05-25T19:01:08.659591"}, {"type": "semantic_renaming", "symbol_id": "NG0109", "symbol": "▰", "old_name": "or_4", "new_name": "or_quantum", "old_code": "ng:logic:or_4", "new_code": "ng:logic:or_quantum", "timestamp": "2025-05-25T19:01:08.659593"}, {"type": "semantic_renaming", "symbol_id": "NG0111", "symbol": "⇎", "old_name": "not_3", "new_name": "not_fuzzy", "old_code": "ng:logic:not_3", "new_code": "ng:logic:not_fuzzy", "timestamp": "2025-05-25T19:01:08.659596"}, {"type": "semantic_renaming", "symbol_id": "NG0112", "symbol": "≹", "old_name": "deref_5", "new_name": "deref_const", "old_code": "ng:memory:deref_5", "new_code": "ng:memory:deref_const", "timestamp": "2025-05-25T19:01:08.659597"}, {"type": "semantic_renaming", "symbol_id": "NG0113", "symbol": "≃", "old_name": "and_5", "new_name": "and_modal", "old_code": "ng:logic:and_5", "new_code": "ng:logic:and_modal", "timestamp": "2025-05-25T19:01:08.659599"}, {"type": "semantic_renaming", "symbol_id": "NG0118", "symbol": "⇷", "old_name": "sub_2", "new_name": "sub_vector", "old_code": "ng:operator:sub_2", "new_code": "ng:operator:sub_vector", "timestamp": "2025-05-25T19:01:08.659602"}, {"type": "semantic_renaming", "symbol_id": "NG0135", "symbol": "⇒", "old_name": "not_5", "new_name": "not_modal", "old_code": "ng:logic:not_5", "new_code": "ng:logic:not_modal", "timestamp": "2025-05-25T19:01:08.659620"}, {"type": "semantic_renaming", "symbol_id": "NG0136", "symbol": "◬", "old_name": "sub_3", "new_name": "sub_matrix", "old_code": "ng:operator:sub_3", "new_code": "ng:operator:sub_matrix", "timestamp": "2025-05-25T19:01:08.659622"}, {"type": "semantic_renaming", "symbol_id": "NG0138", "symbol": "∣", "old_name": "mul_3", "new_name": "mul_matrix", "old_code": "ng:operator:mul_3", "new_code": "ng:operator:mul_matrix", "timestamp": "2025-05-25T19:01:08.659624"}, {"type": "semantic_renaming", "symbol_id": "NG0144", "symbol": "⋓", "old_name": "alloc_5", "new_name": "alloc_shared", "old_code": "ng:memory:alloc_5", "new_code": "ng:memory:alloc_shared", "timestamp": "2025-05-25T19:01:08.659627"}, {"type": "semantic_renaming", "symbol_id": "NG0145", "symbol": "≫", "old_name": "free_2", "new_name": "free_stack", "old_code": "ng:memory:free_2", "new_code": "ng:memory:free_stack", "timestamp": "2025-05-25T19:01:08.659629"}, {"type": "semantic_renaming", "symbol_id": "NG0148", "symbol": "▥", "old_name": "free_3", "new_name": "free_pool", "old_code": "ng:memory:free_3", "new_code": "ng:memory:free_pool", "timestamp": "2025-05-25T19:01:08.659632"}, {"type": "semantic_renaming", "symbol_id": "NG0153", "symbol": "⇕", "old_name": "for_5", "new_name": "for_unrolled", "old_code": "ng:flow:for_5", "new_code": "ng:flow:for_unrolled", "timestamp": "2025-05-25T19:01:08.659635"}, {"type": "semantic_renaming", "symbol_id": "NG0171", "symbol": "↧", "old_name": "sub_4", "new_name": "sub_complex", "old_code": "ng:operator:sub_4", "new_code": "ng:operator:sub_complex", "timestamp": "2025-05-25T19:01:08.659641"}, {"type": "semantic_renaming", "symbol_id": "NG0176", "symbol": "◲", "old_name": "or_5", "new_name": "or_modal", "old_code": "ng:logic:or_5", "new_code": "ng:logic:or_modal", "timestamp": "2025-05-25T19:01:08.659645"}, {"type": "semantic_renaming", "symbol_id": "NG0182", "symbol": "⊳", "old_name": "free_4", "new_name": "free_batch", "old_code": "ng:memory:free_4", "new_code": "ng:memory:free_batch", "timestamp": "2025-05-25T19:01:08.659648"}, {"type": "semantic_renaming", "symbol_id": "NG0188", "symbol": "⇱", "old_name": "div_5", "new_name": "div_modular", "old_code": "ng:operator:div_5", "new_code": "ng:operator:div_modular", "timestamp": "2025-05-25T19:01:08.659651"}, {"type": "semantic_renaming", "symbol_id": "NG0202", "symbol": "≝", "old_name": "sub_5", "new_name": "sub_modular", "old_code": "ng:operator:sub_5", "new_code": "ng:operator:sub_modular", "timestamp": "2025-05-25T19:01:08.659656"}, {"type": "semantic_renaming", "symbol_id": "NG0213", "symbol": "⇖", "old_name": "free_5", "new_name": "free_deferred", "old_code": "ng:memory:free_5", "new_code": "ng:memory:free_deferred", "timestamp": "2025-05-25T19:01:08.659660"}, {"type": "score_update", "symbol_id": "NG0031", "symbol": "◴", "old_score": 95.0, "new_score": 98.0, "timestamp": "2025-05-25T19:01:08.660022"}, {"type": "score_update", "symbol_id": "NG0033", "symbol": "⇵", "old_score": 95.0, "new_score": 98.0, "timestamp": "2025-05-25T19:01:08.660044"}, {"type": "score_update", "symbol_id": "NG0036", "symbol": "◼", "old_score": 95.0, "new_score": 98.0, "timestamp": "2025-05-25T19:01:08.660082"}, {"type": "score_update", "symbol_id": "NG0037", "symbol": "⊁", "old_score": 95.0, "new_score": 98.0, "timestamp": "2025-05-25T19:01:08.660105"}, {"type": "score_update", "symbol_id": "NG0038", "symbol": "⋲", "old_score": 95.0, "new_score": 98.0, "timestamp": "2025-05-25T19:01:08.660124"}, {"type": "score_update", "symbol_id": "NG0039", "symbol": "≙", "old_score": 95.0, "new_score": 98.0, "timestamp": "2025-05-25T19:01:08.660141"}, {"type": "score_update", "symbol_id": "NG0040", "symbol": "⋂", "old_score": 95.0, "new_score": 98.0, "timestamp": "2025-05-25T19:01:08.660158"}, {"type": "score_update", "symbol_id": "NG0046", "symbol": "◗", "old_score": 95.0, "new_score": 98.0, "timestamp": "2025-05-25T19:01:08.660175"}, {"type": "score_update", "symbol_id": "NG0050", "symbol": "◿", "old_score": 95.0, "new_score": 98.0, "timestamp": "2025-05-25T19:01:08.660193"}, {"type": "score_update", "symbol_id": "NG0051", "symbol": "∢", "old_score": 95.0, "new_score": 98.0, "timestamp": "2025-05-25T19:01:08.660210"}, {"type": "score_update", "symbol_id": "NG0052", "symbol": "◇", "old_score": 95.0, "new_score": 98.0, "timestamp": "2025-05-25T19:01:08.660226"}, {"type": "score_update", "symbol_id": "NG0055", "symbol": "←", "old_score": 95.0, "new_score": 98.0, "timestamp": "2025-05-25T19:01:08.660263"}, {"type": "score_update", "symbol_id": "NG0059", "symbol": "⇗", "old_score": 95.0, "new_score": 98.0, "timestamp": "2025-05-25T19:01:08.660319"}, {"type": "score_update", "symbol_id": "NG0060", "symbol": "⊠", "old_score": 95.0, "new_score": 98.0, "timestamp": "2025-05-25T19:01:08.660336"}, {"type": "score_update", "symbol_id": "NG0062", "symbol": "⋉", "old_score": 95.0, "new_score": 98.0, "timestamp": "2025-05-25T19:01:08.660353"}, {"type": "score_update", "symbol_id": "NG0063", "symbol": "⋨", "old_score": 95.0, "new_score": 98.0, "timestamp": "2025-05-25T19:01:08.660371"}, {"type": "score_update", "symbol_id": "NG0065", "symbol": "⇻", "old_score": 95.0, "new_score": 98.0, "timestamp": "2025-05-25T19:01:08.660389"}, {"type": "score_update", "symbol_id": "NG0066", "symbol": "≲", "old_score": 95.0, "new_score": 98.0, "timestamp": "2025-05-25T19:01:08.660406"}, {"type": "score_update", "symbol_id": "NG0070", "symbol": "◂", "old_score": 95.0, "new_score": 98.0, "timestamp": "2025-05-25T19:01:08.660442"}, {"type": "score_update", "symbol_id": "NG0071", "symbol": "◱", "old_score": 95.0, "new_score": 98.0, "timestamp": "2025-05-25T19:01:08.660459"}, {"type": "score_update", "symbol_id": "NG0073", "symbol": "⚳", "old_score": 95.0, "new_score": 98.0, "timestamp": "2025-05-25T19:01:08.660496"}, {"type": "score_update", "symbol_id": "NG0074", "symbol": "↨", "old_score": 95.0, "new_score": 98.0, "timestamp": "2025-05-25T19:01:08.660513"}, {"type": "score_update", "symbol_id": "NG0076", "symbol": "△", "old_score": 95.0, "new_score": 98.0, "timestamp": "2025-05-25T19:01:08.660531"}, {"type": "score_update", "symbol_id": "NG0080", "symbol": "⚼", "old_score": 95.0, "new_score": 98.0, "timestamp": "2025-05-25T19:01:08.660549"}, {"type": "score_update", "symbol_id": "NG0081", "symbol": "◢", "old_score": 95.0, "new_score": 98.0, "timestamp": "2025-05-25T19:01:08.660567"}, {"type": "score_update", "symbol_id": "NG0083", "symbol": "◨", "old_score": 95.0, "new_score": 98.0, "timestamp": "2025-05-25T19:01:08.660605"}, {"type": "score_update", "symbol_id": "NG0084", "symbol": "⇆", "old_score": 95.0, "new_score": 98.0, "timestamp": "2025-05-25T19:01:08.660623"}, {"type": "score_update", "symbol_id": "NG0085", "symbol": "⊃", "old_score": 95.0, "new_score": 98.0, "timestamp": "2025-05-25T19:01:08.660641"}, {"type": "score_update", "symbol_id": "NG0086", "symbol": "◄", "old_score": 95.0, "new_score": 98.0, "timestamp": "2025-05-25T19:01:08.660659"}, {"type": "score_update", "symbol_id": "NG0088", "symbol": "∗", "old_score": 95.0, "new_score": 98.0, "timestamp": "2025-05-25T19:01:08.660678"}, {"type": "score_update", "symbol_id": "NG0089", "symbol": "∿", "old_score": 95.0, "new_score": 98.0, "timestamp": "2025-05-25T19:01:08.660696"}, {"type": "score_update", "symbol_id": "NG0095", "symbol": "∱", "old_score": 95.0, "new_score": 98.0, "timestamp": "2025-05-25T19:01:08.660735"}, {"type": "score_update", "symbol_id": "NG0097", "symbol": "∀", "old_score": 95.0, "new_score": 98.0, "timestamp": "2025-05-25T19:01:08.660754"}, {"type": "score_update", "symbol_id": "NG0101", "symbol": "⋳", "old_score": 95.0, "new_score": 98.0, "timestamp": "2025-05-25T19:01:08.660792"}, {"type": "score_update", "symbol_id": "NG0102", "symbol": "◔", "old_score": 95.0, "new_score": 98.0, "timestamp": "2025-05-25T19:01:08.660811"}, {"type": "score_update", "symbol_id": "NG0103", "symbol": "⇑", "old_score": 95.0, "new_score": 98.0, "timestamp": "2025-05-25T19:01:08.660829"}, {"type": "score_update", "symbol_id": "NG0104", "symbol": "⋝", "old_score": 95.0, "new_score": 98.0, "timestamp": "2025-05-25T19:01:08.660849"}, {"type": "score_update", "symbol_id": "NG0105", "symbol": "⊉", "old_score": 95.0, "new_score": 98.0, "timestamp": "2025-05-25T19:01:08.660867"}, {"type": "score_update", "symbol_id": "NG0108", "symbol": "∯", "old_score": 95.0, "new_score": 98.0, "timestamp": "2025-05-25T19:01:08.660886"}, {"type": "score_update", "symbol_id": "NG0109", "symbol": "▰", "old_score": 95.0, "new_score": 98.0, "timestamp": "2025-05-25T19:01:08.660905"}, {"type": "score_update", "symbol_id": "NG0111", "symbol": "⇎", "old_score": 95.0, "new_score": 98.0, "timestamp": "2025-05-25T19:01:08.660944"}, {"type": "score_update", "symbol_id": "NG0112", "symbol": "≹", "old_score": 95.0, "new_score": 98.0, "timestamp": "2025-05-25T19:01:08.660963"}, {"type": "score_update", "symbol_id": "NG0113", "symbol": "≃", "old_score": 95.0, "new_score": 98.0, "timestamp": "2025-05-25T19:01:08.660982"}, {"type": "score_update", "symbol_id": "NG0118", "symbol": "⇷", "old_score": 95.0, "new_score": 98.0, "timestamp": "2025-05-25T19:01:08.661023"}, {"type": "score_update", "symbol_id": "NG0135", "symbol": "⇒", "old_score": 95.0, "new_score": 98.0, "timestamp": "2025-05-25T19:01:08.661250"}, {"type": "score_update", "symbol_id": "NG0136", "symbol": "◬", "old_score": 95.0, "new_score": 98.0, "timestamp": "2025-05-25T19:01:08.661271"}, {"type": "score_update", "symbol_id": "NG0138", "symbol": "∣", "old_score": 95.0, "new_score": 98.0, "timestamp": "2025-05-25T19:01:08.661313"}, {"type": "score_update", "symbol_id": "NG0144", "symbol": "⋓", "old_score": 95.0, "new_score": 98.0, "timestamp": "2025-05-25T19:01:08.661409"}, {"type": "score_update", "symbol_id": "NG0145", "symbol": "≫", "old_score": 95.0, "new_score": 98.0, "timestamp": "2025-05-25T19:01:08.661429"}, {"type": "score_update", "symbol_id": "NG0148", "symbol": "▥", "old_score": 95.0, "new_score": 98.0, "timestamp": "2025-05-25T19:01:08.661497"}, {"type": "score_update", "symbol_id": "NG0153", "symbol": "⇕", "old_score": 95.0, "new_score": 98.0, "timestamp": "2025-05-25T19:01:08.661580"}, {"type": "score_update", "symbol_id": "NG0171", "symbol": "↧", "old_score": 95.0, "new_score": 98.0, "timestamp": "2025-05-25T19:01:08.661856"}, {"type": "score_update", "symbol_id": "NG0176", "symbol": "◲", "old_score": 95.0, "new_score": 98.0, "timestamp": "2025-05-25T19:01:08.661921"}, {"type": "score_update", "symbol_id": "NG0182", "symbol": "⊳", "old_score": 95.0, "new_score": 98.0, "timestamp": "2025-05-25T19:01:08.661986"}, {"type": "score_update", "symbol_id": "NG0188", "symbol": "⇱", "old_score": 95.0, "new_score": 98.0, "timestamp": "2025-05-25T19:01:08.662049"}, {"type": "score_update", "symbol_id": "NG0202", "symbol": "≝", "old_score": 95.0, "new_score": 98.0, "timestamp": "2025-05-25T19:01:08.662275"}, {"type": "score_update", "symbol_id": "NG0213", "symbol": "⇖", "old_score": 95.0, "new_score": 98.0, "timestamp": "2025-05-25T19:01:08.662423"}, {"type": "score_update", "symbol_id": "NG0514", "symbol": "⏐", "old_score": 90.0, "new_score": 93.0, "timestamp": "2025-05-25T19:01:08.666273"}, {"type": "score_update", "symbol_id": "NG0515", "symbol": "⎆", "old_score": 100.0, "new_score": 98.0, "timestamp": "2025-05-25T19:01:08.666283"}, {"type": "score_update", "symbol_id": "NG0518", "symbol": "⧮", "old_score": 100.0, "new_score": 98.0, "timestamp": "2025-05-25T19:01:08.666287"}, {"type": "score_update", "symbol_id": "NG0519", "symbol": "⪜", "old_score": 90.0, "new_score": 93.0, "timestamp": "2025-05-25T19:01:08.666289"}, {"type": "score_update", "symbol_id": "NG0520", "symbol": "⇡", "old_score": 100.0, "new_score": 98.0, "timestamp": "2025-05-25T19:01:08.666292"}, {"type": "score_update", "symbol_id": "NG0521", "symbol": "⊍", "old_score": 90.0, "new_score": 93.0, "timestamp": "2025-05-25T19:01:08.666295"}, {"type": "score_update", "symbol_id": "NG0522", "symbol": "⧬", "old_score": 100.0, "new_score": 98.0, "timestamp": "2025-05-25T19:01:08.666297"}, {"type": "score_update", "symbol_id": "NG0523", "symbol": "⍛", "old_score": 100.0, "new_score": 98.0, "timestamp": "2025-05-25T19:01:08.666303"}, {"type": "score_update", "symbol_id": "NG0524", "symbol": "⏃", "old_score": 90.0, "new_score": 93.0, "timestamp": "2025-05-25T19:01:08.666307"}, {"type": "score_update", "symbol_id": "NG0526", "symbol": "⦵", "old_score": 90.0, "new_score": 93.0, "timestamp": "2025-05-25T19:01:08.666310"}, {"type": "score_update", "symbol_id": "NG0527", "symbol": "⧄", "old_score": 100.0, "new_score": 98.0, "timestamp": "2025-05-25T19:01:08.666313"}, {"type": "score_update", "symbol_id": "NG0528", "symbol": "⩓", "old_score": 100.0, "new_score": 98.0, "timestamp": "2025-05-25T19:01:08.666316"}, {"type": "score_update", "symbol_id": "NG0529", "symbol": "⎭", "old_score": 90.0, "new_score": 93.0, "timestamp": "2025-05-25T19:01:08.666319"}, {"type": "score_update", "symbol_id": "NG0530", "symbol": "⪫", "old_score": 100.0, "new_score": 98.0, "timestamp": "2025-05-25T19:01:08.666322"}, {"type": "score_update", "symbol_id": "NG0531", "symbol": "⊲", "old_score": 100.0, "new_score": 98.0, "timestamp": "2025-05-25T19:01:08.666325"}, {"type": "score_update", "symbol_id": "NG0532", "symbol": "⩢", "old_score": 100.0, "new_score": 98.0, "timestamp": "2025-05-25T19:01:08.666328"}, {"type": "score_update", "symbol_id": "NG0533", "symbol": "⪣", "old_score": 90.0, "new_score": 93.0, "timestamp": "2025-05-25T19:01:08.666331"}, {"type": "score_update", "symbol_id": "NG0536", "symbol": "⌾", "old_score": 100.0, "new_score": 98.0, "timestamp": "2025-05-25T19:01:08.666335"}, {"type": "score_update", "symbol_id": "NG0538", "symbol": "⩷", "old_score": 100.0, "new_score": 98.0, "timestamp": "2025-05-25T19:01:08.666338"}, {"type": "score_update", "symbol_id": "NG0539", "symbol": "▪", "old_score": 90.0, "new_score": 93.0, "timestamp": "2025-05-25T19:01:08.666341"}, {"type": "score_update", "symbol_id": "NG0542", "symbol": "⪲", "old_score": 100.0, "new_score": 98.0, "timestamp": "2025-05-25T19:01:08.666345"}, {"type": "score_update", "symbol_id": "NG0543", "symbol": "⏟", "old_score": 100.0, "new_score": 98.0, "timestamp": "2025-05-25T19:01:08.666348"}, {"type": "score_update", "symbol_id": "NG0544", "symbol": "⎓", "old_score": 100.0, "new_score": 98.0, "timestamp": "2025-05-25T19:01:08.666351"}, {"type": "score_update", "symbol_id": "NG0545", "symbol": "≘", "old_score": 100.0, "new_score": 98.0, "timestamp": "2025-05-25T19:01:08.666354"}, {"type": "score_update", "symbol_id": "NG0546", "symbol": "⧗", "old_score": 100.0, "new_score": 98.0, "timestamp": "2025-05-25T19:01:08.666358"}, {"type": "score_update", "symbol_id": "NG0548", "symbol": "⎑", "old_score": 90.0, "new_score": 93.0, "timestamp": "2025-05-25T19:01:08.666361"}, {"type": "score_update", "symbol_id": "NG0549", "symbol": "⏰", "old_score": 90.0, "new_score": 93.0, "timestamp": "2025-05-25T19:01:08.666365"}, {"type": "score_update", "symbol_id": "NG0551", "symbol": "⎚", "old_score": 100.0, "new_score": 98.0, "timestamp": "2025-05-25T19:01:08.666369"}, {"type": "score_update", "symbol_id": "NG0553", "symbol": "≊", "old_score": 90.0, "new_score": 93.0, "timestamp": "2025-05-25T19:01:08.666373"}, {"type": "score_update", "symbol_id": "NG0555", "symbol": "⎁", "old_score": 90.0, "new_score": 93.0, "timestamp": "2025-05-25T19:01:08.666376"}, {"type": "score_update", "symbol_id": "NG0556", "symbol": "⎯", "old_score": 100.0, "new_score": 98.0, "timestamp": "2025-05-25T19:01:08.666380"}, {"type": "score_update", "symbol_id": "NG0557", "symbol": "≵", "old_score": 90.0, "new_score": 93.0, "timestamp": "2025-05-25T19:01:08.666384"}, {"type": "score_update", "symbol_id": "NG0558", "symbol": "⎧", "old_score": 90.0, "new_score": 93.0, "timestamp": "2025-05-25T19:01:08.666388"}, {"type": "score_update", "symbol_id": "NG0561", "symbol": "⦗", "old_score": 90.0, "new_score": 93.0, "timestamp": "2025-05-25T19:01:08.666392"}, {"type": "score_update", "symbol_id": "NG0563", "symbol": "⫳", "old_score": 100.0, "new_score": 98.0, "timestamp": "2025-05-25T19:01:08.666396"}, {"type": "score_update", "symbol_id": "NG0565", "symbol": "⊮", "old_score": 90.0, "new_score": 93.0, "timestamp": "2025-05-25T19:01:08.666401"}, {"type": "score_update", "symbol_id": "NG0883", "symbol": "⩒", "old_score": 95.0, "new_score": 98.0, "timestamp": "2025-05-25T19:01:08.666452"}, {"type": "score_update", "symbol_id": "NG0885", "symbol": "⣖", "old_score": 95.0, "new_score": 98.0, "timestamp": "2025-05-25T19:01:08.666456"}, {"type": "score_update", "symbol_id": "NG0888", "symbol": "⢠", "old_score": 95.0, "new_score": 98.0, "timestamp": "2025-05-25T19:01:08.666461"}, {"type": "score_update", "symbol_id": "NG0889", "symbol": "⥮", "old_score": 95.0, "new_score": 98.0, "timestamp": "2025-05-25T19:01:08.666465"}, {"type": "score_update", "symbol_id": "NG0893", "symbol": "⠏", "old_score": 95.0, "new_score": 98.0, "timestamp": "2025-05-25T19:01:08.666471"}, {"type": "score_update", "symbol_id": "NG0896", "symbol": "⨪", "old_score": 95.0, "new_score": 98.0, "timestamp": "2025-05-25T19:01:08.666476"}, {"type": "score_update", "symbol_id": "NG0897", "symbol": "⨄", "old_score": 95.0, "new_score": 98.0, "timestamp": "2025-05-25T19:01:08.666480"}, {"type": "score_update", "symbol_id": "NG0901", "symbol": "⭭", "old_score": 95.0, "new_score": 98.0, "timestamp": "2025-05-25T19:01:08.666485"}, {"type": "score_update", "symbol_id": "NG0906", "symbol": "❣", "old_score": 95.0, "new_score": 98.0, "timestamp": "2025-05-25T19:01:08.666490"}, {"type": "score_update", "symbol_id": "NG0907", "symbol": "⠌", "old_score": 95.0, "new_score": 98.0, "timestamp": "2025-05-25T19:01:08.666495"}, {"type": "score_update", "symbol_id": "NG0910", "symbol": "⢓", "old_score": 95.0, "new_score": 98.0, "timestamp": "2025-05-25T19:01:08.666500"}, {"type": "score_update", "symbol_id": "NG0911", "symbol": "⬹", "old_score": 95.0, "new_score": 98.0, "timestamp": "2025-05-25T19:01:08.666504"}, {"type": "score_update", "symbol_id": "NG0912", "symbol": "✝", "old_score": 95.0, "new_score": 98.0, "timestamp": "2025-05-25T19:01:08.666509"}, {"type": "score_update", "symbol_id": "NG0915", "symbol": "⧟", "old_score": 95.0, "new_score": 98.0, "timestamp": "2025-05-25T19:01:08.666514"}, {"type": "score_update", "symbol_id": "NG0916", "symbol": "⥿", "old_score": 95.0, "new_score": 98.0, "timestamp": "2025-05-25T19:01:08.666519"}, {"type": "score_update", "symbol_id": "NG0917", "symbol": "⬫", "old_score": 95.0, "new_score": 98.0, "timestamp": "2025-05-25T19:01:08.666524"}, {"type": "score_update", "symbol_id": "NG0920", "symbol": "⠣", "old_score": 95.0, "new_score": 98.0, "timestamp": "2025-05-25T19:01:08.666530"}, {"type": "score_update", "symbol_id": "NG0922", "symbol": "⧡", "old_score": 95.0, "new_score": 98.0, "timestamp": "2025-05-25T19:01:08.666535"}, {"type": "score_update", "symbol_id": "NG0926", "symbol": "⨊", "old_score": 95.0, "new_score": 98.0, "timestamp": "2025-05-25T19:01:08.666540"}, {"type": "score_update", "symbol_id": "NG0927", "symbol": "⫙", "old_score": 95.0, "new_score": 98.0, "timestamp": "2025-05-25T19:01:08.666545"}, {"type": "score_update", "symbol_id": "NG0932", "symbol": "⦎", "old_score": 95.0, "new_score": 98.0, "timestamp": "2025-05-25T19:01:08.666551"}, {"type": "score_update", "symbol_id": "NG0937", "symbol": "⣋", "old_score": 95.0, "new_score": 98.0, "timestamp": "2025-05-25T19:01:08.666557"}, {"type": "score_update", "symbol_id": "NG0938", "symbol": "⬈", "old_score": 95.0, "new_score": 98.0, "timestamp": "2025-05-25T19:01:08.666562"}, {"type": "score_update", "symbol_id": "NG0941", "symbol": "⢐", "old_score": 95.0, "new_score": 98.0, "timestamp": "2025-05-25T19:01:08.666568"}, {"type": "score_update", "symbol_id": "NG0949", "symbol": "⡗", "old_score": 95.0, "new_score": 98.0, "timestamp": "2025-05-25T19:01:08.666575"}, {"type": "score_update", "symbol_id": "NG0952", "symbol": "⪠", "old_score": 95.0, "new_score": 98.0, "timestamp": "2025-05-25T19:01:08.666581"}, {"type": "score_update", "symbol_id": "NG0960", "symbol": "⥳", "old_score": 95.0, "new_score": 98.0, "timestamp": "2025-05-25T19:01:08.666588"}, {"type": "score_update", "symbol_id": "NG0969", "symbol": "⮸", "old_score": 95.0, "new_score": 98.0, "timestamp": "2025-05-25T19:01:08.666595"}, {"type": "score_update", "symbol_id": "NG0973", "symbol": "⪘", "old_score": 95.0, "new_score": 98.0, "timestamp": "2025-05-25T19:01:08.666601"}, {"type": "score_update", "symbol_id": "NG0977", "symbol": "⨞", "old_score": 95.0, "new_score": 98.0, "timestamp": "2025-05-25T19:01:08.666608"}, {"type": "score_update", "symbol_id": "NG0981", "symbol": "⧚", "old_score": 95.0, "new_score": 98.0, "timestamp": "2025-05-25T19:01:08.666617"}, {"type": "score_update", "symbol_id": "NG0982", "symbol": "⪟", "old_score": 95.0, "new_score": 98.0, "timestamp": "2025-05-25T19:01:08.666623"}, {"type": "score_update", "symbol_id": "NG0993", "symbol": "⧠", "old_score": 95.0, "new_score": 98.0, "timestamp": "2025-05-25T19:01:08.666631"}, {"type": "score_update", "symbol_id": "NG0995", "symbol": "⧲", "old_score": 95.0, "new_score": 98.0, "timestamp": "2025-05-25T19:01:08.666638"}, {"type": "score_update", "symbol_id": "NG1006", "symbol": "⯐", "old_score": 95.0, "new_score": 98.0, "timestamp": "2025-05-25T19:01:08.666646"}, {"type": "score_update", "symbol_id": "NG1007", "symbol": "⣐", "old_score": 95.0, "new_score": 98.0, "timestamp": "2025-05-25T19:01:08.666652"}, {"type": "score_update", "symbol_id": "NG1008", "symbol": "⬿", "old_score": 95.0, "new_score": 98.0, "timestamp": "2025-05-25T19:01:08.666658"}, {"type": "score_update", "symbol_id": "NG1016", "symbol": "➴", "old_score": 95.0, "new_score": 98.0, "timestamp": "2025-05-25T19:01:08.666666"}, {"type": "score_update", "symbol_id": "NG1019", "symbol": "⠀", "old_score": 95.0, "new_score": 98.0, "timestamp": "2025-05-25T19:01:08.666673"}, {"type": "score_update", "symbol_id": "NG1040", "symbol": "➞", "old_score": 95.0, "new_score": 98.0, "timestamp": "2025-05-25T19:01:08.666683"}, {"type": "score_update", "symbol_id": "NG1050", "symbol": "✱", "old_score": 95.0, "new_score": 98.0, "timestamp": "2025-05-25T19:01:08.666691"}, {"type": "score_update", "symbol_id": "NG1051", "symbol": "⤭", "old_score": 95.0, "new_score": 98.0, "timestamp": "2025-05-25T19:01:08.666698"}, {"type": "score_update", "symbol_id": "NG1061", "symbol": "⪴", "old_score": 95.0, "new_score": 98.0, "timestamp": "2025-05-25T19:01:08.666706"}, {"type": "score_update", "symbol_id": "NG1064", "symbol": "⥔", "old_score": 95.0, "new_score": 98.0, "timestamp": "2025-05-25T19:01:08.666713"}, {"type": "score_update", "symbol_id": "NG1066", "symbol": "⡔", "old_score": 95.0, "new_score": 98.0, "timestamp": "2025-05-25T19:01:08.666720"}, {"type": "score_update", "symbol_id": "NG1072", "symbol": "⦩", "old_score": 95.0, "new_score": 98.0, "timestamp": "2025-05-25T19:01:08.666728"}, {"type": "score_update", "symbol_id": "NG1074", "symbol": "⠎", "old_score": 95.0, "new_score": 98.0, "timestamp": "2025-05-25T19:01:08.666734"}, {"type": "score_update", "symbol_id": "NG1076", "symbol": "❛", "old_score": 95.0, "new_score": 98.0, "timestamp": "2025-05-25T19:01:08.666741"}, {"type": "score_update", "symbol_id": "NG1082", "symbol": "⠹", "old_score": 95.0, "new_score": 98.0, "timestamp": "2025-05-25T19:01:08.666749"}, {"type": "score_update", "symbol_id": "NG1097", "symbol": "⣛", "old_score": 95.0, "new_score": 98.0, "timestamp": "2025-05-25T19:01:08.666760"}, {"type": "score_update", "symbol_id": "NG1108", "symbol": "⫅", "old_score": 95.0, "new_score": 98.0, "timestamp": "2025-05-25T19:01:08.666769"}, {"type": "score_update", "symbol_id": "NG1113", "symbol": "⤙", "old_score": 95.0, "new_score": 98.0, "timestamp": "2025-05-25T19:01:08.666776"}, {"type": "score_update", "symbol_id": "NG1127", "symbol": "⥼", "old_score": 95.0, "new_score": 98.0, "timestamp": "2025-05-25T19:01:08.666786"}, {"type": "score_update", "symbol_id": "NG1130", "symbol": "⡣", "old_score": 95.0, "new_score": 98.0, "timestamp": "2025-05-25T19:01:08.666793"}, {"type": "score_update", "symbol_id": "NG1138", "symbol": "➤", "old_score": 95.0, "new_score": 98.0, "timestamp": "2025-05-25T19:01:08.666802"}, {"type": "score_update", "symbol_id": "NG1145", "symbol": "⡑", "old_score": 95.0, "new_score": 98.0, "timestamp": "2025-05-25T19:01:08.666810"}, {"type": "score_update", "symbol_id": "NG1152", "symbol": "⧅", "old_score": 95.0, "new_score": 98.0, "timestamp": "2025-05-25T19:01:08.666821"}, {"type": "score_update", "symbol_id": "NG1156", "symbol": "❱", "old_score": 95.0, "new_score": 98.0, "timestamp": "2025-05-25T19:01:08.666829"}, {"type": "score_update", "symbol_id": "NG1188", "symbol": "⩶", "old_score": 95.0, "new_score": 98.0, "timestamp": "2025-05-25T19:01:08.666842"}, {"type": "score_update", "symbol_id": "NG1189", "symbol": "⧿", "old_score": 95.0, "new_score": 98.0, "timestamp": "2025-05-25T19:01:08.666850"}, {"type": "score_update", "symbol_id": "NG1190", "symbol": "⦼", "old_score": 95.0, "new_score": 98.0, "timestamp": "2025-05-25T19:01:08.666857"}, {"type": "score_update", "symbol_id": "NG1192", "symbol": "⥈", "old_score": 95.0, "new_score": 98.0, "timestamp": "2025-05-25T19:01:08.666864"}, {"type": "score_update", "symbol_id": "NG1197", "symbol": "⥯", "old_score": 95.0, "new_score": 98.0, "timestamp": "2025-05-25T19:01:08.666872"}, {"type": "score_update", "symbol_id": "NG1208", "symbol": "⢁", "old_score": 95.0, "new_score": 98.0, "timestamp": "2025-05-25T19:01:08.666882"}, {"type": "score_update", "symbol_id": "NG1211", "symbol": "⡶", "old_score": 95.0, "new_score": 98.0, "timestamp": "2025-05-25T19:01:08.666890"}, {"type": "score_update", "symbol_id": "NG1235", "symbol": "⨹", "old_score": 95.0, "new_score": 98.0, "timestamp": "2025-05-25T19:01:08.666902"}, {"type": "score_update", "symbol_id": "NG1236", "symbol": "⡭", "old_score": 95.0, "new_score": 98.0, "timestamp": "2025-05-25T19:01:08.666910"}, {"type": "score_update", "symbol_id": "NG1237", "symbol": "⩟", "old_score": 95.0, "new_score": 98.0, "timestamp": "2025-05-25T19:01:08.666917"}, {"type": "score_update", "symbol_id": "NG1249", "symbol": "⭐", "old_score": 95.0, "new_score": 98.0, "timestamp": "2025-05-25T19:01:08.666927"}, {"type": "score_update", "symbol_id": "NG1255", "symbol": "⮭", "old_score": 95.0, "new_score": 98.0, "timestamp": "2025-05-25T19:01:08.666936"}, {"type": "score_update", "symbol_id": "NG1258", "symbol": "⢵", "old_score": 95.0, "new_score": 98.0, "timestamp": "2025-05-25T19:01:08.666945"}, {"type": "score_update", "symbol_id": "NG1263", "symbol": "✟", "old_score": 95.0, "new_score": 98.0, "timestamp": "2025-05-25T19:01:08.666953"}, {"type": "score_update", "symbol_id": "NG1267", "symbol": "⣔", "old_score": 95.0, "new_score": 98.0, "timestamp": "2025-05-25T19:01:08.666962"}, {"type": "score_update", "symbol_id": "NG1269", "symbol": "⨤", "old_score": 95.0, "new_score": 98.0, "timestamp": "2025-05-25T19:01:08.666971"}, {"type": "score_update", "symbol_id": "NG1272", "symbol": "✪", "old_score": 95.0, "new_score": 98.0, "timestamp": "2025-05-25T19:01:08.666979"}, {"type": "score_update", "symbol_id": "NG1273", "symbol": "⮹", "old_score": 95.0, "new_score": 98.0, "timestamp": "2025-05-25T19:01:08.666987"}, {"type": "score_update", "symbol_id": "NG1277", "symbol": "⦤", "old_score": 95.0, "new_score": 98.0, "timestamp": "2025-05-25T19:01:08.666996"}, {"type": "score_update", "symbol_id": "NG1279", "symbol": "⥠", "old_score": 95.0, "new_score": 98.0, "timestamp": "2025-05-25T19:01:08.667005"}, {"type": "score_update", "symbol_id": "NG1282", "symbol": "⪂", "old_score": 95.0, "new_score": 98.0, "timestamp": "2025-05-25T19:01:08.667014"}, {"type": "score_update", "symbol_id": "NG1285", "symbol": "⪚", "old_score": 95.0, "new_score": 98.0, "timestamp": "2025-05-25T19:01:08.667022"}, {"type": "score_update", "symbol_id": "NG1289", "symbol": "⤥", "old_score": 95.0, "new_score": 98.0, "timestamp": "2025-05-25T19:01:08.667031"}, {"type": "score_update", "symbol_id": "NG1293", "symbol": "⬪", "old_score": 95.0, "new_score": 98.0, "timestamp": "2025-05-25T19:01:08.667041"}, {"type": "score_update", "symbol_id": "NG1298", "symbol": "⭦", "old_score": 95.0, "new_score": 98.0, "timestamp": "2025-05-25T19:01:08.667050"}, {"type": "score_update", "symbol_id": "NG1301", "symbol": "⯼", "old_score": 95.0, "new_score": 98.0, "timestamp": "2025-05-25T19:01:08.667059"}, {"type": "score_update", "symbol_id": "NG1302", "symbol": "⧉", "old_score": 95.0, "new_score": 98.0, "timestamp": "2025-05-25T19:01:08.667068"}, {"type": "score_update", "symbol_id": "NG1309", "symbol": "⤫", "old_score": 95.0, "new_score": 98.0, "timestamp": "2025-05-25T19:01:08.667078"}, {"type": "score_update", "symbol_id": "NG1310", "symbol": "⧽", "old_score": 95.0, "new_score": 98.0, "timestamp": "2025-05-25T19:01:08.667087"}, {"type": "score_update", "symbol_id": "NG1311", "symbol": "⧫", "old_score": 95.0, "new_score": 98.0, "timestamp": "2025-05-25T19:01:08.667095"}, {"type": "score_update", "symbol_id": "NG1314", "symbol": "⠗", "old_score": 95.0, "new_score": 98.0, "timestamp": "2025-05-25T19:01:08.667105"}, {"type": "score_update", "symbol_id": "NG1315", "symbol": "⥏", "old_score": 95.0, "new_score": 98.0, "timestamp": "2025-05-25T19:01:08.667113"}, {"type": "score_update", "symbol_id": "NG1317", "symbol": "❚", "old_score": 95.0, "new_score": 98.0, "timestamp": "2025-05-25T19:01:08.667122"}, {"type": "score_update", "symbol_id": "NG1360", "symbol": "⠰", "old_score": 95.0, "new_score": 98.0, "timestamp": "2025-05-25T19:01:08.667140"}, {"type": "score_update", "symbol_id": "NG1364", "symbol": "⮨", "old_score": 95.0, "new_score": 98.0, "timestamp": "2025-05-25T19:01:08.667178"}, {"type": "score_update", "symbol_id": "NG1365", "symbol": "⫔", "old_score": 95.0, "new_score": 98.0, "timestamp": "2025-05-25T19:01:08.667187"}, {"type": "score_update", "symbol_id": "NG1370", "symbol": "⭕", "old_score": 95.0, "new_score": 98.0, "timestamp": "2025-05-25T19:01:08.667199"}, {"type": "score_update", "symbol_id": "NG1371", "symbol": "⡄", "old_score": 95.0, "new_score": 98.0, "timestamp": "2025-05-25T19:01:08.667208"}, {"type": "score_update", "symbol_id": "NG1375", "symbol": "⮎", "old_score": 95.0, "new_score": 98.0, "timestamp": "2025-05-25T19:01:08.667218"}, {"type": "score_update", "symbol_id": "NG1377", "symbol": "❞", "old_score": 95.0, "new_score": 98.0, "timestamp": "2025-05-25T19:01:08.667228"}, {"type": "score_update", "symbol_id": "NG1378", "symbol": "⪩", "old_score": 95.0, "new_score": 98.0, "timestamp": "2025-05-25T19:01:08.667237"}, {"type": "score_update", "symbol_id": "NG1380", "symbol": "⮀", "old_score": 95.0, "new_score": 98.0, "timestamp": "2025-05-25T19:01:08.667246"}, {"type": "score_update", "symbol_id": "NG1381", "symbol": "⤉", "old_score": 95.0, "new_score": 98.0, "timestamp": "2025-05-25T19:01:08.667256"}, {"type": "score_update", "symbol_id": "NG1393", "symbol": "❴", "old_score": 95.0, "new_score": 98.0, "timestamp": "2025-05-25T19:01:08.667268"}, {"type": "score_update", "symbol_id": "NG1394", "symbol": "⧜", "old_score": 95.0, "new_score": 98.0, "timestamp": "2025-05-25T19:01:08.667278"}, {"type": "score_update", "symbol_id": "NG1396", "symbol": "⣥", "old_score": 95.0, "new_score": 98.0, "timestamp": "2025-05-25T19:01:08.667287"}, {"type": "score_update", "symbol_id": "NG1416", "symbol": "⨶", "old_score": 95.0, "new_score": 98.0, "timestamp": "2025-05-25T19:01:08.667303"}, {"type": "score_update", "symbol_id": "NG1418", "symbol": "⮃", "old_score": 95.0, "new_score": 98.0, "timestamp": "2025-05-25T19:01:08.667313"}, {"type": "score_update", "symbol_id": "NG1429", "symbol": "⯯", "old_score": 95.0, "new_score": 98.0, "timestamp": "2025-05-25T19:01:08.667326"}, {"type": "score_update", "symbol_id": "NG1432", "symbol": "⯉", "old_score": 95.0, "new_score": 98.0, "timestamp": "2025-05-25T19:01:08.667337"}, {"type": "score_update", "symbol_id": "NG1434", "symbol": "⥞", "old_score": 95.0, "new_score": 98.0, "timestamp": "2025-05-25T19:01:08.667351"}, {"type": "score_update", "symbol_id": "NG1437", "symbol": "⩿", "old_score": 95.0, "new_score": 98.0, "timestamp": "2025-05-25T19:01:08.667363"}, {"type": "score_update", "symbol_id": "NG1443", "symbol": "➷", "old_score": 95.0, "new_score": 98.0, "timestamp": "2025-05-25T19:01:08.667375"}, {"type": "score_update", "symbol_id": "NG1450", "symbol": "➡", "old_score": 95.0, "new_score": 98.0, "timestamp": "2025-05-25T19:01:08.667387"}, {"type": "score_update", "symbol_id": "NG1451", "symbol": "⣫", "old_score": 95.0, "new_score": 98.0, "timestamp": "2025-05-25T19:01:08.667398"}, {"type": "score_update", "symbol_id": "NG1452", "symbol": "⫡", "old_score": 95.0, "new_score": 98.0, "timestamp": "2025-05-25T19:01:08.667408"}, {"type": "score_update", "symbol_id": "NG1454", "symbol": "⥢", "old_score": 95.0, "new_score": 98.0, "timestamp": "2025-05-25T19:01:08.667419"}, {"type": "score_update", "symbol_id": "NG1459", "symbol": "➛", "old_score": 95.0, "new_score": 98.0, "timestamp": "2025-05-25T19:01:08.667430"}, {"type": "score_update", "symbol_id": "NG1460", "symbol": "⦱", "old_score": 95.0, "new_score": 98.0, "timestamp": "2025-05-25T19:01:08.667440"}, {"type": "score_update", "symbol_id": "NG1471", "symbol": "⩗", "old_score": 95.0, "new_score": 98.0, "timestamp": "2025-05-25T19:01:08.667454"}, {"type": "score_update", "symbol_id": "NG1472", "symbol": "⤜", "old_score": 95.0, "new_score": 98.0, "timestamp": "2025-05-25T19:01:08.667464"}, {"type": "score_update", "symbol_id": "NG1502", "symbol": "⦺", "old_score": 95.0, "new_score": 98.0, "timestamp": "2025-05-25T19:01:08.667480"}, {"type": "score_update", "symbol_id": "NG1503", "symbol": "⫛", "old_score": 95.0, "new_score": 98.0, "timestamp": "2025-05-25T19:01:08.667491"}, {"type": "score_update", "symbol_id": "NG1507", "symbol": "⧤", "old_score": 95.0, "new_score": 98.0, "timestamp": "2025-05-25T19:01:08.667502"}, {"type": "score_update", "symbol_id": "NG1515", "symbol": "⫕", "old_score": 95.0, "new_score": 98.0, "timestamp": "2025-05-25T19:01:08.667514"}, {"type": "score_update", "symbol_id": "NG1528", "symbol": "⤬", "old_score": 95.0, "new_score": 98.0, "timestamp": "2025-05-25T19:01:08.667528"}, {"type": "score_update", "symbol_id": "NG1542", "symbol": "⫌", "old_score": 95.0, "new_score": 98.0, "timestamp": "2025-05-25T19:01:08.667542"}, {"type": "score_update", "symbol_id": "NG1543", "symbol": "⦮", "old_score": 95.0, "new_score": 98.0, "timestamp": "2025-05-25T19:01:08.667553"}, {"type": "score_update", "symbol_id": "NG1545", "symbol": "⧙", "old_score": 95.0, "new_score": 98.0, "timestamp": "2025-05-25T19:01:08.667564"}, {"type": "score_update", "symbol_id": "NG1546", "symbol": "⨈", "old_score": 95.0, "new_score": 98.0, "timestamp": "2025-05-25T19:01:08.667575"}, {"type": "score_update", "symbol_id": "NG1547", "symbol": "⠴", "old_score": 95.0, "new_score": 98.0, "timestamp": "2025-05-25T19:01:08.667586"}, {"type": "score_update", "symbol_id": "NG1549", "symbol": "⠾", "old_score": 95.0, "new_score": 98.0, "timestamp": "2025-05-25T19:01:08.667597"}, {"type": "score_update", "symbol_id": "NG1550", "symbol": "⬘", "old_score": 95.0, "new_score": 98.0, "timestamp": "2025-05-25T19:01:08.667608"}, {"type": "score_update", "symbol_id": "NG1551", "symbol": "⥩", "old_score": 95.0, "new_score": 98.0, "timestamp": "2025-05-25T19:01:08.667619"}, {"type": "score_update", "symbol_id": "NG1561", "symbol": "⡟", "old_score": 95.0, "new_score": 98.0, "timestamp": "2025-05-25T19:01:08.667632"}, {"type": "score_update", "symbol_id": "NG1567", "symbol": "⮯", "old_score": 95.0, "new_score": 98.0, "timestamp": "2025-05-25T19:01:08.667645"}, {"type": "score_update", "symbol_id": "NG1568", "symbol": "✠", "old_score": 95.0, "new_score": 98.0, "timestamp": "2025-05-25T19:01:08.667656"}, {"type": "score_update", "symbol_id": "NG1572", "symbol": "⢲", "old_score": 95.0, "new_score": 98.0, "timestamp": "2025-05-25T19:01:08.667668"}, {"type": "score_update", "symbol_id": "NG1574", "symbol": "⣱", "old_score": 95.0, "new_score": 98.0, "timestamp": "2025-05-25T19:01:08.667679"}, {"type": "score_update", "symbol_id": "NG1583", "symbol": "⦖", "old_score": 95.0, "new_score": 98.0, "timestamp": "2025-05-25T19:01:08.667693"}, {"type": "score_update", "symbol_id": "NG1584", "symbol": "⭚", "old_score": 95.0, "new_score": 98.0, "timestamp": "2025-05-25T19:01:08.667705"}, {"type": "score_update", "symbol_id": "NG1586", "symbol": "⧖", "old_score": 95.0, "new_score": 98.0, "timestamp": "2025-05-25T19:01:08.667716"}, {"type": "score_update", "symbol_id": "NG1594", "symbol": "⡬", "old_score": 95.0, "new_score": 98.0, "timestamp": "2025-05-25T19:01:08.667729"}, {"type": "score_update", "symbol_id": "NG1599", "symbol": "⨷", "old_score": 95.0, "new_score": 98.0, "timestamp": "2025-05-25T19:01:08.667742"}, {"type": "score_update", "symbol_id": "NG1602", "symbol": "✁", "old_score": 95.0, "new_score": 98.0, "timestamp": "2025-05-25T19:01:08.667755"}, {"type": "score_update", "symbol_id": "NG1606", "symbol": "⦔", "old_score": 95.0, "new_score": 98.0, "timestamp": "2025-05-25T19:01:08.667768"}, {"type": "score_update", "symbol_id": "NG1621", "symbol": "⦐", "old_score": 95.0, "new_score": 98.0, "timestamp": "2025-05-25T19:01:08.667783"}, {"type": "score_update", "symbol_id": "NG1624", "symbol": "❬", "old_score": 95.0, "new_score": 98.0, "timestamp": "2025-05-25T19:01:08.667796"}, {"type": "score_update", "symbol_id": "NG1656", "symbol": "⤌", "old_score": 95.0, "new_score": 98.0, "timestamp": "2025-05-25T19:01:08.667815"}, {"type": "score_update", "symbol_id": "NG1658", "symbol": "⤕", "old_score": 95.0, "new_score": 98.0, "timestamp": "2025-05-25T19:01:08.667828"}, {"type": "score_update", "symbol_id": "NG1660", "symbol": "⥃", "old_score": 95.0, "new_score": 98.0, "timestamp": "2025-05-25T19:01:08.667840"}, {"type": "score_update", "symbol_id": "NG1661", "symbol": "⫺", "old_score": 95.0, "new_score": 98.0, "timestamp": "2025-05-25T19:01:08.667852"}, {"type": "score_update", "symbol_id": "NG1663", "symbol": "⮬", "old_score": 95.0, "new_score": 98.0, "timestamp": "2025-05-25T19:01:08.667864"}, {"type": "score_update", "symbol_id": "NG1667", "symbol": "⪤", "old_score": 95.0, "new_score": 98.0, "timestamp": "2025-05-25T19:01:08.667878"}, {"type": "score_update", "symbol_id": "NG1671", "symbol": "⢗", "old_score": 95.0, "new_score": 98.0, "timestamp": "2025-05-25T19:01:08.667891"}, {"type": "score_update", "symbol_id": "NG1672", "symbol": "⦳", "old_score": 95.0, "new_score": 98.0, "timestamp": "2025-05-25T19:01:08.667903"}, {"type": "score_update", "symbol_id": "NG1674", "symbol": "⧞", "old_score": 95.0, "new_score": 98.0, "timestamp": "2025-05-25T19:01:08.667916"}, {"type": "score_update", "symbol_id": "NG1675", "symbol": "⨘", "old_score": 95.0, "new_score": 98.0, "timestamp": "2025-05-25T19:01:08.667928"}, {"type": "score_update", "symbol_id": "NG1686", "symbol": "⤔", "old_score": 95.0, "new_score": 98.0, "timestamp": "2025-05-25T19:01:08.667944"}, {"type": "score_update", "symbol_id": "NG1687", "symbol": "⫼", "old_score": 95.0, "new_score": 98.0, "timestamp": "2025-05-25T19:01:08.667956"}, {"type": "score_update", "symbol_id": "NG1689", "symbol": "⭜", "old_score": 95.0, "new_score": 98.0, "timestamp": "2025-05-25T19:01:08.667969"}, {"type": "score_update", "symbol_id": "NG1704", "symbol": "⥴", "old_score": 95.0, "new_score": 98.0, "timestamp": "2025-05-25T19:01:08.667986"}, {"type": "score_update", "symbol_id": "NG1706", "symbol": "⧢", "old_score": 95.0, "new_score": 98.0, "timestamp": "2025-05-25T19:01:08.667999"}, {"type": "score_update", "symbol_id": "NG1707", "symbol": "⯾", "old_score": 95.0, "new_score": 98.0, "timestamp": "2025-05-25T19:01:08.668012"}, {"type": "score_update", "symbol_id": "NG1793", "symbol": "⮔", "old_score": 95.0, "new_score": 98.0, "timestamp": "2025-05-25T19:01:08.668105"}, {"type": "score_update", "symbol_id": "NG1802", "symbol": "✣", "old_score": 95.0, "new_score": 98.0, "timestamp": "2025-05-25T19:01:08.668120"}, {"type": "score_update", "symbol_id": "NG1804", "symbol": "➠", "old_score": 95.0, "new_score": 98.0, "timestamp": "2025-05-25T19:01:08.668133"}, {"type": "score_update", "symbol_id": "NG1806", "symbol": "⤚", "old_score": 95.0, "new_score": 98.0, "timestamp": "2025-05-25T19:01:08.668147"}, {"type": "score_update", "symbol_id": "NG1807", "symbol": "⤆", "old_score": 95.0, "new_score": 98.0, "timestamp": "2025-05-25T19:01:08.668161"}, {"type": "score_update", "symbol_id": "NG1814", "symbol": "⯤", "old_score": 95.0, "new_score": 98.0, "timestamp": "2025-05-25T19:01:08.668193"}, {"type": "score_update", "symbol_id": "NG1815", "symbol": "⩦", "old_score": 95.0, "new_score": 98.0, "timestamp": "2025-05-25T19:01:08.668207"}, {"type": "score_update", "symbol_id": "NG1816", "symbol": "⭿", "old_score": 95.0, "new_score": 98.0, "timestamp": "2025-05-25T19:01:08.668221"}, {"type": "score_update", "symbol_id": "NG1819", "symbol": "⩏", "old_score": 95.0, "new_score": 98.0, "timestamp": "2025-05-25T19:01:08.668235"}, {"type": "score_update", "symbol_id": "NG1842", "symbol": "𝞉", "old_score": 95.0, "new_score": 98.0, "timestamp": "2025-05-25T19:01:08.668255"}, {"type": "score_update", "symbol_id": "NG1843", "symbol": "⤣", "old_score": 95.0, "new_score": 98.0, "timestamp": "2025-05-25T19:01:08.668268"}, {"type": "score_update", "symbol_id": "NG1844", "symbol": "⣡", "old_score": 95.0, "new_score": 98.0, "timestamp": "2025-05-25T19:01:08.668282"}, {"type": "score_update", "symbol_id": "NG1846", "symbol": "⢢", "old_score": 95.0, "new_score": 98.0, "timestamp": "2025-05-25T19:01:08.668296"}, {"type": "score_update", "symbol_id": "NG1847", "symbol": "❈", "old_score": 95.0, "new_score": 98.0, "timestamp": "2025-05-25T19:01:08.668311"}, {"type": "score_update", "symbol_id": "NG1850", "symbol": "⢞", "old_score": 95.0, "new_score": 98.0, "timestamp": "2025-05-25T19:01:08.668326"}, {"type": "score_update", "symbol_id": "NG1861", "symbol": "⢡", "old_score": 95.0, "new_score": 98.0, "timestamp": "2025-05-25T19:01:08.668344"}, {"type": "score_update", "symbol_id": "NG1868", "symbol": "⯡", "old_score": 95.0, "new_score": 98.0, "timestamp": "2025-05-25T19:01:08.668360"}, {"type": "score_update", "symbol_id": "NG1873", "symbol": "⨵", "old_score": 95.0, "new_score": 98.0, "timestamp": "2025-05-25T19:01:08.668374"}, {"type": "score_update", "symbol_id": "NG1889", "symbol": "⮞", "old_score": 95.0, "new_score": 98.0, "timestamp": "2025-05-25T19:01:08.668425"}, {"type": "score_update", "symbol_id": "NG1890", "symbol": "⠕", "old_score": 95.0, "new_score": 98.0, "timestamp": "2025-05-25T19:01:08.668439"}, {"type": "score_update", "symbol_id": "NG1893", "symbol": "✆", "old_score": 95.0, "new_score": 98.0, "timestamp": "2025-05-25T19:01:08.668454"}, {"type": "score_update", "symbol_id": "NG1894", "symbol": "⧻", "old_score": 95.0, "new_score": 98.0, "timestamp": "2025-05-25T19:01:08.668468"}, {"type": "score_update", "symbol_id": "NG1906", "symbol": "⬲", "old_score": 95.0, "new_score": 98.0, "timestamp": "2025-05-25T19:01:08.668485"}, {"type": "score_update", "symbol_id": "NG1907", "symbol": "⨢", "old_score": 95.0, "new_score": 98.0, "timestamp": "2025-05-25T19:01:08.668500"}, {"type": "score_update", "symbol_id": "NG1910", "symbol": "⧨", "old_score": 95.0, "new_score": 98.0, "timestamp": "2025-05-25T19:01:08.668517"}, {"type": "score_update", "symbol_id": "NG1915", "symbol": "⩇", "old_score": 95.0, "new_score": 98.0, "timestamp": "2025-05-25T19:01:08.668533"}, {"type": "score_update", "symbol_id": "NG1916", "symbol": "➜", "old_score": 95.0, "new_score": 98.0, "timestamp": "2025-05-25T19:01:08.668548"}, {"type": "score_update", "symbol_id": "NG1918", "symbol": "⩧", "old_score": 95.0, "new_score": 98.0, "timestamp": "2025-05-25T19:01:08.668563"}, {"type": "score_update", "symbol_id": "NG1926", "symbol": "✂", "old_score": 95.0, "new_score": 98.0, "timestamp": "2025-05-25T19:01:08.668579"}, {"type": "score_update", "symbol_id": "NG1928", "symbol": "⠉", "old_score": 95.0, "new_score": 98.0, "timestamp": "2025-05-25T19:01:08.668594"}, {"type": "score_update", "symbol_id": "NG1930", "symbol": "⧺", "old_score": 95.0, "new_score": 98.0, "timestamp": "2025-05-25T19:01:08.668610"}, {"type": "score_update", "symbol_id": "NG1937", "symbol": "⧰", "old_score": 95.0, "new_score": 98.0, "timestamp": "2025-05-25T19:01:08.668627"}, {"type": "score_update", "symbol_id": "NG1940", "symbol": "⣽", "old_score": 95.0, "new_score": 98.0, "timestamp": "2025-05-25T19:01:08.668643"}, {"type": "score_update", "symbol_id": "NG1941", "symbol": "⥗", "old_score": 95.0, "new_score": 98.0, "timestamp": "2025-05-25T19:01:08.668658"}, {"type": "score_update", "symbol_id": "NG1947", "symbol": "✘", "old_score": 95.0, "new_score": 98.0, "timestamp": "2025-05-25T19:01:08.668674"}, {"type": "score_update", "symbol_id": "NG1960", "symbol": "⪖", "old_score": 95.0, "new_score": 98.0, "timestamp": "2025-05-25T19:01:08.668692"}, {"type": "score_update", "symbol_id": "NG1961", "symbol": "⭝", "old_score": 95.0, "new_score": 98.0, "timestamp": "2025-05-25T19:01:08.668707"}, {"type": "score_update", "symbol_id": "NG1962", "symbol": "⠑", "old_score": 95.0, "new_score": 98.0, "timestamp": "2025-05-25T19:01:08.668722"}, {"type": "score_update", "symbol_id": "NG1963", "symbol": "⨮", "old_score": 95.0, "new_score": 98.0, "timestamp": "2025-05-25T19:01:08.668738"}, {"type": "score_update", "symbol_id": "NG1966", "symbol": "⫈", "old_score": 95.0, "new_score": 98.0, "timestamp": "2025-05-25T19:01:08.668753"}, {"type": "score_update", "symbol_id": "NG1969", "symbol": "⠙", "old_score": 95.0, "new_score": 98.0, "timestamp": "2025-05-25T19:01:08.668770"}, {"type": "score_update", "symbol_id": "NG1970", "symbol": "⣧", "old_score": 95.0, "new_score": 98.0, "timestamp": "2025-05-25T19:01:08.668786"}, {"type": "score_update", "symbol_id": "NG1972", "symbol": "⤟", "old_score": 95.0, "new_score": 98.0, "timestamp": "2025-05-25T19:01:08.668801"}, {"type": "score_update", "symbol_id": "NG1974", "symbol": "⪦", "old_score": 95.0, "new_score": 98.0, "timestamp": "2025-05-25T19:01:08.668817"}, {"type": "score_update", "symbol_id": "NG1976", "symbol": "✜", "old_score": 95.0, "new_score": 98.0, "timestamp": "2025-05-25T19:01:08.668833"}, {"type": "score_update", "symbol_id": "NG1977", "symbol": "⬩", "old_score": 95.0, "new_score": 98.0, "timestamp": "2025-05-25T19:01:08.668849"}, {"type": "score_update", "symbol_id": "NG1978", "symbol": "⧂", "old_score": 95.0, "new_score": 98.0, "timestamp": "2025-05-25T19:01:08.668864"}, {"type": "score_update", "symbol_id": "NG1979", "symbol": "✯", "old_score": 95.0, "new_score": 98.0, "timestamp": "2025-05-25T19:01:08.668880"}, {"type": "score_update", "symbol_id": "NG1988", "symbol": "➵", "old_score": 95.0, "new_score": 98.0, "timestamp": "2025-05-25T19:01:08.668899"}, {"type": "score_update", "symbol_id": "NG1994", "symbol": "⭱", "old_score": 95.0, "new_score": 98.0, "timestamp": "2025-05-25T19:01:08.668916"}, {"type": "score_update", "symbol_id": "NG2003", "symbol": "⡳", "old_score": 95.0, "new_score": 98.0, "timestamp": "2025-05-25T19:01:08.668933"}, {"type": "score_update", "symbol_id": "NG2004", "symbol": "⤐", "old_score": 95.0, "new_score": 98.0, "timestamp": "2025-05-25T19:01:08.668950"}, {"type": "score_update", "symbol_id": "NG2009", "symbol": "⬤", "old_score": 95.0, "new_score": 98.0, "timestamp": "2025-05-25T19:01:08.668967"}]}