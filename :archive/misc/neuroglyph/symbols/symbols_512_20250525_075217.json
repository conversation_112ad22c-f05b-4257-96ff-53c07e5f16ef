[{"id": "NG0001", "symbol": "⊕", "name": "fix", "description": "Fix concept in action", "category": "action", "aliases": ["fix_alt", "fix_v2", "do_fix"], "status": "approved", "version": "1.0"}, {"id": "NG0002", "symbol": "⧉", "name": "create", "description": "Create concept in action", "category": "action", "aliases": ["create_alt", "create_v2", "do_create"], "status": "approved", "version": "1.0"}, {"id": "NG0003", "symbol": "↯", "name": "delete", "description": "Delete concept in action", "category": "action", "aliases": ["delete_alt", "delete_v2", "do_delete"], "status": "approved", "version": "1.0"}, {"id": "NG0004", "symbol": "⊖", "name": "update", "description": "Update concept in action", "category": "action", "aliases": ["update_alt", "update_v2", "do_update"], "status": "approved", "version": "1.0"}, {"id": "NG0005", "symbol": "⊞", "name": "patch", "description": "Patch concept in action", "category": "action", "aliases": ["patch_alt", "patch_v2", "do_patch"], "status": "approved", "version": "1.0"}, {"id": "NG0006", "symbol": "⊂", "name": "refactor", "description": "Refactor concept in action", "category": "action", "aliases": ["refactor_alt", "refactor_v2", "do_refactor"], "status": "approved", "version": "1.0"}, {"id": "NG0007", "symbol": "⬒", "name": "retry", "description": "Retry concept in action", "category": "action", "aliases": ["retry_alt", "retry_v2", "do_retry"], "status": "approved", "version": "1.0"}, {"id": "NG0008", "symbol": "✖", "name": "abort", "description": "Abort concept in action", "category": "action", "aliases": ["abort_alt", "abort_v2", "do_abort"], "status": "approved", "version": "1.0"}, {"id": "NG0009", "symbol": "⚠️", "name": "init", "description": "Init concept in action", "category": "action", "aliases": ["init_alt", "init_v2", "do_init"], "status": "approved", "version": "1.0"}, {"id": "NG0010", "symbol": "✔", "name": "assign", "description": "Assign concept in action", "category": "action", "aliases": ["assign_alt", "assign_v2", "do_assign"], "status": "approved", "version": "1.0"}, {"id": "NG0011", "symbol": "∧", "name": "loop", "description": "Loop concept in structure", "category": "structure", "aliases": ["loop_alt", "loop_v2", "do_loop"], "status": "approved", "version": "1.0"}, {"id": "NG0012", "symbol": "∨", "name": "function", "description": "Function concept in structure", "category": "structure", "aliases": ["function_alt", "function_v2", "do_function"], "status": "approved", "version": "1.0"}, {"id": "NG0013", "symbol": "¬", "name": "class", "description": "Class concept in structure", "category": "structure", "aliases": ["class_alt", "class_v2", "do_class"], "status": "approved", "version": "1.0"}, {"id": "NG0014", "symbol": "→", "name": "block", "description": "Block concept in structure", "category": "structure", "aliases": ["block_alt", "block_v2", "do_block"], "status": "approved", "version": "1.0"}, {"id": "NG0015", "symbol": "≡", "name": "condition", "description": "Condition concept in structure", "category": "structure", "aliases": ["condition_alt", "condition_v2", "do_condition"], "status": "approved", "version": "1.0"}, {"id": "NG0016", "symbol": "⊨", "name": "branch", "description": "Branch concept in structure", "category": "structure", "aliases": ["branch_alt", "branch_v2", "do_branch"], "status": "approved", "version": "1.0"}, {"id": "NG0017", "symbol": "⇌", "name": "switch", "description": "Switch concept in structure", "category": "structure", "aliases": ["switch_alt", "switch_v2", "do_switch"], "status": "approved", "version": "1.0"}, {"id": "NG0018", "symbol": "📌", "name": "module", "description": "Module concept in structure", "category": "structure", "aliases": ["module_alt", "module_v2", "do_module"], "status": "approved", "version": "1.0"}, {"id": "NG0019", "symbol": "🧠", "name": "pipeline", "description": "Pipeline concept in structure", "category": "structure", "aliases": ["pipeline_alt", "pipeline_v2", "do_pipeline"], "status": "approved", "version": "1.0"}, {"id": "NG0020", "symbol": "⊘", "name": "import", "description": "Import concept in structure", "category": "structure", "aliases": ["import_alt", "import_v2", "do_import"], "status": "approved", "version": "1.0"}, {"id": "NG0021", "symbol": "⊕", "name": "null", "description": "Null concept in state", "category": "state", "aliases": ["null_alt", "null_v2", "do_null"], "status": "approved", "version": "1.0"}, {"id": "NG0022", "symbol": "⧉", "name": "error", "description": "Error concept in state", "category": "state", "aliases": ["error_alt", "error_v2", "do_error"], "status": "approved", "version": "1.0"}, {"id": "NG0023", "symbol": "↯", "name": "valid", "description": "Valid concept in state", "category": "state", "aliases": ["valid_alt", "valid_v2", "do_valid"], "status": "approved", "version": "1.0"}, {"id": "NG0024", "symbol": "⊖", "name": "warning", "description": "Warning concept in state", "category": "state", "aliases": ["warning_alt", "warning_v2", "do_warning"], "status": "approved", "version": "1.0"}, {"id": "NG0025", "symbol": "⊞", "name": "loading", "description": "Loading concept in state", "category": "state", "aliases": ["loading_alt", "loading_v2", "do_loading"], "status": "approved", "version": "1.0"}, {"id": "NG0026", "symbol": "⊂", "name": "ready", "description": "Ready concept in state", "category": "state", "aliases": ["ready_alt", "ready_v2", "do_ready"], "status": "approved", "version": "1.0"}, {"id": "NG0027", "symbol": "⬒", "name": "terminated", "description": "Terminated concept in state", "category": "state", "aliases": ["terminated_alt", "terminated_v2", "do_terminated"], "status": "approved", "version": "1.0"}, {"id": "NG0028", "symbol": "✖", "name": "retrying", "description": "Retrying concept in state", "category": "state", "aliases": ["retrying_alt", "retrying_v2", "do_retrying"], "status": "approved", "version": "1.0"}, {"id": "NG0029", "symbol": "⚠️", "name": "locked", "description": "Locked concept in state", "category": "state", "aliases": ["locked_alt", "locked_v2", "do_locked"], "status": "approved", "version": "1.0"}, {"id": "NG0030", "symbol": "✔", "name": "incomplete", "description": "Incomplete concept in state", "category": "state", "aliases": ["incomplete_alt", "incomplete_v2", "do_incomplete"], "status": "approved", "version": "1.0"}, {"id": "NG0031", "symbol": "∧", "name": "and", "description": "And concept in logic", "category": "logic", "aliases": ["and_alt", "and_v2", "do_and"], "status": "approved", "version": "1.0"}, {"id": "NG0032", "symbol": "∨", "name": "or", "description": "Or concept in logic", "category": "logic", "aliases": ["or_alt", "or_v2", "do_or"], "status": "approved", "version": "1.0"}, {"id": "NG0033", "symbol": "¬", "name": "not", "description": "Not concept in logic", "category": "logic", "aliases": ["not_alt", "not_v2", "do_not"], "status": "approved", "version": "1.0"}, {"id": "NG0034", "symbol": "→", "name": "implies", "description": "Implies concept in logic", "category": "logic", "aliases": ["implies_alt", "implies_v2", "do_implies"], "status": "approved", "version": "1.0"}, {"id": "NG0035", "symbol": "≡", "name": "equals", "description": "Equals concept in logic", "category": "logic", "aliases": ["equals_alt", "equals_v2", "do_equals"], "status": "approved", "version": "1.0"}, {"id": "NG0036", "symbol": "⊨", "name": "defined_as", "description": "Defined_as concept in logic", "category": "logic", "aliases": ["defined_as_alt", "defined_as_v2", "do_defined_as"], "status": "approved", "version": "1.0"}, {"id": "NG0037", "symbol": "⇌", "name": "greater_than", "description": "Greater_than concept in logic", "category": "logic", "aliases": ["greater_than_alt", "greater_than_v2", "do_greater_than"], "status": "approved", "version": "1.0"}, {"id": "NG0038", "symbol": "📌", "name": "less_than", "description": "Less_than concept in logic", "category": "logic", "aliases": ["less_than_alt", "less_than_v2", "do_less_than"], "status": "approved", "version": "1.0"}, {"id": "NG0039", "symbol": "🧠", "name": "xor", "description": "Xor concept in logic", "category": "logic", "aliases": ["xor_alt", "xor_v2", "do_xor"], "status": "approved", "version": "1.0"}, {"id": "NG0040", "symbol": "⊘", "name": "exists", "description": "Exists concept in logic", "category": "logic", "aliases": ["exists_alt", "exists_v2", "do_exists"], "status": "approved", "version": "1.0"}, {"id": "NG0041", "symbol": "⊕", "name": "user", "description": "User concept in entity", "category": "entity", "aliases": ["user_alt", "user_v2", "do_user"], "status": "approved", "version": "1.0"}, {"id": "NG0042", "symbol": "⧉", "name": "token", "description": "Token concept in entity", "category": "entity", "aliases": ["token_alt", "token_v2", "do_token"], "status": "approved", "version": "1.0"}, {"id": "NG0043", "symbol": "↯", "name": "auth", "description": "Auth concept in entity", "category": "entity", "aliases": ["auth_alt", "auth_v2", "do_auth"], "status": "approved", "version": "1.0"}, {"id": "NG0044", "symbol": "⊖", "name": "payment", "description": "Payment concept in entity", "category": "entity", "aliases": ["payment_alt", "payment_v2", "do_payment"], "status": "approved", "version": "1.0"}, {"id": "NG0045", "symbol": "⊞", "name": "api", "description": "Api concept in entity", "category": "entity", "aliases": ["api_alt", "api_v2", "do_api"], "status": "approved", "version": "1.0"}, {"id": "NG0046", "symbol": "⊂", "name": "session", "description": "Session concept in entity", "category": "entity", "aliases": ["session_alt", "session_v2", "do_session"], "status": "approved", "version": "1.0"}, {"id": "NG0047", "symbol": "⬒", "name": "file", "description": "File concept in entity", "category": "entity", "aliases": ["file_alt", "file_v2", "do_file"], "status": "approved", "version": "1.0"}, {"id": "NG0048", "symbol": "✖", "name": "device", "description": "Device concept in entity", "category": "entity", "aliases": ["device_alt", "device_v2", "do_device"], "status": "approved", "version": "1.0"}, {"id": "NG0049", "symbol": "⚠️", "name": "input", "description": "Input concept in entity", "category": "entity", "aliases": ["input_alt", "input_v2", "do_input"], "status": "approved", "version": "1.0"}, {"id": "NG0050", "symbol": "✔", "name": "output", "description": "Output concept in entity", "category": "entity", "aliases": ["output_alt", "output_v2", "do_output"], "status": "approved", "version": "1.0"}, {"id": "NG0051", "symbol": "∧", "name": "crypto", "description": "Crypto concept in domain", "category": "domain", "aliases": ["crypto_alt", "crypto_v2", "do_crypto"], "status": "approved", "version": "1.0"}, {"id": "NG0052", "symbol": "∨", "name": "health", "description": "Health concept in domain", "category": "domain", "aliases": ["health_alt", "health_v2", "do_health"], "status": "approved", "version": "1.0"}, {"id": "NG0053", "symbol": "¬", "name": "edu", "description": "Edu concept in domain", "category": "domain", "aliases": ["edu_alt", "edu_v2", "do_edu"], "status": "approved", "version": "1.0"}, {"id": "NG0054", "symbol": "→", "name": "gov", "description": "Gov concept in domain", "category": "domain", "aliases": ["gov_alt", "gov_v2", "do_gov"], "status": "approved", "version": "1.0"}, {"id": "NG0055", "symbol": "≡", "name": "store", "description": "Store concept in domain", "category": "domain", "aliases": ["store_alt", "store_v2", "do_store"], "status": "approved", "version": "1.0"}, {"id": "NG0056", "symbol": "⊨", "name": "network", "description": "Network concept in domain", "category": "domain", "aliases": ["network_alt", "network_v2", "do_network"], "status": "approved", "version": "1.0"}, {"id": "NG0057", "symbol": "⇌", "name": "email", "description": "Email concept in domain", "category": "domain", "aliases": ["email_alt", "email_v2", "do_email"], "status": "approved", "version": "1.0"}, {"id": "NG0058", "symbol": "📌", "name": "log", "description": "Log concept in domain", "category": "domain", "aliases": ["log_alt", "log_v2", "do_log"], "status": "approved", "version": "1.0"}, {"id": "NG0059", "symbol": "🧠", "name": "analytics", "description": "Analytics concept in domain", "category": "domain", "aliases": ["analytics_alt", "analytics_v2", "do_analytics"], "status": "approved", "version": "1.0"}, {"id": "NG0060", "symbol": "⊘", "name": "form", "description": "Form concept in domain", "category": "domain", "aliases": ["form_alt", "form_v2", "do_form"], "status": "approved", "version": "1.0"}, {"id": "NG0061", "symbol": "⊕", "name": "fix", "description": "Fix concept in action", "category": "action", "aliases": ["fix_alt", "fix_v2", "do_fix"], "status": "approved", "version": "1.0"}, {"id": "NG0062", "symbol": "⧉", "name": "create", "description": "Create concept in action", "category": "action", "aliases": ["create_alt", "create_v2", "do_create"], "status": "approved", "version": "1.0"}, {"id": "NG0063", "symbol": "↯", "name": "delete", "description": "Delete concept in action", "category": "action", "aliases": ["delete_alt", "delete_v2", "do_delete"], "status": "approved", "version": "1.0"}, {"id": "NG0064", "symbol": "⊖", "name": "update", "description": "Update concept in action", "category": "action", "aliases": ["update_alt", "update_v2", "do_update"], "status": "approved", "version": "1.0"}, {"id": "NG0065", "symbol": "⊞", "name": "patch", "description": "Patch concept in action", "category": "action", "aliases": ["patch_alt", "patch_v2", "do_patch"], "status": "approved", "version": "1.0"}, {"id": "NG0066", "symbol": "⊂", "name": "refactor", "description": "Refactor concept in action", "category": "action", "aliases": ["refactor_alt", "refactor_v2", "do_refactor"], "status": "approved", "version": "1.0"}, {"id": "NG0067", "symbol": "⬒", "name": "retry", "description": "Retry concept in action", "category": "action", "aliases": ["retry_alt", "retry_v2", "do_retry"], "status": "approved", "version": "1.0"}, {"id": "NG0068", "symbol": "✖", "name": "abort", "description": "Abort concept in action", "category": "action", "aliases": ["abort_alt", "abort_v2", "do_abort"], "status": "approved", "version": "1.0"}, {"id": "NG0069", "symbol": "⚠️", "name": "init", "description": "Init concept in action", "category": "action", "aliases": ["init_alt", "init_v2", "do_init"], "status": "approved", "version": "1.0"}, {"id": "NG0070", "symbol": "✔", "name": "assign", "description": "Assign concept in action", "category": "action", "aliases": ["assign_alt", "assign_v2", "do_assign"], "status": "approved", "version": "1.0"}, {"id": "NG0071", "symbol": "∧", "name": "loop", "description": "Loop concept in structure", "category": "structure", "aliases": ["loop_alt", "loop_v2", "do_loop"], "status": "approved", "version": "1.0"}, {"id": "NG0072", "symbol": "∨", "name": "function", "description": "Function concept in structure", "category": "structure", "aliases": ["function_alt", "function_v2", "do_function"], "status": "approved", "version": "1.0"}, {"id": "NG0073", "symbol": "¬", "name": "class", "description": "Class concept in structure", "category": "structure", "aliases": ["class_alt", "class_v2", "do_class"], "status": "approved", "version": "1.0"}, {"id": "NG0074", "symbol": "→", "name": "block", "description": "Block concept in structure", "category": "structure", "aliases": ["block_alt", "block_v2", "do_block"], "status": "approved", "version": "1.0"}, {"id": "NG0075", "symbol": "≡", "name": "condition", "description": "Condition concept in structure", "category": "structure", "aliases": ["condition_alt", "condition_v2", "do_condition"], "status": "approved", "version": "1.0"}, {"id": "NG0076", "symbol": "⊨", "name": "branch", "description": "Branch concept in structure", "category": "structure", "aliases": ["branch_alt", "branch_v2", "do_branch"], "status": "approved", "version": "1.0"}, {"id": "NG0077", "symbol": "⇌", "name": "switch", "description": "Switch concept in structure", "category": "structure", "aliases": ["switch_alt", "switch_v2", "do_switch"], "status": "approved", "version": "1.0"}, {"id": "NG0078", "symbol": "📌", "name": "module", "description": "Module concept in structure", "category": "structure", "aliases": ["module_alt", "module_v2", "do_module"], "status": "approved", "version": "1.0"}, {"id": "NG0079", "symbol": "🧠", "name": "pipeline", "description": "Pipeline concept in structure", "category": "structure", "aliases": ["pipeline_alt", "pipeline_v2", "do_pipeline"], "status": "approved", "version": "1.0"}, {"id": "NG0080", "symbol": "⊘", "name": "import", "description": "Import concept in structure", "category": "structure", "aliases": ["import_alt", "import_v2", "do_import"], "status": "approved", "version": "1.0"}, {"id": "NG0081", "symbol": "⊕", "name": "null", "description": "Null concept in state", "category": "state", "aliases": ["null_alt", "null_v2", "do_null"], "status": "approved", "version": "1.0"}, {"id": "NG0082", "symbol": "⧉", "name": "error", "description": "Error concept in state", "category": "state", "aliases": ["error_alt", "error_v2", "do_error"], "status": "approved", "version": "1.0"}, {"id": "NG0083", "symbol": "↯", "name": "valid", "description": "Valid concept in state", "category": "state", "aliases": ["valid_alt", "valid_v2", "do_valid"], "status": "approved", "version": "1.0"}, {"id": "NG0084", "symbol": "⊖", "name": "warning", "description": "Warning concept in state", "category": "state", "aliases": ["warning_alt", "warning_v2", "do_warning"], "status": "approved", "version": "1.0"}, {"id": "NG0085", "symbol": "⊞", "name": "loading", "description": "Loading concept in state", "category": "state", "aliases": ["loading_alt", "loading_v2", "do_loading"], "status": "approved", "version": "1.0"}, {"id": "NG0086", "symbol": "⊂", "name": "ready", "description": "Ready concept in state", "category": "state", "aliases": ["ready_alt", "ready_v2", "do_ready"], "status": "approved", "version": "1.0"}, {"id": "NG0087", "symbol": "⬒", "name": "terminated", "description": "Terminated concept in state", "category": "state", "aliases": ["terminated_alt", "terminated_v2", "do_terminated"], "status": "approved", "version": "1.0"}, {"id": "NG0088", "symbol": "✖", "name": "retrying", "description": "Retrying concept in state", "category": "state", "aliases": ["retrying_alt", "retrying_v2", "do_retrying"], "status": "approved", "version": "1.0"}, {"id": "NG0089", "symbol": "⚠️", "name": "locked", "description": "Locked concept in state", "category": "state", "aliases": ["locked_alt", "locked_v2", "do_locked"], "status": "approved", "version": "1.0"}, {"id": "NG0090", "symbol": "✔", "name": "incomplete", "description": "Incomplete concept in state", "category": "state", "aliases": ["incomplete_alt", "incomplete_v2", "do_incomplete"], "status": "approved", "version": "1.0"}, {"id": "NG0091", "symbol": "∧", "name": "and", "description": "And concept in logic", "category": "logic", "aliases": ["and_alt", "and_v2", "do_and"], "status": "approved", "version": "1.0"}, {"id": "NG0092", "symbol": "∨", "name": "or", "description": "Or concept in logic", "category": "logic", "aliases": ["or_alt", "or_v2", "do_or"], "status": "approved", "version": "1.0"}, {"id": "NG0093", "symbol": "¬", "name": "not", "description": "Not concept in logic", "category": "logic", "aliases": ["not_alt", "not_v2", "do_not"], "status": "approved", "version": "1.0"}, {"id": "NG0094", "symbol": "→", "name": "implies", "description": "Implies concept in logic", "category": "logic", "aliases": ["implies_alt", "implies_v2", "do_implies"], "status": "approved", "version": "1.0"}, {"id": "NG0095", "symbol": "≡", "name": "equals", "description": "Equals concept in logic", "category": "logic", "aliases": ["equals_alt", "equals_v2", "do_equals"], "status": "approved", "version": "1.0"}, {"id": "NG0096", "symbol": "⊨", "name": "defined_as", "description": "Defined_as concept in logic", "category": "logic", "aliases": ["defined_as_alt", "defined_as_v2", "do_defined_as"], "status": "approved", "version": "1.0"}, {"id": "NG0097", "symbol": "⇌", "name": "greater_than", "description": "Greater_than concept in logic", "category": "logic", "aliases": ["greater_than_alt", "greater_than_v2", "do_greater_than"], "status": "approved", "version": "1.0"}, {"id": "NG0098", "symbol": "📌", "name": "less_than", "description": "Less_than concept in logic", "category": "logic", "aliases": ["less_than_alt", "less_than_v2", "do_less_than"], "status": "approved", "version": "1.0"}, {"id": "NG0099", "symbol": "🧠", "name": "xor", "description": "Xor concept in logic", "category": "logic", "aliases": ["xor_alt", "xor_v2", "do_xor"], "status": "approved", "version": "1.0"}, {"id": "NG0100", "symbol": "⊘", "name": "exists", "description": "Exists concept in logic", "category": "logic", "aliases": ["exists_alt", "exists_v2", "do_exists"], "status": "approved", "version": "1.0"}, {"id": "NG0101", "symbol": "⊕", "name": "user", "description": "User concept in entity", "category": "entity", "aliases": ["user_alt", "user_v2", "do_user"], "status": "approved", "version": "1.0"}, {"id": "NG0102", "symbol": "⧉", "name": "token", "description": "Token concept in entity", "category": "entity", "aliases": ["token_alt", "token_v2", "do_token"], "status": "approved", "version": "1.0"}, {"id": "NG0103", "symbol": "↯", "name": "auth", "description": "Auth concept in entity", "category": "entity", "aliases": ["auth_alt", "auth_v2", "do_auth"], "status": "approved", "version": "1.0"}, {"id": "NG0104", "symbol": "⊖", "name": "payment", "description": "Payment concept in entity", "category": "entity", "aliases": ["payment_alt", "payment_v2", "do_payment"], "status": "approved", "version": "1.0"}, {"id": "NG0105", "symbol": "⊞", "name": "api", "description": "Api concept in entity", "category": "entity", "aliases": ["api_alt", "api_v2", "do_api"], "status": "approved", "version": "1.0"}, {"id": "NG0106", "symbol": "⊂", "name": "session", "description": "Session concept in entity", "category": "entity", "aliases": ["session_alt", "session_v2", "do_session"], "status": "approved", "version": "1.0"}, {"id": "NG0107", "symbol": "⬒", "name": "file", "description": "File concept in entity", "category": "entity", "aliases": ["file_alt", "file_v2", "do_file"], "status": "approved", "version": "1.0"}, {"id": "NG0108", "symbol": "✖", "name": "device", "description": "Device concept in entity", "category": "entity", "aliases": ["device_alt", "device_v2", "do_device"], "status": "approved", "version": "1.0"}, {"id": "NG0109", "symbol": "⚠️", "name": "input", "description": "Input concept in entity", "category": "entity", "aliases": ["input_alt", "input_v2", "do_input"], "status": "approved", "version": "1.0"}, {"id": "NG0110", "symbol": "✔", "name": "output", "description": "Output concept in entity", "category": "entity", "aliases": ["output_alt", "output_v2", "do_output"], "status": "approved", "version": "1.0"}, {"id": "NG0111", "symbol": "∧", "name": "crypto", "description": "Crypto concept in domain", "category": "domain", "aliases": ["crypto_alt", "crypto_v2", "do_crypto"], "status": "approved", "version": "1.0"}, {"id": "NG0112", "symbol": "∨", "name": "health", "description": "Health concept in domain", "category": "domain", "aliases": ["health_alt", "health_v2", "do_health"], "status": "approved", "version": "1.0"}, {"id": "NG0113", "symbol": "¬", "name": "edu", "description": "Edu concept in domain", "category": "domain", "aliases": ["edu_alt", "edu_v2", "do_edu"], "status": "approved", "version": "1.0"}, {"id": "NG0114", "symbol": "→", "name": "gov", "description": "Gov concept in domain", "category": "domain", "aliases": ["gov_alt", "gov_v2", "do_gov"], "status": "approved", "version": "1.0"}, {"id": "NG0115", "symbol": "≡", "name": "store", "description": "Store concept in domain", "category": "domain", "aliases": ["store_alt", "store_v2", "do_store"], "status": "approved", "version": "1.0"}, {"id": "NG0116", "symbol": "⊨", "name": "network", "description": "Network concept in domain", "category": "domain", "aliases": ["network_alt", "network_v2", "do_network"], "status": "approved", "version": "1.0"}, {"id": "NG0117", "symbol": "⇌", "name": "email", "description": "Email concept in domain", "category": "domain", "aliases": ["email_alt", "email_v2", "do_email"], "status": "approved", "version": "1.0"}, {"id": "NG0118", "symbol": "📌", "name": "log", "description": "Log concept in domain", "category": "domain", "aliases": ["log_alt", "log_v2", "do_log"], "status": "approved", "version": "1.0"}, {"id": "NG0119", "symbol": "🧠", "name": "analytics", "description": "Analytics concept in domain", "category": "domain", "aliases": ["analytics_alt", "analytics_v2", "do_analytics"], "status": "approved", "version": "1.0"}, {"id": "NG0120", "symbol": "⊘", "name": "form", "description": "Form concept in domain", "category": "domain", "aliases": ["form_alt", "form_v2", "do_form"], "status": "approved", "version": "1.0"}, {"id": "NG0121", "symbol": "⊕", "name": "fix", "description": "Fix concept in action", "category": "action", "aliases": ["fix_alt", "fix_v2", "do_fix"], "status": "approved", "version": "1.0"}, {"id": "NG0122", "symbol": "⧉", "name": "create", "description": "Create concept in action", "category": "action", "aliases": ["create_alt", "create_v2", "do_create"], "status": "approved", "version": "1.0"}, {"id": "NG0123", "symbol": "↯", "name": "delete", "description": "Delete concept in action", "category": "action", "aliases": ["delete_alt", "delete_v2", "do_delete"], "status": "approved", "version": "1.0"}, {"id": "NG0124", "symbol": "⊖", "name": "update", "description": "Update concept in action", "category": "action", "aliases": ["update_alt", "update_v2", "do_update"], "status": "approved", "version": "1.0"}, {"id": "NG0125", "symbol": "⊞", "name": "patch", "description": "Patch concept in action", "category": "action", "aliases": ["patch_alt", "patch_v2", "do_patch"], "status": "approved", "version": "1.0"}, {"id": "NG0126", "symbol": "⊂", "name": "refactor", "description": "Refactor concept in action", "category": "action", "aliases": ["refactor_alt", "refactor_v2", "do_refactor"], "status": "approved", "version": "1.0"}, {"id": "NG0127", "symbol": "⬒", "name": "retry", "description": "Retry concept in action", "category": "action", "aliases": ["retry_alt", "retry_v2", "do_retry"], "status": "approved", "version": "1.0"}, {"id": "NG0128", "symbol": "✖", "name": "abort", "description": "Abort concept in action", "category": "action", "aliases": ["abort_alt", "abort_v2", "do_abort"], "status": "approved", "version": "1.0"}, {"id": "NG0129", "symbol": "⚠️", "name": "init", "description": "Init concept in action", "category": "action", "aliases": ["init_alt", "init_v2", "do_init"], "status": "approved", "version": "1.0"}, {"id": "NG0130", "symbol": "✔", "name": "assign", "description": "Assign concept in action", "category": "action", "aliases": ["assign_alt", "assign_v2", "do_assign"], "status": "approved", "version": "1.0"}, {"id": "NG0131", "symbol": "∧", "name": "loop", "description": "Loop concept in structure", "category": "structure", "aliases": ["loop_alt", "loop_v2", "do_loop"], "status": "approved", "version": "1.0"}, {"id": "NG0132", "symbol": "∨", "name": "function", "description": "Function concept in structure", "category": "structure", "aliases": ["function_alt", "function_v2", "do_function"], "status": "approved", "version": "1.0"}, {"id": "NG0133", "symbol": "¬", "name": "class", "description": "Class concept in structure", "category": "structure", "aliases": ["class_alt", "class_v2", "do_class"], "status": "approved", "version": "1.0"}, {"id": "NG0134", "symbol": "→", "name": "block", "description": "Block concept in structure", "category": "structure", "aliases": ["block_alt", "block_v2", "do_block"], "status": "approved", "version": "1.0"}, {"id": "NG0135", "symbol": "≡", "name": "condition", "description": "Condition concept in structure", "category": "structure", "aliases": ["condition_alt", "condition_v2", "do_condition"], "status": "approved", "version": "1.0"}, {"id": "NG0136", "symbol": "⊨", "name": "branch", "description": "Branch concept in structure", "category": "structure", "aliases": ["branch_alt", "branch_v2", "do_branch"], "status": "approved", "version": "1.0"}, {"id": "NG0137", "symbol": "⇌", "name": "switch", "description": "Switch concept in structure", "category": "structure", "aliases": ["switch_alt", "switch_v2", "do_switch"], "status": "approved", "version": "1.0"}, {"id": "NG0138", "symbol": "📌", "name": "module", "description": "Module concept in structure", "category": "structure", "aliases": ["module_alt", "module_v2", "do_module"], "status": "approved", "version": "1.0"}, {"id": "NG0139", "symbol": "🧠", "name": "pipeline", "description": "Pipeline concept in structure", "category": "structure", "aliases": ["pipeline_alt", "pipeline_v2", "do_pipeline"], "status": "approved", "version": "1.0"}, {"id": "NG0140", "symbol": "⊘", "name": "import", "description": "Import concept in structure", "category": "structure", "aliases": ["import_alt", "import_v2", "do_import"], "status": "approved", "version": "1.0"}, {"id": "NG0141", "symbol": "⊕", "name": "null", "description": "Null concept in state", "category": "state", "aliases": ["null_alt", "null_v2", "do_null"], "status": "approved", "version": "1.0"}, {"id": "NG0142", "symbol": "⧉", "name": "error", "description": "Error concept in state", "category": "state", "aliases": ["error_alt", "error_v2", "do_error"], "status": "approved", "version": "1.0"}, {"id": "NG0143", "symbol": "↯", "name": "valid", "description": "Valid concept in state", "category": "state", "aliases": ["valid_alt", "valid_v2", "do_valid"], "status": "approved", "version": "1.0"}, {"id": "NG0144", "symbol": "⊖", "name": "warning", "description": "Warning concept in state", "category": "state", "aliases": ["warning_alt", "warning_v2", "do_warning"], "status": "approved", "version": "1.0"}, {"id": "NG0145", "symbol": "⊞", "name": "loading", "description": "Loading concept in state", "category": "state", "aliases": ["loading_alt", "loading_v2", "do_loading"], "status": "approved", "version": "1.0"}, {"id": "NG0146", "symbol": "⊂", "name": "ready", "description": "Ready concept in state", "category": "state", "aliases": ["ready_alt", "ready_v2", "do_ready"], "status": "approved", "version": "1.0"}, {"id": "NG0147", "symbol": "⬒", "name": "terminated", "description": "Terminated concept in state", "category": "state", "aliases": ["terminated_alt", "terminated_v2", "do_terminated"], "status": "approved", "version": "1.0"}, {"id": "NG0148", "symbol": "✖", "name": "retrying", "description": "Retrying concept in state", "category": "state", "aliases": ["retrying_alt", "retrying_v2", "do_retrying"], "status": "approved", "version": "1.0"}, {"id": "NG0149", "symbol": "⚠️", "name": "locked", "description": "Locked concept in state", "category": "state", "aliases": ["locked_alt", "locked_v2", "do_locked"], "status": "approved", "version": "1.0"}, {"id": "NG0150", "symbol": "✔", "name": "incomplete", "description": "Incomplete concept in state", "category": "state", "aliases": ["incomplete_alt", "incomplete_v2", "do_incomplete"], "status": "approved", "version": "1.0"}, {"id": "NG0151", "symbol": "∧", "name": "and", "description": "And concept in logic", "category": "logic", "aliases": ["and_alt", "and_v2", "do_and"], "status": "approved", "version": "1.0"}, {"id": "NG0152", "symbol": "∨", "name": "or", "description": "Or concept in logic", "category": "logic", "aliases": ["or_alt", "or_v2", "do_or"], "status": "approved", "version": "1.0"}, {"id": "NG0153", "symbol": "¬", "name": "not", "description": "Not concept in logic", "category": "logic", "aliases": ["not_alt", "not_v2", "do_not"], "status": "approved", "version": "1.0"}, {"id": "NG0154", "symbol": "→", "name": "implies", "description": "Implies concept in logic", "category": "logic", "aliases": ["implies_alt", "implies_v2", "do_implies"], "status": "approved", "version": "1.0"}, {"id": "NG0155", "symbol": "≡", "name": "equals", "description": "Equals concept in logic", "category": "logic", "aliases": ["equals_alt", "equals_v2", "do_equals"], "status": "approved", "version": "1.0"}, {"id": "NG0156", "symbol": "⊨", "name": "defined_as", "description": "Defined_as concept in logic", "category": "logic", "aliases": ["defined_as_alt", "defined_as_v2", "do_defined_as"], "status": "approved", "version": "1.0"}, {"id": "NG0157", "symbol": "⇌", "name": "greater_than", "description": "Greater_than concept in logic", "category": "logic", "aliases": ["greater_than_alt", "greater_than_v2", "do_greater_than"], "status": "approved", "version": "1.0"}, {"id": "NG0158", "symbol": "📌", "name": "less_than", "description": "Less_than concept in logic", "category": "logic", "aliases": ["less_than_alt", "less_than_v2", "do_less_than"], "status": "approved", "version": "1.0"}, {"id": "NG0159", "symbol": "🧠", "name": "xor", "description": "Xor concept in logic", "category": "logic", "aliases": ["xor_alt", "xor_v2", "do_xor"], "status": "approved", "version": "1.0"}, {"id": "NG0160", "symbol": "⊘", "name": "exists", "description": "Exists concept in logic", "category": "logic", "aliases": ["exists_alt", "exists_v2", "do_exists"], "status": "approved", "version": "1.0"}, {"id": "NG0161", "symbol": "⊕", "name": "user", "description": "User concept in entity", "category": "entity", "aliases": ["user_alt", "user_v2", "do_user"], "status": "approved", "version": "1.0"}, {"id": "NG0162", "symbol": "⧉", "name": "token", "description": "Token concept in entity", "category": "entity", "aliases": ["token_alt", "token_v2", "do_token"], "status": "approved", "version": "1.0"}, {"id": "NG0163", "symbol": "↯", "name": "auth", "description": "Auth concept in entity", "category": "entity", "aliases": ["auth_alt", "auth_v2", "do_auth"], "status": "approved", "version": "1.0"}, {"id": "NG0164", "symbol": "⊖", "name": "payment", "description": "Payment concept in entity", "category": "entity", "aliases": ["payment_alt", "payment_v2", "do_payment"], "status": "approved", "version": "1.0"}, {"id": "NG0165", "symbol": "⊞", "name": "api", "description": "Api concept in entity", "category": "entity", "aliases": ["api_alt", "api_v2", "do_api"], "status": "approved", "version": "1.0"}, {"id": "NG0166", "symbol": "⊂", "name": "session", "description": "Session concept in entity", "category": "entity", "aliases": ["session_alt", "session_v2", "do_session"], "status": "approved", "version": "1.0"}, {"id": "NG0167", "symbol": "⬒", "name": "file", "description": "File concept in entity", "category": "entity", "aliases": ["file_alt", "file_v2", "do_file"], "status": "approved", "version": "1.0"}, {"id": "NG0168", "symbol": "✖", "name": "device", "description": "Device concept in entity", "category": "entity", "aliases": ["device_alt", "device_v2", "do_device"], "status": "approved", "version": "1.0"}, {"id": "NG0169", "symbol": "⚠️", "name": "input", "description": "Input concept in entity", "category": "entity", "aliases": ["input_alt", "input_v2", "do_input"], "status": "approved", "version": "1.0"}, {"id": "NG0170", "symbol": "✔", "name": "output", "description": "Output concept in entity", "category": "entity", "aliases": ["output_alt", "output_v2", "do_output"], "status": "approved", "version": "1.0"}, {"id": "NG0171", "symbol": "∧", "name": "crypto", "description": "Crypto concept in domain", "category": "domain", "aliases": ["crypto_alt", "crypto_v2", "do_crypto"], "status": "approved", "version": "1.0"}, {"id": "NG0172", "symbol": "∨", "name": "health", "description": "Health concept in domain", "category": "domain", "aliases": ["health_alt", "health_v2", "do_health"], "status": "approved", "version": "1.0"}, {"id": "NG0173", "symbol": "¬", "name": "edu", "description": "Edu concept in domain", "category": "domain", "aliases": ["edu_alt", "edu_v2", "do_edu"], "status": "approved", "version": "1.0"}, {"id": "NG0174", "symbol": "→", "name": "gov", "description": "Gov concept in domain", "category": "domain", "aliases": ["gov_alt", "gov_v2", "do_gov"], "status": "approved", "version": "1.0"}, {"id": "NG0175", "symbol": "≡", "name": "store", "description": "Store concept in domain", "category": "domain", "aliases": ["store_alt", "store_v2", "do_store"], "status": "approved", "version": "1.0"}, {"id": "NG0176", "symbol": "⊨", "name": "network", "description": "Network concept in domain", "category": "domain", "aliases": ["network_alt", "network_v2", "do_network"], "status": "approved", "version": "1.0"}, {"id": "NG0177", "symbol": "⇌", "name": "email", "description": "Email concept in domain", "category": "domain", "aliases": ["email_alt", "email_v2", "do_email"], "status": "approved", "version": "1.0"}, {"id": "NG0178", "symbol": "📌", "name": "log", "description": "Log concept in domain", "category": "domain", "aliases": ["log_alt", "log_v2", "do_log"], "status": "approved", "version": "1.0"}, {"id": "NG0179", "symbol": "🧠", "name": "analytics", "description": "Analytics concept in domain", "category": "domain", "aliases": ["analytics_alt", "analytics_v2", "do_analytics"], "status": "approved", "version": "1.0"}, {"id": "NG0180", "symbol": "⊘", "name": "form", "description": "Form concept in domain", "category": "domain", "aliases": ["form_alt", "form_v2", "do_form"], "status": "approved", "version": "1.0"}, {"id": "NG0181", "symbol": "⊕", "name": "fix", "description": "Fix concept in action", "category": "action", "aliases": ["fix_alt", "fix_v2", "do_fix"], "status": "approved", "version": "1.0"}, {"id": "NG0182", "symbol": "⧉", "name": "create", "description": "Create concept in action", "category": "action", "aliases": ["create_alt", "create_v2", "do_create"], "status": "approved", "version": "1.0"}, {"id": "NG0183", "symbol": "↯", "name": "delete", "description": "Delete concept in action", "category": "action", "aliases": ["delete_alt", "delete_v2", "do_delete"], "status": "approved", "version": "1.0"}, {"id": "NG0184", "symbol": "⊖", "name": "update", "description": "Update concept in action", "category": "action", "aliases": ["update_alt", "update_v2", "do_update"], "status": "approved", "version": "1.0"}, {"id": "NG0185", "symbol": "⊞", "name": "patch", "description": "Patch concept in action", "category": "action", "aliases": ["patch_alt", "patch_v2", "do_patch"], "status": "approved", "version": "1.0"}, {"id": "NG0186", "symbol": "⊂", "name": "refactor", "description": "Refactor concept in action", "category": "action", "aliases": ["refactor_alt", "refactor_v2", "do_refactor"], "status": "approved", "version": "1.0"}, {"id": "NG0187", "symbol": "⬒", "name": "retry", "description": "Retry concept in action", "category": "action", "aliases": ["retry_alt", "retry_v2", "do_retry"], "status": "approved", "version": "1.0"}, {"id": "NG0188", "symbol": "✖", "name": "abort", "description": "Abort concept in action", "category": "action", "aliases": ["abort_alt", "abort_v2", "do_abort"], "status": "approved", "version": "1.0"}, {"id": "NG0189", "symbol": "⚠️", "name": "init", "description": "Init concept in action", "category": "action", "aliases": ["init_alt", "init_v2", "do_init"], "status": "approved", "version": "1.0"}, {"id": "NG0190", "symbol": "✔", "name": "assign", "description": "Assign concept in action", "category": "action", "aliases": ["assign_alt", "assign_v2", "do_assign"], "status": "approved", "version": "1.0"}, {"id": "NG0191", "symbol": "∧", "name": "loop", "description": "Loop concept in structure", "category": "structure", "aliases": ["loop_alt", "loop_v2", "do_loop"], "status": "approved", "version": "1.0"}, {"id": "NG0192", "symbol": "∨", "name": "function", "description": "Function concept in structure", "category": "structure", "aliases": ["function_alt", "function_v2", "do_function"], "status": "approved", "version": "1.0"}, {"id": "NG0193", "symbol": "¬", "name": "class", "description": "Class concept in structure", "category": "structure", "aliases": ["class_alt", "class_v2", "do_class"], "status": "approved", "version": "1.0"}, {"id": "NG0194", "symbol": "→", "name": "block", "description": "Block concept in structure", "category": "structure", "aliases": ["block_alt", "block_v2", "do_block"], "status": "approved", "version": "1.0"}, {"id": "NG0195", "symbol": "≡", "name": "condition", "description": "Condition concept in structure", "category": "structure", "aliases": ["condition_alt", "condition_v2", "do_condition"], "status": "approved", "version": "1.0"}, {"id": "NG0196", "symbol": "⊨", "name": "branch", "description": "Branch concept in structure", "category": "structure", "aliases": ["branch_alt", "branch_v2", "do_branch"], "status": "approved", "version": "1.0"}, {"id": "NG0197", "symbol": "⇌", "name": "switch", "description": "Switch concept in structure", "category": "structure", "aliases": ["switch_alt", "switch_v2", "do_switch"], "status": "approved", "version": "1.0"}, {"id": "NG0198", "symbol": "📌", "name": "module", "description": "Module concept in structure", "category": "structure", "aliases": ["module_alt", "module_v2", "do_module"], "status": "approved", "version": "1.0"}, {"id": "NG0199", "symbol": "🧠", "name": "pipeline", "description": "Pipeline concept in structure", "category": "structure", "aliases": ["pipeline_alt", "pipeline_v2", "do_pipeline"], "status": "approved", "version": "1.0"}, {"id": "NG0200", "symbol": "⊘", "name": "import", "description": "Import concept in structure", "category": "structure", "aliases": ["import_alt", "import_v2", "do_import"], "status": "approved", "version": "1.0"}, {"id": "NG0201", "symbol": "⊕", "name": "null", "description": "Null concept in state", "category": "state", "aliases": ["null_alt", "null_v2", "do_null"], "status": "approved", "version": "1.0"}, {"id": "NG0202", "symbol": "⧉", "name": "error", "description": "Error concept in state", "category": "state", "aliases": ["error_alt", "error_v2", "do_error"], "status": "approved", "version": "1.0"}, {"id": "NG0203", "symbol": "↯", "name": "valid", "description": "Valid concept in state", "category": "state", "aliases": ["valid_alt", "valid_v2", "do_valid"], "status": "approved", "version": "1.0"}, {"id": "NG0204", "symbol": "⊖", "name": "warning", "description": "Warning concept in state", "category": "state", "aliases": ["warning_alt", "warning_v2", "do_warning"], "status": "approved", "version": "1.0"}, {"id": "NG0205", "symbol": "⊞", "name": "loading", "description": "Loading concept in state", "category": "state", "aliases": ["loading_alt", "loading_v2", "do_loading"], "status": "approved", "version": "1.0"}, {"id": "NG0206", "symbol": "⊂", "name": "ready", "description": "Ready concept in state", "category": "state", "aliases": ["ready_alt", "ready_v2", "do_ready"], "status": "approved", "version": "1.0"}, {"id": "NG0207", "symbol": "⬒", "name": "terminated", "description": "Terminated concept in state", "category": "state", "aliases": ["terminated_alt", "terminated_v2", "do_terminated"], "status": "approved", "version": "1.0"}, {"id": "NG0208", "symbol": "✖", "name": "retrying", "description": "Retrying concept in state", "category": "state", "aliases": ["retrying_alt", "retrying_v2", "do_retrying"], "status": "approved", "version": "1.0"}, {"id": "NG0209", "symbol": "⚠️", "name": "locked", "description": "Locked concept in state", "category": "state", "aliases": ["locked_alt", "locked_v2", "do_locked"], "status": "approved", "version": "1.0"}, {"id": "NG0210", "symbol": "✔", "name": "incomplete", "description": "Incomplete concept in state", "category": "state", "aliases": ["incomplete_alt", "incomplete_v2", "do_incomplete"], "status": "approved", "version": "1.0"}, {"id": "NG0211", "symbol": "∧", "name": "and", "description": "And concept in logic", "category": "logic", "aliases": ["and_alt", "and_v2", "do_and"], "status": "approved", "version": "1.0"}, {"id": "NG0212", "symbol": "∨", "name": "or", "description": "Or concept in logic", "category": "logic", "aliases": ["or_alt", "or_v2", "do_or"], "status": "approved", "version": "1.0"}, {"id": "NG0213", "symbol": "¬", "name": "not", "description": "Not concept in logic", "category": "logic", "aliases": ["not_alt", "not_v2", "do_not"], "status": "approved", "version": "1.0"}, {"id": "NG0214", "symbol": "→", "name": "implies", "description": "Implies concept in logic", "category": "logic", "aliases": ["implies_alt", "implies_v2", "do_implies"], "status": "approved", "version": "1.0"}, {"id": "NG0215", "symbol": "≡", "name": "equals", "description": "Equals concept in logic", "category": "logic", "aliases": ["equals_alt", "equals_v2", "do_equals"], "status": "approved", "version": "1.0"}, {"id": "NG0216", "symbol": "⊨", "name": "defined_as", "description": "Defined_as concept in logic", "category": "logic", "aliases": ["defined_as_alt", "defined_as_v2", "do_defined_as"], "status": "approved", "version": "1.0"}, {"id": "NG0217", "symbol": "⇌", "name": "greater_than", "description": "Greater_than concept in logic", "category": "logic", "aliases": ["greater_than_alt", "greater_than_v2", "do_greater_than"], "status": "approved", "version": "1.0"}, {"id": "NG0218", "symbol": "📌", "name": "less_than", "description": "Less_than concept in logic", "category": "logic", "aliases": ["less_than_alt", "less_than_v2", "do_less_than"], "status": "approved", "version": "1.0"}, {"id": "NG0219", "symbol": "🧠", "name": "xor", "description": "Xor concept in logic", "category": "logic", "aliases": ["xor_alt", "xor_v2", "do_xor"], "status": "approved", "version": "1.0"}, {"id": "NG0220", "symbol": "⊘", "name": "exists", "description": "Exists concept in logic", "category": "logic", "aliases": ["exists_alt", "exists_v2", "do_exists"], "status": "approved", "version": "1.0"}, {"id": "NG0221", "symbol": "⊕", "name": "user", "description": "User concept in entity", "category": "entity", "aliases": ["user_alt", "user_v2", "do_user"], "status": "approved", "version": "1.0"}, {"id": "NG0222", "symbol": "⧉", "name": "token", "description": "Token concept in entity", "category": "entity", "aliases": ["token_alt", "token_v2", "do_token"], "status": "approved", "version": "1.0"}, {"id": "NG0223", "symbol": "↯", "name": "auth", "description": "Auth concept in entity", "category": "entity", "aliases": ["auth_alt", "auth_v2", "do_auth"], "status": "approved", "version": "1.0"}, {"id": "NG0224", "symbol": "⊖", "name": "payment", "description": "Payment concept in entity", "category": "entity", "aliases": ["payment_alt", "payment_v2", "do_payment"], "status": "approved", "version": "1.0"}, {"id": "NG0225", "symbol": "⊞", "name": "api", "description": "Api concept in entity", "category": "entity", "aliases": ["api_alt", "api_v2", "do_api"], "status": "approved", "version": "1.0"}, {"id": "NG0226", "symbol": "⊂", "name": "session", "description": "Session concept in entity", "category": "entity", "aliases": ["session_alt", "session_v2", "do_session"], "status": "approved", "version": "1.0"}, {"id": "NG0227", "symbol": "⬒", "name": "file", "description": "File concept in entity", "category": "entity", "aliases": ["file_alt", "file_v2", "do_file"], "status": "approved", "version": "1.0"}, {"id": "NG0228", "symbol": "✖", "name": "device", "description": "Device concept in entity", "category": "entity", "aliases": ["device_alt", "device_v2", "do_device"], "status": "approved", "version": "1.0"}, {"id": "NG0229", "symbol": "⚠️", "name": "input", "description": "Input concept in entity", "category": "entity", "aliases": ["input_alt", "input_v2", "do_input"], "status": "approved", "version": "1.0"}, {"id": "NG0230", "symbol": "✔", "name": "output", "description": "Output concept in entity", "category": "entity", "aliases": ["output_alt", "output_v2", "do_output"], "status": "approved", "version": "1.0"}, {"id": "NG0231", "symbol": "∧", "name": "crypto", "description": "Crypto concept in domain", "category": "domain", "aliases": ["crypto_alt", "crypto_v2", "do_crypto"], "status": "approved", "version": "1.0"}, {"id": "NG0232", "symbol": "∨", "name": "health", "description": "Health concept in domain", "category": "domain", "aliases": ["health_alt", "health_v2", "do_health"], "status": "approved", "version": "1.0"}, {"id": "NG0233", "symbol": "¬", "name": "edu", "description": "Edu concept in domain", "category": "domain", "aliases": ["edu_alt", "edu_v2", "do_edu"], "status": "approved", "version": "1.0"}, {"id": "NG0234", "symbol": "→", "name": "gov", "description": "Gov concept in domain", "category": "domain", "aliases": ["gov_alt", "gov_v2", "do_gov"], "status": "approved", "version": "1.0"}, {"id": "NG0235", "symbol": "≡", "name": "store", "description": "Store concept in domain", "category": "domain", "aliases": ["store_alt", "store_v2", "do_store"], "status": "approved", "version": "1.0"}, {"id": "NG0236", "symbol": "⊨", "name": "network", "description": "Network concept in domain", "category": "domain", "aliases": ["network_alt", "network_v2", "do_network"], "status": "approved", "version": "1.0"}, {"id": "NG0237", "symbol": "⇌", "name": "email", "description": "Email concept in domain", "category": "domain", "aliases": ["email_alt", "email_v2", "do_email"], "status": "approved", "version": "1.0"}, {"id": "NG0238", "symbol": "📌", "name": "log", "description": "Log concept in domain", "category": "domain", "aliases": ["log_alt", "log_v2", "do_log"], "status": "approved", "version": "1.0"}, {"id": "NG0239", "symbol": "🧠", "name": "analytics", "description": "Analytics concept in domain", "category": "domain", "aliases": ["analytics_alt", "analytics_v2", "do_analytics"], "status": "approved", "version": "1.0"}, {"id": "NG0240", "symbol": "⊘", "name": "form", "description": "Form concept in domain", "category": "domain", "aliases": ["form_alt", "form_v2", "do_form"], "status": "approved", "version": "1.0"}, {"id": "NG0241", "symbol": "⊕", "name": "fix", "description": "Fix concept in action", "category": "action", "aliases": ["fix_alt", "fix_v2", "do_fix"], "status": "approved", "version": "1.0"}, {"id": "NG0242", "symbol": "⧉", "name": "create", "description": "Create concept in action", "category": "action", "aliases": ["create_alt", "create_v2", "do_create"], "status": "approved", "version": "1.0"}, {"id": "NG0243", "symbol": "↯", "name": "delete", "description": "Delete concept in action", "category": "action", "aliases": ["delete_alt", "delete_v2", "do_delete"], "status": "approved", "version": "1.0"}, {"id": "NG0244", "symbol": "⊖", "name": "update", "description": "Update concept in action", "category": "action", "aliases": ["update_alt", "update_v2", "do_update"], "status": "approved", "version": "1.0"}, {"id": "NG0245", "symbol": "⊞", "name": "patch", "description": "Patch concept in action", "category": "action", "aliases": ["patch_alt", "patch_v2", "do_patch"], "status": "approved", "version": "1.0"}, {"id": "NG0246", "symbol": "⊂", "name": "refactor", "description": "Refactor concept in action", "category": "action", "aliases": ["refactor_alt", "refactor_v2", "do_refactor"], "status": "approved", "version": "1.0"}, {"id": "NG0247", "symbol": "⬒", "name": "retry", "description": "Retry concept in action", "category": "action", "aliases": ["retry_alt", "retry_v2", "do_retry"], "status": "approved", "version": "1.0"}, {"id": "NG0248", "symbol": "✖", "name": "abort", "description": "Abort concept in action", "category": "action", "aliases": ["abort_alt", "abort_v2", "do_abort"], "status": "approved", "version": "1.0"}, {"id": "NG0249", "symbol": "⚠️", "name": "init", "description": "Init concept in action", "category": "action", "aliases": ["init_alt", "init_v2", "do_init"], "status": "approved", "version": "1.0"}, {"id": "NG0250", "symbol": "✔", "name": "assign", "description": "Assign concept in action", "category": "action", "aliases": ["assign_alt", "assign_v2", "do_assign"], "status": "approved", "version": "1.0"}, {"id": "NG0251", "symbol": "∧", "name": "loop", "description": "Loop concept in structure", "category": "structure", "aliases": ["loop_alt", "loop_v2", "do_loop"], "status": "approved", "version": "1.0"}, {"id": "NG0252", "symbol": "∨", "name": "function", "description": "Function concept in structure", "category": "structure", "aliases": ["function_alt", "function_v2", "do_function"], "status": "approved", "version": "1.0"}, {"id": "NG0253", "symbol": "¬", "name": "class", "description": "Class concept in structure", "category": "structure", "aliases": ["class_alt", "class_v2", "do_class"], "status": "approved", "version": "1.0"}, {"id": "NG0254", "symbol": "→", "name": "block", "description": "Block concept in structure", "category": "structure", "aliases": ["block_alt", "block_v2", "do_block"], "status": "approved", "version": "1.0"}, {"id": "NG0255", "symbol": "≡", "name": "condition", "description": "Condition concept in structure", "category": "structure", "aliases": ["condition_alt", "condition_v2", "do_condition"], "status": "approved", "version": "1.0"}, {"id": "NG0256", "symbol": "⊨", "name": "branch", "description": "Branch concept in structure", "category": "structure", "aliases": ["branch_alt", "branch_v2", "do_branch"], "status": "approved", "version": "1.0"}, {"id": "NG0257", "symbol": "⇌", "name": "switch", "description": "Switch concept in structure", "category": "structure", "aliases": ["switch_alt", "switch_v2", "do_switch"], "status": "approved", "version": "1.0"}, {"id": "NG0258", "symbol": "📌", "name": "module", "description": "Module concept in structure", "category": "structure", "aliases": ["module_alt", "module_v2", "do_module"], "status": "approved", "version": "1.0"}, {"id": "NG0259", "symbol": "🧠", "name": "pipeline", "description": "Pipeline concept in structure", "category": "structure", "aliases": ["pipeline_alt", "pipeline_v2", "do_pipeline"], "status": "approved", "version": "1.0"}, {"id": "NG0260", "symbol": "⊘", "name": "import", "description": "Import concept in structure", "category": "structure", "aliases": ["import_alt", "import_v2", "do_import"], "status": "approved", "version": "1.0"}, {"id": "NG0261", "symbol": "⊕", "name": "null", "description": "Null concept in state", "category": "state", "aliases": ["null_alt", "null_v2", "do_null"], "status": "approved", "version": "1.0"}, {"id": "NG0262", "symbol": "⧉", "name": "error", "description": "Error concept in state", "category": "state", "aliases": ["error_alt", "error_v2", "do_error"], "status": "approved", "version": "1.0"}, {"id": "NG0263", "symbol": "↯", "name": "valid", "description": "Valid concept in state", "category": "state", "aliases": ["valid_alt", "valid_v2", "do_valid"], "status": "approved", "version": "1.0"}, {"id": "NG0264", "symbol": "⊖", "name": "warning", "description": "Warning concept in state", "category": "state", "aliases": ["warning_alt", "warning_v2", "do_warning"], "status": "approved", "version": "1.0"}, {"id": "NG0265", "symbol": "⊞", "name": "loading", "description": "Loading concept in state", "category": "state", "aliases": ["loading_alt", "loading_v2", "do_loading"], "status": "approved", "version": "1.0"}, {"id": "NG0266", "symbol": "⊂", "name": "ready", "description": "Ready concept in state", "category": "state", "aliases": ["ready_alt", "ready_v2", "do_ready"], "status": "approved", "version": "1.0"}, {"id": "NG0267", "symbol": "⬒", "name": "terminated", "description": "Terminated concept in state", "category": "state", "aliases": ["terminated_alt", "terminated_v2", "do_terminated"], "status": "approved", "version": "1.0"}, {"id": "NG0268", "symbol": "✖", "name": "retrying", "description": "Retrying concept in state", "category": "state", "aliases": ["retrying_alt", "retrying_v2", "do_retrying"], "status": "approved", "version": "1.0"}, {"id": "NG0269", "symbol": "⚠️", "name": "locked", "description": "Locked concept in state", "category": "state", "aliases": ["locked_alt", "locked_v2", "do_locked"], "status": "approved", "version": "1.0"}, {"id": "NG0270", "symbol": "✔", "name": "incomplete", "description": "Incomplete concept in state", "category": "state", "aliases": ["incomplete_alt", "incomplete_v2", "do_incomplete"], "status": "approved", "version": "1.0"}, {"id": "NG0271", "symbol": "∧", "name": "and", "description": "And concept in logic", "category": "logic", "aliases": ["and_alt", "and_v2", "do_and"], "status": "approved", "version": "1.0"}, {"id": "NG0272", "symbol": "∨", "name": "or", "description": "Or concept in logic", "category": "logic", "aliases": ["or_alt", "or_v2", "do_or"], "status": "approved", "version": "1.0"}, {"id": "NG0273", "symbol": "¬", "name": "not", "description": "Not concept in logic", "category": "logic", "aliases": ["not_alt", "not_v2", "do_not"], "status": "approved", "version": "1.0"}, {"id": "NG0274", "symbol": "→", "name": "implies", "description": "Implies concept in logic", "category": "logic", "aliases": ["implies_alt", "implies_v2", "do_implies"], "status": "approved", "version": "1.0"}, {"id": "NG0275", "symbol": "≡", "name": "equals", "description": "Equals concept in logic", "category": "logic", "aliases": ["equals_alt", "equals_v2", "do_equals"], "status": "approved", "version": "1.0"}, {"id": "NG0276", "symbol": "⊨", "name": "defined_as", "description": "Defined_as concept in logic", "category": "logic", "aliases": ["defined_as_alt", "defined_as_v2", "do_defined_as"], "status": "approved", "version": "1.0"}, {"id": "NG0277", "symbol": "⇌", "name": "greater_than", "description": "Greater_than concept in logic", "category": "logic", "aliases": ["greater_than_alt", "greater_than_v2", "do_greater_than"], "status": "approved", "version": "1.0"}, {"id": "NG0278", "symbol": "📌", "name": "less_than", "description": "Less_than concept in logic", "category": "logic", "aliases": ["less_than_alt", "less_than_v2", "do_less_than"], "status": "approved", "version": "1.0"}, {"id": "NG0279", "symbol": "🧠", "name": "xor", "description": "Xor concept in logic", "category": "logic", "aliases": ["xor_alt", "xor_v2", "do_xor"], "status": "approved", "version": "1.0"}, {"id": "NG0280", "symbol": "⊘", "name": "exists", "description": "Exists concept in logic", "category": "logic", "aliases": ["exists_alt", "exists_v2", "do_exists"], "status": "approved", "version": "1.0"}, {"id": "NG0281", "symbol": "⊕", "name": "user", "description": "User concept in entity", "category": "entity", "aliases": ["user_alt", "user_v2", "do_user"], "status": "approved", "version": "1.0"}, {"id": "NG0282", "symbol": "⧉", "name": "token", "description": "Token concept in entity", "category": "entity", "aliases": ["token_alt", "token_v2", "do_token"], "status": "approved", "version": "1.0"}, {"id": "NG0283", "symbol": "↯", "name": "auth", "description": "Auth concept in entity", "category": "entity", "aliases": ["auth_alt", "auth_v2", "do_auth"], "status": "approved", "version": "1.0"}, {"id": "NG0284", "symbol": "⊖", "name": "payment", "description": "Payment concept in entity", "category": "entity", "aliases": ["payment_alt", "payment_v2", "do_payment"], "status": "approved", "version": "1.0"}, {"id": "NG0285", "symbol": "⊞", "name": "api", "description": "Api concept in entity", "category": "entity", "aliases": ["api_alt", "api_v2", "do_api"], "status": "approved", "version": "1.0"}, {"id": "NG0286", "symbol": "⊂", "name": "session", "description": "Session concept in entity", "category": "entity", "aliases": ["session_alt", "session_v2", "do_session"], "status": "approved", "version": "1.0"}, {"id": "NG0287", "symbol": "⬒", "name": "file", "description": "File concept in entity", "category": "entity", "aliases": ["file_alt", "file_v2", "do_file"], "status": "approved", "version": "1.0"}, {"id": "NG0288", "symbol": "✖", "name": "device", "description": "Device concept in entity", "category": "entity", "aliases": ["device_alt", "device_v2", "do_device"], "status": "approved", "version": "1.0"}, {"id": "NG0289", "symbol": "⚠️", "name": "input", "description": "Input concept in entity", "category": "entity", "aliases": ["input_alt", "input_v2", "do_input"], "status": "approved", "version": "1.0"}, {"id": "NG0290", "symbol": "✔", "name": "output", "description": "Output concept in entity", "category": "entity", "aliases": ["output_alt", "output_v2", "do_output"], "status": "approved", "version": "1.0"}, {"id": "NG0291", "symbol": "∧", "name": "crypto", "description": "Crypto concept in domain", "category": "domain", "aliases": ["crypto_alt", "crypto_v2", "do_crypto"], "status": "approved", "version": "1.0"}, {"id": "NG0292", "symbol": "∨", "name": "health", "description": "Health concept in domain", "category": "domain", "aliases": ["health_alt", "health_v2", "do_health"], "status": "approved", "version": "1.0"}, {"id": "NG0293", "symbol": "¬", "name": "edu", "description": "Edu concept in domain", "category": "domain", "aliases": ["edu_alt", "edu_v2", "do_edu"], "status": "approved", "version": "1.0"}, {"id": "NG0294", "symbol": "→", "name": "gov", "description": "Gov concept in domain", "category": "domain", "aliases": ["gov_alt", "gov_v2", "do_gov"], "status": "approved", "version": "1.0"}, {"id": "NG0295", "symbol": "≡", "name": "store", "description": "Store concept in domain", "category": "domain", "aliases": ["store_alt", "store_v2", "do_store"], "status": "approved", "version": "1.0"}, {"id": "NG0296", "symbol": "⊨", "name": "network", "description": "Network concept in domain", "category": "domain", "aliases": ["network_alt", "network_v2", "do_network"], "status": "approved", "version": "1.0"}, {"id": "NG0297", "symbol": "⇌", "name": "email", "description": "Email concept in domain", "category": "domain", "aliases": ["email_alt", "email_v2", "do_email"], "status": "approved", "version": "1.0"}, {"id": "NG0298", "symbol": "📌", "name": "log", "description": "Log concept in domain", "category": "domain", "aliases": ["log_alt", "log_v2", "do_log"], "status": "approved", "version": "1.0"}, {"id": "NG0299", "symbol": "🧠", "name": "analytics", "description": "Analytics concept in domain", "category": "domain", "aliases": ["analytics_alt", "analytics_v2", "do_analytics"], "status": "approved", "version": "1.0"}, {"id": "NG0300", "symbol": "⊘", "name": "form", "description": "Form concept in domain", "category": "domain", "aliases": ["form_alt", "form_v2", "do_form"], "status": "approved", "version": "1.0"}, {"id": "NG0301", "symbol": "⊕", "name": "fix", "description": "Fix concept in action", "category": "action", "aliases": ["fix_alt", "fix_v2", "do_fix"], "status": "approved", "version": "1.0"}, {"id": "NG0302", "symbol": "⧉", "name": "create", "description": "Create concept in action", "category": "action", "aliases": ["create_alt", "create_v2", "do_create"], "status": "approved", "version": "1.0"}, {"id": "NG0303", "symbol": "↯", "name": "delete", "description": "Delete concept in action", "category": "action", "aliases": ["delete_alt", "delete_v2", "do_delete"], "status": "approved", "version": "1.0"}, {"id": "NG0304", "symbol": "⊖", "name": "update", "description": "Update concept in action", "category": "action", "aliases": ["update_alt", "update_v2", "do_update"], "status": "approved", "version": "1.0"}, {"id": "NG0305", "symbol": "⊞", "name": "patch", "description": "Patch concept in action", "category": "action", "aliases": ["patch_alt", "patch_v2", "do_patch"], "status": "approved", "version": "1.0"}, {"id": "NG0306", "symbol": "⊂", "name": "refactor", "description": "Refactor concept in action", "category": "action", "aliases": ["refactor_alt", "refactor_v2", "do_refactor"], "status": "approved", "version": "1.0"}, {"id": "NG0307", "symbol": "⬒", "name": "retry", "description": "Retry concept in action", "category": "action", "aliases": ["retry_alt", "retry_v2", "do_retry"], "status": "approved", "version": "1.0"}, {"id": "NG0308", "symbol": "✖", "name": "abort", "description": "Abort concept in action", "category": "action", "aliases": ["abort_alt", "abort_v2", "do_abort"], "status": "approved", "version": "1.0"}, {"id": "NG0309", "symbol": "⚠️", "name": "init", "description": "Init concept in action", "category": "action", "aliases": ["init_alt", "init_v2", "do_init"], "status": "approved", "version": "1.0"}, {"id": "NG0310", "symbol": "✔", "name": "assign", "description": "Assign concept in action", "category": "action", "aliases": ["assign_alt", "assign_v2", "do_assign"], "status": "approved", "version": "1.0"}, {"id": "NG0311", "symbol": "∧", "name": "loop", "description": "Loop concept in structure", "category": "structure", "aliases": ["loop_alt", "loop_v2", "do_loop"], "status": "approved", "version": "1.0"}, {"id": "NG0312", "symbol": "∨", "name": "function", "description": "Function concept in structure", "category": "structure", "aliases": ["function_alt", "function_v2", "do_function"], "status": "approved", "version": "1.0"}, {"id": "NG0313", "symbol": "¬", "name": "class", "description": "Class concept in structure", "category": "structure", "aliases": ["class_alt", "class_v2", "do_class"], "status": "approved", "version": "1.0"}, {"id": "NG0314", "symbol": "→", "name": "block", "description": "Block concept in structure", "category": "structure", "aliases": ["block_alt", "block_v2", "do_block"], "status": "approved", "version": "1.0"}, {"id": "NG0315", "symbol": "≡", "name": "condition", "description": "Condition concept in structure", "category": "structure", "aliases": ["condition_alt", "condition_v2", "do_condition"], "status": "approved", "version": "1.0"}, {"id": "NG0316", "symbol": "⊨", "name": "branch", "description": "Branch concept in structure", "category": "structure", "aliases": ["branch_alt", "branch_v2", "do_branch"], "status": "approved", "version": "1.0"}, {"id": "NG0317", "symbol": "⇌", "name": "switch", "description": "Switch concept in structure", "category": "structure", "aliases": ["switch_alt", "switch_v2", "do_switch"], "status": "approved", "version": "1.0"}, {"id": "NG0318", "symbol": "📌", "name": "module", "description": "Module concept in structure", "category": "structure", "aliases": ["module_alt", "module_v2", "do_module"], "status": "approved", "version": "1.0"}, {"id": "NG0319", "symbol": "🧠", "name": "pipeline", "description": "Pipeline concept in structure", "category": "structure", "aliases": ["pipeline_alt", "pipeline_v2", "do_pipeline"], "status": "approved", "version": "1.0"}, {"id": "NG0320", "symbol": "⊘", "name": "import", "description": "Import concept in structure", "category": "structure", "aliases": ["import_alt", "import_v2", "do_import"], "status": "approved", "version": "1.0"}, {"id": "NG0321", "symbol": "⊕", "name": "null", "description": "Null concept in state", "category": "state", "aliases": ["null_alt", "null_v2", "do_null"], "status": "approved", "version": "1.0"}, {"id": "NG0322", "symbol": "⧉", "name": "error", "description": "Error concept in state", "category": "state", "aliases": ["error_alt", "error_v2", "do_error"], "status": "approved", "version": "1.0"}, {"id": "NG0323", "symbol": "↯", "name": "valid", "description": "Valid concept in state", "category": "state", "aliases": ["valid_alt", "valid_v2", "do_valid"], "status": "approved", "version": "1.0"}, {"id": "NG0324", "symbol": "⊖", "name": "warning", "description": "Warning concept in state", "category": "state", "aliases": ["warning_alt", "warning_v2", "do_warning"], "status": "approved", "version": "1.0"}, {"id": "NG0325", "symbol": "⊞", "name": "loading", "description": "Loading concept in state", "category": "state", "aliases": ["loading_alt", "loading_v2", "do_loading"], "status": "approved", "version": "1.0"}, {"id": "NG0326", "symbol": "⊂", "name": "ready", "description": "Ready concept in state", "category": "state", "aliases": ["ready_alt", "ready_v2", "do_ready"], "status": "approved", "version": "1.0"}, {"id": "NG0327", "symbol": "⬒", "name": "terminated", "description": "Terminated concept in state", "category": "state", "aliases": ["terminated_alt", "terminated_v2", "do_terminated"], "status": "approved", "version": "1.0"}, {"id": "NG0328", "symbol": "✖", "name": "retrying", "description": "Retrying concept in state", "category": "state", "aliases": ["retrying_alt", "retrying_v2", "do_retrying"], "status": "approved", "version": "1.0"}, {"id": "NG0329", "symbol": "⚠️", "name": "locked", "description": "Locked concept in state", "category": "state", "aliases": ["locked_alt", "locked_v2", "do_locked"], "status": "approved", "version": "1.0"}, {"id": "NG0330", "symbol": "✔", "name": "incomplete", "description": "Incomplete concept in state", "category": "state", "aliases": ["incomplete_alt", "incomplete_v2", "do_incomplete"], "status": "approved", "version": "1.0"}, {"id": "NG0331", "symbol": "∧", "name": "and", "description": "And concept in logic", "category": "logic", "aliases": ["and_alt", "and_v2", "do_and"], "status": "approved", "version": "1.0"}, {"id": "NG0332", "symbol": "∨", "name": "or", "description": "Or concept in logic", "category": "logic", "aliases": ["or_alt", "or_v2", "do_or"], "status": "approved", "version": "1.0"}, {"id": "NG0333", "symbol": "¬", "name": "not", "description": "Not concept in logic", "category": "logic", "aliases": ["not_alt", "not_v2", "do_not"], "status": "approved", "version": "1.0"}, {"id": "NG0334", "symbol": "→", "name": "implies", "description": "Implies concept in logic", "category": "logic", "aliases": ["implies_alt", "implies_v2", "do_implies"], "status": "approved", "version": "1.0"}, {"id": "NG0335", "symbol": "≡", "name": "equals", "description": "Equals concept in logic", "category": "logic", "aliases": ["equals_alt", "equals_v2", "do_equals"], "status": "approved", "version": "1.0"}, {"id": "NG0336", "symbol": "⊨", "name": "defined_as", "description": "Defined_as concept in logic", "category": "logic", "aliases": ["defined_as_alt", "defined_as_v2", "do_defined_as"], "status": "approved", "version": "1.0"}, {"id": "NG0337", "symbol": "⇌", "name": "greater_than", "description": "Greater_than concept in logic", "category": "logic", "aliases": ["greater_than_alt", "greater_than_v2", "do_greater_than"], "status": "approved", "version": "1.0"}, {"id": "NG0338", "symbol": "📌", "name": "less_than", "description": "Less_than concept in logic", "category": "logic", "aliases": ["less_than_alt", "less_than_v2", "do_less_than"], "status": "approved", "version": "1.0"}, {"id": "NG0339", "symbol": "🧠", "name": "xor", "description": "Xor concept in logic", "category": "logic", "aliases": ["xor_alt", "xor_v2", "do_xor"], "status": "approved", "version": "1.0"}, {"id": "NG0340", "symbol": "⊘", "name": "exists", "description": "Exists concept in logic", "category": "logic", "aliases": ["exists_alt", "exists_v2", "do_exists"], "status": "approved", "version": "1.0"}, {"id": "NG0341", "symbol": "⊕", "name": "user", "description": "User concept in entity", "category": "entity", "aliases": ["user_alt", "user_v2", "do_user"], "status": "approved", "version": "1.0"}, {"id": "NG0342", "symbol": "⧉", "name": "token", "description": "Token concept in entity", "category": "entity", "aliases": ["token_alt", "token_v2", "do_token"], "status": "approved", "version": "1.0"}, {"id": "NG0343", "symbol": "↯", "name": "auth", "description": "Auth concept in entity", "category": "entity", "aliases": ["auth_alt", "auth_v2", "do_auth"], "status": "approved", "version": "1.0"}, {"id": "NG0344", "symbol": "⊖", "name": "payment", "description": "Payment concept in entity", "category": "entity", "aliases": ["payment_alt", "payment_v2", "do_payment"], "status": "approved", "version": "1.0"}, {"id": "NG0345", "symbol": "⊞", "name": "api", "description": "Api concept in entity", "category": "entity", "aliases": ["api_alt", "api_v2", "do_api"], "status": "approved", "version": "1.0"}, {"id": "NG0346", "symbol": "⊂", "name": "session", "description": "Session concept in entity", "category": "entity", "aliases": ["session_alt", "session_v2", "do_session"], "status": "approved", "version": "1.0"}, {"id": "NG0347", "symbol": "⬒", "name": "file", "description": "File concept in entity", "category": "entity", "aliases": ["file_alt", "file_v2", "do_file"], "status": "approved", "version": "1.0"}, {"id": "NG0348", "symbol": "✖", "name": "device", "description": "Device concept in entity", "category": "entity", "aliases": ["device_alt", "device_v2", "do_device"], "status": "approved", "version": "1.0"}, {"id": "NG0349", "symbol": "⚠️", "name": "input", "description": "Input concept in entity", "category": "entity", "aliases": ["input_alt", "input_v2", "do_input"], "status": "approved", "version": "1.0"}, {"id": "NG0350", "symbol": "✔", "name": "output", "description": "Output concept in entity", "category": "entity", "aliases": ["output_alt", "output_v2", "do_output"], "status": "approved", "version": "1.0"}, {"id": "NG0351", "symbol": "∧", "name": "crypto", "description": "Crypto concept in domain", "category": "domain", "aliases": ["crypto_alt", "crypto_v2", "do_crypto"], "status": "approved", "version": "1.0"}, {"id": "NG0352", "symbol": "∨", "name": "health", "description": "Health concept in domain", "category": "domain", "aliases": ["health_alt", "health_v2", "do_health"], "status": "approved", "version": "1.0"}, {"id": "NG0353", "symbol": "¬", "name": "edu", "description": "Edu concept in domain", "category": "domain", "aliases": ["edu_alt", "edu_v2", "do_edu"], "status": "approved", "version": "1.0"}, {"id": "NG0354", "symbol": "→", "name": "gov", "description": "Gov concept in domain", "category": "domain", "aliases": ["gov_alt", "gov_v2", "do_gov"], "status": "approved", "version": "1.0"}, {"id": "NG0355", "symbol": "≡", "name": "store", "description": "Store concept in domain", "category": "domain", "aliases": ["store_alt", "store_v2", "do_store"], "status": "approved", "version": "1.0"}, {"id": "NG0356", "symbol": "⊨", "name": "network", "description": "Network concept in domain", "category": "domain", "aliases": ["network_alt", "network_v2", "do_network"], "status": "approved", "version": "1.0"}, {"id": "NG0357", "symbol": "⇌", "name": "email", "description": "Email concept in domain", "category": "domain", "aliases": ["email_alt", "email_v2", "do_email"], "status": "approved", "version": "1.0"}, {"id": "NG0358", "symbol": "📌", "name": "log", "description": "Log concept in domain", "category": "domain", "aliases": ["log_alt", "log_v2", "do_log"], "status": "approved", "version": "1.0"}, {"id": "NG0359", "symbol": "🧠", "name": "analytics", "description": "Analytics concept in domain", "category": "domain", "aliases": ["analytics_alt", "analytics_v2", "do_analytics"], "status": "approved", "version": "1.0"}, {"id": "NG0360", "symbol": "⊘", "name": "form", "description": "Form concept in domain", "category": "domain", "aliases": ["form_alt", "form_v2", "do_form"], "status": "approved", "version": "1.0"}, {"id": "NG0361", "symbol": "⊕", "name": "fix", "description": "Fix concept in action", "category": "action", "aliases": ["fix_alt", "fix_v2", "do_fix"], "status": "approved", "version": "1.0"}, {"id": "NG0362", "symbol": "⧉", "name": "create", "description": "Create concept in action", "category": "action", "aliases": ["create_alt", "create_v2", "do_create"], "status": "approved", "version": "1.0"}, {"id": "NG0363", "symbol": "↯", "name": "delete", "description": "Delete concept in action", "category": "action", "aliases": ["delete_alt", "delete_v2", "do_delete"], "status": "approved", "version": "1.0"}, {"id": "NG0364", "symbol": "⊖", "name": "update", "description": "Update concept in action", "category": "action", "aliases": ["update_alt", "update_v2", "do_update"], "status": "approved", "version": "1.0"}, {"id": "NG0365", "symbol": "⊞", "name": "patch", "description": "Patch concept in action", "category": "action", "aliases": ["patch_alt", "patch_v2", "do_patch"], "status": "approved", "version": "1.0"}, {"id": "NG0366", "symbol": "⊂", "name": "refactor", "description": "Refactor concept in action", "category": "action", "aliases": ["refactor_alt", "refactor_v2", "do_refactor"], "status": "approved", "version": "1.0"}, {"id": "NG0367", "symbol": "⬒", "name": "retry", "description": "Retry concept in action", "category": "action", "aliases": ["retry_alt", "retry_v2", "do_retry"], "status": "approved", "version": "1.0"}, {"id": "NG0368", "symbol": "✖", "name": "abort", "description": "Abort concept in action", "category": "action", "aliases": ["abort_alt", "abort_v2", "do_abort"], "status": "approved", "version": "1.0"}, {"id": "NG0369", "symbol": "⚠️", "name": "init", "description": "Init concept in action", "category": "action", "aliases": ["init_alt", "init_v2", "do_init"], "status": "approved", "version": "1.0"}, {"id": "NG0370", "symbol": "✔", "name": "assign", "description": "Assign concept in action", "category": "action", "aliases": ["assign_alt", "assign_v2", "do_assign"], "status": "approved", "version": "1.0"}, {"id": "NG0371", "symbol": "∧", "name": "loop", "description": "Loop concept in structure", "category": "structure", "aliases": ["loop_alt", "loop_v2", "do_loop"], "status": "approved", "version": "1.0"}, {"id": "NG0372", "symbol": "∨", "name": "function", "description": "Function concept in structure", "category": "structure", "aliases": ["function_alt", "function_v2", "do_function"], "status": "approved", "version": "1.0"}, {"id": "NG0373", "symbol": "¬", "name": "class", "description": "Class concept in structure", "category": "structure", "aliases": ["class_alt", "class_v2", "do_class"], "status": "approved", "version": "1.0"}, {"id": "NG0374", "symbol": "→", "name": "block", "description": "Block concept in structure", "category": "structure", "aliases": ["block_alt", "block_v2", "do_block"], "status": "approved", "version": "1.0"}, {"id": "NG0375", "symbol": "≡", "name": "condition", "description": "Condition concept in structure", "category": "structure", "aliases": ["condition_alt", "condition_v2", "do_condition"], "status": "approved", "version": "1.0"}, {"id": "NG0376", "symbol": "⊨", "name": "branch", "description": "Branch concept in structure", "category": "structure", "aliases": ["branch_alt", "branch_v2", "do_branch"], "status": "approved", "version": "1.0"}, {"id": "NG0377", "symbol": "⇌", "name": "switch", "description": "Switch concept in structure", "category": "structure", "aliases": ["switch_alt", "switch_v2", "do_switch"], "status": "approved", "version": "1.0"}, {"id": "NG0378", "symbol": "📌", "name": "module", "description": "Module concept in structure", "category": "structure", "aliases": ["module_alt", "module_v2", "do_module"], "status": "approved", "version": "1.0"}, {"id": "NG0379", "symbol": "🧠", "name": "pipeline", "description": "Pipeline concept in structure", "category": "structure", "aliases": ["pipeline_alt", "pipeline_v2", "do_pipeline"], "status": "approved", "version": "1.0"}, {"id": "NG0380", "symbol": "⊘", "name": "import", "description": "Import concept in structure", "category": "structure", "aliases": ["import_alt", "import_v2", "do_import"], "status": "approved", "version": "1.0"}, {"id": "NG0381", "symbol": "⊕", "name": "null", "description": "Null concept in state", "category": "state", "aliases": ["null_alt", "null_v2", "do_null"], "status": "approved", "version": "1.0"}, {"id": "NG0382", "symbol": "⧉", "name": "error", "description": "Error concept in state", "category": "state", "aliases": ["error_alt", "error_v2", "do_error"], "status": "approved", "version": "1.0"}, {"id": "NG0383", "symbol": "↯", "name": "valid", "description": "Valid concept in state", "category": "state", "aliases": ["valid_alt", "valid_v2", "do_valid"], "status": "approved", "version": "1.0"}, {"id": "NG0384", "symbol": "⊖", "name": "warning", "description": "Warning concept in state", "category": "state", "aliases": ["warning_alt", "warning_v2", "do_warning"], "status": "approved", "version": "1.0"}, {"id": "NG0385", "symbol": "⊞", "name": "loading", "description": "Loading concept in state", "category": "state", "aliases": ["loading_alt", "loading_v2", "do_loading"], "status": "approved", "version": "1.0"}, {"id": "NG0386", "symbol": "⊂", "name": "ready", "description": "Ready concept in state", "category": "state", "aliases": ["ready_alt", "ready_v2", "do_ready"], "status": "approved", "version": "1.0"}, {"id": "NG0387", "symbol": "⬒", "name": "terminated", "description": "Terminated concept in state", "category": "state", "aliases": ["terminated_alt", "terminated_v2", "do_terminated"], "status": "approved", "version": "1.0"}, {"id": "NG0388", "symbol": "✖", "name": "retrying", "description": "Retrying concept in state", "category": "state", "aliases": ["retrying_alt", "retrying_v2", "do_retrying"], "status": "approved", "version": "1.0"}, {"id": "NG0389", "symbol": "⚠️", "name": "locked", "description": "Locked concept in state", "category": "state", "aliases": ["locked_alt", "locked_v2", "do_locked"], "status": "approved", "version": "1.0"}, {"id": "NG0390", "symbol": "✔", "name": "incomplete", "description": "Incomplete concept in state", "category": "state", "aliases": ["incomplete_alt", "incomplete_v2", "do_incomplete"], "status": "approved", "version": "1.0"}, {"id": "NG0391", "symbol": "∧", "name": "and", "description": "And concept in logic", "category": "logic", "aliases": ["and_alt", "and_v2", "do_and"], "status": "approved", "version": "1.0"}, {"id": "NG0392", "symbol": "∨", "name": "or", "description": "Or concept in logic", "category": "logic", "aliases": ["or_alt", "or_v2", "do_or"], "status": "approved", "version": "1.0"}, {"id": "NG0393", "symbol": "¬", "name": "not", "description": "Not concept in logic", "category": "logic", "aliases": ["not_alt", "not_v2", "do_not"], "status": "approved", "version": "1.0"}, {"id": "NG0394", "symbol": "→", "name": "implies", "description": "Implies concept in logic", "category": "logic", "aliases": ["implies_alt", "implies_v2", "do_implies"], "status": "approved", "version": "1.0"}, {"id": "NG0395", "symbol": "≡", "name": "equals", "description": "Equals concept in logic", "category": "logic", "aliases": ["equals_alt", "equals_v2", "do_equals"], "status": "approved", "version": "1.0"}, {"id": "NG0396", "symbol": "⊨", "name": "defined_as", "description": "Defined_as concept in logic", "category": "logic", "aliases": ["defined_as_alt", "defined_as_v2", "do_defined_as"], "status": "approved", "version": "1.0"}, {"id": "NG0397", "symbol": "⇌", "name": "greater_than", "description": "Greater_than concept in logic", "category": "logic", "aliases": ["greater_than_alt", "greater_than_v2", "do_greater_than"], "status": "approved", "version": "1.0"}, {"id": "NG0398", "symbol": "📌", "name": "less_than", "description": "Less_than concept in logic", "category": "logic", "aliases": ["less_than_alt", "less_than_v2", "do_less_than"], "status": "approved", "version": "1.0"}, {"id": "NG0399", "symbol": "🧠", "name": "xor", "description": "Xor concept in logic", "category": "logic", "aliases": ["xor_alt", "xor_v2", "do_xor"], "status": "approved", "version": "1.0"}, {"id": "NG0400", "symbol": "⊘", "name": "exists", "description": "Exists concept in logic", "category": "logic", "aliases": ["exists_alt", "exists_v2", "do_exists"], "status": "approved", "version": "1.0"}, {"id": "NG0401", "symbol": "⊕", "name": "user", "description": "User concept in entity", "category": "entity", "aliases": ["user_alt", "user_v2", "do_user"], "status": "approved", "version": "1.0"}, {"id": "NG0402", "symbol": "⧉", "name": "token", "description": "Token concept in entity", "category": "entity", "aliases": ["token_alt", "token_v2", "do_token"], "status": "approved", "version": "1.0"}, {"id": "NG0403", "symbol": "↯", "name": "auth", "description": "Auth concept in entity", "category": "entity", "aliases": ["auth_alt", "auth_v2", "do_auth"], "status": "approved", "version": "1.0"}, {"id": "NG0404", "symbol": "⊖", "name": "payment", "description": "Payment concept in entity", "category": "entity", "aliases": ["payment_alt", "payment_v2", "do_payment"], "status": "approved", "version": "1.0"}, {"id": "NG0405", "symbol": "⊞", "name": "api", "description": "Api concept in entity", "category": "entity", "aliases": ["api_alt", "api_v2", "do_api"], "status": "approved", "version": "1.0"}, {"id": "NG0406", "symbol": "⊂", "name": "session", "description": "Session concept in entity", "category": "entity", "aliases": ["session_alt", "session_v2", "do_session"], "status": "approved", "version": "1.0"}, {"id": "NG0407", "symbol": "⬒", "name": "file", "description": "File concept in entity", "category": "entity", "aliases": ["file_alt", "file_v2", "do_file"], "status": "approved", "version": "1.0"}, {"id": "NG0408", "symbol": "✖", "name": "device", "description": "Device concept in entity", "category": "entity", "aliases": ["device_alt", "device_v2", "do_device"], "status": "approved", "version": "1.0"}, {"id": "NG0409", "symbol": "⚠️", "name": "input", "description": "Input concept in entity", "category": "entity", "aliases": ["input_alt", "input_v2", "do_input"], "status": "approved", "version": "1.0"}, {"id": "NG0410", "symbol": "✔", "name": "output", "description": "Output concept in entity", "category": "entity", "aliases": ["output_alt", "output_v2", "do_output"], "status": "approved", "version": "1.0"}, {"id": "NG0411", "symbol": "∧", "name": "crypto", "description": "Crypto concept in domain", "category": "domain", "aliases": ["crypto_alt", "crypto_v2", "do_crypto"], "status": "approved", "version": "1.0"}, {"id": "NG0412", "symbol": "∨", "name": "health", "description": "Health concept in domain", "category": "domain", "aliases": ["health_alt", "health_v2", "do_health"], "status": "approved", "version": "1.0"}, {"id": "NG0413", "symbol": "¬", "name": "edu", "description": "Edu concept in domain", "category": "domain", "aliases": ["edu_alt", "edu_v2", "do_edu"], "status": "approved", "version": "1.0"}, {"id": "NG0414", "symbol": "→", "name": "gov", "description": "Gov concept in domain", "category": "domain", "aliases": ["gov_alt", "gov_v2", "do_gov"], "status": "approved", "version": "1.0"}, {"id": "NG0415", "symbol": "≡", "name": "store", "description": "Store concept in domain", "category": "domain", "aliases": ["store_alt", "store_v2", "do_store"], "status": "approved", "version": "1.0"}, {"id": "NG0416", "symbol": "⊨", "name": "network", "description": "Network concept in domain", "category": "domain", "aliases": ["network_alt", "network_v2", "do_network"], "status": "approved", "version": "1.0"}, {"id": "NG0417", "symbol": "⇌", "name": "email", "description": "Email concept in domain", "category": "domain", "aliases": ["email_alt", "email_v2", "do_email"], "status": "approved", "version": "1.0"}, {"id": "NG0418", "symbol": "📌", "name": "log", "description": "Log concept in domain", "category": "domain", "aliases": ["log_alt", "log_v2", "do_log"], "status": "approved", "version": "1.0"}, {"id": "NG0419", "symbol": "🧠", "name": "analytics", "description": "Analytics concept in domain", "category": "domain", "aliases": ["analytics_alt", "analytics_v2", "do_analytics"], "status": "approved", "version": "1.0"}, {"id": "NG0420", "symbol": "⊘", "name": "form", "description": "Form concept in domain", "category": "domain", "aliases": ["form_alt", "form_v2", "do_form"], "status": "approved", "version": "1.0"}, {"id": "NG0421", "symbol": "⊕", "name": "fix", "description": "Fix concept in action", "category": "action", "aliases": ["fix_alt", "fix_v2", "do_fix"], "status": "approved", "version": "1.0"}, {"id": "NG0422", "symbol": "⧉", "name": "create", "description": "Create concept in action", "category": "action", "aliases": ["create_alt", "create_v2", "do_create"], "status": "approved", "version": "1.0"}, {"id": "NG0423", "symbol": "↯", "name": "delete", "description": "Delete concept in action", "category": "action", "aliases": ["delete_alt", "delete_v2", "do_delete"], "status": "approved", "version": "1.0"}, {"id": "NG0424", "symbol": "⊖", "name": "update", "description": "Update concept in action", "category": "action", "aliases": ["update_alt", "update_v2", "do_update"], "status": "approved", "version": "1.0"}, {"id": "NG0425", "symbol": "⊞", "name": "patch", "description": "Patch concept in action", "category": "action", "aliases": ["patch_alt", "patch_v2", "do_patch"], "status": "approved", "version": "1.0"}, {"id": "NG0426", "symbol": "⊂", "name": "refactor", "description": "Refactor concept in action", "category": "action", "aliases": ["refactor_alt", "refactor_v2", "do_refactor"], "status": "approved", "version": "1.0"}, {"id": "NG0427", "symbol": "⬒", "name": "retry", "description": "Retry concept in action", "category": "action", "aliases": ["retry_alt", "retry_v2", "do_retry"], "status": "approved", "version": "1.0"}, {"id": "NG0428", "symbol": "✖", "name": "abort", "description": "Abort concept in action", "category": "action", "aliases": ["abort_alt", "abort_v2", "do_abort"], "status": "approved", "version": "1.0"}, {"id": "NG0429", "symbol": "⚠️", "name": "init", "description": "Init concept in action", "category": "action", "aliases": ["init_alt", "init_v2", "do_init"], "status": "approved", "version": "1.0"}, {"id": "NG0430", "symbol": "✔", "name": "assign", "description": "Assign concept in action", "category": "action", "aliases": ["assign_alt", "assign_v2", "do_assign"], "status": "approved", "version": "1.0"}, {"id": "NG0431", "symbol": "∧", "name": "loop", "description": "Loop concept in structure", "category": "structure", "aliases": ["loop_alt", "loop_v2", "do_loop"], "status": "approved", "version": "1.0"}, {"id": "NG0432", "symbol": "∨", "name": "function", "description": "Function concept in structure", "category": "structure", "aliases": ["function_alt", "function_v2", "do_function"], "status": "approved", "version": "1.0"}, {"id": "NG0433", "symbol": "¬", "name": "class", "description": "Class concept in structure", "category": "structure", "aliases": ["class_alt", "class_v2", "do_class"], "status": "approved", "version": "1.0"}, {"id": "NG0434", "symbol": "→", "name": "block", "description": "Block concept in structure", "category": "structure", "aliases": ["block_alt", "block_v2", "do_block"], "status": "approved", "version": "1.0"}, {"id": "NG0435", "symbol": "≡", "name": "condition", "description": "Condition concept in structure", "category": "structure", "aliases": ["condition_alt", "condition_v2", "do_condition"], "status": "approved", "version": "1.0"}, {"id": "NG0436", "symbol": "⊨", "name": "branch", "description": "Branch concept in structure", "category": "structure", "aliases": ["branch_alt", "branch_v2", "do_branch"], "status": "approved", "version": "1.0"}, {"id": "NG0437", "symbol": "⇌", "name": "switch", "description": "Switch concept in structure", "category": "structure", "aliases": ["switch_alt", "switch_v2", "do_switch"], "status": "approved", "version": "1.0"}, {"id": "NG0438", "symbol": "📌", "name": "module", "description": "Module concept in structure", "category": "structure", "aliases": ["module_alt", "module_v2", "do_module"], "status": "approved", "version": "1.0"}, {"id": "NG0439", "symbol": "🧠", "name": "pipeline", "description": "Pipeline concept in structure", "category": "structure", "aliases": ["pipeline_alt", "pipeline_v2", "do_pipeline"], "status": "approved", "version": "1.0"}, {"id": "NG0440", "symbol": "⊘", "name": "import", "description": "Import concept in structure", "category": "structure", "aliases": ["import_alt", "import_v2", "do_import"], "status": "approved", "version": "1.0"}, {"id": "NG0441", "symbol": "⊕", "name": "null", "description": "Null concept in state", "category": "state", "aliases": ["null_alt", "null_v2", "do_null"], "status": "approved", "version": "1.0"}, {"id": "NG0442", "symbol": "⧉", "name": "error", "description": "Error concept in state", "category": "state", "aliases": ["error_alt", "error_v2", "do_error"], "status": "approved", "version": "1.0"}, {"id": "NG0443", "symbol": "↯", "name": "valid", "description": "Valid concept in state", "category": "state", "aliases": ["valid_alt", "valid_v2", "do_valid"], "status": "approved", "version": "1.0"}, {"id": "NG0444", "symbol": "⊖", "name": "warning", "description": "Warning concept in state", "category": "state", "aliases": ["warning_alt", "warning_v2", "do_warning"], "status": "approved", "version": "1.0"}, {"id": "NG0445", "symbol": "⊞", "name": "loading", "description": "Loading concept in state", "category": "state", "aliases": ["loading_alt", "loading_v2", "do_loading"], "status": "approved", "version": "1.0"}, {"id": "NG0446", "symbol": "⊂", "name": "ready", "description": "Ready concept in state", "category": "state", "aliases": ["ready_alt", "ready_v2", "do_ready"], "status": "approved", "version": "1.0"}, {"id": "NG0447", "symbol": "⬒", "name": "terminated", "description": "Terminated concept in state", "category": "state", "aliases": ["terminated_alt", "terminated_v2", "do_terminated"], "status": "approved", "version": "1.0"}, {"id": "NG0448", "symbol": "✖", "name": "retrying", "description": "Retrying concept in state", "category": "state", "aliases": ["retrying_alt", "retrying_v2", "do_retrying"], "status": "approved", "version": "1.0"}, {"id": "NG0449", "symbol": "⚠️", "name": "locked", "description": "Locked concept in state", "category": "state", "aliases": ["locked_alt", "locked_v2", "do_locked"], "status": "approved", "version": "1.0"}, {"id": "NG0450", "symbol": "✔", "name": "incomplete", "description": "Incomplete concept in state", "category": "state", "aliases": ["incomplete_alt", "incomplete_v2", "do_incomplete"], "status": "approved", "version": "1.0"}, {"id": "NG0451", "symbol": "∧", "name": "and", "description": "And concept in logic", "category": "logic", "aliases": ["and_alt", "and_v2", "do_and"], "status": "approved", "version": "1.0"}, {"id": "NG0452", "symbol": "∨", "name": "or", "description": "Or concept in logic", "category": "logic", "aliases": ["or_alt", "or_v2", "do_or"], "status": "approved", "version": "1.0"}, {"id": "NG0453", "symbol": "¬", "name": "not", "description": "Not concept in logic", "category": "logic", "aliases": ["not_alt", "not_v2", "do_not"], "status": "approved", "version": "1.0"}, {"id": "NG0454", "symbol": "→", "name": "implies", "description": "Implies concept in logic", "category": "logic", "aliases": ["implies_alt", "implies_v2", "do_implies"], "status": "approved", "version": "1.0"}, {"id": "NG0455", "symbol": "≡", "name": "equals", "description": "Equals concept in logic", "category": "logic", "aliases": ["equals_alt", "equals_v2", "do_equals"], "status": "approved", "version": "1.0"}, {"id": "NG0456", "symbol": "⊨", "name": "defined_as", "description": "Defined_as concept in logic", "category": "logic", "aliases": ["defined_as_alt", "defined_as_v2", "do_defined_as"], "status": "approved", "version": "1.0"}, {"id": "NG0457", "symbol": "⇌", "name": "greater_than", "description": "Greater_than concept in logic", "category": "logic", "aliases": ["greater_than_alt", "greater_than_v2", "do_greater_than"], "status": "approved", "version": "1.0"}, {"id": "NG0458", "symbol": "📌", "name": "less_than", "description": "Less_than concept in logic", "category": "logic", "aliases": ["less_than_alt", "less_than_v2", "do_less_than"], "status": "approved", "version": "1.0"}, {"id": "NG0459", "symbol": "🧠", "name": "xor", "description": "Xor concept in logic", "category": "logic", "aliases": ["xor_alt", "xor_v2", "do_xor"], "status": "approved", "version": "1.0"}, {"id": "NG0460", "symbol": "⊘", "name": "exists", "description": "Exists concept in logic", "category": "logic", "aliases": ["exists_alt", "exists_v2", "do_exists"], "status": "approved", "version": "1.0"}, {"id": "NG0461", "symbol": "⊕", "name": "user", "description": "User concept in entity", "category": "entity", "aliases": ["user_alt", "user_v2", "do_user"], "status": "approved", "version": "1.0"}, {"id": "NG0462", "symbol": "⧉", "name": "token", "description": "Token concept in entity", "category": "entity", "aliases": ["token_alt", "token_v2", "do_token"], "status": "approved", "version": "1.0"}, {"id": "NG0463", "symbol": "↯", "name": "auth", "description": "Auth concept in entity", "category": "entity", "aliases": ["auth_alt", "auth_v2", "do_auth"], "status": "approved", "version": "1.0"}, {"id": "NG0464", "symbol": "⊖", "name": "payment", "description": "Payment concept in entity", "category": "entity", "aliases": ["payment_alt", "payment_v2", "do_payment"], "status": "approved", "version": "1.0"}, {"id": "NG0465", "symbol": "⊞", "name": "api", "description": "Api concept in entity", "category": "entity", "aliases": ["api_alt", "api_v2", "do_api"], "status": "approved", "version": "1.0"}, {"id": "NG0466", "symbol": "⊂", "name": "session", "description": "Session concept in entity", "category": "entity", "aliases": ["session_alt", "session_v2", "do_session"], "status": "approved", "version": "1.0"}, {"id": "NG0467", "symbol": "⬒", "name": "file", "description": "File concept in entity", "category": "entity", "aliases": ["file_alt", "file_v2", "do_file"], "status": "approved", "version": "1.0"}, {"id": "NG0468", "symbol": "✖", "name": "device", "description": "Device concept in entity", "category": "entity", "aliases": ["device_alt", "device_v2", "do_device"], "status": "approved", "version": "1.0"}, {"id": "NG0469", "symbol": "⚠️", "name": "input", "description": "Input concept in entity", "category": "entity", "aliases": ["input_alt", "input_v2", "do_input"], "status": "approved", "version": "1.0"}, {"id": "NG0470", "symbol": "✔", "name": "output", "description": "Output concept in entity", "category": "entity", "aliases": ["output_alt", "output_v2", "do_output"], "status": "approved", "version": "1.0"}, {"id": "NG0471", "symbol": "∧", "name": "crypto", "description": "Crypto concept in domain", "category": "domain", "aliases": ["crypto_alt", "crypto_v2", "do_crypto"], "status": "approved", "version": "1.0"}, {"id": "NG0472", "symbol": "∨", "name": "health", "description": "Health concept in domain", "category": "domain", "aliases": ["health_alt", "health_v2", "do_health"], "status": "approved", "version": "1.0"}, {"id": "NG0473", "symbol": "¬", "name": "edu", "description": "Edu concept in domain", "category": "domain", "aliases": ["edu_alt", "edu_v2", "do_edu"], "status": "approved", "version": "1.0"}, {"id": "NG0474", "symbol": "→", "name": "gov", "description": "Gov concept in domain", "category": "domain", "aliases": ["gov_alt", "gov_v2", "do_gov"], "status": "approved", "version": "1.0"}, {"id": "NG0475", "symbol": "≡", "name": "store", "description": "Store concept in domain", "category": "domain", "aliases": ["store_alt", "store_v2", "do_store"], "status": "approved", "version": "1.0"}, {"id": "NG0476", "symbol": "⊨", "name": "network", "description": "Network concept in domain", "category": "domain", "aliases": ["network_alt", "network_v2", "do_network"], "status": "approved", "version": "1.0"}, {"id": "NG0477", "symbol": "⇌", "name": "email", "description": "Email concept in domain", "category": "domain", "aliases": ["email_alt", "email_v2", "do_email"], "status": "approved", "version": "1.0"}, {"id": "NG0478", "symbol": "📌", "name": "log", "description": "Log concept in domain", "category": "domain", "aliases": ["log_alt", "log_v2", "do_log"], "status": "approved", "version": "1.0"}, {"id": "NG0479", "symbol": "🧠", "name": "analytics", "description": "Analytics concept in domain", "category": "domain", "aliases": ["analytics_alt", "analytics_v2", "do_analytics"], "status": "approved", "version": "1.0"}, {"id": "NG0480", "symbol": "⊘", "name": "form", "description": "Form concept in domain", "category": "domain", "aliases": ["form_alt", "form_v2", "do_form"], "status": "approved", "version": "1.0"}, {"id": "NG0481", "symbol": "⊕", "name": "fix", "description": "Fix concept in action", "category": "action", "aliases": ["fix_alt", "fix_v2", "do_fix"], "status": "approved", "version": "1.0"}, {"id": "NG0482", "symbol": "⧉", "name": "create", "description": "Create concept in action", "category": "action", "aliases": ["create_alt", "create_v2", "do_create"], "status": "approved", "version": "1.0"}, {"id": "NG0483", "symbol": "↯", "name": "delete", "description": "Delete concept in action", "category": "action", "aliases": ["delete_alt", "delete_v2", "do_delete"], "status": "approved", "version": "1.0"}, {"id": "NG0484", "symbol": "⊖", "name": "update", "description": "Update concept in action", "category": "action", "aliases": ["update_alt", "update_v2", "do_update"], "status": "approved", "version": "1.0"}, {"id": "NG0485", "symbol": "⊞", "name": "patch", "description": "Patch concept in action", "category": "action", "aliases": ["patch_alt", "patch_v2", "do_patch"], "status": "approved", "version": "1.0"}, {"id": "NG0486", "symbol": "⊂", "name": "refactor", "description": "Refactor concept in action", "category": "action", "aliases": ["refactor_alt", "refactor_v2", "do_refactor"], "status": "approved", "version": "1.0"}, {"id": "NG0487", "symbol": "⬒", "name": "retry", "description": "Retry concept in action", "category": "action", "aliases": ["retry_alt", "retry_v2", "do_retry"], "status": "approved", "version": "1.0"}, {"id": "NG0488", "symbol": "✖", "name": "abort", "description": "Abort concept in action", "category": "action", "aliases": ["abort_alt", "abort_v2", "do_abort"], "status": "approved", "version": "1.0"}, {"id": "NG0489", "symbol": "⚠️", "name": "init", "description": "Init concept in action", "category": "action", "aliases": ["init_alt", "init_v2", "do_init"], "status": "approved", "version": "1.0"}, {"id": "NG0490", "symbol": "✔", "name": "assign", "description": "Assign concept in action", "category": "action", "aliases": ["assign_alt", "assign_v2", "do_assign"], "status": "approved", "version": "1.0"}, {"id": "NG0491", "symbol": "∧", "name": "loop", "description": "Loop concept in structure", "category": "structure", "aliases": ["loop_alt", "loop_v2", "do_loop"], "status": "approved", "version": "1.0"}, {"id": "NG0492", "symbol": "∨", "name": "function", "description": "Function concept in structure", "category": "structure", "aliases": ["function_alt", "function_v2", "do_function"], "status": "approved", "version": "1.0"}, {"id": "NG0493", "symbol": "¬", "name": "class", "description": "Class concept in structure", "category": "structure", "aliases": ["class_alt", "class_v2", "do_class"], "status": "approved", "version": "1.0"}, {"id": "NG0494", "symbol": "→", "name": "block", "description": "Block concept in structure", "category": "structure", "aliases": ["block_alt", "block_v2", "do_block"], "status": "approved", "version": "1.0"}, {"id": "NG0495", "symbol": "≡", "name": "condition", "description": "Condition concept in structure", "category": "structure", "aliases": ["condition_alt", "condition_v2", "do_condition"], "status": "approved", "version": "1.0"}, {"id": "NG0496", "symbol": "⊨", "name": "branch", "description": "Branch concept in structure", "category": "structure", "aliases": ["branch_alt", "branch_v2", "do_branch"], "status": "approved", "version": "1.0"}, {"id": "NG0497", "symbol": "⇌", "name": "switch", "description": "Switch concept in structure", "category": "structure", "aliases": ["switch_alt", "switch_v2", "do_switch"], "status": "approved", "version": "1.0"}, {"id": "NG0498", "symbol": "📌", "name": "module", "description": "Module concept in structure", "category": "structure", "aliases": ["module_alt", "module_v2", "do_module"], "status": "approved", "version": "1.0"}, {"id": "NG0499", "symbol": "🧠", "name": "pipeline", "description": "Pipeline concept in structure", "category": "structure", "aliases": ["pipeline_alt", "pipeline_v2", "do_pipeline"], "status": "approved", "version": "1.0"}, {"id": "NG0500", "symbol": "⊘", "name": "import", "description": "Import concept in structure", "category": "structure", "aliases": ["import_alt", "import_v2", "do_import"], "status": "approved", "version": "1.0"}, {"id": "NG0501", "symbol": "⊕", "name": "null", "description": "Null concept in state", "category": "state", "aliases": ["null_alt", "null_v2", "do_null"], "status": "approved", "version": "1.0"}, {"id": "NG0502", "symbol": "⧉", "name": "error", "description": "Error concept in state", "category": "state", "aliases": ["error_alt", "error_v2", "do_error"], "status": "approved", "version": "1.0"}, {"id": "NG0503", "symbol": "↯", "name": "valid", "description": "Valid concept in state", "category": "state", "aliases": ["valid_alt", "valid_v2", "do_valid"], "status": "approved", "version": "1.0"}, {"id": "NG0504", "symbol": "⊖", "name": "warning", "description": "Warning concept in state", "category": "state", "aliases": ["warning_alt", "warning_v2", "do_warning"], "status": "approved", "version": "1.0"}, {"id": "NG0505", "symbol": "⊞", "name": "loading", "description": "Loading concept in state", "category": "state", "aliases": ["loading_alt", "loading_v2", "do_loading"], "status": "approved", "version": "1.0"}, {"id": "NG0506", "symbol": "⊂", "name": "ready", "description": "Ready concept in state", "category": "state", "aliases": ["ready_alt", "ready_v2", "do_ready"], "status": "approved", "version": "1.0"}, {"id": "NG0507", "symbol": "⬒", "name": "terminated", "description": "Terminated concept in state", "category": "state", "aliases": ["terminated_alt", "terminated_v2", "do_terminated"], "status": "approved", "version": "1.0"}, {"id": "NG0508", "symbol": "✖", "name": "retrying", "description": "Retrying concept in state", "category": "state", "aliases": ["retrying_alt", "retrying_v2", "do_retrying"], "status": "approved", "version": "1.0"}, {"id": "NG0509", "symbol": "⚠️", "name": "locked", "description": "Locked concept in state", "category": "state", "aliases": ["locked_alt", "locked_v2", "do_locked"], "status": "approved", "version": "1.0"}, {"id": "NG0510", "symbol": "✔", "name": "incomplete", "description": "Incomplete concept in state", "category": "state", "aliases": ["incomplete_alt", "incomplete_v2", "do_incomplete"], "status": "approved", "version": "1.0"}, {"id": "NG0511", "symbol": "∧", "name": "and", "description": "And concept in logic", "category": "logic", "aliases": ["and_alt", "and_v2", "do_and"], "status": "approved", "version": "1.0"}, {"id": "NG0512", "symbol": "∨", "name": "or", "description": "Or concept in logic", "category": "logic", "aliases": ["or_alt", "or_v2", "do_or"], "status": "approved", "version": "1.0"}]