{"generation_timestamp": "20250525_201502", "total_generated": 14, "target_score_min": 95.0, "symbols": [{"id": "NG2107", "symbol": "⫸", "unicode_point": "U+2AF8", "name": "spatial_navigation", "code": "ng:cognitive_maps:spatial_navigation", "fallback": "[SPATN]", "category": "cognitive_maps", "description": "Symbolic representation for spatial navigation in cognitive_maps", "validation_score": 95.1, "token_cost": 1, "token_density": 0.93, "auto_generated": true, "generator": "ultra_pipeline", "generation_timestamp": "20250525_201502", "approved_date": "2025-05-25T20:15:02.360114", "status": "approved", "tier": "god", "batch_number": 21, "domain_priority": "critical"}, {"id": "NG2108", "symbol": "⏹", "unicode_point": "U+23F9", "name": "conceptual_mapping", "code": "ng:cognitive_maps:conceptual_mapping", "fallback": "[CM]", "category": "cognitive_maps", "description": "Symbolic representation for conceptual mapping in cognitive_maps", "validation_score": 95.9, "token_cost": 1, "token_density": 0.97, "auto_generated": true, "generator": "ultra_pipeline", "generation_timestamp": "20250525_201502", "approved_date": "2025-05-25T20:15:02.360958", "status": "approved", "tier": "god", "batch_number": 21, "domain_priority": "critical"}, {"id": "NG2109", "symbol": "⏺", "unicode_point": "U+23FA", "name": "semantic_spaces", "code": "ng:cognitive_maps:semantic_spaces", "fallback": "[SEMS]", "category": "cognitive_maps", "description": "Symbolic representation for semantic spaces in cognitive_maps", "validation_score": 98.7, "token_cost": 1, "token_density": 0.96, "auto_generated": true, "generator": "ultra_pipeline", "generation_timestamp": "20250525_201502", "approved_date": "2025-05-25T20:15:02.361758", "status": "approved", "tier": "god", "batch_number": 21, "domain_priority": "critical"}, {"id": "NG2110", "symbol": "⋻", "unicode_point": "U+22FB", "name": "topological_reasoning", "code": "ng:cognitive_maps:topological_reasoning", "fallback": "[TREAS]", "category": "cognitive_maps", "description": "Symbolic representation for topological reasoning in cognitive_maps", "validation_score": 98.3, "token_cost": 1, "token_density": 0.95, "auto_generated": true, "generator": "ultra_pipeline", "generation_timestamp": "20250525_201502", "approved_date": "2025-05-25T20:15:02.362581", "status": "approved", "tier": "god", "batch_number": 21, "domain_priority": "critical"}, {"id": "NG2111", "symbol": "⏻", "unicode_point": "U+23FB", "name": "landmark_recognition", "code": "ng:cognitive_maps:landmark_recognition", "fallback": "[LR]", "category": "cognitive_maps", "description": "Symbolic representation for landmark recognition in cognitive_maps", "validation_score": 96.3, "token_cost": 1, "token_density": 0.92, "auto_generated": true, "generator": "ultra_pipeline", "generation_timestamp": "20250525_201502", "approved_date": "2025-05-25T20:15:02.363330", "status": "approved", "tier": "god", "batch_number": 21, "domain_priority": "critical"}, {"id": "NG2112", "symbol": "⫻", "unicode_point": "U+2AFB", "name": "path_planning", "code": "ng:cognitive_maps:path_planning", "fallback": "[PP]", "category": "cognitive_maps", "description": "Symbolic representation for path planning in cognitive_maps", "validation_score": 98.2, "token_cost": 1, "token_density": 0.96, "auto_generated": true, "generator": "ultra_pipeline", "generation_timestamp": "20250525_201502", "approved_date": "2025-05-25T20:15:02.364076", "status": "approved", "tier": "god", "batch_number": 21, "domain_priority": "critical"}, {"id": "NG2113", "symbol": "⯻", "unicode_point": "U+2BFB", "name": "cognitive_landmarks", "code": "ng:cognitive_maps:cognitive_landmarks", "fallback": "[CL]", "category": "cognitive_maps", "description": "Symbolic representation for cognitive landmarks in cognitive_maps", "validation_score": 99.0, "token_cost": 1, "token_density": 0.97, "auto_generated": true, "generator": "ultra_pipeline", "generation_timestamp": "20250525_201502", "approved_date": "2025-05-25T20:15:02.365081", "status": "approved", "tier": "god", "batch_number": 21, "domain_priority": "critical"}, {"id": "NG2114", "symbol": "⋼", "unicode_point": "U+22FC", "name": "spatial_memory", "code": "ng:cognitive_maps:spatial_memory", "fallback": "[SPATM]", "category": "cognitive_maps", "description": "Symbolic representation for spatial memory in cognitive_maps", "validation_score": 99.3, "token_cost": 1, "token_density": 0.92, "auto_generated": true, "generator": "ultra_pipeline", "generation_timestamp": "20250525_201502", "approved_date": "2025-05-25T20:15:02.366329", "status": "approved", "tier": "god", "batch_number": 21, "domain_priority": "critical"}, {"id": "NG2115", "symbol": "⏼", "unicode_point": "U+23FC", "name": "mental_rotation", "code": "ng:cognitive_maps:mental_rotation", "fallback": "[MR]", "category": "cognitive_maps", "description": "Symbolic representation for mental rotation in cognitive_maps", "validation_score": 96.9, "token_cost": 1, "token_density": 0.94, "auto_generated": true, "generator": "ultra_pipeline", "generation_timestamp": "20250525_201502", "approved_date": "2025-05-25T20:15:02.367138", "status": "approved", "tier": "god", "batch_number": 21, "domain_priority": "critical"}, {"id": "NG2116", "symbol": "⋽", "unicode_point": "U+22FD", "name": "coordinate_systems", "code": "ng:cognitive_maps:coordinate_systems", "fallback": "[CS]", "category": "cognitive_maps", "description": "Symbolic representation for coordinate systems in cognitive_maps", "validation_score": 99.1, "token_cost": 1, "token_density": 0.9, "auto_generated": true, "generator": "ultra_pipeline", "generation_timestamp": "20250525_201502", "approved_date": "2025-05-25T20:15:02.367981", "status": "approved", "tier": "god", "batch_number": 21, "domain_priority": "critical"}, {"id": "NG2117", "symbol": "⏽", "unicode_point": "U+23FD", "name": "reference_frames", "code": "ng:cognitive_maps:reference_frames", "fallback": "[RF]", "category": "cognitive_maps", "description": "Symbolic representation for reference frames in cognitive_maps", "validation_score": 98.3, "token_cost": 1, "token_density": 0.97, "auto_generated": true, "generator": "ultra_pipeline", "generation_timestamp": "20250525_201502", "approved_date": "2025-05-25T20:15:02.369212", "status": "approved", "tier": "god", "batch_number": 21, "domain_priority": "critical"}, {"id": "NG2118", "symbol": "⫽", "unicode_point": "U+2AFD", "name": "spatial_updating", "code": "ng:cognitive_maps:spatial_updating", "fallback": "[SPATU]", "category": "cognitive_maps", "description": "Symbolic representation for spatial updating in cognitive_maps", "validation_score": 95.0, "token_cost": 1, "token_density": 0.97, "auto_generated": true, "generator": "ultra_pipeline", "generation_timestamp": "20250525_201502", "approved_date": "2025-05-25T20:15:02.370094", "status": "approved", "tier": "god", "batch_number": 21, "domain_priority": "critical"}, {"id": "NG2119", "symbol": "⯽", "unicode_point": "U+2BFD", "name": "cognitive_distance", "code": "ng:cognitive_maps:cognitive_distance", "fallback": "[CD]", "category": "cognitive_maps", "description": "Symbolic representation for cognitive distance in cognitive_maps", "validation_score": 97.0, "token_cost": 1, "token_density": 0.99, "auto_generated": true, "generator": "ultra_pipeline", "generation_timestamp": "20250525_201502", "approved_date": "2025-05-25T20:15:02.370935", "status": "approved", "tier": "god", "batch_number": 21, "domain_priority": "critical"}, {"id": "NG2120", "symbol": "⏾", "unicode_point": "U+23FE", "name": "spatial_orientation", "code": "ng:cognitive_maps:spatial_orientation", "fallback": "[SPATO]", "category": "cognitive_maps", "description": "Symbolic representation for spatial orientation in cognitive_maps", "validation_score": 96.2, "token_cost": 1, "token_density": 1.0, "auto_generated": true, "generator": "ultra_pipeline", "generation_timestamp": "20250525_201502", "approved_date": "2025-05-25T20:15:02.372221", "status": "approved", "tier": "god", "batch_number": 21, "domain_priority": "critical"}]}