{"rebalance_timestamp": "20250525_201403", "current_state": {"total_symbols": 1790, "domain_counts": {"operator": 99, "logic": 120, "reasoning": 56, "structure": 91, "flow": 107, "memory": 94, "advanced_coding": 306, "meta_programming": 62, "distributed_systems": 108, "quantum_computing": 29, "symbolic_ai": 44, "neural_architectures": 72, "formal_verification": 49, "category_theory": 43, "type_theory": 37, "concurrency_advanced": 79, "machine_learning": 123, "mathematical_structures": 64, "philosophical_concepts": 64, "cognitive_modeling": 54, "reserved_expansion": 89}, "category_counts": {"unknown": 20, "operator": 95, "memory": 94, "logic": 112, "structure": 89, "flow": 102, "reasoning": 55, "advanced_coding": 306, "meta_programming": 62, "distributed_systems": 108, "quantum_computing": 29, "symbolic_ai": 44, "neural_architectures": 72, "formal_verification": 49, "category_theory": 43, "type_theory": 37, "concurrency_advanced": 79, "machine_learning": 123, "mathematical_structures": 64, "philosophical_concepts": 64, "cognitive_modeling": 54, "reserved_expansion": 89}}, "rebalancing_needs": {"total_current": 1790, "total_target": 2020, "remaining_budget": 258, "domain_plans": {"neural_architectures": {"current": 72, "target": 76, "gap": 4, "priority": "LOW", "coverage_percent": 94.73684210526315, "is_critical_secondary": false}, "quantum_computing": {"current": 29, "target": 76, "gap": 47, "priority": "MEDIUM", "coverage_percent": 38.15789473684211, "is_critical_secondary": false}, "symbolic_ai": {"current": 44, "target": 76, "gap": 32, "priority": "MEDIUM", "coverage_percent": 57.89473684210527, "is_critical_secondary": false}, "meta_programming": {"current": 62, "target": 128, "gap": 66, "priority": "MEDIUM", "coverage_percent": 48.4375, "is_critical_secondary": false}, "cognitive_modeling": {"current": 54, "target": 64, "gap": 10, "priority": "HIGH", "coverage_percent": 84.375, "is_critical_secondary": true}, "distributed_systems": {"current": 108, "target": 128, "gap": 20, "priority": "LOW", "coverage_percent": 84.375, "is_critical_secondary": false}, "type_theory": {"current": 37, "target": 64, "gap": 27, "priority": "LOW", "coverage_percent": 57.8125, "is_critical_secondary": false}, "category_theory": {"current": 43, "target": 64, "gap": 21, "priority": "LOW", "coverage_percent": 67.1875, "is_critical_secondary": false}, "formal_verification": {"current": 49, "target": 64, "gap": 15, "priority": "LOW", "coverage_percent": 76.5625, "is_critical_secondary": false}, "machine_learning": {"current": 123, "target": 128, "gap": 5, "priority": "LOW", "coverage_percent": 96.09375, "is_critical_secondary": false}, "concurrency_advanced": {"current": 79, "target": 64, "gap": 0, "priority": "LOW", "coverage_percent": 123.4375, "is_critical_secondary": false}, "mathematical_structures": {"current": 64, "target": 64, "gap": 0, "priority": "LOW", "coverage_percent": 100.0, "is_critical_secondary": false}, "philosophical_concepts": {"current": 64, "target": 64, "gap": 0, "priority": "LOW", "coverage_percent": 100.0, "is_critical_secondary": false}, "logical_dynamics": {"current": 0, "target": 32, "gap": 32, "priority": "MEDIUM", "coverage_percent": 0.0, "is_critical_secondary": false}, "runtime_structures": {"current": 0, "target": 32, "gap": 32, "priority": "MEDIUM", "coverage_percent": 0.0, "is_critical_secondary": false}, "abstract_operators": {"current": 0, "target": 32, "gap": 32, "priority": "MEDIUM", "coverage_percent": 0.0, "is_critical_secondary": false}, "linguistic_mappings": {"current": 0, "target": 32, "gap": 32, "priority": "MEDIUM", "coverage_percent": 0.0, "is_critical_secondary": false}, "cognitive_maps": {"current": 0, "target": 32, "gap": 32, "priority": "HIGH", "coverage_percent": 0.0, "is_critical_secondary": true}, "semantic_networks": {"current": 0, "target": 32, "gap": 32, "priority": "HIGH", "coverage_percent": 0.0, "is_critical_secondary": true}, "knowledge_representation": {"current": 0, "target": 32, "gap": 32, "priority": "MEDIUM", "coverage_percent": 0.0, "is_critical_secondary": true}, "reasoning_patterns": {"current": 0, "target": 32, "gap": 32, "priority": "MEDIUM", "coverage_percent": 0.0, "is_critical_secondary": true}, "advanced_coding": {"current": 306, "target": 256, "gap": 0, "priority": "LOW", "coverage_percent": 119.53125, "is_critical_secondary": false}, "operator": {"current": 99, "target": 64, "gap": 0, "priority": "LOW", "coverage_percent": 154.6875, "is_critical_secondary": false}, "logic": {"current": 120, "target": 64, "gap": 0, "priority": "LOW", "coverage_percent": 187.5, "is_critical_secondary": false}, "reasoning": {"current": 56, "target": 64, "gap": 8, "priority": "LOW", "coverage_percent": 87.5, "is_critical_secondary": false}, "structure": {"current": 91, "target": 64, "gap": 0, "priority": "LOW", "coverage_percent": 142.1875, "is_critical_secondary": false}, "flow": {"current": 107, "target": 64, "gap": 0, "priority": "LOW", "coverage_percent": 167.1875, "is_critical_secondary": false}, "memory": {"current": 94, "target": 64, "gap": 0, "priority": "LOW", "coverage_percent": 146.875, "is_critical_secondary": false}, "reserved_expansion": {"current": 89, "target": 64, "gap": 0, "priority": "LOW", "coverage_percent": 139.0625, "is_critical_secondary": false}}}, "completion_strategy": {"completion_plan": {"cognitive_maps": {"current": 0, "target": 32, "gap": 32, "allocated": 25, "priority_group": "CRITICAL_HIGH", "completion_percent": 78.125, "is_critical": true}, "semantic_networks": {"current": 0, "target": 32, "gap": 32, "allocated": 25, "priority_group": "CRITICAL_HIGH", "completion_percent": 78.125, "is_critical": true}, "knowledge_representation": {"current": 0, "target": 32, "gap": 32, "allocated": 25, "priority_group": "CRITICAL_HIGH", "completion_percent": 78.125, "is_critical": true}, "reasoning_patterns": {"current": 0, "target": 32, "gap": 32, "allocated": 25, "priority_group": "CRITICAL_HIGH", "completion_percent": 78.125, "is_critical": true}, "meta_programming": {"current": 62, "target": 128, "gap": 66, "allocated": 51, "priority_group": "SECONDARY_HIGH", "completion_percent": 88.28125, "is_critical": false}, "quantum_computing": {"current": 29, "target": 76, "gap": 47, "allocated": 47, "priority_group": "SECONDARY_MEDIUM", "completion_percent": 100.0, "is_critical": false}, "symbolic_ai": {"current": 44, "target": 76, "gap": 32, "allocated": 30, "priority_group": "SECONDARY_MEDIUM", "completion_percent": 97.36842105263158, "is_critical": false}, "logical_dynamics": {"current": 0, "target": 32, "gap": 32, "allocated": 3, "priority_group": "SECONDARY_MEDIUM", "completion_percent": 9.375, "is_critical": false}, "runtime_structures": {"current": 0, "target": 32, "gap": 32, "allocated": 3, "priority_group": "SECONDARY_MEDIUM", "completion_percent": 9.375, "is_critical": false}, "abstract_operators": {"current": 0, "target": 32, "gap": 32, "allocated": 3, "priority_group": "SECONDARY_MEDIUM", "completion_percent": 9.375, "is_critical": false}, "linguistic_mappings": {"current": 0, "target": 32, "gap": 32, "allocated": 3, "priority_group": "SECONDARY_MEDIUM", "completion_percent": 9.375, "is_critical": false}, "type_theory": {"current": 37, "target": 64, "gap": 27, "allocated": 3, "priority_group": "SECONDARY_MEDIUM", "completion_percent": 62.5, "is_critical": false}, "category_theory": {"current": 43, "target": 64, "gap": 21, "allocated": 3, "priority_group": "SECONDARY_MEDIUM", "completion_percent": 71.875, "is_critical": false}, "distributed_systems": {"current": 108, "target": 128, "gap": 20, "allocated": 2, "priority_group": "MAINTENANCE", "completion_percent": 85.9375, "is_critical": false}, "formal_verification": {"current": 49, "target": 64, "gap": 15, "allocated": 2, "priority_group": "MAINTENANCE", "completion_percent": 79.6875, "is_critical": false}, "cognitive_modeling": {"current": 54, "target": 64, "gap": 10, "allocated": 2, "priority_group": "MAINTENANCE", "completion_percent": 87.5, "is_critical": true}, "reasoning": {"current": 56, "target": 64, "gap": 8, "allocated": 2, "priority_group": "MAINTENANCE", "completion_percent": 90.625, "is_critical": false}, "machine_learning": {"current": 123, "target": 128, "gap": 5, "allocated": 2, "priority_group": "MAINTENANCE", "completion_percent": 97.65625, "is_critical": false}, "neural_architectures": {"current": 72, "target": 76, "gap": 4, "allocated": 2, "priority_group": "MAINTENANCE", "completion_percent": 97.36842105263158, "is_critical": false}}, "total_allocated": 258, "remaining_budget": 0, "budget_allocation": {"CRITICAL_HIGH": 103, "CRITICAL_MEDIUM": 64, "SECONDARY_HIGH": 51, "SECONDARY_MEDIUM": 25, "MAINTENANCE": 12}, "priority_groups": {"CRITICAL_HIGH": ["cognitive_maps", "semantic_networks", "knowledge_representation", "reasoning_patterns"], "CRITICAL_MEDIUM": [], "SECONDARY_HIGH": ["meta_programming"], "SECONDARY_MEDIUM": ["quantum_computing", "symbolic_ai", "logical_dynamics", "runtime_structures", "abstract_operators", "linguistic_mappings", "type_theory", "category_theory"], "MAINTENANCE": ["distributed_systems", "formal_verification", "cognitive_modeling", "reasoning", "machine_learning", "neural_architectures"]}}, "critical_secondary_domains": {"cognitive_modeling": {"priority": "HIGH", "reason": "Essential for symbolic reasoning and self-awareness", "subdomains": ["working_memory", "attention_control", "executive_functions", "metacognitive_monitoring", "cognitive_load", "mental_models", "schema_activation", "cognitive_flexibility", "inhibitory_control", "cognitive_biases", "heuristic_processing", "dual_process_theory"]}, "cognitive_maps": {"priority": "HIGH", "reason": "Critical for spatial and conceptual reasoning", "subdomains": ["spatial_navigation", "conceptual_mapping", "semantic_spaces", "topological_reasoning", "landmark_recognition", "path_planning", "cognitive_landmarks", "spatial_memory", "mental_rotation", "coordinate_systems", "reference_frames", "spatial_updating"]}, "semantic_networks": {"priority": "HIGH", "reason": "Foundation for knowledge representation and inference", "subdomains": ["concept_nodes", "semantic_relations", "spreading_activation", "semantic_priming", "conceptual_hierarchies", "associative_networks", "semantic_similarity", "concept_formation", "semantic_memory", "lexical_networks", "ontological_structures", "semantic_composition"]}, "knowledge_representation": {"priority": "MEDIUM", "reason": "Essential for symbolic AI and expert systems", "subdomains": ["frames", "scripts", "schemas", "semantic_frames", "conceptual_graphs", "description_logics", "rule_systems", "production_rules", "declarative_knowledge", "procedural_knowledge", "episodic_knowledge", "semantic_knowledge"]}, "reasoning_patterns": {"priority": "MEDIUM", "reason": "Core patterns for logical inference and problem solving", "subdomains": ["deductive_patterns", "inductive_patterns", "abductive_patterns", "analogical_patterns", "causal_patterns", "temporal_patterns", "spatial_patterns", "probabilistic_patterns", "fuzzy_patterns", "modal_patterns", "counterfactual_patterns", "diagnostic_patterns"]}}, "recommendations": {"immediate_actions": ["Prioritizzare domini CRITICAL_HIGH", "<PERSON>rare simboli per cognitive_modeling, cognitive_maps, semantic_networks", "Mantenere score ≥ 95.0 per tutti i nuovi simboli", "Validare fallback consistency ≤ 8 caratteri"], "completion_phases": ["Fase 1: Domini CRITICAL_HIGH (40% budget)", "Fase 2: Domini CRITICAL_MEDIUM (25% budget)", "Fase 3: Domini SECONDARY_HIGH (20% budget)", "Fase 4: Completamento finale (15% budget)"], "quality_assurance": ["Validazione LLM-friendly su batch da 20 simboli", "Test tokenizzazione con Qwen/DeepSeek", "Verifica embedding stability", "Misurazione compressione semantica"]}}