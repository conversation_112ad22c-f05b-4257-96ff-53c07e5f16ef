#!/usr/bin/env python3
"""
NEUROGLYPH - Duplicate Symbol Checker
=====================================

Verifica duplicati nel registry simboli e genera report dettagliato.
Controlla duplicati per: simboli Unicode, codici ng:, ID, unicode_point.

Usage: python check_duplicates.py --registry symbols_registry.json --fix
"""

import json
import sys
import argparse
from typing import Dict, Any, List, Set, Tuple
from pathlib import Path
from collections import defaultdict, Counter

class DuplicateChecker:
    """Verifica e risolve duplicati nel registry simboli."""
    
    def __init__(self, registry_path: str):
        self.registry_path = Path(registry_path)
        self.registry_data = self._load_registry()
        self.symbols = self.registry_data.get('approved_symbols', [])
        
    def _load_registry(self) -> Dict[str, Any]:
        """Carica registry simboli."""
        try:
            with open(self.registry_path, 'r', encoding='utf-8') as f:
                return json.load(f)
        except FileNotFoundError:
            raise FileNotFoundError(f"Registry non trovato: {self.registry_path}")
    
    def check_all_duplicates(self) -> Dict[str, Any]:
        """Verifica tutti i tipi di duplicati."""
        
        print("🔍 NEUROGLYPH - Duplicate Symbol Checker")
        print("=" * 50)
        print(f"📊 Analizzando {len(self.symbols)} simboli...")
        
        results = {
            "total_symbols": len(self.symbols),
            "symbol_duplicates": self._check_symbol_duplicates(),
            "code_duplicates": self._check_code_duplicates(),
            "id_duplicates": self._check_id_duplicates(),
            "unicode_duplicates": self._check_unicode_duplicates(),
            "summary": {}
        }
        
        # Calcola summary
        total_issues = (
            len(results["symbol_duplicates"]["duplicates"]) +
            len(results["code_duplicates"]["duplicates"]) +
            len(results["id_duplicates"]["duplicates"]) +
            len(results["unicode_duplicates"]["duplicates"])
        )
        
        results["summary"] = {
            "total_duplicate_issues": total_issues,
            "has_duplicates": total_issues > 0,
            "registry_health": "HEALTHY" if total_issues == 0 else "NEEDS_CLEANUP"
        }
        
        return results
    
    def _check_symbol_duplicates(self) -> Dict[str, Any]:
        """Verifica duplicati nei simboli Unicode."""
        symbol_counts = Counter(s.get('symbol', '') for s in self.symbols)
        duplicates = {symbol: count for symbol, count in symbol_counts.items() 
                     if count > 1 and symbol}
        
        duplicate_details = []
        for symbol, count in duplicates.items():
            entries = [s for s in self.symbols if s.get('symbol') == symbol]
            duplicate_details.append({
                "symbol": symbol,
                "count": count,
                "entries": [{"id": e.get('id'), "code": e.get('code')} for e in entries]
            })
        
        return {
            "type": "symbol_duplicates",
            "duplicates": duplicate_details,
            "count": len(duplicates),
            "total_affected": sum(duplicates.values()) if duplicates else 0
        }
    
    def _check_code_duplicates(self) -> Dict[str, Any]:
        """Verifica duplicati nei codici ng:."""
        code_counts = Counter(s.get('code', '') for s in self.symbols)
        duplicates = {code: count for code, count in code_counts.items() 
                     if count > 1 and code}
        
        duplicate_details = []
        for code, count in duplicates.items():
            entries = [s for s in self.symbols if s.get('code') == code]
            duplicate_details.append({
                "code": code,
                "count": count,
                "entries": [{"id": e.get('id'), "symbol": e.get('symbol')} for e in entries]
            })
        
        return {
            "type": "code_duplicates",
            "duplicates": duplicate_details,
            "count": len(duplicates),
            "total_affected": sum(duplicates.values()) if duplicates else 0
        }
    
    def _check_id_duplicates(self) -> Dict[str, Any]:
        """Verifica duplicati negli ID."""
        id_counts = Counter(s.get('id', '') for s in self.symbols)
        duplicates = {id_val: count for id_val, count in id_counts.items() 
                     if count > 1 and id_val}
        
        duplicate_details = []
        for id_val, count in duplicates.items():
            entries = [s for s in self.symbols if s.get('id') == id_val]
            duplicate_details.append({
                "id": id_val,
                "count": count,
                "entries": [{"symbol": e.get('symbol'), "code": e.get('code')} for e in entries]
            })
        
        return {
            "type": "id_duplicates",
            "duplicates": duplicate_details,
            "count": len(duplicates),
            "total_affected": sum(duplicates.values()) if duplicates else 0
        }
    
    def _check_unicode_duplicates(self) -> Dict[str, Any]:
        """Verifica duplicati nei unicode_point."""
        unicode_counts = Counter(s.get('unicode_point', '') for s in self.symbols)
        duplicates = {unicode_pt: count for unicode_pt, count in unicode_counts.items() 
                     if count > 1 and unicode_pt}
        
        duplicate_details = []
        for unicode_pt, count in duplicates.items():
            entries = [s for s in self.symbols if s.get('unicode_point') == unicode_pt]
            duplicate_details.append({
                "unicode_point": unicode_pt,
                "count": count,
                "entries": [{"id": e.get('id'), "symbol": e.get('symbol'), "code": e.get('code')} for e in entries]
            })
        
        return {
            "type": "unicode_duplicates",
            "duplicates": duplicate_details,
            "count": len(duplicates),
            "total_affected": sum(duplicates.values()) if duplicates else 0
        }
    
    def generate_report(self, results: Dict[str, Any]) -> str:
        """Genera report dettagliato dei duplicati."""
        
        report = f"""
🔍 NEUROGLYPH - Duplicate Analysis Report
=========================================

📊 OVERVIEW:
  • Total symbols: {results['total_symbols']}
  • Registry health: {results['summary']['registry_health']}
  • Total duplicate issues: {results['summary']['total_duplicate_issues']}

"""
        
        # Report per ogni tipo di duplicato
        for dup_type in ["symbol_duplicates", "code_duplicates", "id_duplicates", "unicode_duplicates"]:
            dup_data = results[dup_type]
            
            if dup_data["count"] > 0:
                report += f"❌ {dup_type.upper()}:\n"
                report += f"  • Found: {dup_data['count']} duplicate groups\n"
                report += f"  • Affected entries: {dup_data['total_affected']}\n"
                
                for dup in dup_data["duplicates"][:5]:  # Mostra primi 5
                    key = list(dup.keys())[0]  # symbol, code, id, o unicode_point
                    report += f"    - {key}: '{dup[key]}' ({dup['count']} occurrences)\n"
                    for entry in dup["entries"][:3]:  # Primi 3 entries
                        report += f"      → {entry}\n"
                
                if len(dup_data["duplicates"]) > 5:
                    report += f"    ... e altri {len(dup_data['duplicates']) - 5} gruppi\n"
                report += "\n"
            else:
                report += f"✅ {dup_type.upper()}: No duplicates found\n\n"
        
        # Raccomandazioni
        if results['summary']['total_duplicate_issues'] > 0:
            report += "💡 RACCOMANDAZIONI:\n"
            report += "  • Eseguire cleanup automatico con --fix\n"
            report += "  • Verificare integrità dopo cleanup\n"
            report += "  • Aggiornare pipeline di validazione\n"
        else:
            report += "🎉 REGISTRY CLEAN: Nessun duplicato trovato!\n"
        
        return report
    
    def fix_duplicates(self, results: Dict[str, Any]) -> Dict[str, Any]:
        """Risolve automaticamente i duplicati."""
        
        print("🔧 Fixing duplicates...")
        
        fixed_count = 0
        removed_symbols = []
        
        # Fix symbol duplicates - mantieni il primo, rimuovi gli altri
        for dup in results["symbol_duplicates"]["duplicates"]:
            symbol = dup["symbol"]
            entries = [s for s in self.symbols if s.get('symbol') == symbol]
            
            # Mantieni il primo (di solito quello con ID più basso)
            entries_sorted = sorted(entries, key=lambda x: x.get('id', ''))
            to_remove = entries_sorted[1:]  # Rimuovi tutti tranne il primo
            
            for entry in to_remove:
                if entry in self.symbols:
                    self.symbols.remove(entry)
                    removed_symbols.append(entry)
                    fixed_count += 1
        
        # Fix code duplicates
        for dup in results["code_duplicates"]["duplicates"]:
            code = dup["code"]
            entries = [s for s in self.symbols if s.get('code') == code]
            
            entries_sorted = sorted(entries, key=lambda x: x.get('id', ''))
            to_remove = entries_sorted[1:]
            
            for entry in to_remove:
                if entry in self.symbols:
                    self.symbols.remove(entry)
                    removed_symbols.append(entry)
                    fixed_count += 1
        
        # Fix ID duplicates - rigenera ID per i duplicati
        for dup in results["id_duplicates"]["duplicates"]:
            id_val = dup["id"]
            entries = [s for s in self.symbols if s.get('id') == id_val]
            
            # Mantieni il primo, rigenera ID per gli altri
            for i, entry in enumerate(entries[1:], 1):
                new_id = self._generate_new_id()
                entry['id'] = new_id
                fixed_count += 1
        
        # Aggiorna registry
        self.registry_data['approved_symbols'] = self.symbols
        
        return {
            "fixed_count": fixed_count,
            "removed_symbols": removed_symbols,
            "new_total": len(self.symbols)
        }
    
    def _generate_new_id(self) -> str:
        """Genera nuovo ID univoco."""
        existing_ids = {s.get('id', '') for s in self.symbols}
        
        # Trova il prossimo numero disponibile
        counter = 1
        while True:
            new_id = f"NG{counter:04d}"
            if new_id not in existing_ids:
                return new_id
            counter += 1
    
    def save_registry(self):
        """Salva registry aggiornato."""
        with open(self.registry_path, 'w', encoding='utf-8') as f:
            json.dump(self.registry_data, f, indent=2, ensure_ascii=False)

def main():
    """Verifica duplicati da command line."""
    parser = argparse.ArgumentParser(description="NEUROGLYPH Duplicate Symbol Checker")
    parser.add_argument("--registry", default="neuroglyph/core/symbols_registry.json",
                       help="Path al registry simboli")
    parser.add_argument("--fix", action="store_true",
                       help="Risolvi automaticamente i duplicati")
    parser.add_argument("--output", default="duplicate_analysis.json",
                       help="File output per analisi")
    parser.add_argument("--report", action="store_true",
                       help="Genera report dettagliato")
    
    args = parser.parse_args()
    
    # Verifica file registry
    if not Path(args.registry).exists():
        print(f"❌ Registry non trovato: {args.registry}")
        sys.exit(1)
    
    try:
        # Crea checker
        checker = DuplicateChecker(args.registry)
        
        # Esegui analisi
        results = checker.check_all_duplicates()
        
        # Salva risultati
        with open(args.output, 'w', encoding='utf-8') as f:
            json.dump(results, f, indent=2, ensure_ascii=False)
        
        # Report console
        print(f"\n📊 RISULTATI:")
        print(f"  • Simboli totali: {results['total_symbols']}")
        print(f"  • Health status: {results['summary']['registry_health']}")
        print(f"  • Duplicate issues: {results['summary']['total_duplicate_issues']}")
        
        # Dettagli duplicati
        for dup_type in ["symbol_duplicates", "code_duplicates", "id_duplicates", "unicode_duplicates"]:
            dup_data = results[dup_type]
            status = "❌" if dup_data["count"] > 0 else "✅"
            print(f"  {status} {dup_type}: {dup_data['count']} gruppi")
        
        # Fix se richiesto
        if args.fix and results['summary']['total_duplicate_issues'] > 0:
            print(f"\n🔧 FIXING DUPLICATES...")
            fix_results = checker.fix_duplicates(results)
            checker.save_registry()
            
            print(f"✅ Fixed {fix_results['fixed_count']} duplicate entries")
            print(f"📊 New total: {fix_results['new_total']} symbols")
            print(f"💾 Registry updated: {args.registry}")
        
        # Report dettagliato
        if args.report:
            report = checker.generate_report(results)
            print(report)
            
            # Salva report
            report_file = f"duplicate_report_{results['total_symbols']}_symbols.txt"
            with open(report_file, 'w', encoding='utf-8') as f:
                f.write(report)
            print(f"📄 Report salvato: {report_file}")
        
        print(f"\n📄 Analisi completa salvata: {args.output}")
        
        # Exit code
        sys.exit(0 if results['summary']['total_duplicate_issues'] == 0 else 1)
        
    except Exception as e:
        print(f"❌ Errore durante analisi: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
