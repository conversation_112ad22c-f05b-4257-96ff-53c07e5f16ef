id,symbol,code,fallback,category,name,validation_score,severity,low_score,unicode_risky,long_fallback,generic_name,suggested_fallback,suggested_name,unicode_range,whitelisted,issues
NG0001,⊕,ng:operator:add,[+],,,100.0,OK,False,False,False,False,,,unknown,False,
NG0002,⊖,ng:operator:subtract,[-],,,100.0,OK,False,False,False,False,,,unknown,False,
NG0003,⊗,ng:operator:multiply,[*],,,100.0,OK,False,False,False,False,,,unknown,False,
NG0004,⊘,ng:operator:divide,[/],,,100.0,OK,False,False,False,False,,,unknown,False,
NG0005,≡,ng:logic:equals,[==],,,100.0,OK,False,False,False,False,,,unknown,False,
NG0006,≢,ng:logic:not_equals,[!=],,,100.0,OK,False,False,False,False,,,unknown,False,
NG0007,∧,ng:logic:and,[AND],,,100.0,OK,False,False,False,False,,,unknown,False,
NG0008,∨,ng:logic:or,[OR],,,100.0,OK,False,False,False,False,,,unknown,False,
NG0009,¬,ng:logic:not,[NOT],,,100.0,OK,False,False,False,False,,,unknown,False,
NG0010,∈,ng:logic:in,[IN],,,100.0,OK,False,False,False,False,,,unknown,False,
NG0011,∉,ng:logic:not_in,[NOTIN],,,100.0,OK,False,False,False,False,,,unknown,False,
NG0012,⊨,ng:reasoning:entails,[ENTAILS],,,100.0,OK,False,False,False,False,,,unknown,False,
NG0013,⇌,ng:logic:biconditional,[IFF],,,100.0,OK,False,False,False,False,,,unknown,False,
NG0014,⟨⟩,ng:structure:function,[FN],,,100.0,OK,False,False,False,False,,,unknown,False,
NG0015,⟪⟫,ng:structure:class,[CLS],,,100.0,OK,False,False,False,False,,,unknown,False,
NG0016,◊,ng:flow:if,[IF],,,100.0,OK,False,False,False,False,,,unknown,False,
NG0017,◈,ng:flow:else,[ELSE],,,100.0,OK,False,False,False,False,,,unknown,False,
NG0018,⟲,ng:flow:for,[FOR],,,100.0,OK,False,False,False,False,,,unknown,False,
NG0019,⟳,ng:flow:while,[WHILE],,,100.0,OK,False,False,False,False,,,unknown,False,
NG0020,⤴,ng:flow:return,[RETURN],,,100.0,OK,False,False,False,False,,,unknown,False,
NG0021,◯,ng:operator:sub,[SUB],operator,sub,95.0,OK,False,False,False,False,,,safe,False,
NG0022,■,ng:memory:pointer,[POINTER],memory,pointer,95.0,OK,False,False,False,False,,,safe,False,
NG0023,☎,ng:memory:alloc,[ALLOC],memory,alloc,95.0,B,False,True,False,False,,,miscellaneous_symbols,False,Risky Unicode range: miscellaneous_symbols
NG0024,≺,ng:logic:implies,[IMPLIES],logic,implies,95.0,OK,False,False,False,False,,,safe,False,
NG0025,☬,ng:logic:or_1,[OR1],logic,or_1,95.0,A,False,True,False,True,,or_bitwise,miscellaneous_symbols,False,Risky Unicode range: miscellaneous_symbols; Generic numbered name
NG0026,⛑,ng:memory:alloc_1,[ALLOC1],memory,alloc_1,95.0,A,False,True,False,True,,alloc_heap,miscellaneous_symbols,False,Risky Unicode range: miscellaneous_symbols; Generic numbered name
NG0027,◟,ng:memory:free,[FREE],memory,free,95.0,OK,False,False,False,False,,,safe,False,
NG0028,⊬,ng:structure:function_1,[FUNCTION1],structure,function_1,95.0,B,False,False,True,True,[FUNCTI..],function_1,safe,False,Long fallback: 11 chars; Generic numbered name
NG0029,≯,ng:operator:mul,[MUL],operator,mul,95.0,OK,False,False,False,False,,,safe,False,
NG0030,↢,ng:operator:mod,[MOD],operator,mod,95.0,OK,False,False,False,False,,,safe,False,
NG0031,◴,ng:operator:add_1,[ADD1],operator,add_1,95.0,C,False,False,False,True,,add_scalar,safe,False,Generic numbered name
NG0032,□,ng:memory:deref,[DEREF],memory,deref,95.0,OK,False,False,False,False,,,safe,False,
NG0033,⇵,ng:operator:add_2,[ADD2],operator,add_2,95.0,C,False,False,False,True,,add_vector,safe,False,Generic numbered name
NG0034,⇣,ng:structure:property,[PROPERTY],structure,property,95.0,OK,False,False,False,False,,,safe,False,
NG0035,◡,ng:flow:return_1,[RETURN1],flow,return_1,95.0,C,False,False,False,True,,return_1,safe,False,Generic numbered name
NG0036,◼,ng:memory:alloc_2,[ALLOC2],memory,alloc_2,95.0,C,False,False,False,True,,alloc_stack,safe,False,Generic numbered name
NG0037,⊁,ng:flow:for_1,[FOR1],flow,for_1,95.0,C,False,False,False,True,,for_range,safe,False,Generic numbered name
NG0038,⋲,ng:operator:add_3,[ADD3],operator,add_3,95.0,C,False,False,False,True,,add_matrix,safe,False,Generic numbered name
NG0039,≙,ng:flow:if_1,[IF1],flow,if_1,95.0,C,False,False,False,True,,if_conditional,safe,False,Generic numbered name
NG0040,⋂,ng:operator:mul_1,[MUL1],operator,mul_1,95.0,C,False,False,False,True,,mul_scalar,safe,False,Generic numbered name
NG0041,♄,ng:logic:and_1,[AND1],logic,and_1,95.0,A,False,True,False,True,,and_bitwise,miscellaneous_symbols,False,Risky Unicode range: miscellaneous_symbols; Generic numbered name
NG0042,↤,ng:structure:function_2,[FUNCTION2],structure,function_2,95.0,B,False,False,True,True,[FUNCTI..],function_2,safe,False,Long fallback: 11 chars; Generic numbered name
NG0043,↰,ng:structure:property_1,[PROPERTY1],structure,property_1,95.0,B,False,False,True,True,[PROPER..],property_1,safe,False,Long fallback: 11 chars; Generic numbered name
NG0044,⚌,ng:memory:pointer_1,[POINTER1],memory,pointer_1,95.0,A,False,True,False,True,,pointer_1,miscellaneous_symbols,False,Risky Unicode range: miscellaneous_symbols; Generic numbered name
NG0045,↘,ng:flow:break,[BREAK],flow,break,95.0,OK,False,False,False,False,,,safe,False,
NG0046,◗,ng:logic:not_1,[NOT1],logic,not_1,95.0,C,False,False,False,True,,not_bitwise,safe,False,Generic numbered name
NG0047,☤,ng:logic:implies_1,[IMPLIES1],logic,implies_1,95.0,A,False,True,False,True,,implies_material,miscellaneous_symbols,False,Risky Unicode range: miscellaneous_symbols; Generic numbered name
NG0048,⚓,ng:flow:break_1,[BREAK1],flow,break_1,95.0,A,False,True,False,True,,break_1,miscellaneous_symbols,False,Risky Unicode range: miscellaneous_symbols; Generic numbered name
NG0049,◜,ng:operator:div,[DIV],operator,div,95.0,OK,False,False,False,False,,,safe,False,
NG0050,◿,ng:operator:add_4,[ADD4],operator,add_4,95.0,C,False,False,False,True,,add_complex,safe,False,Generic numbered name
NG0051,∢,ng:flow:if_2,[IF2],flow,if_2,95.0,C,False,False,False,True,,if_ternary,safe,False,Generic numbered name
NG0052,◇,ng:logic:or_2,[OR2],logic,or_2,95.0,C,False,False,False,True,,or_logical,safe,False,Generic numbered name
NG0053,☴,ng:memory:deref_1,[DEREF1],memory,deref_1,95.0,A,False,True,False,True,,deref_weak,miscellaneous_symbols,False,Risky Unicode range: miscellaneous_symbols; Generic numbered name
NG0054,◆,ng:structure:class_1,[CLASS1],structure,class_1,95.0,C,False,False,False,True,,class_1,safe,False,Generic numbered name
NG0055,←,ng:operator:div_1,[DIV1],operator,div_1,95.0,C,False,False,False,True,,div_scalar,safe,False,Generic numbered name
NG0056,◧,ng:memory:pointer_2,[POINTER2],memory,pointer_2,95.0,C,False,False,False,True,,pointer_2,safe,False,Generic numbered name
NG0057,⇃,ng:flow:break_2,[BREAK2],flow,break_2,95.0,C,False,False,False,True,,break_2,safe,False,Generic numbered name
NG0058,♑,ng:memory:deref_2,[DEREF2],memory,deref_2,95.0,A,False,True,False,True,,deref_strong,miscellaneous_symbols,False,Risky Unicode range: miscellaneous_symbols; Generic numbered name
NG0059,⇗,ng:operator:mod_1,[MOD1],operator,mod_1,95.0,C,False,False,False,True,,mod_integer,safe,False,Generic numbered name
NG0060,⊠,ng:logic:implies_2,[IMPLIES2],logic,implies_2,95.0,C,False,False,False,True,,implies_strict,safe,False,Generic numbered name
NG0061,⚁,ng:flow:for_2,[FOR2],flow,for_2,95.0,A,False,True,False,True,,for_iterator,miscellaneous_symbols,False,Risky Unicode range: miscellaneous_symbols; Generic numbered name
NG0062,⋉,ng:logic:and_2,[AND2],logic,and_2,95.0,C,False,False,False,True,,and_logical,safe,False,Generic numbered name
NG0063,⋨,ng:logic:implies_3,[IMPLIES3],logic,implies_3,95.0,C,False,False,False,True,,implies_relevant,safe,False,Generic numbered name
NG0064,⚮,ng:structure:property_2,[PROPERTY2],structure,property_2,95.0,B,False,False,True,True,[PROPER..],property_2,miscellaneous_symbols,True,Long fallback: 11 chars; Generic numbered name
NG0065,⇻,ng:operator:mod_2,[MOD2],operator,mod_2,95.0,C,False,False,False,True,,mod_polynomial,safe,False,Generic numbered name
NG0066,≲,ng:logic:and_3,[AND3],logic,and_3,95.0,C,False,False,False,True,,and_fuzzy,safe,False,Generic numbered name
NG0067,▤,ng:operator:pow,[POW],operator,pow,95.0,OK,False,False,False,False,,,safe,False,
NG0068,⊏,ng:structure:method,[METHOD],structure,method,95.0,OK,False,False,False,False,,,safe,False,
NG0069,∅,ng:memory:pointer_3,[POINTER3],memory,pointer_3,95.0,C,False,False,False,True,,pointer_3,safe,False,Generic numbered name
NG0070,◂,ng:logic:not_2,[NOT2],logic,not_2,95.0,C,False,False,False,True,,not_logical,safe,False,Generic numbered name
NG0071,◱,ng:operator:mod_3,[MOD3],operator,mod_3,95.0,C,False,False,False,True,,mod_matrix,safe,False,Generic numbered name
NG0072,⇈,ng:flow:break_3,[BREAK3],flow,break_3,95.0,C,False,False,False,True,,break_3,safe,False,Generic numbered name
NG0073,⚳,ng:flow:while_1,[WHILE1],flow,while_1,95.0,C,False,False,False,True,,while_condition,miscellaneous_symbols,True,Generic numbered name
NG0074,↨,ng:flow:if_3,[IF3],flow,if_3,95.0,C,False,False,False,True,,if_switch,safe,False,Generic numbered name
NG0075,♃,ng:operator:mod_4,[MOD4],operator,mod_4,95.0,A,False,True,False,True,,mod_group,miscellaneous_symbols,False,Risky Unicode range: miscellaneous_symbols; Generic numbered name
NG0076,△,ng:operator:div_2,[DIV2],operator,div_2,95.0,C,False,False,False,True,,div_vector,safe,False,Generic numbered name
NG0077,⛉,ng:operator:div_3,[DIV3],operator,div_3,95.0,A,False,True,False,True,,div_matrix,miscellaneous_symbols,False,Risky Unicode range: miscellaneous_symbols; Generic numbered name
NG0078,♒,ng:structure:property_3,[PROPERTY3],structure,property_3,95.0,A,False,True,True,True,[PROPER..],property_3,miscellaneous_symbols,False,Risky Unicode range: miscellaneous_symbols; Long fallback: 11 chars; Generic numbered name
NG0079,⇦,ng:structure:property_4,[PROPERTY4],structure,property_4,95.0,B,False,False,True,True,[PROPER..],property_4,safe,False,Long fallback: 11 chars; Generic numbered name
NG0080,⚼,ng:flow:for_3,[FOR3],flow,for_3,95.0,C,False,False,False,True,,for_parallel,miscellaneous_symbols,True,Generic numbered name
NG0081,◢,ng:memory:deref_3,[DEREF3],memory,deref_3,95.0,C,False,False,False,True,,deref_atomic,safe,False,Generic numbered name
NG0082,◸,ng:memory:pointer_4,[POINTER4],memory,pointer_4,95.0,C,False,False,False,True,,pointer_4,safe,False,Generic numbered name
NG0083,◨,ng:operator:add_5,[ADD5],operator,add_5,95.0,C,False,False,False,True,,add_modular,safe,False,Generic numbered name
NG0084,⇆,ng:logic:and_4,[AND4],logic,and_4,95.0,C,False,False,False,True,,and_quantum,safe,False,Generic numbered name
NG0085,⊃,ng:flow:while_2,[WHILE2],flow,while_2,95.0,C,False,False,False,True,,while_infinite,safe,False,Generic numbered name
NG0086,◄,ng:flow:while_3,[WHILE3],flow,while_3,95.0,C,False,False,False,True,,while_bounded,safe,False,Generic numbered name
NG0087,⇟,ng:structure:function_3,[FUNCTION3],structure,function_3,95.0,B,False,False,True,True,[FUNCTI..],function_3,safe,False,Long fallback: 11 chars; Generic numbered name
NG0088,∗,ng:operator:mul_2,[MUL2],operator,mul_2,95.0,C,False,False,False,True,,mul_vector,safe,False,Generic numbered name
NG0089,∿,ng:logic:implies_4,[IMPLIES4],logic,implies_4,95.0,C,False,False,False,True,,implies_modal,safe,False,Generic numbered name
NG0090,▩,ng:logic:xor,[XOR],logic,xor,95.0,OK,False,False,False,False,,,safe,False,
NG0091,♶,ng:flow:else_1,[ELSE1],flow,else_1,95.0,A,False,True,False,True,,else_1,miscellaneous_symbols,False,Risky Unicode range: miscellaneous_symbols; Generic numbered name
NG0092,♋,ng:structure:method_1,[METHOD1],structure,method_1,95.0,A,False,True,False,True,,method_1,miscellaneous_symbols,False,Risky Unicode range: miscellaneous_symbols; Generic numbered name
NG0093,☏,ng:structure:property_5,[PROPERTY5],structure,property_5,95.0,A,False,True,True,True,[PROPER..],property_5,miscellaneous_symbols,False,Risky Unicode range: miscellaneous_symbols; Long fallback: 11 chars; Generic numbered name
NG0094,⊊,ng:flow:else_2,[ELSE2],flow,else_2,95.0,C,False,False,False,True,,else_2,safe,False,Generic numbered name
NG0095,∱,ng:memory:alloc_3,[ALLOC3],memory,alloc_3,95.0,C,False,False,False,True,,alloc_pool,safe,False,Generic numbered name
NG0096,⚆,ng:operator:sub_1,[SUB1],operator,sub_1,95.0,A,False,True,False,True,,sub_scalar,miscellaneous_symbols,False,Risky Unicode range: miscellaneous_symbols; Generic numbered name
NG0097,∀,ng:operator:div_4,[DIV4],operator,div_4,95.0,C,False,False,False,True,,div_integer,safe,False,Generic numbered name
NG0098,☘,ng:memory:deref_4,[DEREF4],memory,deref_4,95.0,A,False,True,False,True,,deref_volatile,miscellaneous_symbols,False,Risky Unicode range: miscellaneous_symbols; Generic numbered name
NG0099,♬,ng:structure:class_2,[CLASS2],structure,class_2,95.0,A,False,True,False,True,,class_2,miscellaneous_symbols,False,Risky Unicode range: miscellaneous_symbols; Generic numbered name
NG0100,⋁,ng:memory:pointer_5,[POINTER5],memory,pointer_5,95.0,C,False,False,False,True,,pointer_5,safe,False,Generic numbered name
NG0101,⋳,ng:flow:while_4,[WHILE4],flow,while_4,95.0,C,False,False,False,True,,while_monitored,safe,False,Generic numbered name
NG0102,◔,ng:flow:if_4,[IF4],flow,if_4,95.0,C,False,False,False,True,,if_guard,safe,False,Generic numbered name
NG0103,⇑,ng:memory:alloc_4,[ALLOC4],memory,alloc_4,95.0,C,False,False,False,True,,alloc_aligned,safe,False,Generic numbered name
NG0104,⋝,ng:logic:or_3,[OR3],logic,or_3,95.0,C,False,False,False,True,,or_fuzzy,safe,False,Generic numbered name
NG0105,⊉,ng:flow:while_5,[WHILE5],flow,while_5,95.0,C,False,False,False,True,,while_async,safe,False,Generic numbered name
NG0106,♨,ng:flow:while_6,[WHILE6],flow,while_6,95.0,A,False,True,False,True,,while_6,miscellaneous_symbols,False,Risky Unicode range: miscellaneous_symbols; Generic numbered name
NG0107,⇅,ng:structure:function_4,[FUNCTION4],structure,function_4,95.0,B,False,False,True,True,[FUNCTI..],function_4,safe,False,Long fallback: 11 chars; Generic numbered name
NG0108,∯,ng:logic:implies_5,[IMPLIES5],logic,implies_5,95.0,C,False,False,False,True,,implies_quantum,safe,False,Generic numbered name
NG0109,▰,ng:logic:or_4,[OR4],logic,or_4,95.0,C,False,False,False,True,,or_quantum,safe,False,Generic numbered name
NG0110,◒,ng:memory:pointer_6,[POINTER6],memory,pointer_6,95.0,C,False,False,False,True,,pointer_6,safe,False,Generic numbered name
NG0111,⇎,ng:logic:not_3,[NOT3],logic,not_3,95.0,C,False,False,False,True,,not_fuzzy,safe,False,Generic numbered name
NG0112,≹,ng:memory:deref_5,[DEREF5],memory,deref_5,95.0,C,False,False,False,True,,deref_const,safe,False,Generic numbered name
NG0113,≃,ng:logic:and_5,[AND5],logic,and_5,95.0,C,False,False,False,True,,and_modal,safe,False,Generic numbered name
NG0114,↹,ng:structure:property_6,[PROPERTY6],structure,property_6,95.0,B,False,False,True,True,[PROPER..],property_6,safe,False,Long fallback: 11 chars; Generic numbered name
NG0115,⛧,ng:structure:method_2,[METHOD2],structure,method_2,95.0,A,False,True,False,True,,method_2,miscellaneous_symbols,False,Risky Unicode range: miscellaneous_symbols; Generic numbered name
NG0116,☯,ng:memory:free_1,[FREE1],memory,free_1,95.0,A,False,True,False,True,,free_heap,miscellaneous_symbols,False,Risky Unicode range: miscellaneous_symbols; Generic numbered name
NG0117,≁,ng:logic:implies_6,[IMPLIES6],logic,implies_6,95.0,C,False,False,False,True,,implies_6,safe,False,Generic numbered name
NG0118,⇷,ng:operator:sub_2,[SUB2],operator,sub_2,95.0,C,False,False,False,True,,sub_vector,safe,False,Generic numbered name
NG0119,▲,ng:structure:method_3,[METHOD3],structure,method_3,95.0,C,False,False,False,True,,method_3,safe,False,Generic numbered name
NG0120,◹,ng:logic:implies_7,[IMPLIES7],logic,implies_7,95.0,C,False,False,False,True,,implies_7,safe,False,Generic numbered name
NG0121,↥,ng:operator:pow_1,[POW1],operator,pow_1,95.0,C,False,False,False,True,,pow_1,safe,False,Generic numbered name
NG0122,♉,ng:logic:not_4,[NOT4],logic,not_4,95.0,A,False,True,False,True,,not_quantum,miscellaneous_symbols,False,Risky Unicode range: miscellaneous_symbols; Generic numbered name
NG0123,▫,ng:structure:property_7,[PROPERTY7],structure,property_7,95.0,B,False,False,True,True,[PROPER..],property_7,safe,False,Long fallback: 11 chars; Generic numbered name
NG0124,⚒,ng:structure:class_3,[CLASS3],structure,class_3,95.0,A,False,True,False,True,,class_3,miscellaneous_symbols,False,Risky Unicode range: miscellaneous_symbols; Generic numbered name
NG0125,◰,ng:logic:implies_8,[IMPLIES8],logic,implies_8,95.0,C,False,False,False,True,,implies_8,safe,False,Generic numbered name
NG0126,◵,ng:memory:pointer_7,[POINTER7],memory,pointer_7,95.0,C,False,False,False,True,,pointer_7,safe,False,Generic numbered name
NG0127,☙,ng:flow:if_5,[IF5],flow,if_5,95.0,A,False,True,False,True,,if_pattern,miscellaneous_symbols,False,Risky Unicode range: miscellaneous_symbols; Generic numbered name
NG0128,⇪,ng:logic:implies_9,[IMPLIES9],logic,implies_9,95.0,C,False,False,False,True,,implies_9,safe,False,Generic numbered name
NG0129,∰,ng:structure:function_5,[FUNCTION5],structure,function_5,95.0,B,False,False,True,True,[FUNCTI..],function_5,safe,False,Long fallback: 11 chars; Generic numbered name
NG0130,♊,ng:flow:for_4,[FOR4],flow,for_4,95.0,A,False,True,False,True,,for_vectorized,miscellaneous_symbols,False,Risky Unicode range: miscellaneous_symbols; Generic numbered name
NG0131,↭,ng:operator:pow_2,[POW2],operator,pow_2,95.0,C,False,False,False,True,,pow_2,safe,False,Generic numbered name
NG0132,⋋,ng:logic:and_6,[AND6],logic,and_6,95.0,C,False,False,False,True,,and_6,safe,False,Generic numbered name
NG0133,⇴,ng:memory:deref_6,[DEREF6],memory,deref_6,95.0,C,False,False,False,True,,deref_6,safe,False,Generic numbered name
NG0134,∺,ng:logic:xor_1,[XOR1],logic,xor_1,95.0,C,False,False,False,True,,xor_1,safe,False,Generic numbered name
NG0135,⇒,ng:logic:not_5,[NOT5],logic,not_5,95.0,C,False,False,False,True,,not_modal,safe,False,Generic numbered name
NG0136,◬,ng:operator:sub_3,[SUB3],operator,sub_3,95.0,C,False,False,False,True,,sub_matrix,safe,False,Generic numbered name
NG0137,⇿,ng:flow:if_6,[IF6],flow,if_6,95.0,C,False,False,False,True,,if_6,safe,False,Generic numbered name
NG0138,∣,ng:operator:mul_3,[MUL3],operator,mul_3,95.0,C,False,False,False,True,,mul_matrix,safe,False,Generic numbered name
NG0139,☕,ng:flow:while_7,[WHILE7],flow,while_7,95.0,A,False,True,False,True,,while_7,miscellaneous_symbols,False,Risky Unicode range: miscellaneous_symbols; Generic numbered name
NG0140,▽,ng:operator:pow_3,[POW3],operator,pow_3,95.0,C,False,False,False,True,,pow_3,safe,False,Generic numbered name
NG0141,⋊,ng:structure:function_6,[FUNCTION6],structure,function_6,95.0,B,False,False,True,True,[FUNCTI..],function_6,safe,False,Long fallback: 11 chars; Generic numbered name
NG0142,⋺,ng:flow:break_4,[BREAK4],flow,break_4,95.0,C,False,False,False,True,,break_4,safe,False,Generic numbered name
NG0143,◪,ng:flow:return_2,[RETURN2],flow,return_2,95.0,C,False,False,False,True,,return_2,safe,False,Generic numbered name
NG0144,⋓,ng:memory:alloc_5,[ALLOC5],memory,alloc_5,95.0,C,False,False,False,True,,alloc_shared,safe,False,Generic numbered name
NG0145,≫,ng:memory:free_2,[FREE2],memory,free_2,95.0,C,False,False,False,True,,free_stack,safe,False,Generic numbered name
NG0146,⇰,ng:flow:if_7,[IF7],flow,if_7,95.0,C,False,False,False,True,,if_7,safe,False,Generic numbered name
NG0147,⊞,ng:flow:else_3,[ELSE3],flow,else_3,95.0,C,False,False,False,True,,else_3,safe,False,Generic numbered name
NG0148,▥,ng:memory:free_3,[FREE3],memory,free_3,95.0,C,False,False,False,True,,free_pool,safe,False,Generic numbered name
NG0149,↪,ng:memory:ref,[REF],memory,ref,95.0,OK,False,False,False,False,,,safe,False,
NG0150,↷,ng:memory:deref_7,[DEREF7],memory,deref_7,95.0,C,False,False,False,True,,deref_7,safe,False,Generic numbered name
NG0151,⚰,ng:flow:if_8,[IF8],flow,if_8,95.0,C,False,False,False,True,,if_8,miscellaneous_symbols,True,Generic numbered name
NG0152,⋩,ng:memory:ref_1,[REF1],memory,ref_1,95.0,C,False,False,False,True,,ref_1,safe,False,Generic numbered name
NG0153,⇕,ng:flow:for_5,[FOR5],flow,for_5,95.0,C,False,False,False,True,,for_unrolled,safe,False,Generic numbered name
NG0154,◞,ng:memory:pointer_8,[POINTER8],memory,pointer_8,95.0,C,False,False,False,True,,pointer_8,safe,False,Generic numbered name
NG0155,◕,ng:flow:else_4,[ELSE4],flow,else_4,95.0,C,False,False,False,True,,else_4,safe,False,Generic numbered name
NG0156,⋟,ng:memory:ref_2,[REF2],memory,ref_2,95.0,C,False,False,False,True,,ref_2,safe,False,Generic numbered name
NG0157,◮,ng:memory:pointer_9,[POINTER9],memory,pointer_9,95.0,C,False,False,False,True,,pointer_9,safe,False,Generic numbered name
NG0158,↽,ng:structure:class_4,[CLASS4],structure,class_4,95.0,C,False,False,False,True,,class_4,safe,False,Generic numbered name
NG0159,♅,ng:operator:mod_5,[MOD5],operator,mod_5,95.0,A,False,True,False,True,,mod_ring,miscellaneous_symbols,False,Risky Unicode range: miscellaneous_symbols; Generic numbered name
NG0160,♽,ng:flow:if_9,[IF9],flow,if_9,95.0,A,False,True,False,True,,if_9,miscellaneous_symbols,False,Risky Unicode range: miscellaneous_symbols; Generic numbered name
NG0161,⛢,ng:flow:if_10,[IF10],flow,if_10,95.0,A,False,True,False,True,,if_10,miscellaneous_symbols,False,Risky Unicode range: miscellaneous_symbols; Generic numbered name
NG0162,⚢,ng:structure:method_4,[METHOD4],structure,method_4,95.0,C,False,False,False,True,,method_4,miscellaneous_symbols,True,Generic numbered name
NG0163,≑,ng:flow:while_8,[WHILE8],flow,while_8,95.0,C,False,False,False,True,,while_8,safe,False,Generic numbered name
NG0164,⋘,ng:structure:class_5,[CLASS5],structure,class_5,95.0,C,False,False,False,True,,class_5,safe,False,Generic numbered name
NG0165,◃,ng:flow:return_3,[RETURN3],flow,return_3,95.0,C,False,False,False,True,,return_3,safe,False,Generic numbered name
NG0166,⇂,ng:logic:not_6,[NOT6],logic,not_6,95.0,C,False,False,False,True,,not_6,safe,False,Generic numbered name
NG0167,⚑,ng:memory:deref_8,[DEREF8],memory,deref_8,95.0,A,False,True,False,True,,deref_8,miscellaneous_symbols,False,Risky Unicode range: miscellaneous_symbols; Generic numbered name
NG0168,⇠,ng:flow:while_9,[WHILE9],flow,while_9,95.0,C,False,False,False,True,,while_9,safe,False,Generic numbered name
NG0169,♷,ng:structure:class_6,[CLASS6],structure,class_6,95.0,A,False,True,False,True,,class_6,miscellaneous_symbols,False,Risky Unicode range: miscellaneous_symbols; Generic numbered name
NG0170,⚈,ng:structure:function_7,[FUNCTION7],structure,function_7,95.0,A,False,True,True,True,[FUNCTI..],function_7,miscellaneous_symbols,False,Risky Unicode range: miscellaneous_symbols; Long fallback: 11 chars; Generic numbered name
NG0171,↧,ng:operator:sub_4,[SUB4],operator,sub_4,95.0,C,False,False,False,True,,sub_complex,safe,False,Generic numbered name
NG0172,↮,ng:logic:and_7,[AND7],logic,and_7,95.0,C,False,False,False,True,,and_7,safe,False,Generic numbered name
NG0173,⊀,ng:structure:function_8,[FUNCTION8],structure,function_8,95.0,B,False,False,True,True,[FUNCTI..],function_8,safe,False,Long fallback: 11 chars; Generic numbered name
NG0174,◦,ng:flow:return_4,[RETURN4],flow,return_4,95.0,C,False,False,False,True,,return_4,safe,False,Generic numbered name
NG0175,⋛,ng:logic:iff,[IFF],logic,iff,95.0,OK,False,False,False,False,,,safe,False,
NG0176,◲,ng:logic:or_5,[OR5],logic,or_5,95.0,C,False,False,False,True,,or_modal,safe,False,Generic numbered name
NG0177,≚,ng:flow:for_6,[FOR6],flow,for_6,95.0,C,False,False,False,True,,for_6,safe,False,Generic numbered name
NG0178,↩,ng:memory:pointer_10,[POINTER10],memory,pointer_10,95.0,B,False,False,True,True,[POINTE..],pointer_10,safe,False,Long fallback: 11 chars; Generic numbered name
NG0179,↗,ng:flow:while_10,[WHILE10],flow,while_10,95.0,C,False,False,False,True,,while_10,safe,False,Generic numbered name
NG0180,⚘,ng:logic:implies_10,[IMPLIES10],logic,implies_10,95.0,A,False,True,True,True,[IMPLIE..],implies_10,miscellaneous_symbols,False,Risky Unicode range: miscellaneous_symbols; Long fallback: 11 chars; Generic numbered name
NG0181,☛,ng:structure:method_5,[METHOD5],structure,method_5,95.0,A,False,True,False,True,,method_5,miscellaneous_symbols,False,Risky Unicode range: miscellaneous_symbols; Generic numbered name
NG0182,⊳,ng:memory:free_4,[FREE4],memory,free_4,95.0,C,False,False,False,True,,free_batch,safe,False,Generic numbered name
NG0183,◳,ng:flow:if_11,[IF11],flow,if_11,95.0,C,False,False,False,True,,if_11,safe,False,Generic numbered name
NG0184,◙,ng:memory:deref_9,[DEREF9],memory,deref_9,95.0,C,False,False,False,True,,deref_9,safe,False,Generic numbered name
NG0185,⛡,ng:structure:property_8,[PROPERTY8],structure,property_8,95.0,A,False,True,True,True,[PROPER..],property_8,miscellaneous_symbols,False,Risky Unicode range: miscellaneous_symbols; Long fallback: 11 chars; Generic numbered name
NG0186,◐,ng:logic:implies_11,[IMPLIES11],logic,implies_11,95.0,B,False,False,True,True,[IMPLIE..],implies_11,safe,False,Long fallback: 11 chars; Generic numbered name
NG0187,⛽,ng:flow:while_11,[WHILE11],flow,while_11,95.0,A,False,True,False,True,,while_11,miscellaneous_symbols,False,Risky Unicode range: miscellaneous_symbols; Generic numbered name
NG0188,⇱,ng:operator:div_5,[DIV5],operator,div_5,95.0,C,False,False,False,True,,div_modular,safe,False,Generic numbered name
NG0189,∼,ng:logic:not_7,[NOT7],logic,not_7,95.0,C,False,False,False,True,,not_7,safe,False,Generic numbered name
NG0190,≬,ng:memory:ref_3,[REF3],memory,ref_3,95.0,C,False,False,False,True,,ref_3,safe,False,Generic numbered name
NG0191,▸,ng:memory:deref_10,[DEREF10],memory,deref_10,95.0,C,False,False,False,True,,deref_10,safe,False,Generic numbered name
NG0192,≏,ng:structure:property_9,[PROPERTY9],structure,property_9,95.0,B,False,False,True,True,[PROPER..],property_9,safe,False,Long fallback: 11 chars; Generic numbered name
NG0193,⋆,ng:operator:div_6,[DIV6],operator,div_6,95.0,C,False,False,False,True,,div_6,safe,False,Generic numbered name
NG0194,⋴,ng:logic:not_8,[NOT8],logic,not_8,95.0,C,False,False,False,True,,not_8,safe,False,Generic numbered name
NG0195,⇚,ng:structure:property_10,[PROPERTY10],structure,property_10,95.0,B,False,False,True,True,[PROPER..],property_10,safe,False,Long fallback: 12 chars; Generic numbered name
NG0196,⇲,ng:memory:alloc_6,[ALLOC6],memory,alloc_6,95.0,C,False,False,False,True,,alloc_6,safe,False,Generic numbered name
NG0197,▮,ng:operator:div_7,[DIV7],operator,div_7,95.0,C,False,False,False,True,,div_7,safe,False,Generic numbered name
NG0198,≗,ng:memory:alloc_7,[ALLOC7],memory,alloc_7,95.0,C,False,False,False,True,,alloc_7,safe,False,Generic numbered name
NG0199,↾,ng:structure:class_7,[CLASS7],structure,class_7,95.0,C,False,False,False,True,,class_7,safe,False,Generic numbered name
NG0200,◛,ng:flow:for_7,[FOR7],flow,for_7,95.0,C,False,False,False,True,,for_7,safe,False,Generic numbered name
NG0201,∶,ng:structure:function_9,[FUNCTION9],structure,function_9,95.0,B,False,False,True,True,[FUNCTI..],function_9,safe,False,Long fallback: 11 chars; Generic numbered name
NG0202,≝,ng:operator:sub_5,[SUB5],operator,sub_5,95.0,C,False,False,False,True,,sub_modular,safe,False,Generic numbered name
NG0203,⛁,ng:memory:alloc_8,[ALLOC8],memory,alloc_8,95.0,A,False,True,False,True,,alloc_8,miscellaneous_symbols,False,Risky Unicode range: miscellaneous_symbols; Generic numbered name
NG0204,≾,ng:flow:for_8,[FOR8],flow,for_8,95.0,C,False,False,False,True,,for_8,safe,False,Generic numbered name
NG0205,♫,ng:operator:sub_6,[SUB6],operator,sub_6,95.0,A,False,True,False,True,,sub_6,miscellaneous_symbols,False,Risky Unicode range: miscellaneous_symbols; Generic numbered name
NG0206,⊙,ng:structure:method_6,[METHOD6],structure,method_6,95.0,C,False,False,False,True,,method_6,safe,False,Generic numbered name
NG0207,≽,ng:operator:sub_7,[SUB7],operator,sub_7,95.0,C,False,False,False,True,,sub_7,safe,False,Generic numbered name
NG0208,⇢,ng:memory:pointer_11,[POINTER11],memory,pointer_11,95.0,B,False,False,True,True,[POINTE..],pointer_11,safe,False,Long fallback: 11 chars; Generic numbered name
NG0209,↦,ng:operator:pow_4,[POW4],operator,pow_4,95.0,C,False,False,False,True,,pow_4,safe,False,Generic numbered name
NG0210,∂,ng:flow:break_5,[BREAK5],flow,break_5,95.0,C,False,False,False,True,,break_5,safe,False,Generic numbered name
NG0211,⇋,ng:operator:add_6,[ADD6],operator,add_6,95.0,C,False,False,False,True,,add_6,safe,False,Generic numbered name
NG0212,◚,ng:structure:function_10,[FUNCTION10],structure,function_10,95.0,B,False,False,True,True,[FUNCTI..],function_10,safe,False,Long fallback: 12 chars; Generic numbered name
NG0213,⇖,ng:memory:free_5,[FREE5],memory,free_5,95.0,C,False,False,False,True,,free_deferred,safe,False,Generic numbered name
NG0214,☸,ng:flow:if_12,[IF12],flow,if_12,95.0,A,False,True,False,True,,if_12,miscellaneous_symbols,False,Risky Unicode range: miscellaneous_symbols; Generic numbered name
NG0215,⛕,ng:logic:or_6,[OR6],logic,or_6,95.0,A,False,True,False,True,,or_6,miscellaneous_symbols,False,Risky Unicode range: miscellaneous_symbols; Generic numbered name
NG0216,↖,ng:logic:implies_12,[IMPLIES12],logic,implies_12,95.0,B,False,False,True,True,[IMPLIE..],implies_12,safe,False,Long fallback: 11 chars; Generic numbered name
NG0217,⋎,ng:structure:property_11,[PROPERTY11],structure,property_11,95.0,B,False,False,True,True,[PROPER..],property_11,safe,False,Long fallback: 12 chars; Generic numbered name
NG0218,⛨,ng:logic:iff_1,[IFF1],logic,iff_1,95.0,A,False,True,False,True,,iff_1,miscellaneous_symbols,False,Risky Unicode range: miscellaneous_symbols; Generic numbered name
NG0219,⇙,ng:flow:return_5,[RETURN5],flow,return_5,95.0,C,False,False,False,True,,return_5,safe,False,Generic numbered name
NG0220,▧,ng:structure:function_11,[FUNCTION11],structure,function_11,95.0,B,False,False,True,True,[FUNCTI..],function_11,safe,False,Long fallback: 12 chars; Generic numbered name
NG0221,⚉,ng:flow:while_12,[WHILE12],flow,while_12,95.0,A,False,True,False,True,,while_12,miscellaneous_symbols,False,Risky Unicode range: miscellaneous_symbols; Generic numbered name
NG0222,⇁,ng:logic:and_8,[AND8],logic,and_8,95.0,C,False,False,False,True,,and_8,safe,False,Generic numbered name
NG0223,◽,ng:logic:implies_13,[IMPLIES13],logic,implies_13,95.0,B,False,False,True,True,[IMPLIE..],implies_13,safe,False,Long fallback: 11 chars; Generic numbered name
NG0224,▼,ng:memory:deref_11,[DEREF11],memory,deref_11,95.0,C,False,False,False,True,,deref_11,safe,False,Generic numbered name
NG0225,↿,ng:logic:xor_2,[XOR2],logic,xor_2,95.0,C,False,False,False,True,,xor_2,safe,False,Generic numbered name
NG0226,▿,ng:structure:function_12,[FUNCTION12],structure,function_12,95.0,B,False,False,True,True,[FUNCTI..],function_12,safe,False,Long fallback: 12 chars; Generic numbered name
NG0227,☑,ng:operator:mul_4,[MUL4],operator,mul_4,95.0,A,False,True,False,True,,mul_cross,miscellaneous_symbols,False,Risky Unicode range: miscellaneous_symbols; Generic numbered name
NG0228,☿,ng:structure:property_12,[PROPERTY12],structure,property_12,95.0,A,False,True,True,True,[PROPER..],property_12,miscellaneous_symbols,False,Risky Unicode range: miscellaneous_symbols; Long fallback: 12 chars; Generic numbered name
NG0229,♮,ng:operator:mod_6,[MOD6],operator,mod_6,95.0,A,False,True,False,True,,mod_6,miscellaneous_symbols,False,Risky Unicode range: miscellaneous_symbols; Generic numbered name
NG0230,⊹,ng:structure:method_7,[METHOD7],structure,method_7,95.0,C,False,False,False,True,,method_7,safe,False,Generic numbered name
NG0231,⇽,ng:logic:xor_3,[XOR3],logic,xor_3,95.0,C,False,False,False,True,,xor_3,safe,False,Generic numbered name
NG0232,⇤,ng:flow:return_6,[RETURN6],flow,return_6,95.0,C,False,False,False,True,,return_6,safe,False,Generic numbered name
NG0233,☧,ng:operator:mul_5,[MUL5],operator,mul_5,95.0,A,False,True,False,True,,mul_dot,miscellaneous_symbols,False,Risky Unicode range: miscellaneous_symbols; Generic numbered name
NG0234,☮,ng:logic:iff_2,[IFF2],logic,iff_2,95.0,A,False,True,False,True,,iff_2,miscellaneous_symbols,False,Risky Unicode range: miscellaneous_symbols; Generic numbered name
NG0235,⚋,ng:logic:and_9,[AND9],logic,and_9,95.0,A,False,True,False,True,,and_9,miscellaneous_symbols,False,Risky Unicode range: miscellaneous_symbols; Generic numbered name
NG0236,⇀,ng:operator:pow_5,[POW5],operator,pow_5,95.0,C,False,False,False,True,,pow_5,safe,False,Generic numbered name
NG0237,∡,ng:memory:alloc_9,[ALLOC9],memory,alloc_9,95.0,C,False,False,False,True,,alloc_9,safe,False,Generic numbered name
NG0238,⛣,ng:operator:mod_7,[MOD7],operator,mod_7,95.0,A,False,True,False,True,,mod_7,miscellaneous_symbols,False,Risky Unicode range: miscellaneous_symbols; Generic numbered name
NG0239,∷,ng:flow:if_13,[IF13],flow,if_13,95.0,C,False,False,False,True,,if_13,safe,False,Generic numbered name
NG0240,⚗,ng:operator:sub_8,[SUB8],operator,sub_8,95.0,C,False,False,False,True,,sub_8,miscellaneous_symbols,True,Generic numbered name
NG0241,⊧,ng:flow:else_5,[ELSE5],flow,else_5,95.0,C,False,False,False,True,,else_5,safe,False,Generic numbered name
NG0242,⋀,ng:memory:ref_4,[REF4],memory,ref_4,95.0,C,False,False,False,True,,ref_4,safe,False,Generic numbered name
NG0243,↜,ng:operator:pow_6,[POW6],operator,pow_6,95.0,C,False,False,False,True,,pow_6,safe,False,Generic numbered name
NG0244,≎,ng:operator:pow_7,[POW7],operator,pow_7,95.0,C,False,False,False,True,,pow_7,safe,False,Generic numbered name
NG0245,▦,ng:logic:and_10,[AND10],logic,and_10,95.0,C,False,False,False,True,,and_10,safe,False,Generic numbered name
NG0246,◑,ng:structure:property_13,[PROPERTY13],structure,property_13,95.0,B,False,False,True,True,[PROPER..],property_13,safe,False,Long fallback: 12 chars; Generic numbered name
NG0247,−,ng:logic:xor_4,[XOR4],logic,xor_4,95.0,C,False,False,False,True,,xor_4,safe,False,Generic numbered name
NG0248,◘,ng:flow:return_7,[RETURN7],flow,return_7,95.0,C,False,False,False,True,,return_7,safe,False,Generic numbered name
NG0249,⋜,ng:memory:pointer_12,[POINTER12],memory,pointer_12,95.0,B,False,False,True,True,[POINTE..],pointer_12,safe,False,Long fallback: 11 chars; Generic numbered name
NG0250,∋,ng:structure:function_13,[FUNCTION13],structure,function_13,95.0,B,False,False,True,True,[FUNCTI..],function_13,safe,False,Long fallback: 12 chars; Generic numbered name
NG0251,≔,ng:operator:pow_8,[POW8],operator,pow_8,95.0,C,False,False,False,True,,pow_8,safe,False,Generic numbered name
NG0252,↝,ng:logic:implies_14,[IMPLIES14],logic,implies_14,95.0,B,False,False,True,True,[IMPLIE..],implies_14,safe,False,Long fallback: 11 chars; Generic numbered name
NG0253,⛐,ng:memory:alloc_10,[ALLOC10],memory,alloc_10,95.0,A,False,True,False,True,,alloc_10,miscellaneous_symbols,False,Risky Unicode range: miscellaneous_symbols; Generic numbered name
NG0254,⊈,ng:operator:pow_9,[POW9],operator,pow_9,95.0,C,False,False,False,True,,pow_9,safe,False,Generic numbered name
NG0255,◶,ng:operator:pow_10,[POW10],operator,pow_10,95.0,C,False,False,False,True,,pow_10,safe,False,Generic numbered name
NG0256,◻,ng:logic:and_11,[AND11],logic,and_11,95.0,C,False,False,False,True,,and_11,safe,False,Generic numbered name
NG0257,⊯,ng:logic:xor_5,[XOR5],logic,xor_5,95.0,C,False,False,False,True,,xor_5,safe,False,Generic numbered name
NG0258,☝,ng:logic:xor_6,[XOR6],logic,xor_6,95.0,A,False,True,False,True,,xor_6,miscellaneous_symbols,False,Risky Unicode range: miscellaneous_symbols; Generic numbered name
NG0259,↑,ng:memory:free_6,[FREE6],memory,free_6,95.0,C,False,False,False,True,,free_6,safe,False,Generic numbered name
NG0260,⇘,ng:logic:and_12,[AND12],logic,and_12,95.0,C,False,False,False,True,,and_12,safe,False,Generic numbered name
NG0261,⊿,ng:logic:not_9,[NOT9],logic,not_9,95.0,C,False,False,False,True,,not_9,safe,False,Generic numbered name
NG0262,↛,ng:operator:mul_6,[MUL6],operator,mul_6,95.0,C,False,False,False,True,,mul_6,safe,False,Generic numbered name
NG0263,⋞,ng:logic:not_10,[NOT10],logic,not_10,95.0,C,False,False,False,True,,not_10,safe,False,Generic numbered name
NG0264,⚂,ng:operator:mul_7,[MUL7],operator,mul_7,95.0,A,False,True,False,True,,mul_7,miscellaneous_symbols,False,Risky Unicode range: miscellaneous_symbols; Generic numbered name
NG0265,↠,ng:flow:while_13,[WHILE13],flow,while_13,95.0,C,False,False,False,True,,while_13,safe,False,Generic numbered name
NG0266,≐,ng:memory:ref_5,[REF5],memory,ref_5,95.0,C,False,False,False,True,,ref_5,safe,False,Generic numbered name
NG0267,≥,ng:logic:iff_3,[IFF3],logic,iff_3,95.0,C,False,False,False,True,,iff_3,safe,False,Generic numbered name
NG0268,≄,ng:structure:property_14,[PROPERTY14],structure,property_14,95.0,B,False,False,True,True,[PROPER..],property_14,safe,False,Long fallback: 12 chars; Generic numbered name
NG0269,↲,ng:logic:not_11,[NOT11],logic,not_11,95.0,C,False,False,False,True,,not_11,safe,False,Generic numbered name
NG0270,☻,ng:logic:or_7,[OR7],logic,or_7,95.0,A,False,True,False,True,,or_7,miscellaneous_symbols,False,Risky Unicode range: miscellaneous_symbols; Generic numbered name
NG0271,⇄,ng:operator:sub_9,[SUB9],operator,sub_9,95.0,C,False,False,False,True,,sub_9,safe,False,Generic numbered name
NG0272,◓,ng:logic:and_13,[AND13],logic,and_13,95.0,C,False,False,False,True,,and_13,safe,False,Generic numbered name
NG0273,↯,ng:logic:or_8,[OR8],logic,or_8,95.0,C,False,False,False,True,,or_8,safe,False,Generic numbered name
NG0274,⇇,ng:logic:not_12,[NOT12],logic,not_12,95.0,C,False,False,False,True,,not_12,safe,False,Generic numbered name
NG0275,♙,ng:logic:or_9,[OR9],logic,or_9,95.0,A,False,True,False,True,,or_9,miscellaneous_symbols,False,Risky Unicode range: miscellaneous_symbols; Generic numbered name
NG0276,∓,ng:memory:free_7,[FREE7],memory,free_7,95.0,C,False,False,False,True,,free_7,safe,False,Generic numbered name
NG0277,≿,ng:logic:not_13,[NOT13],logic,not_13,95.0,C,False,False,False,True,,not_13,safe,False,Generic numbered name
NG0278,⇝,ng:structure:property_15,[PROPERTY15],structure,property_15,95.0,B,False,False,True,True,[PROPER..],property_15,safe,False,Long fallback: 12 chars; Generic numbered name
NG0279,▨,ng:operator:mod_8,[MOD8],operator,mod_8,95.0,C,False,False,False,True,,mod_8,safe,False,Generic numbered name
NG0280,≓,ng:flow:return_8,[RETURN8],flow,return_8,95.0,C,False,False,False,True,,return_8,safe,False,Generic numbered name
NG0281,⋯,ng:structure:class_8,[CLASS8],structure,class_8,95.0,C,False,False,False,True,,class_8,safe,False,Generic numbered name
NG0282,↔,ng:operator:mod_9,[MOD9],operator,mod_9,95.0,C,False,False,False,True,,mod_9,safe,False,Generic numbered name
NG0283,◍,ng:structure:function_14,[FUNCTION14],structure,function_14,95.0,B,False,False,True,True,[FUNCTI..],function_14,safe,False,Long fallback: 12 chars; Generic numbered name
NG0284,∇,ng:logic:iff_4,[IFF4],logic,iff_4,95.0,C,False,False,False,True,,iff_4,safe,False,Generic numbered name
NG0285,⇳,ng:memory:free_8,[FREE8],memory,free_8,95.0,C,False,False,False,True,,free_8,safe,False,Generic numbered name
NG0286,∠,ng:logic:or_10,[OR10],logic,or_10,95.0,C,False,False,False,True,,or_10,safe,False,Generic numbered name
NG0287,↡,ng:operator:div_8,[DIV8],operator,div_8,95.0,C,False,False,False,True,,div_8,safe,False,Generic numbered name
NG0288,⋾,ng:operator:div_9,[DIV9],operator,div_9,95.0,C,False,False,False,True,,div_9,safe,False,Generic numbered name
NG0289,⊻,ng:structure:function_15,[FUNCTION15],structure,function_15,95.0,B,False,False,True,True,[FUNCTI..],function_15,safe,False,Long fallback: 12 chars; Generic numbered name
NG0290,⇏,ng:flow:if_14,[IF14],flow,if_14,95.0,C,False,False,False,True,,if_14,safe,False,Generic numbered name
NG0291,☊,ng:flow:for_9,[FOR9],flow,for_9,95.0,A,False,True,False,True,,for_9,miscellaneous_symbols,False,Risky Unicode range: miscellaneous_symbols; Generic numbered name
NG0292,⇓,ng:flow:for_10,[FOR10],flow,for_10,95.0,C,False,False,False,True,,for_10,safe,False,Generic numbered name
NG0293,☶,ng:operator:sub_10,[SUB10],operator,sub_10,95.0,A,False,True,False,True,,sub_10,miscellaneous_symbols,False,Risky Unicode range: miscellaneous_symbols; Generic numbered name
NG0294,⇶,ng:structure:class_9,[CLASS9],structure,class_9,95.0,C,False,False,False,True,,class_9,safe,False,Generic numbered name
NG0295,☦,ng:operator:div_10,[DIV10],operator,div_10,95.0,A,False,True,False,True,,div_10,miscellaneous_symbols,False,Risky Unicode range: miscellaneous_symbols; Generic numbered name
NG0296,⊋,ng:flow:for_11,[FOR11],flow,for_11,95.0,C,False,False,False,True,,for_11,safe,False,Generic numbered name
NG0297,⇬,ng:flow:return_9,[RETURN9],flow,return_9,95.0,C,False,False,False,True,,return_9,safe,False,Generic numbered name
NG0298,▻,ng:operator:add_7,[ADD7],operator,add_7,95.0,C,False,False,False,True,,add_7,safe,False,Generic numbered name
NG0299,⚥,ng:flow:while_14,[WHILE14],flow,while_14,95.0,C,False,False,False,True,,while_14,miscellaneous_symbols,True,Generic numbered name
NG0300,→,ng:logic:implies_15,[IMPLIES15],logic,implies_15,95.0,B,False,False,True,True,[IMPLIE..],implies_15,safe,False,Long fallback: 11 chars; Generic numbered name
NG0301,⇩,ng:memory:free_9,[FREE9],memory,free_9,95.0,C,False,False,False,True,,free_9,safe,False,Generic numbered name
NG0302,♭,ng:memory:alloc_11,[ALLOC11],memory,alloc_11,95.0,A,False,True,False,True,,alloc_11,miscellaneous_symbols,False,Risky Unicode range: miscellaneous_symbols; Generic numbered name
NG0303,∤,ng:operator:mod_10,[MOD10],operator,mod_10,95.0,C,False,False,False,True,,mod_10,safe,False,Generic numbered name
NG0304,⇼,ng:flow:while_15,[WHILE15],flow,while_15,95.0,C,False,False,False,True,,while_15,safe,False,Generic numbered name
NG0305,◅,ng:logic:xor_7,[XOR7],logic,xor_7,95.0,C,False,False,False,True,,xor_7,safe,False,Generic numbered name
NG0306,♥,ng:memory:alloc_12,[ALLOC12],memory,alloc_12,95.0,A,False,True,False,True,,alloc_12,miscellaneous_symbols,False,Risky Unicode range: miscellaneous_symbols; Generic numbered name
NG0307,⇸,ng:logic:not_14,[NOT14],logic,not_14,95.0,C,False,False,False,True,,not_14,safe,False,Generic numbered name
NG0308,⚛,ng:flow:for_12,[FOR12],flow,for_12,95.0,C,False,False,False,True,,for_12,miscellaneous_symbols,True,Generic numbered name
NG0309,◺,ng:flow:for_13,[FOR13],flow,for_13,95.0,C,False,False,False,True,,for_13,safe,False,Generic numbered name
NG0310,↶,ng:structure:property_16,[PROPERTY16],structure,property_16,95.0,B,False,False,True,True,[PROPER..],property_16,safe,False,Long fallback: 12 chars; Generic numbered name
NG0311,≠,ng:logic:not_15,[NOT15],logic,not_15,95.0,C,False,False,False,True,,not_15,safe,False,Generic numbered name
NG0312,◥,ng:memory:deref_12,[DEREF12],memory,deref_12,95.0,C,False,False,False,True,,deref_12,safe,False,Generic numbered name
NG0313,⛍,ng:structure:function_16,[FUNCTION16],structure,function_16,95.0,A,False,True,True,True,[FUNCTI..],function_16,miscellaneous_symbols,False,Risky Unicode range: miscellaneous_symbols; Long fallback: 12 chars; Generic numbered name
NG0314,⇺,ng:structure:class_10,[CLASS10],structure,class_10,95.0,C,False,False,False,True,,class_10,safe,False,Generic numbered name
NG0315,☽,ng:flow:else_6,[ELSE6],flow,else_6,95.0,A,False,True,False,True,,else_6,miscellaneous_symbols,False,Risky Unicode range: miscellaneous_symbols; Generic numbered name
NG0316,⊦,ng:logic:or_11,[OR11],logic,or_11,95.0,C,False,False,False,True,,or_11,safe,False,Generic numbered name
NG0317,↻,ng:memory:ref_6,[REF6],memory,ref_6,95.0,C,False,False,False,True,,ref_6,safe,False,Generic numbered name
NG0318,⋈,ng:structure:property_17,[PROPERTY17],structure,property_17,95.0,B,False,False,True,True,[PROPER..],property_17,safe,False,Long fallback: 12 chars; Generic numbered name
NG0319,⊾,ng:structure:property_18,[PROPERTY18],structure,property_18,95.0,B,False,False,True,True,[PROPER..],property_18,safe,False,Long fallback: 12 chars; Generic numbered name
NG0320,☀,ng:logic:iff_5,[IFF5],logic,iff_5,95.0,A,False,True,False,True,,iff_5,miscellaneous_symbols,False,Risky Unicode range: miscellaneous_symbols; Generic numbered name
NG0321,↸,ng:flow:else_7,[ELSE7],flow,else_7,95.0,C,False,False,False,True,,else_7,safe,False,Generic numbered name
NG0322,⊽,ng:logic:iff_6,[IFF6],logic,iff_6,95.0,C,False,False,False,True,,iff_6,safe,False,Generic numbered name
NG0323,♡,ng:memory:free_10,[FREE10],memory,free_10,95.0,A,False,True,False,True,,free_10,miscellaneous_symbols,False,Risky Unicode range: miscellaneous_symbols; Generic numbered name
NG0324,☈,ng:operator:mul_8,[MUL8],operator,mul_8,95.0,A,False,True,False,True,,mul_8,miscellaneous_symbols,False,Risky Unicode range: miscellaneous_symbols; Generic numbered name
NG0325,∑,ng:structure:function_17,[FUNCTION17],structure,function_17,95.0,B,False,False,True,True,[FUNCTI..],function_17,safe,False,Long fallback: 12 chars; Generic numbered name
NG0326,♧,ng:logic:and_14,[AND14],logic,and_14,95.0,A,False,True,False,True,,and_14,miscellaneous_symbols,False,Risky Unicode range: miscellaneous_symbols; Generic numbered name
NG0327,⋮,ng:flow:while_16,[WHILE16],flow,while_16,95.0,C,False,False,False,True,,while_16,safe,False,Generic numbered name
NG0328,∩,ng:logic:iff_7,[IFF7],logic,iff_7,95.0,C,False,False,False,True,,iff_7,safe,False,Generic numbered name
NG0329,⛘,ng:operator:div_11,[DIV11],operator,div_11,95.0,A,False,True,False,True,,div_11,miscellaneous_symbols,False,Risky Unicode range: miscellaneous_symbols; Generic numbered name
NG0330,☡,ng:flow:for_14,[FOR14],flow,for_14,95.0,A,False,True,False,True,,for_14,miscellaneous_symbols,False,Risky Unicode range: miscellaneous_symbols; Generic numbered name
NG0331,⊆,ng:structure:function_18,[FUNCTION18],structure,function_18,95.0,B,False,False,True,True,[FUNCTI..],function_18,safe,False,Long fallback: 12 chars; Generic numbered name
NG0332,∹,ng:logic:or_12,[OR12],logic,or_12,95.0,C,False,False,False,True,,or_12,safe,False,Generic numbered name
NG0333,⛯,ng:flow:if_15,[IF15],flow,if_15,95.0,A,False,True,False,True,,if_15,miscellaneous_symbols,False,Risky Unicode range: miscellaneous_symbols; Generic numbered name
NG0334,∊,ng:operator:div_12,[DIV12],operator,div_12,95.0,C,False,False,False,True,,div_12,safe,False,Generic numbered name
NG0335,⇉,ng:memory:deref_13,[DEREF13],memory,deref_13,95.0,C,False,False,False,True,,deref_13,safe,False,Generic numbered name
NG0336,⋄,ng:flow:return_10,[RETURN10],flow,return_10,95.0,C,False,False,False,True,,return_10,safe,False,Generic numbered name
NG0337,⚅,ng:logic:iff_8,[IFF8],logic,iff_8,95.0,A,False,True,False,True,,iff_8,miscellaneous_symbols,False,Risky Unicode range: miscellaneous_symbols; Generic numbered name
NG0338,♿,ng:flow:for_15,[FOR15],flow,for_15,95.0,A,False,True,False,True,,for_15,miscellaneous_symbols,False,Risky Unicode range: miscellaneous_symbols; Generic numbered name
NG0339,⊛,ng:flow:break_6,[BREAK6],flow,break_6,95.0,C,False,False,False,True,,break_6,safe,False,Generic numbered name
NG0340,⚖,ng:logic:xor_8,[XOR8],logic,xor_8,95.0,C,False,False,False,True,,xor_8,miscellaneous_symbols,True,Generic numbered name
NG0341,≱,ng:flow:return_11,[RETURN11],flow,return_11,95.0,C,False,False,False,True,,return_11,safe,False,Generic numbered name
NG0342,◤,ng:memory:alloc_13,[ALLOC13],memory,alloc_13,95.0,C,False,False,False,True,,alloc_13,safe,False,Generic numbered name
NG0343,⋇,ng:flow:return_12,[RETURN12],flow,return_12,95.0,C,False,False,False,True,,return_12,safe,False,Generic numbered name
NG0344,▹,ng:operator:add_8,[ADD8],operator,add_8,95.0,C,False,False,False,True,,add_8,safe,False,Generic numbered name
NG0345,◠,ng:operator:mul_9,[MUL9],operator,mul_9,95.0,C,False,False,False,True,,mul_9,safe,False,Generic numbered name
NG0346,☵,ng:logic:implies_16,[IMPLIES16],logic,implies_16,95.0,A,False,True,True,True,[IMPLIE..],implies_16,miscellaneous_symbols,False,Risky Unicode range: miscellaneous_symbols; Long fallback: 11 chars; Generic numbered name
NG0347,◀,ng:flow:while_17,[WHILE17],flow,while_17,95.0,C,False,False,False,True,,while_17,safe,False,Generic numbered name
NG0348,↳,ng:operator:div_13,[DIV13],operator,div_13,95.0,C,False,False,False,True,,div_13,safe,False,Generic numbered name
NG0349,∾,ng:logic:xor_9,[XOR9],logic,xor_9,95.0,C,False,False,False,True,,xor_9,safe,False,Generic numbered name
NG0350,⛟,ng:flow:if_16,[IF16],flow,if_16,95.0,A,False,True,False,True,,if_16,miscellaneous_symbols,False,Risky Unicode range: miscellaneous_symbols; Generic numbered name
NG0351,↕,ng:operator:mod_11,[MOD11],operator,mod_11,95.0,C,False,False,False,True,,mod_11,safe,False,Generic numbered name
NG0352,⛂,ng:logic:implies_17,[IMPLIES17],logic,implies_17,95.0,A,False,True,True,True,[IMPLIE..],implies_17,miscellaneous_symbols,False,Risky Unicode range: miscellaneous_symbols; Long fallback: 11 chars; Generic numbered name
NG0353,⛇,ng:memory:deref_14,[DEREF14],memory,deref_14,95.0,A,False,True,False,True,,deref_14,miscellaneous_symbols,False,Risky Unicode range: miscellaneous_symbols; Generic numbered name
NG0354,⛺,ng:logic:implies_18,[IMPLIES18],logic,implies_18,95.0,A,False,True,True,True,[IMPLIE..],implies_18,miscellaneous_symbols,False,Risky Unicode range: miscellaneous_symbols; Long fallback: 11 chars; Generic numbered name
NG0355,☢,ng:flow:for_16,[FOR16],flow,for_16,95.0,A,False,True,False,True,,for_16,miscellaneous_symbols,False,Risky Unicode range: miscellaneous_symbols; Generic numbered name
NG0356,⊅,ng:memory:alloc_14,[ALLOC14],memory,alloc_14,95.0,C,False,False,False,True,,alloc_14,safe,False,Generic numbered name
NG0357,⊒,ng:logic:or_13,[OR13],logic,or_13,95.0,C,False,False,False,True,,or_13,safe,False,Generic numbered name
NG0358,♏,ng:memory:free_11,[FREE11],memory,free_11,95.0,A,False,True,False,True,,free_11,miscellaneous_symbols,False,Risky Unicode range: miscellaneous_symbols; Generic numbered name
NG0359,⋔,ng:logic:not_16,[NOT16],logic,not_16,95.0,C,False,False,False,True,,not_16,safe,False,Generic numbered name
NG0360,▾,ng:memory:alloc_15,[ALLOC15],memory,alloc_15,95.0,C,False,False,False,True,,alloc_15,safe,False,Generic numbered name
NG0361,∐,ng:logic:implies_19,[IMPLIES19],logic,implies_19,95.0,B,False,False,True,True,[IMPLIE..],implies_19,safe,False,Long fallback: 11 chars; Generic numbered name
NG0362,⋿,ng:structure:class_11,[CLASS11],structure,class_11,95.0,C,False,False,False,True,,class_11,safe,False,Generic numbered name
NG0363,⛲,ng:operator:div_14,[DIV14],operator,div_14,95.0,A,False,True,False,True,,div_14,miscellaneous_symbols,False,Risky Unicode range: miscellaneous_symbols; Generic numbered name
NG0364,⚀,ng:logic:xor_10,[XOR10],logic,xor_10,95.0,A,False,True,False,True,,xor_10,miscellaneous_symbols,False,Risky Unicode range: miscellaneous_symbols; Generic numbered name
NG0365,⛊,ng:structure:class_12,[CLASS12],structure,class_12,95.0,A,False,True,False,True,,class_12,miscellaneous_symbols,False,Risky Unicode range: miscellaneous_symbols; Generic numbered name
NG0366,☥,ng:logic:iff_9,[IFF9],logic,iff_9,95.0,A,False,True,False,True,,iff_9,miscellaneous_symbols,False,Risky Unicode range: miscellaneous_symbols; Generic numbered name
NG0367,◭,ng:flow:else_8,[ELSE8],flow,else_8,95.0,C,False,False,False,True,,else_8,safe,False,Generic numbered name
NG0368,◝,ng:memory:pointer_13,[POINTER13],memory,pointer_13,95.0,B,False,False,True,True,[POINTE..],pointer_13,safe,False,Long fallback: 11 chars; Generic numbered name
NG0369,☩,ng:flow:break_7,[BREAK7],flow,break_7,95.0,A,False,True,False,True,,break_7,miscellaneous_symbols,False,Risky Unicode range: miscellaneous_symbols; Generic numbered name
NG0370,♁,ng:structure:class_13,[CLASS13],structure,class_13,95.0,A,False,True,False,True,,class_13,miscellaneous_symbols,False,Risky Unicode range: miscellaneous_symbols; Generic numbered name
NG0371,⛋,ng:operator:sub_11,[SUB11],operator,sub_11,95.0,A,False,True,False,True,,sub_11,miscellaneous_symbols,False,Risky Unicode range: miscellaneous_symbols; Generic numbered name
NG0372,⋱,ng:flow:return_13,[RETURN13],flow,return_13,95.0,C,False,False,False,True,,return_13,safe,False,Generic numbered name
NG0373,↫,ng:structure:class_14,[CLASS14],structure,class_14,95.0,C,False,False,False,True,,class_14,safe,False,Generic numbered name
NG0374,☰,ng:logic:or_14,[OR14],logic,or_14,95.0,A,False,True,False,True,,or_14,miscellaneous_symbols,False,Risky Unicode range: miscellaneous_symbols; Generic numbered name
NG0375,⊱,ng:memory:ref_7,[REF7],memory,ref_7,95.0,C,False,False,False,True,,ref_7,safe,False,Generic numbered name
NG0376,♻,ng:operator:sub_12,[SUB12],operator,sub_12,95.0,A,False,True,False,True,,sub_12,miscellaneous_symbols,False,Risky Unicode range: miscellaneous_symbols; Generic numbered name
NG0377,⋹,ng:flow:break_8,[BREAK8],flow,break_8,95.0,C,False,False,False,True,,break_8,safe,False,Generic numbered name
NG0378,⊎,ng:memory:free_12,[FREE12],memory,free_12,95.0,C,False,False,False,True,,free_12,safe,False,Generic numbered name
NG0379,⊝,ng:logic:implies_20,[IMPLIES20],logic,implies_20,95.0,B,False,False,True,True,[IMPLIE..],implies_20,safe,False,Long fallback: 11 chars; Generic numbered name
NG0380,▵,ng:memory:ref_8,[REF8],memory,ref_8,95.0,C,False,False,False,True,,ref_8,safe,False,Generic numbered name
NG0381,≉,ng:memory:ref_9,[REF9],memory,ref_9,95.0,C,False,False,False,True,,ref_9,safe,False,Generic numbered name
NG0382,⛭,ng:memory:alloc_16,[ALLOC16],memory,alloc_16,95.0,A,False,True,False,True,,alloc_16,miscellaneous_symbols,False,Risky Unicode range: miscellaneous_symbols; Generic numbered name
NG0383,∎,ng:structure:method_8,[METHOD8],structure,method_8,95.0,C,False,False,False,True,,method_8,safe,False,Generic numbered name
NG0384,∪,ng:structure:class_15,[CLASS15],structure,class_15,95.0,C,False,False,False,True,,class_15,safe,False,Generic numbered name
NG0385,⚯,ng:structure:method_9,[METHOD9],structure,method_9,95.0,C,False,False,False,True,,method_9,miscellaneous_symbols,True,Generic numbered name
NG0386,⛱,ng:operator:pow_11,[POW11],operator,pow_11,95.0,A,False,True,False,True,,pow_11,miscellaneous_symbols,False,Risky Unicode range: miscellaneous_symbols; Generic numbered name
NG0387,⊷,ng:structure:function_19,[FUNCTION19],structure,function_19,95.0,B,False,False,True,True,[FUNCTI..],function_19,safe,False,Long fallback: 12 chars; Generic numbered name
NG0388,♛,ng:memory:alloc_17,[ALLOC17],memory,alloc_17,95.0,A,False,True,False,True,,alloc_17,miscellaneous_symbols,False,Risky Unicode range: miscellaneous_symbols; Generic numbered name
NG0389,⚫,ng:memory:free_13,[FREE13],memory,free_13,95.0,C,False,False,False,True,,free_13,miscellaneous_symbols,True,Generic numbered name
NG0390,∖,ng:memory:ref_10,[REF10],memory,ref_10,95.0,C,False,False,False,True,,ref_10,safe,False,Generic numbered name
NG0391,⚻,ng:structure:method_10,[METHOD10],structure,method_10,95.0,C,False,False,False,True,,method_10,miscellaneous_symbols,True,Generic numbered name
NG0392,⋙,ng:memory:alloc_18,[ALLOC18],memory,alloc_18,95.0,C,False,False,False,True,,alloc_18,safe,False,Generic numbered name
NG0393,☟,ng:flow:if_17,[IF17],flow,if_17,95.0,A,False,True,False,True,,if_17,miscellaneous_symbols,False,Risky Unicode range: miscellaneous_symbols; Generic numbered name
NG0394,⇞,ng:structure:method_11,[METHOD11],structure,method_11,95.0,C,False,False,False,True,,method_11,safe,False,Generic numbered name
NG0395,♂,ng:structure:class_16,[CLASS16],structure,class_16,95.0,A,False,True,False,True,,class_16,miscellaneous_symbols,False,Risky Unicode range: miscellaneous_symbols; Generic numbered name
NG0396,↣,ng:memory:ref_11,[REF11],memory,ref_11,95.0,C,False,False,False,True,,ref_11,safe,False,Generic numbered name
NG0397,⚝,ng:memory:ref_12,[REF12],memory,ref_12,95.0,C,False,False,False,True,,ref_12,miscellaneous_symbols,True,Generic numbered name
NG0398,♤,ng:operator:mod_12,[MOD12],operator,mod_12,95.0,A,False,True,False,True,,mod_12,miscellaneous_symbols,False,Risky Unicode range: miscellaneous_symbols; Generic numbered name
NG0399,⇹,ng:memory:free_14,[FREE14],memory,free_14,95.0,C,False,False,False,True,,free_14,safe,False,Generic numbered name
NG0400,▯,ng:memory:deref_15,[DEREF15],memory,deref_15,95.0,C,False,False,False,True,,deref_15,safe,False,Generic numbered name
NG0401,▭,ng:operator:add_9,[ADD9],operator,add_9,95.0,C,False,False,False,True,,add_9,safe,False,Generic numbered name
NG0402,⛞,ng:operator:add_10,[ADD10],operator,add_10,95.0,A,False,True,False,True,,add_10,miscellaneous_symbols,False,Risky Unicode range: miscellaneous_symbols; Generic numbered name
NG0403,⛚,ng:operator:sub_13,[SUB13],operator,sub_13,95.0,A,False,True,False,True,,sub_13,miscellaneous_symbols,False,Risky Unicode range: miscellaneous_symbols; Generic numbered name
NG0404,⚧,ng:structure:class_17,[CLASS17],structure,class_17,95.0,C,False,False,False,True,,class_17,miscellaneous_symbols,True,Generic numbered name
NG0405,▴,ng:memory:free_15,[FREE15],memory,free_15,95.0,C,False,False,False,True,,free_15,safe,False,Generic numbered name
NG0406,≋,ng:operator:add_11,[ADD11],operator,add_11,95.0,C,False,False,False,True,,add_11,safe,False,Generic numbered name
NG0407,⛥,ng:logic:implies_21,[IMPLIES21],logic,implies_21,95.0,A,False,True,True,True,[IMPLIE..],implies_21,miscellaneous_symbols,False,Risky Unicode range: miscellaneous_symbols; Long fallback: 11 chars; Generic numbered name
NG0408,♝,ng:memory:alloc_19,[ALLOC19],memory,alloc_19,95.0,A,False,True,False,True,,alloc_19,miscellaneous_symbols,False,Risky Unicode range: miscellaneous_symbols; Generic numbered name
NG0409,☠,ng:memory:free_16,[FREE16],memory,free_16,95.0,A,False,True,False,True,,free_16,miscellaneous_symbols,False,Risky Unicode range: miscellaneous_symbols; Generic numbered name
NG0410,▢,ng:operator:mod_13,[MOD13],operator,mod_13,95.0,C,False,False,False,True,,mod_13,safe,False,Generic numbered name
NG0411,☇,ng:logic:iff_10,[IFF10],logic,iff_10,95.0,A,False,True,False,True,,iff_10,miscellaneous_symbols,False,Risky Unicode range: miscellaneous_symbols; Generic numbered name
NG0412,≕,ng:memory:alloc_20,[ALLOC20],memory,alloc_20,95.0,C,False,False,False,True,,alloc_20,safe,False,Generic numbered name
NG0413,⇯,ng:logic:or_15,[OR15],logic,or_15,95.0,C,False,False,False,True,,or_15,safe,False,Generic numbered name
NG0414,☞,ng:flow:break_9,[BREAK9],flow,break_9,95.0,A,False,True,False,True,,break_9,miscellaneous_symbols,False,Risky Unicode range: miscellaneous_symbols; Generic numbered name
NG0415,⇧,ng:flow:break_10,[BREAK10],flow,break_10,95.0,C,False,False,False,True,,break_10,safe,False,Generic numbered name
NG0416,⚍,ng:structure:class_18,[CLASS18],structure,class_18,95.0,A,False,True,False,True,,class_18,miscellaneous_symbols,False,Risky Unicode range: miscellaneous_symbols; Generic numbered name
NG0417,↵,ng:logic:or_16,[OR16],logic,or_16,95.0,C,False,False,False,True,,or_16,safe,False,Generic numbered name
NG0418,☭,ng:structure:class_19,[CLASS19],structure,class_19,95.0,A,False,True,False,True,,class_19,miscellaneous_symbols,False,Risky Unicode range: miscellaneous_symbols; Generic numbered name
NG0419,⋧,ng:operator:mul_10,[MUL10],operator,mul_10,95.0,C,False,False,False,True,,mul_10,safe,False,Generic numbered name
NG0420,⋬,ng:operator:mul_11,[MUL11],operator,mul_11,95.0,C,False,False,False,True,,mul_11,safe,False,Generic numbered name
NG0421,∞,ng:logic:not_17,[NOT17],logic,not_17,95.0,C,False,False,False,True,,not_17,safe,False,Generic numbered name
NG0422,▶,ng:memory:pointer_14,[POINTER14],memory,pointer_14,95.0,B,False,False,True,True,[POINTE..],pointer_14,safe,False,Long fallback: 11 chars; Generic numbered name
NG0423,↬,ng:flow:break_11,[BREAK11],flow,break_11,95.0,C,False,False,False,True,,break_11,safe,False,Generic numbered name
NG0424,⋰,ng:memory:ref_13,[REF13],memory,ref_13,95.0,C,False,False,False,True,,ref_13,safe,False,Generic numbered name
NG0425,⚣,ng:flow:else_9,[ELSE9],flow,else_9,95.0,C,False,False,False,True,,else_9,miscellaneous_symbols,True,Generic numbered name
NG0426,♘,ng:flow:for_17,[FOR17],flow,for_17,95.0,A,False,True,False,True,,for_17,miscellaneous_symbols,False,Risky Unicode range: miscellaneous_symbols; Generic numbered name
NG0427,⚬,ng:structure:class_20,[CLASS20],structure,class_20,95.0,C,False,False,False,True,,class_20,miscellaneous_symbols,True,Generic numbered name
NG0428,◣,ng:structure:method_12,[METHOD12],structure,method_12,95.0,C,False,False,False,True,,method_12,safe,False,Generic numbered name
NG0429,∫,ng:logic:xor_11,[XOR11],logic,xor_11,95.0,C,False,False,False,True,,xor_11,safe,False,Generic numbered name
NG0430,♾,ng:operator:div_15,[DIV15],operator,div_15,95.0,A,False,True,False,True,,div_15,miscellaneous_symbols,False,Risky Unicode range: miscellaneous_symbols; Generic numbered name
NG0431,↺,ng:memory:deref_16,[DEREF16],memory,deref_16,95.0,C,False,False,False,True,,deref_16,safe,False,Generic numbered name
NG0432,⚽,ng:memory:pointer_15,[POINTER15],memory,pointer_15,95.0,A,False,True,True,True,[POINTE..],pointer_15,miscellaneous_symbols,False,Risky Unicode range: miscellaneous_symbols; Long fallback: 11 chars; Generic numbered name
NG0433,∆,ng:structure:function_20,[FUNCTION20],structure,function_20,95.0,B,False,False,True,True,[FUNCTI..],function_20,safe,False,Long fallback: 12 chars; Generic numbered name
NG0434,⇜,ng:flow:if_18,[IF18],flow,if_18,95.0,C,False,False,False,True,,if_18,safe,False,Generic numbered name
NG0435,⛒,ng:memory:deref_17,[DEREF17],memory,deref_17,95.0,A,False,True,False,True,,deref_17,miscellaneous_symbols,False,Risky Unicode range: miscellaneous_symbols; Generic numbered name
NG0436,◩,ng:operator:div_16,[DIV16],operator,div_16,95.0,C,False,False,False,True,,div_16,safe,False,Generic numbered name
NG0437,⚜,ng:memory:free_17,[FREE17],memory,free_17,95.0,C,False,False,False,True,,free_17,miscellaneous_symbols,True,Generic numbered name
NG0438,⋦,ng:memory:pointer_16,[POINTER16],memory,pointer_16,95.0,B,False,False,True,True,[POINTE..],pointer_16,safe,False,Long fallback: 11 chars; Generic numbered name
NG0439,♈,ng:structure:class_21,[CLASS21],structure,class_21,95.0,A,False,True,False,True,,class_21,miscellaneous_symbols,False,Risky Unicode range: miscellaneous_symbols; Generic numbered name
NG0440,↓,ng:structure:class_22,[CLASS22],structure,class_22,95.0,C,False,False,False,True,,class_22,safe,False,Generic numbered name
NG0441,▬,ng:logic:not_18,[NOT18],logic,not_18,95.0,C,False,False,False,True,,not_18,safe,False,Generic numbered name
NG0442,∲,ng:operator:div_17,[DIV17],operator,div_17,95.0,C,False,False,False,True,,div_17,safe,False,Generic numbered name
NG0443,≜,ng:structure:property_19,[PROPERTY19],structure,property_19,95.0,B,False,False,True,True,[PROPER..],property_19,safe,False,Long fallback: 12 chars; Generic numbered name
NG0444,⛵,ng:structure:class_23,[CLASS23],structure,class_23,95.0,A,False,True,False,True,,class_23,miscellaneous_symbols,False,Risky Unicode range: miscellaneous_symbols; Generic numbered name
NG0445,⋏,ng:logic:not_19,[NOT19],logic,not_19,95.0,C,False,False,False,True,,not_19,safe,False,Generic numbered name
NG0446,⊪,ng:logic:implies_22,[IMPLIES22],logic,implies_22,95.0,B,False,False,True,True,[IMPLIE..],implies_22,safe,False,Long fallback: 11 chars; Generic numbered name
NG0447,◖,ng:structure:method_13,[METHOD13],structure,method_13,95.0,C,False,False,False,True,,method_13,safe,False,Generic numbered name
NG0448,◫,ng:flow:break_12,[BREAK12],flow,break_12,95.0,C,False,False,False,True,,break_12,safe,False,Generic numbered name
NG0449,♣,ng:memory:free_18,[FREE18],memory,free_18,95.0,A,False,True,False,True,,free_18,miscellaneous_symbols,False,Risky Unicode range: miscellaneous_symbols; Generic numbered name
NG0450,⊰,ng:memory:deref_18,[DEREF18],memory,deref_18,95.0,C,False,False,False,True,,deref_18,safe,False,Generic numbered name
NG0451,⇊,ng:logic:iff_11,[IFF11],logic,iff_11,95.0,C,False,False,False,True,,iff_11,safe,False,Generic numbered name
NG0452,⇥,ng:memory:free_19,[FREE19],memory,free_19,95.0,C,False,False,False,True,,free_19,safe,False,Generic numbered name
NG0453,∁,ng:flow:break_13,[BREAK13],flow,break_13,95.0,C,False,False,False,True,,break_13,safe,False,Generic numbered name
NG0454,∦,ng:structure:function_21,[FUNCTION21],structure,function_21,95.0,B,False,False,True,True,[FUNCTI..],function_21,safe,False,Long fallback: 12 chars; Generic numbered name
NG0455,♗,ng:structure:property_20,[PROPERTY20],structure,property_20,95.0,A,False,True,True,True,[PROPER..],property_20,miscellaneous_symbols,False,Risky Unicode range: miscellaneous_symbols; Long fallback: 12 chars; Generic numbered name
NG0456,☜,ng:logic:or_17,[OR17],logic,or_17,95.0,A,False,True,False,True,,or_17,miscellaneous_symbols,False,Risky Unicode range: miscellaneous_symbols; Generic numbered name
NG0457,≇,ng:memory:free_20,[FREE20],memory,free_20,95.0,C,False,False,False,True,,free_20,safe,False,Generic numbered name
NG0458,⛝,ng:operator:div_18,[DIV18],operator,div_18,95.0,A,False,True,False,True,,div_18,miscellaneous_symbols,False,Risky Unicode range: miscellaneous_symbols; Generic numbered name
NG0459,⛔,ng:operator:pow_12,[POW12],operator,pow_12,95.0,A,False,True,False,True,,pow_12,miscellaneous_symbols,False,Risky Unicode range: miscellaneous_symbols; Generic numbered name
NG0460,◎,ng:logic:or_18,[OR18],logic,or_18,95.0,C,False,False,False,True,,or_18,safe,False,Generic numbered name
NG0461,⋃,ng:flow:for_18,[FOR18],flow,for_18,95.0,C,False,False,False,True,,for_18,safe,False,Generic numbered name
NG0462,◌,ng:flow:return_14,[RETURN14],flow,return_14,95.0,C,False,False,False,True,,return_14,safe,False,Generic numbered name
NG0463,⛖,ng:flow:for_19,[FOR19],flow,for_19,95.0,A,False,True,False,True,,for_19,miscellaneous_symbols,False,Risky Unicode range: miscellaneous_symbols; Generic numbered name
NG0464,☄,ng:operator:add_12,[ADD12],operator,add_12,95.0,A,False,True,False,True,,add_12,miscellaneous_symbols,False,Risky Unicode range: miscellaneous_symbols; Generic numbered name
NG0465,⛸,ng:flow:if_19,[IF19],flow,if_19,95.0,A,False,True,False,True,,if_19,miscellaneous_symbols,False,Risky Unicode range: miscellaneous_symbols; Generic numbered name
NG0466,⊵,ng:logic:xor_12,[XOR12],logic,xor_12,95.0,C,False,False,False,True,,xor_12,safe,False,Generic numbered name
NG0467,♦,ng:logic:xor_13,[XOR13],logic,xor_13,95.0,A,False,True,False,True,,xor_13,miscellaneous_symbols,False,Risky Unicode range: miscellaneous_symbols; Generic numbered name
NG0468,⋵,ng:operator:add_13,[ADD13],operator,add_13,95.0,C,False,False,False,True,,add_13,safe,False,Generic numbered name
NG0469,☾,ng:operator:add_14,[ADD14],operator,add_14,95.0,A,False,True,False,True,,add_14,miscellaneous_symbols,False,Risky Unicode range: miscellaneous_symbols; Generic numbered name
NG0470,▣,ng:structure:property_21,[PROPERTY21],structure,property_21,95.0,B,False,False,True,True,[PROPER..],property_21,safe,False,Long fallback: 12 chars; Generic numbered name
NG0471,⋫,ng:structure:method_14,[METHOD14],structure,method_14,95.0,C,False,False,False,True,,method_14,safe,False,Generic numbered name
NG0472,⛆,ng:operator:mod_14,[MOD14],operator,mod_14,95.0,A,False,True,False,True,,mod_14,miscellaneous_symbols,False,Risky Unicode range: miscellaneous_symbols; Generic numbered name
NG0473,⛗,ng:memory:free_21,[FREE21],memory,free_21,95.0,A,False,True,False,True,,free_21,miscellaneous_symbols,False,Risky Unicode range: miscellaneous_symbols; Generic numbered name
NG0474,♔,ng:logic:xor_14,[XOR14],logic,xor_14,95.0,A,False,True,False,True,,xor_14,miscellaneous_symbols,False,Risky Unicode range: miscellaneous_symbols; Generic numbered name
NG0475,♞,ng:logic:or_19,[OR19],logic,or_19,95.0,A,False,True,False,True,,or_19,miscellaneous_symbols,False,Risky Unicode range: miscellaneous_symbols; Generic numbered name
NG0476,⚹,ng:structure:function_22,[FUNCTION22],structure,function_22,95.0,B,False,False,True,True,[FUNCTI..],function_22,miscellaneous_symbols,True,Long fallback: 12 chars; Generic numbered name
NG0477,⚠,ng:flow:else_10,[ELSE10],flow,else_10,95.0,C,False,False,False,True,,else_10,miscellaneous_symbols,True,Generic numbered name
NG0478,⇍,ng:flow:if_20,[IF20],flow,if_20,95.0,C,False,False,False,True,,if_20,safe,False,Generic numbered name
NG0479,∳,ng:operator:div_19,[DIV19],operator,div_19,95.0,C,False,False,False,True,,div_19,safe,False,Generic numbered name
NG0480,⚟,ng:logic:or_20,[OR20],logic,or_20,95.0,C,False,False,False,True,,or_20,miscellaneous_symbols,True,Generic numbered name
NG0481,▷,ng:flow:if_21,[IF21],flow,if_21,95.0,C,False,False,False,True,,if_21,safe,False,Generic numbered name
NG0482,●,ng:operator:add_15,[ADD15],operator,add_15,95.0,C,False,False,False,True,,add_15,safe,False,Generic numbered name
NG0483,⊐,ng:structure:method_15,[METHOD15],structure,method_15,95.0,C,False,False,False,True,,method_15,safe,False,Generic numbered name
NG0484,⚶,ng:logic:implies_23,[IMPLIES23],logic,implies_23,95.0,B,False,False,True,True,[IMPLIE..],implies_23,miscellaneous_symbols,True,Long fallback: 11 chars; Generic numbered name
NG0485,⇾,ng:operator:div_20,[DIV20],operator,div_20,95.0,C,False,False,False,True,,div_20,safe,False,Generic numbered name
NG0486,∔,ng:operator:add_16,[ADD16],operator,add_16,95.0,C,False,False,False,True,,add_16,safe,False,Generic numbered name
NG0487,☺,ng:flow:break_14,[BREAK14],flow,break_14,95.0,A,False,True,False,True,,break_14,miscellaneous_symbols,False,Risky Unicode range: miscellaneous_symbols; Generic numbered name
NG0488,⇐,ng:operator:add_17,[ADD17],operator,add_17,95.0,C,False,False,False,True,,add_17,safe,False,Generic numbered name
NG0489,⊤,ng:structure:function_23,[FUNCTION23],structure,function_23,95.0,B,False,False,True,True,[FUNCTI..],function_23,safe,False,Long fallback: 12 chars; Generic numbered name
NG0490,⛿,ng:structure:class_24,[CLASS24],structure,class_24,95.0,A,False,True,False,True,,class_24,miscellaneous_symbols,False,Risky Unicode range: miscellaneous_symbols; Generic numbered name
NG0491,☂,ng:operator:div_21,[DIV21],operator,div_21,95.0,A,False,True,False,True,,div_21,miscellaneous_symbols,False,Risky Unicode range: miscellaneous_symbols; Generic numbered name
NG0492,⇮,ng:operator:mod_15,[MOD15],operator,mod_15,95.0,C,False,False,False,True,,mod_15,safe,False,Generic numbered name
NG0493,⚩,ng:flow:if_22,[IF22],flow,if_22,95.0,C,False,False,False,True,,if_22,miscellaneous_symbols,True,Generic numbered name
NG0494,◷,ng:operator:add_18,[ADD18],operator,add_18,95.0,C,False,False,False,True,,add_18,safe,False,Generic numbered name
NG0495,⊢,ng:structure:class_25,[CLASS25],structure,class_25,95.0,C,False,False,False,True,,class_25,safe,False,Generic numbered name
NG0496,∃,ng:logic:xor_15,[XOR15],logic,xor_15,95.0,C,False,False,False,True,,xor_15,safe,False,Generic numbered name
NG0497,☨,ng:logic:not_20,[NOT20],logic,not_20,95.0,A,False,True,False,True,,not_20,miscellaneous_symbols,False,Risky Unicode range: miscellaneous_symbols; Generic numbered name
NG0498,⋢,ng:logic:not_21,[NOT21],logic,not_21,95.0,C,False,False,False,True,,not_21,safe,False,Generic numbered name
NG0499,☗,ng:structure:property_22,[PROPERTY22],structure,property_22,95.0,A,False,True,True,True,[PROPER..],property_22,miscellaneous_symbols,False,Risky Unicode range: miscellaneous_symbols; Long fallback: 12 chars; Generic numbered name
NG0500,≭,ng:logic:implies_24,[IMPLIES24],logic,implies_24,95.0,B,False,False,True,True,[IMPLIE..],implies_24,safe,False,Long fallback: 11 chars; Generic numbered name
NG0501,⇫,ng:flow:while_18,[WHILE18],flow,while_18,95.0,C,False,False,False,True,,while_18,safe,False,Generic numbered name
NG0502,⊺,ng:flow:for_20,[FOR20],flow,for_20,95.0,C,False,False,False,True,,for_20,safe,False,Generic numbered name
NG0503,♯,ng:memory:pointer_17,[POINTER17],memory,pointer_17,95.0,A,False,True,True,True,[POINTE..],pointer_17,miscellaneous_symbols,False,Risky Unicode range: miscellaneous_symbols; Long fallback: 11 chars; Generic numbered name
NG0504,≪,ng:logic:iff_12,[IFF12],logic,iff_12,95.0,C,False,False,False,True,,iff_12,safe,False,Generic numbered name
NG0505,⊚,ng:structure:property_23,[PROPERTY23],structure,property_23,95.0,B,False,False,True,True,[PROPER..],property_23,safe,False,Long fallback: 12 chars; Generic numbered name
NG0506,♰,ng:logic:and_15,[AND15],logic,and_15,95.0,A,False,True,False,True,,and_15,miscellaneous_symbols,False,Risky Unicode range: miscellaneous_symbols; Generic numbered name
NG0507,⚤,ng:flow:else_11,[ELSE11],flow,else_11,95.0,C,False,False,False,True,,else_11,miscellaneous_symbols,True,Generic numbered name
NG0508,☍,ng:logic:or_21,[OR21],logic,or_21,95.0,A,False,True,False,True,,or_21,miscellaneous_symbols,False,Risky Unicode range: miscellaneous_symbols; Generic numbered name
NG0509,◉,ng:logic:xor_16,[XOR16],logic,xor_16,95.0,C,False,False,False,True,,xor_16,safe,False,Generic numbered name
NG0510,⚪,ng:flow:else_12,[ELSE12],flow,else_12,95.0,C,False,False,False,True,,else_12,miscellaneous_symbols,True,Generic numbered name
NG0511,♠,ng:flow:if_23,[IF23],flow,if_23,95.0,A,False,True,False,True,,if_23,miscellaneous_symbols,False,Risky Unicode range: miscellaneous_symbols; Generic numbered name
NG0512,↼,ng:structure:function_24,[FUNCTION24],structure,function_24,95.0,B,False,False,True,True,[FUNCTI..],function_24,safe,False,Long fallback: 12 chars; Generic numbered name
NG0513,⎢,ng:reasoning:planning,[PLANNING],reasoning,planning,100.0,OK,False,False,False,False,,,safe,False,
NG0514,⏐,ng:reasoning:correlation,[CORRELATION],reasoning,correlation,90.0,C,False,False,True,False,[CORR],,safe,False,Long fallback: 13 chars
NG0515,⎆,ng:reasoning:conclusion,[CONCLUSION],reasoning,conclusion,100.0,C,False,False,True,False,[CONCLU..],,safe,False,Long fallback: 12 chars
NG0516,⌔,ng:reasoning:metaphor,[METAPHOR],reasoning,metaphor,100.0,OK,False,False,False,False,,,safe,False,
NG0517,⍸,ng:reasoning:premise,[PREMISE],reasoning,premise,100.0,OK,False,False,False,False,,,safe,False,
NG0518,⧮,ng:reasoning:evaluation,[EVALUATION],reasoning,evaluation,100.0,C,False,False,True,False,[EVALUA..],,safe,False,Long fallback: 12 chars
NG0519,⪜,ng:reasoning:specialization,[SPECIALIZATION],reasoning,specialization,90.0,C,False,False,True,False,[SPEC],,safe,False,Long fallback: 16 chars
NG0520,⇡,ng:reasoning:assessment,[ASSESSMENT],reasoning,assessment,100.0,C,False,False,True,False,[ASSESS..],,safe,False,Long fallback: 12 chars
NG0521,⊍,ng:reasoning:problem_solving,[PROBLEMSOLVING],reasoning,problem_solving,90.0,C,False,False,True,False,[SOLVE],,safe,False,Long fallback: 16 chars
NG0522,⧬,ng:reasoning:creativity,[CREATIVITY],reasoning,creativity,100.0,C,False,False,True,False,[CREATI..],,safe,False,Long fallback: 12 chars
NG0523,⍛,ng:reasoning:intuition,[INTUITION],reasoning,intuition,100.0,C,False,False,True,False,[INTUIT..],,safe,False,Long fallback: 11 chars
NG0524,⏃,ng:reasoning:contrapositive,[CONTRAPOSITIVE],reasoning,contrapositive,90.0,C,False,False,True,False,[CONTRA],,safe,False,Long fallback: 16 chars
NG0525,⪪,ng:reasoning:axiom,[AXIOM],reasoning,axiom,100.0,OK,False,False,False,False,,,safe,False,
NG0526,⦵,ng:reasoning:self_awareness,[SELFAWARENESS],reasoning,self_awareness,90.0,C,False,False,True,False,[AWARE],,safe,False,Long fallback: 15 chars
NG0527,⧄,ng:reasoning:reflection,[REFLECTION],reasoning,reflection,100.0,C,False,False,True,False,[REFLEC..],,safe,False,Long fallback: 12 chars
NG0528,⩓,ng:reasoning:deduction,[DEDUCTION],reasoning,deduction,100.0,C,False,False,True,False,[DEDUCT..],,safe,False,Long fallback: 11 chars
NG0529,⎭,ng:reasoning:modus_ponens,[MODUSPONENS],reasoning,modus_ponens,90.0,C,False,False,True,False,[MPONENS],,safe,False,Long fallback: 13 chars
NG0530,⪫,ng:reasoning:tautology,[TAUTOLOGY],reasoning,tautology,100.0,C,False,False,True,False,[TAUTOL..],,safe,False,Long fallback: 11 chars
NG0531,⊲,ng:reasoning:hypothesis,[HYPOTHESIS],reasoning,hypothesis,100.0,C,False,False,True,False,[HYPOTH..],,safe,False,Long fallback: 12 chars
NG0532,⩢,ng:reasoning:soundness,[SOUNDNESS],reasoning,soundness,100.0,C,False,False,True,False,[SOUNDN..],,safe,False,Long fallback: 11 chars
NG0533,⪣,ng:reasoning:consistency,[CONSISTENCY],reasoning,consistency,90.0,C,False,False,True,False,[CONSIST],,safe,False,Long fallback: 13 chars
NG0534,⏓,ng:reasoning:decision,[DECISION],reasoning,decision,100.0,OK,False,False,False,False,,,safe,False,
NG0535,∟,ng:reasoning:judgment,[JUDGMENT],reasoning,judgment,100.0,OK,False,False,False,False,,,safe,False,
NG0536,⌾,ng:reasoning:inference,[INFERENCE],reasoning,inference,100.0,C,False,False,True,False,[INFERE..],,safe,False,Long fallback: 11 chars
NG0537,⎥,ng:reasoning:paradox,[PARADOX],reasoning,paradox,100.0,OK,False,False,False,False,,,safe,False,
NG0538,⩷,ng:reasoning:similarity,[SIMILARITY],reasoning,similarity,100.0,C,False,False,True,False,[SIMILA..],,safe,False,Long fallback: 12 chars
NG0539,▪,ng:reasoning:contradiction,[CONTRADICTION],reasoning,contradiction,90.0,C,False,False,True,False,[CONTRA],,safe,False,Long fallback: 15 chars
NG0540,⍝,ng:reasoning:proof,[PROOF],reasoning,proof,100.0,OK,False,False,False,False,,,safe,False,
NG0541,⪀,ng:reasoning:lemma,[LEMMA],reasoning,lemma,100.0,OK,False,False,False,False,,,safe,False,
NG0542,⪲,ng:reasoning:causality,[CAUSALITY],reasoning,causality,100.0,C,False,False,True,False,[CAUSAL..],,safe,False,Long fallback: 11 chars
NG0543,⏟,ng:reasoning:monitoring,[MONITORING],reasoning,monitoring,100.0,C,False,False,True,False,[MONITO..],,safe,False,Long fallback: 12 chars
NG0544,⎓,ng:reasoning:syllogism,[SYLLOGISM],reasoning,syllogism,100.0,C,False,False,True,False,[SYLLOG..],,safe,False,Long fallback: 11 chars
NG0545,≘,ng:reasoning:heuristic,[HEURISTIC],reasoning,heuristic,100.0,C,False,False,True,False,[HEURIS..],,safe,False,Long fallback: 11 chars
NG0546,⧗,ng:reasoning:synthesis,[SYNTHESIS],reasoning,synthesis,100.0,C,False,False,True,False,[SYNTHE..],,safe,False,Long fallback: 11 chars
NG0547,⧓,ng:reasoning:pattern,[PATTERN],reasoning,pattern,100.0,OK,False,False,False,False,,,safe,False,
NG0548,⎑,ng:reasoning:metacognition,[METACOGNITION],reasoning,metacognition,90.0,C,False,False,True,False,[META],,safe,False,Long fallback: 15 chars
NG0549,⏰,ng:reasoning:completeness,[COMPLETENESS],reasoning,completeness,90.0,C,False,False,True,False,[COMPLETE],,safe,False,Long fallback: 14 chars
NG0550,⧯,ng:reasoning:fallacy,[FALLACY],reasoning,fallacy,100.0,OK,False,False,False,False,,,safe,False,
NG0551,⎚,ng:reasoning:induction,[INDUCTION],reasoning,induction,100.0,C,False,False,True,False,[INDUCT..],,safe,False,Long fallback: 11 chars
NG0552,⧱,ng:reasoning:strategy,[STRATEGY],reasoning,strategy,100.0,OK,False,False,False,False,,,safe,False,
NG0553,≊,ng:reasoning:generalization,[GENERALIZATION],reasoning,generalization,90.0,C,False,False,True,False,[GENERAL],,safe,False,Long fallback: 16 chars
NG0554,⦸,ng:reasoning:analogy,[ANALOGY],reasoning,analogy,100.0,OK,False,False,False,False,,,safe,False,
NG0555,⎁,ng:reasoning:error_correction,[ERRORCORRECTION],reasoning,error_correction,90.0,C,False,False,True,False,[ERRCORR],,safe,False,Long fallback: 17 chars
NG0556,⎯,ng:reasoning:abduction,[ABDUCTION],reasoning,abduction,100.0,C,False,False,True,False,[ABDUCT..],,safe,False,Long fallback: 11 chars
NG0557,≵,ng:reasoning:classification,[CLASSIFICATION],reasoning,classification,90.0,C,False,False,True,False,[CLASS],,safe,False,Long fallback: 16 chars
NG0558,⎧,ng:reasoning:modus_tollens,[MODUSTOLLENS],reasoning,modus_tollens,90.0,C,False,False,True,False,[MTOLLENS],,safe,False,Long fallback: 14 chars
NG0559,⎬,ng:reasoning:validity,[VALIDITY],reasoning,validity,100.0,OK,False,False,False,False,,,safe,False,
NG0560,⌏,ng:reasoning:theorem,[THEOREM],reasoning,theorem,100.0,OK,False,False,False,False,,,safe,False,
NG0561,⦗,ng:reasoning:abstraction,[ABSTRACTION],reasoning,abstraction,90.0,C,False,False,True,False,[ABSTRACT],,safe,False,Long fallback: 13 chars
NG0562,∏,ng:reasoning:control,[CONTROL],reasoning,control,100.0,OK,False,False,False,False,,,safe,False,
NG0563,⫳,ng:reasoning:difference,[DIFFERENCE],reasoning,difference,100.0,C,False,False,True,False,[DIFFER..],,safe,False,Long fallback: 12 chars
NG0564,⧔,ng:reasoning:insight,[INSIGHT],reasoning,insight,100.0,OK,False,False,False,False,,,safe,False,
NG0565,⊮,ng:reasoning:decidability,[DECIDABILITY],reasoning,decidability,90.0,C,False,False,True,False,[DECIDE],,safe,False,Long fallback: 14 chars
NG0566,⫩,ng:reasoning:analysis,[ANALYSIS],reasoning,analysis,100.0,OK,False,False,False,False,,,safe,False,
NG0567,⦿,ng:reasoning:bias,[BIAS],reasoning,bias,100.0,OK,False,False,False,False,,,safe,False,
NG0568,⚡,ng:async:function,[async],async_concurrency,,1.4000000000000001,A,True,False,False,False,,,unknown,False,Low validation score: 1.4000000000000001
NG0569,⏳,ng:async:await,[await],async_concurrency,,1.4000000000000001,A,True,False,False,False,,,unknown,False,Low validation score: 1.4000000000000001
NG0570,⏸,ng:async:pause,[pause],async_concurrency,,1.4000000000000001,A,True,False,False,False,,,unknown,False,Low validation score: 1.4000000000000001
NG0571,⏯,ng:async:resume,[resume],async_concurrency,,1.4000000000000001,A,True,False,False,False,,,unknown,False,Low validation score: 1.4000000000000001
NG0572,⏹,ng:async:stop,[stop],async_concurrency,,1.4000000000000001,A,True,False,False,False,,,unknown,False,Low validation score: 1.4000000000000001
NG0573,⏱,ng:async:timer,[timer],async_concurrency,,1.4000000000000001,A,True,False,False,False,,,unknown,False,Low validation score: 1.4000000000000001
NG0574,⌛,ng:async:hourglass,[wait],async_concurrency,,1.4000000000000001,A,True,False,False,False,,,unknown,False,Low validation score: 1.4000000000000001
NG0575,🔮,ng:async:future,[future],async_concurrency,,1.4000000000000001,A,True,False,False,False,,,unknown,False,Low validation score: 1.4000000000000001
NG0576,🔄,ng:async:chain,[chain],async_concurrency,,1.4000000000000001,A,True,False,False,False,,,unknown,False,Low validation score: 1.4000000000000001
NG0577,🎯,ng:async:target,[target],async_concurrency,,1.4000000000000001,A,True,False,False,False,,,unknown,False,Low validation score: 1.4000000000000001
NG0578,🌊,ng:async:flow,[flow],async_concurrency,,1.4000000000000001,A,True,False,False,False,,,unknown,False,Low validation score: 1.4000000000000001
NG0580,🔁,ng:async:loop,[loop],async_concurrency,,1.4000000000000001,A,True,False,False,False,,,unknown,False,Low validation score: 1.4000000000000001
NG0581,🔃,ng:async:cycle,[cycle],async_concurrency,,1.4000000000000001,A,True,False,False,False,,,unknown,False,Low validation score: 1.4000000000000001
NG0583,🏭,ng:async:generator,[gen],async_concurrency,,1.4000000000000001,A,True,False,False,False,,,unknown,False,Low validation score: 1.4000000000000001
NG0584,⚙,ng:async:iterator,[iter],async_concurrency,,1.4000000000000001,A,True,False,False,False,,,unknown,False,Low validation score: 1.4000000000000001
NG0585,🔧,ng:async:next,[next],async_concurrency,,1.4000000000000001,A,True,False,False,False,,,unknown,False,Low validation score: 1.4000000000000001
NG0587,🔩,ng:async:throw,[throw],async_concurrency,,1.4000000000000001,A,True,False,False,False,,,unknown,False,Low validation score: 1.4000000000000001
NG0588,🔐,ng:async:enter,[enter],async_concurrency,,1.4000000000000001,A,True,False,False,False,,,unknown,False,Low validation score: 1.4000000000000001
NG0589,🔓,ng:async:exit,[exit],async_concurrency,,1.4000000000000001,A,True,False,False,False,,,unknown,False,Low validation score: 1.4000000000000001
NG0590,🛡,ng:async:guard,[guard],async_concurrency,,1.4000000000000001,A,True,False,False,False,,,unknown,False,Low validation score: 1.4000000000000001
NG0591,🎭,ng:async:with,[with],async_concurrency,,1.4000000000000001,A,True,False,False,False,,,unknown,False,Low validation score: 1.4000000000000001
NG0592,🚨,ng:async:error,[error],async_concurrency,,1.4000000000000001,A,True,False,False,False,,,unknown,False,Low validation score: 1.4000000000000001
NG0593,🛟,ng:async:rescue,[rescue],async_concurrency,,1.4000000000000001,A,True,False,False,False,,,unknown,False,Low validation score: 1.4000000000000001
NG0594,🧵,ng:thread:thread,[thread],async_concurrency,,1.4000000000000001,A,True,False,False,False,,,unknown,False,Low validation score: 1.4000000000000001
NG0596,👥,ng:thread:group,[group],async_concurrency,,1.4000000000000001,A,True,False,False,False,,,unknown,False,Low validation score: 1.4000000000000001
NG0598,🔚,ng:thread:join,[join],async_concurrency,,1.4000000000000001,A,True,False,False,False,,,unknown,False,Low validation score: 1.4000000000000001
NG0599,💀,ng:thread:kill,[kill],async_concurrency,,1.4000000000000001,A,True,False,False,False,,,unknown,False,Low validation score: 1.4000000000000001
NG0600,😴,ng:thread:sleep,[sleep],async_concurrency,,1.4000000000000001,A,True,False,False,False,,,unknown,False,Low validation score: 1.4000000000000001
NG0603,🍴,ng:process:fork,[fork],async_concurrency,,1.4000000000000001,A,True,False,False,False,,,unknown,False,Low validation score: 1.4000000000000001
NG0605,📤,ng:process:send,[send],async_concurrency,,1.4000000000000001,A,True,False,False,False,,,unknown,False,Low validation score: 1.4000000000000001
NG0606,📥,ng:process:recv,[recv],async_concurrency,,1.4000000000000001,A,True,False,False,False,,,unknown,False,Low validation score: 1.4000000000000001
NG0607,🚇,ng:process:pipe,[pipe],async_concurrency,,1.4000000000000001,A,True,False,False,False,,,unknown,False,Low validation score: 1.4000000000000001
NG0608,📡,ng:process:signal,[signal],async_concurrency,,1.4000000000000001,A,True,False,False,False,,,unknown,False,Low validation score: 1.4000000000000001
NG0609,👷,ng:worker:worker,[worker],async_concurrency,,1.4000000000000001,A,True,False,False,False,,,unknown,False,Low validation score: 1.4000000000000001
NG0610,📋,ng:worker:queue,[queue],async_concurrency,,1.4000000000000001,A,True,False,False,False,,,unknown,False,Low validation score: 1.4000000000000001
NG0613,📊,ng:worker:result,[result],async_concurrency,,1.4000000000000001,A,True,False,False,False,,,unknown,False,Low validation score: 1.4000000000000001
NG0615,❌,ng:worker:cancel,[cancel],async_concurrency,,1.4000000000000001,A,True,False,False,False,,,unknown,False,Low validation score: 1.4000000000000001
NG0616,📅,ng:schedule:scheduler,[sched],async_concurrency,,1.4000000000000001,A,True,False,False,False,,,unknown,False,Low validation score: 1.4000000000000001
NG0617,📆,ng:schedule:cron,[cron],async_concurrency,,1.4000000000000001,A,True,False,False,False,,,unknown,False,Low validation score: 1.4000000000000001
NG0621,🛑,ng:schedule:stop,[stop],async_concurrency,,1.4000000000000001,A,True,False,False,False,,,unknown,False,Low validation score: 1.4000000000000001
NG0623,🗝,ng:sync:key,[key],async_concurrency,,1.4000000000000001,A,True,False,False,False,,,unknown,False,Low validation score: 1.4000000000000001
NG0624,🚪,ng:sync:door,[door],async_concurrency,,1.4000000000000001,A,True,False,False,False,,,unknown,False,Low validation score: 1.4000000000000001
NG0627,📖,ng:sync:read,[read],async_concurrency,,1.4000000000000001,A,True,False,False,False,,,unknown,False,Low validation score: 1.4000000000000001
NG0628,📝,ng:sync:write,[write],async_concurrency,,1.4000000000000001,A,True,False,False,False,,,unknown,False,Low validation score: 1.4000000000000001
NG0630,👑,ng:sync:exclusive,[excl],async_concurrency,,1.4000000000000001,A,True,False,False,False,,,unknown,False,Low validation score: 1.4000000000000001
NG0631,🎲,ng:sync:unfair,[unfair],async_concurrency,,1.4000000000000001,A,True,False,False,False,,,unknown,False,Low validation score: 1.4000000000000001
NG0632,🔢,ng:sync:count,[count],async_concurrency,,1.4000000000000001,A,True,False,False,False,,,unknown,False,Low validation score: 1.4000000000000001
NG0633,🚦,ng:sync:semaphore,[sem],async_concurrency,,1.4000000000000001,A,True,False,False,False,,,unknown,False,Low validation score: 1.4000000000000001
NG0638,🔔,ng:sync:condition,[cond],async_concurrency,,1.4000000000000001,A,True,False,False,False,,,unknown,False,Low validation score: 1.4000000000000001
NG0639,🎺,ng:sync:set,[set],async_concurrency,,1.4000000000000001,A,True,False,False,False,,,unknown,False,Low validation score: 1.4000000000000001
NG0640,🔇,ng:sync:clear,[clear],async_concurrency,,1.4000000000000001,A,True,False,False,False,,,unknown,False,Low validation score: 1.4000000000000001
NG0642,📢,ng:sync:notify,[notify],async_concurrency,,1.4000000000000001,A,True,False,False,False,,,unknown,False,Low validation score: 1.4000000000000001
NG0645,✅,ng:future:done,[done],async_concurrency,,1.4000000000000001,A,True,False,False,False,,,unknown,False,Low validation score: 1.4000000000000001
NG0649,🔗,ng:future:then,[then],async_concurrency,,1.4000000000000001,A,True,False,False,False,,,unknown,False,Low validation score: 1.4000000000000001
NG0653,🤝,ng:future:all,[all],async_concurrency,,1.4000000000000001,A,True,False,False,False,,,unknown,False,Low validation score: 1.4000000000000001
NG0654,🏃,ng:future:any,[any],async_concurrency,,1.4000000000000001,A,True,False,False,False,,,unknown,False,Low validation score: 1.4000000000000001
NG0655,🏆,ng:future:race,[race],async_concurrency,,1.4000000000000001,A,True,False,False,False,,,unknown,False,Low validation score: 1.4000000000000001
NG0661,✨,ng:completion:completion,[comp],async_concurrency,,1.4000000000000001,A,True,False,False,False,,,unknown,False,Low validation score: 1.4000000000000001
NG0662,💥,ng:completion:error,[error],async_concurrency,,1.4000000000000001,A,True,False,False,False,,,unknown,False,Low validation score: 1.4000000000000001
NG0671,🔒,ng:sync:lock,[lock],async_concurrency,,1.3,A,True,False,False,False,,,unknown,False,Low validation score: 1.3
NG0678,📣,ng:sync:broadcast,[broadcast],async_concurrency,,1.3,A,True,False,True,False,[broadc..],,unknown,False,Low validation score: 1.3; Long fallback: 11 chars
NG0683,🏁,ng:future:finally,[finally],async_concurrency,,1.3,A,True,False,False,False,,,unknown,False,Low validation score: 1.3
NG0685,🎉,ng:completion:success,[success],async_concurrency,,1.3,A,True,False,False,False,,,unknown,False,Low validation score: 1.3
NG0579,🔀,ng:async:branch,[branch],async_concurrency,,1.4000000000000001,A,True,False,False,False,,,unknown,False,Low validation score: 1.4000000000000001
NG0582,⤵,ng:async:yield,[yield],async_concurrency,,1.4000000000000001,A,True,False,False,False,,,unknown,False,Low validation score: 1.4000000000000001
NG0586,🛠,ng:async:send,[send],async_concurrency,,1.4000000000000001,A,True,False,False,False,,,unknown,False,Low validation score: 1.4000000000000001
NG0674,🔽,ng:sync:downgrade,[downgrade],async_concurrency,,1.3,A,True,False,True,False,[downgr..],,unknown,False,Low validation score: 1.3; Long fallback: 11 chars
NG0675,⬆,ng:sync:acquire,[acquire],async_concurrency,,1.3,A,True,False,False,False,,,unknown,False,Low validation score: 1.3
NG0676,⬇,ng:sync:release,[release],async_concurrency,,1.3,A,True,False,False,False,,,unknown,False,Low validation score: 1.3
NG0677,🚫,ng:sync:blocked,[blocked],async_concurrency,,1.3,A,True,False,False,False,,,unknown,False,Low validation score: 1.3
NG0595,⟴,ng:lifecycle:init,[init],classes_oop,,1.4000000000000001,A,True,False,False,False,,,unknown,False,Low validation score: 1.4000000000000001
NG0686,⟨,ng:generic:open,[<],classes_oop,,1.4000000000000001,A,True,False,False,False,,,unknown,False,Low validation score: 1.4000000000000001
NG0687,⟩,ng:generic:close,[>],classes_oop,,1.4000000000000001,A,True,False,False,False,,,unknown,False,Low validation score: 1.4000000000000001
NG0688,⟪,ng:generic:nested_open,[<<],classes_oop,,1.4000000000000001,A,True,False,False,False,,,unknown,False,Low validation score: 1.4000000000000001
NG0689,⟫,ng:generic:nested_close,[>>],classes_oop,,1.4000000000000001,A,True,False,False,False,,,unknown,False,Low validation score: 1.4000000000000001
NG0690,⟬,ng:generic:constraint,[where],classes_oop,,1.4000000000000001,A,True,False,False,False,,,unknown,False,Low validation score: 1.4000000000000001
NG0691,⟭,ng:generic:bound,[bound],classes_oop,,1.4000000000000001,A,True,False,False,False,,,unknown,False,Low validation score: 1.4000000000000001
NG0692,⟮,ng:generic:variance,[var],classes_oop,,1.4000000000000001,A,True,False,False,False,,,unknown,False,Low validation score: 1.4000000000000001
NG0693,⟯,ng:generic:covariance,[covar],classes_oop,,1.4000000000000001,A,True,False,False,False,,,unknown,False,Low validation score: 1.4000000000000001
NG0694,⟰,ng:inherit:multi_inherit,[multi],classes_oop,,1.3,A,True,False,False,False,,,unknown,False,Low validation score: 1.3
NG0695,⟱,ng:inherit:diamond,[diamond],classes_oop,,1.3,A,True,False,False,False,,,unknown,False,Low validation score: 1.3
NG0696,⤊,ng:inherit:override,[override],classes_oop,,1.3,A,True,False,False,False,,,unknown,False,Low validation score: 1.3
NG0697,⤋,ng:inherit:virtual,[virtual],classes_oop,,1.3,A,True,False,False,False,,,unknown,False,Low validation score: 1.3
NG0698,⤷,ng:inject:dependency,[inject],classes_oop,,1.3,A,True,False,False,False,,,unknown,False,Low validation score: 1.3
NG0699,⤶,ng:inject:provide,[provide],classes_oop,,1.3,A,True,False,False,False,,,unknown,False,Low validation score: 1.3
NG0700,⤸,ng:inject:wire,[wire],classes_oop,,1.3,A,True,False,False,False,,,unknown,False,Low validation score: 1.3
NG0701,⤹,ng:inject:resolve,[resolve],classes_oop,,1.3,A,True,False,False,False,,,unknown,False,Low validation score: 1.3
NG0702,⤺,ng:inject:circular,[circular],classes_oop,,1.3,A,True,False,False,False,,,unknown,False,Low validation score: 1.3
NG0703,⤼,ng:inject:factory,[factory],classes_oop,,1.3,A,True,False,False,False,,,unknown,False,Low validation score: 1.3
NG0704,⤽,ng:inject:builder,[builder],classes_oop,,1.3,A,True,False,False,False,,,unknown,False,Low validation score: 1.3
NG0705,⟵,ng:lifecycle:cleanup,[cleanup],classes_oop,,1.3,A,True,False,False,False,,,unknown,False,Low validation score: 1.3
NG0706,⟶,ng:lifecycle:finalize,[finalize],classes_oop,,1.3,A,True,False,False,False,,,unknown,False,Low validation score: 1.3
NG0707,⟷,ng:lifecycle:copy,[copy],classes_oop,,1.3,A,True,False,False,False,,,unknown,False,Low validation score: 1.3
NG0708,⟸,ng:lifecycle:move,[move],classes_oop,,1.3,A,True,False,False,False,,,unknown,False,Low validation score: 1.3
NG0709,⟹,ng:lifecycle:clone,[clone],classes_oop,,1.3,A,True,False,False,False,,,unknown,False,Low validation score: 1.3
NG0710,⇔,ng:poly:polymorphic,[poly],classes_oop,,1.3,A,True,False,False,False,,,unknown,False,Low validation score: 1.3
NG0711,⟾,ng:method:virtual_call,[vcall],classes_oop,,1.3,A,True,False,False,False,,,unknown,False,Low validation score: 1.3
NG0712,⟿,ng:method:direct_call,[dcall],classes_oop,,1.3,A,True,False,False,False,,,unknown,False,Low validation score: 1.3
NG0713,⤀,ng:method:override,[override],classes_oop,,1.3,A,True,False,False,False,,,unknown,False,Low validation score: 1.3
NG0714,⤁,ng:method:overload,[overload],classes_oop,,1.3,A,True,False,False,False,,,unknown,False,Low validation score: 1.3
NG0715,⤂,ng:method:super_call,[super],classes_oop,,1.3,A,True,False,False,False,,,unknown,False,Low validation score: 1.3
NG0716,⤃,ng:method:this_call,[this],classes_oop,,1.3,A,True,False,False,False,,,unknown,False,Low validation score: 1.3
NG0717,⤄,ng:method:delegate,[delegate],classes_oop,,1.3,A,True,False,False,False,,,unknown,False,Low validation score: 1.3
NG0718,⤅,ng:method:forward,[forward],classes_oop,,1.3,A,True,False,False,False,,,unknown,False,Low validation score: 1.3
NG0719,⚔,ng:pattern:builder,[builder],classes_oop,,1.3,A,True,False,False,False,,,unknown,False,Low validation score: 1.3
NG0720,⚚,ng:pattern:adapter,[adapter],classes_oop,,1.3,A,True,False,False,False,,,unknown,False,Low validation score: 1.3
NG0721,⚞,ng:pattern:facade,[facade],classes_oop,,1.3,A,True,False,False,False,,,unknown,False,Low validation score: 1.3
NG0722,⚨,ng:pattern:mediator,[mediator],classes_oop,,1.3,A,True,False,False,False,,,unknown,False,Low validation score: 1.3
NG0723,⚭,ng:pattern:repository,[repo],classes_oop,,1.3,A,True,False,False,False,,,unknown,False,Low validation score: 1.3
NG0724,⚱,ng:pattern:memento,[memento],classes_oop,,1.3,A,True,False,False,False,,,unknown,False,Low validation score: 1.3
NG0725,⊜,ng:visibility:final,[final],classes_oop,,1.2000000000000002,A,True,False,False,False,,,unknown,False,Low validation score: 1.2000000000000002
NG0726,⤻,ng:inject:singleton,[singleton],classes_oop,,1.2,A,True,False,True,False,[single..],,unknown,False,Low validation score: 1.2; Long fallback: 11 chars
NG0727,⚕,ng:pattern:prototype,[prototype],classes_oop,,1.2,A,True,False,True,False,[protot..],,unknown,False,Low validation score: 1.2; Long fallback: 11 chars
NG0728,⊂,ng:inherit:subclass,[sub],classes_oop,,1.1,A,True,False,False,False,,,unknown,False,Low validation score: 1.1
NG0729,⊇,ng:inherit:extends,[extends],classes_oop,,1.1,A,True,False,False,False,,,unknown,False,Low validation score: 1.1
NG0730,⬟,ng:interface:mixin,[mixin],classes_oop,,1.1,A,True,False,False,False,,,unknown,False,Low validation score: 1.1
NG0731,⬠,ng:interface:trait,[trait],classes_oop,,1.1,A,True,False,False,False,,,unknown,False,Low validation score: 1.1
NG0732,⬢,ng:interface:contract,[contract],classes_oop,,1.1,A,True,False,False,False,,,unknown,False,Low validation score: 1.1
NG0733,⟐,ng:compose:has_a,[has_a],classes_oop,,1.1,A,True,False,False,False,,,unknown,False,Low validation score: 1.1
NG0734,⟑,ng:compose:part_of,[part_of],classes_oop,,1.1,A,True,False,False,False,,,unknown,False,Low validation score: 1.1
NG0735,⟒,ng:compose:contains,[contains],classes_oop,,1.1,A,True,False,False,False,,,unknown,False,Low validation score: 1.1
NG0736,⟕,ng:compose:owns,[owns],classes_oop,,1.1,A,True,False,False,False,,,unknown,False,Low validation score: 1.1
NG0737,⟖,ng:compose:owned_by,[owned_by],classes_oop,,1.1,A,True,False,False,False,,,unknown,False,Low validation score: 1.1
NG0738,⊟,ng:memory:deallocate,[dealloc],classes_oop,,1.1,A,True,False,False,False,,,unknown,False,Low validation score: 1.1
NG0739,⊡,ng:memory:weak_ref,[weak],classes_oop,,1.1,A,True,False,False,False,,,unknown,False,Low validation score: 1.1
NG0740,⊣,ng:memory:shared_ref,[shared],classes_oop,,1.1,A,True,False,False,False,,,unknown,False,Low validation score: 1.1
NG0741,⊥,ng:memory:null_ref,[null],classes_oop,,1.1,A,True,False,False,False,,,unknown,False,Low validation score: 1.1
NG0742,⦀,ng:type:parameter,[T],classes_oop,,1.1,A,True,False,False,False,,,unknown,False,Low validation score: 1.1
NG0743,⦁,ng:type:wildcard,[?],classes_oop,,1.1,A,True,False,False,False,,,unknown,False,Low validation score: 1.1
NG0744,⦂,ng:type:annotation,[:],classes_oop,,1.1,A,True,False,False,False,,,unknown,False,Low validation score: 1.1
NG0745,⦃,ng:type:union,[|],classes_oop,,1.1,A,True,False,False,False,,,unknown,False,Low validation score: 1.1
NG0746,⦄,ng:type:intersection,[&],classes_oop,,1.1,A,True,False,False,False,,,unknown,False,Low validation score: 1.1
NG0747,⦅,ng:type:optional,[?],classes_oop,,1.1,A,True,False,False,False,,,unknown,False,Low validation score: 1.1
NG0748,⦆,ng:type:nullable,[?],classes_oop,,1.1,A,True,False,False,False,,,unknown,False,Low validation score: 1.1
NG0749,⦇,ng:type:array,[[]],classes_oop,,1.1,A,True,False,False,False,,,unknown,False,Low validation score: 1.1
NG0750,⚦,ng:pattern:template,[template],classes_oop,,1.1,A,True,False,False,False,,,unknown,False,Low validation score: 1.1
NG0751,∌,ng:inherit:not_contains,[!contains],classes_oop,,1.0,A,True,False,True,False,[!conta..],,unknown,False,Low validation score: 1.0; Long fallback: 11 chars
NG0752,⬡,ng:interface:signature,[signature],classes_oop,,1.0,A,True,False,True,False,[signat..],,unknown,False,Low validation score: 1.0; Long fallback: 11 chars
NG0753,⟓,ng:compose:aggregates,[aggregates],classes_oop,,1.0,A,True,False,True,False,[aggreg..],,unknown,False,Low validation score: 1.0; Long fallback: 12 chars
NG0754,⟔,ng:compose:composed_of,[composed_of],classes_oop,,1.0,A,True,False,True,False,[CO],,unknown,False,Low validation score: 1.0; Long fallback: 13 chars
NG0755,⟗,ng:compose:delegates,[delegates],classes_oop,,1.0,A,True,False,True,False,[delega..],,unknown,False,Low validation score: 1.0; Long fallback: 11 chars
NG0756,⟦,ng:collection:list,[list],data_structures,,1.5000000000000002,A,True,False,False,False,,,unknown,False,Low validation score: 1.5000000000000002
NG0757,⟧,ng:collection:list_end,[/list],data_structures,,1.5000000000000002,A,True,False,False,False,,,unknown,False,Low validation score: 1.5000000000000002
NG0758,⋅,ng:tree:child,[child],data_structures,,1.3,A,True,False,False,False,,,unknown,False,Low validation score: 1.3
NG0759,⋖,ng:tree:bst,[bst],data_structures,,1.3,A,True,False,False,False,,,unknown,False,Low validation score: 1.3
NG0760,⋗,ng:tree:avl,[avl],data_structures,,1.3,A,True,False,False,False,,,unknown,False,Low validation score: 1.3
NG0761,⌀,ng:algo:quick_sort,[quick],data_structures,,1.3,A,True,False,False,False,,,unknown,False,Low validation score: 1.3
NG0762,⌁,ng:algo:merge_sort,[merge],data_structures,,1.3,A,True,False,False,False,,,unknown,False,Low validation score: 1.3
NG0763,⌅,ng:algo:radix_sort,[radix],data_structures,,1.3,A,True,False,False,False,,,unknown,False,Low validation score: 1.3
NG0764,⌌,ng:algo:jump_search,[jump],data_structures,,1.3,A,True,False,False,False,,,unknown,False,Low validation score: 1.3
NG0765,⦈,ng:collection:map_end,[/map],data_structures,,1.2000000000000002,A,True,False,False,False,,,unknown,False,Low validation score: 1.2000000000000002
NG0766,⦉,ng:collection:dict,[dict],data_structures,,1.2000000000000002,A,True,False,False,False,,,unknown,False,Low validation score: 1.2000000000000002
NG0767,⦊,ng:collection:dict_end,[/dict],data_structures,,1.2000000000000002,A,True,False,False,False,,,unknown,False,Low validation score: 1.2000000000000002
NG0768,⦋,ng:collection:queue,[queue],data_structures,,1.2000000000000002,A,True,False,False,False,,,unknown,False,Low validation score: 1.2000000000000002
NG0769,⦌,ng:collection:queue_end,[/queue],data_structures,,1.2000000000000002,A,True,False,False,False,,,unknown,False,Low validation score: 1.2000000000000002
NG0770,⌂,ng:algo:heap_sort,[heap_sort],data_structures,,1.2,A,True,False,True,False,[HS],,unknown,False,Low validation score: 1.2; Long fallback: 11 chars
NG0771,⌃,ng:algo:insertion_sort,[insertion],data_structures,,1.2,A,True,False,True,False,[insert..],,unknown,False,Low validation score: 1.2; Long fallback: 11 chars
NG0772,⌄,ng:algo:selection_sort,[selection],data_structures,,1.2,A,True,False,True,False,[select..],,unknown,False,Low validation score: 1.2; Long fallback: 11 chars
NG0773,⊩,ng:collection:map_op,[map],data_structures,,1.1,A,True,False,False,False,,,unknown,False,Low validation score: 1.1
NG0774,⊫,ng:collection:fold,[fold],data_structures,,1.1,A,True,False,False,False,,,unknown,False,Low validation score: 1.1
NG0775,⊭,ng:collection:full,[full],data_structures,,1.1,A,True,False,False,False,,,unknown,False,Low validation score: 1.1
NG0776,⋌,ng:tree:search,[search],data_structures,,1.1,A,True,False,False,False,,,unknown,False,Low validation score: 1.1
NG0777,⋍,ng:tree:find,[find],data_structures,,1.1,A,True,False,False,False,,,unknown,False,Low validation score: 1.1
NG0778,⋐,ng:tree:left_child,[left],data_structures,,1.1,A,True,False,False,False,,,unknown,False,Low validation score: 1.1
NG0779,⋑,ng:tree:right_child,[right],data_structures,,1.1,A,True,False,False,False,,,unknown,False,Low validation score: 1.1
NG0780,⋒,ng:tree:height,[height],data_structures,,1.1,A,True,False,False,False,,,unknown,False,Low validation score: 1.1
NG0781,⋚,ng:tree:heap,[heap],data_structures,,1.1,A,True,False,False,False,,,unknown,False,Low validation score: 1.1
NG0782,⋠,ng:graph:edge,[edge],data_structures,,1.1,A,True,False,False,False,,,unknown,False,Low validation score: 1.1
NG0783,⋡,ng:graph:weight,[weight],data_structures,,1.1,A,True,False,False,False,,,unknown,False,Low validation score: 1.1
NG0784,⋤,ng:graph:weighted,[weighted],data_structures,,1.1,A,True,False,False,False,,,unknown,False,Low validation score: 1.1
NG0785,⋪,ng:graph:prim,[prim],data_structures,,1.1,A,True,False,False,False,,,unknown,False,Low validation score: 1.1
NG0786,⋭,ng:graph:cycle_detection,[cycle],data_structures,,1.1,A,True,False,False,False,,,unknown,False,Low validation score: 1.1
NG0787,⋶,ng:graph:degree,[degree],data_structures,,1.1,A,True,False,False,False,,,unknown,False,Low validation score: 1.1
NG0788,⋻,ng:graph:clique,[clique],data_structures,,1.1,A,True,False,False,False,,,unknown,False,Low validation score: 1.1
NG0789,⋽,ng:graph:matching,[matching],data_structures,,1.1,A,True,False,False,False,,,unknown,False,Low validation score: 1.1
NG0790,⌆,ng:algo:linear_search,[linear],data_structures,,1.1,A,True,False,False,False,,,unknown,False,Low validation score: 1.1
NG0791,⌇,ng:algo:binary_search,[binary],data_structures,,1.1,A,True,False,False,False,,,unknown,False,Low validation score: 1.1
NG0792,⌋,ng:algo:ternary_search,[ternary],data_structures,,1.1,A,True,False,False,False,,,unknown,False,Low validation score: 1.1
NG0793,⌍,ng:algo:hash_search,[hash],data_structures,,1.1,A,True,False,False,False,,,unknown,False,Low validation score: 1.1
NG0794,⌎,ng:algo:dp,[dp],data_structures,,1.1,A,True,False,False,False,,,unknown,False,Low validation score: 1.1
NG0795,⌐,ng:algo:tabulation,[tab],data_structures,,1.1,A,True,False,False,False,,,unknown,False,Low validation score: 1.1
NG0796,⌑,ng:algo:optimal_substructure,[optimal],data_structures,,1.1,A,True,False,False,False,,,unknown,False,Low validation score: 1.1
NG0797,⌒,ng:algo:overlapping_subproblems,[overlap],data_structures,,1.1,A,True,False,False,False,,,unknown,False,Low validation score: 1.1
NG0798,⌓,ng:algo:knapsack,[knapsack],data_structures,,1.1,A,True,False,False,False,,,unknown,False,Low validation score: 1.1
NG0799,⌕,ng:algo:edit_distance,[edit],data_structures,,1.1,A,True,False,False,False,,,unknown,False,Low validation score: 1.1
NG0800,⌖,ng:algo:greedy,[greedy],data_structures,,1.1,A,True,False,False,False,,,unknown,False,Low validation score: 1.1
NG0801,⌗,ng:algo:activity_selection,[activity],data_structures,,1.1,A,True,False,False,False,,,unknown,False,Low validation score: 1.1
NG0802,⌙,ng:algo:huffman,[huffman],data_structures,,1.1,A,True,False,False,False,,,unknown,False,Low validation score: 1.1
NG0803,⌚,ng:algo:job_scheduling,[job],data_structures,,1.1,A,True,False,False,False,,,unknown,False,Low validation score: 1.1
NG0804,⌜,ng:algo:minimum_spanning_tree,[mst],data_structures,,1.1,A,True,False,False,False,,,unknown,False,Low validation score: 1.1
NG0805,⌝,ng:algo:single_source_shortest_path,[sssp],data_structures,,1.1,A,True,False,False,False,,,unknown,False,Low validation score: 1.1
NG0806,⋕,ng:tree:rotate_right,[rot_right],data_structures,,1.0,A,True,False,True,False,[RR],,unknown,False,Low validation score: 1.0; Long fallback: 11 chars
NG0807,⋣,ng:graph:undirected,[undirected],data_structures,,1.0,A,True,False,True,False,[undire..],,unknown,False,Low validation score: 1.0; Long fallback: 12 chars
NG0808,⋥,ng:graph:unweighted,[unweighted],data_structures,,1.0,A,True,False,True,False,[unweig..],,unknown,False,Low validation score: 1.0; Long fallback: 12 chars
NG0809,⋷,ng:graph:in_degree,[in_degree],data_structures,,1.0,A,True,False,True,False,[ID],,unknown,False,Low validation score: 1.0; Long fallback: 11 chars
NG0810,⋸,ng:graph:out_degree,[out_degree],data_structures,,1.0,A,True,False,True,False,[OD],,unknown,False,Low validation score: 1.0; Long fallback: 12 chars
NG0811,⋼,ng:graph:independent_set,[independent],data_structures,,1.0,A,True,False,True,False,[indepe..],,unknown,False,Low validation score: 1.0; Long fallback: 13 chars
NG0812,⌈,ng:algo:interpolation_search,[interpolation],data_structures,,1.0,A,True,False,True,False,[interp..],,unknown,False,Low validation score: 1.0; Long fallback: 15 chars
NG0813,⌉,ng:algo:exponential_search,[exponential],data_structures,,1.0,A,True,False,True,False,[expone..],,unknown,False,Low validation score: 1.0; Long fallback: 13 chars
NG0814,⌊,ng:algo:fibonacci_search,[fibonacci],data_structures,,1.0,A,True,False,True,False,[fibona..],,unknown,False,Low validation score: 1.0; Long fallback: 11 chars
NG0815,⌘,ng:algo:fractional_knapsack,[frac_knapsack],data_structures,,1.0,A,True,False,True,False,[FK],,unknown,False,Low validation score: 1.0; Long fallback: 15 chars
NG0816,☁,ng:final:cloud,[cloud],final_completion,,1.0,A,True,False,False,False,,,unknown,False,Low validation score: 1.0
NG0817,☃,ng:final:snowman,[snowman],final_completion,,1.0,A,True,False,False,False,,,unknown,False,Low validation score: 1.0
NG0818,★,ng:final:star,[star],final_completion,,1.0,A,True,False,False,False,,,unknown,False,Low validation score: 1.0
NG0819,☆,ng:final:star_outline,[star_outline],final_completion,,1.0,A,True,False,True,False,[SO],,unknown,False,Low validation score: 1.0; Long fallback: 14 chars
NG0820,☉,ng:final:sun_rays,[sun_rays],final_completion,,1.0,A,True,False,False,False,,,unknown,False,Low validation score: 1.0
NG0821,☋,ng:final:022,[final22],final_completion,,1.0,A,True,False,False,False,,,unknown,False,Low validation score: 1.0
NG0822,☌,ng:final:023,[final23],final_completion,,1.0,A,True,False,False,False,,,unknown,False,Low validation score: 1.0
NG0823,☐,ng:final:027,[final27],final_completion,,1.0,A,True,False,False,False,,,unknown,False,Low validation score: 1.0
NG0824,☒,ng:final:029,[final29],final_completion,,1.0,A,True,False,False,False,,,unknown,False,Low validation score: 1.0
NG0825,☓,ng:final:030,[final30],final_completion,,1.0,A,True,False,False,False,,,unknown,False,Low validation score: 1.0
NG0826,☔,ng:final:031,[final31],final_completion,,1.0,A,True,False,False,False,,,unknown,False,Low validation score: 1.0
NG0827,☖,ng:final:033,[final33],final_completion,,1.0,A,True,False,False,False,,,unknown,False,Low validation score: 1.0
NG0828,☚,ng:final:037,[final37],final_completion,,1.0,A,True,False,False,False,,,unknown,False,Low validation score: 1.0
NG0829,☣,ng:final:046,[final46],final_completion,,1.0,A,True,False,False,False,,,unknown,False,Low validation score: 1.0
NG0830,☪,ng:final:053,[final53],final_completion,,1.0,A,True,False,False,False,,,unknown,False,Low validation score: 1.0
NG0831,☫,ng:final:054,[final54],final_completion,,1.0,A,True,False,False,False,,,unknown,False,Low validation score: 1.0
NG0832,☱,ng:final:060,[final60],final_completion,,1.0,A,True,False,False,False,,,unknown,False,Low validation score: 1.0
NG0833,☲,ng:final:061,[final61],final_completion,,1.0,A,True,False,False,False,,,unknown,False,Low validation score: 1.0
NG0834,☳,ng:final:062,[final62],final_completion,,1.0,A,True,False,False,False,,,unknown,False,Low validation score: 1.0
NG0835,☷,ng:final:066,[final66],final_completion,,1.0,A,True,False,False,False,,,unknown,False,Low validation score: 1.0
NG0836,☹,ng:final:068,[final68],final_completion,,1.0,A,True,False,False,False,,,unknown,False,Low validation score: 1.0
NG0837,☼,ng:final:071,[final71],final_completion,,1.0,A,True,False,False,False,,,unknown,False,Low validation score: 1.0
NG0838,♀,ng:final:075,[final75],final_completion,,1.0,A,True,False,False,False,,,unknown,False,Low validation score: 1.0
NG0839,♆,ng:final:081,[final81],final_completion,,1.0,A,True,False,False,False,,,unknown,False,Low validation score: 1.0
NG0840,♇,ng:final:082,[final82],final_completion,,1.0,A,True,False,False,False,,,unknown,False,Low validation score: 1.0
NG0841,♌,ng:final:087,[final87],final_completion,,1.0,A,True,False,False,False,,,unknown,False,Low validation score: 1.0
NG0842,♍,ng:final:088,[final88],final_completion,,1.0,A,True,False,False,False,,,unknown,False,Low validation score: 1.0
NG0843,♎,ng:final:089,[final89],final_completion,,1.0,A,True,False,False,False,,,unknown,False,Low validation score: 1.0
NG0844,♐,ng:final:091,[final91],final_completion,,1.0,A,True,False,False,False,,,unknown,False,Low validation score: 1.0
NG0845,♓,ng:final:094,[final94],final_completion,,1.0,A,True,False,False,False,,,unknown,False,Low validation score: 1.0
NG0846,♕,ng:final:096,[final96],final_completion,,1.0,A,True,False,False,False,,,unknown,False,Low validation score: 1.0
NG0847,♖,ng:final:097,[final97],final_completion,,1.0,A,True,False,False,False,,,unknown,False,Low validation score: 1.0
NG0848,♚,ng:final:101,[final101],final_completion,,1.0,A,True,False,False,False,,,unknown,False,Low validation score: 1.0
NG0849,♜,ng:final:103,[final103],final_completion,,1.0,A,True,False,False,False,,,unknown,False,Low validation score: 1.0
NG0850,♟,ng:final:106,[final106],final_completion,,1.0,A,True,False,False,False,,,unknown,False,Low validation score: 1.0
NG0851,♢,ng:final:109,[final109],final_completion,,1.0,A,True,False,False,False,,,unknown,False,Low validation score: 1.0
NG0852,♩,ng:final:116,[final116],final_completion,,1.0,A,True,False,False,False,,,unknown,False,Low validation score: 1.0
NG0853,♪,ng:final:117,[final117],final_completion,,1.0,A,True,False,False,False,,,unknown,False,Low validation score: 1.0
NG0854,♱,ng:final:124,[final124],final_completion,,1.0,A,True,False,False,False,,,unknown,False,Low validation score: 1.0
NG0855,♲,ng:final:125,[final125],final_completion,,1.0,A,True,False,False,False,,,unknown,False,Low validation score: 1.0
NG0856,♳,ng:final:126,[final126],final_completion,,1.0,A,True,False,False,False,,,unknown,False,Low validation score: 1.0
NG0857,♴,ng:final:127,[final127],final_completion,,1.0,A,True,False,False,False,,,unknown,False,Low validation score: 1.0
NG0858,♵,ng:final:128,[final128],final_completion,,1.0,A,True,False,False,False,,,unknown,False,Low validation score: 1.0
NG0859,♸,ng:final:131,[final131],final_completion,,1.0,A,True,False,False,False,,,unknown,False,Low validation score: 1.0
NG0860,♹,ng:final:132,[final132],final_completion,,1.0,A,True,False,False,False,,,unknown,False,Low validation score: 1.0
NG0861,♺,ng:final:133,[final133],final_completion,,1.0,A,True,False,False,False,,,unknown,False,Low validation score: 1.0
NG0862,♼,ng:final:135,[final135],final_completion,,1.0,A,True,False,False,False,,,unknown,False,Low validation score: 1.0
NG0863,⚃,ng:final:142,[final142],final_completion,,1.0,A,True,False,False,False,,,unknown,False,Low validation score: 1.0
NG0864,⚄,ng:final:143,[final143],final_completion,,1.0,A,True,False,False,False,,,unknown,False,Low validation score: 1.0
NG0865,⚇,ng:final:146,[final146],final_completion,,1.0,A,True,False,False,False,,,unknown,False,Low validation score: 1.0
NG0866,⚊,ng:final:149,[final149],final_completion,,1.0,A,True,False,False,False,,,unknown,False,Low validation score: 1.0
NG0867,⚎,ng:final:153,[final153],final_completion,,1.0,A,True,False,False,False,,,unknown,False,Low validation score: 1.0
NG0868,⚏,ng:final:154,[final154],final_completion,,1.0,A,True,False,False,False,,,unknown,False,Low validation score: 1.0
NG0869,⚐,ng:final:155,[final155],final_completion,,1.0,A,True,False,False,False,,,unknown,False,Low validation score: 1.0
NG0870,⚲,ng:final:189,[final189],final_completion,,1.0,A,True,False,False,False,,,unknown,False,Low validation score: 1.0
NG0871,⚴,ng:final:191,[final191],final_completion,,1.0,A,True,False,False,False,,,unknown,False,Low validation score: 1.0
NG0872,⚵,ng:final:192,[final192],final_completion,,1.0,A,True,False,False,False,,,unknown,False,Low validation score: 1.0
NG0873,⚷,ng:final:194,[final194],final_completion,,1.0,A,True,False,False,False,,,unknown,False,Low validation score: 1.0
NG0874,⚸,ng:final:195,[final195],final_completion,,1.0,A,True,False,False,False,,,unknown,False,Low validation score: 1.0
NG0875,⚺,ng:final:197,[final197],final_completion,,1.0,A,True,False,False,False,,,unknown,False,Low validation score: 1.0
NG0876,⚾,ng:final:201,[final201],final_completion,,1.0,A,True,False,False,False,,,unknown,False,Low validation score: 1.0
NG0877,⚿,ng:final:202,[final202],final_completion,,1.0,A,True,False,False,False,,,unknown,False,Low validation score: 1.0
NG0878,⛀,ng:final:203,[final203],final_completion,,1.0,A,True,False,False,False,,,unknown,False,Low validation score: 1.0
NG0879,⛃,ng:final:206,[final206],final_completion,,1.0,A,True,False,False,False,,,unknown,False,Low validation score: 1.0
NG0880,⛄,ng:final:207,[final207],final_completion,,1.0,A,True,False,False,False,,,unknown,False,Low validation score: 1.0
NG0881,⛅,ng:final:208,[final208],final_completion,,1.0,A,True,False,False,False,,,unknown,False,Low validation score: 1.0
NG0882,🟖,ng:advanced_coding:ast_transform_fn,[ASTTRANSFORMFN],advanced_coding,ast_transform_fn,95.0,A,False,True,True,False,[ASTTRA..],,emoji_extended,False,Risky Unicode range: emoji_extended; Long fallback: 16 chars
NG0883,⩒,ng:advanced_coding:syntax_tree_sys,[SYNTAXTREESYS],advanced_coding,syntax_tree_sys,95.0,C,False,False,True,False,[SYNTAX..],,safe,False,Long fallback: 15 chars
NG0884,🡡,ng:advanced_coding:emit_fn,[EMITFN],advanced_coding,emit_fn,95.0,B,False,True,False,False,,,emoji_extended,False,Risky Unicode range: emoji_extended
NG0885,⣖,ng:advanced_coding:codegen_op,[CODEGENOP],advanced_coding,codegen_op,95.0,C,False,False,True,False,[CODEGE..],,safe,False,Long fallback: 11 chars
NG0886,🌅,ng:advanced_coding:meta_meta,[METAMETA],advanced_coding,meta_meta,95.0,B,False,True,False,False,,,emoji_extended,False,Risky Unicode range: emoji_extended
NG0887,😏,ng:advanced_coding:introspect_proc,[INTROSPECTPROC],advanced_coding,introspect_proc,95.0,A,False,True,True,False,[INTROS..],,emoji_extended,False,Risky Unicode range: emoji_extended; Long fallback: 16 chars
NG0888,⢠,ng:advanced_coding:introspection,[INTROSPECTION],advanced_coding,introspection,95.0,C,False,False,True,False,[INTROS..],,safe,False,Long fallback: 15 chars
NG0889,⥮,ng:advanced_coding:introspection_fn,[INTROSPECTIONFN],advanced_coding,introspection_fn,95.0,C,False,False,True,False,[INTROS..],,safe,False,Long fallback: 17 chars
NG0890,🠲,ng:advanced_coding:dynamicdispatch_fn,[DYNAMICDISPATCHFN],advanced_coding,dynamicdispatch_fn,95.0,A,False,True,True,False,[DYNAMI..],,emoji_extended,False,Risky Unicode range: emoji_extended; Long fallback: 19 chars
NG0891,✒,ng:advanced_coding:dynamicdispatch_fn_1,[DYNAMICDISPATCHFN1],advanced_coding,dynamicdispatch_fn_1,95.0,B,False,False,True,True,[DYNAMI..],dynamicdispatch_fn_1,safe,False,Long fallback: 20 chars; Generic numbered name
NG0892,🟙,ng:advanced_coding:metaobjects_fn,[METAOBJECTSFN],advanced_coding,metaobjects_fn,95.0,A,False,True,True,False,[METAOB..],,emoji_extended,False,Risky Unicode range: emoji_extended; Long fallback: 15 chars
NG0893,⠏,ng:advanced_coding:metaobjects,[METAOBJECTS],advanced_coding,metaobjects,95.0,C,False,False,True,False,[METAOB..],,safe,False,Long fallback: 13 chars
NG0894,🙈,ng:advanced_coding:bytecode,[BYTECODE],advanced_coding,bytecode,95.0,B,False,True,False,False,,,emoji_extended,False,Risky Unicode range: emoji_extended
NG0895,🜿,ng:advanced_coding:bytecode_sys,[BYTECODESYS],advanced_coding,bytecode_sys,95.0,A,False,True,True,False,[BYTECO..],,emoji_extended,False,Risky Unicode range: emoji_extended; Long fallback: 13 chars
NG0896,⨪,ng:advanced_coding:jitcompilation_core,[JITCOMPILATIONCORE],advanced_coding,jitcompilation_core,95.0,C,False,False,True,False,[JITCOM..],,safe,False,Long fallback: 20 chars
NG0897,⨄,ng:advanced_coding:jitcompilation_meta,[JITCOMPILATIONMETA],advanced_coding,jitcompilation_meta,95.0,C,False,False,True,False,[JITCOM..],,safe,False,Long fallback: 20 chars
NG0898,🟈,ng:advanced_coding:garbagecollection,[GARBAGECOLLECTION],advanced_coding,garbagecollection,95.0,A,False,True,True,False,[GARBAG..],,emoji_extended,False,Risky Unicode range: emoji_extended; Long fallback: 19 chars
NG0899,🞊,ng:advanced_coding:garbagecollection_1,[GARBAGECOLLECTION1],advanced_coding,garbagecollection_1,95.0,A,False,True,True,True,[GARBAG..],garbagecollection_1,emoji_extended,False,Risky Unicode range: emoji_extended; Long fallback: 20 chars; Generic numbered name
NG0900,🥻,ng:advanced_coding:memorypools_meta,[MEMORYPOOLSMETA],advanced_coding,memorypools_meta,95.0,A,False,True,True,False,[MEMORY..],,emoji_extended,False,Risky Unicode range: emoji_extended; Long fallback: 17 chars
NG0901,⭭,ng:advanced_coding:memorypools,[MEMORYPOOLS],advanced_coding,memorypools,95.0,C,False,False,True,False,[MEMORY..],,safe,False,Long fallback: 13 chars
NG0902,🢝,ng:advanced_coding:stackframes,[STACKFRAMES],advanced_coding,stackframes,95.0,A,False,True,True,False,[STACKF..],,emoji_extended,False,Risky Unicode range: emoji_extended; Long fallback: 13 chars
NG0903,🤃,ng:advanced_coding:stackframes_fn,[STACKFRAMESFN],advanced_coding,stackframes_fn,95.0,A,False,True,True,False,[STACKF..],,emoji_extended,False,Risky Unicode range: emoji_extended; Long fallback: 15 chars
NG0904,🜼,ng:advanced_coding:heapmanagement_sys,[HEAPMANAGEMENTSYS],advanced_coding,heapmanagement_sys,95.0,A,False,True,True,False,[HEAPMA..],,emoji_extended,False,Risky Unicode range: emoji_extended; Long fallback: 19 chars
NG0905,🖼,ng:advanced_coding:heapmanagement_proc,[HEAPMANAGEMENTPROC],advanced_coding,heapmanagement_proc,95.0,A,False,True,True,False,[HEAPMA..],,emoji_extended,False,Risky Unicode range: emoji_extended; Long fallback: 20 chars
NG0906,❣,ng:advanced_coding:coroutines_fn,[COROUTINESFN],advanced_coding,coroutines_fn,95.0,C,False,False,True,False,[COROUT..],,safe,False,Long fallback: 14 chars
NG0907,⠌,ng:advanced_coding:coroutines_op,[COROUTINESOP],advanced_coding,coroutines_op,95.0,C,False,False,True,False,[COROUT..],,safe,False,Long fallback: 14 chars
NG0908,🞞,ng:advanced_coding:generators_fn,[GENERATORSFN],advanced_coding,generators_fn,95.0,A,False,True,True,False,[GENERA..],,emoji_extended,False,Risky Unicode range: emoji_extended; Long fallback: 14 chars
NG0909,🡵,ng:advanced_coding:generators_sys,[GENERATORSSYS],advanced_coding,generators_sys,95.0,A,False,True,True,False,[GENERA..],,emoji_extended,False,Risky Unicode range: emoji_extended; Long fallback: 15 chars
NG0910,⢓,ng:advanced_coding:iterators_sys,[ITERATORSSYS],advanced_coding,iterators_sys,95.0,C,False,False,True,False,[ITERAT..],,safe,False,Long fallback: 14 chars
NG0911,⬹,ng:advanced_coding:iterators_ctrl,[ITERATORSCTRL],advanced_coding,iterators_ctrl,95.0,C,False,False,True,False,[ITERAT..],,safe,False,Long fallback: 15 chars
NG0912,✝,ng:advanced_coding:comprehensions_fn,[COMPREHENSIONSFN],advanced_coding,comprehensions_fn,95.0,C,False,False,True,False,[COMPRE..],,safe,False,Long fallback: 18 chars
NG0913,🛩,ng:advanced_coding:comprehensions_sys,[COMPREHENSIONSSYS],advanced_coding,comprehensions_sys,95.0,A,False,True,True,False,[COMPRE..],,emoji_extended,False,Risky Unicode range: emoji_extended; Long fallback: 19 chars
NG0914,🞴,ng:advanced_coding:decorators_fn,[DECORATORSFN],advanced_coding,decorators_fn,95.0,A,False,True,True,False,[DECORA..],,emoji_extended,False,Risky Unicode range: emoji_extended; Long fallback: 14 chars
NG0915,⧟,ng:advanced_coding:decorators_ctrl,[DECORATORSCTRL],advanced_coding,decorators_ctrl,95.0,C,False,False,True,False,[DECORA..],,safe,False,Long fallback: 16 chars
NG0916,⥿,ng:advanced_coding:contextmanagers,[CONTEXTMANAGERS],advanced_coding,contextmanagers,95.0,C,False,False,True,False,[CONTEX..],,safe,False,Long fallback: 17 chars
NG0917,⬫,ng:advanced_coding:contextmanagers_sys,[CONTEXTMANAGERSSYS],advanced_coding,contextmanagers_sys,95.0,C,False,False,True,False,[CONTEX..],,safe,False,Long fallback: 20 chars
NG0918,🜏,ng:advanced_coding:descriptors_op,[DESCRIPTORSOP],advanced_coding,descriptors_op,95.0,A,False,True,True,False,[DESCRI..],,emoji_extended,False,Risky Unicode range: emoji_extended; Long fallback: 15 chars
NG0919,🜦,ng:advanced_coding:descriptors_core,[DESCRIPTORSCORE],advanced_coding,descriptors_core,95.0,A,False,True,True,False,[DESCRI..],,emoji_extended,False,Risky Unicode range: emoji_extended; Long fallback: 17 chars
NG0920,⠣,ng:advanced_coding:metaclasses_sys,[METACLASSESSYS],advanced_coding,metaclasses_sys,95.0,C,False,False,True,False,[METACL..],,safe,False,Long fallback: 16 chars
NG0921,📕,ng:advanced_coding:metaclasses_proc,[METACLASSESPROC],advanced_coding,metaclasses_proc,95.0,A,False,True,True,False,[METACL..],,emoji_extended,False,Risky Unicode range: emoji_extended; Long fallback: 17 chars
NG0922,⧡,ng:advanced_coding:metaclasses,[METACLASSES],advanced_coding,metaclasses,95.0,C,False,False,True,False,[METACL..],,safe,False,Long fallback: 13 chars
NG0923,⯮,ng:advanced_coding:metaclasses_sys_1,[METACLASSESSYS1],advanced_coding,metaclasses_sys_1,95.0,B,False,False,True,True,[METACL..],metaclasses_sys_1,safe,False,Long fallback: 17 chars; Generic numbered name
NG0924,🎣,ng:advanced_coding:metaclasses_ctrl,[METACLASSESCTRL],advanced_coding,metaclasses_ctrl,95.0,A,False,True,True,False,[METACL..],,emoji_extended,False,Risky Unicode range: emoji_extended; Long fallback: 17 chars
NG0925,🎫,ng:advanced_coding:metaclasses_meta,[METACLASSESMETA],advanced_coding,metaclasses_meta,95.0,A,False,True,True,False,[METACL..],,emoji_extended,False,Risky Unicode range: emoji_extended; Long fallback: 17 chars
NG0926,⨊,ng:advanced_coding:metaclasses_op,[METACLASSESOP],advanced_coding,metaclasses_op,95.0,C,False,False,True,False,[METACL..],,safe,False,Long fallback: 15 chars
NG0927,⫙,ng:advanced_coding:metaclasses_fn,[METACLASSESFN],advanced_coding,metaclasses_fn,95.0,C,False,False,True,False,[METACL..],,safe,False,Long fallback: 15 chars
NG0928,🝆,ng:advanced_coding:metaclasses_1,[METACLASSES1],advanced_coding,metaclasses_1,95.0,A,False,True,True,True,[METACL..],metaclasses_1,emoji_extended,False,Risky Unicode range: emoji_extended; Long fallback: 14 chars; Generic numbered name
NG0929,⫏,ng:advanced_coding:metaclasses_sys_2,[METACLASSESSYS2],advanced_coding,metaclasses_sys_2,95.0,B,False,False,True,True,[METACL..],metaclasses_sys_2,safe,False,Long fallback: 17 chars; Generic numbered name
NG0930,🥅,ng:advanced_coding:metaclasses_ctrl_1,[METACLASSESCTRL1],advanced_coding,metaclasses_ctrl_1,95.0,A,False,True,True,True,[METACL..],metaclasses_ctrl_1,emoji_extended,False,Risky Unicode range: emoji_extended; Long fallback: 18 chars; Generic numbered name
NG0931,😇,ng:advanced_coding:metaclasses_sys_3,[METACLASSESSYS3],advanced_coding,metaclasses_sys_3,95.0,A,False,True,True,True,[METACL..],metaclasses_sys_3,emoji_extended,False,Risky Unicode range: emoji_extended; Long fallback: 17 chars; Generic numbered name
NG0932,⦎,ng:advanced_coding:parse_tree_fn,[PARSETREEFN],advanced_coding,parse_tree_fn,95.0,C,False,False,True,False,[PARSET..],,safe,False,Long fallback: 13 chars
NG0933,🜲,ng:advanced_coding:parse_tree_ctrl,[PARSETREECTRL],advanced_coding,parse_tree_ctrl,95.0,A,False,True,True,False,[PARSET..],,emoji_extended,False,Risky Unicode range: emoji_extended; Long fallback: 15 chars
NG0934,🥓,ng:advanced_coding:ast_node_core,[ASTNODECORE],advanced_coding,ast_node_core,95.0,A,False,True,True,False,[ASTNOD..],,emoji_extended,False,Risky Unicode range: emoji_extended; Long fallback: 13 chars
NG0935,🝼,ng:advanced_coding:parse_tree_sys,[PARSETREESYS],advanced_coding,parse_tree_sys,95.0,A,False,True,True,False,[PARSET..],,emoji_extended,False,Risky Unicode range: emoji_extended; Long fallback: 14 chars
NG0936,🌒,ng:advanced_coding:syntax_tree_fn,[SYNTAXTREEFN],advanced_coding,syntax_tree_fn,95.0,A,False,True,True,False,[SYNTAX..],,emoji_extended,False,Risky Unicode range: emoji_extended; Long fallback: 14 chars
NG0937,⣋,ng:advanced_coding:ast_node_fn,[ASTNODEFN],advanced_coding,ast_node_fn,95.0,C,False,False,True,False,[ASTNOD..],,safe,False,Long fallback: 11 chars
NG0938,⬈,ng:advanced_coding:parse_tree,[PARSETREE],advanced_coding,parse_tree,95.0,C,False,False,True,False,[PARSET..],,safe,False,Long fallback: 11 chars
NG0939,🜧,ng:advanced_coding:syntax_tree_op,[SYNTAXTREEOP],advanced_coding,syntax_tree_op,95.0,A,False,True,True,False,[SYNTAX..],,emoji_extended,False,Risky Unicode range: emoji_extended; Long fallback: 14 chars
NG0940,🎁,ng:advanced_coding:parse_tree_meta,[PARSETREEMETA],advanced_coding,parse_tree_meta,95.0,A,False,True,True,False,[PARSET..],,emoji_extended,False,Risky Unicode range: emoji_extended; Long fallback: 15 chars
NG0941,⢐,ng:advanced_coding:ast_node_op,[ASTNODEOP],advanced_coding,ast_node_op,95.0,C,False,False,True,False,[ASTNOD..],,safe,False,Long fallback: 11 chars
NG0942,🚵,ng:advanced_coding:ast_transform_meta,[ASTTRANSFORMMETA],advanced_coding,ast_transform_meta,95.0,A,False,True,True,False,[ASTTRA..],,emoji_extended,False,Risky Unicode range: emoji_extended; Long fallback: 18 chars
NG0943,🚙,ng:advanced_coding:ast_node,[ASTNODE],advanced_coding,ast_node,95.0,B,False,True,False,False,,,emoji_extended,False,Risky Unicode range: emoji_extended
NG0944,🚡,ng:advanced_coding:codegen_ctrl,[CODEGENCTRL],advanced_coding,codegen_ctrl,95.0,A,False,True,True,False,[CODEGE..],,emoji_extended,False,Risky Unicode range: emoji_extended; Long fallback: 13 chars
NG0945,🛸,ng:advanced_coding:codegen_op_1,[CODEGENOP1],advanced_coding,codegen_op_1,95.0,A,False,True,True,True,[CODEGE..],codegen_op_1,emoji_extended,False,Risky Unicode range: emoji_extended; Long fallback: 12 chars; Generic numbered name
NG0946,⤧,ng:advanced_coding:emit_ctrl,[EMITCTRL],advanced_coding,emit_ctrl,95.0,OK,False,False,False,False,,,safe,False,
NG0947,🚏,ng:advanced_coding:emit_core,[EMITCORE],advanced_coding,emit_core,95.0,B,False,True,False,False,,,emoji_extended,False,Risky Unicode range: emoji_extended
NG0948,⦛,ng:advanced_coding:emit_ctrl_1,[EMITCTRL1],advanced_coding,emit_ctrl_1,95.0,B,False,False,True,True,[EMITCT..],emit_ctrl_1,safe,False,Long fallback: 11 chars; Generic numbered name
NG0949,⡗,ng:advanced_coding:generate_sys,[GENERATESYS],advanced_coding,generate_sys,95.0,C,False,False,True,False,[GENERA..],,safe,False,Long fallback: 13 chars
NG0950,🝝,ng:advanced_coding:generate_proc,[GENERATEPROC],advanced_coding,generate_proc,95.0,A,False,True,True,False,[GENERA..],,emoji_extended,False,Risky Unicode range: emoji_extended; Long fallback: 14 chars
NG0951,🠮,ng:advanced_coding:codegen_proc,[CODEGENPROC],advanced_coding,codegen_proc,95.0,A,False,True,True,False,[CODEGE..],,emoji_extended,False,Risky Unicode range: emoji_extended; Long fallback: 13 chars
NG0952,⪠,ng:advanced_coding:compile_core,[COMPILECORE],advanced_coding,compile_core,95.0,C,False,False,True,False,[COMPIL..],,safe,False,Long fallback: 13 chars
NG0953,🢠,ng:advanced_coding:generate_sys_1,[GENERATESYS1],advanced_coding,generate_sys_1,95.0,A,False,True,True,True,[GENERA..],generate_sys_1,emoji_extended,False,Risky Unicode range: emoji_extended; Long fallback: 14 chars; Generic numbered name
NG0954,🞧,ng:advanced_coding:emit_proc,[EMITPROC],advanced_coding,emit_proc,95.0,B,False,True,False,False,,,emoji_extended,False,Risky Unicode range: emoji_extended
NG0955,🟔,ng:advanced_coding:emit,[EMIT],advanced_coding,emit,95.0,B,False,True,False,False,,,emoji_extended,False,Risky Unicode range: emoji_extended
NG0956,😉,ng:advanced_coding:introspect_core,[INTROSPECTCORE],advanced_coding,introspect_core,95.0,A,False,True,True,False,[INTROS..],,emoji_extended,False,Risky Unicode range: emoji_extended; Long fallback: 16 chars
NG0957,🙇,ng:advanced_coding:introspect_sys,[INTROSPECTSYS],advanced_coding,introspect_sys,95.0,A,False,True,True,False,[INTROS..],,emoji_extended,False,Risky Unicode range: emoji_extended; Long fallback: 15 chars
NG0958,📔,ng:advanced_coding:reflect_meta,[REFLECTMETA],advanced_coding,reflect_meta,95.0,A,False,True,True,False,[REFLEC..],,emoji_extended,False,Risky Unicode range: emoji_extended; Long fallback: 13 chars
NG0959,🞿,ng:advanced_coding:introspect_meta,[INTROSPECTMETA],advanced_coding,introspect_meta,95.0,A,False,True,True,False,[INTROS..],,emoji_extended,False,Risky Unicode range: emoji_extended; Long fallback: 16 chars
NG0960,⥳,ng:advanced_coding:introspect,[INTROSPECT],advanced_coding,introspect,95.0,C,False,False,True,False,[INTROS..],,safe,False,Long fallback: 12 chars
NG0961,🞦,ng:advanced_coding:reflect_meta_1,[REFLECTMETA1],advanced_coding,reflect_meta_1,95.0,A,False,True,True,True,[REFLEC..],reflect_meta_1,emoji_extended,False,Risky Unicode range: emoji_extended; Long fallback: 14 chars; Generic numbered name
NG0962,🝫,ng:advanced_coding:meta_meta_1,[METAMETA1],advanced_coding,meta_meta_1,95.0,A,False,True,True,True,[METAME..],meta_meta_1,emoji_extended,False,Risky Unicode range: emoji_extended; Long fallback: 11 chars; Generic numbered name
NG0963,😄,ng:advanced_coding:reflect_ctrl,[REFLECTCTRL],advanced_coding,reflect_ctrl,95.0,A,False,True,True,False,[REFLEC..],,emoji_extended,False,Risky Unicode range: emoji_extended; Long fallback: 13 chars
NG0964,⯜,ng:advanced_coding:introspect_1,[INTROSPECT1],advanced_coding,introspect_1,95.0,B,False,False,True,True,[INTROS..],introspect_1,safe,False,Long fallback: 13 chars; Generic numbered name
NG0965,🦽,ng:advanced_coding:meta_meta_2,[METAMETA2],advanced_coding,meta_meta_2,95.0,A,False,True,True,True,[METAME..],meta_meta_2,emoji_extended,False,Risky Unicode range: emoji_extended; Long fallback: 11 chars; Generic numbered name
NG0966,🞼,ng:advanced_coding:mirror_sys,[MIRRORSYS],advanced_coding,mirror_sys,95.0,A,False,True,True,False,[MIRROR..],,emoji_extended,False,Risky Unicode range: emoji_extended; Long fallback: 11 chars
NG0967,🝠,ng:advanced_coding:reflect_op,[REFLECTOP],advanced_coding,reflect_op,95.0,A,False,True,True,False,[REFLEC..],,emoji_extended,False,Risky Unicode range: emoji_extended; Long fallback: 11 chars
NG0968,😳,ng:advanced_coding:introspection_meta,[INTROSPECTIONMETA],advanced_coding,introspection_meta,95.0,A,False,True,True,False,[INTROS..],,emoji_extended,False,Risky Unicode range: emoji_extended; Long fallback: 19 chars
NG0969,⮸,ng:advanced_coding:introspection_core,[INTROSPECTIONCORE],advanced_coding,introspection_core,95.0,C,False,False,True,False,[INTROS..],,safe,False,Long fallback: 19 chars
NG0970,❑,ng:advanced_coding:introspection_meta_1,[INTROSPECTIONMETA1],advanced_coding,introspection_meta_1,95.0,B,False,False,True,True,[INTROS..],introspection_meta_1,safe,False,Long fallback: 20 chars; Generic numbered name
NG0971,⮷,ng:advanced_coding:introspection_core_1,[INTROSPECTIONCORE1],advanced_coding,introspection_core_1,95.0,B,False,False,True,True,[INTROS..],introspection_core_1,safe,False,Long fallback: 20 chars; Generic numbered name
NG0972,😨,ng:advanced_coding:introspection_1,[INTROSPECTION1],advanced_coding,introspection_1,95.0,A,False,True,True,True,[INTROS..],introspection_1,emoji_extended,False,Risky Unicode range: emoji_extended; Long fallback: 16 chars; Generic numbered name
NG0973,⪘,ng:advanced_coding:introspection_sys,[INTROSPECTIONSYS],advanced_coding,introspection_sys,95.0,C,False,False,True,False,[INTROS..],,safe,False,Long fallback: 18 chars
NG0974,⣠,ng:advanced_coding:introspection_core_2,[INTROSPECTIONCORE2],advanced_coding,introspection_core_2,95.0,B,False,False,True,True,[INTROS..],introspection_core_2,safe,False,Long fallback: 20 chars; Generic numbered name
NG0975,🤅,ng:advanced_coding:introspection_op,[INTROSPECTIONOP],advanced_coding,introspection_op,95.0,A,False,True,True,False,[INTROS..],,emoji_extended,False,Risky Unicode range: emoji_extended; Long fallback: 17 chars
NG0976,😮,ng:advanced_coding:introspection_fn_1,[INTROSPECTIONFN1],advanced_coding,introspection_fn_1,95.0,A,False,True,True,True,[INTROS..],introspection_fn_1,emoji_extended,False,Risky Unicode range: emoji_extended; Long fallback: 18 chars; Generic numbered name
NG0977,⨞,ng:advanced_coding:introspection_ctrl,[INTROSPECTIONCTRL],advanced_coding,introspection_ctrl,95.0,C,False,False,True,False,[INTROS..],,safe,False,Long fallback: 19 chars
NG0978,⨂,ng:advanced_coding:introspection_core_3,[INTROSPECTIONCORE3],advanced_coding,introspection_core_3,95.0,B,False,False,True,True,[INTROS..],introspection_core_3,safe,False,Long fallback: 20 chars; Generic numbered name
NG0979,🞍,ng:advanced_coding:introspection_proc,[INTROSPECTIONPROC],advanced_coding,introspection_proc,95.0,A,False,True,True,False,[INTROS..],,emoji_extended,False,Risky Unicode range: emoji_extended; Long fallback: 19 chars
NG0980,😵,ng:advanced_coding:dynamicdispatch_sys,[DYNAMICDISPATCHSYS],advanced_coding,dynamicdispatch_sys,95.0,A,False,True,True,False,[DYNAMI..],,emoji_extended,False,Risky Unicode range: emoji_extended; Long fallback: 20 chars
NG0981,⧚,ng:advanced_coding:dynamicdispatch_op,[DYNAMICDISPATCHOP],advanced_coding,dynamicdispatch_op,95.0,C,False,False,True,False,[DYNAMI..],,safe,False,Long fallback: 19 chars
NG0982,⪟,ng:advanced_coding:dynamicdispatch,[DYNAMICDISPATCH],advanced_coding,dynamicdispatch,95.0,C,False,False,True,False,[DYNAMI..],,safe,False,Long fallback: 17 chars
NG0983,⢖,ng:advanced_coding:dynamicdispatch_1,[DYNAMICDISPATCH1],advanced_coding,dynamicdispatch_1,95.0,B,False,False,True,True,[DYNAMI..],dynamicdispatch_1,safe,False,Long fallback: 18 chars; Generic numbered name
NG0984,🜂,ng:advanced_coding:dynamicdispatch_fn_2,[DYNAMICDISPATCHFN2],advanced_coding,dynamicdispatch_fn_2,95.0,A,False,True,True,True,[DYNAMI..],dynamicdispatch_fn_2,emoji_extended,False,Risky Unicode range: emoji_extended; Long fallback: 20 chars; Generic numbered name
NG0985,𝝏,ng:advanced_coding:dynamicdispatch_fn_3,[DYNAMICDISPATCHFN3],advanced_coding,dynamicdispatch_fn_3,95.0,B,False,False,True,True,[DYNAMI..],dynamicdispatch_fn_3,safe,False,Long fallback: 20 chars; Generic numbered name
NG0986,⤗,ng:advanced_coding:dynamicdispatch_2,[DYNAMICDISPATCH2],advanced_coding,dynamicdispatch_2,95.0,B,False,False,True,True,[DYNAMI..],dynamicdispatch_2,safe,False,Long fallback: 18 chars; Generic numbered name
NG0987,⮐,ng:advanced_coding:dynamicdispatch_3,[DYNAMICDISPATCH3],advanced_coding,dynamicdispatch_3,95.0,B,False,False,True,True,[DYNAMI..],dynamicdispatch_3,safe,False,Long fallback: 18 chars; Generic numbered name
NG0988,✼,ng:advanced_coding:dynamicdispatch_4,[DYNAMICDISPATCH4],advanced_coding,dynamicdispatch_4,95.0,B,False,False,True,True,[DYNAMI..],dynamicdispatch_4,safe,False,Long fallback: 18 chars; Generic numbered name
NG0989,🤶,ng:advanced_coding:dynamicdispatch_op_1,[DYNAMICDISPATCHOP1],advanced_coding,dynamicdispatch_op_1,95.0,A,False,True,True,True,[DYNAMI..],dynamicdispatch_op_1,emoji_extended,False,Risky Unicode range: emoji_extended; Long fallback: 20 chars; Generic numbered name
NG0990,⫟,ng:advanced_coding:dynamicdispatch_fn_4,[DYNAMICDISPATCHFN4],advanced_coding,dynamicdispatch_fn_4,95.0,B,False,False,True,True,[DYNAMI..],dynamicdispatch_fn_4,safe,False,Long fallback: 20 chars; Generic numbered name
NG0991,⮶,ng:advanced_coding:dynamicdispatch_5,[DYNAMICDISPATCH5],advanced_coding,dynamicdispatch_5,95.0,B,False,False,True,True,[DYNAMI..],dynamicdispatch_5,safe,False,Long fallback: 18 chars; Generic numbered name
NG0992,⫄,ng:advanced_coding:metaobjects_fn_1,[METAOBJECTSFN1],advanced_coding,metaobjects_fn_1,95.0,B,False,False,True,True,[METAOB..],metaobjects_fn_1,safe,False,Long fallback: 16 chars; Generic numbered name
NG0993,⧠,ng:advanced_coding:metaobjects_op,[METAOBJECTSOP],advanced_coding,metaobjects_op,95.0,C,False,False,True,False,[METAOB..],,safe,False,Long fallback: 15 chars
NG0994,🚄,ng:advanced_coding:metaobjects_1,[METAOBJECTS1],advanced_coding,metaobjects_1,95.0,A,False,True,True,True,[METAOB..],metaobjects_1,emoji_extended,False,Risky Unicode range: emoji_extended; Long fallback: 14 chars; Generic numbered name
NG0995,⧲,ng:advanced_coding:metaobjects_ctrl,[METAOBJECTSCTRL],advanced_coding,metaobjects_ctrl,95.0,C,False,False,True,False,[METAOB..],,safe,False,Long fallback: 17 chars
NG0996,🡆,ng:advanced_coding:metaobjects_2,[METAOBJECTS2],advanced_coding,metaobjects_2,95.0,A,False,True,True,True,[METAOB..],metaobjects_2,emoji_extended,False,Risky Unicode range: emoji_extended; Long fallback: 14 chars; Generic numbered name
NG0997,😱,ng:advanced_coding:metaobjects_fn_2,[METAOBJECTSFN2],advanced_coding,metaobjects_fn_2,95.0,A,False,True,True,True,[METAOB..],metaobjects_fn_2,emoji_extended,False,Risky Unicode range: emoji_extended; Long fallback: 16 chars; Generic numbered name
NG0998,😈,ng:advanced_coding:metaobjects_sys,[METAOBJECTSSYS],advanced_coding,metaobjects_sys,95.0,A,False,True,True,False,[METAOB..],,emoji_extended,False,Risky Unicode range: emoji_extended; Long fallback: 16 chars
NG0999,🠬,ng:advanced_coding:metaobjects_op_1,[METAOBJECTSOP1],advanced_coding,metaobjects_op_1,95.0,A,False,True,True,True,[METAOB..],metaobjects_op_1,emoji_extended,False,Risky Unicode range: emoji_extended; Long fallback: 16 chars; Generic numbered name
NG1000,❘,ng:advanced_coding:metaobjects_fn_3,[METAOBJECTSFN3],advanced_coding,metaobjects_fn_3,95.0,B,False,False,True,True,[METAOB..],metaobjects_fn_3,safe,False,Long fallback: 16 chars; Generic numbered name
NG1001,🜒,ng:advanced_coding:metaobjects_op_2,[METAOBJECTSOP2],advanced_coding,metaobjects_op_2,95.0,A,False,True,True,True,[METAOB..],metaobjects_op_2,emoji_extended,False,Risky Unicode range: emoji_extended; Long fallback: 16 chars; Generic numbered name
NG1002,⥸,ng:advanced_coding:metaobjects_sys_1,[METAOBJECTSSYS1],advanced_coding,metaobjects_sys_1,95.0,B,False,False,True,True,[METAOB..],metaobjects_sys_1,safe,False,Long fallback: 17 chars; Generic numbered name
NG1003,⠁,ng:advanced_coding:metaobjects_sys_2,[METAOBJECTSSYS2],advanced_coding,metaobjects_sys_2,95.0,B,False,False,True,True,[METAOB..],metaobjects_sys_2,safe,False,Long fallback: 17 chars; Generic numbered name
NG1004,🤨,ng:advanced_coding:bytecode_ctrl,[BYTECODECTRL],advanced_coding,bytecode_ctrl,95.0,A,False,True,True,False,[BYTECO..],,emoji_extended,False,Risky Unicode range: emoji_extended; Long fallback: 14 chars
NG1005,🚬,ng:advanced_coding:bytecode_fn,[BYTECODEFN],advanced_coding,bytecode_fn,95.0,A,False,True,True,False,[BYTECO..],,emoji_extended,False,Risky Unicode range: emoji_extended; Long fallback: 12 chars
NG1006,⯐,ng:advanced_coding:bytecode_core,[BYTECODECORE],advanced_coding,bytecode_core,95.0,C,False,False,True,False,[BYTECO..],,safe,False,Long fallback: 14 chars
NG1007,⣐,ng:advanced_coding:bytecode_meta,[BYTECODEMETA],advanced_coding,bytecode_meta,95.0,C,False,False,True,False,[BYTECO..],,safe,False,Long fallback: 14 chars
NG1008,⬿,ng:advanced_coding:bytecode_proc,[BYTECODEPROC],advanced_coding,bytecode_proc,95.0,C,False,False,True,False,[BYTECO..],,safe,False,Long fallback: 14 chars
NG1009,❫,ng:advanced_coding:bytecode_sys_1,[BYTECODESYS1],advanced_coding,bytecode_sys_1,95.0,B,False,False,True,True,[BYTECO..],bytecode_sys_1,safe,False,Long fallback: 14 chars; Generic numbered name
NG1010,💊,ng:advanced_coding:bytecode_op,[BYTECODEOP],advanced_coding,bytecode_op,95.0,A,False,True,True,False,[BYTECO..],,emoji_extended,False,Risky Unicode range: emoji_extended; Long fallback: 12 chars
NG1011,❤,ng:advanced_coding:bytecode_1,[BYTECODE1],advanced_coding,bytecode_1,95.0,B,False,False,True,True,[BYTECO..],bytecode_1,safe,False,Long fallback: 11 chars; Generic numbered name
NG1012,❗,ng:advanced_coding:bytecode_sys_2,[BYTECODESYS2],advanced_coding,bytecode_sys_2,95.0,B,False,False,True,True,[BYTECO..],bytecode_sys_2,safe,False,Long fallback: 14 chars; Generic numbered name
NG1013,🛄,ng:advanced_coding:bytecode_2,[BYTECODE2],advanced_coding,bytecode_2,95.0,A,False,True,True,True,[BYTECO..],bytecode_2,emoji_extended,False,Risky Unicode range: emoji_extended; Long fallback: 11 chars; Generic numbered name
NG1014,⠮,ng:advanced_coding:bytecode_ctrl_1,[BYTECODECTRL1],advanced_coding,bytecode_ctrl_1,95.0,B,False,False,True,True,[BYTECO..],bytecode_ctrl_1,safe,False,Long fallback: 15 chars; Generic numbered name
NG1015,🞳,ng:advanced_coding:bytecode_3,[BYTECODE3],advanced_coding,bytecode_3,95.0,A,False,True,True,True,[BYTECO..],bytecode_3,emoji_extended,False,Risky Unicode range: emoji_extended; Long fallback: 11 chars; Generic numbered name
NG1016,➴,ng:advanced_coding:jitcompilation_sys,[JITCOMPILATIONSYS],advanced_coding,jitcompilation_sys,95.0,C,False,False,True,False,[JITCOM..],,safe,False,Long fallback: 19 chars
NG1017,🚆,ng:advanced_coding:jitcompilation_sys_1,[JITCOMPILATIONSYS1],advanced_coding,jitcompilation_sys_1,95.0,A,False,True,True,True,[JITCOM..],jitcompilation_sys_1,emoji_extended,False,Risky Unicode range: emoji_extended; Long fallback: 20 chars; Generic numbered name
NG1018,🥘,ng:advanced_coding:jitcompilation_fn,[JITCOMPILATIONFN],advanced_coding,jitcompilation_fn,95.0,A,False,True,True,False,[JITCOM..],,emoji_extended,False,Risky Unicode range: emoji_extended; Long fallback: 18 chars
NG1019,⠀,ng:advanced_coding:jitcompilation_ctrl,[JITCOMPILATIONCTRL],advanced_coding,jitcompilation_ctrl,95.0,C,False,False,True,False,[JITCOM..],,safe,False,Long fallback: 20 chars
NG1020,⥫,ng:advanced_coding:jitcompilation_fn_1,[JITCOMPILATIONFN1],advanced_coding,jitcompilation_fn_1,95.0,B,False,False,True,True,[JITCOM..],jitcompilation_fn_1,safe,False,Long fallback: 19 chars; Generic numbered name
NG1021,🜢,ng:advanced_coding:jitcompilation_op,[JITCOMPILATIONOP],advanced_coding,jitcompilation_op,95.0,A,False,True,True,False,[JITCOM..],,emoji_extended,False,Risky Unicode range: emoji_extended; Long fallback: 18 chars
NG1022,🕓,ng:advanced_coding:jitcompilation_proc,[JITCOMPILATIONPROC],advanced_coding,jitcompilation_proc,95.0,A,False,True,True,False,[JITCOM..],,emoji_extended,False,Risky Unicode range: emoji_extended; Long fallback: 20 chars
NG1023,⫉,ng:advanced_coding:jitcompilation_sys_2,[JITCOMPILATIONSYS2],advanced_coding,jitcompilation_sys_2,95.0,B,False,False,True,True,[JITCOM..],jitcompilation_sys_2,safe,False,Long fallback: 20 chars; Generic numbered name
NG1024,⭙,ng:advanced_coding:jitcompilation_op_1,[JITCOMPILATIONOP1],advanced_coding,jitcompilation_op_1,95.0,B,False,False,True,True,[JITCOM..],jitcompilation_op_1,safe,False,Long fallback: 19 chars; Generic numbered name
NG1025,🜹,ng:advanced_coding:jitcompilation_sys_3,[JITCOMPILATIONSYS3],advanced_coding,jitcompilation_sys_3,95.0,A,False,True,True,True,[JITCOM..],jitcompilation_sys_3,emoji_extended,False,Risky Unicode range: emoji_extended; Long fallback: 20 chars; Generic numbered name
NG1026,⫬,ng:advanced_coding:jitcompilation_op_2,[JITCOMPILATIONOP2],advanced_coding,jitcompilation_op_2,95.0,B,False,False,True,True,[JITCOM..],jitcompilation_op_2,safe,False,Long fallback: 19 chars; Generic numbered name
NG1027,🠫,ng:advanced_coding:jitcompilation,[JITCOMPILATION],advanced_coding,jitcompilation,95.0,A,False,True,True,False,[JITCOM..],,emoji_extended,False,Risky Unicode range: emoji_extended; Long fallback: 16 chars
NG1028,⠂,ng:advanced_coding:garbagecollection_2,[GARBAGECOLLECTION2],advanced_coding,garbagecollection_2,95.0,B,False,False,True,True,[GARBAG..],garbagecollection_2,safe,False,Long fallback: 20 chars; Generic numbered name
NG1029,⦙,ng:advanced_coding:garbagecollection_3,[GARBAGECOLLECTION3],advanced_coding,garbagecollection_3,95.0,B,False,False,True,True,[GARBAG..],garbagecollection_3,safe,False,Long fallback: 20 chars; Generic numbered name
NG1030,⭌,ng:advanced_coding:garbagecollection_4,[GARBAGECOLLECTION4],advanced_coding,garbagecollection_4,95.0,B,False,False,True,True,[GARBAG..],garbagecollection_4,safe,False,Long fallback: 20 chars; Generic numbered name
NG1031,🞥,ng:advanced_coding:garbagecollection_5,[GARBAGECOLLECTION5],advanced_coding,garbagecollection_5,95.0,A,False,True,True,True,[GARBAG..],garbagecollection_5,emoji_extended,False,Risky Unicode range: emoji_extended; Long fallback: 20 chars; Generic numbered name
NG1032,⨚,ng:advanced_coding:garbagecollection_6,[GARBAGECOLLECTION6],advanced_coding,garbagecollection_6,95.0,B,False,False,True,True,[GARBAG..],garbagecollection_6,safe,False,Long fallback: 20 chars; Generic numbered name
NG1033,⮟,ng:advanced_coding:garbagecollection_7,[GARBAGECOLLECTION7],advanced_coding,garbagecollection_7,95.0,B,False,False,True,True,[GARBAG..],garbagecollection_7,safe,False,Long fallback: 20 chars; Generic numbered name
NG1034,✳,ng:advanced_coding:garbagecollection_8,[GARBAGECOLLECTION8],advanced_coding,garbagecollection_8,95.0,B,False,False,True,True,[GARBAG..],garbagecollection_8,safe,False,Long fallback: 20 chars; Generic numbered name
NG1035,⥇,ng:advanced_coding:garbagecollection_9,[GARBAGECOLLECTION9],advanced_coding,garbagecollection_9,95.0,B,False,False,True,True,[GARBAG..],garbagecollection_9,safe,False,Long fallback: 20 chars; Generic numbered name
NG1036,🝲,ng:advanced_coding:memorypools_fn,[MEMORYPOOLSFN],advanced_coding,memorypools_fn,95.0,A,False,True,True,False,[MEMORY..],,emoji_extended,False,Risky Unicode range: emoji_extended; Long fallback: 15 chars
NG1037,🝮,ng:advanced_coding:memorypools_sys,[MEMORYPOOLSSYS],advanced_coding,memorypools_sys,95.0,A,False,True,True,False,[MEMORY..],,emoji_extended,False,Risky Unicode range: emoji_extended; Long fallback: 16 chars
NG1038,⢈,ng:advanced_coding:memorypools_1,[MEMORYPOOLS1],advanced_coding,memorypools_1,95.0,B,False,False,True,True,[MEMORY..],memorypools_1,safe,False,Long fallback: 14 chars; Generic numbered name
NG1039,😤,ng:advanced_coding:memorypools_core,[MEMORYPOOLSCORE],advanced_coding,memorypools_core,95.0,A,False,True,True,False,[MEMORY..],,emoji_extended,False,Risky Unicode range: emoji_extended; Long fallback: 17 chars
NG1040,➞,ng:advanced_coding:memorypools_op,[MEMORYPOOLSOP],advanced_coding,memorypools_op,95.0,C,False,False,True,False,[MEMORY..],,safe,False,Long fallback: 15 chars
NG1041,🞃,ng:advanced_coding:memorypools_proc,[MEMORYPOOLSPROC],advanced_coding,memorypools_proc,95.0,A,False,True,True,False,[MEMORY..],,emoji_extended,False,Risky Unicode range: emoji_extended; Long fallback: 17 chars
NG1042,⧹,ng:advanced_coding:memorypools_sys_1,[MEMORYPOOLSSYS1],advanced_coding,memorypools_sys_1,95.0,B,False,False,True,True,[MEMORY..],memorypools_sys_1,safe,False,Long fallback: 17 chars; Generic numbered name
NG1043,🤉,ng:advanced_coding:memorypools_proc_1,[MEMORYPOOLSPROC1],advanced_coding,memorypools_proc_1,95.0,A,False,True,True,True,[MEMORY..],memorypools_proc_1,emoji_extended,False,Risky Unicode range: emoji_extended; Long fallback: 18 chars; Generic numbered name
NG1044,🕮,ng:advanced_coding:memorypools_meta_1,[MEMORYPOOLSMETA1],advanced_coding,memorypools_meta_1,95.0,A,False,True,True,True,[MEMORY..],memorypools_meta_1,emoji_extended,False,Risky Unicode range: emoji_extended; Long fallback: 18 chars; Generic numbered name
NG1045,🗜,ng:advanced_coding:memorypools_core_1,[MEMORYPOOLSCORE1],advanced_coding,memorypools_core_1,95.0,A,False,True,True,True,[MEMORY..],memorypools_core_1,emoji_extended,False,Risky Unicode range: emoji_extended; Long fallback: 18 chars; Generic numbered name
NG1046,⨥,ng:advanced_coding:memorypools_sys_2,[MEMORYPOOLSSYS2],advanced_coding,memorypools_sys_2,95.0,B,False,False,True,True,[MEMORY..],memorypools_sys_2,safe,False,Long fallback: 17 chars; Generic numbered name
NG1047,😺,ng:advanced_coding:memorypools_ctrl,[MEMORYPOOLSCTRL],advanced_coding,memorypools_ctrl,95.0,A,False,True,True,False,[MEMORY..],,emoji_extended,False,Risky Unicode range: emoji_extended; Long fallback: 17 chars
NG1048,🠆,ng:advanced_coding:stackframes_proc,[STACKFRAMESPROC],advanced_coding,stackframes_proc,95.0,A,False,True,True,False,[STACKF..],,emoji_extended,False,Risky Unicode range: emoji_extended; Long fallback: 17 chars
NG1049,⯦,ng:advanced_coding:stackframes_fn_1,[STACKFRAMESFN1],advanced_coding,stackframes_fn_1,95.0,B,False,False,True,True,[STACKF..],stackframes_fn_1,safe,False,Long fallback: 16 chars; Generic numbered name
NG1050,✱,ng:advanced_coding:stackframes_op,[STACKFRAMESOP],advanced_coding,stackframes_op,95.0,C,False,False,True,False,[STACKF..],,safe,False,Long fallback: 15 chars
NG1051,⤭,ng:advanced_coding:stackframes_sys,[STACKFRAMESSYS],advanced_coding,stackframes_sys,95.0,C,False,False,True,False,[STACKF..],,safe,False,Long fallback: 16 chars
NG1052,⡘,ng:advanced_coding:stackframes_sys_1,[STACKFRAMESSYS1],advanced_coding,stackframes_sys_1,95.0,B,False,False,True,True,[STACKF..],stackframes_sys_1,safe,False,Long fallback: 17 chars; Generic numbered name
NG1053,🕐,ng:advanced_coding:stackframes_core,[STACKFRAMESCORE],advanced_coding,stackframes_core,95.0,A,False,True,True,False,[STACKF..],,emoji_extended,False,Risky Unicode range: emoji_extended; Long fallback: 17 chars
NG1054,⯨,ng:advanced_coding:stackframes_1,[STACKFRAMES1],advanced_coding,stackframes_1,95.0,B,False,False,True,True,[STACKF..],stackframes_1,safe,False,Long fallback: 14 chars; Generic numbered name
NG1055,🝉,ng:advanced_coding:stackframes_core_1,[STACKFRAMESCORE1],advanced_coding,stackframes_core_1,95.0,A,False,True,True,True,[STACKF..],stackframes_core_1,emoji_extended,False,Risky Unicode range: emoji_extended; Long fallback: 18 chars; Generic numbered name
NG1056,🠸,ng:advanced_coding:stackframes_meta,[STACKFRAMESMETA],advanced_coding,stackframes_meta,95.0,A,False,True,True,False,[STACKF..],,emoji_extended,False,Risky Unicode range: emoji_extended; Long fallback: 17 chars
NG1057,🡢,ng:advanced_coding:stackframes_meta_1,[STACKFRAMESMETA1],advanced_coding,stackframes_meta_1,95.0,A,False,True,True,True,[STACKF..],stackframes_meta_1,emoji_extended,False,Risky Unicode range: emoji_extended; Long fallback: 18 chars; Generic numbered name
NG1058,🡗,ng:advanced_coding:stackframes_fn_2,[STACKFRAMESFN2],advanced_coding,stackframes_fn_2,95.0,A,False,True,True,True,[STACKF..],stackframes_fn_2,emoji_extended,False,Risky Unicode range: emoji_extended; Long fallback: 16 chars; Generic numbered name
NG1059,🕜,ng:advanced_coding:stackframes_core_2,[STACKFRAMESCORE2],advanced_coding,stackframes_core_2,95.0,A,False,True,True,True,[STACKF..],stackframes_core_2,emoji_extended,False,Risky Unicode range: emoji_extended; Long fallback: 18 chars; Generic numbered name
NG1060,❄,ng:advanced_coding:heapmanagement_sys_1,[HEAPMANAGEMENTSYS1],advanced_coding,heapmanagement_sys_1,95.0,B,False,False,True,True,[HEAPMA..],heapmanagement_sys_1,safe,False,Long fallback: 20 chars; Generic numbered name
NG1061,⪴,ng:advanced_coding:heapmanagement_op,[HEAPMANAGEMENTOP],advanced_coding,heapmanagement_op,95.0,C,False,False,True,False,[HEAPMA..],,safe,False,Long fallback: 18 chars
NG1062,🌮,ng:advanced_coding:heapmanagement_sys_2,[HEAPMANAGEMENTSYS2],advanced_coding,heapmanagement_sys_2,95.0,A,False,True,True,True,[HEAPMA..],heapmanagement_sys_2,emoji_extended,False,Risky Unicode range: emoji_extended; Long fallback: 20 chars; Generic numbered name
NG1063,🕀,ng:advanced_coding:heapmanagement_fn,[HEAPMANAGEMENTFN],advanced_coding,heapmanagement_fn,95.0,A,False,True,True,False,[HEAPMA..],,emoji_extended,False,Risky Unicode range: emoji_extended; Long fallback: 18 chars
NG1064,⥔,ng:advanced_coding:heapmanagement,[HEAPMANAGEMENT],advanced_coding,heapmanagement,95.0,C,False,False,True,False,[HEAPMA..],,safe,False,Long fallback: 16 chars
NG1065,🟇,ng:advanced_coding:heapmanagement_fn_1,[HEAPMANAGEMENTFN1],advanced_coding,heapmanagement_fn_1,95.0,A,False,True,True,True,[HEAPMA..],heapmanagement_fn_1,emoji_extended,False,Risky Unicode range: emoji_extended; Long fallback: 19 chars; Generic numbered name
NG1066,⡔,ng:advanced_coding:heapmanagement_ctrl,[HEAPMANAGEMENTCTRL],advanced_coding,heapmanagement_ctrl,95.0,C,False,False,True,False,[HEAPMA..],,safe,False,Long fallback: 20 chars
NG1067,🛲,ng:advanced_coding:heapmanagement_meta,[HEAPMANAGEMENTMETA],advanced_coding,heapmanagement_meta,95.0,A,False,True,True,False,[HEAPMA..],,emoji_extended,False,Risky Unicode range: emoji_extended; Long fallback: 20 chars
NG1068,✞,ng:advanced_coding:heapmanagement_1,[HEAPMANAGEMENT1],advanced_coding,heapmanagement_1,95.0,B,False,False,True,True,[HEAPMA..],heapmanagement_1,safe,False,Long fallback: 17 chars; Generic numbered name
NG1069,🛉,ng:advanced_coding:heapmanagement_core,[HEAPMANAGEMENTCORE],advanced_coding,heapmanagement_core,95.0,A,False,True,True,False,[HEAPMA..],,emoji_extended,False,Risky Unicode range: emoji_extended; Long fallback: 20 chars
NG1070,⪒,ng:advanced_coding:heapmanagement_fn_2,[HEAPMANAGEMENTFN2],advanced_coding,heapmanagement_fn_2,95.0,B,False,False,True,True,[HEAPMA..],heapmanagement_fn_2,safe,False,Long fallback: 19 chars; Generic numbered name
NG1071,⮙,ng:advanced_coding:heapmanagement_2,[HEAPMANAGEMENT2],advanced_coding,heapmanagement_2,95.0,B,False,False,True,True,[HEAPMA..],heapmanagement_2,safe,False,Long fallback: 17 chars; Generic numbered name
NG1072,⦩,ng:advanced_coding:coroutines_ctrl,[COROUTINESCTRL],advanced_coding,coroutines_ctrl,95.0,C,False,False,True,False,[COROUT..],,safe,False,Long fallback: 16 chars
NG1073,🧒,ng:advanced_coding:coroutines_op_1,[COROUTINESOP1],advanced_coding,coroutines_op_1,95.0,A,False,True,True,True,[COROUT..],coroutines_op_1,emoji_extended,False,Risky Unicode range: emoji_extended; Long fallback: 15 chars; Generic numbered name
NG1074,⠎,ng:advanced_coding:coroutines_meta,[COROUTINESMETA],advanced_coding,coroutines_meta,95.0,C,False,False,True,False,[COROUT..],,safe,False,Long fallback: 16 chars
NG1075,🛝,ng:advanced_coding:coroutines_ctrl_1,[COROUTINESCTRL1],advanced_coding,coroutines_ctrl_1,95.0,A,False,True,True,True,[COROUT..],coroutines_ctrl_1,emoji_extended,False,Risky Unicode range: emoji_extended; Long fallback: 17 chars; Generic numbered name
NG1076,❛,ng:advanced_coding:coroutines,[COROUTINES],advanced_coding,coroutines,95.0,C,False,False,True,False,[COROUT..],,safe,False,Long fallback: 12 chars
NG1077,🛖,ng:advanced_coding:coroutines_fn_1,[COROUTINESFN1],advanced_coding,coroutines_fn_1,95.0,A,False,True,True,True,[COROUT..],coroutines_fn_1,emoji_extended,False,Risky Unicode range: emoji_extended; Long fallback: 15 chars; Generic numbered name
NG1078,🞒,ng:advanced_coding:coroutines_op_2,[COROUTINESOP2],advanced_coding,coroutines_op_2,95.0,A,False,True,True,True,[COROUT..],coroutines_op_2,emoji_extended,False,Risky Unicode range: emoji_extended; Long fallback: 15 chars; Generic numbered name
NG1079,👔,ng:advanced_coding:coroutines_1,[COROUTINES1],advanced_coding,coroutines_1,95.0,A,False,True,True,True,[COROUT..],coroutines_1,emoji_extended,False,Risky Unicode range: emoji_extended; Long fallback: 13 chars; Generic numbered name
NG1080,🟆,ng:advanced_coding:coroutines_2,[COROUTINES2],advanced_coding,coroutines_2,95.0,A,False,True,True,True,[COROUT..],coroutines_2,emoji_extended,False,Risky Unicode range: emoji_extended; Long fallback: 13 chars; Generic numbered name
NG1081,⦓,ng:advanced_coding:coroutines_meta_1,[COROUTINESMETA1],advanced_coding,coroutines_meta_1,95.0,B,False,False,True,True,[COROUT..],coroutines_meta_1,safe,False,Long fallback: 17 chars; Generic numbered name
NG1082,⠹,ng:advanced_coding:coroutines_core,[COROUTINESCORE],advanced_coding,coroutines_core,95.0,C,False,False,True,False,[COROUT..],,safe,False,Long fallback: 16 chars
NG1083,🠀,ng:advanced_coding:coroutines_sys,[COROUTINESSYS],advanced_coding,coroutines_sys,95.0,A,False,True,True,False,[COROUT..],,emoji_extended,False,Risky Unicode range: emoji_extended; Long fallback: 15 chars
NG1084,🧤,ng:advanced_coding:generators_ctrl,[GENERATORSCTRL],advanced_coding,generators_ctrl,95.0,A,False,True,True,False,[GENERA..],,emoji_extended,False,Risky Unicode range: emoji_extended; Long fallback: 16 chars
NG1085,🟩,ng:advanced_coding:generators_op,[GENERATORSOP],advanced_coding,generators_op,95.0,A,False,True,True,False,[GENERA..],,emoji_extended,False,Risky Unicode range: emoji_extended; Long fallback: 14 chars
NG1086,🚻,ng:advanced_coding:generators_sys_1,[GENERATORSSYS1],advanced_coding,generators_sys_1,95.0,A,False,True,True,True,[GENERA..],generators_sys_1,emoji_extended,False,Risky Unicode range: emoji_extended; Long fallback: 16 chars; Generic numbered name
NG1087,🧥,ng:advanced_coding:generators_meta,[GENERATORSMETA],advanced_coding,generators_meta,95.0,A,False,True,True,False,[GENERA..],,emoji_extended,False,Risky Unicode range: emoji_extended; Long fallback: 16 chars
NG1088,😅,ng:advanced_coding:generators_meta_1,[GENERATORSMETA1],advanced_coding,generators_meta_1,95.0,A,False,True,True,True,[GENERA..],generators_meta_1,emoji_extended,False,Risky Unicode range: emoji_extended; Long fallback: 17 chars; Generic numbered name
NG1089,🠽,ng:advanced_coding:generators_proc,[GENERATORSPROC],advanced_coding,generators_proc,95.0,A,False,True,True,False,[GENERA..],,emoji_extended,False,Risky Unicode range: emoji_extended; Long fallback: 16 chars
NG1090,😸,ng:advanced_coding:generators_ctrl_1,[GENERATORSCTRL1],advanced_coding,generators_ctrl_1,95.0,A,False,True,True,True,[GENERA..],generators_ctrl_1,emoji_extended,False,Risky Unicode range: emoji_extended; Long fallback: 17 chars; Generic numbered name
NG1091,⪥,ng:advanced_coding:generators_meta_2,[GENERATORSMETA2],advanced_coding,generators_meta_2,95.0,B,False,False,True,True,[GENERA..],generators_meta_2,safe,False,Long fallback: 17 chars; Generic numbered name
NG1092,😗,ng:advanced_coding:generators_ctrl_2,[GENERATORSCTRL2],advanced_coding,generators_ctrl_2,95.0,A,False,True,True,True,[GENERA..],generators_ctrl_2,emoji_extended,False,Risky Unicode range: emoji_extended; Long fallback: 17 chars; Generic numbered name
NG1093,🜥,ng:advanced_coding:generators,[GENERATORS],advanced_coding,generators,95.0,A,False,True,True,False,[GENERA..],,emoji_extended,False,Risky Unicode range: emoji_extended; Long fallback: 12 chars
NG1094,🎽,ng:advanced_coding:generators_proc_1,[GENERATORSPROC1],advanced_coding,generators_proc_1,95.0,A,False,True,True,True,[GENERA..],generators_proc_1,emoji_extended,False,Risky Unicode range: emoji_extended; Long fallback: 17 chars; Generic numbered name
NG1095,📑,ng:advanced_coding:generators_ctrl_3,[GENERATORSCTRL3],advanced_coding,generators_ctrl_3,95.0,A,False,True,True,True,[GENERA..],generators_ctrl_3,emoji_extended,False,Risky Unicode range: emoji_extended; Long fallback: 17 chars; Generic numbered name
NG1096,🏻,ng:advanced_coding:iterators_core,[ITERATORSCORE],advanced_coding,iterators_core,95.0,A,False,True,True,False,[ITERAT..],,emoji_extended,False,Risky Unicode range: emoji_extended; Long fallback: 15 chars
NG1097,⣛,ng:advanced_coding:iterators_fn,[ITERATORSFN],advanced_coding,iterators_fn,95.0,C,False,False,True,False,[ITERAT..],,safe,False,Long fallback: 13 chars
NG1098,🝵,ng:advanced_coding:iterators_fn_1,[ITERATORSFN1],advanced_coding,iterators_fn_1,95.0,A,False,True,True,True,[ITERAT..],iterators_fn_1,emoji_extended,False,Risky Unicode range: emoji_extended; Long fallback: 14 chars; Generic numbered name
NG1099,🤤,ng:advanced_coding:iterators,[ITERATORS],advanced_coding,iterators,95.0,A,False,True,True,False,[ITERAT..],,emoji_extended,False,Risky Unicode range: emoji_extended; Long fallback: 11 chars
NG1100,⫍,ng:advanced_coding:iterators_core_1,[ITERATORSCORE1],advanced_coding,iterators_core_1,95.0,B,False,False,True,True,[ITERAT..],iterators_core_1,safe,False,Long fallback: 16 chars; Generic numbered name
NG1101,⭼,ng:advanced_coding:iterators_sys_1,[ITERATORSSYS1],advanced_coding,iterators_sys_1,95.0,B,False,False,True,True,[ITERAT..],iterators_sys_1,safe,False,Long fallback: 15 chars; Generic numbered name
NG1102,🜕,ng:advanced_coding:iterators_ctrl_1,[ITERATORSCTRL1],advanced_coding,iterators_ctrl_1,95.0,A,False,True,True,True,[ITERAT..],iterators_ctrl_1,emoji_extended,False,Risky Unicode range: emoji_extended; Long fallback: 16 chars; Generic numbered name
NG1103,⪨,ng:advanced_coding:iterators_1,[ITERATORS1],advanced_coding,iterators_1,95.0,B,False,False,True,True,[ITERAT..],iterators_1,safe,False,Long fallback: 12 chars; Generic numbered name
NG1104,✏,ng:advanced_coding:iterators_ctrl_2,[ITERATORSCTRL2],advanced_coding,iterators_ctrl_2,95.0,B,False,False,True,True,[ITERAT..],iterators_ctrl_2,safe,False,Long fallback: 16 chars; Generic numbered name
NG1105,⭃,ng:advanced_coding:iterators_core_2,[ITERATORSCORE2],advanced_coding,iterators_core_2,95.0,B,False,False,True,True,[ITERAT..],iterators_core_2,safe,False,Long fallback: 16 chars; Generic numbered name
NG1106,⥘,ng:advanced_coding:iterators_fn_2,[ITERATORSFN2],advanced_coding,iterators_fn_2,95.0,B,False,False,True,True,[ITERAT..],iterators_fn_2,safe,False,Long fallback: 14 chars; Generic numbered name
NG1107,🛀,ng:advanced_coding:iterators_meta,[ITERATORSMETA],advanced_coding,iterators_meta,95.0,A,False,True,True,False,[ITERAT..],,emoji_extended,False,Risky Unicode range: emoji_extended; Long fallback: 15 chars
NG1108,⫅,ng:advanced_coding:comprehensions_op,[COMPREHENSIONSOP],advanced_coding,comprehensions_op,95.0,C,False,False,True,False,[COMPRE..],,safe,False,Long fallback: 18 chars
NG1109,🡯,ng:advanced_coding:comprehensions_proc,[COMPREHENSIONSPROC],advanced_coding,comprehensions_proc,95.0,A,False,True,True,False,[COMPRE..],,emoji_extended,False,Risky Unicode range: emoji_extended; Long fallback: 20 chars
NG1110,🚹,ng:advanced_coding:comprehensions_op_1,[COMPREHENSIONSOP1],advanced_coding,comprehensions_op_1,95.0,A,False,True,True,True,[COMPRE..],comprehensions_op_1,emoji_extended,False,Risky Unicode range: emoji_extended; Long fallback: 19 chars; Generic numbered name
NG1111,🌧,ng:advanced_coding:comprehensions_sys_1,[COMPREHENSIONSSYS1],advanced_coding,comprehensions_sys_1,95.0,A,False,True,True,True,[COMPRE..],comprehensions_sys_1,emoji_extended,False,Risky Unicode range: emoji_extended; Long fallback: 20 chars; Generic numbered name
NG1112,⢧,ng:advanced_coding:comprehensions_fn_1,[COMPREHENSIONSFN1],advanced_coding,comprehensions_fn_1,95.0,B,False,False,True,True,[COMPRE..],comprehensions_fn_1,safe,False,Long fallback: 19 chars; Generic numbered name
NG1113,⤙,ng:advanced_coding:comprehensions_ctrl,[COMPREHENSIONSCTRL],advanced_coding,comprehensions_ctrl,95.0,C,False,False,True,False,[COMPRE..],,safe,False,Long fallback: 20 chars
NG1114,🍥,ng:advanced_coding:comprehensions,[COMPREHENSIONS],advanced_coding,comprehensions,95.0,A,False,True,True,False,[COMPRE..],,emoji_extended,False,Risky Unicode range: emoji_extended; Long fallback: 16 chars
NG1115,⛾,ng:advanced_coding:comprehensions_op_2,[COMPREHENSIONSOP2],advanced_coding,comprehensions_op_2,95.0,A,False,True,True,True,[COMPRE..],comprehensions_op_2,miscellaneous_symbols,False,Risky Unicode range: miscellaneous_symbols; Long fallback: 19 chars; Generic numbered name
NG1116,🞫,ng:advanced_coding:comprehensions_sys_2,[COMPREHENSIONSSYS2],advanced_coding,comprehensions_sys_2,95.0,A,False,True,True,True,[COMPRE..],comprehensions_sys_2,emoji_extended,False,Risky Unicode range: emoji_extended; Long fallback: 20 chars; Generic numbered name
NG1117,🟣,ng:advanced_coding:comprehensions_1,[COMPREHENSIONS1],advanced_coding,comprehensions_1,95.0,A,False,True,True,True,[COMPRE..],comprehensions_1,emoji_extended,False,Risky Unicode range: emoji_extended; Long fallback: 17 chars; Generic numbered name
NG1118,🡠,ng:advanced_coding:comprehensions_meta,[COMPREHENSIONSMETA],advanced_coding,comprehensions_meta,95.0,A,False,True,True,False,[COMPRE..],,emoji_extended,False,Risky Unicode range: emoji_extended; Long fallback: 20 chars
NG1119,😶,ng:advanced_coding:comprehensions_sys_3,[COMPREHENSIONSSYS3],advanced_coding,comprehensions_sys_3,95.0,A,False,True,True,True,[COMPRE..],comprehensions_sys_3,emoji_extended,False,Risky Unicode range: emoji_extended; Long fallback: 20 chars; Generic numbered name
NG1120,🝗,ng:advanced_coding:decorators_proc,[DECORATORSPROC],advanced_coding,decorators_proc,95.0,A,False,True,True,False,[DECORA..],,emoji_extended,False,Risky Unicode range: emoji_extended; Long fallback: 16 chars
NG1121,🞶,ng:advanced_coding:decorators_op,[DECORATORSOP],advanced_coding,decorators_op,95.0,A,False,True,True,False,[DECORA..],,emoji_extended,False,Risky Unicode range: emoji_extended; Long fallback: 14 chars
NG1122,🝃,ng:advanced_coding:decorators_proc_1,[DECORATORSPROC1],advanced_coding,decorators_proc_1,95.0,A,False,True,True,True,[DECORA..],decorators_proc_1,emoji_extended,False,Risky Unicode range: emoji_extended; Long fallback: 17 chars; Generic numbered name
NG1123,⧳,ng:advanced_coding:decorators_proc_2,[DECORATORSPROC2],advanced_coding,decorators_proc_2,95.0,B,False,False,True,True,[DECORA..],decorators_proc_2,safe,False,Long fallback: 17 chars; Generic numbered name
NG1124,🟤,ng:advanced_coding:decorators_ctrl_1,[DECORATORSCTRL1],advanced_coding,decorators_ctrl_1,95.0,A,False,True,True,True,[DECORA..],decorators_ctrl_1,emoji_extended,False,Risky Unicode range: emoji_extended; Long fallback: 17 chars; Generic numbered name
NG1125,😆,ng:advanced_coding:decorators_proc_3,[DECORATORSPROC3],advanced_coding,decorators_proc_3,95.0,A,False,True,True,True,[DECORA..],decorators_proc_3,emoji_extended,False,Risky Unicode range: emoji_extended; Long fallback: 17 chars; Generic numbered name
NG1126,⦢,ng:advanced_coding:decorators_proc_4,[DECORATORSPROC4],advanced_coding,decorators_proc_4,95.0,B,False,False,True,True,[DECORA..],decorators_proc_4,safe,False,Long fallback: 17 chars; Generic numbered name
NG1127,⥼,ng:advanced_coding:decorators_meta,[DECORATORSMETA],advanced_coding,decorators_meta,95.0,C,False,False,True,False,[DECORA..],,safe,False,Long fallback: 16 chars
NG1128,🞁,ng:advanced_coding:decorators_proc_5,[DECORATORSPROC5],advanced_coding,decorators_proc_5,95.0,A,False,True,True,True,[DECORA..],decorators_proc_5,emoji_extended,False,Risky Unicode range: emoji_extended; Long fallback: 17 chars; Generic numbered name
NG1129,🟒,ng:advanced_coding:decorators_core,[DECORATORSCORE],advanced_coding,decorators_core,95.0,A,False,True,True,False,[DECORA..],,emoji_extended,False,Risky Unicode range: emoji_extended; Long fallback: 16 chars
NG1130,⡣,ng:advanced_coding:decorators_sys,[DECORATORSSYS],advanced_coding,decorators_sys,95.0,C,False,False,True,False,[DECORA..],,safe,False,Long fallback: 15 chars
NG1131,➬,ng:advanced_coding:decorators_sys_1,[DECORATORSSYS1],advanced_coding,decorators_sys_1,95.0,B,False,False,True,True,[DECORA..],decorators_sys_1,safe,False,Long fallback: 16 chars; Generic numbered name
NG1132,🚅,ng:advanced_coding:contextmanagers_op,[CONTEXTMANAGERSOP],advanced_coding,contextmanagers_op,95.0,A,False,True,True,False,[CONTEX..],,emoji_extended,False,Risky Unicode range: emoji_extended; Long fallback: 19 chars
NG1133,🞪,ng:advanced_coding:contextmanagers_op_1,[CONTEXTMANAGERSOP1],advanced_coding,contextmanagers_op_1,95.0,A,False,True,True,True,[CONTEX..],contextmanagers_op_1,emoji_extended,False,Risky Unicode range: emoji_extended; Long fallback: 20 chars; Generic numbered name
NG1134,⯄,ng:advanced_coding:contextmanagers_1,[CONTEXTMANAGERS1],advanced_coding,contextmanagers_1,95.0,B,False,False,True,True,[CONTEX..],contextmanagers_1,safe,False,Long fallback: 18 chars; Generic numbered name
NG1135,🚖,ng:advanced_coding:contextmanagers_op_2,[CONTEXTMANAGERSOP2],advanced_coding,contextmanagers_op_2,95.0,A,False,True,True,True,[CONTEX..],contextmanagers_op_2,emoji_extended,False,Risky Unicode range: emoji_extended; Long fallback: 20 chars; Generic numbered name
NG1136,😜,ng:advanced_coding:contextmanagers_op_3,[CONTEXTMANAGERSOP3],advanced_coding,contextmanagers_op_3,95.0,A,False,True,True,True,[CONTEX..],contextmanagers_op_3,emoji_extended,False,Risky Unicode range: emoji_extended; Long fallback: 20 chars; Generic numbered name
NG1137,🝍,ng:advanced_coding:contextmanagers_2,[CONTEXTMANAGERS2],advanced_coding,contextmanagers_2,95.0,A,False,True,True,True,[CONTEX..],contextmanagers_2,emoji_extended,False,Risky Unicode range: emoji_extended; Long fallback: 18 chars; Generic numbered name
NG1138,➤,ng:advanced_coding:contextmanagers_fn,[CONTEXTMANAGERSFN],advanced_coding,contextmanagers_fn,95.0,C,False,False,True,False,[CONTEX..],,safe,False,Long fallback: 19 chars
NG1139,🜅,ng:advanced_coding:contextmanagers_3,[CONTEXTMANAGERS3],advanced_coding,contextmanagers_3,95.0,A,False,True,True,True,[CONTEX..],contextmanagers_3,emoji_extended,False,Risky Unicode range: emoji_extended; Long fallback: 18 chars; Generic numbered name
NG1140,🦇,ng:advanced_coding:contextmanagers_op_4,[CONTEXTMANAGERSOP4],advanced_coding,contextmanagers_op_4,95.0,A,False,True,True,True,[CONTEX..],contextmanagers_op_4,emoji_extended,False,Risky Unicode range: emoji_extended; Long fallback: 20 chars; Generic numbered name
NG1141,😰,ng:advanced_coding:contextmanagers_op_5,[CONTEXTMANAGERSOP5],advanced_coding,contextmanagers_op_5,95.0,A,False,True,True,True,[CONTEX..],contextmanagers_op_5,emoji_extended,False,Risky Unicode range: emoji_extended; Long fallback: 20 chars; Generic numbered name
NG1142,😊,ng:advanced_coding:contextmanagers_op_6,[CONTEXTMANAGERSOP6],advanced_coding,contextmanagers_op_6,95.0,A,False,True,True,True,[CONTEX..],contextmanagers_op_6,emoji_extended,False,Risky Unicode range: emoji_extended; Long fallback: 20 chars; Generic numbered name
NG1143,✩,ng:advanced_coding:contextmanagers_fn_1,[CONTEXTMANAGERSFN1],advanced_coding,contextmanagers_fn_1,95.0,B,False,False,True,True,[CONTEX..],contextmanagers_fn_1,safe,False,Long fallback: 20 chars; Generic numbered name
NG1144,🧼,ng:advanced_coding:descriptors_meta,[DESCRIPTORSMETA],advanced_coding,descriptors_meta,95.0,A,False,True,True,False,[DESCRI..],,emoji_extended,False,Risky Unicode range: emoji_extended; Long fallback: 17 chars
NG1145,⡑,ng:advanced_coding:descriptors_proc,[DESCRIPTORSPROC],advanced_coding,descriptors_proc,95.0,C,False,False,True,False,[DESCRI..],,safe,False,Long fallback: 17 chars
NG1146,⢣,ng:advanced_coding:descriptors_op_1,[DESCRIPTORSOP1],advanced_coding,descriptors_op_1,95.0,B,False,False,True,True,[DESCRI..],descriptors_op_1,safe,False,Long fallback: 16 chars; Generic numbered name
NG1147,🟕,ng:advanced_coding:descriptors_proc_1,[DESCRIPTORSPROC1],advanced_coding,descriptors_proc_1,95.0,A,False,True,True,True,[DESCRI..],descriptors_proc_1,emoji_extended,False,Risky Unicode range: emoji_extended; Long fallback: 18 chars; Generic numbered name
NG1148,🗖,ng:advanced_coding:descriptors_proc_2,[DESCRIPTORSPROC2],advanced_coding,descriptors_proc_2,95.0,A,False,True,True,True,[DESCRI..],descriptors_proc_2,emoji_extended,False,Risky Unicode range: emoji_extended; Long fallback: 18 chars; Generic numbered name
NG1149,⥣,ng:advanced_coding:descriptors_op_2,[DESCRIPTORSOP2],advanced_coding,descriptors_op_2,95.0,B,False,False,True,True,[DESCRI..],descriptors_op_2,safe,False,Long fallback: 16 chars; Generic numbered name
NG1150,🜍,ng:advanced_coding:descriptors_fn,[DESCRIPTORSFN],advanced_coding,descriptors_fn,95.0,A,False,True,True,False,[DESCRI..],,emoji_extended,False,Risky Unicode range: emoji_extended; Long fallback: 15 chars
NG1151,🜤,ng:advanced_coding:descriptors_meta_1,[DESCRIPTORSMETA1],advanced_coding,descriptors_meta_1,95.0,A,False,True,True,True,[DESCRI..],descriptors_meta_1,emoji_extended,False,Risky Unicode range: emoji_extended; Long fallback: 18 chars; Generic numbered name
NG1152,⧅,ng:advanced_coding:descriptors_sys,[DESCRIPTORSSYS],advanced_coding,descriptors_sys,95.0,C,False,False,True,False,[DESCRI..],,safe,False,Long fallback: 16 chars
NG1153,⠿,ng:advanced_coding:descriptors_fn_1,[DESCRIPTORSFN1],advanced_coding,descriptors_fn_1,95.0,B,False,False,True,True,[DESCRI..],descriptors_fn_1,safe,False,Long fallback: 16 chars; Generic numbered name
NG1154,🝣,ng:advanced_coding:descriptors_core_1,[DESCRIPTORSCORE1],advanced_coding,descriptors_core_1,95.0,A,False,True,True,True,[DESCRI..],descriptors_core_1,emoji_extended,False,Risky Unicode range: emoji_extended; Long fallback: 18 chars; Generic numbered name
NG1155,⦣,ng:advanced_coding:descriptors_meta_2,[DESCRIPTORSMETA2],advanced_coding,descriptors_meta_2,95.0,B,False,False,True,True,[DESCRI..],descriptors_meta_2,safe,False,Long fallback: 18 chars; Generic numbered name
NG1156,❱,ng:advanced_coding:metaclasses_core,[METACLASSESCORE],advanced_coding,metaclasses_core,95.0,C,False,False,True,False,[METACL..],,safe,False,Long fallback: 17 chars
NG1157,⯛,ng:advanced_coding:metaclasses_meta_1,[METACLASSESMETA1],advanced_coding,metaclasses_meta_1,95.0,B,False,False,True,True,[METACL..],metaclasses_meta_1,safe,False,Long fallback: 18 chars; Generic numbered name
NG1158,🝓,ng:advanced_coding:metaclasses_sys_4,[METACLASSESSYS4],advanced_coding,metaclasses_sys_4,95.0,A,False,True,True,True,[METACL..],metaclasses_sys_4,emoji_extended,False,Risky Unicode range: emoji_extended; Long fallback: 17 chars; Generic numbered name
NG1159,🝚,ng:advanced_coding:metaclasses_2,[METACLASSES2],advanced_coding,metaclasses_2,95.0,A,False,True,True,True,[METACL..],metaclasses_2,emoji_extended,False,Risky Unicode range: emoji_extended; Long fallback: 14 chars; Generic numbered name
NG1160,⣃,ng:advanced_coding:metaclasses_ctrl_2,[METACLASSESCTRL2],advanced_coding,metaclasses_ctrl_2,95.0,B,False,False,True,True,[METACL..],metaclasses_ctrl_2,safe,False,Long fallback: 18 chars; Generic numbered name
NG1161,🛅,ng:advanced_coding:metaclasses_sys_5,[METACLASSESSYS5],advanced_coding,metaclasses_sys_5,95.0,A,False,True,True,True,[METACL..],metaclasses_sys_5,emoji_extended,False,Risky Unicode range: emoji_extended; Long fallback: 17 chars; Generic numbered name
NG1162,⨰,ng:advanced_coding:metaclasses_3,[METACLASSES3],advanced_coding,metaclasses_3,95.0,B,False,False,True,True,[METACL..],metaclasses_3,safe,False,Long fallback: 14 chars; Generic numbered name
NG1163,❍,ng:advanced_coding:metaclasses_op_1,[METACLASSESOP1],advanced_coding,metaclasses_op_1,95.0,B,False,False,True,True,[METACL..],metaclasses_op_1,safe,False,Long fallback: 16 chars; Generic numbered name
NG1164,⯅,ng:advanced_coding:metaclasses_op_2,[METACLASSESOP2],advanced_coding,metaclasses_op_2,95.0,B,False,False,True,True,[METACL..],metaclasses_op_2,safe,False,Long fallback: 16 chars; Generic numbered name
NG1165,🡪,ng:advanced_coding:metaclasses_meta_2,[METACLASSESMETA2],advanced_coding,metaclasses_meta_2,95.0,A,False,True,True,True,[METACL..],metaclasses_meta_2,emoji_extended,False,Risky Unicode range: emoji_extended; Long fallback: 18 chars; Generic numbered name
NG1166,🙎,ng:advanced_coding:metaclasses_ctrl_3,[METACLASSESCTRL3],advanced_coding,metaclasses_ctrl_3,95.0,A,False,True,True,True,[METACL..],metaclasses_ctrl_3,emoji_extended,False,Risky Unicode range: emoji_extended; Long fallback: 18 chars; Generic numbered name
NG1167,⥙,ng:advanced_coding:metaclasses_sys_6,[METACLASSESSYS6],advanced_coding,metaclasses_sys_6,95.0,B,False,False,True,True,[METACL..],metaclasses_sys_6,safe,False,Long fallback: 17 chars; Generic numbered name
NG1168,⥝,ng:advanced_coding:metaclasses_proc_1,[METACLASSESPROC1],advanced_coding,metaclasses_proc_1,95.0,B,False,False,True,True,[METACL..],metaclasses_proc_1,safe,False,Long fallback: 18 chars; Generic numbered name
NG1169,⤢,ng:advanced_coding:metaclasses_4,[METACLASSES4],advanced_coding,metaclasses_4,95.0,B,False,False,True,True,[METACL..],metaclasses_4,safe,False,Long fallback: 14 chars; Generic numbered name
NG1170,🝋,ng:advanced_coding:metaclasses_op_3,[METACLASSESOP3],advanced_coding,metaclasses_op_3,95.0,A,False,True,True,True,[METACL..],metaclasses_op_3,emoji_extended,False,Risky Unicode range: emoji_extended; Long fallback: 16 chars; Generic numbered name
NG1171,⮈,ng:advanced_coding:metaclasses_fn_1,[METACLASSESFN1],advanced_coding,metaclasses_fn_1,95.0,B,False,False,True,True,[METACL..],metaclasses_fn_1,safe,False,Long fallback: 16 chars; Generic numbered name
NG1172,🟪,ng:advanced_coding:metaclasses_sys_7,[METACLASSESSYS7],advanced_coding,metaclasses_sys_7,95.0,A,False,True,True,True,[METACL..],metaclasses_sys_7,emoji_extended,False,Risky Unicode range: emoji_extended; Long fallback: 17 chars; Generic numbered name
NG1173,⧩,ng:advanced_coding:metaclasses_meta_3,[METACLASSESMETA3],advanced_coding,metaclasses_meta_3,95.0,B,False,False,True,True,[METACL..],metaclasses_meta_3,safe,False,Long fallback: 18 chars; Generic numbered name
NG1174,🛆,ng:advanced_coding:metaclasses_meta_4,[METACLASSESMETA4],advanced_coding,metaclasses_meta_4,95.0,A,False,True,True,True,[METACL..],metaclasses_meta_4,emoji_extended,False,Risky Unicode range: emoji_extended; Long fallback: 18 chars; Generic numbered name
NG1175,🛋,ng:advanced_coding:metaclasses_core_1,[METACLASSESCORE1],advanced_coding,metaclasses_core_1,95.0,A,False,True,True,True,[METACL..],metaclasses_core_1,emoji_extended,False,Risky Unicode range: emoji_extended; Long fallback: 18 chars; Generic numbered name
NG1176,🔰,ng:advanced_coding:metaclasses_meta_5,[METACLASSESMETA5],advanced_coding,metaclasses_meta_5,95.0,A,False,True,True,True,[METACL..],metaclasses_meta_5,emoji_extended,False,Risky Unicode range: emoji_extended; Long fallback: 18 chars; Generic numbered name
NG1177,📼,ng:advanced_coding:metaclasses_sys_8,[METACLASSESSYS8],advanced_coding,metaclasses_sys_8,95.0,A,False,True,True,True,[METACL..],metaclasses_sys_8,emoji_extended,False,Risky Unicode range: emoji_extended; Long fallback: 17 chars; Generic numbered name
NG1178,❠,ng:advanced_coding:metaclasses_sys_9,[METACLASSESSYS9],advanced_coding,metaclasses_sys_9,95.0,B,False,False,True,True,[METACL..],metaclasses_sys_9,safe,False,Long fallback: 17 chars; Generic numbered name
NG1179,🝞,ng:advanced_coding:metaclasses_core_2,[METACLASSESCORE2],advanced_coding,metaclasses_core_2,95.0,A,False,True,True,True,[METACL..],metaclasses_core_2,emoji_extended,False,Risky Unicode range: emoji_extended; Long fallback: 18 chars; Generic numbered name
NG1180,🖞,ng:advanced_coding:metaclasses_meta_6,[METACLASSESMETA6],advanced_coding,metaclasses_meta_6,95.0,A,False,True,True,True,[METACL..],metaclasses_meta_6,emoji_extended,False,Risky Unicode range: emoji_extended; Long fallback: 18 chars; Generic numbered name
NG1181,⧷,ng:advanced_coding:metaclasses_fn_2,[METACLASSESFN2],advanced_coding,metaclasses_fn_2,95.0,B,False,False,True,True,[METACL..],metaclasses_fn_2,safe,False,Long fallback: 16 chars; Generic numbered name
NG1182,🦀,ng:advanced_coding:metaclasses_fn_3,[METACLASSESFN3],advanced_coding,metaclasses_fn_3,95.0,A,False,True,True,True,[METACL..],metaclasses_fn_3,emoji_extended,False,Risky Unicode range: emoji_extended; Long fallback: 16 chars; Generic numbered name
NG1183,🌇,ng:advanced_coding:metaclasses_sys_10,[METACLASSESSYS10],advanced_coding,metaclasses_sys_10,95.0,A,False,True,True,True,[METACL..],metaclasses_sys_10,emoji_extended,False,Risky Unicode range: emoji_extended; Long fallback: 18 chars; Generic numbered name
NG1184,🛹,ng:advanced_coding:metaclasses_meta_7,[METACLASSESMETA7],advanced_coding,metaclasses_meta_7,95.0,A,False,True,True,True,[METACL..],metaclasses_meta_7,emoji_extended,False,Risky Unicode range: emoji_extended; Long fallback: 18 chars; Generic numbered name
NG1185,🚩,ng:advanced_coding:metaclasses_proc_2,[METACLASSESPROC2],advanced_coding,metaclasses_proc_2,95.0,A,False,True,True,True,[METACL..],metaclasses_proc_2,emoji_extended,False,Risky Unicode range: emoji_extended; Long fallback: 18 chars; Generic numbered name
NG1186,🙌,ng:advanced_coding:metaclasses_ctrl_4,[METACLASSESCTRL4],advanced_coding,metaclasses_ctrl_4,95.0,A,False,True,True,True,[METACL..],metaclasses_ctrl_4,emoji_extended,False,Risky Unicode range: emoji_extended; Long fallback: 18 chars; Generic numbered name
NG1187,⠒,ng:advanced_coding:metaclasses_core_3,[METACLASSESCORE3],advanced_coding,metaclasses_core_3,95.0,B,False,False,True,True,[METACL..],metaclasses_core_3,safe,False,Long fallback: 18 chars; Generic numbered name
NG1188,⩶,ng:meta_programming:codeasdata_proc,[CODEASDATAPROC],meta_programming,codeasdata_proc,95.0,C,False,False,True,False,[CODEAS..],,safe,False,Long fallback: 16 chars
NG1189,⧿,ng:meta_programming:codeasdata,[CODEASDATA],meta_programming,codeasdata,95.0,C,False,False,True,False,[CODEAS..],,safe,False,Long fallback: 12 chars
NG1190,⦼,ng:meta_programming:codeasdata_sys,[CODEASDATASYS],meta_programming,codeasdata_sys,95.0,C,False,False,True,False,[CODEAS..],,safe,False,Long fallback: 15 chars
NG1191,🗟,ng:meta_programming:codeasdata_core,[CODEASDATACORE],meta_programming,codeasdata_core,95.0,A,False,True,True,False,[CODEAS..],,emoji_extended,False,Risky Unicode range: emoji_extended; Long fallback: 16 chars
NG1192,⥈,ng:meta_programming:codeasdata_fn,[CODEASDATAFN],meta_programming,codeasdata_fn,95.0,C,False,False,True,False,[CODEAS..],,safe,False,Long fallback: 14 chars
NG1193,🙅,ng:meta_programming:codeasdata_op,[CODEASDATAOP],meta_programming,codeasdata_op,95.0,A,False,True,True,False,[CODEAS..],,emoji_extended,False,Risky Unicode range: emoji_extended; Long fallback: 14 chars
NG1194,⯚,ng:meta_programming:codeasdata_1,[CODEASDATA1],meta_programming,codeasdata_1,95.0,B,False,False,True,True,[CODEAS..],codeasdata_1,safe,False,Long fallback: 13 chars; Generic numbered name
NG1195,⣹,ng:meta_programming:codeasdata_2,[CODEASDATA2],meta_programming,codeasdata_2,95.0,B,False,False,True,True,[CODEAS..],codeasdata_2,safe,False,Long fallback: 13 chars; Generic numbered name
NG1196,🚂,ng:meta_programming:codeasdata_3,[CODEASDATA3],meta_programming,codeasdata_3,95.0,A,False,True,True,True,[CODEAS..],codeasdata_3,emoji_extended,False,Risky Unicode range: emoji_extended; Long fallback: 13 chars; Generic numbered name
NG1197,⥯,ng:meta_programming:codeasdata_meta,[CODEASDATAMETA],meta_programming,codeasdata_meta,95.0,C,False,False,True,False,[CODEAS..],,safe,False,Long fallback: 16 chars
NG1198,⥍,ng:meta_programming:codeasdata_core_1,[CODEASDATACORE1],meta_programming,codeasdata_core_1,95.0,B,False,False,True,True,[CODEAS..],codeasdata_core_1,safe,False,Long fallback: 17 chars; Generic numbered name
NG1199,🠟,ng:meta_programming:codeasdata_op_1,[CODEASDATAOP1],meta_programming,codeasdata_op_1,95.0,A,False,True,True,True,[CODEAS..],codeasdata_op_1,emoji_extended,False,Risky Unicode range: emoji_extended; Long fallback: 15 chars; Generic numbered name
NG1200,🦢,ng:meta_programming:codeasdata_4,[CODEASDATA4],meta_programming,codeasdata_4,95.0,A,False,True,True,True,[CODEAS..],codeasdata_4,emoji_extended,False,Risky Unicode range: emoji_extended; Long fallback: 13 chars; Generic numbered name
NG1201,🥤,ng:meta_programming:codeasdata_sys_1,[CODEASDATASYS1],meta_programming,codeasdata_sys_1,95.0,A,False,True,True,True,[CODEAS..],codeasdata_sys_1,emoji_extended,False,Risky Unicode range: emoji_extended; Long fallback: 16 chars; Generic numbered name
NG1202,🝻,ng:meta_programming:macrosystems_ctrl,[MACROSYSTEMSCTRL],meta_programming,macrosystems_ctrl,95.0,A,False,True,True,False,[MACROS..],,emoji_extended,False,Risky Unicode range: emoji_extended; Long fallback: 18 chars
NG1203,🥆,ng:meta_programming:macrosystems_ctrl_1,[MACROSYSTEMSCTRL1],meta_programming,macrosystems_ctrl_1,95.0,A,False,True,True,True,[MACROS..],macrosystems_ctrl_1,emoji_extended,False,Risky Unicode range: emoji_extended; Long fallback: 19 chars; Generic numbered name
NG1204,🢩,ng:meta_programming:macrosystems_proc,[MACROSYSTEMSPROC],meta_programming,macrosystems_proc,95.0,A,False,True,True,False,[MACROS..],,emoji_extended,False,Risky Unicode range: emoji_extended; Long fallback: 18 chars
NG1205,🟘,ng:meta_programming:macrosystems_fn,[MACROSYSTEMSFN],meta_programming,macrosystems_fn,95.0,A,False,True,True,False,[MACROS..],,emoji_extended,False,Risky Unicode range: emoji_extended; Long fallback: 16 chars
NG1206,✽,ng:meta_programming:macrosystems_ctrl_2,[MACROSYSTEMSCTRL2],meta_programming,macrosystems_ctrl_2,95.0,B,False,False,True,True,[MACROS..],macrosystems_ctrl_2,safe,False,Long fallback: 19 chars; Generic numbered name
NG1207,🝔,ng:meta_programming:macrosystems,[MACROSYSTEMS],meta_programming,macrosystems,95.0,A,False,True,True,False,[MACROS..],,emoji_extended,False,Risky Unicode range: emoji_extended; Long fallback: 14 chars
NG1208,⢁,ng:meta_programming:macrosystems_sys,[MACROSYSTEMSSYS],meta_programming,macrosystems_sys,95.0,C,False,False,True,False,[MACROS..],,safe,False,Long fallback: 17 chars
NG1209,😣,ng:meta_programming:macrosystems_1,[MACROSYSTEMS1],meta_programming,macrosystems_1,95.0,A,False,True,True,True,[MACROS..],macrosystems_1,emoji_extended,False,Risky Unicode range: emoji_extended; Long fallback: 15 chars; Generic numbered name
NG1210,🜆,ng:meta_programming:macrosystems_2,[MACROSYSTEMS2],meta_programming,macrosystems_2,95.0,A,False,True,True,True,[MACROS..],macrosystems_2,emoji_extended,False,Risky Unicode range: emoji_extended; Long fallback: 15 chars; Generic numbered name
NG1211,⡶,ng:meta_programming:macrosystems_op,[MACROSYSTEMSOP],meta_programming,macrosystems_op,95.0,C,False,False,True,False,[MACROS..],,safe,False,Long fallback: 16 chars
NG1212,⦭,ng:meta_programming:macrosystems_op_1,[MACROSYSTEMSOP1],meta_programming,macrosystems_op_1,95.0,B,False,False,True,True,[MACROS..],macrosystems_op_1,safe,False,Long fallback: 17 chars; Generic numbered name
NG1213,⥅,ng:meta_programming:macrosystems_3,[MACROSYSTEMS3],meta_programming,macrosystems_3,95.0,B,False,False,True,True,[MACROS..],macrosystems_3,safe,False,Long fallback: 15 chars; Generic numbered name
NG1214,⢹,ng:meta_programming:macrosystems_op_2,[MACROSYSTEMSOP2],meta_programming,macrosystems_op_2,95.0,B,False,False,True,True,[MACROS..],macrosystems_op_2,safe,False,Long fallback: 17 chars; Generic numbered name
NG1215,⧛,ng:meta_programming:macrosystems_proc_1,[MACROSYSTEMSPROC1],meta_programming,macrosystems_proc_1,95.0,B,False,False,True,True,[MACROS..],macrosystems_proc_1,safe,False,Long fallback: 19 chars; Generic numbered name
NG1216,🞂,ng:meta_programming:stagedcomputation,[STAGEDCOMPUTATION],meta_programming,stagedcomputation,95.0,A,False,True,True,False,[STAGED..],,emoji_extended,False,Risky Unicode range: emoji_extended; Long fallback: 19 chars
NG1217,🥾,ng:meta_programming:stagedcomputation_1,[STAGEDCOMPUTATION1],meta_programming,stagedcomputation_1,95.0,A,False,True,True,True,[STAGED..],stagedcomputation_1,emoji_extended,False,Risky Unicode range: emoji_extended; Long fallback: 20 chars; Generic numbered name
NG1218,⮜,ng:meta_programming:stagedcomputation_2,[STAGEDCOMPUTATION2],meta_programming,stagedcomputation_2,95.0,B,False,False,True,True,[STAGED..],stagedcomputation_2,safe,False,Long fallback: 20 chars; Generic numbered name
NG1219,🠙,ng:meta_programming:stagedcomputation_3,[STAGEDCOMPUTATION3],meta_programming,stagedcomputation_3,95.0,A,False,True,True,True,[STAGED..],stagedcomputation_3,emoji_extended,False,Risky Unicode range: emoji_extended; Long fallback: 20 chars; Generic numbered name
NG1220,🟍,ng:meta_programming:stagedcomputation_4,[STAGEDCOMPUTATION4],meta_programming,stagedcomputation_4,95.0,A,False,True,True,True,[STAGED..],stagedcomputation_4,emoji_extended,False,Risky Unicode range: emoji_extended; Long fallback: 20 chars; Generic numbered name
NG1221,⬬,ng:meta_programming:stagedcomputation_5,[STAGEDCOMPUTATION5],meta_programming,stagedcomputation_5,95.0,B,False,False,True,True,[STAGED..],stagedcomputation_5,safe,False,Long fallback: 20 chars; Generic numbered name
NG1222,⛰,ng:meta_programming:stagedcomputation_6,[STAGEDCOMPUTATION6],meta_programming,stagedcomputation_6,95.0,A,False,True,True,True,[STAGED..],stagedcomputation_6,miscellaneous_symbols,False,Risky Unicode range: miscellaneous_symbols; Long fallback: 20 chars; Generic numbered name
NG1223,😛,ng:meta_programming:stagedcomputation_7,[STAGEDCOMPUTATION7],meta_programming,stagedcomputation_7,95.0,A,False,True,True,True,[STAGED..],stagedcomputation_7,emoji_extended,False,Risky Unicode range: emoji_extended; Long fallback: 20 chars; Generic numbered name
NG1224,⮘,ng:meta_programming:stagedcomputation_8,[STAGEDCOMPUTATION8],meta_programming,stagedcomputation_8,95.0,B,False,False,True,True,[STAGED..],stagedcomputation_8,safe,False,Long fallback: 20 chars; Generic numbered name
NG1225,🔤,ng:meta_programming:stagedcomputation_9,[STAGEDCOMPUTATION9],meta_programming,stagedcomputation_9,95.0,A,False,True,True,True,[STAGED..],stagedcomputation_9,emoji_extended,False,Risky Unicode range: emoji_extended; Long fallback: 20 chars; Generic numbered name
NG1226,⣘,ng:meta_programming:partialevaluation_1,[PARTIALEVALUATION1],meta_programming,partialevaluation_1,95.0,B,False,False,True,True,[PARTIA..],partialevaluation_1,safe,False,Long fallback: 20 chars; Generic numbered name
NG1227,👢,ng:meta_programming:partialevaluation_2,[PARTIALEVALUATION2],meta_programming,partialevaluation_2,95.0,A,False,True,True,True,[PARTIA..],partialevaluation_2,emoji_extended,False,Risky Unicode range: emoji_extended; Long fallback: 20 chars; Generic numbered name
NG1228,🛈,ng:meta_programming:partialevaluation_3,[PARTIALEVALUATION3],meta_programming,partialevaluation_3,95.0,A,False,True,True,True,[PARTIA..],partialevaluation_3,emoji_extended,False,Risky Unicode range: emoji_extended; Long fallback: 20 chars; Generic numbered name
NG1229,🛰,ng:meta_programming:partialevaluation_4,[PARTIALEVALUATION4],meta_programming,partialevaluation_4,95.0,A,False,True,True,True,[PARTIA..],partialevaluation_4,emoji_extended,False,Risky Unicode range: emoji_extended; Long fallback: 20 chars; Generic numbered name
NG1230,⛜,ng:meta_programming:partialevaluation_5,[PARTIALEVALUATION5],meta_programming,partialevaluation_5,95.0,A,False,True,True,True,[PARTIA..],partialevaluation_5,miscellaneous_symbols,False,Risky Unicode range: miscellaneous_symbols; Long fallback: 20 chars; Generic numbered name
NG1231,🦅,ng:meta_programming:partialevaluation_6,[PARTIALEVALUATION6],meta_programming,partialevaluation_6,95.0,A,False,True,True,True,[PARTIA..],partialevaluation_6,emoji_extended,False,Risky Unicode range: emoji_extended; Long fallback: 20 chars; Generic numbered name
NG1232,⣂,ng:meta_programming:partialevaluation_7,[PARTIALEVALUATION7],meta_programming,partialevaluation_7,95.0,B,False,False,True,True,[PARTIA..],partialevaluation_7,safe,False,Long fallback: 20 chars; Generic numbered name
NG1233,⡆,ng:meta_programming:partialevaluation_8,[PARTIALEVALUATION8],meta_programming,partialevaluation_8,95.0,B,False,False,True,True,[PARTIA..],partialevaluation_8,safe,False,Long fallback: 20 chars; Generic numbered name
NG1234,🐽,ng:meta_programming:partialevaluation_9,[PARTIALEVALUATION9],meta_programming,partialevaluation_9,95.0,A,False,True,True,True,[PARTIA..],partialevaluation_9,emoji_extended,False,Risky Unicode range: emoji_extended; Long fallback: 20 chars; Generic numbered name
NG1235,⨹,ng:meta_programming:programsynthesis_fn,[PROGRAMSYNTHESISFN],meta_programming,programsynthesis_fn,95.0,C,False,False,True,False,[PROGRA..],,safe,False,Long fallback: 20 chars
NG1236,⡭,ng:meta_programming:programsynthesis_op,[PROGRAMSYNTHESISOP],meta_programming,programsynthesis_op,95.0,C,False,False,True,False,[PROGRA..],,safe,False,Long fallback: 20 chars
NG1237,⩟,ng:meta_programming:programsynthesis,[PROGRAMSYNTHESIS],meta_programming,programsynthesis,95.0,C,False,False,True,False,[PROGRA..],,safe,False,Long fallback: 18 chars
NG1238,🟥,ng:meta_programming:programsynthesis_1,[PROGRAMSYNTHESIS1],meta_programming,programsynthesis_1,95.0,A,False,True,True,True,[PROGRA..],programsynthesis_1,emoji_extended,False,Risky Unicode range: emoji_extended; Long fallback: 19 chars; Generic numbered name
NG1239,😢,ng:meta_programming:programsynthesis_2,[PROGRAMSYNTHESIS2],meta_programming,programsynthesis_2,95.0,A,False,True,True,True,[PROGRA..],programsynthesis_2,emoji_extended,False,Risky Unicode range: emoji_extended; Long fallback: 19 chars; Generic numbered name
NG1240,🚁,ng:meta_programming:programsynthesis_3,[PROGRAMSYNTHESIS3],meta_programming,programsynthesis_3,95.0,A,False,True,True,True,[PROGRA..],programsynthesis_3,emoji_extended,False,Risky Unicode range: emoji_extended; Long fallback: 19 chars; Generic numbered name
NG1241,⦴,ng:meta_programming:programsynthesis_4,[PROGRAMSYNTHESIS4],meta_programming,programsynthesis_4,95.0,B,False,False,True,True,[PROGRA..],programsynthesis_4,safe,False,Long fallback: 19 chars; Generic numbered name
NG1242,🠭,ng:meta_programming:programsynthesis_5,[PROGRAMSYNTHESIS5],meta_programming,programsynthesis_5,95.0,A,False,True,True,True,[PROGRA..],programsynthesis_5,emoji_extended,False,Risky Unicode range: emoji_extended; Long fallback: 19 chars; Generic numbered name
NG1243,👿,ng:meta_programming:programsynthesis_6,[PROGRAMSYNTHESIS6],meta_programming,programsynthesis_6,95.0,A,False,True,True,True,[PROGRA..],programsynthesis_6,emoji_extended,False,Risky Unicode range: emoji_extended; Long fallback: 19 chars; Generic numbered name
NG1244,⣩,ng:meta_programming:programsynthesis_7,[PROGRAMSYNTHESIS7],meta_programming,programsynthesis_7,95.0,B,False,False,True,True,[PROGRA..],programsynthesis_7,safe,False,Long fallback: 19 chars; Generic numbered name
NG1245,🤕,ng:meta_programming:programsynthesis_8,[PROGRAMSYNTHESIS8],meta_programming,programsynthesis_8,95.0,A,False,True,True,True,[PROGRA..],programsynthesis_8,emoji_extended,False,Risky Unicode range: emoji_extended; Long fallback: 19 chars; Generic numbered name
NG1246,❥,ng:meta_programming:programsynthesis_9,[PROGRAMSYNTHESIS9],meta_programming,programsynthesis_9,95.0,B,False,False,True,True,[PROGRA..],programsynthesis_9,safe,False,Long fallback: 19 chars; Generic numbered name
NG1247,🍓,ng:meta_programming:programsynthesis_10,[PROGRAMSYNTHESIS10],meta_programming,programsynthesis_10,95.0,A,False,True,True,True,[PROGRA..],programsynthesis_10,emoji_extended,False,Risky Unicode range: emoji_extended; Long fallback: 20 chars; Generic numbered name
NG1248,⧥,ng:meta_programming:programsynthesis_11,[PROGRAMSYNTHESIS11],meta_programming,programsynthesis_11,95.0,B,False,False,True,True,[PROGRA..],programsynthesis_11,safe,False,Long fallback: 20 chars; Generic numbered name
NG1249,⭐,ng:meta_programming:codetransformation,[CODETRANSFORMATION],meta_programming,codetransformation,95.0,C,False,False,True,False,[CODETR..],,safe,False,Long fallback: 20 chars
NG1250,🤇,ng:distributed_systems:raft_core,[RAFTCORE],distributed_systems,raft_core,95.0,B,False,True,False,False,,,emoji_extended,False,Risky Unicode range: emoji_extended
NG1251,🛨,ng:distributed_systems:raft,[RAFT],distributed_systems,raft,95.0,B,False,True,False,False,,,emoji_extended,False,Risky Unicode range: emoji_extended
NG1252,🠰,ng:distributed_systems:paxos_core,[PAXOSCORE],distributed_systems,paxos_core,95.0,A,False,True,True,False,[PAXOSC..],,emoji_extended,False,Risky Unicode range: emoji_extended; Long fallback: 11 chars
NG1253,🜄,ng:distributed_systems:pbft_sys,[PBFTSYS],distributed_systems,pbft_sys,95.0,B,False,True,False,False,,,emoji_extended,False,Risky Unicode range: emoji_extended
NG1254,🤾,ng:distributed_systems:raft_op,[RAFTOP],distributed_systems,raft_op,95.0,B,False,True,False,False,,,emoji_extended,False,Risky Unicode range: emoji_extended
NG1255,⮭,ng:distributed_systems:consensus_op,[CONSENSUSOP],distributed_systems,consensus_op,95.0,C,False,False,True,False,[CONSEN..],,safe,False,Long fallback: 13 chars
NG1256,🏦,ng:distributed_systems:consensus_op_1,[CONSENSUSOP1],distributed_systems,consensus_op_1,95.0,A,False,True,True,True,[CONSEN..],consensus_op_1,emoji_extended,False,Risky Unicode range: emoji_extended; Long fallback: 14 chars; Generic numbered name
NG1257,📭,ng:distributed_systems:distributedlocks_fn,[DISTRIBUTEDLOCKSFN],distributed_systems,distributedlocks_fn,95.0,A,False,True,True,False,[DISTRI..],,emoji_extended,False,Risky Unicode range: emoji_extended; Long fallback: 20 chars
NG1258,⢵,ng:distributed_systems:distributedlocks,[DISTRIBUTEDLOCKS],distributed_systems,distributedlocks,95.0,C,False,False,True,False,[DISTRI..],,safe,False,Long fallback: 18 chars
NG1259,⧴,ng:distributed_systems:distributedlocks_1,[DISTRIBUTEDLOCKS1],distributed_systems,distributedlocks_1,95.0,B,False,False,True,True,[DISTRI..],distributedlocks_1,safe,False,Long fallback: 19 chars; Generic numbered name
NG1260,🝇,ng:distributed_systems:distributedlocks_3,[DISTRIBUTEDLOCKS3],distributed_systems,distributedlocks_3,95.0,A,False,True,True,True,[DISTRI..],distributedlocks_3,emoji_extended,False,Risky Unicode range: emoji_extended; Long fallback: 19 chars; Generic numbered name
NG1261,⤡,ng:distributed_systems:distributedlocks_4,[DISTRIBUTEDLOCKS4],distributed_systems,distributedlocks_4,95.0,B,False,False,True,True,[DISTRI..],distributedlocks_4,safe,False,Long fallback: 19 chars; Generic numbered name
NG1262,⨋,ng:distributed_systems:distributedlocks_5,[DISTRIBUTEDLOCKS5],distributed_systems,distributedlocks_5,95.0,B,False,False,True,True,[DISTRI..],distributedlocks_5,safe,False,Long fallback: 19 chars; Generic numbered name
NG1263,✟,ng:distributed_systems:vectorclocks_op,[VECTORCLOCKSOP],distributed_systems,vectorclocks_op,95.0,C,False,False,True,False,[VECTOR..],,safe,False,Long fallback: 16 chars
NG1264,🧶,ng:distributed_systems:vectorclocks_ctrl,[VECTORCLOCKSCTRL],distributed_systems,vectorclocks_ctrl,95.0,A,False,True,True,False,[VECTOR..],,emoji_extended,False,Risky Unicode range: emoji_extended; Long fallback: 18 chars
NG1265,🠔,ng:distributed_systems:vectorclocks_sys,[VECTORCLOCKSSYS],distributed_systems,vectorclocks_sys,95.0,A,False,True,True,False,[VECTOR..],,emoji_extended,False,Risky Unicode range: emoji_extended; Long fallback: 17 chars
NG1266,⮊,ng:distributed_systems:vectorclocks_op_1,[VECTORCLOCKSOP1],distributed_systems,vectorclocks_op_1,95.0,B,False,False,True,True,[VECTOR..],vectorclocks_op_1,safe,False,Long fallback: 17 chars; Generic numbered name
NG1267,⣔,ng:distributed_systems:vectorclocks_meta,[VECTORCLOCKSMETA],distributed_systems,vectorclocks_meta,95.0,C,False,False,True,False,[VECTOR..],,safe,False,Long fallback: 18 chars
NG1268,🥿,ng:distributed_systems:vectorclocks_proc,[VECTORCLOCKSPROC],distributed_systems,vectorclocks_proc,95.0,A,False,True,True,False,[VECTOR..],,emoji_extended,False,Risky Unicode range: emoji_extended; Long fallback: 18 chars
NG1269,⨤,ng:distributed_systems:captheorem_proc,[CAPTHEOREMPROC],distributed_systems,captheorem_proc,95.0,C,False,False,True,False,[CAPTHE..],,safe,False,Long fallback: 16 chars
NG1270,⥬,ng:distributed_systems:captheorem_core_2,[CAPTHEOREMCORE2],distributed_systems,captheorem_core_2,95.0,B,False,False,True,True,[CAPTHE..],captheorem_core_2,safe,False,Long fallback: 17 chars; Generic numbered name
NG1271,🚧,ng:distributed_systems:captheorem_fn,[CAPTHEOREMFN],distributed_systems,captheorem_fn,95.0,A,False,True,True,False,[CAPTHE..],,emoji_extended,False,Risky Unicode range: emoji_extended; Long fallback: 14 chars
NG1272,✪,ng:distributed_systems:captheorem_op,[CAPTHEOREMOP],distributed_systems,captheorem_op,95.0,C,False,False,True,False,[CAPTHE..],,safe,False,Long fallback: 14 chars
NG1273,⮹,ng:distributed_systems:captheorem,[CAPTHEOREM],distributed_systems,captheorem,95.0,C,False,False,True,False,[CAPTHE..],,safe,False,Long fallback: 12 chars
NG1274,⡪,ng:distributed_systems:captheorem_proc_1,[CAPTHEOREMPROC1],distributed_systems,captheorem_proc_1,95.0,B,False,False,True,True,[CAPTHE..],captheorem_proc_1,safe,False,Long fallback: 17 chars; Generic numbered name
NG1275,🦭,ng:distributed_systems:gossipprotocols_op,[GOSSIPPROTOCOLSOP],distributed_systems,gossipprotocols_op,95.0,A,False,True,True,False,[GOSSIP..],,emoji_extended,False,Risky Unicode range: emoji_extended; Long fallback: 19 chars
NG1276,⡡,ng:distributed_systems:gossipprotocols_1,[GOSSIPPROTOCOLS1],distributed_systems,gossipprotocols_1,95.0,B,False,False,True,True,[GOSSIP..],gossipprotocols_1,safe,False,Long fallback: 18 chars; Generic numbered name
NG1277,⦤,ng:distributed_systems:gossipprotocols_fn,[GOSSIPPROTOCOLSFN],distributed_systems,gossipprotocols_fn,95.0,C,False,False,True,False,[GOSSIP..],,safe,False,Long fallback: 19 chars
NG1278,⣙,ng:distributed_systems:gossipprotocols_op_2,[GOSSIPPROTOCOLSOP2],distributed_systems,gossipprotocols_op_2,95.0,B,False,False,True,True,[GOSSIP..],gossipprotocols_op_2,safe,False,Long fallback: 20 chars; Generic numbered name
NG1279,⥠,ng:distributed_systems:gossipprotocols_sys,[GOSSIPPROTOCOLSSYS],distributed_systems,gossipprotocols_sys,95.0,C,False,False,True,False,[GOSSIP..],,safe,False,Long fallback: 20 chars
NG1280,🙂,ng:distributed_systems:gossipprotocols_fn_1,[GOSSIPPROTOCOLSFN1],distributed_systems,gossipprotocols_fn_1,95.0,A,False,True,True,True,[GOSSIP..],gossipprotocols_fn_1,emoji_extended,False,Risky Unicode range: emoji_extended; Long fallback: 20 chars; Generic numbered name
NG1281,⪾,ng:distributed_systems:gossipprotocols_fn_2,[GOSSIPPROTOCOLSFN2],distributed_systems,gossipprotocols_fn_2,95.0,B,False,False,True,True,[GOSSIP..],gossipprotocols_fn_2,safe,False,Long fallback: 20 chars; Generic numbered name
NG1282,⪂,ng:distributed_systems:leaderelection,[LEADERELECTION],distributed_systems,leaderelection,95.0,C,False,False,True,False,[LEADER..],,safe,False,Long fallback: 16 chars
NG1283,🤭,ng:distributed_systems:leaderelection_core,[LEADERELECTIONCORE],distributed_systems,leaderelection_core,95.0,A,False,True,True,False,[LEADER..],,emoji_extended,False,Risky Unicode range: emoji_extended; Long fallback: 20 chars
NG1284,🜰,ng:distributed_systems:leaderelection_ctrl,[LEADERELECTIONCTRL],distributed_systems,leaderelection_ctrl,95.0,A,False,True,True,False,[LEADER..],,emoji_extended,False,Risky Unicode range: emoji_extended; Long fallback: 20 chars
NG1285,⪚,ng:distributed_systems:leaderelection_fn,[LEADERELECTIONFN],distributed_systems,leaderelection_fn,95.0,C,False,False,True,False,[LEADER..],,safe,False,Long fallback: 18 chars
NG1286,🟅,ng:distributed_systems:leaderelection_op,[LEADERELECTIONOP],distributed_systems,leaderelection_op,95.0,A,False,True,True,False,[LEADER..],,emoji_extended,False,Risky Unicode range: emoji_extended; Long fallback: 18 chars
NG1287,🝬,ng:distributed_systems:leaderelection_meta,[LEADERELECTIONMETA],distributed_systems,leaderelection_meta,95.0,A,False,True,True,False,[LEADER..],,emoji_extended,False,Risky Unicode range: emoji_extended; Long fallback: 20 chars
NG1288,⦷,ng:distributed_systems:leaderelection_op_1,[LEADERELECTIONOP1],distributed_systems,leaderelection_op_1,95.0,B,False,False,True,True,[LEADER..],leaderelection_op_1,safe,False,Long fallback: 19 chars; Generic numbered name
NG1289,⤥,ng:distributed_systems:leaderelection_proc,[LEADERELECTIONPROC],distributed_systems,leaderelection_proc,95.0,C,False,False,True,False,[LEADER..],,safe,False,Long fallback: 20 chars
NG1290,🧆,ng:distributed_systems:sharding_core,[SHARDINGCORE],distributed_systems,sharding_core,95.0,A,False,True,True,False,[SHARDI..],,emoji_extended,False,Risky Unicode range: emoji_extended; Long fallback: 14 chars
NG1291,🧧,ng:distributed_systems:sharding_fn,[SHARDINGFN],distributed_systems,sharding_fn,95.0,A,False,True,True,False,[SHARDI..],,emoji_extended,False,Risky Unicode range: emoji_extended; Long fallback: 12 chars
NG1292,🝟,ng:distributed_systems:sharding_sys,[SHARDINGSYS],distributed_systems,sharding_sys,95.0,A,False,True,True,False,[SHARDI..],,emoji_extended,False,Risky Unicode range: emoji_extended; Long fallback: 13 chars
NG1293,⬪,ng:distributed_systems:sharding_meta,[SHARDINGMETA],distributed_systems,sharding_meta,95.0,C,False,False,True,False,[SHARDI..],,safe,False,Long fallback: 14 chars
NG1294,🦃,ng:distributed_systems:sharding_meta_1,[SHARDINGMETA1],distributed_systems,sharding_meta_1,95.0,A,False,True,True,True,[SHARDI..],sharding_meta_1,emoji_extended,False,Risky Unicode range: emoji_extended; Long fallback: 15 chars; Generic numbered name
NG1295,⯊,ng:distributed_systems:sharding_op_1,[SHARDINGOP1],distributed_systems,sharding_op_1,95.0,B,False,False,True,True,[SHARDI..],sharding_op_1,safe,False,Long fallback: 13 chars; Generic numbered name
NG1296,🧫,ng:distributed_systems:sharding_op_2,[SHARDINGOP2],distributed_systems,sharding_op_2,95.0,A,False,True,True,True,[SHARDI..],sharding_op_2,emoji_extended,False,Risky Unicode range: emoji_extended; Long fallback: 13 chars; Generic numbered name
NG1297,⛈,ng:distributed_systems:sharding_sys_1,[SHARDINGSYS1],distributed_systems,sharding_sys_1,95.0,A,False,True,True,True,[SHARDI..],sharding_sys_1,miscellaneous_symbols,False,Risky Unicode range: miscellaneous_symbols; Long fallback: 14 chars; Generic numbered name
NG1298,⭦,ng:distributed_systems:replication,[REPLICATION],distributed_systems,replication,95.0,C,False,False,True,False,[REPLIC..],,safe,False,Long fallback: 13 chars
NG1299,🡧,ng:distributed_systems:replication_sys,[REPLICATIONSYS],distributed_systems,replication_sys,95.0,A,False,True,True,False,[REPLIC..],,emoji_extended,False,Risky Unicode range: emoji_extended; Long fallback: 16 chars
NG1300,💯,ng:distributed_systems:replication_ctrl,[REPLICATIONCTRL],distributed_systems,replication_ctrl,95.0,A,False,True,True,False,[REPLIC..],,emoji_extended,False,Risky Unicode range: emoji_extended; Long fallback: 17 chars
NG1301,⯼,ng:distributed_systems:replication_fn,[REPLICATIONFN],distributed_systems,replication_fn,95.0,C,False,False,True,False,[REPLIC..],,safe,False,Long fallback: 15 chars
NG1302,⧉,ng:distributed_systems:replication_op,[REPLICATIONOP],distributed_systems,replication_op,95.0,C,False,False,True,False,[REPLIC..],,safe,False,Long fallback: 15 chars
NG1303,🥉,ng:distributed_systems:replication_1,[REPLICATION1],distributed_systems,replication_1,95.0,A,False,True,True,True,[REPLIC..],replication_1,emoji_extended,False,Risky Unicode range: emoji_extended; Long fallback: 14 chars; Generic numbered name
NG1304,⠋,ng:distributed_systems:replication_sys_1,[REPLICATIONSYS1],distributed_systems,replication_sys_1,95.0,B,False,False,True,True,[REPLIC..],replication_sys_1,safe,False,Long fallback: 17 chars; Generic numbered name
NG1305,🛒,ng:distributed_systems:loadbalancing,[LOADBALANCING],distributed_systems,loadbalancing,95.0,A,False,True,True,False,[LOADBA..],,emoji_extended,False,Risky Unicode range: emoji_extended; Long fallback: 15 chars
NG1306,🧁,ng:distributed_systems:loadbalancing_1,[LOADBALANCING1],distributed_systems,loadbalancing_1,95.0,A,False,True,True,True,[LOADBA..],loadbalancing_1,emoji_extended,False,Risky Unicode range: emoji_extended; Long fallback: 16 chars; Generic numbered name
NG1307,🚛,ng:distributed_systems:loadbalancing_sys,[LOADBALANCINGSYS],distributed_systems,loadbalancing_sys,95.0,A,False,True,True,False,[LOADBA..],,emoji_extended,False,Risky Unicode range: emoji_extended; Long fallback: 18 chars
NG1308,💰,ng:distributed_systems:loadbalancing_op,[LOADBALANCINGOP],distributed_systems,loadbalancing_op,95.0,A,False,True,True,False,[LOADBA..],,emoji_extended,False,Risky Unicode range: emoji_extended; Long fallback: 17 chars
NG1309,⤫,ng:distributed_systems:loadbalancing_proc,[LOADBALANCINGPROC],distributed_systems,loadbalancing_proc,95.0,C,False,False,True,False,[LOADBA..],,safe,False,Long fallback: 19 chars
NG1310,⧽,ng:distributed_systems:loadbalancing_ctrl,[LOADBALANCINGCTRL],distributed_systems,loadbalancing_ctrl,95.0,C,False,False,True,False,[LOADBA..],,safe,False,Long fallback: 19 chars
NG1311,⧫,ng:distributed_systems:loadbalancing_fn,[LOADBALANCINGFN],distributed_systems,loadbalancing_fn,95.0,C,False,False,True,False,[LOADBA..],,safe,False,Long fallback: 17 chars
NG1312,⦏,ng:distributed_systems:loadbalancing_proc_1,[LOADBALANCINGPROC1],distributed_systems,loadbalancing_proc_1,95.0,B,False,False,True,True,[LOADBA..],loadbalancing_proc_1,safe,False,Long fallback: 20 chars; Generic numbered name
NG1313,🜬,ng:distributed_systems:loadbalancing_core,[LOADBALANCINGCORE],distributed_systems,loadbalancing_core,95.0,A,False,True,True,False,[LOADBA..],,emoji_extended,False,Risky Unicode range: emoji_extended; Long fallback: 19 chars
NG1314,⠗,ng:distributed_systems:circuitbreakers_fn,[CIRCUITBREAKERSFN],distributed_systems,circuitbreakers_fn,95.0,C,False,False,True,False,[CIRCUI..],,safe,False,Long fallback: 19 chars
NG1315,⥏,ng:distributed_systems:circuitbreakers_op,[CIRCUITBREAKERSOP],distributed_systems,circuitbreakers_op,95.0,C,False,False,True,False,[CIRCUI..],,safe,False,Long fallback: 19 chars
NG1316,🜓,ng:distributed_systems:circuitbreakers,[CIRCUITBREAKERS],distributed_systems,circuitbreakers,95.0,A,False,True,True,False,[CIRCUI..],,emoji_extended,False,Risky Unicode range: emoji_extended; Long fallback: 17 chars
NG1317,❚,ng:distributed_systems:circuitbreakers_sys,[CIRCUITBREAKERSSYS],distributed_systems,circuitbreakers_sys,95.0,C,False,False,True,False,[CIRCUI..],,safe,False,Long fallback: 20 chars
NG1318,⢳,ng:distributed_systems:circuitbreakers_1,[CIRCUITBREAKERS1],distributed_systems,circuitbreakers_1,95.0,B,False,False,True,True,[CIRCUI..],circuitbreakers_1,safe,False,Long fallback: 18 chars; Generic numbered name
NG1319,🙊,ng:distributed_systems:circuitbreakers_2,[CIRCUITBREAKERS2],distributed_systems,circuitbreakers_2,95.0,A,False,True,True,True,[CIRCUI..],circuitbreakers_2,emoji_extended,False,Risky Unicode range: emoji_extended; Long fallback: 18 chars; Generic numbered name
NG1320,🞯,ng:distributed_systems:circuitbreakers_fn_1,[CIRCUITBREAKERSFN1],distributed_systems,circuitbreakers_fn_1,95.0,A,False,True,True,True,[CIRCUI..],circuitbreakers_fn_1,emoji_extended,False,Risky Unicode range: emoji_extended; Long fallback: 20 chars; Generic numbered name
NG1321,⫵,ng:distributed_systems:circuitbreakers_fn_2,[CIRCUITBREAKERSFN2],distributed_systems,circuitbreakers_fn_2,95.0,B,False,False,True,True,[CIRCUI..],circuitbreakers_fn_2,safe,False,Long fallback: 20 chars; Generic numbered name
NG1322,❡,ng:distributed_systems:circuitbreakers_op_1,[CIRCUITBREAKERSOP1],distributed_systems,circuitbreakers_op_1,95.0,B,False,False,True,True,[CIRCUI..],circuitbreakers_op_1,safe,False,Long fallback: 20 chars; Generic numbered name
NG1323,🜔,ng:distributed_systems:circuitbreakers_3,[CIRCUITBREAKERS3],distributed_systems,circuitbreakers_3,95.0,A,False,True,True,True,[CIRCUI..],circuitbreakers_3,emoji_extended,False,Risky Unicode range: emoji_extended; Long fallback: 18 chars; Generic numbered name
NG1324,🞐,ng:distributed_systems:circuitbreakers_fn_3,[CIRCUITBREAKERSFN3],distributed_systems,circuitbreakers_fn_3,95.0,A,False,True,True,True,[CIRCUI..],circuitbreakers_fn_3,emoji_extended,False,Risky Unicode range: emoji_extended; Long fallback: 20 chars; Generic numbered name
NG1325,⩆,ng:distributed_systems:circuitbreakers_op_3,[CIRCUITBREAKERSOP3],distributed_systems,circuitbreakers_op_3,95.0,B,False,False,True,True,[CIRCUI..],circuitbreakers_op_3,safe,False,Long fallback: 20 chars; Generic numbered name
NG1326,❃,ng:distributed_systems:circuitbreakers_fn_4,[CIRCUITBREAKERSFN4],distributed_systems,circuitbreakers_fn_4,95.0,B,False,False,True,True,[CIRCUI..],circuitbreakers_fn_4,safe,False,Long fallback: 20 chars; Generic numbered name
NG1327,❀,ng:distributed_systems:circuitbreakers_op_4,[CIRCUITBREAKERSOP4],distributed_systems,circuitbreakers_op_4,95.0,B,False,False,True,True,[CIRCUI..],circuitbreakers_op_4,safe,False,Long fallback: 20 chars; Generic numbered name
NG1328,🦜,ng:distributed_systems:circuitbreakers_fn_5,[CIRCUITBREAKERSFN5],distributed_systems,circuitbreakers_fn_5,95.0,A,False,True,True,True,[CIRCUI..],circuitbreakers_fn_5,emoji_extended,False,Risky Unicode range: emoji_extended; Long fallback: 20 chars; Generic numbered name
NG1329,⢩,ng:distributed_systems:circuitbreakers_op_5,[CIRCUITBREAKERSOP5],distributed_systems,circuitbreakers_op_5,95.0,B,False,False,True,True,[CIRCUI..],circuitbreakers_op_5,safe,False,Long fallback: 20 chars; Generic numbered name
NG1330,🢪,ng:distributed_systems:circuitbreakers_4,[CIRCUITBREAKERS4],distributed_systems,circuitbreakers_4,95.0,A,False,True,True,True,[CIRCUI..],circuitbreakers_4,emoji_extended,False,Risky Unicode range: emoji_extended; Long fallback: 18 chars; Generic numbered name
NG1331,⮢,ng:distributed_systems:circuitbreakers_op_6,[CIRCUITBREAKERSOP6],distributed_systems,circuitbreakers_op_6,95.0,B,False,False,True,True,[CIRCUI..],circuitbreakers_op_6,safe,False,Long fallback: 20 chars; Generic numbered name
NG1332,⡾,ng:distributed_systems:circuitbreakers_fn_6,[CIRCUITBREAKERSFN6],distributed_systems,circuitbreakers_fn_6,95.0,B,False,False,True,True,[CIRCUI..],circuitbreakers_fn_6,safe,False,Long fallback: 20 chars; Generic numbered name
NG1333,🟉,ng:distributed_systems:circuitbreakers_fn_7,[CIRCUITBREAKERSFN7],distributed_systems,circuitbreakers_fn_7,95.0,A,False,True,True,True,[CIRCUI..],circuitbreakers_fn_7,emoji_extended,False,Risky Unicode range: emoji_extended; Long fallback: 20 chars; Generic numbered name
NG1334,⤦,ng:distributed_systems:circuitbreakers_op_7,[CIRCUITBREAKERSOP7],distributed_systems,circuitbreakers_op_7,95.0,B,False,False,True,True,[CIRCUI..],circuitbreakers_op_7,safe,False,Long fallback: 20 chars; Generic numbered name
NG1335,🝨,ng:distributed_systems:circuitbreakers_fn_8,[CIRCUITBREAKERSFN8],distributed_systems,circuitbreakers_fn_8,95.0,A,False,True,True,True,[CIRCUI..],circuitbreakers_fn_8,emoji_extended,False,Risky Unicode range: emoji_extended; Long fallback: 20 chars; Generic numbered name
NG1336,✦,ng:distributed_systems:circuitbreakers_fn_9,[CIRCUITBREAKERSFN9],distributed_systems,circuitbreakers_fn_9,95.0,B,False,False,True,True,[CIRCUI..],circuitbreakers_fn_9,safe,False,Long fallback: 20 chars; Generic numbered name
NG1337,🤔,ng:distributed_systems:circuitbreakers_5,[CIRCUITBREAKERS5],distributed_systems,circuitbreakers_5,95.0,A,False,True,True,True,[CIRCUI..],circuitbreakers_5,emoji_extended,False,Risky Unicode range: emoji_extended; Long fallback: 18 chars; Generic numbered name
NG1338,🤊,ng:distributed_systems:circuitbreakers_6,[CIRCUITBREAKERS6],distributed_systems,circuitbreakers_6,95.0,A,False,True,True,True,[CIRCUI..],circuitbreakers_6,emoji_extended,False,Risky Unicode range: emoji_extended; Long fallback: 18 chars; Generic numbered name
NG1339,😝,ng:distributed_systems:circuitbreakers_7,[CIRCUITBREAKERS7],distributed_systems,circuitbreakers_7,95.0,A,False,True,True,True,[CIRCUI..],circuitbreakers_7,emoji_extended,False,Risky Unicode range: emoji_extended; Long fallback: 18 chars; Generic numbered name
NG1340,📐,ng:distributed_systems:circuitbreakers_9,[CIRCUITBREAKERS9],distributed_systems,circuitbreakers_9,95.0,A,False,True,True,True,[CIRCUI..],circuitbreakers_9,emoji_extended,False,Risky Unicode range: emoji_extended; Long fallback: 18 chars; Generic numbered name
NG1341,⥎,ng:distributed_systems:circuitbreakers_10,[CIRCUITBREAKERS10],distributed_systems,circuitbreakers_10,95.0,B,False,False,True,True,[CIRCUI..],circuitbreakers_10,safe,False,Long fallback: 19 chars; Generic numbered name
NG1342,🝳,ng:distributed_systems:circuitbreakers_11,[CIRCUITBREAKERS11],distributed_systems,circuitbreakers_11,95.0,A,False,True,True,True,[CIRCUI..],circuitbreakers_11,emoji_extended,False,Risky Unicode range: emoji_extended; Long fallback: 19 chars; Generic numbered name
NG1343,🛻,ng:distributed_systems:circuitbreakers_12,[CIRCUITBREAKERS12],distributed_systems,circuitbreakers_12,95.0,A,False,True,True,True,[CIRCUI..],circuitbreakers_12,emoji_extended,False,Risky Unicode range: emoji_extended; Long fallback: 19 chars; Generic numbered name
NG1344,⦑,ng:distributed_systems:circuitbreakers_13,[CIRCUITBREAKERS13],distributed_systems,circuitbreakers_13,95.0,B,False,False,True,True,[CIRCUI..],circuitbreakers_13,safe,False,Long fallback: 19 chars; Generic numbered name
NG1345,🙃,ng:distributed_systems:circuitbreakers_op_8,[CIRCUITBREAKERSOP8],distributed_systems,circuitbreakers_op_8,95.0,A,False,True,True,True,[CIRCUI..],circuitbreakers_op_8,emoji_extended,False,Risky Unicode range: emoji_extended; Long fallback: 20 chars; Generic numbered name
NG1346,🖆,ng:distributed_systems:circuitbreakers_14,[CIRCUITBREAKERS14],distributed_systems,circuitbreakers_14,95.0,A,False,True,True,True,[CIRCUI..],circuitbreakers_14,emoji_extended,False,Risky Unicode range: emoji_extended; Long fallback: 19 chars; Generic numbered name
NG1347,⨿,ng:distributed_systems:circuitbreakers_op_9,[CIRCUITBREAKERSOP9],distributed_systems,circuitbreakers_op_9,95.0,B,False,False,True,True,[CIRCUI..],circuitbreakers_op_9,safe,False,Long fallback: 20 chars; Generic numbered name
NG1348,❓,ng:distributed_systems:circuitbreakers_15,[CIRCUITBREAKERS15],distributed_systems,circuitbreakers_15,95.0,B,False,False,True,True,[CIRCUI..],circuitbreakers_15,safe,False,Long fallback: 19 chars; Generic numbered name
NG1349,😋,ng:distributed_systems:circuitbreakers_16,[CIRCUITBREAKERS16],distributed_systems,circuitbreakers_16,95.0,A,False,True,True,True,[CIRCUI..],circuitbreakers_16,emoji_extended,False,Risky Unicode range: emoji_extended; Long fallback: 19 chars; Generic numbered name
NG1350,🐜,ng:distributed_systems:circuitbreakers_17,[CIRCUITBREAKERS17],distributed_systems,circuitbreakers_17,95.0,A,False,True,True,True,[CIRCUI..],circuitbreakers_17,emoji_extended,False,Risky Unicode range: emoji_extended; Long fallback: 19 chars; Generic numbered name
NG1351,🏊,ng:distributed_systems:circuitbreakers_18,[CIRCUITBREAKERS18],distributed_systems,circuitbreakers_18,95.0,A,False,True,True,True,[CIRCUI..],circuitbreakers_18,emoji_extended,False,Risky Unicode range: emoji_extended; Long fallback: 19 chars; Generic numbered name
NG1352,⮕,ng:distributed_systems:circuitbreakers_19,[CIRCUITBREAKERS19],distributed_systems,circuitbreakers_19,95.0,B,False,False,True,True,[CIRCUI..],circuitbreakers_19,safe,False,Long fallback: 19 chars; Generic numbered name
NG1353,🕵,ng:distributed_systems:circuitbreakers_20,[CIRCUITBREAKERS20],distributed_systems,circuitbreakers_20,95.0,A,False,True,True,True,[CIRCUI..],circuitbreakers_20,emoji_extended,False,Risky Unicode range: emoji_extended; Long fallback: 19 chars; Generic numbered name
NG1354,⪧,ng:distributed_systems:circuitbreakers_21,[CIRCUITBREAKERS21],distributed_systems,circuitbreakers_21,95.0,B,False,False,True,True,[CIRCUI..],circuitbreakers_21,safe,False,Long fallback: 19 chars; Generic numbered name
NG1355,🛫,ng:distributed_systems:circuitbreakers_22,[CIRCUITBREAKERS22],distributed_systems,circuitbreakers_22,95.0,A,False,True,True,True,[CIRCUI..],circuitbreakers_22,emoji_extended,False,Risky Unicode range: emoji_extended; Long fallback: 19 chars; Generic numbered name
NG1356,⭑,ng:distributed_systems:circuitbreakers_23,[CIRCUITBREAKERS23],distributed_systems,circuitbreakers_23,95.0,B,False,False,True,True,[CIRCUI..],circuitbreakers_23,safe,False,Long fallback: 19 chars; Generic numbered name
NG1357,🞡,ng:distributed_systems:circuitbreakers_25,[CIRCUITBREAKERS25],distributed_systems,circuitbreakers_25,95.0,A,False,True,True,True,[CIRCUI..],circuitbreakers_25,emoji_extended,False,Risky Unicode range: emoji_extended; Long fallback: 19 chars; Generic numbered name
NG1358,🢀,ng:quantum_computing:cnot,[CNOT],quantum_computing,cnot,95.0,B,False,True,False,False,,,emoji_extended,False,Risky Unicode range: emoji_extended
NG1359,⤎,ng:quantum_computing:pauli_op,[PAULIOP],quantum_computing,pauli_op,95.0,OK,False,False,False,False,,,safe,False,
NG1360,⠰,ng:quantum_computing:hadamard_meta,[HADAMARDMETA],quantum_computing,hadamard_meta,95.0,C,False,False,True,False,[HADAMA..],,safe,False,Long fallback: 14 chars
NG1361,🜚,ng:quantum_computing:cnot_proc,[CNOTPROC],quantum_computing,cnot_proc,95.0,B,False,True,False,False,,,emoji_extended,False,Risky Unicode range: emoji_extended
NG1362,⤑,ng:quantum_computing:pauli_op_1,[PAULIOP1],quantum_computing,pauli_op_1,95.0,C,False,False,False,True,,pauli_op_1,safe,False,Generic numbered name
NG1363,🜶,ng:quantum_computing:qgate_op,[QGATEOP],quantum_computing,qgate_op,95.0,B,False,True,False,False,,,emoji_extended,False,Risky Unicode range: emoji_extended
NG1364,⮨,ng:quantum_computing:quantumcircuits_op,[QUANTUMCIRCUITSOP],quantum_computing,quantumcircuits_op,95.0,C,False,False,True,False,[QUANTU..],,safe,False,Long fallback: 19 chars
NG1365,⫔,ng:quantum_computing:quantumcircuits_sys,[QUANTUMCIRCUITSSYS],quantum_computing,quantumcircuits_sys,95.0,C,False,False,True,False,[QUANTU..],,safe,False,Long fallback: 20 chars
NG1366,🝙,ng:quantum_computing:quantumcircuits_fn_1,[QUANTUMCIRCUITSFN1],quantum_computing,quantumcircuits_fn_1,95.0,A,False,True,True,True,[QUANTU..],quantumcircuits_fn_1,emoji_extended,False,Risky Unicode range: emoji_extended; Long fallback: 20 chars; Generic numbered name
NG1367,⥰,ng:quantum_computing:quantumcircuits_fn_2,[QUANTUMCIRCUITSFN2],quantum_computing,quantumcircuits_fn_2,95.0,B,False,False,True,True,[QUANTU..],quantumcircuits_fn_2,safe,False,Long fallback: 20 chars; Generic numbered name
NG1368,🐵,ng:quantum_computing:quantumcircuits_op_1,[QUANTUMCIRCUITSOP1],quantum_computing,quantumcircuits_op_1,95.0,A,False,True,True,True,[QUANTU..],quantumcircuits_op_1,emoji_extended,False,Risky Unicode range: emoji_extended; Long fallback: 20 chars; Generic numbered name
NG1369,😘,ng:quantum_computing:quantumcircuits_fn_3,[QUANTUMCIRCUITSFN3],quantum_computing,quantumcircuits_fn_3,95.0,A,False,True,True,True,[QUANTU..],quantumcircuits_fn_3,emoji_extended,False,Risky Unicode range: emoji_extended; Long fallback: 20 chars; Generic numbered name
NG1370,⭕,ng:quantum_computing:superposition_proc,[SUPERPOSITIONPROC],quantum_computing,superposition_proc,95.0,C,False,False,True,False,[SUPERP..],,safe,False,Long fallback: 19 chars
NG1371,⡄,ng:quantum_computing:superposition,[SUPERPOSITION],quantum_computing,superposition,95.0,C,False,False,True,False,[SUPERP..],,safe,False,Long fallback: 15 chars
NG1372,🧳,ng:quantum_computing:superposition_1,[SUPERPOSITION1],quantum_computing,superposition_1,95.0,A,False,True,True,True,[SUPERP..],superposition_1,emoji_extended,False,Risky Unicode range: emoji_extended; Long fallback: 16 chars; Generic numbered name
NG1373,🚮,ng:quantum_computing:superposition_fn,[SUPERPOSITIONFN],quantum_computing,superposition_fn,95.0,A,False,True,True,False,[SUPERP..],,emoji_extended,False,Risky Unicode range: emoji_extended; Long fallback: 17 chars
NG1374,⦨,ng:quantum_computing:superposition_2,[SUPERPOSITION2],quantum_computing,superposition_2,95.0,B,False,False,True,True,[SUPERP..],superposition_2,safe,False,Long fallback: 16 chars; Generic numbered name
NG1375,⮎,ng:quantum_computing:entanglement_proc,[ENTANGLEMENTPROC],quantum_computing,entanglement_proc,95.0,C,False,False,True,False,[ENTANG..],,safe,False,Long fallback: 18 chars
NG1376,🕡,ng:quantum_computing:entanglement_fn,[ENTANGLEMENTFN],quantum_computing,entanglement_fn,95.0,A,False,True,True,False,[ENTANG..],,emoji_extended,False,Risky Unicode range: emoji_extended; Long fallback: 16 chars
NG1377,❞,ng:quantum_computing:entanglement,[ENTANGLEMENT],quantum_computing,entanglement,95.0,C,False,False,True,False,[ENTANG..],,safe,False,Long fallback: 14 chars
NG1378,⪩,ng:quantum_computing:entanglement_meta,[ENTANGLEMENTMETA],quantum_computing,entanglement_meta,95.0,C,False,False,True,False,[ENTANG..],,safe,False,Long fallback: 18 chars
NG1379,✃,ng:quantum_computing:entanglement_fn_1,[ENTANGLEMENTFN1],quantum_computing,entanglement_fn_1,95.0,B,False,False,True,True,[ENTANG..],entanglement_fn_1,safe,False,Long fallback: 17 chars; Generic numbered name
NG1380,⮀,ng:quantum_computing:entanglement_op,[ENTANGLEMENTOP],quantum_computing,entanglement_op,95.0,C,False,False,True,False,[ENTANG..],,safe,False,Long fallback: 16 chars
NG1381,⤉,ng:quantum_computing:quantumalgorithms,[QUANTUMALGORITHMS],quantum_computing,quantumalgorithms,95.0,C,False,False,True,False,[QUANTU..],,safe,False,Long fallback: 19 chars
NG1382,😕,ng:quantum_computing:quantumalgorithms_2,[QUANTUMALGORITHMS2],quantum_computing,quantumalgorithms_2,95.0,A,False,True,True,True,[QUANTU..],quantumalgorithms_2,emoji_extended,False,Risky Unicode range: emoji_extended; Long fallback: 20 chars; Generic numbered name
NG1383,🦬,ng:quantum_computing:quantumalgorithms_3,[QUANTUMALGORITHMS3],quantum_computing,quantumalgorithms_3,95.0,A,False,True,True,True,[QUANTU..],quantumalgorithms_3,emoji_extended,False,Risky Unicode range: emoji_extended; Long fallback: 20 chars; Generic numbered name
NG1384,⭸,ng:quantum_computing:quantumalgorithms_4,[QUANTUMALGORITHMS4],quantum_computing,quantumalgorithms_4,95.0,B,False,False,True,True,[QUANTU..],quantumalgorithms_4,safe,False,Long fallback: 20 chars; Generic numbered name
NG1385,⭤,ng:quantum_computing:quantumalgorithms_6,[QUANTUMALGORITHMS6],quantum_computing,quantumalgorithms_6,95.0,B,False,False,True,True,[QUANTU..],quantumalgorithms_6,safe,False,Long fallback: 20 chars; Generic numbered name
NG1386,🜴,ng:symbolic_ai:logicalinference_fn,[LOGICALINFERENCEFN],symbolic_ai,logicalinference_fn,95.0,A,False,True,True,False,[LOGICA..],,emoji_extended,False,Risky Unicode range: emoji_extended; Long fallback: 20 chars
NG1387,😷,ng:symbolic_ai:logicalinference_op,[LOGICALINFERENCEOP],symbolic_ai,logicalinference_op,95.0,A,False,True,True,False,[LOGICA..],,emoji_extended,False,Risky Unicode range: emoji_extended; Long fallback: 20 chars
NG1388,🤗,ng:symbolic_ai:logicalinference,[LOGICALINFERENCE],symbolic_ai,logicalinference,95.0,A,False,True,True,False,[LOGICA..],,emoji_extended,False,Risky Unicode range: emoji_extended; Long fallback: 18 chars
NG1389,😼,ng:symbolic_ai:logicalinference_3,[LOGICALINFERENCE3],symbolic_ai,logicalinference_3,95.0,A,False,True,True,True,[LOGICA..],logicalinference_3,emoji_extended,False,Risky Unicode range: emoji_extended; Long fallback: 19 chars; Generic numbered name
NG1390,✹,ng:symbolic_ai:logicalinference_4,[LOGICALINFERENCE4],symbolic_ai,logicalinference_4,95.0,B,False,False,True,True,[LOGICA..],logicalinference_4,safe,False,Long fallback: 19 chars; Generic numbered name
NG1391,⤓,ng:symbolic_ai:logicalinference_5,[LOGICALINFERENCE5],symbolic_ai,logicalinference_5,95.0,B,False,False,True,True,[LOGICA..],logicalinference_5,safe,False,Long fallback: 19 chars; Generic numbered name
NG1392,😙,ng:symbolic_ai:logicalinference_7,[LOGICALINFERENCE7],symbolic_ai,logicalinference_7,95.0,A,False,True,True,True,[LOGICA..],logicalinference_7,emoji_extended,False,Risky Unicode range: emoji_extended; Long fallback: 19 chars; Generic numbered name
NG1393,❴,ng:symbolic_ai:theoremproving,[THEOREMPROVING],symbolic_ai,theoremproving,95.0,C,False,False,True,False,[THEORE..],,safe,False,Long fallback: 16 chars
NG1394,⧜,ng:symbolic_ai:theoremproving_sys,[THEOREMPROVINGSYS],symbolic_ai,theoremproving_sys,95.0,C,False,False,True,False,[THEORE..],,safe,False,Long fallback: 19 chars
NG1395,⫴,ng:symbolic_ai:theoremproving_sys_1,[THEOREMPROVINGSYS1],symbolic_ai,theoremproving_sys_1,95.0,B,False,False,True,True,[THEORE..],theoremproving_sys_1,safe,False,Long fallback: 20 chars; Generic numbered name
NG1396,⣥,ng:symbolic_ai:theoremproving_op,[THEOREMPROVINGOP],symbolic_ai,theoremproving_op,95.0,C,False,False,True,False,[THEORE..],,safe,False,Long fallback: 18 chars
NG1397,⩖,ng:symbolic_ai:theoremproving_sys_2,[THEOREMPROVINGSYS2],symbolic_ai,theoremproving_sys_2,95.0,B,False,False,True,True,[THEORE..],theoremproving_sys_2,safe,False,Long fallback: 20 chars; Generic numbered name
NG1398,⥛,ng:symbolic_ai:theoremproving_1,[THEOREMPROVING1],symbolic_ai,theoremproving_1,95.0,B,False,False,True,True,[THEORE..],theoremproving_1,safe,False,Long fallback: 17 chars; Generic numbered name
NG1399,😒,ng:symbolic_ai:theoremproving_meta,[THEOREMPROVINGMETA],symbolic_ai,theoremproving_meta,95.0,A,False,True,True,False,[THEORE..],,emoji_extended,False,Risky Unicode range: emoji_extended; Long fallback: 20 chars
NG1400,🛏,ng:symbolic_ai:theoremproving_sys_3,[THEOREMPROVINGSYS3],symbolic_ai,theoremproving_sys_3,95.0,A,False,True,True,True,[THEORE..],theoremproving_sys_3,emoji_extended,False,Risky Unicode range: emoji_extended; Long fallback: 20 chars; Generic numbered name
NG1401,🎤,ng:symbolic_ai:expertsystems_proc,[EXPERTSYSTEMSPROC],symbolic_ai,expertsystems_proc,95.0,A,False,True,True,False,[EXPERT..],,emoji_extended,False,Risky Unicode range: emoji_extended; Long fallback: 19 chars
NG1402,🛔,ng:symbolic_ai:expertsystems_ctrl,[EXPERTSYSTEMSCTRL],symbolic_ai,expertsystems_ctrl,95.0,A,False,True,True,False,[EXPERT..],,emoji_extended,False,Risky Unicode range: emoji_extended; Long fallback: 19 chars
NG1403,🧹,ng:symbolic_ai:expertsystems_core_1,[EXPERTSYSTEMSCORE1],symbolic_ai,expertsystems_core_1,95.0,A,False,True,True,True,[EXPERT..],expertsystems_core_1,emoji_extended,False,Risky Unicode range: emoji_extended; Long fallback: 20 chars; Generic numbered name
NG1404,⥀,ng:symbolic_ai:expertsystems_meta_1,[EXPERTSYSTEMSMETA1],symbolic_ai,expertsystems_meta_1,95.0,B,False,False,True,True,[EXPERT..],expertsystems_meta_1,safe,False,Long fallback: 20 chars; Generic numbered name
NG1405,⧎,ng:symbolic_ai:expertsystems_proc_2,[EXPERTSYSTEMSPROC2],symbolic_ai,expertsystems_proc_2,95.0,B,False,False,True,True,[EXPERT..],expertsystems_proc_2,safe,False,Long fallback: 20 chars; Generic numbered name
NG1406,🥋,ng:symbolic_ai:expertsystems_sys,[EXPERTSYSTEMSSYS],symbolic_ai,expertsystems_sys,95.0,A,False,True,True,False,[EXPERT..],,emoji_extended,False,Risky Unicode range: emoji_extended; Long fallback: 18 chars
NG1407,😍,ng:symbolic_ai:expertsystems_op,[EXPERTSYSTEMSOP],symbolic_ai,expertsystems_op,95.0,A,False,True,True,False,[EXPERT..],,emoji_extended,False,Risky Unicode range: emoji_extended; Long fallback: 17 chars
NG1408,⥶,ng:symbolic_ai:expertsystems_meta_2,[EXPERTSYSTEMSMETA2],symbolic_ai,expertsystems_meta_2,95.0,B,False,False,True,True,[EXPERT..],expertsystems_meta_2,safe,False,Long fallback: 20 chars; Generic numbered name
NG1409,🡖,ng:symbolic_ai:semanticnetworks_fn,[SEMANTICNETWORKSFN],symbolic_ai,semanticnetworks_fn,95.0,A,False,True,True,False,[SEMANT..],,emoji_extended,False,Risky Unicode range: emoji_extended; Long fallback: 20 chars
NG1410,🥺,ng:symbolic_ai:semanticnetworks_3,[SEMANTICNETWORKS3],symbolic_ai,semanticnetworks_3,95.0,A,False,True,True,True,[SEMANT..],semanticnetworks_3,emoji_extended,False,Risky Unicode range: emoji_extended; Long fallback: 19 chars; Generic numbered name
NG1411,🐆,ng:symbolic_ai:semanticnetworks_4,[SEMANTICNETWORKS4],symbolic_ai,semanticnetworks_4,95.0,A,False,True,True,True,[SEMANT..],semanticnetworks_4,emoji_extended,False,Risky Unicode range: emoji_extended; Long fallback: 19 chars; Generic numbered name
NG1412,🠒,ng:symbolic_ai:semanticnetworks_5,[SEMANTICNETWORKS5],symbolic_ai,semanticnetworks_5,95.0,A,False,True,True,True,[SEMANT..],semanticnetworks_5,emoji_extended,False,Risky Unicode range: emoji_extended; Long fallback: 19 chars; Generic numbered name
NG1413,✌,ng:symbolic_ai:semanticnetworks_6,[SEMANTICNETWORKS6],symbolic_ai,semanticnetworks_6,95.0,B,False,False,True,True,[SEMANT..],semanticnetworks_6,safe,False,Long fallback: 19 chars; Generic numbered name
NG1414,😔,ng:symbolic_ai:semanticnetworks_7,[SEMANTICNETWORKS7],symbolic_ai,semanticnetworks_7,95.0,A,False,True,True,True,[SEMANT..],semanticnetworks_7,emoji_extended,False,Risky Unicode range: emoji_extended; Long fallback: 19 chars; Generic numbered name
NG1415,⥒,ng:symbolic_ai:semanticnetworks_8,[SEMANTICNETWORKS8],symbolic_ai,semanticnetworks_8,95.0,B,False,False,True,True,[SEMANT..],semanticnetworks_8,safe,False,Long fallback: 19 chars; Generic numbered name
NG1416,⨶,ng:symbolic_ai:ontologies_sys,[ONTOLOGIESSYS],symbolic_ai,ontologies_sys,95.0,C,False,False,True,False,[ONTOLO..],,safe,False,Long fallback: 15 chars
NG1417,⬛,ng:symbolic_ai:ontologies_sys_1,[ONTOLOGIESSYS1],symbolic_ai,ontologies_sys_1,95.0,B,False,False,True,True,[ONTOLO..],ontologies_sys_1,safe,False,Long fallback: 16 chars; Generic numbered name
NG1418,⮃,ng:symbolic_ai:ontologies_fn,[ONTOLOGIESFN],symbolic_ai,ontologies_fn,95.0,C,False,False,True,False,[ONTOLO..],,safe,False,Long fallback: 14 chars
NG1419,➪,ng:symbolic_ai:ontologies_sys_2,[ONTOLOGIESSYS2],symbolic_ai,ontologies_sys_2,95.0,B,False,False,True,True,[ONTOLO..],ontologies_sys_2,safe,False,Long fallback: 16 chars; Generic numbered name
NG1420,🞻,ng:symbolic_ai:ontologies_sys_3,[ONTOLOGIESSYS3],symbolic_ai,ontologies_sys_3,95.0,A,False,True,True,True,[ONTOLO..],ontologies_sys_3,emoji_extended,False,Risky Unicode range: emoji_extended; Long fallback: 16 chars; Generic numbered name
NG1421,🗹,ng:symbolic_ai:ontologies_core_2,[ONTOLOGIESCORE2],symbolic_ai,ontologies_core_2,95.0,A,False,True,True,True,[ONTOLO..],ontologies_core_2,emoji_extended,False,Risky Unicode range: emoji_extended; Long fallback: 17 chars; Generic numbered name
NG1422,🞹,ng:symbolic_ai:descriptionlogics_1,[DESCRIPTIONLOGICS1],symbolic_ai,descriptionlogics_1,95.0,A,False,True,True,True,[DESCRI..],descriptionlogics_1,emoji_extended,False,Risky Unicode range: emoji_extended; Long fallback: 20 chars; Generic numbered name
NG1423,📱,ng:symbolic_ai:descriptionlogics_2,[DESCRIPTIONLOGICS2],symbolic_ai,descriptionlogics_2,95.0,A,False,True,True,True,[DESCRI..],descriptionlogics_2,emoji_extended,False,Risky Unicode range: emoji_extended; Long fallback: 20 chars; Generic numbered name
NG1424,🍄,ng:symbolic_ai:descriptionlogics_4,[DESCRIPTIONLOGICS4],symbolic_ai,descriptionlogics_4,95.0,A,False,True,True,True,[DESCRI..],descriptionlogics_4,emoji_extended,False,Risky Unicode range: emoji_extended; Long fallback: 20 chars; Generic numbered name
NG1425,🏗,ng:symbolic_ai:descriptionlogics_5,[DESCRIPTIONLOGICS5],symbolic_ai,descriptionlogics_5,95.0,A,False,True,True,True,[DESCRI..],descriptionlogics_5,emoji_extended,False,Risky Unicode range: emoji_extended; Long fallback: 20 chars; Generic numbered name
NG1426,🜟,ng:symbolic_ai:descriptionlogics_6,[DESCRIPTIONLOGICS6],symbolic_ai,descriptionlogics_6,95.0,A,False,True,True,True,[DESCRI..],descriptionlogics_6,emoji_extended,False,Risky Unicode range: emoji_extended; Long fallback: 20 chars; Generic numbered name
NG1427,⥡,ng:symbolic_ai:descriptionlogics_7,[DESCRIPTIONLOGICS7],symbolic_ai,descriptionlogics_7,95.0,B,False,False,True,True,[DESCRI..],descriptionlogics_7,safe,False,Long fallback: 20 chars; Generic numbered name
NG1428,🦹,ng:symbolic_ai:descriptionlogics_8,[DESCRIPTIONLOGICS8],symbolic_ai,descriptionlogics_8,95.0,A,False,True,True,True,[DESCRI..],descriptionlogics_8,emoji_extended,False,Risky Unicode range: emoji_extended; Long fallback: 20 chars; Generic numbered name
NG1429,⯯,ng:symbolic_ai:automatedreasoning,[AUTOMATEDREASONING],symbolic_ai,automatedreasoning,95.0,C,False,False,True,False,[AUTOMA..],,safe,False,Long fallback: 20 chars
NG1430,🕎,ng:neural_architectures:multi_head_sys,[MULTIHEADSYS],neural_architectures,multi_head_sys,95.0,A,False,True,True,False,[MULTIH..],,emoji_extended,False,Risky Unicode range: emoji_extended; Long fallback: 14 chars
NG1431,🕭,ng:neural_architectures:multi_head_meta,[MULTIHEADMETA],neural_architectures,multi_head_meta,95.0,A,False,True,True,False,[MULTIH..],,emoji_extended,False,Risky Unicode range: emoji_extended; Long fallback: 15 chars
NG1432,⯉,ng:neural_architectures:multi_head_fn,[MULTIHEADFN],neural_architectures,multi_head_fn,95.0,C,False,False,True,False,[MULTIH..],,safe,False,Long fallback: 13 chars
NG1433,🡑,ng:neural_architectures:attention_sys,[ATTENTIONSYS],neural_architectures,attention_sys,95.0,A,False,True,True,False,[ATTENT..],,emoji_extended,False,Risky Unicode range: emoji_extended; Long fallback: 14 chars
NG1434,⥞,ng:neural_architectures:attention_op,[ATTENTIONOP],neural_architectures,attention_op,95.0,C,False,False,True,False,[ATTENT..],,safe,False,Long fallback: 13 chars
NG1435,🜽,ng:neural_architectures:cross_attn_proc,[CROSSATTNPROC],neural_architectures,cross_attn_proc,95.0,A,False,True,True,False,[CROSSA..],,emoji_extended,False,Risky Unicode range: emoji_extended; Long fallback: 15 chars
NG1436,⮺,ng:neural_architectures:attention_op_1,[ATTENTIONOP1],neural_architectures,attention_op_1,95.0,B,False,False,True,True,[ATTENT..],attention_op_1,safe,False,Long fallback: 14 chars; Generic numbered name
NG1437,⩿,ng:neural_architectures:transformerblocks,[TRANSFORMERBLOCKS],neural_architectures,transformerblocks,95.0,C,False,False,True,False,[TRANSF..],,safe,False,Long fallback: 19 chars
NG1438,⦍,ng:neural_architectures:transformerblocks_1,[TRANSFORMERBLOCKS1],neural_architectures,transformerblocks_1,95.0,B,False,False,True,True,[TRANSF..],transformerblocks_1,safe,False,Long fallback: 20 chars; Generic numbered name
NG1439,😃,ng:neural_architectures:transformerblocks_3,[TRANSFORMERBLOCKS3],neural_architectures,transformerblocks_3,95.0,A,False,True,True,True,[TRANSF..],transformerblocks_3,emoji_extended,False,Risky Unicode range: emoji_extended; Long fallback: 20 chars; Generic numbered name
NG1440,⥕,ng:neural_architectures:transformerblocks_4,[TRANSFORMERBLOCKS4],neural_architectures,transformerblocks_4,95.0,B,False,False,True,True,[TRANSF..],transformerblocks_4,safe,False,Long fallback: 20 chars; Generic numbered name
NG1441,🏩,ng:neural_architectures:transformerblocks_5,[TRANSFORMERBLOCKS5],neural_architectures,transformerblocks_5,95.0,A,False,True,True,True,[TRANSF..],transformerblocks_5,emoji_extended,False,Risky Unicode range: emoji_extended; Long fallback: 20 chars; Generic numbered name
NG1442,⭪,ng:neural_architectures:transformerblocks_7,[TRANSFORMERBLOCKS7],neural_architectures,transformerblocks_7,95.0,B,False,False,True,True,[TRANSF..],transformerblocks_7,safe,False,Long fallback: 20 chars; Generic numbered name
NG1443,➷,ng:neural_architectures:lossfunctions_fn,[LOSSFUNCTIONSFN],neural_architectures,lossfunctions_fn,95.0,C,False,False,True,False,[LOSSFU..],,safe,False,Long fallback: 17 chars
NG1444,🚘,ng:neural_architectures:lossfunctions_proc,[LOSSFUNCTIONSPROC],neural_architectures,lossfunctions_proc,95.0,A,False,True,True,False,[LOSSFU..],,emoji_extended,False,Risky Unicode range: emoji_extended; Long fallback: 19 chars
NG1445,🚭,ng:neural_architectures:lossfunctions_core,[LOSSFUNCTIONSCORE],neural_architectures,lossfunctions_core,95.0,A,False,True,True,False,[LOSSFU..],,emoji_extended,False,Risky Unicode range: emoji_extended; Long fallback: 19 chars
NG1446,🚳,ng:neural_architectures:lossfunctions_meta_1,[LOSSFUNCTIONSMETA1],neural_architectures,lossfunctions_meta_1,95.0,A,False,True,True,True,[LOSSFU..],lossfunctions_meta_1,emoji_extended,False,Risky Unicode range: emoji_extended; Long fallback: 20 chars; Generic numbered name
NG1447,😾,ng:formal_verification:ctl_meta,[CTLMETA],formal_verification,ctl_meta,95.0,B,False,True,False,False,,,emoji_extended,False,Risky Unicode range: emoji_extended
NG1448,🗙,ng:formal_verification:temporal,[TEMPORAL],formal_verification,temporal,95.0,B,False,True,False,False,,,emoji_extended,False,Risky Unicode range: emoji_extended
NG1449,🥰,ng:formal_verification:model_check_fn,[MODELCHECKFN],formal_verification,model_check_fn,95.0,A,False,True,True,False,[MODELC..],,emoji_extended,False,Risky Unicode range: emoji_extended; Long fallback: 14 chars
NG1450,➡,ng:formal_verification:model_check,[MODELCHECK],formal_verification,model_check,95.0,C,False,False,True,False,[MODELC..],,safe,False,Long fallback: 12 chars
NG1451,⣫,ng:formal_verification:verify_ctrl,[VERIFYCTRL],formal_verification,verify_ctrl,95.0,C,False,False,True,False,[VERIFY..],,safe,False,Long fallback: 12 chars
NG1452,⫡,ng:formal_verification:temporal_sys,[TEMPORALSYS],formal_verification,temporal_sys,95.0,C,False,False,True,False,[TEMPOR..],,safe,False,Long fallback: 13 chars
NG1453,🝧,ng:formal_verification:theoremproving_fn,[THEOREMPROVINGFN],formal_verification,theoremproving_fn,95.0,A,False,True,True,False,[THEORE..],,emoji_extended,False,Risky Unicode range: emoji_extended; Long fallback: 18 chars
NG1454,⥢,ng:formal_verification:theoremproving_core,[THEOREMPROVINGCORE],formal_verification,theoremproving_core,95.0,C,False,False,True,False,[THEORE..],,safe,False,Long fallback: 20 chars
NG1455,🜈,ng:formal_verification:theoremproving,[THEOREMPROVING],formal_verification,theoremproving,95.0,A,False,True,True,False,[THEORE..],,emoji_extended,False,Risky Unicode range: emoji_extended; Long fallback: 16 chars
NG1456,😡,ng:formal_verification:theoremproving_op,[THEOREMPROVINGOP],formal_verification,theoremproving_op,95.0,A,False,True,True,False,[THEORE..],,emoji_extended,False,Risky Unicode range: emoji_extended; Long fallback: 18 chars
NG1457,⩞,ng:formal_verification:theoremproving_fn_1,[THEOREMPROVINGFN1],formal_verification,theoremproving_fn_1,95.0,B,False,False,True,True,[THEORE..],theoremproving_fn_1,safe,False,Long fallback: 19 chars; Generic numbered name
NG1458,🙉,ng:formal_verification:staticanalysis,[STATICANALYSIS],formal_verification,staticanalysis,95.0,A,False,True,True,False,[STATIC..],,emoji_extended,False,Risky Unicode range: emoji_extended; Long fallback: 16 chars
NG1459,➛,ng:formal_verification:staticanalysis_core,[STATICANALYSISCORE],formal_verification,staticanalysis_core,95.0,C,False,False,True,False,[STATIC..],,safe,False,Long fallback: 20 chars
NG1460,⦱,ng:formal_verification:staticanalysis_fn,[STATICANALYSISFN],formal_verification,staticanalysis_fn,95.0,C,False,False,True,False,[STATIC..],,safe,False,Long fallback: 18 chars
NG1461,🤧,ng:formal_verification:staticanalysis_1,[STATICANALYSIS1],formal_verification,staticanalysis_1,95.0,A,False,True,True,True,[STATIC..],staticanalysis_1,emoji_extended,False,Risky Unicode range: emoji_extended; Long fallback: 17 chars; Generic numbered name
NG1462,⛷,ng:formal_verification:staticanalysis_meta,[STATICANALYSISMETA],formal_verification,staticanalysis_meta,95.0,A,False,True,True,False,[STATIC..],,miscellaneous_symbols,False,Risky Unicode range: miscellaneous_symbols; Long fallback: 20 chars
NG1463,🛍,ng:formal_verification:symbolicexecution,[SYMBOLICEXECUTION],formal_verification,symbolicexecution,95.0,A,False,True,True,False,[SYMBOL..],,emoji_extended,False,Risky Unicode range: emoji_extended; Long fallback: 19 chars
NG1464,⡧,ng:formal_verification:symbolicexecution_1,[SYMBOLICEXECUTION1],formal_verification,symbolicexecution_1,95.0,B,False,False,True,True,[SYMBOL..],symbolicexecution_1,safe,False,Long fallback: 20 chars; Generic numbered name
NG1465,⪁,ng:formal_verification:symbolicexecution_3,[SYMBOLICEXECUTION3],formal_verification,symbolicexecution_3,95.0,B,False,False,True,True,[SYMBOL..],symbolicexecution_3,safe,False,Long fallback: 20 chars; Generic numbered name
NG1466,😯,ng:formal_verification:symbolicexecution_4,[SYMBOLICEXECUTION4],formal_verification,symbolicexecution_4,95.0,A,False,True,True,True,[SYMBOL..],symbolicexecution_4,emoji_extended,False,Risky Unicode range: emoji_extended; Long fallback: 20 chars; Generic numbered name
NG1467,🥁,ng:formal_verification:temporallogic_fn,[TEMPORALLOGICFN],formal_verification,temporallogic_fn,95.0,A,False,True,True,False,[TEMPOR..],,emoji_extended,False,Risky Unicode range: emoji_extended; Long fallback: 17 chars
NG1468,🌫,ng:formal_verification:temporallogic_proc,[TEMPORALLOGICPROC],formal_verification,temporallogic_proc,95.0,A,False,True,True,False,[TEMPOR..],,emoji_extended,False,Risky Unicode range: emoji_extended; Long fallback: 19 chars
NG1469,✔,ng:formal_verification:temporallogic_ctrl_1,[TEMPORALLOGICCTRL1],formal_verification,temporallogic_ctrl_1,95.0,B,False,False,True,True,[TEMPOR..],temporallogic_ctrl_1,safe,False,Long fallback: 20 chars; Generic numbered name
NG1470,🖒,ng:formal_verification:hoarelogic_meta,[HOARELOGICMETA],formal_verification,hoarelogic_meta,95.0,A,False,True,True,False,[HOAREL..],,emoji_extended,False,Risky Unicode range: emoji_extended; Long fallback: 16 chars
NG1471,⩗,ng:formal_verification:hoarelogic_proc,[HOARELOGICPROC],formal_verification,hoarelogic_proc,95.0,C,False,False,True,False,[HOAREL..],,safe,False,Long fallback: 16 chars
NG1472,⤜,ng:formal_verification:hoarelogic_ctrl,[HOARELOGICCTRL],formal_verification,hoarelogic_ctrl,95.0,C,False,False,True,False,[HOAREL..],,safe,False,Long fallback: 16 chars
NG1473,🠱,ng:formal_verification:hoarelogic_meta_1,[HOARELOGICMETA1],formal_verification,hoarelogic_meta_1,95.0,A,False,True,True,True,[HOAREL..],hoarelogic_meta_1,emoji_extended,False,Risky Unicode range: emoji_extended; Long fallback: 17 chars; Generic numbered name
NG1474,⡠,ng:formal_verification:hoarelogic_meta_2,[HOARELOGICMETA2],formal_verification,hoarelogic_meta_2,95.0,B,False,False,True,True,[HOAREL..],hoarelogic_meta_2,safe,False,Long fallback: 17 chars; Generic numbered name
NG1475,🠑,ng:formal_verification:hoarelogic_sys,[HOARELOGICSYS],formal_verification,hoarelogic_sys,95.0,A,False,True,True,False,[HOAREL..],,emoji_extended,False,Risky Unicode range: emoji_extended; Long fallback: 15 chars
NG1476,🝐,ng:formal_verification:separationlogic_fn,[SEPARATIONLOGICFN],formal_verification,separationlogic_fn,95.0,A,False,True,True,False,[SEPARA..],,emoji_extended,False,Risky Unicode range: emoji_extended; Long fallback: 19 chars
NG1477,🚶,ng:formal_verification:separationlogic_fn_1,[SEPARATIONLOGICFN1],formal_verification,separationlogic_fn_1,95.0,A,False,True,True,True,[SEPARA..],separationlogic_fn_1,emoji_extended,False,Risky Unicode range: emoji_extended; Long fallback: 20 chars; Generic numbered name
NG1478,🙋,ng:formal_verification:separationlogic,[SEPARATIONLOGIC],formal_verification,separationlogic,95.0,A,False,True,True,False,[SEPARA..],,emoji_extended,False,Risky Unicode range: emoji_extended; Long fallback: 17 chars
NG1479,📷,ng:formal_verification:separationlogic_1,[SEPARATIONLOGIC1],formal_verification,separationlogic_1,95.0,A,False,True,True,True,[SEPARA..],separationlogic_1,emoji_extended,False,Risky Unicode range: emoji_extended; Long fallback: 18 chars; Generic numbered name
NG1480,⦰,ng:formal_verification:separationlogic_3,[SEPARATIONLOGIC3],formal_verification,separationlogic_3,95.0,B,False,False,True,True,[SEPARA..],separationlogic_3,safe,False,Long fallback: 18 chars; Generic numbered name
NG1481,🞰,ng:formal_verification:separationlogic_4,[SEPARATIONLOGIC4],formal_verification,separationlogic_4,95.0,A,False,True,True,True,[SEPARA..],separationlogic_4,emoji_extended,False,Risky Unicode range: emoji_extended; Long fallback: 18 chars; Generic numbered name
NG1482,😿,ng:formal_verification:separationlogic_5,[SEPARATIONLOGIC5],formal_verification,separationlogic_5,95.0,A,False,True,True,True,[SEPARA..],separationlogic_5,emoji_extended,False,Risky Unicode range: emoji_extended; Long fallback: 18 chars; Generic numbered name
NG1483,🐐,ng:formal_verification:separationlogic_fn_2,[SEPARATIONLOGICFN2],formal_verification,separationlogic_fn_2,95.0,A,False,True,True,True,[SEPARA..],separationlogic_fn_2,emoji_extended,False,Risky Unicode range: emoji_extended; Long fallback: 20 chars; Generic numbered name
NG1484,⨐,ng:formal_verification:separationlogic_6,[SEPARATIONLOGIC6],formal_verification,separationlogic_6,95.0,B,False,False,True,True,[SEPARA..],separationlogic_6,safe,False,Long fallback: 18 chars; Generic numbered name
NG1485,🟏,ng:formal_verification:separationlogic_op,[SEPARATIONLOGICOP],formal_verification,separationlogic_op,95.0,A,False,True,True,False,[SEPARA..],,emoji_extended,False,Risky Unicode range: emoji_extended; Long fallback: 19 chars
NG1486,⨲,ng:formal_verification:separationlogic_fn_3,[SEPARATIONLOGICFN3],formal_verification,separationlogic_fn_3,95.0,B,False,False,True,True,[SEPARA..],separationlogic_fn_3,safe,False,Long fallback: 20 chars; Generic numbered name
NG1487,🦈,ng:formal_verification:separationlogic_7,[SEPARATIONLOGIC7],formal_verification,separationlogic_7,95.0,A,False,True,True,True,[SEPARA..],separationlogic_7,emoji_extended,False,Risky Unicode range: emoji_extended; Long fallback: 18 chars; Generic numbered name
NG1488,🢂,ng:formal_verification:separationlogic_fn_4,[SEPARATIONLOGICFN4],formal_verification,separationlogic_fn_4,95.0,A,False,True,True,True,[SEPARA..],separationlogic_fn_4,emoji_extended,False,Risky Unicode range: emoji_extended; Long fallback: 20 chars; Generic numbered name
NG1489,🜠,ng:formal_verification:separationlogic_8,[SEPARATIONLOGIC8],formal_verification,separationlogic_8,95.0,A,False,True,True,True,[SEPARA..],separationlogic_8,emoji_extended,False,Risky Unicode range: emoji_extended; Long fallback: 18 chars; Generic numbered name
NG1490,✭,ng:formal_verification:separationlogic_op_1,[SEPARATIONLOGICOP1],formal_verification,separationlogic_op_1,95.0,B,False,False,True,True,[SEPARA..],separationlogic_op_1,safe,False,Long fallback: 20 chars; Generic numbered name
NG1491,❝,ng:formal_verification:separationlogic_9,[SEPARATIONLOGIC9],formal_verification,separationlogic_9,95.0,B,False,False,True,True,[SEPARA..],separationlogic_9,safe,False,Long fallback: 18 chars; Generic numbered name
NG1492,⥪,ng:formal_verification:separationlogic_fn_5,[SEPARATIONLOGICFN5],formal_verification,separationlogic_fn_5,95.0,B,False,False,True,True,[SEPARA..],separationlogic_fn_5,safe,False,Long fallback: 20 chars; Generic numbered name
NG1493,🧮,ng:formal_verification:separationlogic_fn_6,[SEPARATIONLOGICFN6],formal_verification,separationlogic_fn_6,95.0,A,False,True,True,True,[SEPARA..],separationlogic_fn_6,emoji_extended,False,Risky Unicode range: emoji_extended; Long fallback: 20 chars; Generic numbered name
NG1494,🛕,ng:formal_verification:separationlogic_10,[SEPARATIONLOGIC10],formal_verification,separationlogic_10,95.0,A,False,True,True,True,[SEPARA..],separationlogic_10,emoji_extended,False,Risky Unicode range: emoji_extended; Long fallback: 19 chars; Generic numbered name
NG1495,🚱,ng:formal_verification:separationlogic_fn_7,[SEPARATIONLOGICFN7],formal_verification,separationlogic_fn_7,95.0,A,False,True,True,True,[SEPARA..],separationlogic_fn_7,emoji_extended,False,Risky Unicode range: emoji_extended; Long fallback: 20 chars; Generic numbered name
NG1496,🛵,ng:category_theory:map_meta,[MAPMETA],category_theory,map_meta,95.0,B,False,True,False,False,,,emoji_extended,False,Risky Unicode range: emoji_extended
NG1497,🤬,ng:category_theory:fmap_op,[FMAPOP],category_theory,fmap_op,95.0,B,False,True,False,False,,,emoji_extended,False,Risky Unicode range: emoji_extended
NG1498,🞄,ng:category_theory:functor,[FUNCTOR],category_theory,functor,95.0,B,False,True,False,False,,,emoji_extended,False,Risky Unicode range: emoji_extended
NG1499,🏉,ng:category_theory:functor_op,[FUNCTOROP],category_theory,functor_op,95.0,A,False,True,True,False,[FUNCTO..],,emoji_extended,False,Risky Unicode range: emoji_extended; Long fallback: 11 chars
NG1500,🝭,ng:category_theory:fmap_meta,[FMAPMETA],category_theory,fmap_meta,95.0,B,False,True,False,False,,,emoji_extended,False,Risky Unicode range: emoji_extended
NG1501,🝘,ng:category_theory:monads_sys,[MONADSSYS],category_theory,monads_sys,95.0,A,False,True,True,False,[MONADS..],,emoji_extended,False,Risky Unicode range: emoji_extended; Long fallback: 11 chars
NG1502,⦺,ng:category_theory:monads_meta,[MONADSMETA],category_theory,monads_meta,95.0,C,False,False,True,False,[MONADS..],,safe,False,Long fallback: 12 chars
NG1503,⫛,ng:category_theory:monads_core,[MONADSCORE],category_theory,monads_core,95.0,C,False,False,True,False,[MONADS..],,safe,False,Long fallback: 12 chars
NG1504,⤖,ng:category_theory:monads_op_1,[MONADSOP1],category_theory,monads_op_1,95.0,B,False,False,True,True,[MONADS..],monads_op_1,safe,False,Long fallback: 11 chars; Generic numbered name
NG1505,🧨,ng:category_theory:comonads_core,[COMONADSCORE],category_theory,comonads_core,95.0,A,False,True,True,False,[COMONA..],,emoji_extended,False,Risky Unicode range: emoji_extended; Long fallback: 14 chars
NG1506,🤼,ng:category_theory:comonads_core_1,[COMONADSCORE1],category_theory,comonads_core_1,95.0,A,False,True,True,True,[COMONA..],comonads_core_1,emoji_extended,False,Risky Unicode range: emoji_extended; Long fallback: 15 chars; Generic numbered name
NG1507,⧤,ng:category_theory:comonads_op,[COMONADSOP],category_theory,comonads_op,95.0,C,False,False,True,False,[COMONA..],,safe,False,Long fallback: 12 chars
NG1508,🍗,ng:category_theory:comonads_sys,[COMONADSSYS],category_theory,comonads_sys,95.0,A,False,True,True,False,[COMONA..],,emoji_extended,False,Risky Unicode range: emoji_extended; Long fallback: 13 chars
NG1509,⦜,ng:category_theory:comonads_meta_1,[COMONADSMETA1],category_theory,comonads_meta_1,95.0,B,False,False,True,True,[COMONA..],comonads_meta_1,safe,False,Long fallback: 15 chars; Generic numbered name
NG1510,➚,ng:category_theory:comonads_core_2,[COMONADSCORE2],category_theory,comonads_core_2,95.0,B,False,False,True,True,[COMONA..],comonads_core_2,safe,False,Long fallback: 15 chars; Generic numbered name
NG1511,🝡,ng:category_theory:adjunctions_proc,[ADJUNCTIONSPROC],category_theory,adjunctions_proc,95.0,A,False,True,True,False,[ADJUNC..],,emoji_extended,False,Risky Unicode range: emoji_extended; Long fallback: 17 chars
NG1512,🛂,ng:category_theory:adjunctions_1,[ADJUNCTIONS1],category_theory,adjunctions_1,95.0,A,False,True,True,True,[ADJUNC..],adjunctions_1,emoji_extended,False,Risky Unicode range: emoji_extended; Long fallback: 14 chars; Generic numbered name
NG1513,⢉,ng:category_theory:adjunctions_2,[ADJUNCTIONS2],category_theory,adjunctions_2,95.0,B,False,False,True,True,[ADJUNC..],adjunctions_2,safe,False,Long fallback: 14 chars; Generic numbered name
NG1514,🠪,ng:category_theory:adjunctions_core,[ADJUNCTIONSCORE],category_theory,adjunctions_core,95.0,A,False,True,True,False,[ADJUNC..],,emoji_extended,False,Risky Unicode range: emoji_extended; Long fallback: 17 chars
NG1515,⫕,ng:category_theory:limits_ctrl,[LIMITSCTRL],category_theory,limits_ctrl,95.0,C,False,False,True,False,[LIMITS..],,safe,False,Long fallback: 12 chars
NG1516,🞖,ng:category_theory:limits_meta,[LIMITSMETA],category_theory,limits_meta,95.0,A,False,True,True,False,[LIMITS..],,emoji_extended,False,Risky Unicode range: emoji_extended; Long fallback: 12 chars
NG1517,🍳,ng:category_theory:limits_fn,[LIMITSFN],category_theory,limits_fn,95.0,B,False,True,False,False,,,emoji_extended,False,Risky Unicode range: emoji_extended
NG1518,⫲,ng:category_theory:limits_meta_1,[LIMITSMETA1],category_theory,limits_meta_1,95.0,B,False,False,True,True,[LIMITS..],limits_meta_1,safe,False,Long fallback: 13 chars; Generic numbered name
NG1519,🧅,ng:category_theory:limits_sys_1,[LIMITSSYS1],category_theory,limits_sys_1,95.0,A,False,True,True,True,[LIMITS..],limits_sys_1,emoji_extended,False,Risky Unicode range: emoji_extended; Long fallback: 12 chars; Generic numbered name
NG1520,😀,ng:category_theory:colimits_meta,[COLIMITSMETA],category_theory,colimits_meta,95.0,A,False,True,True,False,[COLIMI..],,emoji_extended,False,Risky Unicode range: emoji_extended; Long fallback: 14 chars
NG1521,🝴,ng:category_theory:colimits_fn,[COLIMITSFN],category_theory,colimits_fn,95.0,A,False,True,True,False,[COLIMI..],,emoji_extended,False,Risky Unicode range: emoji_extended; Long fallback: 12 chars
NG1522,➿,ng:category_theory:colimits_meta_1,[COLIMITSMETA1],category_theory,colimits_meta_1,95.0,B,False,False,True,True,[COLIMI..],colimits_meta_1,safe,False,Long fallback: 15 chars; Generic numbered name
NG1523,🝊,ng:category_theory:colimits_ctrl,[COLIMITSCTRL],category_theory,colimits_ctrl,95.0,A,False,True,True,False,[COLIMI..],,emoji_extended,False,Risky Unicode range: emoji_extended; Long fallback: 14 chars
NG1524,👏,ng:category_theory:topoi_core,[TOPOICORE],category_theory,topoi_core,95.0,A,False,True,True,False,[TOPOIC..],,emoji_extended,False,Risky Unicode range: emoji_extended; Long fallback: 11 chars
NG1525,🦌,ng:category_theory:topoi_op,[TOPOIOP],category_theory,topoi_op,95.0,B,False,True,False,False,,,emoji_extended,False,Risky Unicode range: emoji_extended
NG1526,🙆,ng:category_theory:topoi_fn,[TOPOIFN],category_theory,topoi_fn,95.0,B,False,True,False,False,,,emoji_extended,False,Risky Unicode range: emoji_extended
NG1527,🗰,ng:category_theory:topoi_core_1,[TOPOICORE1],category_theory,topoi_core_1,95.0,A,False,True,True,True,[TOPOIC..],topoi_core_1,emoji_extended,False,Risky Unicode range: emoji_extended; Long fallback: 12 chars; Generic numbered name
NG1528,⤬,ng:category_theory:topoi_meta,[TOPOIMETA],category_theory,topoi_meta,95.0,C,False,False,True,False,[TOPOIM..],,safe,False,Long fallback: 11 chars
NG1529,🥖,ng:category_theory:sheaves_op,[SHEAVESOP],category_theory,sheaves_op,95.0,A,False,True,True,False,[SHEAVE..],,emoji_extended,False,Risky Unicode range: emoji_extended; Long fallback: 11 chars
NG1530,🟋,ng:category_theory:sheaves_ctrl,[SHEAVESCTRL],category_theory,sheaves_ctrl,95.0,A,False,True,True,False,[SHEAVE..],,emoji_extended,False,Risky Unicode range: emoji_extended; Long fallback: 13 chars
NG1531,🡭,ng:category_theory:sheaves_ctrl_1,[SHEAVESCTRL1],category_theory,sheaves_ctrl_1,95.0,A,False,True,True,True,[SHEAVE..],sheaves_ctrl_1,emoji_extended,False,Risky Unicode range: emoji_extended; Long fallback: 14 chars; Generic numbered name
NG1532,😓,ng:category_theory:sheaves_fn,[SHEAVESFN],category_theory,sheaves_fn,95.0,A,False,True,True,False,[SHEAVE..],,emoji_extended,False,Risky Unicode range: emoji_extended; Long fallback: 11 chars
NG1533,😞,ng:category_theory:sheaves_sys,[SHEAVESSYS],category_theory,sheaves_sys,95.0,A,False,True,True,False,[SHEAVE..],,emoji_extended,False,Risky Unicode range: emoji_extended; Long fallback: 12 chars
NG1534,⣳,ng:category_theory:sheaves_ctrl_2,[SHEAVESCTRL2],category_theory,sheaves_ctrl_2,95.0,B,False,False,True,True,[SHEAVE..],sheaves_ctrl_2,safe,False,Long fallback: 14 chars; Generic numbered name
NG1535,🟦,ng:category_theory:sheaves_op_2,[SHEAVESOP2],category_theory,sheaves_op_2,95.0,A,False,True,True,True,[SHEAVE..],sheaves_op_2,emoji_extended,False,Risky Unicode range: emoji_extended; Long fallback: 12 chars; Generic numbered name
NG1536,✻,ng:category_theory:sheaves_fn_1,[SHEAVESFN1],category_theory,sheaves_fn_1,95.0,B,False,False,True,True,[SHEAVE..],sheaves_fn_1,safe,False,Long fallback: 12 chars; Generic numbered name
NG1537,⡦,ng:category_theory:sheaves_op_3,[SHEAVESOP3],category_theory,sheaves_op_3,95.0,B,False,False,True,True,[SHEAVE..],sheaves_op_3,safe,False,Long fallback: 12 chars; Generic numbered name
NG1538,🛺,ng:category_theory:sheaves_proc,[SHEAVESPROC],category_theory,sheaves_proc,95.0,A,False,True,True,False,[SHEAVE..],,emoji_extended,False,Risky Unicode range: emoji_extended; Long fallback: 13 chars
NG1539,🡫,ng:type_theory:sigma_type_proc,[SIGMATYPEPROC],type_theory,sigma_type_proc,95.0,A,False,True,True,False,[SIGMAT..],,emoji_extended,False,Risky Unicode range: emoji_extended; Long fallback: 15 chars
NG1540,🠡,ng:type_theory:sigma_type_core,[SIGMATYPECORE],type_theory,sigma_type_core,95.0,A,False,True,True,False,[SIGMAT..],,emoji_extended,False,Risky Unicode range: emoji_extended; Long fallback: 15 chars
NG1541,🍭,ng:type_theory:dep_pair_op,[DEPPAIROP],type_theory,dep_pair_op,95.0,A,False,True,True,False,[DEPPAI..],,emoji_extended,False,Risky Unicode range: emoji_extended; Long fallback: 11 chars
NG1542,⫌,ng:type_theory:pi_type_meta,[PITYPEMETA],type_theory,pi_type_meta,95.0,C,False,False,True,False,[PITYPE..],,safe,False,Long fallback: 12 chars
NG1543,⦮,ng:type_theory:indexed_sys,[INDEXEDSYS],type_theory,indexed_sys,95.0,C,False,False,True,False,[INDEXE..],,safe,False,Long fallback: 12 chars
NG1544,🔥,ng:type_theory:lineartypes_proc,[LINEARTYPESPROC],type_theory,lineartypes_proc,95.0,A,False,True,True,False,[LINEAR..],,emoji_extended,False,Risky Unicode range: emoji_extended; Long fallback: 17 chars
NG1545,⧙,ng:type_theory:lineartypes_op,[LINEARTYPESOP],type_theory,lineartypes_op,95.0,C,False,False,True,False,[LINEAR..],,safe,False,Long fallback: 15 chars
NG1546,⨈,ng:type_theory:lineartypes,[LINEARTYPES],type_theory,lineartypes,95.0,C,False,False,True,False,[LINEAR..],,safe,False,Long fallback: 13 chars
NG1547,⠴,ng:type_theory:lineartypes_fn,[LINEARTYPESFN],type_theory,lineartypes_fn,95.0,C,False,False,True,False,[LINEAR..],,safe,False,Long fallback: 15 chars
NG1548,⡍,ng:type_theory:lineartypes_meta_1,[LINEARTYPESMETA1],type_theory,lineartypes_meta_1,95.0,B,False,False,True,True,[LINEAR..],lineartypes_meta_1,safe,False,Long fallback: 18 chars; Generic numbered name
NG1549,⠾,ng:type_theory:sessiontypes_fn,[SESSIONTYPESFN],type_theory,sessiontypes_fn,95.0,C,False,False,True,False,[SESSIO..],,safe,False,Long fallback: 16 chars
NG1550,⬘,ng:type_theory:sessiontypes_core,[SESSIONTYPESCORE],type_theory,sessiontypes_core,95.0,C,False,False,True,False,[SESSIO..],,safe,False,Long fallback: 18 chars
NG1551,⥩,ng:type_theory:sessiontypes_ctrl,[SESSIONTYPESCTRL],type_theory,sessiontypes_ctrl,95.0,C,False,False,True,False,[SESSIO..],,safe,False,Long fallback: 18 chars
NG1552,⦾,ng:type_theory:sessiontypes_core_1,[SESSIONTYPESCORE1],type_theory,sessiontypes_core_1,95.0,B,False,False,True,True,[SESSIO..],sessiontypes_core_1,safe,False,Long fallback: 19 chars; Generic numbered name
NG1553,🜣,ng:type_theory:sessiontypes_op_1,[SESSIONTYPESOP1],type_theory,sessiontypes_op_1,95.0,A,False,True,True,True,[SESSIO..],sessiontypes_op_1,emoji_extended,False,Risky Unicode range: emoji_extended; Long fallback: 17 chars; Generic numbered name
NG1554,👲,ng:type_theory:sessiontypes,[SESSIONTYPES],type_theory,sessiontypes,95.0,A,False,True,True,False,[SESSIO..],,emoji_extended,False,Risky Unicode range: emoji_extended; Long fallback: 14 chars
NG1555,𝝯,ng:type_theory:effecttypes_sys_1,[EFFECTTYPESSYS1],type_theory,effecttypes_sys_1,95.0,B,False,False,True,True,[EFFECT..],effecttypes_sys_1,safe,False,Long fallback: 17 chars; Generic numbered name
NG1556,⯓,ng:type_theory:effecttypes_sys_2,[EFFECTTYPESSYS2],type_theory,effecttypes_sys_2,95.0,B,False,False,True,True,[EFFECT..],effecttypes_sys_2,safe,False,Long fallback: 17 chars; Generic numbered name
NG1557,🔖,ng:type_theory:effecttypes_ctrl,[EFFECTTYPESCTRL],type_theory,effecttypes_ctrl,95.0,A,False,True,True,False,[EFFECT..],,emoji_extended,False,Risky Unicode range: emoji_extended; Long fallback: 17 chars
NG1558,🝦,ng:type_theory:effecttypes_proc,[EFFECTTYPESPROC],type_theory,effecttypes_proc,95.0,A,False,True,True,False,[EFFECT..],,emoji_extended,False,Risky Unicode range: emoji_extended; Long fallback: 17 chars
NG1559,🚔,ng:type_theory:refinementtypes_op,[REFINEMENTTYPESOP],type_theory,refinementtypes_op,95.0,A,False,True,True,False,[REFINE..],,emoji_extended,False,Risky Unicode range: emoji_extended; Long fallback: 19 chars
NG1560,🢣,ng:type_theory:refinementtypes_1,[REFINEMENTTYPES1],type_theory,refinementtypes_1,95.0,A,False,True,True,True,[REFINE..],refinementtypes_1,emoji_extended,False,Risky Unicode range: emoji_extended; Long fallback: 18 chars; Generic numbered name
NG1561,⡟,ng:type_theory:refinementtypes_fn,[REFINEMENTTYPESFN],type_theory,refinementtypes_fn,95.0,C,False,False,True,False,[REFINE..],,safe,False,Long fallback: 19 chars
NG1562,⣉,ng:type_theory:refinementtypes_fn_1,[REFINEMENTTYPESFN1],type_theory,refinementtypes_fn_1,95.0,B,False,False,True,True,[REFINE..],refinementtypes_fn_1,safe,False,Long fallback: 20 chars; Generic numbered name
NG1563,🠦,ng:type_theory:refinementtypes_fn_2,[REFINEMENTTYPESFN2],type_theory,refinementtypes_fn_2,95.0,A,False,True,True,True,[REFINE..],refinementtypes_fn_2,emoji_extended,False,Risky Unicode range: emoji_extended; Long fallback: 20 chars; Generic numbered name
NG1564,🧉,ng:type_theory:intersectiontypes_1,[INTERSECTIONTYPES1],type_theory,intersectiontypes_1,95.0,A,False,True,True,True,[INTERS..],intersectiontypes_1,emoji_extended,False,Risky Unicode range: emoji_extended; Long fallback: 20 chars; Generic numbered name
NG1565,⥟,ng:type_theory:intersectiontypes_2,[INTERSECTIONTYPES2],type_theory,intersectiontypes_2,95.0,B,False,False,True,True,[INTERS..],intersectiontypes_2,safe,False,Long fallback: 20 chars; Generic numbered name
NG1566,⣿,ng:type_theory:intersectiontypes_4,[INTERSECTIONTYPES4],type_theory,intersectiontypes_4,95.0,B,False,False,True,True,[INTERS..],intersectiontypes_4,safe,False,Long fallback: 20 chars; Generic numbered name
NG1567,⮯,ng:type_theory:uniontypes_meta,[UNIONTYPESMETA],type_theory,uniontypes_meta,95.0,C,False,False,True,False,[UNIONT..],,safe,False,Long fallback: 16 chars
NG1568,✠,ng:type_theory:uniontypes_ctrl,[UNIONTYPESCTRL],type_theory,uniontypes_ctrl,95.0,C,False,False,True,False,[UNIONT..],,safe,False,Long fallback: 16 chars
NG1569,💕,ng:type_theory:uniontypes_1,[UNIONTYPES1],type_theory,uniontypes_1,95.0,A,False,True,True,True,[UNIONT..],uniontypes_1,emoji_extended,False,Risky Unicode range: emoji_extended; Long fallback: 13 chars; Generic numbered name
NG1570,🢕,ng:type_theory:uniontypes_meta_1,[UNIONTYPESMETA1],type_theory,uniontypes_meta_1,95.0,A,False,True,True,True,[UNIONT..],uniontypes_meta_1,emoji_extended,False,Risky Unicode range: emoji_extended; Long fallback: 17 chars; Generic numbered name
NG1571,⩈,ng:type_theory:gradualtyping_core_1,[GRADUALTYPINGCORE1],type_theory,gradualtyping_core_1,95.0,B,False,False,True,True,[GRADUA..],gradualtyping_core_1,safe,False,Long fallback: 20 chars; Generic numbered name
NG1572,⢲,ng:type_theory:gradualtyping_proc,[GRADUALTYPINGPROC],type_theory,gradualtyping_proc,95.0,C,False,False,True,False,[GRADUA..],,safe,False,Long fallback: 19 chars
NG1573,⡲,ng:type_theory:gradualtyping_proc_1,[GRADUALTYPINGPROC1],type_theory,gradualtyping_proc_1,95.0,B,False,False,True,True,[GRADUA..],gradualtyping_proc_1,safe,False,Long fallback: 20 chars; Generic numbered name
NG1574,⣱,ng:type_theory:gradualtyping,[GRADUALTYPING],type_theory,gradualtyping,95.0,C,False,False,True,False,[GRADUA..],,safe,False,Long fallback: 15 chars
NG1575,🕦,ng:type_theory:gradualtyping_fn,[GRADUALTYPINGFN],type_theory,gradualtyping_fn,95.0,A,False,True,True,False,[GRADUA..],,emoji_extended,False,Risky Unicode range: emoji_extended; Long fallback: 17 chars
NG1576,⧣,ng:concurrency_advanced:spawn,[SPAWN],concurrency_advanced,spawn,95.0,OK,False,False,False,False,,,safe,False,
NG1577,🛪,ng:concurrency_advanced:spawn_op,[SPAWNOP],concurrency_advanced,spawn_op,95.0,B,False,True,False,False,,,emoji_extended,False,Risky Unicode range: emoji_extended
NG1578,🞺,ng:concurrency_advanced:mailbox_core,[MAILBOXCORE],concurrency_advanced,mailbox_core,95.0,A,False,True,True,False,[MAILBO..],,emoji_extended,False,Risky Unicode range: emoji_extended; Long fallback: 13 chars
NG1579,🜛,ng:concurrency_advanced:actor_meta,[ACTORMETA],concurrency_advanced,actor_meta,95.0,A,False,True,True,False,[ACTORM..],,emoji_extended,False,Risky Unicode range: emoji_extended; Long fallback: 11 chars
NG1580,⭟,ng:concurrency_advanced:actor_meta_1,[ACTORMETA1],concurrency_advanced,actor_meta_1,95.0,B,False,False,True,True,[ACTORM..],actor_meta_1,safe,False,Long fallback: 12 chars; Generic numbered name
NG1581,🥵,ng:concurrency_advanced:message_sys,[MESSAGESYS],concurrency_advanced,message_sys,95.0,A,False,True,True,False,[MESSAG..],,emoji_extended,False,Risky Unicode range: emoji_extended; Long fallback: 12 chars
NG1582,🖝,ng:concurrency_advanced:message_op,[MESSAGEOP],concurrency_advanced,message_op,95.0,A,False,True,True,False,[MESSAG..],,emoji_extended,False,Risky Unicode range: emoji_extended; Long fallback: 11 chars
NG1583,⦖,ng:concurrency_advanced:spawn_ctrl,[SPAWNCTRL],concurrency_advanced,spawn_ctrl,95.0,C,False,False,True,False,[SPAWNC..],,safe,False,Long fallback: 11 chars
NG1584,⭚,ng:concurrency_advanced:cspchannels_fn,[CSPCHANNELSFN],concurrency_advanced,cspchannels_fn,95.0,C,False,False,True,False,[CSPCHA..],,safe,False,Long fallback: 15 chars
NG1585,👾,ng:concurrency_advanced:cspchannels_meta,[CSPCHANNELSMETA],concurrency_advanced,cspchannels_meta,95.0,A,False,True,True,False,[CSPCHA..],,emoji_extended,False,Risky Unicode range: emoji_extended; Long fallback: 17 chars
NG1586,⧖,ng:concurrency_advanced:cspchannels_op,[CSPCHANNELSOP],concurrency_advanced,cspchannels_op,95.0,C,False,False,True,False,[CSPCHA..],,safe,False,Long fallback: 15 chars
NG1587,⧌,ng:concurrency_advanced:cspchannels_proc_1,[CSPCHANNELSPROC1],concurrency_advanced,cspchannels_proc_1,95.0,B,False,False,True,True,[CSPCHA..],cspchannels_proc_1,safe,False,Long fallback: 18 chars; Generic numbered name
NG1588,😟,ng:concurrency_advanced:cspchannels_fn_2,[CSPCHANNELSFN2],concurrency_advanced,cspchannels_fn_2,95.0,A,False,True,True,True,[CSPCHA..],cspchannels_fn_2,emoji_extended,False,Risky Unicode range: emoji_extended; Long fallback: 16 chars; Generic numbered name
NG1589,🙏,ng:concurrency_advanced:cspchannels_ctrl,[CSPCHANNELSCTRL],concurrency_advanced,cspchannels_ctrl,95.0,A,False,True,True,False,[CSPCHA..],,emoji_extended,False,Risky Unicode range: emoji_extended; Long fallback: 17 chars
NG1590,🟓,ng:concurrency_advanced:cspchannels_op_1,[CSPCHANNELSOP1],concurrency_advanced,cspchannels_op_1,95.0,A,False,True,True,True,[CSPCHA..],cspchannels_op_1,emoji_extended,False,Risky Unicode range: emoji_extended; Long fallback: 16 chars; Generic numbered name
NG1591,🏋,ng:concurrency_advanced:cspchannels_sys_1,[CSPCHANNELSSYS1],concurrency_advanced,cspchannels_sys_1,95.0,A,False,True,True,True,[CSPCHA..],cspchannels_sys_1,emoji_extended,False,Risky Unicode range: emoji_extended; Long fallback: 17 chars; Generic numbered name
NG1592,🥹,ng:concurrency_advanced:lockfreealgorithms,[LOCKFREEALGORITHMS],concurrency_advanced,lockfreealgorithms,95.0,A,False,True,True,False,[LOCKFR..],,emoji_extended,False,Risky Unicode range: emoji_extended; Long fallback: 20 chars
NG1593,🤦,ng:concurrency_advanced:waitfreealgorithms,[WAITFREEALGORITHMS],concurrency_advanced,waitfreealgorithms,95.0,A,False,True,True,False,[WAITFR..],,emoji_extended,False,Risky Unicode range: emoji_extended; Long fallback: 20 chars
NG1594,⡬,ng:concurrency_advanced:memoryordering_proc,[MEMORYORDERINGPROC],concurrency_advanced,memoryordering_proc,95.0,C,False,False,True,False,[MEMORY..],,safe,False,Long fallback: 20 chars
NG1595,🦥,ng:concurrency_advanced:memoryordering_ctrl,[MEMORYORDERINGCTRL],concurrency_advanced,memoryordering_ctrl,95.0,A,False,True,True,False,[MEMORY..],,emoji_extended,False,Risky Unicode range: emoji_extended; Long fallback: 20 chars
NG1596,🞵,ng:concurrency_advanced:memoryordering_op,[MEMORYORDERINGOP],concurrency_advanced,memoryordering_op,95.0,A,False,True,True,False,[MEMORY..],,emoji_extended,False,Risky Unicode range: emoji_extended; Long fallback: 18 chars
NG1597,🝏,ng:concurrency_advanced:memoryordering_op_1,[MEMORYORDERINGOP1],concurrency_advanced,memoryordering_op_1,95.0,A,False,True,True,True,[MEMORY..],memoryordering_op_1,emoji_extended,False,Risky Unicode range: emoji_extended; Long fallback: 19 chars; Generic numbered name
NG1598,⦧,ng:concurrency_advanced:memoryordering_op_2,[MEMORYORDERINGOP2],concurrency_advanced,memoryordering_op_2,95.0,B,False,False,True,True,[MEMORY..],memoryordering_op_2,safe,False,Long fallback: 19 chars; Generic numbered name
NG1599,⨷,ng:concurrency_advanced:memoryordering_fn,[MEMORYORDERINGFN],concurrency_advanced,memoryordering_fn,95.0,C,False,False,True,False,[MEMORY..],,safe,False,Long fallback: 18 chars
NG1600,🡒,ng:concurrency_advanced:memoryordering_fn_1,[MEMORYORDERINGFN1],concurrency_advanced,memoryordering_fn_1,95.0,A,False,True,True,True,[MEMORY..],memoryordering_fn_1,emoji_extended,False,Risky Unicode range: emoji_extended; Long fallback: 19 chars; Generic numbered name
NG1601,🚺,ng:concurrency_advanced:memoryordering,[MEMORYORDERING],concurrency_advanced,memoryordering,95.0,A,False,True,True,False,[MEMORY..],,emoji_extended,False,Risky Unicode range: emoji_extended; Long fallback: 16 chars
NG1602,✁,ng:concurrency_advanced:memoryordering_core,[MEMORYORDERINGCORE],concurrency_advanced,memoryordering_core,95.0,C,False,False,True,False,[MEMORY..],,safe,False,Long fallback: 20 chars
NG1603,⡃,ng:concurrency_advanced:memoryordering_op_4,[MEMORYORDERINGOP4],concurrency_advanced,memoryordering_op_4,95.0,B,False,False,True,True,[MEMORY..],memoryordering_op_4,safe,False,Long fallback: 19 chars; Generic numbered name
NG1604,⯎,ng:concurrency_advanced:memoryordering_sys_1,[MEMORYORDERINGSYS1],concurrency_advanced,memoryordering_sys_1,95.0,B,False,False,True,True,[MEMORY..],memoryordering_sys_1,safe,False,Long fallback: 20 chars; Generic numbered name
NG1605,🛓,ng:concurrency_advanced:atomicoperations_op,[ATOMICOPERATIONSOP],concurrency_advanced,atomicoperations_op,95.0,A,False,True,True,False,[ATOMIC..],,emoji_extended,False,Risky Unicode range: emoji_extended; Long fallback: 20 chars
NG1606,⦔,ng:concurrency_advanced:atomicoperations_fn,[ATOMICOPERATIONSFN],concurrency_advanced,atomicoperations_fn,95.0,C,False,False,True,False,[ATOMIC..],,safe,False,Long fallback: 20 chars
NG1607,⫨,ng:concurrency_advanced:atomicoperations_1,[ATOMICOPERATIONS1],concurrency_advanced,atomicoperations_1,95.0,B,False,False,True,True,[ATOMIC..],atomicoperations_1,safe,False,Long fallback: 19 chars; Generic numbered name
NG1608,🥠,ng:concurrency_advanced:atomicoperations_4,[ATOMICOPERATIONS4],concurrency_advanced,atomicoperations_4,95.0,A,False,True,True,True,[ATOMIC..],atomicoperations_4,emoji_extended,False,Risky Unicode range: emoji_extended; Long fallback: 19 chars; Generic numbered name
NG1609,❙,ng:concurrency_advanced:atomicoperations_8,[ATOMICOPERATIONS8],concurrency_advanced,atomicoperations_8,95.0,B,False,False,True,True,[ATOMIC..],atomicoperations_8,safe,False,Long fallback: 19 chars; Generic numbered name
NG1610,⯺,ng:concurrency_advanced:atomicoperations_9,[ATOMICOPERATIONS9],concurrency_advanced,atomicoperations_9,95.0,B,False,False,True,True,[ATOMIC..],atomicoperations_9,safe,False,Long fallback: 19 chars; Generic numbered name
NG1611,🎷,ng:concurrency_advanced:atomicoperations_10,[ATOMICOPERATIONS10],concurrency_advanced,atomicoperations_10,95.0,A,False,True,True,True,[ATOMIC..],atomicoperations_10,emoji_extended,False,Risky Unicode range: emoji_extended; Long fallback: 20 chars; Generic numbered name
NG1612,🞔,ng:concurrency_advanced:compareandswap_op,[COMPAREANDSWAPOP],concurrency_advanced,compareandswap_op,95.0,A,False,True,True,False,[COMPAR..],,emoji_extended,False,Risky Unicode range: emoji_extended; Long fallback: 18 chars
NG1613,🖡,ng:concurrency_advanced:compareandswap_core,[COMPAREANDSWAPCORE],concurrency_advanced,compareandswap_core,95.0,A,False,True,True,False,[COMPAR..],,emoji_extended,False,Risky Unicode range: emoji_extended; Long fallback: 20 chars
NG1614,🧖,ng:concurrency_advanced:compareandswap_proc,[COMPAREANDSWAPPROC],concurrency_advanced,compareandswap_proc,95.0,A,False,True,True,False,[COMPAR..],,emoji_extended,False,Risky Unicode range: emoji_extended; Long fallback: 20 chars
NG1615,⩨,ng:concurrency_advanced:compareandswap_sys_1,[COMPAREANDSWAPSYS1],concurrency_advanced,compareandswap_sys_1,95.0,B,False,False,True,True,[COMPAR..],compareandswap_sys_1,safe,False,Long fallback: 20 chars; Generic numbered name
NG1616,🜡,ng:concurrency_advanced:compareandswap_fn,[COMPAREANDSWAPFN],concurrency_advanced,compareandswap_fn,95.0,A,False,True,True,False,[COMPAR..],,emoji_extended,False,Risky Unicode range: emoji_extended; Long fallback: 18 chars
NG1617,⫝,ng:concurrency_advanced:compareandswap_sys_2,[COMPAREANDSWAPSYS2],concurrency_advanced,compareandswap_sys_2,95.0,B,False,False,True,True,[COMPAR..],compareandswap_sys_2,safe,False,Long fallback: 20 chars; Generic numbered name
NG1618,🧍,ng:concurrency_advanced:compareandswap_sys_4,[COMPAREANDSWAPSYS4],concurrency_advanced,compareandswap_sys_4,95.0,A,False,True,True,True,[COMPAR..],compareandswap_sys_4,emoji_extended,False,Risky Unicode range: emoji_extended; Long fallback: 20 chars; Generic numbered name
NG1619,🟊,ng:concurrency_advanced:hazardpointers_proc,[HAZARDPOINTERSPROC],concurrency_advanced,hazardpointers_proc,95.0,A,False,True,True,False,[HAZARD..],,emoji_extended,False,Risky Unicode range: emoji_extended; Long fallback: 20 chars
NG1620,🢚,ng:concurrency_advanced:hazardpointers_sys,[HAZARDPOINTERSSYS],concurrency_advanced,hazardpointers_sys,95.0,A,False,True,True,False,[HAZARD..],,emoji_extended,False,Risky Unicode range: emoji_extended; Long fallback: 19 chars
NG1621,⦐,ng:concurrency_advanced:hazardpointers,[HAZARDPOINTERS],concurrency_advanced,hazardpointers,95.0,C,False,False,True,False,[HAZARD..],,safe,False,Long fallback: 16 chars
NG1622,⫯,ng:concurrency_advanced:hazardpointers_fn_1,[HAZARDPOINTERSFN1],concurrency_advanced,hazardpointers_fn_1,95.0,B,False,False,True,True,[HAZARD..],hazardpointers_fn_1,safe,False,Long fallback: 19 chars; Generic numbered name
NG1623,⠥,ng:concurrency_advanced:hazardpointers_op_2,[HAZARDPOINTERSOP2],concurrency_advanced,hazardpointers_op_2,95.0,B,False,False,True,True,[HAZARD..],hazardpointers_op_2,safe,False,Long fallback: 19 chars; Generic numbered name
NG1624,❬,ng:concurrency_advanced:hazardpointers_core,[HAZARDPOINTERSCORE],concurrency_advanced,hazardpointers_core,95.0,C,False,False,True,False,[HAZARD..],,safe,False,Long fallback: 20 chars
NG1625,🍙,ng:concurrency_advanced:hazardpointers_op_3,[HAZARDPOINTERSOP3],concurrency_advanced,hazardpointers_op_3,95.0,A,False,True,True,True,[HAZARD..],hazardpointers_op_3,emoji_extended,False,Risky Unicode range: emoji_extended; Long fallback: 19 chars; Generic numbered name
NG1626,💄,ng:concurrency_advanced:hazardpointers_sys_1,[HAZARDPOINTERSSYS1],concurrency_advanced,hazardpointers_sys_1,95.0,A,False,True,True,True,[HAZARD..],hazardpointers_sys_1,emoji_extended,False,Risky Unicode range: emoji_extended; Long fallback: 20 chars; Generic numbered name
NG1627,⠆,ng:concurrency_advanced:hazardpointers_fn_3,[HAZARDPOINTERSFN3],concurrency_advanced,hazardpointers_fn_3,95.0,B,False,False,True,True,[HAZARD..],hazardpointers_fn_3,safe,False,Long fallback: 19 chars; Generic numbered name
NG1628,🚣,ng:concurrency_advanced:hazardpointers_sys_2,[HAZARDPOINTERSSYS2],concurrency_advanced,hazardpointers_sys_2,95.0,A,False,True,True,True,[HAZARD..],hazardpointers_sys_2,emoji_extended,False,Risky Unicode range: emoji_extended; Long fallback: 20 chars; Generic numbered name
NG1629,⮡,ng:concurrency_advanced:hazardpointers_sys_3,[HAZARDPOINTERSSYS3],concurrency_advanced,hazardpointers_sys_3,95.0,B,False,False,True,True,[HAZARD..],hazardpointers_sys_3,safe,False,Long fallback: 20 chars; Generic numbered name
NG1630,⨏,ng:concurrency_advanced:hazardpointers_op_4,[HAZARDPOINTERSOP4],concurrency_advanced,hazardpointers_op_4,95.0,B,False,False,True,True,[HAZARD..],hazardpointers_op_4,safe,False,Long fallback: 19 chars; Generic numbered name
NG1631,🜀,ng:concurrency_advanced:hazardpointers_sys_5,[HAZARDPOINTERSSYS5],concurrency_advanced,hazardpointers_sys_5,95.0,A,False,True,True,True,[HAZARD..],hazardpointers_sys_5,emoji_extended,False,Risky Unicode range: emoji_extended; Long fallback: 20 chars; Generic numbered name
NG1632,⩎,ng:concurrency_advanced:hazardpointers_fn_6,[HAZARDPOINTERSFN6],concurrency_advanced,hazardpointers_fn_6,95.0,B,False,False,True,True,[HAZARD..],hazardpointers_fn_6,safe,False,Long fallback: 19 chars; Generic numbered name
NG1633,🜋,ng:concurrency_advanced:hazardpointers_op_5,[HAZARDPOINTERSOP5],concurrency_advanced,hazardpointers_op_5,95.0,A,False,True,True,True,[HAZARD..],hazardpointers_op_5,emoji_extended,False,Risky Unicode range: emoji_extended; Long fallback: 19 chars; Generic numbered name
NG1634,🜇,ng:concurrency_advanced:hazardpointers_sys_7,[HAZARDPOINTERSSYS7],concurrency_advanced,hazardpointers_sys_7,95.0,A,False,True,True,True,[HAZARD..],hazardpointers_sys_7,emoji_extended,False,Risky Unicode range: emoji_extended; Long fallback: 20 chars; Generic numbered name
NG1635,🞢,ng:concurrency_advanced:hazardpointers_1,[HAZARDPOINTERS1],concurrency_advanced,hazardpointers_1,95.0,A,False,True,True,True,[HAZARD..],hazardpointers_1,emoji_extended,False,Risky Unicode range: emoji_extended; Long fallback: 17 chars; Generic numbered name
NG1636,🥒,ng:concurrency_advanced:hazardpointers_sys_9,[HAZARDPOINTERSSYS9],concurrency_advanced,hazardpointers_sys_9,95.0,A,False,True,True,True,[HAZARD..],hazardpointers_sys_9,emoji_extended,False,Risky Unicode range: emoji_extended; Long fallback: 20 chars; Generic numbered name
NG1637,👯,ng:concurrency_advanced:hazardpointers_3,[HAZARDPOINTERS3],concurrency_advanced,hazardpointers_3,95.0,A,False,True,True,True,[HAZARD..],hazardpointers_3,emoji_extended,False,Risky Unicode range: emoji_extended; Long fallback: 17 chars; Generic numbered name
NG1638,🡲,ng:concurrency_advanced:hazardpointers_op_6,[HAZARDPOINTERSOP6],concurrency_advanced,hazardpointers_op_6,95.0,A,False,True,True,True,[HAZARD..],hazardpointers_op_6,emoji_extended,False,Risky Unicode range: emoji_extended; Long fallback: 19 chars; Generic numbered name
NG1639,😩,ng:concurrency_advanced:hazardpointers_4,[HAZARDPOINTERS4],concurrency_advanced,hazardpointers_4,95.0,A,False,True,True,True,[HAZARD..],hazardpointers_4,emoji_extended,False,Risky Unicode range: emoji_extended; Long fallback: 17 chars; Generic numbered name
NG1640,⭷,ng:concurrency_advanced:hazardpointers_op_7,[HAZARDPOINTERSOP7],concurrency_advanced,hazardpointers_op_7,95.0,B,False,False,True,True,[HAZARD..],hazardpointers_op_7,safe,False,Long fallback: 19 chars; Generic numbered name
NG1641,🛤,ng:concurrency_advanced:hazardpointers_op_8,[HAZARDPOINTERSOP8],concurrency_advanced,hazardpointers_op_8,95.0,A,False,True,True,True,[HAZARD..],hazardpointers_op_8,emoji_extended,False,Risky Unicode range: emoji_extended; Long fallback: 19 chars; Generic numbered name
NG1642,⡢,ng:concurrency_advanced:hazardpointers_6,[HAZARDPOINTERS6],concurrency_advanced,hazardpointers_6,95.0,B,False,False,True,True,[HAZARD..],hazardpointers_6,safe,False,Long fallback: 17 chars; Generic numbered name
NG1643,❉,ng:concurrency_advanced:hazardpointers_fn_8,[HAZARDPOINTERSFN8],concurrency_advanced,hazardpointers_fn_8,95.0,B,False,False,True,True,[HAZARD..],hazardpointers_fn_8,safe,False,Long fallback: 19 chars; Generic numbered name
NG1644,🐝,ng:concurrency_advanced:hazardpointers_op_11,[HAZARDPOINTERSOP11],concurrency_advanced,hazardpointers_op_11,95.0,A,False,True,True,True,[HAZARD..],hazardpointers_op_11,emoji_extended,False,Risky Unicode range: emoji_extended; Long fallback: 20 chars; Generic numbered name
NG1645,🜝,ng:concurrency_advanced:hazardpointers_8,[HAZARDPOINTERS8],concurrency_advanced,hazardpointers_8,95.0,A,False,True,True,True,[HAZARD..],hazardpointers_8,emoji_extended,False,Risky Unicode range: emoji_extended; Long fallback: 17 chars; Generic numbered name
NG1646,🠜,ng:concurrency_advanced:hazardpointers_9,[HAZARDPOINTERS9],concurrency_advanced,hazardpointers_9,95.0,A,False,True,True,True,[HAZARD..],hazardpointers_9,emoji_extended,False,Risky Unicode range: emoji_extended; Long fallback: 17 chars; Generic numbered name
NG1647,⬀,ng:concurrency_advanced:hazardpointers_fn_9,[HAZARDPOINTERSFN9],concurrency_advanced,hazardpointers_fn_9,95.0,B,False,False,True,True,[HAZARD..],hazardpointers_fn_9,safe,False,Long fallback: 19 chars; Generic numbered name
NG1648,🟨,ng:concurrency_advanced:hazardpointers_11,[HAZARDPOINTERS11],concurrency_advanced,hazardpointers_11,95.0,A,False,True,True,True,[HAZARD..],hazardpointers_11,emoji_extended,False,Risky Unicode range: emoji_extended; Long fallback: 18 chars; Generic numbered name
NG1649,📬,ng:concurrency_advanced:hazardpointers_op_12,[HAZARDPOINTERSOP12],concurrency_advanced,hazardpointers_op_12,95.0,A,False,True,True,True,[HAZARD..],hazardpointers_op_12,emoji_extended,False,Risky Unicode range: emoji_extended; Long fallback: 20 chars; Generic numbered name
NG1650,🖲,ng:concurrency_advanced:hazardpointers_op_13,[HAZARDPOINTERSOP13],concurrency_advanced,hazardpointers_op_13,95.0,A,False,True,True,True,[HAZARD..],hazardpointers_op_13,emoji_extended,False,Risky Unicode range: emoji_extended; Long fallback: 20 chars; Generic numbered name
NG1651,⬒,ng:concurrency_advanced:hazardpointers_fn_10,[HAZARDPOINTERSFN10],concurrency_advanced,hazardpointers_fn_10,95.0,B,False,False,True,True,[HAZARD..],hazardpointers_fn_10,safe,False,Long fallback: 20 chars; Generic numbered name
NG1652,⯥,ng:concurrency_advanced:hazardpointers_op_14,[HAZARDPOINTERSOP14],concurrency_advanced,hazardpointers_op_14,95.0,B,False,False,True,True,[HAZARD..],hazardpointers_op_14,safe,False,Long fallback: 20 chars; Generic numbered name
NG1653,❆,ng:concurrency_advanced:hazardpointers_12,[HAZARDPOINTERS12],concurrency_advanced,hazardpointers_12,95.0,B,False,False,True,True,[HAZARD..],hazardpointers_12,safe,False,Long fallback: 18 chars; Generic numbered name
NG1654,🠻,ng:concurrency_advanced:hazardpointers_13,[HAZARDPOINTERS13],concurrency_advanced,hazardpointers_13,95.0,A,False,True,True,True,[HAZARD..],hazardpointers_13,emoji_extended,False,Risky Unicode range: emoji_extended; Long fallback: 18 chars; Generic numbered name
NG1655,💴,ng:machine_learning:training_proc,[TRAININGPROC],machine_learning,training_proc,95.0,A,False,True,True,False,[TRAINI..],,emoji_extended,False,Risky Unicode range: emoji_extended; Long fallback: 14 chars
NG1656,⤌,ng:machine_learning:prediction_proc,[PREDICTIONPROC],machine_learning,prediction_proc,95.0,C,False,False,True,False,[PREDIC..],,safe,False,Long fallback: 16 chars
NG1657,⮼,ng:machine_learning:training,[TRAINING],machine_learning,training,95.0,OK,False,False,False,False,,,safe,False,
NG1658,⤕,ng:machine_learning:training_op,[TRAININGOP],machine_learning,training_op,95.0,C,False,False,True,False,[TRAINI..],,safe,False,Long fallback: 12 chars
NG1659,⬕,ng:machine_learning:training_proc_1,[TRAININGPROC1],machine_learning,training_proc_1,95.0,B,False,False,True,True,[TRAINI..],training_proc_1,safe,False,Long fallback: 15 chars; Generic numbered name
NG1660,⥃,ng:machine_learning:prediction_meta,[PREDICTIONMETA],machine_learning,prediction_meta,95.0,C,False,False,True,False,[PREDIC..],,safe,False,Long fallback: 16 chars
NG1661,⫺,ng:machine_learning:regression_fn,[REGRESSIONFN],machine_learning,regression_fn,95.0,C,False,False,True,False,[REGRES..],,safe,False,Long fallback: 14 chars
NG1662,🗱,ng:machine_learning:prediction_proc_1,[PREDICTIONPROC1],machine_learning,prediction_proc_1,95.0,A,False,True,True,True,[PREDIC..],prediction_proc_1,emoji_extended,False,Risky Unicode range: emoji_extended; Long fallback: 17 chars; Generic numbered name
NG1663,⮬,ng:machine_learning:classifier_op,[CLASSIFIEROP],machine_learning,classifier_op,95.0,C,False,False,True,False,[CLASSI..],,safe,False,Long fallback: 14 chars
NG1664,🢅,ng:machine_learning:classifier,[CLASSIFIER],machine_learning,classifier,95.0,A,False,True,True,False,[CLASSI..],,emoji_extended,False,Risky Unicode range: emoji_extended; Long fallback: 12 chars
NG1665,🛃,ng:machine_learning:training_ctrl,[TRAININGCTRL],machine_learning,training_ctrl,95.0,A,False,True,True,False,[TRAINI..],,emoji_extended,False,Risky Unicode range: emoji_extended; Long fallback: 14 chars
NG1666,⤩,ng:machine_learning:training_proc_2,[TRAININGPROC2],machine_learning,training_proc_2,95.0,B,False,False,True,True,[TRAINI..],training_proc_2,safe,False,Long fallback: 15 chars; Generic numbered name
NG1667,⪤,ng:machine_learning:prediction_ctrl,[PREDICTIONCTRL],machine_learning,prediction_ctrl,95.0,C,False,False,True,False,[PREDIC..],,safe,False,Long fallback: 16 chars
NG1668,⠼,ng:machine_learning:training_ctrl_1,[TRAININGCTRL1],machine_learning,training_ctrl_1,95.0,B,False,False,True,True,[TRAINI..],training_ctrl_1,safe,False,Long fallback: 15 chars; Generic numbered name
NG1669,🏍,ng:machine_learning:prediction_fn,[PREDICTIONFN],machine_learning,prediction_fn,95.0,A,False,True,True,False,[PREDIC..],,emoji_extended,False,Risky Unicode range: emoji_extended; Long fallback: 14 chars
NG1670,🏳,ng:machine_learning:training_op_1,[TRAININGOP1],machine_learning,training_op_1,95.0,A,False,True,True,True,[TRAINI..],training_op_1,emoji_extended,False,Risky Unicode range: emoji_extended; Long fallback: 13 chars; Generic numbered name
NG1671,⢗,ng:machine_learning:deeplearning_core,[DEEPLEARNINGCORE],machine_learning,deeplearning_core,95.0,C,False,False,True,False,[DEEPLE..],,safe,False,Long fallback: 18 chars
NG1672,⦳,ng:machine_learning:deeplearning,[DEEPLEARNING],machine_learning,deeplearning,95.0,C,False,False,True,False,[DEEPLE..],,safe,False,Long fallback: 14 chars
NG1673,😻,ng:machine_learning:deeplearning_1,[DEEPLEARNING1],machine_learning,deeplearning_1,95.0,A,False,True,True,True,[DEEPLE..],deeplearning_1,emoji_extended,False,Risky Unicode range: emoji_extended; Long fallback: 15 chars; Generic numbered name
NG1674,⧞,ng:machine_learning:deeplearning_sys,[DEEPLEARNINGSYS],machine_learning,deeplearning_sys,95.0,C,False,False,True,False,[DEEPLE..],,safe,False,Long fallback: 17 chars
NG1675,⨘,ng:machine_learning:deeplearning_op,[DEEPLEARNINGOP],machine_learning,deeplearning_op,95.0,C,False,False,True,False,[DEEPLE..],,safe,False,Long fallback: 16 chars
NG1676,🞑,ng:machine_learning:deeplearning_2,[DEEPLEARNING2],machine_learning,deeplearning_2,95.0,A,False,True,True,True,[DEEPLE..],deeplearning_2,emoji_extended,False,Risky Unicode range: emoji_extended; Long fallback: 15 chars; Generic numbered name
NG1677,🚤,ng:machine_learning:deeplearning_ctrl,[DEEPLEARNINGCTRL],machine_learning,deeplearning_ctrl,95.0,A,False,True,True,False,[DEEPLE..],,emoji_extended,False,Risky Unicode range: emoji_extended; Long fallback: 18 chars
NG1678,🧾,ng:machine_learning:deeplearning_meta,[DEEPLEARNINGMETA],machine_learning,deeplearning_meta,95.0,A,False,True,True,False,[DEEPLE..],,emoji_extended,False,Risky Unicode range: emoji_extended; Long fallback: 18 chars
NG1679,😑,ng:machine_learning:deeplearning_sys_1,[DEEPLEARNINGSYS1],machine_learning,deeplearning_sys_1,95.0,A,False,True,True,True,[DEEPLE..],deeplearning_sys_1,emoji_extended,False,Risky Unicode range: emoji_extended; Long fallback: 18 chars; Generic numbered name
NG1680,🟃,ng:machine_learning:deeplearning_ctrl_1,[DEEPLEARNINGCTRL1],machine_learning,deeplearning_ctrl_1,95.0,A,False,True,True,True,[DEEPLE..],deeplearning_ctrl_1,emoji_extended,False,Risky Unicode range: emoji_extended; Long fallback: 19 chars; Generic numbered name
NG1681,✀,ng:machine_learning:deeplearning_sys_2,[DEEPLEARNINGSYS2],machine_learning,deeplearning_sys_2,95.0,B,False,False,True,True,[DEEPLE..],deeplearning_sys_2,safe,False,Long fallback: 18 chars; Generic numbered name
NG1682,⩛,ng:machine_learning:deeplearning_core_1,[DEEPLEARNINGCORE1],machine_learning,deeplearning_core_1,95.0,B,False,False,True,True,[DEEPLE..],deeplearning_core_1,safe,False,Long fallback: 19 chars; Generic numbered name
NG1683,🥇,ng:machine_learning:deeplearning_ctrl_2,[DEEPLEARNINGCTRL2],machine_learning,deeplearning_ctrl_2,95.0,A,False,True,True,True,[DEEPLE..],deeplearning_ctrl_2,emoji_extended,False,Risky Unicode range: emoji_extended; Long fallback: 19 chars; Generic numbered name
NG1684,⪌,ng:machine_learning:deeplearning_op_1,[DEEPLEARNINGOP1],machine_learning,deeplearning_op_1,95.0,B,False,False,True,True,[DEEPLE..],deeplearning_op_1,safe,False,Long fallback: 17 chars; Generic numbered name
NG1685,🚸,ng:machine_learning:deeplearning_meta_1,[DEEPLEARNINGMETA1],machine_learning,deeplearning_meta_1,95.0,A,False,True,True,True,[DEEPLE..],deeplearning_meta_1,emoji_extended,False,Risky Unicode range: emoji_extended; Long fallback: 19 chars; Generic numbered name
NG1686,⤔,ng:machine_learning:deeplearning_proc,[DEEPLEARNINGPROC],machine_learning,deeplearning_proc,95.0,C,False,False,True,False,[DEEPLE..],,safe,False,Long fallback: 18 chars
NG1687,⫼,ng:machine_learning:modelevaluation_sys,[MODELEVALUATIONSYS],machine_learning,modelevaluation_sys,95.0,C,False,False,True,False,[MODELE..],,safe,False,Long fallback: 20 chars
NG1688,😹,ng:machine_learning:modelevaluation,[MODELEVALUATION],machine_learning,modelevaluation,95.0,A,False,True,True,False,[MODELE..],,emoji_extended,False,Risky Unicode range: emoji_extended; Long fallback: 17 chars
NG1689,⭜,ng:machine_learning:modelevaluation_op,[MODELEVALUATIONOP],machine_learning,modelevaluation_op,95.0,C,False,False,True,False,[MODELE..],,safe,False,Long fallback: 19 chars
NG1690,🝢,ng:machine_learning:modelevaluation_fn,[MODELEVALUATIONFN],machine_learning,modelevaluation_fn,95.0,A,False,True,True,False,[MODELE..],,emoji_extended,False,Risky Unicode range: emoji_extended; Long fallback: 19 chars
NG1691,⦥,ng:machine_learning:modelevaluation_1,[MODELEVALUATION1],machine_learning,modelevaluation_1,95.0,B,False,False,True,True,[MODELE..],modelevaluation_1,safe,False,Long fallback: 18 chars; Generic numbered name
NG1692,🠖,ng:machine_learning:modelevaluation_2,[MODELEVALUATION2],machine_learning,modelevaluation_2,95.0,A,False,True,True,True,[MODELE..],modelevaluation_2,emoji_extended,False,Risky Unicode range: emoji_extended; Long fallback: 18 chars; Generic numbered name
NG1693,🤪,ng:machine_learning:modelevaluation_op_1,[MODELEVALUATIONOP1],machine_learning,modelevaluation_op_1,95.0,A,False,True,True,True,[MODELE..],modelevaluation_op_1,emoji_extended,False,Risky Unicode range: emoji_extended; Long fallback: 20 chars; Generic numbered name
NG1694,⩰,ng:machine_learning:modelevaluation_fn_1,[MODELEVALUATIONFN1],machine_learning,modelevaluation_fn_1,95.0,B,False,False,True,True,[MODELE..],modelevaluation_fn_1,safe,False,Long fallback: 20 chars; Generic numbered name
NG1695,⦘,ng:machine_learning:modelevaluation_3,[MODELEVALUATION3],machine_learning,modelevaluation_3,95.0,B,False,False,True,True,[MODELE..],modelevaluation_3,safe,False,Long fallback: 18 chars; Generic numbered name
NG1696,⣶,ng:machine_learning:modelevaluation_fn_2,[MODELEVALUATIONFN2],machine_learning,modelevaluation_fn_2,95.0,B,False,False,True,True,[MODELE..],modelevaluation_fn_2,safe,False,Long fallback: 20 chars; Generic numbered name
NG1697,🝑,ng:machine_learning:modelevaluation_op_2,[MODELEVALUATIONOP2],machine_learning,modelevaluation_op_2,95.0,A,False,True,True,True,[MODELE..],modelevaluation_op_2,emoji_extended,False,Risky Unicode range: emoji_extended; Long fallback: 20 chars; Generic numbered name
NG1698,⥄,ng:machine_learning:modelevaluation_4,[MODELEVALUATION4],machine_learning,modelevaluation_4,95.0,B,False,False,True,True,[MODELE..],modelevaluation_4,safe,False,Long fallback: 18 chars; Generic numbered name
NG1699,🜌,ng:machine_learning:modelevaluation_5,[MODELEVALUATION5],machine_learning,modelevaluation_5,95.0,A,False,True,True,True,[MODELE..],modelevaluation_5,emoji_extended,False,Risky Unicode range: emoji_extended; Long fallback: 18 chars; Generic numbered name
NG1700,🞮,ng:machine_learning:modelevaluation_6,[MODELEVALUATION6],machine_learning,modelevaluation_6,95.0,A,False,True,True,True,[MODELE..],modelevaluation_6,emoji_extended,False,Risky Unicode range: emoji_extended; Long fallback: 18 chars; Generic numbered name
NG1701,❂,ng:machine_learning:modelevaluation_7,[MODELEVALUATION7],machine_learning,modelevaluation_7,95.0,B,False,False,True,True,[MODELE..],modelevaluation_7,safe,False,Long fallback: 18 chars; Generic numbered name
NG1702,🖛,ng:machine_learning:modelevaluation_op_3,[MODELEVALUATIONOP3],machine_learning,modelevaluation_op_3,95.0,A,False,True,True,True,[MODELE..],modelevaluation_op_3,emoji_extended,False,Risky Unicode range: emoji_extended; Long fallback: 20 chars; Generic numbered name
NG1703,🢗,ng:machine_learning:featureengineering,[FEATUREENGINEERING],machine_learning,featureengineering,95.0,A,False,True,True,False,[FEATUR..],,emoji_extended,False,Risky Unicode range: emoji_extended; Long fallback: 20 chars
NG1704,⥴,ng:machine_learning:ensemblemethods_op,[ENSEMBLEMETHODSOP],machine_learning,ensemblemethods_op,95.0,C,False,False,True,False,[ENSEMB..],,safe,False,Long fallback: 19 chars
NG1705,🛞,ng:machine_learning:ensemblemethods_op_1,[ENSEMBLEMETHODSOP1],machine_learning,ensemblemethods_op_1,95.0,A,False,True,True,True,[ENSEMB..],ensemblemethods_op_1,emoji_extended,False,Risky Unicode range: emoji_extended; Long fallback: 20 chars; Generic numbered name
NG1706,⧢,ng:machine_learning:ensemblemethods_fn,[ENSEMBLEMETHODSFN],machine_learning,ensemblemethods_fn,95.0,C,False,False,True,False,[ENSEMB..],,safe,False,Long fallback: 19 chars
NG1707,⯾,ng:machine_learning:ensemblemethods,[ENSEMBLEMETHODS],machine_learning,ensemblemethods,95.0,C,False,False,True,False,[ENSEMB..],,safe,False,Long fallback: 17 chars
NG1708,🍖,ng:machine_learning:ensemblemethods_fn_1,[ENSEMBLEMETHODSFN1],machine_learning,ensemblemethods_fn_1,95.0,A,False,True,True,True,[ENSEMB..],ensemblemethods_fn_1,emoji_extended,False,Risky Unicode range: emoji_extended; Long fallback: 20 chars; Generic numbered name
NG1709,🠚,ng:machine_learning:ensemblemethods_1,[ENSEMBLEMETHODS1],machine_learning,ensemblemethods_1,95.0,A,False,True,True,True,[ENSEMB..],ensemblemethods_1,emoji_extended,False,Risky Unicode range: emoji_extended; Long fallback: 18 chars; Generic numbered name
NG1710,😦,ng:machine_learning:ensemblemethods_fn_2,[ENSEMBLEMETHODSFN2],machine_learning,ensemblemethods_fn_2,95.0,A,False,True,True,True,[ENSEMB..],ensemblemethods_fn_2,emoji_extended,False,Risky Unicode range: emoji_extended; Long fallback: 20 chars; Generic numbered name
NG1711,📮,ng:machine_learning:ensemblemethods_sys,[ENSEMBLEMETHODSSYS],machine_learning,ensemblemethods_sys,95.0,A,False,True,True,False,[ENSEMB..],,emoji_extended,False,Risky Unicode range: emoji_extended; Long fallback: 20 chars
NG1712,⢿,ng:machine_learning:ensemblemethods_2,[ENSEMBLEMETHODS2],machine_learning,ensemblemethods_2,95.0,B,False,False,True,True,[ENSEMB..],ensemblemethods_2,safe,False,Long fallback: 18 chars; Generic numbered name
NG1713,🠶,ng:machine_learning:ensemblemethods_fn_3,[ENSEMBLEMETHODSFN3],machine_learning,ensemblemethods_fn_3,95.0,A,False,True,True,True,[ENSEMB..],ensemblemethods_fn_3,emoji_extended,False,Risky Unicode range: emoji_extended; Long fallback: 20 chars; Generic numbered name
NG1714,⪋,ng:machine_learning:ensemblemethods_fn_4,[ENSEMBLEMETHODSFN4],machine_learning,ensemblemethods_fn_4,95.0,B,False,False,True,True,[ENSEMB..],ensemblemethods_fn_4,safe,False,Long fallback: 20 chars; Generic numbered name
NG1715,🛊,ng:machine_learning:ensemblemethods_op_2,[ENSEMBLEMETHODSOP2],machine_learning,ensemblemethods_op_2,95.0,A,False,True,True,True,[ENSEMB..],ensemblemethods_op_2,emoji_extended,False,Risky Unicode range: emoji_extended; Long fallback: 20 chars; Generic numbered name
NG1716,✙,ng:machine_learning:ensemblemethods_fn_5,[ENSEMBLEMETHODSFN5],machine_learning,ensemblemethods_fn_5,95.0,B,False,False,True,True,[ENSEMB..],ensemblemethods_fn_5,safe,False,Long fallback: 20 chars; Generic numbered name
NG1717,⤱,ng:machine_learning:ensemblemethods_op_3,[ENSEMBLEMETHODSOP3],machine_learning,ensemblemethods_op_3,95.0,B,False,False,True,True,[ENSEMB..],ensemblemethods_op_3,safe,False,Long fallback: 20 chars; Generic numbered name
NG1718,🏥,ng:machine_learning:ensemblemethods_fn_6,[ENSEMBLEMETHODSFN6],machine_learning,ensemblemethods_fn_6,95.0,A,False,True,True,True,[ENSEMB..],ensemblemethods_fn_6,emoji_extended,False,Risky Unicode range: emoji_extended; Long fallback: 20 chars; Generic numbered name
NG1719,➽,ng:machine_learning:ensemblemethods_3,[ENSEMBLEMETHODS3],machine_learning,ensemblemethods_3,95.0,B,False,False,True,True,[ENSEMB..],ensemblemethods_3,safe,False,Long fallback: 18 chars; Generic numbered name
NG1720,🜎,ng:machine_learning:ensemblemethods_fn_7,[ENSEMBLEMETHODSFN7],machine_learning,ensemblemethods_fn_7,95.0,A,False,True,True,True,[ENSEMB..],ensemblemethods_fn_7,emoji_extended,False,Risky Unicode range: emoji_extended; Long fallback: 20 chars; Generic numbered name
NG1721,⪄,ng:machine_learning:ensemblemethods_op_4,[ENSEMBLEMETHODSOP4],machine_learning,ensemblemethods_op_4,95.0,B,False,False,True,True,[ENSEMB..],ensemblemethods_op_4,safe,False,Long fallback: 20 chars; Generic numbered name
NG1722,⮳,ng:machine_learning:ensemblemethods_4,[ENSEMBLEMETHODS4],machine_learning,ensemblemethods_4,95.0,B,False,False,True,True,[ENSEMB..],ensemblemethods_4,safe,False,Long fallback: 18 chars; Generic numbered name
NG1723,🝌,ng:machine_learning:ensemblemethods_5,[ENSEMBLEMETHODS5],machine_learning,ensemblemethods_5,95.0,A,False,True,True,True,[ENSEMB..],ensemblemethods_5,emoji_extended,False,Risky Unicode range: emoji_extended; Long fallback: 18 chars; Generic numbered name
NG1724,⯕,ng:machine_learning:ensemblemethods_fn_8,[ENSEMBLEMETHODSFN8],machine_learning,ensemblemethods_fn_8,95.0,B,False,False,True,True,[ENSEMB..],ensemblemethods_fn_8,safe,False,Long fallback: 20 chars; Generic numbered name
NG1725,🝛,ng:machine_learning:ensemblemethods_op_5,[ENSEMBLEMETHODSOP5],machine_learning,ensemblemethods_op_5,95.0,A,False,True,True,True,[ENSEMB..],ensemblemethods_op_5,emoji_extended,False,Risky Unicode range: emoji_extended; Long fallback: 20 chars; Generic numbered name
NG1726,🝖,ng:machine_learning:ensemblemethods_op_6,[ENSEMBLEMETHODSOP6],machine_learning,ensemblemethods_op_6,95.0,A,False,True,True,True,[ENSEMB..],ensemblemethods_op_6,emoji_extended,False,Risky Unicode range: emoji_extended; Long fallback: 20 chars; Generic numbered name
NG1727,⤏,ng:machine_learning:ensemblemethods_op_7,[ENSEMBLEMETHODSOP7],machine_learning,ensemblemethods_op_7,95.0,B,False,False,True,True,[ENSEMB..],ensemblemethods_op_7,safe,False,Long fallback: 20 chars; Generic numbered name
NG1728,⫮,ng:machine_learning:ensemblemethods_op_8,[ENSEMBLEMETHODSOP8],machine_learning,ensemblemethods_op_8,95.0,B,False,False,True,True,[ENSEMB..],ensemblemethods_op_8,safe,False,Long fallback: 20 chars; Generic numbered name
NG1729,🤚,ng:machine_learning:ensemblemethods_op_9,[ENSEMBLEMETHODSOP9],machine_learning,ensemblemethods_op_9,95.0,A,False,True,True,True,[ENSEMB..],ensemblemethods_op_9,emoji_extended,False,Risky Unicode range: emoji_extended; Long fallback: 20 chars; Generic numbered name
NG1730,⠧,ng:machine_learning:ensemblemethods_6,[ENSEMBLEMETHODS6],machine_learning,ensemblemethods_6,95.0,B,False,False,True,True,[ENSEMB..],ensemblemethods_6,safe,False,Long fallback: 18 chars; Generic numbered name
NG1731,🔻,ng:machine_learning:ensemblemethods_7,[ENSEMBLEMETHODS7],machine_learning,ensemblemethods_7,95.0,A,False,True,True,True,[ENSEMB..],ensemblemethods_7,emoji_extended,False,Risky Unicode range: emoji_extended; Long fallback: 18 chars; Generic numbered name
NG1732,🍠,ng:machine_learning:ensemblemethods_fn_9,[ENSEMBLEMETHODSFN9],machine_learning,ensemblemethods_fn_9,95.0,A,False,True,True,True,[ENSEMB..],ensemblemethods_fn_9,emoji_extended,False,Risky Unicode range: emoji_extended; Long fallback: 20 chars; Generic numbered name
NG1733,🤌,ng:machine_learning:ensemblemethods_8,[ENSEMBLEMETHODS8],machine_learning,ensemblemethods_8,95.0,A,False,True,True,True,[ENSEMB..],ensemblemethods_8,emoji_extended,False,Risky Unicode range: emoji_extended; Long fallback: 18 chars; Generic numbered name
NG1734,🝤,ng:machine_learning:ensemblemethods_9,[ENSEMBLEMETHODS9],machine_learning,ensemblemethods_9,95.0,A,False,True,True,True,[ENSEMB..],ensemblemethods_9,emoji_extended,False,Risky Unicode range: emoji_extended; Long fallback: 18 chars; Generic numbered name
NG1735,🠧,ng:machine_learning:ensemblemethods_10,[ENSEMBLEMETHODS10],machine_learning,ensemblemethods_10,95.0,A,False,True,True,True,[ENSEMB..],ensemblemethods_10,emoji_extended,False,Risky Unicode range: emoji_extended; Long fallback: 19 chars; Generic numbered name
NG1736,🎵,ng:machine_learning:ensemblemethods_11,[ENSEMBLEMETHODS11],machine_learning,ensemblemethods_11,95.0,A,False,True,True,True,[ENSEMB..],ensemblemethods_11,emoji_extended,False,Risky Unicode range: emoji_extended; Long fallback: 19 chars; Generic numbered name
NG1737,⧑,ng:machine_learning:ensemblemethods_12,[ENSEMBLEMETHODS12],machine_learning,ensemblemethods_12,95.0,B,False,False,True,True,[ENSEMB..],ensemblemethods_12,safe,False,Long fallback: 19 chars; Generic numbered name
NG1738,🜖,ng:machine_learning:ensemblemethods_13,[ENSEMBLEMETHODS13],machine_learning,ensemblemethods_13,95.0,A,False,True,True,True,[ENSEMB..],ensemblemethods_13,emoji_extended,False,Risky Unicode range: emoji_extended; Long fallback: 19 chars; Generic numbered name
NG1739,⛩,ng:machine_learning:ensemblemethods_14,[ENSEMBLEMETHODS14],machine_learning,ensemblemethods_14,95.0,A,False,True,True,True,[ENSEMB..],ensemblemethods_14,miscellaneous_symbols,False,Risky Unicode range: miscellaneous_symbols; Long fallback: 19 chars; Generic numbered name
NG1740,⬞,ng:machine_learning:ensemblemethods_15,[ENSEMBLEMETHODS15],machine_learning,ensemblemethods_15,95.0,B,False,False,True,True,[ENSEMB..],ensemblemethods_15,safe,False,Long fallback: 19 chars; Generic numbered name
NG1741,🐊,ng:machine_learning:ensemblemethods_16,[ENSEMBLEMETHODS16],machine_learning,ensemblemethods_16,95.0,A,False,True,True,True,[ENSEMB..],ensemblemethods_16,emoji_extended,False,Risky Unicode range: emoji_extended; Long fallback: 19 chars; Generic numbered name
NG1742,🤀,ng:machine_learning:ensemblemethods_17,[ENSEMBLEMETHODS17],machine_learning,ensemblemethods_17,95.0,A,False,True,True,True,[ENSEMB..],ensemblemethods_17,emoji_extended,False,Risky Unicode range: emoji_extended; Long fallback: 19 chars; Generic numbered name
NG1743,🟑,ng:machine_learning:ensemblemethods_18,[ENSEMBLEMETHODS18],machine_learning,ensemblemethods_18,95.0,A,False,True,True,True,[ENSEMB..],ensemblemethods_18,emoji_extended,False,Risky Unicode range: emoji_extended; Long fallback: 19 chars; Generic numbered name
NG1744,🤺,ng:machine_learning:ensemblemethods_19,[ENSEMBLEMETHODS19],machine_learning,ensemblemethods_19,95.0,A,False,True,True,True,[ENSEMB..],ensemblemethods_19,emoji_extended,False,Risky Unicode range: emoji_extended; Long fallback: 19 chars; Generic numbered name
NG1745,🚉,ng:machine_learning:ensemblemethods_20,[ENSEMBLEMETHODS20],machine_learning,ensemblemethods_20,95.0,A,False,True,True,True,[ENSEMB..],ensemblemethods_20,emoji_extended,False,Risky Unicode range: emoji_extended; Long fallback: 19 chars; Generic numbered name
NG1746,⥵,ng:machine_learning:ensemblemethods_21,[ENSEMBLEMETHODS21],machine_learning,ensemblemethods_21,95.0,B,False,False,True,True,[ENSEMB..],ensemblemethods_21,safe,False,Long fallback: 19 chars; Generic numbered name
NG1747,🜾,ng:machine_learning:ensemblemethods_22,[ENSEMBLEMETHODS22],machine_learning,ensemblemethods_22,95.0,A,False,True,True,True,[ENSEMB..],ensemblemethods_22,emoji_extended,False,Risky Unicode range: emoji_extended; Long fallback: 19 chars; Generic numbered name
NG1748,🞟,ng:machine_learning:ensemblemethods_23,[ENSEMBLEMETHODS23],machine_learning,ensemblemethods_23,95.0,A,False,True,True,True,[ENSEMB..],ensemblemethods_23,emoji_extended,False,Risky Unicode range: emoji_extended; Long fallback: 19 chars; Generic numbered name
NG1749,⩝,ng:machine_learning:ensemblemethods_24,[ENSEMBLEMETHODS24],machine_learning,ensemblemethods_24,95.0,B,False,False,True,True,[ENSEMB..],ensemblemethods_24,safe,False,Long fallback: 19 chars; Generic numbered name
NG1750,🛎,ng:machine_learning:ensemblemethods_25,[ENSEMBLEMETHODS25],machine_learning,ensemblemethods_25,95.0,A,False,True,True,True,[ENSEMB..],ensemblemethods_25,emoji_extended,False,Risky Unicode range: emoji_extended; Long fallback: 19 chars; Generic numbered name
NG1751,😖,ng:machine_learning:ensemblemethods_26,[ENSEMBLEMETHODS26],machine_learning,ensemblemethods_26,95.0,A,False,True,True,True,[ENSEMB..],ensemblemethods_26,emoji_extended,False,Risky Unicode range: emoji_extended; Long fallback: 19 chars; Generic numbered name
NG1752,🐚,ng:machine_learning:ensemblemethods_27,[ENSEMBLEMETHODS27],machine_learning,ensemblemethods_27,95.0,A,False,True,True,True,[ENSEMB..],ensemblemethods_27,emoji_extended,False,Risky Unicode range: emoji_extended; Long fallback: 19 chars; Generic numbered name
NG1753,⛮,ng:machine_learning:ensemblemethods_28,[ENSEMBLEMETHODS28],machine_learning,ensemblemethods_28,95.0,A,False,True,True,True,[ENSEMB..],ensemblemethods_28,miscellaneous_symbols,False,Risky Unicode range: miscellaneous_symbols; Long fallback: 19 chars; Generic numbered name
NG1754,💠,ng:machine_learning:ensemblemethods_29,[ENSEMBLEMETHODS29],machine_learning,ensemblemethods_29,95.0,A,False,True,True,True,[ENSEMB..],ensemblemethods_29,emoji_extended,False,Risky Unicode range: emoji_extended; Long fallback: 19 chars; Generic numbered name
NG1755,🖅,ng:machine_learning:ensemblemethods_30,[ENSEMBLEMETHODS30],machine_learning,ensemblemethods_30,95.0,A,False,True,True,True,[ENSEMB..],ensemblemethods_30,emoji_extended,False,Risky Unicode range: emoji_extended; Long fallback: 19 chars; Generic numbered name
NG1756,🥚,ng:machine_learning:ensemblemethods_31,[ENSEMBLEMETHODS31],machine_learning,ensemblemethods_31,95.0,A,False,True,True,True,[ENSEMB..],ensemblemethods_31,emoji_extended,False,Risky Unicode range: emoji_extended; Long fallback: 19 chars; Generic numbered name
NG1757,😂,ng:machine_learning:ensemblemethods_32,[ENSEMBLEMETHODS32],machine_learning,ensemblemethods_32,95.0,A,False,True,True,True,[ENSEMB..],ensemblemethods_32,emoji_extended,False,Risky Unicode range: emoji_extended; Long fallback: 19 chars; Generic numbered name
NG1758,🌯,ng:machine_learning:ensemblemethods_33,[ENSEMBLEMETHODS33],machine_learning,ensemblemethods_33,95.0,A,False,True,True,True,[ENSEMB..],ensemblemethods_33,emoji_extended,False,Risky Unicode range: emoji_extended; Long fallback: 19 chars; Generic numbered name
NG1759,🡦,ng:machine_learning:ensemblemethods_34,[ENSEMBLEMETHODS34],machine_learning,ensemblemethods_34,95.0,A,False,True,True,True,[ENSEMB..],ensemblemethods_34,emoji_extended,False,Risky Unicode range: emoji_extended; Long fallback: 19 chars; Generic numbered name
NG1760,🞽,ng:machine_learning:ensemblemethods_35,[ENSEMBLEMETHODS35],machine_learning,ensemblemethods_35,95.0,A,False,True,True,True,[ENSEMB..],ensemblemethods_35,emoji_extended,False,Risky Unicode range: emoji_extended; Long fallback: 19 chars; Generic numbered name
NG1761,🢟,ng:machine_learning:ensemblemethods_36,[ENSEMBLEMETHODS36],machine_learning,ensemblemethods_36,95.0,A,False,True,True,True,[ENSEMB..],ensemblemethods_36,emoji_extended,False,Risky Unicode range: emoji_extended; Long fallback: 19 chars; Generic numbered name
NG1762,⤠,ng:machine_learning:ensemblemethods_37,[ENSEMBLEMETHODS37],machine_learning,ensemblemethods_37,95.0,B,False,False,True,True,[ENSEMB..],ensemblemethods_37,safe,False,Long fallback: 19 chars; Generic numbered name
NG1763,🢜,ng:machine_learning:ensemblemethods_38,[ENSEMBLEMETHODS38],machine_learning,ensemblemethods_38,95.0,A,False,True,True,True,[ENSEMB..],ensemblemethods_38,emoji_extended,False,Risky Unicode range: emoji_extended; Long fallback: 19 chars; Generic numbered name
NG1764,⮪,ng:machine_learning:ensemblemethods_39,[ENSEMBLEMETHODS39],machine_learning,ensemblemethods_39,95.0,B,False,False,True,True,[ENSEMB..],ensemblemethods_39,safe,False,Long fallback: 19 chars; Generic numbered name
NG1765,🧻,ng:machine_learning:ensemblemethods_40,[ENSEMBLEMETHODS40],machine_learning,ensemblemethods_40,95.0,A,False,True,True,True,[ENSEMB..],ensemblemethods_40,emoji_extended,False,Risky Unicode range: emoji_extended; Long fallback: 19 chars; Generic numbered name
NG1766,🦶,ng:machine_learning:ensemblemethods_41,[ENSEMBLEMETHODS41],machine_learning,ensemblemethods_41,95.0,A,False,True,True,True,[ENSEMB..],ensemblemethods_41,emoji_extended,False,Risky Unicode range: emoji_extended; Long fallback: 19 chars; Generic numbered name
NG1767,⭰,ng:machine_learning:ensemblemethods_42,[ENSEMBLEMETHODS42],machine_learning,ensemblemethods_42,95.0,B,False,False,True,True,[ENSEMB..],ensemblemethods_42,safe,False,Long fallback: 19 chars; Generic numbered name
NG1768,⥁,ng:machine_learning:ensemblemethods_43,[ENSEMBLEMETHODS43],machine_learning,ensemblemethods_43,95.0,B,False,False,True,True,[ENSEMB..],ensemblemethods_43,safe,False,Long fallback: 19 chars; Generic numbered name
NG1769,⩭,ng:machine_learning:ensemblemethods_44,[ENSEMBLEMETHODS44],machine_learning,ensemblemethods_44,95.0,B,False,False,True,True,[ENSEMB..],ensemblemethods_44,safe,False,Long fallback: 19 chars; Generic numbered name
NG1770,⦻,ng:machine_learning:ensemblemethods_45,[ENSEMBLEMETHODS45],machine_learning,ensemblemethods_45,95.0,B,False,False,True,True,[ENSEMB..],ensemblemethods_45,safe,False,Long fallback: 19 chars; Generic numbered name
NG1771,⨓,ng:machine_learning:ensemblemethods_46,[ENSEMBLEMETHODS46],machine_learning,ensemblemethods_46,95.0,B,False,False,True,True,[ENSEMB..],ensemblemethods_46,safe,False,Long fallback: 19 chars; Generic numbered name
NG1772,🧷,ng:machine_learning:ensemblemethods_47,[ENSEMBLEMETHODS47],machine_learning,ensemblemethods_47,95.0,A,False,True,True,True,[ENSEMB..],ensemblemethods_47,emoji_extended,False,Risky Unicode range: emoji_extended; Long fallback: 19 chars; Generic numbered name
NG1773,⭲,ng:machine_learning:ensemblemethods_48,[ENSEMBLEMETHODS48],machine_learning,ensemblemethods_48,95.0,B,False,False,True,True,[ENSEMB..],ensemblemethods_48,safe,False,Long fallback: 19 chars; Generic numbered name
NG1774,🏐,ng:machine_learning:ensemblemethods_49,[ENSEMBLEMETHODS49],machine_learning,ensemblemethods_49,95.0,A,False,True,True,True,[ENSEMB..],ensemblemethods_49,emoji_extended,False,Risky Unicode range: emoji_extended; Long fallback: 19 chars; Generic numbered name
NG1775,✮,ng:machine_learning:ensemblemethods_50,[ENSEMBLEMETHODS50],machine_learning,ensemblemethods_50,95.0,B,False,False,True,True,[ENSEMB..],ensemblemethods_50,safe,False,Long fallback: 19 chars; Generic numbered name
NG1776,⬁,ng:machine_learning:ensemblemethods_51,[ENSEMBLEMETHODS51],machine_learning,ensemblemethods_51,95.0,B,False,False,True,True,[ENSEMB..],ensemblemethods_51,safe,False,Long fallback: 19 chars; Generic numbered name
NG1777,⥽,ng:machine_learning:ensemblemethods_52,[ENSEMBLEMETHODS52],machine_learning,ensemblemethods_52,95.0,B,False,False,True,True,[ENSEMB..],ensemblemethods_52,safe,False,Long fallback: 19 chars; Generic numbered name
NG1778,⭽,ng:mathematical_structures:ring_op,[RINGOP],mathematical_structures,ring_op,95.0,OK,False,False,False,False,,,safe,False,
NG1779,⢂,ng:mathematical_structures:ring_op_1,[RINGOP1],mathematical_structures,ring_op_1,95.0,C,False,False,False,True,,ring_op_1,safe,False,Generic numbered name
NG1780,🢐,ng:mathematical_structures:field_meta,[FIELDMETA],mathematical_structures,field_meta,95.0,A,False,True,True,False,[FIELDM..],,emoji_extended,False,Risky Unicode range: emoji_extended; Long fallback: 11 chars
NG1781,😽,ng:mathematical_structures:algebra_proc,[ALGEBRAPROC],mathematical_structures,algebra_proc,95.0,A,False,True,True,False,[ALGEBR..],,emoji_extended,False,Risky Unicode range: emoji_extended; Long fallback: 13 chars
NG1782,⢼,ng:mathematical_structures:algebra,[ALGEBRA],mathematical_structures,algebra,95.0,OK,False,False,False,False,,,safe,False,
NG1783,❩,ng:mathematical_structures:ring_meta,[RINGMETA],mathematical_structures,ring_meta,95.0,OK,False,False,False,False,,,safe,False,
NG1784,🞉,ng:mathematical_structures:ring,[RING],mathematical_structures,ring,95.0,B,False,True,False,False,,,emoji_extended,False,Risky Unicode range: emoji_extended
NG1785,⩂,ng:mathematical_structures:algebra_1,[ALGEBRA1],mathematical_structures,algebra_1,95.0,C,False,False,False,True,,algebra_1,safe,False,Generic numbered name
NG1786,👈,ng:mathematical_structures:group_ctrl,[GROUPCTRL],mathematical_structures,group_ctrl,95.0,A,False,True,True,False,[GROUPC..],,emoji_extended,False,Risky Unicode range: emoji_extended; Long fallback: 11 chars
NG1787,🜷,ng:mathematical_structures:group_ctrl_1,[GROUPCTRL1],mathematical_structures,group_ctrl_1,95.0,A,False,True,True,True,[GROUPC..],group_ctrl_1,emoji_extended,False,Risky Unicode range: emoji_extended; Long fallback: 12 chars; Generic numbered name
NG1788,🧈,ng:mathematical_structures:topologicalspaces,[TOPOLOGICALSPACES],mathematical_structures,topologicalspaces,95.0,A,False,True,True,False,[TOPOLO..],,emoji_extended,False,Risky Unicode range: emoji_extended; Long fallback: 19 chars
NG1789,⩩,ng:mathematical_structures:topologicalspaces_1,[TOPOLOGICALSPACES1],mathematical_structures,topologicalspaces_1,95.0,B,False,False,True,True,[TOPOLO..],topologicalspaces_1,safe,False,Long fallback: 20 chars; Generic numbered name
NG1790,⧼,ng:mathematical_structures:topologicalspaces_2,[TOPOLOGICALSPACES2],mathematical_structures,topologicalspaces_2,95.0,B,False,False,True,True,[TOPOLO..],topologicalspaces_2,safe,False,Long fallback: 20 chars; Generic numbered name
NG1791,⢬,ng:mathematical_structures:topologicalspaces_3,[TOPOLOGICALSPACES3],mathematical_structures,topologicalspaces_3,95.0,B,False,False,True,True,[TOPOLO..],topologicalspaces_3,safe,False,Long fallback: 20 chars; Generic numbered name
NG1792,⧕,ng:mathematical_structures:topologicalspaces_4,[TOPOLOGICALSPACES4],mathematical_structures,topologicalspaces_4,95.0,B,False,False,True,True,[TOPOLO..],topologicalspaces_4,safe,False,Long fallback: 20 chars; Generic numbered name
NG1793,⮔,ng:mathematical_structures:measuretheory_sys,[MEASURETHEORYSYS],mathematical_structures,measuretheory_sys,95.0,C,False,False,True,False,[MEASUR..],,safe,False,Long fallback: 18 chars
NG1794,🠩,ng:mathematical_structures:measuretheory_fn,[MEASURETHEORYFN],mathematical_structures,measuretheory_fn,95.0,A,False,True,True,False,[MEASUR..],,emoji_extended,False,Risky Unicode range: emoji_extended; Long fallback: 17 chars
NG1795,🞨,ng:mathematical_structures:measuretheory_op,[MEASURETHEORYOP],mathematical_structures,measuretheory_op,95.0,A,False,True,True,False,[MEASUR..],,emoji_extended,False,Risky Unicode range: emoji_extended; Long fallback: 17 chars
NG1796,🐦,ng:mathematical_structures:measuretheory_op_1,[MEASURETHEORYOP1],mathematical_structures,measuretheory_op_1,95.0,A,False,True,True,True,[MEASUR..],measuretheory_op_1,emoji_extended,False,Risky Unicode range: emoji_extended; Long fallback: 18 chars; Generic numbered name
NG1797,🡩,ng:mathematical_structures:measuretheory_meta,[MEASURETHEORYMETA],mathematical_structures,measuretheory_meta,95.0,A,False,True,True,False,[MEASUR..],,emoji_extended,False,Risky Unicode range: emoji_extended; Long fallback: 19 chars
NG1798,🡸,ng:mathematical_structures:measuretheory_proc,[MEASURETHEORYPROC],mathematical_structures,measuretheory_proc,95.0,A,False,True,True,False,[MEASUR..],,emoji_extended,False,Risky Unicode range: emoji_extended; Long fallback: 19 chars
NG1799,😥,ng:mathematical_structures:measuretheory,[MEASURETHEORY],mathematical_structures,measuretheory,95.0,A,False,True,True,False,[MEASUR..],,emoji_extended,False,Risky Unicode range: emoji_extended; Long fallback: 15 chars
NG1800,😫,ng:mathematical_structures:measuretheory_fn_1,[MEASURETHEORYFN1],mathematical_structures,measuretheory_fn_1,95.0,A,False,True,True,True,[MEASUR..],measuretheory_fn_1,emoji_extended,False,Risky Unicode range: emoji_extended; Long fallback: 18 chars; Generic numbered name
NG1801,❮,ng:mathematical_structures:measuretheory_proc_1,[MEASURETHEORYPROC1],mathematical_structures,measuretheory_proc_1,95.0,B,False,False,True,True,[MEASUR..],measuretheory_proc_1,safe,False,Long fallback: 20 chars; Generic numbered name
NG1802,✣,ng:mathematical_structures:measuretheory_ctrl,[MEASURETHEORYCTRL],mathematical_structures,measuretheory_ctrl,95.0,C,False,False,True,False,[MEASUR..],,safe,False,Long fallback: 19 chars
NG1803,🠥,ng:mathematical_structures:numbertheory_meta,[NUMBERTHEORYMETA],mathematical_structures,numbertheory_meta,95.0,A,False,True,True,False,[NUMBER..],,emoji_extended,False,Risky Unicode range: emoji_extended; Long fallback: 18 chars
NG1804,➠,ng:mathematical_structures:numbertheory_ctrl,[NUMBERTHEORYCTRL],mathematical_structures,numbertheory_ctrl,95.0,C,False,False,True,False,[NUMBER..],,safe,False,Long fallback: 18 chars
NG1805,⣁,ng:mathematical_structures:numbertheory_ctrl_1,[NUMBERTHEORYCTRL1],mathematical_structures,numbertheory_ctrl_1,95.0,B,False,False,True,True,[NUMBER..],numbertheory_ctrl_1,safe,False,Long fallback: 19 chars; Generic numbered name
NG1806,⤚,ng:mathematical_structures:numbertheory,[NUMBERTHEORY],mathematical_structures,numbertheory,95.0,C,False,False,True,False,[NUMBER..],,safe,False,Long fallback: 14 chars
NG1807,⤆,ng:mathematical_structures:numbertheory_op,[NUMBERTHEORYOP],mathematical_structures,numbertheory_op,95.0,C,False,False,True,False,[NUMBER..],,safe,False,Long fallback: 16 chars
NG1808,⛌,ng:mathematical_structures:numbertheory_proc,[NUMBERTHEORYPROC],mathematical_structures,numbertheory_proc,95.0,A,False,True,True,False,[NUMBER..],,miscellaneous_symbols,False,Risky Unicode range: miscellaneous_symbols; Long fallback: 18 chars
NG1809,🚲,ng:mathematical_structures:numbertheory_meta_1,[NUMBERTHEORYMETA1],mathematical_structures,numbertheory_meta_1,95.0,A,False,True,True,True,[NUMBER..],numbertheory_meta_1,emoji_extended,False,Risky Unicode range: emoji_extended; Long fallback: 19 chars; Generic numbered name
NG1810,🠉,ng:mathematical_structures:numbertheory_core,[NUMBERTHEORYCORE],mathematical_structures,numbertheory_core,95.0,A,False,True,True,False,[NUMBER..],,emoji_extended,False,Risky Unicode range: emoji_extended; Long fallback: 18 chars
NG1811,❰,ng:mathematical_structures:numbertheory_proc_1,[NUMBERTHEORYPROC1],mathematical_structures,numbertheory_proc_1,95.0,B,False,False,True,True,[NUMBER..],numbertheory_proc_1,safe,False,Long fallback: 19 chars; Generic numbered name
NG1812,⩐,ng:mathematical_structures:numbertheory_proc_2,[NUMBERTHEORYPROC2],mathematical_structures,numbertheory_proc_2,95.0,B,False,False,True,True,[NUMBER..],numbertheory_proc_2,safe,False,Long fallback: 19 chars; Generic numbered name
NG1813,🚷,ng:mathematical_structures:combinatorics,[COMBINATORICS],mathematical_structures,combinatorics,95.0,A,False,True,True,False,[COMBIN..],,emoji_extended,False,Risky Unicode range: emoji_extended; Long fallback: 15 chars
NG1814,⯤,ng:mathematical_structures:combinatorics_meta,[COMBINATORICSMETA],mathematical_structures,combinatorics_meta,95.0,C,False,False,True,False,[COMBIN..],,safe,False,Long fallback: 19 chars
NG1815,⩦,ng:mathematical_structures:combinatorics_sys,[COMBINATORICSSYS],mathematical_structures,combinatorics_sys,95.0,C,False,False,True,False,[COMBIN..],,safe,False,Long fallback: 18 chars
NG1816,⭿,ng:mathematical_structures:combinatorics_op,[COMBINATORICSOP],mathematical_structures,combinatorics_op,95.0,C,False,False,True,False,[COMBIN..],,safe,False,Long fallback: 17 chars
NG1817,⨑,ng:mathematical_structures:combinatorics_sys_1,[COMBINATORICSSYS1],mathematical_structures,combinatorics_sys_1,95.0,B,False,False,True,True,[COMBIN..],combinatorics_sys_1,safe,False,Long fallback: 19 chars; Generic numbered name
NG1818,👩,ng:mathematical_structures:combinatorics_proc,[COMBINATORICSPROC],mathematical_structures,combinatorics_proc,95.0,A,False,True,True,False,[COMBIN..],,emoji_extended,False,Risky Unicode range: emoji_extended; Long fallback: 19 chars
NG1819,⩏,ng:mathematical_structures:combinatorics_fn,[COMBINATORICSFN],mathematical_structures,combinatorics_fn,95.0,C,False,False,True,False,[COMBIN..],,safe,False,Long fallback: 17 chars
NG1820,🞤,ng:mathematical_structures:combinatorics_core,[COMBINATORICSCORE],mathematical_structures,combinatorics_core,95.0,A,False,True,True,False,[COMBIN..],,emoji_extended,False,Risky Unicode range: emoji_extended; Long fallback: 19 chars
NG1821,⭅,ng:mathematical_structures:combinatorics_proc_1,[COMBINATORICSPROC1],mathematical_structures,combinatorics_proc_1,95.0,B,False,False,True,True,[COMBIN..],combinatorics_proc_1,safe,False,Long fallback: 20 chars; Generic numbered name
NG1822,⤇,ng:mathematical_structures:combinatorics_op_1,[COMBINATORICSOP1],mathematical_structures,combinatorics_op_1,95.0,B,False,False,True,True,[COMBIN..],combinatorics_op_1,safe,False,Long fallback: 18 chars; Generic numbered name
NG1823,🧙,ng:mathematical_structures:combinatorics_core_1,[COMBINATORICSCORE1],mathematical_structures,combinatorics_core_1,95.0,A,False,True,True,True,[COMBIN..],combinatorics_core_1,emoji_extended,False,Risky Unicode range: emoji_extended; Long fallback: 20 chars; Generic numbered name
NG1824,🛴,ng:mathematical_structures:combinatorics_op_2,[COMBINATORICSOP2],mathematical_structures,combinatorics_op_2,95.0,A,False,True,True,True,[COMBIN..],combinatorics_op_2,emoji_extended,False,Risky Unicode range: emoji_extended; Long fallback: 18 chars; Generic numbered name
NG1825,🡤,ng:mathematical_structures:combinatorics_core_2,[COMBINATORICSCORE2],mathematical_structures,combinatorics_core_2,95.0,A,False,True,True,True,[COMBIN..],combinatorics_core_2,emoji_extended,False,Risky Unicode range: emoji_extended; Long fallback: 20 chars; Generic numbered name
NG1826,🗶,ng:mathematical_structures:combinatorics_meta_1,[COMBINATORICSMETA1],mathematical_structures,combinatorics_meta_1,95.0,A,False,True,True,True,[COMBIN..],combinatorics_meta_1,emoji_extended,False,Risky Unicode range: emoji_extended; Long fallback: 20 chars; Generic numbered name
NG1827,⠲,ng:mathematical_structures:combinatorics_core_3,[COMBINATORICSCORE3],mathematical_structures,combinatorics_core_3,95.0,B,False,False,True,True,[COMBIN..],combinatorics_core_3,safe,False,Long fallback: 20 chars; Generic numbered name
NG1828,🟗,ng:mathematical_structures:combinatorics_ctrl,[COMBINATORICSCTRL],mathematical_structures,combinatorics_ctrl,95.0,A,False,True,True,False,[COMBIN..],,emoji_extended,False,Risky Unicode range: emoji_extended; Long fallback: 19 chars
NG1829,🥪,ng:mathematical_structures:combinatorics_fn_1,[COMBINATORICSFN1],mathematical_structures,combinatorics_fn_1,95.0,A,False,True,True,True,[COMBIN..],combinatorics_fn_1,emoji_extended,False,Risky Unicode range: emoji_extended; Long fallback: 18 chars; Generic numbered name
NG1830,⣕,ng:mathematical_structures:combinatorics_core_4,[COMBINATORICSCORE4],mathematical_structures,combinatorics_core_4,95.0,B,False,False,True,True,[COMBIN..],combinatorics_core_4,safe,False,Long fallback: 20 chars; Generic numbered name
NG1831,⥲,ng:mathematical_structures:combinatorics_meta_2,[COMBINATORICSMETA2],mathematical_structures,combinatorics_meta_2,95.0,B,False,False,True,True,[COMBIN..],combinatorics_meta_2,safe,False,Long fallback: 20 chars; Generic numbered name
NG1832,⥉,ng:mathematical_structures:combinatorics_ctrl_1,[COMBINATORICSCTRL1],mathematical_structures,combinatorics_ctrl_1,95.0,B,False,False,True,True,[COMBIN..],combinatorics_ctrl_1,safe,False,Long fallback: 20 chars; Generic numbered name
NG1833,🚴,ng:mathematical_structures:combinatorics_fn_2,[COMBINATORICSFN2],mathematical_structures,combinatorics_fn_2,95.0,A,False,True,True,True,[COMBIN..],combinatorics_fn_2,emoji_extended,False,Risky Unicode range: emoji_extended; Long fallback: 18 chars; Generic numbered name
NG1834,⮌,ng:mathematical_structures:combinatorics_meta_3,[COMBINATORICSMETA3],mathematical_structures,combinatorics_meta_3,95.0,B,False,False,True,True,[COMBIN..],combinatorics_meta_3,safe,False,Long fallback: 20 chars; Generic numbered name
NG1835,🝿,ng:mathematical_structures:combinatorics_op_3,[COMBINATORICSOP3],mathematical_structures,combinatorics_op_3,95.0,A,False,True,True,True,[COMBIN..],combinatorics_op_3,emoji_extended,False,Risky Unicode range: emoji_extended; Long fallback: 18 chars; Generic numbered name
NG1836,🜵,ng:mathematical_structures:combinatorics_core_5,[COMBINATORICSCORE5],mathematical_structures,combinatorics_core_5,95.0,A,False,True,True,True,[COMBIN..],combinatorics_core_5,emoji_extended,False,Risky Unicode range: emoji_extended; Long fallback: 20 chars; Generic numbered name
NG1837,🥦,ng:mathematical_structures:combinatorics_op_4,[COMBINATORICSOP4],mathematical_structures,combinatorics_op_4,95.0,A,False,True,True,True,[COMBIN..],combinatorics_op_4,emoji_extended,False,Risky Unicode range: emoji_extended; Long fallback: 18 chars; Generic numbered name
NG1838,⫶,ng:mathematical_structures:combinatorics_fn_3,[COMBINATORICSFN3],mathematical_structures,combinatorics_fn_3,95.0,B,False,False,True,True,[COMBIN..],combinatorics_fn_3,safe,False,Long fallback: 18 chars; Generic numbered name
NG1839,⤲,ng:mathematical_structures:combinatorics_fn_4,[COMBINATORICSFN4],mathematical_structures,combinatorics_fn_4,95.0,B,False,False,True,True,[COMBIN..],combinatorics_fn_4,safe,False,Long fallback: 18 chars; Generic numbered name
NG1840,🦩,ng:mathematical_structures:combinatorics_ctrl_2,[COMBINATORICSCTRL2],mathematical_structures,combinatorics_ctrl_2,95.0,A,False,True,True,True,[COMBIN..],combinatorics_ctrl_2,emoji_extended,False,Risky Unicode range: emoji_extended; Long fallback: 20 chars; Generic numbered name
NG1841,⮰,ng:mathematical_structures:combinatorics_meta_4,[COMBINATORICSMETA4],mathematical_structures,combinatorics_meta_4,95.0,B,False,False,True,True,[COMBIN..],combinatorics_meta_4,safe,False,Long fallback: 20 chars; Generic numbered name
NG1842,𝞉,ng:philosophical_concepts:knowledge_ctrl,[KNOWLEDGECTRL],philosophical_concepts,knowledge_ctrl,95.0,C,False,False,True,False,[KNOWLE..],,safe,False,Long fallback: 15 chars
NG1843,⤣,ng:philosophical_concepts:justification_sys,[JUSTIFICATIONSYS],philosophical_concepts,justification_sys,95.0,C,False,False,True,False,[JUSTIF..],,safe,False,Long fallback: 18 chars
NG1844,⣡,ng:philosophical_concepts:justification_proc,[JUSTIFICATIONPROC],philosophical_concepts,justification_proc,95.0,C,False,False,True,False,[JUSTIF..],,safe,False,Long fallback: 19 chars
NG1845,😪,ng:philosophical_concepts:knowledge_ctrl_1,[KNOWLEDGECTRL1],philosophical_concepts,knowledge_ctrl_1,95.0,A,False,True,True,True,[KNOWLE..],knowledge_ctrl_1,emoji_extended,False,Risky Unicode range: emoji_extended; Long fallback: 16 chars; Generic numbered name
NG1846,⢢,ng:philosophical_concepts:belief_core,[BELIEFCORE],philosophical_concepts,belief_core,95.0,C,False,False,True,False,[BELIEF..],,safe,False,Long fallback: 12 chars
NG1847,❈,ng:philosophical_concepts:belief_ctrl,[BELIEFCTRL],philosophical_concepts,belief_ctrl,95.0,C,False,False,True,False,[BELIEF..],,safe,False,Long fallback: 12 chars
NG1848,🞓,ng:philosophical_concepts:knowledge_proc,[KNOWLEDGEPROC],philosophical_concepts,knowledge_proc,95.0,A,False,True,True,False,[KNOWLE..],,emoji_extended,False,Risky Unicode range: emoji_extended; Long fallback: 15 chars
NG1849,⧘,ng:philosophical_concepts:belief_fn,[BELIEFFN],philosophical_concepts,belief_fn,95.0,OK,False,False,False,False,,,safe,False,
NG1850,⢞,ng:philosophical_concepts:justification_ctrl,[JUSTIFICATIONCTRL],philosophical_concepts,justification_ctrl,95.0,C,False,False,True,False,[JUSTIF..],,safe,False,Long fallback: 19 chars
NG1851,🎊,ng:philosophical_concepts:belief_sys,[BELIEFSYS],philosophical_concepts,belief_sys,95.0,A,False,True,True,False,[BELIEF..],,emoji_extended,False,Risky Unicode range: emoji_extended; Long fallback: 11 chars
NG1852,🜪,ng:philosophical_concepts:ontology_op,[ONTOLOGYOP],philosophical_concepts,ontology_op,95.0,A,False,True,True,False,[ONTOLO..],,emoji_extended,False,Risky Unicode range: emoji_extended; Long fallback: 12 chars
NG1853,🤵,ng:philosophical_concepts:ontology_sys,[ONTOLOGYSYS],philosophical_concepts,ontology_sys,95.0,A,False,True,True,False,[ONTOLO..],,emoji_extended,False,Risky Unicode range: emoji_extended; Long fallback: 13 chars
NG1854,🔕,ng:philosophical_concepts:ontology_sys_1,[ONTOLOGYSYS1],philosophical_concepts,ontology_sys_1,95.0,A,False,True,True,True,[ONTOLO..],ontology_sys_1,emoji_extended,False,Risky Unicode range: emoji_extended; Long fallback: 14 chars; Generic numbered name
NG1855,🡄,ng:philosophical_concepts:ontology_op_1,[ONTOLOGYOP1],philosophical_concepts,ontology_op_1,95.0,A,False,True,True,True,[ONTOLO..],ontology_op_1,emoji_extended,False,Risky Unicode range: emoji_extended; Long fallback: 13 chars; Generic numbered name
NG1856,⤯,ng:philosophical_concepts:ontology_sys_2,[ONTOLOGYSYS2],philosophical_concepts,ontology_sys_2,95.0,B,False,False,True,True,[ONTOLO..],ontology_sys_2,safe,False,Long fallback: 14 chars; Generic numbered name
NG1857,🔊,ng:philosophical_concepts:ontology_op_2,[ONTOLOGYOP2],philosophical_concepts,ontology_op_2,95.0,A,False,True,True,True,[ONTOLO..],ontology_op_2,emoji_extended,False,Risky Unicode range: emoji_extended; Long fallback: 13 chars; Generic numbered name
NG1858,💔,ng:philosophical_concepts:ontology_meta,[ONTOLOGYMETA],philosophical_concepts,ontology_meta,95.0,A,False,True,True,False,[ONTOLO..],,emoji_extended,False,Risky Unicode range: emoji_extended; Long fallback: 14 chars
NG1859,🞗,ng:philosophical_concepts:ontology_op_3,[ONTOLOGYOP3],philosophical_concepts,ontology_op_3,95.0,A,False,True,True,True,[ONTOLO..],ontology_op_3,emoji_extended,False,Risky Unicode range: emoji_extended; Long fallback: 13 chars; Generic numbered name
NG1860,🡇,ng:philosophical_concepts:ontology_op_4,[ONTOLOGYOP4],philosophical_concepts,ontology_op_4,95.0,A,False,True,True,True,[ONTOLO..],ontology_op_4,emoji_extended,False,Risky Unicode range: emoji_extended; Long fallback: 13 chars; Generic numbered name
NG1861,⢡,ng:philosophical_concepts:ontology_core,[ONTOLOGYCORE],philosophical_concepts,ontology_core,95.0,C,False,False,True,False,[ONTOLO..],,safe,False,Long fallback: 14 chars
NG1862,🐤,ng:philosophical_concepts:logicphilosophy_op,[LOGICPHILOSOPHYOP],philosophical_concepts,logicphilosophy_op,95.0,A,False,True,True,False,[LOGICP..],,emoji_extended,False,Risky Unicode range: emoji_extended; Long fallback: 19 chars
NG1863,🞣,ng:philosophical_concepts:logicphilosophy,[LOGICPHILOSOPHY],philosophical_concepts,logicphilosophy,95.0,A,False,True,True,False,[LOGICP..],,emoji_extended,False,Risky Unicode range: emoji_extended; Long fallback: 17 chars
NG1864,📻,ng:philosophical_concepts:logicphilosophy_fn,[LOGICPHILOSOPHYFN],philosophical_concepts,logicphilosophy_fn,95.0,A,False,True,True,False,[LOGICP..],,emoji_extended,False,Risky Unicode range: emoji_extended; Long fallback: 19 chars
NG1865,✬,ng:philosophical_concepts:logicphilosophy_fn_1,[LOGICPHILOSOPHYFN1],philosophical_concepts,logicphilosophy_fn_1,95.0,B,False,False,True,True,[LOGICP..],logicphilosophy_fn_1,safe,False,Long fallback: 20 chars; Generic numbered name
NG1866,⢫,ng:philosophical_concepts:logicphilosophy_op_1,[LOGICPHILOSOPHYOP1],philosophical_concepts,logicphilosophy_op_1,95.0,B,False,False,True,True,[LOGICP..],logicphilosophy_op_1,safe,False,Long fallback: 20 chars; Generic numbered name
NG1867,🥷,ng:philosophical_concepts:logicphilosophy_fn_2,[LOGICPHILOSOPHYFN2],philosophical_concepts,logicphilosophy_fn_2,95.0,A,False,True,True,True,[LOGICP..],logicphilosophy_fn_2,emoji_extended,False,Risky Unicode range: emoji_extended; Long fallback: 20 chars; Generic numbered name
NG1868,⯡,ng:philosophical_concepts:logicphilosophy_sys,[LOGICPHILOSOPHYSYS],philosophical_concepts,logicphilosophy_sys,95.0,C,False,False,True,False,[LOGICP..],,safe,False,Long fallback: 20 chars
NG1869,➱,ng:philosophical_concepts:logicphilosophy_op_2,[LOGICPHILOSOPHYOP2],philosophical_concepts,logicphilosophy_op_2,95.0,B,False,False,True,True,[LOGICP..],logicphilosophy_op_2,safe,False,Long fallback: 20 chars; Generic numbered name
NG1870,⪙,ng:philosophical_concepts:logicphilosophy_fn_3,[LOGICPHILOSOPHYFN3],philosophical_concepts,logicphilosophy_fn_3,95.0,B,False,False,True,True,[LOGICP..],logicphilosophy_fn_3,safe,False,Long fallback: 20 chars; Generic numbered name
NG1871,💽,ng:philosophical_concepts:logicphilosophy_op_3,[LOGICPHILOSOPHYOP3],philosophical_concepts,logicphilosophy_op_3,95.0,A,False,True,True,True,[LOGICP..],logicphilosophy_op_3,emoji_extended,False,Risky Unicode range: emoji_extended; Long fallback: 20 chars; Generic numbered name
NG1872,🍊,ng:philosophical_concepts:philosophyofmind_fn,[PHILOSOPHYOFMINDFN],philosophical_concepts,philosophyofmind_fn,95.0,A,False,True,True,False,[PHILOS..],,emoji_extended,False,Risky Unicode range: emoji_extended; Long fallback: 20 chars
NG1873,⨵,ng:philosophical_concepts:philosophyofmind,[PHILOSOPHYOFMIND],philosophical_concepts,philosophyofmind,95.0,C,False,False,True,False,[PHILOS..],,safe,False,Long fallback: 18 chars
NG1874,🤂,ng:philosophical_concepts:philosophyofmind_1,[PHILOSOPHYOFMIND1],philosophical_concepts,philosophyofmind_1,95.0,A,False,True,True,True,[PHILOS..],philosophyofmind_1,emoji_extended,False,Risky Unicode range: emoji_extended; Long fallback: 19 chars; Generic numbered name
NG1875,😐,ng:philosophical_concepts:philosophyofmind_op,[PHILOSOPHYOFMINDOP],philosophical_concepts,philosophyofmind_op,95.0,A,False,True,True,False,[PHILOS..],,emoji_extended,False,Risky Unicode range: emoji_extended; Long fallback: 20 chars
NG1876,🖜,ng:philosophical_concepts:philosophyofmind_2,[PHILOSOPHYOFMIND2],philosophical_concepts,philosophyofmind_2,95.0,A,False,True,True,True,[PHILOS..],philosophyofmind_2,emoji_extended,False,Risky Unicode range: emoji_extended; Long fallback: 19 chars; Generic numbered name
NG1877,⧈,ng:philosophical_concepts:philosophyofmind_3,[PHILOSOPHYOFMIND3],philosophical_concepts,philosophyofmind_3,95.0,B,False,False,True,True,[PHILOS..],philosophyofmind_3,safe,False,Long fallback: 19 chars; Generic numbered name
NG1878,⮉,ng:philosophical_concepts:philosophyofmind_4,[PHILOSOPHYOFMIND4],philosophical_concepts,philosophyofmind_4,95.0,B,False,False,True,True,[PHILOS..],philosophyofmind_4,safe,False,Long fallback: 19 chars; Generic numbered name
NG1879,🢰,ng:philosophical_concepts:ethics_op,[ETHICSOP],philosophical_concepts,ethics_op,95.0,B,False,True,False,False,,,emoji_extended,False,Risky Unicode range: emoji_extended
NG1880,🤻,ng:philosophical_concepts:ethics_op_1,[ETHICSOP1],philosophical_concepts,ethics_op_1,95.0,A,False,True,True,True,[ETHICS..],ethics_op_1,emoji_extended,False,Risky Unicode range: emoji_extended; Long fallback: 11 chars; Generic numbered name
NG1881,🤽,ng:philosophical_concepts:ethics_meta,[ETHICSMETA],philosophical_concepts,ethics_meta,95.0,A,False,True,True,False,[ETHICS..],,emoji_extended,False,Risky Unicode range: emoji_extended; Long fallback: 12 chars
NG1882,🜑,ng:philosophical_concepts:ethics_core,[ETHICSCORE],philosophical_concepts,ethics_core,95.0,A,False,True,True,False,[ETHICS..],,emoji_extended,False,Risky Unicode range: emoji_extended; Long fallback: 12 chars
NG1883,⬑,ng:philosophical_concepts:ethics,[ETHICS],philosophical_concepts,ethics,95.0,OK,False,False,False,False,,,safe,False,
NG1884,🧰,ng:philosophical_concepts:ethics_1,[ETHICS1],philosophical_concepts,ethics_1,95.0,A,False,True,False,True,,ethics_1,emoji_extended,False,Risky Unicode range: emoji_extended; Generic numbered name
NG1885,🤡,ng:philosophical_concepts:ethics_core_1,[ETHICSCORE1],philosophical_concepts,ethics_core_1,95.0,A,False,True,True,True,[ETHICS..],ethics_core_1,emoji_extended,False,Risky Unicode range: emoji_extended; Long fallback: 13 chars; Generic numbered name
NG1886,⯖,ng:philosophical_concepts:ethics_fn,[ETHICSFN],philosophical_concepts,ethics_fn,95.0,OK,False,False,False,False,,,safe,False,
NG1887,⪵,ng:philosophical_concepts:ethics_2,[ETHICS2],philosophical_concepts,ethics_2,95.0,C,False,False,False,True,,ethics_2,safe,False,Generic numbered name
NG1888,🥔,ng:philosophical_concepts:ethics_3,[ETHICS3],philosophical_concepts,ethics_3,95.0,A,False,True,False,True,,ethics_3,emoji_extended,False,Risky Unicode range: emoji_extended; Generic numbered name
NG1889,⮞,ng:philosophical_concepts:metaphysics_meta,[METAPHYSICSMETA],philosophical_concepts,metaphysics_meta,95.0,C,False,False,True,False,[METAPH..],,safe,False,Long fallback: 17 chars
NG1890,⠕,ng:philosophical_concepts:metaphysics_ctrl,[METAPHYSICSCTRL],philosophical_concepts,metaphysics_ctrl,95.0,C,False,False,True,False,[METAPH..],,safe,False,Long fallback: 17 chars
NG1891,➰,ng:philosophical_concepts:metaphysics_ctrl_1,[METAPHYSICSCTRL1],philosophical_concepts,metaphysics_ctrl_1,95.0,B,False,False,True,True,[METAPH..],metaphysics_ctrl_1,safe,False,Long fallback: 18 chars; Generic numbered name
NG1892,🧇,ng:philosophical_concepts:metaphysics_ctrl_2,[METAPHYSICSCTRL2],philosophical_concepts,metaphysics_ctrl_2,95.0,A,False,True,True,True,[METAPH..],metaphysics_ctrl_2,emoji_extended,False,Risky Unicode range: emoji_extended; Long fallback: 18 chars; Generic numbered name
NG1893,✆,ng:philosophical_concepts:metaphysics_fn,[METAPHYSICSFN],philosophical_concepts,metaphysics_fn,95.0,C,False,False,True,False,[METAPH..],,safe,False,Long fallback: 15 chars
NG1894,⧻,ng:philosophical_concepts:metaphysics,[METAPHYSICS],philosophical_concepts,metaphysics,95.0,C,False,False,True,False,[METAPH..],,safe,False,Long fallback: 13 chars
NG1895,⦬,ng:philosophical_concepts:metaphysics_ctrl_3,[METAPHYSICSCTRL3],philosophical_concepts,metaphysics_ctrl_3,95.0,B,False,False,True,True,[METAPH..],metaphysics_ctrl_3,safe,False,Long fallback: 18 chars; Generic numbered name
NG1896,🦚,ng:philosophical_concepts:metaphysics_ctrl_4,[METAPHYSICSCTRL4],philosophical_concepts,metaphysics_ctrl_4,95.0,A,False,True,True,True,[METAPH..],metaphysics_ctrl_4,emoji_extended,False,Risky Unicode range: emoji_extended; Long fallback: 18 chars; Generic numbered name
NG1897,💩,ng:philosophical_concepts:metaphysics_meta_1,[METAPHYSICSMETA1],philosophical_concepts,metaphysics_meta_1,95.0,A,False,True,True,True,[METAPH..],metaphysics_meta_1,emoji_extended,False,Risky Unicode range: emoji_extended; Long fallback: 18 chars; Generic numbered name
NG1898,🞩,ng:philosophical_concepts:metaphysics_core,[METAPHYSICSCORE],philosophical_concepts,metaphysics_core,95.0,A,False,True,True,False,[METAPH..],,emoji_extended,False,Risky Unicode range: emoji_extended; Long fallback: 17 chars
NG1899,🚞,ng:philosophical_concepts:metaphysics_1,[METAPHYSICS1],philosophical_concepts,metaphysics_1,95.0,A,False,True,True,True,[METAPH..],metaphysics_1,emoji_extended,False,Risky Unicode range: emoji_extended; Long fallback: 14 chars; Generic numbered name
NG1900,🕯,ng:philosophical_concepts:metaphysics_fn_1,[METAPHYSICSFN1],philosophical_concepts,metaphysics_fn_1,95.0,A,False,True,True,True,[METAPH..],metaphysics_fn_1,emoji_extended,False,Risky Unicode range: emoji_extended; Long fallback: 16 chars; Generic numbered name
NG1901,🝾,ng:philosophical_concepts:metaphysics_ctrl_5,[METAPHYSICSCTRL5],philosophical_concepts,metaphysics_ctrl_5,95.0,A,False,True,True,True,[METAPH..],metaphysics_ctrl_5,emoji_extended,False,Risky Unicode range: emoji_extended; Long fallback: 18 chars; Generic numbered name
NG1902,🛣,ng:philosophical_concepts:metaphysics_fn_2,[METAPHYSICSFN2],philosophical_concepts,metaphysics_fn_2,95.0,A,False,True,True,True,[METAPH..],metaphysics_fn_2,emoji_extended,False,Risky Unicode range: emoji_extended; Long fallback: 16 chars; Generic numbered name
NG1903,❜,ng:philosophical_concepts:metaphysics_fn_3,[METAPHYSICSFN3],philosophical_concepts,metaphysics_fn_3,95.0,B,False,False,True,True,[METAPH..],metaphysics_fn_3,safe,False,Long fallback: 16 chars; Generic numbered name
NG1904,🦞,ng:philosophical_concepts:metaphysics_proc,[METAPHYSICSPROC],philosophical_concepts,metaphysics_proc,95.0,A,False,True,True,False,[METAPH..],,emoji_extended,False,Risky Unicode range: emoji_extended; Long fallback: 17 chars
NG1905,🦔,ng:philosophical_concepts:metaphysics_ctrl_6,[METAPHYSICSCTRL6],philosophical_concepts,metaphysics_ctrl_6,95.0,A,False,True,True,True,[METAPH..],metaphysics_ctrl_6,emoji_extended,False,Risky Unicode range: emoji_extended; Long fallback: 18 chars; Generic numbered name
NG1906,⬲,ng:cognitive_modeling:attention_proc,[ATTENTIONPROC],cognitive_modeling,attention_proc,95.0,C,False,False,True,False,[ATTENT..],,safe,False,Long fallback: 15 chars
NG1907,⨢,ng:cognitive_modeling:cognition_proc,[COGNITIONPROC],cognitive_modeling,cognition_proc,95.0,C,False,False,True,False,[COGNIT..],,safe,False,Long fallback: 15 chars
NG1908,🤛,ng:cognitive_modeling:reasoning_fn,[REASONINGFN],cognitive_modeling,reasoning_fn,95.0,A,False,True,True,False,[REASON..],,emoji_extended,False,Risky Unicode range: emoji_extended; Long fallback: 13 chars
NG1909,⨧,ng:cognitive_modeling:attention_proc_1,[ATTENTIONPROC1],cognitive_modeling,attention_proc_1,95.0,B,False,False,True,True,[ATTENT..],attention_proc_1,safe,False,Long fallback: 16 chars; Generic numbered name
NG1910,⧨,ng:cognitive_modeling:memory_core,[MEMORYCORE],cognitive_modeling,memory_core,95.0,C,False,False,True,False,[MEMORY..],,safe,False,Long fallback: 12 chars
NG1911,➕,ng:cognitive_modeling:memory_op,[MEMORYOP],cognitive_modeling,memory_op,95.0,OK,False,False,False,False,,,safe,False,
NG1912,🚍,ng:cognitive_modeling:cognition,[COGNITION],cognitive_modeling,cognition,95.0,A,False,True,True,False,[COGNIT..],,emoji_extended,False,Risky Unicode range: emoji_extended; Long fallback: 11 chars
NG1913,🍉,ng:cognitive_modeling:memory_meta,[MEMORYMETA],cognitive_modeling,memory_meta,95.0,A,False,True,True,False,[MEMORY..],,emoji_extended,False,Risky Unicode range: emoji_extended; Long fallback: 12 chars
NG1914,😧,ng:cognitive_modeling:reasoning,[REASONING],cognitive_modeling,reasoning,95.0,A,False,True,True,False,[REASON..],,emoji_extended,False,Risky Unicode range: emoji_extended; Long fallback: 11 chars
NG1915,⩇,ng:cognitive_modeling:attention_ctrl,[ATTENTIONCTRL],cognitive_modeling,attention_ctrl,95.0,C,False,False,True,False,[ATTENT..],,safe,False,Long fallback: 15 chars
NG1916,➜,ng:cognitive_modeling:memorymodels_op,[MEMORYMODELSOP],cognitive_modeling,memorymodels_op,95.0,C,False,False,True,False,[MEMORY..],,safe,False,Long fallback: 16 chars
NG1917,🕾,ng:cognitive_modeling:memorymodels,[MEMORYMODELS],cognitive_modeling,memorymodels,95.0,A,False,True,True,False,[MEMORY..],,emoji_extended,False,Risky Unicode range: emoji_extended; Long fallback: 14 chars
NG1918,⩧,ng:cognitive_modeling:memorymodels_proc,[MEMORYMODELSPROC],cognitive_modeling,memorymodels_proc,95.0,C,False,False,True,False,[MEMORY..],,safe,False,Long fallback: 18 chars
NG1919,➫,ng:cognitive_modeling:memorymodels_1,[MEMORYMODELS1],cognitive_modeling,memorymodels_1,95.0,B,False,False,True,True,[MEMORY..],memorymodels_1,safe,False,Long fallback: 15 chars; Generic numbered name
NG1920,🧜,ng:cognitive_modeling:memorymodels_op_1,[MEMORYMODELSOP1],cognitive_modeling,memorymodels_op_1,95.0,A,False,True,True,True,[MEMORY..],memorymodels_op_1,emoji_extended,False,Risky Unicode range: emoji_extended; Long fallback: 17 chars; Generic numbered name
NG1921,🛌,ng:cognitive_modeling:memorymodels_ctrl,[MEMORYMODELSCTRL],cognitive_modeling,memorymodels_ctrl,95.0,A,False,True,True,False,[MEMORY..],,emoji_extended,False,Risky Unicode range: emoji_extended; Long fallback: 18 chars
NG1922,📀,ng:cognitive_modeling:memorymodels_2,[MEMORYMODELS2],cognitive_modeling,memorymodels_2,95.0,A,False,True,True,True,[MEMORY..],memorymodels_2,emoji_extended,False,Risky Unicode range: emoji_extended; Long fallback: 15 chars; Generic numbered name
NG1923,✤,ng:cognitive_modeling:memorymodels_proc_1,[MEMORYMODELSPROC1],cognitive_modeling,memorymodels_proc_1,95.0,B,False,False,True,True,[MEMORY..],memorymodels_proc_1,safe,False,Long fallback: 19 chars; Generic numbered name
NG1924,🐮,ng:cognitive_modeling:memorymodels_ctrl_1,[MEMORYMODELSCTRL1],cognitive_modeling,memorymodels_ctrl_1,95.0,A,False,True,True,True,[MEMORY..],memorymodels_ctrl_1,emoji_extended,False,Risky Unicode range: emoji_extended; Long fallback: 19 chars; Generic numbered name
NG1925,🛢,ng:cognitive_modeling:memorymodels_sys,[MEMORYMODELSSYS],cognitive_modeling,memorymodels_sys,95.0,A,False,True,True,False,[MEMORY..],,emoji_extended,False,Risky Unicode range: emoji_extended; Long fallback: 17 chars
NG1926,✂,ng:cognitive_modeling:attentionmodels,[ATTENTIONMODELS],cognitive_modeling,attentionmodels,95.0,C,False,False,True,False,[ATTENT..],,safe,False,Long fallback: 17 chars
NG1927,🔑,ng:cognitive_modeling:attentionmodels_1,[ATTENTIONMODELS1],cognitive_modeling,attentionmodels_1,95.0,A,False,True,True,True,[ATTENT..],attentionmodels_1,emoji_extended,False,Risky Unicode range: emoji_extended; Long fallback: 18 chars; Generic numbered name
NG1928,⠉,ng:cognitive_modeling:attentionmodels_op,[ATTENTIONMODELSOP],cognitive_modeling,attentionmodels_op,95.0,C,False,False,True,False,[ATTENT..],,safe,False,Long fallback: 19 chars
NG1929,🦫,ng:cognitive_modeling:attentionmodels_fn,[ATTENTIONMODELSFN],cognitive_modeling,attentionmodels_fn,95.0,A,False,True,True,False,[ATTENT..],,emoji_extended,False,Risky Unicode range: emoji_extended; Long fallback: 19 chars
NG1930,⧺,ng:cognitive_modeling:attentionmodels_sys,[ATTENTIONMODELSSYS],cognitive_modeling,attentionmodels_sys,95.0,C,False,False,True,False,[ATTENT..],,safe,False,Long fallback: 20 chars
NG1931,⢸,ng:cognitive_modeling:attentionmodels_op_1,[ATTENTIONMODELSOP1],cognitive_modeling,attentionmodels_op_1,95.0,B,False,False,True,True,[ATTENT..],attentionmodels_op_1,safe,False,Long fallback: 20 chars; Generic numbered name
NG1932,⨯,ng:cognitive_modeling:attentionmodels_fn_1,[ATTENTIONMODELSFN1],cognitive_modeling,attentionmodels_fn_1,95.0,B,False,False,True,True,[ATTENT..],attentionmodels_fn_1,safe,False,Long fallback: 20 chars; Generic numbered name
NG1933,⢮,ng:cognitive_modeling:attentionmodels_2,[ATTENTIONMODELS2],cognitive_modeling,attentionmodels_2,95.0,B,False,False,True,True,[ATTENT..],attentionmodels_2,safe,False,Long fallback: 18 chars; Generic numbered name
NG1934,⮲,ng:cognitive_modeling:attentionmodels_op_2,[ATTENTIONMODELSOP2],cognitive_modeling,attentionmodels_op_2,95.0,B,False,False,True,True,[ATTENT..],attentionmodels_op_2,safe,False,Long fallback: 20 chars; Generic numbered name
NG1935,🥴,ng:cognitive_modeling:attentionmodels_3,[ATTENTIONMODELS3],cognitive_modeling,attentionmodels_3,95.0,A,False,True,True,True,[ATTENT..],attentionmodels_3,emoji_extended,False,Risky Unicode range: emoji_extended; Long fallback: 18 chars; Generic numbered name
NG1936,🜫,ng:cognitive_modeling:decisionmaking_proc,[DECISIONMAKINGPROC],cognitive_modeling,decisionmaking_proc,95.0,A,False,True,True,False,[DECISI..],,emoji_extended,False,Risky Unicode range: emoji_extended; Long fallback: 20 chars
NG1937,⧰,ng:cognitive_modeling:decisionmaking_core,[DECISIONMAKINGCORE],cognitive_modeling,decisionmaking_core,95.0,C,False,False,True,False,[DECISI..],,safe,False,Long fallback: 20 chars
NG1938,🥭,ng:cognitive_modeling:decisionmaking_ctrl,[DECISIONMAKINGCTRL],cognitive_modeling,decisionmaking_ctrl,95.0,A,False,True,True,False,[DECISI..],,emoji_extended,False,Risky Unicode range: emoji_extended; Long fallback: 20 chars
NG1939,🠤,ng:cognitive_modeling:decisionmaking_op,[DECISIONMAKINGOP],cognitive_modeling,decisionmaking_op,95.0,A,False,True,True,False,[DECISI..],,emoji_extended,False,Risky Unicode range: emoji_extended; Long fallback: 18 chars
NG1940,⣽,ng:cognitive_modeling:decisionmaking_fn,[DECISIONMAKINGFN],cognitive_modeling,decisionmaking_fn,95.0,C,False,False,True,False,[DECISI..],,safe,False,Long fallback: 18 chars
NG1941,⥗,ng:cognitive_modeling:decisionmaking_meta,[DECISIONMAKINGMETA],cognitive_modeling,decisionmaking_meta,95.0,C,False,False,True,False,[DECISI..],,safe,False,Long fallback: 20 chars
NG1942,🜁,ng:cognitive_modeling:decisionmaking_fn_1,[DECISIONMAKINGFN1],cognitive_modeling,decisionmaking_fn_1,95.0,A,False,True,True,True,[DECISI..],decisionmaking_fn_1,emoji_extended,False,Risky Unicode range: emoji_extended; Long fallback: 19 chars; Generic numbered name
NG1943,❋,ng:cognitive_modeling:decisionmaking_op_1,[DECISIONMAKINGOP1],cognitive_modeling,decisionmaking_op_1,95.0,B,False,False,True,True,[DECISI..],decisionmaking_op_1,safe,False,Long fallback: 19 chars; Generic numbered name
NG1944,🡨,ng:cognitive_modeling:decisionmaking,[DECISIONMAKING],cognitive_modeling,decisionmaking,95.0,A,False,True,True,False,[DECISI..],,emoji_extended,False,Risky Unicode range: emoji_extended; Long fallback: 16 chars
NG1945,⠠,ng:cognitive_modeling:decisionmaking_1,[DECISIONMAKING1],cognitive_modeling,decisionmaking_1,95.0,B,False,False,True,True,[DECISI..],decisionmaking_1,safe,False,Long fallback: 17 chars; Generic numbered name
NG1946,🔎,ng:cognitive_modeling:learningmechanisms,[LEARNINGMECHANISMS],cognitive_modeling,learningmechanisms,95.0,A,False,True,True,False,[LEARNI..],,emoji_extended,False,Risky Unicode range: emoji_extended; Long fallback: 20 chars
NG1947,✘,ng:cognitive_modeling:perceptionmodels,[PERCEPTIONMODELS],cognitive_modeling,perceptionmodels,95.0,C,False,False,True,False,[PERCEP..],,safe,False,Long fallback: 18 chars
NG1948,🤞,ng:cognitive_modeling:perceptionmodels_op,[PERCEPTIONMODELSOP],cognitive_modeling,perceptionmodels_op,95.0,A,False,True,True,False,[PERCEP..],,emoji_extended,False,Risky Unicode range: emoji_extended; Long fallback: 20 chars
NG1949,🤥,ng:cognitive_modeling:perceptionmodels_fn,[PERCEPTIONMODELSFN],cognitive_modeling,perceptionmodels_fn,95.0,A,False,True,True,False,[PERCEP..],,emoji_extended,False,Risky Unicode range: emoji_extended; Long fallback: 20 chars
NG1950,⡹,ng:cognitive_modeling:perceptionmodels_1,[PERCEPTIONMODELS1],cognitive_modeling,perceptionmodels_1,95.0,B,False,False,True,True,[PERCEP..],perceptionmodels_1,safe,False,Long fallback: 19 chars; Generic numbered name
NG1951,🚟,ng:cognitive_modeling:perceptionmodels_2,[PERCEPTIONMODELS2],cognitive_modeling,perceptionmodels_2,95.0,A,False,True,True,True,[PERCEP..],perceptionmodels_2,emoji_extended,False,Risky Unicode range: emoji_extended; Long fallback: 19 chars; Generic numbered name
NG1952,💨,ng:cognitive_modeling:perceptionmodels_3,[PERCEPTIONMODELS3],cognitive_modeling,perceptionmodels_3,95.0,A,False,True,True,True,[PERCEP..],perceptionmodels_3,emoji_extended,False,Risky Unicode range: emoji_extended; Long fallback: 19 chars; Generic numbered name
NG1953,🚌,ng:cognitive_modeling:perceptionmodels_4,[PERCEPTIONMODELS4],cognitive_modeling,perceptionmodels_4,95.0,A,False,True,True,True,[PERCEP..],perceptionmodels_4,emoji_extended,False,Risky Unicode range: emoji_extended; Long fallback: 19 chars; Generic numbered name
NG1954,⬺,ng:cognitive_modeling:perceptionmodels_5,[PERCEPTIONMODELS5],cognitive_modeling,perceptionmodels_5,95.0,B,False,False,True,True,[PERCEP..],perceptionmodels_5,safe,False,Long fallback: 19 chars; Generic numbered name
NG1955,⠚,ng:cognitive_modeling:perceptionmodels_6,[PERCEPTIONMODELS6],cognitive_modeling,perceptionmodels_6,95.0,B,False,False,True,True,[PERCEP..],perceptionmodels_6,safe,False,Long fallback: 19 chars; Generic numbered name
NG1956,⣭,ng:cognitive_modeling:perceptionmodels_7,[PERCEPTIONMODELS7],cognitive_modeling,perceptionmodels_7,95.0,B,False,False,True,True,[PERCEP..],perceptionmodels_7,safe,False,Long fallback: 19 chars; Generic numbered name
NG1957,🐗,ng:cognitive_modeling:perceptionmodels_8,[PERCEPTIONMODELS8],cognitive_modeling,perceptionmodels_8,95.0,A,False,True,True,True,[PERCEP..],perceptionmodels_8,emoji_extended,False,Risky Unicode range: emoji_extended; Long fallback: 19 chars; Generic numbered name
NG1958,🠁,ng:cognitive_modeling:perceptionmodels_9,[PERCEPTIONMODELS9],cognitive_modeling,perceptionmodels_9,95.0,A,False,True,True,True,[PERCEP..],perceptionmodels_9,emoji_extended,False,Risky Unicode range: emoji_extended; Long fallback: 19 chars; Generic numbered name
NG1959,❁,ng:cognitive_modeling:perceptionmodels_10,[PERCEPTIONMODELS10],cognitive_modeling,perceptionmodels_10,95.0,B,False,False,True,True,[PERCEP..],perceptionmodels_10,safe,False,Long fallback: 20 chars; Generic numbered name
NG1960,⪖,ng:reserved_expansion:future_sys,[FUTURESYS],reserved_expansion,future_sys,95.0,C,False,False,True,False,[FUTURE..],,safe,False,Long fallback: 11 chars
NG1961,⭝,ng:reserved_expansion:experimental_fn,[EXPERIMENTALFN],reserved_expansion,experimental_fn,95.0,C,False,False,True,False,[EXPERI..],,safe,False,Long fallback: 16 chars
NG1962,⠑,ng:reserved_expansion:experimental_op,[EXPERIMENTALOP],reserved_expansion,experimental_op,95.0,C,False,False,True,False,[EXPERI..],,safe,False,Long fallback: 16 chars
NG1963,⨮,ng:reserved_expansion:research_ctrl,[RESEARCHCTRL],reserved_expansion,research_ctrl,95.0,C,False,False,True,False,[RESEAR..],,safe,False,Long fallback: 14 chars
NG1964,🞾,ng:reserved_expansion:future_sys_1,[FUTURESYS1],reserved_expansion,future_sys_1,95.0,A,False,True,True,True,[FUTURE..],future_sys_1,emoji_extended,False,Risky Unicode range: emoji_extended; Long fallback: 12 chars; Generic numbered name
NG1965,🍿,ng:reserved_expansion:future,[FUTURE],reserved_expansion,future,95.0,B,False,True,False,False,,,emoji_extended,False,Risky Unicode range: emoji_extended
NG1966,⫈,ng:reserved_expansion:experimental_meta,[EXPERIMENTALMETA],reserved_expansion,experimental_meta,95.0,C,False,False,True,False,[EXPERI..],,safe,False,Long fallback: 18 chars
NG1967,🡺,ng:reserved_expansion:future_ctrl,[FUTURECTRL],reserved_expansion,future_ctrl,95.0,A,False,True,True,False,[FUTURE..],,emoji_extended,False,Risky Unicode range: emoji_extended; Long fallback: 12 chars
NG1968,🠛,ng:reserved_expansion:future_op,[FUTUREOP],reserved_expansion,future_op,95.0,B,False,True,False,False,,,emoji_extended,False,Risky Unicode range: emoji_extended
NG1969,⠙,ng:reserved_expansion:novel_core,[NOVELCORE],reserved_expansion,novel_core,95.0,C,False,False,True,False,[NOVELC..],,safe,False,Long fallback: 11 chars
NG1970,⣧,ng:reserved_expansion:novel_meta,[NOVELMETA],reserved_expansion,novel_meta,95.0,C,False,False,True,False,[NOVELM..],,safe,False,Long fallback: 11 chars
NG1971,🏘,ng:reserved_expansion:novel_ctrl,[NOVELCTRL],reserved_expansion,novel_ctrl,95.0,A,False,True,True,False,[NOVELC..],,emoji_extended,False,Risky Unicode range: emoji_extended; Long fallback: 11 chars
NG1972,⤟,ng:reserved_expansion:future_core,[FUTURECORE],reserved_expansion,future_core,95.0,C,False,False,True,False,[FUTURE..],,safe,False,Long fallback: 12 chars
NG1973,✗,ng:reserved_expansion:research,[RESEARCH],reserved_expansion,research,95.0,OK,False,False,False,False,,,safe,False,
NG1974,⪦,ng:reserved_expansion:researchareas_meta,[RESEARCHAREASMETA],reserved_expansion,researchareas_meta,95.0,C,False,False,True,False,[RESEAR..],,safe,False,Long fallback: 19 chars
NG1975,🕠,ng:reserved_expansion:researchareas_ctrl,[RESEARCHAREASCTRL],reserved_expansion,researchareas_ctrl,95.0,A,False,True,True,False,[RESEAR..],,emoji_extended,False,Risky Unicode range: emoji_extended; Long fallback: 19 chars
NG1976,✜,ng:reserved_expansion:researchareas_sys,[RESEARCHAREASSYS],reserved_expansion,researchareas_sys,95.0,C,False,False,True,False,[RESEAR..],,safe,False,Long fallback: 18 chars
NG1977,⬩,ng:reserved_expansion:researchareas,[RESEARCHAREAS],reserved_expansion,researchareas,95.0,C,False,False,True,False,[RESEAR..],,safe,False,Long fallback: 15 chars
NG1978,⧂,ng:reserved_expansion:researchareas_core,[RESEARCHAREASCORE],reserved_expansion,researchareas_core,95.0,C,False,False,True,False,[RESEAR..],,safe,False,Long fallback: 19 chars
NG1979,✯,ng:reserved_expansion:researchareas_op,[RESEARCHAREASOP],reserved_expansion,researchareas_op,95.0,C,False,False,True,False,[RESEAR..],,safe,False,Long fallback: 17 chars
NG1980,🞚,ng:reserved_expansion:researchareas_1,[RESEARCHAREAS1],reserved_expansion,researchareas_1,95.0,A,False,True,True,True,[RESEAR..],researchareas_1,emoji_extended,False,Risky Unicode range: emoji_extended; Long fallback: 16 chars; Generic numbered name
NG1981,🚽,ng:reserved_expansion:researchareas_core_1,[RESEARCHAREASCORE1],reserved_expansion,researchareas_core_1,95.0,A,False,True,True,True,[RESEAR..],researchareas_core_1,emoji_extended,False,Risky Unicode range: emoji_extended; Long fallback: 20 chars; Generic numbered name
NG1982,⠻,ng:reserved_expansion:researchareas_ctrl_1,[RESEARCHAREASCTRL1],reserved_expansion,researchareas_ctrl_1,95.0,B,False,False,True,True,[RESEAR..],researchareas_ctrl_1,safe,False,Long fallback: 20 chars; Generic numbered name
NG1983,⭯,ng:reserved_expansion:researchareas_op_1,[RESEARCHAREASOP1],reserved_expansion,researchareas_op_1,95.0,B,False,False,True,True,[RESEAR..],researchareas_op_1,safe,False,Long fallback: 18 chars; Generic numbered name
NG1984,🛗,ng:reserved_expansion:researchareas_fn,[RESEARCHAREASFN],reserved_expansion,researchareas_fn,95.0,A,False,True,True,False,[RESEAR..],,emoji_extended,False,Risky Unicode range: emoji_extended; Long fallback: 17 chars
NG1985,⯶,ng:reserved_expansion:researchareas_fn_1,[RESEARCHAREASFN1],reserved_expansion,researchareas_fn_1,95.0,B,False,False,True,True,[RESEAR..],researchareas_fn_1,safe,False,Long fallback: 18 chars; Generic numbered name
NG1986,👆,ng:reserved_expansion:researchareas_ctrl_2,[RESEARCHAREASCTRL2],reserved_expansion,researchareas_ctrl_2,95.0,A,False,True,True,True,[RESEAR..],researchareas_ctrl_2,emoji_extended,False,Risky Unicode range: emoji_extended; Long fallback: 20 chars; Generic numbered name
NG1987,😁,ng:reserved_expansion:researchareas_fn_2,[RESEARCHAREASFN2],reserved_expansion,researchareas_fn_2,95.0,A,False,True,True,True,[RESEAR..],researchareas_fn_2,emoji_extended,False,Risky Unicode range: emoji_extended; Long fallback: 18 chars; Generic numbered name
NG1988,➵,ng:reserved_expansion:emergingparadigms,[EMERGINGPARADIGMS],reserved_expansion,emergingparadigms,95.0,C,False,False,True,False,[EMERGI..],,safe,False,Long fallback: 19 chars
NG1989,➮,ng:reserved_expansion:emergingparadigms_1,[EMERGINGPARADIGMS1],reserved_expansion,emergingparadigms_1,95.0,B,False,False,True,True,[EMERGI..],emergingparadigms_1,safe,False,Long fallback: 20 chars; Generic numbered name
NG1990,🡻,ng:reserved_expansion:emergingparadigms_2,[EMERGINGPARADIGMS2],reserved_expansion,emergingparadigms_2,95.0,A,False,True,True,True,[EMERGI..],emergingparadigms_2,emoji_extended,False,Risky Unicode range: emoji_extended; Long fallback: 20 chars; Generic numbered name
NG1991,🜘,ng:reserved_expansion:emergingparadigms_3,[EMERGINGPARADIGMS3],reserved_expansion,emergingparadigms_3,95.0,A,False,True,True,True,[EMERGI..],emergingparadigms_3,emoji_extended,False,Risky Unicode range: emoji_extended; Long fallback: 20 chars; Generic numbered name
NG1992,🞆,ng:reserved_expansion:emergingparadigms_4,[EMERGINGPARADIGMS4],reserved_expansion,emergingparadigms_4,95.0,A,False,True,True,True,[EMERGI..],emergingparadigms_4,emoji_extended,False,Risky Unicode range: emoji_extended; Long fallback: 20 chars; Generic numbered name
NG1993,✊,ng:reserved_expansion:emergingparadigms_5,[EMERGINGPARADIGMS5],reserved_expansion,emergingparadigms_5,95.0,B,False,False,True,True,[EMERGI..],emergingparadigms_5,safe,False,Long fallback: 20 chars; Generic numbered name
NG1994,⭱,ng:reserved_expansion:novelabstractions,[NOVELABSTRACTIONS],reserved_expansion,novelabstractions,95.0,C,False,False,True,False,[NOVELA..],,safe,False,Long fallback: 19 chars
NG1995,🍰,ng:reserved_expansion:novelabstractions_1,[NOVELABSTRACTIONS1],reserved_expansion,novelabstractions_1,95.0,A,False,True,True,True,[NOVELA..],novelabstractions_1,emoji_extended,False,Risky Unicode range: emoji_extended; Long fallback: 20 chars; Generic numbered name
NG1996,🤹,ng:reserved_expansion:novelabstractions_2,[NOVELABSTRACTIONS2],reserved_expansion,novelabstractions_2,95.0,A,False,True,True,True,[NOVELA..],novelabstractions_2,emoji_extended,False,Risky Unicode range: emoji_extended; Long fallback: 20 chars; Generic numbered name
NG1997,🏚,ng:reserved_expansion:novelabstractions_3,[NOVELABSTRACTIONS3],reserved_expansion,novelabstractions_3,95.0,A,False,True,True,True,[NOVELA..],novelabstractions_3,emoji_extended,False,Risky Unicode range: emoji_extended; Long fallback: 20 chars; Generic numbered name
NG1998,⮣,ng:reserved_expansion:novelabstractions_4,[NOVELABSTRACTIONS4],reserved_expansion,novelabstractions_4,95.0,B,False,False,True,True,[NOVELA..],novelabstractions_4,safe,False,Long fallback: 20 chars; Generic numbered name
NG1999,🡕,ng:reserved_expansion:novelabstractions_5,[NOVELABSTRACTIONS5],reserved_expansion,novelabstractions_5,95.0,A,False,True,True,True,[NOVELA..],novelabstractions_5,emoji_extended,False,Risky Unicode range: emoji_extended; Long fallback: 20 chars; Generic numbered name
NG2000,🐣,ng:reserved_expansion:novelabstractions_6,[NOVELABSTRACTIONS6],reserved_expansion,novelabstractions_6,95.0,A,False,True,True,True,[NOVELA..],novelabstractions_6,emoji_extended,False,Risky Unicode range: emoji_extended; Long fallback: 20 chars; Generic numbered name
NG2001,🝱,ng:reserved_expansion:novelabstractions_7,[NOVELABSTRACTIONS7],reserved_expansion,novelabstractions_7,95.0,A,False,True,True,True,[NOVELA..],novelabstractions_7,emoji_extended,False,Risky Unicode range: emoji_extended; Long fallback: 20 chars; Generic numbered name
NG2002,🜺,ng:reserved_expansion:novelabstractions_8,[NOVELABSTRACTIONS8],reserved_expansion,novelabstractions_8,95.0,A,False,True,True,True,[NOVELA..],novelabstractions_8,emoji_extended,False,Risky Unicode range: emoji_extended; Long fallback: 20 chars; Generic numbered name
NG2003,⡳,ng:reserved_expansion:extensionpoints_op,[EXTENSIONPOINTSOP],reserved_expansion,extensionpoints_op,95.0,C,False,False,True,False,[EXTENS..],,safe,False,Long fallback: 19 chars
NG2004,⤐,ng:reserved_expansion:extensionpoints_fn,[EXTENSIONPOINTSFN],reserved_expansion,extensionpoints_fn,95.0,C,False,False,True,False,[EXTENS..],,safe,False,Long fallback: 19 chars
NG2005,😌,ng:reserved_expansion:extensionpoints_fn_1,[EXTENSIONPOINTSFN1],reserved_expansion,extensionpoints_fn_1,95.0,A,False,True,True,True,[EXTENS..],extensionpoints_fn_1,emoji_extended,False,Risky Unicode range: emoji_extended; Long fallback: 20 chars; Generic numbered name
NG2006,🜞,ng:reserved_expansion:extensionpoints_op_1,[EXTENSIONPOINTSOP1],reserved_expansion,extensionpoints_op_1,95.0,A,False,True,True,True,[EXTENS..],extensionpoints_op_1,emoji_extended,False,Risky Unicode range: emoji_extended; Long fallback: 20 chars; Generic numbered name
NG2007,⤤,ng:reserved_expansion:extensionpoints_op_2,[EXTENSIONPOINTSOP2],reserved_expansion,extensionpoints_op_2,95.0,B,False,False,True,True,[EXTENS..],extensionpoints_op_2,safe,False,Long fallback: 20 chars; Generic numbered name
NG2008,🙍,ng:reserved_expansion:extensionpoints_op_3,[EXTENSIONPOINTSOP3],reserved_expansion,extensionpoints_op_3,95.0,A,False,True,True,True,[EXTENS..],extensionpoints_op_3,emoji_extended,False,Risky Unicode range: emoji_extended; Long fallback: 20 chars; Generic numbered name
NG2009,⬤,ng:reserved_expansion:extensionpoints_sys,[EXTENSIONPOINTSSYS],reserved_expansion,extensionpoints_sys,95.0,C,False,False,True,False,[EXTENS..],,safe,False,Long fallback: 20 chars
NG2010,🧺,ng:reserved_expansion:extensionpoints,[EXTENSIONPOINTS],reserved_expansion,extensionpoints,95.0,A,False,True,True,False,[EXTENS..],,emoji_extended,False,Risky Unicode range: emoji_extended; Long fallback: 17 chars
NG2011,⭢,ng:reserved_expansion:extensionpoints_1,[EXTENSIONPOINTS1],reserved_expansion,extensionpoints_1,95.0,B,False,False,True,True,[EXTENS..],extensionpoints_1,safe,False,Long fallback: 18 chars; Generic numbered name
NG2012,🤏,ng:reserved_expansion:extensionpoints_fn_2,[EXTENSIONPOINTSFN2],reserved_expansion,extensionpoints_fn_2,95.0,A,False,True,True,True,[EXTENS..],extensionpoints_fn_2,emoji_extended,False,Risky Unicode range: emoji_extended; Long fallback: 20 chars; Generic numbered name
NG2013,🥜,ng:reserved_expansion:extensionpoints_2,[EXTENSIONPOINTS2],reserved_expansion,extensionpoints_2,95.0,A,False,True,True,True,[EXTENS..],extensionpoints_2,emoji_extended,False,Risky Unicode range: emoji_extended; Long fallback: 18 chars; Generic numbered name
NG2014,🚓,ng:reserved_expansion:extensionpoints_op_4,[EXTENSIONPOINTSOP4],reserved_expansion,extensionpoints_op_4,95.0,A,False,True,True,True,[EXTENS..],extensionpoints_op_4,emoji_extended,False,Risky Unicode range: emoji_extended; Long fallback: 20 chars; Generic numbered name
NG2015,⢋,ng:reserved_expansion:extensionpoints_fn_3,[EXTENSIONPOINTSFN3],reserved_expansion,extensionpoints_fn_3,95.0,B,False,False,True,True,[EXTENS..],extensionpoints_fn_3,safe,False,Long fallback: 20 chars; Generic numbered name
NG2016,🟂,ng:reserved_expansion:extensionpoints_fn_4,[EXTENSIONPOINTSFN4],reserved_expansion,extensionpoints_fn_4,95.0,A,False,True,True,True,[EXTENS..],extensionpoints_fn_4,emoji_extended,False,Risky Unicode range: emoji_extended; Long fallback: 20 chars; Generic numbered name
NG2017,𝜕,ng:reserved_expansion:extensionpoints_3,[EXTENSIONPOINTS3],reserved_expansion,extensionpoints_3,95.0,B,False,False,True,True,[EXTENS..],extensionpoints_3,safe,False,Long fallback: 18 chars; Generic numbered name
NG2018,🝰,ng:reserved_expansion:extensionpoints_4,[EXTENSIONPOINTS4],reserved_expansion,extensionpoints_4,95.0,A,False,True,True,True,[EXTENS..],extensionpoints_4,emoji_extended,False,Risky Unicode range: emoji_extended; Long fallback: 18 chars; Generic numbered name
NG2019,⬚,ng:reserved_expansion:extensionpoints_fn_5,[EXTENSIONPOINTSFN5],reserved_expansion,extensionpoints_fn_5,95.0,B,False,False,True,True,[EXTENS..],extensionpoints_fn_5,safe,False,Long fallback: 20 chars; Generic numbered name
NG2020,⦦,ng:reserved_expansion:extensionpoints_fn_6,[EXTENSIONPOINTSFN6],reserved_expansion,extensionpoints_fn_6,95.0,B,False,False,True,True,[EXTENS..],extensionpoints_fn_6,safe,False,Long fallback: 20 chars; Generic numbered name
NG2021,🟁,ng:reserved_expansion:extensionpoints_fn_7,[EXTENSIONPOINTSFN7],reserved_expansion,extensionpoints_fn_7,95.0,A,False,True,True,True,[EXTENS..],extensionpoints_fn_7,emoji_extended,False,Risky Unicode range: emoji_extended; Long fallback: 20 chars; Generic numbered name
NG2022,🧃,ng:reserved_expansion:extensionpoints_5,[EXTENSIONPOINTS5],reserved_expansion,extensionpoints_5,95.0,A,False,True,True,True,[EXTENS..],extensionpoints_5,emoji_extended,False,Risky Unicode range: emoji_extended; Long fallback: 18 chars; Generic numbered name
NG2023,💙,ng:reserved_expansion:extensionpoints_op_5,[EXTENSIONPOINTSOP5],reserved_expansion,extensionpoints_op_5,95.0,A,False,True,True,True,[EXTENS..],extensionpoints_op_5,emoji_extended,False,Risky Unicode range: emoji_extended; Long fallback: 20 chars; Generic numbered name
NG2024,✰,ng:reserved_expansion:extensionpoints_op_6,[EXTENSIONPOINTSOP6],reserved_expansion,extensionpoints_op_6,95.0,B,False,False,True,True,[EXTENS..],extensionpoints_op_6,safe,False,Long fallback: 20 chars; Generic numbered name
NG2025,🦯,ng:reserved_expansion:extensionpoints_op_7,[EXTENSIONPOINTSOP7],reserved_expansion,extensionpoints_op_7,95.0,A,False,True,True,True,[EXTENS..],extensionpoints_op_7,emoji_extended,False,Risky Unicode range: emoji_extended; Long fallback: 20 chars; Generic numbered name
NG2026,➭,ng:reserved_expansion:extensionpoints_op_8,[EXTENSIONPOINTSOP8],reserved_expansion,extensionpoints_op_8,95.0,B,False,False,True,True,[EXTENS..],extensionpoints_op_8,safe,False,Long fallback: 20 chars; Generic numbered name
NG2027,⥨,ng:reserved_expansion:extensionpoints_fn_8,[EXTENSIONPOINTSFN8],reserved_expansion,extensionpoints_fn_8,95.0,B,False,False,True,True,[EXTENS..],extensionpoints_fn_8,safe,False,Long fallback: 20 chars; Generic numbered name
NG2028,🢢,ng:reserved_expansion:extensionpoints_6,[EXTENSIONPOINTS6],reserved_expansion,extensionpoints_6,95.0,A,False,True,True,True,[EXTENS..],extensionpoints_6,emoji_extended,False,Risky Unicode range: emoji_extended; Long fallback: 18 chars; Generic numbered name
NG2029,⦟,ng:reserved_expansion:extensionpoints_7,[EXTENSIONPOINTS7],reserved_expansion,extensionpoints_7,95.0,B,False,False,True,True,[EXTENS..],extensionpoints_7,safe,False,Long fallback: 18 chars; Generic numbered name
NG2030,🝎,ng:reserved_expansion:extensionpoints_op_9,[EXTENSIONPOINTSOP9],reserved_expansion,extensionpoints_op_9,95.0,A,False,True,True,True,[EXTENS..],extensionpoints_op_9,emoji_extended,False,Risky Unicode range: emoji_extended; Long fallback: 20 chars; Generic numbered name
NG2031,🟧,ng:reserved_expansion:extensionpoints_fn_9,[EXTENSIONPOINTSFN9],reserved_expansion,extensionpoints_fn_9,95.0,A,False,True,True,True,[EXTENS..],extensionpoints_fn_9,emoji_extended,False,Risky Unicode range: emoji_extended; Long fallback: 20 chars; Generic numbered name
NG2032,⠷,ng:reserved_expansion:extensionpoints_8,[EXTENSIONPOINTS8],reserved_expansion,extensionpoints_8,95.0,B,False,False,True,True,[EXTENS..],extensionpoints_8,safe,False,Long fallback: 18 chars; Generic numbered name
NG2033,🠝,ng:reserved_expansion:extensionpoints_9,[EXTENSIONPOINTS9],reserved_expansion,extensionpoints_9,95.0,A,False,True,True,True,[EXTENS..],extensionpoints_9,emoji_extended,False,Risky Unicode range: emoji_extended; Long fallback: 18 chars; Generic numbered name
NG2034,⮏,ng:reserved_expansion:extensionpoints_10,[EXTENSIONPOINTS10],reserved_expansion,extensionpoints_10,95.0,B,False,False,True,True,[EXTENS..],extensionpoints_10,safe,False,Long fallback: 19 chars; Generic numbered name
NG2035,🦛,ng:reserved_expansion:extensionpoints_11,[EXTENSIONPOINTS11],reserved_expansion,extensionpoints_11,95.0,A,False,True,True,True,[EXTENS..],extensionpoints_11,emoji_extended,False,Risky Unicode range: emoji_extended; Long fallback: 19 chars; Generic numbered name
NG2036,🥬,ng:reserved_expansion:extensionpoints_12,[EXTENSIONPOINTS12],reserved_expansion,extensionpoints_12,95.0,A,False,True,True,True,[EXTENS..],extensionpoints_12,emoji_extended,False,Risky Unicode range: emoji_extended; Long fallback: 19 chars; Generic numbered name
NG2037,🗏,ng:reserved_expansion:extensionpoints_13,[EXTENSIONPOINTS13],reserved_expansion,extensionpoints_13,95.0,A,False,True,True,True,[EXTENS..],extensionpoints_13,emoji_extended,False,Risky Unicode range: emoji_extended; Long fallback: 19 chars; Generic numbered name
NG2038,🏮,ng:reserved_expansion:extensionpoints_14,[EXTENSIONPOINTS14],reserved_expansion,extensionpoints_14,95.0,A,False,True,True,True,[EXTENS..],extensionpoints_14,emoji_extended,False,Risky Unicode range: emoji_extended; Long fallback: 19 chars; Generic numbered name
NG2039,🗠,ng:reserved_expansion:extensionpoints_15,[EXTENSIONPOINTS15],reserved_expansion,extensionpoints_15,95.0,A,False,True,True,True,[EXTENS..],extensionpoints_15,emoji_extended,False,Risky Unicode range: emoji_extended; Long fallback: 19 chars; Generic numbered name
NG2040,⬄,ng:reserved_expansion:extensionpoints_16,[EXTENSIONPOINTS16],reserved_expansion,extensionpoints_16,95.0,B,False,False,True,True,[EXTENS..],extensionpoints_16,safe,False,Long fallback: 19 chars; Generic numbered name
NG2041,🠢,ng:reserved_expansion:extensionpoints_17,[EXTENSIONPOINTS17],reserved_expansion,extensionpoints_17,95.0,A,False,True,True,True,[EXTENS..],extensionpoints_17,emoji_extended,False,Risky Unicode range: emoji_extended; Long fallback: 19 chars; Generic numbered name
NG2042,⨽,ng:reserved_expansion:extensionpoints_18,[EXTENSIONPOINTS18],reserved_expansion,extensionpoints_18,95.0,B,False,False,True,True,[EXTENS..],extensionpoints_18,safe,False,Long fallback: 19 chars; Generic numbered name
NG2043,⩌,ng:reserved_expansion:extensionpoints_19,[EXTENSIONPOINTS19],reserved_expansion,extensionpoints_19,95.0,B,False,False,True,True,[EXTENS..],extensionpoints_19,safe,False,Long fallback: 19 chars; Generic numbered name
NG2044,⣷,ng:reserved_expansion:extensionpoints_20,[EXTENSIONPOINTS20],reserved_expansion,extensionpoints_20,95.0,B,False,False,True,True,[EXTENS..],extensionpoints_20,safe,False,Long fallback: 19 chars; Generic numbered name
NG2045,⯃,ng:reserved_expansion:extensionpoints_21,[EXTENSIONPOINTS21],reserved_expansion,extensionpoints_21,95.0,B,False,False,True,True,[EXTENS..],extensionpoints_21,safe,False,Long fallback: 19 chars; Generic numbered name
NG2046,🌶,ng:reserved_expansion:extensionpoints_22,[EXTENSIONPOINTS22],reserved_expansion,extensionpoints_22,95.0,A,False,True,True,True,[EXTENS..],extensionpoints_22,emoji_extended,False,Risky Unicode range: emoji_extended; Long fallback: 19 chars; Generic numbered name
NG2047,🦕,ng:reserved_expansion:extensionpoints_23,[EXTENSIONPOINTS23],reserved_expansion,extensionpoints_23,95.0,A,False,True,True,True,[EXTENS..],extensionpoints_23,emoji_extended,False,Risky Unicode range: emoji_extended; Long fallback: 19 chars; Generic numbered name
NG2048,⫾,ng:reserved_expansion:extensionpoints_24,[EXTENSIONPOINTS24],reserved_expansion,extensionpoints_24,95.0,B,False,False,True,True,[EXTENS..],extensionpoints_24,safe,False,Long fallback: 19 chars; Generic numbered name
