{"symbols": [{"symbol": "⊕", "code": "ng:operator:add", "fallback": "[+]", "category": "operator", "meaning": "add", "llm_support": ["openai", "qwen", "deepseek", "llama.cpp"], "token_cost": 1, "token_density": 1.0, "examples": []}, {"symbol": "⊖", "code": "ng:operator:sub", "fallback": "[-]", "category": "operator", "meaning": "sub", "llm_support": ["openai", "qwen", "deepseek", "llama.cpp"], "token_cost": 1, "token_density": 1.0, "examples": []}, {"symbol": "⊗", "code": "ng:operator:mul", "fallback": "[*]", "category": "operator", "meaning": "mul", "llm_support": ["openai", "qwen", "deepseek", "llama.cpp"], "token_cost": 1, "token_density": 1.0, "examples": []}, {"symbol": "⊘", "code": "ng:operator:div", "fallback": "[/]", "category": "operator", "meaning": "div", "llm_support": ["openai", "qwen", "deepseek", "llama.cpp"], "token_cost": 1, "token_density": 1.0, "examples": []}, {"symbol": "≡", "code": "ng:operator:eq", "fallback": "[eq]", "category": "operator", "meaning": "eq", "llm_support": ["openai", "qwen", "deepseek", "llama.cpp"], "token_cost": 1, "token_density": 1.0, "examples": []}, {"symbol": "≢", "code": "ng:operator:ne", "fallback": "[ne]", "category": "operator", "meaning": "ne", "llm_support": ["openai", "qwen", "deepseek", "llama.cpp"], "token_cost": 1, "token_density": 1.0, "examples": []}, {"symbol": "∧", "code": "ng:operator:and", "fallback": "[and]", "category": "operator", "meaning": "and", "llm_support": ["openai", "qwen", "deepseek", "llama.cpp"], "token_cost": 1, "token_density": 1.0, "examples": []}, {"symbol": "∨", "code": "ng:operator:or", "fallback": "[or]", "category": "operator", "meaning": "or", "llm_support": ["openai", "qwen", "deepseek", "llama.cpp"], "token_cost": 1, "token_density": 1.0, "examples": []}, {"symbol": "¬", "code": "ng:operator:not", "fallback": "[not]", "category": "operator", "meaning": "not", "llm_support": ["openai", "qwen", "deepseek", "llama.cpp"], "token_cost": 1, "token_density": 1.0, "examples": []}, {"symbol": "∈", "code": "ng:operator:in", "fallback": "[in]", "category": "operator", "meaning": "in", "llm_support": ["openai", "qwen", "deepseek", "llama.cpp"], "token_cost": 1, "token_density": 1.0, "examples": []}, {"symbol": "∉", "code": "ng:operator:not_in", "fallback": "[!in]", "category": "operator", "meaning": "not_in", "llm_support": ["openai", "qwen", "deepseek", "llama.cpp"], "token_cost": 1, "token_density": 1.0, "examples": []}, {"symbol": "⊨", "code": "ng:logic:entailment", "fallback": "[models]", "category": "logic", "meaning": "entailment", "llm_support": ["openai", "qwen", "deepseek", "llama.cpp"], "token_cost": 1, "token_density": 1.0, "examples": []}, {"symbol": "⇌", "code": "ng:logic:equilibrium", "fallback": "[<=>]", "category": "logic", "meaning": "equilibrium", "llm_support": ["openai", "qwen", "deepseek", "llama.cpp"], "token_cost": 1, "token_density": 1.0, "examples": []}, {"symbol": "⟨⟩", "code": "ng:control:function", "fallback": "[fn]", "category": "control", "meaning": "function", "llm_support": ["openai", "qwen", "deepseek", "llama.cpp"], "token_cost": 1, "token_density": 1.0, "examples": []}, {"symbol": "⟪⟫", "code": "ng:control:class", "fallback": "[cls]", "category": "control", "meaning": "class", "llm_support": ["openai", "qwen", "deepseek", "llama.cpp"], "token_cost": 1, "token_density": 1.0, "examples": []}, {"symbol": "◊", "code": "ng:control:if", "fallback": "[if]", "category": "control", "meaning": "if", "llm_support": ["openai", "qwen", "deepseek", "llama.cpp"], "token_cost": 1, "token_density": 1.0, "examples": []}, {"symbol": "◈", "code": "ng:control:else", "fallback": "[else]", "category": "control", "meaning": "else", "llm_support": ["openai", "qwen", "deepseek", "llama.cpp"], "token_cost": 1, "token_density": 1.0, "examples": []}, {"symbol": "⟲", "code": "ng:control:for", "fallback": "[for]", "category": "control", "meaning": "for", "llm_support": ["openai", "qwen", "deepseek", "llama.cpp"], "token_cost": 1, "token_density": 1.0, "examples": []}, {"symbol": "⟳", "code": "ng:control:while", "fallback": "[while]", "category": "control", "meaning": "while", "llm_support": ["openai", "qwen", "deepseek", "llama.cpp"], "token_cost": 1, "token_density": 1.0, "examples": []}, {"symbol": "⤴", "code": "ng:control:return", "fallback": "[return]", "category": "control", "meaning": "return", "llm_support": ["openai", "qwen", "deepseek", "llama.cpp"], "token_cost": 1, "token_density": 1.0, "examples": []}, {"symbol": "⊰", "code": "ng:construct:async", "fallback": "[async]", "category": "construct", "meaning": "async", "llm_support": ["openai", "qwen", "deepseek", "llama.cpp"], "token_cost": 1, "token_density": 1.0, "examples": []}, {"symbol": "⊱", "code": "ng:construct:await", "fallback": "[await]", "category": "construct", "meaning": "await", "llm_support": ["openai", "qwen", "deepseek", "llama.cpp"], "token_cost": 1, "token_density": 1.0, "examples": []}, {"symbol": "⊲", "code": "ng:construct:generator", "fallback": "[gen]", "category": "construct", "meaning": "generator", "llm_support": ["openai", "qwen", "deepseek", "llama.cpp"], "token_cost": 1, "token_density": 1.0, "examples": []}, {"symbol": "⊳", "code": "ng:construct:decorator", "fallback": "[@]", "category": "construct", "meaning": "decorator", "llm_support": ["openai", "qwen", "deepseek", "llama.cpp"], "token_cost": 1, "token_density": 1.0, "examples": []}, {"symbol": "⊴", "code": "ng:construct:context", "fallback": "[ctx]", "category": "construct", "meaning": "context", "llm_support": ["openai", "qwen", "deepseek", "llama.cpp"], "token_cost": 1, "token_density": 1.0, "examples": []}, {"symbol": "⊸", "code": "ng:construct:static_method", "fallback": "[static]", "category": "construct", "meaning": "static_method", "llm_support": ["openai", "qwen", "deepseek", "llama.cpp"], "token_cost": 1, "token_density": 1.0, "examples": []}]}