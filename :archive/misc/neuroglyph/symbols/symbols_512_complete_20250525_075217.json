[{"id": "NG0001", "symbol": "⊕", "name": "fix", "description": "Fix concept in action - operation that modifies state", "category": "action", "aliases": ["repair", "correct", "resolve"], "status": "approved", "version": "1.0", "code": "ng:action:fix", "fallback": "[FIX]", "unicode_point": "U+2295"}, {"id": "NG0002", "symbol": "⊖", "name": "create", "description": "Create concept in action - operation that modifies state", "category": "action", "aliases": ["make", "build", "generate"], "status": "approved", "version": "1.0", "code": "ng:action:create", "fallback": "[CREATE]", "unicode_point": "U+2296"}, {"id": "NG0003", "symbol": "⊗", "name": "delete", "description": "Delete concept in action - operation that modifies state", "category": "action", "aliases": ["remove", "destroy", "eliminate"], "status": "approved", "version": "1.0", "code": "ng:action:delete", "fallback": "[DELETE]", "unicode_point": "U+2297"}, {"id": "NG0004", "symbol": "⊘", "name": "abort", "description": "Abort concept in action - operation that modifies state", "category": "action", "aliases": ["abort", "abort"], "status": "approved", "version": "1.0", "code": "ng:action:abort", "fallback": "[ABORT]", "unicode_point": "U+2298"}, {"id": "NG0005", "symbol": "⊙", "name": "start", "description": "Start concept in action - operation that modifies state", "category": "action", "aliases": ["start", "start"], "status": "approved", "version": "1.0", "code": "ng:action:start", "fallback": "[START]", "unicode_point": "U+2299"}, {"id": "NG0006", "symbol": "⊚", "name": "stop", "description": "Stop concept in action - operation that modifies state", "category": "action", "aliases": ["stop", "stop"], "status": "approved", "version": "1.0", "code": "ng:action:stop", "fallback": "[STOP]", "unicode_point": "U+229A"}, {"id": "NG0007", "symbol": "⊛", "name": "pause", "description": "Pause concept in action - operation that modifies state", "category": "action", "aliases": ["pause", "pause"], "status": "approved", "version": "1.0", "code": "ng:action:pause", "fallback": "[PAUSE]", "unicode_point": "U+229B"}, {"id": "NG0008", "symbol": "⊜", "name": "resume", "description": "Resume concept in action - operation that modifies state", "category": "action", "aliases": ["resume", "resume"], "status": "approved", "version": "1.0", "code": "ng:action:resume", "fallback": "[RESUME]", "unicode_point": "U+229C"}, {"id": "NG0009", "symbol": "⊝", "name": "reset", "description": "Reset concept in action - operation that modifies state", "category": "action", "aliases": ["reset", "reset"], "status": "approved", "version": "1.0", "code": "ng:action:reset", "fallback": "[RESET]", "unicode_point": "U+229D"}, {"id": "NG0010", "symbol": "⊞", "name": "clear", "description": "Clear concept in action - operation that modifies state", "category": "action", "aliases": ["clear", "clear"], "status": "approved", "version": "1.0", "code": "ng:action:clear", "fallback": "[CLEAR]", "unicode_point": "U+229E"}, {"id": "NG0011", "symbol": "⊟", "name": "copy", "description": "Copy concept in action - operation that modifies state", "category": "action", "aliases": ["copy", "copy"], "status": "approved", "version": "1.0", "code": "ng:action:copy", "fallback": "[COPY]", "unicode_point": "U+229F"}, {"id": "NG0012", "symbol": "⊠", "name": "move", "description": "Move concept in action - operation that modifies state", "category": "action", "aliases": ["move", "move"], "status": "approved", "version": "1.0", "code": "ng:action:move", "fallback": "[MOVE]", "unicode_point": "U+22A0"}, {"id": "NG0013", "symbol": "⊡", "name": "merge", "description": "Merge concept in action - operation that modifies state", "category": "action", "aliases": ["merge", "merge"], "status": "approved", "version": "1.0", "code": "ng:action:merge", "fallback": "[MERGE]", "unicode_point": "U+22A1"}, {"id": "NG0014", "symbol": "⊢", "name": "split", "description": "Split concept in action - operation that modifies state", "category": "action", "aliases": ["split", "split"], "status": "approved", "version": "1.0", "code": "ng:action:split", "fallback": "[SPLIT]", "unicode_point": "U+22A2"}, {"id": "NG0015", "symbol": "⊣", "name": "join", "description": "Join concept in action - operation that modifies state", "category": "action", "aliases": ["join", "join"], "status": "approved", "version": "1.0", "code": "ng:action:join", "fallback": "[JOIN]", "unicode_point": "U+22A3"}, {"id": "NG0016", "symbol": "⊤", "name": "separate", "description": "Separate concept in action - operation that modifies state", "category": "action", "aliases": ["separate", "separate"], "status": "approved", "version": "1.0", "code": "ng:action:separate", "fallback": "[SEPARATE]", "unicode_point": "U+22A4"}, {"id": "NG0017", "symbol": "⊥", "name": "combine", "description": "Combine concept in action - operation that modifies state", "category": "action", "aliases": ["combine", "combine"], "status": "approved", "version": "1.0", "code": "ng:action:combine", "fallback": "[COMBINE]", "unicode_point": "U+22A5"}, {"id": "NG0018", "symbol": "⊦", "name": "extract", "description": "Extract concept in action - operation that modifies state", "category": "action", "aliases": ["extract", "extract"], "status": "approved", "version": "1.0", "code": "ng:action:extract", "fallback": "[EXTRACT]", "unicode_point": "U+22A6"}, {"id": "NG0019", "symbol": "⊧", "name": "insert", "description": "Insert concept in action - operation that modifies state", "category": "action", "aliases": ["insert", "insert"], "status": "approved", "version": "1.0", "code": "ng:action:insert", "fallback": "[INSERT]", "unicode_point": "U+22A7"}, {"id": "NG0020", "symbol": "⊨", "name": "remove", "description": "Remove concept in action - operation that modifies state", "category": "action", "aliases": ["remove", "remove"], "status": "approved", "version": "1.0", "code": "ng:action:remove", "fallback": "[REMOVE]", "unicode_point": "U+22A8"}, {"id": "NG0021", "symbol": "⊩", "name": "update", "description": "Update concept in action - operation that modifies state", "category": "action", "aliases": ["update", "update"], "status": "approved", "version": "1.0", "code": "ng:action:update", "fallback": "[UPDATE]", "unicode_point": "U+22A9"}, {"id": "NG0022", "symbol": "⊪", "name": "modify", "description": "Modify concept in action - operation that modifies state", "category": "action", "aliases": ["modify", "modify"], "status": "approved", "version": "1.0", "code": "ng:action:modify", "fallback": "[MODIFY]", "unicode_point": "U+22AA"}, {"id": "NG0023", "symbol": "⊫", "name": "change", "description": "Change concept in action - operation that modifies state", "category": "action", "aliases": ["change", "change"], "status": "approved", "version": "1.0", "code": "ng:action:change", "fallback": "[CHANGE]", "unicode_point": "U+22AB"}, {"id": "NG0024", "symbol": "⊬", "name": "transform", "description": "Transform concept in action - operation that modifies state", "category": "action", "aliases": ["transform", "transform"], "status": "approved", "version": "1.0", "code": "ng:action:transform", "fallback": "[TRANSFORM]", "unicode_point": "U+22AC"}, {"id": "NG0025", "symbol": "⊭", "name": "convert", "description": "Convert concept in action - operation that modifies state", "category": "action", "aliases": ["convert", "convert"], "status": "approved", "version": "1.0", "code": "ng:action:convert", "fallback": "[CONVERT]", "unicode_point": "U+22AD"}, {"id": "NG0026", "symbol": "⊮", "name": "translate", "description": "Translate concept in action - operation that modifies state", "category": "action", "aliases": ["translate", "translate"], "status": "approved", "version": "1.0", "code": "ng:action:translate", "fallback": "[TRANSLATE]", "unicode_point": "U+22AE"}, {"id": "NG0027", "symbol": "⊯", "name": "encode", "description": "Encode concept in action - operation that modifies state", "category": "action", "aliases": ["encode", "encode"], "status": "approved", "version": "1.0", "code": "ng:action:encode", "fallback": "[ENCODE]", "unicode_point": "U+22AF"}, {"id": "NG0028", "symbol": "⊰", "name": "decode", "description": "Decode concept in action - operation that modifies state", "category": "action", "aliases": ["decode", "decode"], "status": "approved", "version": "1.0", "code": "ng:action:decode", "fallback": "[DECODE]", "unicode_point": "U+22B0"}, {"id": "NG0029", "symbol": "⊱", "name": "compress", "description": "Compress concept in action - operation that modifies state", "category": "action", "aliases": ["compress", "compress"], "status": "approved", "version": "1.0", "code": "ng:action:compress", "fallback": "[COMPRESS]", "unicode_point": "U+22B1"}, {"id": "NG0030", "symbol": "⊲", "name": "expand", "description": "Expand concept in action - operation that modifies state", "category": "action", "aliases": ["expand", "expand"], "status": "approved", "version": "1.0", "code": "ng:action:expand", "fallback": "[EXPAND]", "unicode_point": "U+22B2"}, {"id": "NG0031", "symbol": "⊳", "name": "validate", "description": "Validate concept in action - operation that modifies state", "category": "action", "aliases": ["validate", "validate"], "status": "approved", "version": "1.0", "code": "ng:action:validate", "fallback": "[VALIDATE]", "unicode_point": "U+22B3"}, {"id": "NG0032", "symbol": "⊴", "name": "verify", "description": "Verify concept in action - operation that modifies state", "category": "action", "aliases": ["verify", "verify"], "status": "approved", "version": "1.0", "code": "ng:action:verify", "fallback": "[VERIFY]", "unicode_point": "U+22B4"}, {"id": "NG0033", "symbol": "⊵", "name": "check", "description": "Check concept in action - operation that modifies state", "category": "action", "aliases": ["check", "check"], "status": "approved", "version": "1.0", "code": "ng:action:check", "fallback": "[CHECK]", "unicode_point": "U+22B5"}, {"id": "NG0034", "symbol": "⊶", "name": "test", "description": "Test concept in action - operation that modifies state", "category": "action", "aliases": ["test", "test"], "status": "approved", "version": "1.0", "code": "ng:action:test", "fallback": "[TEST]", "unicode_point": "U+22B6"}, {"id": "NG0035", "symbol": "⊷", "name": "debug", "description": "Debug concept in action - operation that modifies state", "category": "action", "aliases": ["debug", "debug"], "status": "approved", "version": "1.0", "code": "ng:action:debug", "fallback": "[DEBUG]", "unicode_point": "U+22B7"}, {"id": "NG0036", "symbol": "⊸", "name": "trace", "description": "Trace concept in action - operation that modifies state", "category": "action", "aliases": ["trace", "trace"], "status": "approved", "version": "1.0", "code": "ng:action:trace", "fallback": "[TRACE]", "unicode_point": "U+22B8"}, {"id": "NG0037", "symbol": "⊹", "name": "monitor", "description": "Monitor concept in action - operation that modifies state", "category": "action", "aliases": ["monitor", "monitor"], "status": "approved", "version": "1.0", "code": "ng:action:monitor", "fallback": "[MONITOR]", "unicode_point": "U+22B9"}, {"id": "NG0038", "symbol": "⊺", "name": "observe", "description": "Observe concept in action - operation that modifies state", "category": "action", "aliases": ["observe", "observe"], "status": "approved", "version": "1.0", "code": "ng:action:observe", "fallback": "[OBSERVE]", "unicode_point": "U+22BA"}, {"id": "NG0039", "symbol": "⊻", "name": "measure", "description": "Measure concept in action - operation that modifies state", "category": "action", "aliases": ["measure", "measure"], "status": "approved", "version": "1.0", "code": "ng:action:measure", "fallback": "[MEASURE]", "unicode_point": "U+22BB"}, {"id": "NG0040", "symbol": "⊼", "name": "count", "description": "Count concept in action - operation that modifies state", "category": "action", "aliases": ["count", "count"], "status": "approved", "version": "1.0", "code": "ng:action:count", "fallback": "[COUNT]", "unicode_point": "U+22BC"}, {"id": "NG0041", "symbol": "⊽", "name": "search", "description": "Search concept in action - operation that modifies state", "category": "action", "aliases": ["search", "search"], "status": "approved", "version": "1.0", "code": "ng:action:search", "fallback": "[SEARCH]", "unicode_point": "U+22BD"}, {"id": "NG0042", "symbol": "→", "name": "find", "description": "Find concept in action - operation that modifies state", "category": "action", "aliases": ["find", "find"], "status": "approved", "version": "1.0", "code": "ng:action:find", "fallback": "[FIND]", "unicode_point": "U+2192"}, {"id": "NG0043", "symbol": "←", "name": "locate", "description": "Locate concept in action - operation that modifies state", "category": "action", "aliases": ["locate", "locate"], "status": "approved", "version": "1.0", "code": "ng:action:locate", "fallback": "[LOCATE]", "unicode_point": "U+2190"}, {"id": "NG0044", "symbol": "↑", "name": "function", "description": "Function concept in structure - organizational construct", "category": "structure", "aliases": ["method", "procedure", "routine"], "status": "approved", "version": "1.0", "code": "ng:structure:function", "fallback": "[FUNCTION]", "unicode_point": "U+2191"}, {"id": "NG0045", "symbol": "↓", "name": "class", "description": "Class concept in structure - organizational construct", "category": "structure", "aliases": ["type", "template", "blueprint"], "status": "approved", "version": "1.0", "code": "ng:structure:class", "fallback": "[CLASS]", "unicode_point": "U+2193"}, {"id": "NG0046", "symbol": "↔", "name": "block", "description": "Block concept in structure - organizational construct", "category": "structure", "aliases": ["block", "block"], "status": "approved", "version": "1.0", "code": "ng:structure:block", "fallback": "[BLOCK]", "unicode_point": "U+2194"}, {"id": "NG0047", "symbol": "↕", "name": "module", "description": "Module concept in structure - organizational construct", "category": "structure", "aliases": ["module", "module"], "status": "approved", "version": "1.0", "code": "ng:structure:module", "fallback": "[MODULE]", "unicode_point": "U+2195"}, {"id": "NG0048", "symbol": "↖", "name": "package", "description": "Package concept in structure - organizational construct", "category": "structure", "aliases": ["package", "package"], "status": "approved", "version": "1.0", "code": "ng:structure:package", "fallback": "[PACKAGE]", "unicode_point": "U+2196"}, {"id": "NG0049", "symbol": "↗", "name": "namespace", "description": "Namespace concept in structure - organizational construct", "category": "structure", "aliases": ["namespace", "namespace"], "status": "approved", "version": "1.0", "code": "ng:structure:namespace", "fallback": "[NAMESPACE]", "unicode_point": "U+2197"}, {"id": "NG0050", "symbol": "↘", "name": "scope", "description": "Scope concept in structure - organizational construct", "category": "structure", "aliases": ["scope", "scope"], "status": "approved", "version": "1.0", "code": "ng:structure:scope", "fallback": "[SCOPE]", "unicode_point": "U+2198"}, {"id": "NG0051", "symbol": "↙", "name": "context", "description": "Context concept in structure - organizational construct", "category": "structure", "aliases": ["context", "context"], "status": "approved", "version": "1.0", "code": "ng:structure:context", "fallback": "[CONTEXT]", "unicode_point": "U+2199"}, {"id": "NG0052", "symbol": "↚", "name": "frame", "description": "Frame concept in structure - organizational construct", "category": "structure", "aliases": ["frame", "frame"], "status": "approved", "version": "1.0", "code": "ng:structure:frame", "fallback": "[FRAME]", "unicode_point": "U+219A"}, {"id": "NG0053", "symbol": "↛", "name": "stack", "description": "Stack concept in structure - organizational construct", "category": "structure", "aliases": ["stack", "stack"], "status": "approved", "version": "1.0", "code": "ng:structure:stack", "fallback": "[STACK]", "unicode_point": "U+219B"}, {"id": "NG0054", "symbol": "↜", "name": "queue", "description": "Queue concept in structure - organizational construct", "category": "structure", "aliases": ["queue", "queue"], "status": "approved", "version": "1.0", "code": "ng:structure:queue", "fallback": "[QUEUE]", "unicode_point": "U+219C"}, {"id": "NG0055", "symbol": "↝", "name": "list", "description": "List concept in structure - organizational construct", "category": "structure", "aliases": ["list", "list"], "status": "approved", "version": "1.0", "code": "ng:structure:list", "fallback": "[LIST]", "unicode_point": "U+219D"}, {"id": "NG0056", "symbol": "↞", "name": "array", "description": "Array concept in structure - organizational construct", "category": "structure", "aliases": ["array", "array"], "status": "approved", "version": "1.0", "code": "ng:structure:array", "fallback": "[ARRAY]", "unicode_point": "U+219E"}, {"id": "NG0057", "symbol": "↟", "name": "vector", "description": "Vector concept in structure - organizational construct", "category": "structure", "aliases": ["vector", "vector"], "status": "approved", "version": "1.0", "code": "ng:structure:vector", "fallback": "[VECTOR]", "unicode_point": "U+219F"}, {"id": "NG0058", "symbol": "↠", "name": "matrix", "description": "Matrix concept in structure - organizational construct", "category": "structure", "aliases": ["matrix", "matrix"], "status": "approved", "version": "1.0", "code": "ng:structure:matrix", "fallback": "[MATRIX]", "unicode_point": "U+21A0"}, {"id": "NG0059", "symbol": "↡", "name": "table", "description": "Table concept in structure - organizational construct", "category": "structure", "aliases": ["table", "table"], "status": "approved", "version": "1.0", "code": "ng:structure:table", "fallback": "[TABLE]", "unicode_point": "U+21A1"}, {"id": "NG0060", "symbol": "↢", "name": "tree", "description": "Tree concept in structure - organizational construct", "category": "structure", "aliases": ["tree", "tree"], "status": "approved", "version": "1.0", "code": "ng:structure:tree", "fallback": "[TREE]", "unicode_point": "U+21A2"}, {"id": "NG0061", "symbol": "↣", "name": "graph", "description": "Graph concept in structure - organizational construct", "category": "structure", "aliases": ["graph", "graph"], "status": "approved", "version": "1.0", "code": "ng:structure:graph", "fallback": "[GRAPH]", "unicode_point": "U+21A3"}, {"id": "NG0062", "symbol": "↤", "name": "node", "description": "Node concept in structure - organizational construct", "category": "structure", "aliases": ["node", "node"], "status": "approved", "version": "1.0", "code": "ng:structure:node", "fallback": "[NODE]", "unicode_point": "U+21A4"}, {"id": "NG0063", "symbol": "↥", "name": "edge", "description": "Edge concept in structure - organizational construct", "category": "structure", "aliases": ["edge", "edge"], "status": "approved", "version": "1.0", "code": "ng:structure:edge", "fallback": "[EDGE]", "unicode_point": "U+21A5"}, {"id": "NG0064", "symbol": "↦", "name": "interface", "description": "Interface concept in structure - organizational construct", "category": "structure", "aliases": ["interface", "interface"], "status": "approved", "version": "1.0", "code": "ng:structure:interface", "fallback": "[INTERFACE]", "unicode_point": "U+21A6"}, {"id": "NG0065", "symbol": "↧", "name": "protocol", "description": "Protocol concept in structure - organizational construct", "category": "structure", "aliases": ["protocol", "protocol"], "status": "approved", "version": "1.0", "code": "ng:structure:protocol", "fallback": "[PROTOCOL]", "unicode_point": "U+21A7"}, {"id": "NG0066", "symbol": "↨", "name": "contract", "description": "Contract concept in structure - organizational construct", "category": "structure", "aliases": ["contract", "contract"], "status": "approved", "version": "1.0", "code": "ng:structure:contract", "fallback": "[CONTRACT]", "unicode_point": "U+21A8"}, {"id": "NG0067", "symbol": "↩", "name": "schema", "description": "Schema concept in structure - organizational construct", "category": "structure", "aliases": ["schema", "schema"], "status": "approved", "version": "1.0", "code": "ng:structure:schema", "fallback": "[SCHEMA]", "unicode_point": "U+21A9"}, {"id": "NG0068", "symbol": "↪", "name": "template", "description": "Template concept in structure - organizational construct", "category": "structure", "aliases": ["template", "template"], "status": "approved", "version": "1.0", "code": "ng:structure:template", "fallback": "[TEMPLATE]", "unicode_point": "U+21AA"}, {"id": "NG0069", "symbol": "↫", "name": "pattern", "description": "Pattern concept in structure - organizational construct", "category": "structure", "aliases": ["pattern", "pattern"], "status": "approved", "version": "1.0", "code": "ng:structure:pattern", "fallback": "[PATTERN]", "unicode_point": "U+21AB"}, {"id": "NG0070", "symbol": "↬", "name": "blueprint", "description": "Blueprint concept in structure - organizational construct", "category": "structure", "aliases": ["blueprint", "blueprint"], "status": "approved", "version": "1.0", "code": "ng:structure:blueprint", "fallback": "[BLUEPRINT]", "unicode_point": "U+21AC"}, {"id": "NG0071", "symbol": "↭", "name": "model", "description": "Model concept in structure - organizational construct", "category": "structure", "aliases": ["model", "model"], "status": "approved", "version": "1.0", "code": "ng:structure:model", "fallback": "[MODEL]", "unicode_point": "U+21AD"}, {"id": "NG0072", "symbol": "↮", "name": "view", "description": "View concept in structure - organizational construct", "category": "structure", "aliases": ["view", "view"], "status": "approved", "version": "1.0", "code": "ng:structure:view", "fallback": "[VIEW]", "unicode_point": "U+21AE"}, {"id": "NG0073", "symbol": "↯", "name": "controller", "description": "Controller concept in structure - organizational construct", "category": "structure", "aliases": ["controller", "controller"], "status": "approved", "version": "1.0", "code": "ng:structure:controller", "fallback": "[CONTROLLER]", "unicode_point": "U+21AF"}, {"id": "NG0074", "symbol": "⇀", "name": "component", "description": "Component concept in structure - organizational construct", "category": "structure", "aliases": ["component", "component"], "status": "approved", "version": "1.0", "code": "ng:structure:component", "fallback": "[COMPONENT]", "unicode_point": "U+21C0"}, {"id": "NG0075", "symbol": "⇁", "name": "element", "description": "Element concept in structure - organizational construct", "category": "structure", "aliases": ["element", "element"], "status": "approved", "version": "1.0", "code": "ng:structure:element", "fallback": "[ELEMENT]", "unicode_point": "U+21C1"}, {"id": "NG0076", "symbol": "⇂", "name": "widget", "description": "Widget concept in structure - organizational construct", "category": "structure", "aliases": ["widget", "widget"], "status": "approved", "version": "1.0", "code": "ng:structure:widget", "fallback": "[WIDGET]", "unicode_point": "U+21C2"}, {"id": "NG0077", "symbol": "⇃", "name": "control", "description": "Control concept in structure - organizational construct", "category": "structure", "aliases": ["control", "control"], "status": "approved", "version": "1.0", "code": "ng:structure:control", "fallback": "[CONTROL]", "unicode_point": "U+21C3"}, {"id": "NG0078", "symbol": "⇄", "name": "panel", "description": "Panel concept in structure - organizational construct", "category": "structure", "aliases": ["panel", "panel"], "status": "approved", "version": "1.0", "code": "ng:structure:panel", "fallback": "[PANEL]", "unicode_point": "U+21C4"}, {"id": "NG0079", "symbol": "⇅", "name": "window", "description": "Window concept in structure - organizational construct", "category": "structure", "aliases": ["window", "window"], "status": "approved", "version": "1.0", "code": "ng:structure:window", "fallback": "[WINDOW]", "unicode_point": "U+21C5"}, {"id": "NG0080", "symbol": "⇆", "name": "dialog", "description": "Dialog concept in structure - organizational construct", "category": "structure", "aliases": ["dialog", "dialog"], "status": "approved", "version": "1.0", "code": "ng:structure:dialog", "fallback": "[DIALOG]", "unicode_point": "U+21C6"}, {"id": "NG0081", "symbol": "⇇", "name": "form", "description": "Form concept in structure - organizational construct", "category": "structure", "aliases": ["form", "form"], "status": "approved", "version": "1.0", "code": "ng:structure:form", "fallback": "[FORM]", "unicode_point": "U+21C7"}, {"id": "NG0082", "symbol": "⇈", "name": "field", "description": "Field concept in structure - organizational construct", "category": "structure", "aliases": ["field", "field"], "status": "approved", "version": "1.0", "code": "ng:structure:field", "fallback": "[FIELD]", "unicode_point": "U+21C8"}, {"id": "NG0083", "symbol": "⇉", "name": "input", "description": "Input concept in structure - organizational construct", "category": "structure", "aliases": ["input", "input"], "status": "approved", "version": "1.0", "code": "ng:structure:input", "fallback": "[INPUT]", "unicode_point": "U+21C9"}, {"id": "NG0084", "symbol": "⇊", "name": "output", "description": "Output concept in structure - organizational construct", "category": "structure", "aliases": ["output", "output"], "status": "approved", "version": "1.0", "code": "ng:structure:output", "fallback": "[OUTPUT]", "unicode_point": "U+21CA"}, {"id": "NG0085", "symbol": "⇋", "name": "stream", "description": "Stream concept in structure - organizational construct", "category": "structure", "aliases": ["stream", "stream"], "status": "approved", "version": "1.0", "code": "ng:structure:stream", "fallback": "[STREAM]", "unicode_point": "U+21CB"}, {"id": "NG0086", "symbol": "⇌", "name": "buffer", "description": "Buffer concept in structure - organizational construct", "category": "structure", "aliases": ["buffer", "buffer"], "status": "approved", "version": "1.0", "code": "ng:structure:buffer", "fallback": "[BUFFER]", "unicode_point": "U+21CC"}, {"id": "NG0087", "symbol": "⇍", "name": "error", "description": "Error concept in state - condition or status indicator", "category": "state", "aliases": ["failure", "exception", "crash"], "status": "approved", "version": "1.0", "code": "ng:state:error", "fallback": "[ERROR]", "unicode_point": "U+21CD"}, {"id": "NG0088", "symbol": "⇎", "name": "warning", "description": "Warning concept in state - condition or status indicator", "category": "state", "aliases": ["warning", "warning"], "status": "approved", "version": "1.0", "code": "ng:state:warning", "fallback": "[WARNING]", "unicode_point": "U+21CE"}, {"id": "NG0089", "symbol": "⇏", "name": "success", "description": "Success concept in state - condition or status indicator", "category": "state", "aliases": ["complete", "done", "finished"], "status": "approved", "version": "1.0", "code": "ng:state:success", "fallback": "[SUCCESS]", "unicode_point": "U+21CF"}, {"id": "NG0090", "symbol": "⤴", "name": "null", "description": "Null concept in state - condition or status indicator", "category": "state", "aliases": ["null", "null"], "status": "approved", "version": "1.0", "code": "ng:state:null", "fallback": "[NULL]", "unicode_point": "U+2934"}, {"id": "NG0091", "symbol": "⤵", "name": "empty", "description": "Empty concept in state - condition or status indicator", "category": "state", "aliases": ["empty", "empty"], "status": "approved", "version": "1.0", "code": "ng:state:empty", "fallback": "[EMPTY]", "unicode_point": "U+2935"}, {"id": "NG0092", "symbol": "⤶", "name": "full", "description": "Full concept in state - condition or status indicator", "category": "state", "aliases": ["full", "full"], "status": "approved", "version": "1.0", "code": "ng:state:full", "fallback": "[FULL]", "unicode_point": "U+2936"}, {"id": "NG0093", "symbol": "⤷", "name": "ready", "description": "Ready concept in state - condition or status indicator", "category": "state", "aliases": ["ready", "ready"], "status": "approved", "version": "1.0", "code": "ng:state:ready", "fallback": "[READY]", "unicode_point": "U+2937"}, {"id": "NG0094", "symbol": "⤸", "name": "busy", "description": "Busy concept in state - condition or status indicator", "category": "state", "aliases": ["busy", "busy"], "status": "approved", "version": "1.0", "code": "ng:state:busy", "fallback": "[BUSY]", "unicode_point": "U+2938"}, {"id": "NG0095", "symbol": "⤹", "name": "idle", "description": "Idle concept in state - condition or status indicator", "category": "state", "aliases": ["idle", "idle"], "status": "approved", "version": "1.0", "code": "ng:state:idle", "fallback": "[IDLE]", "unicode_point": "U+2939"}, {"id": "NG0096", "symbol": "⤺", "name": "active", "description": "Active concept in state - condition or status indicator", "category": "state", "aliases": ["active", "active"], "status": "approved", "version": "1.0", "code": "ng:state:active", "fallback": "[ACTIVE]", "unicode_point": "U+293A"}, {"id": "NG0097", "symbol": "⤻", "name": "inactive", "description": "Inactive concept in state - condition or status indicator", "category": "state", "aliases": ["inactive", "inactive"], "status": "approved", "version": "1.0", "code": "ng:state:inactive", "fallback": "[INACTIVE]", "unicode_point": "U+293B"}, {"id": "NG0098", "symbol": "⤼", "name": "enabled", "description": "Enabled concept in state - condition or status indicator", "category": "state", "aliases": ["enabled", "enabled"], "status": "approved", "version": "1.0", "code": "ng:state:enabled", "fallback": "[ENABLED]", "unicode_point": "U+293C"}, {"id": "NG0099", "symbol": "⤽", "name": "disabled", "description": "Disabled concept in state - condition or status indicator", "category": "state", "aliases": ["disabled", "disabled"], "status": "approved", "version": "1.0", "code": "ng:state:disabled", "fallback": "[DISABLED]", "unicode_point": "U+293D"}, {"id": "NG0100", "symbol": "⤾", "name": "visible", "description": "Visible concept in state - condition or status indicator", "category": "state", "aliases": ["visible", "visible"], "status": "approved", "version": "1.0", "code": "ng:state:visible", "fallback": "[VISIBLE]", "unicode_point": "U+293E"}, {"id": "NG0101", "symbol": "⤿", "name": "hidden", "description": "Hidden concept in state - condition or status indicator", "category": "state", "aliases": ["hidden", "hidden"], "status": "approved", "version": "1.0", "code": "ng:state:hidden", "fallback": "[HIDDEN]", "unicode_point": "U+293F"}, {"id": "NG0102", "symbol": "⥀", "name": "open", "description": "Open concept in state - condition or status indicator", "category": "state", "aliases": ["open", "open"], "status": "approved", "version": "1.0", "code": "ng:state:open", "fallback": "[OPEN]", "unicode_point": "U+2940"}, {"id": "NG0103", "symbol": "⥁", "name": "closed", "description": "Closed concept in state - condition or status indicator", "category": "state", "aliases": ["closed", "closed"], "status": "approved", "version": "1.0", "code": "ng:state:closed", "fallback": "[CLOSED]", "unicode_point": "U+2941"}, {"id": "NG0104", "symbol": "⥂", "name": "locked", "description": "Locked concept in state - condition or status indicator", "category": "state", "aliases": ["locked", "locked"], "status": "approved", "version": "1.0", "code": "ng:state:locked", "fallback": "[LOCKED]", "unicode_point": "U+2942"}, {"id": "NG0105", "symbol": "⥃", "name": "unlocked", "description": "Unlocked concept in state - condition or status indicator", "category": "state", "aliases": ["unlocked", "unlocked"], "status": "approved", "version": "1.0", "code": "ng:state:unlocked", "fallback": "[UNLOCKED]", "unicode_point": "U+2943"}, {"id": "NG0106", "symbol": "◊", "name": "secure", "description": "Secure concept in state - condition or status indicator", "category": "state", "aliases": ["secure", "secure"], "status": "approved", "version": "1.0", "code": "ng:state:secure", "fallback": "[SECURE]", "unicode_point": "U+25CA"}, {"id": "NG0107", "symbol": "◈", "name": "valid", "description": "Valid concept in state - condition or status indicator", "category": "state", "aliases": ["valid", "valid"], "status": "approved", "version": "1.0", "code": "ng:state:valid", "fallback": "[VALID]", "unicode_point": "U+25C8"}, {"id": "NG0108", "symbol": "◉", "name": "invalid", "description": "Invalid concept in state - condition or status indicator", "category": "state", "aliases": ["invalid", "invalid"], "status": "approved", "version": "1.0", "code": "ng:state:invalid", "fallback": "[INVALID]", "unicode_point": "U+25C9"}, {"id": "NG0109", "symbol": "◎", "name": "dirty", "description": "Dirty concept in state - condition or status indicator", "category": "state", "aliases": ["dirty", "dirty"], "status": "approved", "version": "1.0", "code": "ng:state:dirty", "fallback": "[DIRTY]", "unicode_point": "U+25CE"}, {"id": "NG0110", "symbol": "●", "name": "clean", "description": "Clean concept in state - condition or status indicator", "category": "state", "aliases": ["clean", "clean"], "status": "approved", "version": "1.0", "code": "ng:state:clean", "fallback": "[CLEAN]", "unicode_point": "U+25CF"}, {"id": "NG0111", "symbol": "◐", "name": "fresh", "description": "Fresh concept in state - condition or status indicator", "category": "state", "aliases": ["fresh", "fresh"], "status": "approved", "version": "1.0", "code": "ng:state:fresh", "fallback": "[FRESH]", "unicode_point": "U+25D0"}, {"id": "NG0112", "symbol": "◑", "name": "stale", "description": "Stale concept in state - condition or status indicator", "category": "state", "aliases": ["stale", "stale"], "status": "approved", "version": "1.0", "code": "ng:state:stale", "fallback": "[STALE]", "unicode_point": "U+25D1"}, {"id": "NG0113", "symbol": "◒", "name": "new", "description": "New concept in state - condition or status indicator", "category": "state", "aliases": ["new", "new"], "status": "approved", "version": "1.0", "code": "ng:state:new", "fallback": "[NEW]", "unicode_point": "U+25D2"}, {"id": "NG0114", "symbol": "◓", "name": "old", "description": "Old concept in state - condition or status indicator", "category": "state", "aliases": ["old", "old"], "status": "approved", "version": "1.0", "code": "ng:state:old", "fallback": "[OLD]", "unicode_point": "U+25D3"}, {"id": "NG0115", "symbol": "◔", "name": "current", "description": "Current concept in state - condition or status indicator", "category": "state", "aliases": ["current", "current"], "status": "approved", "version": "1.0", "code": "ng:state:current", "fallback": "[CURRENT]", "unicode_point": "U+25D4"}, {"id": "NG0116", "symbol": "◕", "name": "previous", "description": "Previous concept in state - condition or status indicator", "category": "state", "aliases": ["previous", "previous"], "status": "approved", "version": "1.0", "code": "ng:state:previous", "fallback": "[PREVIOUS]", "unicode_point": "U+25D5"}, {"id": "NG0117", "symbol": "◖", "name": "next", "description": "Next concept in state - condition or status indicator", "category": "state", "aliases": ["next", "next"], "status": "approved", "version": "1.0", "code": "ng:state:next", "fallback": "[NEXT]", "unicode_point": "U+25D6"}, {"id": "NG0118", "symbol": "◗", "name": "first", "description": "First concept in state - condition or status indicator", "category": "state", "aliases": ["first", "first"], "status": "approved", "version": "1.0", "code": "ng:state:first", "fallback": "[FIRST]", "unicode_point": "U+25D7"}, {"id": "NG0119", "symbol": "◘", "name": "last", "description": "Last concept in state - condition or status indicator", "category": "state", "aliases": ["last", "last"], "status": "approved", "version": "1.0", "code": "ng:state:last", "fallback": "[LAST]", "unicode_point": "U+25D8"}, {"id": "NG0120", "symbol": "◙", "name": "initial", "description": "Initial concept in state - condition or status indicator", "category": "state", "aliases": ["initial", "initial"], "status": "approved", "version": "1.0", "code": "ng:state:initial", "fallback": "[INITIAL]", "unicode_point": "U+25D9"}, {"id": "NG0121", "symbol": "◚", "name": "final", "description": "Final concept in state - condition or status indicator", "category": "state", "aliases": ["final", "final"], "status": "approved", "version": "1.0", "code": "ng:state:final", "fallback": "[FINAL]", "unicode_point": "U+25DA"}, {"id": "NG0122", "symbol": "◛", "name": "temporary", "description": "Temporary concept in state - condition or status indicator", "category": "state", "aliases": ["temporary", "temporary"], "status": "approved", "version": "1.0", "code": "ng:state:temporary", "fallback": "[TEMPORARY]", "unicode_point": "U+25DB"}, {"id": "NG0123", "symbol": "◜", "name": "permanent", "description": "Permanent concept in state - condition or status indicator", "category": "state", "aliases": ["permanent", "permanent"], "status": "approved", "version": "1.0", "code": "ng:state:permanent", "fallback": "[PERMANENT]", "unicode_point": "U+25DC"}, {"id": "NG0124", "symbol": "◝", "name": "volatile", "description": "Volatile concept in state - condition or status indicator", "category": "state", "aliases": ["volatile", "volatile"], "status": "approved", "version": "1.0", "code": "ng:state:volatile", "fallback": "[VOLATILE]", "unicode_point": "U+25DD"}, {"id": "NG0125", "symbol": "◞", "name": "stable", "description": "Stable concept in state - condition or status indicator", "category": "state", "aliases": ["stable", "stable"], "status": "approved", "version": "1.0", "code": "ng:state:stable", "fallback": "[STABLE]", "unicode_point": "U+25DE"}, {"id": "NG0126", "symbol": "◟", "name": "unstable", "description": "Unstable concept in state - condition or status indicator", "category": "state", "aliases": ["unstable", "unstable"], "status": "approved", "version": "1.0", "code": "ng:state:unstable", "fallback": "[UNSTABLE]", "unicode_point": "U+25DF"}, {"id": "NG0127", "symbol": "◠", "name": "connected", "description": "Connected concept in state - condition or status indicator", "category": "state", "aliases": ["connected", "connected"], "status": "approved", "version": "1.0", "code": "ng:state:connected", "fallback": "[CONNECTED]", "unicode_point": "U+25E0"}, {"id": "NG0128", "symbol": "◡", "name": "disconnected", "description": "Disconnected concept in state - condition or status indicator", "category": "state", "aliases": ["disconnected", "disconnected"], "status": "approved", "version": "1.0", "code": "ng:state:disconnected", "fallback": "[DISCONNECTED]", "unicode_point": "U+25E1"}, {"id": "NG0129", "symbol": "◢", "name": "online", "description": "Online concept in state - condition or status indicator", "category": "state", "aliases": ["online", "online"], "status": "approved", "version": "1.0", "code": "ng:state:online", "fallback": "[ONLINE]", "unicode_point": "U+25E2"}, {"id": "NG0130", "symbol": "◣", "name": "and", "description": "And concept in logic - logical relationship or operation", "category": "logic", "aliases": ["and", "and"], "status": "approved", "version": "1.0", "code": "ng:logic:and", "fallback": "[AND]", "unicode_point": "U+25E3"}, {"id": "NG0131", "symbol": "◤", "name": "or", "description": "Or concept in logic - logical relationship or operation", "category": "logic", "aliases": ["or", "or"], "status": "approved", "version": "1.0", "code": "ng:logic:or", "fallback": "[OR]", "unicode_point": "U+25E4"}, {"id": "NG0132", "symbol": "◥", "name": "not", "description": "Not concept in logic - logical relationship or operation", "category": "logic", "aliases": ["not", "not"], "status": "approved", "version": "1.0", "code": "ng:logic:not", "fallback": "[NOT]", "unicode_point": "U+25E5"}, {"id": "NG0133", "symbol": "◦", "name": "implies", "description": "Implies concept in logic - logical relationship or operation", "category": "logic", "aliases": ["implies", "implies"], "status": "approved", "version": "1.0", "code": "ng:logic:implies", "fallback": "[IMPLIES]", "unicode_point": "U+25E6"}, {"id": "NG0134", "symbol": "◧", "name": "equals", "description": "Equals concept in logic - logical relationship or operation", "category": "logic", "aliases": ["equals", "equals"], "status": "approved", "version": "1.0", "code": "ng:logic:equals", "fallback": "[EQUALS]", "unicode_point": "U+25E7"}, {"id": "NG0135", "symbol": "◨", "name": "entails", "description": "Entails concept in logic - logical relationship or operation", "category": "logic", "aliases": ["entails", "entails"], "status": "approved", "version": "1.0", "code": "ng:logic:entails", "fallback": "[ENTAILS]", "unicode_point": "U+25E8"}, {"id": "NG0136", "symbol": "◩", "name": "biconditional", "description": "Biconditional concept in logic - logical relationship or operation", "category": "logic", "aliases": ["biconditional", "biconditional"], "status": "approved", "version": "1.0", "code": "ng:logic:biconditional", "fallback": "[BICONDITIONAL]", "unicode_point": "U+25E9"}, {"id": "NG0137", "symbol": "◪", "name": "subset", "description": "Subset concept in logic - logical relationship or operation", "category": "logic", "aliases": ["subset", "subset"], "status": "approved", "version": "1.0", "code": "ng:logic:subset", "fallback": "[SUBSET]", "unicode_point": "U+25EA"}, {"id": "NG0138", "symbol": "◫", "name": "superset", "description": "Superset concept in logic - logical relationship or operation", "category": "logic", "aliases": ["superset", "superset"], "status": "approved", "version": "1.0", "code": "ng:logic:superset", "fallback": "[SUPERSET]", "unicode_point": "U+25EB"}, {"id": "NG0139", "symbol": "◬", "name": "intersection", "description": "Intersection concept in logic - logical relationship or operation", "category": "logic", "aliases": ["intersection", "intersection"], "status": "approved", "version": "1.0", "code": "ng:logic:intersection", "fallback": "[INTERSECTION]", "unicode_point": "U+25EC"}, {"id": "NG0140", "symbol": "◭", "name": "union", "description": "Union concept in logic - logical relationship or operation", "category": "logic", "aliases": ["union", "union"], "status": "approved", "version": "1.0", "code": "ng:logic:union", "fallback": "[UNION]", "unicode_point": "U+25ED"}, {"id": "NG0141", "symbol": "◮", "name": "true", "description": "True concept in logic - logical relationship or operation", "category": "logic", "aliases": ["true", "true"], "status": "approved", "version": "1.0", "code": "ng:logic:true", "fallback": "[TRUE]", "unicode_point": "U+25EE"}, {"id": "NG0142", "symbol": "◯", "name": "false", "description": "False concept in logic - logical relationship or operation", "category": "logic", "aliases": ["false", "false"], "status": "approved", "version": "1.0", "code": "ng:logic:false", "fallback": "[FALSE]", "unicode_point": "U+25EF"}, {"id": "NG0143", "symbol": "◰", "name": "maybe", "description": "Maybe concept in logic - logical relationship or operation", "category": "logic", "aliases": ["maybe", "maybe"], "status": "approved", "version": "1.0", "code": "ng:logic:maybe", "fallback": "[MAYBE]", "unicode_point": "U+25F0"}, {"id": "NG0144", "symbol": "◱", "name": "unknown", "description": "Unknown concept in logic - logical relationship or operation", "category": "logic", "aliases": ["unknown", "unknown"], "status": "approved", "version": "1.0", "code": "ng:logic:unknown", "fallback": "[UNKNOWN]", "unicode_point": "U+25F1"}, {"id": "NG0145", "symbol": "◲", "name": "exists", "description": "Exists concept in logic - logical relationship or operation", "category": "logic", "aliases": ["exists", "exists"], "status": "approved", "version": "1.0", "code": "ng:logic:exists", "fallback": "[EXISTS]", "unicode_point": "U+25F2"}, {"id": "NG0146", "symbol": "◳", "name": "forall", "description": "Forall concept in logic - logical relationship or operation", "category": "logic", "aliases": ["forall", "forall"], "status": "approved", "version": "1.0", "code": "ng:logic:forall", "fallback": "[FORALL]", "unicode_point": "U+25F3"}, {"id": "NG0147", "symbol": "◴", "name": "some", "description": "Some concept in logic - logical relationship or operation", "category": "logic", "aliases": ["some", "some"], "status": "approved", "version": "1.0", "code": "ng:logic:some", "fallback": "[SOME]", "unicode_point": "U+25F4"}, {"id": "NG0148", "symbol": "◵", "name": "none", "description": "None concept in logic - logical relationship or operation", "category": "logic", "aliases": ["none", "none"], "status": "approved", "version": "1.0", "code": "ng:logic:none", "fallback": "[NONE]", "unicode_point": "U+25F5"}, {"id": "NG0149", "symbol": "◶", "name": "all", "description": "All concept in logic - logical relationship or operation", "category": "logic", "aliases": ["all", "all"], "status": "approved", "version": "1.0", "code": "ng:logic:all", "fallback": "[ALL]", "unicode_point": "U+25F6"}, {"id": "NG0150", "symbol": "◷", "name": "any", "description": "Any concept in logic - logical relationship or operation", "category": "logic", "aliases": ["any", "any"], "status": "approved", "version": "1.0", "code": "ng:logic:any", "fallback": "[ANY]", "unicode_point": "U+25F7"}, {"id": "NG0151", "symbol": "◸", "name": "if", "description": "If concept in logic - logical relationship or operation", "category": "logic", "aliases": ["condition", "branch", "test"], "status": "approved", "version": "1.0", "code": "ng:logic:if", "fallback": "[IF]", "unicode_point": "U+25F8"}, {"id": "NG0152", "symbol": "◹", "name": "then", "description": "Then concept in logic - logical relationship or operation", "category": "logic", "aliases": ["then", "then"], "status": "approved", "version": "1.0", "code": "ng:logic:then", "fallback": "[THEN]", "unicode_point": "U+25F9"}, {"id": "NG0153", "symbol": "◺", "name": "else", "description": "Else concept in logic - logical relationship or operation", "category": "logic", "aliases": ["else", "else"], "status": "approved", "version": "1.0", "code": "ng:logic:else", "fallback": "[ELSE]", "unicode_point": "U+25FA"}, {"id": "NG0154", "symbol": "◻", "name": "when", "description": "When concept in logic - logical relationship or operation", "category": "logic", "aliases": ["when", "when"], "status": "approved", "version": "1.0", "code": "ng:logic:when", "fallback": "[WHEN]", "unicode_point": "U+25FB"}, {"id": "NG0155", "symbol": "◼", "name": "while", "description": "While concept in logic - logical relationship or operation", "category": "logic", "aliases": ["while", "while"], "status": "approved", "version": "1.0", "code": "ng:logic:while", "fallback": "[WHILE]", "unicode_point": "U+25FC"}, {"id": "NG0156", "symbol": "◽", "name": "until", "description": "Until concept in logic - logical relationship or operation", "category": "logic", "aliases": ["until", "until"], "status": "approved", "version": "1.0", "code": "ng:logic:until", "fallback": "[UNTIL]", "unicode_point": "U+25FD"}, {"id": "NG0157", "symbol": "◾", "name": "unless", "description": "Unless concept in logic - logical relationship or operation", "category": "logic", "aliases": ["unless", "unless"], "status": "approved", "version": "1.0", "code": "ng:logic:unless", "fallback": "[UNLESS]", "unicode_point": "U+25FE"}, {"id": "NG0158", "symbol": "◿", "name": "because", "description": "Because concept in logic - logical relationship or operation", "category": "logic", "aliases": ["because", "because"], "status": "approved", "version": "1.0", "code": "ng:logic:because", "fallback": "[BECAUSE]", "unicode_point": "U+25FF"}, {"id": "NG0159", "symbol": "⬒", "name": "since", "description": "Since concept in logic - logical relationship or operation", "category": "logic", "aliases": ["since", "since"], "status": "approved", "version": "1.0", "code": "ng:logic:since", "fallback": "[SINCE]", "unicode_point": "U+2B12"}, {"id": "NG0160", "symbol": "⬓", "name": "therefore", "description": "Therefore concept in logic - logical relationship or operation", "category": "logic", "aliases": ["therefore", "therefore"], "status": "approved", "version": "1.0", "code": "ng:logic:therefore", "fallback": "[THEREFORE]", "unicode_point": "U+2B13"}, {"id": "NG0161", "symbol": "⬔", "name": "greater", "description": "Greater concept in logic - logical relationship or operation", "category": "logic", "aliases": ["greater", "greater"], "status": "approved", "version": "1.0", "code": "ng:logic:greater", "fallback": "[GREATER]", "unicode_point": "U+2B14"}, {"id": "NG0162", "symbol": "⬕", "name": "less", "description": "Less concept in logic - logical relationship or operation", "category": "logic", "aliases": ["less", "less"], "status": "approved", "version": "1.0", "code": "ng:logic:less", "fallback": "[LESS]", "unicode_point": "U+2B15"}, {"id": "NG0163", "symbol": "⬖", "name": "equal", "description": "Equal concept in logic - logical relationship or operation", "category": "logic", "aliases": ["equal", "equal"], "status": "approved", "version": "1.0", "code": "ng:logic:equal", "fallback": "[EQUAL]", "unicode_point": "U+2B16"}, {"id": "NG0164", "symbol": "⬗", "name": "different", "description": "Different concept in logic - logical relationship or operation", "category": "logic", "aliases": ["different", "different"], "status": "approved", "version": "1.0", "code": "ng:logic:different", "fallback": "[DIFFERENT]", "unicode_point": "U+2B17"}, {"id": "NG0165", "symbol": "⬘", "name": "similar", "description": "Similar concept in logic - logical relationship or operation", "category": "logic", "aliases": ["similar", "similar"], "status": "approved", "version": "1.0", "code": "ng:logic:similar", "fallback": "[SIMILAR]", "unicode_point": "U+2B18"}, {"id": "NG0166", "symbol": "⬙", "name": "identical", "description": "Identical concept in logic - logical relationship or operation", "category": "logic", "aliases": ["identical", "identical"], "status": "approved", "version": "1.0", "code": "ng:logic:identical", "fallback": "[IDENTICAL]", "unicode_point": "U+2B19"}, {"id": "NG0167", "symbol": "⬚", "name": "equivalent", "description": "Equivalent concept in logic - logical relationship or operation", "category": "logic", "aliases": ["equivalent", "equivalent"], "status": "approved", "version": "1.0", "code": "ng:logic:equivalent", "fallback": "[EQUIVALENT]", "unicode_point": "U+2B1A"}, {"id": "NG0168", "symbol": "⬛", "name": "compatible", "description": "Compatible concept in logic - logical relationship or operation", "category": "logic", "aliases": ["compatible", "compatible"], "status": "approved", "version": "1.0", "code": "ng:logic:compatible", "fallback": "[COMPATIBLE]", "unicode_point": "U+2B1B"}, {"id": "NG0169", "symbol": "⬜", "name": "incompatible", "description": "Incompatible concept in logic - logical relationship or operation", "category": "logic", "aliases": ["incompatible", "incompatible"], "status": "approved", "version": "1.0", "code": "ng:logic:incompatible", "fallback": "[INCOMPATIBLE]", "unicode_point": "U+2B1C"}, {"id": "NG0170", "symbol": "∀", "name": "consistent", "description": "Consistent concept in logic - logical relationship or operation", "category": "logic", "aliases": ["consistent", "consistent"], "status": "approved", "version": "1.0", "code": "ng:logic:consistent", "fallback": "[CONSISTENT]", "unicode_point": "U+2200"}, {"id": "NG0171", "symbol": "∃", "name": "inconsistent", "description": "Inconsistent concept in logic - logical relationship or operation", "category": "logic", "aliases": ["inconsistent", "inconsistent"], "status": "approved", "version": "1.0", "code": "ng:logic:inconsistent", "fallback": "[INCONSISTENT]", "unicode_point": "U+2203"}, {"id": "NG0172", "symbol": "∄", "name": "valid_1", "description": "Valid 1 concept in logic - logical relationship or operation", "category": "logic", "aliases": ["valid1", "valid"], "status": "approved", "version": "1.0", "code": "ng:logic:valid_1", "fallback": "[VALID1]", "unicode_point": "U+2204"}, {"id": "NG0173", "symbol": "∅", "name": "variable", "description": "Variable concept in entity - data container or object", "category": "entity", "aliases": ["var", "data", "value"], "status": "approved", "version": "1.0", "code": "ng:entity:variable", "fallback": "[VARIABLE]", "unicode_point": "U+2205"}, {"id": "NG0174", "symbol": "∆", "name": "constant", "description": "Constant concept in entity - data container or object", "category": "entity", "aliases": ["constant", "constant"], "status": "approved", "version": "1.0", "code": "ng:entity:constant", "fallback": "[CONSTANT]", "unicode_point": "U+2206"}, {"id": "NG0175", "symbol": "∇", "name": "parameter", "description": "Parameter concept in entity - data container or object", "category": "entity", "aliases": ["parameter", "parameter"], "status": "approved", "version": "1.0", "code": "ng:entity:parameter", "fallback": "[PARAMETER]", "unicode_point": "U+2207"}, {"id": "NG0176", "symbol": "∈", "name": "argument", "description": "Argument concept in entity - data container or object", "category": "entity", "aliases": ["argument", "argument"], "status": "approved", "version": "1.0", "code": "ng:entity:argument", "fallback": "[ARGUMENT]", "unicode_point": "U+2208"}, {"id": "NG0177", "symbol": "∉", "name": "value", "description": "Value concept in entity - data container or object", "category": "entity", "aliases": ["value", "value"], "status": "approved", "version": "1.0", "code": "ng:entity:value", "fallback": "[VALUE]", "unicode_point": "U+2209"}, {"id": "NG0178", "symbol": "∊", "name": "reference", "description": "Reference concept in entity - data container or object", "category": "entity", "aliases": ["reference", "reference"], "status": "approved", "version": "1.0", "code": "ng:entity:reference", "fallback": "[REFERENCE]", "unicode_point": "U+220A"}, {"id": "NG0179", "symbol": "∋", "name": "pointer", "description": "Pointer concept in entity - data container or object", "category": "entity", "aliases": ["pointer", "pointer"], "status": "approved", "version": "1.0", "code": "ng:entity:pointer", "fallback": "[POINTER]", "unicode_point": "U+220B"}, {"id": "NG0180", "symbol": "∌", "name": "address", "description": "Address concept in entity - data container or object", "category": "entity", "aliases": ["address", "address"], "status": "approved", "version": "1.0", "code": "ng:entity:address", "fallback": "[ADDRESS]", "unicode_point": "U+220C"}, {"id": "NG0181", "symbol": "∍", "name": "identifier", "description": "Identifier concept in entity - data container or object", "category": "entity", "aliases": ["identifier", "identifier"], "status": "approved", "version": "1.0", "code": "ng:entity:identifier", "fallback": "[IDENTIFIER]", "unicode_point": "U+220D"}, {"id": "NG0182", "symbol": "∎", "name": "name", "description": "Name concept in entity - data container or object", "category": "entity", "aliases": ["name", "name"], "status": "approved", "version": "1.0", "code": "ng:entity:name", "fallback": "[NAME]", "unicode_point": "U+220E"}, {"id": "NG0183", "symbol": "∏", "name": "object", "description": "Object concept in entity - data container or object", "category": "entity", "aliases": ["object", "object"], "status": "approved", "version": "1.0", "code": "ng:entity:object", "fallback": "[OBJECT]", "unicode_point": "U+220F"}, {"id": "NG0184", "symbol": "∐", "name": "instance", "description": "Instance concept in entity - data container or object", "category": "entity", "aliases": ["instance", "instance"], "status": "approved", "version": "1.0", "code": "ng:entity:instance", "fallback": "[INSTANCE]", "unicode_point": "U+2210"}, {"id": "NG0185", "symbol": "∑", "name": "type", "description": "Type concept in entity - data container or object", "category": "entity", "aliases": ["type", "type"], "status": "approved", "version": "1.0", "code": "ng:entity:type", "fallback": "[TYPE]", "unicode_point": "U+2211"}, {"id": "NG0186", "symbol": "∧", "name": "class_1", "description": "Class 1 concept in entity - data container or object", "category": "entity", "aliases": ["class1", "class"], "status": "approved", "version": "1.0", "code": "ng:entity:class_1", "fallback": "[CLASS1]", "unicode_point": "U+2227"}, {"id": "NG0187", "symbol": "∨", "name": "interface_1", "description": "Interface 1 concept in entity - data container or object", "category": "entity", "aliases": ["interface1", "interface"], "status": "approved", "version": "1.0", "code": "ng:entity:interface_1", "fallback": "[INTERFACE1]", "unicode_point": "U+2228"}, {"id": "NG0188", "symbol": "∩", "name": "trait", "description": "Trait concept in entity - data container or object", "category": "entity", "aliases": ["trait", "trait"], "status": "approved", "version": "1.0", "code": "ng:entity:trait", "fallback": "[TRAIT]", "unicode_point": "U+2229"}, {"id": "NG0189", "symbol": "∪", "name": "mixin", "description": "Mixin concept in entity - data container or object", "category": "entity", "aliases": ["mixin", "mixin"], "status": "approved", "version": "1.0", "code": "ng:entity:mixin", "fallback": "[MIXIN]", "unicode_point": "U+222A"}, {"id": "NG0190", "symbol": "∫", "name": "prototype", "description": "Prototype concept in entity - data container or object", "category": "entity", "aliases": ["prototype", "prototype"], "status": "approved", "version": "1.0", "code": "ng:entity:prototype", "fallback": "[PROTOTYPE]", "unicode_point": "U+222B"}, {"id": "NG0191", "symbol": "∬", "name": "factory", "description": "Factory concept in entity - data container or object", "category": "entity", "aliases": ["factory", "factory"], "status": "approved", "version": "1.0", "code": "ng:entity:factory", "fallback": "[FACTORY]", "unicode_point": "U+222C"}, {"id": "NG0192", "symbol": "∭", "name": "builder", "description": "Builder concept in entity - data container or object", "category": "entity", "aliases": ["builder", "builder"], "status": "approved", "version": "1.0", "code": "ng:entity:builder", "fallback": "[BUILDER]", "unicode_point": "U+222D"}, {"id": "NG0193", "symbol": "∮", "name": "user", "description": "User concept in entity - data container or object", "category": "entity", "aliases": ["user", "user"], "status": "approved", "version": "1.0", "code": "ng:entity:user", "fallback": "[USER]", "unicode_point": "U+222E"}, {"id": "NG0194", "symbol": "∯", "name": "client", "description": "Client concept in entity - data container or object", "category": "entity", "aliases": ["client", "client"], "status": "approved", "version": "1.0", "code": "ng:entity:client", "fallback": "[CLIENT]", "unicode_point": "U+222F"}, {"id": "NG0195", "symbol": "∰", "name": "server", "description": "Server concept in entity - data container or object", "category": "entity", "aliases": ["server", "server"], "status": "approved", "version": "1.0", "code": "ng:entity:server", "fallback": "[SERVER]", "unicode_point": "U+2230"}, {"id": "NG0196", "symbol": "∱", "name": "service", "description": "Service concept in entity - data container or object", "category": "entity", "aliases": ["service", "service"], "status": "approved", "version": "1.0", "code": "ng:entity:service", "fallback": "[SERVICE]", "unicode_point": "U+2231"}, {"id": "NG0197", "symbol": "∲", "name": "resource", "description": "Resource concept in entity - data container or object", "category": "entity", "aliases": ["resource", "resource"], "status": "approved", "version": "1.0", "code": "ng:entity:resource", "fallback": "[RESOURCE]", "unicode_point": "U+2232"}, {"id": "NG0198", "symbol": "∳", "name": "asset", "description": "Asset concept in entity - data container or object", "category": "entity", "aliases": ["asset", "asset"], "status": "approved", "version": "1.0", "code": "ng:entity:asset", "fallback": "[ASSET]", "unicode_point": "U+2233"}, {"id": "NG0199", "symbol": "∴", "name": "property", "description": "Property concept in entity - data container or object", "category": "entity", "aliases": ["property", "property"], "status": "approved", "version": "1.0", "code": "ng:entity:property", "fallback": "[PROPERTY]", "unicode_point": "U+2234"}, {"id": "NG0200", "symbol": "∵", "name": "attribute", "description": "Attribute concept in entity - data container or object", "category": "entity", "aliases": ["attribute", "attribute"], "status": "approved", "version": "1.0", "code": "ng:entity:attribute", "fallback": "[ATTRIBUTE]", "unicode_point": "U+2235"}, {"id": "NG0201", "symbol": "∶", "name": "field_1", "description": "Field 1 concept in entity - data container or object", "category": "entity", "aliases": ["field1", "field"], "status": "approved", "version": "1.0", "code": "ng:entity:field_1", "fallback": "[FIELD1]", "unicode_point": "U+2236"}, {"id": "NG0202", "symbol": "∷", "name": "member", "description": "Member concept in entity - data container or object", "category": "entity", "aliases": ["member", "member"], "status": "approved", "version": "1.0", "code": "ng:entity:member", "fallback": "[MEMBER]", "unicode_point": "U+2237"}, {"id": "NG0203", "symbol": "∸", "name": "record", "description": "Record concept in entity - data container or object", "category": "entity", "aliases": ["record", "record"], "status": "approved", "version": "1.0", "code": "ng:entity:record", "fallback": "[RECORD]", "unicode_point": "U+2238"}, {"id": "NG0204", "symbol": "∹", "name": "tuple", "description": "Tuple concept in entity - data container or object", "category": "entity", "aliases": ["tuple", "tuple"], "status": "approved", "version": "1.0", "code": "ng:entity:tuple", "fallback": "[TUPLE]", "unicode_point": "U+2239"}, {"id": "NG0205", "symbol": "∺", "name": "struct", "description": "Struct concept in entity - data container or object", "category": "entity", "aliases": ["struct", "struct"], "status": "approved", "version": "1.0", "code": "ng:entity:struct", "fallback": "[STRUCT]", "unicode_point": "U+223A"}, {"id": "NG0206", "symbol": "∻", "name": "union_1", "description": "Union 1 concept in entity - data container or object", "category": "entity", "aliases": ["union1", "union"], "status": "approved", "version": "1.0", "code": "ng:entity:union_1", "fallback": "[UNION1]", "unicode_point": "U+223B"}, {"id": "NG0207", "symbol": "∼", "name": "enum", "description": "Enum concept in entity - data container or object", "category": "entity", "aliases": ["enum", "enum"], "status": "approved", "version": "1.0", "code": "ng:entity:enum", "fallback": "[ENUM]", "unicode_point": "U+223C"}, {"id": "NG0208", "symbol": "∽", "name": "variant", "description": "Variant concept in entity - data container or object", "category": "entity", "aliases": ["variant", "variant"], "status": "approved", "version": "1.0", "code": "ng:entity:variant", "fallback": "[VARIANT]", "unicode_point": "U+223D"}, {"id": "NG0209", "symbol": "∾", "name": "option", "description": "Option concept in entity - data container or object", "category": "entity", "aliases": ["option", "option"], "status": "approved", "version": "1.0", "code": "ng:entity:option", "fallback": "[OPTION]", "unicode_point": "U+223E"}, {"id": "NG0210", "symbol": "∿", "name": "result", "description": "Result concept in entity - data container or object", "category": "entity", "aliases": ["result", "result"], "status": "approved", "version": "1.0", "code": "ng:entity:result", "fallback": "[RESULT]", "unicode_point": "U+223F"}, {"id": "NG0211", "symbol": "≀", "name": "either", "description": "Either concept in entity - data container or object", "category": "entity", "aliases": ["either", "either"], "status": "approved", "version": "1.0", "code": "ng:entity:either", "fallback": "[EITHER]", "unicode_point": "U+2240"}, {"id": "NG0212", "symbol": "≁", "name": "maybe_1", "description": "Maybe 1 concept in entity - data container or object", "category": "entity", "aliases": ["maybe1", "maybe"], "status": "approved", "version": "1.0", "code": "ng:entity:maybe_1", "fallback": "[MAYBE1]", "unicode_point": "U+2241"}, {"id": "NG0213", "symbol": "≂", "name": "token", "description": "Token concept in entity - data container or object", "category": "entity", "aliases": ["token", "token"], "status": "approved", "version": "1.0", "code": "ng:entity:token", "fallback": "[TOKEN]", "unicode_point": "U+2242"}, {"id": "NG0214", "symbol": "≃", "name": "symbol", "description": "Symbol concept in entity - data container or object", "category": "entity", "aliases": ["symbol", "symbol"], "status": "approved", "version": "1.0", "code": "ng:entity:symbol", "fallback": "[SYMBOL]", "unicode_point": "U+2243"}, {"id": "NG0215", "symbol": "≄", "name": "literal", "description": "Literal concept in entity - data container or object", "category": "entity", "aliases": ["literal", "literal"], "status": "approved", "version": "1.0", "code": "ng:entity:literal", "fallback": "[LITERAL]", "unicode_point": "U+2244"}, {"id": "NG0216", "symbol": "≅", "name": "web", "description": "Web concept in domain - domain-specific concept", "category": "domain", "aliases": ["web", "web"], "status": "approved", "version": "1.0", "code": "ng:domain:web", "fallback": "[WEB]", "unicode_point": "U+2245"}, {"id": "NG0217", "symbol": "≆", "name": "mobile", "description": "Mobile concept in domain - domain-specific concept", "category": "domain", "aliases": ["mobile", "mobile"], "status": "approved", "version": "1.0", "code": "ng:domain:mobile", "fallback": "[MOBILE]", "unicode_point": "U+2246"}, {"id": "NG0218", "symbol": "≇", "name": "desktop", "description": "Desktop concept in domain - domain-specific concept", "category": "domain", "aliases": ["desktop", "desktop"], "status": "approved", "version": "1.0", "code": "ng:domain:desktop", "fallback": "[DESKTOP]", "unicode_point": "U+2247"}, {"id": "NG0219", "symbol": "≈", "name": "server_1", "description": "Server 1 concept in domain - domain-specific concept", "category": "domain", "aliases": ["server1", "server"], "status": "approved", "version": "1.0", "code": "ng:domain:server_1", "fallback": "[SERVER1]", "unicode_point": "U+2248"}, {"id": "NG0220", "symbol": "≉", "name": "cloud", "description": "Cloud concept in domain - domain-specific concept", "category": "domain", "aliases": ["cloud", "cloud"], "status": "approved", "version": "1.0", "code": "ng:domain:cloud", "fallback": "[CLOUD]", "unicode_point": "U+2249"}, {"id": "NG0221", "symbol": "≊", "name": "edge_1", "description": "Edge 1 concept in domain - domain-specific concept", "category": "domain", "aliases": ["edge1", "edge"], "status": "approved", "version": "1.0", "code": "ng:domain:edge_1", "fallback": "[EDGE1]", "unicode_point": "U+224A"}, {"id": "NG0222", "symbol": "≋", "name": "iot", "description": "Iot concept in domain - domain-specific concept", "category": "domain", "aliases": ["iot", "iot"], "status": "approved", "version": "1.0", "code": "ng:domain:iot", "fallback": "[IOT]", "unicode_point": "U+224B"}, {"id": "NG0223", "symbol": "≌", "name": "embedded", "description": "Embedded concept in domain - domain-specific concept", "category": "domain", "aliases": ["embedded", "embedded"], "status": "approved", "version": "1.0", "code": "ng:domain:embedded", "fallback": "[EMBEDDED]", "unicode_point": "U+224C"}, {"id": "NG0224", "symbol": "≍", "name": "real_time", "description": "Real Time concept in domain - domain-specific concept", "category": "domain", "aliases": ["realtime", "real"], "status": "approved", "version": "1.0", "code": "ng:domain:real_time", "fallback": "[REALTIME]", "unicode_point": "U+224D"}, {"id": "NG0225", "symbol": "≎", "name": "batch", "description": "Batch concept in domain - domain-specific concept", "category": "domain", "aliases": ["batch", "batch"], "status": "approved", "version": "1.0", "code": "ng:domain:batch", "fallback": "[BATCH]", "unicode_point": "U+224E"}, {"id": "NG0226", "symbol": "≏", "name": "database", "description": "Database concept in domain - domain-specific concept", "category": "domain", "aliases": ["database", "database"], "status": "approved", "version": "1.0", "code": "ng:domain:database", "fallback": "[DATABASE]", "unicode_point": "U+224F"}, {"id": "NG0227", "symbol": "≐", "name": "storage", "description": "Storage concept in domain - domain-specific concept", "category": "domain", "aliases": ["storage", "storage"], "status": "approved", "version": "1.0", "code": "ng:domain:storage", "fallback": "[STORAGE]", "unicode_point": "U+2250"}, {"id": "NG0228", "symbol": "≑", "name": "memory", "description": "Memory concept in domain - domain-specific concept", "category": "domain", "aliases": ["memory", "memory"], "status": "approved", "version": "1.0", "code": "ng:domain:memory", "fallback": "[MEMORY]", "unicode_point": "U+2251"}, {"id": "NG0229", "symbol": "≒", "name": "cache", "description": "Cache concept in domain - domain-specific concept", "category": "domain", "aliases": ["cache", "cache"], "status": "approved", "version": "1.0", "code": "ng:domain:cache", "fallback": "[CACHE]", "unicode_point": "U+2252"}, {"id": "NG0230", "symbol": "≓", "name": "network", "description": "Network concept in domain - domain-specific concept", "category": "domain", "aliases": ["network", "network"], "status": "approved", "version": "1.0", "code": "ng:domain:network", "fallback": "[NETWORK]", "unicode_point": "U+2253"}, {"id": "NG0231", "symbol": "≔", "name": "security", "description": "Security concept in domain - domain-specific concept", "category": "domain", "aliases": ["security", "security"], "status": "approved", "version": "1.0", "code": "ng:domain:security", "fallback": "[SECURITY]", "unicode_point": "U+2254"}, {"id": "NG0232", "symbol": "≕", "name": "crypto", "description": "Crypto concept in domain - domain-specific concept", "category": "domain", "aliases": ["crypto", "crypto"], "status": "approved", "version": "1.0", "code": "ng:domain:crypto", "fallback": "[CRYPTO]", "unicode_point": "U+2255"}, {"id": "NG0233", "symbol": "≖", "name": "auth", "description": "Auth concept in domain - domain-specific concept", "category": "domain", "aliases": ["auth", "auth"], "status": "approved", "version": "1.0", "code": "ng:domain:auth", "fallback": "[AUTH]", "unicode_point": "U+2256"}, {"id": "NG0234", "symbol": "≗", "name": "permission", "description": "Permission concept in domain - domain-specific concept", "category": "domain", "aliases": ["permission", "permission"], "status": "approved", "version": "1.0", "code": "ng:domain:permission", "fallback": "[PERMISSION]", "unicode_point": "U+2257"}, {"id": "NG0235", "symbol": "≘", "name": "role", "description": "Role concept in domain - domain-specific concept", "category": "domain", "aliases": ["role", "role"], "status": "approved", "version": "1.0", "code": "ng:domain:role", "fallback": "[ROLE]", "unicode_point": "U+2258"}, {"id": "NG0236", "symbol": "≙", "name": "ui", "description": "Ui concept in domain - domain-specific concept", "category": "domain", "aliases": ["ui", "ui"], "status": "approved", "version": "1.0", "code": "ng:domain:ui", "fallback": "[UI]", "unicode_point": "U+2259"}, {"id": "NG0237", "symbol": "≚", "name": "ux", "description": "Ux concept in domain - domain-specific concept", "category": "domain", "aliases": ["ux", "ux"], "status": "approved", "version": "1.0", "code": "ng:domain:ux", "fallback": "[UX]", "unicode_point": "U+225A"}, {"id": "NG0238", "symbol": "≛", "name": "frontend", "description": "Frontend concept in domain - domain-specific concept", "category": "domain", "aliases": ["frontend", "frontend"], "status": "approved", "version": "1.0", "code": "ng:domain:frontend", "fallback": "[FRONTEND]", "unicode_point": "U+225B"}, {"id": "NG0239", "symbol": "≜", "name": "backend", "description": "Backend concept in domain - domain-specific concept", "category": "domain", "aliases": ["backend", "backend"], "status": "approved", "version": "1.0", "code": "ng:domain:backend", "fallback": "[BACKEND]", "unicode_point": "U+225C"}, {"id": "NG0240", "symbol": "≝", "name": "middleware", "description": "Middleware concept in domain - domain-specific concept", "category": "domain", "aliases": ["middleware", "middleware"], "status": "approved", "version": "1.0", "code": "ng:domain:middleware", "fallback": "[MIDDLEWARE]", "unicode_point": "U+225D"}, {"id": "NG0241", "symbol": "≞", "name": "api", "description": "Api concept in domain - domain-specific concept", "category": "domain", "aliases": ["api", "api"], "status": "approved", "version": "1.0", "code": "ng:domain:api", "fallback": "[API]", "unicode_point": "U+225E"}, {"id": "NG0242", "symbol": "≟", "name": "rest", "description": "Rest concept in domain - domain-specific concept", "category": "domain", "aliases": ["rest", "rest"], "status": "approved", "version": "1.0", "code": "ng:domain:rest", "fallback": "[REST]", "unicode_point": "U+225F"}, {"id": "NG0243", "symbol": "≠", "name": "graphql", "description": "Graphql concept in domain - domain-specific concept", "category": "domain", "aliases": ["graphql", "graphql"], "status": "approved", "version": "1.0", "code": "ng:domain:graphql", "fallback": "[GRAPHQL]", "unicode_point": "U+2260"}, {"id": "NG0244", "symbol": "≡", "name": "rpc", "description": "Rpc concept in domain - domain-specific concept", "category": "domain", "aliases": ["rpc", "rpc"], "status": "approved", "version": "1.0", "code": "ng:domain:rpc", "fallback": "[RPC]", "unicode_point": "U+2261"}, {"id": "NG0245", "symbol": "≢", "name": "messaging", "description": "Messaging concept in domain - domain-specific concept", "category": "domain", "aliases": ["messaging", "messaging"], "status": "approved", "version": "1.0", "code": "ng:domain:messaging", "fallback": "[MESSAGING]", "unicode_point": "U+2262"}, {"id": "NG0246", "symbol": "≣", "name": "ml", "description": "Ml concept in domain - domain-specific concept", "category": "domain", "aliases": ["ml", "ml"], "status": "approved", "version": "1.0", "code": "ng:domain:ml", "fallback": "[ML]", "unicode_point": "U+2263"}, {"id": "NG0247", "symbol": "≤", "name": "ai", "description": "Ai concept in domain - domain-specific concept", "category": "domain", "aliases": ["ai", "ai"], "status": "approved", "version": "1.0", "code": "ng:domain:ai", "fallback": "[AI]", "unicode_point": "U+2264"}, {"id": "NG0248", "symbol": "≥", "name": "data", "description": "Data concept in domain - domain-specific concept", "category": "domain", "aliases": ["data", "data"], "status": "approved", "version": "1.0", "code": "ng:domain:data", "fallback": "[DATA]", "unicode_point": "U+2265"}, {"id": "NG0249", "symbol": "≦", "name": "analytics", "description": "Analytics concept in domain - domain-specific concept", "category": "domain", "aliases": ["analytics", "analytics"], "status": "approved", "version": "1.0", "code": "ng:domain:analytics", "fallback": "[ANALYTICS]", "unicode_point": "U+2266"}, {"id": "NG0250", "symbol": "⟨", "name": "visualization", "description": "Visualization concept in domain - domain-specific concept", "category": "domain", "aliases": ["visualization", "visualization"], "status": "approved", "version": "1.0", "code": "ng:domain:visualization", "fallback": "[VISUALIZATION]", "unicode_point": "U+27E8"}, {"id": "NG0251", "symbol": "⟩", "name": "reporting", "description": "Reporting concept in domain - domain-specific concept", "category": "domain", "aliases": ["reporting", "reporting"], "status": "approved", "version": "1.0", "code": "ng:domain:reporting", "fallback": "[REPORTING]", "unicode_point": "U+27E9"}, {"id": "NG0252", "symbol": "⟪", "name": "monitoring", "description": "Monitoring concept in domain - domain-specific concept", "category": "domain", "aliases": ["monitoring", "monitoring"], "status": "approved", "version": "1.0", "code": "ng:domain:monitoring", "fallback": "[MONITORING]", "unicode_point": "U+27EA"}, {"id": "NG0253", "symbol": "⟫", "name": "logging", "description": "Logging concept in domain - domain-specific concept", "category": "domain", "aliases": ["logging", "logging"], "status": "approved", "version": "1.0", "code": "ng:domain:logging", "fallback": "[LOGGING]", "unicode_point": "U+27EB"}, {"id": "NG0254", "symbol": "⟬", "name": "metrics", "description": "Metrics concept in domain - domain-specific concept", "category": "domain", "aliases": ["metrics", "metrics"], "status": "approved", "version": "1.0", "code": "ng:domain:metrics", "fallback": "[METRICS]", "unicode_point": "U+27EC"}, {"id": "NG0255", "symbol": "⟭", "name": "alerts", "description": "Alerts concept in domain - domain-specific concept", "category": "domain", "aliases": ["alerts", "alerts"], "status": "approved", "version": "1.0", "code": "ng:domain:alerts", "fallback": "[ALERTS]", "unicode_point": "U+27ED"}, {"id": "NG0256", "symbol": "⟮", "name": "game", "description": "Game concept in domain - domain-specific concept", "category": "domain", "aliases": ["game", "game"], "status": "approved", "version": "1.0", "code": "ng:domain:game", "fallback": "[GAME]", "unicode_point": "U+27EE"}, {"id": "NG0257", "symbol": "⟯", "name": "graphics", "description": "Graphics concept in domain - domain-specific concept", "category": "domain", "aliases": ["graphics", "graphics"], "status": "approved", "version": "1.0", "code": "ng:domain:graphics", "fallback": "[GRAPHICS]", "unicode_point": "U+27EF"}, {"id": "NG0258", "symbol": "⟰", "name": "audio", "description": "Audio concept in domain - domain-specific concept", "category": "domain", "aliases": ["audio", "audio"], "status": "approved", "version": "1.0", "code": "ng:domain:audio", "fallback": "[AUDIO]", "unicode_point": "U+27F0"}, {"id": "NG0259", "symbol": "⟱", "name": "if_1", "description": "If 1 concept in flow - control flow construct", "category": "flow", "aliases": ["if1", "if"], "status": "approved", "version": "1.0", "code": "ng:flow:if_1", "fallback": "[IF1]", "unicode_point": "U+27F1"}, {"id": "NG0260", "symbol": "⟲", "name": "else_1", "description": "Else 1 concept in flow - control flow construct", "category": "flow", "aliases": ["else1", "else"], "status": "approved", "version": "1.0", "code": "ng:flow:else_1", "fallback": "[ELSE1]", "unicode_point": "U+27F2"}, {"id": "NG0261", "symbol": "⟳", "name": "for_loop", "description": "For Loop concept in flow - control flow construct", "category": "flow", "aliases": ["iterate", "repeat", "cycle"], "status": "approved", "version": "1.0", "code": "ng:flow:for_loop", "fallback": "[FORLOOP]", "unicode_point": "U+27F3"}, {"id": "NG0262", "symbol": "⟴", "name": "while_loop", "description": "While Loop concept in flow - control flow construct", "category": "flow", "aliases": ["whileloop", "while"], "status": "approved", "version": "1.0", "code": "ng:flow:while_loop", "fallback": "[WHILELOOP]", "unicode_point": "U+27F4"}, {"id": "NG0263", "symbol": "⟵", "name": "return", "description": "Return concept in flow - control flow construct", "category": "flow", "aliases": ["return", "return"], "status": "approved", "version": "1.0", "code": "ng:flow:return", "fallback": "[RETURN]", "unicode_point": "U+27F5"}, {"id": "NG0264", "symbol": "⟶", "name": "yield", "description": "Yield concept in flow - control flow construct", "category": "flow", "aliases": ["yield", "yield"], "status": "approved", "version": "1.0", "code": "ng:flow:yield", "fallback": "[YIELD]", "unicode_point": "U+27F6"}, {"id": "NG0265", "symbol": "⟷", "name": "async", "description": "Async concept in flow - control flow construct", "category": "flow", "aliases": ["async", "async"], "status": "approved", "version": "1.0", "code": "ng:flow:async", "fallback": "[ASYNC]", "unicode_point": "U+27F7"}, {"id": "NG0266", "symbol": "⟸", "name": "await", "description": "Await concept in flow - control flow construct", "category": "flow", "aliases": ["await", "await"], "status": "approved", "version": "1.0", "code": "ng:flow:await", "fallback": "[AWAIT]", "unicode_point": "U+27F8"}, {"id": "NG0267", "symbol": "⟹", "name": "break", "description": "Break concept in flow - control flow construct", "category": "flow", "aliases": ["break", "break"], "status": "approved", "version": "1.0", "code": "ng:flow:break", "fallback": "[BREAK]", "unicode_point": "U+27F9"}, {"id": "NG0268", "symbol": "⟺", "name": "continue", "description": "Continue concept in flow - control flow construct", "category": "flow", "aliases": ["continue", "continue"], "status": "approved", "version": "1.0", "code": "ng:flow:continue", "fallback": "[CONTINUE]", "unicode_point": "U+27FA"}, {"id": "NG0269", "symbol": "⟻", "name": "try", "description": "Try concept in flow - control flow construct", "category": "flow", "aliases": ["try", "try"], "status": "approved", "version": "1.0", "code": "ng:flow:try", "fallback": "[TRY]", "unicode_point": "U+27FB"}, {"id": "NG0270", "symbol": "⟼", "name": "catch", "description": "Catch concept in flow - control flow construct", "category": "flow", "aliases": ["catch", "catch"], "status": "approved", "version": "1.0", "code": "ng:flow:catch", "fallback": "[CATCH]", "unicode_point": "U+27FC"}, {"id": "NG0271", "symbol": "⟽", "name": "finally", "description": "Finally concept in flow - control flow construct", "category": "flow", "aliases": ["finally", "finally"], "status": "approved", "version": "1.0", "code": "ng:flow:finally", "fallback": "[FINALLY]", "unicode_point": "U+27FD"}, {"id": "NG0272", "symbol": "⟾", "name": "throw", "description": "Throw concept in flow - control flow construct", "category": "flow", "aliases": ["throw", "throw"], "status": "approved", "version": "1.0", "code": "ng:flow:throw", "fallback": "[THROW]", "unicode_point": "U+27FE"}, {"id": "NG0273", "symbol": "⟿", "name": "raise", "description": "Raise concept in flow - control flow construct", "category": "flow", "aliases": ["raise", "raise"], "status": "approved", "version": "1.0", "code": "ng:flow:raise", "fallback": "[RAISE]", "unicode_point": "U+27FF"}, {"id": "NG0274", "symbol": "⤀", "name": "handle", "description": "Handle concept in flow - control flow construct", "category": "flow", "aliases": ["handle", "handle"], "status": "approved", "version": "1.0", "code": "ng:flow:handle", "fallback": "[HANDLE]", "unicode_point": "U+2900"}, {"id": "NG0275", "symbol": "⤁", "name": "recover", "description": "Recover concept in flow - control flow construct", "category": "flow", "aliases": ["recover", "recover"], "status": "approved", "version": "1.0", "code": "ng:flow:recover", "fallback": "[RECOVER]", "unicode_point": "U+2901"}, {"id": "NG0276", "symbol": "⤂", "name": "retry", "description": "Retry concept in flow - control flow construct", "category": "flow", "aliases": ["retry", "retry"], "status": "approved", "version": "1.0", "code": "ng:flow:retry", "fallback": "[RETRY]", "unicode_point": "U+2902"}, {"id": "NG0277", "symbol": "⤃", "name": "timeout", "description": "Timeout concept in flow - control flow construct", "category": "flow", "aliases": ["timeout", "timeout"], "status": "approved", "version": "1.0", "code": "ng:flow:timeout", "fallback": "[TIMEOUT]", "unicode_point": "U+2903"}, {"id": "NG0278", "symbol": "⤄", "name": "cancel", "description": "Cancel concept in flow - control flow construct", "category": "flow", "aliases": ["cancel", "cancel"], "status": "approved", "version": "1.0", "code": "ng:flow:cancel", "fallback": "[CANCEL]", "unicode_point": "U+2904"}, {"id": "NG0279", "symbol": "⤅", "name": "call", "description": "Call concept in flow - control flow construct", "category": "flow", "aliases": ["call", "call"], "status": "approved", "version": "1.0", "code": "ng:flow:call", "fallback": "[CALL]", "unicode_point": "U+2905"}, {"id": "NG0280", "symbol": "⤆", "name": "invoke", "description": "Invoke concept in flow - control flow construct", "category": "flow", "aliases": ["invoke", "invoke"], "status": "approved", "version": "1.0", "code": "ng:flow:invoke", "fallback": "[INVOKE]", "unicode_point": "U+2906"}, {"id": "NG0281", "symbol": "⤇", "name": "execute", "description": "Execute concept in flow - control flow construct", "category": "flow", "aliases": ["execute", "execute"], "status": "approved", "version": "1.0", "code": "ng:flow:execute", "fallback": "[EXECUTE]", "unicode_point": "U+2907"}, {"id": "NG0282", "symbol": "⚀", "name": "run", "description": "Run concept in flow - control flow construct", "category": "flow", "aliases": ["run", "run"], "status": "approved", "version": "1.0", "code": "ng:flow:run", "fallback": "[RUN]", "unicode_point": "U+2680"}, {"id": "NG0283", "symbol": "⚁", "name": "start_1", "description": "Start 1 concept in flow - control flow construct", "category": "flow", "aliases": ["start1", "start"], "status": "approved", "version": "1.0", "code": "ng:flow:start_1", "fallback": "[START1]", "unicode_point": "U+2681"}, {"id": "NG0284", "symbol": "⚂", "name": "stop_1", "description": "Stop 1 concept in flow - control flow construct", "category": "flow", "aliases": ["stop1", "stop"], "status": "approved", "version": "1.0", "code": "ng:flow:stop_1", "fallback": "[STOP1]", "unicode_point": "U+2682"}, {"id": "NG0285", "symbol": "⚃", "name": "pause_1", "description": "Pause 1 concept in flow - control flow construct", "category": "flow", "aliases": ["pause1", "pause"], "status": "approved", "version": "1.0", "code": "ng:flow:pause_1", "fallback": "[PAUSE1]", "unicode_point": "U+2683"}, {"id": "NG0286", "symbol": "⚄", "name": "resume_1", "description": "Resume 1 concept in flow - control flow construct", "category": "flow", "aliases": ["resume1", "resume"], "status": "approved", "version": "1.0", "code": "ng:flow:resume_1", "fallback": "[RESUME1]", "unicode_point": "U+2684"}, {"id": "NG0287", "symbol": "⚅", "name": "schedule", "description": "Schedule concept in flow - control flow construct", "category": "flow", "aliases": ["schedule", "schedule"], "status": "approved", "version": "1.0", "code": "ng:flow:schedule", "fallback": "[SCHEDULE]", "unicode_point": "U+2685"}, {"id": "NG0288", "symbol": "⚆", "name": "defer", "description": "Defer concept in flow - control flow construct", "category": "flow", "aliases": ["defer", "defer"], "status": "approved", "version": "1.0", "code": "ng:flow:defer", "fallback": "[DEFER]", "unicode_point": "U+2686"}, {"id": "NG0289", "symbol": "⚇", "name": "sequence", "description": "Sequence concept in flow - control flow construct", "category": "flow", "aliases": ["sequence", "sequence"], "status": "approved", "version": "1.0", "code": "ng:flow:sequence", "fallback": "[SEQUENCE]", "unicode_point": "U+2687"}, {"id": "NG0290", "symbol": "⚈", "name": "parallel", "description": "Parallel concept in flow - control flow construct", "category": "flow", "aliases": ["parallel", "parallel"], "status": "approved", "version": "1.0", "code": "ng:flow:parallel", "fallback": "[PARALLEL]", "unicode_point": "U+2688"}, {"id": "NG0291", "symbol": "⚉", "name": "concurrent", "description": "Concurrent concept in flow - control flow construct", "category": "flow", "aliases": ["concurrent", "concurrent"], "status": "approved", "version": "1.0", "code": "ng:flow:concurrent", "fallback": "[CONCURRENT]", "unicode_point": "U+2689"}, {"id": "NG0292", "symbol": "⚊", "name": "serial", "description": "Serial concept in flow - control flow construct", "category": "flow", "aliases": ["serial", "serial"], "status": "approved", "version": "1.0", "code": "ng:flow:serial", "fallback": "[SERIAL]", "unicode_point": "U+268A"}, {"id": "NG0293", "symbol": "⚋", "name": "pipeline", "description": "Pipeline concept in flow - control flow construct", "category": "flow", "aliases": ["pipeline", "pipeline"], "status": "approved", "version": "1.0", "code": "ng:flow:pipeline", "fallback": "[PIPELINE]", "unicode_point": "U+268B"}, {"id": "NG0294", "symbol": "⚌", "name": "workflow", "description": "Workflow concept in flow - control flow construct", "category": "flow", "aliases": ["workflow", "workflow"], "status": "approved", "version": "1.0", "code": "ng:flow:workflow", "fallback": "[WORKFLOW]", "unicode_point": "U+268C"}, {"id": "NG0295", "symbol": "⚍", "name": "process", "description": "Process concept in flow - control flow construct", "category": "flow", "aliases": ["process", "process"], "status": "approved", "version": "1.0", "code": "ng:flow:process", "fallback": "[PROCESS]", "unicode_point": "U+268D"}, {"id": "NG0296", "symbol": "⚎", "name": "thread", "description": "Thread concept in flow - control flow construct", "category": "flow", "aliases": ["thread", "thread"], "status": "approved", "version": "1.0", "code": "ng:flow:thread", "fallback": "[THREAD]", "unicode_point": "U+268E"}, {"id": "NG0297", "symbol": "⚏", "name": "task", "description": "Task concept in flow - control flow construct", "category": "flow", "aliases": ["task", "task"], "status": "approved", "version": "1.0", "code": "ng:flow:task", "fallback": "[TASK]", "unicode_point": "U+268F"}, {"id": "NG0298", "symbol": "⚐", "name": "job", "description": "Job concept in flow - control flow construct", "category": "flow", "aliases": ["job", "job"], "status": "approved", "version": "1.0", "code": "ng:flow:job", "fallback": "[JOB]", "unicode_point": "U+2690"}, {"id": "NG0299", "symbol": "⚑", "name": "event", "description": "Event concept in flow - control flow construct", "category": "flow", "aliases": ["event", "event"], "status": "approved", "version": "1.0", "code": "ng:flow:event", "fallback": "[EVENT]", "unicode_point": "U+2691"}, {"id": "NG0300", "symbol": "⚒", "name": "trigger", "description": "Trigger concept in flow - control flow construct", "category": "flow", "aliases": ["trigger", "trigger"], "status": "approved", "version": "1.0", "code": "ng:flow:trigger", "fallback": "[TRIGGER]", "unicode_point": "U+2692"}, {"id": "NG0301", "symbol": "⚓", "name": "signal", "description": "Signal concept in flow - control flow construct", "category": "flow", "aliases": ["signal", "signal"], "status": "approved", "version": "1.0", "code": "ng:flow:signal", "fallback": "[SIGNAL]", "unicode_point": "U+2693"}, {"id": "NG0302", "symbol": "⚔", "name": "string", "description": "String concept in data - data type or structure", "category": "data", "aliases": ["string", "string"], "status": "approved", "version": "1.0", "code": "ng:data:string", "fallback": "[STRING]", "unicode_point": "U+2694"}, {"id": "NG0303", "symbol": "⚕", "name": "number", "description": "Number concept in data - data type or structure", "category": "data", "aliases": ["number", "number"], "status": "approved", "version": "1.0", "code": "ng:data:number", "fallback": "[NUMBER]", "unicode_point": "U+2695"}, {"id": "NG0304", "symbol": "⚖", "name": "boolean", "description": "Boolean concept in data - data type or structure", "category": "data", "aliases": ["boolean", "boolean"], "status": "approved", "version": "1.0", "code": "ng:data:boolean", "fallback": "[BOOLEAN]", "unicode_point": "U+2696"}, {"id": "NG0305", "symbol": "⚗", "name": "array_1", "description": "Array 1 concept in data - data type or structure", "category": "data", "aliases": ["array1", "array"], "status": "approved", "version": "1.0", "code": "ng:data:array_1", "fallback": "[ARRAY1]", "unicode_point": "U+2697"}, {"id": "NG0306", "symbol": "⚘", "name": "object_1", "description": "Object 1 concept in data - data type or structure", "category": "data", "aliases": ["object1", "object"], "status": "approved", "version": "1.0", "code": "ng:data:object_1", "fallback": "[OBJECT1]", "unicode_point": "U+2698"}, {"id": "NG0307", "symbol": "⚙", "name": "map", "description": "Map concept in data - data type or structure", "category": "data", "aliases": ["map", "map"], "status": "approved", "version": "1.0", "code": "ng:data:map", "fallback": "[MAP]", "unicode_point": "U+2699"}, {"id": "NG0308", "symbol": "⚚", "name": "set", "description": "Set concept in data - data type or structure", "category": "data", "aliases": ["set", "set"], "status": "approved", "version": "1.0", "code": "ng:data:set", "fallback": "[SET]", "unicode_point": "U+269A"}, {"id": "NG0309", "symbol": "⚛", "name": "list_1", "description": "List 1 concept in data - data type or structure", "category": "data", "aliases": ["list1", "list"], "status": "approved", "version": "1.0", "code": "ng:data:list_1", "fallback": "[LIST1]", "unicode_point": "U+269B"}, {"id": "NG0310", "symbol": "⚜", "name": "tuple_1", "description": "Tuple 1 concept in data - data type or structure", "category": "data", "aliases": ["tuple1", "tuple"], "status": "approved", "version": "1.0", "code": "ng:data:tuple_1", "fallback": "[TUPLE1]", "unicode_point": "U+269C"}, {"id": "NG0311", "symbol": "⚝", "name": "record_1", "description": "Record 1 concept in data - data type or structure", "category": "data", "aliases": ["record1", "record"], "status": "approved", "version": "1.0", "code": "ng:data:record_1", "fallback": "[RECORD1]", "unicode_point": "U+269D"}, {"id": "NG0312", "symbol": "⚞", "name": "json", "description": "Json concept in data - data type or structure", "category": "data", "aliases": ["json", "json"], "status": "approved", "version": "1.0", "code": "ng:data:json", "fallback": "[JSON]", "unicode_point": "U+269E"}, {"id": "NG0313", "symbol": "⚟", "name": "xml", "description": "Xml concept in data - data type or structure", "category": "data", "aliases": ["xml", "xml"], "status": "approved", "version": "1.0", "code": "ng:data:xml", "fallback": "[XML]", "unicode_point": "U+269F"}, {"id": "NG0314", "symbol": "⚠", "name": "csv", "description": "Csv concept in data - data type or structure", "category": "data", "aliases": ["csv", "csv"], "status": "approved", "version": "1.0", "code": "ng:data:csv", "fallback": "[CSV]", "unicode_point": "U+26A0"}, {"id": "NG0315", "symbol": "⚡", "name": "binary", "description": "Binary concept in data - data type or structure", "category": "data", "aliases": ["binary", "binary"], "status": "approved", "version": "1.0", "code": "ng:data:binary", "fallback": "[BINARY]", "unicode_point": "U+26A1"}, {"id": "NG0316", "symbol": "⚢", "name": "text", "description": "Text concept in data - data type or structure", "category": "data", "aliases": ["text", "text"], "status": "approved", "version": "1.0", "code": "ng:data:text", "fallback": "[TEXT]", "unicode_point": "U+26A2"}, {"id": "NG0317", "symbol": "⚣", "name": "image", "description": "Image concept in data - data type or structure", "category": "data", "aliases": ["image", "image"], "status": "approved", "version": "1.0", "code": "ng:data:image", "fallback": "[IMAGE]", "unicode_point": "U+26A3"}, {"id": "NG0318", "symbol": "⚤", "name": "audio_1", "description": "Audio 1 concept in data - data type or structure", "category": "data", "aliases": ["audio1", "audio"], "status": "approved", "version": "1.0", "code": "ng:data:audio_1", "fallback": "[AUDIO1]", "unicode_point": "U+26A4"}, {"id": "NG0319", "symbol": "⚥", "name": "video", "description": "Video concept in data - data type or structure", "category": "data", "aliases": ["video", "video"], "status": "approved", "version": "1.0", "code": "ng:data:video", "fallback": "[VIDEO]", "unicode_point": "U+26A5"}, {"id": "NG0320", "symbol": "⚦", "name": "document", "description": "Document concept in data - data type or structure", "category": "data", "aliases": ["document", "document"], "status": "approved", "version": "1.0", "code": "ng:data:document", "fallback": "[DOCUMENT]", "unicode_point": "U+26A6"}, {"id": "NG0321", "symbol": "⚧", "name": "file", "description": "File concept in data - data type or structure", "category": "data", "aliases": ["file", "file"], "status": "approved", "version": "1.0", "code": "ng:data:file", "fallback": "[FILE]", "unicode_point": "U+26A7"}, {"id": "NG0322", "symbol": "⚨", "name": "byte", "description": "Byte concept in data - data type or structure", "category": "data", "aliases": ["byte", "byte"], "status": "approved", "version": "1.0", "code": "ng:data:byte", "fallback": "[BYTE]", "unicode_point": "U+26A8"}, {"id": "NG0323", "symbol": "⚩", "name": "bit", "description": "Bit concept in data - data type or structure", "category": "data", "aliases": ["bit", "bit"], "status": "approved", "version": "1.0", "code": "ng:data:bit", "fallback": "[BIT]", "unicode_point": "U+26A9"}, {"id": "NG0324", "symbol": "⚪", "name": "word", "description": "Word concept in data - data type or structure", "category": "data", "aliases": ["word", "word"], "status": "approved", "version": "1.0", "code": "ng:data:word", "fallback": "[WORD]", "unicode_point": "U+26AA"}, {"id": "NG0325", "symbol": "⚫", "name": "block_1", "description": "Block 1 concept in data - data type or structure", "category": "data", "aliases": ["block1", "block"], "status": "approved", "version": "1.0", "code": "ng:data:block_1", "fallback": "[BLOCK1]", "unicode_point": "U+26AB"}, {"id": "NG0326", "symbol": "⚬", "name": "page", "description": "Page concept in data - data type or structure", "category": "data", "aliases": ["page", "page"], "status": "approved", "version": "1.0", "code": "ng:data:page", "fallback": "[PAGE]", "unicode_point": "U+26AC"}, {"id": "NG0327", "symbol": "⚭", "name": "segment", "description": "Segment concept in data - data type or structure", "category": "data", "aliases": ["segment", "segment"], "status": "approved", "version": "1.0", "code": "ng:data:segment", "fallback": "[SEGMENT]", "unicode_point": "U+26AD"}, {"id": "NG0328", "symbol": "⚮", "name": "chunk", "description": "Chunk concept in data - data type or structure", "category": "data", "aliases": ["chunk", "chunk"], "status": "approved", "version": "1.0", "code": "ng:data:chunk", "fallback": "[CHUNK]", "unicode_point": "U+26AE"}, {"id": "NG0329", "symbol": "⚯", "name": "packet", "description": "Packet concept in data - data type or structure", "category": "data", "aliases": ["packet", "packet"], "status": "approved", "version": "1.0", "code": "ng:data:packet", "fallback": "[PACKET]", "unicode_point": "U+26AF"}, {"id": "NG0330", "symbol": "⚰", "name": "frame_1", "description": "Frame 1 concept in data - data type or structure", "category": "data", "aliases": ["frame1", "frame"], "status": "approved", "version": "1.0", "code": "ng:data:frame_1", "fallback": "[FRAME1]", "unicode_point": "U+26B0"}, {"id": "NG0331", "symbol": "⚱", "name": "message", "description": "Message concept in data - data type or structure", "category": "data", "aliases": ["message", "message"], "status": "approved", "version": "1.0", "code": "ng:data:message", "fallback": "[MESSAGE]", "unicode_point": "U+26B1"}, {"id": "NG0332", "symbol": "⚲", "name": "key", "description": "Key concept in data - data type or structure", "category": "data", "aliases": ["key", "key"], "status": "approved", "version": "1.0", "code": "ng:data:key", "fallback": "[KEY]", "unicode_point": "U+26B2"}, {"id": "NG0333", "symbol": "⚳", "name": "value_1", "description": "Value 1 concept in data - data type or structure", "category": "data", "aliases": ["value1", "value"], "status": "approved", "version": "1.0", "code": "ng:data:value_1", "fallback": "[VALUE1]", "unicode_point": "U+26B3"}, {"id": "NG0334", "symbol": "⚴", "name": "pair", "description": "Pair concept in data - data type or structure", "category": "data", "aliases": ["pair", "pair"], "status": "approved", "version": "1.0", "code": "ng:data:pair", "fallback": "[PAIR]", "unicode_point": "U+26B4"}, {"id": "NG0335", "symbol": "⚵", "name": "entry", "description": "Entry concept in data - data type or structure", "category": "data", "aliases": ["entry", "entry"], "status": "approved", "version": "1.0", "code": "ng:data:entry", "fallback": "[ENTRY]", "unicode_point": "U+26B5"}, {"id": "NG0336", "symbol": "⚶", "name": "item", "description": "Item concept in data - data type or structure", "category": "data", "aliases": ["item", "item"], "status": "approved", "version": "1.0", "code": "ng:data:item", "fallback": "[ITEM]", "unicode_point": "U+26B6"}, {"id": "NG0337", "symbol": "⚷", "name": "element_1", "description": "Element 1 concept in data - data type or structure", "category": "data", "aliases": ["element1", "element"], "status": "approved", "version": "1.0", "code": "ng:data:element_1", "fallback": "[ELEMENT1]", "unicode_point": "U+26B7"}, {"id": "NG0338", "symbol": "⚸", "name": "node_1", "description": "Node 1 concept in data - data type or structure", "category": "data", "aliases": ["node1", "node"], "status": "approved", "version": "1.0", "code": "ng:data:node_1", "fallback": "[NODE1]", "unicode_point": "U+26B8"}, {"id": "NG0339", "symbol": "⚹", "name": "leaf", "description": "Leaf concept in data - data type or structure", "category": "data", "aliases": ["leaf", "leaf"], "status": "approved", "version": "1.0", "code": "ng:data:leaf", "fallback": "[LEAF]", "unicode_point": "U+26B9"}, {"id": "NG0340", "symbol": "⚺", "name": "branch", "description": "Branch concept in data - data type or structure", "category": "data", "aliases": ["branch", "branch"], "status": "approved", "version": "1.0", "code": "ng:data:branch", "fallback": "[BRANCH]", "unicode_point": "U+26BA"}, {"id": "NG0341", "symbol": "⚻", "name": "root", "description": "Root concept in data - data type or structure", "category": "data", "aliases": ["root", "root"], "status": "approved", "version": "1.0", "code": "ng:data:root", "fallback": "[ROOT]", "unicode_point": "U+26BB"}, {"id": "NG0342", "symbol": "⚼", "name": "header", "description": "Header concept in data - data type or structure", "category": "data", "aliases": ["header", "header"], "status": "approved", "version": "1.0", "code": "ng:data:header", "fallback": "[HEADER]", "unicode_point": "U+26BC"}, {"id": "NG0343", "symbol": "⚽", "name": "body", "description": "Body concept in data - data type or structure", "category": "data", "aliases": ["body", "body"], "status": "approved", "version": "1.0", "code": "ng:data:body", "fallback": "[BODY]", "unicode_point": "U+26BD"}, {"id": "NG0344", "symbol": "⚾", "name": "footer", "description": "Footer concept in data - data type or structure", "category": "data", "aliases": ["footer", "footer"], "status": "approved", "version": "1.0", "code": "ng:data:footer", "fallback": "[FOOTER]", "unicode_point": "U+26BE"}, {"id": "NG0345", "symbol": "⚿", "name": "target", "description": "Target concept in meta - meta-programming concept", "category": "meta", "aliases": ["target", "target"], "status": "approved", "version": "1.0", "code": "ng:meta:target", "fallback": "[TARGET]", "unicode_point": "U+26BF"}, {"id": "NG0346", "symbol": "⧀", "name": "measure_1", "description": "Measure 1 concept in meta - meta-programming concept", "category": "meta", "aliases": ["measure1", "measure"], "status": "approved", "version": "1.0", "code": "ng:meta:measure_1", "fallback": "[MEASURE1]", "unicode_point": "U+29C0"}, {"id": "NG0347", "symbol": "⧁", "name": "iterate", "description": "Iterate concept in meta - meta-programming concept", "category": "meta", "aliases": ["iterate", "iterate"], "status": "approved", "version": "1.0", "code": "ng:meta:iterate", "fallback": "[ITERATE]", "unicode_point": "U+29C1"}, {"id": "NG0348", "symbol": "⧂", "name": "configure", "description": "Configure concept in meta - meta-programming concept", "category": "meta", "aliases": ["configure", "configure"], "status": "approved", "version": "1.0", "code": "ng:meta:configure", "fallback": "[CONFIGURE]", "unicode_point": "U+29C2"}, {"id": "NG0349", "symbol": "⧃", "name": "optimize", "description": "Optimize concept in meta - meta-programming concept", "category": "meta", "aliases": ["optimize", "optimize"], "status": "approved", "version": "1.0", "code": "ng:meta:optimize", "fallback": "[OPTIMIZE]", "unicode_point": "U+29C3"}, {"id": "NG0350", "symbol": "⧄", "name": "profile", "description": "Profile concept in meta - meta-programming concept", "category": "meta", "aliases": ["profile", "profile"], "status": "approved", "version": "1.0", "code": "ng:meta:profile", "fallback": "[PROFILE]", "unicode_point": "U+29C4"}, {"id": "NG0351", "symbol": "⧅", "name": "benchmark", "description": "Benchmark concept in meta - meta-programming concept", "category": "meta", "aliases": ["benchmark", "benchmark"], "status": "approved", "version": "1.0", "code": "ng:meta:benchmark", "fallback": "[BENCHMARK]", "unicode_point": "U+29C5"}, {"id": "NG0352", "symbol": "⧆", "name": "test_1", "description": "Test 1 concept in meta - meta-programming concept", "category": "meta", "aliases": ["test1", "test"], "status": "approved", "version": "1.0", "code": "ng:meta:test_1", "fallback": "[TEST1]", "unicode_point": "U+29C6"}, {"id": "NG0353", "symbol": "⧇", "name": "mock", "description": "Mock concept in meta - meta-programming concept", "category": "meta", "aliases": ["mock", "mock"], "status": "approved", "version": "1.0", "code": "ng:meta:mock", "fallback": "[MOCK]", "unicode_point": "U+29C7"}, {"id": "NG0354", "symbol": "⧈", "name": "stub", "description": "Stub concept in meta - meta-programming concept", "category": "meta", "aliases": ["stub", "stub"], "status": "approved", "version": "1.0", "code": "ng:meta:stub", "fallback": "[STUB]", "unicode_point": "U+29C8"}, {"id": "NG0355", "symbol": "⧉", "name": "annotation", "description": "Annotation concept in meta - meta-programming concept", "category": "meta", "aliases": ["annotation", "annotation"], "status": "approved", "version": "1.0", "code": "ng:meta:annotation", "fallback": "[ANNOTATION]", "unicode_point": "U+29C9"}, {"id": "NG0356", "symbol": "⧊", "name": "decorator", "description": "Decorator concept in meta - meta-programming concept", "category": "meta", "aliases": ["decorator", "decorator"], "status": "approved", "version": "1.0", "code": "ng:meta:decorator", "fallback": "[DECORATOR]", "unicode_point": "U+29CA"}, {"id": "NG0357", "symbol": "⧋", "name": "attribute_1", "description": "Attribute 1 concept in meta - meta-programming concept", "category": "meta", "aliases": ["attribute1", "attribute"], "status": "approved", "version": "1.0", "code": "ng:meta:attribute_1", "fallback": "[ATTRIBUTE1]", "unicode_point": "U+29CB"}, {"id": "NG0358", "symbol": "⧌", "name": "property_1", "description": "Property 1 concept in meta - meta-programming concept", "category": "meta", "aliases": ["property1", "property"], "status": "approved", "version": "1.0", "code": "ng:meta:property_1", "fallback": "[PROPERTY1]", "unicode_point": "U+29CC"}, {"id": "NG0359", "symbol": "⧍", "name": "metadata", "description": "Metadata concept in meta - meta-programming concept", "category": "meta", "aliases": ["metadata", "metadata"], "status": "approved", "version": "1.0", "code": "ng:meta:metadata", "fallback": "[METADATA]", "unicode_point": "U+29CD"}, {"id": "NG0360", "symbol": "⧎", "name": "reflection", "description": "Reflection concept in meta - meta-programming concept", "category": "meta", "aliases": ["reflection", "reflection"], "status": "approved", "version": "1.0", "code": "ng:meta:reflection", "fallback": "[REFLECTION]", "unicode_point": "U+29CE"}, {"id": "NG0361", "symbol": "⧏", "name": "introspection", "description": "Introspection concept in meta - meta-programming concept", "category": "meta", "aliases": ["introspection", "introspection"], "status": "approved", "version": "1.0", "code": "ng:meta:introspection", "fallback": "[INTROSPECTION]", "unicode_point": "U+29CF"}, {"id": "NG0362", "symbol": "⧐", "name": "inspection", "description": "Inspection concept in meta - meta-programming concept", "category": "meta", "aliases": ["inspection", "inspection"], "status": "approved", "version": "1.0", "code": "ng:meta:inspection", "fallback": "[INSPECTION]", "unicode_point": "U+29D0"}, {"id": "NG0363", "symbol": "⧑", "name": "analysis", "description": "Analysis concept in meta - meta-programming concept", "category": "meta", "aliases": ["analysis", "analysis"], "status": "approved", "version": "1.0", "code": "ng:meta:analysis", "fallback": "[ANALYSIS]", "unicode_point": "U+29D1"}, {"id": "NG0364", "symbol": "⧒", "name": "synthesis", "description": "Synthesis concept in meta - meta-programming concept", "category": "meta", "aliases": ["synthesis", "synthesis"], "status": "approved", "version": "1.0", "code": "ng:meta:synthesis", "fallback": "[SYNTHESIS]", "unicode_point": "U+29D2"}, {"id": "NG0365", "symbol": "⧓", "name": "generation", "description": "Generation concept in meta - meta-programming concept", "category": "meta", "aliases": ["generation", "generation"], "status": "approved", "version": "1.0", "code": "ng:meta:generation", "fallback": "[GENERATION]", "unicode_point": "U+29D3"}, {"id": "NG0366", "symbol": "⧔", "name": "compilation", "description": "Compilation concept in meta - meta-programming concept", "category": "meta", "aliases": ["compilation", "compilation"], "status": "approved", "version": "1.0", "code": "ng:meta:compilation", "fallback": "[COMPILATION]", "unicode_point": "U+29D4"}, {"id": "NG0367", "symbol": "⧕", "name": "interpretation", "description": "Interpretation concept in meta - meta-programming concept", "category": "meta", "aliases": ["interpretation", "interpretation"], "status": "approved", "version": "1.0", "code": "ng:meta:interpretation", "fallback": "[INTERPRETATION]", "unicode_point": "U+29D5"}, {"id": "NG0368", "symbol": "⧖", "name": "evaluation", "description": "Evaluation concept in meta - meta-programming concept", "category": "meta", "aliases": ["evaluation", "evaluation"], "status": "approved", "version": "1.0", "code": "ng:meta:evaluation", "fallback": "[EVALUATION]", "unicode_point": "U+29D6"}, {"id": "NG0369", "symbol": "⧗", "name": "execution", "description": "Execution concept in meta - meta-programming concept", "category": "meta", "aliases": ["execution", "execution"], "status": "approved", "version": "1.0", "code": "ng:meta:execution", "fallback": "[EXECUTION]", "unicode_point": "U+29D7"}, {"id": "NG0370", "symbol": "⧘", "name": "simulation", "description": "Simulation concept in meta - meta-programming concept", "category": "meta", "aliases": ["simulation", "simulation"], "status": "approved", "version": "1.0", "code": "ng:meta:simulation", "fallback": "[SIMULATION]", "unicode_point": "U+29D8"}, {"id": "NG0371", "symbol": "⧙", "name": "emulation", "description": "Emulation concept in meta - meta-programming concept", "category": "meta", "aliases": ["emulation", "emulation"], "status": "approved", "version": "1.0", "code": "ng:meta:emulation", "fallback": "[EMULATION]", "unicode_point": "U+29D9"}, {"id": "NG0372", "symbol": "⧚", "name": "virtualization", "description": "Virtualization concept in meta - meta-programming concept", "category": "meta", "aliases": ["virtualization", "virtualization"], "status": "approved", "version": "1.0", "code": "ng:meta:virtualization", "fallback": "[VIRTUALIZATION]", "unicode_point": "U+29DA"}, {"id": "NG0373", "symbol": "⧛", "name": "abstraction", "description": "Abstraction concept in meta - meta-programming concept", "category": "meta", "aliases": ["abstraction", "abstraction"], "status": "approved", "version": "1.0", "code": "ng:meta:abstraction", "fallback": "[ABSTRACTION]", "unicode_point": "U+29DB"}, {"id": "NG0374", "symbol": "⧜", "name": "encapsulation", "description": "Encapsulation concept in meta - meta-programming concept", "category": "meta", "aliases": ["encapsulation", "encapsulation"], "status": "approved", "version": "1.0", "code": "ng:meta:encapsulation", "fallback": "[ENCAPSULATION]", "unicode_point": "U+29DC"}, {"id": "NG0375", "symbol": "⧝", "name": "inheritance", "description": "Inheritance concept in meta - meta-programming concept", "category": "meta", "aliases": ["inheritance", "inheritance"], "status": "approved", "version": "1.0", "code": "ng:meta:inheritance", "fallback": "[INHERITANCE]", "unicode_point": "U+29DD"}, {"id": "NG0376", "symbol": "⧞", "name": "composition", "description": "Composition concept in meta - meta-programming concept", "category": "meta", "aliases": ["composition", "composition"], "status": "approved", "version": "1.0", "code": "ng:meta:composition", "fallback": "[COMPOSITION]", "unicode_point": "U+29DE"}, {"id": "NG0377", "symbol": "⧟", "name": "aggregation", "description": "Aggregation concept in meta - meta-programming concept", "category": "meta", "aliases": ["aggregation", "aggregation"], "status": "approved", "version": "1.0", "code": "ng:meta:aggregation", "fallback": "[AGGREGATION]", "unicode_point": "U+29DF"}, {"id": "NG0378", "symbol": "✓", "name": "association", "description": "Association concept in meta - meta-programming concept", "category": "meta", "aliases": ["association", "association"], "status": "approved", "version": "1.0", "code": "ng:meta:association", "fallback": "[ASSOCIATION]", "unicode_point": "U+2713"}, {"id": "NG0379", "symbol": "✔", "name": "dependency", "description": "Dependency concept in meta - meta-programming concept", "category": "meta", "aliases": ["dependency", "dependency"], "status": "approved", "version": "1.0", "code": "ng:meta:dependency", "fallback": "[DEPENDENCY]", "unicode_point": "U+2714"}, {"id": "NG0380", "symbol": "✕", "name": "coupling", "description": "Coupling concept in meta - meta-programming concept", "category": "meta", "aliases": ["coupling", "coupling"], "status": "approved", "version": "1.0", "code": "ng:meta:coupling", "fallback": "[COUPLING]", "unicode_point": "U+2715"}, {"id": "NG0381", "symbol": "✖", "name": "cohesion", "description": "Cohesion concept in meta - meta-programming concept", "category": "meta", "aliases": ["cohesion", "cohesion"], "status": "approved", "version": "1.0", "code": "ng:meta:cohesion", "fallback": "[COHESION]", "unicode_point": "U+2716"}, {"id": "NG0382", "symbol": "✗", "name": "modularity", "description": "Modularity concept in meta - meta-programming concept", "category": "meta", "aliases": ["modularity", "modularity"], "status": "approved", "version": "1.0", "code": "ng:meta:modularity", "fallback": "[MODULARITY]", "unicode_point": "U+2717"}, {"id": "NG0383", "symbol": "✘", "name": "reusability", "description": "Reusability concept in meta - meta-programming concept", "category": "meta", "aliases": ["reusability", "reusability"], "status": "approved", "version": "1.0", "code": "ng:meta:reusability", "fallback": "[REUSABILITY]", "unicode_point": "U+2718"}, {"id": "NG0384", "symbol": "✙", "name": "maintainability", "description": "Maintainability concept in meta - meta-programming concept", "category": "meta", "aliases": ["maintainability", "maintainability"], "status": "approved", "version": "1.0", "code": "ng:meta:maintainability", "fallback": "[MAINTAINABILITY]", "unicode_point": "U+2719"}, {"id": "NG0385", "symbol": "✚", "name": "scalability", "description": "Scalability concept in meta - meta-programming concept", "category": "meta", "aliases": ["scalability", "scalability"], "status": "approved", "version": "1.0", "code": "ng:meta:scalability", "fallback": "[SCALABILITY]", "unicode_point": "U+271A"}, {"id": "NG0386", "symbol": "✛", "name": "performance", "description": "Performance concept in meta - meta-programming concept", "category": "meta", "aliases": ["performance", "performance"], "status": "approved", "version": "1.0", "code": "ng:meta:performance", "fallback": "[PERFORMANCE]", "unicode_point": "U+271B"}, {"id": "NG0387", "symbol": "✜", "name": "kernel", "description": "Kernel concept in system - system-level component", "category": "system", "aliases": ["kernel", "kernel"], "status": "approved", "version": "1.0", "code": "ng:system:kernel", "fallback": "[KERNEL]", "unicode_point": "U+271C"}, {"id": "NG0388", "symbol": "✝", "name": "driver", "description": "Driver concept in system - system-level component", "category": "system", "aliases": ["driver", "driver"], "status": "approved", "version": "1.0", "code": "ng:system:driver", "fallback": "[DRIVER]", "unicode_point": "U+271D"}, {"id": "NG0389", "symbol": "✞", "name": "daemon", "description": "Daemon concept in system - system-level component", "category": "system", "aliases": ["daemon", "daemon"], "status": "approved", "version": "1.0", "code": "ng:system:daemon", "fallback": "[DAEMON]", "unicode_point": "U+271E"}, {"id": "NG0390", "symbol": "✟", "name": "service_1", "description": "Service 1 concept in system - system-level component", "category": "system", "aliases": ["service1", "service"], "status": "approved", "version": "1.0", "code": "ng:system:service_1", "fallback": "[SERVICE1]", "unicode_point": "U+271F"}, {"id": "NG0391", "symbol": "✠", "name": "process_1", "description": "Process 1 concept in system - system-level component", "category": "system", "aliases": ["process1", "process"], "status": "approved", "version": "1.0", "code": "ng:system:process_1", "fallback": "[PROCESS1]", "unicode_point": "U+2720"}, {"id": "NG0392", "symbol": "✡", "name": "thread_1", "description": "Thread 1 concept in system - system-level component", "category": "system", "aliases": ["thread1", "thread"], "status": "approved", "version": "1.0", "code": "ng:system:thread_1", "fallback": "[THREAD1]", "unicode_point": "U+2721"}, {"id": "NG0393", "symbol": "✢", "name": "fiber", "description": "Fiber concept in system - system-level component", "category": "system", "aliases": ["fiber", "fiber"], "status": "approved", "version": "1.0", "code": "ng:system:fiber", "fallback": "[FIBER]", "unicode_point": "U+2722"}, {"id": "NG0394", "symbol": "✣", "name": "coroutine", "description": "Coroutine concept in system - system-level component", "category": "system", "aliases": ["coroutine", "coroutine"], "status": "approved", "version": "1.0", "code": "ng:system:coroutine", "fallback": "[COROUTINE]", "unicode_point": "U+2723"}, {"id": "NG0395", "symbol": "✤", "name": "task_1", "description": "Task 1 concept in system - system-level component", "category": "system", "aliases": ["task1", "task"], "status": "approved", "version": "1.0", "code": "ng:system:task_1", "fallback": "[TASK1]", "unicode_point": "U+2724"}, {"id": "NG0396", "symbol": "✥", "name": "job_1", "description": "Job 1 concept in system - system-level component", "category": "system", "aliases": ["job1", "job"], "status": "approved", "version": "1.0", "code": "ng:system:job_1", "fallback": "[JOB1]", "unicode_point": "U+2725"}, {"id": "NG0397", "symbol": "✦", "name": "scheduler", "description": "Scheduler concept in system - system-level component", "category": "system", "aliases": ["scheduler", "scheduler"], "status": "approved", "version": "1.0", "code": "ng:system:scheduler", "fallback": "[SCHEDULER]", "unicode_point": "U+2726"}, {"id": "NG0398", "symbol": "✧", "name": "dispatcher", "description": "Dispatcher concept in system - system-level component", "category": "system", "aliases": ["dispatcher", "dispatcher"], "status": "approved", "version": "1.0", "code": "ng:system:dispatcher", "fallback": "[DISPATCHER]", "unicode_point": "U+2727"}, {"id": "NG0399", "symbol": "✨", "name": "allocator", "description": "Allocator concept in system - system-level component", "category": "system", "aliases": ["allocator", "allocator"], "status": "approved", "version": "1.0", "code": "ng:system:allocator", "fallback": "[ALLOCATOR]", "unicode_point": "U+2728"}, {"id": "NG0400", "symbol": "✩", "name": "garbage_collector", "description": "Garbage Collector concept in system - system-level component", "category": "system", "aliases": ["garbagecollector", "garbage"], "status": "approved", "version": "1.0", "code": "ng:system:garbage_collector", "fallback": "[GARBAGECOLLECTOR]", "unicode_point": "U+2729"}, {"id": "NG0401", "symbol": "✪", "name": "memory_manager", "description": "Memory Manager concept in system - system-level component", "category": "system", "aliases": ["memorymanager", "memory"], "status": "approved", "version": "1.0", "code": "ng:system:memory_manager", "fallback": "[MEMORYMANAGER]", "unicode_point": "U+272A"}, {"id": "NG0402", "symbol": "✫", "name": "file_system", "description": "File System concept in system - system-level component", "category": "system", "aliases": ["filesystem", "file"], "status": "approved", "version": "1.0", "code": "ng:system:file_system", "fallback": "[FILESYSTEM]", "unicode_point": "U+272B"}, {"id": "NG0403", "symbol": "✬", "name": "network_stack", "description": "Network Stack concept in system - system-level component", "category": "system", "aliases": ["networkstack", "network"], "status": "approved", "version": "1.0", "code": "ng:system:network_stack", "fallback": "[NETWORKSTACK]", "unicode_point": "U+272C"}, {"id": "NG0404", "symbol": "✭", "name": "protocol_handler", "description": "Protocol Handler concept in system - system-level component", "category": "system", "aliases": ["protocolhandler", "protocol"], "status": "approved", "version": "1.0", "code": "ng:system:protocol_handler", "fallback": "[PROTOCOLHANDLER]", "unicode_point": "U+272D"}, {"id": "NG0405", "symbol": "✮", "name": "device_manager", "description": "Device Manager concept in system - system-level component", "category": "system", "aliases": ["devicemanager", "device"], "status": "approved", "version": "1.0", "code": "ng:system:device_manager", "fallback": "[DEVICEMANAGER]", "unicode_point": "U+272E"}, {"id": "NG0406", "symbol": "✯", "name": "interrupt_handler", "description": "Interrupt Handler concept in system - system-level component", "category": "system", "aliases": ["interrupthandler", "interrupt"], "status": "approved", "version": "1.0", "code": "ng:system:interrupt_handler", "fallback": "[INTERRUPTHANDLER]", "unicode_point": "U+272F"}, {"id": "NG0407", "symbol": "✰", "name": "bootloader", "description": "Bootloader concept in system - system-level component", "category": "system", "aliases": ["bootloader", "bootloader"], "status": "approved", "version": "1.0", "code": "ng:system:bootloader", "fallback": "[BOOTLOADER]", "unicode_point": "U+2730"}, {"id": "NG0408", "symbol": "✱", "name": "firmware", "description": "Firmware concept in system - system-level component", "category": "system", "aliases": ["firmware", "firmware"], "status": "approved", "version": "1.0", "code": "ng:system:firmware", "fallback": "[FIRMWARE]", "unicode_point": "U+2731"}, {"id": "NG0409", "symbol": "✲", "name": "bios", "description": "Bios concept in system - system-level component", "category": "system", "aliases": ["bios", "bios"], "status": "approved", "version": "1.0", "code": "ng:system:bios", "fallback": "[BIOS]", "unicode_point": "U+2732"}, {"id": "NG0410", "symbol": "⋆", "name": "uefi", "description": "Uefi concept in system - system-level component", "category": "system", "aliases": ["uefi", "uefi"], "status": "approved", "version": "1.0", "code": "ng:system:uefi", "fallback": "[UEFI]", "unicode_point": "U+22C6"}, {"id": "NG0411", "symbol": "⋇", "name": "hypervisor", "description": "Hypervisor concept in system - system-level component", "category": "system", "aliases": ["hypervisor", "hypervisor"], "status": "approved", "version": "1.0", "code": "ng:system:hypervisor", "fallback": "[HYPERVISOR]", "unicode_point": "U+22C7"}, {"id": "NG0412", "symbol": "⋈", "name": "container", "description": "Container concept in system - system-level component", "category": "system", "aliases": ["container", "container"], "status": "approved", "version": "1.0", "code": "ng:system:container", "fallback": "[CONTAINER]", "unicode_point": "U+22C8"}, {"id": "NG0413", "symbol": "⋉", "name": "sandbox", "description": "Sandbox concept in system - system-level component", "category": "system", "aliases": ["sandbox", "sandbox"], "status": "approved", "version": "1.0", "code": "ng:system:sandbox", "fallback": "[SANDBOX]", "unicode_point": "U+22C9"}, {"id": "NG0414", "symbol": "⋊", "name": "jail", "description": "Jail concept in system - system-level component", "category": "system", "aliases": ["jail", "jail"], "status": "approved", "version": "1.0", "code": "ng:system:jail", "fallback": "[JAIL]", "unicode_point": "U+22CA"}, {"id": "NG0415", "symbol": "⋋", "name": "chroot", "description": "Chroot concept in system - system-level component", "category": "system", "aliases": ["chroot", "chroot"], "status": "approved", "version": "1.0", "code": "ng:system:chroot", "fallback": "[CHROOT]", "unicode_point": "U+22CB"}, {"id": "NG0416", "symbol": "⋌", "name": "namespace_1", "description": "Namespace 1 concept in system - system-level component", "category": "system", "aliases": ["namespace1", "namespace"], "status": "approved", "version": "1.0", "code": "ng:system:namespace_1", "fallback": "[NAMESPACE1]", "unicode_point": "U+22CC"}, {"id": "NG0417", "symbol": "⋍", "name": "registry", "description": "Registry concept in system - system-level component", "category": "system", "aliases": ["registry", "registry"], "status": "approved", "version": "1.0", "code": "ng:system:registry", "fallback": "[REGISTRY]", "unicode_point": "U+22CD"}, {"id": "NG0418", "symbol": "⋎", "name": "configuration", "description": "Configuration concept in system - system-level component", "category": "system", "aliases": ["configuration", "configuration"], "status": "approved", "version": "1.0", "code": "ng:system:configuration", "fallback": "[CONFIGURATION]", "unicode_point": "U+22CE"}, {"id": "NG0419", "symbol": "⋏", "name": "environment", "description": "Environment concept in system - system-level component", "category": "system", "aliases": ["environment", "environment"], "status": "approved", "version": "1.0", "code": "ng:system:environment", "fallback": "[ENVIRONMENT]", "unicode_point": "U+22CF"}, {"id": "NG0420", "symbol": "⋐", "name": "variable_1", "description": "Variable 1 concept in system - system-level component", "category": "system", "aliases": ["variable1", "variable"], "status": "approved", "version": "1.0", "code": "ng:system:variable_1", "fallback": "[VARIABLE1]", "unicode_point": "U+22D0"}, {"id": "NG0421", "symbol": "⋑", "name": "path", "description": "Path concept in system - system-level component", "category": "system", "aliases": ["path", "path"], "status": "approved", "version": "1.0", "code": "ng:system:path", "fallback": "[PATH]", "unicode_point": "U+22D1"}, {"id": "NG0422", "symbol": "⋒", "name": "library", "description": "Library concept in system - system-level component", "category": "system", "aliases": ["library", "library"], "status": "approved", "version": "1.0", "code": "ng:system:library", "fallback": "[LIBRARY]", "unicode_point": "U+22D2"}, {"id": "NG0423", "symbol": "⋓", "name": "framework", "description": "Framework concept in system - system-level component", "category": "system", "aliases": ["framework", "framework"], "status": "approved", "version": "1.0", "code": "ng:system:framework", "fallback": "[FRAMEWORK]", "unicode_point": "U+22D3"}, {"id": "NG0424", "symbol": "⋔", "name": "runtime", "description": "Runtime concept in system - system-level component", "category": "system", "aliases": ["runtime", "runtime"], "status": "approved", "version": "1.0", "code": "ng:system:runtime", "fallback": "[RUNTIME]", "unicode_point": "U+22D4"}, {"id": "NG0425", "symbol": "⋕", "name": "virtual_machine", "description": "Virtual Machine concept in system - system-level component", "category": "system", "aliases": ["virtualmachine", "virtual"], "status": "approved", "version": "1.0", "code": "ng:system:virtual_machine", "fallback": "[VIRTUALMACHINE]", "unicode_point": "U+22D5"}, {"id": "NG0426", "symbol": "⋖", "name": "interpreter", "description": "Interpreter concept in system - system-level component", "category": "system", "aliases": ["interpreter", "interpreter"], "status": "approved", "version": "1.0", "code": "ng:system:interpreter", "fallback": "[INTERPRETER]", "unicode_point": "U+22D6"}, {"id": "NG0427", "symbol": "⋗", "name": "compiler", "description": "Compiler concept in system - system-level component", "category": "system", "aliases": ["compiler", "compiler"], "status": "approved", "version": "1.0", "code": "ng:system:compiler", "fallback": "[COMPILER]", "unicode_point": "U+22D7"}, {"id": "NG0428", "symbol": "⋘", "name": "linker", "description": "Linker concept in system - system-level component", "category": "system", "aliases": ["linker", "linker"], "status": "approved", "version": "1.0", "code": "ng:system:linker", "fallback": "[LINKER]", "unicode_point": "U+22D8"}, {"id": "NG0429", "symbol": "⋙", "name": "store", "description": "Store concept in memory - memory management operation", "category": "memory", "aliases": ["store", "store"], "status": "approved", "version": "1.0", "code": "ng:memory:store", "fallback": "[STORE]", "unicode_point": "U+22D9"}, {"id": "NG0430", "symbol": "⋚", "name": "retrieve", "description": "Retrieve concept in memory - memory management operation", "category": "memory", "aliases": ["retrieve", "retrieve"], "status": "approved", "version": "1.0", "code": "ng:memory:retrieve", "fallback": "[RETRIEVE]", "unicode_point": "U+22DA"}, {"id": "NG0431", "symbol": "⋛", "name": "pin", "description": "Pin concept in memory - memory management operation", "category": "memory", "aliases": ["pin", "pin"], "status": "approved", "version": "1.0", "code": "ng:memory:pin", "fallback": "[PIN]", "unicode_point": "U+22DB"}, {"id": "NG0432", "symbol": "⋜", "name": "index", "description": "Index concept in memory - memory management operation", "category": "memory", "aliases": ["index", "index"], "status": "approved", "version": "1.0", "code": "ng:memory:index", "fallback": "[INDEX]", "unicode_point": "U+22DC"}, {"id": "NG0433", "symbol": "⋝", "name": "link", "description": "Link concept in memory - memory management operation", "category": "memory", "aliases": ["link", "link"], "status": "approved", "version": "1.0", "code": "ng:memory:link", "fallback": "[LINK]", "unicode_point": "U+22DD"}, {"id": "NG0434", "symbol": "⋞", "name": "pattern_1", "description": "Pattern 1 concept in memory - memory management operation", "category": "memory", "aliases": ["pattern1", "pattern"], "status": "approved", "version": "1.0", "code": "ng:memory:pattern_1", "fallback": "[PATTERN1]", "unicode_point": "U+22DE"}, {"id": "NG0435", "symbol": "⋟", "name": "evolve", "description": "Evolve concept in memory - memory management operation", "category": "memory", "aliases": ["evolve", "evolve"], "status": "approved", "version": "1.0", "code": "ng:memory:evolve", "fallback": "[EVOLVE]", "unicode_point": "U+22DF"}, {"id": "NG0436", "symbol": "⋠", "name": "cache_1", "description": "Cache 1 concept in memory - memory management operation", "category": "memory", "aliases": ["cache1", "cache"], "status": "approved", "version": "1.0", "code": "ng:memory:cache_1", "fallback": "[CACHE1]", "unicode_point": "U+22E0"}, {"id": "NG0437", "symbol": "⋡", "name": "buffer_1", "description": "Buffer 1 concept in memory - memory management operation", "category": "memory", "aliases": ["buffer1", "buffer"], "status": "approved", "version": "1.0", "code": "ng:memory:buffer_1", "fallback": "[BUFFER1]", "unicode_point": "U+22E1"}, {"id": "NG0438", "symbol": "⋢", "name": "pool", "description": "Pool concept in memory - memory management operation", "category": "memory", "aliases": ["pool", "pool"], "status": "approved", "version": "1.0", "code": "ng:memory:pool", "fallback": "[POOL]", "unicode_point": "U+22E2"}, {"id": "NG0439", "symbol": "⋣", "name": "heap", "description": "Heap concept in memory - memory management operation", "category": "memory", "aliases": ["heap", "heap"], "status": "approved", "version": "1.0", "code": "ng:memory:heap", "fallback": "[HEAP]", "unicode_point": "U+22E3"}, {"id": "NG0440", "symbol": "⋤", "name": "stack_1", "description": "Stack 1 concept in memory - memory management operation", "category": "memory", "aliases": ["stack1", "stack"], "status": "approved", "version": "1.0", "code": "ng:memory:stack_1", "fallback": "[STACK1]", "unicode_point": "U+22E4"}, {"id": "NG0441", "symbol": "⋥", "name": "register", "description": "Register concept in memory - memory management operation", "category": "memory", "aliases": ["register", "register"], "status": "approved", "version": "1.0", "code": "ng:memory:register", "fallback": "[REGISTER]", "unicode_point": "U+22E5"}, {"id": "NG0442", "symbol": "📌", "name": "page_1", "description": "Page 1 concept in memory - memory management operation", "category": "memory", "aliases": ["page1", "page"], "status": "approved", "version": "1.0", "code": "ng:memory:page_1", "fallback": "[PAGE1]", "unicode_point": "U+1F4CC"}, {"id": "NG0443", "symbol": "📍", "name": "segment_1", "description": "Segment 1 concept in memory - memory management operation", "category": "memory", "aliases": ["segment1", "segment"], "status": "approved", "version": "1.0", "code": "ng:memory:segment_1", "fallback": "[SEGMENT1]", "unicode_point": "U+1F4CD"}, {"id": "NG0444", "symbol": "📎", "name": "block_1", "description": "Block 1 concept in memory - memory management operation", "category": "memory", "aliases": ["block1", "block"], "status": "approved", "version": "1.0", "code": "ng:memory:block_1", "fallback": "[BLOCK1]", "unicode_point": "U+1F4CE"}, {"id": "NG0445", "symbol": "📏", "name": "chunk_1", "description": "Chunk 1 concept in memory - memory management operation", "category": "memory", "aliases": ["chunk1", "chunk"], "status": "approved", "version": "1.0", "code": "ng:memory:chunk_1", "fallback": "[CHUNK1]", "unicode_point": "U+1F4CF"}, {"id": "NG0446", "symbol": "📐", "name": "slab", "description": "Slab concept in memory - memory management operation", "category": "memory", "aliases": ["slab", "slab"], "status": "approved", "version": "1.0", "code": "ng:memory:slab", "fallback": "[SLAB]", "unicode_point": "U+1F4D0"}, {"id": "NG0447", "symbol": "📑", "name": "arena", "description": "Arena concept in memory - memory management operation", "category": "memory", "aliases": ["arena", "arena"], "status": "approved", "version": "1.0", "code": "ng:memory:arena", "fallback": "[ARENA]", "unicode_point": "U+1F4D1"}, {"id": "NG0448", "symbol": "📒", "name": "zone", "description": "Zone concept in memory - memory management operation", "category": "memory", "aliases": ["zone", "zone"], "status": "approved", "version": "1.0", "code": "ng:memory:zone", "fallback": "[ZONE]", "unicode_point": "U+1F4D2"}, {"id": "NG0449", "symbol": "📓", "name": "allocate", "description": "Allocate concept in memory - memory management operation", "category": "memory", "aliases": ["allocate", "allocate"], "status": "approved", "version": "1.0", "code": "ng:memory:allocate", "fallback": "[ALLOCATE]", "unicode_point": "U+1F4D3"}, {"id": "NG0450", "symbol": "📔", "name": "deallocate", "description": "Deallocate concept in memory - memory management operation", "category": "memory", "aliases": ["deallocate", "deallocate"], "status": "approved", "version": "1.0", "code": "ng:memory:deallocate", "fallback": "[DEALLOCATE]", "unicode_point": "U+1F4D4"}, {"id": "NG0451", "symbol": "📕", "name": "free", "description": "Free concept in memory - memory management operation", "category": "memory", "aliases": ["free", "free"], "status": "approved", "version": "1.0", "code": "ng:memory:free", "fallback": "[FREE]", "unicode_point": "U+1F4D5"}, {"id": "NG0452", "symbol": "📖", "name": "reserve", "description": "Reserve concept in memory - memory management operation", "category": "memory", "aliases": ["reserve", "reserve"], "status": "approved", "version": "1.0", "code": "ng:memory:reserve", "fallback": "[RESERVE]", "unicode_point": "U+1F4D6"}, {"id": "NG0453", "symbol": "📗", "name": "commit", "description": "Commit concept in memory - memory management operation", "category": "memory", "aliases": ["commit", "commit"], "status": "approved", "version": "1.0", "code": "ng:memory:commit", "fallback": "[COMMIT]", "unicode_point": "U+1F4D7"}, {"id": "NG0454", "symbol": "📘", "name": "protect", "description": "Protect concept in memory - memory management operation", "category": "memory", "aliases": ["protect", "protect"], "status": "approved", "version": "1.0", "code": "ng:memory:protect", "fallback": "[PROTECT]", "unicode_point": "U+1F4D8"}, {"id": "NG0455", "symbol": "📙", "name": "map_1", "description": "Map 1 concept in memory - memory management operation", "category": "memory", "aliases": ["map1", "map"], "status": "approved", "version": "1.0", "code": "ng:memory:map_1", "fallback": "[MAP1]", "unicode_point": "U+1F4D9"}, {"id": "NG0456", "symbol": "📚", "name": "unmap", "description": "Unmap concept in memory - memory management operation", "category": "memory", "aliases": ["unmap", "unmap"], "status": "approved", "version": "1.0", "code": "ng:memory:unmap", "fallback": "[UNMAP]", "unicode_point": "U+1F4DA"}, {"id": "NG0457", "symbol": "📛", "name": "flush", "description": "Flush concept in memory - memory management operation", "category": "memory", "aliases": ["flush", "flush"], "status": "approved", "version": "1.0", "code": "ng:memory:flush", "fallback": "[FLUSH]", "unicode_point": "U+1F4DB"}, {"id": "NG0458", "symbol": "📜", "name": "sync", "description": "Sync concept in memory - memory management operation", "category": "memory", "aliases": ["sync", "sync"], "status": "approved", "version": "1.0", "code": "ng:memory:sync", "fallback": "[SYNC]", "unicode_point": "U+1F4DC"}, {"id": "NG0459", "symbol": "📝", "name": "remember", "description": "Remember concept in memory - memory management operation", "category": "memory", "aliases": ["remember", "remember"], "status": "approved", "version": "1.0", "code": "ng:memory:remember", "fallback": "[REMEMBER]", "unicode_point": "U+1F4DD"}, {"id": "NG0460", "symbol": "📞", "name": "forget", "description": "Forget concept in memory - memory management operation", "category": "memory", "aliases": ["forget", "forget"], "status": "approved", "version": "1.0", "code": "ng:memory:forget", "fallback": "[FORGET]", "unicode_point": "U+1F4DE"}, {"id": "NG0461", "symbol": "📟", "name": "recall", "description": "Recall concept in memory - memory management operation", "category": "memory", "aliases": ["recall", "recall"], "status": "approved", "version": "1.0", "code": "ng:memory:recall", "fallback": "[RECALL]", "unicode_point": "U+1F4DF"}, {"id": "NG0462", "symbol": "📠", "name": "recognize", "description": "Recognize concept in memory - memory management operation", "category": "memory", "aliases": ["recognize", "recognize"], "status": "approved", "version": "1.0", "code": "ng:memory:recognize", "fallback": "[RECOGNIZE]", "unicode_point": "U+1F4E0"}, {"id": "NG0463", "symbol": "📡", "name": "associate", "description": "Associate concept in memory - memory management operation", "category": "memory", "aliases": ["associate", "associate"], "status": "approved", "version": "1.0", "code": "ng:memory:associate", "fallback": "[ASSOCIATE]", "unicode_point": "U+1F4E1"}, {"id": "NG0464", "symbol": "📢", "name": "relate", "description": "Relate concept in memory - memory management operation", "category": "memory", "aliases": ["relate", "relate"], "status": "approved", "version": "1.0", "code": "ng:memory:relate", "fallback": "[RELATE]", "unicode_point": "U+1F4E2"}, {"id": "NG0465", "symbol": "📣", "name": "connect", "description": "Connect concept in memory - memory management operation", "category": "memory", "aliases": ["connect", "connect"], "status": "approved", "version": "1.0", "code": "ng:memory:connect", "fallback": "[CONNECT]", "unicode_point": "U+1F4E3"}, {"id": "NG0466", "symbol": "📤", "name": "bind", "description": "Bind concept in memory - memory management operation", "category": "memory", "aliases": ["bind", "bind"], "status": "approved", "version": "1.0", "code": "ng:memory:bind", "fallback": "[BIND]", "unicode_point": "U+1F4E4"}, {"id": "NG0467", "symbol": "📥", "name": "reference_1", "description": "Reference 1 concept in memory - memory management operation", "category": "memory", "aliases": ["reference1", "reference"], "status": "approved", "version": "1.0", "code": "ng:memory:reference_1", "fallback": "[REFERENCE1]", "unicode_point": "U+1F4E5"}, {"id": "NG0468", "symbol": "📦", "name": "dereference", "description": "Dereference concept in memory - memory management operation", "category": "memory", "aliases": ["dereference", "dereference"], "status": "approved", "version": "1.0", "code": "ng:memory:dereference", "fallback": "[DEREFERENCE]", "unicode_point": "U+1F4E6"}, {"id": "NG0469", "symbol": "📧", "name": "persist", "description": "Persist concept in memory - memory management operation", "category": "memory", "aliases": ["persist", "persist"], "status": "approved", "version": "1.0", "code": "ng:memory:persist", "fallback": "[PERSIST]", "unicode_point": "U+1F4E7"}, {"id": "NG0470", "symbol": "📨", "name": "volatile_1", "description": "Volatile 1 concept in memory - memory management operation", "category": "memory", "aliases": ["volatile1", "volatile"], "status": "approved", "version": "1.0", "code": "ng:memory:volatile_1", "fallback": "[VOLATILE1]", "unicode_point": "U+1F4E8"}, {"id": "NG0471", "symbol": "📩", "name": "think", "description": "Think concept in reasoning - cognitive or reasoning process", "category": "reasoning", "aliases": ["think", "think"], "status": "approved", "version": "1.0", "code": "ng:reasoning:think", "fallback": "[THINK]", "unicode_point": "U+1F4E9"}, {"id": "NG0472", "symbol": "📪", "name": "analyze", "description": "Analyze concept in reasoning - cognitive or reasoning process", "category": "reasoning", "aliases": ["analyze", "analyze"], "status": "approved", "version": "1.0", "code": "ng:reasoning:analyze", "fallback": "[ANALYZE]", "unicode_point": "U+1F4EA"}, {"id": "NG0473", "symbol": "📫", "name": "synthesize", "description": "Synthesize concept in reasoning - cognitive or reasoning process", "category": "reasoning", "aliases": ["synthesize", "synthesize"], "status": "approved", "version": "1.0", "code": "ng:reasoning:synthesize", "fallback": "[SYNTHESIZE]", "unicode_point": "U+1F4EB"}, {"id": "NG0474", "symbol": "📬", "name": "deduce", "description": "Deduce concept in reasoning - cognitive or reasoning process", "category": "reasoning", "aliases": ["deduce", "deduce"], "status": "approved", "version": "1.0", "code": "ng:reasoning:deduce", "fallback": "[DEDUCE]", "unicode_point": "U+1F4EC"}, {"id": "NG0475", "symbol": "📭", "name": "induce", "description": "Induce concept in reasoning - cognitive or reasoning process", "category": "reasoning", "aliases": ["induce", "induce"], "status": "approved", "version": "1.0", "code": "ng:reasoning:induce", "fallback": "[INDUCE]", "unicode_point": "U+1F4ED"}, {"id": "NG0476", "symbol": "📮", "name": "infer", "description": "Infer concept in reasoning - cognitive or reasoning process", "category": "reasoning", "aliases": ["infer", "infer"], "status": "approved", "version": "1.0", "code": "ng:reasoning:infer", "fallback": "[INFER]", "unicode_point": "U+1F4EE"}, {"id": "NG0477", "symbol": "📯", "name": "conclude", "description": "Conclude concept in reasoning - cognitive or reasoning process", "category": "reasoning", "aliases": ["conclude", "conclude"], "status": "approved", "version": "1.0", "code": "ng:reasoning:conclude", "fallback": "[CONCLUDE]", "unicode_point": "U+1F4EF"}, {"id": "NG0478", "symbol": "📰", "name": "prove", "description": "Prove concept in reasoning - cognitive or reasoning process", "category": "reasoning", "aliases": ["prove", "prove"], "status": "approved", "version": "1.0", "code": "ng:reasoning:prove", "fallback": "[PROVE]", "unicode_point": "U+1F4F0"}, {"id": "NG0479", "symbol": "📱", "name": "disprove", "description": "Disprove concept in reasoning - cognitive or reasoning process", "category": "reasoning", "aliases": ["disprove", "disprove"], "status": "approved", "version": "1.0", "code": "ng:reasoning:disprove", "fallback": "[DISPROVE]", "unicode_point": "U+1F4F1"}, {"id": "NG0480", "symbol": "📲", "name": "verify_1", "description": "Verify 1 concept in reasoning - cognitive or reasoning process", "category": "reasoning", "aliases": ["verify1", "verify"], "status": "approved", "version": "1.0", "code": "ng:reasoning:verify_1", "fallback": "[VERIFY1]", "unicode_point": "U+1F4F2"}, {"id": "NG0481", "symbol": "📳", "name": "reason", "description": "Reason concept in reasoning - cognitive or reasoning process", "category": "reasoning", "aliases": ["reason", "reason"], "status": "approved", "version": "1.0", "code": "ng:reasoning:reason", "fallback": "[REASON]", "unicode_point": "U+1F4F3"}, {"id": "NG0482", "symbol": "📴", "name": "logic", "description": "Logic concept in reasoning - cognitive or reasoning process", "category": "reasoning", "aliases": ["logic", "logic"], "status": "approved", "version": "1.0", "code": "ng:reasoning:logic", "fallback": "[LOGIC]", "unicode_point": "U+1F4F4"}, {"id": "NG0483", "symbol": "📵", "name": "intuition", "description": "Intuition concept in reasoning - cognitive or reasoning process", "category": "reasoning", "aliases": ["intuition", "intuition"], "status": "approved", "version": "1.0", "code": "ng:reasoning:intuition", "fallback": "[INTUITION]", "unicode_point": "U+1F4F5"}, {"id": "NG0484", "symbol": "🔍", "name": "insight", "description": "Insight concept in reasoning - cognitive or reasoning process", "category": "reasoning", "aliases": ["insight", "insight"], "status": "approved", "version": "1.0", "code": "ng:reasoning:insight", "fallback": "[INSIGHT]", "unicode_point": "U+1F50D"}, {"id": "NG0485", "symbol": "🔎", "name": "understanding", "description": "Understanding concept in reasoning - cognitive or reasoning process", "category": "reasoning", "aliases": ["understanding", "understanding"], "status": "approved", "version": "1.0", "code": "ng:reasoning:understanding", "fallback": "[UNDERSTANDING]", "unicode_point": "U+1F50E"}, {"id": "NG0486", "symbol": "🔏", "name": "comprehension", "description": "Comprehension concept in reasoning - cognitive or reasoning process", "category": "reasoning", "aliases": ["comprehension", "comprehension"], "status": "approved", "version": "1.0", "code": "ng:reasoning:comprehension", "fallback": "[COMPREHENSION]", "unicode_point": "U+1F50F"}, {"id": "NG0487", "symbol": "🔐", "name": "knowledge", "description": "Knowledge concept in reasoning - cognitive or reasoning process", "category": "reasoning", "aliases": ["knowledge", "knowledge"], "status": "approved", "version": "1.0", "code": "ng:reasoning:knowledge", "fallback": "[KNOWLEDGE]", "unicode_point": "U+1F510"}, {"id": "NG0488", "symbol": "🔑", "name": "wisdom", "description": "Wisdom concept in reasoning - cognitive or reasoning process", "category": "reasoning", "aliases": ["wisdom", "wisdom"], "status": "approved", "version": "1.0", "code": "ng:reasoning:wisdom", "fallback": "[WISDOM]", "unicode_point": "U+1F511"}, {"id": "NG0489", "symbol": "🔒", "name": "intelligence", "description": "Intelligence concept in reasoning - cognitive or reasoning process", "category": "reasoning", "aliases": ["intelligence", "intelligence"], "status": "approved", "version": "1.0", "code": "ng:reasoning:intelligence", "fallback": "[INTELLIGENCE]", "unicode_point": "U+1F512"}, {"id": "NG0490", "symbol": "🔓", "name": "consciousness", "description": "Consciousness concept in reasoning - cognitive or reasoning process", "category": "reasoning", "aliases": ["consciousness", "consciousness"], "status": "approved", "version": "1.0", "code": "ng:reasoning:consciousness", "fallback": "[CONSCIOUSNESS]", "unicode_point": "U+1F513"}, {"id": "NG0491", "symbol": "🔔", "name": "plan", "description": "Plan concept in reasoning - cognitive or reasoning process", "category": "reasoning", "aliases": ["plan", "plan"], "status": "approved", "version": "1.0", "code": "ng:reasoning:plan", "fallback": "[PLAN]", "unicode_point": "U+1F514"}, {"id": "NG0492", "symbol": "🔕", "name": "strategy", "description": "Strategy concept in reasoning - cognitive or reasoning process", "category": "reasoning", "aliases": ["strategy", "strategy"], "status": "approved", "version": "1.0", "code": "ng:reasoning:strategy", "fallback": "[STRATEGY]", "unicode_point": "U+1F515"}, {"id": "NG0493", "symbol": "🔖", "name": "tactic", "description": "Tactic concept in reasoning - cognitive or reasoning process", "category": "reasoning", "aliases": ["tactic", "tactic"], "status": "approved", "version": "1.0", "code": "ng:reasoning:tactic", "fallback": "[TACTIC]", "unicode_point": "U+1F516"}, {"id": "NG0494", "symbol": "🔗", "name": "approach", "description": "Approach concept in reasoning - cognitive or reasoning process", "category": "reasoning", "aliases": ["approach", "approach"], "status": "approved", "version": "1.0", "code": "ng:reasoning:approach", "fallback": "[APPROACH]", "unicode_point": "U+1F517"}, {"id": "NG0495", "symbol": "🔘", "name": "method", "description": "Method concept in reasoning - cognitive or reasoning process", "category": "reasoning", "aliases": ["method", "method"], "status": "approved", "version": "1.0", "code": "ng:reasoning:method", "fallback": "[METHOD]", "unicode_point": "U+1F518"}, {"id": "NG0496", "symbol": "🔙", "name": "technique", "description": "Technique concept in reasoning - cognitive or reasoning process", "category": "reasoning", "aliases": ["technique", "technique"], "status": "approved", "version": "1.0", "code": "ng:reasoning:technique", "fallback": "[TECHNIQUE]", "unicode_point": "U+1F519"}, {"id": "NG0497", "symbol": "🔚", "name": "algorithm", "description": "Algorithm concept in reasoning - cognitive or reasoning process", "category": "reasoning", "aliases": ["algorithm", "algorithm"], "status": "approved", "version": "1.0", "code": "ng:reasoning:algorithm", "fallback": "[ALGORITHM]", "unicode_point": "U+1F51A"}, {"id": "NG0498", "symbol": "🔛", "name": "heuristic", "description": "Heuristic concept in reasoning - cognitive or reasoning process", "category": "reasoning", "aliases": ["heuristic", "heuristic"], "status": "approved", "version": "1.0", "code": "ng:reasoning:heuristic", "fallback": "[HEURISTIC]", "unicode_point": "U+1F51B"}, {"id": "NG0499", "symbol": "🔜", "name": "pattern_1", "description": "Pattern 1 concept in reasoning - cognitive or reasoning process", "category": "reasoning", "aliases": ["pattern1", "pattern"], "status": "approved", "version": "1.0", "code": "ng:reasoning:pattern_1", "fallback": "[PATTERN1]", "unicode_point": "U+1F51C"}, {"id": "NG0500", "symbol": "🔝", "name": "model_1", "description": "Model 1 concept in reasoning - cognitive or reasoning process", "category": "reasoning", "aliases": ["model1", "model"], "status": "approved", "version": "1.0", "code": "ng:reasoning:model_1", "fallback": "[MODEL1]", "unicode_point": "U+1F51D"}, {"id": "NG0501", "symbol": "🔞", "name": "simulate", "description": "Simulate concept in reasoning - cognitive or reasoning process", "category": "reasoning", "aliases": ["simulate", "simulate"], "status": "approved", "version": "1.0", "code": "ng:reasoning:simulate", "fallback": "[SIMULATE]", "unicode_point": "U+1F51E"}, {"id": "NG0502", "symbol": "🔟", "name": "predict", "description": "Predict concept in reasoning - cognitive or reasoning process", "category": "reasoning", "aliases": ["predict", "predict"], "status": "approved", "version": "1.0", "code": "ng:reasoning:predict", "fallback": "[PREDICT]", "unicode_point": "U+1F51F"}, {"id": "NG0503", "symbol": "🔠", "name": "forecast", "description": "Forecast concept in reasoning - cognitive or reasoning process", "category": "reasoning", "aliases": ["forecast", "forecast"], "status": "approved", "version": "1.0", "code": "ng:reasoning:forecast", "fallback": "[FORECAST]", "unicode_point": "U+1F520"}, {"id": "NG0504", "symbol": "🔡", "name": "estimate", "description": "Estimate concept in reasoning - cognitive or reasoning process", "category": "reasoning", "aliases": ["estimate", "estimate"], "status": "approved", "version": "1.0", "code": "ng:reasoning:estimate", "fallback": "[ESTIMATE]", "unicode_point": "U+1F521"}, {"id": "NG0505", "symbol": "🔢", "name": "approximate", "description": "Approximate concept in reasoning - cognitive or reasoning process", "category": "reasoning", "aliases": ["approximate", "approximate"], "status": "approved", "version": "1.0", "code": "ng:reasoning:approximate", "fallback": "[APPROXIMATE]", "unicode_point": "U+1F522"}, {"id": "NG0506", "symbol": "🔣", "name": "calculate", "description": "Calculate concept in reasoning - cognitive or reasoning process", "category": "reasoning", "aliases": ["calculate", "calculate"], "status": "approved", "version": "1.0", "code": "ng:reasoning:calculate", "fallback": "[CALCULATE]", "unicode_point": "U+1F523"}, {"id": "NG0507", "symbol": "🔤", "name": "compute", "description": "Compute concept in reasoning - cognitive or reasoning process", "category": "reasoning", "aliases": ["compute", "compute"], "status": "approved", "version": "1.0", "code": "ng:reasoning:compute", "fallback": "[COMPUTE]", "unicode_point": "U+1F524"}, {"id": "NG0508", "symbol": "🔥", "name": "solve", "description": "Solve concept in reasoning - cognitive or reasoning process", "category": "reasoning", "aliases": ["solve", "solve"], "status": "approved", "version": "1.0", "code": "ng:reasoning:solve", "fallback": "[SOLVE]", "unicode_point": "U+1F525"}, {"id": "NG0509", "symbol": "🔦", "name": "optimize_1", "description": "Optimize 1 concept in reasoning - cognitive or reasoning process", "category": "reasoning", "aliases": ["optimize1", "optimize"], "status": "approved", "version": "1.0", "code": "ng:reasoning:optimize_1", "fallback": "[OPTIMIZE1]", "unicode_point": "U+1F526"}, {"id": "NG0510", "symbol": "🔧", "name": "minimize", "description": "Minimize concept in reasoning - cognitive or reasoning process", "category": "reasoning", "aliases": ["minimize", "minimize"], "status": "approved", "version": "1.0", "code": "ng:reasoning:minimize", "fallback": "[MINIMIZE]", "unicode_point": "U+1F527"}, {"id": "NG0511", "symbol": "🔨", "name": "maximize", "description": "Maximize concept in reasoning - cognitive or reasoning process", "category": "reasoning", "aliases": ["maximize", "maximize"], "status": "approved", "version": "1.0", "code": "ng:reasoning:maximize", "fallback": "[MAXIMIZE]", "unicode_point": "U+1F528"}, {"id": "NG0512", "symbol": "🧠", "name": "balance", "description": "Balance concept in reasoning - cognitive or reasoning process", "category": "reasoning", "aliases": ["balance", "balance"], "status": "approved", "version": "1.0", "code": "ng:reasoning:balance", "fallback": "[BALANCE]", "unicode_point": "U+1F9E0"}]