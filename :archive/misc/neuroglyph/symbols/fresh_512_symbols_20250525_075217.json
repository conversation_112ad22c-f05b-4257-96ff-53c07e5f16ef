{"registry_version": "3.0", "description": "NEUROGLYPH ULTRA - Fresh 512 Symbols", "created": "2025-05-23 23:26:51", "total_symbols": 512, "generator": "fresh_512_v1", "criteria": {"usu": "Unicode Symbolic Uniqueness", "ctu": "Coded Textual Unification", "lcl": "LLM Compatibility Layer"}, "categories": {"logic": {"target": 96, "actual": 96}, "structure": {"target": 80, "actual": 80}, "flow": {"target": 72, "actual": 72}, "operator": {"target": 64, "actual": 64}, "memory": {"target": 64, "actual": 64}, "reasoning": {"target": 56, "actual": 56}, "meta": {"target": 48, "actual": 48}, "quantum": {"target": 32, "actual": 32}}, "symbols": [{"id": "NG0001", "symbol": "⌥", "code": "ng:logic:and", "fallback": "[AND]", "category": "logic", "meaning": "and", "description": "Logic operation: and", "unicode_point": "U+2325", "unicode_name": "OPTION KEY", "token_cost": 1, "token_density": 1.0, "quality_score": 99.99999999999999, "llm_support": ["openai", "qwen", "llama"], "created": "2025-05-23", "generator": "fresh_512_v1"}, {"id": "NG0002", "symbol": "♓", "code": "ng:logic:or", "fallback": "[OR]", "category": "logic", "meaning": "or", "description": "Logic operation: or", "unicode_point": "U+2653", "unicode_name": "PISCES", "token_cost": 1, "token_density": 1.0, "quality_score": 99.99999999999999, "llm_support": ["openai", "qwen", "llama"], "created": "2025-05-23", "generator": "fresh_512_v1"}, {"id": "NG0003", "symbol": "⟢", "code": "ng:logic:not", "fallback": "[NOT]", "category": "logic", "meaning": "not", "description": "Logic operation: not", "unicode_point": "U+27E2", "unicode_name": "WHITE CONCAVE-SIDED DIAMOND WITH LEFTWARDS TICK", "token_cost": 1, "token_density": 1.0, "quality_score": 99.99999999999999, "llm_support": ["openai", "qwen", "llama"], "created": "2025-05-23", "generator": "fresh_512_v1"}, {"id": "NG0004", "symbol": "⍷", "code": "ng:logic:implies", "fallback": "[IMPLIE]", "category": "logic", "meaning": "implies", "description": "Logic operation: implies", "unicode_point": "U+2377", "unicode_name": "APL FUNCTIONAL SYMBOL EPSILON UNDERBAR", "token_cost": 1, "token_density": 1.0, "quality_score": 99.99999999999999, "llm_support": ["openai", "qwen", "llama"], "created": "2025-05-23", "generator": "fresh_512_v1"}, {"id": "NG0005", "symbol": "♔", "code": "ng:logic:iff", "fallback": "[IFF]", "category": "logic", "meaning": "iff", "description": "Logic operation: iff", "unicode_point": "U+2654", "unicode_name": "WHITE CHESS KING", "token_cost": 1, "token_density": 1.0, "quality_score": 99.99999999999999, "llm_support": ["openai", "qwen", "llama"], "created": "2025-05-23", "generator": "fresh_512_v1"}, {"id": "NG0006", "symbol": "⊫", "code": "ng:logic:xor", "fallback": "[XOR]", "category": "logic", "meaning": "xor", "description": "Logic operation: xor", "unicode_point": "U+22AB", "unicode_name": "DOUBLE VERTICAL BAR DOUBLE RIGHT TURNSTILE", "token_cost": 1, "token_density": 1.0, "quality_score": 99.99999999999999, "llm_support": ["openai", "qwen", "llama"], "created": "2025-05-23", "generator": "fresh_512_v1"}, {"id": "NG0007", "symbol": "⏴", "code": "ng:logic:nand", "fallback": "[NAND]", "category": "logic", "meaning": "nand", "description": "Logic operation: nand", "unicode_point": "U+23F4", "unicode_name": "BLACK MEDIUM LEFT-POINTING TRIANGLE", "token_cost": 1, "token_density": 1.0, "quality_score": 99.99999999999999, "llm_support": ["openai", "qwen", "llama"], "created": "2025-05-23", "generator": "fresh_512_v1"}, {"id": "NG0008", "symbol": "⟘", "code": "ng:logic:nor", "fallback": "[NOR]", "category": "logic", "meaning": "nor", "description": "Logic operation: nor", "unicode_point": "U+27D8", "unicode_name": "LARGE UP TACK", "token_cost": 1, "token_density": 1.0, "quality_score": 99.99999999999999, "llm_support": ["openai", "qwen", "llama"], "created": "2025-05-23", "generator": "fresh_512_v1"}, {"id": "NG0009", "symbol": "⩧", "code": "ng:logic:forall", "fallback": "[FORALL]", "category": "logic", "meaning": "forall", "description": "Logic operation: forall", "unicode_point": "U+2A67", "unicode_name": "IDENTICAL WITH DOT ABOVE", "token_cost": 1, "token_density": 1.0, "quality_score": 99.99999999999999, "llm_support": ["openai", "qwen", "llama"], "created": "2025-05-23", "generator": "fresh_512_v1"}, {"id": "NG0010", "symbol": "⟟", "code": "ng:logic:exists", "fallback": "[EXISTS]", "category": "logic", "meaning": "exists", "description": "Logic operation: exists", "unicode_point": "U+27DF", "unicode_name": "UP TACK WITH CIRCLE ABOVE", "token_cost": 1, "token_density": 1.0, "quality_score": 99.99999999999999, "llm_support": ["openai", "qwen", "llama"], "created": "2025-05-23", "generator": "fresh_512_v1"}, {"id": "NG0011", "symbol": "⟩", "code": "ng:logic:unique", "fallback": "[UNIQUE]", "category": "logic", "meaning": "unique", "description": "Logic operation: unique", "unicode_point": "U+27E9", "unicode_name": "MATHEMATICAL RIGHT ANGLE BRACKET", "token_cost": 1, "token_density": 1.0, "quality_score": 99.99999999999999, "llm_support": ["openai", "qwen", "llama"], "created": "2025-05-23", "generator": "fresh_512_v1"}, {"id": "NG0012", "symbol": "⨟", "code": "ng:logic:lambda", "fallback": "[LAMBDA]", "category": "logic", "meaning": "lambda", "description": "Logic operation: lambda", "unicode_point": "U+2A1F", "unicode_name": "Z NOTATION SCHEMA COMPOSITION", "token_cost": 1, "token_density": 1.0, "quality_score": 99.99999999999999, "llm_support": ["openai", "qwen", "llama"], "created": "2025-05-23", "generator": "fresh_512_v1"}, {"id": "NG0013", "symbol": "⪴", "code": "ng:logic:apply", "fallback": "[APPLY]", "category": "logic", "meaning": "apply", "description": "Logic operation: apply", "unicode_point": "U+2AB4", "unicode_name": "SUCCEEDS ABOVE EQUALS SIGN", "token_cost": 1, "token_density": 1.0, "quality_score": 99.99999999999999, "llm_support": ["openai", "qwen", "llama"], "created": "2025-05-23", "generator": "fresh_512_v1"}, {"id": "NG0014", "symbol": "⩖", "code": "ng:logic:compose", "fallback": "[COMPOS]", "category": "logic", "meaning": "compose", "description": "Logic operation: compose", "unicode_point": "U+2A56", "unicode_name": "TWO INTERSECTING LOGICAL OR", "token_cost": 1, "token_density": 1.0, "quality_score": 99.99999999999999, "llm_support": ["openai", "qwen", "llama"], "created": "2025-05-23", "generator": "fresh_512_v1"}, {"id": "NG0015", "symbol": "▻", "code": "ng:logic:curry", "fallback": "[CURRY]", "category": "logic", "meaning": "curry", "description": "Logic operation: curry", "unicode_point": "U+25BB", "unicode_name": "WHITE RIGHT-POINTING POINTER", "token_cost": 1, "token_density": 1.0, "quality_score": 99.99999999999999, "llm_support": ["openai", "qwen", "llama"], "created": "2025-05-23", "generator": "fresh_512_v1"}, {"id": "NG0016", "symbol": "♽", "code": "ng:logic:necessary", "fallback": "[NECESS]", "category": "logic", "meaning": "necessary", "description": "Logic operation: necessary", "unicode_point": "U+267D", "unicode_name": "PARTIALLY-RECYCLED PAPER SYMBOL", "token_cost": 1, "token_density": 1.0, "quality_score": 99.99999999999999, "llm_support": ["openai", "qwen", "llama"], "created": "2025-05-23", "generator": "fresh_512_v1"}, {"id": "NG0017", "symbol": "⫄", "code": "ng:logic:possible", "fallback": "[POSSIB]", "category": "logic", "meaning": "possible", "description": "Logic operation: possible", "unicode_point": "U+2AC4", "unicode_name": "SUPERSET OF OR EQUAL TO WITH DOT ABOVE", "token_cost": 1, "token_density": 1.0, "quality_score": 99.99999999999999, "llm_support": ["openai", "qwen", "llama"], "created": "2025-05-23", "generator": "fresh_512_v1"}, {"id": "NG0018", "symbol": "⋮", "code": "ng:logic:knows", "fallback": "[KNOWS]", "category": "logic", "meaning": "knows", "description": "Logic operation: knows", "unicode_point": "U+22EE", "unicode_name": "VERTICAL ELLIPSIS", "token_cost": 1, "token_density": 1.0, "quality_score": 99.99999999999999, "llm_support": ["openai", "qwen", "llama"], "created": "2025-05-23", "generator": "fresh_512_v1"}, {"id": "NG0019", "symbol": "↥", "code": "ng:logic:believes", "fallback": "[BELIEV]", "category": "logic", "meaning": "believes", "description": "Logic operation: believes", "unicode_point": "U+21A5", "unicode_name": "UPWARDS ARROW FROM BAR", "token_cost": 1, "token_density": 1.0, "quality_score": 99.99999999999999, "llm_support": ["openai", "qwen", "llama"], "created": "2025-05-23", "generator": "fresh_512_v1"}, {"id": "NG0020", "symbol": "⇦", "code": "ng:logic:ought", "fallback": "[OUGHT]", "category": "logic", "meaning": "ought", "description": "Logic operation: ought", "unicode_point": "U+21E6", "unicode_name": "LEFTWARDS WHITE ARROW", "token_cost": 1, "token_density": 1.0, "quality_score": 99.99999999999999, "llm_support": ["openai", "qwen", "llama"], "created": "2025-05-23", "generator": "fresh_512_v1"}, {"id": "NG0021", "symbol": "≀", "code": "ng:logic:permitted", "fallback": "[PERMIT]", "category": "logic", "meaning": "permitted", "description": "Logic operation: permitted", "unicode_point": "U+2240", "unicode_name": "WREATH PRODUCT", "token_cost": 1, "token_density": 1.0, "quality_score": 99.99999999999999, "llm_support": ["openai", "qwen", "llama"], "created": "2025-05-23", "generator": "fresh_512_v1"}, {"id": "NG0022", "symbol": "▹", "code": "ng:logic:always", "fallback": "[ALWAYS]", "category": "logic", "meaning": "always", "description": "Logic operation: always", "unicode_point": "U+25B9", "unicode_name": "WHITE RIGHT-POINTING SMALL TRIANGLE", "token_cost": 1, "token_density": 1.0, "quality_score": 99.99999999999999, "llm_support": ["openai", "qwen", "llama"], "created": "2025-05-23", "generator": "fresh_512_v1"}, {"id": "NG0023", "symbol": "⇃", "code": "ng:logic:eventually", "fallback": "[EVENTU]", "category": "logic", "meaning": "eventually", "description": "Logic operation: eventually", "unicode_point": "U+21C3", "unicode_name": "DOWNWARDS HARPOON WITH BARB LEFTWARDS", "token_cost": 1, "token_density": 1.0, "quality_score": 99.99999999999999, "llm_support": ["openai", "qwen", "llama"], "created": "2025-05-23", "generator": "fresh_512_v1"}, {"id": "NG0024", "symbol": "⪓", "code": "ng:logic:until", "fallback": "[UNTIL]", "category": "logic", "meaning": "until", "description": "Logic operation: until", "unicode_point": "U+2A93", "unicode_name": "LESS-<PERSON><PERSON><PERSON> ABOVE SLANTED EQUAL ABOVE GREATER-THAN ABOVE SLANTED EQUAL", "token_cost": 1, "token_density": 1.0, "quality_score": 99.99999999999999, "llm_support": ["openai", "qwen", "llama"], "created": "2025-05-23", "generator": "fresh_512_v1"}, {"id": "NG0025", "symbol": "⟇", "code": "ng:logic:since", "fallback": "[SINCE]", "category": "logic", "meaning": "since", "description": "Logic operation: since", "unicode_point": "U+27C7", "unicode_name": "OR WITH DOT INSIDE", "token_cost": 1, "token_density": 1.0, "quality_score": 99.99999999999999, "llm_support": ["openai", "qwen", "llama"], "created": "2025-05-23", "generator": "fresh_512_v1"}, {"id": "NG0026", "symbol": "⏁", "code": "ng:logic:next", "fallback": "[NEXT]", "category": "logic", "meaning": "next", "description": "Logic operation: next", "unicode_point": "U+23C1", "unicode_name": "DENTISTRY SYMBOL LIGHT DOWN AND <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> WITH CIRCLE", "token_cost": 1, "token_density": 1.0, "quality_score": 99.99999999999999, "llm_support": ["openai", "qwen", "llama"], "created": "2025-05-23", "generator": "fresh_512_v1"}, {"id": "NG0027", "symbol": "⟖", "code": "ng:logic:previous", "fallback": "[PREVIO]", "category": "logic", "meaning": "previous", "description": "Logic operation: previous", "unicode_point": "U+27D6", "unicode_name": "RIGHT OUTER JOIN", "token_cost": 1, "token_density": 1.0, "quality_score": 99.99999999999999, "llm_support": ["openai", "qwen", "llama"], "created": "2025-05-23", "generator": "fresh_512_v1"}, {"id": "NG0028", "symbol": "⊬", "code": "ng:logic:entails", "fallback": "[ENTAIL]", "category": "logic", "meaning": "entails", "description": "Logic operation: entails", "unicode_point": "U+22AC", "unicode_name": "DOES NOT PROVE", "token_cost": 1, "token_density": 1.0, "quality_score": 99.99999999999999, "llm_support": ["openai", "qwen", "llama"], "created": "2025-05-23", "generator": "fresh_512_v1"}, {"id": "NG0029", "symbol": "⏄", "code": "ng:logic:proves", "fallback": "[PROVES]", "category": "logic", "meaning": "proves", "description": "Logic operation: proves", "unicode_point": "U+23C4", "unicode_name": "DENTISTRY SYMBOL LIGHT DOWN AND <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> WITH TRIANGLE", "token_cost": 1, "token_density": 1.0, "quality_score": 99.99999999999999, "llm_support": ["openai", "qwen", "llama"], "created": "2025-05-23", "generator": "fresh_512_v1"}, {"id": "NG0030", "symbol": "♲", "code": "ng:logic:derives", "fallback": "[DERIVE]", "category": "logic", "meaning": "derives", "description": "Logic operation: derives", "unicode_point": "U+2672", "unicode_name": "UNIVERSAL RECYCLING SYMBOL", "token_cost": 1, "token_density": 1.0, "quality_score": 99.99999999999999, "llm_support": ["openai", "qwen", "llama"], "created": "2025-05-23", "generator": "fresh_512_v1"}, {"id": "NG0031", "symbol": "⋁", "code": "ng:logic:concludes", "fallback": "[CONCLU]", "category": "logic", "meaning": "concludes", "description": "Logic operation: concludes", "unicode_point": "U+22C1", "unicode_name": "N-ARY LOGICAL OR", "token_cost": 1, "token_density": 1.0, "quality_score": 99.99999999999999, "llm_support": ["openai", "qwen", "llama"], "created": "2025-05-23", "generator": "fresh_512_v1"}, {"id": "NG0032", "symbol": "⏟", "code": "ng:logic:assumes", "fallback": "[ASSUME]", "category": "logic", "meaning": "assumes", "description": "Logic operation: assumes", "unicode_point": "U+23DF", "unicode_name": "BOTTOM CURLY BRACKET", "token_cost": 1, "token_density": 1.0, "quality_score": 99.99999999999999, "llm_support": ["openai", "qwen", "llama"], "created": "2025-05-23", "generator": "fresh_512_v1"}, {"id": "NG0033", "symbol": "⟣", "code": "ng:logic:supposes", "fallback": "[SUPPOS]", "category": "logic", "meaning": "supposes", "description": "Logic operation: supposes", "unicode_point": "U+27E3", "unicode_name": "WHITE CONCAVE-SIDED DIAMOND WITH RIGHTWARDS TICK", "token_cost": 1, "token_density": 1.0, "quality_score": 99.99999999999999, "llm_support": ["openai", "qwen", "llama"], "created": "2025-05-23", "generator": "fresh_512_v1"}, {"id": "NG0034", "symbol": "⟞", "code": "ng:logic:infer", "fallback": "[INFER]", "category": "logic", "meaning": "infer", "description": "Logic operation: infer", "unicode_point": "U+27DE", "unicode_name": "LONG LEFT TACK", "token_cost": 1, "token_density": 1.0, "quality_score": 99.99999999999999, "llm_support": ["openai", "qwen", "llama"], "created": "2025-05-23", "generator": "fresh_512_v1"}, {"id": "NG0035", "symbol": "◫", "code": "ng:logic:deduce", "fallback": "[DEDUCE]", "category": "logic", "meaning": "deduce", "description": "Logic operation: deduce", "unicode_point": "U+25EB", "unicode_name": "WHITE SQUARE WITH VERTICAL BISECTING LINE", "token_cost": 1, "token_density": 1.0, "quality_score": 99.99999999999999, "llm_support": ["openai", "qwen", "llama"], "created": "2025-05-23", "generator": "fresh_512_v1"}, {"id": "NG0036", "symbol": "⊈", "code": "ng:logic:induce", "fallback": "[INDUCE]", "category": "logic", "meaning": "induce", "description": "Logic operation: induce", "unicode_point": "U+2288", "unicode_name": "NEITHER A SUBSET OF NOR EQUAL TO", "token_cost": 1, "token_density": 1.0, "quality_score": 99.99999999999999, "llm_support": ["openai", "qwen", "llama"], "created": "2025-05-23", "generator": "fresh_512_v1"}, {"id": "NG0037", "symbol": "←", "code": "ng:logic:reason", "fallback": "[REASON]", "category": "logic", "meaning": "reason", "description": "Logic operation: reason", "unicode_point": "U+2190", "unicode_name": "LEFTWARDS ARROW", "token_cost": 1, "token_density": 1.0, "quality_score": 99.99999999999999, "llm_support": ["openai", "qwen", "llama"], "created": "2025-05-23", "generator": "fresh_512_v1"}, {"id": "NG0038", "symbol": "⇙", "code": "ng:logic:think", "fallback": "[THINK]", "category": "logic", "meaning": "think", "description": "Logic operation: think", "unicode_point": "U+21D9", "unicode_name": "SOUTH WEST DOUBLE ARROW", "token_cost": 1, "token_density": 1.0, "quality_score": 99.99999999999999, "llm_support": ["openai", "qwen", "llama"], "created": "2025-05-23", "generator": "fresh_512_v1"}, {"id": "NG0039", "symbol": "♺", "code": "ng:logic:conclude", "fallback": "[CONCLU1]", "category": "logic", "meaning": "conclude", "description": "Logic operation: conclude", "unicode_point": "U+267A", "unicode_name": "RECYCLING <PERSON><PERSON><PERSON><PERSON> FOR GENERIC MATERIALS", "token_cost": 1, "token_density": 1.0, "quality_score": 99.99999999999999, "llm_support": ["openai", "qwen", "llama"], "created": "2025-05-23", "generator": "fresh_512_v1"}, {"id": "NG0040", "symbol": "⏞", "code": "ng:logic:premise", "fallback": "[PREMIS]", "category": "logic", "meaning": "premise", "description": "Logic operation: premise", "unicode_point": "U+23DE", "unicode_name": "TOP CURLY BRACKET", "token_cost": 1, "token_density": 1.0, "quality_score": 99.99999999999999, "llm_support": ["openai", "qwen", "llama"], "created": "2025-05-23", "generator": "fresh_512_v1"}, {"id": "NG0041", "symbol": "⟂", "code": "ng:logic:axiom", "fallback": "[AXIOM]", "category": "logic", "meaning": "axiom", "description": "Logic operation: axiom", "unicode_point": "U+27C2", "unicode_name": "PERPENDICULAR", "token_cost": 1, "token_density": 1.0, "quality_score": 99.99999999999999, "llm_support": ["openai", "qwen", "llama"], "created": "2025-05-23", "generator": "fresh_512_v1"}, {"id": "NG0042", "symbol": "⧬", "code": "ng:logic:theorem", "fallback": "[THEORE]", "category": "logic", "meaning": "theorem", "description": "Logic operation: theorem", "unicode_point": "U+29EC", "unicode_name": "WHITE CIRCLE WITH DOWN ARROW", "token_cost": 1, "token_density": 1.0, "quality_score": 99.99999999999999, "llm_support": ["openai", "qwen", "llama"], "created": "2025-05-23", "generator": "fresh_512_v1"}, {"id": "NG0043", "symbol": "↣", "code": "ng:logic:lemma", "fallback": "[LEMMA]", "category": "logic", "meaning": "lemma", "description": "Logic operation: lemma", "unicode_point": "U+21A3", "unicode_name": "RIGHTWARDS ARROW WITH TAIL", "token_cost": 1, "token_density": 1.0, "quality_score": 99.99999999999999, "llm_support": ["openai", "qwen", "llama"], "created": "2025-05-23", "generator": "fresh_512_v1"}, {"id": "NG0044", "symbol": "⚛", "code": "ng:logic:proof", "fallback": "[PROOF]", "category": "logic", "meaning": "proof", "description": "Logic operation: proof", "unicode_point": "U+269B", "unicode_name": "ATOM SYMBOL", "token_cost": 1, "token_density": 1.0, "quality_score": 99.99999999999999, "llm_support": ["openai", "qwen", "llama"], "created": "2025-05-23", "generator": "fresh_512_v1"}, {"id": "NG0045", "symbol": "⍗", "code": "ng:logic:valid", "fallback": "[VALID]", "category": "logic", "meaning": "valid", "description": "Logic operation: valid", "unicode_point": "U+2357", "unicode_name": "APL FUNCTIONAL SYMBOL QUAD DOWNWARDS ARROW", "token_cost": 1, "token_density": 1.0, "quality_score": 99.99999999999999, "llm_support": ["openai", "qwen", "llama"], "created": "2025-05-23", "generator": "fresh_512_v1"}, {"id": "NG0046", "symbol": "⚢", "code": "ng:logic:sound", "fallback": "[SOUND]", "category": "logic", "meaning": "sound", "description": "Logic operation: sound", "unicode_point": "U+26A2", "unicode_name": "DOUBLED FEMALE SIGN", "token_cost": 1, "token_density": 1.0, "quality_score": 99.99999999999999, "llm_support": ["openai", "qwen", "llama"], "created": "2025-05-23", "generator": "fresh_512_v1"}, {"id": "NG0047", "symbol": "≞", "code": "ng:logic:complete", "fallback": "[COMPLE]", "category": "logic", "meaning": "complete", "description": "Logic operation: complete", "unicode_point": "U+225E", "unicode_name": "MEASURED BY", "token_cost": 1, "token_density": 1.0, "quality_score": 99.99999999999999, "llm_support": ["openai", "qwen", "llama"], "created": "2025-05-23", "generator": "fresh_512_v1"}, {"id": "NG0048", "symbol": "◱", "code": "ng:logic:consistent", "fallback": "[CONSIS]", "category": "logic", "meaning": "consistent", "description": "Logic operation: consistent", "unicode_point": "U+25F1", "unicode_name": "WHITE SQUARE WITH LOWER LEFT QUADRANT", "token_cost": 1, "token_density": 1.0, "quality_score": 99.99999999999999, "llm_support": ["openai", "qwen", "llama"], "created": "2025-05-23", "generator": "fresh_512_v1"}, {"id": "NG0049", "symbol": "⇱", "code": "ng:logic:and_1", "fallback": "[AND1]", "category": "logic", "meaning": "and", "description": "Logic operation: and", "unicode_point": "U+21F1", "unicode_name": "NORTH WEST ARROW TO CORNER", "token_cost": 1, "token_density": 1.0, "quality_score": 99.99999999999999, "llm_support": ["openai", "qwen", "llama"], "created": "2025-05-23", "generator": "fresh_512_v1"}, {"id": "NG0050", "symbol": "↬", "code": "ng:logic:or_1", "fallback": "[OR1]", "category": "logic", "meaning": "or", "description": "Logic operation: or", "unicode_point": "U+21AC", "unicode_name": "RIGHTWARDS ARROW WITH LOOP", "token_cost": 1, "token_density": 1.0, "quality_score": 99.99999999999999, "llm_support": ["openai", "qwen", "llama"], "created": "2025-05-23", "generator": "fresh_512_v1"}, {"id": "NG0051", "symbol": "⋬", "code": "ng:logic:not_1", "fallback": "[NOT1]", "category": "logic", "meaning": "not", "description": "Logic operation: not", "unicode_point": "U+22EC", "unicode_name": "NOT NORMAL SUBGROUP OF OR EQUAL TO", "token_cost": 1, "token_density": 1.0, "quality_score": 99.99999999999999, "llm_support": ["openai", "qwen", "llama"], "created": "2025-05-23", "generator": "fresh_512_v1"}, {"id": "NG0052", "symbol": "⦣", "code": "ng:logic:implies_1", "fallback": "[IMPLIE1]", "category": "logic", "meaning": "implies", "description": "Logic operation: implies", "unicode_point": "U+29A3", "unicode_name": "REVERSED ANGLE", "token_cost": 1, "token_density": 1.0, "quality_score": 99.99999999999999, "llm_support": ["openai", "qwen", "llama"], "created": "2025-05-23", "generator": "fresh_512_v1"}, {"id": "NG0053", "symbol": "⩲", "code": "ng:logic:iff_1", "fallback": "[IFF1]", "category": "logic", "meaning": "iff", "description": "Logic operation: iff", "unicode_point": "U+2A72", "unicode_name": "PLUS SIGN ABOVE EQUALS SIGN", "token_cost": 1, "token_density": 1.0, "quality_score": 99.99999999999999, "llm_support": ["openai", "qwen", "llama"], "created": "2025-05-23", "generator": "fresh_512_v1"}, {"id": "NG0054", "symbol": "⛤", "code": "ng:logic:xor_1", "fallback": "[XOR1]", "category": "logic", "meaning": "xor", "description": "Logic operation: xor", "unicode_point": "U+26E4", "unicode_name": "PENTAGRAM", "token_cost": 1, "token_density": 1.0, "quality_score": 99.99999999999999, "llm_support": ["openai", "qwen", "llama"], "created": "2025-05-23", "generator": "fresh_512_v1"}, {"id": "NG0055", "symbol": "⫬", "code": "ng:logic:nand_1", "fallback": "[NAND1]", "category": "logic", "meaning": "nand", "description": "Logic operation: nand", "unicode_point": "U+2AEC", "unicode_name": "DOUBLE STROKE NOT SIGN", "token_cost": 1, "token_density": 1.0, "quality_score": 99.99999999999999, "llm_support": ["openai", "qwen", "llama"], "created": "2025-05-23", "generator": "fresh_512_v1"}, {"id": "NG0056", "symbol": "⛹", "code": "ng:logic:nor_1", "fallback": "[NOR1]", "category": "logic", "meaning": "nor", "description": "Logic operation: nor", "unicode_point": "U+26F9", "unicode_name": "PERSON WITH BALL", "token_cost": 1, "token_density": 1.0, "quality_score": 99.99999999999999, "llm_support": ["openai", "qwen", "llama"], "created": "2025-05-23", "generator": "fresh_512_v1"}, {"id": "NG0057", "symbol": "⇻", "code": "ng:logic:forall_1", "fallback": "[FORALL1]", "category": "logic", "meaning": "forall", "description": "Logic operation: forall", "unicode_point": "U+21FB", "unicode_name": "RIGHTWARDS ARROW WITH DOUBLE VERTICAL STROKE", "token_cost": 1, "token_density": 1.0, "quality_score": 99.99999999999999, "llm_support": ["openai", "qwen", "llama"], "created": "2025-05-23", "generator": "fresh_512_v1"}, {"id": "NG0058", "symbol": "⇝", "code": "ng:logic:exists_1", "fallback": "[EXISTS1]", "category": "logic", "meaning": "exists", "description": "Logic operation: exists", "unicode_point": "U+21DD", "unicode_name": "RIGHTWARDS SQUIGGLE ARROW", "token_cost": 1, "token_density": 1.0, "quality_score": 99.99999999999999, "llm_support": ["openai", "qwen", "llama"], "created": "2025-05-23", "generator": "fresh_512_v1"}, {"id": "NG0059", "symbol": "⩍", "code": "ng:logic:unique_1", "fallback": "[UNIQUE1]", "category": "logic", "meaning": "unique", "description": "Logic operation: unique", "unicode_point": "U+2A4D", "unicode_name": "CLOSED INTERSECTION WITH SERIFS", "token_cost": 1, "token_density": 1.0, "quality_score": 99.99999999999999, "llm_support": ["openai", "qwen", "llama"], "created": "2025-05-23", "generator": "fresh_512_v1"}, {"id": "NG0060", "symbol": "⏫", "code": "ng:logic:lambda_1", "fallback": "[LAMBDA1]", "category": "logic", "meaning": "lambda", "description": "Logic operation: lambda", "unicode_point": "U+23EB", "unicode_name": "BLACK UP-POINTING DOUBLE TRIANGLE", "token_cost": 1, "token_density": 1.0, "quality_score": 99.99999999999999, "llm_support": ["openai", "qwen", "llama"], "created": "2025-05-23", "generator": "fresh_512_v1"}, {"id": "NG0061", "symbol": "◦", "code": "ng:logic:apply_1", "fallback": "[APPLY1]", "category": "logic", "meaning": "apply", "description": "Logic operation: apply", "unicode_point": "U+25E6", "unicode_name": "WHITE BULLET", "token_cost": 1, "token_density": 1.0, "quality_score": 99.99999999999999, "llm_support": ["openai", "qwen", "llama"], "created": "2025-05-23", "generator": "fresh_512_v1"}, {"id": "NG0062", "symbol": "⚃", "code": "ng:logic:compose_1", "fallback": "[COMPOS1]", "category": "logic", "meaning": "compose", "description": "Logic operation: compose", "unicode_point": "U+2683", "unicode_name": "DIE FACE-4", "token_cost": 1, "token_density": 1.0, "quality_score": 99.99999999999999, "llm_support": ["openai", "qwen", "llama"], "created": "2025-05-23", "generator": "fresh_512_v1"}, {"id": "NG0063", "symbol": "⪒", "code": "ng:logic:curry_1", "fallback": "[CURRY1]", "category": "logic", "meaning": "curry", "description": "Logic operation: curry", "unicode_point": "U+2A92", "unicode_name": "GREATER-THAN ABOVE LESS-THAN ABOVE DOUBLE-LINE EQUAL", "token_cost": 1, "token_density": 1.0, "quality_score": 99.99999999999999, "llm_support": ["openai", "qwen", "llama"], "created": "2025-05-23", "generator": "fresh_512_v1"}, {"id": "NG0064", "symbol": "⇚", "code": "ng:logic:necessary_1", "fallback": "[NECESS1]", "category": "logic", "meaning": "necessary", "description": "Logic operation: necessary", "unicode_point": "U+21DA", "unicode_name": "LEFTWARDS TRIPLE ARROW", "token_cost": 1, "token_density": 1.0, "quality_score": 99.99999999999999, "llm_support": ["openai", "qwen", "llama"], "created": "2025-05-23", "generator": "fresh_512_v1"}, {"id": "NG0065", "symbol": "◽", "code": "ng:logic:possible_1", "fallback": "[POSSIB1]", "category": "logic", "meaning": "possible", "description": "Logic operation: possible", "unicode_point": "U+25FD", "unicode_name": "WHITE MEDIUM SMALL SQUARE", "token_cost": 1, "token_density": 1.0, "quality_score": 99.99999999999999, "llm_support": ["openai", "qwen", "llama"], "created": "2025-05-23", "generator": "fresh_512_v1"}, {"id": "NG0066", "symbol": "⫌", "code": "ng:logic:knows_1", "fallback": "[KNOWS1]", "category": "logic", "meaning": "knows", "description": "Logic operation: knows", "unicode_point": "U+2ACC", "unicode_name": "SUPERSET OF ABOVE NOT EQUAL TO", "token_cost": 1, "token_density": 1.0, "quality_score": 99.99999999999999, "llm_support": ["openai", "qwen", "llama"], "created": "2025-05-23", "generator": "fresh_512_v1"}, {"id": "NG0067", "symbol": "↙", "code": "ng:logic:believes_1", "fallback": "[BELIEV1]", "category": "logic", "meaning": "believes", "description": "Logic operation: believes", "unicode_point": "U+2199", "unicode_name": "SOUTH WEST ARROW", "token_cost": 1, "token_density": 1.0, "quality_score": 99.99999999999999, "llm_support": ["openai", "qwen", "llama"], "created": "2025-05-23", "generator": "fresh_512_v1"}, {"id": "NG0068", "symbol": "⟋", "code": "ng:logic:ought_1", "fallback": "[OUGHT1]", "category": "logic", "meaning": "ought", "description": "Logic operation: ought", "unicode_point": "U+27CB", "unicode_name": "MATHEMATICAL RISING DIAGONAL", "token_cost": 1, "token_density": 1.0, "quality_score": 99.99999999999999, "llm_support": ["openai", "qwen", "llama"], "created": "2025-05-23", "generator": "fresh_512_v1"}, {"id": "NG0069", "symbol": "⟥", "code": "ng:logic:permitted_1", "fallback": "[PERMIT1]", "category": "logic", "meaning": "permitted", "description": "Logic operation: permitted", "unicode_point": "U+27E5", "unicode_name": "WHITE SQUARE WITH RIGHTWARDS TICK", "token_cost": 1, "token_density": 1.0, "quality_score": 99.99999999999999, "llm_support": ["openai", "qwen", "llama"], "created": "2025-05-23", "generator": "fresh_512_v1"}, {"id": "NG0070", "symbol": "⊜", "code": "ng:logic:always_1", "fallback": "[ALWAYS1]", "category": "logic", "meaning": "always", "description": "Logic operation: always", "unicode_point": "U+229C", "unicode_name": "CIRCLED EQUALS", "token_cost": 1, "token_density": 1.0, "quality_score": 99.99999999999999, "llm_support": ["openai", "qwen", "llama"], "created": "2025-05-23", "generator": "fresh_512_v1"}, {"id": "NG0071", "symbol": "⟃", "code": "ng:logic:eventually_1", "fallback": "[EVENTU1]", "category": "logic", "meaning": "eventually", "description": "Logic operation: eventually", "unicode_point": "U+27C3", "unicode_name": "OPEN SUBSET", "token_cost": 1, "token_density": 1.0, "quality_score": 99.99999999999999, "llm_support": ["openai", "qwen", "llama"], "created": "2025-05-23", "generator": "fresh_512_v1"}, {"id": "NG0072", "symbol": "∰", "code": "ng:logic:until_1", "fallback": "[UNTIL1]", "category": "logic", "meaning": "until", "description": "Logic operation: until", "unicode_point": "U+2230", "unicode_name": "VOLUME INTEGRAL", "token_cost": 1, "token_density": 1.0, "quality_score": 99.99999999999999, "llm_support": ["openai", "qwen", "llama"], "created": "2025-05-23", "generator": "fresh_512_v1"}, {"id": "NG0073", "symbol": "⟮", "code": "ng:logic:since_1", "fallback": "[SINCE1]", "category": "logic", "meaning": "since", "description": "Logic operation: since", "unicode_point": "U+27EE", "unicode_name": "MATHEMATICAL LEFT FLATTENED PARENTHESIS", "token_cost": 1, "token_density": 1.0, "quality_score": 99.99999999999999, "llm_support": ["openai", "qwen", "llama"], "created": "2025-05-23", "generator": "fresh_512_v1"}, {"id": "NG0074", "symbol": "⨺", "code": "ng:logic:next_1", "fallback": "[NEXT1]", "category": "logic", "meaning": "next", "description": "Logic operation: next", "unicode_point": "U+2A3A", "unicode_name": "MINUS SIGN IN TRIANGLE", "token_cost": 1, "token_density": 1.0, "quality_score": 99.99999999999999, "llm_support": ["openai", "qwen", "llama"], "created": "2025-05-23", "generator": "fresh_512_v1"}, {"id": "NG0075", "symbol": "≫", "code": "ng:logic:previous_1", "fallback": "[PREVIO1]", "category": "logic", "meaning": "previous", "description": "Logic operation: previous", "unicode_point": "U+226B", "unicode_name": "MUCH GREATER-THAN", "token_cost": 1, "token_density": 1.0, "quality_score": 99.99999999999999, "llm_support": ["openai", "qwen", "llama"], "created": "2025-05-23", "generator": "fresh_512_v1"}, {"id": "NG0076", "symbol": "⨘", "code": "ng:logic:entails_1", "fallback": "[ENTAIL1]", "category": "logic", "meaning": "entails", "description": "Logic operation: entails", "unicode_point": "U+2A18", "unicode_name": "INTEGRAL WITH TIMES SIGN", "token_cost": 1, "token_density": 1.0, "quality_score": 99.99999999999999, "llm_support": ["openai", "qwen", "llama"], "created": "2025-05-23", "generator": "fresh_512_v1"}, {"id": "NG0077", "symbol": "↴", "code": "ng:logic:proves_1", "fallback": "[PROVES1]", "category": "logic", "meaning": "proves", "description": "Logic operation: proves", "unicode_point": "U+21B4", "unicode_name": "RIGHTWARDS ARROW WITH CORNER DOWNWARDS", "token_cost": 1, "token_density": 1.0, "quality_score": 99.99999999999999, "llm_support": ["openai", "qwen", "llama"], "created": "2025-05-23", "generator": "fresh_512_v1"}, {"id": "NG0078", "symbol": "⧵", "code": "ng:logic:derives_1", "fallback": "[DERIVE1]", "category": "logic", "meaning": "derives", "description": "Logic operation: derives", "unicode_point": "U+29F5", "unicode_name": "REVERSE SOLIDUS OPERATOR", "token_cost": 1, "token_density": 1.0, "quality_score": 99.99999999999999, "llm_support": ["openai", "qwen", "llama"], "created": "2025-05-23", "generator": "fresh_512_v1"}, {"id": "NG0079", "symbol": "≋", "code": "ng:logic:concludes_1", "fallback": "[CONCLU2]", "category": "logic", "meaning": "concludes", "description": "Logic operation: concludes", "unicode_point": "U+224B", "unicode_name": "TRIPLE TILDE", "token_cost": 1, "token_density": 1.0, "quality_score": 99.99999999999999, "llm_support": ["openai", "qwen", "llama"], "created": "2025-05-23", "generator": "fresh_512_v1"}, {"id": "NG0080", "symbol": "⧈", "code": "ng:logic:assumes_1", "fallback": "[ASSUME1]", "category": "logic", "meaning": "assumes", "description": "Logic operation: assumes", "unicode_point": "U+29C8", "unicode_name": "SQUARED SQUARE", "token_cost": 1, "token_density": 1.0, "quality_score": 99.99999999999999, "llm_support": ["openai", "qwen", "llama"], "created": "2025-05-23", "generator": "fresh_512_v1"}, {"id": "NG0081", "symbol": "⧑", "code": "ng:logic:supposes_1", "fallback": "[SUPPOS1]", "category": "logic", "meaning": "supposes", "description": "Logic operation: supposes", "unicode_point": "U+29D1", "unicode_name": "BOWTIE WITH LEFT HALF BLACK", "token_cost": 1, "token_density": 1.0, "quality_score": 99.99999999999999, "llm_support": ["openai", "qwen", "llama"], "created": "2025-05-23", "generator": "fresh_512_v1"}, {"id": "NG0082", "symbol": "⧔", "code": "ng:logic:infer_1", "fallback": "[INFER1]", "category": "logic", "meaning": "infer", "description": "Logic operation: infer", "unicode_point": "U+29D4", "unicode_name": "TIMES WITH LEFT HALF BLACK", "token_cost": 1, "token_density": 1.0, "quality_score": 99.99999999999999, "llm_support": ["openai", "qwen", "llama"], "created": "2025-05-23", "generator": "fresh_512_v1"}, {"id": "NG0083", "symbol": "⏵", "code": "ng:logic:deduce_1", "fallback": "[DEDUCE1]", "category": "logic", "meaning": "deduce", "description": "Logic operation: deduce", "unicode_point": "U+23F5", "unicode_name": "BLACK MEDIUM RIGHT-POINTING TRIANGLE", "token_cost": 1, "token_density": 1.0, "quality_score": 99.99999999999999, "llm_support": ["openai", "qwen", "llama"], "created": "2025-05-23", "generator": "fresh_512_v1"}, {"id": "NG0084", "symbol": "⏃", "code": "ng:logic:induce_1", "fallback": "[INDUCE1]", "category": "logic", "meaning": "induce", "description": "Logic operation: induce", "unicode_point": "U+23C3", "unicode_name": "DENTISTRY SYMBOL LIGHT VERTICAL WITH TRIANGLE", "token_cost": 1, "token_density": 1.0, "quality_score": 99.99999999999999, "llm_support": ["openai", "qwen", "llama"], "created": "2025-05-23", "generator": "fresh_512_v1"}, {"id": "NG0085", "symbol": "⪚", "code": "ng:logic:reason_1", "fallback": "[REASON1]", "category": "logic", "meaning": "reason", "description": "Logic operation: reason", "unicode_point": "U+2A9A", "unicode_name": "DOUBLE-LINE EQUAL TO OR GREATER-THAN", "token_cost": 1, "token_density": 1.0, "quality_score": 99.99999999999999, "llm_support": ["openai", "qwen", "llama"], "created": "2025-05-23", "generator": "fresh_512_v1"}, {"id": "NG0086", "symbol": "∴", "code": "ng:logic:think_1", "fallback": "[THINK1]", "category": "logic", "meaning": "think", "description": "Logic operation: think", "unicode_point": "U+2234", "unicode_name": "THEREFORE", "token_cost": 1, "token_density": 1.0, "quality_score": 99.99999999999999, "llm_support": ["openai", "qwen", "llama"], "created": "2025-05-23", "generator": "fresh_512_v1"}, {"id": "NG0087", "symbol": "↨", "code": "ng:logic:conclude_1", "fallback": "[CONCLU3]", "category": "logic", "meaning": "conclude", "description": "Logic operation: conclude", "unicode_point": "U+21A8", "unicode_name": "UP DOWN ARROW WITH BASE", "token_cost": 1, "token_density": 1.0, "quality_score": 99.99999999999999, "llm_support": ["openai", "qwen", "llama"], "created": "2025-05-23", "generator": "fresh_512_v1"}, {"id": "NG0088", "symbol": "∿", "code": "ng:logic:premise_1", "fallback": "[PREMIS1]", "category": "logic", "meaning": "premise", "description": "Logic operation: premise", "unicode_point": "U+223F", "unicode_name": "SINE WAVE", "token_cost": 1, "token_density": 1.0, "quality_score": 99.99999999999999, "llm_support": ["openai", "qwen", "llama"], "created": "2025-05-23", "generator": "fresh_512_v1"}, {"id": "NG0089", "symbol": "⇇", "code": "ng:logic:axiom_1", "fallback": "[AXIOM1]", "category": "logic", "meaning": "axiom", "description": "Logic operation: axiom", "unicode_point": "U+21C7", "unicode_name": "LEFTWARDS PAIRED ARROWS", "token_cost": 1, "token_density": 1.0, "quality_score": 99.99999999999999, "llm_support": ["openai", "qwen", "llama"], "created": "2025-05-23", "generator": "fresh_512_v1"}, {"id": "NG0090", "symbol": "⚳", "code": "ng:logic:theorem_1", "fallback": "[THEORE1]", "category": "logic", "meaning": "theorem", "description": "Logic operation: theorem", "unicode_point": "U+26B3", "unicode_name": "CERES", "token_cost": 1, "token_density": 1.0, "quality_score": 99.99999999999999, "llm_support": ["openai", "qwen", "llama"], "created": "2025-05-23", "generator": "fresh_512_v1"}, {"id": "NG0091", "symbol": "⩕", "code": "ng:logic:lemma_1", "fallback": "[LEMMA1]", "category": "logic", "meaning": "lemma", "description": "Logic operation: lemma", "unicode_point": "U+2A55", "unicode_name": "TWO INTERSECTING LOGICAL AND", "token_cost": 1, "token_density": 1.0, "quality_score": 99.99999999999999, "llm_support": ["openai", "qwen", "llama"], "created": "2025-05-23", "generator": "fresh_512_v1"}, {"id": "NG0092", "symbol": "⦎", "code": "ng:logic:proof_1", "fallback": "[PROOF1]", "category": "logic", "meaning": "proof", "description": "Logic operation: proof", "unicode_point": "U+298E", "unicode_name": "RIGHT SQUARE BRACKET WITH TICK IN BOTTOM CORNER", "token_cost": 1, "token_density": 1.0, "quality_score": 99.99999999999999, "llm_support": ["openai", "qwen", "llama"], "created": "2025-05-23", "generator": "fresh_512_v1"}, {"id": "NG0093", "symbol": "⦪", "code": "ng:logic:valid_1", "fallback": "[VALID1]", "category": "logic", "meaning": "valid", "description": "Logic operation: valid", "unicode_point": "U+29AA", "unicode_name": "MEASURED <PERSON><PERSON>LE WITH OPEN ARM ENDING IN ARROW POINTING DOWN AND RIGHT", "token_cost": 1, "token_density": 1.0, "quality_score": 99.99999999999999, "llm_support": ["openai", "qwen", "llama"], "created": "2025-05-23", "generator": "fresh_512_v1"}, {"id": "NG0094", "symbol": "↔", "code": "ng:logic:sound_1", "fallback": "[SOUND1]", "category": "logic", "meaning": "sound", "description": "Logic operation: sound", "unicode_point": "U+2194", "unicode_name": "LEFT RIGHT ARROW", "token_cost": 1, "token_density": 1.0, "quality_score": 99.99999999999999, "llm_support": ["openai", "qwen", "llama"], "created": "2025-05-23", "generator": "fresh_512_v1"}, {"id": "NG0095", "symbol": "▭", "code": "ng:logic:complete_1", "fallback": "[COMPLE1]", "category": "logic", "meaning": "complete", "description": "Logic operation: complete", "unicode_point": "U+25AD", "unicode_name": "WHITE RECTANGLE", "token_cost": 1, "token_density": 1.0, "quality_score": 99.99999999999999, "llm_support": ["openai", "qwen", "llama"], "created": "2025-05-23", "generator": "fresh_512_v1"}, {"id": "NG0096", "symbol": "♒", "code": "ng:logic:consistent_1", "fallback": "[CONSIS1]", "category": "logic", "meaning": "consistent", "description": "Logic operation: consistent", "unicode_point": "U+2652", "unicode_name": "AQUARIUS", "token_cost": 1, "token_density": 1.0, "quality_score": 99.99999999999999, "llm_support": ["openai", "qwen", "llama"], "created": "2025-05-23", "generator": "fresh_512_v1"}, {"id": "NG0097", "symbol": "⟭", "code": "ng:structure:class", "fallback": "[CLASS]", "category": "structure", "meaning": "class", "description": "Structure operation: class", "unicode_point": "U+27ED", "unicode_name": "MATHEMATICAL RIGHT WHITE TORTOISE SHELL BRACKET", "token_cost": 1, "token_density": 1.0, "quality_score": 99.99999999999999, "llm_support": ["openai", "qwen", "llama"], "created": "2025-05-23", "generator": "fresh_512_v1"}, {"id": "NG0098", "symbol": "↢", "code": "ng:structure:interface", "fallback": "[INTERF]", "category": "structure", "meaning": "interface", "description": "Structure operation: interface", "unicode_point": "U+21A2", "unicode_name": "LEFTWARDS ARROW WITH TAIL", "token_cost": 1, "token_density": 1.0, "quality_score": 99.99999999999999, "llm_support": ["openai", "qwen", "llama"], "created": "2025-05-23", "generator": "fresh_512_v1"}, {"id": "NG0099", "symbol": "⩒", "code": "ng:structure:abstract", "fallback": "[ABSTRA]", "category": "structure", "meaning": "abstract", "description": "Structure operation: abstract", "unicode_point": "U+2A52", "unicode_name": "LOGICAL OR WITH DOT ABOVE", "token_cost": 1, "token_density": 1.0, "quality_score": 99.99999999999999, "llm_support": ["openai", "qwen", "llama"], "created": "2025-05-23", "generator": "fresh_512_v1"}, {"id": "NG0100", "symbol": "⏗", "code": "ng:structure:concrete", "fallback": "[CONCRE]", "category": "structure", "meaning": "concrete", "description": "Structure operation: concrete", "unicode_point": "U+23D7", "unicode_name": "METRICAL TRISEME", "token_cost": 1, "token_density": 1.0, "quality_score": 99.99999999999999, "llm_support": ["openai", "qwen", "llama"], "created": "2025-05-23", "generator": "fresh_512_v1"}, {"id": "NG0101", "symbol": "⧃", "code": "ng:structure:inherit", "fallback": "[INHERI]", "category": "structure", "meaning": "inherit", "description": "Structure operation: inherit", "unicode_point": "U+29C3", "unicode_name": "CIRCLE WITH TWO HOR<PERSON><PERSON><PERSON><PERSON>L STROKES TO THE RIGHT", "token_cost": 1, "token_density": 1.0, "quality_score": 99.99999999999999, "llm_support": ["openai", "qwen", "llama"], "created": "2025-05-23", "generator": "fresh_512_v1"}, {"id": "NG0102", "symbol": "⏡", "code": "ng:structure:implement", "fallback": "[IMPLEM]", "category": "structure", "meaning": "implement", "description": "Structure operation: implement", "unicode_point": "U+23E1", "unicode_name": "BOTTOM TORTOISE SHELL BRACKET", "token_cost": 1, "token_density": 1.0, "quality_score": 99.99999999999999, "llm_support": ["openai", "qwen", "llama"], "created": "2025-05-23", "generator": "fresh_512_v1"}, {"id": "NG0103", "symbol": "⏰", "code": "ng:structure:function", "fallback": "[FUNCTI]", "category": "structure", "meaning": "function", "description": "Structure operation: function", "unicode_point": "U+23F0", "unicode_name": "ALARM CLOCK", "token_cost": 1, "token_density": 1.0, "quality_score": 99.99999999999999, "llm_support": ["openai", "qwen", "llama"], "created": "2025-05-23", "generator": "fresh_512_v1"}, {"id": "NG0104", "symbol": "∲", "code": "ng:structure:method", "fallback": "[METHOD]", "category": "structure", "meaning": "method", "description": "Structure operation: method", "unicode_point": "U+2232", "unicode_name": "CLOCKWISE CONTOUR INTEGRAL", "token_cost": 1, "token_density": 1.0, "quality_score": 99.99999999999999, "llm_support": ["openai", "qwen", "llama"], "created": "2025-05-23", "generator": "fresh_512_v1"}, {"id": "NG0105", "symbol": "⋘", "code": "ng:structure:procedure", "fallback": "[PROCED]", "category": "structure", "meaning": "procedure", "description": "Structure operation: procedure", "unicode_point": "U+22D8", "unicode_name": "VERY MUCH LESS-THAN", "token_cost": 1, "token_density": 1.0, "quality_score": 99.99999999999999, "llm_support": ["openai", "qwen", "llama"], "created": "2025-05-23", "generator": "fresh_512_v1"}, {"id": "NG0106", "symbol": "⇑", "code": "ng:structure:lambda", "fallback": "[LAMBDA2]", "category": "structure", "meaning": "lambda", "description": "Structure operation: lambda", "unicode_point": "U+21D1", "unicode_name": "UPWARDS DOUBLE ARROW", "token_cost": 1, "token_density": 1.0, "quality_score": 99.99999999999999, "llm_support": ["openai", "qwen", "llama"], "created": "2025-05-23", "generator": "fresh_512_v1"}, {"id": "NG0107", "symbol": "▥", "code": "ng:structure:closure", "fallback": "[CLOSUR]", "category": "structure", "meaning": "closure", "description": "Structure operation: closure", "unicode_point": "U+25A5", "unicode_name": "SQUARE WITH VERTICAL FILL", "token_cost": 1, "token_density": 1.0, "quality_score": 99.99999999999999, "llm_support": ["openai", "qwen", "llama"], "created": "2025-05-23", "generator": "fresh_512_v1"}, {"id": "NG0108", "symbol": "⧚", "code": "ng:structure:partial", "fallback": "[PARTIA]", "category": "structure", "meaning": "partial", "description": "Structure operation: partial", "unicode_point": "U+29DA", "unicode_name": "LEFT DOUBLE WIGGLY FENCE", "token_cost": 1, "token_density": 1.0, "quality_score": 99.99999999999999, "llm_support": ["openai", "qwen", "llama"], "created": "2025-05-23", "generator": "fresh_512_v1"}, {"id": "NG0109", "symbol": "⇧", "code": "ng:structure:module", "fallback": "[MODULE]", "category": "structure", "meaning": "module", "description": "Structure operation: module", "unicode_point": "U+21E7", "unicode_name": "UPWARDS WHITE ARROW", "token_cost": 1, "token_density": 1.0, "quality_score": 99.99999999999999, "llm_support": ["openai", "qwen", "llama"], "created": "2025-05-23", "generator": "fresh_512_v1"}, {"id": "NG0110", "symbol": "⌤", "code": "ng:structure:package", "fallback": "[PACKAG]", "category": "structure", "meaning": "package", "description": "Structure operation: package", "unicode_point": "U+2324", "unicode_name": "UP ARROWHEAD BET<PERSON>EEN TWO HORIZONTAL BARS", "token_cost": 1, "token_density": 1.0, "quality_score": 99.99999999999999, "llm_support": ["openai", "qwen", "llama"], "created": "2025-05-23", "generator": "fresh_512_v1"}, {"id": "NG0111", "symbol": "⛖", "code": "ng:structure:namespace", "fallback": "[NAMESP]", "category": "structure", "meaning": "namespace", "description": "Structure operation: namespace", "unicode_point": "U+26D6", "unicode_name": "BLACK TWO-WAY LEFT WAY TRAFFIC", "token_cost": 1, "token_density": 1.0, "quality_score": 99.99999999999999, "llm_support": ["openai", "qwen", "llama"], "created": "2025-05-23", "generator": "fresh_512_v1"}, {"id": "NG0112", "symbol": "⚊", "code": "ng:structure:import", "fallback": "[IMPORT]", "category": "structure", "meaning": "import", "description": "Structure operation: import", "unicode_point": "U+268A", "unicode_name": "MONOGRAM FOR YANG", "token_cost": 1, "token_density": 1.0, "quality_score": 99.99999999999999, "llm_support": ["openai", "qwen", "llama"], "created": "2025-05-23", "generator": "fresh_512_v1"}, {"id": "NG0113", "symbol": "⍽", "code": "ng:structure:export", "fallback": "[EXPORT]", "category": "structure", "meaning": "export", "description": "Structure operation: export", "unicode_point": "U+237D", "unicode_name": "SHOULDERED OPEN BOX", "token_cost": 1, "token_density": 1.0, "quality_score": 99.99999999999999, "llm_support": ["openai", "qwen", "llama"], "created": "2025-05-23", "generator": "fresh_512_v1"}, {"id": "NG0114", "symbol": "⟤", "code": "ng:structure:include", "fallback": "[INCLUD]", "category": "structure", "meaning": "include", "description": "Structure operation: include", "unicode_point": "U+27E4", "unicode_name": "WHITE SQUARE WITH LEFTWARDS TICK", "token_cost": 1, "token_density": 1.0, "quality_score": 99.99999999999999, "llm_support": ["openai", "qwen", "llama"], "created": "2025-05-23", "generator": "fresh_512_v1"}, {"id": "NG0115", "symbol": "⛼", "code": "ng:structure:global", "fallback": "[GLOBAL]", "category": "structure", "meaning": "global", "description": "Structure operation: global", "unicode_point": "U+26FC", "unicode_name": "HEADSTONE GRAVEYARD SYMBOL", "token_cost": 1, "token_density": 1.0, "quality_score": 99.99999999999999, "llm_support": ["openai", "qwen", "llama"], "created": "2025-05-23", "generator": "fresh_512_v1"}, {"id": "NG0116", "symbol": "☑", "code": "ng:structure:local", "fallback": "[LOCAL]", "category": "structure", "meaning": "local", "description": "Structure operation: local", "unicode_point": "U+2611", "unicode_name": "BALLOT BOX WITH CHECK", "token_cost": 1, "token_density": 1.0, "quality_score": 99.99999999999999, "llm_support": ["openai", "qwen", "llama"], "created": "2025-05-23", "generator": "fresh_512_v1"}, {"id": "NG0117", "symbol": "⪹", "code": "ng:structure:private", "fallback": "[PRIVAT]", "category": "structure", "meaning": "private", "description": "Structure operation: private", "unicode_point": "U+2AB9", "unicode_name": "PRECEDES ABOVE NOT ALMOST EQUAL TO", "token_cost": 1, "token_density": 1.0, "quality_score": 99.99999999999999, "llm_support": ["openai", "qwen", "llama"], "created": "2025-05-23", "generator": "fresh_512_v1"}, {"id": "NG0118", "symbol": "⦨", "code": "ng:structure:public", "fallback": "[PUBLIC]", "category": "structure", "meaning": "public", "description": "Structure operation: public", "unicode_point": "U+29A8", "unicode_name": "MEASURED AN<PERSON>LE WITH OPEN ARM ENDING IN ARROW POINTING UP AND RIGHT", "token_cost": 1, "token_density": 1.0, "quality_score": 99.99999999999999, "llm_support": ["openai", "qwen", "llama"], "created": "2025-05-23", "generator": "fresh_512_v1"}, {"id": "NG0119", "symbol": "⟦", "code": "ng:structure:protected", "fallback": "[PROTEC]", "category": "structure", "meaning": "protected", "description": "Structure operation: protected", "unicode_point": "U+27E6", "unicode_name": "MATHEMATICAL LEFT WHITE SQUARE BRACKET", "token_cost": 1, "token_density": 1.0, "quality_score": 99.99999999999999, "llm_support": ["openai", "qwen", "llama"], "created": "2025-05-23", "generator": "fresh_512_v1"}, {"id": "NG0120", "symbol": "⋭", "code": "ng:structure:static", "fallback": "[STATIC]", "category": "structure", "meaning": "static", "description": "Structure operation: static", "unicode_point": "U+22ED", "unicode_name": "DOES NOT CONTAIN AS NORMAL SUBGROUP OR EQUAL", "token_cost": 1, "token_density": 1.0, "quality_score": 99.99999999999999, "llm_support": ["openai", "qwen", "llama"], "created": "2025-05-23", "generator": "fresh_512_v1"}, {"id": "NG0121", "symbol": "⟗", "code": "ng:structure:array", "fallback": "[ARRAY]", "category": "structure", "meaning": "array", "description": "Structure operation: array", "unicode_point": "U+27D7", "unicode_name": "FULL OUTER JOIN", "token_cost": 1, "token_density": 1.0, "quality_score": 99.99999999999999, "llm_support": ["openai", "qwen", "llama"], "created": "2025-05-23", "generator": "fresh_512_v1"}, {"id": "NG0122", "symbol": "⟙", "code": "ng:structure:list", "fallback": "[LIST]", "category": "structure", "meaning": "list", "description": "Structure operation: list", "unicode_point": "U+27D9", "unicode_name": "LARGE DOWN TACK", "token_cost": 1, "token_density": 1.0, "quality_score": 99.99999999999999, "llm_support": ["openai", "qwen", "llama"], "created": "2025-05-23", "generator": "fresh_512_v1"}, {"id": "NG0123", "symbol": "⟄", "code": "ng:structure:dict", "fallback": "[DICT]", "category": "structure", "meaning": "dict", "description": "Structure operation: dict", "unicode_point": "U+27C4", "unicode_name": "OPEN SUPERSET", "token_cost": 1, "token_density": 1.0, "quality_score": 99.99999999999999, "llm_support": ["openai", "qwen", "llama"], "created": "2025-05-23", "generator": "fresh_512_v1"}, {"id": "NG0124", "symbol": "⌎", "code": "ng:structure:set", "fallback": "[SET]", "category": "structure", "meaning": "set", "description": "Structure operation: set", "unicode_point": "U+230E", "unicode_name": "TOP RIGHT CROP", "token_cost": 1, "token_density": 1.0, "quality_score": 99.99999999999999, "llm_support": ["openai", "qwen", "llama"], "created": "2025-05-23", "generator": "fresh_512_v1"}, {"id": "NG0125", "symbol": "⧦", "code": "ng:structure:tuple", "fallback": "[TUPLE]", "category": "structure", "meaning": "tuple", "description": "Structure operation: tuple", "unicode_point": "U+29E6", "unicode_name": "GLEICH STARK", "token_cost": 1, "token_density": 1.0, "quality_score": 99.99999999999999, "llm_support": ["openai", "qwen", "llama"], "created": "2025-05-23", "generator": "fresh_512_v1"}, {"id": "NG0126", "symbol": "⛀", "code": "ng:structure:struct", "fallback": "[STRUCT]", "category": "structure", "meaning": "struct", "description": "Structure operation: struct", "unicode_point": "U+26C0", "unicode_name": "WHITE DRAUGHTS MAN", "token_cost": 1, "token_density": 1.0, "quality_score": 99.99999999999999, "llm_support": ["openai", "qwen", "llama"], "created": "2025-05-23", "generator": "fresh_512_v1"}, {"id": "NG0127", "symbol": "⍠", "code": "ng:structure:object", "fallback": "[OBJECT]", "category": "structure", "meaning": "object", "description": "Structure operation: object", "unicode_point": "U+2360", "unicode_name": "APL FUNCTIONAL SYMBOL QUAD COLON", "token_cost": 1, "token_density": 1.0, "quality_score": 99.99999999999999, "llm_support": ["openai", "qwen", "llama"], "created": "2025-05-23", "generator": "fresh_512_v1"}, {"id": "NG0128", "symbol": "⌢", "code": "ng:structure:instance", "fallback": "[INSTAN]", "category": "structure", "meaning": "instance", "description": "Structure operation: instance", "unicode_point": "U+2322", "unicode_name": "FROWN", "token_cost": 1, "token_density": 1.0, "quality_score": 99.99999999999999, "llm_support": ["openai", "qwen", "llama"], "created": "2025-05-23", "generator": "fresh_512_v1"}, {"id": "NG0129", "symbol": "◁", "code": "ng:structure:property", "fallback": "[PROPER]", "category": "structure", "meaning": "property", "description": "Structure operation: property", "unicode_point": "U+25C1", "unicode_name": "WHITE LEFT-POINTING TRIANGLE", "token_cost": 1, "token_density": 1.0, "quality_score": 99.99999999999999, "llm_support": ["openai", "qwen", "llama"], "created": "2025-05-23", "generator": "fresh_512_v1"}, {"id": "NG0130", "symbol": "◥", "code": "ng:structure:field", "fallback": "[FIELD]", "category": "structure", "meaning": "field", "description": "Structure operation: field", "unicode_point": "U+25E5", "unicode_name": "BLACK UPPER RIGHT TRIANGLE", "token_cost": 1, "token_density": 1.0, "quality_score": 99.99999999999999, "llm_support": ["openai", "qwen", "llama"], "created": "2025-05-23", "generator": "fresh_512_v1"}, {"id": "NG0131", "symbol": "⧧", "code": "ng:structure:attribute", "fallback": "[ATTRIB]", "category": "structure", "meaning": "attribute", "description": "Structure operation: attribute", "unicode_point": "U+29E7", "unicode_name": "THERMODYNAMIC", "token_cost": 1, "token_density": 1.0, "quality_score": 99.99999999999999, "llm_support": ["openai", "qwen", "llama"], "created": "2025-05-23", "generator": "fresh_512_v1"}, {"id": "NG0132", "symbol": "⛒", "code": "ng:structure:member", "fallback": "[MEMBER]", "category": "structure", "meaning": "member", "description": "Structure operation: member", "unicode_point": "U+26D2", "unicode_name": "CIRCLED CROSSING LANES", "token_cost": 1, "token_density": 1.0, "quality_score": 99.99999999999999, "llm_support": ["openai", "qwen", "llama"], "created": "2025-05-23", "generator": "fresh_512_v1"}, {"id": "NG0133", "symbol": "∗", "code": "ng:structure:constructor", "fallback": "[CONSTR]", "category": "structure", "meaning": "constructor", "description": "Structure operation: constructor", "unicode_point": "U+2217", "unicode_name": "ASTERISK OPERATOR", "token_cost": 1, "token_density": 1.0, "quality_score": 99.99999999999999, "llm_support": ["openai", "qwen", "llama"], "created": "2025-05-23", "generator": "fresh_512_v1"}, {"id": "NG0134", "symbol": "↯", "code": "ng:structure:destructor", "fallback": "[DESTRU]", "category": "structure", "meaning": "destructor", "description": "Structure operation: destructor", "unicode_point": "U+21AF", "unicode_name": "DOWNWARDS ZIGZAG ARROW", "token_cost": 1, "token_density": 1.0, "quality_score": 99.99999999999999, "llm_support": ["openai", "qwen", "llama"], "created": "2025-05-23", "generator": "fresh_512_v1"}, {"id": "NG0135", "symbol": "⦢", "code": "ng:structure:getter", "fallback": "[GETTER]", "category": "structure", "meaning": "getter", "description": "Structure operation: getter", "unicode_point": "U+29A2", "unicode_name": "TURNED ANGLE", "token_cost": 1, "token_density": 1.0, "quality_score": 99.99999999999999, "llm_support": ["openai", "qwen", "llama"], "created": "2025-05-23", "generator": "fresh_512_v1"}, {"id": "NG0136", "symbol": "⨗", "code": "ng:structure:setter", "fallback": "[SETTER]", "category": "structure", "meaning": "setter", "description": "Structure operation: setter", "unicode_point": "U+2A17", "unicode_name": "INTEGRAL WITH LEFTWARDS ARROW WITH HOOK", "token_cost": 1, "token_density": 1.0, "quality_score": 99.99999999999999, "llm_support": ["openai", "qwen", "llama"], "created": "2025-05-23", "generator": "fresh_512_v1"}, {"id": "NG0137", "symbol": "⌏", "code": "ng:structure:accessor", "fallback": "[ACCESS]", "category": "structure", "meaning": "accessor", "description": "Structure operation: accessor", "unicode_point": "U+230F", "unicode_name": "TOP LEFT CROP", "token_cost": 1, "token_density": 1.0, "quality_score": 99.99999999999999, "llm_support": ["openai", "qwen", "llama"], "created": "2025-05-23", "generator": "fresh_512_v1"}, {"id": "NG0138", "symbol": "⇭", "code": "ng:structure:mutator", "fallback": "[MUTATO]", "category": "structure", "meaning": "mutator", "description": "Structure operation: mutator", "unicode_point": "U+21ED", "unicode_name": "UPWARDS WHITE ARROW ON PEDESTAL WITH VERTICAL BAR", "token_cost": 1, "token_density": 1.0, "quality_score": 99.99999999999999, "llm_support": ["openai", "qwen", "llama"], "created": "2025-05-23", "generator": "fresh_512_v1"}, {"id": "NG0139", "symbol": "◚", "code": "ng:structure:factory", "fallback": "[FACTOR]", "category": "structure", "meaning": "factory", "description": "Structure operation: factory", "unicode_point": "U+25DA", "unicode_name": "UPPER HALF INVERSE WHITE CIRCLE", "token_cost": 1, "token_density": 1.0, "quality_score": 99.99999999999999, "llm_support": ["openai", "qwen", "llama"], "created": "2025-05-23", "generator": "fresh_512_v1"}, {"id": "NG0140", "symbol": "⇵", "code": "ng:structure:builder", "fallback": "[BUILDE]", "category": "structure", "meaning": "builder", "description": "Structure operation: builder", "unicode_point": "U+21F5", "unicode_name": "DOWNWARDS ARROW LEFTWARDS OF UPWARDS ARROW", "token_cost": 1, "token_density": 1.0, "quality_score": 99.99999999999999, "llm_support": ["openai", "qwen", "llama"], "created": "2025-05-23", "generator": "fresh_512_v1"}, {"id": "NG0141", "symbol": "◰", "code": "ng:structure:class_1", "fallback": "[CLASS1]", "category": "structure", "meaning": "class", "description": "Structure operation: class", "unicode_point": "U+25F0", "unicode_name": "WHITE SQUARE WITH UPPER LEFT QUADRANT", "token_cost": 1, "token_density": 1.0, "quality_score": 99.99999999999999, "llm_support": ["openai", "qwen", "llama"], "created": "2025-05-23", "generator": "fresh_512_v1"}, {"id": "NG0142", "symbol": "→", "code": "ng:structure:interface_1", "fallback": "[INTERF1]", "category": "structure", "meaning": "interface", "description": "Structure operation: interface", "unicode_point": "U+2192", "unicode_name": "RIGHTWARDS ARROW", "token_cost": 1, "token_density": 1.0, "quality_score": 99.99999999999999, "llm_support": ["openai", "qwen", "llama"], "created": "2025-05-23", "generator": "fresh_512_v1"}, {"id": "NG0143", "symbol": "⩹", "code": "ng:structure:abstract_1", "fallback": "[ABSTRA1]", "category": "structure", "meaning": "abstract", "description": "Structure operation: abstract", "unicode_point": "U+2A79", "unicode_name": "LESS-THAN WITH CIRCLE INSIDE", "token_cost": 1, "token_density": 1.0, "quality_score": 99.99999999999999, "llm_support": ["openai", "qwen", "llama"], "created": "2025-05-23", "generator": "fresh_512_v1"}, {"id": "NG0144", "symbol": "⍂", "code": "ng:structure:concrete_1", "fallback": "[CONCRE1]", "category": "structure", "meaning": "concrete", "description": "Structure operation: concrete", "unicode_point": "U+2342", "unicode_name": "APL FUNCTIONAL SYMBOL QUAD BACKSLASH", "token_cost": 1, "token_density": 1.0, "quality_score": 99.99999999999999, "llm_support": ["openai", "qwen", "llama"], "created": "2025-05-23", "generator": "fresh_512_v1"}, {"id": "NG0145", "symbol": "☍", "code": "ng:structure:inherit_1", "fallback": "[INHERI1]", "category": "structure", "meaning": "inherit", "description": "Structure operation: inherit", "unicode_point": "U+260D", "unicode_name": "OPPOSITION", "token_cost": 1, "token_density": 1.0, "quality_score": 99.99999999999999, "llm_support": ["openai", "qwen", "llama"], "created": "2025-05-23", "generator": "fresh_512_v1"}, {"id": "NG0146", "symbol": "⪌", "code": "ng:structure:implement_1", "fallback": "[IMPLEM1]", "category": "structure", "meaning": "implement", "description": "Structure operation: implement", "unicode_point": "U+2A8C", "unicode_name": "GREATER-<PERSON><PERSON><PERSON> ABOVE DOUBLE-LINE EQUAL ABOVE LESS-THAN", "token_cost": 1, "token_density": 1.0, "quality_score": 99.99999999999999, "llm_support": ["openai", "qwen", "llama"], "created": "2025-05-23", "generator": "fresh_512_v1"}, {"id": "NG0147", "symbol": "⧤", "code": "ng:structure:function_1", "fallback": "[FUNCTI1]", "category": "structure", "meaning": "function", "description": "Structure operation: function", "unicode_point": "U+29E4", "unicode_name": "EQUALS SIGN AND SL<PERSON><PERSON>D PARALLEL WITH TILDE ABOVE", "token_cost": 1, "token_density": 1.0, "quality_score": 99.99999999999999, "llm_support": ["openai", "qwen", "llama"], "created": "2025-05-23", "generator": "fresh_512_v1"}, {"id": "NG0148", "symbol": "∂", "code": "ng:structure:method_1", "fallback": "[METHOD1]", "category": "structure", "meaning": "method", "description": "Structure operation: method", "unicode_point": "U+2202", "unicode_name": "PARTIAL DIFFERENTIAL", "token_cost": 1, "token_density": 1.0, "quality_score": 99.99999999999999, "llm_support": ["openai", "qwen", "llama"], "created": "2025-05-23", "generator": "fresh_512_v1"}, {"id": "NG0149", "symbol": "◔", "code": "ng:structure:procedure_1", "fallback": "[PROCED1]", "category": "structure", "meaning": "procedure", "description": "Structure operation: procedure", "unicode_point": "U+25D4", "unicode_name": "CIRCLE WITH UPPER RIGHT QUADRANT BLACK", "token_cost": 1, "token_density": 1.0, "quality_score": 99.99999999999999, "llm_support": ["openai", "qwen", "llama"], "created": "2025-05-23", "generator": "fresh_512_v1"}, {"id": "NG0150", "symbol": "⟯", "code": "ng:structure:lambda_1", "fallback": "[LAMBDA3]", "category": "structure", "meaning": "lambda", "description": "Structure operation: lambda", "unicode_point": "U+27EF", "unicode_name": "MATHEMATICAL RIGHT FLATTENED PARENTHESIS", "token_cost": 1, "token_density": 1.0, "quality_score": 99.99999999999999, "llm_support": ["openai", "qwen", "llama"], "created": "2025-05-23", "generator": "fresh_512_v1"}, {"id": "NG0151", "symbol": "⋊", "code": "ng:structure:closure_1", "fallback": "[CLOSUR1]", "category": "structure", "meaning": "closure", "description": "Structure operation: closure", "unicode_point": "U+22CA", "unicode_name": "RIGHT NORMAL FACTOR SEMIDIRECT PRODUCT", "token_cost": 1, "token_density": 1.0, "quality_score": 99.99999999999999, "llm_support": ["openai", "qwen", "llama"], "created": "2025-05-23", "generator": "fresh_512_v1"}, {"id": "NG0152", "symbol": "◆", "code": "ng:structure:partial_1", "fallback": "[PARTIA1]", "category": "structure", "meaning": "partial", "description": "Structure operation: partial", "unicode_point": "U+25C6", "unicode_name": "BLACK DIAMOND", "token_cost": 1, "token_density": 1.0, "quality_score": 99.99999999999999, "llm_support": ["openai", "qwen", "llama"], "created": "2025-05-23", "generator": "fresh_512_v1"}, {"id": "NG0153", "symbol": "⦍", "code": "ng:structure:module_1", "fallback": "[MODULE1]", "category": "structure", "meaning": "module", "description": "Structure operation: module", "unicode_point": "U+298D", "unicode_name": "LEFT SQUARE BRACKET WITH TICK IN TOP CORNER", "token_cost": 1, "token_density": 1.0, "quality_score": 99.99999999999999, "llm_support": ["openai", "qwen", "llama"], "created": "2025-05-23", "generator": "fresh_512_v1"}, {"id": "NG0154", "symbol": "▸", "code": "ng:structure:package_1", "fallback": "[PACKAG1]", "category": "structure", "meaning": "package", "description": "Structure operation: package", "unicode_point": "U+25B8", "unicode_name": "BLACK RIGHT-POINTING SMALL TRIANGLE", "token_cost": 1, "token_density": 1.0, "quality_score": 99.99999999999999, "llm_support": ["openai", "qwen", "llama"], "created": "2025-05-23", "generator": "fresh_512_v1"}, {"id": "NG0155", "symbol": "☸", "code": "ng:structure:namespace_1", "fallback": "[NAMESP1]", "category": "structure", "meaning": "namespace", "description": "Structure operation: namespace", "unicode_point": "U+2638", "unicode_name": "WHEEL OF DHARMA", "token_cost": 1, "token_density": 1.0, "quality_score": 99.99999999999999, "llm_support": ["openai", "qwen", "llama"], "created": "2025-05-23", "generator": "fresh_512_v1"}, {"id": "NG0156", "symbol": "⧕", "code": "ng:structure:import_1", "fallback": "[IMPORT1]", "category": "structure", "meaning": "import", "description": "Structure operation: import", "unicode_point": "U+29D5", "unicode_name": "TIMES WITH RIGHT HALF BLACK", "token_cost": 1, "token_density": 1.0, "quality_score": 99.99999999999999, "llm_support": ["openai", "qwen", "llama"], "created": "2025-05-23", "generator": "fresh_512_v1"}, {"id": "NG0157", "symbol": "⟍", "code": "ng:structure:export_1", "fallback": "[EXPORT1]", "category": "structure", "meaning": "export", "description": "Structure operation: export", "unicode_point": "U+27CD", "unicode_name": "MATHEMATICAL FALLING DIAGONAL", "token_cost": 1, "token_density": 1.0, "quality_score": 99.99999999999999, "llm_support": ["openai", "qwen", "llama"], "created": "2025-05-23", "generator": "fresh_512_v1"}, {"id": "NG0158", "symbol": "⋧", "code": "ng:structure:include_1", "fallback": "[INCLUD1]", "category": "structure", "meaning": "include", "description": "Structure operation: include", "unicode_point": "U+22E7", "unicode_name": "GREATER-THAN BUT NOT EQUIVALENT TO", "token_cost": 1, "token_density": 1.0, "quality_score": 99.99999999999999, "llm_support": ["openai", "qwen", "llama"], "created": "2025-05-23", "generator": "fresh_512_v1"}, {"id": "NG0159", "symbol": "⚫", "code": "ng:structure:global_1", "fallback": "[GLOBAL1]", "category": "structure", "meaning": "global", "description": "Structure operation: global", "unicode_point": "U+26AB", "unicode_name": "MEDIUM BLACK CIRCLE", "token_cost": 1, "token_density": 1.0, "quality_score": 99.99999999999999, "llm_support": ["openai", "qwen", "llama"], "created": "2025-05-23", "generator": "fresh_512_v1"}, {"id": "NG0160", "symbol": "♜", "code": "ng:structure:local_1", "fallback": "[LOCAL1]", "category": "structure", "meaning": "local", "description": "Structure operation: local", "unicode_point": "U+265C", "unicode_name": "BLACK CHESS ROOK", "token_cost": 1, "token_density": 1.0, "quality_score": 99.99999999999999, "llm_support": ["openai", "qwen", "llama"], "created": "2025-05-23", "generator": "fresh_512_v1"}, {"id": "NG0161", "symbol": "⧝", "code": "ng:structure:private_1", "fallback": "[PRIVAT1]", "category": "structure", "meaning": "private", "description": "Structure operation: private", "unicode_point": "U+29DD", "unicode_name": "TIE OVER INFINITY", "token_cost": 1, "token_density": 1.0, "quality_score": 99.99999999999999, "llm_support": ["openai", "qwen", "llama"], "created": "2025-05-23", "generator": "fresh_512_v1"}, {"id": "NG0162", "symbol": "⏹", "code": "ng:structure:public_1", "fallback": "[PUBLIC1]", "category": "structure", "meaning": "public", "description": "Structure operation: public", "unicode_point": "U+23F9", "unicode_name": "BLACK SQUARE FOR STOP", "token_cost": 1, "token_density": 1.0, "quality_score": 99.99999999999999, "llm_support": ["openai", "qwen", "llama"], "created": "2025-05-23", "generator": "fresh_512_v1"}, {"id": "NG0163", "symbol": "◒", "code": "ng:structure:protected_1", "fallback": "[PROTEC1]", "category": "structure", "meaning": "protected", "description": "Structure operation: protected", "unicode_point": "U+25D2", "unicode_name": "CIRCLE WITH LOWER HALF BLACK", "token_cost": 1, "token_density": 1.0, "quality_score": 99.99999999999999, "llm_support": ["openai", "qwen", "llama"], "created": "2025-05-23", "generator": "fresh_512_v1"}, {"id": "NG0164", "symbol": "⊃", "code": "ng:structure:static_1", "fallback": "[STATIC1]", "category": "structure", "meaning": "static", "description": "Structure operation: static", "unicode_point": "U+2283", "unicode_name": "SUPERSET OF", "token_cost": 1, "token_density": 1.0, "quality_score": 99.99999999999999, "llm_support": ["openai", "qwen", "llama"], "created": "2025-05-23", "generator": "fresh_512_v1"}, {"id": "NG0165", "symbol": "⎺", "code": "ng:structure:array_1", "fallback": "[ARRAY1]", "category": "structure", "meaning": "array", "description": "Structure operation: array", "unicode_point": "U+23BA", "unicode_name": "HORIZONTAL SCAN LINE-1", "token_cost": 1, "token_density": 1.0, "quality_score": 99.99999999999999, "llm_support": ["openai", "qwen", "llama"], "created": "2025-05-23", "generator": "fresh_512_v1"}, {"id": "NG0166", "symbol": "⎀", "code": "ng:structure:list_1", "fallback": "[LIST1]", "category": "structure", "meaning": "list", "description": "Structure operation: list", "unicode_point": "U+2380", "unicode_name": "INSERTION SYMBOL", "token_cost": 1, "token_density": 1.0, "quality_score": 99.99999999999999, "llm_support": ["openai", "qwen", "llama"], "created": "2025-05-23", "generator": "fresh_512_v1"}, {"id": "NG0167", "symbol": "◟", "code": "ng:structure:dict_1", "fallback": "[DICT1]", "category": "structure", "meaning": "dict", "description": "Structure operation: dict", "unicode_point": "U+25DF", "unicode_name": "LOWER LEFT QUADRANT CIRCULAR ARC", "token_cost": 1, "token_density": 1.0, "quality_score": 99.99999999999999, "llm_support": ["openai", "qwen", "llama"], "created": "2025-05-23", "generator": "fresh_512_v1"}, {"id": "NG0168", "symbol": "◂", "code": "ng:structure:set_1", "fallback": "[SET1]", "category": "structure", "meaning": "set", "description": "Structure operation: set", "unicode_point": "U+25C2", "unicode_name": "BLACK LEFT-POINTING SMALL TRIANGLE", "token_cost": 1, "token_density": 1.0, "quality_score": 99.99999999999999, "llm_support": ["openai", "qwen", "llama"], "created": "2025-05-23", "generator": "fresh_512_v1"}, {"id": "NG0169", "symbol": "⧯", "code": "ng:structure:tuple_1", "fallback": "[TUPLE1]", "category": "structure", "meaning": "tuple", "description": "Structure operation: tuple", "unicode_point": "U+29EF", "unicode_name": "ERROR-BARRED BLACK SQUARE", "token_cost": 1, "token_density": 1.0, "quality_score": 99.99999999999999, "llm_support": ["openai", "qwen", "llama"], "created": "2025-05-23", "generator": "fresh_512_v1"}, {"id": "NG0170", "symbol": "⇥", "code": "ng:structure:struct_1", "fallback": "[STRUCT1]", "category": "structure", "meaning": "struct", "description": "Structure operation: struct", "unicode_point": "U+21E5", "unicode_name": "RIGHTWARDS ARROW TO BAR", "token_cost": 1, "token_density": 1.0, "quality_score": 99.99999999999999, "llm_support": ["openai", "qwen", "llama"], "created": "2025-05-23", "generator": "fresh_512_v1"}, {"id": "NG0171", "symbol": "▿", "code": "ng:structure:object_1", "fallback": "[OBJECT1]", "category": "structure", "meaning": "object", "description": "Structure operation: object", "unicode_point": "U+25BF", "unicode_name": "WHITE DOWN-POINTING SMALL TRIANGLE", "token_cost": 1, "token_density": 1.0, "quality_score": 99.99999999999999, "llm_support": ["openai", "qwen", "llama"], "created": "2025-05-23", "generator": "fresh_512_v1"}, {"id": "NG0172", "symbol": "⪮", "code": "ng:structure:instance_1", "fallback": "[INSTAN1]", "category": "structure", "meaning": "instance", "description": "Structure operation: instance", "unicode_point": "U+2AAE", "unicode_name": "EQUALS SIGN WITH BUMPY ABOVE", "token_cost": 1, "token_density": 1.0, "quality_score": 99.99999999999999, "llm_support": ["openai", "qwen", "llama"], "created": "2025-05-23", "generator": "fresh_512_v1"}, {"id": "NG0173", "symbol": "◹", "code": "ng:structure:property_1", "fallback": "[PROPER1]", "category": "structure", "meaning": "property", "description": "Structure operation: property", "unicode_point": "U+25F9", "unicode_name": "UPPER RIGHT TRIANGLE", "token_cost": 1, "token_density": 1.0, "quality_score": 99.99999999999999, "llm_support": ["openai", "qwen", "llama"], "created": "2025-05-23", "generator": "fresh_512_v1"}, {"id": "NG0174", "symbol": "⋚", "code": "ng:structure:field_1", "fallback": "[FIELD1]", "category": "structure", "meaning": "field", "description": "Structure operation: field", "unicode_point": "U+22DA", "unicode_name": "LESS-<PERSON>HA<PERSON> EQUAL TO OR GREATER-THAN", "token_cost": 1, "token_density": 1.0, "quality_score": 99.99999999999999, "llm_support": ["openai", "qwen", "llama"], "created": "2025-05-23", "generator": "fresh_512_v1"}, {"id": "NG0175", "symbol": "☡", "code": "ng:structure:attribute_1", "fallback": "[ATTRIB1]", "category": "structure", "meaning": "attribute", "description": "Structure operation: attribute", "unicode_point": "U+2621", "unicode_name": "CAUTION SIGN", "token_cost": 1, "token_density": 1.0, "quality_score": 99.99999999999999, "llm_support": ["openai", "qwen", "llama"], "created": "2025-05-23", "generator": "fresh_512_v1"}, {"id": "NG0176", "symbol": "⫆", "code": "ng:structure:member_1", "fallback": "[MEMBER1]", "category": "structure", "meaning": "member", "description": "Structure operation: member", "unicode_point": "U+2AC6", "unicode_name": "SUPERSET OF ABOVE EQUALS SIGN", "token_cost": 1, "token_density": 1.0, "quality_score": 99.99999999999999, "llm_support": ["openai", "qwen", "llama"], "created": "2025-05-23", "generator": "fresh_512_v1"}, {"id": "NG0177", "symbol": "⍻", "code": "ng:flow:if", "fallback": "[IF]", "category": "flow", "meaning": "if", "description": "Flow operation: if", "unicode_point": "U+237B", "unicode_name": "NOT CHECK MARK", "token_cost": 1, "token_density": 1.0, "quality_score": 99.99999999999999, "llm_support": ["openai", "qwen", "llama"], "created": "2025-05-23", "generator": "fresh_512_v1"}, {"id": "NG0178", "symbol": "⌰", "code": "ng:flow:else", "fallback": "[ELSE]", "category": "flow", "meaning": "else", "description": "Flow operation: else", "unicode_point": "U+2330", "unicode_name": "TOTAL RUNOUT", "token_cost": 1, "token_density": 1.0, "quality_score": 99.99999999999999, "llm_support": ["openai", "qwen", "llama"], "created": "2025-05-23", "generator": "fresh_512_v1"}, {"id": "NG0179", "symbol": "⟐", "code": "ng:flow:elif", "fallback": "[ELIF]", "category": "flow", "meaning": "elif", "description": "Flow operation: elif", "unicode_point": "U+27D0", "unicode_name": "WHITE DIAMOND WITH CENTRED DOT", "token_cost": 1, "token_density": 1.0, "quality_score": 99.99999999999999, "llm_support": ["openai", "qwen", "llama"], "created": "2025-05-23", "generator": "fresh_512_v1"}, {"id": "NG0180", "symbol": "⨀", "code": "ng:flow:switch", "fallback": "[SWITCH]", "category": "flow", "meaning": "switch", "description": "Flow operation: switch", "unicode_point": "U+2A00", "unicode_name": "N-ARY CIRCLED DOT OPERATOR", "token_cost": 1, "token_density": 1.0, "quality_score": 99.99999999999999, "llm_support": ["openai", "qwen", "llama"], "created": "2025-05-23", "generator": "fresh_512_v1"}, {"id": "NG0181", "symbol": "⚏", "code": "ng:flow:case", "fallback": "[CASE]", "category": "flow", "meaning": "case", "description": "Flow operation: case", "unicode_point": "U+268F", "unicode_name": "DIGRAM FOR GREATER YIN", "token_cost": 1, "token_density": 1.0, "quality_score": 99.99999999999999, "llm_support": ["openai", "qwen", "llama"], "created": "2025-05-23", "generator": "fresh_512_v1"}, {"id": "NG0182", "symbol": "⧿", "code": "ng:flow:default", "fallback": "[DEFAUL]", "category": "flow", "meaning": "default", "description": "Flow operation: default", "unicode_point": "U+29FF", "unicode_name": "MINY", "token_cost": 1, "token_density": 1.0, "quality_score": 99.99999999999999, "llm_support": ["openai", "qwen", "llama"], "created": "2025-05-23", "generator": "fresh_512_v1"}, {"id": "NG0183", "symbol": "◊", "code": "ng:flow:when", "fallback": "[WHEN]", "category": "flow", "meaning": "when", "description": "Flow operation: when", "unicode_point": "U+25CA", "unicode_name": "LOZENGE", "token_cost": 1, "token_density": 1.0, "quality_score": 99.99999999999999, "llm_support": ["openai", "qwen", "llama"], "created": "2025-05-23", "generator": "fresh_512_v1"}, {"id": "NG0184", "symbol": "⦁", "code": "ng:flow:for", "fallback": "[FOR]", "category": "flow", "meaning": "for", "description": "Flow operation: for", "unicode_point": "U+2981", "unicode_name": "Z NOTATION SPOT", "token_cost": 1, "token_density": 1.0, "quality_score": 99.99999999999999, "llm_support": ["openai", "qwen", "llama"], "created": "2025-05-23", "generator": "fresh_512_v1"}, {"id": "NG0185", "symbol": "⫓", "code": "ng:flow:while", "fallback": "[WHILE]", "category": "flow", "meaning": "while", "description": "Flow operation: while", "unicode_point": "U+2AD3", "unicode_name": "SUBSET ABOVE SUPERSET", "token_cost": 1, "token_density": 1.0, "quality_score": 99.99999999999999, "llm_support": ["openai", "qwen", "llama"], "created": "2025-05-23", "generator": "fresh_512_v1"}, {"id": "NG0186", "symbol": "☮", "code": "ng:flow:do", "fallback": "[DO]", "category": "flow", "meaning": "do", "description": "Flow operation: do", "unicode_point": "U+262E", "unicode_name": "PEACE SYMBOL", "token_cost": 1, "token_density": 1.0, "quality_score": 99.99999999999999, "llm_support": ["openai", "qwen", "llama"], "created": "2025-05-23", "generator": "fresh_512_v1"}, {"id": "NG0187", "symbol": "⛂", "code": "ng:flow:repeat", "fallback": "[REPEAT]", "category": "flow", "meaning": "repeat", "description": "Flow operation: repeat", "unicode_point": "U+26C2", "unicode_name": "BLACK DRAUGHTS MAN", "token_cost": 1, "token_density": 1.0, "quality_score": 99.99999999999999, "llm_support": ["openai", "qwen", "llama"], "created": "2025-05-23", "generator": "fresh_512_v1"}, {"id": "NG0188", "symbol": "⌼", "code": "ng:flow:until", "fallback": "[UNTIL2]", "category": "flow", "meaning": "until", "description": "Flow operation: until", "unicode_point": "U+233C", "unicode_name": "APL FUNCTIONAL SYMBOL QUAD CIRCLE", "token_cost": 1, "token_density": 1.0, "quality_score": 99.99999999999999, "llm_support": ["openai", "qwen", "llama"], "created": "2025-05-23", "generator": "fresh_512_v1"}, {"id": "NG0189", "symbol": "⍵", "code": "ng:flow:foreach", "fallback": "[FOREAC]", "category": "flow", "meaning": "foreach", "description": "Flow operation: foreach", "unicode_point": "U+2375", "unicode_name": "APL FUNCTIONAL SYMBOL OMEGA", "token_cost": 1, "token_density": 1.0, "quality_score": 99.99999999999999, "llm_support": ["openai", "qwen", "llama"], "created": "2025-05-23", "generator": "fresh_512_v1"}, {"id": "NG0190", "symbol": "⦊", "code": "ng:flow:map", "fallback": "[MAP]", "category": "flow", "meaning": "map", "description": "Flow operation: map", "unicode_point": "U+298A", "unicode_name": "Z NOTATION RIGHT BINDING BRACKET", "token_cost": 1, "token_density": 1.0, "quality_score": 99.99999999999999, "llm_support": ["openai", "qwen", "llama"], "created": "2025-05-23", "generator": "fresh_512_v1"}, {"id": "NG0191", "symbol": "≤", "code": "ng:flow:filter", "fallback": "[FILTER]", "category": "flow", "meaning": "filter", "description": "Flow operation: filter", "unicode_point": "U+2264", "unicode_name": "LESS-THAN OR EQUAL TO", "token_cost": 1, "token_density": 1.0, "quality_score": 99.99999999999999, "llm_support": ["openai", "qwen", "llama"], "created": "2025-05-23", "generator": "fresh_512_v1"}, {"id": "NG0192", "symbol": "↓", "code": "ng:flow:try", "fallback": "[TRY]", "category": "flow", "meaning": "try", "description": "Flow operation: try", "unicode_point": "U+2193", "unicode_name": "DOWNWARDS ARROW", "token_cost": 1, "token_density": 1.0, "quality_score": 99.99999999999999, "llm_support": ["openai", "qwen", "llama"], "created": "2025-05-23", "generator": "fresh_512_v1"}, {"id": "NG0193", "symbol": "⋂", "code": "ng:flow:catch", "fallback": "[CATCH]", "category": "flow", "meaning": "catch", "description": "Flow operation: catch", "unicode_point": "U+22C2", "unicode_name": "N-ARY INTERSECTION", "token_cost": 1, "token_density": 1.0, "quality_score": 99.99999999999999, "llm_support": ["openai", "qwen", "llama"], "created": "2025-05-23", "generator": "fresh_512_v1"}, {"id": "NG0194", "symbol": "⛴", "code": "ng:flow:throw", "fallback": "[THROW]", "category": "flow", "meaning": "throw", "description": "Flow operation: throw", "unicode_point": "U+26F4", "unicode_name": "FERRY", "token_cost": 1, "token_density": 1.0, "quality_score": 99.99999999999999, "llm_support": ["openai", "qwen", "llama"], "created": "2025-05-23", "generator": "fresh_512_v1"}, {"id": "NG0195", "symbol": "⧷", "code": "ng:flow:finally", "fallback": "[FINALL]", "category": "flow", "meaning": "finally", "description": "Flow operation: finally", "unicode_point": "U+29F7", "unicode_name": "REVERSE SOLIDUS WITH HORIZONTAL STROKE", "token_cost": 1, "token_density": 1.0, "quality_score": 99.99999999999999, "llm_support": ["openai", "qwen", "llama"], "created": "2025-05-23", "generator": "fresh_512_v1"}, {"id": "NG0196", "symbol": "⫑", "code": "ng:flow:raise", "fallback": "[RAISE]", "category": "flow", "meaning": "raise", "description": "Flow operation: raise", "unicode_point": "U+2AD1", "unicode_name": "CLOSED SUBSET OR EQUAL TO", "token_cost": 1, "token_density": 1.0, "quality_score": 99.99999999999999, "llm_support": ["openai", "qwen", "llama"], "created": "2025-05-23", "generator": "fresh_512_v1"}, {"id": "NG0197", "symbol": "⎆", "code": "ng:flow:handle", "fallback": "[HANDLE]", "category": "flow", "meaning": "handle", "description": "Flow operation: handle", "unicode_point": "U+2386", "unicode_name": "ENTER <PERSON>", "token_cost": 1, "token_density": 1.0, "quality_score": 99.99999999999999, "llm_support": ["openai", "qwen", "llama"], "created": "2025-05-23", "generator": "fresh_512_v1"}, {"id": "NG0198", "symbol": "⧖", "code": "ng:flow:rescue", "fallback": "[RESCUE]", "category": "flow", "meaning": "rescue", "description": "Flow operation: rescue", "unicode_point": "U+29D6", "unicode_name": "WHITE HOURGLASS", "token_cost": 1, "token_density": 1.0, "quality_score": 99.99999999999999, "llm_support": ["openai", "qwen", "llama"], "created": "2025-05-23", "generator": "fresh_512_v1"}, {"id": "NG0199", "symbol": "⌜", "code": "ng:flow:async", "fallback": "[ASYNC]", "category": "flow", "meaning": "async", "description": "Flow operation: async", "unicode_point": "U+231C", "unicode_name": "TOP LEFT CORNER", "token_cost": 1, "token_density": 1.0, "quality_score": 99.99999999999999, "llm_support": ["openai", "qwen", "llama"], "created": "2025-05-23", "generator": "fresh_512_v1"}, {"id": "NG0200", "symbol": "▱", "code": "ng:flow:await", "fallback": "[AWAIT]", "category": "flow", "meaning": "await", "description": "Flow operation: await", "unicode_point": "U+25B1", "unicode_name": "WHITE PARALLELOGRAM", "token_cost": 1, "token_density": 1.0, "quality_score": 99.99999999999999, "llm_support": ["openai", "qwen", "llama"], "created": "2025-05-23", "generator": "fresh_512_v1"}, {"id": "NG0201", "symbol": "⩌", "code": "ng:flow:promise", "fallback": "[PROMIS]", "category": "flow", "meaning": "promise", "description": "Flow operation: promise", "unicode_point": "U+2A4C", "unicode_name": "CLOSED UNION WITH SERIFS", "token_cost": 1, "token_density": 1.0, "quality_score": 99.99999999999999, "llm_support": ["openai", "qwen", "llama"], "created": "2025-05-23", "generator": "fresh_512_v1"}, {"id": "NG0202", "symbol": "♴", "code": "ng:flow:future", "fallback": "[FUTURE]", "category": "flow", "meaning": "future", "description": "Flow operation: future", "unicode_point": "U+2674", "unicode_name": "RECYCLING <PERSON><PERSON><PERSON><PERSON> FOR TYPE-2 PLASTICS", "token_cost": 1, "token_density": 1.0, "quality_score": 99.99999999999999, "llm_support": ["openai", "qwen", "llama"], "created": "2025-05-23", "generator": "fresh_512_v1"}, {"id": "NG0203", "symbol": "◳", "code": "ng:flow:task", "fallback": "[TASK]", "category": "flow", "meaning": "task", "description": "Flow operation: task", "unicode_point": "U+25F3", "unicode_name": "WHITE SQUARE WITH UPPER RIGHT QUADRANT", "token_cost": 1, "token_density": 1.0, "quality_score": 99.99999999999999, "llm_support": ["openai", "qwen", "llama"], "created": "2025-05-23", "generator": "fresh_512_v1"}, {"id": "NG0204", "symbol": "◖", "code": "ng:flow:thread", "fallback": "[THREAD]", "category": "flow", "meaning": "thread", "description": "Flow operation: thread", "unicode_point": "U+25D6", "unicode_name": "LEFT HALF BLACK CIRCLE", "token_cost": 1, "token_density": 1.0, "quality_score": 99.99999999999999, "llm_support": ["openai", "qwen", "llama"], "created": "2025-05-23", "generator": "fresh_512_v1"}, {"id": "NG0205", "symbol": "⨣", "code": "ng:flow:process", "fallback": "[PROCES]", "category": "flow", "meaning": "process", "description": "Flow operation: process", "unicode_point": "U+2A23", "unicode_name": "PLUS SIGN WITH CIRCUMFLEX ACCENT ABOVE", "token_cost": 1, "token_density": 1.0, "quality_score": 99.99999999999999, "llm_support": ["openai", "qwen", "llama"], "created": "2025-05-23", "generator": "fresh_512_v1"}, {"id": "NG0206", "symbol": "⍫", "code": "ng:flow:yield", "fallback": "[YIELD]", "category": "flow", "meaning": "yield", "description": "Flow operation: yield", "unicode_point": "U+236B", "unicode_name": "APL FUNCTIONAL SYMBOL DEL TILDE", "token_cost": 1, "token_density": 1.0, "quality_score": 99.99999999999999, "llm_support": ["openai", "qwen", "llama"], "created": "2025-05-23", "generator": "fresh_512_v1"}, {"id": "NG0207", "symbol": "≕", "code": "ng:flow:break", "fallback": "[BREAK]", "category": "flow", "meaning": "break", "description": "Flow operation: break", "unicode_point": "U+2255", "unicode_name": "EQUALS COLON", "token_cost": 1, "token_density": 1.0, "quality_score": 99.99999999999999, "llm_support": ["openai", "qwen", "llama"], "created": "2025-05-23", "generator": "fresh_512_v1"}, {"id": "NG0208", "symbol": "⋥", "code": "ng:flow:continue", "fallback": "[CONTIN]", "category": "flow", "meaning": "continue", "description": "Flow operation: continue", "unicode_point": "U+22E5", "unicode_name": "SQUARE ORIGINAL OF OR NOT EQUAL TO", "token_cost": 1, "token_density": 1.0, "quality_score": 99.99999999999999, "llm_support": ["openai", "qwen", "llama"], "created": "2025-05-23", "generator": "fresh_512_v1"}, {"id": "NG0209", "symbol": "⫍", "code": "ng:flow:return", "fallback": "[RETURN]", "category": "flow", "meaning": "return", "description": "Flow operation: return", "unicode_point": "U+2ACD", "unicode_name": "SQUARE LEFT OPEN BOX OPERATOR", "token_cost": 1, "token_density": 1.0, "quality_score": 99.99999999999999, "llm_support": ["openai", "qwen", "llama"], "created": "2025-05-23", "generator": "fresh_512_v1"}, {"id": "NG0210", "symbol": "⎹", "code": "ng:flow:goto", "fallback": "[GOTO]", "category": "flow", "meaning": "goto", "description": "Flow operation: goto", "unicode_point": "U+23B9", "unicode_name": "RIGHT VERTICAL BOX LINE", "token_cost": 1, "token_density": 1.0, "quality_score": 99.99999999999999, "llm_support": ["openai", "qwen", "llama"], "created": "2025-05-23", "generator": "fresh_512_v1"}, {"id": "NG0211", "symbol": "⌋", "code": "ng:flow:call", "fallback": "[CALL]", "category": "flow", "meaning": "call", "description": "Flow operation: call", "unicode_point": "U+230B", "unicode_name": "RIGHT FLOOR", "token_cost": 1, "token_density": 1.0, "quality_score": 99.99999999999999, "llm_support": ["openai", "qwen", "llama"], "created": "2025-05-23", "generator": "fresh_512_v1"}, {"id": "NG0212", "symbol": "∌", "code": "ng:flow:invoke", "fallback": "[INVOKE]", "category": "flow", "meaning": "invoke", "description": "Flow operation: invoke", "unicode_point": "U+220C", "unicode_name": "DOES NOT CONTAIN AS MEMBER", "token_cost": 1, "token_density": 1.0, "quality_score": 99.99999999999999, "llm_support": ["openai", "qwen", "llama"], "created": "2025-05-23", "generator": "fresh_512_v1"}, {"id": "NG0213", "symbol": "⎉", "code": "ng:flow:execute", "fallback": "[EXECUT]", "category": "flow", "meaning": "execute", "description": "Flow operation: execute", "unicode_point": "U+2389", "unicode_name": "CIRCLED <PERSON>OR<PERSON><PERSON>ON<PERSON>L BAR WITH NOTCH", "token_cost": 1, "token_density": 1.0, "quality_score": 99.99999999999999, "llm_support": ["openai", "qwen", "llama"], "created": "2025-05-23", "generator": "fresh_512_v1"}, {"id": "NG0214", "symbol": "⩨", "code": "ng:flow:run", "fallback": "[RUN]", "category": "flow", "meaning": "run", "description": "Flow operation: run", "unicode_point": "U+2A68", "unicode_name": "TRIPLE HORIZONTAL BAR WITH DOUBLE VERTICAL STROKE", "token_cost": 1, "token_density": 1.0, "quality_score": 99.99999999999999, "llm_support": ["openai", "qwen", "llama"], "created": "2025-05-23", "generator": "fresh_512_v1"}, {"id": "NG0215", "symbol": "⟠", "code": "ng:flow:start", "fallback": "[START]", "category": "flow", "meaning": "start", "description": "Flow operation: start", "unicode_point": "U+27E0", "unicode_name": "LOZENGE DIVIDED BY HORIZONTAL RULE", "token_cost": 1, "token_density": 1.0, "quality_score": 99.99999999999999, "llm_support": ["openai", "qwen", "llama"], "created": "2025-05-23", "generator": "fresh_512_v1"}, {"id": "NG0216", "symbol": "⪅", "code": "ng:flow:stop", "fallback": "[STOP]", "category": "flow", "meaning": "stop", "description": "Flow operation: stop", "unicode_point": "U+2A85", "unicode_name": "LESS-THAN OR APPROXIMATE", "token_cost": 1, "token_density": 1.0, "quality_score": 99.99999999999999, "llm_support": ["openai", "qwen", "llama"], "created": "2025-05-23", "generator": "fresh_512_v1"}, {"id": "NG0217", "symbol": "⧰", "code": "ng:flow:pause", "fallback": "[PAUSE]", "category": "flow", "meaning": "pause", "description": "Flow operation: pause", "unicode_point": "U+29F0", "unicode_name": "ERROR-BARRED WHITE DIAMOND", "token_cost": 1, "token_density": 1.0, "quality_score": 99.99999999999999, "llm_support": ["openai", "qwen", "llama"], "created": "2025-05-23", "generator": "fresh_512_v1"}, {"id": "NG0218", "symbol": "∐", "code": "ng:flow:resume", "fallback": "[RESUME]", "category": "flow", "meaning": "resume", "description": "Flow operation: resume", "unicode_point": "U+2210", "unicode_name": "N-ARY COPRODUCT", "token_cost": 1, "token_density": 1.0, "quality_score": 99.99999999999999, "llm_support": ["openai", "qwen", "llama"], "created": "2025-05-23", "generator": "fresh_512_v1"}, {"id": "NG0219", "symbol": "◅", "code": "ng:flow:suspend", "fallback": "[SUSPEN]", "category": "flow", "meaning": "suspend", "description": "Flow operation: suspend", "unicode_point": "U+25C5", "unicode_name": "WHITE LEFT-POINTING POINTER", "token_cost": 1, "token_density": 1.0, "quality_score": 99.99999999999999, "llm_support": ["openai", "qwen", "llama"], "created": "2025-05-23", "generator": "fresh_512_v1"}, {"id": "NG0220", "symbol": "⟧", "code": "ng:flow:terminate", "fallback": "[TERMIN]", "category": "flow", "meaning": "terminate", "description": "Flow operation: terminate", "unicode_point": "U+27E7", "unicode_name": "MATHEMATICAL RIGHT WHITE SQUARE BRACKET", "token_cost": 1, "token_density": 1.0, "quality_score": 99.99999999999999, "llm_support": ["openai", "qwen", "llama"], "created": "2025-05-23", "generator": "fresh_512_v1"}, {"id": "NG0221", "symbol": "⦒", "code": "ng:flow:if_1", "fallback": "[IF1]", "category": "flow", "meaning": "if", "description": "Flow operation: if", "unicode_point": "U+2992", "unicode_name": "RIGHT ANGLE BRACKET WITH DOT", "token_cost": 1, "token_density": 1.0, "quality_score": 99.99999999999999, "llm_support": ["openai", "qwen", "llama"], "created": "2025-05-23", "generator": "fresh_512_v1"}, {"id": "NG0222", "symbol": "⨦", "code": "ng:flow:else_1", "fallback": "[ELSE1]", "category": "flow", "meaning": "else", "description": "Flow operation: else", "unicode_point": "U+2A26", "unicode_name": "PLUS SIGN WITH TILDE BELOW", "token_cost": 1, "token_density": 1.0, "quality_score": 99.99999999999999, "llm_support": ["openai", "qwen", "llama"], "created": "2025-05-23", "generator": "fresh_512_v1"}, {"id": "NG0223", "symbol": "⟚", "code": "ng:flow:elif_1", "fallback": "[ELIF1]", "category": "flow", "meaning": "elif", "description": "Flow operation: elif", "unicode_point": "U+27DA", "unicode_name": "LEFT AND RIGHT DOUBLE TURNSTILE", "token_cost": 1, "token_density": 1.0, "quality_score": 99.99999999999999, "llm_support": ["openai", "qwen", "llama"], "created": "2025-05-23", "generator": "fresh_512_v1"}, {"id": "NG0224", "symbol": "⟝", "code": "ng:flow:switch_1", "fallback": "[SWITCH1]", "category": "flow", "meaning": "switch", "description": "Flow operation: switch", "unicode_point": "U+27DD", "unicode_name": "LONG RIGHT TACK", "token_cost": 1, "token_density": 1.0, "quality_score": 99.99999999999999, "llm_support": ["openai", "qwen", "llama"], "created": "2025-05-23", "generator": "fresh_512_v1"}, {"id": "NG0225", "symbol": "⍍", "code": "ng:flow:case_1", "fallback": "[CASE1]", "category": "flow", "meaning": "case", "description": "Flow operation: case", "unicode_point": "U+234D", "unicode_name": "APL FUNCTIONAL SYMBOL QUAD DELTA", "token_cost": 1, "token_density": 1.0, "quality_score": 99.99999999999999, "llm_support": ["openai", "qwen", "llama"], "created": "2025-05-23", "generator": "fresh_512_v1"}, {"id": "NG0226", "symbol": "☋", "code": "ng:flow:default_1", "fallback": "[DEFAUL1]", "category": "flow", "meaning": "default", "description": "Flow operation: default", "unicode_point": "U+260B", "unicode_name": "DESCENDING NODE", "token_cost": 1, "token_density": 1.0, "quality_score": 99.99999999999999, "llm_support": ["openai", "qwen", "llama"], "created": "2025-05-23", "generator": "fresh_512_v1"}, {"id": "NG0227", "symbol": "⨍", "code": "ng:flow:when_1", "fallback": "[WHEN1]", "category": "flow", "meaning": "when", "description": "Flow operation: when", "unicode_point": "U+2A0D", "unicode_name": "FINITE PART INTEGRAL", "token_cost": 1, "token_density": 1.0, "quality_score": 99.99999999999999, "llm_support": ["openai", "qwen", "llama"], "created": "2025-05-23", "generator": "fresh_512_v1"}, {"id": "NG0228", "symbol": "⫁", "code": "ng:flow:for_1", "fallback": "[FOR1]", "category": "flow", "meaning": "for", "description": "Flow operation: for", "unicode_point": "U+2AC1", "unicode_name": "SUBSET WITH MULTIPLICATION SIGN BELOW", "token_cost": 1, "token_density": 1.0, "quality_score": 99.99999999999999, "llm_support": ["openai", "qwen", "llama"], "created": "2025-05-23", "generator": "fresh_512_v1"}, {"id": "NG0229", "symbol": "⊂", "code": "ng:flow:while_1", "fallback": "[WHILE1]", "category": "flow", "meaning": "while", "description": "Flow operation: while", "unicode_point": "U+2282", "unicode_name": "SUBSET OF", "token_cost": 1, "token_density": 1.0, "quality_score": 99.99999999999999, "llm_support": ["openai", "qwen", "llama"], "created": "2025-05-23", "generator": "fresh_512_v1"}, {"id": "NG0230", "symbol": "⦓", "code": "ng:flow:do_1", "fallback": "[DO1]", "category": "flow", "meaning": "do", "description": "Flow operation: do", "unicode_point": "U+2993", "unicode_name": "LEFT ARC LESS-THAN BRACKET", "token_cost": 1, "token_density": 1.0, "quality_score": 99.99999999999999, "llm_support": ["openai", "qwen", "llama"], "created": "2025-05-23", "generator": "fresh_512_v1"}, {"id": "NG0231", "symbol": "⊡", "code": "ng:flow:repeat_1", "fallback": "[REPEAT1]", "category": "flow", "meaning": "repeat", "description": "Flow operation: repeat", "unicode_point": "U+22A1", "unicode_name": "SQUARED DOT OPERATOR", "token_cost": 1, "token_density": 1.0, "quality_score": 99.99999999999999, "llm_support": ["openai", "qwen", "llama"], "created": "2025-05-23", "generator": "fresh_512_v1"}, {"id": "NG0232", "symbol": "⪋", "code": "ng:flow:until_1", "fallback": "[UNTIL3]", "category": "flow", "meaning": "until", "description": "Flow operation: until", "unicode_point": "U+2A8B", "unicode_name": "LESS-<PERSON><PERSON><PERSON> ABOVE DOUBLE-LINE EQUAL ABOVE GREATER-THAN", "token_cost": 1, "token_density": 1.0, "quality_score": 99.99999999999999, "llm_support": ["openai", "qwen", "llama"], "created": "2025-05-23", "generator": "fresh_512_v1"}, {"id": "NG0233", "symbol": "⦉", "code": "ng:flow:foreach_1", "fallback": "[FOREAC1]", "category": "flow", "meaning": "foreach", "description": "Flow operation: foreach", "unicode_point": "U+2989", "unicode_name": "Z NOTATION LEFT BINDING BRACKET", "token_cost": 1, "token_density": 1.0, "quality_score": 99.99999999999999, "llm_support": ["openai", "qwen", "llama"], "created": "2025-05-23", "generator": "fresh_512_v1"}, {"id": "NG0234", "symbol": "⫲", "code": "ng:flow:map_1", "fallback": "[MAP1]", "category": "flow", "meaning": "map", "description": "Flow operation: map", "unicode_point": "U+2AF2", "unicode_name": "PARALLEL WITH HORIZONTAL STROKE", "token_cost": 1, "token_density": 1.0, "quality_score": 99.99999999999999, "llm_support": ["openai", "qwen", "llama"], "created": "2025-05-23", "generator": "fresh_512_v1"}, {"id": "NG0235", "symbol": "♧", "code": "ng:flow:filter_1", "fallback": "[FILTER1]", "category": "flow", "meaning": "filter", "description": "Flow operation: filter", "unicode_point": "U+2667", "unicode_name": "WHITE CLUB SUIT", "token_cost": 1, "token_density": 1.0, "quality_score": 99.99999999999999, "llm_support": ["openai", "qwen", "llama"], "created": "2025-05-23", "generator": "fresh_512_v1"}, {"id": "NG0236", "symbol": "⦏", "code": "ng:flow:try_1", "fallback": "[TRY1]", "category": "flow", "meaning": "try", "description": "Flow operation: try", "unicode_point": "U+298F", "unicode_name": "LEFT SQUARE BRACKET WITH TICK IN BOTTOM CORNER", "token_cost": 1, "token_density": 1.0, "quality_score": 99.99999999999999, "llm_support": ["openai", "qwen", "llama"], "created": "2025-05-23", "generator": "fresh_512_v1"}, {"id": "NG0237", "symbol": "◓", "code": "ng:flow:catch_1", "fallback": "[CATCH1]", "category": "flow", "meaning": "catch", "description": "Flow operation: catch", "unicode_point": "U+25D3", "unicode_name": "CIRCLE WITH UPPER HALF BLACK", "token_cost": 1, "token_density": 1.0, "quality_score": 99.99999999999999, "llm_support": ["openai", "qwen", "llama"], "created": "2025-05-23", "generator": "fresh_512_v1"}, {"id": "NG0238", "symbol": "⇴", "code": "ng:flow:throw_1", "fallback": "[THROW1]", "category": "flow", "meaning": "throw", "description": "Flow operation: throw", "unicode_point": "U+21F4", "unicode_name": "RIGHT ARROW WITH SMALL CIRCLE", "token_cost": 1, "token_density": 1.0, "quality_score": 99.99999999999999, "llm_support": ["openai", "qwen", "llama"], "created": "2025-05-23", "generator": "fresh_512_v1"}, {"id": "NG0239", "symbol": "◯", "code": "ng:flow:finally_1", "fallback": "[FINALL1]", "category": "flow", "meaning": "finally", "description": "Flow operation: finally", "unicode_point": "U+25EF", "unicode_name": "LARGE CIRCLE", "token_cost": 1, "token_density": 1.0, "quality_score": 99.99999999999999, "llm_support": ["openai", "qwen", "llama"], "created": "2025-05-23", "generator": "fresh_512_v1"}, {"id": "NG0240", "symbol": "⟁", "code": "ng:flow:raise_1", "fallback": "[RAISE1]", "category": "flow", "meaning": "raise", "description": "Flow operation: raise", "unicode_point": "U+27C1", "unicode_name": "WHITE TRIANGLE CONTAINING SMALL WHITE TRIANGLE", "token_cost": 1, "token_density": 1.0, "quality_score": 99.99999999999999, "llm_support": ["openai", "qwen", "llama"], "created": "2025-05-23", "generator": "fresh_512_v1"}, {"id": "NG0241", "symbol": "⏓", "code": "ng:flow:handle_1", "fallback": "[HANDLE1]", "category": "flow", "meaning": "handle", "description": "Flow operation: handle", "unicode_point": "U+23D3", "unicode_name": "METRICAL SHORT OVER LONG", "token_cost": 1, "token_density": 1.0, "quality_score": 99.99999999999999, "llm_support": ["openai", "qwen", "llama"], "created": "2025-05-23", "generator": "fresh_512_v1"}, {"id": "NG0242", "symbol": "↰", "code": "ng:flow:rescue_1", "fallback": "[RESCUE1]", "category": "flow", "meaning": "rescue", "description": "Flow operation: rescue", "unicode_point": "U+21B0", "unicode_name": "UPWARDS ARROW WITH TIP LEFTWARDS", "token_cost": 1, "token_density": 1.0, "quality_score": 99.99999999999999, "llm_support": ["openai", "qwen", "llama"], "created": "2025-05-23", "generator": "fresh_512_v1"}, {"id": "NG0243", "symbol": "⦗", "code": "ng:flow:async_1", "fallback": "[ASYNC1]", "category": "flow", "meaning": "async", "description": "Flow operation: async", "unicode_point": "U+2997", "unicode_name": "LEFT BLACK TORTOISE SHELL BRACKET", "token_cost": 1, "token_density": 1.0, "quality_score": 99.99999999999999, "llm_support": ["openai", "qwen", "llama"], "created": "2025-05-23", "generator": "fresh_512_v1"}, {"id": "NG0244", "symbol": "⪫", "code": "ng:flow:await_1", "fallback": "[AWAIT1]", "category": "flow", "meaning": "await", "description": "Flow operation: await", "unicode_point": "U+2AAB", "unicode_name": "LARGER THAN", "token_cost": 1, "token_density": 1.0, "quality_score": 99.99999999999999, "llm_support": ["openai", "qwen", "llama"], "created": "2025-05-23", "generator": "fresh_512_v1"}, {"id": "NG0245", "symbol": "◐", "code": "ng:flow:promise_1", "fallback": "[PROMIS1]", "category": "flow", "meaning": "promise", "description": "Flow operation: promise", "unicode_point": "U+25D0", "unicode_name": "CIRCLE WITH LEFT HALF BLACK", "token_cost": 1, "token_density": 1.0, "quality_score": 99.99999999999999, "llm_support": ["openai", "qwen", "llama"], "created": "2025-05-23", "generator": "fresh_512_v1"}, {"id": "NG0246", "symbol": "⍨", "code": "ng:flow:future_1", "fallback": "[FUTURE1]", "category": "flow", "meaning": "future", "description": "Flow operation: future", "unicode_point": "U+2368", "unicode_name": "APL FUNCTIONAL SYMBOL TILDE DIAERESIS", "token_cost": 1, "token_density": 1.0, "quality_score": 99.99999999999999, "llm_support": ["openai", "qwen", "llama"], "created": "2025-05-23", "generator": "fresh_512_v1"}, {"id": "NG0247", "symbol": "⌴", "code": "ng:flow:task_1", "fallback": "[TASK1]", "category": "flow", "meaning": "task", "description": "Flow operation: task", "unicode_point": "U+2334", "unicode_name": "COUNTERBORE", "token_cost": 1, "token_density": 1.0, "quality_score": 99.99999999999999, "llm_support": ["openai", "qwen", "llama"], "created": "2025-05-23", "generator": "fresh_512_v1"}, {"id": "NG0248", "symbol": "◀", "code": "ng:flow:thread_1", "fallback": "[THREAD1]", "category": "flow", "meaning": "thread", "description": "Flow operation: thread", "unicode_point": "U+25C0", "unicode_name": "BLACK LEFT-POINTING TRIANGLE", "token_cost": 1, "token_density": 1.0, "quality_score": 99.99999999999999, "llm_support": ["openai", "qwen", "llama"], "created": "2025-05-23", "generator": "fresh_512_v1"}, {"id": "NG0249", "symbol": "−", "code": "ng:operator:add", "fallback": "[ADD]", "category": "operator", "meaning": "add", "description": "Operator operation: add", "unicode_point": "U+2212", "unicode_name": "MINUS SIGN", "token_cost": 1, "token_density": 1.0, "quality_score": 99.99999999999999, "llm_support": ["openai", "qwen", "llama"], "created": "2025-05-23", "generator": "fresh_512_v1"}, {"id": "NG0250", "symbol": "⇲", "code": "ng:operator:sub", "fallback": "[SUB]", "category": "operator", "meaning": "sub", "description": "Operator operation: sub", "unicode_point": "U+21F2", "unicode_name": "SOUTH EAST ARROW TO CORNER", "token_cost": 1, "token_density": 1.0, "quality_score": 99.99999999999999, "llm_support": ["openai", "qwen", "llama"], "created": "2025-05-23", "generator": "fresh_512_v1"}, {"id": "NG0251", "symbol": "⚉", "code": "ng:operator:mul", "fallback": "[MUL]", "category": "operator", "meaning": "mul", "description": "Operator operation: mul", "unicode_point": "U+2689", "unicode_name": "BLACK CIRCLE WITH TWO WHITE DOTS", "token_cost": 1, "token_density": 1.0, "quality_score": 99.99999999999999, "llm_support": ["openai", "qwen", "llama"], "created": "2025-05-23", "generator": "fresh_512_v1"}, {"id": "NG0252", "symbol": "⨌", "code": "ng:operator:div", "fallback": "[DIV]", "category": "operator", "meaning": "div", "description": "Operator operation: div", "unicode_point": "U+2A0C", "unicode_name": "QUADRUPLE INTEGRAL OPERATOR", "token_cost": 1, "token_density": 1.0, "quality_score": 99.99999999999999, "llm_support": ["openai", "qwen", "llama"], "created": "2025-05-23", "generator": "fresh_512_v1"}, {"id": "NG0253", "symbol": "⎠", "code": "ng:operator:mod", "fallback": "[MOD]", "category": "operator", "meaning": "mod", "description": "Operator operation: mod", "unicode_point": "U+23A0", "unicode_name": "RIGHT PARENTHESIS LOWER HOOK", "token_cost": 1, "token_density": 1.0, "quality_score": 99.99999999999999, "llm_support": ["openai", "qwen", "llama"], "created": "2025-05-23", "generator": "fresh_512_v1"}, {"id": "NG0254", "symbol": "⦘", "code": "ng:operator:pow", "fallback": "[POW]", "category": "operator", "meaning": "pow", "description": "Operator operation: pow", "unicode_point": "U+2998", "unicode_name": "RIGHT B<PERSON><PERSON><PERSON> TORTOISE SHELL BRACKET", "token_cost": 1, "token_density": 1.0, "quality_score": 99.99999999999999, "llm_support": ["openai", "qwen", "llama"], "created": "2025-05-23", "generator": "fresh_512_v1"}, {"id": "NG0255", "symbol": "⏘", "code": "ng:operator:sqrt", "fallback": "[SQRT]", "category": "operator", "meaning": "sqrt", "description": "Operator operation: sqrt", "unicode_point": "U+23D8", "unicode_name": "METRICAL TETRASEME", "token_cost": 1, "token_density": 1.0, "quality_score": 99.99999999999999, "llm_support": ["openai", "qwen", "llama"], "created": "2025-05-23", "generator": "fresh_512_v1"}, {"id": "NG0256", "symbol": "⍞", "code": "ng:operator:abs", "fallback": "[ABS]", "category": "operator", "meaning": "abs", "description": "Operator operation: abs", "unicode_point": "U+235E", "unicode_name": "APL FUNCTIONAL SYMBOL QUOTE QUAD", "token_cost": 1, "token_density": 1.0, "quality_score": 99.99999999999999, "llm_support": ["openai", "qwen", "llama"], "created": "2025-05-23", "generator": "fresh_512_v1"}, {"id": "NG0257", "symbol": "◮", "code": "ng:operator:neg", "fallback": "[NEG]", "category": "operator", "meaning": "neg", "description": "Operator operation: neg", "unicode_point": "U+25EE", "unicode_name": "UP-POINTING T<PERSON><PERSON><PERSON><PERSON> WITH RIGHT HALF BLACK", "token_cost": 1, "token_density": 1.0, "quality_score": 99.99999999999999, "llm_support": ["openai", "qwen", "llama"], "created": "2025-05-23", "generator": "fresh_512_v1"}, {"id": "NG0258", "symbol": "⏸", "code": "ng:operator:inc", "fallback": "[INC]", "category": "operator", "meaning": "inc", "description": "Operator operation: inc", "unicode_point": "U+23F8", "unicode_name": "DOUBLE VERTICAL BAR", "token_cost": 1, "token_density": 1.0, "quality_score": 99.99999999999999, "llm_support": ["openai", "qwen", "llama"], "created": "2025-05-23", "generator": "fresh_512_v1"}, {"id": "NG0259", "symbol": "⟎", "code": "ng:operator:dec", "fallback": "[DEC]", "category": "operator", "meaning": "dec", "description": "Operator operation: dec", "unicode_point": "U+27CE", "unicode_name": "SQUARED LOGICAL AND", "token_cost": 1, "token_density": 1.0, "quality_score": 99.99999999999999, "llm_support": ["openai", "qwen", "llama"], "created": "2025-05-23", "generator": "fresh_512_v1"}, {"id": "NG0260", "symbol": "▨", "code": "ng:operator:eq", "fallback": "[EQ]", "category": "operator", "meaning": "eq", "description": "Operator operation: eq", "unicode_point": "U+25A8", "unicode_name": "SQUARE WITH UPPER RIGHT TO LOWER LEFT FILL", "token_cost": 1, "token_density": 1.0, "quality_score": 99.99999999999999, "llm_support": ["openai", "qwen", "llama"], "created": "2025-05-23", "generator": "fresh_512_v1"}, {"id": "NG0261", "symbol": "⛾", "code": "ng:operator:ne", "fallback": "[NE]", "category": "operator", "meaning": "ne", "description": "Operator operation: ne", "unicode_point": "U+26FE", "unicode_name": "CUP ON BLACK SQUARE", "token_cost": 1, "token_density": 1.0, "quality_score": 99.99999999999999, "llm_support": ["openai", "qwen", "llama"], "created": "2025-05-23", "generator": "fresh_512_v1"}, {"id": "NG0262", "symbol": "⛊", "code": "ng:operator:lt", "fallback": "[LT]", "category": "operator", "meaning": "lt", "description": "Operator operation: lt", "unicode_point": "U+26CA", "unicode_name": "TURNED BLACK SHOGI PIECE", "token_cost": 1, "token_density": 1.0, "quality_score": 99.99999999999999, "llm_support": ["openai", "qwen", "llama"], "created": "2025-05-23", "generator": "fresh_512_v1"}, {"id": "NG0263", "symbol": "⦾", "code": "ng:operator:le", "fallback": "[LE]", "category": "operator", "meaning": "le", "description": "Operator operation: le", "unicode_point": "U+29BE", "unicode_name": "CIRCLED WHITE BULLET", "token_cost": 1, "token_density": 1.0, "quality_score": 99.99999999999999, "llm_support": ["openai", "qwen", "llama"], "created": "2025-05-23", "generator": "fresh_512_v1"}, {"id": "NG0264", "symbol": "⟊", "code": "ng:operator:gt", "fallback": "[GT]", "category": "operator", "meaning": "gt", "description": "Operator operation: gt", "unicode_point": "U+27CA", "unicode_name": "VERTICAL BAR WITH HORIZONTAL STROKE", "token_cost": 1, "token_density": 1.0, "quality_score": 99.99999999999999, "llm_support": ["openai", "qwen", "llama"], "created": "2025-05-23", "generator": "fresh_512_v1"}, {"id": "NG0265", "symbol": "⟆", "code": "ng:operator:ge", "fallback": "[GE]", "category": "operator", "meaning": "ge", "description": "Operator operation: ge", "unicode_point": "U+27C6", "unicode_name": "RIGHT S-SHAPED BAG DELIMITER", "token_cost": 1, "token_density": 1.0, "quality_score": 99.99999999999999, "llm_support": ["openai", "qwen", "llama"], "created": "2025-05-23", "generator": "fresh_512_v1"}, {"id": "NG0266", "symbol": "∇", "code": "ng:operator:min", "fallback": "[MIN]", "category": "operator", "meaning": "min", "description": "Operator operation: min", "unicode_point": "U+2207", "unicode_name": "NABLA", "token_cost": 1, "token_density": 1.0, "quality_score": 99.99999999999999, "llm_support": ["openai", "qwen", "llama"], "created": "2025-05-23", "generator": "fresh_512_v1"}, {"id": "NG0267", "symbol": "⟫", "code": "ng:operator:max", "fallback": "[MAX]", "category": "operator", "meaning": "max", "description": "Operator operation: max", "unicode_point": "U+27EB", "unicode_name": "MATHEMATICAL RIGHT DOUBLE ANGLE BRACKET", "token_cost": 1, "token_density": 1.0, "quality_score": 99.99999999999999, "llm_support": ["openai", "qwen", "llama"], "created": "2025-05-23", "generator": "fresh_512_v1"}, {"id": "NG0268", "symbol": "∋", "code": "ng:operator:clamp", "fallback": "[CLAMP]", "category": "operator", "meaning": "clamp", "description": "Operator operation: clamp", "unicode_point": "U+220B", "unicode_name": "CONTAINS AS MEMBER", "token_cost": 1, "token_density": 1.0, "quality_score": 99.99999999999999, "llm_support": ["openai", "qwen", "llama"], "created": "2025-05-23", "generator": "fresh_512_v1"}, {"id": "NG0269", "symbol": "⌵", "code": "ng:operator:and", "fallback": "[AND2]", "category": "operator", "meaning": "and", "description": "Operator operation: and", "unicode_point": "U+2335", "unicode_name": "COUNTERSINK", "token_cost": 1, "token_density": 1.0, "quality_score": 99.99999999999999, "llm_support": ["openai", "qwen", "llama"], "created": "2025-05-23", "generator": "fresh_512_v1"}, {"id": "NG0270", "symbol": "⇌", "code": "ng:operator:or", "fallback": "[OR2]", "category": "operator", "meaning": "or", "description": "Operator operation: or", "unicode_point": "U+21CC", "unicode_name": "RIGHTWARDS HARPOON OVER LEFTWARDS HARPOON", "token_cost": 1, "token_density": 1.0, "quality_score": 99.99999999999999, "llm_support": ["openai", "qwen", "llama"], "created": "2025-05-23", "generator": "fresh_512_v1"}, {"id": "NG0271", "symbol": "↑", "code": "ng:operator:xor", "fallback": "[XOR2]", "category": "operator", "meaning": "xor", "description": "Operator operation: xor", "unicode_point": "U+2191", "unicode_name": "UPWARDS ARROW", "token_cost": 1, "token_density": 1.0, "quality_score": 99.99999999999999, "llm_support": ["openai", "qwen", "llama"], "created": "2025-05-23", "generator": "fresh_512_v1"}, {"id": "NG0272", "symbol": "⎲", "code": "ng:operator:not", "fallback": "[NOT2]", "category": "operator", "meaning": "not", "description": "Operator operation: not", "unicode_point": "U+23B2", "unicode_name": "SUMMATION TOP", "token_cost": 1, "token_density": 1.0, "quality_score": 99.99999999999999, "llm_support": ["openai", "qwen", "llama"], "created": "2025-05-23", "generator": "fresh_512_v1"}, {"id": "NG0273", "symbol": "∍", "code": "ng:operator:shl", "fallback": "[SHL]", "category": "operator", "meaning": "shl", "description": "Operator operation: shl", "unicode_point": "U+220D", "unicode_name": "SMALL CONTAINS AS MEMBER", "token_cost": 1, "token_density": 1.0, "quality_score": 99.99999999999999, "llm_support": ["openai", "qwen", "llama"], "created": "2025-05-23", "generator": "fresh_512_v1"}, {"id": "NG0274", "symbol": "⟡", "code": "ng:operator:shr", "fallback": "[SHR]", "category": "operator", "meaning": "shr", "description": "Operator operation: shr", "unicode_point": "U+27E1", "unicode_name": "WHITE CONCAVE-SIDED DIAMOND", "token_cost": 1, "token_density": 1.0, "quality_score": 99.99999999999999, "llm_support": ["openai", "qwen", "llama"], "created": "2025-05-23", "generator": "fresh_512_v1"}, {"id": "NG0275", "symbol": "♉", "code": "ng:operator:assign", "fallback": "[ASSIGN]", "category": "operator", "meaning": "assign", "description": "Operator operation: assign", "unicode_point": "U+2649", "unicode_name": "TAURUS", "token_cost": 1, "token_density": 1.0, "quality_score": 99.99999999999999, "llm_support": ["openai", "qwen", "llama"], "created": "2025-05-23", "generator": "fresh_512_v1"}, {"id": "NG0276", "symbol": "⩫", "code": "ng:operator:add_assign", "fallback": "[ADDASS]", "category": "operator", "meaning": "add_assign", "description": "Operator operation: add_assign", "unicode_point": "U+2A6B", "unicode_name": "TILDE OPERATOR WITH RISING DOTS", "token_cost": 1, "token_density": 1.0, "quality_score": 99.99999999999999, "llm_support": ["openai", "qwen", "llama"], "created": "2025-05-23", "generator": "fresh_512_v1"}, {"id": "NG0277", "symbol": "⋿", "code": "ng:operator:sub_assign", "fallback": "[SUBASS]", "category": "operator", "meaning": "sub_assign", "description": "Operator operation: sub_assign", "unicode_point": "U+22FF", "unicode_name": "Z NOTATION BAG MEMBERSHIP", "token_cost": 1, "token_density": 1.0, "quality_score": 99.99999999999999, "llm_support": ["openai", "qwen", "llama"], "created": "2025-05-23", "generator": "fresh_512_v1"}, {"id": "NG0278", "symbol": "⛯", "code": "ng:operator:mul_assign", "fallback": "[MULASS]", "category": "operator", "meaning": "mul_assign", "description": "Operator operation: mul_assign", "unicode_point": "U+26EF", "unicode_name": "MAP SYMBOL FOR LIGHTHOUSE", "token_cost": 1, "token_density": 1.0, "quality_score": 99.99999999999999, "llm_support": ["openai", "qwen", "llama"], "created": "2025-05-23", "generator": "fresh_512_v1"}, {"id": "NG0279", "symbol": "◾", "code": "ng:operator:div_assign", "fallback": "[DIVASS]", "category": "operator", "meaning": "div_assign", "description": "Operator operation: div_assign", "unicode_point": "U+25FE", "unicode_name": "BLACK MEDIUM SMALL SQUARE", "token_cost": 1, "token_density": 1.0, "quality_score": 99.99999999999999, "llm_support": ["openai", "qwen", "llama"], "created": "2025-05-23", "generator": "fresh_512_v1"}, {"id": "NG0280", "symbol": "⦅", "code": "ng:operator:mod_assign", "fallback": "[MODASS]", "category": "operator", "meaning": "mod_assign", "description": "Operator operation: mod_assign", "unicode_point": "U+2985", "unicode_name": "LEFT WHITE PARENTHESIS", "token_cost": 1, "token_density": 1.0, "quality_score": 99.99999999999999, "llm_support": ["openai", "qwen", "llama"], "created": "2025-05-23", "generator": "fresh_512_v1"}, {"id": "NG0281", "symbol": "☗", "code": "ng:operator:xor_assign", "fallback": "[XORASS]", "category": "operator", "meaning": "xor_assign", "description": "Operator operation: xor_assign", "unicode_point": "U+2617", "unicode_name": "BLACK SHOGI PIECE", "token_cost": 1, "token_density": 1.0, "quality_score": 99.99999999999999, "llm_support": ["openai", "qwen", "llama"], "created": "2025-05-23", "generator": "fresh_512_v1"}, {"id": "NG0282", "symbol": "☛", "code": "ng:operator:or_assign", "fallback": "[ORASSI]", "category": "operator", "meaning": "or_assign", "description": "Operator operation: or_assign", "unicode_point": "U+261B", "unicode_name": "BLACK RIGHT POINTING INDEX", "token_cost": 1, "token_density": 1.0, "quality_score": 99.99999999999999, "llm_support": ["openai", "qwen", "llama"], "created": "2025-05-23", "generator": "fresh_512_v1"}, {"id": "NG0283", "symbol": "⩩", "code": "ng:operator:and_assign", "fallback": "[ANDASS]", "category": "operator", "meaning": "and_assign", "description": "Operator operation: and_assign", "unicode_point": "U+2A69", "unicode_name": "TRIPLE HORIZONTAL BAR WITH TRIPLE VERTICAL STROKE", "token_cost": 1, "token_density": 1.0, "quality_score": 99.99999999999999, "llm_support": ["openai", "qwen", "llama"], "created": "2025-05-23", "generator": "fresh_512_v1"}, {"id": "NG0284", "symbol": "⟉", "code": "ng:operator:concat", "fallback": "[CONCAT]", "category": "operator", "meaning": "concat", "description": "Operator operation: concat", "unicode_point": "U+27C9", "unicode_name": "SUPERSET PRECEDING SOLIDUS", "token_cost": 1, "token_density": 1.0, "quality_score": 99.99999999999999, "llm_support": ["openai", "qwen", "llama"], "created": "2025-05-23", "generator": "fresh_512_v1"}, {"id": "NG0285", "symbol": "▪", "code": "ng:operator:append", "fallback": "[APPEND]", "category": "operator", "meaning": "append", "description": "Operator operation: append", "unicode_point": "U+25AA", "unicode_name": "BLACK SMALL SQUARE", "token_cost": 1, "token_density": 1.0, "quality_score": 99.99999999999999, "llm_support": ["openai", "qwen", "llama"], "created": "2025-05-23", "generator": "fresh_512_v1"}, {"id": "NG0286", "symbol": "⦀", "code": "ng:operator:prepend", "fallback": "[PREPEN]", "category": "operator", "meaning": "prepend", "description": "Operator operation: prepend", "unicode_point": "U+2980", "unicode_name": "TRIPLE VERTICAL BAR DELIMITER", "token_cost": 1, "token_density": 1.0, "quality_score": 99.99999999999999, "llm_support": ["openai", "qwen", "llama"], "created": "2025-05-23", "generator": "fresh_512_v1"}, {"id": "NG0287", "symbol": "⦡", "code": "ng:operator:add_1", "fallback": "[ADD1]", "category": "operator", "meaning": "add", "description": "Operator operation: add", "unicode_point": "U+29A1", "unicode_name": "SPHERICAL ANGLE OPENING UP", "token_cost": 1, "token_density": 1.0, "quality_score": 99.99999999999999, "llm_support": ["openai", "qwen", "llama"], "created": "2025-05-23", "generator": "fresh_512_v1"}, {"id": "NG0288", "symbol": "≸", "code": "ng:operator:sub_1", "fallback": "[SUB1]", "category": "operator", "meaning": "sub", "description": "Operator operation: sub", "unicode_point": "U+2278", "unicode_name": "NEITHER LESS-THAN NOR GREATER-THAN", "token_cost": 1, "token_density": 1.0, "quality_score": 99.99999999999999, "llm_support": ["openai", "qwen", "llama"], "created": "2025-05-23", "generator": "fresh_512_v1"}, {"id": "NG0289", "symbol": "☒", "code": "ng:operator:mul_1", "fallback": "[MUL1]", "category": "operator", "meaning": "mul", "description": "Operator operation: mul", "unicode_point": "U+2612", "unicode_name": "BALLOT BOX WITH X", "token_cost": 1, "token_density": 1.0, "quality_score": 99.99999999999999, "llm_support": ["openai", "qwen", "llama"], "created": "2025-05-23", "generator": "fresh_512_v1"}, {"id": "NG0290", "symbol": "≭", "code": "ng:operator:div_1", "fallback": "[DIV1]", "category": "operator", "meaning": "div", "description": "Operator operation: div", "unicode_point": "U+226D", "unicode_name": "NOT EQUIVALENT TO", "token_cost": 1, "token_density": 1.0, "quality_score": 99.99999999999999, "llm_support": ["openai", "qwen", "llama"], "created": "2025-05-23", "generator": "fresh_512_v1"}, {"id": "NG0291", "symbol": "⌁", "code": "ng:operator:mod_1", "fallback": "[MOD1]", "category": "operator", "meaning": "mod", "description": "Operator operation: mod", "unicode_point": "U+2301", "unicode_name": "ELECTRIC ARROW", "token_cost": 1, "token_density": 1.0, "quality_score": 99.99999999999999, "llm_support": ["openai", "qwen", "llama"], "created": "2025-05-23", "generator": "fresh_512_v1"}, {"id": "NG0292", "symbol": "⦕", "code": "ng:operator:pow_1", "fallback": "[POW1]", "category": "operator", "meaning": "pow", "description": "Operator operation: pow", "unicode_point": "U+2995", "unicode_name": "DOUBLE LEFT ARC GREATER-THAN BRACKET", "token_cost": 1, "token_density": 1.0, "quality_score": 99.99999999999999, "llm_support": ["openai", "qwen", "llama"], "created": "2025-05-23", "generator": "fresh_512_v1"}, {"id": "NG0293", "symbol": "⫽", "code": "ng:operator:sqrt_1", "fallback": "[SQRT1]", "category": "operator", "meaning": "sqrt", "description": "Operator operation: sqrt", "unicode_point": "U+2AFD", "unicode_name": "DOUBLE SOLIDUS OPERATOR", "token_cost": 1, "token_density": 1.0, "quality_score": 99.99999999999999, "llm_support": ["openai", "qwen", "llama"], "created": "2025-05-23", "generator": "fresh_512_v1"}, {"id": "NG0294", "symbol": "⟅", "code": "ng:operator:abs_1", "fallback": "[ABS1]", "category": "operator", "meaning": "abs", "description": "Operator operation: abs", "unicode_point": "U+27C5", "unicode_name": "LEFT S-SHAPED BAG DELIMITER", "token_cost": 1, "token_density": 1.0, "quality_score": 99.99999999999999, "llm_support": ["openai", "qwen", "llama"], "created": "2025-05-23", "generator": "fresh_512_v1"}, {"id": "NG0295", "symbol": "⊹", "code": "ng:operator:neg_1", "fallback": "[NEG1]", "category": "operator", "meaning": "neg", "description": "Operator operation: neg", "unicode_point": "U+22B9", "unicode_name": "HERMITIAN CONJUGATE MATRIX", "token_cost": 1, "token_density": 1.0, "quality_score": 99.99999999999999, "llm_support": ["openai", "qwen", "llama"], "created": "2025-05-23", "generator": "fresh_512_v1"}, {"id": "NG0296", "symbol": "⎾", "code": "ng:operator:inc_1", "fallback": "[INC1]", "category": "operator", "meaning": "inc", "description": "Operator operation: inc", "unicode_point": "U+23BE", "unicode_name": "DENTISTRY SYMBOL LIGHT VERTICAL AND TOP RIGHT", "token_cost": 1, "token_density": 1.0, "quality_score": 99.99999999999999, "llm_support": ["openai", "qwen", "llama"], "created": "2025-05-23", "generator": "fresh_512_v1"}, {"id": "NG0297", "symbol": "⩙", "code": "ng:operator:dec_1", "fallback": "[DEC1]", "category": "operator", "meaning": "dec", "description": "Operator operation: dec", "unicode_point": "U+2A59", "unicode_name": "LOGICAL OR OVERLAPPING LOGICAL AND", "token_cost": 1, "token_density": 1.0, "quality_score": 99.99999999999999, "llm_support": ["openai", "qwen", "llama"], "created": "2025-05-23", "generator": "fresh_512_v1"}, {"id": "NG0298", "symbol": "⚶", "code": "ng:operator:eq_1", "fallback": "[EQ1]", "category": "operator", "meaning": "eq", "description": "Operator operation: eq", "unicode_point": "U+26B6", "unicode_name": "VESTA", "token_cost": 1, "token_density": 1.0, "quality_score": 99.99999999999999, "llm_support": ["openai", "qwen", "llama"], "created": "2025-05-23", "generator": "fresh_512_v1"}, {"id": "NG0299", "symbol": "⩵", "code": "ng:operator:ne_1", "fallback": "[NE1]", "category": "operator", "meaning": "ne", "description": "Operator operation: ne", "unicode_point": "U+2A75", "unicode_name": "TWO CONSECUTIVE EQUALS SIGNS", "token_cost": 1, "token_density": 1.0, "quality_score": 99.99999999999999, "llm_support": ["openai", "qwen", "llama"], "created": "2025-05-23", "generator": "fresh_512_v1"}, {"id": "NG0300", "symbol": "↦", "code": "ng:operator:lt_1", "fallback": "[LT1]", "category": "operator", "meaning": "lt", "description": "Operator operation: lt", "unicode_point": "U+21A6", "unicode_name": "RIGHTWARDS ARROW FROM BAR", "token_cost": 1, "token_density": 1.0, "quality_score": 99.99999999999999, "llm_support": ["openai", "qwen", "llama"], "created": "2025-05-23", "generator": "fresh_512_v1"}, {"id": "NG0301", "symbol": "⫡", "code": "ng:operator:le_1", "fallback": "[LE1]", "category": "operator", "meaning": "le", "description": "Operator operation: le", "unicode_point": "U+2AE1", "unicode_name": "PERPENDICULAR WITH S", "token_cost": 1, "token_density": 1.0, "quality_score": 99.99999999999999, "llm_support": ["openai", "qwen", "llama"], "created": "2025-05-23", "generator": "fresh_512_v1"}, {"id": "NG0302", "symbol": "⟬", "code": "ng:operator:gt_1", "fallback": "[GT1]", "category": "operator", "meaning": "gt", "description": "Operator operation: gt", "unicode_point": "U+27EC", "unicode_name": "MATHEMATICAL LEFT WHITE TORTOISE SHELL BRACKET", "token_cost": 1, "token_density": 1.0, "quality_score": 99.99999999999999, "llm_support": ["openai", "qwen", "llama"], "created": "2025-05-23", "generator": "fresh_512_v1"}, {"id": "NG0303", "symbol": "⇐", "code": "ng:operator:ge_1", "fallback": "[GE1]", "category": "operator", "meaning": "ge", "description": "Operator operation: ge", "unicode_point": "U+21D0", "unicode_name": "LEFTWARDS DOUBLE ARROW", "token_cost": 1, "token_density": 1.0, "quality_score": 99.99999999999999, "llm_support": ["openai", "qwen", "llama"], "created": "2025-05-23", "generator": "fresh_512_v1"}, {"id": "NG0304", "symbol": "♮", "code": "ng:operator:min_1", "fallback": "[MIN1]", "category": "operator", "meaning": "min", "description": "Operator operation: min", "unicode_point": "U+266E", "unicode_name": "MUSIC NATURAL SIGN", "token_cost": 1, "token_density": 1.0, "quality_score": 99.99999999999999, "llm_support": ["openai", "qwen", "llama"], "created": "2025-05-23", "generator": "fresh_512_v1"}, {"id": "NG0305", "symbol": "⋓", "code": "ng:operator:max_1", "fallback": "[MAX1]", "category": "operator", "meaning": "max", "description": "Operator operation: max", "unicode_point": "U+22D3", "unicode_name": "DOUBLE UNION", "token_cost": 1, "token_density": 1.0, "quality_score": 99.99999999999999, "llm_support": ["openai", "qwen", "llama"], "created": "2025-05-23", "generator": "fresh_512_v1"}, {"id": "NG0306", "symbol": "☷", "code": "ng:operator:clamp_1", "fallback": "[CLAMP1]", "category": "operator", "meaning": "clamp", "description": "Operator operation: clamp", "unicode_point": "U+2637", "unicode_name": "TRIGRAM FOR EARTH", "token_cost": 1, "token_density": 1.0, "quality_score": 99.99999999999999, "llm_support": ["openai", "qwen", "llama"], "created": "2025-05-23", "generator": "fresh_512_v1"}, {"id": "NG0307", "symbol": "▣", "code": "ng:operator:and_1", "fallback": "[AND3]", "category": "operator", "meaning": "and", "description": "Operator operation: and", "unicode_point": "U+25A3", "unicode_name": "WHITE SQUARE CONTAINING BLACK SMALL SQUARE", "token_cost": 1, "token_density": 1.0, "quality_score": 99.99999999999999, "llm_support": ["openai", "qwen", "llama"], "created": "2025-05-23", "generator": "fresh_512_v1"}, {"id": "NG0308", "symbol": "⌓", "code": "ng:operator:or_1", "fallback": "[OR3]", "category": "operator", "meaning": "or", "description": "Operator operation: or", "unicode_point": "U+2313", "unicode_name": "SEGMENT", "token_cost": 1, "token_density": 1.0, "quality_score": 99.99999999999999, "llm_support": ["openai", "qwen", "llama"], "created": "2025-05-23", "generator": "fresh_512_v1"}, {"id": "NG0309", "symbol": "⋽", "code": "ng:operator:xor_1", "fallback": "[XOR3]", "category": "operator", "meaning": "xor", "description": "Operator operation: xor", "unicode_point": "U+22FD", "unicode_name": "CONTAINS WITH OVERBAR", "token_cost": 1, "token_density": 1.0, "quality_score": 99.99999999999999, "llm_support": ["openai", "qwen", "llama"], "created": "2025-05-23", "generator": "fresh_512_v1"}, {"id": "NG0310", "symbol": "⨂", "code": "ng:operator:not_1", "fallback": "[NOT3]", "category": "operator", "meaning": "not", "description": "Operator operation: not", "unicode_point": "U+2A02", "unicode_name": "N-ARY CIRCLED TIMES OPERATOR", "token_cost": 1, "token_density": 1.0, "quality_score": 99.99999999999999, "llm_support": ["openai", "qwen", "llama"], "created": "2025-05-23", "generator": "fresh_512_v1"}, {"id": "NG0311", "symbol": "∠", "code": "ng:operator:shl_1", "fallback": "[SHL1]", "category": "operator", "meaning": "shl", "description": "Operator operation: shl", "unicode_point": "U+2220", "unicode_name": "ANGLE", "token_cost": 1, "token_density": 1.0, "quality_score": 99.99999999999999, "llm_support": ["openai", "qwen", "llama"], "created": "2025-05-23", "generator": "fresh_512_v1"}, {"id": "NG0312", "symbol": "⇸", "code": "ng:operator:shr_1", "fallback": "[SHR1]", "category": "operator", "meaning": "shr", "description": "Operator operation: shr", "unicode_point": "U+21F8", "unicode_name": "RIGHTWARDS ARROW WITH VERTICAL STROKE", "token_cost": 1, "token_density": 1.0, "quality_score": 99.99999999999999, "llm_support": ["openai", "qwen", "llama"], "created": "2025-05-23", "generator": "fresh_512_v1"}, {"id": "NG0313", "symbol": "⇩", "code": "ng:memory:alloc", "fallback": "[ALLOC]", "category": "memory", "meaning": "alloc", "description": "Memory operation: alloc", "unicode_point": "U+21E9", "unicode_name": "DOWNWARDS WHITE ARROW", "token_cost": 1, "token_density": 1.0, "quality_score": 99.99999999999999, "llm_support": ["openai", "qwen", "llama"], "created": "2025-05-23", "generator": "fresh_512_v1"}, {"id": "NG0314", "symbol": "◉", "code": "ng:memory:malloc", "fallback": "[MALLOC]", "category": "memory", "meaning": "malloc", "description": "Memory operation: malloc", "unicode_point": "U+25C9", "unicode_name": "FISHEYE", "token_cost": 1, "token_density": 1.0, "quality_score": 99.99999999999999, "llm_support": ["openai", "qwen", "llama"], "created": "2025-05-23", "generator": "fresh_512_v1"}, {"id": "NG0315", "symbol": "⍖", "code": "ng:memory:calloc", "fallback": "[CALLOC]", "category": "memory", "meaning": "calloc", "description": "Memory operation: calloc", "unicode_point": "U+2356", "unicode_name": "APL FUNCTIONAL SYMBOL DOWNWARDS VANE", "token_cost": 1, "token_density": 1.0, "quality_score": 99.99999999999999, "llm_support": ["openai", "qwen", "llama"], "created": "2025-05-23", "generator": "fresh_512_v1"}, {"id": "NG0316", "symbol": "♈", "code": "ng:memory:realloc", "fallback": "[REALLO]", "category": "memory", "meaning": "realloc", "description": "Memory operation: realloc", "unicode_point": "U+2648", "unicode_name": "ARIES", "token_cost": 1, "token_density": 1.0, "quality_score": 99.99999999999999, "llm_support": ["openai", "qwen", "llama"], "created": "2025-05-23", "generator": "fresh_512_v1"}, {"id": "NG0317", "symbol": "↡", "code": "ng:memory:new", "fallback": "[NEW]", "category": "memory", "meaning": "new", "description": "Memory operation: new", "unicode_point": "U+21A1", "unicode_name": "DOWNWARDS TWO HEADED ARROW", "token_cost": 1, "token_density": 1.0, "quality_score": 99.99999999999999, "llm_support": ["openai", "qwen", "llama"], "created": "2025-05-23", "generator": "fresh_512_v1"}, {"id": "NG0318", "symbol": "√", "code": "ng:memory:create", "fallback": "[CREATE]", "category": "memory", "meaning": "create", "description": "Memory operation: create", "unicode_point": "U+221A", "unicode_name": "SQUARE ROOT", "token_cost": 1, "token_density": 1.0, "quality_score": 99.99999999999999, "llm_support": ["openai", "qwen", "llama"], "created": "2025-05-23", "generator": "fresh_512_v1"}, {"id": "NG0319", "symbol": "⚤", "code": "ng:memory:reserve", "fallback": "[RESERV]", "category": "memory", "meaning": "reserve", "description": "Memory operation: reserve", "unicode_point": "U+26A4", "unicode_name": "INTERLOCKED FEMALE AND <PERSON>LE SIGN", "token_cost": 1, "token_density": 1.0, "quality_score": 99.99999999999999, "llm_support": ["openai", "qwen", "llama"], "created": "2025-05-23", "generator": "fresh_512_v1"}, {"id": "NG0320", "symbol": "≷", "code": "ng:memory:free", "fallback": "[FREE]", "category": "memory", "meaning": "free", "description": "Memory operation: free", "unicode_point": "U+2277", "unicode_name": "GREATER-THAN OR LESS-THAN", "token_cost": 1, "token_density": 1.0, "quality_score": 99.99999999999999, "llm_support": ["openai", "qwen", "llama"], "created": "2025-05-23", "generator": "fresh_512_v1"}, {"id": "NG0321", "symbol": "≲", "code": "ng:memory:delete", "fallback": "[DELETE]", "category": "memory", "meaning": "delete", "description": "Memory operation: delete", "unicode_point": "U+2272", "unicode_name": "LESS-THAN OR EQUIVALENT TO", "token_cost": 1, "token_density": 1.0, "quality_score": 99.99999999999999, "llm_support": ["openai", "qwen", "llama"], "created": "2025-05-23", "generator": "fresh_512_v1"}, {"id": "NG0322", "symbol": "⎰", "code": "ng:memory:destroy", "fallback": "[DESTRO]", "category": "memory", "meaning": "destroy", "description": "Memory operation: destroy", "unicode_point": "U+23B0", "unicode_name": "UPPER LEFT OR LOWER RIGHT CURLY BRACKET SECTION", "token_cost": 1, "token_density": 1.0, "quality_score": 99.99999999999999, "llm_support": ["openai", "qwen", "llama"], "created": "2025-05-23", "generator": "fresh_512_v1"}, {"id": "NG0323", "symbol": "⟓", "code": "ng:memory:ref", "fallback": "[REF]", "category": "memory", "meaning": "ref", "description": "Memory operation: ref", "unicode_point": "U+27D3", "unicode_name": "LOWER RIGHT CORNER WITH DOT", "token_cost": 1, "token_density": 1.0, "quality_score": 99.99999999999999, "llm_support": ["openai", "qwen", "llama"], "created": "2025-05-23", "generator": "fresh_512_v1"}, {"id": "NG0324", "symbol": "⇟", "code": "ng:memory:deref", "fallback": "[DEREF]", "category": "memory", "meaning": "deref", "description": "Memory operation: deref", "unicode_point": "U+21DF", "unicode_name": "DOWNWARDS ARROW WITH DOUBLE STROKE", "token_cost": 1, "token_density": 1.0, "quality_score": 99.99999999999999, "llm_support": ["openai", "qwen", "llama"], "created": "2025-05-23", "generator": "fresh_512_v1"}, {"id": "NG0325", "symbol": "∊", "code": "ng:memory:pointer", "fallback": "[POINTE]", "category": "memory", "meaning": "pointer", "description": "Memory operation: pointer", "unicode_point": "U+220A", "unicode_name": "SMALL ELEMENT OF", "token_cost": 1, "token_density": 1.0, "quality_score": 99.99999999999999, "llm_support": ["openai", "qwen", "llama"], "created": "2025-05-23", "generator": "fresh_512_v1"}, {"id": "NG0326", "symbol": "⚟", "code": "ng:memory:address", "fallback": "[ADDRES]", "category": "memory", "meaning": "address", "description": "Memory operation: address", "unicode_point": "U+269F", "unicode_name": "THREE LINES CONVERGING LEFT", "token_cost": 1, "token_density": 1.0, "quality_score": 99.99999999999999, "llm_support": ["openai", "qwen", "llama"], "created": "2025-05-23", "generator": "fresh_512_v1"}, {"id": "NG0327", "symbol": "⦂", "code": "ng:memory:offset", "fallback": "[OFFSET]", "category": "memory", "meaning": "offset", "description": "Memory operation: offset", "unicode_point": "U+2982", "unicode_name": "Z NOTATION TYPE COLON", "token_cost": 1, "token_density": 1.0, "quality_score": 99.99999999999999, "llm_support": ["openai", "qwen", "llama"], "created": "2025-05-23", "generator": "fresh_512_v1"}, {"id": "NG0328", "symbol": "⧍", "code": "ng:memory:index", "fallback": "[INDEX]", "category": "memory", "meaning": "index", "description": "Memory operation: index", "unicode_point": "U+29CD", "unicode_name": "TRIANGLE WITH SERIFS AT BOTTOM", "token_cost": 1, "token_density": 1.0, "quality_score": 99.99999999999999, "llm_support": ["openai", "qwen", "llama"], "created": "2025-05-23", "generator": "fresh_512_v1"}, {"id": "NG0329", "symbol": "∜", "code": "ng:memory:slice", "fallback": "[SLICE]", "category": "memory", "meaning": "slice", "description": "Memory operation: slice", "unicode_point": "U+221C", "unicode_name": "FOURTH ROOT", "token_cost": 1, "token_density": 1.0, "quality_score": 99.99999999999999, "llm_support": ["openai", "qwen", "llama"], "created": "2025-05-23", "generator": "fresh_512_v1"}, {"id": "NG0330", "symbol": "⩿", "code": "ng:memory:cache", "fallback": "[CACHE]", "category": "memory", "meaning": "cache", "description": "Memory operation: cache", "unicode_point": "U+2A7F", "unicode_name": "LESS-THAN OR SLANTED EQUAL TO WITH DOT INSIDE", "token_cost": 1, "token_density": 1.0, "quality_score": 99.99999999999999, "llm_support": ["openai", "qwen", "llama"], "created": "2025-05-23", "generator": "fresh_512_v1"}, {"id": "NG0331", "symbol": "⎽", "code": "ng:memory:memoize", "fallback": "[MEMOIZ]", "category": "memory", "meaning": "memoize", "description": "Memory operation: memoize", "unicode_point": "U+23BD", "unicode_name": "HORIZONTAL SCAN LINE-9", "token_cost": 1, "token_density": 1.0, "quality_score": 99.99999999999999, "llm_support": ["openai", "qwen", "llama"], "created": "2025-05-23", "generator": "fresh_512_v1"}, {"id": "NG0332", "symbol": "◝", "code": "ng:memory:buffer", "fallback": "[BUFFER]", "category": "memory", "meaning": "buffer", "description": "Memory operation: buffer", "unicode_point": "U+25DD", "unicode_name": "UPPER RIGHT QUADRANT CIRCULAR ARC", "token_cost": 1, "token_density": 1.0, "quality_score": 99.99999999999999, "llm_support": ["openai", "qwen", "llama"], "created": "2025-05-23", "generator": "fresh_512_v1"}, {"id": "NG0333", "symbol": "⧢", "code": "ng:memory:prefetch", "fallback": "[PREFET]", "category": "memory", "meaning": "prefetch", "description": "Memory operation: prefetch", "unicode_point": "U+29E2", "unicode_name": "SHUFFLE PRODUCT", "token_cost": 1, "token_density": 1.0, "quality_score": 99.99999999999999, "llm_support": ["openai", "qwen", "llama"], "created": "2025-05-23", "generator": "fresh_512_v1"}, {"id": "NG0334", "symbol": "⦭", "code": "ng:memory:evict", "fallback": "[EVICT]", "category": "memory", "meaning": "evict", "description": "Memory operation: evict", "unicode_point": "U+29AD", "unicode_name": "MEASURED AN<PERSON>LE WITH OPEN ARM ENDING IN ARROW POINTING LEFT AND UP", "token_cost": 1, "token_density": 1.0, "quality_score": 99.99999999999999, "llm_support": ["openai", "qwen", "llama"], "created": "2025-05-23", "generator": "fresh_512_v1"}, {"id": "NG0335", "symbol": "▴", "code": "ng:memory:flush", "fallback": "[FLUSH]", "category": "memory", "meaning": "flush", "description": "Memory operation: flush", "unicode_point": "U+25B4", "unicode_name": "BLACK UP-POINTING SMALL TRIANGLE", "token_cost": 1, "token_density": 1.0, "quality_score": 99.99999999999999, "llm_support": ["openai", "qwen", "llama"], "created": "2025-05-23", "generator": "fresh_512_v1"}, {"id": "NG0336", "symbol": "◿", "code": "ng:memory:sync", "fallback": "[SYNC]", "category": "memory", "meaning": "sync", "description": "Memory operation: sync", "unicode_point": "U+25FF", "unicode_name": "LOWER RIGHT TRIANGLE", "token_cost": 1, "token_density": 1.0, "quality_score": 99.99999999999999, "llm_support": ["openai", "qwen", "llama"], "created": "2025-05-23", "generator": "fresh_512_v1"}, {"id": "NG0337", "symbol": "◕", "code": "ng:memory:stack", "fallback": "[STACK]", "category": "memory", "meaning": "stack", "description": "Memory operation: stack", "unicode_point": "U+25D5", "unicode_name": "CIRCLE WITH ALL BUT UPPER LEFT QUADRANT BLACK", "token_cost": 1, "token_density": 1.0, "quality_score": 99.99999999999999, "llm_support": ["openai", "qwen", "llama"], "created": "2025-05-23", "generator": "fresh_512_v1"}, {"id": "NG0338", "symbol": "⨐", "code": "ng:memory:heap", "fallback": "[HEAP]", "category": "memory", "meaning": "heap", "description": "Memory operation: heap", "unicode_point": "U+2A10", "unicode_name": "CIRCULATION FUNCTION", "token_cost": 1, "token_density": 1.0, "quality_score": 99.99999999999999, "llm_support": ["openai", "qwen", "llama"], "created": "2025-05-23", "generator": "fresh_512_v1"}, {"id": "NG0339", "symbol": "☲", "code": "ng:memory:register", "fallback": "[REGIST]", "category": "memory", "meaning": "register", "description": "Memory operation: register", "unicode_point": "U+2632", "unicode_name": "TRIGRAM FOR FIRE", "token_cost": 1, "token_density": 1.0, "quality_score": 99.99999999999999, "llm_support": ["openai", "qwen", "llama"], "created": "2025-05-23", "generator": "fresh_512_v1"}, {"id": "NG0340", "symbol": "⌟", "code": "ng:memory:volatile", "fallback": "[VOLATI]", "category": "memory", "meaning": "volatile", "description": "Memory operation: volatile", "unicode_point": "U+231F", "unicode_name": "BOTTOM RIGHT CORNER", "token_cost": 1, "token_density": 1.0, "quality_score": 99.99999999999999, "llm_support": ["openai", "qwen", "llama"], "created": "2025-05-23", "generator": "fresh_512_v1"}, {"id": "NG0341", "symbol": "⧆", "code": "ng:memory:persistent", "fallback": "[PERSIS]", "category": "memory", "meaning": "persistent", "description": "Memory operation: persistent", "unicode_point": "U+29C6", "unicode_name": "SQUARED ASTERISK", "token_cost": 1, "token_density": 1.0, "quality_score": 99.99999999999999, "llm_support": ["openai", "qwen", "llama"], "created": "2025-05-23", "generator": "fresh_512_v1"}, {"id": "NG0342", "symbol": "↛", "code": "ng:memory:transient", "fallback": "[TRANSI]", "category": "memory", "meaning": "transient", "description": "Memory operation: transient", "unicode_point": "U+219B", "unicode_name": "RIGHTWARDS ARROW WITH STROKE", "token_cost": 1, "token_density": 1.0, "quality_score": 99.99999999999999, "llm_support": ["openai", "qwen", "llama"], "created": "2025-05-23", "generator": "fresh_512_v1"}, {"id": "NG0343", "symbol": "⫳", "code": "ng:memory:immutable", "fallback": "[IMMUTA]", "category": "memory", "meaning": "immutable", "description": "Memory operation: immutable", "unicode_point": "U+2AF3", "unicode_name": "PARALLEL WITH TILDE OPERATOR", "token_cost": 1, "token_density": 1.0, "quality_score": 99.99999999999999, "llm_support": ["openai", "qwen", "llama"], "created": "2025-05-23", "generator": "fresh_512_v1"}, {"id": "NG0344", "symbol": "☫", "code": "ng:memory:mutable", "fallback": "[MUTABL]", "category": "memory", "meaning": "mutable", "description": "Memory operation: mutable", "unicode_point": "U+262B", "unicode_name": "FARSI SYMBOL", "token_cost": 1, "token_density": 1.0, "quality_score": 99.99999999999999, "llm_support": ["openai", "qwen", "llama"], "created": "2025-05-23", "generator": "fresh_512_v1"}, {"id": "NG0345", "symbol": "◷", "code": "ng:memory:copy", "fallback": "[COPY]", "category": "memory", "meaning": "copy", "description": "Memory operation: copy", "unicode_point": "U+25F7", "unicode_name": "WHITE CIRCLE WITH UPPER RIGHT QUADRANT", "token_cost": 1, "token_density": 1.0, "quality_score": 99.99999999999999, "llm_support": ["openai", "qwen", "llama"], "created": "2025-05-23", "generator": "fresh_512_v1"}, {"id": "NG0346", "symbol": "⧒", "code": "ng:memory:move", "fallback": "[MOVE]", "category": "memory", "meaning": "move", "description": "Memory operation: move", "unicode_point": "U+29D2", "unicode_name": "BOWTIE WITH RIGHT HALF BLACK", "token_cost": 1, "token_density": 1.0, "quality_score": 99.99999999999999, "llm_support": ["openai", "qwen", "llama"], "created": "2025-05-23", "generator": "fresh_512_v1"}, {"id": "NG0347", "symbol": "∙", "code": "ng:memory:clone", "fallback": "[CLONE]", "category": "memory", "meaning": "clone", "description": "Memory operation: clone", "unicode_point": "U+2219", "unicode_name": "BULLET OPERATOR", "token_cost": 1, "token_density": 1.0, "quality_score": 99.99999999999999, "llm_support": ["openai", "qwen", "llama"], "created": "2025-05-23", "generator": "fresh_512_v1"}, {"id": "NG0348", "symbol": "⍇", "code": "ng:memory:share", "fallback": "[SHARE]", "category": "memory", "meaning": "share", "description": "Memory operation: share", "unicode_point": "U+2347", "unicode_name": "APL FUNCTIONAL SYMBOL QUAD LEFTWARDS ARROW", "token_cost": 1, "token_density": 1.0, "quality_score": 99.99999999999999, "llm_support": ["openai", "qwen", "llama"], "created": "2025-05-23", "generator": "fresh_512_v1"}, {"id": "NG0349", "symbol": "⎙", "code": "ng:memory:borrow", "fallback": "[BORROW]", "category": "memory", "meaning": "borrow", "description": "Memory operation: borrow", "unicode_point": "U+2399", "unicode_name": "PRINT SCREEN SYMBOL", "token_cost": 1, "token_density": 1.0, "quality_score": 99.99999999999999, "llm_support": ["openai", "qwen", "llama"], "created": "2025-05-23", "generator": "fresh_512_v1"}, {"id": "NG0350", "symbol": "⇖", "code": "ng:memory:own", "fallback": "[OWN]", "category": "memory", "meaning": "own", "description": "Memory operation: own", "unicode_point": "U+21D6", "unicode_name": "NORTH WEST DOUBLE ARROW", "token_cost": 1, "token_density": 1.0, "quality_score": 99.99999999999999, "llm_support": ["openai", "qwen", "llama"], "created": "2025-05-23", "generator": "fresh_512_v1"}, {"id": "NG0351", "symbol": "⛅", "code": "ng:memory:lease", "fallback": "[LEASE]", "category": "memory", "meaning": "lease", "description": "Memory operation: lease", "unicode_point": "U+26C5", "unicode_name": "SUN BEHIND CLOUD", "token_cost": 1, "token_density": 1.0, "quality_score": 99.99999999999999, "llm_support": ["openai", "qwen", "llama"], "created": "2025-05-23", "generator": "fresh_512_v1"}, {"id": "NG0352", "symbol": "≇", "code": "ng:memory:lock", "fallback": "[LOCK]", "category": "memory", "meaning": "lock", "description": "Memory operation: lock", "unicode_point": "U+2247", "unicode_name": "NEITHER APPROXIMATELY NOR ACTUALLY EQUAL TO", "token_cost": 1, "token_density": 1.0, "quality_score": 99.99999999999999, "llm_support": ["openai", "qwen", "llama"], "created": "2025-05-23", "generator": "fresh_512_v1"}, {"id": "NG0353", "symbol": "⍿", "code": "ng:memory:alloc_1", "fallback": "[ALLOC1]", "category": "memory", "meaning": "alloc", "description": "Memory operation: alloc", "unicode_point": "U+237F", "unicode_name": "VERTICAL LINE WITH MIDDLE DOT", "token_cost": 1, "token_density": 1.0, "quality_score": 99.99999999999999, "llm_support": ["openai", "qwen", "llama"], "created": "2025-05-23", "generator": "fresh_512_v1"}, {"id": "NG0354", "symbol": "⚍", "code": "ng:memory:malloc_1", "fallback": "[MALLOC1]", "category": "memory", "meaning": "malloc", "description": "Memory operation: malloc", "unicode_point": "U+268D", "unicode_name": "DIGRAM FOR LESSER YIN", "token_cost": 1, "token_density": 1.0, "quality_score": 99.99999999999999, "llm_support": ["openai", "qwen", "llama"], "created": "2025-05-23", "generator": "fresh_512_v1"}, {"id": "NG0355", "symbol": "◍", "code": "ng:memory:calloc_1", "fallback": "[CALLOC1]", "category": "memory", "meaning": "calloc", "description": "Memory operation: calloc", "unicode_point": "U+25CD", "unicode_name": "CIRCLE WITH VERTICAL FILL", "token_cost": 1, "token_density": 1.0, "quality_score": 99.99999999999999, "llm_support": ["openai", "qwen", "llama"], "created": "2025-05-23", "generator": "fresh_512_v1"}, {"id": "NG0356", "symbol": "⊭", "code": "ng:memory:realloc_1", "fallback": "[REALLO1]", "category": "memory", "meaning": "realloc", "description": "Memory operation: realloc", "unicode_point": "U+22AD", "unicode_name": "NOT TRUE", "token_cost": 1, "token_density": 1.0, "quality_score": 99.99999999999999, "llm_support": ["openai", "qwen", "llama"], "created": "2025-05-23", "generator": "fresh_512_v1"}, {"id": "NG0357", "symbol": "⩢", "code": "ng:memory:new_1", "fallback": "[NEW1]", "category": "memory", "meaning": "new", "description": "Memory operation: new", "unicode_point": "U+2A62", "unicode_name": "LOGICAL OR WITH DOUBLE OVERBAR", "token_cost": 1, "token_density": 1.0, "quality_score": 99.99999999999999, "llm_support": ["openai", "qwen", "llama"], "created": "2025-05-23", "generator": "fresh_512_v1"}, {"id": "NG0358", "symbol": "⛁", "code": "ng:memory:create_1", "fallback": "[CREATE1]", "category": "memory", "meaning": "create", "description": "Memory operation: create", "unicode_point": "U+26C1", "unicode_name": "WHITE DRAUGHTS KING", "token_cost": 1, "token_density": 1.0, "quality_score": 99.99999999999999, "llm_support": ["openai", "qwen", "llama"], "created": "2025-05-23", "generator": "fresh_512_v1"}, {"id": "NG0359", "symbol": "⎚", "code": "ng:memory:reserve_1", "fallback": "[RESERV1]", "category": "memory", "meaning": "reserve", "description": "Memory operation: reserve", "unicode_point": "U+239A", "unicode_name": "CLEAR SCREEN SYMBOL", "token_cost": 1, "token_density": 1.0, "quality_score": 99.99999999999999, "llm_support": ["openai", "qwen", "llama"], "created": "2025-05-23", "generator": "fresh_512_v1"}, {"id": "NG0360", "symbol": "⧟", "code": "ng:memory:free_1", "fallback": "[FREE1]", "category": "memory", "meaning": "free", "description": "Memory operation: free", "unicode_point": "U+29DF", "unicode_name": "DOUBLE-ENDED MULTIMAP", "token_cost": 1, "token_density": 1.0, "quality_score": 99.99999999999999, "llm_support": ["openai", "qwen", "llama"], "created": "2025-05-23", "generator": "fresh_512_v1"}, {"id": "NG0361", "symbol": "⟔", "code": "ng:memory:delete_1", "fallback": "[DELETE1]", "category": "memory", "meaning": "delete", "description": "Memory operation: delete", "unicode_point": "U+27D4", "unicode_name": "UPPER LEFT CORNER WITH DOT", "token_cost": 1, "token_density": 1.0, "quality_score": 99.99999999999999, "llm_support": ["openai", "qwen", "llama"], "created": "2025-05-23", "generator": "fresh_512_v1"}, {"id": "NG0362", "symbol": "◸", "code": "ng:memory:destroy_1", "fallback": "[DESTRO1]", "category": "memory", "meaning": "destroy", "description": "Memory operation: destroy", "unicode_point": "U+25F8", "unicode_name": "UPPER LEFT TRIANGLE", "token_cost": 1, "token_density": 1.0, "quality_score": 99.99999999999999, "llm_support": ["openai", "qwen", "llama"], "created": "2025-05-23", "generator": "fresh_512_v1"}, {"id": "NG0363", "symbol": "◎", "code": "ng:memory:ref_1", "fallback": "[REF1]", "category": "memory", "meaning": "ref", "description": "Memory operation: ref", "unicode_point": "U+25CE", "unicode_name": "BULLSEYE", "token_cost": 1, "token_density": 1.0, "quality_score": 99.99999999999999, "llm_support": ["openai", "qwen", "llama"], "created": "2025-05-23", "generator": "fresh_512_v1"}, {"id": "NG0364", "symbol": "◄", "code": "ng:memory:deref_1", "fallback": "[DEREF1]", "category": "memory", "meaning": "deref", "description": "Memory operation: deref", "unicode_point": "U+25C4", "unicode_name": "BLACK LEFT-POINTING POINTER", "token_cost": 1, "token_density": 1.0, "quality_score": 99.99999999999999, "llm_support": ["openai", "qwen", "llama"], "created": "2025-05-23", "generator": "fresh_512_v1"}, {"id": "NG0365", "symbol": "⊑", "code": "ng:memory:pointer_1", "fallback": "[POINTE1]", "category": "memory", "meaning": "pointer", "description": "Memory operation: pointer", "unicode_point": "U+2291", "unicode_name": "SQUARE IMAGE OF OR EQUAL TO", "token_cost": 1, "token_density": 1.0, "quality_score": 99.99999999999999, "llm_support": ["openai", "qwen", "llama"], "created": "2025-05-23", "generator": "fresh_512_v1"}, {"id": "NG0366", "symbol": "⧡", "code": "ng:memory:address_1", "fallback": "[ADDRES1]", "category": "memory", "meaning": "address", "description": "Memory operation: address", "unicode_point": "U+29E1", "unicode_name": "INCREASES AS", "token_cost": 1, "token_density": 1.0, "quality_score": 99.99999999999999, "llm_support": ["openai", "qwen", "llama"], "created": "2025-05-23", "generator": "fresh_512_v1"}, {"id": "NG0367", "symbol": "⫤", "code": "ng:memory:offset_1", "fallback": "[OFFSET1]", "category": "memory", "meaning": "offset", "description": "Memory operation: offset", "unicode_point": "U+2AE4", "unicode_name": "VERTICAL BAR DOUBLE LEFT TURNSTILE", "token_cost": 1, "token_density": 1.0, "quality_score": 99.99999999999999, "llm_support": ["openai", "qwen", "llama"], "created": "2025-05-23", "generator": "fresh_512_v1"}, {"id": "NG0368", "symbol": "⛉", "code": "ng:memory:index_1", "fallback": "[INDEX1]", "category": "memory", "meaning": "index", "description": "Memory operation: index", "unicode_point": "U+26C9", "unicode_name": "TURNED WHITE SHOGI PIECE", "token_cost": 1, "token_density": 1.0, "quality_score": 99.99999999999999, "llm_support": ["openai", "qwen", "llama"], "created": "2025-05-23", "generator": "fresh_512_v1"}, {"id": "NG0369", "symbol": "⏣", "code": "ng:memory:slice_1", "fallback": "[SLICE1]", "category": "memory", "meaning": "slice", "description": "Memory operation: slice", "unicode_point": "U+23E3", "unicode_name": "BENZENE RING WITH CIRCLE", "token_cost": 1, "token_density": 1.0, "quality_score": 99.99999999999999, "llm_support": ["openai", "qwen", "llama"], "created": "2025-05-23", "generator": "fresh_512_v1"}, {"id": "NG0370", "symbol": "⟪", "code": "ng:memory:cache_1", "fallback": "[CACHE1]", "category": "memory", "meaning": "cache", "description": "Memory operation: cache", "unicode_point": "U+27EA", "unicode_name": "MATHEMATICAL LEFT DOUBLE ANGLE BRACKET", "token_cost": 1, "token_density": 1.0, "quality_score": 99.99999999999999, "llm_support": ["openai", "qwen", "llama"], "created": "2025-05-23", "generator": "fresh_512_v1"}, {"id": "NG0371", "symbol": "⛇", "code": "ng:memory:memoize_1", "fallback": "[MEMOIZ1]", "category": "memory", "meaning": "memoize", "description": "Memory operation: memoize", "unicode_point": "U+26C7", "unicode_name": "BLACK SNOWMAN", "token_cost": 1, "token_density": 1.0, "quality_score": 99.99999999999999, "llm_support": ["openai", "qwen", "llama"], "created": "2025-05-23", "generator": "fresh_512_v1"}, {"id": "NG0372", "symbol": "◙", "code": "ng:memory:buffer_1", "fallback": "[BUFFER1]", "category": "memory", "meaning": "buffer", "description": "Memory operation: buffer", "unicode_point": "U+25D9", "unicode_name": "INVERSE WHITE CIRCLE", "token_cost": 1, "token_density": 1.0, "quality_score": 99.99999999999999, "llm_support": ["openai", "qwen", "llama"], "created": "2025-05-23", "generator": "fresh_512_v1"}, {"id": "NG0373", "symbol": "◨", "code": "ng:memory:prefetch_1", "fallback": "[PREFET1]", "category": "memory", "meaning": "prefetch", "description": "Memory operation: prefetch", "unicode_point": "U+25E8", "unicode_name": "SQUARE WITH RIGHT HALF BLACK", "token_cost": 1, "token_density": 1.0, "quality_score": 99.99999999999999, "llm_support": ["openai", "qwen", "llama"], "created": "2025-05-23", "generator": "fresh_512_v1"}, {"id": "NG0374", "symbol": "⌊", "code": "ng:memory:evict_1", "fallback": "[EVICT1]", "category": "memory", "meaning": "evict", "description": "Memory operation: evict", "unicode_point": "U+230A", "unicode_name": "LEFT FLOOR", "token_cost": 1, "token_density": 1.0, "quality_score": 99.99999999999999, "llm_support": ["openai", "qwen", "llama"], "created": "2025-05-23", "generator": "fresh_512_v1"}, {"id": "NG0375", "symbol": "⩶", "code": "ng:memory:flush_1", "fallback": "[FLUSH1]", "category": "memory", "meaning": "flush", "description": "Memory operation: flush", "unicode_point": "U+2A76", "unicode_name": "THREE CONSECUTIVE EQUALS SIGNS", "token_cost": 1, "token_density": 1.0, "quality_score": 99.99999999999999, "llm_support": ["openai", "qwen", "llama"], "created": "2025-05-23", "generator": "fresh_512_v1"}, {"id": "NG0376", "symbol": "☶", "code": "ng:memory:sync_1", "fallback": "[SYNC1]", "category": "memory", "meaning": "sync", "description": "Memory operation: sync", "unicode_point": "U+2636", "unicode_name": "TRIGRAM FOR MOUNTAIN", "token_cost": 1, "token_density": 1.0, "quality_score": 99.99999999999999, "llm_support": ["openai", "qwen", "llama"], "created": "2025-05-23", "generator": "fresh_512_v1"}, {"id": "NG0377", "symbol": "⦴", "code": "ng:reasoning:induct", "fallback": "[INDUCT]", "category": "reasoning", "meaning": "induct", "description": "Reasoning operation: induct", "unicode_point": "U+29B4", "unicode_name": "EMPTY SET WITH LEFT ARROW ABOVE", "token_cost": 1, "token_density": 1.0, "quality_score": 99.99999999999999, "llm_support": ["openai", "qwen", "llama"], "created": "2025-05-23", "generator": "fresh_512_v1"}, {"id": "NG0378", "symbol": "⇬", "code": "ng:reasoning:base_case", "fallback": "[BASECA]", "category": "reasoning", "meaning": "base_case", "description": "Reasoning operation: base_case", "unicode_point": "U+21EC", "unicode_name": "UPWARDS WHITE ARROW ON PEDESTAL WITH HORIZONTAL BAR", "token_cost": 1, "token_density": 1.0, "quality_score": 99.99999999999999, "llm_support": ["openai", "qwen", "llama"], "created": "2025-05-23", "generator": "fresh_512_v1"}, {"id": "NG0379", "symbol": "⩓", "code": "ng:reasoning:inductive_step", "fallback": "[INDUCT1]", "category": "reasoning", "meaning": "inductive_step", "description": "Reasoning operation: inductive_step", "unicode_point": "U+2A53", "unicode_name": "DOUBLE LOGICAL AND", "token_cost": 1, "token_density": 1.0, "quality_score": 99.99999999999999, "llm_support": ["openai", "qwen", "llama"], "created": "2025-05-23", "generator": "fresh_512_v1"}, {"id": "NG0380", "symbol": "⧴", "code": "ng:reasoning:generalize", "fallback": "[GENERA]", "category": "reasoning", "meaning": "generalize", "description": "Reasoning operation: generalize", "unicode_point": "U+29F4", "unicode_name": "RULE-DELAYED", "token_cost": 1, "token_density": 1.0, "quality_score": 99.99999999999999, "llm_support": ["openai", "qwen", "llama"], "created": "2025-05-23", "generator": "fresh_512_v1"}, {"id": "NG0381", "symbol": "⛋", "code": "ng:reasoning:pattern", "fallback": "[PATTER]", "category": "reasoning", "meaning": "pattern", "description": "Reasoning operation: pattern", "unicode_point": "U+26CB", "unicode_name": "WHITE DIAMOND IN SQUARE", "token_cost": 1, "token_density": 1.0, "quality_score": 99.99999999999999, "llm_support": ["openai", "qwen", "llama"], "created": "2025-05-23", "generator": "fresh_512_v1"}, {"id": "NG0382", "symbol": "⚧", "code": "ng:reasoning:deduce", "fallback": "[DEDUCE2]", "category": "reasoning", "meaning": "deduce", "description": "Reasoning operation: deduce", "unicode_point": "U+26A7", "unicode_name": "MALE WITH STROKE AND MALE AND FEMALE SIGN", "token_cost": 1, "token_density": 1.0, "quality_score": 99.99999999999999, "llm_support": ["openai", "qwen", "llama"], "created": "2025-05-23", "generator": "fresh_512_v1"}, {"id": "NG0383", "symbol": "♢", "code": "ng:reasoning:syllogism", "fallback": "[SYLLOG]", "category": "reasoning", "meaning": "syllogism", "description": "Reasoning operation: syllogism", "unicode_point": "U+2662", "unicode_name": "WHITE DIAMOND SUIT", "token_cost": 1, "token_density": 1.0, "quality_score": 99.99999999999999, "llm_support": ["openai", "qwen", "llama"], "created": "2025-05-23", "generator": "fresh_512_v1"}, {"id": "NG0384", "symbol": "♨", "code": "ng:reasoning:modus_ponens", "fallback": "[MODUSP]", "category": "reasoning", "meaning": "modus_ponens", "description": "Reasoning operation: modus_ponens", "unicode_point": "U+2668", "unicode_name": "HOT SPRINGS", "token_cost": 1, "token_density": 1.0, "quality_score": 99.99999999999999, "llm_support": ["openai", "qwen", "llama"], "created": "2025-05-23", "generator": "fresh_512_v1"}, {"id": "NG0385", "symbol": "↸", "code": "ng:reasoning:modus_tollens", "fallback": "[MODUST]", "category": "reasoning", "meaning": "modus_tollens", "description": "Reasoning operation: modus_tollens", "unicode_point": "U+21B8", "unicode_name": "NORTH WEST ARROW TO LONG BAR", "token_cost": 1, "token_density": 1.0, "quality_score": 99.99999999999999, "llm_support": ["openai", "qwen", "llama"], "created": "2025-05-23", "generator": "fresh_512_v1"}, {"id": "NG0386", "symbol": "⨭", "code": "ng:reasoning:contrapositive", "fallback": "[CONTRA]", "category": "reasoning", "meaning": "contrapositive", "description": "Reasoning operation: contrapositive", "unicode_point": "U+2A2D", "unicode_name": "PLUS SIGN IN LEFT HALF CIRCLE", "token_cost": 1, "token_density": 1.0, "quality_score": 99.99999999999999, "llm_support": ["openai", "qwen", "llama"], "created": "2025-05-23", "generator": "fresh_512_v1"}, {"id": "NG0387", "symbol": "★", "code": "ng:reasoning:analogous", "fallback": "[ANALOG]", "category": "reasoning", "meaning": "analogous", "description": "Reasoning operation: analogous", "unicode_point": "U+2605", "unicode_name": "BLACK STAR", "token_cost": 1, "token_density": 1.0, "quality_score": 99.99999999999999, "llm_support": ["openai", "qwen", "llama"], "created": "2025-05-23", "generator": "fresh_512_v1"}, {"id": "NG0388", "symbol": "◑", "code": "ng:reasoning:similar", "fallback": "[SIMILA]", "category": "reasoning", "meaning": "similar", "description": "Reasoning operation: similar", "unicode_point": "U+25D1", "unicode_name": "CIRCLE WITH RIGHT HALF BLACK", "token_cost": 1, "token_density": 1.0, "quality_score": 99.99999999999999, "llm_support": ["openai", "qwen", "llama"], "created": "2025-05-23", "generator": "fresh_512_v1"}, {"id": "NG0389", "symbol": "⧥", "code": "ng:reasoning:metaphor", "fallback": "[METAPH]", "category": "reasoning", "meaning": "metaphor", "description": "Reasoning operation: metaphor", "unicode_point": "U+29E5", "unicode_name": "IDENTICAL TO AND SLANTED PARALLEL", "token_cost": 1, "token_density": 1.0, "quality_score": 99.99999999999999, "llm_support": ["openai", "qwen", "llama"], "created": "2025-05-23", "generator": "fresh_512_v1"}, {"id": "NG0390", "symbol": "⧶", "code": "ng:reasoning:compare", "fallback": "[COMPAR]", "category": "reasoning", "meaning": "compare", "description": "Reasoning operation: compare", "unicode_point": "U+29F6", "unicode_name": "SOLIDUS WITH OVERBAR", "token_cost": 1, "token_density": 1.0, "quality_score": 99.99999999999999, "llm_support": ["openai", "qwen", "llama"], "created": "2025-05-23", "generator": "fresh_512_v1"}, {"id": "NG0391", "symbol": "♄", "code": "ng:reasoning:contrast", "fallback": "[CONTRA1]", "category": "reasoning", "meaning": "contrast", "description": "Reasoning operation: contrast", "unicode_point": "U+2644", "unicode_name": "SATURN", "token_cost": 1, "token_density": 1.0, "quality_score": 99.99999999999999, "llm_support": ["openai", "qwen", "llama"], "created": "2025-05-23", "generator": "fresh_512_v1"}, {"id": "NG0392", "symbol": "⋢", "code": "ng:reasoning:relate", "fallback": "[RELATE]", "category": "reasoning", "meaning": "relate", "description": "Reasoning operation: relate", "unicode_point": "U+22E2", "unicode_name": "NOT SQUARE IMAGE OF OR EQUAL TO", "token_cost": 1, "token_density": 1.0, "quality_score": 99.99999999999999, "llm_support": ["openai", "qwen", "llama"], "created": "2025-05-23", "generator": "fresh_512_v1"}, {"id": "NG0393", "symbol": "⍣", "code": "ng:reasoning:causality", "fallback": "[CAUSAL]", "category": "reasoning", "meaning": "causality", "description": "Reasoning operation: causality", "unicode_point": "U+2363", "unicode_name": "APL FUNCTIONAL SYMBOL STAR DIAERESIS", "token_cost": 1, "token_density": 1.0, "quality_score": 99.99999999999999, "llm_support": ["openai", "qwen", "llama"], "created": "2025-05-23", "generator": "fresh_512_v1"}, {"id": "NG0394", "symbol": "↕", "code": "ng:reasoning:correlation", "fallback": "[CORREL]", "category": "reasoning", "meaning": "correlation", "description": "Reasoning operation: correlation", "unicode_point": "U+2195", "unicode_name": "UP DOWN ARROW", "token_cost": 1, "token_density": 1.0, "quality_score": 99.99999999999999, "llm_support": ["openai", "qwen", "llama"], "created": "2025-05-23", "generator": "fresh_512_v1"}, {"id": "NG0395", "symbol": "◈", "code": "ng:reasoning:similarity", "fallback": "[SIMILA1]", "category": "reasoning", "meaning": "similarity", "description": "Reasoning operation: similarity", "unicode_point": "U+25C8", "unicode_name": "WHITE DIAMOND CONTAINING BLACK SMALL DIAMOND", "token_cost": 1, "token_density": 1.0, "quality_score": 99.99999999999999, "llm_support": ["openai", "qwen", "llama"], "created": "2025-05-23", "generator": "fresh_512_v1"}, {"id": "NG0396", "symbol": "⩟", "code": "ng:reasoning:difference", "fallback": "[DIFFER]", "category": "reasoning", "meaning": "difference", "description": "Reasoning operation: difference", "unicode_point": "U+2A5F", "unicode_name": "LOGICAL AND WITH UNDERBAR", "token_cost": 1, "token_density": 1.0, "quality_score": 99.99999999999999, "llm_support": ["openai", "qwen", "llama"], "created": "2025-05-23", "generator": "fresh_512_v1"}, {"id": "NG0397", "symbol": "⍌", "code": "ng:reasoning:abstraction", "fallback": "[ABSTRA2]", "category": "reasoning", "meaning": "abstraction", "description": "Reasoning operation: abstraction", "unicode_point": "U+234C", "unicode_name": "APL FUNCTIONAL SYMBOL QUAD DOWN CARET", "token_cost": 1, "token_density": 1.0, "quality_score": 99.99999999999999, "llm_support": ["openai", "qwen", "llama"], "created": "2025-05-23", "generator": "fresh_512_v1"}, {"id": "NG0398", "symbol": "↵", "code": "ng:reasoning:generalization", "fallback": "[GENERA1]", "category": "reasoning", "meaning": "generalization", "description": "Reasoning operation: generalization", "unicode_point": "U+21B5", "unicode_name": "DOWNWARDS ARROW WITH CORNER LEFTWARDS", "token_cost": 1, "token_density": 1.0, "quality_score": 99.99999999999999, "llm_support": ["openai", "qwen", "llama"], "created": "2025-05-23", "generator": "fresh_512_v1"}, {"id": "NG0399", "symbol": "⊝", "code": "ng:reasoning:specialization", "fallback": "[SPECIA]", "category": "reasoning", "meaning": "specialization", "description": "Reasoning operation: specialization", "unicode_point": "U+229D", "unicode_name": "CIRCLED DASH", "token_cost": 1, "token_density": 1.0, "quality_score": 99.99999999999999, "llm_support": ["openai", "qwen", "llama"], "created": "2025-05-23", "generator": "fresh_512_v1"}, {"id": "NG0400", "symbol": "∀", "code": "ng:reasoning:classification", "fallback": "[CLASSI]", "category": "reasoning", "meaning": "classification", "description": "Reasoning operation: classification", "unicode_point": "U+2200", "unicode_name": "FOR ALL", "token_cost": 1, "token_density": 1.0, "quality_score": 99.99999999999999, "llm_support": ["openai", "qwen", "llama"], "created": "2025-05-23", "generator": "fresh_512_v1"}, {"id": "NG0401", "symbol": "⪷", "code": "ng:reasoning:categorize", "fallback": "[CATEGO]", "category": "reasoning", "meaning": "categorize", "description": "Reasoning operation: categorize", "unicode_point": "U+2AB7", "unicode_name": "PRECEDES ABOVE ALMOST EQUAL TO", "token_cost": 1, "token_density": 1.0, "quality_score": 99.99999999999999, "llm_support": ["openai", "qwen", "llama"], "created": "2025-05-23", "generator": "fresh_512_v1"}, {"id": "NG0402", "symbol": "⋾", "code": "ng:reasoning:cluster", "fallback": "[CLUSTE]", "category": "reasoning", "meaning": "cluster", "description": "Reasoning operation: cluster", "unicode_point": "U+22FE", "unicode_name": "SMALL CONTAINS WITH OVERBAR", "token_cost": 1, "token_density": 1.0, "quality_score": 99.99999999999999, "llm_support": ["openai", "qwen", "llama"], "created": "2025-05-23", "generator": "fresh_512_v1"}, {"id": "NG0403", "symbol": "⨱", "code": "ng:reasoning:group", "fallback": "[GROUP]", "category": "reasoning", "meaning": "group", "description": "Reasoning operation: group", "unicode_point": "U+2A31", "unicode_name": "MULTIPLICATION SIGN WITH UNDERBAR", "token_cost": 1, "token_density": 1.0, "quality_score": 99.99999999999999, "llm_support": ["openai", "qwen", "llama"], "created": "2025-05-23", "generator": "fresh_512_v1"}, {"id": "NG0404", "symbol": "⨠", "code": "ng:reasoning:separate", "fallback": "[SEPARA]", "category": "reasoning", "meaning": "separate", "description": "Reasoning operation: separate", "unicode_point": "U+2A20", "unicode_name": "Z NOTATION SCHEMA PIPING", "token_cost": 1, "token_density": 1.0, "quality_score": 99.99999999999999, "llm_support": ["openai", "qwen", "llama"], "created": "2025-05-23", "generator": "fresh_512_v1"}, {"id": "NG0405", "symbol": "☠", "code": "ng:reasoning:analyze", "fallback": "[ANALYZ]", "category": "reasoning", "meaning": "analyze", "description": "Reasoning operation: analyze", "unicode_point": "U+2620", "unicode_name": "SKULL AND CROSSBONES", "token_cost": 1, "token_density": 1.0, "quality_score": 99.99999999999999, "llm_support": ["openai", "qwen", "llama"], "created": "2025-05-23", "generator": "fresh_512_v1"}, {"id": "NG0406", "symbol": "⌡", "code": "ng:reasoning:synthesize", "fallback": "[SYNTHE]", "category": "reasoning", "meaning": "synthesize", "description": "Reasoning operation: synthesize", "unicode_point": "U+2321", "unicode_name": "BOTTOM HALF INTEGRAL", "token_cost": 1, "token_density": 1.0, "quality_score": 99.99999999999999, "llm_support": ["openai", "qwen", "llama"], "created": "2025-05-23", "generator": "fresh_512_v1"}, {"id": "NG0407", "symbol": "⋟", "code": "ng:reasoning:evaluate", "fallback": "[EVALUA]", "category": "reasoning", "meaning": "evaluate", "description": "Reasoning operation: evaluate", "unicode_point": "U+22DF", "unicode_name": "EQUAL TO OR SUCCEEDS", "token_cost": 1, "token_density": 1.0, "quality_score": 99.99999999999999, "llm_support": ["openai", "qwen", "llama"], "created": "2025-05-23", "generator": "fresh_512_v1"}, {"id": "NG0408", "symbol": "⍋", "code": "ng:reasoning:judge", "fallback": "[JUDGE]", "category": "reasoning", "meaning": "judge", "description": "Reasoning operation: judge", "unicode_point": "U+234B", "unicode_name": "APL FUNCTIONAL SYMBOL DELTA STILE", "token_cost": 1, "token_density": 1.0, "quality_score": 99.99999999999999, "llm_support": ["openai", "qwen", "llama"], "created": "2025-05-23", "generator": "fresh_512_v1"}, {"id": "NG0409", "symbol": "⨰", "code": "ng:reasoning:decide", "fallback": "[DECIDE]", "category": "reasoning", "meaning": "decide", "description": "Reasoning operation: decide", "unicode_point": "U+2A30", "unicode_name": "MULTIPLICATION SIGN WITH DOT ABOVE", "token_cost": 1, "token_density": 1.0, "quality_score": 99.99999999999999, "llm_support": ["openai", "qwen", "llama"], "created": "2025-05-23", "generator": "fresh_512_v1"}, {"id": "NG0410", "symbol": "⎪", "code": "ng:reasoning:choose", "fallback": "[CHOOSE]", "category": "reasoning", "meaning": "choose", "description": "Reasoning operation: choose", "unicode_point": "U+23AA", "unicode_name": "CURLY BRACKET EXTENSION", "token_cost": 1, "token_density": 1.0, "quality_score": 99.99999999999999, "llm_support": ["openai", "qwen", "llama"], "created": "2025-05-23", "generator": "fresh_512_v1"}, {"id": "NG0411", "symbol": "⌾", "code": "ng:reasoning:select", "fallback": "[SELECT]", "category": "reasoning", "meaning": "select", "description": "Reasoning operation: select", "unicode_point": "U+233E", "unicode_name": "APL FUNCTIONAL SYMBOL CIRCLE JOT", "token_cost": 1, "token_density": 1.0, "quality_score": 99.99999999999999, "llm_support": ["openai", "qwen", "llama"], "created": "2025-05-23", "generator": "fresh_512_v1"}, {"id": "NG0412", "symbol": "⌚", "code": "ng:reasoning:prefer", "fallback": "[PREFER]", "category": "reasoning", "meaning": "prefer", "description": "Reasoning operation: prefer", "unicode_point": "U+231A", "unicode_name": "WATCH", "token_cost": 1, "token_density": 1.0, "quality_score": 99.99999999999999, "llm_support": ["openai", "qwen", "llama"], "created": "2025-05-23", "generator": "fresh_512_v1"}, {"id": "NG0413", "symbol": "♫", "code": "ng:reasoning:induct_1", "fallback": "[INDUCT2]", "category": "reasoning", "meaning": "induct", "description": "Reasoning operation: induct", "unicode_point": "U+266B", "unicode_name": "BEAMED EIGHTH NOTES", "token_cost": 1, "token_density": 1.0, "quality_score": 99.99999999999999, "llm_support": ["openai", "qwen", "llama"], "created": "2025-05-23", "generator": "fresh_512_v1"}, {"id": "NG0414", "symbol": "⏥", "code": "ng:reasoning:base_case_1", "fallback": "[BASECA1]", "category": "reasoning", "meaning": "base_case", "description": "Reasoning operation: base_case", "unicode_point": "U+23E5", "unicode_name": "FLATNESS", "token_cost": 1, "token_density": 1.0, "quality_score": 99.99999999999999, "llm_support": ["openai", "qwen", "llama"], "created": "2025-05-23", "generator": "fresh_512_v1"}, {"id": "NG0415", "symbol": "⇯", "code": "ng:reasoning:inductive_step_1", "fallback": "[INDUCT3]", "category": "reasoning", "meaning": "inductive_step", "description": "Reasoning operation: inductive_step", "unicode_point": "U+21EF", "unicode_name": "UPWARDS WHITE DOUBLE ARROW ON PEDESTAL", "token_cost": 1, "token_density": 1.0, "quality_score": 99.99999999999999, "llm_support": ["openai", "qwen", "llama"], "created": "2025-05-23", "generator": "fresh_512_v1"}, {"id": "NG0416", "symbol": "▼", "code": "ng:reasoning:generalize_1", "fallback": "[GENERA2]", "category": "reasoning", "meaning": "generalize", "description": "Reasoning operation: generalize", "unicode_point": "U+25BC", "unicode_name": "BLACK DOWN-POINTING TRIANGLE", "token_cost": 1, "token_density": 1.0, "quality_score": 99.99999999999999, "llm_support": ["openai", "qwen", "llama"], "created": "2025-05-23", "generator": "fresh_512_v1"}, {"id": "NG0417", "symbol": "⍧", "code": "ng:reasoning:pattern_1", "fallback": "[PATTER1]", "category": "reasoning", "meaning": "pattern", "description": "Reasoning operation: pattern", "unicode_point": "U+2367", "unicode_name": "APL FUNCTIONAL SYMBOL LEFT SHOE STILE", "token_cost": 1, "token_density": 1.0, "quality_score": 99.99999999999999, "llm_support": ["openai", "qwen", "llama"], "created": "2025-05-23", "generator": "fresh_512_v1"}, {"id": "NG0418", "symbol": "☰", "code": "ng:reasoning:deduce_1", "fallback": "[DEDUCE3]", "category": "reasoning", "meaning": "deduce", "description": "Reasoning operation: deduce", "unicode_point": "U+2630", "unicode_name": "TRIGRAM FOR HEAVEN", "token_cost": 1, "token_density": 1.0, "quality_score": 99.99999999999999, "llm_support": ["openai", "qwen", "llama"], "created": "2025-05-23", "generator": "fresh_512_v1"}, {"id": "NG0419", "symbol": "⛥", "code": "ng:reasoning:syllogism_1", "fallback": "[SYLLOG1]", "category": "reasoning", "meaning": "syllogism", "description": "Reasoning operation: syllogism", "unicode_point": "U+26E5", "unicode_name": "RIGHT-<PERSON><PERSON><PERSON>D INTERLACED PENTAGRAM", "token_cost": 1, "token_density": 1.0, "quality_score": 99.99999999999999, "llm_support": ["openai", "qwen", "llama"], "created": "2025-05-23", "generator": "fresh_512_v1"}, {"id": "NG0420", "symbol": "▫", "code": "ng:reasoning:modus_ponens_1", "fallback": "[MODUSP1]", "category": "reasoning", "meaning": "modus_ponens", "description": "Reasoning operation: modus_ponens", "unicode_point": "U+25AB", "unicode_name": "WHITE SMALL SQUARE", "token_cost": 1, "token_density": 1.0, "quality_score": 99.99999999999999, "llm_support": ["openai", "qwen", "llama"], "created": "2025-05-23", "generator": "fresh_512_v1"}, {"id": "NG0421", "symbol": "⧁", "code": "ng:reasoning:modus_tollens_1", "fallback": "[MODUST1]", "category": "reasoning", "meaning": "modus_tollens", "description": "Reasoning operation: modus_tollens", "unicode_point": "U+29C1", "unicode_name": "CIRCLED GREATER-THAN", "token_cost": 1, "token_density": 1.0, "quality_score": 99.99999999999999, "llm_support": ["openai", "qwen", "llama"], "created": "2025-05-23", "generator": "fresh_512_v1"}, {"id": "NG0422", "symbol": "⇿", "code": "ng:reasoning:contrapositive_1", "fallback": "[CONTRA2]", "category": "reasoning", "meaning": "contrapositive", "description": "Reasoning operation: contrapositive", "unicode_point": "U+21FF", "unicode_name": "LEFT RIGHT OPEN-HEADED ARROW", "token_cost": 1, "token_density": 1.0, "quality_score": 99.99999999999999, "llm_support": ["openai", "qwen", "llama"], "created": "2025-05-23", "generator": "fresh_512_v1"}, {"id": "NG0423", "symbol": "◺", "code": "ng:reasoning:analogous_1", "fallback": "[ANALOG1]", "category": "reasoning", "meaning": "analogous", "description": "Reasoning operation: analogous", "unicode_point": "U+25FA", "unicode_name": "LOWER LEFT TRIANGLE", "token_cost": 1, "token_density": 1.0, "quality_score": 99.99999999999999, "llm_support": ["openai", "qwen", "llama"], "created": "2025-05-23", "generator": "fresh_512_v1"}, {"id": "NG0424", "symbol": "◗", "code": "ng:reasoning:similar_1", "fallback": "[SIMILA2]", "category": "reasoning", "meaning": "similar", "description": "Reasoning operation: similar", "unicode_point": "U+25D7", "unicode_name": "RIGHT HALF BLACK CIRCLE", "token_cost": 1, "token_density": 1.0, "quality_score": 99.99999999999999, "llm_support": ["openai", "qwen", "llama"], "created": "2025-05-23", "generator": "fresh_512_v1"}, {"id": "NG0425", "symbol": "⊠", "code": "ng:reasoning:metaphor_1", "fallback": "[METAPH1]", "category": "reasoning", "meaning": "metaphor", "description": "Reasoning operation: metaphor", "unicode_point": "U+22A0", "unicode_name": "SQUARED TIMES", "token_cost": 1, "token_density": 1.0, "quality_score": 99.99999999999999, "llm_support": ["openai", "qwen", "llama"], "created": "2025-05-23", "generator": "fresh_512_v1"}, {"id": "NG0426", "symbol": "⟀", "code": "ng:reasoning:compare_1", "fallback": "[COMPAR1]", "category": "reasoning", "meaning": "compare", "description": "Reasoning operation: compare", "unicode_point": "U+27C0", "unicode_name": "THREE DIMENSIONAL ANGLE", "token_cost": 1, "token_density": 1.0, "quality_score": 99.99999999999999, "llm_support": ["openai", "qwen", "llama"], "created": "2025-05-23", "generator": "fresh_512_v1"}, {"id": "NG0427", "symbol": "⟜", "code": "ng:reasoning:contrast_1", "fallback": "[CONTRA3]", "category": "reasoning", "meaning": "contrast", "description": "Reasoning operation: contrast", "unicode_point": "U+27DC", "unicode_name": "LEFT MULTIMAP", "token_cost": 1, "token_density": 1.0, "quality_score": 99.99999999999999, "llm_support": ["openai", "qwen", "llama"], "created": "2025-05-23", "generator": "fresh_512_v1"}, {"id": "NG0428", "symbol": "♸", "code": "ng:reasoning:relate_1", "fallback": "[RELATE1]", "category": "reasoning", "meaning": "relate", "description": "Reasoning operation: relate", "unicode_point": "U+2678", "unicode_name": "RECYCLING <PERSON><PERSON><PERSON><PERSON> FOR TYPE-6 PLASTICS", "token_cost": 1, "token_density": 1.0, "quality_score": 99.99999999999999, "llm_support": ["openai", "qwen", "llama"], "created": "2025-05-23", "generator": "fresh_512_v1"}, {"id": "NG0429", "symbol": "⨜", "code": "ng:reasoning:causality_1", "fallback": "[CAUSAL1]", "category": "reasoning", "meaning": "causality", "description": "Reasoning operation: causality", "unicode_point": "U+2A1C", "unicode_name": "INTEGRAL WITH UNDERBAR", "token_cost": 1, "token_density": 1.0, "quality_score": 99.99999999999999, "llm_support": ["openai", "qwen", "llama"], "created": "2025-05-23", "generator": "fresh_512_v1"}, {"id": "NG0430", "symbol": "⏎", "code": "ng:reasoning:correlation_1", "fallback": "[CORREL1]", "category": "reasoning", "meaning": "correlation", "description": "Reasoning operation: correlation", "unicode_point": "U+23CE", "unicode_name": "RETURN SYMBOL", "token_cost": 1, "token_density": 1.0, "quality_score": 99.99999999999999, "llm_support": ["openai", "qwen", "llama"], "created": "2025-05-23", "generator": "fresh_512_v1"}, {"id": "NG0431", "symbol": "►", "code": "ng:reasoning:similarity_1", "fallback": "[SIMILA3]", "category": "reasoning", "meaning": "similarity", "description": "Reasoning operation: similarity", "unicode_point": "U+25BA", "unicode_name": "BLACK RIGHT-POINTING POINTER", "token_cost": 1, "token_density": 1.0, "quality_score": 99.99999999999999, "llm_support": ["openai", "qwen", "llama"], "created": "2025-05-23", "generator": "fresh_512_v1"}, {"id": "NG0432", "symbol": "⧻", "code": "ng:reasoning:difference_1", "fallback": "[DIFFER1]", "category": "reasoning", "meaning": "difference", "description": "Reasoning operation: difference", "unicode_point": "U+29FB", "unicode_name": "TRIPLE PLUS", "token_cost": 1, "token_density": 1.0, "quality_score": 99.99999999999999, "llm_support": ["openai", "qwen", "llama"], "created": "2025-05-23", "generator": "fresh_512_v1"}, {"id": "NG0433", "symbol": "〈", "code": "ng:meta:reflect", "fallback": "[REFLEC]", "category": "meta", "meaning": "reflect", "description": "Meta operation: reflect", "unicode_point": "U+2329", "unicode_name": "LEFT-POINTING ANGLE BRACKET", "token_cost": 1, "token_density": 1.0, "quality_score": 99.99999999999999, "llm_support": ["openai", "qwen", "llama"], "created": "2025-05-23", "generator": "fresh_512_v1"}, {"id": "NG0434", "symbol": "⫹", "code": "ng:meta:introspect", "fallback": "[INTROS]", "category": "meta", "meaning": "introspect", "description": "Meta operation: introspect", "unicode_point": "U+2AF9", "unicode_name": "DOUBLE-LIN<PERSON> SLANTED LESS-THAN OR EQUAL TO", "token_cost": 1, "token_density": 1.0, "quality_score": 99.99999999999999, "llm_support": ["openai", "qwen", "llama"], "created": "2025-05-23", "generator": "fresh_512_v1"}, {"id": "NG0435", "symbol": "⟑", "code": "ng:meta:meta", "fallback": "[META]", "category": "meta", "meaning": "meta", "description": "Meta operation: meta", "unicode_point": "U+27D1", "unicode_name": "AND WITH DOT", "token_cost": 1, "token_density": 1.0, "quality_score": 99.99999999999999, "llm_support": ["openai", "qwen", "llama"], "created": "2025-05-23", "generator": "fresh_512_v1"}, {"id": "NG0436", "symbol": "⊣", "code": "ng:meta:self", "fallback": "[SELF]", "category": "meta", "meaning": "self", "description": "Meta operation: self", "unicode_point": "U+22A3", "unicode_name": "LEFT TACK", "token_cost": 1, "token_density": 1.0, "quality_score": 99.99999999999999, "llm_support": ["openai", "qwen", "llama"], "created": "2025-05-23", "generator": "fresh_512_v1"}, {"id": "NG0437", "symbol": "⏖", "code": "ng:meta:type_of", "fallback": "[TYPEOF]", "category": "meta", "meaning": "type_of", "description": "Meta operation: type_of", "unicode_point": "U+23D6", "unicode_name": "METRICAL TWO SHORTS JOINED", "token_cost": 1, "token_density": 1.0, "quality_score": 99.99999999999999, "llm_support": ["openai", "qwen", "llama"], "created": "2025-05-23", "generator": "fresh_512_v1"}, {"id": "NG0438", "symbol": "↶", "code": "ng:meta:instance_of", "fallback": "[INSTAN2]", "category": "meta", "meaning": "instance_of", "description": "Meta operation: instance_of", "unicode_point": "U+21B6", "unicode_name": "ANTICLOCKWISE TOP SEMICIRCLE ARROW", "token_cost": 1, "token_density": 1.0, "quality_score": 99.99999999999999, "llm_support": ["openai", "qwen", "llama"], "created": "2025-05-23", "generator": "fresh_512_v1"}, {"id": "NG0439", "symbol": "⟏", "code": "ng:meta:recurse", "fallback": "[RECURS]", "category": "meta", "meaning": "recurse", "description": "Meta operation: recurse", "unicode_point": "U+27CF", "unicode_name": "SQUARED LOGICAL OR", "token_cost": 1, "token_density": 1.0, "quality_score": 99.99999999999999, "llm_support": ["openai", "qwen", "llama"], "created": "2025-05-23", "generator": "fresh_512_v1"}, {"id": "NG0440", "symbol": "⇛", "code": "ng:meta:fixpoint", "fallback": "[FIXPOI]", "category": "meta", "meaning": "fixpoint", "description": "Meta operation: fixpoint", "unicode_point": "U+21DB", "unicode_name": "RIGHTWARDS TRIPLE ARROW", "token_cost": 1, "token_density": 1.0, "quality_score": 99.99999999999999, "llm_support": ["openai", "qwen", "llama"], "created": "2025-05-23", "generator": "fresh_512_v1"}, {"id": "NG0441", "symbol": "⍥", "code": "ng:meta:iterate", "fallback": "[ITERAT]", "category": "meta", "meaning": "iterate", "description": "Meta operation: iterate", "unicode_point": "U+2365", "unicode_name": "APL FUNCTIONAL SYMBOL CIRCLE DIAERESIS", "token_cost": 1, "token_density": 1.0, "quality_score": 99.99999999999999, "llm_support": ["openai", "qwen", "llama"], "created": "2025-05-23", "generator": "fresh_512_v1"}, {"id": "NG0442", "symbol": "⎢", "code": "ng:meta:unfold", "fallback": "[UNFOLD]", "category": "meta", "meaning": "unfold", "description": "Meta operation: unfold", "unicode_point": "U+23A2", "unicode_name": "LEFT SQUARE BRACKET EXTENSION", "token_cost": 1, "token_density": 1.0, "quality_score": 99.99999999999999, "llm_support": ["openai", "qwen", "llama"], "created": "2025-05-23", "generator": "fresh_512_v1"}, {"id": "NG0443", "symbol": "⚜", "code": "ng:meta:fold", "fallback": "[FOLD]", "category": "meta", "meaning": "fold", "description": "Meta operation: fold", "unicode_point": "U+269C", "unicode_name": "FLEUR-DE-LIS", "token_cost": 1, "token_density": 1.0, "quality_score": 99.99999999999999, "llm_support": ["openai", "qwen", "llama"], "created": "2025-05-23", "generator": "fresh_512_v1"}, {"id": "NG0444", "symbol": "⋗", "code": "ng:meta:reduce", "fallback": "[REDUCE]", "category": "meta", "meaning": "reduce", "description": "Meta operation: reduce", "unicode_point": "U+22D7", "unicode_name": "GREATER-THAN WITH DOT", "token_cost": 1, "token_density": 1.0, "quality_score": 99.99999999999999, "llm_support": ["openai", "qwen", "llama"], "created": "2025-05-23", "generator": "fresh_512_v1"}, {"id": "NG0445", "symbol": "⩡", "code": "ng:meta:compile", "fallback": "[COMPIL]", "category": "meta", "meaning": "compile", "description": "Meta operation: compile", "unicode_point": "U+2A61", "unicode_name": "SMALL VEE WITH UNDERBAR", "token_cost": 1, "token_density": 1.0, "quality_score": 99.99999999999999, "llm_support": ["openai", "qwen", "llama"], "created": "2025-05-23", "generator": "fresh_512_v1"}, {"id": "NG0446", "symbol": "⊺", "code": "ng:meta:parse", "fallback": "[PARSE]", "category": "meta", "meaning": "parse", "description": "Meta operation: parse", "unicode_point": "U+22BA", "unicode_name": "INTERCALATE", "token_cost": 1, "token_density": 1.0, "quality_score": 99.99999999999999, "llm_support": ["openai", "qwen", "llama"], "created": "2025-05-23", "generator": "fresh_512_v1"}, {"id": "NG0447", "symbol": "∬", "code": "ng:meta:lex", "fallback": "[LEX]", "category": "meta", "meaning": "lex", "description": "Meta operation: lex", "unicode_point": "U+222C", "unicode_name": "DOUBLE INTEGRAL", "token_cost": 1, "token_density": 1.0, "quality_score": 99.99999999999999, "llm_support": ["openai", "qwen", "llama"], "created": "2025-05-23", "generator": "fresh_512_v1"}, {"id": "NG0448", "symbol": "⇨", "code": "ng:meta:optimize", "fallback": "[OPTIMI]", "category": "meta", "meaning": "optimize", "description": "Meta operation: optimize", "unicode_point": "U+21E8", "unicode_name": "RIGHTWARDS WHITE ARROW", "token_cost": 1, "token_density": 1.0, "quality_score": 99.99999999999999, "llm_support": ["openai", "qwen", "llama"], "created": "2025-05-23", "generator": "fresh_512_v1"}, {"id": "NG0449", "symbol": "≢", "code": "ng:meta:transform", "fallback": "[TRANSF]", "category": "meta", "meaning": "transform", "description": "Meta operation: transform", "unicode_point": "U+2262", "unicode_name": "NOT IDENTICAL TO", "token_cost": 1, "token_density": 1.0, "quality_score": 99.99999999999999, "llm_support": ["openai", "qwen", "llama"], "created": "2025-05-23", "generator": "fresh_512_v1"}, {"id": "NG0450", "symbol": "⌷", "code": "ng:meta:generate", "fallback": "[GENERA3]", "category": "meta", "meaning": "generate", "description": "Meta operation: generate", "unicode_point": "U+2337", "unicode_name": "APL FUNCTIONAL SYMBOL SQUISH QUAD", "token_cost": 1, "token_density": 1.0, "quality_score": 99.99999999999999, "llm_support": ["openai", "qwen", "llama"], "created": "2025-05-23", "generator": "fresh_512_v1"}, {"id": "NG0451", "symbol": "⍡", "code": "ng:meta:interpret", "fallback": "[INTERP]", "category": "meta", "meaning": "interpret", "description": "Meta operation: interpret", "unicode_point": "U+2361", "unicode_name": "APL FUNCTIONAL SYMBOL UP TACK DIAERESIS", "token_cost": 1, "token_density": 1.0, "quality_score": 99.99999999999999, "llm_support": ["openai", "qwen", "llama"], "created": "2025-05-23", "generator": "fresh_512_v1"}, {"id": "NG0452", "symbol": "⧀", "code": "ng:meta:evaluate", "fallback": "[EVALUA1]", "category": "meta", "meaning": "evaluate", "description": "Meta operation: evaluate", "unicode_point": "U+29C0", "unicode_name": "CIRCLED LESS-THAN", "token_cost": 1, "token_density": 1.0, "quality_score": 99.99999999999999, "llm_support": ["openai", "qwen", "llama"], "created": "2025-05-23", "generator": "fresh_512_v1"}, {"id": "NG0453", "symbol": "⌖", "code": "ng:meta:debug", "fallback": "[DEBUG]", "category": "meta", "meaning": "debug", "description": "Meta operation: debug", "unicode_point": "U+2316", "unicode_name": "POSITION INDICATOR", "token_cost": 1, "token_density": 1.0, "quality_score": 99.99999999999999, "llm_support": ["openai", "qwen", "llama"], "created": "2025-05-23", "generator": "fresh_512_v1"}, {"id": "NG0454", "symbol": "⟕", "code": "ng:meta:profile", "fallback": "[PROFIL]", "category": "meta", "meaning": "profile", "description": "Meta operation: profile", "unicode_point": "U+27D5", "unicode_name": "LEFT OUTER JOIN", "token_cost": 1, "token_density": 1.0, "quality_score": 99.99999999999999, "llm_support": ["openai", "qwen", "llama"], "created": "2025-05-23", "generator": "fresh_512_v1"}, {"id": "NG0455", "symbol": "⫾", "code": "ng:meta:trace", "fallback": "[TRACE]", "category": "meta", "meaning": "trace", "description": "Meta operation: trace", "unicode_point": "U+2AFE", "unicode_name": "WHITE VERTICAL BAR", "token_cost": 1, "token_density": 1.0, "quality_score": 99.99999999999999, "llm_support": ["openai", "qwen", "llama"], "created": "2025-05-23", "generator": "fresh_512_v1"}, {"id": "NG0456", "symbol": "⛎", "code": "ng:meta:monitor", "fallback": "[MONITO]", "category": "meta", "meaning": "monitor", "description": "Meta operation: monitor", "unicode_point": "U+26CE", "unicode_name": "OPHIUCHUS", "token_cost": 1, "token_density": 1.0, "quality_score": 99.99999999999999, "llm_support": ["openai", "qwen", "llama"], "created": "2025-05-23", "generator": "fresh_512_v1"}, {"id": "NG0457", "symbol": "⦻", "code": "ng:meta:measure", "fallback": "[MEASUR]", "category": "meta", "meaning": "measure", "description": "Meta operation: measure", "unicode_point": "U+29BB", "unicode_name": "CIRCLE WITH SUPERIMPOSED X", "token_cost": 1, "token_density": 1.0, "quality_score": 99.99999999999999, "llm_support": ["openai", "qwen", "llama"], "created": "2025-05-23", "generator": "fresh_512_v1"}, {"id": "NG0458", "symbol": "⋵", "code": "ng:meta:benchmark", "fallback": "[BENCHM]", "category": "meta", "meaning": "benchmark", "description": "Meta operation: benchmark", "unicode_point": "U+22F5", "unicode_name": "ELEMENT OF WITH DOT ABOVE", "token_cost": 1, "token_density": 1.0, "quality_score": 99.99999999999999, "llm_support": ["openai", "qwen", "llama"], "created": "2025-05-23", "generator": "fresh_512_v1"}, {"id": "NG0459", "symbol": "∝", "code": "ng:meta:test", "fallback": "[TEST]", "category": "meta", "meaning": "test", "description": "Meta operation: test", "unicode_point": "U+221D", "unicode_name": "PROPORTIONAL TO", "token_cost": 1, "token_density": 1.0, "quality_score": 99.99999999999999, "llm_support": ["openai", "qwen", "llama"], "created": "2025-05-23", "generator": "fresh_512_v1"}, {"id": "NG0460", "symbol": "⍙", "code": "ng:meta:verify", "fallback": "[VERIFY]", "category": "meta", "meaning": "verify", "description": "Meta operation: verify", "unicode_point": "U+2359", "unicode_name": "APL FUNCTIONAL SYMBOL DELTA UNDERBAR", "token_cost": 1, "token_density": 1.0, "quality_score": 99.99999999999999, "llm_support": ["openai", "qwen", "llama"], "created": "2025-05-23", "generator": "fresh_512_v1"}, {"id": "NG0461", "symbol": "∟", "code": "ng:meta:validate", "fallback": "[VALIDA]", "category": "meta", "meaning": "validate", "description": "Meta operation: validate", "unicode_point": "U+221F", "unicode_name": "RIGHT ANGLE", "token_cost": 1, "token_density": 1.0, "quality_score": 99.99999999999999, "llm_support": ["openai", "qwen", "llama"], "created": "2025-05-23", "generator": "fresh_512_v1"}, {"id": "NG0462", "symbol": "⩳", "code": "ng:meta:check", "fallback": "[CHECK]", "category": "meta", "meaning": "check", "description": "Meta operation: check", "unicode_point": "U+2A73", "unicode_name": "EQUALS SIGN ABOVE TILDE OPERATOR", "token_cost": 1, "token_density": 1.0, "quality_score": 99.99999999999999, "llm_support": ["openai", "qwen", "llama"], "created": "2025-05-23", "generator": "fresh_512_v1"}, {"id": "NG0463", "symbol": "⨴", "code": "ng:meta:assert", "fallback": "[ASSERT]", "category": "meta", "meaning": "assert", "description": "Meta operation: assert", "unicode_point": "U+2A34", "unicode_name": "MULTIPLICATION SIGN IN LEFT HALF CIRCLE", "token_cost": 1, "token_density": 1.0, "quality_score": 99.99999999999999, "llm_support": ["openai", "qwen", "llama"], "created": "2025-05-23", "generator": "fresh_512_v1"}, {"id": "NG0464", "symbol": "≰", "code": "ng:meta:ensure", "fallback": "[ENSURE]", "category": "meta", "meaning": "ensure", "description": "Meta operation: ensure", "unicode_point": "U+2270", "unicode_name": "NEITHER LESS-THAN NOR EQUAL TO", "token_cost": 1, "token_density": 1.0, "quality_score": 99.99999999999999, "llm_support": ["openai", "qwen", "llama"], "created": "2025-05-23", "generator": "fresh_512_v1"}, {"id": "NG0465", "symbol": "⏅", "code": "ng:meta:guarantee", "fallback": "[GUARAN]", "category": "meta", "meaning": "guarantee", "description": "Meta operation: guarantee", "unicode_point": "U+23C5", "unicode_name": "DENTISTRY SYMBOL LIGHT UP AND <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> WITH TRIANGLE", "token_cost": 1, "token_density": 1.0, "quality_score": 99.99999999999999, "llm_support": ["openai", "qwen", "llama"], "created": "2025-05-23", "generator": "fresh_512_v1"}, {"id": "NG0466", "symbol": "⪀", "code": "ng:meta:prove", "fallback": "[PROVE]", "category": "meta", "meaning": "prove", "description": "Meta operation: prove", "unicode_point": "U+2A80", "unicode_name": "GREATER-THAN OR SLANTED EQUAL TO WITH DOT INSIDE", "token_cost": 1, "token_density": 1.0, "quality_score": 99.99999999999999, "llm_support": ["openai", "qwen", "llama"], "created": "2025-05-23", "generator": "fresh_512_v1"}, {"id": "NG0467", "symbol": "⇓", "code": "ng:meta:disprove", "fallback": "[DISPRO]", "category": "meta", "meaning": "disprove", "description": "Meta operation: disprove", "unicode_point": "U+21D3", "unicode_name": "DOWNWARDS DOUBLE ARROW", "token_cost": 1, "token_density": 1.0, "quality_score": 99.99999999999999, "llm_support": ["openai", "qwen", "llama"], "created": "2025-05-23", "generator": "fresh_512_v1"}, {"id": "NG0468", "symbol": "⧹", "code": "ng:meta:refute", "fallback": "[REFUTE]", "category": "meta", "meaning": "refute", "description": "Meta operation: refute", "unicode_point": "U+29F9", "unicode_name": "BIG REVERSE SOLIDUS", "token_cost": 1, "token_density": 1.0, "quality_score": 99.99999999999999, "llm_support": ["openai", "qwen", "llama"], "created": "2025-05-23", "generator": "fresh_512_v1"}, {"id": "NG0469", "symbol": "♙", "code": "ng:meta:reflect_1", "fallback": "[REFLEC1]", "category": "meta", "meaning": "reflect", "description": "Meta operation: reflect", "unicode_point": "U+2659", "unicode_name": "WHITE CHESS PAWN", "token_cost": 1, "token_density": 1.0, "quality_score": 99.99999999999999, "llm_support": ["openai", "qwen", "llama"], "created": "2025-05-23", "generator": "fresh_512_v1"}, {"id": "NG0470", "symbol": "⩠", "code": "ng:meta:introspect_1", "fallback": "[INTROS1]", "category": "meta", "meaning": "introspect", "description": "Meta operation: introspect", "unicode_point": "U+2A60", "unicode_name": "LOGICAL AND WITH DOUBLE UNDERBAR", "token_cost": 1, "token_density": 1.0, "quality_score": 99.99999999999999, "llm_support": ["openai", "qwen", "llama"], "created": "2025-05-23", "generator": "fresh_512_v1"}, {"id": "NG0471", "symbol": "⧺", "code": "ng:meta:meta_1", "fallback": "[META1]", "category": "meta", "meaning": "meta", "description": "Meta operation: meta", "unicode_point": "U+29FA", "unicode_name": "DOUBLE PLUS", "token_cost": 1, "token_density": 1.0, "quality_score": 99.99999999999999, "llm_support": ["openai", "qwen", "llama"], "created": "2025-05-23", "generator": "fresh_512_v1"}, {"id": "NG0472", "symbol": "↠", "code": "ng:meta:self_1", "fallback": "[SELF1]", "category": "meta", "meaning": "self", "description": "Meta operation: self", "unicode_point": "U+21A0", "unicode_name": "RIGHTWARDS TWO HEADED ARROW", "token_cost": 1, "token_density": 1.0, "quality_score": 99.99999999999999, "llm_support": ["openai", "qwen", "llama"], "created": "2025-05-23", "generator": "fresh_512_v1"}, {"id": "NG0473", "symbol": "⫯", "code": "ng:meta:type_of_1", "fallback": "[TYPEOF1]", "category": "meta", "meaning": "type_of", "description": "Meta operation: type_of", "unicode_point": "U+2AEF", "unicode_name": "VERTICAL LINE WITH CIRCLE ABOVE", "token_cost": 1, "token_density": 1.0, "quality_score": 99.99999999999999, "llm_support": ["openai", "qwen", "llama"], "created": "2025-05-23", "generator": "fresh_512_v1"}, {"id": "NG0474", "symbol": "⋴", "code": "ng:meta:instance_of_1", "fallback": "[INSTAN3]", "category": "meta", "meaning": "instance_of", "description": "Meta operation: instance_of", "unicode_point": "U+22F4", "unicode_name": "SMALL ELEMENT OF WITH VERTICAL BAR AT END OF HORIZONTAL STROKE", "token_cost": 1, "token_density": 1.0, "quality_score": 99.99999999999999, "llm_support": ["openai", "qwen", "llama"], "created": "2025-05-23", "generator": "fresh_512_v1"}, {"id": "NG0475", "symbol": "⊞", "code": "ng:meta:recurse_1", "fallback": "[RECURS1]", "category": "meta", "meaning": "recurse", "description": "Meta operation: recurse", "unicode_point": "U+229E", "unicode_name": "SQUARED PLUS", "token_cost": 1, "token_density": 1.0, "quality_score": 99.99999999999999, "llm_support": ["openai", "qwen", "llama"], "created": "2025-05-23", "generator": "fresh_512_v1"}, {"id": "NG0476", "symbol": "⚕", "code": "ng:meta:fixpoint_1", "fallback": "[FIXPOI1]", "category": "meta", "meaning": "fixpoint", "description": "Meta operation: fixpoint", "unicode_point": "U+2695", "unicode_name": "STAFF OF AESCULAPIUS", "token_cost": 1, "token_density": 1.0, "quality_score": 99.99999999999999, "llm_support": ["openai", "qwen", "llama"], "created": "2025-05-23", "generator": "fresh_512_v1"}, {"id": "NG0477", "symbol": "⊧", "code": "ng:meta:iterate_1", "fallback": "[ITERAT1]", "category": "meta", "meaning": "iterate", "description": "Meta operation: iterate", "unicode_point": "U+22A7", "unicode_name": "MODELS", "token_cost": 1, "token_density": 1.0, "quality_score": 99.99999999999999, "llm_support": ["openai", "qwen", "llama"], "created": "2025-05-23", "generator": "fresh_512_v1"}, {"id": "NG0478", "symbol": "⌮", "code": "ng:meta:unfold_1", "fallback": "[UNFOLD1]", "category": "meta", "meaning": "unfold", "description": "Meta operation: unfold", "unicode_point": "U+232E", "unicode_name": "ALL AROUND-PROFILE", "token_cost": 1, "token_density": 1.0, "quality_score": 99.99999999999999, "llm_support": ["openai", "qwen", "llama"], "created": "2025-05-23", "generator": "fresh_512_v1"}, {"id": "NG0479", "symbol": "⎊", "code": "ng:meta:fold_1", "fallback": "[FOLD1]", "category": "meta", "meaning": "fold", "description": "Meta operation: fold", "unicode_point": "U+238A", "unicode_name": "CIRCLED TRIANGLE DOWN", "token_cost": 1, "token_density": 1.0, "quality_score": 99.99999999999999, "llm_support": ["openai", "qwen", "llama"], "created": "2025-05-23", "generator": "fresh_512_v1"}, {"id": "NG0480", "symbol": "☦", "code": "ng:meta:reduce_1", "fallback": "[REDUCE1]", "category": "meta", "meaning": "reduce", "description": "Meta operation: reduce", "unicode_point": "U+2626", "unicode_name": "ORTHODOX CROSS", "token_cost": 1, "token_density": 1.0, "quality_score": 99.99999999999999, "llm_support": ["openai", "qwen", "llama"], "created": "2025-05-23", "generator": "fresh_512_v1"}, {"id": "NG0481", "symbol": "↲", "code": "ng:quantum:pauli_y", "fallback": "[PAULIY]", "category": "quantum", "meaning": "pauli_y", "description": "Quantum operation: pauli_y", "unicode_point": "U+21B2", "unicode_name": "DOWNWARDS ARROW WITH TIP LEFTWARDS", "token_cost": 1, "token_density": 1.0, "quality_score": 99.99999999999999, "llm_support": ["openai", "qwen", "llama"], "created": "2025-05-23", "generator": "fresh_512_v1"}, {"id": "NG0482", "symbol": "⍅", "code": "ng:quantum:cnot", "fallback": "[CNOT]", "category": "quantum", "meaning": "cnot", "description": "Quantum operation: cnot", "unicode_point": "U+2345", "unicode_name": "APL FUNCTIONAL SYMBOL LEFTWARDS VANE", "token_cost": 1, "token_density": 1.0, "quality_score": 99.99999999999999, "llm_support": ["openai", "qwen", "llama"], "created": "2025-05-23", "generator": "fresh_512_v1"}, {"id": "NG0483", "symbol": "⧾", "code": "ng:quantum:state", "fallback": "[STATE]", "category": "quantum", "meaning": "state", "description": "Quantum operation: state", "unicode_point": "U+29FE", "unicode_name": "TINY", "token_cost": 1, "token_density": 1.0, "quality_score": 99.99999999999999, "llm_support": ["openai", "qwen", "llama"], "created": "2025-05-23", "generator": "fresh_512_v1"}, {"id": "NG0484", "symbol": "⟨", "code": "ng:quantum:fredkin", "fallback": "[FREDKI]", "category": "quantum", "meaning": "fredkin", "description": "Quantum operation: fredkin", "unicode_point": "U+27E8", "unicode_name": "MATHEMATICAL LEFT ANGLE BRACKET", "token_cost": 1, "token_density": 1.0, "quality_score": 99.99999999999999, "llm_support": ["openai", "qwen", "llama"], "created": "2025-05-23", "generator": "fresh_512_v1"}, {"id": "NG0485", "symbol": "⧜", "code": "ng:quantum:uncertainty", "fallback": "[UNCERT]", "category": "quantum", "meaning": "uncertainty", "description": "Quantum operation: uncertainty", "unicode_point": "U+29DC", "unicode_name": "INCOMPLETE INFINITY", "token_cost": 1, "token_density": 1.0, "quality_score": 99.99999999999999, "llm_support": ["openai", "qwen", "llama"], "created": "2025-05-23", "generator": "fresh_512_v1"}, {"id": "NG0486", "symbol": "≿", "code": "ng:quantum:amplitude", "fallback": "[AMPLIT]", "category": "quantum", "meaning": "amplitude", "description": "Quantum operation: amplitude", "unicode_point": "U+227F", "unicode_name": "SUCCEEDS OR EQUIVALENT TO", "token_cost": 1, "token_density": 1.0, "quality_score": 99.99999999999999, "llm_support": ["openai", "qwen", "llama"], "created": "2025-05-23", "generator": "fresh_512_v1"}, {"id": "NG0487", "symbol": "⇗", "code": "ng:quantum:interfere", "fallback": "[INTERF2]", "category": "quantum", "meaning": "interfere", "description": "Quantum operation: interfere", "unicode_point": "U+21D7", "unicode_name": "NORTH EAST DOUBLE ARROW", "token_cost": 1, "token_density": 1.0, "quality_score": 99.99999999999999, "llm_support": ["openai", "qwen", "llama"], "created": "2025-05-23", "generator": "fresh_512_v1"}, {"id": "NG0488", "symbol": "⦑", "code": "ng:quantum:epr", "fallback": "[EPR]", "category": "quantum", "meaning": "epr", "description": "Quantum operation: epr", "unicode_point": "U+2991", "unicode_name": "LEFT ANGLE BRACKET WITH DOT", "token_cost": 1, "token_density": 1.0, "quality_score": 99.99999999999999, "llm_support": ["openai", "qwen", "llama"], "created": "2025-05-23", "generator": "fresh_512_v1"}, {"id": "NG0489", "symbol": "⊆", "code": "ng:quantum:collapse", "fallback": "[COLLAP]", "category": "quantum", "meaning": "collapse", "description": "Quantum operation: collapse", "unicode_point": "U+2286", "unicode_name": "SUBSET OF OR EQUAL TO", "token_cost": 1, "token_density": 1.0, "quality_score": 99.99999999999999, "llm_support": ["openai", "qwen", "llama"], "created": "2025-05-23", "generator": "fresh_512_v1"}, {"id": "NG0490", "symbol": "⇹", "code": "ng:quantum:decohere", "fallback": "[DECOHE]", "category": "quantum", "meaning": "decohere", "description": "Quantum operation: decohere", "unicode_point": "U+21F9", "unicode_name": "LEFT RIGHT ARROW WITH VERTICAL STROKE", "token_cost": 1, "token_density": 1.0, "quality_score": 99.99999999999999, "llm_support": ["openai", "qwen", "llama"], "created": "2025-05-23", "generator": "fresh_512_v1"}, {"id": "NG0491", "symbol": "⨮", "code": "ng:quantum:teleport", "fallback": "[TELEPO]", "category": "quantum", "meaning": "teleport", "description": "Quantum operation: teleport", "unicode_point": "U+2A2E", "unicode_name": "PLUS SIGN IN RIGHT HALF CIRCLE", "token_cost": 1, "token_density": 1.0, "quality_score": 99.99999999999999, "llm_support": ["openai", "qwen", "llama"], "created": "2025-05-23", "generator": "fresh_512_v1"}, {"id": "NG0492", "symbol": "⚄", "code": "ng:quantum:phase", "fallback": "[PHASE]", "category": "quantum", "meaning": "phase", "description": "Quantum operation: phase", "unicode_point": "U+2684", "unicode_name": "DIE FACE-5", "token_cost": 1, "token_density": 1.0, "quality_score": 99.99999999999999, "llm_support": ["openai", "qwen", "llama"], "created": "2025-05-23", "generator": "fresh_512_v1"}, {"id": "NG0493", "symbol": "⇼", "code": "ng:quantum:error_correct", "fallback": "[ERRORC]", "category": "quantum", "meaning": "error_correct", "description": "Quantum operation: error_correct", "unicode_point": "U+21FC", "unicode_name": "LEFT RIGHT ARROW WITH DOUBLE VERTICAL STROKE", "token_cost": 1, "token_density": 1.0, "quality_score": 99.99999999999999, "llm_support": ["openai", "qwen", "llama"], "created": "2025-05-23", "generator": "fresh_512_v1"}, {"id": "NG0494", "symbol": "≡", "code": "ng:quantum:bell_state", "fallback": "[BELLST]", "category": "quantum", "meaning": "bell_state", "description": "Quantum operation: bell_state", "unicode_point": "U+2261", "unicode_name": "IDENTICAL TO", "token_cost": 1, "token_density": 1.0, "quality_score": 99.99999999999999, "llm_support": ["openai", "qwen", "llama"], "created": "2025-05-23", "generator": "fresh_512_v1"}, {"id": "NG0495", "symbol": "≠", "code": "ng:quantum:correlate", "fallback": "[CORREL2]", "category": "quantum", "meaning": "correlate", "description": "Quantum operation: correlate", "unicode_point": "U+2260", "unicode_name": "NOT EQUAL TO", "token_cost": 1, "token_density": 1.0, "quality_score": 99.99999999999999, "llm_support": ["openai", "qwen", "llama"], "created": "2025-05-23", "generator": "fresh_512_v1"}, {"id": "NG0496", "symbol": "⨏", "code": "ng:quantum:observe", "fallback": "[OBSERV]", "category": "quantum", "meaning": "observe", "description": "Quantum operation: observe", "unicode_point": "U+2A0F", "unicode_name": "INTEGRAL AVERAGE WITH SLASH", "token_cost": 1, "token_density": 1.0, "quality_score": 99.99999999999999, "llm_support": ["openai", "qwen", "llama"], "created": "2025-05-23", "generator": "fresh_512_v1"}, {"id": "NG0497", "symbol": "⌠", "code": "ng:quantum:particle", "fallback": "[PARTIC]", "category": "quantum", "meaning": "particle", "description": "Quantum operation: particle", "unicode_point": "U+2320", "unicode_name": "TOP HALF INTEGRAL", "token_cost": 1, "token_density": 1.0, "quality_score": 99.99999999999999, "llm_support": ["openai", "qwen", "llama"], "created": "2025-05-23", "generator": "fresh_512_v1"}, {"id": "NG0498", "symbol": "⪔", "code": "ng:quantum:separate", "fallback": "[SEPARA1]", "category": "quantum", "meaning": "separate", "description": "Quantum operation: separate", "unicode_point": "U+2A94", "unicode_name": "GREATER-<PERSON><PERSON>N ABOVE SLANTED EQUAL ABOVE LESS-THAN ABOVE SLANTED EQUAL", "token_cost": 1, "token_density": 1.0, "quality_score": 99.99999999999999, "llm_support": ["openai", "qwen", "llama"], "created": "2025-05-23", "generator": "fresh_512_v1"}, {"id": "NG0499", "symbol": "⩽", "code": "ng:quantum:duality", "fallback": "[DUALIT]", "category": "quantum", "meaning": "duality", "description": "Quantum operation: duality", "unicode_point": "U+2A7D", "unicode_name": "LESS-THAN OR SLANTED EQUAL TO", "token_cost": 1, "token_density": 1.0, "quality_score": 99.99999999999999, "llm_support": ["openai", "qwen", "llama"], "created": "2025-05-23", "generator": "fresh_512_v1"}, {"id": "NG0500", "symbol": "⋸", "code": "ng:quantum:spooky", "fallback": "[SPOOKY]", "category": "quantum", "meaning": "spooky", "description": "Quantum operation: spooky", "unicode_point": "U+22F8", "unicode_name": "ELEMENT OF WITH UNDERBAR", "token_cost": 1, "token_density": 1.0, "quality_score": 99.99999999999999, "llm_support": ["openai", "qwen", "llama"], "created": "2025-05-23", "generator": "fresh_512_v1"}, {"id": "NG0501", "symbol": "⇶", "code": "ng:quantum:wave", "fallback": "[WAVE]", "category": "quantum", "meaning": "wave", "description": "Quantum operation: wave", "unicode_point": "U+21F6", "unicode_name": "THREE RIGHTWARDS ARROWS", "token_cost": 1, "token_density": 1.0, "quality_score": 99.99999999999999, "llm_support": ["openai", "qwen", "llama"], "created": "2025-05-23", "generator": "fresh_512_v1"}, {"id": "NG0502", "symbol": "≑", "code": "ng:quantum:hadamard", "fallback": "[HADAMA]", "category": "quantum", "meaning": "<PERSON><PERSON><PERSON>", "description": "Quantum operation: hadamard", "unicode_point": "U+2251", "unicode_name": "GEOMETRICALLY EQUAL TO", "token_cost": 1, "token_density": 1.0, "quality_score": 99.99999999999999, "llm_support": ["openai", "qwen", "llama"], "created": "2025-05-23", "generator": "fresh_512_v1"}, {"id": "NG0503", "symbol": "⩇", "code": "ng:quantum:superposition", "fallback": "[SUPERP]", "category": "quantum", "meaning": "superposition", "description": "Quantum operation: superposition", "unicode_point": "U+2A47", "unicode_name": "INTERSECTION ABOVE UNION", "token_cost": 1, "token_density": 1.0, "quality_score": 99.99999999999999, "llm_support": ["openai", "qwen", "llama"], "created": "2025-05-23", "generator": "fresh_512_v1"}, {"id": "NG0504", "symbol": "⛚", "code": "ng:quantum:decrypt", "fallback": "[DECRYP]", "category": "quantum", "meaning": "decrypt", "description": "Quantum operation: decrypt", "unicode_point": "U+26DA", "unicode_name": "DRIVE SLOW SIGN", "token_cost": 1, "token_density": 1.0, "quality_score": 99.99999999999999, "llm_support": ["openai", "qwen", "llama"], "created": "2025-05-23", "generator": "fresh_512_v1"}, {"id": "NG0505", "symbol": "≽", "code": "ng:quantum:pauli_z", "fallback": "[PAULIZ]", "category": "quantum", "meaning": "pauli_z", "description": "Quantum operation: pauli_z", "unicode_point": "U+227D", "unicode_name": "SUCCEEDS OR EQUAL TO", "token_cost": 1, "token_density": 1.0, "quality_score": 99.99999999999999, "llm_support": ["openai", "qwen", "llama"], "created": "2025-05-23", "generator": "fresh_512_v1"}, {"id": "NG0506", "symbol": "⚪", "code": "ng:quantum:pauli_x", "fallback": "[PAULIX]", "category": "quantum", "meaning": "pauli_x", "description": "Quantum operation: pauli_x", "unicode_point": "U+26AA", "unicode_name": "MEDIUM WHITE CIRCLE", "token_cost": 1, "token_density": 1.0, "quality_score": 99.99999999999999, "llm_support": ["openai", "qwen", "llama"], "created": "2025-05-23", "generator": "fresh_512_v1"}, {"id": "NG0507", "symbol": "⚮", "code": "ng:quantum:measure", "fallback": "[MEASUR1]", "category": "quantum", "meaning": "measure", "description": "Quantum operation: measure", "unicode_point": "U+26AE", "unicode_name": "DIVORCE SYMBOL", "token_cost": 1, "token_density": 1.0, "quality_score": 99.99999999999999, "llm_support": ["openai", "qwen", "llama"], "created": "2025-05-23", "generator": "fresh_512_v1"}, {"id": "NG0508", "symbol": "♷", "code": "ng:quantum:to<PERSON><PERSON>", "fallback": "[TOFFOL]", "category": "quantum", "meaning": "to<PERSON><PERSON>", "description": "Quantum operation: to<PERSON><PERSON>", "unicode_point": "U+2677", "unicode_name": "RECYCLING <PERSON><PERSON><PERSON><PERSON> FOR TYPE-5 PLASTICS", "token_cost": 1, "token_density": 1.0, "quality_score": 99.99999999999999, "llm_support": ["openai", "qwen", "llama"], "created": "2025-05-23", "generator": "fresh_512_v1"}, {"id": "NG0509", "symbol": "⩼", "code": "ng:quantum:entangle", "fallback": "[ENTANG]", "category": "quantum", "meaning": "entangle", "description": "Quantum operation: entangle", "unicode_point": "U+2A7C", "unicode_name": "GREATER-THAN WITH QUESTION MARK ABOVE", "token_cost": 1, "token_density": 1.0, "quality_score": 99.99999999999999, "llm_support": ["openai", "qwen", "llama"], "created": "2025-05-23", "generator": "fresh_512_v1"}, {"id": "NG0510", "symbol": "◡", "code": "ng:quantum:encrypt", "fallback": "[ENCRYP]", "category": "quantum", "meaning": "encrypt", "description": "Quantum operation: encrypt", "unicode_point": "U+25E1", "unicode_name": "LOWER HALF CIRCLE", "token_cost": 1, "token_density": 1.0, "quality_score": 99.99999999999999, "llm_support": ["openai", "qwen", "llama"], "created": "2025-05-23", "generator": "fresh_512_v1"}, {"id": "NG0511", "symbol": "▦", "code": "ng:quantum:qubit", "fallback": "[QUBIT]", "category": "quantum", "meaning": "qubit", "description": "Quantum operation: qubit", "unicode_point": "U+25A6", "unicode_name": "SQUARE WITH ORTHOGONAL CROSSHATCH FILL", "token_cost": 1, "token_density": 1.0, "quality_score": 99.99999999999999, "llm_support": ["openai", "qwen", "llama"], "created": "2025-05-23", "generator": "fresh_512_v1"}, {"id": "NG0512", "symbol": "⊯", "code": "ng:quantum:probability", "fallback": "[PROBAB]", "category": "quantum", "meaning": "probability", "description": "Quantum operation: probability", "unicode_point": "U+22AF", "unicode_name": "NEGATED DOUBLE VERTICAL BAR DOUBLE RIGHT TURNSTILE", "token_cost": 1, "token_density": 1.0, "quality_score": 99.99999999999999, "llm_support": ["openai", "qwen", "llama"], "created": "2025-05-23", "generator": "fresh_512_v1"}]}