{"validation_info": {"batch_number": 1, "tier": "ultra", "theme": "async_concurrency", "validation_date": "2025-05-25T12:25:46.689773", "validator_version": "1.0.0"}, "statistics": {"total_symbols": 122, "approved_count": 0, "rejected_count": 122, "rejection_reasons": {"not_unique": 110, "visual_collision": 122}, "validation_date": "2025-05-25T12:25:46.598835"}, "approved_symbols": [], "rejected_symbols": [{"id": "NG0568", "symbol": "⚡", "code": "ng:async:function", "fallback": "[async]", "description": "Funzione asincrona", "category": "async_concurrency", "batch": 1, "tier": "ultra", "approved_date": "2025-05-25", "validation_score": 1.4000000000000001, "status": "rejected", "rejection_reasons": ["not_unique", "visual_collision"], "validation_date": "2025-05-25"}, {"id": "NG0569", "symbol": "⏳", "code": "ng:async:await", "fallback": "[await]", "description": "Operazione await", "category": "async_concurrency", "batch": 1, "tier": "ultra", "approved_date": "2025-05-25", "validation_score": 1.4000000000000001, "status": "rejected", "rejection_reasons": ["not_unique", "visual_collision"], "validation_date": "2025-05-25"}, {"id": "NG0570", "symbol": "⏸", "code": "ng:async:pause", "fallback": "[pause]", "description": "Pausa asincrona", "category": "async_concurrency", "batch": 1, "tier": "ultra", "approved_date": "2025-05-25", "validation_score": 1.4000000000000001, "status": "rejected", "rejection_reasons": ["not_unique", "visual_collision"], "validation_date": "2025-05-25"}, {"id": "NG0571", "symbol": "⏯", "code": "ng:async:resume", "fallback": "[resume]", "description": "Ripresa asin<PERSON>", "category": "async_concurrency", "batch": 1, "tier": "ultra", "approved_date": "2025-05-25", "validation_score": 1.4000000000000001, "status": "rejected", "rejection_reasons": ["not_unique", "visual_collision"], "validation_date": "2025-05-25"}, {"id": "NG0572", "symbol": "⏹", "code": "ng:async:stop", "fallback": "[stop]", "description": "Stop asincrono", "category": "async_concurrency", "batch": 1, "tier": "ultra", "approved_date": "2025-05-25", "validation_score": 1.4000000000000001, "status": "rejected", "rejection_reasons": ["not_unique", "visual_collision"], "validation_date": "2025-05-25"}, {"id": "NG0573", "symbol": "⏱", "code": "ng:async:timer", "fallback": "[timer]", "description": "Timer as<PERSON><PERSON><PERSON>o", "category": "async_concurrency", "batch": 1, "tier": "ultra", "approved_date": "2025-05-25", "validation_score": 1.4000000000000001, "status": "rejected", "rejection_reasons": ["not_unique", "visual_collision"], "validation_date": "2025-05-25"}, {"id": "NG0574", "symbol": "⌛", "code": "ng:async:hourglass", "fallback": "[wait]", "description": "<PERSON><PERSON><PERSON> asin<PERSON>", "category": "async_concurrency", "batch": 1, "tier": "ultra", "approved_date": "2025-05-25", "validation_score": 1.4000000000000001, "status": "rejected", "rejection_reasons": ["not_unique", "visual_collision"], "validation_date": "2025-05-25"}, {"id": "NG0575", "symbol": "🔮", "code": "ng:async:future", "fallback": "[future]", "description": "Risultato futuro", "category": "async_concurrency", "batch": 1, "tier": "ultra", "approved_date": "2025-05-25", "validation_score": 1.4000000000000001, "status": "rejected", "rejection_reasons": ["not_unique", "visual_collision"], "validation_date": "2025-05-25"}, {"id": "NG0576", "symbol": "🔄", "code": "ng:async:chain", "fallback": "[chain]", "description": "<PERSON><PERSON> promesse", "category": "async_concurrency", "batch": 1, "tier": "ultra", "approved_date": "2025-05-25", "validation_score": 1.4000000000000001, "status": "rejected", "rejection_reasons": ["not_unique", "visual_collision"], "validation_date": "2025-05-25"}, {"id": "NG0577", "symbol": "🎯", "code": "ng:async:target", "fallback": "[target]", "description": "Target asincrono", "category": "async_concurrency", "batch": 1, "tier": "ultra", "approved_date": "2025-05-25", "validation_score": 1.4000000000000001, "status": "rejected", "rejection_reasons": ["not_unique", "visual_collision"], "validation_date": "2025-05-25"}, {"id": "NG0578", "symbol": "🌊", "code": "ng:async:flow", "fallback": "[flow]", "description": "<PERSON><PERSON><PERSON> as<PERSON><PERSON>o", "category": "async_concurrency", "batch": 1, "tier": "ultra", "approved_date": "2025-05-25", "validation_score": 1.4000000000000001, "status": "rejected", "rejection_reasons": ["not_unique", "visual_collision"], "validation_date": "2025-05-25"}, {"id": "NG0579", "symbol": "🔀", "code": "ng:async:branch", "fallback": "[branch]", "description": "<PERSON><PERSON>", "category": "async_concurrency", "batch": 1, "tier": "ultra", "approved_date": "2025-05-25", "validation_score": 1.4000000000000001, "status": "rejected", "rejection_reasons": ["visual_collision"], "validation_date": "2025-05-25"}, {"id": "NG0580", "symbol": "🔁", "code": "ng:async:loop", "fallback": "[loop]", "description": "Loop asincrono", "category": "async_concurrency", "batch": 1, "tier": "ultra", "approved_date": "2025-05-25", "validation_score": 1.4000000000000001, "status": "rejected", "rejection_reasons": ["not_unique", "visual_collision"], "validation_date": "2025-05-25"}, {"id": "NG0581", "symbol": "🔃", "code": "ng:async:cycle", "fallback": "[cycle]", "description": "<PERSON><PERSON><PERSON>o", "category": "async_concurrency", "batch": 1, "tier": "ultra", "approved_date": "2025-05-25", "validation_score": 1.4000000000000001, "status": "rejected", "rejection_reasons": ["not_unique", "visual_collision"], "validation_date": "2025-05-25"}, {"id": "NG0582", "symbol": "⤵", "code": "ng:async:yield", "fallback": "[yield]", "description": "Yield asin<PERSON>rono", "category": "async_concurrency", "batch": 1, "tier": "ultra", "approved_date": "2025-05-25", "validation_score": 1.4000000000000001, "status": "rejected", "rejection_reasons": ["visual_collision"], "validation_date": "2025-05-25"}, {"id": "NG0583", "symbol": "🏭", "code": "ng:async:generator", "fallback": "[gen]", "description": "Generatore asincrono", "category": "async_concurrency", "batch": 1, "tier": "ultra", "approved_date": "2025-05-25", "validation_score": 1.4000000000000001, "status": "rejected", "rejection_reasons": ["not_unique", "visual_collision"], "validation_date": "2025-05-25"}, {"id": "NG0584", "symbol": "⚙", "code": "ng:async:iterator", "fallback": "[iter]", "description": "Iteratore asincrono", "category": "async_concurrency", "batch": 1, "tier": "ultra", "approved_date": "2025-05-25", "validation_score": 1.4000000000000001, "status": "rejected", "rejection_reasons": ["not_unique", "visual_collision"], "validation_date": "2025-05-25"}, {"id": "NG0585", "symbol": "🔧", "code": "ng:async:next", "fallback": "[next]", "description": "Next asincrono", "category": "async_concurrency", "batch": 1, "tier": "ultra", "approved_date": "2025-05-25", "validation_score": 1.4000000000000001, "status": "rejected", "rejection_reasons": ["not_unique", "visual_collision"], "validation_date": "2025-05-25"}, {"id": "NG0586", "symbol": "🛠", "code": "ng:async:send", "fallback": "[send]", "description": "Send asincrono", "category": "async_concurrency", "batch": 1, "tier": "ultra", "approved_date": "2025-05-25", "validation_score": 1.4000000000000001, "status": "rejected", "rejection_reasons": ["visual_collision"], "validation_date": "2025-05-25"}, {"id": "NG0587", "symbol": "🔩", "code": "ng:async:throw", "fallback": "[throw]", "description": "Throw asincrono", "category": "async_concurrency", "batch": 1, "tier": "ultra", "approved_date": "2025-05-25", "validation_score": 1.4000000000000001, "status": "rejected", "rejection_reasons": ["not_unique", "visual_collision"], "validation_date": "2025-05-25"}, {"id": "NG0588", "symbol": "🔐", "code": "ng:async:enter", "fallback": "[enter]", "description": "Enter asincrono", "category": "async_concurrency", "batch": 1, "tier": "ultra", "approved_date": "2025-05-25", "validation_score": 1.4000000000000001, "status": "rejected", "rejection_reasons": ["not_unique", "visual_collision"], "validation_date": "2025-05-25"}, {"id": "NG0589", "symbol": "🔓", "code": "ng:async:exit", "fallback": "[exit]", "description": "Exit asincrono", "category": "async_concurrency", "batch": 1, "tier": "ultra", "approved_date": "2025-05-25", "validation_score": 1.4000000000000001, "status": "rejected", "rejection_reasons": ["not_unique", "visual_collision"], "validation_date": "2025-05-25"}, {"id": "NG0590", "symbol": "🛡", "code": "ng:async:guard", "fallback": "[guard]", "description": "Guard asin<PERSON>rono", "category": "async_concurrency", "batch": 1, "tier": "ultra", "approved_date": "2025-05-25", "validation_score": 1.4000000000000001, "status": "rejected", "rejection_reasons": ["not_unique", "visual_collision"], "validation_date": "2025-05-25"}, {"id": "NG0591", "symbol": "🎭", "code": "ng:async:with", "fallback": "[with]", "description": "With asincrono", "category": "async_concurrency", "batch": 1, "tier": "ultra", "approved_date": "2025-05-25", "validation_score": 1.4000000000000001, "status": "rejected", "rejection_reasons": ["not_unique", "visual_collision"], "validation_date": "2025-05-25"}, {"id": "NG0592", "symbol": "🚨", "code": "ng:async:error", "fallback": "[error]", "description": "Errore asincrono", "category": "async_concurrency", "batch": 1, "tier": "ultra", "approved_date": "2025-05-25", "validation_score": 1.4000000000000001, "status": "rejected", "rejection_reasons": ["not_unique", "visual_collision"], "validation_date": "2025-05-25"}, {"id": "NG0593", "symbol": "🛟", "code": "ng:async:rescue", "fallback": "[rescue]", "description": "Rescue asincrono", "category": "async_concurrency", "batch": 1, "tier": "ultra", "approved_date": "2025-05-25", "validation_score": 1.4000000000000001, "status": "rejected", "rejection_reasons": ["not_unique", "visual_collision"], "validation_date": "2025-05-25"}, {"id": "NG0594", "symbol": "🧵", "code": "ng:thread:thread", "fallback": "[thread]", "description": "<PERSON><PERSON><PERSON>", "category": "async_concurrency", "batch": 1, "tier": "ultra", "approved_date": "2025-05-25", "validation_score": 1.4000000000000001, "status": "rejected", "rejection_reasons": ["not_unique", "visual_collision"], "validation_date": "2025-05-25"}, {"id": "NG0595", "symbol": "🎭", "code": "ng:thread:pool", "fallback": "[pool]", "description": "Pool di thread", "category": "async_concurrency", "batch": 1, "tier": "ultra", "approved_date": "2025-05-25", "validation_score": 1.4000000000000001, "status": "rejected", "rejection_reasons": ["not_unique", "visual_collision"], "validation_date": "2025-05-25"}, {"id": "NG0596", "symbol": "👥", "code": "ng:thread:group", "fallback": "[group]", "description": "Gruppo thread", "category": "async_concurrency", "batch": 1, "tier": "ultra", "approved_date": "2025-05-25", "validation_score": 1.4000000000000001, "status": "rejected", "rejection_reasons": ["not_unique", "visual_collision"], "validation_date": "2025-05-25"}, {"id": "NG0597", "symbol": "🔀", "code": "ng:thread:spawn", "fallback": "[spawn]", "description": "Spawn thread", "category": "async_concurrency", "batch": 1, "tier": "ultra", "approved_date": "2025-05-25", "validation_score": 1.4000000000000001, "status": "rejected", "rejection_reasons": ["visual_collision"], "validation_date": "2025-05-25"}, {"id": "NG0598", "symbol": "🔚", "code": "ng:thread:join", "fallback": "[join]", "description": "Join thread", "category": "async_concurrency", "batch": 1, "tier": "ultra", "approved_date": "2025-05-25", "validation_score": 1.4000000000000001, "status": "rejected", "rejection_reasons": ["not_unique", "visual_collision"], "validation_date": "2025-05-25"}, {"id": "NG0599", "symbol": "💀", "code": "ng:thread:kill", "fallback": "[kill]", "description": "Kill thread", "category": "async_concurrency", "batch": 1, "tier": "ultra", "approved_date": "2025-05-25", "validation_score": 1.4000000000000001, "status": "rejected", "rejection_reasons": ["not_unique", "visual_collision"], "validation_date": "2025-05-25"}, {"id": "NG0600", "symbol": "😴", "code": "ng:thread:sleep", "fallback": "[sleep]", "description": "Sleep thread", "category": "async_concurrency", "batch": 1, "tier": "ultra", "approved_date": "2025-05-25", "validation_score": 1.4000000000000001, "status": "rejected", "rejection_reasons": ["not_unique", "visual_collision"], "validation_date": "2025-05-25"}, {"id": "NG0601", "symbol": "⚙", "code": "ng:process:process", "fallback": "[proc]", "description": "Processo", "category": "async_concurrency", "batch": 1, "tier": "ultra", "approved_date": "2025-05-25", "validation_score": 1.4000000000000001, "status": "rejected", "rejection_reasons": ["not_unique", "visual_collision"], "validation_date": "2025-05-25"}, {"id": "NG0602", "symbol": "🏭", "code": "ng:process:pool", "fallback": "[ppool]", "description": "Pool processi", "category": "async_concurrency", "batch": 1, "tier": "ultra", "approved_date": "2025-05-25", "validation_score": 1.4000000000000001, "status": "rejected", "rejection_reasons": ["not_unique", "visual_collision"], "validation_date": "2025-05-25"}, {"id": "NG0603", "symbol": "🍴", "code": "ng:process:fork", "fallback": "[fork]", "description": "Fork processo", "category": "async_concurrency", "batch": 1, "tier": "ultra", "approved_date": "2025-05-25", "validation_score": 1.4000000000000001, "status": "rejected", "rejection_reasons": ["not_unique", "visual_collision"], "validation_date": "2025-05-25"}, {"id": "NG0604", "symbol": "🔄", "code": "ng:process:exec", "fallback": "[exec]", "description": "Exec processo", "category": "async_concurrency", "batch": 1, "tier": "ultra", "approved_date": "2025-05-25", "validation_score": 1.4000000000000001, "status": "rejected", "rejection_reasons": ["not_unique", "visual_collision"], "validation_date": "2025-05-25"}, {"id": "NG0605", "symbol": "📤", "code": "ng:process:send", "fallback": "[send]", "description": "Send processo", "category": "async_concurrency", "batch": 1, "tier": "ultra", "approved_date": "2025-05-25", "validation_score": 1.4000000000000001, "status": "rejected", "rejection_reasons": ["not_unique", "visual_collision"], "validation_date": "2025-05-25"}, {"id": "NG0606", "symbol": "📥", "code": "ng:process:recv", "fallback": "[recv]", "description": "Recv processo", "category": "async_concurrency", "batch": 1, "tier": "ultra", "approved_date": "2025-05-25", "validation_score": 1.4000000000000001, "status": "rejected", "rejection_reasons": ["not_unique", "visual_collision"], "validation_date": "2025-05-25"}, {"id": "NG0607", "symbol": "🚇", "code": "ng:process:pipe", "fallback": "[pipe]", "description": "<PERSON><PERSON> processo", "category": "async_concurrency", "batch": 1, "tier": "ultra", "approved_date": "2025-05-25", "validation_score": 1.4000000000000001, "status": "rejected", "rejection_reasons": ["not_unique", "visual_collision"], "validation_date": "2025-05-25"}, {"id": "NG0608", "symbol": "📡", "code": "ng:process:signal", "fallback": "[signal]", "description": "Signal processo", "category": "async_concurrency", "batch": 1, "tier": "ultra", "approved_date": "2025-05-25", "validation_score": 1.4000000000000001, "status": "rejected", "rejection_reasons": ["not_unique", "visual_collision"], "validation_date": "2025-05-25"}, {"id": "NG0609", "symbol": "👷", "code": "ng:worker:worker", "fallback": "[worker]", "description": "Worker", "category": "async_concurrency", "batch": 1, "tier": "ultra", "approved_date": "2025-05-25", "validation_score": 1.4000000000000001, "status": "rejected", "rejection_reasons": ["not_unique", "visual_collision"], "validation_date": "2025-05-25"}, {"id": "NG0610", "symbol": "📋", "code": "ng:worker:queue", "fallback": "[queue]", "description": "Coda task", "category": "async_concurrency", "batch": 1, "tier": "ultra", "approved_date": "2025-05-25", "validation_score": 1.4000000000000001, "status": "rejected", "rejection_reasons": ["not_unique", "visual_collision"], "validation_date": "2025-05-25"}, {"id": "NG0611", "symbol": "⚡", "code": "ng:worker:executor", "fallback": "[exec]", "description": "Esecutore task", "category": "async_concurrency", "batch": 1, "tier": "ultra", "approved_date": "2025-05-25", "validation_score": 1.4000000000000001, "status": "rejected", "rejection_reasons": ["not_unique", "visual_collision"], "validation_date": "2025-05-25"}, {"id": "NG0612", "symbol": "🎯", "code": "ng:worker:target", "fallback": "[target]", "description": "Target task", "category": "async_concurrency", "batch": 1, "tier": "ultra", "approved_date": "2025-05-25", "validation_score": 1.4000000000000001, "status": "rejected", "rejection_reasons": ["not_unique", "visual_collision"], "validation_date": "2025-05-25"}, {"id": "NG0613", "symbol": "📊", "code": "ng:worker:result", "fallback": "[result]", "description": "Risultato task", "category": "async_concurrency", "batch": 1, "tier": "ultra", "approved_date": "2025-05-25", "validation_score": 1.4000000000000001, "status": "rejected", "rejection_reasons": ["not_unique", "visual_collision"], "validation_date": "2025-05-25"}, {"id": "NG0614", "symbol": "🔄", "code": "ng:worker:retry", "fallback": "[retry]", "description": "Retry task", "category": "async_concurrency", "batch": 1, "tier": "ultra", "approved_date": "2025-05-25", "validation_score": 1.4000000000000001, "status": "rejected", "rejection_reasons": ["not_unique", "visual_collision"], "validation_date": "2025-05-25"}, {"id": "NG0615", "symbol": "❌", "code": "ng:worker:cancel", "fallback": "[cancel]", "description": "Cancel task", "category": "async_concurrency", "batch": 1, "tier": "ultra", "approved_date": "2025-05-25", "validation_score": 1.4000000000000001, "status": "rejected", "rejection_reasons": ["not_unique", "visual_collision"], "validation_date": "2025-05-25"}, {"id": "NG0616", "symbol": "📅", "code": "ng:schedule:scheduler", "fallback": "[sched]", "description": "Scheduler", "category": "async_concurrency", "batch": 1, "tier": "ultra", "approved_date": "2025-05-25", "validation_score": 1.4000000000000001, "status": "rejected", "rejection_reasons": ["not_unique", "visual_collision"], "validation_date": "2025-05-25"}, {"id": "NG0617", "symbol": "📆", "code": "ng:schedule:cron", "fallback": "[cron]", "description": "Cron schedule", "category": "async_concurrency", "batch": 1, "tier": "ultra", "approved_date": "2025-05-25", "validation_score": 1.4000000000000001, "status": "rejected", "rejection_reasons": ["not_unique", "visual_collision"], "validation_date": "2025-05-25"}, {"id": "NG0618", "symbol": "⚡", "code": "ng:schedule:immediate", "fallback": "[now]", "description": "Schedule immediato", "category": "async_concurrency", "batch": 1, "tier": "ultra", "approved_date": "2025-05-25", "validation_score": 1.4000000000000001, "status": "rejected", "rejection_reasons": ["not_unique", "visual_collision"], "validation_date": "2025-05-25"}, {"id": "NG0619", "symbol": "⏳", "code": "ng:schedule:delayed", "fallback": "[delay]", "description": "Schedule ritardato", "category": "async_concurrency", "batch": 1, "tier": "ultra", "approved_date": "2025-05-25", "validation_score": 1.4000000000000001, "status": "rejected", "rejection_reasons": ["not_unique", "visual_collision"], "validation_date": "2025-05-25"}, {"id": "NG0620", "symbol": "🔁", "code": "ng:schedule:repeat", "fallback": "[repeat]", "description": "Schedule ripetuto", "category": "async_concurrency", "batch": 1, "tier": "ultra", "approved_date": "2025-05-25", "validation_score": 1.4000000000000001, "status": "rejected", "rejection_reasons": ["not_unique", "visual_collision"], "validation_date": "2025-05-25"}, {"id": "NG0621", "symbol": "🛑", "code": "ng:schedule:stop", "fallback": "[stop]", "description": "Stop schedule", "category": "async_concurrency", "batch": 1, "tier": "ultra", "approved_date": "2025-05-25", "validation_score": 1.4000000000000001, "status": "rejected", "rejection_reasons": ["not_unique", "visual_collision"], "validation_date": "2025-05-25"}, {"id": "NG0622", "symbol": "🔓", "code": "ng:sync:unlock", "fallback": "[unlock]", "description": "Unlock", "category": "async_concurrency", "batch": 1, "tier": "ultra", "approved_date": "2025-05-25", "validation_score": 1.4000000000000001, "status": "rejected", "rejection_reasons": ["not_unique", "visual_collision"], "validation_date": "2025-05-25"}, {"id": "NG0623", "symbol": "🗝", "code": "ng:sync:key", "fallback": "[key]", "description": "Chiave lock", "category": "async_concurrency", "batch": 1, "tier": "ultra", "approved_date": "2025-05-25", "validation_score": 1.4000000000000001, "status": "rejected", "rejection_reasons": ["not_unique", "visual_collision"], "validation_date": "2025-05-25"}, {"id": "NG0624", "symbol": "🚪", "code": "ng:sync:door", "fallback": "[door]", "description": "Porta lock", "category": "async_concurrency", "batch": 1, "tier": "ultra", "approved_date": "2025-05-25", "validation_score": 1.4000000000000001, "status": "rejected", "rejection_reasons": ["not_unique", "visual_collision"], "validation_date": "2025-05-25"}, {"id": "NG0625", "symbol": "🛡", "code": "ng:sync:guard", "fallback": "[guard]", "description": "Guard lock", "category": "async_concurrency", "batch": 1, "tier": "ultra", "approved_date": "2025-05-25", "validation_score": 1.4000000000000001, "status": "rejected", "rejection_reasons": ["not_unique", "visual_collision"], "validation_date": "2025-05-25"}, {"id": "NG0626", "symbol": "🔄", "code": "ng:sync:reentrant", "fallback": "[reent]", "description": "Lock rientrante", "category": "async_concurrency", "batch": 1, "tier": "ultra", "approved_date": "2025-05-25", "validation_score": 1.4000000000000001, "status": "rejected", "rejection_reasons": ["not_unique", "visual_collision"], "validation_date": "2025-05-25"}, {"id": "NG0627", "symbol": "📖", "code": "ng:sync:read", "fallback": "[read]", "description": "Read lock", "category": "async_concurrency", "batch": 1, "tier": "ultra", "approved_date": "2025-05-25", "validation_score": 1.4000000000000001, "status": "rejected", "rejection_reasons": ["not_unique", "visual_collision"], "validation_date": "2025-05-25"}, {"id": "NG0628", "symbol": "📝", "code": "ng:sync:write", "fallback": "[write]", "description": "Write lock", "category": "async_concurrency", "batch": 1, "tier": "ultra", "approved_date": "2025-05-25", "validation_score": 1.4000000000000001, "status": "rejected", "rejection_reasons": ["not_unique", "visual_collision"], "validation_date": "2025-05-25"}, {"id": "NG0629", "symbol": "🎭", "code": "ng:sync:shared", "fallback": "[shared]", "description": "Lock condiviso", "category": "async_concurrency", "batch": 1, "tier": "ultra", "approved_date": "2025-05-25", "validation_score": 1.4000000000000001, "status": "rejected", "rejection_reasons": ["not_unique", "visual_collision"], "validation_date": "2025-05-25"}, {"id": "NG0630", "symbol": "👑", "code": "ng:sync:exclusive", "fallback": "[excl]", "description": "Lock esclusivo", "category": "async_concurrency", "batch": 1, "tier": "ultra", "approved_date": "2025-05-25", "validation_score": 1.4000000000000001, "status": "rejected", "rejection_reasons": ["not_unique", "visual_collision"], "validation_date": "2025-05-25"}, {"id": "NG0631", "symbol": "🎲", "code": "ng:sync:unfair", "fallback": "[unfair]", "description": "Lock non equo", "category": "async_concurrency", "batch": 1, "tier": "ultra", "approved_date": "2025-05-25", "validation_score": 1.4000000000000001, "status": "rejected", "rejection_reasons": ["not_unique", "visual_collision"], "validation_date": "2025-05-25"}, {"id": "NG0632", "symbol": "🔢", "code": "ng:sync:count", "fallback": "[count]", "description": "Count lock", "category": "async_concurrency", "batch": 1, "tier": "ultra", "approved_date": "2025-05-25", "validation_score": 1.4000000000000001, "status": "rejected", "rejection_reasons": ["not_unique", "visual_collision"], "validation_date": "2025-05-25"}, {"id": "NG0633", "symbol": "🚦", "code": "ng:sync:semaphore", "fallback": "[sem]", "description": "Semaforo", "category": "async_concurrency", "batch": 1, "tier": "ultra", "approved_date": "2025-05-25", "validation_score": 1.4000000000000001, "status": "rejected", "rejection_reasons": ["not_unique", "visual_collision"], "validation_date": "2025-05-25"}, {"id": "NG0634", "symbol": "🔢", "code": "ng:sync:sem_count", "fallback": "[count]", "description": "Count <PERSON><PERSON><PERSON><PERSON>", "category": "async_concurrency", "batch": 1, "tier": "ultra", "approved_date": "2025-05-25", "validation_score": 1.4000000000000001, "status": "rejected", "rejection_reasons": ["not_unique", "visual_collision"], "validation_date": "2025-05-25"}, {"id": "NG0635", "symbol": "🎯", "code": "ng:sync:permit", "fallback": "[permit]", "description": "Permit semaforo", "category": "async_concurrency", "batch": 1, "tier": "ultra", "approved_date": "2025-05-25", "validation_score": 1.4000000000000001, "status": "rejected", "rejection_reasons": ["not_unique", "visual_collision"], "validation_date": "2025-05-25"}, {"id": "NG0636", "symbol": "⏳", "code": "ng:sync:wait", "fallback": "[wait]", "description": "<PERSON> semaforo", "category": "async_concurrency", "batch": 1, "tier": "ultra", "approved_date": "2025-05-25", "validation_score": 1.4000000000000001, "status": "rejected", "rejection_reasons": ["not_unique", "visual_collision"], "validation_date": "2025-05-25"}, {"id": "NG0637", "symbol": "📊", "code": "ng:sync:available", "fallback": "[avail]", "description": "Available semaforo", "category": "async_concurrency", "batch": 1, "tier": "ultra", "approved_date": "2025-05-25", "validation_score": 1.4000000000000001, "status": "rejected", "rejection_reasons": ["not_unique", "visual_collision"], "validation_date": "2025-05-25"}, {"id": "NG0638", "symbol": "🔔", "code": "ng:sync:condition", "fallback": "[cond]", "description": "Condizione", "category": "async_concurrency", "batch": 1, "tier": "ultra", "approved_date": "2025-05-25", "validation_score": 1.4000000000000001, "status": "rejected", "rejection_reasons": ["not_unique", "visual_collision"], "validation_date": "2025-05-25"}, {"id": "NG0639", "symbol": "🎺", "code": "ng:sync:set", "fallback": "[set]", "description": "Set evento", "category": "async_concurrency", "batch": 1, "tier": "ultra", "approved_date": "2025-05-25", "validation_score": 1.4000000000000001, "status": "rejected", "rejection_reasons": ["not_unique", "visual_collision"], "validation_date": "2025-05-25"}, {"id": "NG0640", "symbol": "🔇", "code": "ng:sync:clear", "fallback": "[clear]", "description": "Clear evento", "category": "async_concurrency", "batch": 1, "tier": "ultra", "approved_date": "2025-05-25", "validation_score": 1.4000000000000001, "status": "rejected", "rejection_reasons": ["not_unique", "visual_collision"], "validation_date": "2025-05-25"}, {"id": "NG0641", "symbol": "⏳", "code": "ng:sync:wait_event", "fallback": "[wait]", "description": "Wait evento", "category": "async_concurrency", "batch": 1, "tier": "ultra", "approved_date": "2025-05-25", "validation_score": 1.4000000000000001, "status": "rejected", "rejection_reasons": ["not_unique", "visual_collision"], "validation_date": "2025-05-25"}, {"id": "NG0642", "symbol": "📢", "code": "ng:sync:notify", "fallback": "[notify]", "description": "Notify evento", "category": "async_concurrency", "batch": 1, "tier": "ultra", "approved_date": "2025-05-25", "validation_score": 1.4000000000000001, "status": "rejected", "rejection_reasons": ["not_unique", "visual_collision"], "validation_date": "2025-05-25"}, {"id": "NG0643", "symbol": "🎭", "code": "ng:sync:once", "fallback": "[once]", "description": "<PERSON><PERSON> singolo", "category": "async_concurrency", "batch": 1, "tier": "ultra", "approved_date": "2025-05-25", "validation_score": 1.4000000000000001, "status": "rejected", "rejection_reasons": ["not_unique", "visual_collision"], "validation_date": "2025-05-25"}, {"id": "NG0644", "symbol": "🔮", "code": "ng:future:future", "fallback": "[future]", "description": "Future", "category": "async_concurrency", "batch": 1, "tier": "ultra", "approved_date": "2025-05-25", "validation_score": 1.4000000000000001, "status": "rejected", "rejection_reasons": ["not_unique", "visual_collision"], "validation_date": "2025-05-25"}, {"id": "NG0645", "symbol": "✅", "code": "ng:future:done", "fallback": "[done]", "description": "Future completato", "category": "async_concurrency", "batch": 1, "tier": "ultra", "approved_date": "2025-05-25", "validation_score": 1.4000000000000001, "status": "rejected", "rejection_reasons": ["not_unique", "visual_collision"], "validation_date": "2025-05-25"}, {"id": "NG0646", "symbol": "❌", "code": "ng:future:failed", "fallback": "[failed]", "description": "Future fallito", "category": "async_concurrency", "batch": 1, "tier": "ultra", "approved_date": "2025-05-25", "validation_score": 1.4000000000000001, "status": "rejected", "rejection_reasons": ["not_unique", "visual_collision"], "validation_date": "2025-05-25"}, {"id": "NG0647", "symbol": "📊", "code": "ng:future:result", "fallback": "[result]", "description": "Risultato future", "category": "async_concurrency", "batch": 1, "tier": "ultra", "approved_date": "2025-05-25", "validation_score": 1.4000000000000001, "status": "rejected", "rejection_reasons": ["not_unique", "visual_collision"], "validation_date": "2025-05-25"}, {"id": "NG0648", "symbol": "🎯", "code": "ng:future:get", "fallback": "[get]", "description": "Get future", "category": "async_concurrency", "batch": 1, "tier": "ultra", "approved_date": "2025-05-25", "validation_score": 1.4000000000000001, "status": "rejected", "rejection_reasons": ["not_unique", "visual_collision"], "validation_date": "2025-05-25"}, {"id": "NG0649", "symbol": "🔗", "code": "ng:future:then", "fallback": "[then]", "description": "Then future", "category": "async_concurrency", "batch": 1, "tier": "ultra", "approved_date": "2025-05-25", "validation_score": 1.4000000000000001, "status": "rejected", "rejection_reasons": ["not_unique", "visual_collision"], "validation_date": "2025-05-25"}, {"id": "NG0650", "symbol": "🚨", "code": "ng:future:catch", "fallback": "[catch]", "description": "Catch future", "category": "async_concurrency", "batch": 1, "tier": "ultra", "approved_date": "2025-05-25", "validation_score": 1.4000000000000001, "status": "rejected", "rejection_reasons": ["not_unique", "visual_collision"], "validation_date": "2025-05-25"}, {"id": "NG0651", "symbol": "🔀", "code": "ng:future:map", "fallback": "[map]", "description": "Map future", "category": "async_concurrency", "batch": 1, "tier": "ultra", "approved_date": "2025-05-25", "validation_score": 1.4000000000000001, "status": "rejected", "rejection_reasons": ["visual_collision"], "validation_date": "2025-05-25"}, {"id": "NG0652", "symbol": "🔄", "code": "ng:future:retry", "fallback": "[retry]", "description": "Retry future", "category": "async_concurrency", "batch": 1, "tier": "ultra", "approved_date": "2025-05-25", "validation_score": 1.4000000000000001, "status": "rejected", "rejection_reasons": ["not_unique", "visual_collision"], "validation_date": "2025-05-25"}, {"id": "NG0653", "symbol": "🤝", "code": "ng:future:all", "fallback": "[all]", "description": "All futures", "category": "async_concurrency", "batch": 1, "tier": "ultra", "approved_date": "2025-05-25", "validation_score": 1.4000000000000001, "status": "rejected", "rejection_reasons": ["not_unique", "visual_collision"], "validation_date": "2025-05-25"}, {"id": "NG0654", "symbol": "🏃", "code": "ng:future:any", "fallback": "[any]", "description": "Any future", "category": "async_concurrency", "batch": 1, "tier": "ultra", "approved_date": "2025-05-25", "validation_score": 1.4000000000000001, "status": "rejected", "rejection_reasons": ["not_unique", "visual_collision"], "validation_date": "2025-05-25"}, {"id": "NG0655", "symbol": "🏆", "code": "ng:future:race", "fallback": "[race]", "description": "Race futures", "category": "async_concurrency", "batch": 1, "tier": "ultra", "approved_date": "2025-05-25", "validation_score": 1.4000000000000001, "status": "rejected", "rejection_reasons": ["not_unique", "visual_collision"], "validation_date": "2025-05-25"}, {"id": "NG0656", "symbol": "⚡", "code": "ng:future:first", "fallback": "[first]", "description": "First future", "category": "async_concurrency", "batch": 1, "tier": "ultra", "approved_date": "2025-05-25", "validation_score": 1.4000000000000001, "status": "rejected", "rejection_reasons": ["not_unique", "visual_collision"], "validation_date": "2025-05-25"}, {"id": "NG0657", "symbol": "🔗", "code": "ng:future:chain", "fallback": "[chain]", "description": "Chain futures", "category": "async_concurrency", "batch": 1, "tier": "ultra", "approved_date": "2025-05-25", "validation_score": 1.4000000000000001, "status": "rejected", "rejection_reasons": ["not_unique", "visual_collision"], "validation_date": "2025-05-25"}, {"id": "NG0658", "symbol": "🌊", "code": "ng:future:sequence", "fallback": "[seq]", "description": "Sequence futures", "category": "async_concurrency", "batch": 1, "tier": "ultra", "approved_date": "2025-05-25", "validation_score": 1.4000000000000001, "status": "rejected", "rejection_reasons": ["not_unique", "visual_collision"], "validation_date": "2025-05-25"}, {"id": "NG0659", "symbol": "🎯", "code": "ng:future:select", "fallback": "[select]", "description": "Select future", "category": "async_concurrency", "batch": 1, "tier": "ultra", "approved_date": "2025-05-25", "validation_score": 1.4000000000000001, "status": "rejected", "rejection_reasons": ["not_unique", "visual_collision"], "validation_date": "2025-05-25"}, {"id": "NG0660", "symbol": "🔀", "code": "ng:future:merge", "fallback": "[merge]", "description": "Merge futures", "category": "async_concurrency", "batch": 1, "tier": "ultra", "approved_date": "2025-05-25", "validation_score": 1.4000000000000001, "status": "rejected", "rejection_reasons": ["visual_collision"], "validation_date": "2025-05-25"}, {"id": "NG0661", "symbol": "✨", "code": "ng:completion:completion", "fallback": "[comp]", "description": "Completion", "category": "async_concurrency", "batch": 1, "tier": "ultra", "approved_date": "2025-05-25", "validation_score": 1.4000000000000001, "status": "rejected", "rejection_reasons": ["not_unique", "visual_collision"], "validation_date": "2025-05-25"}, {"id": "NG0662", "symbol": "💥", "code": "ng:completion:error", "fallback": "[error]", "description": "Completion errore", "category": "async_concurrency", "batch": 1, "tier": "ultra", "approved_date": "2025-05-25", "validation_score": 1.4000000000000001, "status": "rejected", "rejection_reasons": ["not_unique", "visual_collision"], "validation_date": "2025-05-25"}, {"id": "NG0663", "symbol": "🎭", "code": "ng:completion:stage", "fallback": "[stage]", "description": "Stage completion", "category": "async_concurrency", "batch": 1, "tier": "ultra", "approved_date": "2025-05-25", "validation_score": 1.4000000000000001, "status": "rejected", "rejection_reasons": ["not_unique", "visual_collision"], "validation_date": "2025-05-25"}, {"id": "NG0664", "symbol": "📋", "code": "ng:async:promise", "fallback": "[promise]", "description": "Promessa pendente", "category": "async_concurrency", "batch": 1, "tier": "ultra", "approved_date": "2025-05-25", "validation_score": 1.3, "status": "rejected", "rejection_reasons": ["not_unique", "visual_collision"], "validation_date": "2025-05-25"}, {"id": "NG0665", "symbol": "✅", "code": "ng:async:resolved", "fallback": "[resolved]", "description": "<PERSON><PERSON><PERSON> risolta", "category": "async_concurrency", "batch": 1, "tier": "ultra", "approved_date": "2025-05-25", "validation_score": 1.3, "status": "rejected", "rejection_reasons": ["not_unique", "visual_collision"], "validation_date": "2025-05-25"}, {"id": "NG0666", "symbol": "❌", "code": "ng:async:rejected", "fallback": "[rejected]", "description": "Promessa <PERSON>", "category": "async_concurrency", "batch": 1, "tier": "ultra", "approved_date": "2025-05-25", "validation_score": 1.3, "status": "rejected", "rejection_reasons": ["not_unique", "visual_collision"], "validation_date": "2025-05-25"}, {"id": "NG0667", "symbol": "🚪", "code": "ng:async:context", "fallback": "[context]", "description": "<PERSON>o asincrono", "category": "async_concurrency", "batch": 1, "tier": "ultra", "approved_date": "2025-05-25", "validation_score": 1.3, "status": "rejected", "rejection_reasons": ["not_unique", "visual_collision"], "validation_date": "2025-05-25"}, {"id": "NG0668", "symbol": "⏸", "code": "ng:thread:suspend", "fallback": "[suspend]", "description": "Suspend thread", "category": "async_concurrency", "batch": 1, "tier": "ultra", "approved_date": "2025-05-25", "validation_score": 1.3, "status": "rejected", "rejection_reasons": ["not_unique", "visual_collision"], "validation_date": "2025-05-25"}, {"id": "NG0669", "symbol": "⏱", "code": "ng:worker:timeout", "fallback": "[timeout]", "description": "Timeout task", "category": "async_concurrency", "batch": 1, "tier": "ultra", "approved_date": "2025-05-25", "validation_score": 1.3, "status": "rejected", "rejection_reasons": ["not_unique", "visual_collision"], "validation_date": "2025-05-25"}, {"id": "NG0670", "symbol": "🔔", "code": "ng:schedule:trigger", "fallback": "[trigger]", "description": "Trigger schedule", "category": "async_concurrency", "batch": 1, "tier": "ultra", "approved_date": "2025-05-25", "validation_score": 1.3, "status": "rejected", "rejection_reasons": ["not_unique", "visual_collision"], "validation_date": "2025-05-25"}, {"id": "NG0671", "symbol": "🔒", "code": "ng:sync:lock", "fallback": "[lock]", "description": "Lock", "category": "async_concurrency", "batch": 1, "tier": "ultra", "approved_date": "2025-05-25", "validation_score": 1.3, "status": "rejected", "rejection_reasons": ["not_unique", "visual_collision"], "validation_date": "2025-05-25"}, {"id": "NG0672", "symbol": "⏱", "code": "ng:sync:timeout", "fallback": "[timeout]", "description": "Timeout lock", "category": "async_concurrency", "batch": 1, "tier": "ultra", "approved_date": "2025-05-25", "validation_score": 1.3, "status": "rejected", "rejection_reasons": ["not_unique", "visual_collision"], "validation_date": "2025-05-25"}, {"id": "NG0673", "symbol": "🔀", "code": "ng:sync:upgrade", "fallback": "[upgrade]", "description": "Upgrade lock", "category": "async_concurrency", "batch": 1, "tier": "ultra", "approved_date": "2025-05-25", "validation_score": 1.3, "status": "rejected", "rejection_reasons": ["visual_collision"], "validation_date": "2025-05-25"}, {"id": "NG0674", "symbol": "🔽", "code": "ng:sync:downgrade", "fallback": "[downgrade]", "description": "Downgrade lock", "category": "async_concurrency", "batch": 1, "tier": "ultra", "approved_date": "2025-05-25", "validation_score": 1.3, "status": "rejected", "rejection_reasons": ["visual_collision"], "validation_date": "2025-05-25"}, {"id": "NG0675", "symbol": "⬆", "code": "ng:sync:acquire", "fallback": "[acquire]", "description": "Acquire semaforo", "category": "async_concurrency", "batch": 1, "tier": "ultra", "approved_date": "2025-05-25", "validation_score": 1.3, "status": "rejected", "rejection_reasons": ["visual_collision"], "validation_date": "2025-05-25"}, {"id": "NG0676", "symbol": "⬇", "code": "ng:sync:release", "fallback": "[release]", "description": "Release semaforo", "category": "async_concurrency", "batch": 1, "tier": "ultra", "approved_date": "2025-05-25", "validation_score": 1.3, "status": "rejected", "rejection_reasons": ["visual_collision"], "validation_date": "2025-05-25"}, {"id": "NG0677", "symbol": "🚫", "code": "ng:sync:blocked", "fallback": "[blocked]", "description": "Blocked semaforo", "category": "async_concurrency", "batch": 1, "tier": "ultra", "approved_date": "2025-05-25", "validation_score": 1.3, "status": "rejected", "rejection_reasons": ["visual_collision"], "validation_date": "2025-05-25"}, {"id": "NG0678", "symbol": "📣", "code": "ng:sync:broadcast", "fallback": "[broadcast]", "description": "Broadcast evento", "category": "async_concurrency", "batch": 1, "tier": "ultra", "approved_date": "2025-05-25", "validation_score": 1.3, "status": "rejected", "rejection_reasons": ["not_unique", "visual_collision"], "validation_date": "2025-05-25"}, {"id": "NG0679", "symbol": "⏳", "code": "ng:future:pending", "fallback": "[pending]", "description": "Future pendente", "category": "async_concurrency", "batch": 1, "tier": "ultra", "approved_date": "2025-05-25", "validation_score": 1.3, "status": "rejected", "rejection_reasons": ["not_unique", "visual_collision"], "validation_date": "2025-05-25"}, {"id": "NG0680", "symbol": "🚫", "code": "ng:future:cancelled", "fallback": "[cancelled]", "description": "Future cancellato", "category": "async_concurrency", "batch": 1, "tier": "ultra", "approved_date": "2025-05-25", "validation_score": 1.3, "status": "rejected", "rejection_reasons": ["visual_collision"], "validation_date": "2025-05-25"}, {"id": "NG0681", "symbol": "⏱", "code": "ng:future:timeout", "fallback": "[timeout]", "description": "Future timeout", "category": "async_concurrency", "batch": 1, "tier": "ultra", "approved_date": "2025-05-25", "validation_score": 1.3, "status": "rejected", "rejection_reasons": ["not_unique", "visual_collision"], "validation_date": "2025-05-25"}, {"id": "NG0682", "symbol": "🔄", "code": "ng:future:running", "fallback": "[running]", "description": "Future in esecuzione", "category": "async_concurrency", "batch": 1, "tier": "ultra", "approved_date": "2025-05-25", "validation_score": 1.3, "status": "rejected", "rejection_reasons": ["not_unique", "visual_collision"], "validation_date": "2025-05-25"}, {"id": "NG0683", "symbol": "🏁", "code": "ng:future:finally", "fallback": "[finally]", "description": "Finally future", "category": "async_concurrency", "batch": 1, "tier": "ultra", "approved_date": "2025-05-25", "validation_score": 1.3, "status": "rejected", "rejection_reasons": ["not_unique", "visual_collision"], "validation_date": "2025-05-25"}, {"id": "NG0684", "symbol": "🎭", "code": "ng:future:flatmap", "fallback": "[flatmap]", "description": "FlatMap future", "category": "async_concurrency", "batch": 1, "tier": "ultra", "approved_date": "2025-05-25", "validation_score": 1.3, "status": "rejected", "rejection_reasons": ["not_unique", "visual_collision"], "validation_date": "2025-05-25"}, {"id": "NG0685", "symbol": "🎉", "code": "ng:completion:success", "fallback": "[success]", "description": "Completion successo", "category": "async_concurrency", "batch": 1, "tier": "ultra", "approved_date": "2025-05-25", "validation_score": 1.3, "status": "rejected", "rejection_reasons": ["not_unique", "visual_collision"], "validation_date": "2025-05-25"}, {"id": "NG0686", "symbol": "📝", "code": "ng:completion:callback", "fallback": "[callback]", "description": "Callback completion", "category": "async_concurrency", "batch": 1, "tier": "ultra", "approved_date": "2025-05-25", "validation_score": 1.3, "status": "rejected", "rejection_reasons": ["not_unique", "visual_collision"], "validation_date": "2025-05-25"}, {"id": "NG0687", "symbol": "🔔", "code": "ng:completion:handler", "fallback": "[handler]", "description": "Handler completion", "category": "async_concurrency", "batch": 1, "tier": "ultra", "approved_date": "2025-05-25", "validation_score": 1.3, "status": "rejected", "rejection_reasons": ["not_unique", "visual_collision"], "validation_date": "2025-05-25"}, {"id": "NG0688", "symbol": "⚡", "code": "ng:completion:immediate", "fallback": "[immediate]", "description": "Completion immediato", "category": "async_concurrency", "batch": 1, "tier": "ultra", "approved_date": "2025-05-25", "validation_score": 1.3, "status": "rejected", "rejection_reasons": ["not_unique", "visual_collision"], "validation_date": "2025-05-25"}, {"id": "NG0689", "symbol": "⏳", "code": "ng:completion:deferred", "fallback": "[deferred]", "description": "Completion differito", "category": "async_concurrency", "batch": 1, "tier": "ultra", "approved_date": "2025-05-25", "validation_score": 1.3, "status": "rejected", "rejection_reasons": ["not_unique", "visual_collision"], "validation_date": "2025-05-25"}], "validation_thresholds": {"min_score": 1.0, "max_tokenizer_cost": 2, "max_collision_risk": 0.3, "min_semantic_clarity": 0.7}}