#!/usr/bin/env python3
"""
NEUROGLYPH - Simple Duplicate Fixer
===================================

Rimuove duplicati dal registry in modo semplice e diretto.
"""

import json
from collections import defaultdict

def fix_duplicates():
    """Rimuove duplicati dal registry."""
    
    print("🔧 NEUROGLYPH - Fixing duplicates...")
    
    # Carica registry
    with open('neuroglyph/core/symbols_registry.json', 'r', encoding='utf-8') as f:
        data = json.load(f)
    
    symbols = data.get('approved_symbols', [])
    print(f"📊 Simboli originali: {len(symbols)}")
    
    # Traccia simboli visti
    seen_symbols = set()
    seen_codes = set()
    seen_ids = set()
    
    # Lista simboli unici
    unique_symbols = []
    
    # Contatori
    removed_symbol_dups = 0
    removed_code_dups = 0
    removed_id_dups = 0
    
    for symbol in symbols:
        symbol_char = symbol.get('symbol', '')
        code = symbol.get('code', '')
        symbol_id = symbol.get('id', '')
        
        # Verifica duplicati
        is_duplicate = False
        
        if symbol_char in seen_symbols:
            removed_symbol_dups += 1
            is_duplicate = True
            print(f"  ❌ Simbolo duplicato rimosso: {symbol_char} ({code})")
        
        if code in seen_codes:
            removed_code_dups += 1
            is_duplicate = True
            print(f"  ❌ Codice duplicato rimosso: {code} ({symbol_char})")
        
        if symbol_id in seen_ids:
            removed_id_dups += 1
            is_duplicate = True
            print(f"  ❌ ID duplicato rimosso: {symbol_id} ({symbol_char})")
        
        # Se non è duplicato, aggiungilo
        if not is_duplicate:
            unique_symbols.append(symbol)
            seen_symbols.add(symbol_char)
            seen_codes.add(code)
            seen_ids.add(symbol_id)
    
    # Aggiorna registry
    data['approved_symbols'] = unique_symbols
    
    # Aggiorna statistiche
    data['stats']['approved'] = len(unique_symbols)
    data['stats']['total_submissions'] = len(unique_symbols)
    
    # Salva registry pulito
    with open('neuroglyph/core/symbols_registry.json', 'w', encoding='utf-8') as f:
        json.dump(data, f, indent=2, ensure_ascii=False)
    
    print(f"\n✅ CLEANUP COMPLETATO:")
    print(f"  • Simboli finali: {len(unique_symbols)}")
    print(f"  • Duplicati simbolo rimossi: {removed_symbol_dups}")
    print(f"  • Duplicati codice rimossi: {removed_code_dups}")
    print(f"  • Duplicati ID rimossi: {removed_id_dups}")
    print(f"  • Totale rimossi: {len(symbols) - len(unique_symbols)}")
    print(f"💾 Registry aggiornato!")

if __name__ == "__main__":
    fix_duplicates()
