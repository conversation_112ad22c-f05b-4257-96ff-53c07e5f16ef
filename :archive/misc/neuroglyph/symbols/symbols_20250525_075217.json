[{"id": "NG0001", "symbol": "⊕", "name": "add", "description": "Addition operator - mathematical addition", "category": "operator", "aliases": ["plus", "sum", "combine"], "status": "approved", "version": "1.0", "code": "ng:operator:add", "fallback": "[+]", "unicode_point": "U+2295", "token_cost": 1, "token_density": 1.0, "llm_support": ["openai", "qwen", "deepseek", "llama"]}, {"id": "NG0002", "symbol": "⊖", "name": "subtract", "description": "Subtraction operator - mathematical subtraction", "category": "operator", "aliases": ["minus", "sub", "remove"], "status": "approved", "version": "1.0", "code": "ng:operator:subtract", "fallback": "[-]", "unicode_point": "U+2296", "token_cost": 1, "token_density": 1.0, "llm_support": ["openai", "qwen", "deepseek", "llama"]}, {"id": "NG0003", "symbol": "⊗", "name": "multiply", "description": "Multiplication operator - mathematical multiplication", "category": "operator", "aliases": ["times", "mul", "product"], "status": "approved", "version": "1.0", "code": "ng:operator:multiply", "fallback": "[*]", "unicode_point": "U+2297", "token_cost": 1, "token_density": 1.0, "llm_support": ["openai", "qwen", "deepseek", "llama"]}, {"id": "NG0004", "symbol": "⊘", "name": "divide", "description": "Division operator - mathematical division", "category": "operator", "aliases": ["div", "quotient", "split"], "status": "approved", "version": "1.0", "code": "ng:operator:divide", "fallback": "[/]", "unicode_point": "U+2298", "token_cost": 1, "token_density": 1.0, "llm_support": ["openai", "qwen", "deepseek", "llama"]}, {"id": "NG0005", "symbol": "≡", "name": "equals", "description": "Equality operator - logical equivalence", "category": "logic", "aliases": ["eq", "equivalent", "same"], "status": "approved", "version": "1.0", "code": "ng:logic:equals", "fallback": "[==]", "unicode_point": "U+2261", "token_cost": 1, "token_density": 1.0, "llm_support": ["openai", "qwen", "deepseek", "llama"]}, {"id": "NG0006", "symbol": "≢", "name": "not_equals", "description": "Inequality operator - logical non-equivalence", "category": "logic", "aliases": ["ne", "different", "not_same"], "status": "approved", "version": "1.0", "code": "ng:logic:not_equals", "fallback": "[!=]", "unicode_point": "U+2262", "token_cost": 1, "token_density": 1.0, "llm_support": ["openai", "qwen", "deepseek", "llama"]}, {"id": "NG0007", "symbol": "∧", "name": "and", "description": "Logical AND operator - conjunction", "category": "logic", "aliases": ["conjunction", "both", "all"], "status": "approved", "version": "1.0", "code": "ng:logic:and", "fallback": "[AND]", "unicode_point": "U+2227", "token_cost": 1, "token_density": 1.0, "llm_support": ["openai", "qwen", "deepseek", "llama"]}, {"id": "NG0008", "symbol": "∨", "name": "or", "description": "Logical OR operator - disjunction", "category": "logic", "aliases": ["disjunction", "either", "any"], "status": "approved", "version": "1.0", "code": "ng:logic:or", "fallback": "[OR]", "unicode_point": "U+2228", "token_cost": 1, "token_density": 1.0, "llm_support": ["openai", "qwen", "deepseek", "llama"]}, {"id": "NG0009", "symbol": "¬", "name": "not", "description": "Logical NOT operator - negation", "category": "logic", "aliases": ["negation", "inverse", "opposite"], "status": "approved", "version": "1.0", "code": "ng:logic:not", "fallback": "[NOT]", "unicode_point": "U+00AC", "token_cost": 1, "token_density": 1.0, "llm_support": ["openai", "qwen", "deepseek", "llama"]}, {"id": "NG0010", "symbol": "∈", "name": "in", "description": "Set membership operator - element belongs to set", "category": "logic", "aliases": ["belongs", "member", "contains"], "status": "approved", "version": "1.0", "code": "ng:logic:in", "fallback": "[IN]", "unicode_point": "U+2208", "token_cost": 1, "token_density": 1.0, "llm_support": ["openai", "qwen", "deepseek", "llama"]}, {"id": "NG0011", "symbol": "∉", "name": "not_in", "description": "Set non-membership operator - element does not belong to set", "category": "logic", "aliases": ["not_belongs", "not_member", "excludes"], "status": "approved", "version": "1.0", "code": "ng:logic:not_in", "fallback": "[NOTIN]", "unicode_point": "U+2209", "token_cost": 1, "token_density": 1.0, "llm_support": ["openai", "qwen", "deepseek", "llama"]}, {"id": "NG0012", "symbol": "⊨", "name": "entails", "description": "Logical entailment operator - semantic consequence", "category": "reasoning", "aliases": ["models", "proves", "derives"], "status": "approved", "version": "1.0", "code": "ng:reasoning:entails", "fallback": "[ENTAILS]", "unicode_point": "U+22A8", "token_cost": 1, "token_density": 1.0, "llm_support": ["openai", "qwen", "deepseek", "llama"]}, {"id": "NG0013", "symbol": "⇌", "name": "biconditional", "description": "Biconditional operator - if and only if", "category": "logic", "aliases": ["iff", "equivalent", "bidirectional"], "status": "approved", "version": "1.0", "code": "ng:logic:biconditional", "fallback": "[IFF]", "unicode_point": "U+21CC", "token_cost": 1, "token_density": 1.0, "llm_support": ["openai", "qwen", "deepseek", "llama"]}, {"id": "NG0014", "symbol": "⟨⟩", "name": "function", "description": "Function definition construct - callable unit", "category": "structure", "aliases": ["fn", "method", "procedure"], "status": "approved", "version": "1.0", "code": "ng:structure:function", "fallback": "[FN]", "unicode_point": "U+27E8U+27E9", "token_cost": 1, "token_density": 1.0, "llm_support": ["openai", "qwen", "deepseek", "llama"]}, {"id": "NG0015", "symbol": "⟪⟫", "name": "class", "description": "Class definition construct - object template", "category": "structure", "aliases": ["cls", "type", "template"], "status": "approved", "version": "1.0", "code": "ng:structure:class", "fallback": "[CLS]", "unicode_point": "U+27EAU+27EB", "token_cost": 1, "token_density": 1.0, "llm_support": ["openai", "qwen", "deepseek", "llama"]}, {"id": "NG0016", "symbol": "◊", "name": "if", "description": "Conditional construct - if statement", "category": "flow", "aliases": ["condition", "branch", "test"], "status": "approved", "version": "1.0", "code": "ng:flow:if", "fallback": "[IF]", "unicode_point": "U+25CA", "token_cost": 1, "token_density": 1.0, "llm_support": ["openai", "qwen", "deepseek", "llama"]}, {"id": "NG0017", "symbol": "◈", "name": "else", "description": "Alternative construct - else statement", "category": "flow", "aliases": ["otherwise", "alternative", "default"], "status": "approved", "version": "1.0", "code": "ng:flow:else", "fallback": "[ELSE]", "unicode_point": "U+25C8", "token_cost": 1, "token_density": 1.0, "llm_support": ["openai", "qwen", "deepseek", "llama"]}, {"id": "NG0018", "symbol": "⟲", "name": "for", "description": "For loop construct - iteration", "category": "flow", "aliases": ["loop", "iterate", "repeat"], "status": "approved", "version": "1.0", "code": "ng:flow:for", "fallback": "[FOR]", "unicode_point": "U+27F2", "token_cost": 1, "token_density": 1.0, "llm_support": ["openai", "qwen", "deepseek", "llama"]}, {"id": "NG0019", "symbol": "⟳", "name": "while", "description": "While loop construct - conditional iteration", "category": "flow", "aliases": ["loop", "continue", "persist"], "status": "approved", "version": "1.0", "code": "ng:flow:while", "fallback": "[WHILE]", "unicode_point": "U+27F3", "token_cost": 1, "token_density": 1.0, "llm_support": ["openai", "qwen", "deepseek", "llama"]}, {"id": "NG0020", "symbol": "⤴", "name": "return", "description": "Return construct - value output", "category": "flow", "aliases": ["output", "yield", "result"], "status": "approved", "version": "1.0", "code": "ng:flow:return", "fallback": "[RETURN]", "unicode_point": "U+2934", "token_cost": 1, "token_density": 1.0, "llm_support": ["openai", "qwen", "deepseek", "llama"]}]