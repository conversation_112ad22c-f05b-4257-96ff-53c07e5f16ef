[{"id": "NG0001", "symbol": "⊕", "name": "fix", "description": "Fix concept in action - primary repair operation", "category": "action", "aliases": ["repair", "correct", "resolve"], "status": "approved", "version": "1.0", "code": "ng:action:fix", "fallback": "[FIX]", "unicode_point": "U+2295"}, {"id": "NG0002", "symbol": "⧉", "name": "create", "description": "Create concept in action - primary creation operation", "category": "action", "aliases": ["make", "build", "generate"], "status": "approved", "version": "1.0", "code": "ng:action:create", "fallback": "[CREATE]", "unicode_point": "U+29C9"}, {"id": "NG0003", "symbol": "↯", "name": "error", "description": "Error concept in state - critical failure indicator", "category": "state", "aliases": ["failure", "exception", "crash"], "status": "approved", "version": "1.0", "code": "ng:state:error", "fallback": "[ERROR]", "unicode_point": "U+21AF"}, {"id": "NG0004", "symbol": "⊖", "name": "delete", "description": "Delete concept in action - removal operation", "category": "action", "aliases": ["remove", "destroy", "eliminate"], "status": "approved", "version": "1.0", "code": "ng:action:delete", "fallback": "[DELETE]", "unicode_point": "U+2296"}, {"id": "NG0005", "symbol": "⊞", "name": "variable", "description": "Variable concept in entity - data container", "category": "entity", "aliases": ["var", "data", "value"], "status": "approved", "version": "1.0", "code": "ng:entity:variable", "fallback": "[VAR]", "unicode_point": "U+229E"}, {"id": "NG0006", "symbol": "⊂", "name": "subset", "description": "Subset concept in logic - containment relation", "category": "logic", "aliases": ["contains", "includes", "within"], "status": "approved", "version": "1.0", "code": "ng:logic:subset", "fallback": "[SUBSET]", "unicode_point": "U+2282"}, {"id": "NG0007", "symbol": "⬒", "name": "block", "description": "Block concept in structure - code block", "category": "structure", "aliases": ["scope", "section", "segment"], "status": "approved", "version": "1.0", "code": "ng:structure:block", "fallback": "[BLOCK]", "unicode_point": "U+2B12"}, {"id": "NG0008", "symbol": "✖", "name": "abort", "description": "Abort concept in action - termination operation", "category": "action", "aliases": ["cancel", "stop", "terminate"], "status": "approved", "version": "1.0", "code": "ng:action:abort", "fallback": "[ABORT]", "unicode_point": "U+2716"}, {"id": "NG0009", "symbol": "⚠", "name": "warning", "description": "Warning concept in state - caution indicator", "category": "state", "aliases": ["caution", "alert", "notice"], "status": "approved", "version": "1.0", "code": "ng:state:warning", "fallback": "[WARN]", "unicode_point": "U+26A0"}, {"id": "NG0010", "symbol": "✔", "name": "success", "description": "Success concept in state - completion indicator", "category": "state", "aliases": ["complete", "done", "finished"], "status": "approved", "version": "1.0", "code": "ng:state:success", "fallback": "[SUCCESS]", "unicode_point": "U+2714"}, {"id": "NG0011", "symbol": "∧", "name": "and", "description": "And concept in logic - logical conjunction", "category": "logic", "aliases": ["conjunction", "both", "all"], "status": "approved", "version": "1.0", "code": "ng:logic:and", "fallback": "[AND]", "unicode_point": "U+2227"}, {"id": "NG0012", "symbol": "∨", "name": "or", "description": "Or concept in logic - logical disjunction", "category": "logic", "aliases": ["disjunction", "either", "any"], "status": "approved", "version": "1.0", "code": "ng:logic:or", "fallback": "[OR]", "unicode_point": "U+2228"}, {"id": "NG0013", "symbol": "¬", "name": "not", "description": "Not concept in logic - logical negation", "category": "logic", "aliases": ["negation", "inverse", "opposite"], "status": "approved", "version": "1.0", "code": "ng:logic:not", "fallback": "[NOT]", "unicode_point": "U+00AC"}, {"id": "NG0014", "symbol": "→", "name": "implies", "description": "Implies concept in logic - logical implication", "category": "logic", "aliases": ["then", "leads_to", "causes"], "status": "approved", "version": "1.0", "code": "ng:logic:implies", "fallback": "[IMPLIES]", "unicode_point": "U+2192"}, {"id": "NG0015", "symbol": "≡", "name": "equals", "description": "Equals concept in logic - equivalence relation", "category": "logic", "aliases": ["equivalent", "same", "identical"], "status": "approved", "version": "1.0", "code": "ng:logic:equals", "fallback": "[EQUALS]", "unicode_point": "U+2261"}, {"id": "NG0016", "symbol": "⊨", "name": "entails", "description": "Entails concept in logic - logical entailment", "category": "logic", "aliases": ["follows", "derives", "proves"], "status": "approved", "version": "1.0", "code": "ng:logic:entails", "fallback": "[ENTAILS]", "unicode_point": "U+22A8"}, {"id": "NG0017", "symbol": "⇌", "name": "biconditional", "description": "Biconditional concept in logic - if and only if", "category": "logic", "aliases": ["iff", "equivalent", "bidirectional"], "status": "approved", "version": "1.0", "code": "ng:logic:biconditional", "fallback": "[IFF]", "unicode_point": "U+21CC"}, {"id": "NG0018", "symbol": "📌", "name": "pin", "description": "Pin concept in memory - important marker", "category": "memory", "aliases": ["mark", "flag", "bookmark"], "status": "approved", "version": "1.0", "code": "ng:memory:pin", "fallback": "[PIN]", "unicode_point": "U+1F4CC"}, {"id": "NG0019", "symbol": "🧠", "name": "think", "description": "Think concept in reasoning - cognitive process", "category": "reasoning", "aliases": ["reason", "analyze", "process"], "status": "approved", "version": "1.0", "code": "ng:reasoning:think", "fallback": "[THINK]", "unicode_point": "U+1F9E0"}, {"id": "NG0020", "symbol": "⊘", "name": "null", "description": "Null concept in state - empty or undefined", "category": "state", "aliases": ["empty", "void", "undefined"], "status": "approved", "version": "1.0", "code": "ng:state:null", "fallback": "[NULL]", "unicode_point": "U+2298"}, {"id": "NG0021", "symbol": "⟨⟩", "name": "function", "description": "Function concept in structure - callable unit", "category": "structure", "aliases": ["method", "procedure", "routine"], "status": "approved", "version": "1.0", "code": "ng:structure:function", "fallback": "[FUNC]", "unicode_point": "U+27E8U+27E9"}, {"id": "NG0022", "symbol": "⟪⟫", "name": "class", "description": "Class concept in structure - object template", "category": "structure", "aliases": ["type", "template", "blueprint"], "status": "approved", "version": "1.0", "code": "ng:structure:class", "fallback": "[CLASS]", "unicode_point": "U+27EAU+27EB"}, {"id": "NG0023", "symbol": "◊", "name": "if", "description": "If concept in flow - conditional branch", "category": "flow", "aliases": ["condition", "branch", "test"], "status": "approved", "version": "1.0", "code": "ng:flow:if", "fallback": "[IF]", "unicode_point": "U+25CA"}, {"id": "NG0024", "symbol": "◈", "name": "else", "description": "Else concept in flow - alternative branch", "category": "flow", "aliases": ["otherwise", "alternative", "default"], "status": "approved", "version": "1.0", "code": "ng:flow:else", "fallback": "[ELSE]", "unicode_point": "U+25C8"}, {"id": "NG0025", "symbol": "⟲", "name": "for_loop", "description": "For loop concept in flow - iteration construct", "category": "flow", "aliases": ["iterate", "repeat", "cycle"], "status": "approved", "version": "1.0", "code": "ng:flow:for", "fallback": "[FOR]", "unicode_point": "U+27F2"}, {"id": "NG0026", "symbol": "⟳", "name": "while_loop", "description": "While loop concept in flow - conditional iteration", "category": "flow", "aliases": ["loop", "continue", "persist"], "status": "approved", "version": "1.0", "code": "ng:flow:while", "fallback": "[WHILE]", "unicode_point": "U+27F3"}, {"id": "NG0027", "symbol": "⤴", "name": "return", "description": "Return concept in flow - value output", "category": "flow", "aliases": ["output", "yield", "result"], "status": "approved", "version": "1.0", "code": "ng:flow:return", "fallback": "[RETURN]", "unicode_point": "U+2934"}, {"id": "NG0028", "symbol": "⤵", "name": "yield", "description": "Yield concept in flow - generator output", "category": "flow", "aliases": ["generate", "produce", "emit"], "status": "approved", "version": "1.0", "code": "ng:flow:yield", "fallback": "[YIELD]", "unicode_point": "U+2935"}, {"id": "NG0029", "symbol": "⊰", "name": "async", "description": "Async concept in flow - asynchronous operation", "category": "flow", "aliases": ["concurrent", "parallel", "non_blocking"], "status": "approved", "version": "1.0", "code": "ng:flow:async", "fallback": "[ASYNC]", "unicode_point": "U+22B0"}, {"id": "NG0030", "symbol": "⊱", "name": "await", "description": "Await concept in flow - wait for async result", "category": "flow", "aliases": ["wait", "suspend", "pause"], "status": "approved", "version": "1.0", "code": "ng:flow:await", "fallback": "[AWAIT]", "unicode_point": "U+22B1"}]