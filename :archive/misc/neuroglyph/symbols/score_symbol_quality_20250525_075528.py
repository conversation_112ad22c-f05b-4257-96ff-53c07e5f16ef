"""
Punteggio qualità simbolo: density, token cost, unicità, fallback
"""
import json
from pathlib import Path

path = Path("core/symbols_ultra.json")
with open(path, "r", encoding="utf-8") as f:
    data = json.load(f)

for s in data["symbols"]:
    score = 1.0
    if len(s["symbol"]) != 1:
        score -= 0.3
    if not s["fallback"]:
        score -= 0.3
    if s.get("token_cost", 1) > 2:
        score -= 0.2
    if s.get("token_density", 1.0) < 0.9:
        score -= 0.2
    print(f"{s['symbol']} ({s['code']}): score = {round(score * 100)}")
