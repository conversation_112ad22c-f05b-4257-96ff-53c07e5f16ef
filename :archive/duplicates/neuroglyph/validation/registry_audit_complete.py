#!/usr/bin/env python3
"""
NEUROGLYPH REGISTRY AUDIT COMPLETO
Verifica rigorosa di integrità, duplicati, qualità e validazione pipeline
"""

import json
import sys
from pathlib import Path
from datetime import datetime
from typing import Dict, List, Any, Set, Tuple
from collections import defaultdict, Counter

class RegistryAuditor:
    """Auditor completo per registry NEUROGLYPH."""
    
    def __init__(self, registry_path: str = "neuroglyph/core/symbols_registry.json"):
        self.registry_path = Path(registry_path)
        self.registry = {}
        self.audit_timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        self.audit_results = {}
        
    def load_registry(self) -> bool:
        """Carica registry per audit."""
        try:
            with open(self.registry_path, 'r', encoding='utf-8') as f:
                self.registry = json.load(f)
            symbols_count = len(self.registry.get("approved_symbols", []))
            print(f"✅ Registry caricato: {symbols_count} simboli")
            return True
        except Exception as e:
            print(f"❌ Errore caricamento registry: {e}")
            return False
    
    def check_duplicates(self) -> Dict[str, Any]:
        """Verifica duplicati in tutti i campi critici."""
        symbols = self.registry.get("approved_symbols", [])
        
        # Raccogli tutti i valori per campo
        ids = [s.get("id", "") for s in symbols]
        unicode_points = [s.get("unicode_point", "") for s in symbols]
        symbol_chars = [s.get("symbol", "") for s in symbols]
        codes = [s.get("code", "") for s in symbols]
        names = [s.get("name", "") for s in symbols]
        
        # Trova duplicati
        duplicate_results = {
            "total_symbols": len(symbols),
            "duplicate_ids": [],
            "duplicate_unicode": [],
            "duplicate_symbols": [],
            "duplicate_codes": [],
            "duplicate_names": [],
            "has_duplicates": False
        }
        
        # Check ID duplicati
        id_counts = Counter(ids)
        for id_val, count in id_counts.items():
            if count > 1 and id_val:
                duplicate_results["duplicate_ids"].append({"id": id_val, "count": count})
        
        # Check Unicode duplicati
        unicode_counts = Counter(unicode_points)
        for unicode_val, count in unicode_counts.items():
            if count > 1 and unicode_val:
                duplicate_results["duplicate_unicode"].append({"unicode": unicode_val, "count": count})
        
        # Check simboli duplicati
        symbol_counts = Counter(symbol_chars)
        for symbol_val, count in symbol_counts.items():
            if count > 1 and symbol_val:
                duplicate_results["duplicate_symbols"].append({"symbol": symbol_val, "count": count})
        
        # Check code duplicati
        code_counts = Counter(codes)
        for code_val, count in code_counts.items():
            if count > 1 and code_val:
                duplicate_results["duplicate_codes"].append({"code": code_val, "count": count})
        
        # Check nomi duplicati
        name_counts = Counter(names)
        for name_val, count in name_counts.items():
            if count > 1 and name_val:
                duplicate_results["duplicate_names"].append({"name": name_val, "count": count})
        
        # Determina se ci sono duplicati
        duplicate_results["has_duplicates"] = any([
            duplicate_results["duplicate_ids"],
            duplicate_results["duplicate_unicode"],
            duplicate_results["duplicate_symbols"],
            duplicate_results["duplicate_codes"],
            duplicate_results["duplicate_names"]
        ])
        
        return duplicate_results
    
    def check_data_integrity(self) -> Dict[str, Any]:
        """Verifica integrità dei dati."""
        symbols = self.registry.get("approved_symbols", [])
        
        integrity_results = {
            "total_symbols": len(symbols),
            "missing_fields": [],
            "invalid_formats": [],
            "quality_issues": [],
            "integrity_score": 0
        }
        
        required_fields = ["id", "symbol", "unicode_point", "name", "code", "fallback", "category"]
        
        for i, symbol in enumerate(symbols):
            symbol_issues = []
            
            # Check campi obbligatori
            for field in required_fields:
                if not symbol.get(field):
                    symbol_issues.append(f"Missing {field}")
            
            # Check formato ID
            symbol_id = symbol.get("id", "")
            if symbol_id and not symbol_id.startswith("NG"):
                symbol_issues.append("Invalid ID format")
            
            # Check formato Unicode
            unicode_point = symbol.get("unicode_point", "")
            if unicode_point and not unicode_point.startswith("U+"):
                symbol_issues.append("Invalid Unicode format")
            
            # Check formato code
            code = symbol.get("code", "")
            if code and not code.startswith("ng:"):
                symbol_issues.append("Invalid code format")
            
            # Check validation score
            score = symbol.get("validation_score", 0)
            if score < 95.0:
                symbol_issues.append(f"Low validation score: {score}")
            
            # Check token cost
            token_cost = symbol.get("token_cost", 999)
            if token_cost > 2:
                symbol_issues.append(f"High token cost: {token_cost}")
            
            # Check fallback length
            fallback = symbol.get("fallback", "")
            if len(fallback) > 10:
                symbol_issues.append(f"Long fallback: {len(fallback)} chars")
            
            if symbol_issues:
                integrity_results["quality_issues"].append({
                    "index": i,
                    "id": symbol_id,
                    "symbol": symbol.get("symbol", ""),
                    "issues": symbol_issues
                })
        
        # Calcola score integrità
        total_issues = len(integrity_results["quality_issues"])
        integrity_results["integrity_score"] = max(0, 100 - (total_issues / len(symbols) * 100))
        
        return integrity_results
    
    def check_unicode_safety(self) -> Dict[str, Any]:
        """Verifica sicurezza Unicode."""
        symbols = self.registry.get("approved_symbols", [])
        
        # Range Unicode sicuri
        safe_ranges = [
            (0x2200, 0x22FF, "Mathematical Operators"),
            (0x2300, 0x23FF, "Miscellaneous Technical"),
            (0x25A0, 0x25FF, "Geometric Shapes"),
            (0x2700, 0x27BF, "Dingbats"),
            (0x2900, 0x297F, "Supplemental Arrows-B"),
            (0x2980, 0x29FF, "Miscellaneous Mathematical Symbols-B"),
            (0x2A00, 0x2AFF, "Supplemental Mathematical Operators"),
            (0x2B00, 0x2BFF, "Miscellaneous Symbols and Arrows"),
            (0x1F300, 0x1F5FF, "Miscellaneous Symbols and Pictographs")
        ]
        
        unicode_results = {
            "total_symbols": len(symbols),
            "safe_unicode": 0,
            "unsafe_unicode": 0,
            "range_distribution": {},
            "unsafe_symbols": []
        }
        
        for symbol in symbols:
            unicode_point = symbol.get("unicode_point", "")
            if not unicode_point:
                continue
                
            try:
                code_point = int(unicode_point.replace("U+", ""), 16)
                is_safe = False
                
                for start, end, range_name in safe_ranges:
                    if start <= code_point <= end:
                        is_safe = True
                        unicode_results["range_distribution"][range_name] = \
                            unicode_results["range_distribution"].get(range_name, 0) + 1
                        break
                
                if is_safe:
                    unicode_results["safe_unicode"] += 1
                else:
                    unicode_results["unsafe_unicode"] += 1
                    unicode_results["unsafe_symbols"].append({
                        "id": symbol.get("id", ""),
                        "symbol": symbol.get("symbol", ""),
                        "unicode_point": unicode_point,
                        "code_point": code_point
                    })
                    
            except Exception:
                unicode_results["unsafe_unicode"] += 1
                unicode_results["unsafe_symbols"].append({
                    "id": symbol.get("id", ""),
                    "symbol": symbol.get("symbol", ""),
                    "unicode_point": unicode_point,
                    "error": "Invalid Unicode format"
                })
        
        return unicode_results
    
    def check_domain_distribution(self) -> Dict[str, Any]:
        """Verifica distribuzione domini."""
        symbols = self.registry.get("approved_symbols", [])
        
        domain_results = {
            "total_symbols": len(symbols),
            "domain_counts": {},
            "category_counts": {},
            "tier_distribution": {},
            "generator_distribution": {}
        }
        
        for symbol in symbols:
            # Conta per categoria
            category = symbol.get("category", "unknown")
            domain_results["category_counts"][category] = \
                domain_results["category_counts"].get(category, 0) + 1
            
            # Estrai dominio dal code
            code = symbol.get("code", "")
            if ":" in code:
                parts = code.split(":")
                if len(parts) >= 2:
                    domain = parts[1]
                    domain_results["domain_counts"][domain] = \
                        domain_results["domain_counts"].get(domain, 0) + 1
            
            # Conta per tier
            tier = symbol.get("tier", "unknown")
            domain_results["tier_distribution"][tier] = \
                domain_results["tier_distribution"].get(tier, 0) + 1
            
            # Conta per generator
            generator = symbol.get("generator", "unknown")
            domain_results["generator_distribution"][generator] = \
                domain_results["generator_distribution"].get(generator, 0) + 1
        
        return domain_results
    
    def check_quality_metrics(self) -> Dict[str, Any]:
        """Verifica metriche di qualità."""
        symbols = self.registry.get("approved_symbols", [])
        
        scores = [s.get("validation_score", 0) for s in symbols if s.get("validation_score")]
        token_costs = [s.get("token_cost", 999) for s in symbols if s.get("token_cost")]
        token_densities = [s.get("token_density", 0) for s in symbols if s.get("token_density")]
        
        quality_results = {
            "total_symbols": len(symbols),
            "score_stats": {
                "min": min(scores) if scores else 0,
                "max": max(scores) if scores else 0,
                "avg": sum(scores) / len(scores) if scores else 0,
                "below_95": sum(1 for s in scores if s < 95.0)
            },
            "token_cost_stats": {
                "min": min(token_costs) if token_costs else 0,
                "max": max(token_costs) if token_costs else 0,
                "avg": sum(token_costs) / len(token_costs) if token_costs else 0,
                "above_2": sum(1 for t in token_costs if t > 2)
            },
            "token_density_stats": {
                "min": min(token_densities) if token_densities else 0,
                "max": max(token_densities) if token_densities else 0,
                "avg": sum(token_densities) / len(token_densities) if token_densities else 0,
                "below_09": sum(1 for d in token_densities if d < 0.9)
            }
        }
        
        return quality_results
    
    def generate_audit_summary(self, duplicate_results: Dict[str, Any],
                             integrity_results: Dict[str, Any],
                             unicode_results: Dict[str, Any],
                             domain_results: Dict[str, Any],
                             quality_results: Dict[str, Any]) -> Dict[str, Any]:
        """Genera summary completo dell'audit."""
        
        # Calcola score complessivo
        duplicate_score = 100 if not duplicate_results["has_duplicates"] else 0
        integrity_score = integrity_results["integrity_score"]
        unicode_score = (unicode_results["safe_unicode"] / unicode_results["total_symbols"]) * 100
        quality_score = (quality_results["score_stats"]["avg"] / 100) * 100 if quality_results["score_stats"]["avg"] else 0
        
        overall_score = (duplicate_score + integrity_score + unicode_score + quality_score) / 4
        
        return {
            "audit_timestamp": self.audit_timestamp,
            "registry_version": self.registry.get("version", "unknown"),
            "registry_status": self.registry.get("status", "unknown"),
            "total_symbols": duplicate_results["total_symbols"],
            "audit_scores": {
                "duplicate_check": duplicate_score,
                "integrity_check": round(integrity_score, 1),
                "unicode_safety": round(unicode_score, 1),
                "quality_metrics": round(quality_score, 1),
                "overall_score": round(overall_score, 1)
            },
            "critical_issues": {
                "has_duplicates": duplicate_results["has_duplicates"],
                "integrity_issues": len(integrity_results["quality_issues"]),
                "unsafe_unicode": unicode_results["unsafe_unicode"],
                "low_quality_symbols": quality_results["score_stats"]["below_95"]
            },
            "registry_health": {
                "excellent": overall_score >= 95,
                "good": 85 <= overall_score < 95,
                "acceptable": 70 <= overall_score < 85,
                "needs_improvement": overall_score < 70
            },
            "recommendations": self._generate_recommendations(
                duplicate_results, integrity_results, unicode_results, quality_results
            )
        }
    
    def _generate_recommendations(self, duplicate_results: Dict[str, Any],
                                integrity_results: Dict[str, Any],
                                unicode_results: Dict[str, Any],
                                quality_results: Dict[str, Any]) -> List[str]:
        """Genera raccomandazioni per miglioramento."""
        recommendations = []
        
        if duplicate_results["has_duplicates"]:
            recommendations.append("CRITICO: Rimuovere duplicati dal registry")
        
        if integrity_results["integrity_score"] < 95:
            recommendations.append("Correggere problemi di integrità dati")
        
        if unicode_results["unsafe_unicode"] > 0:
            recommendations.append("Sostituire Unicode non sicuri con range approvati")
        
        if quality_results["score_stats"]["below_95"] > 0:
            recommendations.append("Migliorare simboli con validation score < 95.0")
        
        if not recommendations:
            recommendations.append("Registry in ottimo stato - pronto per produzione!")
        
        return recommendations

def main():
    """Esegue audit completo del registry."""
    print("🔍 NEUROGLYPH REGISTRY AUDIT COMPLETO")
    print("🎯 Verifica integrità, duplicati, qualità e validazione")
    print("=" * 60)
    
    auditor = RegistryAuditor()
    
    if not auditor.load_registry():
        sys.exit(1)
    
    print("🔍 Controllo duplicati...")
    duplicate_results = auditor.check_duplicates()
    
    print("🔍 Controllo integrità dati...")
    integrity_results = auditor.check_data_integrity()
    
    print("🔍 Controllo sicurezza Unicode...")
    unicode_results = auditor.check_unicode_safety()
    
    print("🔍 Controllo distribuzione domini...")
    domain_results = auditor.check_domain_distribution()
    
    print("🔍 Controllo metriche qualità...")
    quality_results = auditor.check_quality_metrics()
    
    print("📊 Generazione summary audit...")
    audit_summary = auditor.generate_audit_summary(
        duplicate_results, integrity_results, unicode_results, domain_results, quality_results
    )
    
    # Salva risultati audit
    audit_data = {
        "audit_summary": audit_summary,
        "duplicate_results": duplicate_results,
        "integrity_results": integrity_results,
        "unicode_results": unicode_results,
        "domain_results": domain_results,
        "quality_results": quality_results
    }
    
    output_path = f"neuroglyph/validation/registry_audit_{auditor.audit_timestamp}.json"
    with open(output_path, 'w', encoding='utf-8') as f:
        json.dump(audit_data, f, indent=2, ensure_ascii=False)
    
    # Stampa risultati
    summary = audit_summary
    print(f"\n🎉 AUDIT REGISTRY COMPLETATO!")
    print(f"📊 Simboli auditati: {summary['total_symbols']}")
    print(f"📋 Versione registry: {summary['registry_version']}")
    print(f"🏆 Score complessivo: {summary['audit_scores']['overall_score']}/100")
    
    print(f"\n📈 AUDIT SCORES:")
    for check, score in summary["audit_scores"].items():
        if check != "overall_score":
            print(f"  • {check.replace('_', ' ').title()}: {score}/100")
    
    print(f"\n🚨 PROBLEMI CRITICI:")
    issues = summary["critical_issues"]
    print(f"  • Duplicati: {'❌' if issues['has_duplicates'] else '✅'}")
    print(f"  • Problemi integrità: {issues['integrity_issues']}")
    print(f"  • Unicode non sicuri: {issues['unsafe_unicode']}")
    print(f"  • Simboli bassa qualità: {issues['low_quality_symbols']}")
    
    print(f"\n💡 RACCOMANDAZIONI:")
    for rec in summary["recommendations"]:
        print(f"  • {rec}")
    
    print(f"\n💾 Audit salvato: {output_path}")
    
    # Determina stato registry
    health = summary["registry_health"]
    if health["excellent"]:
        print(f"\n✅ REGISTRY IN STATO ECCELLENTE - PRONTO PER PRODUZIONE!")
        return True
    elif health["good"]:
        print(f"\n🟡 REGISTRY IN BUONO STATO - MIGLIORAMENTI MINORI RACCOMANDATI")
        return True
    elif health["acceptable"]:
        print(f"\n🟠 REGISTRY ACCETTABILE - MIGLIORAMENTI NECESSARI")
        return False
    else:
        print(f"\n🔴 REGISTRY NECESSITA MIGLIORAMENTI SIGNIFICATIVI")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
