#!/usr/bin/env python3
"""
🧠 NEUROGLYPH ULTRA - Definitive Symbol Validator
=================================================

Validatore definitivo che implementa i criteri USU/CTU/LCL per NEUROGLYPH ULTRA.
Segue le specifiche esatte per rendere il vocabolario simbolico perfetto.

Criteri implementati:
1. USU - Unicità Simbolica Universale
2. CTU - Codifica Testuale Unificata  
3. LCL - LLM Compatibility Layer
4. Categorie e Significato
5. Requisiti Tecnici e Formali
6. Tracciabilità e Storico

Usage: python neuroglyph_ultra_validator.py <symbol> <code> <fallback> <category> <meaning>
"""

import json
import sys
import unicodedata
from typing import Dict, Any, List, Set, Optional
from pathlib import Path
import re

class NeuroGlyphUltraValidator:
    """Validatore definitivo per simboli NEUROGLYPH ULTRA."""
    
    def __init__(self):
        # Categorie valide secondo specifiche ULTRA
        self.valid_categories = {
            "control", "operator", "data_type", "reasoning", "logic", 
            "memory", "meta", "structure", "semantic_marker", "construct", 
            "entity", "state", "flow", "variable"
        }
        
        # Paths
        self.symbols_path = Path("core/symbols_ultra.json")
        self.registry_path = Path("core/symbols_registry.json")
        
        # Cache simboli esistenti
        self.existing_symbols: Set[str] = set()
        self.existing_codes: Set[str] = set()
        self.existing_fallbacks: Set[str] = set()
        
        self._load_existing_symbols()
        
    def _load_existing_symbols(self):
        """Carica simboli esistenti per controllo duplicati."""
        try:
            if self.registry_path.exists():
                with open(self.registry_path, 'r', encoding='utf-8') as f:
                    registry = json.load(f)
                    
                for symbol_data in registry.get("approved_symbols", []):
                    self.existing_symbols.add(symbol_data["symbol"])
                    self.existing_codes.add(symbol_data["code"])
                    if "fallback" in symbol_data:
                        self.existing_fallbacks.add(symbol_data["fallback"])
                        
        except Exception as e:
            print(f"⚠️ Warning: Could not load existing symbols: {e}")
            
    def validate_usu_criteria(self, symbol: str, code: str) -> Dict[str, Any]:
        """1. USU - Unicità Simbolica Universale"""
        result = {
            "valid": True,
            "errors": [],
            "warnings": [],
            "score": 100.0
        }
        
        # Verifica simbolo singolo carattere Unicode
        if len(symbol) != 1:
            result["errors"].append("Symbol must be a single Unicode character")
            result["valid"] = False
            result["score"] -= 25
            
        # Verifica nome Unicode valido
        try:
            unicode_name = unicodedata.name(symbol)
        except ValueError:
            result["errors"].append("Symbol has invalid Unicode codepoint")
            result["valid"] = False
            result["score"] -= 25
            
        # Verifica unicità simbolo
        if symbol in self.existing_symbols:
            result["errors"].append("Symbol already exists in registry")
            result["valid"] = False
            result["score"] -= 25
            
        # Verifica unicità codice
        if code in self.existing_codes:
            result["errors"].append("Code already exists in registry")
            result["valid"] = False
            result["score"] -= 25
            
        return result
        
    def validate_ctu_criteria(self, code: str, fallback: str) -> Dict[str, Any]:
        """2. CTU - Codifica Testuale Unificata"""
        result = {
            "valid": True,
            "errors": [],
            "warnings": [],
            "score": 100.0
        }
        
        # Verifica formato code ng:<categoria>:<funzione>
        if not code.startswith("ng:"):
            result["errors"].append("Code must start with 'ng:'")
            result["valid"] = False
            result["score"] -= 30
            
        code_parts = code.split(":")
        if len(code_parts) < 3:
            result["errors"].append("Code must have at least 2 sub-levels (ng:category:function)")
            result["valid"] = False
            result["score"] -= 30
            
        # Verifica fallback ASCII puro
        if not fallback:
            result["errors"].append("Fallback is required")
            result["valid"] = False
            result["score"] -= 20
        elif not fallback.isascii():
            result["errors"].append("Fallback must be pure ASCII (no UTF-8 characters)")
            result["valid"] = False
            result["score"] -= 20
            
        # Verifica fallback distintivo
        if fallback in ["[?]", "[UNKNOWN]", "[GENERIC]"]:
            result["errors"].append("Fallback must be distinctive (not generic)")
            result["valid"] = False
            result["score"] -= 10
            
        # Verifica unicità fallback
        if fallback in self.existing_fallbacks:
            result["warnings"].append("Fallback already exists (not critical but not ideal)")
            result["score"] -= 10
            
        return result
        
    def validate_lcl_criteria(self, symbol: str) -> Dict[str, Any]:
        """3. LCL - LLM Compatibility Layer"""
        result = {
            "valid": True,
            "errors": [],
            "warnings": [],
            "score": 100.0,
            "token_cost": 1,
            "token_density": 1.0,
            "llm_support": []
        }
        
        # Test tokenizer compatibility (simulato per ora)
        token_cost = self._estimate_token_cost(symbol)
        result["token_cost"] = token_cost
        
        # Verifica token cost ≤ 2
        if token_cost > 2:
            result["errors"].append(f"Token cost {token_cost} exceeds maximum of 2")
            result["valid"] = False
            result["score"] -= 30
            
        # Calcola token density (significato/token_cost)
        token_density = 1.0 / token_cost  # Semplificato
        result["token_density"] = token_density
        
        # Verifica token density ≥ 0.9
        if token_density < 0.9:
            result["warnings"].append(f"Token density {token_density:.2f} below recommended 0.9")
            result["score"] -= 15
            
        # Simula supporto LLM (in implementazione reale userebbe tokenizer veri)
        result["llm_support"] = ["openai", "qwen", "llama.cpp"]
        
        return result
        
    def validate_categories_and_meaning(self, category: str, meaning: str) -> Dict[str, Any]:
        """4. Categorie e Significato"""
        result = {
            "valid": True,
            "errors": [],
            "warnings": [],
            "score": 100.0
        }
        
        # Verifica categoria valida
        if category not in self.valid_categories:
            result["errors"].append(f"Category '{category}' not in valid categories: {sorted(self.valid_categories)}")
            result["valid"] = False
            result["score"] -= 40
            
        # Verifica meaning non vuoto
        if not meaning or meaning.strip() == "":
            result["errors"].append("Meaning cannot be empty")
            result["valid"] = False
            result["score"] -= 30
            
        # Verifica meaning univoco nella categoria (semplificato)
        if len(meaning) < 2:
            result["warnings"].append("Meaning should be descriptive (at least 2 characters)")
            result["score"] -= 10
            
        return result
        
    def validate_technical_requirements(self, symbol_data: Dict[str, Any]) -> Dict[str, Any]:
        """5. Requisiti Tecnici e Formali"""
        result = {
            "valid": True,
            "errors": [],
            "warnings": [],
            "score": 100.0
        }
        
        # Verifica campi obbligatori
        required_fields = ["symbol", "code", "fallback", "category", "meaning"]
        for field in required_fields:
            if field not in symbol_data or symbol_data[field] is None:
                result["errors"].append(f"Required field '{field}' is missing or null")
                result["valid"] = False
                result["score"] -= 20
                
        # Verifica nessun campo vuoto
        for field in required_fields:
            if field in symbol_data and str(symbol_data[field]).strip() == "":
                result["errors"].append(f"Field '{field}' cannot be empty")
                result["valid"] = False
                result["score"] -= 15
                
        return result
        
    def _estimate_token_cost(self, symbol: str) -> int:
        """Stima costo token (implementazione semplificata)."""
        # In implementazione reale userebbe tiktoken, transformers, etc.
        # Per ora stima basata su Unicode range
        code_point = ord(symbol)
        
        if code_point < 0x80:  # ASCII
            return 1
        elif code_point < 0x800:  # Latin extended
            return 1
        elif 0x2000 <= code_point <= 0x2FFF:  # Mathematical symbols
            return 1
        else:
            return 2  # Altri caratteri potrebbero costare di più
            
    def calculate_quality_score(self, symbol: str, fallback: str, token_cost: int, token_density: float) -> float:
        """Calcola score qualità secondo formula definitiva."""
        score = (
            0.4 * token_density +
            0.3 * (1 if token_cost <= 2 else 0) +
            0.2 * (1 if len(symbol) == 1 else 0) +
            0.1 * (1 if fallback.isascii() else 0)
        ) * 100
        
        return min(100.0, max(0.0, score))
        
    def validate_symbol_complete(self, symbol: str, code: str, fallback: str, 
                                category: str, meaning: str, description: str = "") -> Dict[str, Any]:
        """Validazione completa di un simbolo secondo tutti i criteri ULTRA."""
        
        # Crea symbol_data
        symbol_data = {
            "symbol": symbol,
            "code": code,
            "fallback": fallback,
            "category": category,
            "meaning": meaning,
            "description": description
        }
        
        # Esegui tutte le validazioni
        usu_result = self.validate_usu_criteria(symbol, code)
        ctu_result = self.validate_ctu_criteria(code, fallback)
        lcl_result = self.validate_lcl_criteria(symbol)
        category_result = self.validate_categories_and_meaning(category, meaning)
        technical_result = self.validate_technical_requirements(symbol_data)
        
        # Combina risultati
        all_valid = all([
            usu_result["valid"],
            ctu_result["valid"], 
            lcl_result["valid"],
            category_result["valid"],
            technical_result["valid"]
        ])
        
        all_errors = []
        all_warnings = []
        
        for result in [usu_result, ctu_result, lcl_result, category_result, technical_result]:
            all_errors.extend(result["errors"])
            all_warnings.extend(result["warnings"])
            
        # Calcola score complessivo
        scores = [
            usu_result["score"] * 0.25,      # 25% USU
            ctu_result["score"] * 0.20,      # 20% CTU  
            lcl_result["score"] * 0.20,      # 20% LCL
            category_result["score"] * 0.20, # 20% Categorie
            technical_result["score"] * 0.15 # 15% Tecnici
        ]
        
        overall_score = sum(scores)
        
        # Score qualità aggiuntivo
        quality_score = self.calculate_quality_score(
            symbol, fallback, lcl_result["token_cost"], lcl_result["token_density"]
        )
        
        # Score finale (media tra overall e quality)
        final_score = (overall_score + quality_score) / 2
        
        return {
            "valid": all_valid,
            "score": final_score,
            "quality_score": quality_score,
            "errors": all_errors,
            "warnings": all_warnings,
            "details": {
                "usu": usu_result,
                "ctu": ctu_result,
                "lcl": lcl_result,
                "categories": category_result,
                "technical": technical_result
            },
            "token_cost": lcl_result["token_cost"],
            "token_density": lcl_result["token_density"],
            "llm_support": lcl_result["llm_support"]
        }
        
    def generate_report(self, validation_result: Dict[str, Any]) -> str:
        """Genera report dettagliato della validazione."""
        lines = [
            "🧠 NEUROGLYPH ULTRA - Symbol Validation Report",
            "=" * 60,
            f"✅ Valid: {validation_result['valid']}",
            f"📊 Score: {validation_result['score']:.1f}%",
            f"🏆 Quality Score: {validation_result['quality_score']:.1f}%",
            f"💰 Token Cost: {validation_result['token_cost']}",
            f"📈 Token Density: {validation_result['token_density']:.2f}",
            f"🤖 LLM Support: {', '.join(validation_result['llm_support'])}",
            ""
        ]
        
        if validation_result["errors"]:
            lines.append("❌ ERRORS:")
            for error in validation_result["errors"]:
                lines.append(f"  • {error}")
            lines.append("")
            
        if validation_result["warnings"]:
            lines.append("⚠️ WARNINGS:")
            for warning in validation_result["warnings"]:
                lines.append(f"  • {warning}")
            lines.append("")
            
        # Dettagli per criterio
        lines.append("📋 DETAILED SCORES:")
        for criterion, result in validation_result["details"].items():
            lines.append(f"  • {criterion.upper()}: {result['score']:.1f}%")
            
        return "\n".join(lines)


def main():
    """Main function per command line usage."""
    if len(sys.argv) < 6:
        print("Usage: python neuroglyph_ultra_validator.py <symbol> <code> <fallback> <category> <meaning> [description]")
        print("Example: python neuroglyph_ultra_validator.py '⊕' 'ng:operator:add' '[+]' 'operator' 'add' 'Addition operator'")
        sys.exit(1)
        
    symbol = sys.argv[1]
    code = sys.argv[2]
    fallback = sys.argv[3]
    category = sys.argv[4]
    meaning = sys.argv[5]
    description = sys.argv[6] if len(sys.argv) > 6 else ""
    
    validator = NeuroGlyphUltraValidator()
    result = validator.validate_symbol_complete(symbol, code, fallback, category, meaning, description)
    
    report = validator.generate_report(result)
    print(report)
    
    # Exit code basato su validità
    sys.exit(0 if result["valid"] else 1)


if __name__ == "__main__":
    main()
