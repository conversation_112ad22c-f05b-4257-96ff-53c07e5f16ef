{"instruction": "Genera codice Python dai neuroglifi.", "input": "⟨⟩α⊕β⤴α⊕β", "output": "def add(a, b):\n    return a + b", "task_type": "symbol_to_code", "difficulty": "easy", "language": "python"}
{"instruction": "Traduci la seguente funzione Python in simboli neuroglifi.", "input": "def subtract(a, b):\n    return a - b", "output": "⟨⟩α⊖β⤴α⊖β", "task_type": "code_to_symbol", "difficulty": "easy", "language": "python"}
{"instruction": "Spiega cosa fa questa sequenza di neuroglifi.", "input": "⟨⟩◊α≤1⤴1◈⤴α⊗ƒ⟨α⊖1⟩", "output": "Calcola il fattoriale di α in modo ricorsivo.", "task_type": "symbol_reasoning", "difficulty": "medium", "language": "symbol"}
{"instruction": "Traduci questi neuroglifi in codice Rust.", "input": "⟨⟩α⊗β⤴α⊗β", "output": "fn multiply(a: i32, b: i32) -> i32 {\n    a * b\n}", "task_type": "symbol_to_code", "difficulty": "easy", "language": "rust"}
{"instruction": "Cosa restituisce questa funzione simbolica se α=7 e β=3?", "input": "⟨⟩α⊖β⤴α⊖β", "output": "4", "task_type": "symbol_reasoning", "difficulty": "easy", "language": "symbol"}
{"instruction": "Traduci questa funzione Python in simboli neuroglifi.", "input": "def max_value(a, b):\n    return a if a > b else b", "output": "⟨⟩◊α>β⤴α◈⤴β", "task_type": "code_to_symbol", "difficulty": "medium", "language": "python"}
{"instruction": "Traduci questi neuroglifi in codice Python.", "input": "⟨⟩[α₁,α₂,...,αₙ]→|α|", "output": "def list_length(lst):\n    return len(lst)", "task_type": "symbol_to_code", "difficulty": "easy", "language": "python"}
{"instruction": "Traduci questa funzione Python in simboli neuroglifi.", "input": "def logical_not(a):\n    return not a", "output": "⟨⟩¬α⤴¬α", "task_type": "code_to_symbol", "difficulty": "easy", "language": "python"}
{"instruction": "Spiega cosa fa questa funzione simbolica.", "input": "⟨⟩⟪⟫μ⟐α⟑◊⟦⟧⊕", "output": "Definisce una classe con metodo che somma una lista se una condizione è verificata.", "task_type": "symbol_reasoning", "difficulty": "hard", "language": "symbol"}
{"instruction": "Traduci questi neuroglifi in codice JavaScript.", "input": "⟨⟩α⊘β⤴α⊘β", "output": "function divide(a, b) {\n    return a / b;\n}", "task_type": "symbol_to_code", "difficulty": "easy", "language": "js"}
