#!/usr/bin/env python3
"""
NEUROGLYPH GOD TIER - Complete Generation Pipeline
==================================================

Pipeline automatizzata per generare tutti i 1024 simboli GOD TIER (da 1024 a 2048)
con validazione completa, integrazione automatica e reporting.

Usage: python generate_complete_god_tier.py --auto --validate --integrate
"""

import json
import sys
import argparse
import subprocess
from datetime import datetime
from typing import Dict, Any, List
from pathlib import Path
import time

class CompleteGodTierPipeline:
    """Pipeline completa per generazione GOD TIER."""
    
    def __init__(self):
        self.base_path = Path(__file__).parent
        self.output_dir = self.base_path / "god_tier_output"
        self.output_dir.mkdir(exist_ok=True)
        
        # Domini GOD TIER con target simboli
        self.god_tier_domains = {
            "advanced_coding": 256,
            "meta_programming": 128,
            "distributed_systems": 128,
            "quantum_computing": 64,
            "symbolic_ai": 128,
            "neural_architectures": 64,
            "formal_verification": 64,
            "category_theory": 64,
            "type_theory": 64,
            "concurrency_advanced": 128,
            "memory_management": 64,
            "compiler_internals": 64,
            "runtime_systems": 64,
            "protocol_design": 64,
            "cryptographic_primitives": 64,
            "machine_learning": 128,
            "cognitive_modeling": 64,
            "philosophical_concepts": 64,
            "mathematical_structures": 64,
            "reserved_expansion": 64
        }
        
        self.generation_results = []
        self.integration_results = []
    
    def run_complete_pipeline(self, validate: bool = True, integrate: bool = True, 
                            batch_size: int = 5) -> Dict[str, Any]:
        """Esegue pipeline completa di generazione GOD TIER."""
        
        print("🚀 NEUROGLYPH GOD TIER - Complete Generation Pipeline")
        print("=" * 60)
        print(f"📊 Target: {sum(self.god_tier_domains.values())} simboli in {len(self.god_tier_domains)} domini")
        print(f"🔧 Validazione: {'✅' if validate else '❌'}")
        print(f"🔗 Integrazione: {'✅' if integrate else '❌'}")
        print(f"📦 Batch size: {batch_size}")
        print()
        
        start_time = time.time()
        
        # Fase 1: Generazione per domini
        print("📋 FASE 1: Generazione simboli per domini")
        print("-" * 40)
        
        domain_batches = list(self.god_tier_domains.items())
        
        for i in range(0, len(domain_batches), batch_size):
            batch = domain_batches[i:i + batch_size]
            
            print(f"\n🔄 Batch {i//batch_size + 1}: {[d[0] for d in batch]}")
            
            for domain, count in batch:
                result = self._generate_domain(domain, count)
                self.generation_results.append(result)
                
                if result['success']:
                    print(f"  ✅ {domain}: {result['symbols_generated']} simboli")
                else:
                    print(f"  ❌ {domain}: {result['error']}")
            
            # Pausa tra batch
            if i + batch_size < len(domain_batches):
                print(f"⏸️ Pausa 2s tra batch...")
                time.sleep(2)
        
        # Fase 2: Integrazione (se richiesta)
        if integrate:
            print(f"\n📋 FASE 2: Integrazione nel registry principale")
            print("-" * 40)
            
            for result in self.generation_results:
                if result['success']:
                    integration_result = self._integrate_domain(result, validate)
                    self.integration_results.append(integration_result)
                    
                    if integration_result['success']:
                        print(f"  ✅ {result['domain']}: {integration_result['symbols_integrated']} integrati")
                    else:
                        print(f"  ❌ {result['domain']}: {integration_result['error']}")
        
        # Fase 3: Report finale
        end_time = time.time()
        duration = end_time - start_time
        
        final_report = self._generate_final_report(duration)
        
        print(f"\n📋 FASE 3: Report finale")
        print("-" * 40)
        print(final_report)
        
        return {
            "success": True,
            "duration": duration,
            "generation_results": self.generation_results,
            "integration_results": self.integration_results,
            "final_report": final_report
        }
    
    def _generate_domain(self, domain: str, count: int) -> Dict[str, Any]:
        """Genera simboli per un singolo dominio."""
        try:
            output_file = self.output_dir / f"{domain}_symbols.json"
            
            # Comando per generazione
            cmd = [
                sys.executable,
                str(self.base_path / "generate_god_tier_symbols.py"),
                "--domain", domain,
                "--count", str(count),
                "--output", str(output_file)
            ]
            
            # Esegui generazione
            result = subprocess.run(cmd, capture_output=True, text=True, timeout=300)
            
            if result.returncode == 0:
                # Verifica file output
                if output_file.exists():
                    with open(output_file, 'r', encoding='utf-8') as f:
                        data = json.load(f)
                    
                    symbols_generated = len(data.get('symbols', []))
                    
                    return {
                        "success": True,
                        "domain": domain,
                        "symbols_generated": symbols_generated,
                        "output_file": str(output_file),
                        "target_count": count
                    }
                else:
                    return {
                        "success": False,
                        "domain": domain,
                        "error": "File output non creato"
                    }
            else:
                return {
                    "success": False,
                    "domain": domain,
                    "error": f"Generazione fallita: {result.stderr}"
                }
                
        except subprocess.TimeoutExpired:
            return {
                "success": False,
                "domain": domain,
                "error": "Timeout durante generazione"
            }
        except Exception as e:
            return {
                "success": False,
                "domain": domain,
                "error": f"Errore: {str(e)}"
            }
    
    def _integrate_domain(self, generation_result: Dict[str, Any], validate: bool) -> Dict[str, Any]:
        """Integra simboli di un dominio nel registry."""
        try:
            # Comando per integrazione
            cmd = [
                sys.executable,
                str(self.base_path / "integrate_god_tier.py"),
                "--input", generation_result['output_file']
            ]
            
            if validate:
                cmd.append("--validate")
            
            # Esegui integrazione
            result = subprocess.run(cmd, capture_output=True, text=True, timeout=120)
            
            if result.returncode == 0:
                return {
                    "success": True,
                    "domain": generation_result['domain'],
                    "symbols_integrated": generation_result['symbols_generated'],
                    "output": result.stdout
                }
            else:
                return {
                    "success": False,
                    "domain": generation_result['domain'],
                    "error": f"Integrazione fallita: {result.stderr}"
                }
                
        except subprocess.TimeoutExpired:
            return {
                "success": False,
                "domain": generation_result['domain'],
                "error": "Timeout durante integrazione"
            }
        except Exception as e:
            return {
                "success": False,
                "domain": generation_result['domain'],
                "error": f"Errore: {str(e)}"
            }
    
    def _generate_final_report(self, duration: float) -> str:
        """Genera report finale della pipeline."""
        
        # Statistiche generazione
        total_domains = len(self.god_tier_domains)
        successful_generations = sum(1 for r in self.generation_results if r['success'])
        total_symbols_generated = sum(r.get('symbols_generated', 0) for r in self.generation_results if r['success'])
        target_symbols = sum(self.god_tier_domains.values())
        
        # Statistiche integrazione
        successful_integrations = sum(1 for r in self.integration_results if r['success'])
        total_symbols_integrated = sum(r.get('symbols_integrated', 0) for r in self.integration_results if r['success'])
        
        report = f"""
🎉 NEUROGLYPH GOD TIER - Pipeline Completata
============================================

⏱️ TIMING:
  • Durata totale: {duration:.1f}s
  • Tempo medio per dominio: {duration/total_domains:.1f}s

📊 GENERAZIONE:
  • Domini processati: {successful_generations}/{total_domains}
  • Simboli generati: {total_symbols_generated}/{target_symbols}
  • Successo: {(successful_generations/total_domains)*100:.1f}%

🔗 INTEGRAZIONE:
  • Domini integrati: {successful_integrations}/{successful_generations}
  • Simboli integrati: {total_symbols_integrated}
  • Successo: {(successful_integrations/successful_generations)*100:.1f}% (se abilitata)

📈 DOMINI COMPLETATI:
"""
        
        for result in self.generation_results:
            if result['success']:
                domain = result['domain']
                generated = result['symbols_generated']
                target = result['target_count']
                percentage = (generated / target * 100) if target > 0 else 0
                status = "✅" if generated >= target * 0.9 else "⚠️"
                report += f"  {status} {domain}: {generated}/{target} ({percentage:.1f}%)\n"
            else:
                report += f"  ❌ {result['domain']}: FAILED - {result['error']}\n"
        
        # Status finale
        completion_rate = (total_symbols_generated / target_symbols * 100) if target_symbols > 0 else 0
        
        if completion_rate >= 95:
            status_emoji = "🎉"
            status_text = "GOD TIER COMPLETATO!"
        elif completion_rate >= 80:
            status_emoji = "🚀"
            status_text = "GOD TIER QUASI COMPLETO"
        else:
            status_emoji = "🔄"
            status_text = "GOD TIER IN PROGRESSO"
        
        report += f"""
{status_emoji} STATUS FINALE: {status_text}
  • Completamento: {completion_rate:.1f}%
  • Simboli totali: {1024 + total_symbols_integrated} (ULTRA + GOD)
  
📁 OUTPUT FILES: {self.output_dir}
🔧 REGISTRY: neuroglyph/core/symbols_registry.json

{'🎊 NEUROGLYPH è ora pronto per il symbolic reasoning supremo!' if completion_rate >= 95 else ''}
"""
        
        return report
    
    def cleanup_output_files(self):
        """Pulisce file temporanei di output."""
        try:
            for file in self.output_dir.glob("*.json"):
                file.unlink()
            print(f"🧹 Puliti file temporanei in {self.output_dir}")
        except Exception as e:
            print(f"⚠️ Errore pulizia: {e}")

def main():
    """Esegue pipeline completa da command line."""
    parser = argparse.ArgumentParser(description="NEUROGLYPH GOD TIER Complete Pipeline")
    parser.add_argument("--auto", action="store_true",
                       help="Esegue pipeline automatica completa")
    parser.add_argument("--validate", action="store_true",
                       help="Valida simboli durante integrazione")
    parser.add_argument("--integrate", action="store_true",
                       help="Integra simboli nel registry principale")
    parser.add_argument("--batch-size", type=int, default=5,
                       help="Numero domini per batch")
    parser.add_argument("--cleanup", action="store_true",
                       help="Pulisce file temporanei alla fine")
    
    args = parser.parse_args()
    
    if not args.auto:
        print("❌ Usa --auto per eseguire la pipeline completa")
        print("Esempio: python generate_complete_god_tier.py --auto --validate --integrate")
        sys.exit(1)
    
    # Crea pipeline
    pipeline = CompleteGodTierPipeline()
    
    try:
        # Esegui pipeline
        result = pipeline.run_complete_pipeline(
            validate=args.validate,
            integrate=args.integrate,
            batch_size=args.batch_size
        )
        
        # Salva report finale
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        report_file = f"god_tier_complete_report_{timestamp}.txt"
        
        with open(report_file, 'w', encoding='utf-8') as f:
            f.write(result['final_report'])
        
        print(f"\n📄 Report completo salvato: {report_file}")
        
        # Cleanup opzionale
        if args.cleanup:
            pipeline.cleanup_output_files()
        
        print(f"\n🎉 Pipeline GOD TIER completata con successo!")
        
    except KeyboardInterrupt:
        print(f"\n⚠️ Pipeline interrotta dall'utente")
        sys.exit(1)
    except Exception as e:
        print(f"\n❌ Errore durante pipeline: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
