#!/usr/bin/env python3
"""
🧠 NEUROGLYPH ULTRA - Automated 512 Symbol Generation Pipeline
==============================================================

Script automatico GENIALE per generare 512 simboli ULTRA di qualità suprema:

🎯 PIPELINE AUTOMATICA:
1. Analizza simboli esistenti (evita duplicati)
2. Genera simbolo unico con AI semantica
3. Esegue validazione completa USU/CTU/LCL
4. Auto-approva solo simboli perfetti (score ≥ 90%)
5. Si ripete fino a raggiungere 512 simboli

🚀 VANTAGGI ULTRA:
✅ Qualità garantita: Solo simboli che passano tutti i test
✅ Scalabilità: Da 20 a 512 automaticamente
✅ Consistenza: Stessi criteri rigorosi per tutti
✅ Efficienza: Nessun intervento manuale
✅ Tracciabilità: Log completo di ogni decisione

Usage: python generate_512_symbols_ultra_pipeline.py [--target 512] [--batch-size 10] [--min-score 90]
"""

import json
import sys
import os
import time
import random
import unicodedata
from datetime import datetime
from typing import Dict, Any, List, Set, Optional, Tuple
from pathlib import Path
import argparse
import logging

# Import dei validatori esistenti
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

class UltraSymbolGenerator:
    """Generatore automatico di simboli NEUROGLYPH ULTRA."""

    def __init__(self, target_count: int = 512, min_score: float = 90.0, batch_size: int = 10):
        self.target_count = target_count
        self.min_score = min_score
        self.batch_size = batch_size

        # Paths
        self.registry_path = Path("core/symbols_registry.json")
        self.symbols_path = Path("core/symbols_ultra.json")
        self.log_path = Path("logs/ultra_generation.log")

        # Stato generazione
        self.existing_symbols: Set[str] = set()
        self.existing_codes: Set[str] = set()
        self.existing_unicode_points: Set[str] = set()
        self.generated_count = 0
        self.attempt_count = 0
        self.success_rate = 0.0

        # Statistiche
        self.stats = {
            "start_time": datetime.now(),
            "total_attempts": 0,
            "successful_generations": 0,
            "failed_validations": 0,
            "duplicate_rejections": 0,
            "categories_distribution": {},
            "average_score": 0.0,
            "generation_rate": 0.0  # simboli/minuto
        }

        # Setup logging
        self._setup_logging()

        # Carica stato esistente
        self._load_existing_symbols()

        # Categorie semantiche ULTRA
        self.categories = self._get_ultra_categories()

        # Unicode ranges per simboli unici
        self.unicode_ranges = self._get_unicode_ranges()

    def _setup_logging(self):
        """Setup logging system."""
        os.makedirs("logs", exist_ok=True)

        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(levelname)s - %(message)s',
            handlers=[
                logging.FileHandler(self.log_path, encoding='utf-8'),
                logging.StreamHandler(sys.stdout)
            ]
        )
        self.logger = logging.getLogger(__name__)

    def _load_existing_symbols(self):
        """Carica simboli esistenti per evitare duplicati."""
        try:
            if self.registry_path.exists():
                with open(self.registry_path, 'r', encoding='utf-8') as f:
                    registry = json.load(f)

                for symbol_data in registry.get("approved_symbols", []):
                    self.existing_symbols.add(symbol_data["symbol"])
                    self.existing_codes.add(symbol_data["code"])

                    # Unicode point
                    symbol = symbol_data["symbol"]
                    if len(symbol) == 1:
                        unicode_point = f"U+{ord(symbol):04X}"
                        self.existing_unicode_points.add(unicode_point)

                self.generated_count = len(registry.get("approved_symbols", []))
                self.logger.info(f"📊 Caricati {self.generated_count} simboli esistenti")

        except Exception as e:
            self.logger.error(f"❌ Errore caricamento simboli esistenti: {e}")

    def _get_ultra_categories(self) -> Dict[str, Dict[str, Any]]:
        """Definisce categorie semantiche ULTRA per 512 simboli."""
        return {
            "operator": {
                "count_target": 64,  # 12.5% - Operatori matematici/logici
                "semantic_fields": [
                    "arithmetic", "comparison", "bitwise", "assignment",
                    "increment", "modulo", "power", "root", "factorial",
                    "matrix", "vector", "tensor", "complex", "quaternion"
                ],
                "unicode_ranges": ["2200-22FF", "2A00-2AFF", "27C0-27EF"]
            },
            "logic": {
                "count_target": 96,  # 18.75% - Logica e ragionamento
                "semantic_fields": [
                    "propositional", "predicate", "modal", "temporal", "fuzzy",
                    "quantum", "probabilistic", "deontic", "epistemic", "belief",
                    "inference", "proof", "theorem", "axiom", "lemma"
                ],
                "unicode_ranges": ["2200-22FF", "27C0-27EF", "2980-29FF"]
            },
            "structure": {
                "count_target": 80,  # 15.6% - Strutture dati e codice
                "semantic_fields": [
                    "class", "function", "method", "property", "interface",
                    "module", "package", "namespace", "scope", "closure",
                    "lambda", "generator", "iterator", "decorator", "annotation"
                ],
                "unicode_ranges": ["2500-257F", "2580-259F", "25A0-25FF"]
            },
            "flow": {
                "count_target": 72,  # 14.1% - Controllo di flusso
                "semantic_fields": [
                    "conditional", "loop", "branch", "jump", "call", "return",
                    "exception", "try", "catch", "finally", "async", "await",
                    "yield", "break", "continue", "goto"
                ],
                "unicode_ranges": ["2190-21FF", "27F0-27FF", "2900-297F"]
            },
            "memory": {
                "count_target": 64,  # 12.5% - Gestione memoria
                "semantic_fields": [
                    "allocate", "deallocate", "reference", "pointer", "address",
                    "stack", "heap", "cache", "buffer", "register", "volatile",
                    "persistent", "transient", "immutable", "mutable"
                ],
                "unicode_ranges": ["2600-26FF", "2700-27BF", "1F300-1F5FF"]
            },
            "reasoning": {
                "count_target": 56,  # 10.9% - Ragionamento avanzato
                "semantic_fields": [
                    "induction", "deduction", "abduction", "analogy", "metaphor",
                    "causality", "correlation", "pattern", "similarity", "difference",
                    "abstraction", "generalization", "specialization", "classification"
                ],
                "unicode_ranges": ["2980-29FF", "2A00-2AFF", "1F600-1F64F"]
            },
            "meta": {
                "count_target": 48,  # 9.4% - Meta-operazioni
                "semantic_fields": [
                    "reflection", "introspection", "metacognition", "self_reference",
                    "recursion", "fixpoint", "transformation", "compilation",
                    "interpretation", "evaluation", "optimization", "debugging"
                ],
                "unicode_ranges": ["2300-23FF", "2400-243F", "25A0-25FF"]
            },
            "quantum": {
                "count_target": 32,  # 6.25% - Computazione quantistica
                "semantic_fields": [
                    "qubit", "superposition", "entanglement", "measurement",
                    "gate", "circuit", "algorithm", "teleportation", "cryptography",
                    "error_correction", "decoherence", "interference"
                ],
                "unicode_ranges": ["2200-22FF", "27C0-27EF", "2980-29FF"]
            }
        }

    def _get_unicode_ranges(self) -> List[Tuple[int, int]]:
        """Ottiene range Unicode per simboli unici."""
        ranges = []

        # Range principali per simboli matematici e tecnici
        unicode_blocks = [
            (0x2190, 0x21FF),  # Arrows
            (0x2200, 0x22FF),  # Mathematical Operators
            (0x2300, 0x23FF),  # Miscellaneous Technical
            (0x2400, 0x243F),  # Control Pictures
            (0x2440, 0x245F),  # Optical Character Recognition
            (0x2460, 0x24FF),  # Enclosed Alphanumerics
            (0x2500, 0x257F),  # Box Drawing
            (0x2580, 0x259F),  # Block Elements
            (0x25A0, 0x25FF),  # Geometric Shapes
            (0x2600, 0x26FF),  # Miscellaneous Symbols
            (0x2700, 0x27BF),  # Dingbats
            (0x27C0, 0x27EF),  # Miscellaneous Mathematical Symbols-A
            (0x27F0, 0x27FF),  # Supplemental Arrows-A
            (0x2900, 0x297F),  # Supplemental Arrows-B
            (0x2980, 0x29FF),  # Miscellaneous Mathematical Symbols-B
            (0x2A00, 0x2AFF),  # Supplemental Mathematical Operators
            (0x2B00, 0x2BFF),  # Miscellaneous Symbols and Arrows
        ]

        return unicode_blocks

    def generate_unique_symbol(self, category: str, semantic_field: str) -> Optional[Dict[str, Any]]:
        """Genera un simbolo unico per categoria e campo semantico."""
        max_attempts = 100

        for attempt in range(max_attempts):
            try:
                # Seleziona range Unicode appropriato per categoria
                unicode_ranges = self.unicode_ranges
                start, end = random.choice(unicode_ranges)

                # Genera carattere Unicode casuale nel range
                unicode_point = random.randint(start, end)
                symbol = chr(unicode_point)

                # Verifica unicità
                if symbol in self.existing_symbols:
                    continue

                # Verifica che sia un carattere valido
                try:
                    unicodedata.name(symbol)
                except ValueError:
                    continue

                # Genera nome semantico
                name = self._generate_semantic_name(semantic_field, attempt)

                # Genera codice ng:
                code = f"ng:{category}:{name}"

                # Verifica unicità codice
                if code in self.existing_codes:
                    continue

                # Genera fallback ASCII
                fallback = self._generate_fallback(name, semantic_field)

                # Crea entry simbolo
                symbol_data = {
                    "symbol": symbol,
                    "code": code,
                    "fallback": fallback,
                    "category": category,
                    "name": name,
                    "description": self._generate_description(name, category, semantic_field),
                    "semantic_field": semantic_field,
                    "unicode_point": f"U+{unicode_point:04X}",
                    "attempt": attempt + 1
                }

                return symbol_data

            except Exception as e:
                self.logger.warning(f"⚠️ Errore generazione simbolo (tentativo {attempt + 1}): {e}")
                continue

        self.logger.error(f"❌ Impossibile generare simbolo unico per {category}:{semantic_field}")
        return None

    def _generate_semantic_name(self, semantic_field: str, attempt: int) -> str:
        """Genera nome semantico per il campo."""
        # Dizionario di nomi semantici per campo
        semantic_names = {
            # Operator
            "arithmetic": ["add", "sub", "mul", "div", "mod", "pow", "sqrt", "abs", "neg", "inc", "dec"],
            "comparison": ["eq", "ne", "lt", "le", "gt", "ge", "cmp", "min", "max", "clamp"],
            "bitwise": ["and", "or", "xor", "not", "shl", "shr", "rol", "ror", "mask", "flip"],
            "assignment": ["assign", "add_assign", "sub_assign", "mul_assign", "div_assign", "mod_assign"],
            "matrix": ["transpose", "inverse", "determinant", "trace", "eigenvalue", "eigenvector"],

            # Logic
            "propositional": ["and", "or", "not", "implies", "iff", "xor", "nand", "nor"],
            "predicate": ["forall", "exists", "unique", "lambda", "apply", "compose", "curry"],
            "modal": ["necessary", "possible", "knows", "believes", "ought", "permitted"],
            "temporal": ["always", "eventually", "until", "since", "next", "previous"],
            "inference": ["entails", "proves", "derives", "concludes", "assumes", "supposes"],

            # Structure
            "class": ["class", "interface", "abstract", "concrete", "inherit", "implement"],
            "function": ["function", "method", "procedure", "lambda", "closure", "partial"],
            "module": ["module", "package", "namespace", "import", "export", "include"],
            "scope": ["global", "local", "private", "public", "protected", "static"],

            # Flow
            "conditional": ["if", "else", "elif", "switch", "case", "default", "when"],
            "loop": ["for", "while", "do", "repeat", "until", "foreach", "map", "filter"],
            "exception": ["try", "catch", "throw", "finally", "raise", "handle", "rescue"],
            "async": ["async", "await", "promise", "future", "task", "thread", "process"],

            # Memory
            "allocate": ["alloc", "malloc", "calloc", "realloc", "new", "create", "reserve"],
            "reference": ["ref", "deref", "pointer", "address", "offset", "index", "slice"],
            "cache": ["cache", "memoize", "buffer", "prefetch", "evict", "flush", "sync"],

            # Reasoning
            "induction": ["induct", "base_case", "inductive_step", "generalize", "pattern"],
            "deduction": ["deduce", "syllogism", "modus_ponens", "modus_tollens", "contrapositive"],
            "analogy": ["analogous", "similar", "metaphor", "compare", "contrast", "relate"],

            # Meta
            "reflection": ["reflect", "introspect", "meta", "self", "type_of", "instance_of"],
            "recursion": ["recurse", "fixpoint", "iterate", "unfold", "fold", "reduce"],
            "compilation": ["compile", "parse", "lex", "optimize", "transform", "generate"],

            # Quantum
            "qubit": ["qubit", "superposition", "collapse", "measure", "observe", "state"],
            "entanglement": ["entangle", "bell_state", "epr", "spooky", "correlate", "separate"],
            "gate": ["hadamard", "pauli_x", "pauli_y", "pauli_z", "cnot", "toffoli", "fredkin"]
        }

        names = semantic_names.get(semantic_field, [semantic_field])

        # Seleziona nome base
        if attempt < len(names):
            base_name = names[attempt]
        else:
            base_name = f"{semantic_field}_{attempt - len(names) + 1}"

        return base_name

    def _generate_fallback(self, name: str, semantic_field: str) -> str:
        """Genera fallback ASCII per il simbolo."""
        # Converti nome in fallback ASCII
        fallback = name.upper().replace('_', '')

        # Limita lunghezza
        if len(fallback) > 8:
            fallback = fallback[:8]

        return f"[{fallback}]"

    def _generate_description(self, name: str, category: str, semantic_field: str) -> str:
        """Genera descrizione semantica per il simbolo."""
        descriptions = {
            "operator": f"Mathematical {semantic_field} operation: {name}",
            "logic": f"Logical {semantic_field} construct: {name}",
            "structure": f"Code {semantic_field} element: {name}",
            "flow": f"Control {semantic_field} statement: {name}",
            "memory": f"Memory {semantic_field} operation: {name}",
            "reasoning": f"Reasoning {semantic_field} process: {name}",
            "meta": f"Meta {semantic_field} operation: {name}",
            "quantum": f"Quantum {semantic_field} concept: {name}"
        }

        return descriptions.get(category, f"{category} {semantic_field}: {name}")

    def validate_symbol(self, symbol_data: Dict[str, Any]) -> Dict[str, Any]:
        """Valida simbolo usando pipeline completa USU/CTU/LCL."""
        try:
            # Import validatori
            from validate_symbol import SymbolValidator
            from test_llm_tokenizer_compat import TokenizerCompatibilityTester
            from score_symbol_quality import SymbolQualityScorer

            # 1. Validazione base USU/CTU/LCL
            validator = SymbolValidator()
            validation_result = validator.validate_symbol(
                symbol_data["symbol"],
                symbol_data["code"],
                symbol_data["fallback"],
                symbol_data["category"],
                symbol_data["name"]
            )

            # 2. Test compatibilità tokenizer
            tokenizer_tester = TokenizerCompatibilityTester()
            tokenizer_result = tokenizer_tester.test_symbol(symbol_data["symbol"])

            # 3. Score qualità
            quality_scorer = SymbolQualityScorer()
            quality_result = quality_scorer.score_symbol(
                symbol_data["symbol"],
                symbol_data["code"],
                symbol_data["fallback"],
                symbol_data["name"],
                symbol_data["description"]
            )

            # Combina risultati
            combined_result = {
                "symbol": symbol_data["symbol"],
                "valid": validation_result["valid"],
                "usu_score": validation_result.get("usu_score", 0),
                "ctu_score": validation_result.get("ctu_score", 0),
                "lcl_score": validation_result.get("lcl_score", 0),
                "tokenizer_score": tokenizer_result.get("overall_score", 0),
                "quality_score": quality_result.get("overall_score", 0),
                "token_cost": tokenizer_result.get("tiktoken_results", {}).get("min_tokens", 1),
                "errors": validation_result.get("errors", []),
                "warnings": validation_result.get("warnings", [])
            }

            # Calcola score finale (media pesata)
            weights = {
                "usu_score": 0.25,
                "ctu_score": 0.20,
                "lcl_score": 0.20,
                "tokenizer_score": 0.20,
                "quality_score": 0.15
            }

            final_score = sum(
                combined_result[key] * weight
                for key, weight in weights.items()
                if key in combined_result
            )

            combined_result["score"] = final_score

            return combined_result

        except Exception as e:
            self.logger.error(f"❌ Errore validazione simbolo {symbol_data['symbol']}: {e}")
            return {
                "symbol": symbol_data["symbol"],
                "valid": False,
                "score": 0,
                "errors": [f"Validation error: {e}"]
            }

    def approve_symbol(self, symbol_data: Dict[str, Any], validation_result: Dict[str, Any]) -> bool:
        """Approva simbolo e lo aggiunge al registry."""
        try:
            # Carica registry esistente
            if self.registry_path.exists():
                with open(self.registry_path, 'r', encoding='utf-8') as f:
                    registry = json.load(f)
            else:
                registry = {
                    "registry_version": "1.0",
                    "created": datetime.now().strftime("%Y-%m-%d"),
                    "description": "NEUROGLYPH ULTRA Symbol Registry - Auto-generated",
                    "stats": {"total_submissions": 0, "approved": 0, "rejected": 0, "pending": 0},
                    "approved_symbols": [],
                    "rejected_symbols": [],
                    "pending_symbols": [],
                    "validation_criteria": {
                        "usu_required": ["unicode_unique", "code_unique", "visually_distinct"],
                        "ctu_required": ["format_valid", "category_valid", "function_valid"],
                        "lcl_required": ["utf8_compatible", "fallback_format_valid"],
                        "minimum_score": self.min_score
                    },
                    "categories": {},
                    "next_id": "NG0001",
                    "reserved_ranges": {}
                }

            # Genera nuovo ID
            next_id_num = int(registry.get("next_id", "NG0001")[2:]) if registry.get("next_id") else 1
            new_id = f"NG{next_id_num:04d}"

            # Crea entry approvata
            approved_symbol = {
                "id": new_id,
                "symbol": symbol_data["symbol"],
                "code": symbol_data["code"],
                "fallback": symbol_data["fallback"],
                "category": symbol_data["category"],
                "name": symbol_data["name"],
                "description": symbol_data["description"],
                "semantic_field": symbol_data["semantic_field"],
                "unicode_point": symbol_data["unicode_point"],
                "approved_date": datetime.now().strftime("%Y-%m-%d"),
                "validation_score": validation_result["score"],
                "status": "certified",
                "token_cost": validation_result.get("token_cost", 1),
                "auto_generated": True
            }

            # Aggiorna registry
            registry["approved_symbols"].append(approved_symbol)
            registry["stats"]["approved"] += 1
            registry["stats"]["total_submissions"] += 1
            registry["next_id"] = f"NG{next_id_num + 1:04d}"

            # Aggiorna conteggio categoria
            category = symbol_data["category"]
            if category not in registry["categories"]:
                registry["categories"][category] = {"count": 0, "description": f"{category} operations"}
            registry["categories"][category]["count"] += 1

            # Salva registry
            with open(self.registry_path, 'w', encoding='utf-8') as f:
                json.dump(registry, f, indent=2, ensure_ascii=False)

            # Aggiorna stato interno
            self.existing_symbols.add(symbol_data["symbol"])
            self.existing_codes.add(symbol_data["code"])
            self.existing_unicode_points.add(symbol_data["unicode_point"])
            self.generated_count += 1

            self.logger.info(f"✅ Simbolo approvato: {symbol_data['symbol']} ({new_id}) - Score: {validation_result['score']:.1f}%")
            return True

        except Exception as e:
            self.logger.error(f"❌ Errore approvazione simbolo {symbol_data['symbol']}: {e}")
            return False

    def generate_batch(self) -> List[Dict[str, Any]]:
        """Genera un batch di simboli per tutte le categorie."""
        batch_results = []

        for category, category_info in self.categories.items():
            # Calcola quanti simboli servono per questa categoria
            # Conta simboli esistenti per questa categoria dal registry
            current_count = 0
            try:
                if self.registry_path.exists():
                    with open(self.registry_path, 'r', encoding='utf-8') as f:
                        registry = json.load(f)
                    current_count = registry.get("categories", {}).get(category, {}).get("count", 0)
            except:
                current_count = 0

            target_count = category_info["count_target"]
            needed = target_count - current_count

            if needed <= 0:
                continue

            # Genera simboli per questa categoria
            semantic_fields = category_info["semantic_fields"]
            symbols_per_field = max(1, needed // len(semantic_fields))

            for semantic_field in semantic_fields:
                for _ in range(min(symbols_per_field, self.batch_size)):
                    if self.generated_count >= self.target_count:
                        break

                    self.stats["total_attempts"] += 1

                    # Genera simbolo
                    symbol_data = self.generate_unique_symbol(category, semantic_field)
                    if not symbol_data:
                        self.stats["failed_validations"] += 1
                        continue

                    # Valida simbolo
                    validation_result = self.validate_symbol(symbol_data)

                    # Verifica se passa i criteri
                    if validation_result["valid"] and validation_result["score"] >= self.min_score:
                        # Auto-approva
                        if self.approve_symbol(symbol_data, validation_result):
                            batch_results.append({
                                "symbol_data": symbol_data,
                                "validation_result": validation_result,
                                "status": "approved"
                            })
                            self.stats["successful_generations"] += 1
                        else:
                            batch_results.append({
                                "symbol_data": symbol_data,
                                "validation_result": validation_result,
                                "status": "approval_failed"
                            })
                    else:
                        # Rigetta
                        batch_results.append({
                            "symbol_data": symbol_data,
                            "validation_result": validation_result,
                            "status": "rejected"
                        })
                        self.stats["failed_validations"] += 1

                if self.generated_count >= self.target_count:
                    break

            if self.generated_count >= self.target_count:
                break

        return batch_results

    def run_pipeline(self) -> bool:
        """Esegue pipeline completa di generazione."""
        self.logger.info(f"🚀 Avvio pipeline ULTRA: target {self.target_count} simboli")
        self.logger.info(f"📊 Simboli esistenti: {self.generated_count}")
        self.logger.info(f"🎯 Simboli da generare: {self.target_count - self.generated_count}")

        start_time = time.time()

        while self.generated_count < self.target_count:
            batch_start = time.time()

            # Genera batch
            self.logger.info(f"🔄 Generazione batch {self.generated_count + 1}-{min(self.generated_count + self.batch_size, self.target_count)}")

            batch_results = self.generate_batch()

            # Statistiche batch
            approved = sum(1 for r in batch_results if r["status"] == "approved")
            rejected = sum(1 for r in batch_results if r["status"] == "rejected")

            batch_time = time.time() - batch_start

            self.logger.info(f"📈 Batch completato: {approved} approvati, {rejected} rigettati in {batch_time:.1f}s")

            # Aggiorna statistiche
            self.success_rate = (self.stats["successful_generations"] / max(1, self.stats["total_attempts"])) * 100
            elapsed_time = (time.time() - start_time) / 60  # minuti
            self.stats["generation_rate"] = self.stats["successful_generations"] / max(0.1, elapsed_time)

            # Progress report
            progress = (self.generated_count / self.target_count) * 100
            self.logger.info(f"🎯 Progresso: {self.generated_count}/{self.target_count} ({progress:.1f}%) - Success rate: {self.success_rate:.1f}% - Rate: {self.stats['generation_rate']:.1f} simboli/min")

            # Pausa tra batch per evitare overload
            if self.generated_count < self.target_count:
                time.sleep(1)

        # Report finale
        total_time = time.time() - start_time
        self.logger.info(f"🎉 PIPELINE COMPLETATA!")
        self.logger.info(f"✅ Generati {self.generated_count} simboli in {total_time/60:.1f} minuti")
        self.logger.info(f"📊 Success rate finale: {self.success_rate:.1f}%")
        self.logger.info(f"⚡ Rate medio: {self.stats['generation_rate']:.1f} simboli/minuto")

        return self.generated_count >= self.target_count

    def generate_final_report(self) -> str:
        """Genera report finale della generazione."""
        report_lines = [
            "🧠 NEUROGLYPH ULTRA - Final Generation Report",
            "=" * 60,
            f"🎯 Target: {self.target_count} simboli",
            f"✅ Generati: {self.generated_count} simboli",
            f"📊 Success rate: {self.success_rate:.1f}%",
            f"⚡ Rate medio: {self.stats['generation_rate']:.1f} simboli/minuto",
            f"🕒 Tempo totale: {(datetime.now() - self.stats['start_time']).total_seconds()/60:.1f} minuti",
            "",
            "📈 STATISTICHE DETTAGLIATE:",
            f"  • Tentativi totali: {self.stats['total_attempts']}",
            f"  • Generazioni riuscite: {self.stats['successful_generations']}",
            f"  • Validazioni fallite: {self.stats['failed_validations']}",
            f"  • Duplicati rigettati: {self.stats['duplicate_rejections']}",
            "",
            "🏷️ DISTRIBUZIONE CATEGORIE:"
        ]

        # Carica registry per statistiche categorie
        try:
            if self.registry_path.exists():
                with open(self.registry_path, 'r', encoding='utf-8') as f:
                    registry = json.load(f)

                for category, info in registry.get("categories", {}).items():
                    target = self.categories.get(category, {}).get("count_target", 0)
                    current = info.get("count", 0)
                    percentage = (current / target * 100) if target > 0 else 0
                    report_lines.append(f"  • {category}: {current}/{target} ({percentage:.1f}%)")
        except Exception as e:
            report_lines.append(f"  ❌ Errore lettura statistiche: {e}")

        report_lines.extend([
            "",
            "🚀 STATO FINALE:",
            f"  • Registry: {self.registry_path}",
            f"  • Log: {self.log_path}",
            f"  • Simboli pronti per primo LLM pensante: {self.generated_count >= self.target_count}",
            "",
            "🎉 NEUROGLYPH ULTRA READY!" if self.generated_count >= self.target_count else "⚠️ Target non raggiunto"
        ])

        return "\n".join(report_lines)


def main():
    """Main function per esecuzione da command line."""
    parser = argparse.ArgumentParser(
        description="🧠 NEUROGLYPH ULTRA - Automated 512 Symbol Generation Pipeline",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
🎯 ESEMPI DI UTILIZZO:

# Genera 512 simboli con criteri standard
python generate_512_symbols_ultra_pipeline.py

# Genera 256 simboli con score minimo 95%
python generate_512_symbols_ultra_pipeline.py --target 256 --min-score 95

# Genera con batch più piccoli per debugging
python generate_512_symbols_ultra_pipeline.py --batch-size 5 --target 50

🚀 PIPELINE AUTOMATICA:
1. Analizza simboli esistenti (evita duplicati)
2. Genera simbolo unico con AI semantica
3. Esegue validazione completa USU/CTU/LCL
4. Auto-approva solo simboli perfetti (score ≥ min-score)
5. Si ripete fino a raggiungere target

✅ QUALITÀ GARANTITA: Solo simboli che passano tutti i test!
        """
    )

    parser.add_argument(
        "--target",
        type=int,
        default=512,
        help="Numero target di simboli da generare (default: 512)"
    )

    parser.add_argument(
        "--min-score",
        type=float,
        default=90.0,
        help="Score minimo per auto-approvazione (default: 90.0)"
    )

    parser.add_argument(
        "--batch-size",
        type=int,
        default=10,
        help="Dimensione batch per generazione (default: 10)"
    )

    parser.add_argument(
        "--resume",
        action="store_true",
        help="Riprende da simboli esistenti nel registry"
    )

    parser.add_argument(
        "--dry-run",
        action="store_true",
        help="Simula generazione senza salvare simboli"
    )

    parser.add_argument(
        "--verbose",
        action="store_true",
        help="Output verboso con dettagli validazione"
    )

    args = parser.parse_args()

    # Validazione argomenti
    if args.target <= 0:
        print("❌ Errore: target deve essere > 0")
        sys.exit(1)

    if args.min_score < 0 or args.min_score > 100:
        print("❌ Errore: min-score deve essere tra 0 e 100")
        sys.exit(1)

    if args.batch_size <= 0:
        print("❌ Errore: batch-size deve essere > 0")
        sys.exit(1)

    # Banner di avvio
    print("🧠 NEUROGLYPH ULTRA - Automated Symbol Generation Pipeline")
    print("=" * 70)
    print(f"🎯 Target: {args.target} simboli")
    print(f"📊 Score minimo: {args.min_score}%")
    print(f"📦 Batch size: {args.batch_size}")
    print(f"🔄 Resume: {'Sì' if args.resume else 'No'}")
    print(f"🧪 Dry run: {'Sì' if args.dry_run else 'No'}")
    print("=" * 70)

    try:
        # Crea generatore
        generator = UltraSymbolGenerator(
            target_count=args.target,
            min_score=args.min_score,
            batch_size=args.batch_size
        )

        if args.dry_run:
            print("🧪 DRY RUN: Simulazione senza salvataggio")
            # TODO: Implementa modalità dry-run
            return

        # Esegui pipeline
        success = generator.run_pipeline()

        # Report finale
        final_report = generator.generate_final_report()
        print("\n" + final_report)

        # Salva report
        report_path = Path("logs/ultra_generation_report.txt")
        with open(report_path, 'w', encoding='utf-8') as f:
            f.write(final_report)
        print(f"\n💾 Report salvato in: {report_path}")

        # Exit code
        if success:
            print("\n🎉 PIPELINE COMPLETATA CON SUCCESSO!")
            print("🚀 NEUROGLYPH ULTRA è pronto per il primo LLM pensante!")
            sys.exit(0)
        else:
            print("\n⚠️ Pipeline incompleta - vedere log per dettagli")
            sys.exit(1)

    except KeyboardInterrupt:
        print("\n⏹️ Pipeline interrotta dall'utente")
        sys.exit(130)

    except Exception as e:
        print(f"\n❌ Errore fatale: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)


if __name__ == "__main__":
    main()
