#!/usr/bin/env python3
"""
NEUROGLYPH LLM - Generate ULTRA Batch 2
======================================

Genera secondo batch ULTRA: 128 simboli per Classes + OOP
Focus: inheritance, composition, polymorphism, design patterns
Simboli più specifici per evitare duplicati
"""

import json
import unicodedata
from pathlib import Path
from typing import Dict, List, Tuple
from datetime import datetime

class UltraBatch2Generator:
    """Generatore secondo batch ULTRA - Classes + OOP"""
    
    def __init__(self):
        self.registry_path = Path("neuroglyph/core/symbols_registry.json")
        self.current_symbols = self._load_current_registry()
        self.batch_size = 128
        self.batch_theme = "classes_oop"
        
        # Simboli candidati per Classes + OOP (più specifici)
        self.oop_candidates = self._define_oop_candidates()
        
    def _load_current_registry(self) -> Dict:
        """Carica registry attuale"""
        if self.registry_path.exists():
            with open(self.registry_path, 'r', encoding='utf-8') as f:
                return json.load(f)
        return {"approved_symbols": []}
    
    def _define_oop_candidates(self) -> List[Dict]:
        """Definisce candidati simboli per OOP - simboli più specifici e unici"""
        candidates = []
        
        # 1. INHERITANCE PATTERNS (32 simboli) - Unicode specifici
        inheritance_patterns = [
            # Core inheritance - simboli matematici specifici
            ("⊃", "SUPERSET", "ng:inherit:superclass", "[super]", "Superclasse"),
            ("⊂", "SUBSET", "ng:inherit:subclass", "[sub]", "Sottoclasse"),
            ("⊇", "SUPERSET_EQUAL", "ng:inherit:extends", "[extends]", "Estende classe"),
            ("⊆", "SUBSET_EQUAL", "ng:inherit:implements", "[impl]", "Implementa interfaccia"),
            ("∈", "ELEMENT_OF", "ng:inherit:instanceof", "[instanceof]", "Istanza di"),
            ("∉", "NOT_ELEMENT_OF", "ng:inherit:not_instanceof", "[!instanceof]", "Non istanza di"),
            ("∋", "CONTAINS", "ng:inherit:contains", "[contains]", "Contiene tipo"),
            ("∌", "NOT_CONTAINS", "ng:inherit:not_contains", "[!contains]", "Non contiene tipo"),
            
            # Inheritance relationships - simboli frecce specifici
            ("⇈", "UPWARDS_PAIRED_ARROWS", "ng:inherit:up_cast", "[upcast]", "Upcast"),
            ("⇊", "DOWNWARDS_PAIRED_ARROWS", "ng:inherit:down_cast", "[downcast]", "Downcast"),
            ("⇅", "UP_DOWN_ARROW", "ng:inherit:cast", "[cast]", "Cast"),
            ("⇵", "DOWNWARDS_ARROW_LEFTWARDS", "ng:inherit:safe_cast", "[safe_cast]", "Safe cast"),
            ("⟰", "UPWARDS_QUADRUPLE_ARROW", "ng:inherit:multi_inherit", "[multi]", "Ereditarietà multipla"),
            ("⟱", "DOWNWARDS_QUADRUPLE_ARROW", "ng:inherit:diamond", "[diamond]", "Diamond problem"),
            ("⤊", "UPWARDS_ARROW_THROUGH_CIRCLE", "ng:inherit:override", "[override]", "Override metodo"),
            ("⤋", "DOWNWARDS_ARROW_THROUGH_CIRCLE", "ng:inherit:virtual", "[virtual]", "Metodo virtuale"),
            
            # Interface patterns - simboli geometrici
            ("◊", "WHITE_DIAMOND", "ng:interface:interface", "[interface]", "Interfaccia"),
            ("◈", "WHITE_DIAMOND_CONTAINING_BLACK", "ng:interface:abstract", "[abstract]", "Classe astratta"),
            ("◇", "WHITE_DIAMOND_SUIT", "ng:interface:protocol", "[protocol]", "Protocollo"),
            ("◆", "BLACK_DIAMOND", "ng:interface:concrete", "[concrete]", "Implementazione concreta"),
            ("⬟", "BLACK_PENTAGON", "ng:interface:mixin", "[mixin]", "Mixin"),
            ("⬠", "WHITE_PENTAGON", "ng:interface:trait", "[trait]", "Trait"),
            ("⬢", "BLACK_HEXAGON", "ng:interface:contract", "[contract]", "Contratto"),
            ("⬡", "WHITE_HEXAGON", "ng:interface:signature", "[signature]", "Signature"),
            
            # Visibility modifiers - simboli specifici
            ("⊕", "CIRCLED_PLUS", "ng:visibility:public", "[public]", "Pubblico"),
            ("⊖", "CIRCLED_MINUS", "ng:visibility:private", "[private]", "Privato"),
            ("⊗", "CIRCLED_TIMES", "ng:visibility:protected", "[protected]", "Protetto"),
            ("⊘", "CIRCLED_DIVISION_SLASH", "ng:visibility:internal", "[internal]", "Interno"),
            ("⊙", "CIRCLED_DOT", "ng:visibility:package", "[package]", "Package"),
            ("⊚", "CIRCLED_RING", "ng:visibility:friend", "[friend]", "Friend"),
            ("⊛", "CIRCLED_ASTERISK", "ng:visibility:static", "[static]", "Statico"),
            ("⊜", "CIRCLED_EQUALS", "ng:visibility:final", "[final]", "Finale")
        ]
        
        # 2. COMPOSITION PATTERNS (32 simboli) - Simboli di composizione
        composition_patterns = [
            # Core composition - simboli di aggregazione
            ("⟐", "DOUBLE_SUBSET", "ng:compose:has_a", "[has_a]", "Ha una relazione"),
            ("⟑", "SUPERSET_WITH_DOT", "ng:compose:part_of", "[part_of]", "Parte di"),
            ("⟒", "SUBSET_WITH_DOT", "ng:compose:contains", "[contains]", "Contiene"),
            ("⟓", "SUPERSET_WITH_PLUS", "ng:compose:aggregates", "[aggregates]", "Aggrega"),
            ("⟔", "SUBSET_WITH_PLUS", "ng:compose:composed_of", "[composed_of]", "Composto da"),
            ("⟕", "SUPERSET_WITH_MULTIPLICATION", "ng:compose:owns", "[owns]", "Possiede"),
            ("⟖", "SUBSET_WITH_MULTIPLICATION", "ng:compose:owned_by", "[owned_by]", "Posseduto da"),
            ("⟗", "SUPERSET_WITH_DOT_ABOVE", "ng:compose:delegates", "[delegates]", "Delega a"),
            
            # Dependency injection - simboli di iniezione
            ("⤷", "ARROW_POINTING_DOWNWARDS_THEN_CURVING_LEFTWARDS", "ng:inject:dependency", "[inject]", "Iniezione dipendenza"),
            ("⤶", "ARROW_POINTING_DOWNWARDS_THEN_CURVING_RIGHTWARDS", "ng:inject:provide", "[provide]", "Fornisce dipendenza"),
            ("⤸", "ARROW_POINTING_RIGHTWARDS_THEN_CURVING_DOWNWARDS", "ng:inject:wire", "[wire]", "Collega dipendenze"),
            ("⤹", "ARROW_POINTING_LEFTWARDS_THEN_CURVING_DOWNWARDS", "ng:inject:resolve", "[resolve]", "Risolve dipendenza"),
            ("⤺", "ANTICLOCKWISE_TOP_SEMICIRCLE_ARROW", "ng:inject:circular", "[circular]", "Dipendenza circolare"),
            ("⤻", "CLOCKWISE_TOP_SEMICIRCLE_ARROW", "ng:inject:singleton", "[singleton]", "Singleton"),
            ("⤼", "LEFTWARDS_ARROW_WITH_LOOP", "ng:inject:factory", "[factory]", "Factory"),
            ("⤽", "RIGHTWARDS_ARROW_WITH_LOOP", "ng:inject:builder", "[builder]", "Builder"),
            
            # Object lifecycle - simboli di ciclo vita
            ("⟲", "ANTICLOCKWISE_GAPPED_CIRCLE_ARROW", "ng:lifecycle:create", "[create]", "Crea oggetto"),
            ("⟳", "CLOCKWISE_GAPPED_CIRCLE_ARROW", "ng:lifecycle:destroy", "[destroy]", "Distrugge oggetto"),
            ("⟴", "RIGHT_ARROW_WITH_SMALL_CIRCLE", "ng:lifecycle:init", "[init]", "Inizializza"),
            ("⟵", "LEFTWARDS_ARROW_FROM_BAR", "ng:lifecycle:cleanup", "[cleanup]", "Pulizia"),
            ("⟶", "RIGHTWARDS_ARROW_FROM_BAR", "ng:lifecycle:finalize", "[finalize]", "Finalizza"),
            ("⟷", "LEFT_RIGHT_ARROW", "ng:lifecycle:copy", "[copy]", "Copia oggetto"),
            ("⟸", "LEFTWARDS_DOUBLE_ARROW_FROM_BAR", "ng:lifecycle:move", "[move]", "Sposta oggetto"),
            ("⟹", "RIGHTWARDS_DOUBLE_ARROW_FROM_BAR", "ng:lifecycle:clone", "[clone]", "Clona oggetto"),
            
            # Memory management - simboli di gestione memoria
            ("⊞", "SQUARED_PLUS", "ng:memory:allocate", "[alloc]", "Alloca memoria"),
            ("⊟", "SQUARED_MINUS", "ng:memory:deallocate", "[dealloc]", "Dealloca memoria"),
            ("⊠", "SQUARED_TIMES", "ng:memory:reference", "[ref]", "Riferimento"),
            ("⊡", "SQUARED_DOT", "ng:memory:weak_ref", "[weak]", "Riferimento debole"),
            ("⊢", "RIGHT_TACK", "ng:memory:strong_ref", "[strong]", "Riferimento forte"),
            ("⊣", "LEFT_TACK", "ng:memory:shared_ref", "[shared]", "Riferimento condiviso"),
            ("⊤", "DOWN_TACK", "ng:memory:unique_ref", "[unique]", "Riferimento unico"),
            ("⊥", "UP_TACK", "ng:memory:null_ref", "[null]", "Riferimento nullo")
        ]
        
        # 3. POLYMORPHISM PATTERNS (32 simboli) - Simboli di polimorfismo
        polymorphism_patterns = [
            # Core polymorphism - simboli di trasformazione
            ("⇔", "LEFT_RIGHT_DOUBLE_ARROW", "ng:poly:polymorphic", "[poly]", "Polimorfico"),
            ("⇎", "LEFT_RIGHT_DOUBLE_ARROW_WITH_STROKE", "ng:poly:monomorphic", "[mono]", "Monomorfico"),
            ("⇏", "RIGHTWARDS_DOUBLE_ARROW_WITH_STROKE", "ng:poly:dispatch", "[dispatch]", "Dispatch"),
            ("⇍", "LEFTWARDS_DOUBLE_ARROW_WITH_STROKE", "ng:poly:resolve", "[resolve]", "Risoluzione"),
            ("⇐", "LEFTWARDS_DOUBLE_ARROW", "ng:poly:late_binding", "[late]", "Late binding"),
            ("⇒", "RIGHTWARDS_DOUBLE_ARROW", "ng:poly:early_binding", "[early]", "Early binding"),
            ("⇓", "DOWNWARDS_DOUBLE_ARROW", "ng:poly:dynamic", "[dynamic]", "Dinamico"),
            ("⇑", "UPWARDS_DOUBLE_ARROW", "ng:poly:static", "[static]", "Statico"),
            
            # Method resolution - simboli di risoluzione
            ("⟾", "RIGHTWARDS_ARROW_WITH_DOUBLE_VERTICAL_STROKE", "ng:method:virtual_call", "[vcall]", "Chiamata virtuale"),
            ("⟿", "LEFTWARDS_ARROW_WITH_DOUBLE_VERTICAL_STROKE", "ng:method:direct_call", "[dcall]", "Chiamata diretta"),
            ("⤀", "RIGHTWARDS_TWO_HEADED_ARROW_WITH_VERTICAL_STROKE", "ng:method:override", "[override]", "Override"),
            ("⤁", "LEFTWARDS_TWO_HEADED_ARROW_WITH_VERTICAL_STROKE", "ng:method:overload", "[overload]", "Overload"),
            ("⤂", "LEFTWARDS_ARROW_WITH_TAIL", "ng:method:super_call", "[super]", "Chiamata super"),
            ("⤃", "RIGHTWARDS_ARROW_WITH_TAIL", "ng:method:this_call", "[this]", "Chiamata this"),
            ("⤄", "LEFTWARDS_ARROW_FROM_BAR", "ng:method:delegate", "[delegate]", "Delega"),
            ("⤅", "RIGHTWARDS_ARROW_FROM_BAR", "ng:method:forward", "[forward]", "Inoltra"),
            
            # Generic types - simboli di generici
            ("⟨", "MATHEMATICAL_LEFT_ANGLE_BRACKET", "ng:generic:open", "[<]", "Apri generici"),
            ("⟩", "MATHEMATICAL_RIGHT_ANGLE_BRACKET", "ng:generic:close", "[>]", "Chiudi generici"),
            ("⟪", "MATHEMATICAL_LEFT_DOUBLE_ANGLE_BRACKET", "ng:generic:nested_open", "[<<]", "Apri generici annidati"),
            ("⟫", "MATHEMATICAL_RIGHT_DOUBLE_ANGLE_BRACKET", "ng:generic:nested_close", "[>>]", "Chiudi generici annidati"),
            ("⟬", "MATHEMATICAL_LEFT_WHITE_TORTOISE_SHELL_BRACKET", "ng:generic:constraint", "[where]", "Vincolo generico"),
            ("⟭", "MATHEMATICAL_RIGHT_WHITE_TORTOISE_SHELL_BRACKET", "ng:generic:bound", "[bound]", "Limite generico"),
            ("⟮", "MATHEMATICAL_LEFT_FLATTENED_PARENTHESIS", "ng:generic:variance", "[var]", "Varianza"),
            ("⟯", "MATHEMATICAL_RIGHT_FLATTENED_PARENTHESIS", "ng:generic:covariance", "[covar]", "Covarianza"),
            
            # Type parameters - simboli di parametri tipo
            ("⦀", "THREE_DIMENSIONAL_ANGLE", "ng:type:parameter", "[T]", "Parametro tipo"),
            ("⦁", "BULLET", "ng:type:wildcard", "[?]", "Wildcard"),
            ("⦂", "Z_NOTATION_TYPE_COLON", "ng:type:annotation", "[:]", "Annotazione tipo"),
            ("⦃", "LEFT_WHITE_CURLY_BRACKET", "ng:type:union", "[|]", "Unione tipi"),
            ("⦄", "RIGHT_WHITE_CURLY_BRACKET", "ng:type:intersection", "[&]", "Intersezione tipi"),
            ("⦅", "LEFT_WHITE_PARENTHESIS", "ng:type:optional", "[?]", "Tipo opzionale"),
            ("⦆", "RIGHT_WHITE_PARENTHESIS", "ng:type:nullable", "[?]", "Tipo nullable"),
            ("⦇", "Z_NOTATION_LEFT_IMAGE_BRACKET", "ng:type:array", "[[]]", "Tipo array")
        ]
        
        # 4. DESIGN PATTERNS (32 simboli) - Simboli di pattern
        pattern_symbols = [
            # Creational patterns - simboli di creazione
            ("⚒", "HAMMER_AND_PICK", "ng:pattern:factory", "[factory]", "Factory Pattern"),
            ("⚓", "ANCHOR", "ng:pattern:singleton", "[singleton]", "Singleton Pattern"),
            ("⚔", "CROSSED_SWORDS", "ng:pattern:builder", "[builder]", "Builder Pattern"),
            ("⚕", "STAFF_OF_AESCULAPIUS", "ng:pattern:prototype", "[prototype]", "Prototype Pattern"),
            ("⚖", "SCALES", "ng:pattern:abstract_factory", "[abs_factory]", "Abstract Factory"),
            ("⚗", "ALEMBIC", "ng:pattern:object_pool", "[pool]", "Object Pool"),
            ("⚘", "FLOWER", "ng:pattern:dependency_injection", "[di]", "Dependency Injection"),
            ("⚙", "GEAR", "ng:pattern:service_locator", "[locator]", "Service Locator"),
            
            # Structural patterns - simboli di struttura
            ("⚚", "STAFF_OF_HERMES", "ng:pattern:adapter", "[adapter]", "Adapter Pattern"),
            ("⚛", "ATOM_SYMBOL", "ng:pattern:bridge", "[bridge]", "Bridge Pattern"),
            ("⚜", "FLEUR_DE_LIS", "ng:pattern:composite", "[composite]", "Composite Pattern"),
            ("⚝", "OUTLINED_WHITE_STAR", "ng:pattern:decorator", "[decorator]", "Decorator Pattern"),
            ("⚞", "TURNED_WHITE_SHOGI_PIECE", "ng:pattern:facade", "[facade]", "Facade Pattern"),
            ("⚟", "TURNED_BLACK_SHOGI_PIECE", "ng:pattern:flyweight", "[flyweight]", "Flyweight Pattern"),
            ("⚠", "WARNING_SIGN", "ng:pattern:proxy", "[proxy]", "Proxy Pattern"),
            ("⚡", "HIGH_VOLTAGE_SIGN", "ng:pattern:module", "[module]", "Module Pattern"),
            
            # Behavioral patterns - simboli di comportamento
            ("⚢", "DOUBLED_FEMALE_SIGN", "ng:pattern:observer", "[observer]", "Observer Pattern"),
            ("⚣", "DOUBLED_MALE_SIGN", "ng:pattern:strategy", "[strategy]", "Strategy Pattern"),
            ("⚤", "MALE_AND_FEMALE_SIGN", "ng:pattern:command", "[command]", "Command Pattern"),
            ("⚥", "MALE_WITH_STROKE_SIGN", "ng:pattern:state", "[state]", "State Pattern"),
            ("⚦", "MALE_WITH_STROKE_AND_MALE_AND_FEMALE_SIGN", "ng:pattern:template", "[template]", "Template Method"),
            ("⚧", "TRANSGENDER_SYMBOL", "ng:pattern:visitor", "[visitor]", "Visitor Pattern"),
            ("⚨", "VERTICAL_MALE_WITH_STROKE_SIGN", "ng:pattern:mediator", "[mediator]", "Mediator Pattern"),
            ("⚩", "HORIZONTAL_MALE_WITH_STROKE_SIGN", "ng:pattern:chain", "[chain]", "Chain of Responsibility"),
            
            # Advanced patterns - simboli avanzati
            ("⚪", "MEDIUM_WHITE_CIRCLE", "ng:pattern:mvc", "[mvc]", "MVC Pattern"),
            ("⚫", "MEDIUM_BLACK_CIRCLE", "ng:pattern:mvp", "[mvp]", "MVP Pattern"),
            ("⚬", "MEDIUM_SMALL_WHITE_CIRCLE", "ng:pattern:mvvm", "[mvvm]", "MVVM Pattern"),
            ("⚭", "MARRIAGE_SYMBOL", "ng:pattern:repository", "[repo]", "Repository Pattern"),
            ("⚮", "DIVORCE_SYMBOL", "ng:pattern:unit_of_work", "[uow]", "Unit of Work"),
            ("⚯", "UNMARRIED_PARTNERSHIP_SYMBOL", "ng:pattern:specification", "[spec]", "Specification Pattern"),
            ("⚰", "COFFIN", "ng:pattern:null_object", "[null_obj]", "Null Object Pattern"),
            ("⚱", "FUNERAL_URN", "ng:pattern:memento", "[memento]", "Memento Pattern")
        ]
        
        # Combina tutti i pattern
        all_patterns = inheritance_patterns + composition_patterns + polymorphism_patterns + pattern_symbols
        
        # Converti in formato candidati
        for symbol, unicode_name, ng_code, fallback, description in all_patterns:
            candidates.append({
                "symbol": symbol,
                "unicode_name": unicode_name,
                "ng_code": ng_code,
                "fallback": fallback,
                "description": description,
                "category": "classes_oop",
                "batch": 2,
                "tier": "ultra"
            })
        
        return candidates
    
    def validate_candidates(self, candidates: List[Dict]) -> List[Dict]:
        """Valida candidati per qualità e unicità"""
        print(f"🔍 Validazione {len(candidates)} candidati...")
        
        # Simboli già esistenti
        existing_symbols = {s["symbol"] for s in self.current_symbols.get("approved_symbols", [])}
        existing_codes = {s["code"] for s in self.current_symbols.get("approved_symbols", [])}
        
        validated = []
        
        for candidate in candidates:
            symbol = candidate["symbol"]
            ng_code = candidate["ng_code"]
            
            # Skip se già esistente
            if symbol in existing_symbols:
                print(f"   ⚠️ Simbolo già esistente: {symbol}")
                continue
            
            if ng_code in existing_codes:
                print(f"   ⚠️ Codice già esistente: {ng_code}")
                continue
            
            # Validazione Unicode
            try:
                symbol.encode('utf-8')
                unicodedata.name(symbol)  # Deve avere nome Unicode
                if len(symbol) == 1:  # Solo simboli singoli
                    validated.append(candidate)
                else:
                    print(f"   ❌ Simbolo multi-carattere: {symbol}")
            except (UnicodeError, ValueError):
                print(f"   ❌ Simbolo non valido Unicode: {symbol}")
        
        print(f"✅ Validati {len(validated)} candidati")
        return validated
    
    def select_best_candidates(self, candidates: List[Dict]) -> List[Dict]:
        """Seleziona i migliori candidati per il batch"""
        print(f"🎯 Selezione migliori {self.batch_size} candidati...")
        
        # Score candidati
        scored_candidates = []
        
        for candidate in candidates:
            score = self._calculate_candidate_score(candidate)
            candidate["score"] = score
            scored_candidates.append(candidate)
        
        # Ordina per score
        scored_candidates.sort(key=lambda x: x["score"], reverse=True)
        
        # Seleziona top batch_size
        selected = scored_candidates[:self.batch_size]
        
        print(f"✅ Selezionati {len(selected)} candidati migliori")
        return selected
    
    def _calculate_candidate_score(self, candidate: Dict) -> float:
        """Calcola score qualità candidato"""
        score = 1.0
        
        symbol = candidate["symbol"]
        
        # Bonus per simboli matematici (più stabili)
        try:
            name = unicodedata.name(symbol).lower()
            if "mathematical" in name:
                score += 0.3
            if "arrow" in name:
                score += 0.2
            if "circle" in name:
                score += 0.1
        except:
            pass
        
        # Bonus per fallback chiari
        fallback = candidate.get("fallback", "")
        if len(fallback) <= 10:
            score += 0.1
        
        # Bonus per descrizioni specifiche
        description = candidate.get("description", "")
        if "pattern" in description.lower():
            score += 0.2
        
        return score
    
    def generate_batch_symbols(self) -> List[Dict]:
        """Genera batch completo di simboli"""
        print("🚀 GENERAZIONE BATCH 2 ULTRA - CLASSES + OOP")
        print("=" * 70)
        
        # 1. Definisci candidati
        candidates = self.oop_candidates
        print(f"📋 Candidati definiti: {len(candidates)}")
        
        # 2. Valida candidati
        validated = self.validate_candidates(candidates)
        
        # 3. Seleziona migliori
        selected = self.select_best_candidates(validated)
        
        # 4. Formatta per registry
        formatted = []
        current_count = len(self.current_symbols.get("approved_symbols", []))
        
        for i, candidate in enumerate(selected):
            symbol_id = f"NG{current_count + i + 1:04d}"
            
            formatted_symbol = {
                "id": symbol_id,
                "symbol": candidate["symbol"],
                "code": candidate["ng_code"],
                "fallback": candidate["fallback"],
                "description": candidate["description"],
                "category": candidate["category"],
                "batch": candidate["batch"],
                "tier": candidate["tier"],
                "approved_date": datetime.now().strftime("%Y-%m-%d"),
                "validation_score": candidate.get("score", 1.0),
                "status": "pending_approval"
            }
            
            formatted.append(formatted_symbol)
        
        return formatted
    
    def save_batch_proposal(self, symbols: List[Dict]) -> str:
        """Salva proposta batch per review"""
        output_path = Path("neuroglyph/symbols/ultra_batch2_proposal.json")
        
        proposal = {
            "batch_info": {
                "batch_number": 2,
                "tier": "ultra",
                "theme": self.batch_theme,
                "target_size": self.batch_size,
                "actual_size": len(symbols),
                "generated_date": datetime.now().isoformat()
            },
            "symbols": symbols,
            "summary": {
                "inheritance_patterns": len([s for s in symbols if "inherit" in s["code"]]),
                "composition_patterns": len([s for s in symbols if "compose" in s["code"] or "inject" in s["code"] or "lifecycle" in s["code"] or "memory" in s["code"]]),
                "polymorphism_patterns": len([s for s in symbols if "poly" in s["code"] or "method" in s["code"] or "generic" in s["code"] or "type" in s["code"]]),
                "design_patterns": len([s for s in symbols if "pattern" in s["code"]])
            }
        }
        
        with open(output_path, 'w', encoding='utf-8') as f:
            json.dump(proposal, f, indent=2, ensure_ascii=False)
        
        print(f"💾 Proposta batch salvata: {output_path}")
        return str(output_path)

def main():
    """Genera secondo batch ULTRA"""
    print("🧠 NEUROGLYPH LLM - Generate ULTRA Batch 2")
    print("🎯 Classes + OOP Symbols")
    print("=" * 70)
    
    generator = UltraBatch2Generator()
    
    # Genera simboli
    symbols = generator.generate_batch_symbols()
    
    # Salva proposta
    proposal_path = generator.save_batch_proposal(symbols)
    
    # Mostra statistiche
    print(f"\n📊 BATCH 2 GENERATO:")
    print(f"   Simboli generati: {len(symbols)}")
    print(f"   Target batch: {generator.batch_size}")
    print(f"   Tema: {generator.batch_theme}")
    
    # Mostra primi esempi
    print(f"\n🎯 PRIMI 10 SIMBOLI:")
    for i, symbol in enumerate(symbols[:10]):
        print(f"   {i+1}. {symbol['symbol']} ({symbol['code']}) - {symbol['description']}")
    
    print(f"\n✅ Batch 2 ULTRA pronto per review!")
    print(f"📁 File: {proposal_path}")
    
    return symbols

if __name__ == "__main__":
    main()
