#!/usr/bin/env python3
"""
NEUROGLYPH LLM - Generate ULTRA Batch 3
======================================

Genera terzo batch ULTRA: 128 simboli per Data Structures
Focus: collections, trees, graphs, algorithms
Target: completare ULTRA Tier (755 → 883 simboli)
"""

import json
import unicodedata
from pathlib import Path
from typing import Dict, List, Tuple
from datetime import datetime

class UltraBatch3Generator:
    """Generatore terzo batch ULTRA - Data Structures"""
    
    def __init__(self):
        self.registry_path = Path("neuroglyph/core/symbols_registry.json")
        self.current_symbols = self._load_current_registry()
        self.batch_size = 128
        self.batch_theme = "data_structures"
        
        # Simboli candidati per Data Structures
        self.data_candidates = self._define_data_candidates()
        
    def _load_current_registry(self) -> Dict:
        """Carica registry attuale"""
        if self.registry_path.exists():
            with open(self.registry_path, 'r', encoding='utf-8') as f:
                return json.load(f)
        return {"approved_symbols": []}
    
    def _define_data_candidates(self) -> List[Dict]:
        """Definisce candidati simboli per Data Structures"""
        candidates = []
        
        # 1. COLLECTIONS (32 simboli) - Simboli di collezioni
        collection_patterns = [
            # Basic collections - simboli contenitori
            ("⟦", "MATHEMATICAL_LEFT_WHITE_SQUARE_BRACKET", "ng:collection:list", "[list]", "Lista"),
            ("⟧", "MATHEMATICAL_RIGHT_WHITE_SQUARE_BRACKET", "ng:collection:list_end", "[/list]", "Fine lista"),
            ("⟨", "MATHEMATICAL_LEFT_ANGLE_BRACKET", "ng:collection:vector", "[vec]", "Vettore"),
            ("⟩", "MATHEMATICAL_RIGHT_ANGLE_BRACKET", "ng:collection:vector_end", "[/vec]", "Fine vettore"),
            ("⟪", "MATHEMATICAL_LEFT_DOUBLE_ANGLE_BRACKET", "ng:collection:array", "[array]", "Array"),
            ("⟫", "MATHEMATICAL_RIGHT_DOUBLE_ANGLE_BRACKET", "ng:collection:array_end", "[/array]", "Fine array"),
            ("⦃", "LEFT_WHITE_CURLY_BRACKET", "ng:collection:set", "[set]", "Set"),
            ("⦄", "RIGHT_WHITE_CURLY_BRACKET", "ng:collection:set_end", "[/set]", "Fine set"),
            
            # Advanced collections - simboli avanzati
            ("⦅", "LEFT_WHITE_PARENTHESIS", "ng:collection:tuple", "[tuple]", "Tupla"),
            ("⦆", "RIGHT_WHITE_PARENTHESIS", "ng:collection:tuple_end", "[/tuple]", "Fine tupla"),
            ("⦇", "Z_NOTATION_LEFT_IMAGE_BRACKET", "ng:collection:map", "[map]", "Mappa"),
            ("⦈", "Z_NOTATION_RIGHT_IMAGE_BRACKET", "ng:collection:map_end", "[/map]", "Fine mappa"),
            ("⦉", "Z_NOTATION_LEFT_BINDING_BRACKET", "ng:collection:dict", "[dict]", "Dizionario"),
            ("⦊", "Z_NOTATION_RIGHT_BINDING_BRACKET", "ng:collection:dict_end", "[/dict]", "Fine dizionario"),
            ("⦋", "LEFT_SQUARE_BRACKET_WITH_UNDERBAR", "ng:collection:queue", "[queue]", "Coda"),
            ("⦌", "RIGHT_SQUARE_BRACKET_WITH_UNDERBAR", "ng:collection:queue_end", "[/queue]", "Fine coda"),
            
            # Collection operations - operazioni
            ("⊞", "SQUARED_PLUS", "ng:collection:add", "[add]", "Aggiungi elemento"),
            ("⊟", "SQUARED_MINUS", "ng:collection:remove", "[remove]", "Rimuovi elemento"),
            ("⊠", "SQUARED_TIMES", "ng:collection:multiply", "[mult]", "Moltiplica collezione"),
            ("⊡", "SQUARED_DOT", "ng:collection:contains", "[contains]", "Contiene elemento"),
            ("⊢", "RIGHT_TACK", "ng:collection:push", "[push]", "Push elemento"),
            ("⊣", "LEFT_TACK", "ng:collection:pop", "[pop]", "Pop elemento"),
            ("⊤", "DOWN_TACK", "ng:collection:top", "[top]", "Elemento in cima"),
            ("⊥", "UP_TACK", "ng:collection:bottom", "[bottom]", "Elemento in fondo"),
            
            # Iteration - iterazione
            ("⊦", "ASSERTION", "ng:collection:iterate", "[iter]", "Itera collezione"),
            ("⊧", "MODELS", "ng:collection:foreach", "[foreach]", "Per ogni elemento"),
            ("⊨", "TRUE", "ng:collection:filter", "[filter]", "Filtra collezione"),
            ("⊩", "FORCES", "ng:collection:map_op", "[map]", "Mappa operazione"),
            ("⊪", "TRIPLE_VERTICAL_BAR_RIGHT_TURNSTILE", "ng:collection:reduce", "[reduce]", "Riduci collezione"),
            ("⊫", "DOUBLE_VERTICAL_BAR_DOUBLE_RIGHT_TURNSTILE", "ng:collection:fold", "[fold]", "Fold collezione"),
            ("⊬", "DOES_NOT_PROVE", "ng:collection:empty", "[empty]", "Collezione vuota"),
            ("⊭", "NOT_TRUE", "ng:collection:full", "[full]", "Collezione piena")
        ]
        
        # 2. TREES (32 simboli) - Strutture ad albero
        tree_patterns = [
            # Tree structure - struttura albero
            ("⊰", "UPWARDS_ARROW_WITH_TIP_LEFTWARDS", "ng:tree:root", "[root]", "Radice albero"),
            ("⊱", "UPWARDS_ARROW_WITH_TIP_RIGHTWARDS", "ng:tree:leaf", "[leaf]", "Foglia albero"),
            ("⋀", "N_ARY_LOGICAL_AND", "ng:tree:branch", "[branch]", "Ramo albero"),
            ("⋁", "N_ARY_LOGICAL_OR", "ng:tree:node", "[node]", "Nodo albero"),
            ("⋂", "N_ARY_INTERSECTION", "ng:tree:subtree", "[subtree]", "Sottoalbero"),
            ("⋃", "N_ARY_UNION", "ng:tree:merge", "[merge]", "Unisci alberi"),
            ("⋄", "DIAMOND_OPERATOR", "ng:tree:parent", "[parent]", "Nodo genitore"),
            ("⋅", "DOT_OPERATOR", "ng:tree:child", "[child]", "Nodo figlio"),
            
            # Tree traversal - attraversamento
            ("⋆", "STAR_OPERATOR", "ng:tree:preorder", "[preorder]", "Preorder traversal"),
            ("⋇", "DIVISION_TIMES", "ng:tree:inorder", "[inorder]", "Inorder traversal"),
            ("⋈", "BOWTIE", "ng:tree:postorder", "[postorder]", "Postorder traversal"),
            ("⋉", "LEFT_NORMAL_FACTOR_SEMIDIRECT_PRODUCT", "ng:tree:levelorder", "[levelorder]", "Level order traversal"),
            ("⋊", "RIGHT_NORMAL_FACTOR_SEMIDIRECT_PRODUCT", "ng:tree:dfs", "[dfs]", "Depth-first search"),
            ("⋋", "LEFT_SEMIDIRECT_PRODUCT", "ng:tree:bfs", "[bfs]", "Breadth-first search"),
            ("⋌", "RIGHT_SEMIDIRECT_PRODUCT", "ng:tree:search", "[search]", "Cerca in albero"),
            ("⋍", "REVERSED_TILDE_EQUALS", "ng:tree:find", "[find]", "Trova nodo"),
            
            # Binary trees - alberi binari
            ("⋎", "CURLY_LOGICAL_OR", "ng:tree:binary", "[binary]", "Albero binario"),
            ("⋏", "CURLY_LOGICAL_AND", "ng:tree:balanced", "[balanced]", "Albero bilanciato"),
            ("⋐", "DOUBLE_SUBSET", "ng:tree:left_child", "[left]", "Figlio sinistro"),
            ("⋑", "DOUBLE_SUPERSET", "ng:tree:right_child", "[right]", "Figlio destro"),
            ("⋒", "DOUBLE_INTERSECTION", "ng:tree:height", "[height]", "Altezza albero"),
            ("⋓", "DOUBLE_UNION", "ng:tree:depth", "[depth]", "Profondità nodo"),
            ("⋔", "PITCHFORK", "ng:tree:rotate_left", "[rot_left]", "Rotazione sinistra"),
            ("⋕", "EQUAL_AND_PARALLEL_TO", "ng:tree:rotate_right", "[rot_right]", "Rotazione destra"),
            
            # Tree types - tipi di albero
            ("⋖", "LESS_THAN_WITH_DOT", "ng:tree:bst", "[bst]", "Binary search tree"),
            ("⋗", "GREATER_THAN_WITH_DOT", "ng:tree:avl", "[avl]", "AVL tree"),
            ("⋘", "VERY_MUCH_LESS_THAN", "ng:tree:red_black", "[rb]", "Red-black tree"),
            ("⋙", "VERY_MUCH_GREATER_THAN", "ng:tree:btree", "[btree]", "B-tree"),
            ("⋚", "LESS_THAN_EQUAL_TO_OR_GREATER_THAN", "ng:tree:heap", "[heap]", "Heap"),
            ("⋛", "GREATER_THAN_EQUAL_TO_OR_LESS_THAN", "ng:tree:trie", "[trie]", "Trie"),
            ("⋜", "EQUAL_TO_OR_LESS_THAN", "ng:tree:suffix", "[suffix]", "Suffix tree"),
            ("⋝", "EQUAL_TO_OR_GREATER_THAN", "ng:tree:segment", "[segment]", "Segment tree")
        ]
        
        # 3. GRAPHS (32 simboli) - Grafi
        graph_patterns = [
            # Graph structure - struttura grafo
            ("⋞", "EQUAL_TO_OR_PRECEDES", "ng:graph:graph", "[graph]", "Grafo"),
            ("⋟", "EQUAL_TO_OR_SUCCEEDS", "ng:graph:vertex", "[vertex]", "Vertice"),
            ("⋠", "DOES_NOT_PRECEDE_OR_EQUAL", "ng:graph:edge", "[edge]", "Arco"),
            ("⋡", "DOES_NOT_SUCCEED_OR_EQUAL", "ng:graph:weight", "[weight]", "Peso arco"),
            ("⋢", "NOT_SQUARE_IMAGE_OF_OR_EQUAL_TO", "ng:graph:directed", "[directed]", "Grafo diretto"),
            ("⋣", "NOT_SQUARE_ORIGINAL_OF_OR_EQUAL_TO", "ng:graph:undirected", "[undirected]", "Grafo non diretto"),
            ("⋤", "SQUARE_IMAGE_OF_OR_NOT_EQUAL_TO", "ng:graph:weighted", "[weighted]", "Grafo pesato"),
            ("⋥", "SQUARE_ORIGINAL_OF_OR_NOT_EQUAL_TO", "ng:graph:unweighted", "[unweighted]", "Grafo non pesato"),
            
            # Graph algorithms - algoritmi grafi
            ("⋦", "LESS_THAN_BUT_NOT_EQUIVALENT_TO", "ng:graph:dijkstra", "[dijkstra]", "Algoritmo Dijkstra"),
            ("⋧", "GREATER_THAN_BUT_NOT_EQUIVALENT_TO", "ng:graph:bellman_ford", "[bellman]", "Bellman-Ford"),
            ("⋨", "PRECEDES_BUT_NOT_EQUIVALENT_TO", "ng:graph:floyd", "[floyd]", "Floyd-Warshall"),
            ("⋩", "SUCCEEDS_BUT_NOT_EQUIVALENT_TO", "ng:graph:kruskal", "[kruskal]", "Kruskal MST"),
            ("⋪", "NOT_NORMAL_SUBGROUP_OF", "ng:graph:prim", "[prim]", "Prim MST"),
            ("⋫", "DOES_NOT_CONTAIN_AS_NORMAL_SUBGROUP", "ng:graph:topological", "[topo]", "Ordinamento topologico"),
            ("⋬", "NOT_NORMAL_SUBGROUP_OF_OR_EQUAL_TO", "ng:graph:strongly_connected", "[scc]", "Componenti fortemente connesse"),
            ("⋭", "DOES_NOT_CONTAIN_AS_NORMAL_SUBGROUP_OR_EQUAL", "ng:graph:cycle_detection", "[cycle]", "Rilevamento cicli"),
            
            # Graph traversal - attraversamento grafi
            ("⋮", "VERTICAL_ELLIPSIS", "ng:graph:dfs_graph", "[dfs]", "DFS su grafo"),
            ("⋯", "MIDLINE_HORIZONTAL_ELLIPSIS", "ng:graph:bfs_graph", "[bfs]", "BFS su grafo"),
            ("⋰", "UP_RIGHT_DIAGONAL_ELLIPSIS", "ng:graph:path", "[path]", "Percorso"),
            ("⋱", "DOWN_RIGHT_DIAGONAL_ELLIPSIS", "ng:graph:shortest_path", "[shortest]", "Percorso minimo"),
            ("⋲", "ELEMENT_OF_WITH_LONG_HORIZONTAL_STROKE", "ng:graph:reachable", "[reachable]", "Raggiungibile"),
            ("⋳", "ELEMENT_OF_WITH_VERTICAL_BAR_AT_END_OF_HORIZONTAL_STROKE", "ng:graph:connected", "[connected]", "Connesso"),
            ("⋴", "SMALL_ELEMENT_OF_WITH_VERTICAL_BAR_AT_END_OF_HORIZONTAL_STROKE", "ng:graph:bipartite", "[bipartite]", "Bipartito"),
            ("⋵", "ELEMENT_OF_WITH_DOT_ABOVE", "ng:graph:planar", "[planar]", "Planare"),
            
            # Graph properties - proprietà grafi
            ("⋶", "ELEMENT_OF_WITH_OVERBAR", "ng:graph:degree", "[degree]", "Grado vertice"),
            ("⋷", "SMALL_ELEMENT_OF_WITH_OVERBAR", "ng:graph:in_degree", "[in_degree]", "Grado entrante"),
            ("⋸", "ELEMENT_OF_WITH_UNDERBAR", "ng:graph:out_degree", "[out_degree]", "Grado uscente"),
            ("⋹", "ELEMENT_OF_WITH_TWO_HORIZONTAL_STROKES", "ng:graph:adjacency", "[adjacency]", "Adiacenza"),
            ("⋺", "CONTAINS_WITH_LONG_HORIZONTAL_STROKE", "ng:graph:incidence", "[incidence]", "Incidenza"),
            ("⋻", "CONTAINS_WITH_VERTICAL_BAR_AT_END_OF_HORIZONTAL_STROKE", "ng:graph:clique", "[clique]", "Clique"),
            ("⋼", "SMALL_CONTAINS_WITH_VERTICAL_BAR_AT_END_OF_HORIZONTAL_STROKE", "ng:graph:independent_set", "[independent]", "Set indipendente"),
            ("⋽", "CONTAINS_WITH_OVERBAR", "ng:graph:matching", "[matching]", "Matching")
        ]
        
        # 4. ALGORITHMS (32 simboli) - Algoritmi
        algorithm_patterns = [
            # Sorting algorithms - algoritmi ordinamento
            ("⋾", "SMALL_CONTAINS_WITH_OVERBAR", "ng:algo:sort", "[sort]", "Ordinamento"),
            ("⋿", "Z_NOTATION_BAG_MEMBERSHIP", "ng:algo:bubble_sort", "[bubble]", "Bubble sort"),
            ("⌀", "DIAMETER_SIGN", "ng:algo:quick_sort", "[quick]", "Quick sort"),
            ("⌁", "ELECTRIC_ARROW", "ng:algo:merge_sort", "[merge]", "Merge sort"),
            ("⌂", "HOUSE", "ng:algo:heap_sort", "[heap_sort]", "Heap sort"),
            ("⌃", "UP_ARROWHEAD", "ng:algo:insertion_sort", "[insertion]", "Insertion sort"),
            ("⌄", "DOWN_ARROWHEAD", "ng:algo:selection_sort", "[selection]", "Selection sort"),
            ("⌅", "PROJECTIVE", "ng:algo:radix_sort", "[radix]", "Radix sort"),
            
            # Search algorithms - algoritmi ricerca
            ("⌆", "PERSPECTIVE", "ng:algo:linear_search", "[linear]", "Ricerca lineare"),
            ("⌇", "WAVY_LINE", "ng:algo:binary_search", "[binary]", "Ricerca binaria"),
            ("⌈", "LEFT_CEILING", "ng:algo:interpolation_search", "[interpolation]", "Ricerca interpolazione"),
            ("⌉", "RIGHT_CEILING", "ng:algo:exponential_search", "[exponential]", "Ricerca esponenziale"),
            ("⌊", "LEFT_FLOOR", "ng:algo:fibonacci_search", "[fibonacci]", "Ricerca Fibonacci"),
            ("⌋", "RIGHT_FLOOR", "ng:algo:ternary_search", "[ternary]", "Ricerca ternaria"),
            ("⌌", "BOTTOM_RIGHT_CROP", "ng:algo:jump_search", "[jump]", "Jump search"),
            ("⌍", "BOTTOM_LEFT_CROP", "ng:algo:hash_search", "[hash]", "Ricerca hash"),
            
            # Dynamic programming - programmazione dinamica
            ("⌎", "TOP_RIGHT_CROP", "ng:algo:dp", "[dp]", "Programmazione dinamica"),
            ("⌏", "TOP_LEFT_CROP", "ng:algo:memoization", "[memo]", "Memoizzazione"),
            ("⌐", "REVERSED_NOT_SIGN", "ng:algo:tabulation", "[tab]", "Tabulazione"),
            ("⌑", "SQUARE_LOZENGE", "ng:algo:optimal_substructure", "[optimal]", "Sottostruttura ottimale"),
            ("⌒", "ARC", "ng:algo:overlapping_subproblems", "[overlap]", "Sottoproblemi sovrapposti"),
            ("⌓", "SEGMENT", "ng:algo:knapsack", "[knapsack]", "Problema zaino"),
            ("⌔", "SECTOR", "ng:algo:lcs", "[lcs]", "Longest common subsequence"),
            ("⌕", "TELEPHONE_RECORDER", "ng:algo:edit_distance", "[edit]", "Distanza di edit"),
            
            # Greedy algorithms - algoritmi greedy
            ("⌖", "POSITION_INDICATOR", "ng:algo:greedy", "[greedy]", "Algoritmo greedy"),
            ("⌗", "VIEWDATA_SQUARE", "ng:algo:activity_selection", "[activity]", "Selezione attività"),
            ("⌘", "PLACE_OF_INTEREST_SIGN", "ng:algo:fractional_knapsack", "[frac_knapsack]", "Zaino frazionario"),
            ("⌙", "TURNED_NOT_SIGN", "ng:algo:huffman", "[huffman]", "Codifica Huffman"),
            ("⌚", "WATCH", "ng:algo:job_scheduling", "[job]", "Scheduling lavori"),
            ("⌛", "HOURGLASS", "ng:algo:coin_change", "[coin]", "Cambio monete"),
            ("⌜", "TOP_LEFT_CORNER", "ng:algo:minimum_spanning_tree", "[mst]", "Albero ricoprente minimo"),
            ("⌝", "TOP_RIGHT_CORNER", "ng:algo:single_source_shortest_path", "[sssp]", "Percorso minimo sorgente singola")
        ]
        
        # Combina tutti i pattern
        all_patterns = collection_patterns + tree_patterns + graph_patterns + algorithm_patterns
        
        # Converti in formato candidati
        for symbol, unicode_name, ng_code, fallback, description in all_patterns:
            candidates.append({
                "symbol": symbol,
                "unicode_name": unicode_name,
                "ng_code": ng_code,
                "fallback": fallback,
                "description": description,
                "category": "data_structures",
                "batch": 3,
                "tier": "ultra"
            })
        
        return candidates
    
    def validate_candidates(self, candidates: List[Dict]) -> List[Dict]:
        """Valida candidati per qualità e unicità"""
        print(f"🔍 Validazione {len(candidates)} candidati...")
        
        # Simboli già esistenti
        existing_symbols = {s["symbol"] for s in self.current_symbols.get("approved_symbols", [])}
        existing_codes = {s["code"] for s in self.current_symbols.get("approved_symbols", [])}
        
        validated = []
        
        for candidate in candidates:
            symbol = candidate["symbol"]
            ng_code = candidate["ng_code"]
            
            # Skip se già esistente
            if symbol in existing_symbols:
                print(f"   ⚠️ Simbolo già esistente: {symbol}")
                continue
            
            if ng_code in existing_codes:
                print(f"   ⚠️ Codice già esistente: {ng_code}")
                continue
            
            # Validazione Unicode
            try:
                symbol.encode('utf-8')
                unicodedata.name(symbol)  # Deve avere nome Unicode
                if len(symbol) == 1:  # Solo simboli singoli
                    validated.append(candidate)
                else:
                    print(f"   ❌ Simbolo multi-carattere: {symbol}")
            except (UnicodeError, ValueError):
                print(f"   ❌ Simbolo non valido Unicode: {symbol}")
        
        print(f"✅ Validati {len(validated)} candidati")
        return validated
    
    def select_best_candidates(self, candidates: List[Dict]) -> List[Dict]:
        """Seleziona i migliori candidati per il batch"""
        print(f"🎯 Selezione migliori {self.batch_size} candidati...")
        
        # Score candidati
        scored_candidates = []
        
        for candidate in candidates:
            score = self._calculate_candidate_score(candidate)
            candidate["score"] = score
            scored_candidates.append(candidate)
        
        # Ordina per score
        scored_candidates.sort(key=lambda x: x["score"], reverse=True)
        
        # Seleziona top batch_size
        selected = scored_candidates[:self.batch_size]
        
        print(f"✅ Selezionati {len(selected)} candidati migliori")
        return selected
    
    def _calculate_candidate_score(self, candidate: Dict) -> float:
        """Calcola score qualità candidato"""
        score = 1.0
        
        symbol = candidate["symbol"]
        
        # Bonus per simboli matematici (più stabili)
        try:
            name = unicodedata.name(symbol).lower()
            if "mathematical" in name:
                score += 0.3
            if "operator" in name:
                score += 0.2
            if "bracket" in name:
                score += 0.1
        except:
            pass
        
        # Bonus per fallback chiari
        fallback = candidate.get("fallback", "")
        if len(fallback) <= 10:
            score += 0.1
        
        # Bonus per descrizioni specifiche
        description = candidate.get("description", "")
        if any(word in description.lower() for word in ["tree", "graph", "sort", "search"]):
            score += 0.2
        
        return score
    
    def generate_batch_symbols(self) -> List[Dict]:
        """Genera batch completo di simboli"""
        print("🚀 GENERAZIONE BATCH 3 ULTRA - DATA STRUCTURES")
        print("=" * 70)
        
        # 1. Definisci candidati
        candidates = self.data_candidates
        print(f"📋 Candidati definiti: {len(candidates)}")
        
        # 2. Valida candidati
        validated = self.validate_candidates(candidates)
        
        # 3. Seleziona migliori
        selected = self.select_best_candidates(validated)
        
        # 4. Formatta per registry
        formatted = []
        current_count = len(self.current_symbols.get("approved_symbols", []))
        
        for i, candidate in enumerate(selected):
            symbol_id = f"NG{current_count + i + 1:04d}"
            
            formatted_symbol = {
                "id": symbol_id,
                "symbol": candidate["symbol"],
                "code": candidate["ng_code"],
                "fallback": candidate["fallback"],
                "description": candidate["description"],
                "category": candidate["category"],
                "batch": candidate["batch"],
                "tier": candidate["tier"],
                "approved_date": datetime.now().strftime("%Y-%m-%d"),
                "validation_score": candidate.get("score", 1.0),
                "status": "pending_approval"
            }
            
            formatted.append(formatted_symbol)
        
        return formatted
    
    def save_batch_proposal(self, symbols: List[Dict]) -> str:
        """Salva proposta batch per review"""
        output_path = Path("neuroglyph/symbols/ultra_batch3_proposal.json")
        
        proposal = {
            "batch_info": {
                "batch_number": 3,
                "tier": "ultra",
                "theme": self.batch_theme,
                "target_size": self.batch_size,
                "actual_size": len(symbols),
                "generated_date": datetime.now().isoformat()
            },
            "symbols": symbols,
            "summary": {
                "collection_patterns": len([s for s in symbols if "collection" in s["code"]]),
                "tree_patterns": len([s for s in symbols if "tree" in s["code"]]),
                "graph_patterns": len([s for s in symbols if "graph" in s["code"]]),
                "algorithm_patterns": len([s for s in symbols if "algo" in s["code"]])
            }
        }
        
        with open(output_path, 'w', encoding='utf-8') as f:
            json.dump(proposal, f, indent=2, ensure_ascii=False)
        
        print(f"💾 Proposta batch salvata: {output_path}")
        return str(output_path)

def main():
    """Genera terzo batch ULTRA"""
    print("🧠 NEUROGLYPH LLM - Generate ULTRA Batch 3")
    print("🎯 Data Structures Symbols")
    print("=" * 70)
    
    generator = UltraBatch3Generator()
    
    # Genera simboli
    symbols = generator.generate_batch_symbols()
    
    # Salva proposta
    proposal_path = generator.save_batch_proposal(symbols)
    
    # Mostra statistiche
    print(f"\n📊 BATCH 3 GENERATO:")
    print(f"   Simboli generati: {len(symbols)}")
    print(f"   Target batch: {generator.batch_size}")
    print(f"   Tema: {generator.batch_theme}")
    
    # Mostra primi esempi
    print(f"\n🎯 PRIMI 10 SIMBOLI:")
    for i, symbol in enumerate(symbols[:10]):
        print(f"   {i+1}. {symbol['symbol']} ({symbol['code']}) - {symbol['description']}")
    
    print(f"\n✅ Batch 3 ULTRA pronto per review!")
    print(f"📁 File: {proposal_path}")
    
    return symbols

if __name__ == "__main__":
    main()
