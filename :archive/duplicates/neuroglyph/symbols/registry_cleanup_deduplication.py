#!/usr/bin/env python3
"""
NEUROGLYPH REGISTRY CLEANUP & DEDUPLICATION
Rimuove duplicati e mantiene solo simboli unici e significativi
"""

import json
import sys
from datetime import datetime
from collections import defaultdict
from pathlib import Path

def cleanup_registry():
    """Pulisce registry rimuovendo duplicati e simboli di bassa qualità."""
    
    # Carica registry
    registry_path = "neuroglyph/core/locked_registry_godmode_v9.json"
    with open(registry_path, 'r', encoding='utf-8') as f:
        registry = json.load(f)
    
    symbols = registry.get('approved_symbols', [])
    print(f"📊 Registry originale: {len(symbols)} simboli")
    
    # FASE 1: Rimuovi duplicati per codice
    print("\n🔧 FASE 1: Rimozione duplicati per codice...")
    
    unique_by_code = {}
    for symbol in symbols:
        code = symbol.get('code', '')
        if code not in unique_by_code:
            unique_by_code[code] = symbol
        else:
            # Mantieni quello con score più alto
            existing = unique_by_code[code]
            if symbol.get('score', 0) > existing.get('score', 0):
                unique_by_code[code] = symbol
    
    symbols_after_code = list(unique_by_code.values())
    print(f"✅ Dopo rimozione duplicati codice: {len(symbols_after_code)} simboli (-{len(symbols) - len(symbols_after_code)})")
    
    # FASE 2: Rimuovi duplicati per simbolo Unicode
    print("\n🔧 FASE 2: Rimozione duplicati per simbolo Unicode...")
    
    unique_by_symbol = {}
    for symbol in symbols_after_code:
        unicode_char = symbol.get('symbol', '')
        if unicode_char not in unique_by_symbol:
            unique_by_symbol[unicode_char] = symbol
        else:
            # Mantieni quello con score più alto
            existing = unique_by_symbol[unicode_char]
            if symbol.get('score', 0) > existing.get('score', 0):
                unique_by_symbol[unicode_char] = symbol
    
    symbols_after_unicode = list(unique_by_symbol.values())
    print(f"✅ Dopo rimozione duplicati Unicode: {len(symbols_after_unicode)} simboli (-{len(symbols_after_code) - len(symbols_after_unicode)})")
    
    # FASE 3: Rimuovi duplicati per fallback
    print("\n🔧 FASE 3: Rimozione duplicati per fallback...")
    
    unique_by_fallback = {}
    for symbol in symbols_after_unicode:
        fallback = symbol.get('fallback', '')
        if fallback not in unique_by_fallback:
            unique_by_fallback[fallback] = symbol
        else:
            # Mantieni quello con score più alto
            existing = unique_by_fallback[fallback]
            if symbol.get('score', 0) > existing.get('score', 0):
                unique_by_fallback[fallback] = symbol
    
    symbols_after_fallback = list(unique_by_fallback.values())
    print(f"✅ Dopo rimozione duplicati fallback: {len(symbols_after_fallback)} simboli (-{len(symbols_after_unicode) - len(symbols_after_fallback)})")
    
    # FASE 4: Filtra per qualità
    print("\n🔧 FASE 4: Filtro qualità...")
    
    quality_symbols = []
    for symbol in symbols_after_fallback:
        # Criteri di qualità
        score = symbol.get('score', 0)
        fallback = symbol.get('fallback', '')
        code = symbol.get('code', '')
        
        # Mantieni solo simboli di alta qualità
        if (score >= 95.0 and 
            len(fallback) <= 8 and 
            code.startswith('ng:') and
            len(code.split(':')) >= 3):
            quality_symbols.append(symbol)
    
    print(f"✅ Dopo filtro qualità: {len(quality_symbols)} simboli (-{len(symbols_after_fallback) - len(quality_symbols)})")
    
    # FASE 5: Mantieni distribuzione bilanciata per categoria
    print("\n🔧 FASE 5: Bilanciamento categorie...")
    
    # Raggruppa per categoria
    by_category = defaultdict(list)
    for symbol in quality_symbols:
        category = symbol.get('category', 'unknown')
        by_category[category].append(symbol)
    
    # Limita simboli per categoria per evitare sbilanciamenti
    balanced_symbols = []
    max_per_category = 150  # Limite ragionevole per categoria
    
    for category, cat_symbols in by_category.items():
        # Ordina per score decrescente
        cat_symbols.sort(key=lambda x: x.get('score', 0), reverse=True)
        
        # Prendi i migliori
        selected = cat_symbols[:max_per_category]
        balanced_symbols.extend(selected)
        
        print(f"   {category}: {len(selected)}/{len(cat_symbols)} simboli")
    
    print(f"✅ Dopo bilanciamento: {len(balanced_symbols)} simboli (-{len(quality_symbols) - len(balanced_symbols)})")
    
    # FASE 6: Verifica copertura cognitiva
    print("\n🔧 FASE 6: Verifica copertura cognitiva...")
    
    cognitive_domains = [
        'neural_architectures', 'quantum_computing', 'symbolic_ai', 'meta_programming',
        'reasoning_patterns', 'cognitive_architectures', 'knowledge_representation',
        'semantic_understanding', 'contextual_reasoning', 'causal_reasoning',
        'analogical_reasoning', 'creative_thinking', 'problem_solving', 'learning_algorithms',
        'consciousness_models', 'self_reflection', 'goal_oriented_behavior',
        'emotional_intelligence', 'social_cognition', 'multimodal_reasoning',
        'temporal_cognition', 'language_understanding', 'memory_architectures',
        'attention_systems', 'decision_making', 'adaptive_intelligence'
    ]
    
    cognitive_symbols = len([s for s in balanced_symbols 
                           if any(domain in s.get('code', '') for domain in cognitive_domains)])
    
    cognitive_coverage = cognitive_symbols / len(balanced_symbols) * 100
    
    print(f"🧠 Simboli cognitivi: {cognitive_symbols}/{len(balanced_symbols)} ({cognitive_coverage:.1f}%)")
    
    # Aggiorna registry
    registry['approved_symbols'] = balanced_symbols
    
    # Aggiorna statistiche
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    registry['stats']['cleanup_deduplication'] = timestamp
    registry['stats']['symbols_before_cleanup'] = len(symbols)
    registry['stats']['symbols_after_cleanup'] = len(balanced_symbols)
    registry['stats']['symbols_removed'] = len(symbols) - len(balanced_symbols)
    registry['stats']['cleanup_cognitive_coverage'] = cognitive_coverage
    registry['stats']['cleanup_cognitive_symbols'] = cognitive_symbols
    registry['stats']['registry_cleaned'] = True
    registry['stats']['registry_quality'] = "HIGH"
    
    # Backup
    backup_path = f"neuroglyph/core/locked_registry_godmode_v9.backup_before_cleanup_{timestamp}.json"
    with open(backup_path, 'w', encoding='utf-8') as f:
        json.dump({'approved_symbols': symbols, 'stats': registry.get('stats', {})}, f, indent=2, ensure_ascii=False)
    print(f"💾 Backup pre-cleanup salvato: {backup_path}")
    
    # Salva registry pulito
    with open(registry_path, 'w', encoding='utf-8') as f:
        json.dump(registry, f, indent=2, ensure_ascii=False)
    
    print(f"\n🎉 CLEANUP COMPLETATO!")
    print(f"📊 Simboli originali: {len(symbols)}")
    print(f"📊 Simboli finali: {len(balanced_symbols)}")
    print(f"📊 Simboli rimossi: {len(symbols) - len(balanced_symbols)} ({(len(symbols) - len(balanced_symbols))/len(symbols)*100:.1f}%)")
    print(f"🧠 Copertura cognitiva: {cognitive_coverage:.1f}%")
    print(f"✅ Registry pulito e ottimizzato salvato")
    
    return len(balanced_symbols), cognitive_coverage

if __name__ == "__main__":
    print("🧹 NEUROGLYPH REGISTRY CLEANUP & DEDUPLICATION")
    print("=" * 55)
    
    final_count, cognitive_coverage = cleanup_registry()
    
    if cognitive_coverage >= 40.0:
        print(f"\n✅ SUCCESSO: Registry pulito con {final_count} simboli unici")
        print(f"🧠 Copertura cognitiva mantenuta: {cognitive_coverage:.1f}%")
    else:
        print(f"\n⚠️  ATTENZIONE: Copertura cognitiva ridotta a {cognitive_coverage:.1f}%")
        print("Potrebbe essere necessario rigenerare alcuni domini cognitivi")
