#!/usr/bin/env python3
"""
NEUROGLYPH LLM - Generate ULTRA Batch 1
======================================

Genera primo batch ULTRA: 128 simboli per Async + Concurrency
Focus: async/await, threading, locks, futures, promises
"""

import json
import unicodedata
from pathlib import Path
from typing import Dict, List, Tuple
from datetime import datetime

class UltraBatch1Generator:
    """Generatore primo batch ULTRA - Async + Concurrency"""
    
    def __init__(self):
        self.registry_path = Path("neuroglyph/core/symbols_registry.json")
        self.current_symbols = self._load_current_registry()
        self.batch_size = 128
        self.batch_theme = "async_concurrency"
        
        # Simboli candidati per Async + Concurrency
        self.async_candidates = self._define_async_candidates()
        
    def _load_current_registry(self) -> Dict:
        """Carica registry attuale"""
        if self.registry_path.exists():
            with open(self.registry_path, 'r', encoding='utf-8') as f:
                return json.load(f)
        return {"approved_symbols": []}
    
    def _define_async_candidates(self) -> List[Dict]:
        """Definisce candidati simboli per async/concurrency"""
        candidates = []
        
        # 1. ASYNC/AWAIT PATTERNS (32 simboli)
        async_patterns = [
            # Core async symbols
            ("⚡", "ASYNC_FUNCTION", "ng:async:function", "[async]", "Funzione asincrona"),
            ("⏳", "AWAIT_OPERATION", "ng:async:await", "[await]", "Operazione await"),
            ("⏸", "ASYNC_PAUSE", "ng:async:pause", "[pause]", "Pausa asincrona"),
            ("⏯", "ASYNC_RESUME", "ng:async:resume", "[resume]", "Ripresa asincrona"),
            ("⏹", "ASYNC_STOP", "ng:async:stop", "[stop]", "Stop asincrono"),
            ("⏱", "ASYNC_TIMER", "ng:async:timer", "[timer]", "Timer asincrono"),
            ("⏰", "ASYNC_ALARM", "ng:async:alarm", "[alarm]", "Allarme asincrono"),
            ("⌛", "ASYNC_HOURGLASS", "ng:async:hourglass", "[wait]", "Attesa asincrona"),
            
            # Promise/Future symbols
            ("🔮", "FUTURE_RESULT", "ng:async:future", "[future]", "Risultato futuro"),
            ("📋", "PROMISE_PENDING", "ng:async:promise", "[promise]", "Promessa pendente"),
            ("✅", "PROMISE_RESOLVED", "ng:async:resolved", "[resolved]", "Promessa risolta"),
            ("❌", "PROMISE_REJECTED", "ng:async:rejected", "[rejected]", "Promessa rigettata"),
            ("🔄", "PROMISE_CHAIN", "ng:async:chain", "[chain]", "Catena promesse"),
            ("🎯", "ASYNC_TARGET", "ng:async:target", "[target]", "Target asincrono"),
            
            # Async flow control
            ("🌊", "ASYNC_FLOW", "ng:async:flow", "[flow]", "Flusso asincrono"),
            ("🔀", "ASYNC_BRANCH", "ng:async:branch", "[branch]", "Ramo asincrono"),
            ("🔁", "ASYNC_LOOP", "ng:async:loop", "[loop]", "Loop asincrono"),
            ("🔃", "ASYNC_CYCLE", "ng:async:cycle", "[cycle]", "Ciclo asincrono"),
            ("⤴", "ASYNC_RETURN", "ng:async:return", "[return]", "Return asincrono"),
            ("⤵", "ASYNC_YIELD", "ng:async:yield", "[yield]", "Yield asincrono"),
            
            # Async generators
            ("🏭", "ASYNC_GENERATOR", "ng:async:generator", "[gen]", "Generatore asincrono"),
            ("⚙", "ASYNC_ITERATOR", "ng:async:iterator", "[iter]", "Iteratore asincrono"),
            ("🔧", "ASYNC_NEXT", "ng:async:next", "[next]", "Next asincrono"),
            ("🛠", "ASYNC_SEND", "ng:async:send", "[send]", "Send asincrono"),
            ("🔩", "ASYNC_THROW", "ng:async:throw", "[throw]", "Throw asincrono"),
            
            # Context managers
            ("🚪", "ASYNC_CONTEXT", "ng:async:context", "[context]", "Contesto asincrono"),
            ("🔐", "ASYNC_ENTER", "ng:async:enter", "[enter]", "Enter asincrono"),
            ("🔓", "ASYNC_EXIT", "ng:async:exit", "[exit]", "Exit asincrono"),
            ("🛡", "ASYNC_GUARD", "ng:async:guard", "[guard]", "Guard asincrono"),
            ("🎭", "ASYNC_WITH", "ng:async:with", "[with]", "With asincrono"),
            
            # Error handling
            ("⚠", "ASYNC_WARNING", "ng:async:warning", "[warn]", "Warning asincrono"),
            ("🚨", "ASYNC_ERROR", "ng:async:error", "[error]", "Errore asincrono"),
            ("🛟", "ASYNC_RESCUE", "ng:async:rescue", "[rescue]", "Rescue asincrono")
        ]
        
        # 2. THREADING/MULTIPROCESSING (32 simboli)
        threading_patterns = [
            # Core threading
            ("🧵", "THREAD", "ng:thread:thread", "[thread]", "Thread"),
            ("🎭", "THREAD_POOL", "ng:thread:pool", "[pool]", "Pool di thread"),
            ("👥", "THREAD_GROUP", "ng:thread:group", "[group]", "Gruppo thread"),
            ("🔀", "THREAD_SPAWN", "ng:thread:spawn", "[spawn]", "Spawn thread"),
            ("🔚", "THREAD_JOIN", "ng:thread:join", "[join]", "Join thread"),
            ("💀", "THREAD_KILL", "ng:thread:kill", "[kill]", "Kill thread"),
            ("😴", "THREAD_SLEEP", "ng:thread:sleep", "[sleep]", "Sleep thread"),
            ("⏸", "THREAD_SUSPEND", "ng:thread:suspend", "[suspend]", "Suspend thread"),
            
            # Process symbols
            ("⚙", "PROCESS", "ng:process:process", "[proc]", "Processo"),
            ("🏭", "PROCESS_POOL", "ng:process:pool", "[ppool]", "Pool processi"),
            ("🍴", "PROCESS_FORK", "ng:process:fork", "[fork]", "Fork processo"),
            ("🔄", "PROCESS_EXEC", "ng:process:exec", "[exec]", "Exec processo"),
            ("📤", "PROCESS_SEND", "ng:process:send", "[send]", "Send processo"),
            ("📥", "PROCESS_RECV", "ng:process:recv", "[recv]", "Recv processo"),
            ("🚇", "PROCESS_PIPE", "ng:process:pipe", "[pipe]", "Pipe processo"),
            ("📡", "PROCESS_SIGNAL", "ng:process:signal", "[signal]", "Signal processo"),
            
            # Worker patterns
            ("👷", "WORKER", "ng:worker:worker", "[worker]", "Worker"),
            ("📋", "TASK_QUEUE", "ng:worker:queue", "[queue]", "Coda task"),
            ("⚡", "TASK_EXECUTOR", "ng:worker:executor", "[exec]", "Esecutore task"),
            ("🎯", "TASK_TARGET", "ng:worker:target", "[target]", "Target task"),
            ("📊", "TASK_RESULT", "ng:worker:result", "[result]", "Risultato task"),
            ("⏱", "TASK_TIMEOUT", "ng:worker:timeout", "[timeout]", "Timeout task"),
            ("🔄", "TASK_RETRY", "ng:worker:retry", "[retry]", "Retry task"),
            ("❌", "TASK_CANCEL", "ng:worker:cancel", "[cancel]", "Cancel task"),
            
            # Scheduling
            ("📅", "SCHEDULER", "ng:schedule:scheduler", "[sched]", "Scheduler"),
            ("⏰", "SCHEDULE_TIME", "ng:schedule:time", "[time]", "Tempo schedule"),
            ("🔔", "SCHEDULE_TRIGGER", "ng:schedule:trigger", "[trigger]", "Trigger schedule"),
            ("📆", "SCHEDULE_CRON", "ng:schedule:cron", "[cron]", "Cron schedule"),
            ("⚡", "SCHEDULE_IMMEDIATE", "ng:schedule:immediate", "[now]", "Schedule immediato"),
            ("⏳", "SCHEDULE_DELAYED", "ng:schedule:delayed", "[delay]", "Schedule ritardato"),
            ("🔁", "SCHEDULE_REPEAT", "ng:schedule:repeat", "[repeat]", "Schedule ripetuto"),
            ("🛑", "SCHEDULE_STOP", "ng:schedule:stop", "[stop]", "Stop schedule")
        ]
        
        # 3. LOCKS/SYNCHRONIZATION (32 simboli)
        sync_patterns = [
            # Basic locks
            ("🔒", "LOCK", "ng:sync:lock", "[lock]", "Lock"),
            ("🔓", "UNLOCK", "ng:sync:unlock", "[unlock]", "Unlock"),
            ("🗝", "LOCK_KEY", "ng:sync:key", "[key]", "Chiave lock"),
            ("🚪", "LOCK_DOOR", "ng:sync:door", "[door]", "Porta lock"),
            ("🛡", "LOCK_GUARD", "ng:sync:guard", "[guard]", "Guard lock"),
            ("⏱", "LOCK_TIMEOUT", "ng:sync:timeout", "[timeout]", "Timeout lock"),
            ("🔄", "LOCK_REENTRANT", "ng:sync:reentrant", "[reent]", "Lock rientrante"),
            ("📖", "LOCK_READ", "ng:sync:read", "[read]", "Read lock"),
            
            # Advanced locks
            ("📝", "LOCK_WRITE", "ng:sync:write", "[write]", "Write lock"),
            ("🎭", "LOCK_SHARED", "ng:sync:shared", "[shared]", "Lock condiviso"),
            ("👑", "LOCK_EXCLUSIVE", "ng:sync:exclusive", "[excl]", "Lock esclusivo"),
            ("🔀", "LOCK_UPGRADE", "ng:sync:upgrade", "[upgrade]", "Upgrade lock"),
            ("🔽", "LOCK_DOWNGRADE", "ng:sync:downgrade", "[downgrade]", "Downgrade lock"),
            ("⚖", "LOCK_FAIR", "ng:sync:fair", "[fair]", "Lock equo"),
            ("🎲", "LOCK_UNFAIR", "ng:sync:unfair", "[unfair]", "Lock non equo"),
            ("🔢", "LOCK_COUNT", "ng:sync:count", "[count]", "Count lock"),
            
            # Semaphores
            ("🚦", "SEMAPHORE", "ng:sync:semaphore", "[sem]", "Semaforo"),
            ("🔢", "SEM_COUNT", "ng:sync:sem_count", "[count]", "Count semaforo"),
            ("⬆", "SEM_ACQUIRE", "ng:sync:acquire", "[acquire]", "Acquire semaforo"),
            ("⬇", "SEM_RELEASE", "ng:sync:release", "[release]", "Release semaforo"),
            ("🎯", "SEM_PERMIT", "ng:sync:permit", "[permit]", "Permit semaforo"),
            ("⏳", "SEM_WAIT", "ng:sync:wait", "[wait]", "Wait semaforo"),
            ("📊", "SEM_AVAILABLE", "ng:sync:available", "[avail]", "Available semaforo"),
            ("🚫", "SEM_BLOCKED", "ng:sync:blocked", "[blocked]", "Blocked semaforo"),
            
            # Conditions & Events
            ("🔔", "CONDITION", "ng:sync:condition", "[cond]", "Condizione"),
            ("⏰", "EVENT", "ng:sync:event", "[event]", "Evento"),
            ("🎺", "EVENT_SET", "ng:sync:set", "[set]", "Set evento"),
            ("🔇", "EVENT_CLEAR", "ng:sync:clear", "[clear]", "Clear evento"),
            ("⏳", "EVENT_WAIT", "ng:sync:wait_event", "[wait]", "Wait evento"),
            ("📢", "EVENT_NOTIFY", "ng:sync:notify", "[notify]", "Notify evento"),
            ("📣", "EVENT_BROADCAST", "ng:sync:broadcast", "[broadcast]", "Broadcast evento"),
            ("🎭", "EVENT_ONCE", "ng:sync:once", "[once]", "Evento singolo")
        ]
        
        # 4. FUTURES/PROMISES (32 simboli)
        future_patterns = [
            # Future states
            ("🔮", "FUTURE", "ng:future:future", "[future]", "Future"),
            ("⏳", "FUTURE_PENDING", "ng:future:pending", "[pending]", "Future pendente"),
            ("✅", "FUTURE_DONE", "ng:future:done", "[done]", "Future completato"),
            ("❌", "FUTURE_FAILED", "ng:future:failed", "[failed]", "Future fallito"),
            ("🚫", "FUTURE_CANCELLED", "ng:future:cancelled", "[cancelled]", "Future cancellato"),
            ("⏱", "FUTURE_TIMEOUT", "ng:future:timeout", "[timeout]", "Future timeout"),
            ("🔄", "FUTURE_RUNNING", "ng:future:running", "[running]", "Future in esecuzione"),
            ("📊", "FUTURE_RESULT", "ng:future:result", "[result]", "Risultato future"),
            
            # Future operations
            ("🎯", "FUTURE_GET", "ng:future:get", "[get]", "Get future"),
            ("⏰", "FUTURE_WAIT", "ng:future:wait", "[wait]", "Wait future"),
            ("🔗", "FUTURE_THEN", "ng:future:then", "[then]", "Then future"),
            ("🚨", "FUTURE_CATCH", "ng:future:catch", "[catch]", "Catch future"),
            ("🏁", "FUTURE_FINALLY", "ng:future:finally", "[finally]", "Finally future"),
            ("🔀", "FUTURE_MAP", "ng:future:map", "[map]", "Map future"),
            ("🎭", "FUTURE_FLATMAP", "ng:future:flatmap", "[flatmap]", "FlatMap future"),
            ("🔄", "FUTURE_RETRY", "ng:future:retry", "[retry]", "Retry future"),
            
            # Combinators
            ("🤝", "FUTURE_ALL", "ng:future:all", "[all]", "All futures"),
            ("🏃", "FUTURE_ANY", "ng:future:any", "[any]", "Any future"),
            ("🏆", "FUTURE_RACE", "ng:future:race", "[race]", "Race futures"),
            ("⚡", "FUTURE_FIRST", "ng:future:first", "[first]", "First future"),
            ("🔗", "FUTURE_CHAIN", "ng:future:chain", "[chain]", "Chain futures"),
            ("🌊", "FUTURE_SEQUENCE", "ng:future:sequence", "[seq]", "Sequence futures"),
            ("🎯", "FUTURE_SELECT", "ng:future:select", "[select]", "Select future"),
            ("🔀", "FUTURE_MERGE", "ng:future:merge", "[merge]", "Merge futures"),
            
            # Completion
            ("✨", "COMPLETION", "ng:completion:completion", "[comp]", "Completion"),
            ("🎉", "COMPLETION_SUCCESS", "ng:completion:success", "[success]", "Completion successo"),
            ("💥", "COMPLETION_ERROR", "ng:completion:error", "[error]", "Completion errore"),
            ("📝", "COMPLETION_CALLBACK", "ng:completion:callback", "[callback]", "Callback completion"),
            ("🔔", "COMPLETION_HANDLER", "ng:completion:handler", "[handler]", "Handler completion"),
            ("⚡", "COMPLETION_IMMEDIATE", "ng:completion:immediate", "[immediate]", "Completion immediato"),
            ("⏳", "COMPLETION_DEFERRED", "ng:completion:deferred", "[deferred]", "Completion differito"),
            ("🎭", "COMPLETION_STAGE", "ng:completion:stage", "[stage]", "Stage completion")
        ]
        
        # Combina tutti i pattern
        all_patterns = async_patterns + threading_patterns + sync_patterns + future_patterns
        
        # Converti in formato candidati
        for symbol, name, ng_code, fallback, description in all_patterns:
            candidates.append({
                "symbol": symbol,
                "unicode_name": name,
                "ng_code": ng_code,
                "fallback": fallback,
                "description": description,
                "category": "async_concurrency",
                "batch": 1,
                "tier": "ultra"
            })
        
        return candidates
    
    def validate_candidates(self, candidates: List[Dict]) -> List[Dict]:
        """Valida candidati per qualità e unicità"""
        print(f"🔍 Validazione {len(candidates)} candidati...")
        
        # Simboli già esistenti
        existing_symbols = {s["symbol"] for s in self.current_symbols.get("approved_symbols", [])}
        existing_codes = {s["code"] for s in self.current_symbols.get("approved_symbols", [])}
        
        validated = []
        
        for candidate in candidates:
            symbol = candidate["symbol"]
            ng_code = candidate["ng_code"]
            
            # Skip se già esistente
            if symbol in existing_symbols:
                print(f"   ⚠️ Simbolo già esistente: {symbol}")
                continue
            
            if ng_code in existing_codes:
                print(f"   ⚠️ Codice già esistente: {ng_code}")
                continue
            
            # Validazione Unicode
            try:
                symbol.encode('utf-8')
                # Per emoji, potrebbero essere multi-byte
                if len(symbol) <= 2:  # Accetta emoji
                    validated.append(candidate)
                else:
                    print(f"   ❌ Simbolo troppo lungo: {symbol}")
            except UnicodeError:
                print(f"   ❌ Simbolo non valido Unicode: {symbol}")
        
        print(f"✅ Validati {len(validated)} candidati")
        return validated
    
    def select_best_candidates(self, candidates: List[Dict]) -> List[Dict]:
        """Seleziona i migliori candidati per il batch"""
        print(f"🎯 Selezione migliori {self.batch_size} candidati...")
        
        # Score candidati
        scored_candidates = []
        
        for candidate in candidates:
            score = self._calculate_candidate_score(candidate)
            candidate["score"] = score
            scored_candidates.append(candidate)
        
        # Ordina per score
        scored_candidates.sort(key=lambda x: x["score"], reverse=True)
        
        # Seleziona top batch_size
        selected = scored_candidates[:self.batch_size]
        
        print(f"✅ Selezionati {len(selected)} candidati migliori")
        return selected
    
    def _calculate_candidate_score(self, candidate: Dict) -> float:
        """Calcola score qualità candidato"""
        score = 1.0
        
        symbol = candidate["symbol"]
        
        # Bonus per simboli semplici
        if len(symbol) == 1:
            score += 0.2
        
        # Bonus per simboli ASCII-friendly
        try:
            symbol.encode('ascii')
            score += 0.1
        except UnicodeEncodeError:
            pass
        
        # Bonus per fallback chiari
        fallback = candidate.get("fallback", "")
        if len(fallback) <= 8:
            score += 0.1
        
        # Bonus per descrizioni chiare
        description = candidate.get("description", "")
        if len(description) > 5:
            score += 0.1
        
        return score
    
    def generate_batch_symbols(self) -> List[Dict]:
        """Genera batch completo di simboli"""
        print("🚀 GENERAZIONE BATCH 1 ULTRA - ASYNC + CONCURRENCY")
        print("=" * 70)
        
        # 1. Definisci candidati
        candidates = self.async_candidates
        print(f"📋 Candidati definiti: {len(candidates)}")
        
        # 2. Valida candidati
        validated = self.validate_candidates(candidates)
        
        # 3. Seleziona migliori
        selected = self.select_best_candidates(validated)
        
        # 4. Formatta per registry
        formatted = []
        current_count = len(self.current_symbols.get("approved_symbols", []))
        
        for i, candidate in enumerate(selected):
            symbol_id = f"NG{current_count + i + 1:04d}"
            
            formatted_symbol = {
                "id": symbol_id,
                "symbol": candidate["symbol"],
                "code": candidate["ng_code"],
                "fallback": candidate["fallback"],
                "description": candidate["description"],
                "category": candidate["category"],
                "batch": candidate["batch"],
                "tier": candidate["tier"],
                "approved_date": datetime.now().strftime("%Y-%m-%d"),
                "validation_score": candidate.get("score", 1.0),
                "status": "pending_approval"
            }
            
            formatted.append(formatted_symbol)
        
        return formatted
    
    def save_batch_proposal(self, symbols: List[Dict]) -> str:
        """Salva proposta batch per review"""
        output_path = Path("neuroglyph/symbols/ultra_batch1_proposal.json")
        
        proposal = {
            "batch_info": {
                "batch_number": 1,
                "tier": "ultra",
                "theme": self.batch_theme,
                "target_size": self.batch_size,
                "actual_size": len(symbols),
                "generated_date": datetime.now().isoformat()
            },
            "symbols": symbols,
            "summary": {
                "async_patterns": len([s for s in symbols if "async" in s["code"]]),
                "thread_patterns": len([s for s in symbols if "thread" in s["code"] or "process" in s["code"]]),
                "sync_patterns": len([s for s in symbols if "sync" in s["code"]]),
                "future_patterns": len([s for s in symbols if "future" in s["code"] or "completion" in s["code"]])
            }
        }
        
        with open(output_path, 'w', encoding='utf-8') as f:
            json.dump(proposal, f, indent=2, ensure_ascii=False)
        
        print(f"💾 Proposta batch salvata: {output_path}")
        return str(output_path)

def main():
    """Genera primo batch ULTRA"""
    print("🧠 NEUROGLYPH LLM - Generate ULTRA Batch 1")
    print("🎯 Async + Concurrency Symbols")
    print("=" * 70)
    
    generator = UltraBatch1Generator()
    
    # Genera simboli
    symbols = generator.generate_batch_symbols()
    
    # Salva proposta
    proposal_path = generator.save_batch_proposal(symbols)
    
    # Mostra statistiche
    print(f"\n📊 BATCH 1 GENERATO:")
    print(f"   Simboli generati: {len(symbols)}")
    print(f"   Target batch: {generator.batch_size}")
    print(f"   Tema: {generator.batch_theme}")
    
    # Mostra primi esempi
    print(f"\n🎯 PRIMI 10 SIMBOLI:")
    for i, symbol in enumerate(symbols[:10]):
        print(f"   {i+1}. {symbol['symbol']} ({symbol['code']}) - {symbol['description']}")
    
    print(f"\n✅ Batch 1 ULTRA pronto per review!")
    print(f"📁 File: {proposal_path}")
    
    return symbols

if __name__ == "__main__":
    main()
