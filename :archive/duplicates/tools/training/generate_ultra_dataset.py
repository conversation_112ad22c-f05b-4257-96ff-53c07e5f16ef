#!/usr/bin/env python3
"""
NEUROGLYPH ULTRA DATASET GENERATOR
Script rapido per generare dataset di training avanzato
"""

import sys
import os
from pathlib import Path

# Aggiungi path per import
sys.path.append(str(Path(__file__).parent.parent.parent))

from neuroglyph.training.advanced_dataset_generator import AdvancedDatasetGenerator, DatasetQualityValidator
import argparse
import json

def main():
    """Script principale per generazione dataset."""
    parser = argparse.ArgumentParser(description="Generate NEUROGLYPH ULTRA Dataset")
    parser.add_argument("--size", type=int, default=10000, help="Target dataset size")
    parser.add_argument("--output-dir", type=str, default="neuroglyph/training/colab_package", 
                       help="Output directory")
    parser.add_argument("--registry", type=str, default="neuroglyph/core/locked_registry_godmode_v9.json",
                       help="Symbols registry path")
    parser.add_argument("--validate-only", action="store_true", 
                       help="Only validate existing dataset")
    parser.add_argument("--quick", action="store_true", 
                       help="Quick generation (smaller dataset for testing)")
    
    args = parser.parse_args()
    
    print("🧠 NEUROGLYPH ULTRA Dataset Generator")
    print("=" * 50)
    
    if args.quick:
        args.size = 1000
        print("🚀 Quick mode: generating 1000 examples")
    
    # Verifica registry simboli
    if not os.path.exists(args.registry):
        print(f"❌ Registry simboli non trovato: {args.registry}")
        print("💡 Assicurati che il registry sia presente")
        return 1
    
    # Inizializza generatore
    try:
        generator = AdvancedDatasetGenerator(args.registry)
        print(f"✅ Generatore inizializzato con {len(generator.symbols_registry)} simboli")
    except Exception as e:
        print(f"❌ Errore inizializzazione generatore: {e}")
        return 1
    
    # Modalità validazione
    if args.validate_only:
        return validate_existing_dataset(args.output_dir, generator.symbols_registry)
    
    # Genera dataset
    try:
        print(f"🚀 Generazione {args.size} esempi...")
        examples = generator.generate_complete_dataset(target_size=args.size)
        
        if not examples:
            print("❌ Nessun esempio generato")
            return 1
        
        print(f"✅ Generati {len(examples)} esempi")
        
        # Validazione qualità
        validator = DatasetQualityValidator(generator.symbols_registry)
        validation_results = validator.validate_dataset(examples)
        
        if not validation_results['validation_passed']:
            print("⚠️ Dataset non supera i criteri di qualità")
            if not args.quick:
                response = input("Continuare comunque? (y/N): ")
                if response.lower() != 'y':
                    print("❌ Generazione annullata")
                    return 1
        
        # Salva dataset
        output_dir = Path(args.output_dir)
        output_dir.mkdir(parents=True, exist_ok=True)
        
        # Split dataset
        import random
        random.shuffle(examples)
        
        train_size = int(len(examples) * 0.8)
        val_size = int(len(examples) * 0.15)
        
        train_examples = examples[:train_size]
        val_examples = examples[train_size:train_size + val_size]
        test_examples = examples[train_size + val_size:]
        
        # Salva file
        version_suffix = "_v2" if not args.quick else "_quick"
        
        train_path = generator.save_dataset_unsloth_format(
            train_examples, 
            str(output_dir / f"neuroglyph_training_unsloth_ULTRA{version_suffix}.jsonl.gz")
        )
        
        val_path = generator.save_dataset_unsloth_format(
            val_examples,
            str(output_dir / f"neuroglyph_validation_unsloth_ULTRA{version_suffix}.jsonl.gz")
        )
        
        test_path = generator.save_dataset_unsloth_format(
            test_examples,
            str(output_dir / f"neuroglyph_test_unsloth_ULTRA{version_suffix}.jsonl.gz")
        )
        
        # Salva report
        report_path = output_dir / f"dataset_validation_report{version_suffix}.json"
        with open(report_path, 'w', encoding='utf-8') as f:
            json.dump(validation_results, f, indent=2, ensure_ascii=False)
        
        # Summary
        print(f"\n🎉 Dataset NEUROGLYPH ULTRA generato con successo!")
        print(f"📁 Output directory: {output_dir}")
        print(f"📊 Training: {len(train_examples)} esempi")
        print(f"📊 Validation: {len(val_examples)} esempi")
        print(f"📊 Test: {len(test_examples)} esempi")
        print(f"📋 Report: {report_path}")
        
        if validation_results['validation_passed']:
            print("✅ Qualità: PASSED - Ready for training!")
        else:
            print("⚠️ Qualità: ISSUES - Review report for details")
        
        return 0
        
    except Exception as e:
        print(f"❌ Errore durante generazione: {e}")
        import traceback
        traceback.print_exc()
        return 1

def validate_existing_dataset(output_dir: str, symbols_registry: dict) -> int:
    """Valida dataset esistente."""
    print("🔍 Modalità validazione dataset esistente")
    
    # Cerca file dataset
    output_path = Path(output_dir)
    dataset_files = list(output_path.glob("neuroglyph_*_unsloth_ULTRA*.jsonl.gz"))
    
    if not dataset_files:
        print(f"❌ Nessun dataset trovato in {output_dir}")
        return 1
    
    print(f"📁 Trovati {len(dataset_files)} file dataset")
    
    # Carica e valida ogni file
    validator = DatasetQualityValidator(symbols_registry)
    
    for dataset_file in dataset_files:
        print(f"\n🔍 Validazione: {dataset_file.name}")
        
        try:
            # Carica esempi (implementazione semplificata)
            import gzip
            import json
            
            examples = []
            with gzip.open(dataset_file, 'rt', encoding='utf-8') as f:
                for line in f:
                    data = json.loads(line)
                    # Conversione semplificata per validazione
                    example_data = {
                        'validation_symbols': [],  # Estrai da text
                        'quality_score': data.get('quality_score', 0.8),
                        'domain': data.get('domain', 'unknown'),
                        'curriculum_level': data.get('curriculum_level', 0)
                    }
                    examples.append(type('Example', (), example_data))
            
            print(f"📊 Caricati {len(examples)} esempi")
            
            # Validazione semplificata
            avg_quality = sum(ex.quality_score for ex in examples) / len(examples)
            print(f"🏆 Qualità media: {avg_quality:.3f}")
            
            if avg_quality >= 0.8:
                print("✅ Qualità: PASSED")
            else:
                print("⚠️ Qualità: NEEDS IMPROVEMENT")
                
        except Exception as e:
            print(f"❌ Errore validazione {dataset_file.name}: {e}")
    
    return 0

if __name__ == "__main__":
    exit(main())
