{"metadata": {"current_symbols": 3947, "target_symbols": 8000, "expansion_needed": 4053, "generation_date": "2025-05-31T17:01:05.931910", "strategy_version": "1.0.0"}, "phases": [{"phase_id": 1, "priority": 1, "name": "Priority 1 Domains", "domains": [{"domain": "logic_advanced", "target_symbols": 500, "concepts": ["modal_logic", "temporal_logic", "fuzzy_logic", "quantum_logic", "paraconsistent_logic", "relevance_logic", "intuitionistic_logic", "linear_logic", "substructural_logic", "many_valued_logic"], "generation_strategy": "formal_derivation", "validation_focus": ["uniqueness", "score_threshold", "fallback_compliance", "logical_consistency", "formal_correctness"]}, {"domain": "coding_patterns", "target_symbols": 600, "concepts": ["design_patterns", "architectural_patterns", "concurrency_patterns", "functional_patterns", "reactive_patterns", "microservice_patterns", "security_patterns", "performance_patterns", "testing_patterns"], "generation_strategy": "pattern_analysis", "validation_focus": ["uniqueness", "score_threshold", "fallback_compliance", "ast_compatibility", "language_coverage"]}, {"domain": "ast_structures", "target_symbols": 500, "concepts": ["expression_nodes", "statement_nodes", "declaration_nodes", "type_nodes", "literal_nodes", "operator_nodes", "control_nodes", "scope_nodes", "annotation_nodes", "metadata_nodes"], "generation_strategy": "pattern_analysis", "validation_focus": ["uniqueness", "score_threshold", "fallback_compliance", "semantic_coherence", "conceptual_clarity"]}, {"domain": "memory_management", "target_symbols": 400, "concepts": ["garbage_collection", "reference_counting", "memory_pools", "stack_management", "heap_management", "virtual_memory", "cache_coherence", "memory_barriers", "lock_free_structures"], "generation_strategy": "semantic_expansion", "validation_focus": ["uniqueness", "score_threshold", "fallback_compliance", "semantic_coherence", "conceptual_clarity"]}, {"domain": "concurrency_advanced", "target_symbols": 350, "concepts": ["actor_model", "csp_model", "dataflow_programming", "reactive_streams", "async_await", "coroutines", "green_threads", "work_stealing", "lock_free_programming", "wait_free_programming"], "generation_strategy": "semantic_expansion", "validation_focus": ["uniqueness", "score_threshold", "fallback_compliance", "semantic_coherence", "conceptual_clarity"]}], "total_symbols": 2350, "estimated_time": "23.5 hours"}, {"phase_id": 2, "priority": 2, "name": "Priority 2 Domains", "domains": [{"domain": "mathematical_advanced", "target_symbols": 600, "concepts": ["category_theory", "type_theory", "lambda_calculus", "combinatorics", "topology", "abstract_algebra", "number_theory", "graph_theory", "optimization_theory", "information_theory", "complexity_theory"], "generation_strategy": "formal_derivation", "validation_focus": ["uniqueness", "score_threshold", "fallback_compliance", "mathematical_rigor", "theorem_consistency"]}, {"domain": "ai_ml_concepts", "target_symbols": 500, "concepts": ["neural_architectures", "attention_mechanisms", "transformer_variants", "reinforcement_learning", "meta_learning", "few_shot_learning", "continual_learning", "federated_learning", "adversarial_learning"], "generation_strategy": "conceptual_mapping", "validation_focus": ["uniqueness", "score_threshold", "fallback_compliance", "semantic_coherence", "conceptual_clarity"]}, {"domain": "cognitive_science", "target_symbols": 450, "concepts": ["consciousness_models", "metacognition", "theory_of_mind", "cognitive_architectures", "memory_systems", "attention_models", "perception_models", "decision_making", "problem_solving"], "generation_strategy": "conceptual_mapping", "validation_focus": ["uniqueness", "score_threshold", "fallback_compliance", "semantic_coherence", "conceptual_clarity"]}, {"domain": "type_systems", "target_symbols": 300, "concepts": ["dependent_types", "linear_types", "affine_types", "session_types", "refinement_types", "intersection_types", "union_types", "existential_types", "higher_kinded_types", "effect_types"], "generation_strategy": "semantic_expansion", "validation_focus": ["uniqueness", "score_threshold", "fallback_compliance", "semantic_coherence", "conceptual_clarity"]}, {"domain": "compiler_internals", "target_symbols": 250, "concepts": ["lexical_analysis", "syntax_analysis", "semantic_analysis", "code_generation", "optimization_passes", "register_allocation", "instruction_selection", "scheduling", "dead_code_elimination"], "generation_strategy": "semantic_expansion", "validation_focus": ["uniqueness", "score_threshold", "fallback_compliance", "semantic_coherence", "conceptual_clarity"]}], "total_symbols": 2100, "estimated_time": "21.0 hours"}, {"phase_id": 3, "priority": 3, "name": "Priority 3 Domains", "domains": [{"domain": "quantum_computing", "target_symbols": 300, "concepts": ["quantum_gates", "quantum_algorithms", "quantum_error_correction", "quantum_entanglement", "quantum_superposition", "quantum_measurement", "quantum_decoherence", "quantum_teleportation", "quantum_cryptography"], "generation_strategy": "semantic_expansion", "validation_focus": ["uniqueness", "score_threshold", "fallback_compliance", "semantic_coherence", "conceptual_clarity"]}, {"domain": "philosophical_concepts", "target_symbols": 350, "concepts": ["ontology", "epistemology", "metaphysics", "philosophy_of_mind", "philosophy_of_language", "ethics", "aesthetics", "logic_philosophy", "philosophy_of_science", "phenomenology", "existentialism"], "generation_strategy": "semantic_expansion", "validation_focus": ["uniqueness", "score_threshold", "fallback_compliance", "semantic_coherence", "conceptual_clarity"]}], "total_symbols": 650, "estimated_time": "6.5 hours"}], "quality_criteria": {"min_score": 95.0, "max_fallback_length": 8, "unicode_safety": true, "semantic_uniqueness": true, "atomic_concepts": true}, "validation_pipeline": ["USU_criteria_check", "CTU_format_validation", "LCL_compatibility_test", "semantic_uniqueness_verification", "unicode_safety_validation"]}