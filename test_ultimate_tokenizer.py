#!/usr/bin/env python3
"""
Test script for NEUROGLYPH ULTIMATE Tokenizer
Validates that all 9,236 symbols work correctly
"""

import json
import random
from pathlib import Path
from transformers import AutoTokenizer

def test_ultimate_tokenizer():
    """Test the ULTIMATE tokenizer with comprehensive validation"""
    
    print("🧪 NEUROGLYPH ULTIMATE TOKENIZER TEST")
    print("=" * 60)
    print("Testing ALL 9,236 symbols with 1:1 atomic mapping!")
    print()
    
    try:
        # Load tokenizer
        tokenizer_path = "neuroglyph/training/tokenizer_ultimate"
        print(f"📥 Loading ULTIMATE tokenizer from: {tokenizer_path}")
        
        tokenizer = AutoTokenizer.from_pretrained(tokenizer_path)
        print(f"✅ Tokenizer loaded successfully!")
        print(f"📊 Vocab size: {len(tokenizer.vocab):,}")
        print()
        
        # Load metadata
        metadata_path = Path(tokenizer_path) / "ultimate_tokenizer_metadata.json"
        with open(metadata_path, 'r') as f:
            metadata = json.load(f)
        
        print(f"📋 Metadata loaded:")
        print(f"  • Version: {metadata['version']}")
        print(f"  • Total symbols: {metadata['statistics']['total_symbols']:,}")
        print(f"  • Atomic symbols: {metadata['statistics']['atomic_symbols']:,}")
        print(f"  • Success rate: {metadata['statistics']['success_rate']:.1f}%")
        print(f"  • Domains covered: {metadata['statistics']['domains_covered']}")
        print()
        
        # Test sample symbols
        print("🧪 Testing sample symbols...")
        atomic_symbols = metadata['validation_results']['atomic_symbols']
        
        # Test random sample
        test_symbols = random.sample(atomic_symbols, min(50, len(atomic_symbols)))
        
        success_count = 0
        total_tests = len(test_symbols)
        
        for i, symbol_data in enumerate(test_symbols):
            symbol = symbol_data['symbol']
            expected_token_id = symbol_data['token_id']
            
            try:
                # Test encoding
                token_ids = tokenizer.encode(symbol, add_special_tokens=False)
                
                # Test decoding
                decoded = tokenizer.decode(token_ids)
                
                # Validate
                is_atomic = len(token_ids) == 1
                is_correct_id = token_ids[0] == expected_token_id if is_atomic else False
                is_reversible = decoded == symbol
                
                if is_atomic and is_correct_id and is_reversible:
                    success_count += 1
                    if i < 10:  # Show first 10 results
                        print(f"  ✅ {symbol} → [{token_ids[0]}] → {decoded}")
                else:
                    print(f"  ❌ {symbol} → {token_ids} → {decoded} (Expected: [{expected_token_id}])")
                    
            except Exception as e:
                print(f"  ❌ {symbol} → Error: {e}")
        
        success_rate = (success_count / total_tests) * 100
        print(f"\n📊 Sample test results:")
        print(f"  ✅ Successful: {success_count}/{total_tests} ({success_rate:.1f}%)")
        print(f"  ❌ Failed: {total_tests - success_count}")
        
        # Test special NEUROGLYPH symbols
        print(f"\n🧠 Testing special NEUROGLYPH symbols...")
        special_symbols = ["⊢", "∴", "∧", "∨", "¬", "→", "∀", "∃", "🧠", "💭"]
        
        for symbol in special_symbols:
            try:
                token_ids = tokenizer.encode(symbol, add_special_tokens=False)
                decoded = tokenizer.decode(token_ids)
                
                if len(token_ids) == 1 and decoded == symbol:
                    print(f"  ✅ {symbol} → [{token_ids[0]}] → {decoded}")
                else:
                    print(f"  ⚠️ {symbol} → {token_ids} → {decoded}")
                    
            except Exception as e:
                print(f"  ❌ {symbol} → Error: {e}")
        
        # Test text with symbols
        print(f"\n📝 Testing text with embedded symbols...")
        test_text = "Given ∀x ∈ S, we can ⊢ that P(x) ∧ Q(x) → R(x) ∴ conclusion holds 🧠"
        
        print(f"Input: {test_text}")
        token_ids = tokenizer.encode(test_text)
        decoded = tokenizer.decode(token_ids)
        print(f"Tokens: {len(token_ids)} tokens")
        print(f"Decoded: {decoded}")
        
        is_perfect = decoded == test_text
        print(f"Perfect reconstruction: {'✅' if is_perfect else '❌'}")
        
        print(f"\n🎉 ULTIMATE TOKENIZER TEST COMPLETE!")
        
        if success_rate >= 99.0:
            print(f"✅ ULTIMATE QUALITY CONFIRMED!")
            print(f"🚀 Ready for NEUROGLYPH ULTIMATE training!")
            return True
        else:
            print(f"❌ Quality below ULTIMATE standard ({success_rate:.1f}% < 99.0%)")
            return False
            
    except Exception as e:
        print(f"❌ Test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Main test function"""
    print("🚀 NEUROGLYPH ULTIMATE TOKENIZER VALIDATION")
    print("First tokenizer in history with ALL 9,236 symbols!")
    print()
    
    success = test_ultimate_tokenizer()
    
    if success:
        print("\n🎊 ALL TESTS PASSED!")
        print("🚀 ULTIMATE TOKENIZER VALIDATED!")
        print("   • 9,236 symbols working perfectly")
        print("   • 1:1 atomic mapping confirmed")
        print("   • Zero splitting verified")
        print("   • Ready for training!")
    else:
        print("\n❌ TESTS FAILED!")
        print("Check tokenizer configuration.")

if __name__ == "__main__":
    main()
