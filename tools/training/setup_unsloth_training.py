#!/usr/bin/env python3
"""
NEUROGLYPH LLM - Setup Unsloth Training
======================================

Setup per fine-tuning QLoRA 4-bit con Unsloth di Qwen2.5-Coder-1.5B-Instruct
per NEUROGLYPH LLM su Mac M2 8GB.

Basato su: https://docs.unsloth.ai/get-started/fine-tuning-guide
"""

import os
import sys
import subprocess
import json
from pathlib import Path

def check_environment():
    """Verifica environment e dipendenze"""
    print("🔍 VERIFICA ENVIRONMENT UNSLOTH")
    print("=" * 60)

    # Verifica Python
    python_version = sys.version_info
    print(f"🐍 Python: {python_version.major}.{python_version.minor}.{python_version.micro}")

    if python_version < (3, 8):
        print("❌ Python 3.8+ richiesto per Unsloth")
        return False

    # Verifica memoria
    try:
        import psutil
        memory = psutil.virtual_memory()
        available_gb = memory.available / (1024**3)
        print(f"💾 Memoria disponibile: {available_gb:.1f} GB")

        if available_gb < 2.0:
            print("⚠️ Memoria limitata - raccomandato >4GB per training")
        else:
            print("✅ Memoria sufficiente")
    except ImportError:
        print("⚠️ psutil non disponibile - skip verifica memoria")

    # Verifica PyTorch MPS
    try:
        import torch
        print(f"🔥 PyTorch: {torch.__version__}")

        if torch.backends.mps.is_available():
            print("✅ MPS (Metal) disponibile")
        else:
            print("⚠️ MPS non disponibile")
    except ImportError:
        print("⚠️ PyTorch non installato")

    return True

def install_unsloth():
    """Installa Unsloth e dipendenze"""
    print("\n📦 INSTALLAZIONE UNSLOTH")
    print("=" * 60)

    # Pacchetti richiesti per Unsloth
    packages = [
        "unsloth[colab-new] @ git+https://github.com/unslothai/unsloth.git",
        "torch",
        "transformers>=4.36.0",
        "datasets",
        "accelerate",
        "peft",
        "trl",
        "bitsandbytes"
    ]

    for package in packages:
        print(f"📦 Installazione: {package}")
        try:
            subprocess.run([
                sys.executable, "-m", "pip", "install", package
            ], check=True, capture_output=True)
            print(f"✅ {package.split('@')[0].split('[')[0]} installato")
        except subprocess.CalledProcessError as e:
            print(f"❌ Errore installazione {package}: {e}")
            return False

    return True

def verify_model_download():
    """Verifica download modello Unsloth"""
    print("\n📁 VERIFICA MODELLO UNSLOTH")
    print("=" * 60)

    # Controlla entrambe le possibili location
    model_paths = [
        Path("model/qwen2.5coder"),
        Path("Qwen2.5-1.5B-bnb-4bit")
    ]

    model_path = None
    for path in model_paths:
        if path.exists():
            model_path = path
            break

    if model_path:
        print(f"✅ Modello trovato: {model_path}")

        # Verifica file essenziali
        essential_files = [
            "config (1).json",
            "tokenizer.json",
            "tokenizer_config (1).json",
            "model.safetensors"
        ]

        all_files_present = True
        for file in essential_files:
            file_path = model_path / file
            if file_path.exists():
                size_mb = file_path.stat().st_size / (1024*1024)
                print(f"   ✅ {file} ({size_mb:.1f}MB)")
            else:
                print(f"   ❌ {file} mancante")
                all_files_present = False

        if all_files_present:
            print(f"🎉 Modello Unsloth completo e pronto!")

        return all_files_present
    else:
        print(f"❌ Modello non trovato in nessuna location")
        print("💡 Verifica che sia in model/qwen2.5coder/ o Qwen2.5-1.5B-bnb-4bit/")
        return False

def create_training_script():
    """Crea script di training Unsloth per NEUROGLYPH"""
    print("\n🔧 CREAZIONE SCRIPT TRAINING")
    print("=" * 60)

    training_script = '''#!/usr/bin/env python3
"""
NEUROGLYPH LLM - Unsloth Training Script
Fine-tuning QLoRA 4-bit di Qwen2.5-Coder-1.5B per neuroglifi
"""

import torch
from unsloth import FastLanguageModel
from datasets import Dataset
import json

def load_neuroglyph_dataset():
    """Carica dataset neuroglifi per training"""
    # Esempio dataset - da sostituire con dataset reale
    data = [
        {
            "instruction": "Converti questo codice in neuroglifi",
            "input": "def add(a, b): return a + b",
            "output": "⟨⟩α⊕β⤴α⊕β"
        },
        {
            "instruction": "Genera codice da neuroglifi",
            "input": "⟨⟩α⊕β⤴α⊕β",
            "output": "def add(a, b): return a + b"
        }
    ]

    return Dataset.from_list(data)

def setup_model():
    """Setup modello Unsloth"""
    # Trova il path del modello
    model_paths = ["./model/qwen2.5coder", "./Qwen2.5-1.5B-bnb-4bit"]
    model_path = None
    for path in model_paths:
        if os.path.exists(path):
            model_path = path
            break

    if not model_path:
        raise FileNotFoundError("Modello Unsloth non trovato")

    print(f"📦 Caricamento modello da: {model_path}")

    model, tokenizer = FastLanguageModel.from_pretrained(
        model_name=model_path,
        max_seq_length=2048,
        dtype=None,  # Auto detect
        load_in_4bit=True,
    )

    # Setup LoRA
    model = FastLanguageModel.get_peft_model(
        model,
        r=16,           # LoRA rank
        target_modules=["q_proj", "k_proj", "v_proj", "o_proj",
                       "gate_proj", "up_proj", "down_proj"],
        lora_alpha=16,
        lora_dropout=0,
        bias="none",
        use_gradient_checkpointing="unsloth",
        random_state=3407,
        use_rslora=False,
        loftq_config=None,
    )

    return model, tokenizer

def format_prompts(examples):
    """Formatta prompt per training"""
    texts = []
    for instruction, input_text, output in zip(
        examples["instruction"], examples["input"], examples["output"]
    ):
        text = f"""### Instruction:
{instruction}

### Input:
{input_text}

### Response:
{output}"""
        texts.append(text)
    return {"text": texts}

def main():
    """Training principale"""
    print("🚀 NEUROGLYPH LLM - Unsloth Training")

    # Setup modello
    model, tokenizer = setup_model()

    # Carica dataset
    dataset = load_neuroglyph_dataset()
    dataset = dataset.map(format_prompts, batched=True)

    # Setup trainer
    from trl import SFTTrainer
    from transformers import TrainingArguments

    trainer = SFTTrainer(
        model=model,
        tokenizer=tokenizer,
        train_dataset=dataset,
        dataset_text_field="text",
        max_seq_length=2048,
        dataset_num_proc=2,
        args=TrainingArguments(
            per_device_train_batch_size=2,
            gradient_accumulation_steps=4,
            warmup_steps=100,
            max_steps=1000,
            learning_rate=2e-4,
            fp16=not torch.cuda.is_available(),
            bf16=torch.cuda.is_available(),
            logging_steps=10,
            optim="adamw_8bit",
            weight_decay=0.01,
            lr_scheduler_type="linear",
            seed=3407,
            output_dir="outputs",
        ),
    )

    # Avvia training
    print("🔥 Inizio training...")
    trainer.train()

    # Salva modello
    print("💾 Salvataggio modello...")
    model.save_pretrained("neuroglyph_llm_lora")
    tokenizer.save_pretrained("neuroglyph_llm_lora")

    print("✅ Training completato!")

if __name__ == "__main__":
    main()
'''

    with open("scripts/train_neuroglyph_unsloth.py", "w") as f:
        f.write(training_script)

    os.chmod("scripts/train_neuroglyph_unsloth.py", 0o755)
    print("✅ Script training creato: scripts/train_neuroglyph_unsloth.py")

def create_dataset_converter():
    """Crea script per convertire dataset NEUROGLYPH per Unsloth"""
    print("\n📊 CREAZIONE DATASET CONVERTER")
    print("=" * 60)

    converter_script = '''#!/usr/bin/env python3
"""
Converte dataset NEUROGLYPH esistenti per formato Unsloth
"""

import json
from pathlib import Path

def convert_ng_to_code_dataset():
    """Converte ng_to_code.jsonl per Unsloth"""
    input_file = Path("datasets/ng_to_code.jsonl")
    output_file = Path("datasets/unsloth_neuroglyph_dataset.jsonl")

    if not input_file.exists():
        print(f"❌ Dataset non trovato: {input_file}")
        return False

    converted_data = []

    with open(input_file, 'r') as f:
        for line in f:
            data = json.loads(line)

            # Formato Unsloth
            converted = {
                "instruction": "Converti neuroglifi in codice Python",
                "input": data.get("neuroglyphs", ""),
                "output": data.get("code", "")
            }
            converted_data.append(converted)

    # Salva dataset convertito
    with open(output_file, 'w') as f:
        for item in converted_data:
            f.write(json.dumps(item) + '\\n')

    print(f"✅ Dataset convertito: {len(converted_data)} esempi")
    print(f"📁 Salvato in: {output_file}")
    return True

if __name__ == "__main__":
    convert_ng_to_code_dataset()
'''

    with open("scripts/convert_dataset_unsloth.py", "w") as f:
        f.write(converter_script)

    os.chmod("scripts/convert_dataset_unsloth.py", 0o755)
    print("✅ Dataset converter creato: scripts/convert_dataset_unsloth.py")

def main():
    """Setup principale Unsloth"""
    print("🚀 NEUROGLYPH LLM - Setup Unsloth Training")
    print("🎯 QLoRA 4-bit Fine-tuning per Mac M2 8GB")
    print("=" * 70)

    # Verifica environment
    if not check_environment():
        print("❌ Environment non pronto")
        return False

    # Installa Unsloth
    if not install_unsloth():
        print("❌ Installazione Unsloth fallita")
        return False

    # Verifica modello
    if not verify_model_download():
        print("❌ Modello non disponibile")
        return False

    # Crea script training
    create_training_script()

    # Crea dataset converter
    create_dataset_converter()

    print("\n🎉 SETUP UNSLOTH COMPLETATO!")
    print("=" * 70)
    print("📋 PROSSIMI PASSI:")
    print("   1. Converti dataset: python3 scripts/convert_dataset_unsloth.py")
    print("   2. Avvia training: python3 scripts/train_neuroglyph_unsloth.py")
    print("   3. Monitora progress in outputs/")
    print("")
    print("🧠 NEUROGLYPH LLM pronto per fine-tuning Unsloth!")

    return True

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
