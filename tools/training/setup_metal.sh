#!/bin/bash
# Setup Metal GPU per NEUROGLYPH LLM
echo "🚀 NEUROGLYPH LLM - Metal GPU Setup"

# Verifica memoria
echo "💾 Verifica memoria..."
python3 -c "
import psutil
mem = psutil.virtual_memory()
available = mem.available/(1024**3)
print(f'Memoria disponibile: {available:.1f} GB')
if available < 3.0:
    print('⚠️ ATTENZIONE: Chiudi altre app per liberare memoria')
    exit(1)
else:
    print('✅ Memoria sufficiente')
"

if [ $? -ne 0 ]; then
    echo "❌ Memoria insufficiente. Chiudi altre applicazioni."
    exit 1
fi

# Crea virtual environment
echo "🔧 Creazione virtual environment..."
python3 -m venv venv_metal
source venv_metal/bin/activate

# Installa dipendenze base
echo "📦 Installazione dipendenze..."
pip install --upgrade pip
pip install psutil

# Installa llama-cpp-python con Metal
echo "🚀 Installazione llama-cpp-python con Metal support..."
CMAKE_ARGS="-DLLAMA_METAL=on" pip install llama-cpp-python --no-cache-dir

# Test installazione
echo "🧪 Test installazione..."
python3 -c "
from llama_cpp import Llama
print('✅ llama-cpp-python con Metal installato correttamente')
"

echo "🎉 Setup Metal completato!"
echo "💡 Per usare: source venv_metal/bin/activate"
