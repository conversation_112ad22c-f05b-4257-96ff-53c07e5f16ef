#!/usr/bin/env python3
"""
NEUROGLYPH LLM - Dataset di Training
===================================

Genera dataset completo per training NEUROGLYPH LLM con neuroglifi.
Formato ottimizzato per Unsloth instruction-tuning.
"""

import json
import random
from typing import List, Dict, Any
from pathlib import Path

class NeuroglyphDatasetGenerator:
    """Generatore dataset NEUROGLYPH per training"""
    
    def __init__(self):
        self.symbols_registry = self._load_symbols_registry()
        self.training_examples = []
        
    def _load_symbols_registry(self) -> Dict[str, Any]:
        """Carica registry simboli neuroglifi"""
        try:
            with open("symbols_registry.json", "r", encoding="utf-8") as f:
                return json.load(f)
        except FileNotFoundError:
            print("⚠️ symbols_registry.json non trovato, uso simboli base")
            return {"symbols": []}
    
    def generate_basic_operations(self) -> List[Dict[str, str]]:
        """Genera esempi per operazioni base"""
        examples = []
        
        # Operazioni aritmetiche
        arithmetic_examples = [
            {
                "instruction": "Converti questi neuroglifi in una funzione Python",
                "input": "⟨⟩α⊕β⤴α⊕β",
                "output": "def add(a, b):\n    return a + b"
            },
            {
                "instruction": "Implementa la funzione rappresentata dai neuroglifi",
                "input": "⟨⟩α⊖β⤴α⊖β", 
                "output": "def subtract(a, b):\n    return a - b"
            },
            {
                "instruction": "Traduci i simboli neuroglifi in codice Python",
                "input": "⟨⟩α⊗β⤴α⊗β",
                "output": "def multiply(a, b):\n    return a * b"
            },
            {
                "instruction": "Genera codice Python dai neuroglifi",
                "input": "⟨⟩α⊘β⤴α⊘β",
                "output": "def divide(a, b):\n    return a / b"
            },
            {
                "instruction": "Converti neuroglifi in funzione Python",
                "input": "⟨⟩α^β⤴α**β",
                "output": "def power(a, b):\n    return a ** b"
            }
        ]
        
        examples.extend(arithmetic_examples)
        
        # Operazioni logiche
        logical_examples = [
            {
                "instruction": "Implementa la funzione logica dai neuroglifi",
                "input": "⟨⟩α∧β⤴α∧β",
                "output": "def logical_and(a, b):\n    return a and b"
            },
            {
                "instruction": "Traduci neuroglifi in operazione logica Python",
                "input": "⟨⟩α∨β⤴α∨β", 
                "output": "def logical_or(a, b):\n    return a or b"
            },
            {
                "instruction": "Converti simboli in funzione di negazione",
                "input": "⟨⟩¬α⤴¬α",
                "output": "def logical_not(a):\n    return not a"
            }
        ]
        
        examples.extend(logical_examples)
        
        return examples
    
    def generate_control_flow(self) -> List[Dict[str, str]]:
        """Genera esempi per strutture di controllo"""
        examples = []
        
        # Condizionali
        conditional_examples = [
            {
                "instruction": "Implementa la struttura condizionale dai neuroglifi",
                "input": "⟨⟩α≤1?α:ƒ⟨α-1⟩",
                "output": "def factorial(n):\n    return n if n <= 1 else factorial(n-1)"
            },
            {
                "instruction": "Traduci neuroglifi in controllo condizionale",
                "input": "⟨⟩α>β?α:β",
                "output": "def max_value(a, b):\n    return a if a > b else b"
            },
            {
                "instruction": "Converti simboli in funzione condizionale",
                "input": "⟨⟩α<β?α:β",
                "output": "def min_value(a, b):\n    return a if a < b else b"
            }
        ]
        
        examples.extend(conditional_examples)
        
        # Cicli
        loop_examples = [
            {
                "instruction": "Implementa il ciclo rappresentato dai neuroglifi",
                "input": "⟨⟩∑ᵢ₌₁ⁿαᵢ",
                "output": "def sum_range(n):\n    return sum(range(1, n+1))"
            },
            {
                "instruction": "Traduci neuroglifi in iterazione Python",
                "input": "⟨⟩∏ᵢ₌₁ⁿαᵢ",
                "output": "def product_range(n):\n    result = 1\n    for i in range(1, n+1):\n        result *= i\n    return result"
            }
        ]
        
        examples.extend(loop_examples)
        
        return examples
    
    def generate_data_structures(self) -> List[Dict[str, str]]:
        """Genera esempi per strutture dati"""
        examples = []
        
        # Liste
        list_examples = [
            {
                "instruction": "Implementa operazione su lista dai neuroglifi",
                "input": "⟨⟩[α₁,α₂,...,αₙ]→|α|",
                "output": "def list_length(lst):\n    return len(lst)"
            },
            {
                "instruction": "Traduci neuroglifi in operazione lista",
                "input": "⟨⟩[α₁,α₂,...,αₙ]→α₁",
                "output": "def first_element(lst):\n    return lst[0] if lst else None"
            },
            {
                "instruction": "Converti simboli in funzione lista",
                "input": "⟨⟩[α₁,α₂,...,αₙ]→αₙ",
                "output": "def last_element(lst):\n    return lst[-1] if lst else None"
            }
        ]
        
        examples.extend(list_examples)
        
        # Dizionari
        dict_examples = [
            {
                "instruction": "Implementa operazione dizionario dai neuroglifi",
                "input": "⟨⟩{κ:ν}→ν[κ]",
                "output": "def get_value(dictionary, key):\n    return dictionary.get(key)"
            },
            {
                "instruction": "Traduci neuroglifi in operazione chiavi",
                "input": "⟨⟩{κ:ν}→[κ]",
                "output": "def get_keys(dictionary):\n    return list(dictionary.keys())"
            }
        ]
        
        examples.extend(dict_examples)
        
        return examples
    
    def generate_algorithms(self) -> List[Dict[str, str]]:
        """Genera esempi per algoritmi comuni"""
        examples = []
        
        # Algoritmi di ordinamento
        sorting_examples = [
            {
                "instruction": "Implementa l'algoritmo di ordinamento dai neuroglifi",
                "input": "⟨⟩[α]→sort([α])",
                "output": "def bubble_sort(arr):\n    n = len(arr)\n    for i in range(n):\n        for j in range(0, n-i-1):\n            if arr[j] > arr[j+1]:\n                arr[j], arr[j+1] = arr[j+1], arr[j]\n    return arr"
            },
            {
                "instruction": "Traduci neuroglifi in ricerca binaria",
                "input": "⟨⟩search(α,β)→idx",
                "output": "def binary_search(arr, target):\n    left, right = 0, len(arr) - 1\n    while left <= right:\n        mid = (left + right) // 2\n        if arr[mid] == target:\n            return mid\n        elif arr[mid] < target:\n            left = mid + 1\n        else:\n            right = mid - 1\n    return -1"
            }
        ]
        
        examples.extend(sorting_examples)
        
        # Algoritmi ricorsivi
        recursive_examples = [
            {
                "instruction": "Implementa la sequenza di Fibonacci dai neuroglifi",
                "input": "⟨⟩ƒ⟨n⟩⤴n≤1?n:ƒ⟨n-1⟩⊕ƒ⟨n-2⟩",
                "output": "def fibonacci(n):\n    if n <= 1:\n        return n\n    return fibonacci(n-1) + fibonacci(n-2)"
            },
            {
                "instruction": "Traduci neuroglifi in calcolo fattoriale",
                "input": "⟨⟩n!⤴n≤1?1:n×(n-1)!",
                "output": "def factorial(n):\n    if n <= 1:\n        return 1\n    return n * factorial(n-1)"
            }
        ]
        
        examples.extend(recursive_examples)
        
        return examples
    
    def generate_reverse_examples(self) -> List[Dict[str, str]]:
        """Genera esempi per conversione codice → neuroglifi"""
        examples = []
        
        reverse_examples = [
            {
                "instruction": "Converti questa funzione Python in neuroglifi",
                "input": "def add(a, b):\n    return a + b",
                "output": "⟨⟩α⊕β⤴α⊕β"
            },
            {
                "instruction": "Traduci il codice Python in simboli neuroglifi",
                "input": "def multiply(x, y):\n    return x * y",
                "output": "⟨⟩α⊗β⤴α⊗β"
            },
            {
                "instruction": "Rappresenta questa funzione con neuroglifi",
                "input": "def is_even(n):\n    return n % 2 == 0",
                "output": "⟨⟩α%2=0⤴α%2=0"
            },
            {
                "instruction": "Converti in notazione neuroglifo",
                "input": "def max_of_three(a, b, c):\n    return max(a, b, c)",
                "output": "⟨⟩max(α,β,γ)⤴max(α,β,γ)"
            }
        ]
        
        examples.extend(reverse_examples)
        
        return examples
    
    def generate_complete_dataset(self) -> List[Dict[str, str]]:
        """Genera dataset completo"""
        print("🔄 Generazione dataset NEUROGLYPH...")
        
        all_examples = []
        
        # Genera tutte le categorie
        all_examples.extend(self.generate_basic_operations())
        all_examples.extend(self.generate_control_flow())
        all_examples.extend(self.generate_data_structures())
        all_examples.extend(self.generate_algorithms())
        all_examples.extend(self.generate_reverse_examples())
        
        # Mescola esempi
        random.shuffle(all_examples)
        
        print(f"✅ Dataset generato: {len(all_examples)} esempi")
        
        return all_examples
    
    def save_dataset(self, examples: List[Dict[str, str]], filename: str = "neuroglyph_training.jsonl"):
        """Salva dataset in formato JSONL"""
        output_path = Path("datasets") / filename
        output_path.parent.mkdir(exist_ok=True)
        
        with open(output_path, "w", encoding="utf-8") as f:
            for example in examples:
                f.write(json.dumps(example, ensure_ascii=False) + "\n")
        
        print(f"💾 Dataset salvato: {output_path}")
        print(f"📊 Esempi totali: {len(examples)}")
        
        return output_path
    
    def create_train_test_split(self, examples: List[Dict[str, str]], test_ratio: float = 0.1):
        """Crea split train/test"""
        random.shuffle(examples)
        
        split_idx = int(len(examples) * (1 - test_ratio))
        train_examples = examples[:split_idx]
        test_examples = examples[split_idx:]
        
        # Salva train set
        train_path = self.save_dataset(train_examples, "neuroglyph_train.jsonl")
        
        # Salva test set
        test_path = self.save_dataset(test_examples, "neuroglyph_test.jsonl")
        
        print(f"📊 Train: {len(train_examples)} esempi")
        print(f"📊 Test: {len(test_examples)} esempi")
        
        return train_path, test_path

def main():
    """Genera dataset principale"""
    print("🧠 NEUROGLYPH LLM - Generazione Dataset")
    print("=" * 60)
    
    generator = NeuroglyphDatasetGenerator()
    
    # Genera dataset completo
    examples = generator.generate_complete_dataset()
    
    # Crea split train/test
    train_path, test_path = generator.create_train_test_split(examples)
    
    # Mostra esempi
    print(f"\n📝 ESEMPI DATASET:")
    print("=" * 60)
    
    for i, example in enumerate(examples[:3]):
        print(f"\n🔸 Esempio {i+1}:")
        print(f"   Instruction: {example['instruction']}")
        print(f"   Input: {example['input']}")
        print(f"   Output: {example['output'][:50]}...")
    
    print(f"\n🎉 Dataset NEUROGLYPH pronto per training!")
    print(f"📁 Files: {train_path}, {test_path}")
    
    return train_path, test_path

if __name__ == "__main__":
    main()
