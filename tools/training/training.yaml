# NEUROGLIPH LLM Training Configuration
# =====================================
# Configurazione completa per il training di modelli LLM ottimizzati per neuroglifi

model_config:
  name: "neurogliph-llm-v1"
  architecture: "transformer"
  base_model: "unsloth/Qwen2.5-1.5B-bnb-4bit"  # Modello base Unsloth QLoRA 4-bit - Apache 2.0 License - OPTIMIZED FOR EFFICIENCY

  # Configurazione architettura
  hidden_size: 4096
  num_attention_heads: 32
  num_hidden_layers: 32
  intermediate_size: 11008
  max_position_embeddings: 4096
  vocab_size: 32000  # Esteso per includere simboli neuroglifi

  # Configurazioni specifiche per neuroglifi
  neuroglyph_vocab_size: 2048  # Simboli neuroglifi dedicati
  semantic_embedding_dim: 512
  compression_aware: true
  multi_language_support: true

tokenizer_config:
  type: "neuroglyph_tokenizer"
  base_tokenizer: "sentencepiece"

  # Configurazione simboli neuroglifi
  special_tokens:
    neuroglyph_start: "<NG_START>"
    neuroglyph_end: "<NG_END>"
    scope_marker: "<SCOPE>"
    semantic_marker: "<SEM>"
    compression_marker: "<COMP>"

  # Gestione caratteri Unicode avanzati
  unicode_support: true
  normalization: "NFKC"
  preserve_neuroglyph_symbols: true

training_config:
  # Parametri di training Unsloth QLoRA 4-bit
  learning_rate: 2e-4  # Unsloth raccomanda learning rate più alto
  batch_size: 2        # Ridotto per Mac M2 8GB
  gradient_accumulation_steps: 4  # Per simulare batch_size=8
  max_steps: 1000      # Ridotto per testing rapido
  warmup_steps: 100    # Proporzionale a max_steps
  weight_decay: 0.01

  # QLoRA 4-bit configuration
  use_qlora: true
  qlora_rank: 16
  qlora_alpha: 16
  qlora_dropout: 0.0
  target_modules: ["q_proj", "k_proj", "v_proj", "o_proj", "gate_proj", "up_proj", "down_proj"]

  # Ottimizzazioni specifiche
  mixed_precision: "fp16"
  gradient_checkpointing: true
  dataloader_num_workers: 8

  # Schedulers
  lr_scheduler: "cosine"
  lr_scheduler_kwargs:
    T_max: 50000
    eta_min: 1e-6

  # Regularization
  dropout: 0.1
  attention_dropout: 0.1

  # Early stopping
  early_stopping_patience: 5
  early_stopping_threshold: 0.001

dataset_config:
  # Dataset di training
  train_datasets:
    - name: "neuroglyph_code_pairs"
      path: "datasets/ng_to_code.jsonl"
      weight: 0.6
      format: "jsonl"

    - name: "text_to_neuroglyph"
      path: "datasets/text_to_ng.jsonl"
      weight: 0.3
      format: "jsonl"

    - name: "code_understanding"
      path: "datasets/code_qa.jsonl"
      weight: 0.1
      format: "jsonl"

  # Dataset di validazione
  validation_datasets:
    - name: "neuroglyph_validation"
      path: "datasets/validation_ng.jsonl"
      format: "jsonl"

  # Preprocessing
  max_sequence_length: 2048
  truncation: true
  padding: "max_length"

  # Data augmentation
  augmentation:
    enabled: true
    techniques:
      - "symbol_substitution"
      - "semantic_permutation"
      - "compression_variation"
    probability: 0.15

loss_config:
  # Funzione di loss principale
  primary_loss: "cross_entropy"

  # Loss aggiuntive per ottimizzazione neuroglifi
  auxiliary_losses:
    - name: "semantic_consistency"
      weight: 0.2
      description: "Mantiene coerenza semantica tra codice e neuroglifi"

    - name: "compression_efficiency"
      weight: 0.1
      description: "Ottimizza per efficienza di compressione"

    - name: "reconstruction_accuracy"
      weight: 0.15
      description: "Accuratezza nella ricostruzione del codice"

evaluation_config:
  # Metriche di valutazione
  metrics:
    - "perplexity"
    - "bleu_score"
    - "code_similarity"
    - "compression_ratio"
    - "semantic_accuracy"
    - "execution_correctness"

  # Frequenza valutazione
  eval_steps: 500
  save_steps: 1000
  logging_steps: 100

  # Test specifici per neuroglifi
  neuroglyph_tests:
    - "round_trip_accuracy"  # Codice -> NG -> Codice
    - "semantic_preservation"  # Mantenimento significato
    - "compression_quality"  # Qualità compressione
    - "multi_language_consistency"  # Coerenza multi-linguaggio

optimization_config:
  # Ottimizzatore
  optimizer: "adamw"
  optimizer_kwargs:
    betas: [0.9, 0.999]
    eps: 1e-8

  # Gradient clipping
  max_grad_norm: 1.0

  # Memory optimization
  gradient_checkpointing: true
  use_cache: false

  # Distributed training
  distributed:
    enabled: false  # Impostare true per training distribuito
    backend: "nccl"
    world_size: 1

hardware_config:
  # Configurazione hardware Mac M2 8GB
  device: "mps"   # Metal Performance Shaders per Mac M2
  num_gpus: 1
  gpu_memory_fraction: 0.8  # Conservativo per Mac M2 8GB

  # Ottimizzazioni specifiche
  compile_model: true  # PyTorch 2.0 compile
  use_flash_attention: true  # Se disponibile

  # Memory management
  empty_cache_steps: 100
  max_memory_per_gpu: "24GB"

monitoring_config:
  # Logging e monitoring
  wandb:
    enabled: true
    project: "neurogliph-llm"
    entity: "neurogliph-research"
    tags: ["neurogliph", "code-generation", "compression"]

  tensorboard:
    enabled: true
    log_dir: "logs/tensorboard"

  # Checkpointing
  checkpoint_dir: "checkpoints/"
  save_total_limit: 3
  save_best_model: true

  # Alerts
  alerts:
    loss_threshold: 10.0  # Alert se loss supera soglia
    memory_threshold: 0.95  # Alert se memoria supera 95%

inference_config:
  # Configurazione per inferenza
  generation_config:
    max_new_tokens: 512
    temperature: 0.7
    top_p: 0.9
    top_k: 50
    repetition_penalty: 1.1
    do_sample: true

  # Ottimizzazioni inferenza
  use_cache: true
  torch_compile: true

  # Batch inference
  batch_size: 8
  max_batch_size: 32

experimental_features:
  # Features sperimentali
  adaptive_compression: true
  semantic_attention: true
  multi_modal_support: false

  # Research features
  neurosymbolic_reasoning: true
  code_execution_simulation: false

  # Advanced optimizations
  knowledge_distillation: false
  pruning: false
  quantization: false

# Configurazioni ambiente
environment:
  python_version: "3.10+"
  pytorch_version: "2.1.0"
  transformers_version: "4.35.0"

  required_packages:
    - torch
    - transformers
    - datasets
    - wandb
    - tensorboard
    - accelerate
    - peft
    - bitsandbytes

  # Variabili ambiente
  env_vars:
    TOKENIZERS_PARALLELISM: "false"
    CUDA_VISIBLE_DEVICES: "0"
    WANDB_PROJECT: "neurogliph-llm"
