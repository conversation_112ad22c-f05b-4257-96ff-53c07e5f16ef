#!/usr/bin/env python3
"""
NEUROGLYPH LLM - Training Script Semplificato
"""

import torch
from transformers import AutoTokenizer, AutoModelForCausalLM
from datasets import Dataset
import json

def create_sample_dataset():
    """Crea dataset di esempio per neuroglifi"""
    data = [
        {
            "instruction": "Converti neuroglifi in codice Python",
            "input": "⟨⟩α⊕β⤴α⊕β",
            "output": "def add(a, b):\n    return a + b"
        },
        {
            "instruction": "Genera codice da neuroglifi",
            "input": "⟨⟩ƒ⟨n⟩⤴n≤1?n:ƒ⟨n-1⟩⊕ƒ⟨n-2⟩", 
            "output": "def fibonacci(n):\n    return n if n <= 1 else fibon<PERSON><PERSON>(n-1) + fi<PERSON><PERSON><PERSON>(n-2)"
        },
        {
            "instruction": "Implementa funzione da simboli",
            "input": "⟨⟩α×β⤴α×β",
            "output": "def multiply(a, b):\n    return a * b"
        }
    ]
    
    return Dataset.from_list(data)

def format_prompt(example):
    """Formatta prompt per training"""
    return f"""### Instruction:
{example['instruction']}

### Input:
{example['input']}

### Response:
{example['output']}"""

def main():
    """Training principale"""
    print("🚀 NEUROGLYPH LLM - Training Semplificato")
    
    # Carica tokenizer
    model_path = "model/qwen2.5coder"
    tokenizer = AutoTokenizer.from_pretrained(model_path)
    
    # Crea dataset
    dataset = create_sample_dataset()
    
    print(f"📊 Dataset: {len(dataset)} esempi")
    
    # Mostra esempio formattato
    example = dataset[0]
    formatted = format_prompt(example)
    print(f"\n📝 Esempio formattato:")
    print(formatted)
    
    # Tokenizza esempio
    tokens = tokenizer.encode(formatted)
    print(f"\n🔤 Tokens: {len(tokens)} token")
    print(f"   Primi 10: {tokens[:10]}")
    
    print("\n✅ Setup training completato!")
    print("💡 Per training completo, installare Unsloth")

if __name__ == "__main__":
    main()
