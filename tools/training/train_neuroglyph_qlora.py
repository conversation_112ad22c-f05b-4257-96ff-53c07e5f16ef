#!/usr/bin/env python3
"""
NEUROGLYPH LLM - Training QLoRA 4-bit
====================================

Training script per NEUROGLYPH LLM usando QLoRA 4-bit su Qwen2.5-Coder-1.5B.
Ottimizzato per Mac M2 8GB con Metal Performance Shaders.
"""

import os
import sys
import json
import torch
from pathlib import Path
from typing import Dict, List, Any
from datetime import datetime

def check_environment():
    """Verifica environment di training"""
    print("🔍 VERIFICA ENVIRONMENT TRAINING")
    print("=" * 60)
    
    # Memoria
    import psutil
    memory = psutil.virtual_memory()
    available_gb = memory.available / (1024**3)
    print(f"💾 Memoria disponibile: {available_gb:.1f} GB")
    
    if available_gb < 2.0:
        print("⚠️ Memoria limitata - chiudi altre app")
        return False
    
    # PyTorch MPS
    if torch.backends.mps.is_available():
        print("✅ Metal Performance Shaders disponibile")
        device = "mps"
    else:
        print("⚠️ MPS non disponibile, uso CPU")
        device = "cpu"
    
    # Modello
    model_path = "model/qwen2.5coder"
    if not Path(model_path).exists():
        print(f"❌ Modello non trovato: {model_path}")
        return False
    
    print(f"✅ Modello trovato: {model_path}")
    
    # Dataset
    train_path = "datasets/neuroglyph_train.jsonl"
    if not Path(train_path).exists():
        print(f"❌ Dataset training non trovato: {train_path}")
        return False
    
    print(f"✅ Dataset training: {train_path}")
    
    return True

def load_dataset(file_path: str) -> List[Dict[str, str]]:
    """Carica dataset JSONL"""
    print(f"📊 Caricamento dataset: {file_path}")
    
    examples = []
    with open(file_path, "r", encoding="utf-8") as f:
        for line in f:
            if line.strip():
                examples.append(json.loads(line))
    
    print(f"✅ Caricati {len(examples)} esempi")
    return examples

def format_prompt(example: Dict[str, str]) -> str:
    """Formatta prompt per training instruction-following"""
    return f"""### Instruction:
{example['instruction']}

### Input:
{example['input']}

### Response:
{example['output']}"""

def setup_model_and_tokenizer():
    """Setup modello e tokenizer per training"""
    print("\n🔧 SETUP MODELLO E TOKENIZER")
    print("=" * 60)
    
    try:
        from transformers import AutoTokenizer, AutoModelForCausalLM
        from peft import LoraConfig, get_peft_model, TaskType
        
        model_path = "model/qwen2.5coder"
        
        # Carica tokenizer
        print("📝 Caricamento tokenizer...")
        tokenizer = AutoTokenizer.from_pretrained(model_path)
        
        # Aggiungi pad token se mancante
        if tokenizer.pad_token is None:
            tokenizer.pad_token = tokenizer.eos_token
        
        print(f"✅ Tokenizer caricato (vocab: {tokenizer.vocab_size})")
        
        # Carica modello
        print("🧠 Caricamento modello...")
        
        # Configurazione per Mac M2 8GB
        model = AutoModelForCausalLM.from_pretrained(
            model_path,
            torch_dtype=torch.float16,
            device_map="auto",
            load_in_4bit=True,
            trust_remote_code=True
        )
        
        print("✅ Modello base caricato")
        
        # Configurazione LoRA
        lora_config = LoraConfig(
            r=16,                    # Rank LoRA
            lora_alpha=16,           # Alpha scaling
            target_modules=[         # Moduli target per Qwen2
                "q_proj", "k_proj", "v_proj", "o_proj",
                "gate_proj", "up_proj", "down_proj"
            ],
            lora_dropout=0.0,        # No dropout per stabilità
            bias="none",             # No bias
            task_type=TaskType.CAUSAL_LM
        )
        
        # Applica LoRA
        model = get_peft_model(model, lora_config)
        
        # Conta parametri trainable
        trainable_params = sum(p.numel() for p in model.parameters() if p.requires_grad)
        total_params = sum(p.numel() for p in model.parameters())
        
        print(f"🎯 Parametri trainable: {trainable_params:,}")
        print(f"📊 Parametri totali: {total_params:,}")
        print(f"📈 Percentuale trainable: {100 * trainable_params / total_params:.2f}%")
        
        return model, tokenizer
        
    except Exception as e:
        print(f"❌ Errore setup modello: {e}")
        return None, None

def prepare_training_data(examples: List[Dict[str, str]], tokenizer):
    """Prepara dati per training"""
    print("\n📊 PREPARAZIONE DATI TRAINING")
    print("=" * 60)
    
    try:
        from datasets import Dataset
        
        # Formatta prompt
        formatted_examples = []
        for example in examples:
            formatted_text = format_prompt(example)
            formatted_examples.append({"text": formatted_text})
        
        # Crea dataset
        dataset = Dataset.from_list(formatted_examples)
        
        # Tokenizza
        def tokenize_function(examples):
            return tokenizer(
                examples["text"],
                truncation=True,
                padding=False,
                max_length=512,  # Ridotto per Mac M2 8GB
                return_tensors=None
            )
        
        tokenized_dataset = dataset.map(
            tokenize_function,
            batched=True,
            remove_columns=dataset.column_names
        )
        
        print(f"✅ Dataset tokenizzato: {len(tokenized_dataset)} esempi")
        
        return tokenized_dataset
        
    except Exception as e:
        print(f"❌ Errore preparazione dati: {e}")
        return None

def train_model(model, tokenizer, dataset):
    """Training principale"""
    print("\n🚀 AVVIO TRAINING")
    print("=" * 60)
    
    try:
        from transformers import TrainingArguments, Trainer, DataCollatorForLanguageModeling
        
        # Configurazione training ottimizzata per Mac M2 8GB
        training_args = TrainingArguments(
            output_dir="./outputs/neuroglyph_qlora",
            
            # Parametri training
            num_train_epochs=3,
            per_device_train_batch_size=1,      # Ridotto per memoria
            gradient_accumulation_steps=4,      # Simula batch_size=4
            learning_rate=2e-4,                 # LR più alto per LoRA
            
            # Ottimizzazioni memoria
            fp16=True,                          # Mixed precision
            dataloader_pin_memory=False,        # Risparmia memoria
            gradient_checkpointing=True,        # Risparmia memoria
            
            # Logging e salvataggio
            logging_steps=5,
            save_steps=50,
            save_total_limit=2,
            
            # Evaluation
            evaluation_strategy="no",           # Skip per velocità
            
            # Ottimizzazioni
            remove_unused_columns=False,
            warmup_steps=10,
            weight_decay=0.01,
            
            # Report
            report_to=None,                     # No wandb
            run_name=f"neuroglyph_qlora_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
        )
        
        # Data collator
        data_collator = DataCollatorForLanguageModeling(
            tokenizer=tokenizer,
            mlm=False,  # Causal LM
            pad_to_multiple_of=8
        )
        
        # Trainer
        trainer = Trainer(
            model=model,
            args=training_args,
            train_dataset=dataset,
            data_collator=data_collator,
            tokenizer=tokenizer
        )
        
        print("🔥 Inizio training...")
        print(f"📊 Esempi: {len(dataset)}")
        print(f"📊 Epochs: {training_args.num_train_epochs}")
        print(f"📊 Batch size effettivo: {training_args.per_device_train_batch_size * training_args.gradient_accumulation_steps}")
        
        # Avvia training
        trainer.train()
        
        print("✅ Training completato!")
        
        # Salva modello finale
        output_dir = "outputs/neuroglyph_qlora_final"
        trainer.save_model(output_dir)
        tokenizer.save_pretrained(output_dir)
        
        print(f"💾 Modello salvato in: {output_dir}")
        
        return True
        
    except Exception as e:
        print(f"❌ Errore training: {e}")
        return False

def test_trained_model():
    """Test veloce del modello trainato"""
    print("\n🧪 TEST MODELLO TRAINATO")
    print("=" * 60)
    
    try:
        from transformers import AutoTokenizer, AutoModelForCausalLM
        from peft import PeftModel
        
        # Carica modello base
        base_model_path = "model/qwen2.5coder"
        model = AutoModelForCausalLM.from_pretrained(
            base_model_path,
            torch_dtype=torch.float16,
            device_map="auto",
            load_in_4bit=True
        )
        
        # Carica LoRA weights
        lora_path = "outputs/neuroglyph_qlora_final"
        if Path(lora_path).exists():
            model = PeftModel.from_pretrained(model, lora_path)
            print("✅ LoRA weights caricati")
        else:
            print("⚠️ LoRA weights non trovati, uso modello base")
        
        # Carica tokenizer
        tokenizer = AutoTokenizer.from_pretrained(lora_path if Path(lora_path).exists() else base_model_path)
        
        # Test esempi
        test_examples = [
            "⟨⟩α⊕β⤴α⊕β",
            "⟨⟩α⊗β⤴α⊗β",
            "⟨⟩ƒ⟨n⟩⤴n≤1?n:ƒ⟨n-1⟩⊕ƒ⟨n-2⟩"
        ]
        
        for i, test_input in enumerate(test_examples):
            print(f"\n🔸 Test {i+1}:")
            print(f"   Input: {test_input}")
            
            prompt = f"""### Instruction:
Converti questi neuroglifi in una funzione Python

### Input:
{test_input}

### Response:
"""
            
            inputs = tokenizer(prompt, return_tensors="pt")
            
            with torch.no_grad():
                outputs = model.generate(
                    **inputs,
                    max_new_tokens=100,
                    temperature=0.7,
                    do_sample=True,
                    pad_token_id=tokenizer.eos_token_id
                )
            
            response = tokenizer.decode(outputs[0], skip_special_tokens=True)
            generated = response[len(prompt):].strip()
            
            print(f"   Output: {generated[:100]}...")
        
        print("\n✅ Test completato!")
        return True
        
    except Exception as e:
        print(f"❌ Errore test: {e}")
        return False

def main():
    """Training principale"""
    print("🧠 NEUROGLYPH LLM - Training QLoRA 4-bit")
    print("🎯 Qwen2.5-Coder-1.5B + Neuroglifi su Mac M2 8GB")
    print("=" * 70)
    
    # Verifica environment
    if not check_environment():
        print("❌ Environment non pronto")
        return False
    
    # Carica dataset
    examples = load_dataset("datasets/neuroglyph_train.jsonl")
    if not examples:
        print("❌ Dataset non caricato")
        return False
    
    # Setup modello
    model, tokenizer = setup_model_and_tokenizer()
    if model is None or tokenizer is None:
        print("❌ Setup modello fallito")
        return False
    
    # Prepara dati
    dataset = prepare_training_data(examples, tokenizer)
    if dataset is None:
        print("❌ Preparazione dati fallita")
        return False
    
    # Training
    success = train_model(model, tokenizer, dataset)
    if not success:
        print("❌ Training fallito")
        return False
    
    # Test modello
    test_trained_model()
    
    print("\n🎉 NEUROGLYPH LLM TRAINING COMPLETATO!")
    print("✅ Primo LLM pensante con neuroglifi pronto!")
    
    return True

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
