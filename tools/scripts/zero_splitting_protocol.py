#!/usr/bin/env python3
"""
NEUROGLYPH ZERO SPLITTING PROTOCOL
Garantisce che TUTTI i 9,236 simboli rimangano token atomici - ZERO SPLITTING TOLLERATO
"""

import json
import os
from pathlib import Path
from typing import Dict, List, Set, Tuple
from datetime import datetime

class ZeroSplittingProtocol:
    """Protocollo per garantire zero splitting dei simboli NEUROGLYPH."""
    
    def __init__(self):
        self.registry_path = "neuroglyph/core/neuroglyph_ULTIMATE_registry.json"
        self.tokenizer_output = "neuroglyph/training/zero_splitting_tokenizer"
        self.validation_log = "neuroglyph/logs/zero_splitting_validation.json"
        
        # CRITERI ZERO SPLITTING
        self.zero_splitting_criteria = {
            "max_tokens_per_symbol": 1,  # ASSOLUTO - MAI PIÙ DI 1
            "perfect_roundtrip": True,   # encode/decode deve essere identico
            "no_token_drift": True,      # mapping stabile durante training
            "emergency_rollback": True   # rollback automatico se violazione
        }
        
        self.validation_results = {
            "total_symbols": 0,
            "atomic_symbols": 0,
            "split_symbols": [],
            "failed_roundtrip": [],
            "encoding_errors": [],
            "zero_splitting_achieved": False
        }
    
    def load_ultimate_registry(self) -> List[Dict]:
        """Carica registry ULTIMATE con 9,236 simboli."""
        
        try:
            with open(self.registry_path, 'r', encoding='utf-8') as f:
                registry = json.load(f)
            
            symbols = registry.get('approved_symbols', [])
            print(f"✅ Loaded {len(symbols)} symbols from ULTIMATE registry")
            
            return symbols
            
        except Exception as e:
            print(f"❌ CRITICAL ERROR loading registry: {e}")
            return []
    
    def create_zero_splitting_tokenizer(self, symbols: List[Dict]) -> bool:
        """Crea tokenizer con GARANZIA zero splitting."""
        
        print("🔒 CREATING ZERO SPLITTING TOKENIZER")
        print("=" * 50)
        print("⚠️  CRITICAL: Every symbol MUST be exactly 1 token")
        print("⚠️  FAILURE TOLERANCE: ZERO")
        print()
        
        try:
            from transformers import AutoTokenizer
        except ImportError:
            print("❌ CRITICAL: transformers library required")
            print("   Install with: pip install transformers")
            return False
        
        # Carica tokenizer base
        base_model = "Qwen/Qwen2.5-Coder-1.5B-Instruct"
        print(f"📥 Loading base tokenizer: {base_model}")
        
        try:
            tokenizer = AutoTokenizer.from_pretrained(base_model)
            original_vocab_size = len(tokenizer.vocab)
            print(f"✅ Base tokenizer loaded: {original_vocab_size:,} tokens")
        except Exception as e:
            print(f"❌ CRITICAL ERROR loading base tokenizer: {e}")
            return False
        
        # Estrai simboli unici
        symbol_list = []
        seen_symbols = set()
        
        for symbol_data in symbols:
            symbol = symbol_data.get('symbol', '')
            if symbol and symbol not in seen_symbols:
                symbol_list.append(symbol)
                seen_symbols.add(symbol)
        
        print(f"📊 Unique symbols to add: {len(symbol_list):,}")
        
        # FASE CRITICA: Aggiungi simboli come special tokens
        print(f"\n🔒 CRITICAL PHASE: Adding symbols as special tokens...")
        print(f"⚠️  This phase determines success or failure")
        
        special_tokens_dict = {
            "additional_special_tokens": symbol_list
        }
        
        try:
            num_added = tokenizer.add_special_tokens(special_tokens_dict)
            new_vocab_size = len(tokenizer.vocab)
            
            print(f"✅ Added {num_added:,} special tokens")
            print(f"📊 New vocab size: {new_vocab_size:,}")
            print(f"📈 Increase: {new_vocab_size - original_vocab_size:,} tokens")
            
        except Exception as e:
            print(f"❌ CRITICAL ERROR adding special tokens: {e}")
            return False
        
        # VALIDAZIONE ZERO SPLITTING - FASE CRITICA
        print(f"\n🧪 ZERO SPLITTING VALIDATION - CRITICAL TEST")
        print(f"=" * 50)
        
        success = self.validate_zero_splitting(tokenizer, symbol_list)
        
        if not success:
            print(f"❌ ZERO SPLITTING VALIDATION FAILED")
            print(f"🚨 CRITICAL FAILURE - CANNOT PROCEED")
            return False
        
        # Salva tokenizer solo se validazione OK
        print(f"\n💾 Saving ZERO SPLITTING tokenizer...")
        
        try:
            os.makedirs(self.tokenizer_output, exist_ok=True)
            tokenizer.save_pretrained(self.tokenizer_output)
            
            # Salva stato locked
            locked_state = {
                "timestamp": datetime.now().isoformat(),
                "base_model": base_model,
                "original_vocab_size": original_vocab_size,
                "new_vocab_size": new_vocab_size,
                "symbols_added": len(symbol_list),
                "zero_splitting_validated": True,
                "validation_results": self.validation_results,
                "emergency_rollback_available": True
            }
            
            with open(f"{self.tokenizer_output}/zero_splitting_state.json", 'w') as f:
                json.dump(locked_state, f, indent=2)
            
            print(f"✅ ZERO SPLITTING tokenizer saved: {self.tokenizer_output}")
            
            return True
            
        except Exception as e:
            print(f"❌ CRITICAL ERROR saving tokenizer: {e}")
            return False
    
    def validate_zero_splitting(self, tokenizer, symbols: List[str]) -> bool:
        """Validazione CRITICA zero splitting."""
        
        print(f"🔍 Testing {len(symbols):,} symbols for zero splitting...")
        
        self.validation_results["total_symbols"] = len(symbols)
        
        atomic_count = 0
        split_count = 0
        
        for i, symbol in enumerate(symbols):
            # Test encoding
            try:
                tokens = tokenizer.encode(symbol, add_special_tokens=False)
                
                # CRITERIO CRITICO: Esattamente 1 token
                if len(tokens) == 1:
                    # Test roundtrip
                    decoded = tokenizer.decode(tokens, skip_special_tokens=False)
                    
                    if decoded == symbol:
                        atomic_count += 1
                        self.validation_results["atomic_symbols"] += 1
                    else:
                        self.validation_results["failed_roundtrip"].append({
                            "symbol": symbol,
                            "expected": symbol,
                            "decoded": decoded,
                            "token_id": tokens[0]
                        })
                        split_count += 1
                
                elif len(tokens) > 1:
                    # FALLIMENTO CRITICO: Simbolo diviso
                    self.validation_results["split_symbols"].append({
                        "symbol": symbol,
                        "token_count": len(tokens),
                        "tokens": tokens,
                        "decoded_parts": [tokenizer.decode([t]) for t in tokens]
                    })
                    split_count += 1
                
                else:
                    # Errore encoding
                    self.validation_results["encoding_errors"].append({
                        "symbol": symbol,
                        "error": "No tokens generated"
                    })
                    split_count += 1
                    
            except Exception as e:
                self.validation_results["encoding_errors"].append({
                    "symbol": symbol,
                    "error": str(e)
                })
                split_count += 1
            
            # Progress report
            if (i + 1) % 1000 == 0:
                progress = ((i + 1) / len(symbols)) * 100
                print(f"   Progress: {i + 1:,}/{len(symbols):,} ({progress:.1f}%) - Atomic: {atomic_count:,}, Split: {split_count:,}")
        
        # Risultati finali
        atomicity_percentage = (atomic_count / len(symbols)) * 100
        
        print(f"\n📊 ZERO SPLITTING VALIDATION RESULTS:")
        print(f"   Total symbols tested: {len(symbols):,}")
        print(f"   Atomic symbols (1 token): {atomic_count:,}")
        print(f"   Split symbols (>1 token): {split_count:,}")
        print(f"   Failed roundtrip: {len(self.validation_results['failed_roundtrip']):,}")
        print(f"   Encoding errors: {len(self.validation_results['encoding_errors']):,}")
        print(f"   Atomicity percentage: {atomicity_percentage:.2f}%")
        
        # CRITERIO SUCCESSO: 100% atomicità
        success = atomicity_percentage == 100.0
        self.validation_results["zero_splitting_achieved"] = success
        
        if success:
            print(f"\n🎊 ZERO SPLITTING ACHIEVED!")
            print(f"✅ ALL {atomic_count:,} symbols are atomic")
            print(f"✅ ZERO symbols split into subtokens")
            print(f"✅ Perfect tokenizer atomicity guaranteed")
        else:
            print(f"\n🚨 ZERO SPLITTING FAILED!")
            print(f"❌ {split_count:,} symbols are split into subtokens")
            print(f"❌ Atomicity: {atomicity_percentage:.2f}% (Required: 100.0%)")
            
            # Mostra primi simboli problematici
            if self.validation_results["split_symbols"]:
                print(f"\n🔍 First split symbols:")
                for split_symbol in self.validation_results["split_symbols"][:5]:
                    symbol = split_symbol["symbol"]
                    token_count = split_symbol["token_count"]
                    print(f"   '{symbol}' → {token_count} tokens")
        
        # Salva log validazione
        self.save_validation_log()
        
        return success
    
    def save_validation_log(self):
        """Salva log di validazione."""
        
        os.makedirs("neuroglyph/logs", exist_ok=True)
        
        with open(self.validation_log, 'w', encoding='utf-8') as f:
            json.dump(self.validation_results, f, indent=2, ensure_ascii=False)
        
        print(f"📝 Validation log saved: {self.validation_log}")
    
    def emergency_rollback_check(self) -> bool:
        """Verifica se è necessario emergency rollback."""
        
        if not self.validation_results["zero_splitting_achieved"]:
            print(f"\n🚨 EMERGENCY ROLLBACK REQUIRED")
            print(f"❌ Zero splitting not achieved")
            print(f"❌ Cannot proceed with training")
            print(f"❌ Tokenizer must be fixed before continuing")
            
            return True
        
        return False

def main():
    """Esegue protocollo zero splitting."""
    
    print("🧠 NEUROGLYPH ZERO SPLITTING PROTOCOL")
    print("=" * 60)
    print("🎯 OBJECTIVE: 100% atomic tokenization")
    print("🚨 FAILURE TOLERANCE: ZERO")
    print("⚠️  ANY symbol split = TOTAL FAILURE")
    print()
    
    protocol = ZeroSplittingProtocol()
    
    # 1. Carica simboli
    symbols = protocol.load_ultimate_registry()
    if not symbols:
        print("❌ CRITICAL: Cannot load symbols")
        return False
    
    # 2. Crea tokenizer zero splitting
    success = protocol.create_zero_splitting_tokenizer(symbols)
    
    # 3. Check emergency rollback
    if protocol.emergency_rollback_check():
        print(f"\n🚨 PROTOCOL FAILED - EMERGENCY ROLLBACK")
        return False
    
    if success:
        print(f"\n🎊 ZERO SPLITTING PROTOCOL SUCCESS!")
        print(f"✅ ALL {protocol.validation_results['atomic_symbols']:,} symbols atomic")
        print(f"✅ ZERO splitting guaranteed")
        print(f"✅ Ready for GOD MODE training")
        
        return True
    else:
        print(f"\n❌ ZERO SPLITTING PROTOCOL FAILED")
        print(f"🚨 CANNOT PROCEED WITH TRAINING")
        
        return False

if __name__ == "__main__":
    success = main()
    
    if success:
        print(f"\n🚀 NEXT: Proceed with NEUROGLYPH training")
        print(f"   Tokenizer: neuroglyph/training/zero_splitting_tokenizer")
        print(f"   Atomicity: 100% GUARANTEED")
    else:
        print(f"\n🔧 FIX TOKENIZER BEFORE PROCEEDING")
        print(f"   Review validation log for split symbols")
        print(f"   Implement alternative tokenization strategy")
