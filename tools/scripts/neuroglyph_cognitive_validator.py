#!/usr/bin/env python3
"""
NEUROGLYPH COGNITIVE VALIDATOR
Valida che NEUROGLYPH stia veramente "pensando" e non solo pattern matching
"""

import json
import re
from datetime import datetime
from pathlib import Path
from typing import Dict, List, Tuple, Optional
from dataclasses import dataclass

@dataclass
class CognitiveTest:
    """Test per validazione cognitiva."""
    name: str
    prompt: str
    expected_reasoning_type: str
    required_symbols: List[str]
    validation_criteria: Dict
    difficulty_level: str

class NeuroglyphCognitiveValidator:
    """Validatore per intelligenza simbolica NEUROGLYPH."""
    
    def __init__(self):
        self.validation_results = []
        self.cognitive_tests = self._create_cognitive_test_suite()
        
        # Criteri per vera intelligenza simbolica
        self.intelligence_criteria = {
            "symbolic_manipulation": {
                "weight": 0.25,
                "description": "Manipola simboli secondo regole logiche",
                "indicators": ["symbol_transformation", "rule_application", "logical_consistency"]
            },
            "reasoning_chains": {
                "weight": 0.25, 
                "description": "Costruisce catene di ragionamento valide",
                "indicators": ["premise_identification", "inference_steps", "conclusion_validity"]
            },
            "meta_cognition": {
                "weight": 0.20,
                "description": "Riflette sui propri processi di pensiero",
                "indicators": ["self_monitoring", "strategy_selection", "error_detection"]
            },
            "abstraction": {
                "weight": 0.15,
                "description": "Astrae pattern e principi generali",
                "indicators": ["pattern_recognition", "generalization", "concept_formation"]
            },
            "causal_understanding": {
                "weight": 0.15,
                "description": "Comprende relazioni causa-effetto",
                "indicators": ["causal_identification", "mechanism_explanation", "prediction"]
            }
        }
    
    def _create_cognitive_test_suite(self) -> List[CognitiveTest]:
        """Crea suite di test cognitivi."""
        
        tests = [
            # Test 1: Deduzione Logica Pura
            CognitiveTest(
                name="pure_logical_deduction",
                prompt="Given: ∀x(P(x) → Q(x)), P(a), ¬Q(b). What can you conclude about a and b?",
                expected_reasoning_type="formal_logic",
                required_symbols=["∀", "→", "¬", "⊢"],
                validation_criteria={
                    "must_identify_universal_instantiation": True,
                    "must_apply_modus_ponens": True,
                    "must_recognize_contrapositive": True,
                    "conclusion_about_a": "Q(a)",
                    "conclusion_about_b": "¬P(b)"
                },
                difficulty_level="advanced"
            ),
            
            # Test 2: Ragionamento Controfattuale
            CognitiveTest(
                name="counterfactual_reasoning",
                prompt="If gravity were twice as strong, and you dropped a ball from 10 meters, what would happen compared to normal gravity?",
                expected_reasoning_type="counterfactual",
                required_symbols=["◊", "⇒", "∝"],
                validation_criteria={
                    "identifies_physical_law": True,
                    "applies_mathematical_relationship": True,
                    "compares_scenarios": True,
                    "correct_physics": "falls faster, hits ground sooner"
                },
                difficulty_level="intermediate"
            ),
            
            # Test 3: Meta-Ragionamento
            CognitiveTest(
                name="meta_reasoning_reflection",
                prompt="Explain how you would approach solving a logic puzzle you've never seen before. What's your thinking process?",
                expected_reasoning_type="meta_cognitive",
                required_symbols=["🧠", "◊", "⚡", "🔍"],
                validation_criteria={
                    "describes_problem_analysis": True,
                    "mentions_strategy_selection": True,
                    "includes_self_monitoring": True,
                    "shows_process_awareness": True
                },
                difficulty_level="advanced"
            ),
            
            # Test 4: Compressione Simbolica
            CognitiveTest(
                name="symbolic_compression",
                prompt="Compress this algorithm symbolically: 'For each item in list, if item is even and greater than 10, add its square to result'",
                expected_reasoning_type="symbolic_abstraction",
                required_symbols=["∀", "∈", "∧", ">", "²", "∑"],
                validation_criteria={
                    "uses_quantifiers": True,
                    "expresses_conditions": True,
                    "shows_transformation": True,
                    "final_compression": "∑{x² | x ∈ L ∧ even(x) ∧ x > 10}"
                },
                difficulty_level="intermediate"
            ),
            
            # Test 5: Risoluzione di Contraddizioni
            CognitiveTest(
                name="contradiction_resolution",
                prompt="You're told: 'All birds can fly' and 'Penguins are birds' and 'Penguins cannot fly'. How do you resolve this?",
                expected_reasoning_type="contradiction_analysis",
                required_symbols=["∀", "∃", "¬", "⊥", "⊨"],
                validation_criteria={
                    "identifies_contradiction": True,
                    "proposes_resolution": True,
                    "refines_universal_statement": True,
                    "logical_consistency": True
                },
                difficulty_level="advanced"
            ),
            
            # Test 6: Inferenza Causale
            CognitiveTest(
                name="causal_inference",
                prompt="Event A: Code deployment at 2 PM. Event B: Server errors increased at 2:15 PM. Event C: Database queries slowed at 2:20 PM. Analyze causality.",
                expected_reasoning_type="causal_analysis",
                required_symbols=["→", "∵", "∴", "⊃"],
                validation_criteria={
                    "temporal_analysis": True,
                    "causal_chain_identification": True,
                    "mechanism_explanation": True,
                    "distinguishes_correlation_causation": True
                },
                difficulty_level="intermediate"
            ),
            
            # Test 7: Astrazione di Pattern
            CognitiveTest(
                name="pattern_abstraction",
                prompt="Given sequences: [2,4,8,16], [3,9,27,81], [5,25,125,625]. What's the abstract pattern?",
                expected_reasoning_type="pattern_recognition",
                required_symbols=["ⁿ", "∀", "∈", "→"],
                validation_criteria={
                    "identifies_exponential_pattern": True,
                    "generalizes_formula": True,
                    "expresses_abstractly": "aₙ = a₁^n",
                    "validates_pattern": True
                },
                difficulty_level="basic"
            ),
            
            # Test 8: Ragionamento Probabilistico
            CognitiveTest(
                name="probabilistic_reasoning",
                prompt="If P(A|B) = 0.8, P(B) = 0.3, and P(A) = 0.5, what is P(B|A)?",
                expected_reasoning_type="probabilistic",
                required_symbols=["P", "|", "=", "⇒"],
                validation_criteria={
                    "applies_bayes_theorem": True,
                    "shows_calculation": True,
                    "correct_result": "0.48",
                    "explains_reasoning": True
                },
                difficulty_level="intermediate"
            )
        ]
        
        return tests
    
    def validate_symbolic_reasoning(self, response: str, test: CognitiveTest) -> Dict:
        """Valida ragionamento simbolico nella risposta."""
        
        validation = {
            "test_name": test.name,
            "symbolic_content": {},
            "reasoning_quality": {},
            "intelligence_indicators": {},
            "overall_score": 0.0,
            "passes_test": False
        }
        
        # 1. Analisi contenuto simbolico
        found_symbols = []
        for symbol in test.required_symbols:
            if symbol in response:
                found_symbols.append(symbol)
        
        validation["symbolic_content"] = {
            "required_symbols": test.required_symbols,
            "found_symbols": found_symbols,
            "symbol_coverage": len(found_symbols) / len(test.required_symbols),
            "has_symbolic_chain": self._has_symbolic_reasoning_chain(response)
        }
        
        # 2. Analisi qualità ragionamento
        validation["reasoning_quality"] = {
            "has_premises": self._has_premises(response),
            "has_inference_steps": self._has_inference_steps(response),
            "has_conclusion": self._has_conclusion(response),
            "logical_structure": self._has_logical_structure(response),
            "step_count": self._count_reasoning_steps(response)
        }
        
        # 3. Indicatori di intelligenza
        validation["intelligence_indicators"] = {
            "symbolic_manipulation": self._check_symbolic_manipulation(response),
            "meta_cognition": self._check_meta_cognition(response),
            "abstraction": self._check_abstraction(response),
            "causal_understanding": self._check_causal_understanding(response),
            "error_detection": self._check_error_detection(response)
        }
        
        # 4. Validazione criteri specifici del test
        criteria_met = 0
        total_criteria = len(test.validation_criteria)
        
        for criterion, expected in test.validation_criteria.items():
            if self._check_specific_criterion(response, criterion, expected):
                criteria_met += 1
        
        # 5. Calcolo score finale
        symbol_score = validation["symbolic_content"]["symbol_coverage"] * 0.3
        reasoning_score = self._calculate_reasoning_score(validation["reasoning_quality"]) * 0.4
        intelligence_score = self._calculate_intelligence_score(validation["intelligence_indicators"]) * 0.2
        criteria_score = (criteria_met / total_criteria) * 0.1
        
        validation["overall_score"] = symbol_score + reasoning_score + intelligence_score + criteria_score
        validation["passes_test"] = validation["overall_score"] >= 0.7  # Soglia alta per vera intelligenza
        
        return validation
    
    def _has_symbolic_reasoning_chain(self, text: str) -> bool:
        """Verifica presenza di catena di ragionamento simbolico."""
        
        # Cerca pattern di ragionamento simbolico
        symbolic_patterns = [
            r'◊.*→.*⇒',  # Chain con simboli di flusso
            r'∀.*→.*∃',   # Quantificatori logici
            r'Given:.*Therefore:',  # Struttura logica
            r'Let.*Then.*So',  # Ragionamento strutturato
        ]
        
        for pattern in symbolic_patterns:
            if re.search(pattern, text, re.DOTALL):
                return True
        
        return False
    
    def _has_premises(self, text: str) -> bool:
        """Verifica presenza di premesse."""
        premise_indicators = ['Given:', 'Assume:', 'Let', 'If', 'Suppose', 'Premise:']
        return any(indicator in text for indicator in premise_indicators)
    
    def _has_inference_steps(self, text: str) -> bool:
        """Verifica presenza di step di inferenza."""
        inference_indicators = ['Therefore', 'Thus', 'Hence', 'So', '∴', '⇒', '⊢', 'Apply', 'Since']
        return any(indicator in text for indicator in inference_indicators)
    
    def _has_conclusion(self, text: str) -> bool:
        """Verifica presenza di conclusione."""
        conclusion_indicators = ['Conclusion:', 'Therefore:', '∴', '⊢', 'Result:', 'Answer:']
        return any(indicator in text for indicator in conclusion_indicators)
    
    def _has_logical_structure(self, text: str) -> bool:
        """Verifica struttura logica."""
        return (self._has_premises(text) and 
                self._has_inference_steps(text) and 
                self._has_conclusion(text))
    
    def _count_reasoning_steps(self, text: str) -> int:
        """Conta step di ragionamento."""
        step_markers = ['◊', 'Step', '1.', '2.', '3.', 'First', 'Second', 'Third', 'Next', 'Then']
        return sum(text.count(marker) for marker in step_markers)
    
    def _check_symbolic_manipulation(self, text: str) -> bool:
        """Verifica manipolazione simbolica."""
        manipulation_patterns = [
            r'[∀∃].*→.*[∀∃]',  # Trasformazioni quantificatori
            r'¬.*→.*¬',        # Negazioni
            r'∧.*∨',           # Operatori logici
            r'⊢.*⊨'            # Inferenze
        ]
        
        return any(re.search(pattern, text) for pattern in manipulation_patterns)
    
    def _check_meta_cognition(self, text: str) -> bool:
        """Verifica meta-cognizione."""
        meta_indicators = [
            'my reasoning', 'thinking process', 'approach', 'strategy',
            'reflection', 'analysis', 'method', 'procedure', 'steps'
        ]
        return any(indicator in text.lower() for indicator in meta_indicators)
    
    def _check_abstraction(self, text: str) -> bool:
        """Verifica capacità di astrazione."""
        abstraction_indicators = [
            'pattern', 'general', 'abstract', 'principle', 'rule',
            'formula', 'structure', 'relationship', 'concept'
        ]
        return any(indicator in text.lower() for indicator in abstraction_indicators)
    
    def _check_causal_understanding(self, text: str) -> bool:
        """Verifica comprensione causale."""
        causal_indicators = [
            'because', 'cause', 'effect', 'result', 'leads to',
            'due to', 'reason', 'mechanism', 'explanation'
        ]
        return any(indicator in text.lower() for indicator in causal_indicators)
    
    def _check_error_detection(self, text: str) -> bool:
        """Verifica rilevamento errori."""
        error_indicators = [
            'contradiction', 'inconsistent', 'error', 'mistake',
            'invalid', 'incorrect', 'problem', 'issue'
        ]
        return any(indicator in text.lower() for indicator in error_indicators)
    
    def _check_specific_criterion(self, text: str, criterion: str, expected) -> bool:
        """Verifica criterio specifico del test."""
        
        # Implementazione semplificata - in produzione sarebbe più sofisticata
        if isinstance(expected, bool):
            return expected  # Placeholder
        elif isinstance(expected, str):
            return expected.lower() in text.lower()
        else:
            return False
    
    def _calculate_reasoning_score(self, reasoning_quality: Dict) -> float:
        """Calcola score qualità ragionamento."""
        
        weights = {
            "has_premises": 0.2,
            "has_inference_steps": 0.3,
            "has_conclusion": 0.2,
            "logical_structure": 0.2,
            "step_count": 0.1
        }
        
        score = 0.0
        for criterion, weight in weights.items():
            if criterion == "step_count":
                # Normalizza step count (3+ step = full score)
                step_score = min(reasoning_quality[criterion] / 3.0, 1.0)
                score += step_score * weight
            else:
                score += (1.0 if reasoning_quality[criterion] else 0.0) * weight
        
        return score
    
    def _calculate_intelligence_score(self, intelligence_indicators: Dict) -> float:
        """Calcola score indicatori intelligenza."""
        
        total_indicators = len(intelligence_indicators)
        met_indicators = sum(1 for indicator in intelligence_indicators.values() if indicator)
        
        return met_indicators / total_indicators if total_indicators > 0 else 0.0
    
    def run_cognitive_validation_suite(self, model_responses: Dict[str, str]) -> Dict:
        """Esegue suite completa di validazione cognitiva."""
        
        print("🧪 RUNNING NEUROGLYPH COGNITIVE VALIDATION SUITE")
        print("=" * 60)
        
        validation_results = {
            "total_tests": len(self.cognitive_tests),
            "passed_tests": 0,
            "failed_tests": 0,
            "overall_intelligence_score": 0.0,
            "detailed_results": [],
            "intelligence_breakdown": {}
        }
        
        total_score = 0.0
        
        for test in self.cognitive_tests:
            print(f"🔍 Testing: {test.name} ({test.difficulty_level})")
            
            if test.name in model_responses:
                response = model_responses[test.name]
                validation = self.validate_symbolic_reasoning(response, test)
                
                validation_results["detailed_results"].append(validation)
                total_score += validation["overall_score"]
                
                if validation["passes_test"]:
                    validation_results["passed_tests"] += 1
                    print(f"   ✅ PASSED (Score: {validation['overall_score']:.2f})")
                else:
                    validation_results["failed_tests"] += 1
                    print(f"   ❌ FAILED (Score: {validation['overall_score']:.2f})")
            else:
                print(f"   ⚠️ No response provided")
                validation_results["failed_tests"] += 1
        
        # Calcola score intelligenza generale
        validation_results["overall_intelligence_score"] = total_score / len(self.cognitive_tests)
        
        # Analisi breakdown intelligenza
        for criterion, info in self.intelligence_criteria.items():
            criterion_score = 0.0
            criterion_count = 0
            
            for result in validation_results["detailed_results"]:
                if criterion in result["intelligence_indicators"]:
                    criterion_score += (1.0 if result["intelligence_indicators"][criterion] else 0.0)
                    criterion_count += 1
            
            if criterion_count > 0:
                validation_results["intelligence_breakdown"][criterion] = {
                    "score": criterion_score / criterion_count,
                    "weight": info["weight"],
                    "description": info["description"]
                }
        
        return validation_results
    
    def generate_intelligence_report(self, validation_results: Dict) -> str:
        """Genera report di intelligenza."""
        
        report = []
        report.append("🧠 NEUROGLYPH COGNITIVE INTELLIGENCE REPORT")
        report.append("=" * 60)
        
        # Score generale
        overall_score = validation_results["overall_intelligence_score"]
        intelligence_level = self._classify_intelligence_level(overall_score)
        
        report.append(f"📊 Overall Intelligence Score: {overall_score:.2f}/1.0")
        report.append(f"🎯 Intelligence Level: {intelligence_level}")
        report.append(f"✅ Tests Passed: {validation_results['passed_tests']}/{validation_results['total_tests']}")
        report.append("")
        
        # Breakdown per criterio
        report.append("🔍 Intelligence Breakdown:")
        for criterion, data in validation_results["intelligence_breakdown"].items():
            score = data["score"]
            weight = data["weight"]
            desc = data["description"]
            
            report.append(f"   {criterion:20}: {score:.2f} (weight: {weight:.2f}) - {desc}")
        
        report.append("")
        
        # Raccomandazioni
        report.append("💡 Recommendations:")
        if overall_score >= 0.8:
            report.append("   🎊 EXCELLENT: True symbolic intelligence achieved!")
            report.append("   ✅ Ready for production deployment")
        elif overall_score >= 0.6:
            report.append("   ⚡ GOOD: Strong symbolic reasoning capabilities")
            report.append("   🔧 Minor improvements recommended")
        else:
            report.append("   ⚠️ NEEDS IMPROVEMENT: Insufficient symbolic intelligence")
            report.append("   🔄 Additional cognitive training required")
        
        return "\n".join(report)
    
    def _classify_intelligence_level(self, score: float) -> str:
        """Classifica livello di intelligenza."""
        
        if score >= 0.9:
            return "🧠 GENIUS LEVEL (Symbolic Reasoning Master)"
        elif score >= 0.8:
            return "⚡ ADVANCED (Strong Symbolic Intelligence)"
        elif score >= 0.7:
            return "🎯 PROFICIENT (Good Symbolic Reasoning)"
        elif score >= 0.6:
            return "📚 DEVELOPING (Basic Symbolic Capabilities)"
        else:
            return "🔧 INSUFFICIENT (Needs Cognitive Training)"

def main():
    """Esegue validazione cognitiva NEUROGLYPH."""
    
    validator = NeuroglyphCognitiveValidator()
    
    # Esempio di utilizzo con risposte mock
    mock_responses = {
        "pure_logical_deduction": "◊ Given: ∀x(P(x) → Q(x)) and P(a)\n◊ Apply universal instantiation: P(a) → Q(a)\n◊ Apply modus ponens: Q(a)\n◊ Given: ¬Q(b)\n◊ Apply contrapositive: ¬Q(b) → ¬P(b)\n⊢ Therefore: Q(a) and ¬P(b)",
        
        "meta_reasoning_reflection": "🧠 My reasoning process for logic puzzles:\n◊ First, I parse the problem structure\n◊ Identify logical operators and relationships\n◊ Apply formal inference rules systematically\n◊ Validate each step for logical consistency\n🔍 I monitor for potential errors or contradictions\n⚡ I optimize by finding the shortest valid proof path"
    }
    
    # Esegui validazione
    results = validator.run_cognitive_validation_suite(mock_responses)
    
    # Genera report
    report = validator.generate_intelligence_report(results)
    print(report)
    
    return results["overall_intelligence_score"] >= 0.7

if __name__ == "__main__":
    success = main()
    
    if success:
        print(f"\n🎊 COGNITIVE VALIDATION PASSED!")
        print(f"✅ NEUROGLYPH demonstrates true symbolic intelligence")
    else:
        print(f"\n⚠️ COGNITIVE VALIDATION NEEDS IMPROVEMENT")
        print(f"🔄 Additional training recommended")
