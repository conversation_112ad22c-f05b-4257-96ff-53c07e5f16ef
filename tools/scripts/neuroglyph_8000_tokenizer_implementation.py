#!/usr/bin/env python3
"""
NEUROGLYPH 8000 TOKENIZER IMPLEMENTATION
Implementazione pratica per garantire atomicità di tutti gli 8000 simboli
"""

import json
import os
import hashlib
from pathlib import Path
from typing import Dict, List, Set, Tuple
from transformers import AutoTokenizer, AutoModel
from datetime import datetime

class NeuroglyphTokenizerImplementation:
    """Implementazione sicura tokenizer per 8000 simboli NEUROGLYPH."""
    
    def __init__(self):
        self.base_model = "Qwen/Qwen2.5-Coder-1.5B-Instruct"
        self.registry_path = "neuroglyph/core/locked_registry_godmode_v9.json"
        self.output_dir = "neuroglyph/training/neuroglyph_8000_tokenizer"
        self.backup_dir = "neuroglyph/training/tokenizer_backups"
        
        # Stato di sicurezza
        self.validation_results = {}
        self.locked_mappings = {}
        self.safety_checks_passed = False
        
    def load_current_symbols(self) -> List[Dict]:
        """Carica simboli attuali dal registry."""
        try:
            with open(self.registry_path, 'r', encoding='utf-8') as f:
                registry = json.load(f)
            
            symbols = registry.get('approved_symbols', [])
            print(f"✅ Caricati {len(symbols)} simboli dal registry")
            return symbols
            
        except Exception as e:
            print(f"❌ Errore caricamento registry: {e}")
            return []
    
    def validate_unicode_safety(self, symbols: List[Dict]) -> Dict:
        """Valida sicurezza Unicode per tutti i simboli."""
        
        print("🔍 Validating Unicode safety for all symbols...")
        
        validation_results = {
            "safe_symbols": [],
            "unsafe_symbols": [],
            "multi_codepoint": [],
            "rendering_issues": [],
            "total_validated": 0
        }
        
        # Blocchi Unicode sicuri per NEUROGLYPH
        safe_unicode_blocks = {
            (0x2000, 0x206F): "General Punctuation",
            (0x2070, 0x209F): "Superscripts and Subscripts", 
            (0x20A0, 0x20CF): "Currency Symbols",
            (0x2100, 0x214F): "Letterlike Symbols",
            (0x2190, 0x21FF): "Arrows",
            (0x2200, 0x22FF): "Mathematical Operators",
            (0x2300, 0x23FF): "Miscellaneous Technical",
            (0x2500, 0x257F): "Box Drawing",
            (0x2580, 0x259F): "Block Elements",
            (0x25A0, 0x25FF): "Geometric Shapes",
            (0x2600, 0x26FF): "Miscellaneous Symbols",
            (0x2700, 0x27BF): "Dingbats",
            (0x2900, 0x297F): "Supplemental Arrows-B",
            (0x2A00, 0x2AFF): "Supplemental Mathematical Operators"
        }
        
        for symbol_data in symbols:
            symbol = symbol_data.get('symbol', '')
            if not symbol:
                continue
                
            validation_results["total_validated"] += 1
            
            # Skip ng: symbols (sempre sicuri)
            if symbol.startswith('ng:'):
                validation_results["safe_symbols"].append(symbol_data)
                continue
            
            # Verifica Unicode
            try:
                # Test multi-codepoint
                if len(symbol) > 1:
                    codepoints = [ord(c) for c in symbol]
                    if len(codepoints) > 1:
                        validation_results["multi_codepoint"].append({
                            "symbol": symbol,
                            "codepoints": codepoints,
                            "reason": "Multiple codepoints"
                        })
                        continue
                
                # Test blocco Unicode
                codepoint = ord(symbol)
                is_safe = False
                
                for (start, end), block_name in safe_unicode_blocks.items():
                    if start <= codepoint <= end:
                        is_safe = True
                        symbol_data["unicode_block"] = block_name
                        break
                
                if is_safe:
                    validation_results["safe_symbols"].append(symbol_data)
                else:
                    validation_results["unsafe_symbols"].append({
                        "symbol": symbol,
                        "codepoint": codepoint,
                        "reason": f"Outside safe Unicode blocks (U+{codepoint:04X})"
                    })
                    
            except Exception as e:
                validation_results["rendering_issues"].append({
                    "symbol": symbol,
                    "error": str(e)
                })
        
        # Report risultati
        safe_count = len(validation_results["safe_symbols"])
        total_count = validation_results["total_validated"]
        safety_percentage = (safe_count / total_count) * 100 if total_count > 0 else 0
        
        print(f"📊 Unicode Safety Results:")
        print(f"   Safe symbols: {safe_count}/{total_count} ({safety_percentage:.1f}%)")
        print(f"   Unsafe symbols: {len(validation_results['unsafe_symbols'])}")
        print(f"   Multi-codepoint: {len(validation_results['multi_codepoint'])}")
        print(f"   Rendering issues: {len(validation_results['rendering_issues'])}")
        
        return validation_results
    
    def create_tokenizer_with_symbols(self, safe_symbols: List[Dict]) -> Tuple[AutoTokenizer, Dict]:
        """Crea tokenizer con simboli NEUROGLYPH integrati."""
        
        print("🔧 Creating NEUROGLYPH tokenizer with 8000 symbols...")
        
        # Carica tokenizer base
        tokenizer = AutoTokenizer.from_pretrained(self.base_model)
        original_vocab_size = len(tokenizer.vocab)
        
        print(f"📊 Base tokenizer vocab size: {original_vocab_size}")
        
        # Prepara simboli per integrazione
        symbols_to_add = []
        symbol_metadata = {}
        
        for symbol_data in safe_symbols:
            symbol = symbol_data.get('symbol', '')
            fallback = symbol_data.get('fallback', '')
            
            if symbol and symbol not in tokenizer.vocab:
                symbols_to_add.append(symbol)
                symbol_metadata[symbol] = {
                    "fallback": fallback,
                    "category": symbol_data.get('category', 'unknown'),
                    "score": symbol_data.get('score', 0),
                    "id": symbol_data.get('id', '')
                }
        
        print(f"📝 Adding {len(symbols_to_add)} new symbols to tokenizer...")
        
        # Aggiungi simboli come special tokens
        special_tokens_dict = {
            "additional_special_tokens": symbols_to_add
        }
        
        num_added = tokenizer.add_special_tokens(special_tokens_dict)
        new_vocab_size = len(tokenizer.vocab)
        
        print(f"✅ Added {num_added} special tokens")
        print(f"📊 New vocab size: {new_vocab_size} (increase: {new_vocab_size - original_vocab_size})")
        
        return tokenizer, symbol_metadata
    
    def validate_tokenizer_atomicity(self, tokenizer: AutoTokenizer, symbols: List[str]) -> Dict:
        """Valida che tutti i simboli siano token atomici."""
        
        print("🧪 Validating tokenizer atomicity for all symbols...")
        
        validation_results = {
            "atomic_symbols": [],
            "multi_token_symbols": [],
            "encoding_errors": [],
            "decoding_errors": [],
            "total_tested": 0
        }
        
        for symbol in symbols:
            validation_results["total_tested"] += 1
            
            try:
                # Test encoding
                tokens = tokenizer.encode(symbol, add_special_tokens=False)
                
                if len(tokens) == 1:
                    # Test decoding roundtrip
                    decoded = tokenizer.decode(tokens, skip_special_tokens=False)
                    
                    if decoded == symbol:
                        validation_results["atomic_symbols"].append({
                            "symbol": symbol,
                            "token_id": tokens[0],
                            "roundtrip_success": True
                        })
                    else:
                        validation_results["decoding_errors"].append({
                            "symbol": symbol,
                            "expected": symbol,
                            "decoded": decoded,
                            "token_id": tokens[0]
                        })
                else:
                    validation_results["multi_token_symbols"].append({
                        "symbol": symbol,
                        "token_count": len(tokens),
                        "tokens": tokens
                    })
                    
            except Exception as e:
                validation_results["encoding_errors"].append({
                    "symbol": symbol,
                    "error": str(e)
                })
        
        # Report risultati
        atomic_count = len(validation_results["atomic_symbols"])
        total_count = validation_results["total_tested"]
        atomicity_percentage = (atomic_count / total_count) * 100 if total_count > 0 else 0
        
        print(f"📊 Atomicity Validation Results:")
        print(f"   Atomic symbols: {atomic_count}/{total_count} ({atomicity_percentage:.1f}%)")
        print(f"   Multi-token symbols: {len(validation_results['multi_token_symbols'])}")
        print(f"   Encoding errors: {len(validation_results['encoding_errors'])}")
        print(f"   Decoding errors: {len(validation_results['decoding_errors'])}")
        
        # Salva mapping locked
        if atomicity_percentage >= 99.0:  # Soglia di successo
            self.locked_mappings = {
                item["symbol"]: item["token_id"] 
                for item in validation_results["atomic_symbols"]
            }
            print("✅ Tokenizer atomicity validation PASSED")
            self.safety_checks_passed = True
        else:
            print("❌ Tokenizer atomicity validation FAILED")
            self.safety_checks_passed = False
        
        return validation_results
    
    def save_tokenizer_with_backup(self, tokenizer: AutoTokenizer, metadata: Dict) -> bool:
        """Salva tokenizer con backup di sicurezza."""
        
        if not self.safety_checks_passed:
            print("❌ Cannot save tokenizer - safety checks failed")
            return False
        
        print("💾 Saving NEUROGLYPH tokenizer with backup...")
        
        # Crea directory
        os.makedirs(self.output_dir, exist_ok=True)
        os.makedirs(self.backup_dir, exist_ok=True)
        
        # Salva tokenizer principale
        tokenizer.save_pretrained(self.output_dir)
        
        # Salva metadata e locked state
        locked_state = {
            "timestamp": datetime.now().isoformat(),
            "tokenizer_path": self.base_model,
            "vocab_size": len(tokenizer.vocab),
            "symbols_count": len(self.locked_mappings),
            "locked_symbol_ids": self.locked_mappings,
            "symbol_metadata": metadata,
            "safety_validation_passed": self.safety_checks_passed,
            "checksum": self._calculate_checksum()
        }
        
        locked_state_path = os.path.join(self.output_dir, "neuroglyph_locked_state.json")
        with open(locked_state_path, 'w', encoding='utf-8') as f:
            json.dump(locked_state, f, indent=2, ensure_ascii=False)
        
        # Backup con timestamp
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        backup_path = os.path.join(self.backup_dir, f"tokenizer_backup_{timestamp}")
        tokenizer.save_pretrained(backup_path)
        
        backup_state_path = os.path.join(backup_path, "locked_state.json")
        with open(backup_state_path, 'w', encoding='utf-8') as f:
            json.dump(locked_state, f, indent=2, ensure_ascii=False)
        
        print(f"✅ Tokenizer saved to: {self.output_dir}")
        print(f"✅ Backup saved to: {backup_path}")
        print(f"✅ Locked state saved with {len(self.locked_mappings)} symbols")
        
        return True
    
    def _calculate_checksum(self) -> str:
        """Calcola checksum per validazione integrità."""
        symbols_data = json.dumps(self.locked_mappings, sort_keys=True)
        return hashlib.sha256(symbols_data.encode()).hexdigest()[:16]
    
    def run_complete_implementation(self) -> bool:
        """Esegue implementazione completa tokenizer 8000 simboli."""
        
        print("🚀 NEUROGLYPH 8000 TOKENIZER IMPLEMENTATION")
        print("=" * 60)
        
        # 1. Carica simboli
        symbols = self.load_current_symbols()
        if not symbols:
            print("❌ Failed to load symbols")
            return False
        
        # 2. Valida Unicode safety
        unicode_validation = self.validate_unicode_safety(symbols)
        safe_symbols = unicode_validation["safe_symbols"]
        
        if len(safe_symbols) < 7000:  # Soglia minima
            print(f"❌ Insufficient safe symbols: {len(safe_symbols)}")
            return False
        
        # 3. Crea tokenizer
        tokenizer, metadata = self.create_tokenizer_with_symbols(safe_symbols)
        
        # 4. Valida atomicità
        symbol_list = [s.get('symbol', '') for s in safe_symbols if s.get('symbol')]
        atomicity_validation = self.validate_tokenizer_atomicity(tokenizer, symbol_list)
        
        # 5. Salva se tutto OK
        if self.safety_checks_passed:
            success = self.save_tokenizer_with_backup(tokenizer, metadata)
            
            if success:
                print("\n🎊 IMPLEMENTATION SUCCESS!")
                print(f"✅ {len(safe_symbols)} simboli integrati")
                print(f"✅ {len(self.locked_mappings)} mapping atomici locked")
                print(f"✅ Tokenizer pronto per training NEUROGLYPH")
                return True
        
        print("\n❌ IMPLEMENTATION FAILED")
        return False

def main():
    """Esegue implementazione tokenizer NEUROGLYPH 8000."""
    implementation = NeuroglyphTokenizerImplementation()
    success = implementation.run_complete_implementation()
    
    if success:
        print("\n🎯 NEXT STEPS:")
        print("1. Test tokenizer con esempi reali")
        print("2. Integra nel training pipeline")
        print("3. Configura LoRA per nuovo vocab_size")
        print("4. Avvia training con monitoring")
    else:
        print("\n🔧 TROUBLESHOOTING NEEDED:")
        print("1. Verifica registry simboli")
        print("2. Controlla Unicode safety")
        print("3. Debug tokenizer integration")

if __name__ == "__main__":
    main()
