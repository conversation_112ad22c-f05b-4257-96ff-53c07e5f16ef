#!/usr/bin/env python3
"""
NEUROGLYPH READINESS CHECK
Verifica che tutti i componenti siano pronti per il training GOD MODE
"""

import json
import os
from pathlib import Path
from typing import Dict, List, Tuple
from datetime import datetime

class NeuroglyphReadinessChecker:
    """Checker per verificare readiness completa NEUROGLYPH."""
    
    def __init__(self):
        self.checks = {
            "registry": False,
            "tokenizer": False,
            "dataset": False,
            "trainer": False,
            "validator": False,
            "dependencies": False
        }
        
        self.critical_files = {
            "registry": "neuroglyph/core/neuroglyph_ULTIMATE_registry.json",
            "tokenizer": "neuroglyph/training/zero_splitting_tokenizer",
            "dataset": "neuroglyph/training/cognitive_dataset/neuroglyph_cognitive_unsloth.json",
            "trainer": "neuroglyph_cognitive_trainer.py",
            "validator": "neuroglyph_cognitive_validator.py",
            "zero_splitting": "zero_splitting_protocol.py"
        }
        
        self.readiness_score = 0.0
    
    def check_registry_ultimate(self) -> <PERSON><PERSON>[bool, str]:
        """Verifica registry ULTIMATE."""
        
        registry_path = self.critical_files["registry"]
        
        if not os.path.exists(registry_path):
            return False, f"❌ Registry not found: {registry_path}"
        
        try:
            with open(registry_path, 'r', encoding='utf-8') as f:
                registry = json.load(f)
            
            symbols = registry.get('approved_symbols', [])
            if len(symbols) < 9000:
                return False, f"❌ Insufficient symbols: {len(symbols)} < 9000"
            
            # Verifica qualità simboli
            high_quality = sum(1 for s in symbols if s.get('score', 0) >= 95.0)
            quality_ratio = high_quality / len(symbols)
            
            if quality_ratio < 0.8:
                return False, f"❌ Low quality ratio: {quality_ratio:.2f} < 0.8"
            
            return True, f"✅ Registry: {len(symbols)} symbols, {quality_ratio:.2f} quality"
            
        except Exception as e:
            return False, f"❌ Registry error: {e}"
    
    def check_tokenizer_ready(self) -> Tuple[bool, str]:
        """Verifica tokenizer zero splitting."""
        
        tokenizer_path = self.critical_files["tokenizer"]
        
        if not os.path.exists(tokenizer_path):
            return False, f"❌ Tokenizer not found: {tokenizer_path}"
        
        # Verifica file tokenizer
        required_files = [
            "tokenizer.json",
            "tokenizer_config.json", 
            "special_tokens_map.json",
            "zero_splitting_state.json"
        ]
        
        for file in required_files:
            if not os.path.exists(os.path.join(tokenizer_path, file)):
                return False, f"❌ Missing tokenizer file: {file}"
        
        # Verifica stato zero splitting
        try:
            state_file = os.path.join(tokenizer_path, "zero_splitting_state.json")
            with open(state_file, 'r') as f:
                state = json.load(f)
            
            if not state.get('zero_splitting_validated', False):
                return False, f"❌ Zero splitting not validated"
            
            symbols_added = state.get('symbols_added', 0)
            if symbols_added < 9000:
                return False, f"❌ Insufficient symbols in tokenizer: {symbols_added}"
            
            return True, f"✅ Tokenizer: {symbols_added} symbols, zero splitting validated"
            
        except Exception as e:
            return False, f"❌ Tokenizer state error: {e}"
    
    def check_cognitive_dataset(self) -> Tuple[bool, str]:
        """Verifica dataset cognitivo."""
        
        dataset_path = self.critical_files["dataset"]
        
        if not os.path.exists(dataset_path):
            return False, f"❌ Dataset not found: {dataset_path}"
        
        try:
            with open(dataset_path, 'r', encoding='utf-8') as f:
                dataset = json.load(f)
            
            if len(dataset) < 1000:
                return False, f"❌ Insufficient examples: {len(dataset)} < 1000"
            
            # Verifica qualità esempi
            symbolic_count = 0
            for example in dataset:
                if 'instruction' in example and 'output' in example:
                    text = example['instruction'] + example['output']
                    if any(symbol in text for symbol in ['◊', '⇒', '∀', '∃', '∧', '∨', '→']):
                        symbolic_count += 1
            
            symbolic_ratio = symbolic_count / len(dataset)
            if symbolic_ratio < 0.7:
                return False, f"❌ Low symbolic content: {symbolic_ratio:.2f} < 0.7"
            
            return True, f"✅ Dataset: {len(dataset)} examples, {symbolic_ratio:.2f} symbolic"
            
        except Exception as e:
            return False, f"❌ Dataset error: {e}"
    
    def check_training_components(self) -> Tuple[bool, str]:
        """Verifica componenti training."""
        
        trainer_path = self.critical_files["trainer"]
        validator_path = self.critical_files["validator"]
        
        if not os.path.exists(trainer_path):
            return False, f"❌ Trainer not found: {trainer_path}"
        
        if not os.path.exists(validator_path):
            return False, f"❌ Validator not found: {validator_path}"
        
        return True, f"✅ Training components: trainer + validator ready"
    
    def check_dependencies(self) -> Tuple[bool, str]:
        """Verifica dipendenze Python."""
        
        required_packages = [
            'torch',
            'transformers', 
            'datasets',
            'accelerate',
            'peft',
            'bitsandbytes'
        ]
        
        missing_packages = []
        
        for package in required_packages:
            try:
                __import__(package)
            except ImportError:
                missing_packages.append(package)
        
        if missing_packages:
            return False, f"❌ Missing packages: {', '.join(missing_packages)}"
        
        # Verifica Unsloth (opzionale ma raccomandato)
        try:
            __import__('unsloth')
            unsloth_status = "with Unsloth"
        except ImportError:
            unsloth_status = "without Unsloth (slower)"
        
        return True, f"✅ Dependencies: all required packages {unsloth_status}"
    
    def check_system_resources(self) -> Tuple[bool, str]:
        """Verifica risorse sistema."""
        
        import psutil
        
        # Memoria RAM
        memory = psutil.virtual_memory()
        available_gb = memory.available / (1024**3)
        
        if available_gb < 6:
            return False, f"❌ Insufficient RAM: {available_gb:.1f}GB < 6GB"
        
        # Spazio disco
        disk = psutil.disk_usage('.')
        free_gb = disk.free / (1024**3)
        
        if free_gb < 10:
            return False, f"❌ Insufficient disk space: {free_gb:.1f}GB < 10GB"
        
        # GPU/MPS (se disponibile)
        gpu_status = "CPU only"
        try:
            import torch
            if torch.cuda.is_available():
                gpu_status = f"CUDA GPU available"
            elif hasattr(torch.backends, 'mps') and torch.backends.mps.is_available():
                gpu_status = f"Apple MPS available"
        except:
            pass
        
        return True, f"✅ Resources: {available_gb:.1f}GB RAM, {free_gb:.1f}GB disk, {gpu_status}"
    
    def run_complete_readiness_check(self) -> Dict:
        """Esegue check completo di readiness."""
        
        print("🔍 NEUROGLYPH READINESS CHECK")
        print("=" * 50)
        
        results = {
            "timestamp": datetime.now().isoformat(),
            "checks": {},
            "overall_ready": False,
            "readiness_score": 0.0,
            "next_steps": []
        }
        
        # Esegui tutti i check
        check_functions = [
            ("Registry ULTIMATE", self.check_registry_ultimate),
            ("Tokenizer Zero Splitting", self.check_tokenizer_ready),
            ("Cognitive Dataset", self.check_cognitive_dataset),
            ("Training Components", self.check_training_components),
            ("Dependencies", self.check_dependencies),
            ("System Resources", self.check_system_resources)
        ]
        
        passed_checks = 0
        
        for check_name, check_func in check_functions:
            try:
                success, message = check_func()
                results["checks"][check_name] = {
                    "passed": success,
                    "message": message
                }
                
                print(f"{message}")
                
                if success:
                    passed_checks += 1
                else:
                    results["next_steps"].append(f"Fix: {check_name}")
                    
            except Exception as e:
                error_msg = f"❌ {check_name}: Error - {e}"
                results["checks"][check_name] = {
                    "passed": False,
                    "message": error_msg
                }
                print(error_msg)
                results["next_steps"].append(f"Fix: {check_name}")
        
        # Calcola readiness score
        results["readiness_score"] = passed_checks / len(check_functions)
        results["overall_ready"] = results["readiness_score"] >= 0.9  # 90% dei check devono passare
        
        print(f"\n📊 READINESS SUMMARY")
        print(f"Passed checks: {passed_checks}/{len(check_functions)}")
        print(f"Readiness score: {results['readiness_score']:.2f}")
        
        if results["overall_ready"]:
            print(f"🎊 NEUROGLYPH IS READY FOR GOD MODE TRAINING!")
            print(f"✅ All critical components verified")
            print(f"✅ Proceed with cognitive training")
        else:
            print(f"⚠️ NEUROGLYPH NOT READY - Issues found")
            print(f"🔧 Next steps:")
            for step in results["next_steps"]:
                print(f"   - {step}")
        
        return results
    
    def save_readiness_report(self, results: Dict) -> bool:
        """Salva report di readiness."""
        
        try:
            os.makedirs("neuroglyph/logs", exist_ok=True)
            
            report_file = f"neuroglyph/logs/readiness_check_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
            
            with open(report_file, 'w', encoding='utf-8') as f:
                json.dump(results, f, indent=2, ensure_ascii=False)
            
            print(f"📝 Readiness report saved: {report_file}")
            return True
            
        except Exception as e:
            print(f"❌ Error saving report: {e}")
            return False

def main():
    """Esegue check di readiness NEUROGLYPH."""
    
    checker = NeuroglyphReadinessChecker()
    
    # Esegui check completo
    results = checker.run_complete_readiness_check()
    
    # Salva report
    checker.save_readiness_report(results)
    
    return results["overall_ready"]

if __name__ == "__main__":
    ready = main()
    
    if ready:
        print(f"\n🚀 READY TO PROCEED WITH NEUROGLYPH TRAINING!")
        print(f"   Next: python3 neuroglyph_cognitive_trainer.py")
    else:
        print(f"\n🔧 RESOLVE ISSUES BEFORE PROCEEDING")
        print(f"   Check the readiness report for details")
