#!/usr/bin/env python3
"""
NEUROGLYPH SYMBOLIC REASONING INTERFACE
=====================================

Interface principale che collega il training LLM con il motore SOCRATE,
permettendo integrazione vera del ragionamento simbolico durante l'apprendimento.

Questo modulo:
1. Traduce testo naturale in rappresentazioni simboliche
2. <PERSON><PERSON><PERSON> inferenze usando SOCRATE engine
3. Valida logicamente le risposte generate
4. Fornisce feedback simbolico per il training
5. Implementa curriculum learning basato su complessità logica

Autore: NEUROGLYPH ULTRA Team
Data: 2025-01-26
"""

import re
import json
import time
import logging
from typing import Dict, List, Any, Optional, Tuple, Union
from dataclasses import dataclass
from enum import Enum
import sys
import os

# Add paths for NEUROGLYPH modules
current_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.append(current_dir)
sys.path.append(os.path.join(current_dir, 'neuroglyph'))
sys.path.append(os.path.join(current_dir, 'docs'))

# Import SOCRATE components with fallback
SOCRATE_AVAILABLE = False
print("⚠️ Using fallback mode for SOCRATE components")

# Define fallback classes
class ReasoningType(Enum):
    DEDUCTION = "deduction"
    INDUCTION = "induction"
    ABDUCTION = "abduction"
    CAUSAL = "causal"
    TEMPORAL = "temporal"

class SimulationResult(Enum):
    SUCCESS = "success"
    FAILURE = "failure"
    CONTRADICTION = "contradiction"
    UNCERTAINTY = "uncertainty"

class ReasoningDAG:
    """Fallback ReasoningDAG class."""
    def __init__(self):
        self.nodes = {}
        self.edges = []
        self.max_depth = 1

class SOCRATEPlanner:
    """Fallback SOCRATEPlanner class."""
    def plan_reasoning(self, premises, goal, reasoning_type):
        return ReasoningDAG()
    
    def create_dag_from_chains(self, chains):
        return ReasoningDAG()

class SOCRATELogicSimulator:
    """Fallback SOCRATELogicSimulator class."""
    def simulate_reasoning(self, dag):
        class MockResult:
            def __init__(self):
                self.result = SimulationResult.SUCCESS
                self.confidence = 0.8
        return MockResult()

# Setup logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class LogicalPattern(Enum):
    """Pattern logici riconosciuti nell'input."""
    MODUS_PONENS = "modus_ponens"          # If A then B, A ⊢ B
    MODUS_TOLLENS = "modus_tollens"        # If A then B, not B ⊢ not A
    DISJUNCTIVE_SYLLOGISM = "disj_syll"    # A or B, not A ⊢ B
    HYPOTHETICAL_SYLLOGISM = "hyp_syll"    # A→B, B→C ⊢ A→C
    UNIVERSAL_INSTANTIATION = "univ_inst"   # ∀x P(x), a ⊢ P(a)
    CONJUNCTION = "conjunction"             # A, B ⊢ A ∧ B
    CONTRADICTION = "contradiction"         # A, ¬A ⊢ ⊥

@dataclass
class SymbolicRepresentation:
    """Rappresentazione simbolica di un problema logico."""
    variables: Dict[str, str]          # Mapping simbolo → descrizione
    premises: List[str]                # Premesse in logica formale
    goal: Optional[str]                # Obiettivo da dimostrare
    logical_pattern: LogicalPattern    # Pattern logico identificato
    complexity_score: float            # Complessità del problema (0-1)
    reasoning_type: ReasoningType      # Tipo di ragionamento necessario

@dataclass
class ReasoningStep:
    """Singolo step nel processo di ragionamento."""
    step_number: int
    rule_applied: str                  # Regola logica applicata
    input_formulas: List[str]          # Formule in input
    output_formula: str                # Formula risultante
    justification: str                 # Giustificazione umana
    confidence: float                  # Confidenza nello step (0-1)
    is_valid: bool                     # Validità logica dello step

@dataclass
class SymbolicSolution:
    """Soluzione simbolica completa di un problema."""
    original_problem: str
    symbolic_repr: SymbolicRepresentation
    reasoning_steps: List[ReasoningStep]
    final_conclusion: str
    overall_confidence: float
    logical_validity: bool
    execution_time: float

class SymbolicReasoningInterface:
    """Interface principale per ragionamento simbolico NEUROGLYPH."""
    
    def __init__(self):
        """Inizializza l'interface con SOCRATE engine."""
        self.socrate_available = SOCRATE_AVAILABLE
        
        if self.socrate_available:
            try:
                self.planner = SOCRATEPlanner()
                self.logic_simulator = SOCRATELogicSimulator()
                print("🧠 SOCRATE Engine initialized successfully")
            except Exception as e:
                print(f"⚠️ Error initializing SOCRATE: {e}")
                self.socrate_available = False
        
        # Pattern recognition regex
        self._init_pattern_recognition()
        
        # Statistics
        self.problems_solved = 0
        self.logical_validity_rate = 0.0
        self.average_complexity = 0.0
        
    def _init_pattern_recognition(self):
        """Inizializza pattern per riconoscimento logico."""
        self.logical_patterns = {
            # Modus Ponens: If A then B, A
            LogicalPattern.MODUS_PONENS: [
                r"if\s+(.+?)\s+then\s+(.+?)[\.\,].*?(.+?)(?:\?|$)",
                r"(.+?)\s*→\s*(.+?).*?(.+?)(?:\?|$)",
            ],
            
            # Modus Tollens: If A then B, not B
            LogicalPattern.MODUS_TOLLENS: [
                r"if\s+(.+?)\s+then\s+(.+?).*?(?:not|isn't|doesn't)\s+(.+?)(?:\?|$)",
                r"(.+?)\s*→\s*(.+?).*?¬(.+?)(?:\?|$)",
            ],
            
            # Disjunctive Syllogism: A or B, not A
            LogicalPattern.DISJUNCTIVE_SYLLOGISM: [
                r"(?:either\s+)?(.+?)\s+or\s+(.+?).*?(?:not|isn't)\s+(.+?)(?:\?|$)",
                r"(.+?)\s*∨\s*(.+?).*?¬(.+?)(?:\?|$)",
            ],
            
            # Universal Instantiation: All X are Y, Z is X
            LogicalPattern.UNIVERSAL_INSTANTIATION: [
                r"all\s+(.+?)\s+are\s+(.+?).*?(.+?)\s+is\s+(.+?)(?:\?|$)",
                r"∀[a-z]\((.+?)\s*→\s*(.+?)\).*?(.+?)(?:\?|$)",
            ]
        }
    
    def parse_natural_language(self, text: str) -> SymbolicRepresentation:
        """Converte testo naturale in rappresentazione simbolica."""
        logger.info(f"Parsing natural language: {text[:100]}...")
        
        # Pulisci il testo
        cleaned_text = self._clean_text(text)
        
        # Identifica pattern logico
        pattern, matches = self._identify_logical_pattern(cleaned_text)
        
        # Estrai variabili e premesse
        variables = self._extract_variables(cleaned_text, pattern, matches)
        premises = self._extract_premises(cleaned_text, pattern, matches)
        goal = self._extract_goal(cleaned_text)
        
        # Determina tipo di ragionamento
        reasoning_type = self._determine_reasoning_type(pattern)
        
        # Calcola complessità
        complexity = self._calculate_complexity(premises, variables)
        
        symbolic_repr = SymbolicRepresentation(
            variables=variables,
            premises=premises,
            goal=goal,
            logical_pattern=pattern,
            complexity_score=complexity,
            reasoning_type=reasoning_type
        )
        
        logger.info(f"Parsed symbolic representation: {len(premises)} premises, complexity {complexity:.2f}")
        return symbolic_repr
    
    def _clean_text(self, text: str) -> str:
        """Pulisce e normalizza il testo."""
        # Rimuovi tags HTML/markdown
        text = re.sub(r'<[^>]+>', '', text)
        text = re.sub(r'\*+', '', text)
        
        # Normalizza spazi
        text = re.sub(r'\s+', ' ', text).strip()
        
        # Normalizza punteggiatura
        text = re.sub(r'([.!?])\s*', r'\1 ', text)
        
        return text.lower()
    
    def _identify_logical_pattern(self, text: str) -> Tuple[LogicalPattern, Optional[re.Match]]:
        """Identifica il pattern logico nel testo."""
        for pattern, regexes in self.logical_patterns.items():
            for regex in regexes:
                match = re.search(regex, text, re.IGNORECASE | re.DOTALL)
                if match:
                    logger.debug(f"Identified pattern: {pattern.value}")
                    return pattern, match
        
        # Default pattern se non riconosciuto
        logger.warning("No specific logical pattern identified, using deduction")
        return LogicalPattern.MODUS_PONENS, None
    
    def _extract_variables(self, text: str, pattern: LogicalPattern, matches: Optional[re.Match]) -> Dict[str, str]:
        """Estrae variabili logiche dal testo."""
        variables = {}
        
        if not matches:
            # Estrazione generica
            words = text.split()
            for i, word in enumerate(words):
                if word in ['rain', 'rains', 'wet', 'secure', 'vulnerabilities', 'mortal', 'human']:
                    var_name = word[0].upper()
                    if var_name not in variables:
                        variables[var_name] = word
            return variables
        
        # Estrazione basata su pattern
        if pattern == LogicalPattern.MODUS_PONENS:
            if len(matches.groups()) >= 2:
                variables['A'] = matches.group(1).strip()
                variables['B'] = matches.group(2).strip()
        
        elif pattern == LogicalPattern.UNIVERSAL_INSTANTIATION:
            if len(matches.groups()) >= 3:
                variables['P'] = matches.group(1).strip()
                variables['Q'] = matches.group(2).strip()
                variables['a'] = matches.group(3).strip()
        
        return variables
    
    def _extract_premises(self, text: str, pattern: LogicalPattern, matches: Optional[re.Match]) -> List[str]:
        """Estrae premesse logiche dal testo."""
        premises = []
        
        # Dividi in frasi
        sentences = re.split(r'[.!]', text)
        
        for sentence in sentences:
            sentence = sentence.strip()
            if not sentence or sentence.endswith('?'):
                continue
            
            # Converti in forma logica
            logical_form = self._convert_to_logical_form(sentence, pattern)
            if logical_form:
                premises.append(logical_form)
        
        return premises
    
    def _convert_to_logical_form(self, sentence: str, pattern: LogicalPattern) -> Optional[str]:
        """Converte frase in forma logica."""
        if 'if' in sentence and 'then' in sentence:
            # Implicazione: if A then B → A → B
            match = re.search(r'if\s+(.+?)\s+then\s+(.+)', sentence)
            if match:
                return f"{match.group(1).strip()} → {match.group(2).strip()}"
        
        elif 'all' in sentence and 'are' in sentence:
            # Quantificazione universale: All A are B → ∀x(A(x) → B(x))
            match = re.search(r'all\s+(.+?)\s+are\s+(.+)', sentence)
            if match:
                return f"∀x({match.group(1).strip()}(x) → {match.group(2).strip()}(x))"
        
        elif 'or' in sentence:
            # Disgiunzione: A or B → A ∨ B
            parts = sentence.split(' or ')
            if len(parts) == 2:
                return f"{parts[0].strip()} ∨ {parts[1].strip()}"
        
        elif 'not' in sentence or "isn't" in sentence or "doesn't" in sentence:
            # Negazione: not A → ¬A
            if 'not' in sentence:
                content = sentence.replace('not', '').strip()
                return f"¬{content}"
        
        # Predicato semplice
        return sentence
    
    def _extract_goal(self, text: str) -> Optional[str]:
        """Estrae l'obiettivo (domanda) dal testo."""
        # Cerca frasi che terminano con ?
        questions = re.findall(r'([^.!]*\?)', text)
        if questions:
            return questions[-1].strip()
        return None
    
    def _determine_reasoning_type(self, pattern: LogicalPattern) -> ReasoningType:
        """Determina il tipo di ragionamento dal pattern."""
        pattern_to_reasoning = {
            LogicalPattern.MODUS_PONENS: ReasoningType.DEDUCTION,
            LogicalPattern.MODUS_TOLLENS: ReasoningType.DEDUCTION,
            LogicalPattern.DISJUNCTIVE_SYLLOGISM: ReasoningType.DEDUCTION,
            LogicalPattern.HYPOTHETICAL_SYLLOGISM: ReasoningType.DEDUCTION,
            LogicalPattern.UNIVERSAL_INSTANTIATION: ReasoningType.DEDUCTION,
            LogicalPattern.CONJUNCTION: ReasoningType.DEDUCTION,
            LogicalPattern.CONTRADICTION: ReasoningType.DEDUCTION,
        }
        
        return pattern_to_reasoning.get(pattern, ReasoningType.DEDUCTION)
    
    def _calculate_complexity(self, premises: List[str], variables: Dict[str, str]) -> float:
        """Calcola complessità del problema logico."""
        # Fattori di complessità
        num_premises = len(premises)
        num_variables = len(variables)
        
        # Analizza operatori logici
        logical_operators = 0
        for premise in premises:
            logical_operators += premise.count('→') + premise.count('∧') + premise.count('∨')
            logical_operators += premise.count('¬') + premise.count('∀') + premise.count('∃')
        
        # Formula complessità (normalizzata 0-1)
        complexity = (num_premises * 0.3 + num_variables * 0.2 + logical_operators * 0.5) / 10.0
        
        return min(complexity, 1.0)
    
    def solve_symbolically(self, symbolic_repr: SymbolicRepresentation) -> SymbolicSolution:
        """Risolve problema usando ragionamento simbolico SOCRATE."""
        import time
        start_time = time.time()
        
        logger.info(f"Solving problem with {len(symbolic_repr.premises)} premises")
        
        if self.socrate_available:
            return self._solve_with_socrate(symbolic_repr, start_time)
        else:
            return self._solve_with_fallback(symbolic_repr, start_time)
    
    def _solve_with_socrate(self, symbolic_repr: SymbolicRepresentation, start_time: float) -> SymbolicSolution:
        """Risolve usando SOCRATE engine."""
        try:
            # Crea DAG di ragionamento
            reasoning_dag = self.planner.plan_reasoning(
                premises=symbolic_repr.premises,
                goal=symbolic_repr.goal or "conclusion",
                reasoning_type=symbolic_repr.reasoning_type
            )
            
            # Simula esecuzione
            simulation = self.logic_simulator.simulate_reasoning(reasoning_dag)
            
            # Estrai steps di ragionamento
            reasoning_steps = self._extract_reasoning_steps(reasoning_dag, simulation)
            
            # Genera conclusione
            conclusion = self._generate_conclusion(reasoning_steps, symbolic_repr)
            
            execution_time = time.time() - start_time
            
            solution = SymbolicSolution(
                original_problem=f"Problem with {len(symbolic_repr.premises)} premises",
                symbolic_repr=symbolic_repr,
                reasoning_steps=reasoning_steps,
                final_conclusion=conclusion,
                overall_confidence=getattr(simulation, 'confidence', 0.8),
                logical_validity=(simulation.result == SimulationResult.SUCCESS),
                execution_time=execution_time
            )
            
            self._update_statistics(solution)
            return solution
            
        except Exception as e:
            logger.error(f"Error in SOCRATE solving: {e}")
            return self._solve_with_fallback(symbolic_repr, start_time)
    
    def _solve_with_fallback(self, symbolic_repr: SymbolicRepresentation, start_time: float) -> SymbolicSolution:
        """Risolve con logica di fallback."""
        logger.info("Using fallback symbolic reasoning")
        
        # Genera steps di ragionamento semplificati
        reasoning_steps = []
        
        # Step 1: Identificazione variabili
        if symbolic_repr.variables:
            step = ReasoningStep(
                step_number=1,
                rule_applied="Variable Assignment",
                input_formulas=[],
                output_formula=f"Variables: {', '.join(f'{k} = {v}' for k, v in symbolic_repr.variables.items())}",
                justification="Assign logical variables to concepts",
                confidence=0.9,
                is_valid=True
            )
            reasoning_steps.append(step)
        
        # Step 2: Premesse
        for i, premise in enumerate(symbolic_repr.premises):
            step = ReasoningStep(
                step_number=len(reasoning_steps) + 1,
                rule_applied="Premise Statement",
                input_formulas=[],
                output_formula=f"Given: {premise}",
                justification=f"Premise {i+1} from problem statement",
                confidence=0.95,
                is_valid=True
            )
            reasoning_steps.append(step)
        
        # Step 3: Applicazione regola logica
        if symbolic_repr.logical_pattern == LogicalPattern.MODUS_PONENS:
            rule = "Modus Ponens"
            conclusion = "Conclusion follows from premises"
        elif symbolic_repr.logical_pattern == LogicalPattern.MODUS_TOLLENS:
            rule = "Modus Tollens"
            conclusion = "Negation of antecedent follows"
        else:
            rule = "Logical Inference"
            conclusion = "Conclusion derived from premises"
        
        step = ReasoningStep(
            step_number=len(reasoning_steps) + 1,
            rule_applied=rule,
            input_formulas=symbolic_repr.premises,
            output_formula=conclusion,
            justification=f"Apply {rule} rule to premises",
            confidence=0.8,
            is_valid=True
        )
        reasoning_steps.append(step)
        
        execution_time = time.time() - start_time
        
        solution = SymbolicSolution(
            original_problem=f"Problem with {len(symbolic_repr.premises)} premises",
            symbolic_repr=symbolic_repr,
            reasoning_steps=reasoning_steps,
            final_conclusion=conclusion,
            overall_confidence=0.8,
            logical_validity=True,
            execution_time=execution_time
        )
        
        self._update_statistics(solution)
        return solution
    
    def _extract_reasoning_steps(self, dag, simulation) -> List[ReasoningStep]:
        """Estrae steps di ragionamento dal DAG SOCRATE."""
        steps = []
        
        # Placeholder - da implementare con vero DAG
        step = ReasoningStep(
            step_number=1,
            rule_applied="SOCRATE Inference",
            input_formulas=["premise1", "premise2"],
            output_formula="conclusion",
            justification="Applied SOCRATE reasoning",
            confidence=0.9,
            is_valid=True
        )
        steps.append(step)
        
        return steps
    
    def _generate_conclusion(self, reasoning_steps: List[ReasoningStep], symbolic_repr: SymbolicRepresentation) -> str:
        """Genera conclusione finale dal ragionamento."""
        if reasoning_steps:
            last_step = reasoning_steps[-1]
            return last_step.output_formula
        return "No conclusion reached"
    
    def _update_statistics(self, solution: SymbolicSolution):
        """Aggiorna statistiche di performance."""
        self.problems_solved += 1
        
        # Update validity rate
        if solution.logical_validity:
            valid_count = (self.logical_validity_rate * (self.problems_solved - 1)) + 1
        else:
            valid_count = self.logical_validity_rate * (self.problems_solved - 1)
        
        self.logical_validity_rate = valid_count / self.problems_solved
        
        # Update complexity
        total_complexity = (self.average_complexity * (self.problems_solved - 1)) + solution.symbolic_repr.complexity_score
        self.average_complexity = total_complexity / self.problems_solved
    
    def validate_reasoning_output(self, output: str, expected_solution: SymbolicSolution) -> Dict[str, Any]:
        """Valida output del modello contro soluzione simbolica."""
        validation_result = {
            "symbolic_chain_present": False,
            "logical_operators_used": False,
            "correct_conclusion": False,
            "reasoning_steps_count": 0,
            "logic_score": 0.0,
            "consistency_score": 0.0
        }
        
        # Verifica presenza catena simbolica
        if '◊' in output:
            validation_result["symbolic_chain_present"] = True
        
        # Verifica operatori logici
        logical_ops = ['→', '∧', '∨', '¬', '∀', '∃', '⊢']
        if any(op in output for op in logical_ops):
            validation_result["logical_operators_used"] = True
        
        # Conta steps di ragionamento
        validation_result["reasoning_steps_count"] = output.count('◊')
        
        # Verifica conclusione
        if expected_solution.final_conclusion.lower() in output.lower():
            validation_result["correct_conclusion"] = True
        
        # Calcola logic score
        score = 0.0
        if validation_result["symbolic_chain_present"]:
            score += 0.3
        if validation_result["logical_operators_used"]:
            score += 0.3
        if validation_result["correct_conclusion"]:
            score += 0.4
        
        validation_result["logic_score"] = score
        validation_result["consistency_score"] = score * expected_solution.overall_confidence
        
        return validation_result
    
    def get_curriculum_difficulty(self, problem: str) -> float:
        """Calcola difficoltà per curriculum learning."""
        symbolic_repr = self.parse_natural_language(problem)
        return symbolic_repr.complexity_score
    
    def generate_symbolic_feedback(self, model_output: str, target_output: str) -> Dict[str, Any]:
        """Genera feedback simbolico per training."""
        # Parse target per ottenere soluzione attesa
        target_symbolic = self.parse_natural_language(target_output)
        target_solution = self.solve_symbolically(target_symbolic)
        
        # Valida output del modello
        validation = self.validate_reasoning_output(model_output, target_solution)
        
        # Genera feedback strutturato
        feedback = {
            "symbolic_accuracy": validation["logic_score"],
            "reasoning_completeness": min(validation["reasoning_steps_count"] / 3, 1.0),
            "logical_consistency": validation["consistency_score"],
            "curriculum_difficulty": target_symbolic.complexity_score,
            "improvement_suggestions": []
        }
        
        # Suggerimenti di miglioramento
        if not validation["symbolic_chain_present"]:
            feedback["improvement_suggestions"].append("Add symbolic reasoning chain with ◊ markers")
        
        if not validation["logical_operators_used"]:
            feedback["improvement_suggestions"].append("Use formal logical operators (→, ∧, ∨, ¬)")
        
        if not validation["correct_conclusion"]:
            feedback["improvement_suggestions"].append("Verify logical conclusion follows from premises")
        
        return feedback
    
    def get_statistics(self) -> Dict[str, Any]:
        """Ritorna statistiche di performance."""
        return {
            "problems_solved": self.problems_solved,
            "logical_validity_rate": self.logical_validity_rate,
            "average_complexity": self.average_complexity,
            "socrate_available": self.socrate_available
        }

# Test function
def test_symbolic_interface():
    """Test dell'interface simbolica."""
    print("🧪 Testing NEUROGLYPH Symbolic Reasoning Interface")
    print("=" * 50)
    
    interface = SymbolicReasoningInterface()
    
    # Test problems
    test_problems = [
        "If it rains, the ground gets wet. The ground is not wet. Did it rain?",
        "All humans are mortal. Socrates is human. Is Socrates mortal?",
        "Either the system is secure or it has vulnerabilities. It's not secure. Does it have vulnerabilities?"
    ]
    
    for i, problem in enumerate(test_problems):
        print(f"\n🔍 Test Problem {i+1}: {problem}")
        
        # Parse to symbolic
        symbolic_repr = interface.parse_natural_language(problem)
        print(f"  Variables: {symbolic_repr.variables}")
        print(f"  Premises: {symbolic_repr.premises}")
        print(f"  Pattern: {symbolic_repr.logical_pattern.value}")
        print(f"  Complexity: {symbolic_repr.complexity_score:.2f}")
        
        # Solve symbolically
        solution = interface.solve_symbolically(symbolic_repr)
        print(f"  Steps: {len(solution.reasoning_steps)}")
        print(f"  Conclusion: {solution.final_conclusion}")
        print(f"  Valid: {solution.logical_validity}")
        print(f"  Confidence: {solution.overall_confidence:.2f}")
    
    # Statistics
    stats = interface.get_statistics()
    print(f"\n📊 Final Statistics:")
    print(f"  Problems solved: {stats['problems_solved']}")
    print(f"  Validity rate: {stats['logical_validity_rate']:.2%}")
    print(f"  Avg complexity: {stats['average_complexity']:.2f}")
    print(f"  SOCRATE available: {stats['socrate_available']}")

if __name__ == "__main__":
    test_symbolic_interface()
