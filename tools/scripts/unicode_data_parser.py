#!/usr/bin/env python3
"""
NEUROGLYPH UNICODE DATA PARSER
Estrae simboli semanticamente ricchi da UnicodeData.txt per NEUROGLYPH 8000+
"""

import json
import requests
import re
import hashlib
from datetime import datetime
from typing import Dict, List, Set, Tuple
from collections import Counter

class UnicodeDataParser:
    """Parser per UnicodeData.txt ottimizzato per NEUROGLYPH."""
    
    def __init__(self):
        self.unicode_data_url = "https://www.unicode.org/Public/16.0.0/ucd/UnicodeData.txt"
        self.output_path = "neuroglyph/core/unicode_symbols_extracted.json"
        
        # Categorie Unicode rilevanti per NEUROGLYPH
        self.relevant_categories = {
            'Sm': 'Mathematical Symbols',      # ∀, ∃, ∑, ∫, ≡, ⊂, ⇒
            'So': 'Other Symbols',             # ⚡, 🧠, ⚗️, ⚙️, ⚖️
            'Sc': 'Currency Symbols',          # $, €, ¥, ₿ (per economics)
            'Sk': 'Modifier Symbols',          # ˆ, ˜, ´ (per linguistics)
            'Pd': 'Dash Punctuation',          # —, –, ⸺ (per structure)
            'Po': 'Other Punctuation',         # ‽, ⁇, ⁈ (per logic)
        }
        
        # Domini NEUROGLYPH per mapping semantico
        self.neuroglyph_domains = {
            'logic': ['all', 'exist', 'not', 'and', 'or', 'implies', 'equivalent', 'turnstile', 'entail'],
            'math': ['sum', 'integral', 'product', 'union', 'intersection', 'subset', 'element', 'infinity'],
            'ai': ['neural', 'network', 'gradient', 'tensor', 'embedding', 'attention', 'transformer'],
            'cognitive': ['brain', 'mind', 'consciousness', 'memory', 'attention', 'perception', 'thought'],
            'quantum': ['superposition', 'entanglement', 'measurement', 'gate', 'qubit', 'coherence'],
            'code': ['function', 'lambda', 'closure', 'recursion', 'iteration', 'composition', 'abstraction'],
            'structure': ['hierarchy', 'tree', 'graph', 'node', 'edge', 'path', 'cycle', 'component'],
            'flow': ['sequence', 'parallel', 'branch', 'merge', 'loop', 'condition', 'exception'],
            'meta': ['reflection', 'introspection', 'metacognition', 'abstraction', 'composition']
        }
        
        self.extracted_symbols = []
        self.stats = {
            'total_processed': 0,
            'relevant_symbols': 0,
            'mapped_symbols': 0,
            'high_quality_symbols': 0
        }
    
    def download_unicode_data(self) -> List[str]:
        """Scarica UnicodeData.txt da Unicode.org."""
        
        print(f"📥 Downloading UnicodeData.txt from Unicode.org...")
        
        try:
            response = requests.get(self.unicode_data_url, timeout=30)
            response.raise_for_status()
            
            lines = response.text.strip().split('\n')
            print(f"✅ Downloaded {len(lines)} Unicode entries")
            
            return lines
            
        except Exception as e:
            print(f"❌ Error downloading Unicode data: {e}")
            return []
    
    def parse_unicode_line(self, line: str) -> Dict:
        """Parsa una linea di UnicodeData.txt."""
        
        fields = line.split(';')
        if len(fields) < 15:
            return None
        
        return {
            'codepoint': fields[0],
            'name': fields[1],
            'category': fields[2],
            'combining_class': fields[3],
            'bidi_class': fields[4],
            'decomposition': fields[5],
            'decimal_digit': fields[6],
            'digit': fields[7],
            'numeric': fields[8],
            'mirrored': fields[9],
            'unicode_1_name': fields[10],
            'iso_comment': fields[11],
            'uppercase': fields[12],
            'lowercase': fields[13],
            'titlecase': fields[14]
        }
    
    def is_relevant_symbol(self, unicode_entry: Dict) -> bool:
        """Verifica se il simbolo è rilevante per NEUROGLYPH."""
        
        category = unicode_entry.get('category', '')
        name = unicode_entry.get('name', '').lower()
        
        # Filtra per categorie rilevanti
        if category not in self.relevant_categories:
            return False
        
        # Escludi simboli decorativi o problematici
        excluded_patterns = [
            'combining',
            'variation',
            'selector',
            'private',
            'surrogate',
            'noncharacter',
            'control',
            'format'
        ]
        
        for pattern in excluded_patterns:
            if pattern in name:
                return False
        
        # Includi simboli semanticamente ricchi
        semantic_patterns = [
            'for all', 'there exists', 'not sign', 'logical',
            'summation', 'integral', 'product', 'union', 'intersection',
            'subset', 'element', 'infinity', 'partial', 'nabla',
            'implies', 'equivalent', 'turnstile', 'entails',
            'arrow', 'double', 'triple', 'quadruple',
            'brain', 'gear', 'lightning', 'scales', 'atom'
        ]
        
        for pattern in semantic_patterns:
            if pattern in name:
                return True
        
        # Includi simboli matematici e logici generici
        if category == 'Sm' and len(name) > 3:  # Evita simboli troppo generici
            return True
        
        return False
    
    def map_to_neuroglyph_domain(self, unicode_entry: Dict) -> Tuple[str, str, float]:
        """Mappa simbolo Unicode a dominio NEUROGLYPH."""
        
        name = unicode_entry.get('name', '').lower()
        category = unicode_entry.get('category', '')
        
        # Mapping esplicito per simboli noti
        explicit_mappings = {
            'for all': ('logic', 'universal_quantifier', 98.0),
            'there exists': ('logic', 'existential_quantifier', 98.0),
            'not sign': ('logic', 'negation', 97.0),
            'logical and': ('logic', 'conjunction', 97.0),
            'logical or': ('logic', 'disjunction', 97.0),
            'n-ary summation': ('math', 'summation', 98.0),
            'integral': ('math', 'integration', 98.0),
            'n-ary product': ('math', 'product', 97.0),
            'union': ('math', 'set_union', 96.0),
            'intersection': ('math', 'set_intersection', 96.0),
            'subset': ('math', 'subset_relation', 96.0),
            'element of': ('math', 'membership', 96.0),
            'infinity': ('math', 'infinity_concept', 97.0),
            'partial differential': ('math', 'partial_derivative', 97.0),
            'nabla': ('math', 'gradient_operator', 97.0),
            'brain': ('cognitive', 'consciousness', 95.0),
            'gear': ('structure', 'mechanism', 94.0),
            'lightning': ('flow', 'energy_transfer', 94.0),
            'scales': ('meta', 'balance_judgment', 94.0),
            'atom': ('quantum', 'atomic_structure', 95.0)
        }
        
        # Cerca mapping esplicito
        for pattern, (domain, concept, score) in explicit_mappings.items():
            if pattern in name:
                return domain, concept, score
        
        # Mapping per categoria
        if category == 'Sm':  # Mathematical symbols
            # Analizza il nome per determinare il dominio
            if any(word in name for word in ['logical', 'turnstile', 'entail', 'implies']):
                return 'logic', self._extract_concept(name), 95.0
            elif any(word in name for word in ['sum', 'integral', 'product', 'differential']):
                return 'math', self._extract_concept(name), 95.0
            else:
                return 'math', self._extract_concept(name), 93.0
        
        elif category == 'So':  # Other symbols
            if any(word in name for word in ['brain', 'mind', 'head']):
                return 'cognitive', self._extract_concept(name), 94.0
            elif any(word in name for word in ['gear', 'cog', 'wheel']):
                return 'structure', self._extract_concept(name), 93.0
            elif any(word in name for word in ['lightning', 'bolt', 'flash']):
                return 'flow', self._extract_concept(name), 93.0
            else:
                return 'meta', self._extract_concept(name), 92.0
        
        # Default mapping
        return 'meta', self._extract_concept(name), 90.0
    
    def _extract_concept(self, name: str) -> str:
        """Estrae concetto dal nome Unicode."""
        
        # Rimuovi parole comuni
        stop_words = ['sign', 'symbol', 'character', 'mark', 'with', 'and', 'or', 'of', 'the']
        
        words = name.lower().split()
        concept_words = [w for w in words if w not in stop_words and len(w) > 2]
        
        # Prendi le prime 2-3 parole più significative
        concept = '_'.join(concept_words[:3])
        
        # Pulisci il concetto
        concept = re.sub(r'[^a-z0-9_]', '', concept)
        
        return concept[:20]  # Limita lunghezza
    
    def generate_fallback(self, domain: str, concept: str, index: int) -> str:
        """Genera fallback NEUROGLYPH-compliant."""
        
        # Abbrevia domain e concept
        domain_abbrev = domain[:3].upper()
        concept_abbrev = concept.replace('_', '')[:4].upper()
        
        # Genera fallback
        if len(f"[{domain_abbrev}{concept_abbrev}]") <= 8:
            fallback = f"[{domain_abbrev}{concept_abbrev}]"
        else:
            fallback = f"[{domain_abbrev}{index%100:02d}]"
        
        return fallback
    
    def extract_neuroglyph_symbols(self) -> bool:
        """Estrae simboli Unicode per NEUROGLYPH."""
        
        print("🧠 NEUROGLYPH UNICODE DATA EXTRACTION")
        print("=" * 50)
        
        # Scarica dati Unicode
        unicode_lines = self.download_unicode_data()
        if not unicode_lines:
            return False
        
        print(f"🔍 Processing {len(unicode_lines)} Unicode entries...")
        
        extracted_count = 0
        existing_fallbacks = set()
        
        for i, line in enumerate(unicode_lines):
            self.stats['total_processed'] += 1
            
            # Parsa linea
            unicode_entry = self.parse_unicode_line(line)
            if not unicode_entry:
                continue
            
            # Verifica rilevanza
            if not self.is_relevant_symbol(unicode_entry):
                continue
            
            self.stats['relevant_symbols'] += 1
            
            # Mappa a dominio NEUROGLYPH
            domain, concept, score = self.map_to_neuroglyph_domain(unicode_entry)
            self.stats['mapped_symbols'] += 1
            
            # Genera simbolo
            try:
                codepoint_int = int(unicode_entry['codepoint'], 16)
                symbol = chr(codepoint_int)
            except (ValueError, OverflowError):
                continue
            
            # Genera fallback
            fallback = self.generate_fallback(domain, concept, extracted_count)
            
            # Assicura unicità fallback
            counter = 1
            original_fallback = fallback
            while fallback in existing_fallbacks:
                fallback = f"[{original_fallback[1:-1]}{counter}]"
                counter += 1
                if len(fallback) > 8:
                    fallback = f"[U{extracted_count%1000:03d}]"
                    break
            
            existing_fallbacks.add(fallback)
            
            # Crea symbol data
            symbol_data = {
                "id": f"UNI{extracted_count:04d}",
                "symbol": symbol,
                "unicode_name": unicode_entry['name'],
                "unicode_category": unicode_entry['category'],
                "codepoint": unicode_entry['codepoint'],
                "unicode_point": f"U+{unicode_entry['codepoint']}",
                "domain": domain,
                "concept": concept,
                "fallback": fallback,
                "score": score,
                "extraction_source": "UnicodeData.txt",
                "extraction_date": datetime.now().isoformat(),
                "neuroglyph_compliant": True,
                "atomic_guaranteed": True,
                "tokenizer_safe": True,
                "symbol_type": "unicode_official"
            }
            
            self.extracted_symbols.append(symbol_data)
            extracted_count += 1
            
            if score >= 95.0:
                self.stats['high_quality_symbols'] += 1
            
            # Progress report
            if extracted_count % 100 == 0:
                print(f"   Extracted {extracted_count} symbols...")
        
        print(f"✅ Extraction complete: {extracted_count} symbols")
        return extracted_count > 0
    
    def save_extracted_symbols(self) -> bool:
        """Salva simboli estratti."""
        
        print(f"💾 Saving extracted Unicode symbols...")
        
        # Crea registry
        registry = {
            "version": "UNICODE_EXTRACTED.1.0",
            "source": "UnicodeData.txt v16.0.0",
            "extraction_date": datetime.now().isoformat(),
            "stats": self.stats,
            "total_symbols": len(self.extracted_symbols),
            "extraction_criteria": {
                "relevant_categories": list(self.relevant_categories.keys()),
                "min_score": 90.0,
                "semantic_filtering": True,
                "neuroglyph_mapping": True
            },
            "extracted_symbols": self.extracted_symbols
        }
        
        # Crea directory se necessario
        import os
        os.makedirs("neuroglyph/core", exist_ok=True)
        
        try:
            with open(self.output_path, 'w', encoding='utf-8') as f:
                json.dump(registry, f, indent=2, ensure_ascii=False)
            
            print(f"✅ Symbols saved: {self.output_path}")
            
            # Backup
            backup_path = f"neuroglyph/core/unicode_backup_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
            with open(backup_path, 'w', encoding='utf-8') as f:
                json.dump(registry, f, indent=2, ensure_ascii=False)
            
            print(f"✅ Backup saved: {backup_path}")
            
            return True
            
        except Exception as e:
            print(f"❌ Error saving: {e}")
            return False
    
    def print_extraction_report(self):
        """Stampa report di estrazione."""
        
        print(f"\n📊 UNICODE EXTRACTION REPORT")
        print("=" * 40)
        print(f"Total processed: {self.stats['total_processed']:,}")
        print(f"Relevant symbols: {self.stats['relevant_symbols']:,}")
        print(f"Mapped symbols: {self.stats['mapped_symbols']:,}")
        print(f"High quality (≥95): {self.stats['high_quality_symbols']:,}")
        print(f"Final extracted: {len(self.extracted_symbols):,}")
        
        # Analisi per dominio
        domain_counts = Counter(s['domain'] for s in self.extracted_symbols)
        print(f"\n🏷️ Domain Distribution:")
        for domain, count in domain_counts.most_common():
            percentage = (count / len(self.extracted_symbols)) * 100
            print(f"   {domain:12}: {count:3} ({percentage:5.1f}%)")
        
        # Analisi per categoria Unicode
        category_counts = Counter(s['unicode_category'] for s in self.extracted_symbols)
        print(f"\n📂 Unicode Category Distribution:")
        for category, count in category_counts.most_common():
            cat_name = self.relevant_categories.get(category, 'Unknown')
            percentage = (count / len(self.extracted_symbols)) * 100
            print(f"   {category} ({cat_name[:20]}): {count:3} ({percentage:5.1f}%)")

def main():
    """Esegue estrazione Unicode per NEUROGLYPH."""
    
    parser = UnicodeDataParser()
    
    # Estrai simboli
    success = parser.extract_neuroglyph_symbols()
    
    if success:
        # Salva risultati
        save_success = parser.save_extracted_symbols()
        
        # Stampa report
        parser.print_extraction_report()
        
        if save_success:
            print(f"\n🎊 UNICODE EXTRACTION SUCCESS!")
            print(f"✅ {len(parser.extracted_symbols)} simboli Unicode estratti")
            print(f"✅ Qualità: {parser.stats['high_quality_symbols']} simboli ≥95 score")
            print(f"✅ Pronti per integrazione NEUROGLYPH")
            
            return True
    
    print(f"\n❌ EXTRACTION FAILED")
    return False

if __name__ == "__main__":
    success = main()
    
    if success:
        print(f"\n🚀 NEXT: Integrate with NEUROGLYPH 8000 registry")
        print(f"   Combine with existing symbols for ultimate coverage")
    else:
        print(f"\n🔧 TROUBLESHOOTING NEEDED")
