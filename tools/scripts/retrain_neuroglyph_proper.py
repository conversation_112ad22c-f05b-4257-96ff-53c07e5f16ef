#!/usr/bin/env python3
"""
🔄 NEUROGLYPH Re-training with Proper Tokenizer
==============================================

Re-training script che usa il tokenizer NEUROGLYPH corretto.
CRITICAL: Questo risolve il problema della frammentazione simbolica.
"""

from transformers import AutoTokenizer, AutoModelForCausalLM
from unsloth import FastLanguageModel
import torch

def retrain_neuroglyph_proper():
    print("🔄 Re-training NEUROGLYPH with proper tokenizer...")
    
    # 1. Carica tokenizer corretto
    tokenizer = AutoTokenizer.from_pretrained("neuroglyph/training/proper_tokenizer")
    print(f"✅ Tokenizer caricato: {len(tokenizer.vocab)} tokens")
    
    # 2. Carica modello base
    model, _ = FastLanguageModel.from_pretrained(
        model_name="Qwen/Qwen2.5-Coder-1.5B-Instruct",
        max_seq_length=2048,
        dtype=None,
        load_in_4bit=True,
    )
    
    # 3. CRITICAL: Ridimensiona embedding layer
    model.resize_token_embeddings(len(tokenizer.vocab))
    print(f"🔧 Embedding layer resized to {len(tokenizer.vocab)}")
    
    # 4. Verifica simboli
    test_symbols = ["⚡", "🔄", "ng:operator:add", "ng:logic:implies"]
    for symbol in test_symbols:
        tokens = tokenizer.encode(symbol, add_special_tokens=False)
        print(f"🔍 {symbol} → {tokens} ({len(tokens)} tokens)")
    
    # 5. Configura LoRA
    model = FastLanguageModel.get_peft_model(
        model,
        r=16,
        target_modules=["q_proj", "k_proj", "v_proj", "o_proj",
                       "gate_proj", "up_proj", "down_proj"],
        lora_alpha=16,
        lora_dropout=0,
        bias="none",
        use_gradient_checkpointing="unsloth",
        random_state=3407,
    )
    
    print("🚀 Ready for proper NEUROGLYPH training!")
    return model, tokenizer

if __name__ == "__main__":
    model, tokenizer = retrain_neuroglyph_proper()
    print("✅ Setup completed - ready for training!")
