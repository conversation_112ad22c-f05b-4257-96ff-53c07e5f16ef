#!/usr/bin/env python3
"""
NEUROGLYPH GOD MODE STATUS CHECKER
==================================
Quick status check for NEUROGLYPH GOD MODE components
"""

import os
import json
from rich.console import Console
from rich.table import Table
from rich.panel import Panel

console = Console()

def check_neuroglyph_status():
    """Controlla status componenti NEUROGLYPH."""
    console.print(Panel.fit("🧠 NEUROGLYPH GOD MODE STATUS CHECK", style="bold blue"))
    
    status_table = Table(title="📋 Component Status")
    status_table.add_column("Component", style="cyan")
    status_table.add_column("Path", style="white")
    status_table.add_column("Status", style="green")
    status_table.add_column("Details", style="yellow")
    
    # Check model paths
    model_paths = [
        ("/Volumes/DANIELE/NEUROGLYPH/neuroglyph_god_mode_final", "Fine-tuned Model"),
        ("/Volumes/DANIELE/NEUROGLYPH/models/god_mode", "GOD MODE Model"),
        ("/Volumes/DANIELE/NEUROGLYPH/models/base", "Base Model")
    ]
    
    for path, name in model_paths:
        if os.path.exists(path):
            try:
                files = os.listdir(path)
                status = "✅ Found"
                details = f"{len(files)} files"
            except:
                status = "⚠️ Access Error"
                details = "Cannot read"
        else:
            status = "❌ Missing"
            details = "Not found"
        
        status_table.add_row(name, path, status, details)
    
    # Check tokenizer
    tokenizer_path = "/Volumes/DANIELE/NEUROGLYPH/neuroglyph/training/colab_god_mode_package/tokenizer"
    if os.path.exists(tokenizer_path):
        try:
            files = os.listdir(tokenizer_path)
            status = "✅ Found"
            details = f"{len(files)} files"
        except:
            status = "⚠️ Access Error"
            details = "Cannot read"
    else:
        status = "❌ Missing"
        details = "Not found"
    
    status_table.add_row("Zero Split Tokenizer", tokenizer_path, status, details)
    
    # Check ULTIMATE registry
    registry_path = "/Volumes/DANIELE/NEUROGLYPH/neuroglyph/training/colab_god_mode_package/neuroglyph_ULTIMATE_registry.json"
    if os.path.exists(registry_path):
        try:
            with open(registry_path, 'r') as f:
                registry = json.load(f)
            
            symbols_count = registry.get('stats', {}).get('total_symbols', 0)
            status = "✅ Found"
            details = f"{symbols_count:,} symbols"
        except Exception as e:
            status = "⚠️ Parse Error"
            details = str(e)[:30]
    else:
        status = "❌ Missing"
        details = "Not found"
    
    status_table.add_row("ULTIMATE Registry", registry_path, status, details)
    
    # Check cognitive dataset
    dataset_path = "/Volumes/DANIELE/NEUROGLYPH/neuroglyph/training/colab_god_mode_package/neuroglyph_cognitive_unsloth.json"
    if os.path.exists(dataset_path):
        try:
            with open(dataset_path, 'r') as f:
                dataset = json.load(f)
            
            examples_count = len(dataset) if isinstance(dataset, list) else 0
            status = "✅ Found"
            details = f"{examples_count} examples"
        except Exception as e:
            status = "⚠️ Parse Error"
            details = str(e)[:30]
    else:
        status = "❌ Missing"
        details = "Not found"
    
    status_table.add_row("Cognitive Dataset", dataset_path, status, details)
    
    console.print(status_table)
    
    # Summary and next steps
    console.print("\n📝 Next Steps:")
    
    if not os.path.exists("/Volumes/DANIELE/NEUROGLYPH/neuroglyph_god_mode_final"):
        console.print("🔴 CRITICAL: Fine-tuned model missing!")
        console.print("   📱 The model is likely still on Google Colab")
        console.print("   💾 You need to download it from Colab:")
        console.print("   1. Open your Colab notebook")
        console.print("   2. Run: !zip -r neuroglyph_god_mode_final.zip ./neuroglyph_god_mode_final")
        console.print("   3. Download the zip file")
        console.print("   4. Extract to: /Volumes/DANIELE/NEUROGLYPH/")
        console.print("")
    
    if os.path.exists(tokenizer_path) and os.path.exists(registry_path):
        console.print("🟢 Training components ready!")
        console.print("   ✅ Tokenizer with 9,236 symbols found")
        console.print("   ✅ ULTIMATE registry loaded")
        console.print("   ✅ Cognitive dataset available")
        console.print("")
    
    console.print("🚀 When the fine-tuned model is available, you can:")
    console.print("   1. Test symbolic reasoning capabilities")
    console.print("   2. Validate zero-splitting atomicity")
    console.print("   3. Benchmark GOD MODE performance")
    console.print("   4. Compare with base model")

if __name__ == "__main__":
    check_neuroglyph_status()
