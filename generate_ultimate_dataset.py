#!/usr/bin/env python3
"""
NEUROGLYPH ULTIMATE Dataset Generation Script
Generates the complete dataset with ALL 9,236 symbols
"""

import sys
import time
from pathlib import Path

# Add current directory to path
sys.path.insert(0, '.')

from neuroglyph.training.dataset_generation.neuroglyph_ultimate_dataset_generator import NeuroGlyphUltimateDatasetGenerator

def main():
    """Generate the complete ULTIMATE dataset"""
    
    print("🚀 NEUROGLYPH ULTIMATE DATASET GENERATION")
    print("=" * 70)
    print("REVOLUTIONARY: Using ALL 9,236 symbols for the first time!")
    print()
    
    # Configuration
    registry_path = "neuroglyph/core/utils/neuroglyph_ULTIMATE_registry.json"
    output_path = "neuroglyph/training/datasets/neuroglyph_ULTIMATE_dataset_10k.json"
    
    # Dataset parameters
    total_examples = 10000
    validation_threshold = 9.0
    
    print(f"📋 Configuration:")
    print(f"  • Registry: {registry_path}")
    print(f"  • Output: {output_path}")
    print(f"  • Total examples: {total_examples:,}")
    print(f"  • Quality threshold: {validation_threshold}")
    print()
    
    try:
        # Initialize generator
        print("🔧 Initializing ULTIMATE generator...")
        start_time = time.time()
        
        generator = NeuroGlyphUltimateDatasetGenerator(registry_path)
        
        init_time = time.time() - start_time
        print(f"✅ Generator initialized in {init_time:.2f}s")
        print(f"📊 Loaded {generator.total_symbols:,} symbols from {len(generator.symbols_by_domain)} domains")
        print()
        
        # Generate coverage analysis
        print("📊 Analyzing symbol coverage...")
        coverage = generator.analyze_symbol_coverage()
        
        print(f"🎯 Coverage Analysis:")
        print(f"  • Total symbols: {coverage['total_symbols']:,}")
        print(f"  • Quality tiers: {len(coverage['quality_coverage'])}")
        print(f"  • Domain coverage: {len(coverage['domain_coverage'])}")
        print()
        
        # Show top domains
        print("🏗️ Top 10 domains:")
        for domain, info in list(coverage['domain_coverage'].items())[:10]:
            print(f"  • {domain:20}: {info['symbol_count']:4,} symbols ({info['percentage']:5.1f}%)")
        print()
        
        # Generate dataset
        print(f"🚀 Starting ULTIMATE dataset generation...")
        print(f"📝 Target: {total_examples:,} examples with quality ≥{validation_threshold}")
        print()
        
        generation_start = time.time()
        
        dataset = generator.generate_ultimate_dataset(
            total_examples=total_examples,
            output_path=output_path,
            validation_threshold=validation_threshold
        )
        
        generation_time = time.time() - generation_start
        
        # Display results
        print()
        print("🎉 ULTIMATE DATASET GENERATION COMPLETE!")
        print("=" * 70)
        
        stats = dataset['metadata']['statistics']
        
        print(f"📊 Final Statistics:")
        print(f"  • Total examples generated: {stats['total_examples_generated']:,}")
        print(f"  • Valid examples: {stats['total_valid_examples']:,}")
        print(f"  • Validation rate: {stats['validation_rate']:.1%}")
        print(f"  • Average quality: {stats['average_quality_score']:.3f}")
        print(f"  • Quality std: {stats['quality_std']:.3f}")
        print(f"  • Symbols utilized: {stats['symbols_used']:,}")
        print(f"  • Domains covered: {stats['domains_covered']}")
        print()
        
        print(f"📁 Dataset Splits:")
        for split, count in stats['dataset_splits'].items():
            percentage = (count / stats['total_valid_examples']) * 100
            print(f"  • {split:10}: {count:5,} examples ({percentage:5.1f}%)")
        print()
        
        print(f"⏱️ Performance:")
        print(f"  • Generation time: {generation_time:.1f}s")
        print(f"  • Examples per second: {stats['total_valid_examples']/generation_time:.1f}")
        print(f"  • Total time: {(time.time() - start_time):.1f}s")
        print()
        
        print(f"🎯 Example Type Distribution:")
        for example_type, count in stats['generation_stats'].items():
            if example_type != 'total_examples':
                percentage = (count / stats['total_valid_examples']) * 100
                print(f"  • {example_type:20}: {count:5,} examples ({percentage:5.1f}%)")
        print()
        
        # Verify file
        output_file = Path(output_path)
        if output_file.exists():
            file_size_mb = output_file.stat().st_size / (1024 * 1024)
            print(f"💾 Dataset saved successfully!")
            print(f"  • File: {output_path}")
            print(f"  • Size: {file_size_mb:.1f} MB")
            print(f"  • Summary: {output_path.replace('.json', '.summary.json')}")
        
        print()
        print("🚀 NEUROGLYPH ULTIMATE DATASET READY!")
        print("🎯 First dataset in history using ALL 9,236 symbols!")
        print("🧠 Ready for ULTIMATE LLM training!")
        
        return True
        
    except Exception as e:
        print(f"❌ Error during generation: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = main()
    if success:
        print("\n✅ SUCCESS: ULTIMATE dataset generation completed!")
        sys.exit(0)
    else:
        print("\n❌ FAILED: Dataset generation failed!")
        sys.exit(1)
