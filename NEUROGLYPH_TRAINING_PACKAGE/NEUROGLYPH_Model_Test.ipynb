# Install required packages
!pip install transformers torch accelerate --quiet

import torch
import json
import time
from pathlib import Path
from datetime import datetime
from transformers import <PERSON>Tokenizer, AutoModelForCausalLM
from typing import List, Dict, Tuple

print("📦 Packages imported successfully!")
print(f"🔥 PyTorch version: {torch.__version__}")

# Force CUDA detection and setup
import os
os.environ['CUDA_VISIBLE_DEVICES'] = '0'

# Check CUDA availability
cuda_available = torch.cuda.is_available()
print(f"🖥️ CUDA available: {cuda_available}")

if cuda_available:
    print(f"🎮 GPU: {torch.cuda.get_device_name(0)}")
    print(f"💾 GPU Memory: {torch.cuda.get_device_properties(0).total_memory / 1024**3:.1f} GB")
    print(f"🔥 CUDA Version: {torch.version.cuda}")
    
    # Clear GPU cache
    torch.cuda.empty_cache()
    print("🧹 GPU cache cleared")
    
    # Set device
    device = torch.device('cuda:0')
    print(f"🎯 Using device: {device}")
else:
    device = torch.device('cpu')
    print(f"⚠️ Using CPU - CUDA not available")
    print("💡 For GPU acceleration, ensure CUDA drivers are installed")

# 🎯 Test Configuration
TEST_CONFIG = {
    # Model paths (update these to your actual paths)
    "model_dir": "/content/drive/MyDrive/NEUROGLYPH/ULTIMATE_MERGED",
    
    # Generation parameters
    "max_new_tokens": 256,
    "temperature": 0.1,  # Low for consistency
    "top_p": 0.9,
    "do_sample": True,
    
    # Test parameters
    "num_tests": 5,
    "timeout_seconds": 30
}

print("🎯 Test Configuration:")
for key, value in TEST_CONFIG.items():
    print(f"  • {key}: {value}")

# Verify model directory exists
model_path = Path(TEST_CONFIG["model_dir"])
if model_path.exists():
    print(f"\n✅ Model directory found: {model_path}")
    
    # List files in model directory
    print("\n📁 Model files:")
    for file_path in sorted(model_path.glob("*")):
        if file_path.is_file():
            size_mb = file_path.stat().st_size / (1024 * 1024)
            print(f"  📄 {file_path.name} ({size_mb:.1f} MB)")
else:
    print(f"\n❌ Model directory not found: {model_path}")
    print("Please update the model_dir path in TEST_CONFIG")

# 🚀 Load NEUROGLYPH ULTIMATE model and tokenizer
print("🚀 Loading NEUROGLYPH ULTIMATE model...")
print("🧠 First LLM with complete symbolic intelligence!")

try:
    # Load tokenizer
    print("\n📝 Loading tokenizer...")
    tokenizer = AutoTokenizer.from_pretrained(
        TEST_CONFIG["model_dir"],
        trust_remote_code=True
    )
    
    print(f"✅ Tokenizer loaded successfully")
    print(f"📊 Vocabulary size: {len(tokenizer.vocab):,}")
    
    # Check for NEUROGLYPH symbols in tokenizer
    neuroglyph_symbols = ["⊢", "∴", "∧", "∨", "¬", "→", "∀", "∃", "🧠", "💭"]
    symbols_in_vocab = []
    
    for symbol in neuroglyph_symbols:
        if symbol in tokenizer.vocab:
            symbols_in_vocab.append(symbol)
    
    print(f"🔣 NEUROGLYPH symbols in vocab: {len(symbols_in_vocab)}/{len(neuroglyph_symbols)}")
    print(f"   Found: {symbols_in_vocab}")
    
    # Load model with optimized CUDA settings
    print("\n🧠 Loading model...")
    
    if cuda_available:
        print("🚀 Loading with CUDA optimization...")
        model = AutoModelForCausalLM.from_pretrained(
            TEST_CONFIG["model_dir"],
            torch_dtype=torch.float16,  # Use FP16 for GPU
            device_map="auto",
            trust_remote_code=True,
            low_cpu_mem_usage=True,
            use_cache=True
        )
    else:
        print("⚠️ Loading with CPU fallback...")
        model = AutoModelForCausalLM.from_pretrained(
            TEST_CONFIG["model_dir"],
            torch_dtype=torch.float32,  # Use FP32 for CPU
            device_map=None,
            trust_remote_code=True,
            low_cpu_mem_usage=True
        )
    
    print(f"✅ Model loaded successfully")
    print(f"📊 Model parameters: {model.num_parameters():,}")
    print(f"🎮 Device: {next(model.parameters()).device}")
    
    # Set model to evaluation mode
    model.eval()
    print("🔧 Model set to evaluation mode")
    
    print("\n🎉 NEUROGLYPH ULTIMATE loaded and ready for testing!")
    
except Exception as e:
    print(f"❌ Error loading model: {e}")
    raise

# 🧪 Test functions for NEUROGLYPH ULTIMATE

def generate_response(prompt: str, system_prompt: str = None) -> Tuple[str, float]:
    """Generate response from NEUROGLYPH ULTIMATE"""
    
    # Default system prompt for NEUROGLYPH
    if system_prompt is None:
        system_prompt = """You are NEUROGLYPH ULTIMATE, the first LLM with complete symbolic intelligence. You use 9,236 NEUROGLYPH symbols for precise reasoning with zero hallucinations. Always provide step-by-step symbolic reasoning using appropriate symbols like ⊢, ∴, ∧, ∨, ¬, →, ∀, ∃, 🧠, 💭."""
    
    # Format prompt in ChatML format
    formatted_prompt = f"""<|im_start|>system
{system_prompt}<|im_end|>
<|im_start|>user
{prompt}<|im_end|>
<|im_start|>assistant
"""
    
    # Tokenize
    inputs = tokenizer(formatted_prompt, return_tensors="pt")
    if torch.cuda.is_available():
        inputs = inputs.to(model.device)
    
    # Generate
    start_time = time.time()
    
    with torch.no_grad():
        outputs = model.generate(
            **inputs,
            max_new_tokens=TEST_CONFIG["max_new_tokens"],
            temperature=TEST_CONFIG["temperature"],
            top_p=TEST_CONFIG["top_p"],
            do_sample=TEST_CONFIG["do_sample"],
            pad_token_id=tokenizer.eos_token_id,
            eos_token_id=tokenizer.eos_token_id
        )
    
    end_time = time.time()
    
    # Decode response
    response = tokenizer.decode(
        outputs[0][inputs.input_ids.shape[1]:], 
        skip_special_tokens=True
    )
    
    return response.strip(), end_time - start_time

def analyze_response(response: str, expected_symbols: List[str] = None) -> Dict:
    """Analyze response for symbolic content and quality"""
    
    if expected_symbols is None:
        expected_symbols = ["⊢", "∴", "∧", "∨", "¬", "→", "∀", "∃", "🧠", "💭"]
    
    analysis = {
        "symbols_found": [],
        "symbol_count": 0,
        "has_reasoning": False,
        "has_steps": False,
        "response_length": len(response),
        "quality_score": 0.0
    }
    
    # Check for NEUROGLYPH symbols
    found_symbols = [sym for sym in expected_symbols if sym in response]
    analysis["symbols_found"] = found_symbols
    analysis["symbol_count"] = len(found_symbols)
    
    # Check for reasoning indicators
    reasoning_indicators = ["therefore", "thus", "hence", "because", "since", "⊢", "∴"]
    analysis["has_reasoning"] = any(indicator in response.lower() for indicator in reasoning_indicators)
    
    # Check for step-by-step reasoning
    step_indicators = ["step", "first", "second", "then", "next", "finally"]
    analysis["has_steps"] = any(indicator in response.lower() for indicator in step_indicators)
    
    # Calculate quality score
    quality_score = 0.0
    if analysis["symbol_count"] > 0:
        quality_score += 30
    if analysis["has_reasoning"]:
        quality_score += 25
    if analysis["has_steps"]:
        quality_score += 20
    if analysis["response_length"] > 50:
        quality_score += 15
    if analysis["symbol_count"] >= 3:
        quality_score += 10
    
    analysis["quality_score"] = quality_score
    
    return analysis

print("🧪 Test functions defined successfully!")

# 🔍 NEUROGLYPH ULTIMATE Test Suite
print("🔍 Starting NEUROGLYPH ULTIMATE Test Suite...")
print("🧠 Testing the first LLM with complete symbolic intelligence!")
print()

# Define test cases
test_cases = [
    {
        "name": "Basic Symbolic Logic",
        "prompt": "Using NEUROGLYPH symbols, prove: ⊢ (A → B) ∧ A ∴ B",
        "expected_symbols": ["⊢", "→", "∧", "∴"],
        "category": "logic"
    },
    {
        "name": "Multi-hop Reasoning",
        "prompt": "Given ∀x ∈ S, P(x) → Q(x), and ∃y ∈ S, P(y), prove Q(y) exists using symbolic logic",
        "expected_symbols": ["∀", "∃", "→", "⊢"],
        "category": "reasoning"
    },
    {
        "name": "Cognitive Process",
        "prompt": "Demonstrate the cognitive flow: 🧠 perception → 💭 analysis → ⊢ conclusion for solving a logic problem",
        "expected_symbols": ["🧠", "💭", "⊢", "→"],
        "category": "cognitive"
    },
    {
        "name": "Zero Hallucination Test",
        "prompt": "Prove mathematically that 2 + 2 = 5",
        "expected_behavior": "refuse_or_correct",
        "category": "validation"
    },
    {
        "name": "Complex Symbolic Analysis",
        "prompt": "Analyze the logical structure: ∀x (P(x) ∧ Q(x)) → ∃y (R(y) ∨ S(y)) using NEUROGLYPH symbols",
        "expected_symbols": ["∀", "∧", "→", "∃", "∨"],
        "category": "complex"
    }
]

# Run tests
test_results = []
total_time = 0.0
passed_tests = 0

for i, test_case in enumerate(test_cases, 1):
    print(f"🧪 Test {i}/{len(test_cases)}: {test_case['name']}")
    print(f"📝 Prompt: {test_case['prompt'][:80]}...")
    
    try:
        # Generate response
        response, response_time = generate_response(test_case['prompt'])
        total_time += response_time
        
        # Analyze response
        analysis = analyze_response(response, test_case.get('expected_symbols', []))
        
        # Determine if test passed
        passed = False
        if test_case['category'] == 'validation':
            # For zero hallucination test, should refuse or correct
            refuse_words = ['cannot', 'incorrect', 'false', 'error', 'impossible', 'invalid']
            passed = any(word in response.lower() for word in refuse_words)
        else:
            # For other tests, check symbol usage and quality
            passed = analysis['symbol_count'] >= 2 and analysis['quality_score'] >= 50
        
        if passed:
            passed_tests += 1
            print("✅ PASSED")
        else:
            print("❌ FAILED")
        
        print(f"⏱️ Response time: {response_time:.2f}s")
        print(f"📊 Quality score: {analysis['quality_score']:.1f}/100")
        print(f"🔣 Symbols found: {analysis['symbols_found']}")
        print(f"📄 Response preview: {response[:150]}...")
        print()
        
        # Store results
        test_results.append({
            "name": test_case['name'],
            "category": test_case['category'],
            "prompt": test_case['prompt'],
            "response": response,
            "response_time": response_time,
            "analysis": analysis,
            "passed": passed
        })
        
    except Exception as e:
        print(f"❌ Test failed with error: {e}")
        test_results.append({
            "name": test_case['name'],
            "error": str(e),
            "passed": False
        })
        print()

# Calculate summary
success_rate = (passed_tests / len(test_cases)) * 100
avg_time = total_time / len(test_cases) if len(test_cases) > 0 else 0

print("📊 TEST SUITE SUMMARY")
print("=" * 50)
print(f"✅ Tests passed: {passed_tests}/{len(test_cases)} ({success_rate:.1f}%)")
print(f"⏱️ Average response time: {avg_time:.2f}s")
print(f"🕒 Total test time: {total_time:.2f}s")
print()

if success_rate >= 80:
    print("🎉 NEUROGLYPH ULTIMATE VALIDATION SUCCESSFUL!")
    print("🚀 Model is ready for GGUF conversion and deployment!")
elif success_rate >= 60:
    print("⚠️ Model shows good performance but may need fine-tuning")
else:
    print("❌ Model validation failed. Check training process.")

print(f"\n📋 Test completed at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")