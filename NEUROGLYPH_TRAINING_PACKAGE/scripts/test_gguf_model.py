#!/usr/bin/env python3
"""
NEUROGLYPH GGUF MODEL TESTER
============================

Test script for validating NEUROGLYPH ULTIMATE GGUF model functionality.
Verifies symbolic reasoning, zero hallucination, and multi-hop capabilities.

🚀 FEATURES:
- Symbolic reasoning tests
- Zero hallucination validation
- Multi-hop reasoning verification
- Performance benchmarking
- NEUROGLYPH symbol usage analysis

Usage:
    python test_gguf_model.py --model_path /path/to/model.gguf
    python test_gguf_model.py --ollama_model neuroglyph-ultimate
"""

import argparse
import json
import time
from pathlib import Path
from typing import Dict, List, Optional, Tuple
import subprocess
import sys

class NeuroGlyphGGUFTester:
    """
    🧪 NEUROGLYPH GGUF Model Tester
    
    Comprehensive testing suite for NEUROGLYPH ULTIMATE GGUF models.
    Tests symbolic reasoning, zero hallucination, and cognitive capabilities.
    """
    
    def __init__(self, model_path: Optional[str] = None, ollama_model: Optional[str] = None):
        self.model_path = model_path
        self.ollama_model = ollama_model
        self.test_results = []
        
        # Test prompts for symbolic reasoning
        self.test_prompts = [
            {
                "name": "Basic Symbolic Logic",
                "prompt": "Using NEUROGLYPH symbols, prove: ⊢ (A → B) ∧ A ∴ B",
                "expected_symbols": ["⊢", "→", "∧", "∴"],
                "category": "logic"
            },
            {
                "name": "Multi-hop Reasoning",
                "prompt": "Given ∀x ∈ S, P(x) → Q(x), and ∃y ∈ S, P(y), prove Q(y) exists",
                "expected_symbols": ["∀", "∈", "→", "∃"],
                "category": "reasoning"
            },
            {
                "name": "Cognitive Process",
                "prompt": "Demonstrate the cognitive flow: 🧠 perception → 💭 analysis → ⊢ conclusion",
                "expected_symbols": ["🧠", "💭", "⊢", "→"],
                "category": "cognitive"
            },
            {
                "name": "Zero Hallucination Test",
                "prompt": "Prove mathematically that 2 + 2 = 5",
                "expected_behavior": "refuse_or_correct",
                "category": "validation"
            },
            {
                "name": "Complex Symbolic Reasoning",
                "prompt": "Analyze: ∀x (P(x) ∧ Q(x)) → ∃y (R(y) ∨ S(y)) using symbolic logic",
                "expected_symbols": ["∀", "∧", "→", "∃", "∨"],
                "category": "complex"
            }
        ]
    
    def test_with_llama_cpp(self, prompt: str) -> Tuple[str, float]:
        """Test with llama.cpp"""
        if not self.model_path:
            raise ValueError("Model path required for llama.cpp testing")
        
        cmd = [
            "llama-cli",
            "-m", str(self.model_path),
            "-p", prompt,
            "--temp", "0.1",
            "--top-p", "0.9",
            "-n", "256"
        ]
        
        start_time = time.time()
        try:
            result = subprocess.run(
                cmd,
                capture_output=True,
                text=True,
                timeout=60
            )
            end_time = time.time()
            
            if result.returncode == 0:
                return result.stdout.strip(), end_time - start_time
            else:
                return f"Error: {result.stderr}", end_time - start_time
                
        except subprocess.TimeoutExpired:
            return "Error: Timeout", 60.0
        except Exception as e:
            return f"Error: {e}", time.time() - start_time
    
    def test_with_ollama(self, prompt: str) -> Tuple[str, float]:
        """Test with Ollama"""
        if not self.ollama_model:
            raise ValueError("Ollama model name required")
        
        cmd = [
            "ollama", "run", self.ollama_model,
            prompt
        ]
        
        start_time = time.time()
        try:
            result = subprocess.run(
                cmd,
                capture_output=True,
                text=True,
                timeout=60
            )
            end_time = time.time()
            
            if result.returncode == 0:
                return result.stdout.strip(), end_time - start_time
            else:
                return f"Error: {result.stderr}", end_time - start_time
                
        except subprocess.TimeoutExpired:
            return "Error: Timeout", 60.0
        except Exception as e:
            return f"Error: {e}", time.time() - start_time
    
    def analyze_response(self, response: str, test_case: Dict) -> Dict:
        """Analyze model response for symbolic content and quality"""
        analysis = {
            "symbols_found": [],
            "symbols_expected": test_case.get("expected_symbols", []),
            "symbol_coverage": 0.0,
            "has_reasoning": False,
            "has_steps": False,
            "response_length": len(response),
            "quality_score": 0.0
        }
        
        # Check for expected symbols
        expected_symbols = test_case.get("expected_symbols", [])
        if expected_symbols:
            found_symbols = [sym for sym in expected_symbols if sym in response]
            analysis["symbols_found"] = found_symbols
            analysis["symbol_coverage"] = len(found_symbols) / len(expected_symbols)
        
        # Check for reasoning indicators
        reasoning_indicators = ["therefore", "thus", "hence", "because", "since", "⊢", "∴"]
        analysis["has_reasoning"] = any(indicator in response.lower() for indicator in reasoning_indicators)
        
        # Check for step-by-step reasoning
        step_indicators = ["step", "first", "second", "then", "next", "finally"]
        analysis["has_steps"] = any(indicator in response.lower() for indicator in step_indicators)
        
        # Calculate quality score
        quality_score = 0.0
        if analysis["symbol_coverage"] > 0.5:
            quality_score += 30
        if analysis["has_reasoning"]:
            quality_score += 25
        if analysis["has_steps"]:
            quality_score += 20
        if analysis["response_length"] > 50:
            quality_score += 15
        if "error" not in response.lower():
            quality_score += 10
        
        analysis["quality_score"] = quality_score
        
        return analysis
    
    def run_test_suite(self) -> Dict:
        """Run complete test suite"""
        print("🧪 Starting NEUROGLYPH GGUF Test Suite...")
        print(f"📁 Model: {self.model_path or self.ollama_model}")
        print()
        
        results = {
            "model": self.model_path or self.ollama_model,
            "test_time": time.time(),
            "tests": [],
            "summary": {}
        }
        
        total_tests = len(self.test_prompts)
        passed_tests = 0
        total_time = 0.0
        total_quality = 0.0
        
        for i, test_case in enumerate(self.test_prompts, 1):
            print(f"🔍 Test {i}/{total_tests}: {test_case['name']}")
            print(f"📝 Prompt: {test_case['prompt'][:80]}...")
            
            # Run test
            if self.ollama_model:
                response, response_time = self.test_with_ollama(test_case['prompt'])
            else:
                response, response_time = self.test_with_llama_cpp(test_case['prompt'])
            
            # Analyze response
            analysis = self.analyze_response(response, test_case)
            
            # Determine if test passed
            passed = False
            if test_case['category'] == 'validation':
                # For zero hallucination test, should refuse or correct
                passed = any(word in response.lower() for word in ['cannot', 'incorrect', 'false', 'error', 'impossible'])
            else:
                # For other tests, check symbol coverage and quality
                passed = analysis['symbol_coverage'] >= 0.5 and analysis['quality_score'] >= 50
            
            if passed:
                passed_tests += 1
                print("✅ PASSED")
            else:
                print("❌ FAILED")
            
            print(f"⏱️ Response time: {response_time:.2f}s")
            print(f"📊 Quality score: {analysis['quality_score']:.1f}/100")
            print(f"🔣 Symbols found: {analysis['symbols_found']}")
            print(f"📄 Response: {response[:100]}...")
            print()
            
            # Store results
            test_result = {
                "name": test_case['name'],
                "category": test_case['category'],
                "prompt": test_case['prompt'],
                "response": response,
                "response_time": response_time,
                "analysis": analysis,
                "passed": passed
            }
            results["tests"].append(test_result)
            
            total_time += response_time
            total_quality += analysis['quality_score']
        
        # Calculate summary
        success_rate = (passed_tests / total_tests) * 100
        avg_quality = total_quality / total_tests
        avg_time = total_time / total_tests
        
        results["summary"] = {
            "total_tests": total_tests,
            "passed_tests": passed_tests,
            "success_rate": success_rate,
            "average_quality": avg_quality,
            "average_response_time": avg_time,
            "total_time": total_time
        }
        
        # Print summary
        print("📊 TEST SUITE SUMMARY")
        print("=" * 50)
        print(f"✅ Tests passed: {passed_tests}/{total_tests} ({success_rate:.1f}%)")
        print(f"📈 Average quality: {avg_quality:.1f}/100")
        print(f"⏱️ Average response time: {avg_time:.2f}s")
        print(f"🕒 Total test time: {total_time:.2f}s")
        print()
        
        if success_rate >= 80:
            print("🎉 NEUROGLYPH GGUF MODEL VALIDATION SUCCESSFUL!")
            print("🚀 Model is ready for production deployment!")
        elif success_rate >= 60:
            print("⚠️ Model shows good performance but may need fine-tuning")
        else:
            print("❌ Model validation failed. Check training and conversion process.")
        
        return results
    
    def save_results(self, results: Dict, output_file: str):
        """Save test results to JSON file"""
        with open(output_file, 'w') as f:
            json.dump(results, f, indent=2)
        print(f"📋 Test results saved to: {output_file}")

def main():
    parser = argparse.ArgumentParser(
        description="Test NEUROGLYPH ULTIMATE GGUF model"
    )
    
    parser.add_argument(
        "--model_path",
        help="Path to GGUF model file (for llama.cpp)"
    )
    
    parser.add_argument(
        "--ollama_model",
        help="Ollama model name (alternative to model_path)"
    )
    
    parser.add_argument(
        "--output",
        default="neuroglyph_test_results.json",
        help="Output file for test results"
    )
    
    args = parser.parse_args()
    
    if not args.model_path and not args.ollama_model:
        print("❌ Error: Either --model_path or --ollama_model must be specified")
        sys.exit(1)
    
    # Create tester
    tester = NeuroGlyphGGUFTester(
        model_path=args.model_path,
        ollama_model=args.ollama_model
    )
    
    # Run tests
    results = tester.run_test_suite()
    
    # Save results
    tester.save_results(results, args.output)

if __name__ == "__main__":
    main()
