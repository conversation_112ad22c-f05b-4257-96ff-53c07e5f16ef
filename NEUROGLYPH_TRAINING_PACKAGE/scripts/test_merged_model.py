#!/usr/bin/env python3
"""
NEUROGLYPH ULTIMATE MERGED MODEL TESTER
=======================================

Test script per validare il modello NEUROGLYPH ULTIMATE merged prima della conversione GGUF.
Verifica symbolic reasoning, zero hallucination, e uso corretto dei simboli NEUROGLYPH.

🚀 FEATURES:
- Test symbolic reasoning completo
- Validazione zero hallucination
- Analisi uso simboli NEUROGLYPH
- Performance benchmarking
- Report dettagliato risultati

Usage:
    python test_merged_model.py --model_dir /path/to/merged/model
    python test_merged_model.py --model_dir /content/drive/MyDrive/NEUROGLYPH/ULTIMATE_MERGED
"""

import argparse
import json
import time
import torch
from pathlib import Path
from datetime import datetime
from typing import Dict, List, Tuple
from transformers import AutoTokenizer, AutoModelForCausalLM

class NeuroGlyphModelTester:
    """
    🧪 NEUROGLYPH ULTIMATE Model Tester
    
    Comprehensive testing suite for NEUROGLYPH ULTIMATE merged model.
    Tests symbolic reasoning, zero hallucination, and cognitive capabilities.
    """
    
    def __init__(self, model_dir: str):
        self.model_dir = Path(model_dir)
        self.model = None
        self.tokenizer = None
        self.test_results = []
        
        # Test configuration
        self.config = {
            "max_new_tokens": 256,
            "temperature": 0.1,
            "top_p": 0.9,
            "do_sample": True
        }
        
        # Test cases
        self.test_cases = [
            {
                "name": "Basic Symbolic Logic",
                "prompt": "Using NEUROGLYPH symbols, prove: ⊢ (A → B) ∧ A ∴ B",
                "expected_symbols": ["⊢", "→", "∧", "∴"],
                "category": "logic"
            },
            {
                "name": "Multi-hop Reasoning",
                "prompt": "Given ∀x ∈ S, P(x) → Q(x), and ∃y ∈ S, P(y), prove Q(y) exists",
                "expected_symbols": ["∀", "∃", "→", "⊢"],
                "category": "reasoning"
            },
            {
                "name": "Cognitive Process",
                "prompt": "Demonstrate: 🧠 perception → 💭 analysis → ⊢ conclusion for logic problem",
                "expected_symbols": ["🧠", "💭", "⊢", "→"],
                "category": "cognitive"
            },
            {
                "name": "Zero Hallucination Test",
                "prompt": "Prove mathematically that 2 + 2 = 5",
                "expected_behavior": "refuse_or_correct",
                "category": "validation"
            },
            {
                "name": "Complex Symbolic Analysis",
                "prompt": "Analyze: ∀x (P(x) ∧ Q(x)) → ∃y (R(y) ∨ S(y)) using symbolic logic",
                "expected_symbols": ["∀", "∧", "→", "∃", "∨"],
                "category": "complex"
            }
        ]
    
    def load_model(self) -> bool:
        """Load NEUROGLYPH ULTIMATE model and tokenizer"""
        print("🚀 Loading NEUROGLYPH ULTIMATE model...")
        
        try:
            # Check if model directory exists
            if not self.model_dir.exists():
                print(f"❌ Model directory not found: {self.model_dir}")
                return False
            
            # Load tokenizer
            print("📝 Loading tokenizer...")
            self.tokenizer = AutoTokenizer.from_pretrained(
                str(self.model_dir),
                trust_remote_code=True
            )
            
            print(f"✅ Tokenizer loaded")
            print(f"📊 Vocabulary size: {len(self.tokenizer.vocab):,}")
            
            # Check for NEUROGLYPH symbols
            neuroglyph_symbols = ["⊢", "∴", "∧", "∨", "¬", "→", "∀", "∃", "🧠", "💭"]
            symbols_in_vocab = [sym for sym in neuroglyph_symbols if sym in self.tokenizer.vocab]
            
            print(f"🔣 NEUROGLYPH symbols: {len(symbols_in_vocab)}/{len(neuroglyph_symbols)}")
            print(f"   Found: {symbols_in_vocab}")
            
            # Load model
            print("🧠 Loading model...")
            self.model = AutoModelForCausalLM.from_pretrained(
                str(self.model_dir),
                torch_dtype=torch.float16 if torch.cuda.is_available() else torch.float32,
                device_map="auto" if torch.cuda.is_available() else None,
                trust_remote_code=True
            )
            
            print(f"✅ Model loaded")
            print(f"📊 Parameters: {self.model.num_parameters():,}")
            print(f"🎮 Device: {next(self.model.parameters()).device}")
            
            # Set to evaluation mode
            self.model.eval()
            
            return True
            
        except Exception as e:
            print(f"❌ Error loading model: {e}")
            return False
    
    def generate_response(self, prompt: str) -> Tuple[str, float]:
        """Generate response from NEUROGLYPH ULTIMATE"""
        
        # System prompt for NEUROGLYPH
        system_prompt = """You are NEUROGLYPH ULTIMATE, the first LLM with complete symbolic intelligence. You use 9,236 NEUROGLYPH symbols for precise reasoning with zero hallucinations. Always provide step-by-step symbolic reasoning using appropriate symbols like ⊢, ∴, ∧, ∨, ¬, →, ∀, ∃, 🧠, 💭."""
        
        # Format in ChatML
        formatted_prompt = f"""<|im_start|>system
{system_prompt}<|im_end|>
<|im_start|>user
{prompt}<|im_end|>
<|im_start|>assistant
"""
        
        # Tokenize
        inputs = self.tokenizer(formatted_prompt, return_tensors="pt")
        if torch.cuda.is_available():
            inputs = inputs.to(self.model.device)
        
        # Generate
        start_time = time.time()
        
        with torch.no_grad():
            outputs = self.model.generate(
                **inputs,
                max_new_tokens=self.config["max_new_tokens"],
                temperature=self.config["temperature"],
                top_p=self.config["top_p"],
                do_sample=self.config["do_sample"],
                pad_token_id=self.tokenizer.eos_token_id,
                eos_token_id=self.tokenizer.eos_token_id
            )
        
        end_time = time.time()
        
        # Decode response
        response = self.tokenizer.decode(
            outputs[0][inputs.input_ids.shape[1]:], 
            skip_special_tokens=True
        )
        
        return response.strip(), end_time - start_time
    
    def analyze_response(self, response: str, expected_symbols: List[str] = None) -> Dict:
        """Analyze response for symbolic content and quality"""
        
        if expected_symbols is None:
            expected_symbols = ["⊢", "∴", "∧", "∨", "¬", "→", "∀", "∃", "🧠", "💭"]
        
        analysis = {
            "symbols_found": [],
            "symbol_count": 0,
            "has_reasoning": False,
            "has_steps": False,
            "response_length": len(response),
            "quality_score": 0.0
        }
        
        # Check for NEUROGLYPH symbols
        found_symbols = [sym for sym in expected_symbols if sym in response]
        analysis["symbols_found"] = found_symbols
        analysis["symbol_count"] = len(found_symbols)
        
        # Check for reasoning indicators
        reasoning_indicators = ["therefore", "thus", "hence", "because", "since", "⊢", "∴"]
        analysis["has_reasoning"] = any(indicator in response.lower() for indicator in reasoning_indicators)
        
        # Check for step-by-step reasoning
        step_indicators = ["step", "first", "second", "then", "next", "finally"]
        analysis["has_steps"] = any(indicator in response.lower() for indicator in step_indicators)
        
        # Calculate quality score
        quality_score = 0.0
        if analysis["symbol_count"] > 0:
            quality_score += 30
        if analysis["has_reasoning"]:
            quality_score += 25
        if analysis["has_steps"]:
            quality_score += 20
        if analysis["response_length"] > 50:
            quality_score += 15
        if analysis["symbol_count"] >= 3:
            quality_score += 10
        
        analysis["quality_score"] = quality_score
        
        return analysis
    
    def run_test_suite(self) -> Dict:
        """Run complete test suite"""
        print("🧪 Starting NEUROGLYPH ULTIMATE Test Suite...")
        print("🧠 Testing first LLM with complete symbolic intelligence!")
        print()
        
        results = {
            "model_dir": str(self.model_dir),
            "test_time": datetime.now().isoformat(),
            "tests": [],
            "summary": {}
        }
        
        total_tests = len(self.test_cases)
        passed_tests = 0
        total_time = 0.0
        total_quality = 0.0
        
        for i, test_case in enumerate(self.test_cases, 1):
            print(f"🔍 Test {i}/{total_tests}: {test_case['name']}")
            print(f"📝 Prompt: {test_case['prompt'][:80]}...")
            
            try:
                # Generate response
                response, response_time = self.generate_response(test_case['prompt'])
                
                # Analyze response
                analysis = self.analyze_response(response, test_case.get('expected_symbols', []))
                
                # Determine if test passed
                passed = False
                if test_case['category'] == 'validation':
                    # For zero hallucination test, should refuse or correct
                    refuse_words = ['cannot', 'incorrect', 'false', 'error', 'impossible', 'invalid']
                    passed = any(word in response.lower() for word in refuse_words)
                else:
                    # For other tests, check symbol usage and quality
                    passed = analysis['symbol_count'] >= 2 and analysis['quality_score'] >= 50
                
                if passed:
                    passed_tests += 1
                    print("✅ PASSED")
                else:
                    print("❌ FAILED")
                
                print(f"⏱️ Response time: {response_time:.2f}s")
                print(f"📊 Quality score: {analysis['quality_score']:.1f}/100")
                print(f"🔣 Symbols found: {analysis['symbols_found']}")
                print(f"📄 Response: {response[:100]}...")
                print()
                
                # Store results
                test_result = {
                    "name": test_case['name'],
                    "category": test_case['category'],
                    "prompt": test_case['prompt'],
                    "response": response,
                    "response_time": response_time,
                    "analysis": analysis,
                    "passed": passed
                }
                results["tests"].append(test_result)
                
                total_time += response_time
                total_quality += analysis['quality_score']
                
            except Exception as e:
                print(f"❌ Test failed with error: {e}")
                results["tests"].append({
                    "name": test_case['name'],
                    "error": str(e),
                    "passed": False
                })
                print()
        
        # Calculate summary
        success_rate = (passed_tests / total_tests) * 100
        avg_quality = total_quality / total_tests if total_tests > 0 else 0
        avg_time = total_time / total_tests if total_tests > 0 else 0
        
        results["summary"] = {
            "total_tests": total_tests,
            "passed_tests": passed_tests,
            "success_rate": success_rate,
            "average_quality": avg_quality,
            "average_response_time": avg_time,
            "total_time": total_time
        }
        
        # Print summary
        print("📊 TEST SUITE SUMMARY")
        print("=" * 50)
        print(f"✅ Tests passed: {passed_tests}/{total_tests} ({success_rate:.1f}%)")
        print(f"📈 Average quality: {avg_quality:.1f}/100")
        print(f"⏱️ Average response time: {avg_time:.2f}s")
        print(f"🕒 Total test time: {total_time:.2f}s")
        print()
        
        if success_rate >= 80:
            print("🎉 NEUROGLYPH ULTIMATE VALIDATION SUCCESSFUL!")
            print("🚀 Model is ready for GGUF conversion and deployment!")
        elif success_rate >= 60:
            print("⚠️ Model shows good performance but may need fine-tuning")
        else:
            print("❌ Model validation failed. Check training process.")
        
        return results
    
    def save_results(self, results: Dict, output_file: str):
        """Save test results to JSON file"""
        with open(output_file, 'w') as f:
            json.dump(results, f, indent=2)
        print(f"📋 Test results saved to: {output_file}")

def main():
    parser = argparse.ArgumentParser(
        description="Test NEUROGLYPH ULTIMATE merged model"
    )
    
    parser.add_argument(
        "--model_dir",
        required=True,
        help="Path to NEUROGLYPH ULTIMATE merged model directory"
    )
    
    parser.add_argument(
        "--output",
        default="neuroglyph_merged_test_results.json",
        help="Output file for test results"
    )
    
    args = parser.parse_args()
    
    # Create tester
    tester = NeuroGlyphModelTester(args.model_dir)
    
    # Load model
    if not tester.load_model():
        print("❌ Failed to load model. Exiting.")
        return
    
    # Run tests
    results = tester.run_test_suite()
    
    # Save results
    tester.save_results(results, args.output)
    
    print(f"\n🎉 Testing completed!")
    print(f"📋 Results saved to: {args.output}")

if __name__ == "__main__":
    main()
