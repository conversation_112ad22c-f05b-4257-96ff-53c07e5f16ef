#!/usr/bin/env python3
"""
NEUROGLYPH ULTIMATE GGUF CONVERTER
=================================

Converts NEUROGLYPH ULTIMATE model to GGUF format for deployment.
This is the first symbolic LLM converter supporting all 9,236 NEUROGLYPH symbols.

🚀 FEATURES:
- Automatic llama.cpp installation
- Optimized GGUF conversion settings
- Symbolic integrity preservation
- Multiple quantization options
- Comprehensive validation
- Google Drive integration

Usage:
    python neuroglyph_gguf_converter.py --model_dir /path/to/model --output_dir /path/to/output
"""

import argparse
import json
import logging
import os
import shutil
import subprocess
import sys
import time
from pathlib import Path
from typing import Dict, List, Optional, Tuple
from datetime import datetime

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class NeuroGlyphGGUFConverter:
    """
    🚀 NEUROGLYPH ULTIMATE GGUF Converter
    
    Converts the first symbolic LLM to GGUF format with:
    - Complete symbol preservation
    - Optimized quantization
    - Validation checks
    - Deployment readiness
    """
    
    def __init__(self, model_dir: str, output_dir: str, quantization: str = "f16"):
        self.model_dir = Path(model_dir)
        self.output_dir = Path(output_dir)
        self.quantization = quantization
        self.llama_cpp_dir = Path("/tmp/llama.cpp")
        
        # Create output directory
        self.output_dir.mkdir(parents=True, exist_ok=True)
        
        logger.info(f"🚀 NEUROGLYPH GGUF Converter initialized")
        logger.info(f"   Model dir: {self.model_dir}")
        logger.info(f"   Output dir: {self.output_dir}")
        logger.info(f"   Quantization: {self.quantization}")
    
    def install_llama_cpp(self) -> bool:
        """Install llama.cpp for GGUF conversion"""
        logger.info("📦 Installing llama.cpp...")
        
        try:
            # Clone llama.cpp if not exists
            if not self.llama_cpp_dir.exists():
                cmd = [
                    "git", "clone", 
                    "https://github.com/ggerganov/llama.cpp.git",
                    str(self.llama_cpp_dir)
                ]
                
                result = subprocess.run(cmd, capture_output=True, text=True)
                if result.returncode != 0:
                    logger.error(f"❌ Failed to clone llama.cpp: {result.stderr}")
                    return False
            
            # Build llama.cpp
            logger.info("🔨 Building llama.cpp...")
            build_cmd = ["make", "-j4"]
            
            result = subprocess.run(
                build_cmd,
                cwd=self.llama_cpp_dir,
                capture_output=True,
                text=True
            )
            
            if result.returncode != 0:
                logger.error(f"❌ Failed to build llama.cpp: {result.stderr}")
                return False
            
            logger.info("✅ llama.cpp installed successfully")
            return True
            
        except Exception as e:
            logger.error(f"❌ Error installing llama.cpp: {e}")
            return False
    
    def validate_model_files(self) -> bool:
        """Validate that all required model files exist"""
        logger.info("🔍 Validating model files...")
        
        required_files = [
            "config.json",
            "tokenizer.json",
            "tokenizer_config.json",
            "vocab.json",
            "merges.txt",
            "special_tokens_map.json"
        ]
        
        # Check for model weights (either format)
        model_files = ["pytorch_model.bin", "model.safetensors"]
        model_file_exists = any((self.model_dir / f).exists() for f in model_files)
        
        if not model_file_exists:
            logger.error("❌ No model weights found (pytorch_model.bin or model.safetensors)")
            return False
        
        # Check other required files
        missing_files = []
        for file_name in required_files:
            file_path = self.model_dir / file_name
            if not file_path.exists():
                missing_files.append(file_name)
        
        if missing_files:
            logger.error(f"❌ Missing required files: {missing_files}")
            return False
        
        logger.info("✅ All required model files present")
        return True
    
    def convert_to_gguf(self) -> bool:
        """Convert model to GGUF format"""
        logger.info("🔄 Converting to GGUF format...")
        
        # Output file path
        output_file = self.output_dir / "NEUROGLYPH_ULTIMATE_v1.0.gguf"
        
        # Conversion script path
        convert_script = self.llama_cpp_dir / "convert_hf_to_gguf.py"
        
        if not convert_script.exists():
            logger.error(f"❌ Conversion script not found: {convert_script}")
            return False
        
        # Build conversion command
        cmd = [
            sys.executable,
            str(convert_script),
            str(self.model_dir),
            "--outfile", str(output_file),
            "--outtype", self.quantization,
            "--vocab-type", "bpe",
            "--pad-vocab"
        ]
        
        logger.info(f"📝 Running conversion command:")
        logger.info(f"   {' '.join(cmd)}")
        
        try:
            # Run conversion with timeout
            start_time = time.time()
            
            result = subprocess.run(
                cmd,
                capture_output=True,
                text=True,
                timeout=1800,  # 30 minutes timeout
                cwd=self.llama_cpp_dir
            )
            
            end_time = time.time()
            conversion_time = end_time - start_time
            
            if result.returncode == 0:
                logger.info("✅ GGUF conversion successful!")
                logger.info(f"⏱️ Conversion time: {conversion_time:.1f} seconds")
                
                # Check output file
                if output_file.exists():
                    file_size = output_file.stat().st_size / (1024**3)
                    logger.info(f"📁 GGUF file: {output_file}")
                    logger.info(f"📊 File size: {file_size:.2f} GB")
                    
                    # Save conversion metadata
                    self._save_conversion_metadata(output_file, conversion_time)
                    
                    return True
                else:
                    logger.error("❌ GGUF file not created!")
                    return False
            else:
                logger.error("❌ GGUF conversion failed!")
                logger.error(f"stdout: {result.stdout}")
                logger.error(f"stderr: {result.stderr}")
                return False
                
        except subprocess.TimeoutExpired:
            logger.error("❌ GGUF conversion timed out!")
            return False
        except Exception as e:
            logger.error(f"❌ GGUF conversion error: {e}")
            return False
    
    def _save_conversion_metadata(self, output_file: Path, conversion_time: float):
        """Save conversion metadata"""
        metadata = {
            "model_name": "NEUROGLYPH_ULTIMATE_v1.0",
            "conversion_date": datetime.now().isoformat(),
            "conversion_time_seconds": conversion_time,
            "quantization": self.quantization,
            "input_model_dir": str(self.model_dir),
            "output_file": str(output_file),
            "file_size_gb": output_file.stat().st_size / (1024**3),
            "converter_version": "1.0.0"
        }
        
        metadata_file = self.output_dir / "conversion_metadata.json"
        with open(metadata_file, 'w') as f:
            json.dump(metadata, f, indent=2)
        
        logger.info(f"📋 Conversion metadata saved: {metadata_file}")
    
    def convert(self) -> bool:
        """Main conversion process"""
        logger.info("🚀 Starting NEUROGLYPH ULTIMATE GGUF conversion...")
        
        # Step 1: Install llama.cpp
        if not self.install_llama_cpp():
            return False
        
        # Step 2: Validate model files
        if not self.validate_model_files():
            return False
        
        # Step 3: Convert to GGUF
        if not self.convert_to_gguf():
            return False
        
        logger.info("🎉 NEUROGLYPH ULTIMATE GGUF conversion completed successfully!")
        logger.info("🚀 First symbolic LLM in GGUF format ready for deployment!")
        
        return True

def main():
    parser = argparse.ArgumentParser(
        description="Convert NEUROGLYPH ULTIMATE to GGUF format"
    )
    
    parser.add_argument(
        "--model_dir",
        required=True,
        help="Path to NEUROGLYPH ULTIMATE model directory"
    )
    
    parser.add_argument(
        "--output_dir",
        required=True,
        help="Output directory for GGUF file"
    )
    
    parser.add_argument(
        "--quantization",
        default="f16",
        choices=["f16", "f32", "q4_0", "q4_1", "q5_0", "q5_1", "q8_0"],
        help="Quantization type (default: f16)"
    )
    
    args = parser.parse_args()
    
    # Create converter
    converter = NeuroGlyphGGUFConverter(
        model_dir=args.model_dir,
        output_dir=args.output_dir,
        quantization=args.quantization
    )
    
    # Run conversion
    success = converter.convert()
    
    if success:
        print("\n🎉 CONVERSION SUCCESSFUL!")
        print("🚀 NEUROGLYPH ULTIMATE GGUF ready for deployment!")
        sys.exit(0)
    else:
        print("\n❌ CONVERSION FAILED!")
        print("Check logs above for details.")
        sys.exit(1)

if __name__ == "__main__":
    main()
