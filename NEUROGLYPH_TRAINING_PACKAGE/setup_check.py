#!/usr/bin/env python3
"""
NEUROGLYPH ULTIMATE Setup Verification
=====================================

Verifies that all files are present and paths are correct
before starting the training process.

Run this in Colab before executing the main training notebook.
"""

import os
import json
from pathlib import Path

def check_google_drive():
    """Check if Google Drive is mounted"""
    print("🔍 Checking Google Drive mount...")
    
    drive_path = Path("/content/drive/MyDrive")
    if drive_path.exists():
        print("✅ Google Drive mounted successfully!")
        return True
    else:
        print("❌ Google Drive not mounted!")
        print("📋 Please run: from google.colab import drive; drive.mount('/content/drive')")
        return False

def check_base_model():
    """Check if Qwen base model is available"""
    print("🔍 Checking Qwen base model...")
    
    base_model_path = Path("/content/drive/MyDrive/NEUROGLYPH/models/base/Qwen2.5-Coder-1.5B-Instruct")
    
    if base_model_path.exists():
        print("✅ Qwen base model found!")
        
        # Check for required files
        required_files = ["config.json", "tokenizer.json", "pytorch_model.bin"]
        missing_files = []
        
        for file_name in required_files:
            file_path = base_model_path / file_name
            if file_path.exists():
                print(f"  ✅ {file_name}")
            else:
                print(f"  ❌ {file_name} (missing)")
                missing_files.append(file_name)
        
        if len(missing_files) == 0:
            print("✅ Base model complete!")
            return True
        else:
            print(f"❌ Missing base model files: {missing_files}")
            return False
    else:
        print("❌ Qwen base model not found!")
        print(f"📁 Expected location: {base_model_path}")
        return False

def check_training_package():
    """Check if training package is uploaded"""
    print("🔍 Checking NEUROGLYPH training package...")
    
    package_path = Path("/content/drive/MyDrive/NEUROGLYPH_TRAINING_PACKAGE")
    
    if package_path.exists():
        print("✅ Training package found!")
        
        # Check required components
        components = {
            "datasets/neuroglyph_ULTIMATE_dataset_10k.json": "Dataset",
            "tokenizer_ultimate/tokenizer.json": "Tokenizer",
            "tokenizer_ultimate/vocab.json": "Vocabulary",
            "tokenizer_ultimate/added_tokens.json": "NEUROGLYPH symbols",
            "NEUROGLYPH_ULTIMATE_Training.ipynb": "Training notebook"
        }
        
        missing_components = []
        
        for file_path, description in components.items():
            full_path = package_path / file_path
            if full_path.exists():
                print(f"  ✅ {description}")
            else:
                print(f"  ❌ {description} (missing)")
                missing_components.append(file_path)
        
        if len(missing_components) == 0:
            print("✅ Training package complete!")
            return True
        else:
            print(f"❌ Missing components: {missing_components}")
            return False
    else:
        print("❌ Training package not found!")
        print(f"📁 Expected location: {package_path}")
        return False

def check_dataset_quality():
    """Check dataset quality and statistics"""
    print("🔍 Checking dataset quality...")
    
    dataset_path = Path("/content/drive/MyDrive/NEUROGLYPH_TRAINING_PACKAGE/datasets/neuroglyph_ULTIMATE_dataset_10k.json")
    
    try:
        with open(dataset_path, 'r') as f:
            dataset = json.load(f)
        
        print(f"✅ Dataset loaded successfully!")
        print(f"  📊 Total examples: {len(dataset):,}")
        
        # Check first example structure
        if len(dataset) > 0:
            first_example = dataset[0]
            required_keys = ["instruction", "input", "output"]
            
            for key in required_keys:
                if key in first_example:
                    print(f"  ✅ {key} field present")
                else:
                    print(f"  ❌ {key} field missing")
                    return False
        
        print("✅ Dataset structure valid!")
        return True
        
    except Exception as e:
        print(f"❌ Dataset check failed: {e}")
        return False

def check_tokenizer_symbols():
    """Check NEUROGLYPH symbols in tokenizer"""
    print("🔍 Checking NEUROGLYPH symbols...")
    
    symbols_path = Path("/content/drive/MyDrive/NEUROGLYPH_TRAINING_PACKAGE/tokenizer_ultimate/added_tokens.json")
    
    try:
        with open(symbols_path, 'r') as f:
            added_tokens = json.load(f)
        
        print(f"✅ Added tokens loaded!")
        print(f"  🔢 Total symbols: {len(added_tokens):,}")
        
        # Count ng: symbols
        ng_symbols = sum(1 for token in added_tokens if isinstance(token, str) and token.startswith('ng:'))
        print(f"  🎯 NEUROGLYPH symbols: {ng_symbols:,}")
        
        if ng_symbols >= 9000:
            print("✅ NEUROGLYPH symbols complete!")
            return True
        else:
            print(f"❌ Insufficient NEUROGLYPH symbols: {ng_symbols} < 9000")
            return False
            
    except Exception as e:
        print(f"❌ Tokenizer symbols check failed: {e}")
        return False

def check_gpu_availability():
    """Check GPU availability"""
    print("🔍 Checking GPU availability...")
    
    try:
        import torch
        
        if torch.cuda.is_available():
            gpu_name = torch.cuda.get_device_name(0)
            gpu_memory = torch.cuda.get_device_properties(0).total_memory / 1e9
            
            print(f"✅ GPU available: {gpu_name}")
            print(f"  💾 GPU Memory: {gpu_memory:.1f} GB")
            
            if gpu_memory >= 15:
                print("✅ Sufficient GPU memory for training!")
                return True
            else:
                print("⚠️ Limited GPU memory - consider reducing batch size")
                return True
        else:
            print("❌ No GPU available!")
            print("📋 Please set Runtime → Change runtime type → GPU")
            return False
            
    except ImportError:
        print("⚠️ PyTorch not installed - will be installed during setup")
        return True

def main():
    """Main setup verification"""
    print("🚀 NEUROGLYPH ULTIMATE SETUP VERIFICATION")
    print("=" * 60)
    print("Verifying all components for training the first symbolic LLM!")
    print()
    
    checks = [
        ("Google Drive Mount", check_google_drive),
        ("Qwen Base Model", check_base_model),
        ("Training Package", check_training_package),
        ("Dataset Quality", check_dataset_quality),
        ("NEUROGLYPH Symbols", check_tokenizer_symbols),
        ("GPU Availability", check_gpu_availability)
    ]
    
    results = {}
    
    for check_name, check_function in checks:
        print(f"\n{'='*20} {check_name} {'='*20}")
        results[check_name] = check_function()
    
    print("\n" + "="*60)
    print("📊 SETUP VERIFICATION SUMMARY:")
    print("-" * 30)
    
    all_passed = True
    for check_name, result in results.items():
        status = "✅ PASSED" if result else "❌ FAILED"
        print(f"  {check_name:20}: {status}")
        if not result:
            all_passed = False
    
    print()
    if all_passed:
        print("🎉 ALL CHECKS PASSED!")
        print("🚀 READY TO TRAIN NEUROGLYPH ULTIMATE!")
        print("   • Open NEUROGLYPH_ULTIMATE_Training.ipynb")
        print("   • Execute cells sequentially")
        print("   • Monitor training progress")
        print("   • Enjoy making AI history!")
    else:
        print("❌ SOME CHECKS FAILED!")
        print("🔧 Please fix the issues above before training.")
        print("📋 Refer to README.md for detailed instructions.")
    
    return all_passed

if __name__ == "__main__":
    success = main()
    if not success:
        exit(1)
