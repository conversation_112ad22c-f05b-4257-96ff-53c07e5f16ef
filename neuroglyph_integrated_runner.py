#!/usr/bin/env python3
"""
NEUROGLYPH INTEGRATED TRAINING RUNNER
=====================================

Script di integrazione che combina il SOCRATE trainer con l'interface simbolica
per creare il primo sistema di training LLM con ragionamento simbolico integrato.

Questo script:
1. Coordina SOCRATE engine, symbolic interface e training pipeline
2. Implementa curriculum learning basato su complessità simbolica
3. Fornisce validazione logica real-time durante training
4. Genera report completi di performance simbolica
5. Gestisce fallback robusti quando SOCRATE non è disponibile

Autore: NEUROGLYPH ULTRA Team
Data: 2025-01-26
"""

import os
import sys
import json
import time
import argparse
from datetime import datetime
from pathlib import Path
from typing import Dict, List, Any, Optional

# Add NEUROGLYPH paths
current_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.append(current_dir)

# Import NEUROGLYPH components
try:
    from neuroglyph_socrate_trainer import SOCRATEEnhancedTrainer, SOCRATETrainingConfig
    from neuroglyph_symbolic_interface import SymbolicReasoningInterface
    print("✅ NEUROGLYPH components imported successfully")
except ImportError as e:
    print(f"❌ Failed to import NEUROGLYPH components: {e}")
    sys.exit(1)

class NeuroglyphIntegratedRunner:
    """Runner principale per training integrato NEUROGLYPH."""
    
    def __init__(self, config_path: Optional[str] = None):
        """Inizializza runner con configurazione."""
        self.start_time = datetime.now()
        
        # Load configuration
        if config_path and os.path.exists(config_path):
            self.config = self._load_config(config_path)
        else:
            self.config = SOCRATETrainingConfig()
        
        # Initialize components
        self.symbolic_interface = SymbolicReasoningInterface()
        self.trainer = SOCRATEEnhancedTrainer(self.config)
        
        # Training state
        self.training_session_id = f"neuroglyph_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
        self.results = {
            "session_id": self.training_session_id,
            "start_time": self.start_time.isoformat(),
            "config": self.config.__dict__,
            "symbolic_stats": {},
            "training_results": {},
            "validation_results": {},
            "performance_metrics": {}
        }
        
        print(f"🚀 NEUROGLYPH Integrated Runner initialized")
        print(f"   Session ID: {self.training_session_id}")
        print(f"   SOCRATE Available: {self.symbolic_interface.socrate_available}")
        print(f"   Device: {self.trainer.device}")
    
    def _load_config(self, config_path: str) -> SOCRATETrainingConfig:
        """Carica configurazione da file."""
        try:
            with open(config_path, 'r') as f:
                config_dict = json.load(f)
            
            # Create config object from dict
            config = SOCRATETrainingConfig()
            for key, value in config_dict.items():
                if hasattr(config, key):
                    setattr(config, key, value)
            
            print(f"📥 Configuration loaded from: {config_path}")
            return config
            
        except Exception as e:
            print(f"⚠️ Failed to load config from {config_path}: {e}")
            print("   Using default configuration")
            return SOCRATETrainingConfig()
    
    def run_complete_training(self) -> bool:
        """Esegue training completo con tutte le fasi."""
        print(f"\n🧠 STARTING NEUROGLYPH INTEGRATED TRAINING")
        print("=" * 60)
        print(f"Session: {self.training_session_id}")
        print(f"Start Time: {self.start_time}")
        print("=" * 60)
        
        success = True
        
        try:
            # Phase 1: Pre-training analysis
            print(f"\\n📊 PHASE 1: PRE-TRAINING ANALYSIS")
            self._run_pretraining_analysis()
            
            # Phase 2: Dataset enhancement
            print(f"\\n🔧 PHASE 2: DATASET ENHANCEMENT")
            self._enhance_dataset_with_symbolic_reasoning()
            
            # Phase 3: Curriculum learning setup
            print(f"\\n📚 PHASE 3: CURRICULUM LEARNING SETUP")
            self._setup_curriculum_learning()
            
            # Phase 4: Integrated training
            print(f"\\n🚀 PHASE 4: SOCRATE-INTEGRATED TRAINING")
            training_success = self._run_integrated_training()
            
            # Phase 5: Post-training validation
            print(f"\\n✅ PHASE 5: POST-TRAINING VALIDATION")
            self._run_posttraining_validation()
            
            # Phase 6: Results analysis
            print(f"\\n📈 PHASE 6: RESULTS ANALYSIS")
            self._analyze_training_results()
            
            success = training_success
            
        except Exception as e:
            print(f"❌ Error in integrated training: {e}")
            success = False
        
        finally:
            # Save results regardless of success/failure
            self._save_session_results()
        
        # Final summary
        self._print_final_summary(success)
        return success
    
    def _run_pretraining_analysis(self):
        """Analisi pre-training del dataset e sistema."""
        print("🔍 Analyzing training environment...")
        
        # Check dataset
        if os.path.exists(self.config.dataset_path):
            with open(self.config.dataset_path, 'r') as f:
                dataset = json.load(f)
            
            print(f"   Dataset: {len(dataset)} examples")
            
            # Analyze sample complexity
            sample_complexities = []
            for i, example in enumerate(dataset[:50]):  # Sample first 50
                instruction = example.get("instruction", "")
                complexity = self.symbolic_interface.get_curriculum_difficulty(instruction)
                sample_complexities.append(complexity)
            
            avg_complexity = sum(sample_complexities) / len(sample_complexities)
            print(f"   Average sample complexity: {avg_complexity:.3f}")
            
            self.results["symbolic_stats"]["dataset_size"] = len(dataset)
            self.results["symbolic_stats"]["average_complexity"] = avg_complexity
            
        else:
            print(f"   ⚠️ Dataset not found: {self.config.dataset_path}")
        
        # Check symbolic interface
        interface_stats = self.symbolic_interface.get_statistics()
        print(f"   SOCRATE Engine: {'Available' if interface_stats['socrate_available'] else 'Fallback mode'}")
        
        self.results["symbolic_stats"]["interface_stats"] = interface_stats
    
    def _enhance_dataset_with_symbolic_reasoning(self):
        """Arricchisce dataset con ragionamento simbolico."""
        print("🔧 Enhancing dataset with symbolic reasoning...")
        
        if not os.path.exists(self.config.dataset_path):
            print("   ❌ Dataset not found, skipping enhancement")
            return
        
        # Load dataset
        with open(self.config.dataset_path, 'r') as f:
            dataset = json.load(f)
        
        enhanced_examples = 0
        total_symbolic_score = 0.0
        
        # Process subset for demonstration
        sample_size = min(100, len(dataset))
        print(f"   Processing {sample_size} examples for symbolic enhancement...")
        
        for i in range(sample_size):
            example = dataset[i]
            instruction = example.get("instruction", "")
            
            try:
                # Parse symbolically
                symbolic_repr = self.symbolic_interface.parse_natural_language(instruction)
                
                # Solve symbolically
                solution = self.symbolic_interface.solve_symbolically(symbolic_repr)
                
                if solution.logical_validity:
                    enhanced_examples += 1
                    total_symbolic_score += solution.overall_confidence
                
            except Exception as e:
                print(f"   ⚠️ Error processing example {i}: {e}")
        
        avg_symbolic_score = total_symbolic_score / enhanced_examples if enhanced_examples > 0 else 0.0
        
        print(f"   Enhanced: {enhanced_examples}/{sample_size} examples")
        print(f"   Average symbolic score: {avg_symbolic_score:.3f}")
        
        self.results["symbolic_stats"]["enhanced_examples"] = enhanced_examples
        self.results["symbolic_stats"]["enhancement_rate"] = enhanced_examples / sample_size
        self.results["symbolic_stats"]["avg_symbolic_score"] = avg_symbolic_score
    
    def _setup_curriculum_learning(self):
        """Setup curriculum learning basato su complessità simbolica."""
        print("📚 Setting up curriculum learning...")
        
        if self.config.dynamic_curriculum:
            print("   Dynamic curriculum learning: ENABLED")
            print(f"   Curriculum will adapt based on symbolic complexity")
            print(f"   Reasoning loss weight: {self.config.reasoning_loss_weight}")
            print(f"   Logic validation threshold: {self.config.logic_validation_threshold}")
        else:
            print("   Dynamic curriculum learning: DISABLED")
        
        self.results["training_results"]["curriculum_enabled"] = self.config.dynamic_curriculum
    
    def _run_integrated_training(self) -> bool:
        """Esegue training integrato principale."""
        print("🚀 Starting SOCRATE-integrated training...")
        
        # Run training with symbolic integration
        training_start = time.time()
        success = self.trainer.train_with_socrate_integration()
        training_time = time.time() - training_start
        
        print(f"   Training completed in {training_time:.1f} seconds")
        print(f"   Success: {success}")
        
        # Collect training results
        self.results["training_results"]["success"] = success
        self.results["training_results"]["training_time"] = training_time
        self.results["training_results"]["trainer_logs"] = self.trainer.training_logs
        
        return success
    
    def _run_posttraining_validation(self):
        """Validazione post-training con test simbolici."""
        print("✅ Running post-training validation...")
        
        # Test problems for validation
        validation_problems = [
            "If it rains, the ground gets wet. The ground is not wet. Did it rain?",
            "All humans are mortal. Socrates is human. Is Socrates mortal?",
            "Either the system is secure or it has vulnerabilities. It's not secure. Does it have vulnerabilities?",
            "If the server is running, it responds to ping. The server doesn't respond to ping. Is the server running?",
            "All cats are mammals. Fluffy is a cat. Is Fluffy a mammal?"
        ]
        
        validation_results = []
        total_symbolic_score = 0.0
        
        for i, problem in enumerate(validation_problems):
            print(f"   Testing problem {i+1}: {problem[:50]}...")
            
            try:
                # Parse and solve with symbolic interface
                symbolic_repr = self.symbolic_interface.parse_natural_language(problem)
                solution = self.symbolic_interface.solve_symbolically(symbolic_repr)
                
                result = {
                    "problem": problem,
                    "complexity": symbolic_repr.complexity_score,
                    "logical_validity": solution.logical_validity,
                    "confidence": solution.overall_confidence,
                    "reasoning_steps": len(solution.reasoning_steps),
                    "execution_time": solution.execution_time
                }
                
                validation_results.append(result)
                total_symbolic_score += solution.overall_confidence
                
                print(f"     Complexity: {symbolic_repr.complexity_score:.2f}")
                print(f"     Valid: {solution.logical_validity}")
                print(f"     Confidence: {solution.overall_confidence:.2f}")
                
            except Exception as e:
                print(f"     ⚠️ Error: {e}")
        
        avg_validation_score = total_symbolic_score / len(validation_results) if validation_results else 0.0
        
        print(f"   Validation completed: {len(validation_results)} problems")
        print(f"   Average symbolic score: {avg_validation_score:.3f}")
        
        self.results["validation_results"]["problems_tested"] = len(validation_results)
        self.results["validation_results"]["results"] = validation_results
        self.results["validation_results"]["avg_score"] = avg_validation_score
    
    def _analyze_training_results(self):
        """Analizza risultati di training completi."""
        print("📈 Analyzing training results...")
        
        # Calculate performance metrics
        metrics = {}
        
        # Symbolic reasoning performance
        if "symbolic_stats" in self.results:
            symbolic_stats = self.results["symbolic_stats"]
            metrics["symbolic_enhancement_rate"] = symbolic_stats.get("enhancement_rate", 0.0)
            metrics["avg_dataset_complexity"] = symbolic_stats.get("average_complexity", 0.0)
        
        # Training performance
        if "training_results" in self.results:
            training_results = self.results["training_results"]
            metrics["training_success"] = training_results.get("success", False)
            metrics["training_time"] = training_results.get("training_time", 0.0)
        
        # Validation performance
        if "validation_results" in self.results:
            validation_results = self.results["validation_results"]
            metrics["validation_score"] = validation_results.get("avg_score", 0.0)
            metrics["problems_solved"] = validation_results.get("problems_tested", 0)
        
        # Interface statistics
        interface_stats = self.symbolic_interface.get_statistics()
        metrics["interface_problems_solved"] = interface_stats["problems_solved"]
        metrics["interface_validity_rate"] = interface_stats["logical_validity_rate"]
        
        print(f"   Performance Metrics:")
        for key, value in metrics.items():
            if isinstance(value, float):
                print(f"     {key}: {value:.3f}")
            else:
                print(f"     {key}: {value}")
        
        self.results["performance_metrics"] = metrics
    
    def _save_session_results(self):
        """Salva risultati della sessione."""
        print("💾 Saving session results...")
        
        # Add final metadata
        self.results["end_time"] = datetime.now().isoformat()
        self.results["total_duration"] = (datetime.now() - self.start_time).total_seconds()
        
        # Create results directory
        results_dir = Path("neuroglyph/results")
        results_dir.mkdir(parents=True, exist_ok=True)
        
        # Save detailed results
        results_file = results_dir / f"{self.training_session_id}_results.json"
        
        try:
            with open(results_file, 'w') as f:
                json.dump(self.results, f, indent=2, default=str)
            
            print(f"   Results saved to: {results_file}")
            
            # Save summary
            summary_file = results_dir / f"{self.training_session_id}_summary.txt"
            self._save_text_summary(summary_file)
            
        except Exception as e:
            print(f"   ⚠️ Error saving results: {e}")
    
    def _save_text_summary(self, summary_file: Path):
        """Salva summary testuale dei risultati."""
        try:
            with open(summary_file, 'w') as f:
                f.write(f"NEUROGLYPH TRAINING SESSION SUMMARY\\n")
                f.write(f"{'='*50}\\n")
                f.write(f"Session ID: {self.training_session_id}\\n")
                f.write(f"Start Time: {self.results['start_time']}\\n")
                f.write(f"End Time: {self.results['end_time']}\\n")
                f.write(f"Duration: {self.results['total_duration']:.1f} seconds\\n\\n")
                
                f.write(f"CONFIGURATION:\\n")
                f.write(f"- Base Model: {self.config.base_model}\\n")
                f.write(f"- Dataset: {self.config.dataset_path}\\n")
                f.write(f"- Symbolic Validation: {self.config.symbolic_validation}\\n")
                f.write(f"- Dynamic Curriculum: {self.config.dynamic_curriculum}\\n\\n")
                
                f.write(f"PERFORMANCE METRICS:\\n")
                for key, value in self.results.get("performance_metrics", {}).items():
                    f.write(f"- {key}: {value}\\n")
                
                f.write(f"\\nTRAINING SUCCESS: {self.results.get('training_results', {}).get('success', False)}\\n")
            
            print(f"   Summary saved to: {summary_file}")
            
        except Exception as e:
            print(f"   ⚠️ Error saving summary: {e}")
    
    def _print_final_summary(self, success: bool):
        """Stampa summary finale."""
        print(f"\\n🎯 NEUROGLYPH TRAINING SESSION COMPLETED")
        print("=" * 60)
        print(f"Session ID: {self.training_session_id}")
        print(f"Duration: {(datetime.now() - self.start_time).total_seconds():.1f} seconds")
        print(f"Overall Success: {'✅ YES' if success else '❌ NO'}")
        
        if "performance_metrics" in self.results:
            metrics = self.results["performance_metrics"]
            print(f"\\n📊 Key Metrics:")
            print(f"  Training Success: {metrics.get('training_success', False)}")
            print(f"  Validation Score: {metrics.get('validation_score', 0.0):.3f}")
            print(f"  Symbolic Enhancement Rate: {metrics.get('symbolic_enhancement_rate', 0.0):.2%}")
            print(f"  Interface Validity Rate: {metrics.get('interface_validity_rate', 0.0):.2%}")
        
        if self.symbolic_interface.socrate_available:
            print(f"\\n🧠 SOCRATE Engine: Active")
        else:
            print(f"\\n⚠️ SOCRATE Engine: Fallback mode")
        
        print(f"\\n💾 Results saved in: neuroglyph/results/")
        print("=" * 60)

def main():
    """Entry point principale."""
    parser = argparse.ArgumentParser(description="NEUROGLYPH Integrated Training Runner")
    parser.add_argument("--config", type=str, help="Path to configuration file")
    parser.add_argument("--verbose", action="store_true", help="Enable verbose output")
    
    args = parser.parse_args()
    
    print("🧠 NEUROGLYPH INTEGRATED TRAINING RUNNER")
    print("=========================================")
    print("Advanced symbolic reasoning LLM training")
    print()
    
    try:
        # Initialize runner
        runner = NeuroglyphIntegratedRunner(config_path=args.config)
        
        # Run complete training
        success = runner.run_complete_training()
        
        # Exit with appropriate code
        sys.exit(0 if success else 1)
        
    except KeyboardInterrupt:
        print("\\n⚠️ Training interrupted by user")
        sys.exit(1)
    except Exception as e:
        print(f"\\n❌ Fatal error: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
