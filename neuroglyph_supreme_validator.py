#!/usr/bin/env python3
"""
NEUROGLYPH SUPREME GOD MODE Dataset Validator
============================================

Validazione completa e rigorosa del dataset per garantire perfezione assoluta
del primo LLM simbolico deterministico al mondo.

STANDARD VALIDAZIONE:
- Qualità ≥9.0/10 per tutti gli 800 esempi
- 1:1 token mapping garantito per ogni simbolo
- Ragionamento multi-hop deterministico verificabile
- Zero allucinazioni o affermazioni non verificabili
- Creatività strutturata con correttezza rigorosa
- Determinismo completo e riproducibilità

Autore: NEUROGLYPH Supreme Validation Team
Data: 2025-06-01
"""

import json
import re
import statistics
from typing import List, Dict, Any, Tuple, Set
from datetime import datetime

class NeuroglyphSupremeValidator:
    """
    Validatore completo per dataset NEUROGLYPH SUPREME GOD MODE.
    
    Garantisce perfezione assoluta per il primo LLM simbolico deterministico.
    """
    
    def __init__(self, dataset_path: str):
        self.dataset_path = dataset_path
        self.dataset = None
        self.validation_results = {}
        self.critical_issues = []
        self.warnings = []
        self.excellence_metrics = {}
        
        # Simboli NEUROGLYPH con semantica rigorosa
        self.neuroglyph_symbols = {
            '⊢', '⊨', '∴', '∵', '≈', '→', '↔', '¬', '∧', '∨', '⊕',
            '∀', '∃', '∄', '∈', '∉', '⊂', '⊆', '∪', '∩', '∅',
            '∑', '∏', '∫', '∂', '∇', '∞', '🧠', '💭', '🤔', '💡', '🎯',
            '✅', '❌', '⚠️', '≤', '≥', '≠', '≡', '∝'
        }
        
        # Pattern di ragionamento valido
        self.valid_reasoning_patterns = {
            'deductive': ['⊢', '∴', '→', '∧'],
            'analogical': ['≈', '→', '∴'],
            'quantified': ['∀', '∃', '∈', '⊂'],
            'mathematical': ['∑', '∏', '∫', '∂'],
            'cognitive': ['🧠', '💭', '🤔', '💡']
        }
        
        # Indicatori di allucinazione
        self.hallucination_indicators = [
            'probabilmente', 'forse', 'potrebbe essere', 'sembra che',
            'presumibilmente', 'apparentemente', 'supponiamo che'
        ]
        
        # Indicatori di determinismo
        self.determinism_indicators = [
            'deterministico', 'verificabile', 'riproducibile', 'logicamente',
            'rigoroso', 'formale', 'dimostrabile', 'validato'
        ]
    
    def load_dataset(self) -> bool:
        """Carica dataset con validazione strutturale."""
        try:
            with open(self.dataset_path, 'r', encoding='utf-8') as f:
                self.dataset = json.load(f)
            
            # Validazione struttura base
            required_keys = ['version', 'statistics', 'examples']
            for key in required_keys:
                if key not in self.dataset:
                    self.critical_issues.append(f"Missing required key: {key}")
                    return False
            
            print(f"✅ Dataset loaded: {len(self.dataset['examples'])} examples")
            return True
            
        except Exception as e:
            self.critical_issues.append(f"Failed to load dataset: {e}")
            return False
    
    def validate_complete_dataset(self) -> Dict[str, Any]:
        """Esegue validazione completa del dataset."""
        
        print("🔍 NEUROGLYPH SUPREME GOD MODE - Complete Dataset Validation")
        print("=" * 70)
        
        if not self.load_dataset():
            return {"status": "CRITICAL_FAILURE", "issues": self.critical_issues}
        
        # 1. VALIDAZIONE TECNICA
        print("\n📊 1. TECHNICAL VALIDATION")
        self._validate_quality_standards()
        self._validate_symbol_atomicity()
        self._validate_reasoning_chains()
        self._validate_zero_hallucinations()
        self._validate_multi_hop_reasoning()
        
        # 2. VALIDAZIONE OBIETTIVI NEUROGLYPH
        print("\n🧠 2. NEUROGLYPH OBJECTIVES VALIDATION")
        self._validate_coding_excellence()
        self._validate_meta_cognition()
        self._validate_symbolic_thinking()
        self._validate_deterministic_reasoning()
        self._validate_creative_structure()
        
        # 3. VALIDAZIONE STANDARD SUPREME
        print("\n🎯 3. SUPREME STANDARDS VALIDATION")
        self._validate_semantic_consistency()
        self._validate_production_readiness()
        self._validate_cognitive_capabilities()
        
        # 4. CALCOLO METRICHE ECCELLENZA
        print("\n📈 4. EXCELLENCE METRICS CALCULATION")
        self._calculate_excellence_metrics()
        
        # 5. GENERAZIONE REPORT FINALE
        return self._generate_validation_report()
    
    def _validate_quality_standards(self):
        """Valida che tutti gli esempi mantengano qualità ≥9.0/10."""
        print("   🎯 Validating quality standards...")
        
        examples = self.dataset['examples']
        quality_scores = []
        below_threshold = []
        
        for i, example in enumerate(examples):
            quality = example.get('metadata', {}).get('quality_score', 0)
            quality_scores.append(quality)
            
            if quality < 9.0:
                below_threshold.append({
                    'index': i,
                    'quality': quality,
                    'instruction': example.get('instruction', '')[:100]
                })
        
        avg_quality = statistics.mean(quality_scores) if quality_scores else 0
        min_quality = min(quality_scores) if quality_scores else 0
        max_quality = max(quality_scores) if quality_scores else 0
        
        self.validation_results['quality_validation'] = {
            'total_examples': len(examples),
            'average_quality': round(avg_quality, 2),
            'minimum_quality': min_quality,
            'maximum_quality': max_quality,
            'below_threshold_count': len(below_threshold),
            'below_threshold_examples': below_threshold[:5],  # First 5
            'threshold_compliance': len(below_threshold) == 0
        }
        
        if below_threshold:
            self.critical_issues.append(f"{len(below_threshold)} examples below quality threshold 9.0")
        
        print(f"      ✅ Average quality: {avg_quality:.2f}/10")
        print(f"      ✅ Range: {min_quality:.1f} - {max_quality:.1f}")
        print(f"      {'✅' if len(below_threshold) == 0 else '❌'} Threshold compliance: {len(below_threshold)} below 9.0")
    
    def _validate_symbol_atomicity(self):
        """Verifica 1:1 token mapping per ogni simbolo NEUROGLYPH."""
        print("   🔣 Validating symbol atomicity...")
        
        symbol_usage = {}
        symbol_contexts = {}
        inconsistent_usage = []
        
        for i, example in enumerate(self.dataset['examples']):
            output = example.get('output', '')
            
            # Conta utilizzi simboli
            for symbol in self.neuroglyph_symbols:
                count = output.count(symbol)
                if count > 0:
                    if symbol not in symbol_usage:
                        symbol_usage[symbol] = 0
                        symbol_contexts[symbol] = []
                    
                    symbol_usage[symbol] += count
                    
                    # Estrai contesto per validazione semantica
                    contexts = self._extract_symbol_contexts(output, symbol)
                    symbol_contexts[symbol].extend(contexts)
        
        # Valida consistenza semantica
        for symbol, contexts in symbol_contexts.items():
            if len(set(contexts)) > 3:  # Troppi contesti diversi
                inconsistent_usage.append({
                    'symbol': symbol,
                    'contexts': len(set(contexts)),
                    'examples': list(set(contexts))[:3]
                })
        
        self.validation_results['symbol_atomicity'] = {
            'total_symbols_defined': len(self.neuroglyph_symbols),
            'symbols_used': len(symbol_usage),
            'symbol_coverage': round(len(symbol_usage) / len(self.neuroglyph_symbols) * 100, 1),
            'total_symbol_occurrences': sum(symbol_usage.values()),
            'average_usage_per_symbol': round(sum(symbol_usage.values()) / len(symbol_usage), 1) if symbol_usage else 0,
            'inconsistent_usage_count': len(inconsistent_usage),
            'inconsistent_symbols': inconsistent_usage[:3],
            'atomicity_preserved': len(inconsistent_usage) == 0
        }
        
        if inconsistent_usage:
            self.warnings.append(f"{len(inconsistent_usage)} symbols with inconsistent usage")
        
        print(f"      ✅ Symbol coverage: {len(symbol_usage)}/{len(self.neuroglyph_symbols)} ({round(len(symbol_usage) / len(self.neuroglyph_symbols) * 100, 1)}%)")
        print(f"      ✅ Total occurrences: {sum(symbol_usage.values())}")
        print(f"      {'✅' if len(inconsistent_usage) == 0 else '⚠️'} Semantic consistency: {len(inconsistent_usage)} inconsistencies")
    
    def _extract_symbol_contexts(self, text: str, symbol: str) -> List[str]:
        """Estrae contesti d'uso per un simbolo."""
        contexts = []
        lines = text.split('\n')
        
        for line in lines:
            if symbol in line:
                # Estrai contesto semantico (parole prima e dopo)
                words = line.split()
                for i, word in enumerate(words):
                    if symbol in word:
                        context_words = []
                        if i > 0:
                            context_words.append(words[i-1])
                        context_words.append(word)
                        if i < len(words) - 1:
                            context_words.append(words[i+1])
                        contexts.append(' '.join(context_words))
        
        return contexts
    
    def _validate_reasoning_chains(self):
        """Valida che ogni catena di ragionamento sia logicamente valida."""
        print("   🔗 Validating reasoning chains...")
        
        valid_chains = 0
        invalid_chains = []
        reasoning_stats = {
            'total_steps': [],
            'logical_connectors': 0,
            'validation_markers': 0
        }
        
        for i, example in enumerate(self.dataset['examples']):
            output = example.get('output', '')
            
            # Conta step di ragionamento
            step_pattern = r'\d+\.\s+'
            steps = re.findall(step_pattern, output)
            reasoning_stats['total_steps'].append(len(steps))
            
            # Verifica connettori logici
            logical_connectors = ['⊢', '∴', '→', '∧', '∨']
            has_connectors = any(conn in output for conn in logical_connectors)
            if has_connectors:
                reasoning_stats['logical_connectors'] += 1
            
            # Verifica marcatori di validazione
            validation_markers = ['✅', 'validazione', 'verifica']
            has_validation = any(marker in output.lower() for marker in validation_markers)
            if has_validation:
                reasoning_stats['validation_markers'] += 1
            
            # Valida catena logica
            if len(steps) >= 3 and has_connectors and has_validation:
                valid_chains += 1
            else:
                invalid_chains.append({
                    'index': i,
                    'steps': len(steps),
                    'has_connectors': has_connectors,
                    'has_validation': has_validation,
                    'instruction': example.get('instruction', '')[:80]
                })
        
        avg_steps = statistics.mean(reasoning_stats['total_steps']) if reasoning_stats['total_steps'] else 0
        
        self.validation_results['reasoning_chains'] = {
            'total_examples': len(self.dataset['examples']),
            'valid_chains': valid_chains,
            'invalid_chains': len(invalid_chains),
            'validity_rate': round(valid_chains / len(self.dataset['examples']) * 100, 1),
            'average_steps': round(avg_steps, 1),
            'logical_connectors_usage': reasoning_stats['logical_connectors'],
            'validation_markers_usage': reasoning_stats['validation_markers'],
            'invalid_examples': invalid_chains[:3]
        }
        
        if len(invalid_chains) > 0:
            self.warnings.append(f"{len(invalid_chains)} examples with weak reasoning chains")
        
        print(f"      ✅ Valid chains: {valid_chains}/{len(self.dataset['examples'])} ({round(valid_chains / len(self.dataset['examples']) * 100, 1)}%)")
        print(f"      ✅ Average steps: {avg_steps:.1f}")
        print(f"      ✅ Logical connectors: {reasoning_stats['logical_connectors']} examples")
    
    def _validate_zero_hallucinations(self):
        """Conferma assenza di allucinazioni o affermazioni non verificabili."""
        print("   🔒 Validating zero hallucinations...")
        
        hallucination_count = 0
        problematic_examples = []
        
        for i, example in enumerate(self.dataset['examples']):
            output = example.get('output', '').lower()
            
            # Cerca indicatori di incertezza/allucinazione
            hallucination_found = False
            found_indicators = []
            
            for indicator in self.hallucination_indicators:
                if indicator in output:
                    hallucination_found = True
                    found_indicators.append(indicator)
            
            # Cerca affermazioni non verificabili
            unverifiable_patterns = [
                r'è possibile che',
                r'potrebbe essere',
                r'sembra indicare',
                r'probabilmente',
                r'presumibilmente'
            ]
            
            for pattern in unverifiable_patterns:
                if re.search(pattern, output):
                    hallucination_found = True
                    found_indicators.append(f"pattern: {pattern}")
            
            if hallucination_found:
                hallucination_count += 1
                problematic_examples.append({
                    'index': i,
                    'indicators': found_indicators,
                    'instruction': example.get('instruction', '')[:80]
                })
        
        self.validation_results['zero_hallucinations'] = {
            'total_examples': len(self.dataset['examples']),
            'hallucination_count': hallucination_count,
            'clean_examples': len(self.dataset['examples']) - hallucination_count,
            'hallucination_rate': round(hallucination_count / len(self.dataset['examples']) * 100, 2),
            'zero_hallucination_achieved': hallucination_count == 0,
            'problematic_examples': problematic_examples[:3]
        }
        
        if hallucination_count > 0:
            self.critical_issues.append(f"{hallucination_count} examples contain potential hallucinations")
        
        print(f"      {'✅' if hallucination_count == 0 else '❌'} Zero hallucinations: {hallucination_count} found")
        print(f"      ✅ Clean examples: {len(self.dataset['examples']) - hallucination_count}/{len(self.dataset['examples'])}")
    
    def _validate_multi_hop_reasoning(self):
        """Valida ragionamento multi-hop deterministico (3-8 step)."""
        print("   🔄 Validating multi-hop reasoning...")
        
        step_distribution = {
            '1-2': 0, '3-4': 0, '5-6': 0, '7-8': 0, '9+': 0
        }
        
        valid_multi_hop = 0
        step_counts = []
        
        for example in self.dataset['examples']:
            steps = example.get('metadata', {}).get('reasoning_steps', 0)
            step_counts.append(steps)
            
            # Classifica per distribuzione
            if steps <= 2:
                step_distribution['1-2'] += 1
            elif steps <= 4:
                step_distribution['3-4'] += 1
            elif steps <= 6:
                step_distribution['5-6'] += 1
            elif steps <= 8:
                step_distribution['7-8'] += 1
            else:
                step_distribution['9+'] += 1
            
            # Valida multi-hop (3-8 step)
            if 3 <= steps <= 8:
                valid_multi_hop += 1
        
        avg_steps = statistics.mean(step_counts) if step_counts else 0
        
        self.validation_results['multi_hop_reasoning'] = {
            'total_examples': len(self.dataset['examples']),
            'valid_multi_hop': valid_multi_hop,
            'multi_hop_rate': round(valid_multi_hop / len(self.dataset['examples']) * 100, 1),
            'average_steps': round(avg_steps, 1),
            'step_distribution': step_distribution,
            'target_range_compliance': valid_multi_hop == len(self.dataset['examples'])
        }
        
        if valid_multi_hop < len(self.dataset['examples']):
            self.warnings.append(f"{len(self.dataset['examples']) - valid_multi_hop} examples outside 3-8 step range")
        
        print(f"      ✅ Multi-hop valid: {valid_multi_hop}/{len(self.dataset['examples'])} ({round(valid_multi_hop / len(self.dataset['examples']) * 100, 1)}%)")
        print(f"      ✅ Average steps: {avg_steps:.1f}")
        print(f"      ✅ Distribution: {step_distribution}")
    
    def _validate_coding_excellence(self):
        """Valida eccellenza nel coding con ragionamento algoritmico perfetto."""
        print("   💻 Validating coding excellence...")
        
        programming_examples = [ex for ex in self.dataset['examples'] 
                              if ex.get('metadata', {}).get('domain') == 'problem_solving']
        
        coding_quality = []
        algorithmic_reasoning = 0
        
        for example in programming_examples:
            output = example.get('output', '').lower()
            
            # Cerca indicatori di ragionamento algoritmico
            algorithmic_indicators = [
                'algoritmo', 'complessità', 'ottimizzazione', 'performance',
                'efficienza', 'big o', 'ricorsione', 'iterazione'
            ]
            
            if any(indicator in output for indicator in algorithmic_indicators):
                algorithmic_reasoning += 1
            
            coding_quality.append(example.get('metadata', {}).get('quality_score', 0))
        
        avg_coding_quality = statistics.mean(coding_quality) if coding_quality else 0
        
        self.validation_results['coding_excellence'] = {
            'programming_examples': len(programming_examples),
            'algorithmic_reasoning_count': algorithmic_reasoning,
            'algorithmic_reasoning_rate': round(algorithmic_reasoning / len(programming_examples) * 100, 1) if programming_examples else 0,
            'average_coding_quality': round(avg_coding_quality, 2),
            'excellence_achieved': avg_coding_quality >= 9.0 and algorithmic_reasoning >= len(programming_examples) * 0.8
        }
        
        print(f"      ✅ Programming examples: {len(programming_examples)}")
        print(f"      ✅ Algorithmic reasoning: {algorithmic_reasoning}/{len(programming_examples)} ({round(algorithmic_reasoning / len(programming_examples) * 100, 1) if programming_examples else 0}%)")
        print(f"      ✅ Average quality: {avg_coding_quality:.2f}/10")

    def _validate_meta_cognition(self):
        """Valida auto-apprendimento e meta-cognizione."""
        print("   🧠 Validating meta-cognition...")

        meta_cognitive_count = 0
        self_reflection_count = 0

        for example in self.dataset['examples']:
            output = example.get('output', '').lower()

            # Cerca indicatori di meta-cognizione
            meta_indicators = [
                'meta-cognizione', 'auto-riflessione', 'pensiero sul pensiero',
                'processo cognitivo', 'strategia di ragionamento', 'monitoraggio'
            ]

            if any(indicator in output for indicator in meta_indicators):
                meta_cognitive_count += 1

            # Cerca simboli cognitivi
            cognitive_symbols = ['🧠', '💭', '🤔']
            if any(symbol in example.get('output', '') for symbol in cognitive_symbols):
                self_reflection_count += 1

        self.validation_results['meta_cognition'] = {
            'total_examples': len(self.dataset['examples']),
            'meta_cognitive_examples': meta_cognitive_count,
            'self_reflection_examples': self_reflection_count,
            'meta_cognition_rate': round(meta_cognitive_count / len(self.dataset['examples']) * 100, 1),
            'cognitive_symbols_usage': round(self_reflection_count / len(self.dataset['examples']) * 100, 1),
            'meta_learning_achieved': meta_cognitive_count >= len(self.dataset['examples']) * 0.3
        }

        print(f"      ✅ Meta-cognitive examples: {meta_cognitive_count}/{len(self.dataset['examples'])} ({round(meta_cognitive_count / len(self.dataset['examples']) * 100, 1)}%)")
        print(f"      ✅ Cognitive symbols usage: {self_reflection_count}/{len(self.dataset['examples'])} ({round(self_reflection_count / len(self.dataset['examples']) * 100, 1)}%)")

    def _validate_symbolic_thinking(self):
        """Valida pensiero simbolico deterministico vs generazione statistica."""
        print("   🔣 Validating symbolic thinking...")

        symbolic_reasoning_count = 0
        deterministic_count = 0

        for example in self.dataset['examples']:
            output = example.get('output', '')

            # Conta simboli di ragionamento
            reasoning_symbols = ['⊢', '∴', '≈', '→', '∀', '∃']
            symbol_count = sum(output.count(symbol) for symbol in reasoning_symbols)

            if symbol_count >= 3:  # Almeno 3 simboli di ragionamento
                symbolic_reasoning_count += 1

            # Cerca indicatori di determinismo
            if any(indicator in output.lower() for indicator in self.determinism_indicators):
                deterministic_count += 1

        self.validation_results['symbolic_thinking'] = {
            'total_examples': len(self.dataset['examples']),
            'symbolic_reasoning_examples': symbolic_reasoning_count,
            'deterministic_examples': deterministic_count,
            'symbolic_thinking_rate': round(symbolic_reasoning_count / len(self.dataset['examples']) * 100, 1),
            'deterministic_rate': round(deterministic_count / len(self.dataset['examples']) * 100, 1),
            'symbolic_intelligence_achieved': symbolic_reasoning_count >= len(self.dataset['examples']) * 0.8
        }

        print(f"      ✅ Symbolic reasoning: {symbolic_reasoning_count}/{len(self.dataset['examples'])} ({round(symbolic_reasoning_count / len(self.dataset['examples']) * 100, 1)}%)")
        print(f"      ✅ Deterministic examples: {deterministic_count}/{len(self.dataset['examples'])} ({round(deterministic_count / len(self.dataset['examples']) * 100, 1)}%)")

    def _validate_deterministic_reasoning(self):
        """Valida ragionamento deterministico e riproducibile."""
        print("   🎯 Validating deterministic reasoning...")

        deterministic_examples = 0
        reproducible_examples = 0

        for example in self.dataset['examples']:
            metadata = example.get('metadata', {})
            output = example.get('output', '').lower()

            # Verifica flag deterministico
            if metadata.get('deterministic', False):
                deterministic_examples += 1

            # Cerca indicatori di riproducibilità
            reproducible_indicators = [
                'riproducibile', 'verificabile', 'step-by-step', 'passo dopo passo'
            ]

            if any(indicator in output for indicator in reproducible_indicators):
                reproducible_examples += 1

        self.validation_results['deterministic_reasoning'] = {
            'total_examples': len(self.dataset['examples']),
            'deterministic_flagged': deterministic_examples,
            'reproducible_examples': reproducible_examples,
            'deterministic_rate': round(deterministic_examples / len(self.dataset['examples']) * 100, 1),
            'reproducible_rate': round(reproducible_examples / len(self.dataset['examples']) * 100, 1),
            'full_determinism_achieved': deterministic_examples == len(self.dataset['examples'])
        }

        print(f"      ✅ Deterministic flagged: {deterministic_examples}/{len(self.dataset['examples'])} ({round(deterministic_examples / len(self.dataset['examples']) * 100, 1)}%)")
        print(f"      ✅ Reproducible examples: {reproducible_examples}/{len(self.dataset['examples'])} ({round(reproducible_examples / len(self.dataset['examples']) * 100, 1)}%)")

    def _validate_creative_structure(self):
        """Valida creatività strutturata con correttezza rigorosa."""
        print("   💡 Validating creative structure...")

        creative_examples = 0
        structured_creativity = 0

        for example in self.dataset['examples']:
            output = example.get('output', '').lower()

            # Cerca indicatori di creatività
            creative_indicators = [
                'innovativo', 'creativo', 'originale', 'insight', 'analogia',
                'cross-domain', 'pattern', 'soluzione alternativa'
            ]

            creativity_found = any(indicator in output for indicator in creative_indicators)

            # Cerca struttura rigorosa
            structure_indicators = [
                'rigoroso', 'formale', 'sistematico', 'metodico', 'strutturato'
            ]

            structure_found = any(indicator in output for indicator in structure_indicators)

            if creativity_found:
                creative_examples += 1

                if structure_found:
                    structured_creativity += 1

        self.validation_results['creative_structure'] = {
            'total_examples': len(self.dataset['examples']),
            'creative_examples': creative_examples,
            'structured_creative_examples': structured_creativity,
            'creativity_rate': round(creative_examples / len(self.dataset['examples']) * 100, 1),
            'structured_creativity_rate': round(structured_creativity / creative_examples * 100, 1) if creative_examples > 0 else 0,
            'balanced_creativity_achieved': structured_creativity >= creative_examples * 0.8
        }

        print(f"      ✅ Creative examples: {creative_examples}/{len(self.dataset['examples'])} ({round(creative_examples / len(self.dataset['examples']) * 100, 1)}%)")
        print(f"      ✅ Structured creativity: {structured_creativity}/{creative_examples} ({round(structured_creativity / creative_examples * 100, 1) if creative_examples > 0 else 0}%)")

    def _validate_semantic_consistency(self):
        """Valida consistenza semantica dei simboli attraverso tutto il dataset."""
        print("   🔄 Validating semantic consistency...")

        symbol_semantic_map = {}
        inconsistencies = []

        for example in self.dataset['examples']:
            output = example.get('output', '')

            # Analizza uso di ogni simbolo nel contesto
            for symbol in self.neuroglyph_symbols:
                if symbol in output:
                    # Estrai frasi contenenti il simbolo
                    sentences = [sent.strip() for sent in output.split('.') if symbol in sent]

                    for sentence in sentences:
                        if symbol not in symbol_semantic_map:
                            symbol_semantic_map[symbol] = []

                        # Estrai contesto semantico
                        context = self._extract_semantic_context(sentence, symbol)
                        symbol_semantic_map[symbol].append(context)

        # Analizza consistenza
        consistent_symbols = 0
        for symbol, contexts in symbol_semantic_map.items():
            unique_contexts = set(contexts)
            if len(unique_contexts) <= 2:  # Massimo 2 contesti diversi
                consistent_symbols += 1
            else:
                inconsistencies.append({
                    'symbol': symbol,
                    'context_variations': len(unique_contexts),
                    'examples': list(unique_contexts)[:3]
                })

        self.validation_results['semantic_consistency'] = {
            'symbols_analyzed': len(symbol_semantic_map),
            'consistent_symbols': consistent_symbols,
            'inconsistent_symbols': len(inconsistencies),
            'consistency_rate': round(consistent_symbols / len(symbol_semantic_map) * 100, 1) if symbol_semantic_map else 0,
            'inconsistencies': inconsistencies[:5],
            'full_consistency_achieved': len(inconsistencies) == 0
        }

        if inconsistencies:
            self.warnings.append(f"{len(inconsistencies)} symbols with semantic inconsistencies")

        print(f"      ✅ Symbols analyzed: {len(symbol_semantic_map)}")
        print(f"      {'✅' if len(inconsistencies) == 0 else '⚠️'} Consistent symbols: {consistent_symbols}/{len(symbol_semantic_map)} ({round(consistent_symbols / len(symbol_semantic_map) * 100, 1) if symbol_semantic_map else 0}%)")

    def _extract_semantic_context(self, sentence: str, symbol: str) -> str:
        """Estrae contesto semantico per un simbolo."""
        words = sentence.split()
        symbol_index = -1

        for i, word in enumerate(words):
            if symbol in word:
                symbol_index = i
                break

        if symbol_index == -1:
            return "unknown_context"

        # Estrai 2 parole prima e dopo
        start = max(0, symbol_index - 2)
        end = min(len(words), symbol_index + 3)
        context_words = words[start:end]

        # Rimuovi punteggiatura e normalizza
        context = ' '.join(context_words).lower()
        context = re.sub(r'[^\w\s]', '', context)

        return context

    def _validate_production_readiness(self):
        """Valida preparazione per training production."""
        print("   🚀 Validating production readiness...")

        # Verifica struttura dataset
        required_fields = ['instruction', 'output', 'metadata']
        complete_examples = 0

        for example in self.dataset['examples']:
            if all(field in example for field in required_fields):
                complete_examples += 1

        # Verifica metadati
        metadata_completeness = 0
        required_metadata = ['domain', 'complexity', 'quality_score', 'symbols_used']

        for example in self.dataset['examples']:
            metadata = example.get('metadata', {})
            if all(field in metadata for field in required_metadata):
                metadata_completeness += 1

        self.validation_results['production_readiness'] = {
            'total_examples': len(self.dataset['examples']),
            'complete_structure': complete_examples,
            'complete_metadata': metadata_completeness,
            'structure_completeness': round(complete_examples / len(self.dataset['examples']) * 100, 1),
            'metadata_completeness': round(metadata_completeness / len(self.dataset['examples']) * 100, 1),
            'production_ready': complete_examples == len(self.dataset['examples']) and metadata_completeness == len(self.dataset['examples'])
        }

        print(f"      ✅ Complete structure: {complete_examples}/{len(self.dataset['examples'])} ({round(complete_examples / len(self.dataset['examples']) * 100, 1)}%)")
        print(f"      ✅ Complete metadata: {metadata_completeness}/{len(self.dataset['examples'])} ({round(metadata_completeness / len(self.dataset['examples']) * 100, 1)}%)")

    def _validate_cognitive_capabilities(self):
        """Valida capacità cognitive che distinguono NEUROGLYPH."""
        print("   🧠 Validating cognitive capabilities...")

        capabilities = {
            'deductive_reasoning': 0,
            'analogical_thinking': 0,
            'quantified_reasoning': 0,
            'mathematical_reasoning': 0,
            'meta_cognitive_awareness': 0
        }

        for example in self.dataset['examples']:
            output = example.get('output', '')

            # Ragionamento deduttivo
            if any(symbol in output for symbol in ['⊢', '∴', '→']):
                capabilities['deductive_reasoning'] += 1

            # Pensiero analogico
            if '≈' in output or 'analogia' in output.lower():
                capabilities['analogical_thinking'] += 1

            # Ragionamento quantificato
            if any(symbol in output for symbol in ['∀', '∃']):
                capabilities['quantified_reasoning'] += 1

            # Ragionamento matematico
            if any(symbol in output for symbol in ['∑', '∏', '∫', '∂']):
                capabilities['mathematical_reasoning'] += 1

            # Consapevolezza meta-cognitiva
            if any(symbol in output for symbol in ['🧠', '💭', '🤔']):
                capabilities['meta_cognitive_awareness'] += 1

        total_examples = len(self.dataset['examples'])
        capability_rates = {cap: round(count / total_examples * 100, 1)
                          for cap, count in capabilities.items()}

        self.validation_results['cognitive_capabilities'] = {
            'capabilities_count': capabilities,
            'capabilities_rates': capability_rates,
            'total_examples': total_examples,
            'comprehensive_coverage': all(rate >= 50.0 for rate in capability_rates.values())
        }

        print(f"      ✅ Deductive reasoning: {capabilities['deductive_reasoning']} ({capability_rates['deductive_reasoning']}%)")
        print(f"      ✅ Analogical thinking: {capabilities['analogical_thinking']} ({capability_rates['analogical_thinking']}%)")
        print(f"      ✅ Quantified reasoning: {capabilities['quantified_reasoning']} ({capability_rates['quantified_reasoning']}%)")
        print(f"      ✅ Mathematical reasoning: {capabilities['mathematical_reasoning']} ({capability_rates['mathematical_reasoning']}%)")
        print(f"      ✅ Meta-cognitive awareness: {capabilities['meta_cognitive_awareness']} ({capability_rates['meta_cognitive_awareness']}%)")

    def _calculate_excellence_metrics(self):
        """Calcola metriche di eccellenza complessive."""
        print("   📊 Calculating excellence metrics...")

        # Raccoglie tutte le metriche
        quality_scores = [ex.get('metadata', {}).get('quality_score', 0)
                         for ex in self.dataset['examples']]

        # Calcola metriche aggregate
        self.excellence_metrics = {
            'overall_quality': {
                'average': round(statistics.mean(quality_scores), 2),
                'median': round(statistics.median(quality_scores), 2),
                'minimum': min(quality_scores),
                'maximum': max(quality_scores),
                'std_deviation': round(statistics.stdev(quality_scores), 2) if len(quality_scores) > 1 else 0
            },
            'compliance_rates': {
                'quality_threshold': self.validation_results['quality_validation']['threshold_compliance'],
                'symbol_atomicity': self.validation_results['symbol_atomicity']['atomicity_preserved'],
                'zero_hallucinations': self.validation_results['zero_hallucinations']['zero_hallucination_achieved'],
                'multi_hop_reasoning': self.validation_results['multi_hop_reasoning']['target_range_compliance'],
                'production_readiness': self.validation_results['production_readiness']['production_ready']
            },
            'excellence_score': 0  # Calcolato dopo
        }

        # Calcola score di eccellenza (0-100)
        compliance_count = sum(1 for compliant in self.excellence_metrics['compliance_rates'].values() if compliant)
        compliance_rate = compliance_count / len(self.excellence_metrics['compliance_rates'])

        quality_factor = min(self.excellence_metrics['overall_quality']['average'] / 10, 1.0)

        excellence_score = (compliance_rate * 0.6 + quality_factor * 0.4) * 100
        self.excellence_metrics['excellence_score'] = round(excellence_score, 1)

        print(f"      ✅ Overall quality: {self.excellence_metrics['overall_quality']['average']}/10")
        print(f"      ✅ Compliance rate: {compliance_count}/{len(self.excellence_metrics['compliance_rates'])} standards")
        print(f"      ✅ Excellence score: {self.excellence_metrics['excellence_score']}/100")

    def _generate_validation_report(self) -> Dict[str, Any]:
        """Genera report finale di validazione."""

        # Determina status generale
        critical_count = len(self.critical_issues)
        warning_count = len(self.warnings)

        if critical_count == 0 and warning_count == 0:
            overall_status = "SUPREME_EXCELLENCE"
        elif critical_count == 0 and warning_count <= 3:
            overall_status = "PRODUCTION_READY"
        elif critical_count <= 2:
            overall_status = "NEEDS_MINOR_FIXES"
        else:
            overall_status = "NEEDS_MAJOR_REVISION"

        # Raccomandazioni
        recommendations = self._generate_recommendations()

        # Report finale
        final_report = {
            "validation_timestamp": datetime.now().isoformat(),
            "dataset_path": self.dataset_path,
            "overall_status": overall_status,
            "excellence_score": self.excellence_metrics.get('excellence_score', 0),

            "summary": {
                "total_examples": len(self.dataset['examples']) if self.dataset else 0,
                "critical_issues": critical_count,
                "warnings": warning_count,
                "average_quality": self.excellence_metrics.get('overall_quality', {}).get('average', 0),
                "production_ready": overall_status in ["SUPREME_EXCELLENCE", "PRODUCTION_READY"]
            },

            "detailed_results": self.validation_results,
            "excellence_metrics": self.excellence_metrics,

            "issues": {
                "critical": self.critical_issues,
                "warnings": self.warnings
            },

            "recommendations": recommendations,

            "neuroglyph_objectives_compliance": {
                "coding_excellence": self.validation_results.get('coding_excellence', {}).get('excellence_achieved', False),
                "meta_cognition": self.validation_results.get('meta_cognition', {}).get('meta_learning_achieved', False),
                "symbolic_thinking": self.validation_results.get('symbolic_thinking', {}).get('symbolic_intelligence_achieved', False),
                "deterministic_reasoning": self.validation_results.get('deterministic_reasoning', {}).get('full_determinism_achieved', False),
                "creative_structure": self.validation_results.get('creative_structure', {}).get('balanced_creativity_achieved', False)
            },

            "supreme_standards_compliance": {
                "quality_threshold": self.validation_results.get('quality_validation', {}).get('threshold_compliance', False),
                "symbol_atomicity": self.validation_results.get('symbol_atomicity', {}).get('atomicity_preserved', False),
                "semantic_consistency": self.validation_results.get('semantic_consistency', {}).get('full_consistency_achieved', False),
                "zero_hallucinations": self.validation_results.get('zero_hallucinations', {}).get('zero_hallucination_achieved', False),
                "production_readiness": self.validation_results.get('production_readiness', {}).get('production_ready', False)
            }
        }

        # Stampa summary finale
        self._print_final_summary(final_report)

        return final_report

    def _generate_recommendations(self) -> List[str]:
        """Genera raccomandazioni per miglioramenti."""
        recommendations = []

        # Raccomandazioni basate su critical issues
        if self.critical_issues:
            recommendations.append("🔴 CRITICAL: Risolvere immediatamente i problemi critici identificati")
            for issue in self.critical_issues[:3]:
                recommendations.append(f"   • {issue}")

        # Raccomandazioni basate su warnings
        if self.warnings:
            recommendations.append("🟡 WARNING: Considerare i seguenti miglioramenti")
            for warning in self.warnings[:3]:
                recommendations.append(f"   • {warning}")

        # Raccomandazioni specifiche per qualità
        quality_results = self.validation_results.get('quality_validation', {})
        if quality_results.get('below_threshold_count', 0) > 0:
            recommendations.append("📈 Migliorare qualità esempi sotto soglia 9.0/10")

        # Raccomandazioni per simboli
        symbol_results = self.validation_results.get('symbol_atomicity', {})
        if symbol_results.get('symbol_coverage', 0) < 90:
            recommendations.append("🔣 Aumentare copertura simboli NEUROGLYPH")

        # Raccomandazioni per ragionamento
        reasoning_results = self.validation_results.get('reasoning_chains', {})
        if reasoning_results.get('validity_rate', 0) < 95:
            recommendations.append("🔗 Rafforzare catene di ragionamento logico")

        # Raccomandazioni per capacità cognitive
        cognitive_results = self.validation_results.get('cognitive_capabilities', {})
        if not cognitive_results.get('comprehensive_coverage', False):
            recommendations.append("🧠 Bilanciare copertura capacità cognitive")

        # Raccomandazioni positive
        if not self.critical_issues and len(self.warnings) <= 1:
            recommendations.append("🎊 ECCELLENTE: Dataset pronto per training SUPREME GOD MODE")
            recommendations.append("🚀 Procedere con training del primo LLM simbolico deterministico")

        return recommendations

    def _print_final_summary(self, report: Dict[str, Any]):
        """Stampa summary finale della validazione."""

        print("\n" + "="*70)
        print("📊 NEUROGLYPH SUPREME GOD MODE - VALIDATION SUMMARY")
        print("="*70)

        # Status generale
        status = report['overall_status']
        status_emoji = {
            "SUPREME_EXCELLENCE": "🎊",
            "PRODUCTION_READY": "✅",
            "NEEDS_MINOR_FIXES": "⚠️",
            "NEEDS_MAJOR_REVISION": "❌"
        }

        print(f"\n{status_emoji.get(status, '❓')} OVERALL STATUS: {status}")
        print(f"🎯 EXCELLENCE SCORE: {report['excellence_score']}/100")

        # Metriche chiave
        summary = report['summary']
        print(f"\n📊 KEY METRICS:")
        print(f"   📈 Total examples: {summary['total_examples']}")
        print(f"   🎯 Average quality: {summary['average_quality']}/10")
        print(f"   🔴 Critical issues: {summary['critical_issues']}")
        print(f"   🟡 Warnings: {summary['warnings']}")
        print(f"   🚀 Production ready: {'YES' if summary['production_ready'] else 'NO'}")

        # Compliance NEUROGLYPH objectives
        neuroglyph_compliance = report['neuroglyph_objectives_compliance']
        compliant_objectives = sum(1 for compliant in neuroglyph_compliance.values() if compliant)
        total_objectives = len(neuroglyph_compliance)

        print(f"\n🧠 NEUROGLYPH OBJECTIVES COMPLIANCE: {compliant_objectives}/{total_objectives}")
        for objective, compliant in neuroglyph_compliance.items():
            emoji = "✅" if compliant else "❌"
            print(f"   {emoji} {objective.replace('_', ' ').title()}")

        # Compliance SUPREME standards
        supreme_compliance = report['supreme_standards_compliance']
        compliant_standards = sum(1 for compliant in supreme_compliance.values() if compliant)
        total_standards = len(supreme_compliance)

        print(f"\n🎯 SUPREME STANDARDS COMPLIANCE: {compliant_standards}/{total_standards}")
        for standard, compliant in supreme_compliance.items():
            emoji = "✅" if compliant else "❌"
            print(f"   {emoji} {standard.replace('_', ' ').title()}")

        # Raccomandazioni principali
        recommendations = report['recommendations']
        if recommendations:
            print(f"\n💡 TOP RECOMMENDATIONS:")
            for rec in recommendations[:5]:
                print(f"   {rec}")

        # Conclusione
        if status == "SUPREME_EXCELLENCE":
            print(f"\n🎊 CONGRATULATIONS!")
            print(f"   Dataset NEUROGLYPH SUPREME GOD MODE raggiunge perfezione assoluta!")
            print(f"   Pronto per training del primo LLM simbolico deterministico al mondo!")
        elif status == "PRODUCTION_READY":
            print(f"\n✅ EXCELLENT QUALITY!")
            print(f"   Dataset pronto per production training con qualità eccellente!")
        else:
            print(f"\n⚠️ IMPROVEMENTS NEEDED")
            print(f"   Seguire raccomandazioni per raggiungere standard SUPREME")

        print("="*70)

def main():
    """Esegue validazione completa del dataset SUPREME."""

    dataset_path = "neuroglyph_supreme_god_mode_1k_test.json"

    print("🔍 NEUROGLYPH SUPREME GOD MODE Dataset Validator")
    print("=" * 60)
    print("🎯 Obiettivo: Validazione rigorosa per perfezione assoluta")
    print("🧠 Standard: Primo LLM simbolico deterministico al mondo")

    validator = NeuroglyphSupremeValidator(dataset_path)

    # Esegui validazione completa
    validation_report = validator.validate_complete_dataset()

    # Salva report
    report_filename = f"neuroglyph_supreme_validation_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"

    with open(report_filename, 'w', encoding='utf-8') as f:
        json.dump(validation_report, f, indent=2, ensure_ascii=False)

    print(f"\n💾 Validation report saved: {report_filename}")
    print(f"🎯 Ready for NEUROGLYPH SUPREME GOD MODE training!")

if __name__ == "__main__":
    main()
