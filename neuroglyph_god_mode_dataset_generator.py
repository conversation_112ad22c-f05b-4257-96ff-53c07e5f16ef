#!/usr/bin/env python3
"""
NEUROGLYPH GOD MODE Dataset Generator
====================================

Genera dataset massivo per vero GOD MODE con ragionamento simbolico deterministico.
Target: 20K-50K esempi di alta qualità con simboli NEUROGLYPH.

Autore: NEUROGLYPH NG-THINK Team
Data: 2025-01-27
"""

import json
import random
import itertools
from typing import List, Dict, Any, Tuple
from datetime import datetime

class NeuroglyphGodModeDatasetGenerator:
    """
    Generatore di dataset massivo per NEUROGLYPH GOD MODE.
    
    Crea esempi di ragionamento simbolico deterministico con:
    - Multi-hop reasoning chains (3-7 step)
    - High symbol density (5-15 simboli per esempio)
    - 8 domini cognitivi bilanciati
    - Complexity scaling progressiva
    """
    
    def __init__(self):
        self.neuroglyph_symbols = self._get_neuroglyph_symbols()
        self.cognitive_domains = self._get_cognitive_domains()
        self.reasoning_patterns = self._get_reasoning_patterns()
        self.complexity_levels = ['basic', 'intermediate', 'advanced', 'expert', 'god_mode']
        
    def _get_neuroglyph_symbols(self) -> Dict[str, List[str]]:
        """Simboli NEUROGLYPH organizzati per categoria."""
        return {
            'reasoning': ['⊢', '⊨', '⊬', '⊭', '∴', '∵', '≈', '≉', '↯', '⟹'],
            'logic': ['¬', '∧', '∨', '→', '↔', '⊕', '⊗', '⊙', '⊖', '⊘'],
            'sets': ['∈', '∉', '⊂', '⊃', '⊆', '⊇', '∪', '∩', '∅', '℘'],
            'quantifiers': ['∀', '∃', '∄', '∃!', '∀!', '∃?', '∀?', '∃*', '∀*', '∃+'],
            'math': ['∑', '∏', '∫', '∮', '∂', '∇', '△', '▽', '□', '◊'],
            'relations': ['≤', '≥', '≠', '≡', '≢', '∝', '∞', '∘', '∙', '×'],
            'cognitive': ['🧠', '💭', '🤔', '💡', '🎯', '🔍', '🔗', '⚡', '🚀', '✨'],
            'validation': ['✅', '❌', '⚠️', '🔴', '🟡', '🟢', '🔵', '🟣', '🟠', '⚫']
        }
    
    def _get_cognitive_domains(self) -> List[str]:
        """Domini cognitivi per bilanciamento dataset."""
        return [
            'programming', 'mathematics', 'logic', 'ai_ml', 
            'systems', 'cognitive', 'planning', 'memory'
        ]
    
    def _get_reasoning_patterns(self) -> Dict[str, List[str]]:
        """Pattern di ragionamento per generazione esempi."""
        return {
            'deduction': [
                'Se {premise1} ⊢ {premise2} e {premise2} → {conclusion}, allora {premise1} ⊢ {conclusion}',
                'Dato {fact} ∧ ({fact} → {result}), possiamo dedurre {result} ∴ conclusione valida',
                'Da {condition} ⊢ {intermediate} e {intermediate} ≈ {target}, deduciamo {condition} → {target}'
            ],
            'analogy': [
                'Se {concept1} ≈ {concept2} e {concept2} ha proprietà {property}, allora {concept1} ≈ {property}',
                '{domain1} ≈ {domain2} implica che pattern in {domain1} → pattern simili in {domain2}',
                'Analogia: {source} ≈ {target} ∴ {source_property} → {target_property}'
            ],
            'contradiction': [
                'Se {statement} ∧ ¬{statement}, allora ↯ contraddizione rilevata',
                'Dato {premise} → {conclusion} e {premise} → ¬{conclusion}, sistema inconsistente ↯',
                '{fact1} ⊢ {result} ma {fact2} ⊢ ¬{result} ∴ revisione necessaria'
            ],
            'quantification': [
                '∀x ∈ {domain}: {property}(x) ⊢ {specific_case} ha {property}',
                '∃x ∈ {set}: {condition}(x) ∴ {set} non vuoto',
                '∀x: {premise}(x) → {conclusion}(x) ⊢ universale validato'
            ]
        }
    
    def generate_symbolic_reasoning_example(self, complexity: str, domain: str) -> Dict[str, str]:
        """Genera esempio di ragionamento simbolico."""

        # Seleziona simboli appropriati
        symbols = self._select_symbols_for_complexity(complexity)

        # Genera variabili per il dominio
        variables = self._generate_domain_variables(domain, complexity)

        # Crea reasoning chain semplificato
        reasoning_chain = self._create_simple_reasoning_chain(domain, complexity, symbols)

        # Genera instruction e output
        instruction = self._generate_instruction(domain, complexity, symbols)
        output = self._generate_symbolic_output(reasoning_chain, symbols, complexity)

        return {
            'instruction': instruction,
            'output': output,
            'metadata': {
                'domain': domain,
                'complexity': complexity,
                'symbols_used': len([s for s in output if s in ''.join(sum(self.neuroglyph_symbols.values(), []))]),
                'reasoning_steps': len(reasoning_chain),
                'god_mode_quality': self._calculate_god_mode_quality(output, complexity)
            }
        }
    
    def _generate_domain_variables(self, domain: str, complexity: str) -> Dict[str, str]:
        """Genera variabili specifiche per dominio."""

        domain_vars = {
            'programming': {
                'basic': ['funzione', 'variabile', 'loop', 'condizione'],
                'advanced': ['algoritmo', 'complessità', 'ottimizzazione', 'ricorsione'],
                'god_mode': ['paradigma', 'astrazione', 'invariante', 'semantica']
            },
            'mathematics': {
                'basic': ['numero', 'operazione', 'equazione', 'risultato'],
                'advanced': ['teorema', 'dimostrazione', 'lemma', 'corollario'],
                'god_mode': ['struttura', 'morfismo', 'categoria', 'funtore']
            },
            'logic': {
                'basic': ['proposizione', 'premessa', 'conclusione', 'validità'],
                'advanced': ['sillogismo', 'inferenza', 'deduzione', 'induzione'],
                'god_mode': ['metalogica', 'completezza', 'consistenza', 'decidibilità']
            }
        }

        vars_list = domain_vars.get(domain, {}).get(complexity, ['concetto', 'elemento', 'proprietà', 'relazione'])
        selected_vars = random.sample(vars_list, min(4, len(vars_list)))

        # Crea mapping completo per tutti i placeholder possibili
        return {
            'premise1': selected_vars[0] if len(selected_vars) > 0 else 'premessa',
            'premise2': selected_vars[1] if len(selected_vars) > 1 else 'condizione',
            'conclusion': selected_vars[2] if len(selected_vars) > 2 else 'conclusione',
            'fact': selected_vars[0] if len(selected_vars) > 0 else 'fatto',
            'result': selected_vars[2] if len(selected_vars) > 2 else 'risultato',
            'condition': selected_vars[1] if len(selected_vars) > 1 else 'condizione',
            'intermediate': 'passaggio intermedio',
            'target': selected_vars[3] if len(selected_vars) > 3 else 'obiettivo',
            'concept1': selected_vars[0] if len(selected_vars) > 0 else 'concetto A',
            'concept2': selected_vars[1] if len(selected_vars) > 1 else 'concetto B',
            'property': 'proprietà',
            'domain1': domain,
            'domain2': f'{domain} correlato',
            'source': selected_vars[0] if len(selected_vars) > 0 else 'sorgente',
            'source_property': 'proprietà sorgente',
            'target_property': 'proprietà target',
            'statement': selected_vars[0] if len(selected_vars) > 0 else 'affermazione',
            'fact1': selected_vars[0] if len(selected_vars) > 0 else 'fatto 1',
            'fact2': selected_vars[1] if len(selected_vars) > 1 else 'fatto 2',
            'domain': domain,
            'set': 'insieme',
            'specific_case': 'caso specifico'
        }
    
    def _create_simple_reasoning_chain(self, domain: str, complexity: str, symbols: List[str]) -> List[str]:
        """Crea catena di ragionamento semplificata."""

        steps_count = {
            'basic': 2,
            'intermediate': 3,
            'advanced': 5,
            'expert': 6,
            'god_mode': 8
        }.get(complexity, 3)

        # Template di ragionamento per dominio
        domain_templates = {
            'programming': [
                "Analisi del codice con simboli logici",
                "Applicazione di pattern di design",
                "Ottimizzazione algoritmica",
                "Validazione della correttezza",
                "Verifica della complessità",
                "Testing e debugging",
                "Refactoring simbolico",
                "Deployment e monitoraggio"
            ],
            'mathematics': [
                "Definizione del problema matematico",
                "Identificazione delle variabili",
                "Applicazione di teoremi",
                "Sviluppo della dimostrazione",
                "Verifica dei passaggi logici",
                "Controllo della consistenza",
                "Generalizzazione del risultato",
                "Validazione finale"
            ],
            'logic': [
                "Formulazione delle premesse",
                "Identificazione delle regole di inferenza",
                "Applicazione del modus ponens",
                "Verifica della validità logica",
                "Controllo delle contraddizioni",
                "Deduzione delle conclusioni",
                "Validazione del sillogismo",
                "Conferma della coerenza"
            ]
        }

        templates = domain_templates.get(domain, [
            "Analisi del problema",
            "Identificazione dei pattern",
            "Applicazione delle regole",
            "Verifica dei risultati",
            "Validazione finale"
        ])

        chain = []
        for i in range(min(steps_count, len(templates))):
            symbol = symbols[i % len(symbols)] if symbols else "⊢"
            step = f"{templates[i]} {symbol}"
            chain.append(step)

        return chain
    
    def _select_symbols_for_complexity(self, complexity: str) -> List[str]:
        """Seleziona simboli appropriati per livello di complessità."""
        
        symbol_counts = {
            'basic': 3,
            'intermediate': 5,
            'advanced': 8,
            'expert': 12,
            'god_mode': 15
        }
        
        count = symbol_counts.get(complexity, 5)
        all_symbols = sum(self.neuroglyph_symbols.values(), [])
        return random.sample(all_symbols, min(count, len(all_symbols)))
    
    def _generate_instruction(self, domain: str, complexity: str, symbols: List[str]) -> str:
        """Genera instruction per l'esempio."""
        
        symbol_str = ' '.join(symbols[:3])  # Primi 3 simboli nell'instruction
        
        instructions = {
            'basic': f"Usa i simboli {symbol_str} per analizzare questo problema di {domain}",
            'intermediate': f"Applica ragionamento simbolico con {symbol_str} per risolvere questo caso di {domain}",
            'advanced': f"Crea una catena di ragionamento simbolico usando {symbol_str} per questo problema complesso di {domain}",
            'expert': f"Sviluppa un'analisi simbolica multi-hop con {symbol_str} per questo scenario avanzato di {domain}",
            'god_mode': f"Costruisci un sistema di ragionamento simbolico deterministico usando {symbol_str} per questo problema di {domain} di livello GOD MODE"
        }
        
        return instructions.get(complexity, f"Analizza usando simboli {symbol_str} nel dominio {domain}")
    
    def _generate_symbolic_output(self, reasoning_chain: List[str], symbols: List[str], complexity: str) -> str:
        """Genera output simbolico strutturato."""
        
        output_parts = [
            f"🧠 Ragionamento Simbolico {complexity.upper()}:",
            "",
            "📋 Analisi Simbolica:"
        ]
        
        # Aggiungi simboli con significati
        for i, symbol in enumerate(symbols[:5]):  # Primi 5 simboli
            meaning = self._get_symbol_meaning(symbol)
            output_parts.append(f"   {symbol}: {meaning}")
        
        output_parts.extend([
            "",
            "🔗 Catena di Ragionamento:"
        ])
        
        # Aggiungi reasoning chain con simboli
        for i, step in enumerate(reasoning_chain):
            symbol = symbols[i % len(symbols)]
            output_parts.append(f"   {i+1}. {step} {symbol}")
        
        output_parts.extend([
            "",
            f"∴ Conclusione: Ragionamento simbolico {complexity} completato con {len(symbols)} simboli NEUROGLYPH",
            f"✅ Validazione: {' '.join(symbols[:3])} preservati atomicamente",
            f"🎯 Qualità GOD MODE: {self._calculate_god_mode_quality(' '.join(output_parts), complexity):.1f}/10"
        ])
        
        return "\n".join(output_parts)
    
    def _get_symbol_meaning(self, symbol: str) -> str:
        """Restituisce significato del simbolo."""
        meanings = {
            '⊢': 'deduzione logica valida',
            '≈': 'analogia semantica',
            '→': 'implicazione causale',
            '∴': 'quindi/conclusione',
            '¬': 'negazione logica',
            '∧': 'congiunzione AND',
            '∨': 'disgiunzione OR',
            '∀': 'quantificatore universale',
            '∃': 'quantificatore esistenziale',
            '∈': 'appartenenza a insieme',
            '🧠': 'processo cognitivo',
            '💭': 'pensiero/riflessione',
            '✅': 'validazione positiva'
        }
        return meanings.get(symbol, 'simbolo cognitivo')
    
    def _calculate_god_mode_quality(self, output: str, complexity: str) -> float:
        """Calcola qualità GOD MODE dell'esempio."""
        
        # Conta simboli NEUROGLYPH
        all_symbols = sum(self.neuroglyph_symbols.values(), [])
        symbol_count = sum(1 for char in output if char in all_symbols)
        
        # Calcola metriche qualità
        length_score = min(len(output) / 1000, 1.0)  # Lunghezza ottimale
        symbol_density = min(symbol_count / 10, 1.0)  # Densità simboli
        complexity_bonus = {
            'basic': 0.2, 'intermediate': 0.4, 'advanced': 0.6, 
            'expert': 0.8, 'god_mode': 1.0
        }.get(complexity, 0.5)
        
        # Calcola score finale
        quality_score = (length_score * 0.3 + symbol_density * 0.4 + complexity_bonus * 0.3) * 10
        
        return min(quality_score, 10.0)
    
    def generate_god_mode_dataset(self, target_size: int = 20000) -> List[Dict[str, str]]:
        """Genera dataset completo GOD MODE."""
        
        print(f"🚀 Generando dataset GOD MODE con {target_size:,} esempi...")
        
        dataset = []
        
        # Distribuzione per complessità (più esempi complessi per GOD MODE)
        complexity_distribution = {
            'basic': 0.10,          # 10% basic
            'intermediate': 0.15,   # 15% intermediate  
            'advanced': 0.25,       # 25% advanced
            'expert': 0.30,         # 30% expert
            'god_mode': 0.20        # 20% god mode
        }
        
        # Distribuzione per domini (bilanciata)
        domain_distribution = {domain: 1/len(self.cognitive_domains) for domain in self.cognitive_domains}
        
        for i in range(target_size):
            # Seleziona complessità e dominio
            complexity = random.choices(
                list(complexity_distribution.keys()),
                weights=list(complexity_distribution.values())
            )[0]
            
            domain = random.choices(
                list(domain_distribution.keys()),
                weights=list(domain_distribution.values())
            )[0]
            
            # Genera esempio
            example = self.generate_symbolic_reasoning_example(complexity, domain)
            dataset.append(example)
            
            # Progress tracking
            if (i + 1) % 1000 == 0:
                print(f"   ✅ Generati {i+1:,}/{target_size:,} esempi ({(i+1)/target_size*100:.1f}%)")
        
        print(f"🎊 Dataset GOD MODE completato: {len(dataset):,} esempi")
        return dataset
    
    def save_god_mode_dataset(self, dataset: List[Dict[str, str]], filename: str = "neuroglyph_god_mode_dataset.json"):
        """Salva dataset GOD MODE."""
        
        # Calcola statistiche
        stats = self._calculate_dataset_stats(dataset)
        
        # Crea struttura finale
        final_dataset = {
            "version": "NEUROGLYPH_GOD_MODE_v1.0",
            "created_at": datetime.now().isoformat(),
            "description": "Dataset massivo per NEUROGLYPH GOD MODE con ragionamento simbolico deterministico",
            "statistics": stats,
            "examples": dataset
        }
        
        # Salva file
        with open(filename, 'w', encoding='utf-8') as f:
            json.dump(final_dataset, f, indent=2, ensure_ascii=False)
        
        print(f"💾 Dataset salvato: {filename}")
        print(f"📊 Statistiche: {stats}")
        
        return filename
    
    def _calculate_dataset_stats(self, dataset: List[Dict[str, str]]) -> Dict[str, Any]:
        """Calcola statistiche del dataset."""
        
        total_examples = len(dataset)
        
        # Conta per complessità
        complexity_counts = {}
        domain_counts = {}
        quality_scores = []
        symbol_counts = []
        
        for example in dataset:
            metadata = example.get('metadata', {})
            
            complexity = metadata.get('complexity', 'unknown')
            complexity_counts[complexity] = complexity_counts.get(complexity, 0) + 1
            
            domain = metadata.get('domain', 'unknown')
            domain_counts[domain] = domain_counts.get(domain, 0) + 1
            
            quality_scores.append(metadata.get('god_mode_quality', 0))
            symbol_counts.append(metadata.get('symbols_used', 0))
        
        avg_quality = sum(quality_scores) / len(quality_scores) if quality_scores else 0
        avg_symbols = sum(symbol_counts) / len(symbol_counts) if symbol_counts else 0
        
        return {
            "total_examples": total_examples,
            "complexity_distribution": complexity_counts,
            "domain_distribution": domain_counts,
            "average_god_mode_quality": round(avg_quality, 2),
            "average_symbols_per_example": round(avg_symbols, 1),
            "god_mode_ready": avg_quality >= 7.0 and total_examples >= 20000
        }

def main():
    """Genera dataset GOD MODE."""
    
    print("🧠 NEUROGLYPH GOD MODE Dataset Generator")
    print("=" * 50)
    
    generator = NeuroglyphGodModeDatasetGenerator()
    
    # Genera dataset di diverse dimensioni
    sizes = [5000, 20000, 50000]  # Test, GOD MODE, ULTRA GOD MODE
    
    for size in sizes:
        print(f"\n🎯 Generando dataset {size:,} esempi...")
        
        dataset = generator.generate_god_mode_dataset(size)
        filename = f"neuroglyph_god_mode_dataset_{size//1000}k.json"
        
        generator.save_god_mode_dataset(dataset, filename)
        
        print(f"✅ Completato: {filename}")
    
    print("\n🎊 Tutti i dataset GOD MODE generati!")
    print("🚀 Pronto per training NEUROGLYPH ULTRA GOD MODE!")

if __name__ == "__main__":
    main()
