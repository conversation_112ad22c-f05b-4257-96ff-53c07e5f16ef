#!/usr/bin/env python3
"""
Test semplice NG_CONTEXT_PRIORITIZER
"""

import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), 'neuroglyph'))

def test_basic():
    print("🧪 Test basic NG_CONTEXT_PRIORITIZER")
    
    try:
        from neuroglyph.ng_think.v1_base.ng_context_prioritizer import NGContextPrioritizer
        from neuroglyph.ng_think.v1_base.ng_parser import NGParser
        
        print("✅ Import successful")
        
        # Inizializza moduli
        parser = NGParser()
        prioritizer = NGContextPrioritizer()
        
        print("✅ Modules initialized")
        
        # Test semplice
        test_prompt = "Crea una funzione Python"
        
        # Parse
        parsed = parser.parse(test_prompt)
        print(f"✅ Parsed: {len(parsed.tokens)} tokens")
        
        # Prioritize
        priority_vector = prioritizer.prioritize(parsed)
        print(f"✅ Prioritized: domain={priority_vector.domain}, urgency={priority_vector.urgency:.3f}")
        
        print("🎉 Test completato con successo!")
        
    except Exception as e:
        print(f"❌ Errore: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_basic()
