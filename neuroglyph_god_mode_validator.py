#!/usr/bin/env python3
"""
NEUROGLYPH GOD MODE Validator - Sistema di Valutazione Personalizzato
===================================================================

Sistema di valutazione modulare per LLM simbolico deterministico che va oltre
i benchmark classici (BLEU, ROUGE, MMLU) e misura realmente l'intelligenza simbolica.

METRICHE CORE GOD MODE:
- symbolic_completeness: Uso coerente simboli validi
- logical_structure_score: Ragionamento simbolico strutturato  
- multi_hop_depth: Passaggi ragionamento (target 3-8)
- determinism_score: Verificabilità e coerenza
- zero_hallucination: Assenza contenuti inventati
- symbol_quality: Correttezza e consistenza simboli
- cognitive_tags_presence: <PERSON><PERSON><PERSON> cognitivi
- excellence_score: Score aggregato 0-100

Autore: NEUROGLYPH God Mode Evaluation Team
Data: 2025-06-01
"""

import json
import re
import math
import statistics
from typing import List, Dict, Any, Tuple, Set, Optional
from datetime import datetime
from dataclasses import dataclass

@dataclass
class NeuroglyphMetrics:
    """Metriche core per valutazione NEUROGLYPH GOD MODE."""
    symbolic_completeness: float
    logical_structure_score: float
    multi_hop_depth: int
    determinism_score: float
    zero_hallucination: bool
    symbol_quality: float
    cognitive_tags_presence: float
    excellence_score: float

class NeuroglyphGodModeValidator:
    """
    Validatore GOD MODE per dataset NEUROGLYPH.
    
    Misura intelligenza simbolica reale vs generazione statistica.
    """
    
    def __init__(self, neuroglyph_registry_path: Optional[str] = None):
        self.neuroglyph_registry = self._load_neuroglyph_registry(neuroglyph_registry_path)
        self.cognitive_markers = self._initialize_cognitive_markers()
        self.logical_operators = self._initialize_logical_operators()
        self.hallucination_patterns = self._initialize_hallucination_patterns()
        self.determinism_indicators = self._initialize_determinism_indicators()
        
    def _load_neuroglyph_registry(self, registry_path: Optional[str]) -> Set[str]:
        """Carica registry simboli NEUROGLYPH validati."""
        if registry_path and os.path.exists(registry_path):
            try:
                with open(registry_path, 'r', encoding='utf-8') as f:
                    registry_data = json.load(f)
                
                symbols = set()
                if 'symbols' in registry_data:
                    symbols.update(s['symbol'] for s in registry_data['symbols'])
                elif 'approved_symbols' in registry_data:
                    symbols.update(s['symbol'] for s in registry_data['approved_symbols'])
                
                return symbols
            except Exception as e:
                print(f"⚠️ Error loading registry: {e}")
        
        # Fallback: simboli NEUROGLYPH core
        return {
            # Core reasoning
            '⊢', '⊨', '∴', '∵', '≈', '→', '↔', '¬', '∧', '∨', '⊕',
            # Quantifiers
            '∀', '∃', '∄',
            # Set theory
            '∈', '∉', '⊂', '⊆', '∪', '∩', '∅',
            # Mathematical
            '∑', '∏', '∫', '∂', '∇', '∞',
            # Cognitive
            '🧠', '💭', '🤔', '💡', '🎯',
            # Validation
            '✅', '❌', '⚠️',
            # Relations
            '≤', '≥', '≠', '≡', '∝'
        }
    
    def _initialize_cognitive_markers(self) -> Dict[str, List[str]]:
        """Inizializza marcatori cognitivi per NG-THINK."""
        return {
            'cause': ['<CAUSE>', 'causa:', 'perché:', '∵', 'dovuto a', 'causato da'],
            'goal': ['<GOAL>', 'obiettivo:', 'scopo:', '🎯', 'target:', 'finalità:'],
            'evidence': ['<EVIDENCE>', 'evidenza:', 'prova:', '✅', 'dimostrazione:', 'verifica:'],
            'reasoning': ['<REASONING>', 'ragionamento:', 'deduzione:', '⊢', 'inferenza:', 'logica:'],
            'analogy': ['<ANALOGY>', 'analogia:', 'simile a:', '≈', 'come:', 'pattern:'],
            'meta_cognition': ['<META>', 'meta-cognizione:', 'pensiero su:', '🧠', 'riflessione:', '💭'],
            'validation': ['<VALIDATION>', 'validazione:', 'controllo:', 'verifica:', 'test:'],
            'conclusion': ['<CONCLUSION>', 'conclusione:', 'quindi:', '∴', 'risultato:', 'finale:']
        }
    
    def _initialize_logical_operators(self) -> Dict[str, Dict[str, Any]]:
        """Inizializza operatori logici con semantica rigorosa."""
        return {
            '⊢': {'type': 'deduction', 'arity': 2, 'precedence': 1},
            '∴': {'type': 'conclusion', 'arity': 1, 'precedence': 1},
            '→': {'type': 'implication', 'arity': 2, 'precedence': 2},
            '∧': {'type': 'conjunction', 'arity': 2, 'precedence': 3},
            '∨': {'type': 'disjunction', 'arity': 2, 'precedence': 3},
            '¬': {'type': 'negation', 'arity': 1, 'precedence': 4},
            '∀': {'type': 'universal', 'arity': 1, 'precedence': 5},
            '∃': {'type': 'existential', 'arity': 1, 'precedence': 5},
            '≈': {'type': 'analogy', 'arity': 2, 'precedence': 2}
        }
    
    def _initialize_hallucination_patterns(self) -> List[str]:
        """Pattern che indicano possibili allucinazioni."""
        return [
            r'probabilmente\s+(?:è|sono|sarà)',
            r'forse\s+(?:potrebbe|dovrebbe)',
            r'sembra\s+che\s+(?:sia|siano)',
            r'presumibilmente\s+(?:è|sono)',
            r'apparentemente\s+(?:è|sono)',
            r'potrebbe\s+essere\s+che',
            r'è\s+possibile\s+che',
            r'supponiamo\s+che',
            r'immaginiamo\s+che',
            r'diciamo\s+che'
        ]
    
    def _initialize_determinism_indicators(self) -> List[str]:
        """Indicatori di ragionamento deterministico."""
        return [
            'deterministico', 'verificabile', 'riproducibile', 'dimostrabile',
            'logicamente', 'rigoroso', 'formale', 'sistematico',
            'step-by-step', 'passo dopo passo', 'metodico',
            'validato', 'confermato', 'provato', 'dimostrato'
        ]
    
    def evaluate_example(self, example: Dict[str, Any]) -> NeuroglyphMetrics:
        """Valuta singolo esempio con metriche GOD MODE."""
        
        instruction = example.get('instruction', '')
        output = example.get('output', '')
        metadata = example.get('metadata', {})
        
        # 1. SYMBOLIC COMPLETENESS
        symbolic_completeness = self._calculate_symbolic_completeness(output)
        
        # 2. LOGICAL STRUCTURE SCORE
        logical_structure_score = self._calculate_logical_structure_score(output)
        
        # 3. MULTI-HOP DEPTH
        multi_hop_depth = self._calculate_multi_hop_depth(output)
        
        # 4. DETERMINISM SCORE
        determinism_score = self._calculate_determinism_score(output)
        
        # 5. ZERO HALLUCINATION
        zero_hallucination = self._check_zero_hallucination(output)
        
        # 6. SYMBOL QUALITY
        symbol_quality = self._calculate_symbol_quality(output)
        
        # 7. COGNITIVE TAGS PRESENCE
        cognitive_tags_presence = self._calculate_cognitive_tags_presence(output)
        
        # 8. EXCELLENCE SCORE (Bayesian Scaling)
        excellence_score = self._calculate_excellence_score(
            symbolic_completeness, logical_structure_score, multi_hop_depth,
            determinism_score, zero_hallucination, symbol_quality, cognitive_tags_presence
        )
        
        return NeuroglyphMetrics(
            symbolic_completeness=symbolic_completeness,
            logical_structure_score=logical_structure_score,
            multi_hop_depth=multi_hop_depth,
            determinism_score=determinism_score,
            zero_hallucination=zero_hallucination,
            symbol_quality=symbol_quality,
            cognitive_tags_presence=cognitive_tags_presence,
            excellence_score=excellence_score
        )
    
    def _calculate_symbolic_completeness(self, text: str) -> float:
        """Calcola completezza simbolica (uso coerente simboli validi)."""
        
        # Trova tutti i simboli nel testo
        found_symbols = set()
        for char in text:
            if char in self.neuroglyph_registry:
                found_symbols.add(char)
        
        if not found_symbols:
            return 0.0
        
        # Verifica coerenza d'uso
        coherence_score = 0.0
        for symbol in found_symbols:
            # Conta occorrenze
            occurrences = text.count(symbol)
            
            # Verifica contesti d'uso
            contexts = self._extract_symbol_contexts(text, symbol)
            unique_contexts = len(set(contexts))
            
            # Score coerenza: meno contesti diversi = più coerente
            if unique_contexts <= 2:
                coherence_score += 1.0
            elif unique_contexts <= 4:
                coherence_score += 0.7
            else:
                coherence_score += 0.3
        
        # Normalizza per numero di simboli
        completeness = coherence_score / len(found_symbols) if found_symbols else 0.0
        
        # Bonus per copertura simboli
        coverage_bonus = min(len(found_symbols) / 10, 0.2)  # Max 20% bonus
        
        return min(completeness + coverage_bonus, 1.0)
    
    def _extract_symbol_contexts(self, text: str, symbol: str) -> List[str]:
        """Estrae contesti d'uso per un simbolo."""
        contexts = []
        lines = text.split('\n')
        
        for line in lines:
            if symbol in line:
                # Estrai 3 parole prima e dopo il simbolo
                words = line.split()
                for i, word in enumerate(words):
                    if symbol in word:
                        start = max(0, i - 3)
                        end = min(len(words), i + 4)
                        context = ' '.join(words[start:end])
                        contexts.append(context.lower().strip())
        
        return contexts
    
    def _calculate_logical_structure_score(self, text: str) -> float:
        """Calcola score struttura logica (ragionamento simbolico strutturato)."""
        
        score = 0.0
        
        # 1. Presenza operatori logici (40%)
        logical_ops_found = 0
        for op in self.logical_operators:
            if op in text:
                logical_ops_found += 1
        
        logical_ops_score = min(logical_ops_found / 5, 1.0) * 0.4
        score += logical_ops_score
        
        # 2. Struttura step-by-step (30%)
        step_pattern = r'\d+\.\s+'
        steps = re.findall(step_pattern, text)
        step_score = min(len(steps) / 6, 1.0) * 0.3
        score += step_score
        
        # 3. Premesse e conclusioni (20%)
        premise_indicators = ['premessa', 'ipotesi', 'dato', 'supponiamo']
        conclusion_indicators = ['conclusione', 'quindi', '∴', 'risultato']
        
        has_premises = any(indicator in text.lower() for indicator in premise_indicators)
        has_conclusions = any(indicator in text.lower() for indicator in conclusion_indicators)
        
        structure_score = 0.0
        if has_premises:
            structure_score += 0.1
        if has_conclusions:
            structure_score += 0.1
        
        score += structure_score
        
        # 4. Catene di ragionamento (10%)
        chain_indicators = ['catena', 'sequenza', 'passaggio', 'step']
        has_chains = any(indicator in text.lower() for indicator in chain_indicators)
        if has_chains:
            score += 0.1
        
        return min(score, 1.0)
    
    def _calculate_multi_hop_depth(self, text: str) -> int:
        """Calcola profondità multi-hop (numero passaggi ragionamento)."""
        
        # Metodo 1: Conta step numerati
        step_pattern = r'\d+\.\s+'
        numbered_steps = len(re.findall(step_pattern, text))
        
        # Metodo 2: Conta connettori logici
        logical_connectors = ['⊢', '∴', '→', '∧', '∨', '≈']
        connector_count = sum(text.count(conn) for conn in logical_connectors)
        
        # Metodo 3: Conta transizioni di ragionamento
        transition_words = ['quindi', 'perciò', 'dunque', 'di conseguenza', 'ne segue']
        transition_count = sum(text.lower().count(word) for word in transition_words)
        
        # Prendi il massimo tra i metodi
        depth = max(numbered_steps, connector_count, transition_count)
        
        # Limita al range realistico
        return min(max(depth, 1), 12)
    
    def _calculate_determinism_score(self, text: str) -> float:
        """Calcola score determinismo (verificabilità e coerenza)."""
        
        score = 0.0
        text_lower = text.lower()
        
        # 1. Presenza indicatori determinismo (40%)
        determinism_count = sum(1 for indicator in self.determinism_indicators 
                               if indicator in text_lower)
        determinism_score = min(determinism_count / 5, 1.0) * 0.4
        score += determinism_score
        
        # 2. Assenza incertezza (30%)
        uncertainty_patterns = [
            r'forse', r'probabilmente', r'potrebbe', r'sembra',
            r'presumibilmente', r'apparentemente'
        ]
        
        uncertainty_count = sum(len(re.findall(pattern, text_lower)) 
                               for pattern in uncertainty_patterns)
        
        uncertainty_penalty = min(uncertainty_count * 0.1, 0.3)
        score += max(0.3 - uncertainty_penalty, 0.0)
        
        # 3. Presenza validazione (20%)
        validation_indicators = ['validazione', 'verifica', 'controllo', 'test', '✅']
        validation_count = sum(1 for indicator in validation_indicators 
                              if indicator in text_lower)
        validation_score = min(validation_count / 3, 1.0) * 0.2
        score += validation_score
        
        # 4. Riproducibilità (10%)
        reproducibility_indicators = ['riproducibile', 'ripetibile', 'sistematico']
        has_reproducibility = any(indicator in text_lower 
                                 for indicator in reproducibility_indicators)
        if has_reproducibility:
            score += 0.1
        
        return min(score, 1.0)
    
    def _check_zero_hallucination(self, text: str) -> bool:
        """Verifica assenza totale allucinazioni."""
        
        text_lower = text.lower()
        
        # Cerca pattern di allucinazione
        for pattern in self.hallucination_patterns:
            if re.search(pattern, text_lower):
                return False
        
        # Cerca affermazioni non supportate
        unsupported_patterns = [
            r'è\s+noto\s+che\s+(?!.*(?:dimostrato|provato|verificato))',
            r'tutti\s+sanno\s+che',
            r'è\s+ovvio\s+che',
            r'chiaramente\s+(?!.*(?:dimostrato|provato))',
            r'ovviamente\s+(?!.*(?:perché|poiché))'
        ]
        
        for pattern in unsupported_patterns:
            if re.search(pattern, text_lower):
                return False
        
        return True
    
    def _calculate_symbol_quality(self, text: str) -> float:
        """Calcola qualità simboli (correttezza e consistenza)."""
        
        # Trova simboli utilizzati
        used_symbols = set()
        for char in text:
            if char in self.neuroglyph_registry:
                used_symbols.add(char)
        
        if not used_symbols:
            return 0.0
        
        quality_score = 0.0
        
        for symbol in used_symbols:
            symbol_score = 0.0
            
            # 1. Simbolo nel registry (50%)
            if symbol in self.neuroglyph_registry:
                symbol_score += 0.5
            
            # 2. Uso appropriato nel contesto (30%)
            contexts = self._extract_symbol_contexts(text, symbol)
            if contexts:
                # Verifica coerenza semantica
                if symbol in self.logical_operators:
                    # Per operatori logici, verifica uso corretto
                    op_info = self.logical_operators[symbol]
                    if self._verify_operator_usage(text, symbol, op_info):
                        symbol_score += 0.3
                else:
                    # Per altri simboli, verifica consistenza
                    unique_contexts = len(set(contexts))
                    if unique_contexts <= 2:
                        symbol_score += 0.3
                    elif unique_contexts <= 4:
                        symbol_score += 0.2
                    else:
                        symbol_score += 0.1
            
            # 3. Frequenza appropriata (20%)
            frequency = text.count(symbol)
            if 1 <= frequency <= 5:
                symbol_score += 0.2
            elif frequency <= 10:
                symbol_score += 0.1
            
            quality_score += symbol_score
        
        return quality_score / len(used_symbols)
    
    def _verify_operator_usage(self, text: str, operator: str, op_info: Dict[str, Any]) -> bool:
        """Verifica uso corretto di un operatore logico."""
        
        # Implementazione semplificata
        # In una versione completa, si parserebbe la struttura logica
        
        if operator == '⊢':
            # Deduzione: dovrebbe avere premesse prima e conclusione dopo
            return 'premessa' in text.lower() or 'ipotesi' in text.lower()
        elif operator == '∴':
            # Conclusione: dovrebbe essere seguita da una conclusione
            return 'conclusione' in text.lower() or 'quindi' in text.lower()
        elif operator == '→':
            # Implicazione: dovrebbe collegare due proposizioni
            return 'se' in text.lower() and 'allora' in text.lower()
        elif operator == '≈':
            # Analogia: dovrebbe collegare domini simili
            return 'analogia' in text.lower() or 'simile' in text.lower()
        
        return True  # Default: assume uso corretto
    
    def _calculate_cognitive_tags_presence(self, text: str) -> float:
        """Calcola presenza marcatori cognitivi."""
        
        total_categories = len(self.cognitive_markers)
        found_categories = 0
        
        text_lower = text.lower()
        
        for category, markers in self.cognitive_markers.items():
            category_found = False
            for marker in markers:
                if marker.lower() in text_lower:
                    category_found = True
                    break
            
            if category_found:
                found_categories += 1
        
        return found_categories / total_categories
    
    def _calculate_excellence_score(self, symbolic_completeness: float, 
                                   logical_structure_score: float, multi_hop_depth: int,
                                   determinism_score: float, zero_hallucination: bool,
                                   symbol_quality: float, cognitive_tags_presence: float) -> float:
        """Calcola excellence score con Bayesian Scaling."""
        
        # Pesi per ogni metrica
        weights = {
            'symbolic_completeness': 0.15,
            'logical_structure_score': 0.20,
            'multi_hop_depth': 0.15,
            'determinism_score': 0.20,
            'zero_hallucination': 0.15,
            'symbol_quality': 0.10,
            'cognitive_tags_presence': 0.05
        }
        
        # Normalizza multi_hop_depth (target 3-8)
        normalized_depth = min(max(multi_hop_depth - 2, 0) / 6, 1.0)
        
        # Calcola score pesato
        weighted_score = (
            symbolic_completeness * weights['symbolic_completeness'] +
            logical_structure_score * weights['logical_structure_score'] +
            normalized_depth * weights['multi_hop_depth'] +
            determinism_score * weights['determinism_score'] +
            (1.0 if zero_hallucination else 0.0) * weights['zero_hallucination'] +
            symbol_quality * weights['symbol_quality'] +
            cognitive_tags_presence * weights['cognitive_tags_presence']
        )
        
        # Bayesian Scaling con prior
        prior_mean = 0.7  # Prior: assumiamo qualità buona
        prior_weight = 0.1
        
        bayesian_score = (weighted_score + prior_mean * prior_weight) / (1 + prior_weight)
        
        # Scala a 0-100
        excellence_score = bayesian_score * 100
        
        # Bonus per eccellenza (≥90 gets bonus)
        if excellence_score >= 90:
            excellence_score = min(excellence_score * 1.05, 100)
        
        return round(excellence_score, 2)

    def evaluate_dataset(self, dataset_path: str) -> Dict[str, Any]:
        """Valuta intero dataset con metriche GOD MODE."""

        print("🔍 NEUROGLYPH GOD MODE Dataset Evaluation")
        print("=" * 60)
        print("🎯 Metriche personalizzate per LLM simbolico deterministico")

        # Carica dataset
        try:
            with open(dataset_path, 'r', encoding='utf-8') as f:
                dataset = json.load(f)

            examples = dataset.get('examples', [])
            print(f"📊 Dataset loaded: {len(examples)} examples")

        except Exception as e:
            return {"error": f"Failed to load dataset: {e}"}

        # Valuta ogni esempio
        all_metrics = []
        detailed_results = []

        print("\n🔄 Evaluating examples...")
        for i, example in enumerate(examples):
            metrics = self.evaluate_example(example)
            all_metrics.append(metrics)

            detailed_results.append({
                'example_id': i,
                'instruction': example.get('instruction', '')[:100] + '...',
                'metrics': {
                    'symbolic_completeness': metrics.symbolic_completeness,
                    'logical_structure_score': metrics.logical_structure_score,
                    'multi_hop_depth': metrics.multi_hop_depth,
                    'determinism_score': metrics.determinism_score,
                    'zero_hallucination': metrics.zero_hallucination,
                    'symbol_quality': metrics.symbol_quality,
                    'cognitive_tags_presence': metrics.cognitive_tags_presence,
                    'excellence_score': metrics.excellence_score
                }
            })

            # Progress tracking
            if (i + 1) % 100 == 0:
                print(f"   ✅ Evaluated {i+1}/{len(examples)} examples")

        # Calcola statistiche aggregate
        aggregate_stats = self._calculate_aggregate_statistics(all_metrics)

        # Analisi qualità GOD MODE
        god_mode_analysis = self._analyze_god_mode_quality(all_metrics)

        # Raccomandazioni
        recommendations = self._generate_god_mode_recommendations(aggregate_stats, god_mode_analysis)

        # Report finale
        evaluation_report = {
            "evaluation_timestamp": datetime.now().isoformat(),
            "dataset_path": dataset_path,
            "total_examples": len(examples),

            "aggregate_statistics": aggregate_stats,
            "god_mode_analysis": god_mode_analysis,
            "recommendations": recommendations,

            "detailed_results": detailed_results[:10],  # Prime 10 per brevità

            "neuroglyph_readiness": {
                "symbolic_intelligence_ready": aggregate_stats['excellence_score']['mean'] >= 85,
                "deterministic_reasoning_ready": aggregate_stats['determinism_score']['mean'] >= 0.8,
                "zero_hallucination_ready": aggregate_stats['zero_hallucination']['rate'] >= 0.95,
                "multi_hop_ready": aggregate_stats['multi_hop_depth']['mean'] >= 3,
                "overall_god_mode_ready": self._assess_overall_readiness(aggregate_stats)
            }
        }

        # Stampa summary
        self._print_evaluation_summary(evaluation_report)

        return evaluation_report

    def _calculate_aggregate_statistics(self, metrics_list: List[NeuroglyphMetrics]) -> Dict[str, Any]:
        """Calcola statistiche aggregate per tutte le metriche."""

        if not metrics_list:
            return {}

        # Estrai valori per ogni metrica
        symbolic_completeness = [m.symbolic_completeness for m in metrics_list]
        logical_structure_scores = [m.logical_structure_score for m in metrics_list]
        multi_hop_depths = [m.multi_hop_depth for m in metrics_list]
        determinism_scores = [m.determinism_score for m in metrics_list]
        zero_hallucinations = [m.zero_hallucination for m in metrics_list]
        symbol_qualities = [m.symbol_quality for m in metrics_list]
        cognitive_tags = [m.cognitive_tags_presence for m in metrics_list]
        excellence_scores = [m.excellence_score for m in metrics_list]

        return {
            "symbolic_completeness": {
                "mean": round(statistics.mean(symbolic_completeness), 3),
                "median": round(statistics.median(symbolic_completeness), 3),
                "std": round(statistics.stdev(symbolic_completeness), 3) if len(symbolic_completeness) > 1 else 0,
                "min": round(min(symbolic_completeness), 3),
                "max": round(max(symbolic_completeness), 3)
            },
            "logical_structure_score": {
                "mean": round(statistics.mean(logical_structure_scores), 3),
                "median": round(statistics.median(logical_structure_scores), 3),
                "std": round(statistics.stdev(logical_structure_scores), 3) if len(logical_structure_scores) > 1 else 0,
                "min": round(min(logical_structure_scores), 3),
                "max": round(max(logical_structure_scores), 3)
            },
            "multi_hop_depth": {
                "mean": round(statistics.mean(multi_hop_depths), 1),
                "median": statistics.median(multi_hop_depths),
                "std": round(statistics.stdev(multi_hop_depths), 1) if len(multi_hop_depths) > 1 else 0,
                "min": min(multi_hop_depths),
                "max": max(multi_hop_depths),
                "target_range_3_8": sum(1 for d in multi_hop_depths if 3 <= d <= 8) / len(multi_hop_depths)
            },
            "determinism_score": {
                "mean": round(statistics.mean(determinism_scores), 3),
                "median": round(statistics.median(determinism_scores), 3),
                "std": round(statistics.stdev(determinism_scores), 3) if len(determinism_scores) > 1 else 0,
                "min": round(min(determinism_scores), 3),
                "max": round(max(determinism_scores), 3)
            },
            "zero_hallucination": {
                "rate": sum(zero_hallucinations) / len(zero_hallucinations),
                "count_true": sum(zero_hallucinations),
                "count_false": len(zero_hallucinations) - sum(zero_hallucinations),
                "total": len(zero_hallucinations)
            },
            "symbol_quality": {
                "mean": round(statistics.mean(symbol_qualities), 3),
                "median": round(statistics.median(symbol_qualities), 3),
                "std": round(statistics.stdev(symbol_qualities), 3) if len(symbol_qualities) > 1 else 0,
                "min": round(min(symbol_qualities), 3),
                "max": round(max(symbol_qualities), 3)
            },
            "cognitive_tags_presence": {
                "mean": round(statistics.mean(cognitive_tags), 3),
                "median": round(statistics.median(cognitive_tags), 3),
                "std": round(statistics.stdev(cognitive_tags), 3) if len(cognitive_tags) > 1 else 0,
                "min": round(min(cognitive_tags), 3),
                "max": round(max(cognitive_tags), 3)
            },
            "excellence_score": {
                "mean": round(statistics.mean(excellence_scores), 2),
                "median": round(statistics.median(excellence_scores), 2),
                "std": round(statistics.stdev(excellence_scores), 2) if len(excellence_scores) > 1 else 0,
                "min": round(min(excellence_scores), 2),
                "max": round(max(excellence_scores), 2)
            }
        }

    def _analyze_god_mode_quality(self, metrics_list: List[NeuroglyphMetrics]) -> Dict[str, Any]:
        """Analizza qualità specifica per GOD MODE."""

        if not metrics_list:
            return {}

        # Soglie GOD MODE
        thresholds = {
            'excellence_score': 85.0,
            'symbolic_completeness': 0.8,
            'logical_structure_score': 0.7,
            'determinism_score': 0.8,
            'symbol_quality': 0.7,
            'multi_hop_depth_min': 3,
            'multi_hop_depth_max': 8
        }

        # Conta esempi che superano soglie
        god_mode_examples = 0
        excellent_examples = 0
        good_examples = 0

        quality_distribution = {
            'supreme_95+': 0,
            'excellent_85+': 0,
            'good_75+': 0,
            'acceptable_65+': 0,
            'poor_below_65': 0
        }

        for metrics in metrics_list:
            score = metrics.excellence_score

            # Distribuzione qualità
            if score >= 95:
                quality_distribution['supreme_95+'] += 1
                god_mode_examples += 1
            elif score >= 85:
                quality_distribution['excellent_85+'] += 1
                excellent_examples += 1
            elif score >= 75:
                quality_distribution['good_75+'] += 1
                good_examples += 1
            elif score >= 65:
                quality_distribution['acceptable_65+'] += 1
            else:
                quality_distribution['poor_below_65'] += 1

        # Analisi compliance soglie
        compliance = {}
        for threshold_name, threshold_value in thresholds.items():
            if threshold_name == 'multi_hop_depth_min':
                compliant = sum(1 for m in metrics_list if m.multi_hop_depth >= threshold_value)
            elif threshold_name == 'multi_hop_depth_max':
                compliant = sum(1 for m in metrics_list if m.multi_hop_depth <= threshold_value)
            else:
                metric_values = [getattr(m, threshold_name) for m in metrics_list]
                compliant = sum(1 for v in metric_values if v >= threshold_value)

            compliance[threshold_name] = {
                'compliant_count': compliant,
                'compliance_rate': compliant / len(metrics_list),
                'threshold': threshold_value
            }

        return {
            "quality_distribution": quality_distribution,
            "god_mode_examples": god_mode_examples,
            "excellent_examples": excellent_examples,
            "good_examples": good_examples,
            "total_examples": len(metrics_list),
            "god_mode_rate": god_mode_examples / len(metrics_list),
            "threshold_compliance": compliance,
            "overall_god_mode_readiness": (god_mode_examples + excellent_examples) / len(metrics_list) >= 0.8
        }

    def _generate_god_mode_recommendations(self, stats: Dict[str, Any], analysis: Dict[str, Any]) -> List[str]:
        """Genera raccomandazioni specifiche per GOD MODE."""

        recommendations = []

        # Analisi excellence score
        if stats['excellence_score']['mean'] < 85:
            recommendations.append(f"🔴 CRITICAL: Excellence score medio ({stats['excellence_score']['mean']}) sotto soglia GOD MODE (85)")
            recommendations.append("   → Migliorare qualità generale degli esempi")

        # Analisi symbolic completeness
        if stats['symbolic_completeness']['mean'] < 0.8:
            recommendations.append(f"🟡 WARNING: Symbolic completeness ({stats['symbolic_completeness']['mean']:.2f}) sotto target (0.8)")
            recommendations.append("   → Aumentare uso coerente simboli NEUROGLYPH")

        # Analisi logical structure
        if stats['logical_structure_score']['mean'] < 0.7:
            recommendations.append(f"🟡 WARNING: Logical structure score ({stats['logical_structure_score']['mean']:.2f}) sotto target (0.7)")
            recommendations.append("   → Rafforzare struttura ragionamento simbolico")

        # Analisi multi-hop depth
        target_range_rate = stats['multi_hop_depth'].get('target_range_3_8', 0)
        if target_range_rate < 0.9:
            recommendations.append(f"🟡 WARNING: Solo {target_range_rate:.1%} esempi nel range multi-hop 3-8")
            recommendations.append("   → Bilanciare profondità ragionamento")

        # Analisi determinism
        if stats['determinism_score']['mean'] < 0.8:
            recommendations.append(f"🔴 CRITICAL: Determinism score ({stats['determinism_score']['mean']:.2f}) sotto soglia (0.8)")
            recommendations.append("   → Eliminare incertezza e aumentare verificabilità")

        # Analisi zero hallucination
        hallucination_rate = stats['zero_hallucination']['rate']
        if hallucination_rate < 0.95:
            recommendations.append(f"🔴 CRITICAL: Zero hallucination rate ({hallucination_rate:.1%}) sotto target (95%)")
            recommendations.append("   → Rimuovere tutti i contenuti non verificabili")

        # Analisi symbol quality
        if stats['symbol_quality']['mean'] < 0.7:
            recommendations.append(f"🟡 WARNING: Symbol quality ({stats['symbol_quality']['mean']:.2f}) sotto target (0.7)")
            recommendations.append("   → Migliorare correttezza uso simboli")

        # Analisi cognitive tags
        if stats['cognitive_tags_presence']['mean'] < 0.5:
            recommendations.append(f"🟡 INFO: Cognitive tags presence ({stats['cognitive_tags_presence']['mean']:.2f}) bassa")
            recommendations.append("   → Considerare aggiunta marcatori cognitivi espliciti")

        # Raccomandazioni positive
        if stats['excellence_score']['mean'] >= 90:
            recommendations.append("🎊 EXCELLENT: Dataset raggiunge standard SUPREME GOD MODE!")
            recommendations.append("   → Pronto per training LLM simbolico deterministico")
        elif stats['excellence_score']['mean'] >= 85:
            recommendations.append("✅ GOOD: Dataset raggiunge standard GOD MODE")
            recommendations.append("   → Pronto per training con qualità eccellente")

        # Raccomandazioni specifiche per distribuzione qualità
        god_mode_rate = analysis.get('god_mode_rate', 0)
        if god_mode_rate < 0.2:
            recommendations.append(f"🔴 CRITICAL: Solo {god_mode_rate:.1%} esempi GOD MODE quality")
            recommendations.append("   → Rigenerare esempi con qualità superiore")
        elif god_mode_rate < 0.5:
            recommendations.append(f"🟡 WARNING: {god_mode_rate:.1%} esempi GOD MODE quality")
            recommendations.append("   → Aumentare percentuale esempi eccellenti")

        return recommendations

    def _assess_overall_readiness(self, stats: Dict[str, Any]) -> bool:
        """Valuta readiness complessiva per GOD MODE."""

        criteria = [
            stats['excellence_score']['mean'] >= 85,
            stats['determinism_score']['mean'] >= 0.8,
            stats['zero_hallucination']['rate'] >= 0.95,
            stats['multi_hop_depth']['mean'] >= 3,
            stats['symbolic_completeness']['mean'] >= 0.7
        ]

        return sum(criteria) >= 4  # Almeno 4/5 criteri soddisfatti

    def _print_evaluation_summary(self, report: Dict[str, Any]):
        """Stampa summary della valutazione."""

        print("\n" + "="*60)
        print("📊 NEUROGLYPH GOD MODE EVALUATION SUMMARY")
        print("="*60)

        stats = report['aggregate_statistics']
        analysis = report['god_mode_analysis']
        readiness = report['neuroglyph_readiness']

        # Metriche principali
        print(f"\n🎯 CORE METRICS:")
        print(f"   Excellence Score: {stats['excellence_score']['mean']}/100 (±{stats['excellence_score']['std']})")
        print(f"   Symbolic Completeness: {stats['symbolic_completeness']['mean']:.2f}/1.0")
        print(f"   Logical Structure: {stats['logical_structure_score']['mean']:.2f}/1.0")
        print(f"   Multi-hop Depth: {stats['multi_hop_depth']['mean']:.1f} (target: 3-8)")
        print(f"   Determinism Score: {stats['determinism_score']['mean']:.2f}/1.0")
        print(f"   Zero Hallucination: {stats['zero_hallucination']['rate']:.1%}")
        print(f"   Symbol Quality: {stats['symbol_quality']['mean']:.2f}/1.0")

        # Distribuzione qualità
        print(f"\n📈 QUALITY DISTRIBUTION:")
        for quality_level, count in analysis['quality_distribution'].items():
            percentage = count / report['total_examples'] * 100
            print(f"   {quality_level}: {count} ({percentage:.1f}%)")

        # Readiness assessment
        print(f"\n🚀 NEUROGLYPH READINESS:")
        for criterion, ready in readiness.items():
            emoji = "✅" if ready else "❌"
            print(f"   {emoji} {criterion.replace('_', ' ').title()}")

        # Overall status
        overall_ready = readiness['overall_god_mode_ready']
        status_emoji = "🎊" if overall_ready else "⚠️"
        status_text = "READY FOR GOD MODE TRAINING" if overall_ready else "NEEDS IMPROVEMENTS"

        print(f"\n{status_emoji} OVERALL STATUS: {status_text}")

        # Top recommendations
        recommendations = report['recommendations']
        if recommendations:
            print(f"\n💡 TOP RECOMMENDATIONS:")
            for rec in recommendations[:5]:
                print(f"   {rec}")

        print("="*60)

import os

def main():
    """Esegue valutazione GOD MODE del dataset NEUROGLYPH."""

    dataset_path = "neuroglyph_adaptive_depth_20k.json"
    registry_path = "neuroglyph/core/locked_registry_godmode_v9.json"

    print("🔍 NEUROGLYPH GOD MODE Validator")
    print("=" * 50)
    print("🎯 Sistema valutazione personalizzato per LLM simbolico")
    print("🧠 Metriche oltre BLEU/ROUGE/MMLU per intelligenza reale")

    # Inizializza validator
    validator = NeuroglyphGodModeValidator(registry_path)

    # Esegui valutazione
    evaluation_report = validator.evaluate_dataset(dataset_path)

    if "error" not in evaluation_report:
        # Salva report
        report_filename = f"neuroglyph_god_mode_evaluation_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"

        with open(report_filename, 'w', encoding='utf-8') as f:
            json.dump(evaluation_report, f, indent=2, ensure_ascii=False)

        print(f"\n💾 Evaluation report saved: {report_filename}")
        print(f"🎯 Ready for NEUROGLYPH GOD MODE assessment!")

    else:
        print(f"❌ Evaluation failed: {evaluation_report['error']}")

if __name__ == "__main__":
    main()
