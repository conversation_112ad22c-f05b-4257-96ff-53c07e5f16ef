#!/usr/bin/env python3
"""
NEUROGLYPH FASE 3 - LLM-FRIENDLY VALIDATION
Validazione finale LLM-friendly: tokenizzazione, embedding stability, compressione semantica
"""

import json
import sys
import time
from pathlib import Path
from datetime import datetime
from typing import Dict, List, Any, Tuple
import hashlib

class LLMFriendlyValidator:
    """Validator per compatibilità LLM del registry NEUROGLYPH."""
    
    def __init__(self, registry_path: str = "neuroglyph/core/symbols_registry.json"):
        self.registry_path = Path(registry_path)
        self.registry = {}
        self.validation_timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        self.validation_results = {}
        
    def load_registry(self) -> bool:
        """Carica il registry dei simboli."""
        try:
            with open(self.registry_path, 'r', encoding='utf-8') as f:
                self.registry = json.load(f)
            print(f"✅ Registry caricato: {len(self.registry.get('approved_symbols', []))} simboli")
            return True
        except Exception as e:
            print(f"❌ Errore caricamento registry: {e}")
            return False
    
    def validate_tokenization_compatibility(self) -> Dict[str, Any]:
        """Valida compatibilità tokenizzazione per LLM."""
        symbols = self.registry.get("approved_symbols", [])
        
        tokenization_stats = {
            "total_symbols": len(symbols),
            "single_token_symbols": 0,
            "multi_token_symbols": 0,
            "problematic_symbols": [],
            "token_cost_distribution": {},
            "fallback_analysis": {
                "total_fallbacks": 0,
                "compact_fallbacks": 0,  # ≤ 8 chars
                "long_fallbacks": 0,     # > 8 chars
                "avg_fallback_length": 0
            }
        }
        
        total_fallback_length = 0
        
        for symbol in symbols:
            token_cost = symbol.get("token_cost", 1)
            fallback = symbol.get("fallback", "")
            symbol_char = symbol.get("symbol", "")
            
            # Analisi token cost
            if token_cost == 1:
                tokenization_stats["single_token_symbols"] += 1
            else:
                tokenization_stats["multi_token_symbols"] += 1
                if token_cost > 2:
                    tokenization_stats["problematic_symbols"].append({
                        "id": symbol.get("id", ""),
                        "symbol": symbol_char,
                        "token_cost": token_cost,
                        "reason": "High token cost"
                    })
            
            # Distribuzione token cost
            cost_key = str(token_cost)
            tokenization_stats["token_cost_distribution"][cost_key] = \
                tokenization_stats["token_cost_distribution"].get(cost_key, 0) + 1
            
            # Analisi fallback
            if fallback:
                tokenization_stats["fallback_analysis"]["total_fallbacks"] += 1
                fallback_length = len(fallback)
                total_fallback_length += fallback_length
                
                if fallback_length <= 8:
                    tokenization_stats["fallback_analysis"]["compact_fallbacks"] += 1
                else:
                    tokenization_stats["fallback_analysis"]["long_fallbacks"] += 1
                    tokenization_stats["problematic_symbols"].append({
                        "id": symbol.get("id", ""),
                        "symbol": symbol_char,
                        "fallback": fallback,
                        "fallback_length": fallback_length,
                        "reason": "Long fallback"
                    })
        
        # Calcola media lunghezza fallback
        if tokenization_stats["fallback_analysis"]["total_fallbacks"] > 0:
            tokenization_stats["fallback_analysis"]["avg_fallback_length"] = \
                total_fallback_length / tokenization_stats["fallback_analysis"]["total_fallbacks"]
        
        return tokenization_stats
    
    def validate_embedding_stability(self) -> Dict[str, Any]:
        """Valida stabilità embedding per simboli."""
        symbols = self.registry.get("approved_symbols", [])
        
        embedding_stats = {
            "total_symbols": len(symbols),
            "unicode_ranges": {},
            "symbol_density": {},
            "potential_conflicts": [],
            "unicode_coverage": {
                "mathematical_operators": 0,
                "miscellaneous_technical": 0,
                "geometric_shapes": 0,
                "dingbats": 0,
                "supplemental_arrows": 0,
                "other_ranges": 0
            }
        }
        
        # Analizza range Unicode
        for symbol in symbols:
            unicode_point = symbol.get("unicode_point", "")
            if unicode_point:
                try:
                    code_point = int(unicode_point.replace("U+", ""), 16)
                    
                    # Classifica per range
                    if 0x2200 <= code_point <= 0x22FF:
                        range_name = "mathematical_operators"
                    elif 0x2300 <= code_point <= 0x23FF:
                        range_name = "miscellaneous_technical"
                    elif 0x25A0 <= code_point <= 0x25FF:
                        range_name = "geometric_shapes"
                    elif 0x2700 <= code_point <= 0x27BF:
                        range_name = "dingbats"
                    elif 0x2900 <= code_point <= 0x29FF:
                        range_name = "supplemental_arrows"
                    else:
                        range_name = "other_ranges"
                    
                    embedding_stats["unicode_coverage"][range_name] += 1
                    
                    # Conta per range
                    range_key = f"{code_point // 256 * 256:04X}-{(code_point // 256 + 1) * 256 - 1:04X}"
                    embedding_stats["unicode_ranges"][range_key] = \
                        embedding_stats["unicode_ranges"].get(range_key, 0) + 1
                    
                except Exception:
                    embedding_stats["potential_conflicts"].append({
                        "id": symbol.get("id", ""),
                        "unicode_point": unicode_point,
                        "reason": "Invalid unicode point"
                    })
        
        return embedding_stats
    
    def validate_semantic_compression(self) -> Dict[str, Any]:
        """Valida compressione semantica del registry."""
        symbols = self.registry.get("approved_symbols", [])
        
        compression_stats = {
            "total_symbols": len(symbols),
            "domain_distribution": {},
            "semantic_density": {},
            "code_patterns": {},
            "naming_consistency": {
                "consistent_naming": 0,
                "inconsistent_naming": 0,
                "naming_issues": []
            }
        }
        
        # Analizza distribuzione domini
        for symbol in symbols:
            category = symbol.get("category", "unknown")
            code = symbol.get("code", "")
            name = symbol.get("name", "")
            
            # Conta per dominio
            compression_stats["domain_distribution"][category] = \
                compression_stats["domain_distribution"].get(category, 0) + 1
            
            # Analizza pattern code
            if ":" in code:
                parts = code.split(":")
                if len(parts) >= 2:
                    pattern = f"{parts[0]}:{parts[1]}"
                    compression_stats["code_patterns"][pattern] = \
                        compression_stats["code_patterns"].get(pattern, 0) + 1
            
            # Analizza consistenza naming
            if "_" in name and not name.endswith("_1") and not name.endswith("_2"):
                compression_stats["naming_consistency"]["consistent_naming"] += 1
            else:
                compression_stats["naming_consistency"]["inconsistent_naming"] += 1
                if name.endswith(("_1", "_2", "_3")):
                    compression_stats["naming_consistency"]["naming_issues"].append({
                        "id": symbol.get("id", ""),
                        "name": name,
                        "reason": "Generic numbered name"
                    })
        
        return compression_stats
    
    def calculate_registry_hash(self) -> str:
        """Calcola hash del registry per validazione integrità."""
        symbols = self.registry.get("approved_symbols", [])
        
        # Crea stringa deterministica per hash
        hash_data = []
        for symbol in sorted(symbols, key=lambda x: x.get("id", "")):
            symbol_str = f"{symbol.get('id', '')}{symbol.get('symbol', '')}{symbol.get('code', '')}"
            hash_data.append(symbol_str)
        
        registry_string = "|".join(hash_data)
        return hashlib.sha256(registry_string.encode()).hexdigest()
    
    def generate_validation_summary(self, tokenization_stats: Dict[str, Any],
                                  embedding_stats: Dict[str, Any],
                                  compression_stats: Dict[str, Any]) -> Dict[str, Any]:
        """Genera summary completo della validazione."""
        registry_hash = self.calculate_registry_hash()
        
        # Calcola score di qualità LLM
        llm_quality_score = 0
        
        # Score tokenizzazione (40% peso)
        token_score = (tokenization_stats["single_token_symbols"] / 
                      tokenization_stats["total_symbols"]) * 40
        
        # Score embedding (30% peso)
        safe_ranges = sum([
            embedding_stats["unicode_coverage"]["mathematical_operators"],
            embedding_stats["unicode_coverage"]["miscellaneous_technical"],
            embedding_stats["unicode_coverage"]["geometric_shapes"]
        ])
        embedding_score = (safe_ranges / embedding_stats["total_symbols"]) * 30
        
        # Score compressione (30% peso)
        compression_score = (compression_stats["naming_consistency"]["consistent_naming"] / 
                           compression_stats["total_symbols"]) * 30
        
        llm_quality_score = token_score + embedding_score + compression_score
        
        return {
            "validation_timestamp": self.validation_timestamp,
            "registry_hash": registry_hash,
            "total_symbols": tokenization_stats["total_symbols"],
            "llm_quality_score": round(llm_quality_score, 1),
            "tokenization_stats": tokenization_stats,
            "embedding_stats": embedding_stats,
            "compression_stats": compression_stats,
            "readiness_assessment": {
                "tokenization_ready": tokenization_stats["single_token_symbols"] / tokenization_stats["total_symbols"] > 0.9,
                "embedding_ready": len(embedding_stats["potential_conflicts"]) == 0,
                "compression_ready": compression_stats["naming_consistency"]["consistent_naming"] / compression_stats["total_symbols"] > 0.8,
                "overall_ready": llm_quality_score > 80.0
            },
            "recommendations": self._generate_recommendations(tokenization_stats, embedding_stats, compression_stats)
        }
    
    def _generate_recommendations(self, tokenization_stats: Dict[str, Any],
                                embedding_stats: Dict[str, Any],
                                compression_stats: Dict[str, Any]) -> List[str]:
        """Genera raccomandazioni per miglioramento."""
        recommendations = []
        
        # Raccomandazioni tokenizzazione
        if tokenization_stats["multi_token_symbols"] > tokenization_stats["total_symbols"] * 0.1:
            recommendations.append("Ridurre simboli multi-token per migliorare efficienza")
        
        if tokenization_stats["fallback_analysis"]["long_fallbacks"] > 0:
            recommendations.append("Abbreviare fallback lunghi per compatibilità terminale")
        
        # Raccomandazioni embedding
        if embedding_stats["potential_conflicts"]:
            recommendations.append("Risolvere conflitti Unicode per stabilità embedding")
        
        # Raccomandazioni compressione
        if compression_stats["naming_consistency"]["inconsistent_naming"] > compression_stats["total_symbols"] * 0.2:
            recommendations.append("Migliorare consistenza naming per compressione semantica")
        
        if not recommendations:
            recommendations.append("Registry pronto per integrazione LLM simbolico!")
        
        return recommendations
    
    def save_validation_results(self, validation_summary: Dict[str, Any]) -> str:
        """Salva risultati validazione."""
        output_path = f"neuroglyph/validation/llm_validation_results_{self.validation_timestamp}.json"
        
        with open(output_path, 'w', encoding='utf-8') as f:
            json.dump(validation_summary, f, indent=2, ensure_ascii=False)
        
        return output_path

def main():
    """Esegue validazione LLM-friendly completa."""
    print("🧠 NEUROGLYPH LLM - FASE 3: LLM-FRIENDLY VALIDATION")
    print("🎯 Validazione finale: tokenizzazione, embedding stability, compressione semantica")
    print("=" * 70)
    
    # Crea validator
    validator = LLMFriendlyValidator()
    
    # Carica registry
    if not validator.load_registry():
        sys.exit(1)
    
    print("🔍 Validazione tokenizzazione...")
    tokenization_stats = validator.validate_tokenization_compatibility()
    
    print("🔍 Validazione embedding stability...")
    embedding_stats = validator.validate_embedding_stability()
    
    print("🔍 Validazione compressione semantica...")
    compression_stats = validator.validate_semantic_compression()
    
    print("📊 Generazione summary...")
    validation_summary = validator.generate_validation_summary(
        tokenization_stats, embedding_stats, compression_stats
    )
    
    # Salva risultati
    output_path = validator.save_validation_results(validation_summary)
    
    # Stampa risultati
    print(f"\n🎉 VALIDAZIONE LLM-FRIENDLY COMPLETATA!")
    print(f"📊 Simboli validati: {validation_summary['total_symbols']}")
    print(f"🏆 LLM Quality Score: {validation_summary['llm_quality_score']}/100")
    print(f"🔐 Registry Hash: {validation_summary['registry_hash'][:16]}...")
    
    print(f"\n📈 READINESS ASSESSMENT:")
    readiness = validation_summary['readiness_assessment']
    print(f"  ✅ Tokenization Ready: {'✅' if readiness['tokenization_ready'] else '❌'}")
    print(f"  ✅ Embedding Ready: {'✅' if readiness['embedding_ready'] else '❌'}")
    print(f"  ✅ Compression Ready: {'✅' if readiness['compression_ready'] else '❌'}")
    print(f"  🎯 Overall Ready: {'✅' if readiness['overall_ready'] else '❌'}")
    
    print(f"\n💡 RACCOMANDAZIONI:")
    for rec in validation_summary['recommendations']:
        print(f"  • {rec}")
    
    print(f"\n💾 Risultati salvati: {output_path}")
    
    print(f"\n🚀 PROSSIMI PASSI:")
    if readiness['overall_ready']:
        print(f"  1. ✅ Registry pronto per test inferenza")
        print(f"  2. Test con Qwen/DeepSeek quantizzati")
        print(f"  3. Benchmark stabilità semantica")
        print(f"  4. Lock GOD-tier registry finale")
    else:
        print(f"  1. Applicare raccomandazioni di miglioramento")
        print(f"  2. Ri-eseguire validazione")
        print(f"  3. Procedere con test inferenza")
    
    return readiness['overall_ready']

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
