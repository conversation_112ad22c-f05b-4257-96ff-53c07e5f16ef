"""
NEUROGLYPH LLM - AST Corrector
==============================

Validatore e correttore automatico di strutture AST per garantire
codice logicamente perfetto e sintatticamente corretto.

Autore: NEUROGLYPH Team
Data: 2025
Licenza: MIT
"""

import ast
import logging
from typing import Dict, List, Any, Optional, Tuple, Union
from dataclasses import dataclass
from enum import Enum

# Setup logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class ErrorType(Enum):
    """Tipi di errori AST"""
    SYNTAX_ERROR = "syntax_error"
    SEMANTIC_ERROR = "semantic_error"
    TYPE_ERROR = "type_error"
    SCOPE_ERROR = "scope_error"
    LOGIC_ERROR = "logic_error"

@dataclass
class ASTError:
    """Rappresenta un errore nell'AST"""
    type: ErrorType
    node: Optional[ast.AST]
    message: str
    line: Optional[int] = None
    column: Optional[int] = None
    severity: str = "error"  # error, warning, info

@dataclass
class ValidationResult:
    """Risultato della validazione AST"""
    is_valid: bool
    errors: List[ASTError]
    warnings: List[ASTError]
    score: float
    suggestions: List[str]

class ASTValidator:
    """
    Validatore AST che verifica correttezza sintattica e semantica.
    """
    
    def __init__(self):
        """Inizializza il validatore"""
        self.validation_rules = self._load_validation_rules()
        self.stats = {
            "total_validations": 0,
            "successful_validations": 0,
            "errors_found": 0,
            "warnings_found": 0
        }
        
        logger.info("🔍 ASTValidator inizializzato")
    
    def _load_validation_rules(self) -> Dict[str, Any]:
        """Carica regole di validazione"""
        return {
            "required_nodes": ["FunctionDef"],
            "forbidden_patterns": ["eval", "exec"],
            "max_complexity": 10,
            "max_depth": 5,
            "naming_conventions": {
                "functions": r"^[a-z_][a-z0-9_]*$",
                "classes": r"^[A-Z][a-zA-Z0-9]*$",
                "variables": r"^[a-z_][a-z0-9_]*$"
            }
        }
    
    def validate(self, code: Union[str, ast.AST]) -> ValidationResult:
        """
        Valida codice o AST.
        
        Args:
            code: Codice sorgente o AST da validare
            
        Returns:
            ValidationResult con errori e suggerimenti
        """
        logger.info("🔍 Inizio validazione AST...")
        self.stats["total_validations"] += 1
        
        try:
            # Converte a AST se necessario
            if isinstance(code, str):
                tree = ast.parse(code)
            else:
                tree = code
            
            errors = []
            warnings = []
            
            # Validazione sintattica
            syntax_errors = self._validate_syntax(tree)
            errors.extend(syntax_errors)
            
            # Validazione semantica
            semantic_errors = self._validate_semantics(tree)
            errors.extend(semantic_errors)
            
            # Validazione logica
            logic_errors = self._validate_logic(tree)
            errors.extend(logic_errors)
            
            # Calcola score
            score = self._calculate_validation_score(errors, warnings)
            
            # Genera suggerimenti
            suggestions = self._generate_suggestions(errors, warnings)
            
            is_valid = len([e for e in errors if e.severity == "error"]) == 0
            
            if is_valid:
                self.stats["successful_validations"] += 1
            
            self.stats["errors_found"] += len(errors)
            self.stats["warnings_found"] += len(warnings)
            
            result = ValidationResult(
                is_valid=is_valid,
                errors=errors,
                warnings=warnings,
                score=score,
                suggestions=suggestions
            )
            
            logger.info(f"📊 Validazione completata: {score:.2f}/100")
            return result
            
        except Exception as e:
            logger.error(f"❌ Errore durante validazione: {e}")
            return ValidationResult(
                is_valid=False,
                errors=[ASTError(ErrorType.SYNTAX_ERROR, None, str(e))],
                warnings=[],
                score=0.0,
                suggestions=["Correggere errori di sintassi"]
            )
    
    def _validate_syntax(self, tree: ast.AST) -> List[ASTError]:
        """Valida sintassi AST"""
        errors = []
        
        # Verifica nodi richiesti
        required_nodes = self.validation_rules["required_nodes"]
        found_nodes = {type(node).__name__ for node in ast.walk(tree)}
        
        for required in required_nodes:
            if required not in found_nodes:
                errors.append(ASTError(
                    type=ErrorType.SYNTAX_ERROR,
                    node=None,
                    message=f"Nodo richiesto mancante: {required}"
                ))
        
        # Verifica pattern proibiti
        forbidden = self.validation_rules["forbidden_patterns"]
        for node in ast.walk(tree):
            if isinstance(node, ast.Name) and node.id in forbidden:
                errors.append(ASTError(
                    type=ErrorType.SYNTAX_ERROR,
                    node=node,
                    message=f"Pattern proibito: {node.id}",
                    line=getattr(node, 'lineno', None)
                ))
        
        return errors
    
    def _validate_semantics(self, tree: ast.AST) -> List[ASTError]:
        """Valida semantica AST"""
        errors = []
        
        # Verifica complessità
        complexity = self._calculate_complexity(tree)
        max_complexity = self.validation_rules["max_complexity"]
        
        if complexity > max_complexity:
            errors.append(ASTError(
                type=ErrorType.SEMANTIC_ERROR,
                node=None,
                message=f"Complessità troppo alta: {complexity} > {max_complexity}"
            ))
        
        # Verifica profondità
        depth = self._calculate_depth(tree)
        max_depth = self.validation_rules["max_depth"]
        
        if depth > max_depth:
            errors.append(ASTError(
                type=ErrorType.SEMANTIC_ERROR,
                node=None,
                message=f"Profondità troppo alta: {depth} > {max_depth}"
            ))
        
        return errors
    
    def _validate_logic(self, tree: ast.AST) -> List[ASTError]:
        """Valida logica AST"""
        errors = []
        
        # Verifica return statements
        for node in ast.walk(tree):
            if isinstance(node, ast.FunctionDef):
                if not self._has_return_statement(node):
                    errors.append(ASTError(
                        type=ErrorType.LOGIC_ERROR,
                        node=node,
                        message=f"Funzione {node.name} senza return statement",
                        line=getattr(node, 'lineno', None)
                    ))
        
        return errors
    
    def _calculate_complexity(self, tree: ast.AST) -> int:
        """Calcola complessità ciclomatica"""
        complexity = 1  # Base complexity
        
        for node in ast.walk(tree):
            if isinstance(node, (ast.If, ast.While, ast.For, ast.Try)):
                complexity += 1
            elif isinstance(node, ast.BoolOp):
                complexity += len(node.values) - 1
        
        return complexity
    
    def _calculate_depth(self, tree: ast.AST) -> int:
        """Calcola profondità massima dell'AST"""
        def get_depth(node, current_depth=0):
            max_depth = current_depth
            for child in ast.iter_child_nodes(node):
                child_depth = get_depth(child, current_depth + 1)
                max_depth = max(max_depth, child_depth)
            return max_depth
        
        return get_depth(tree)
    
    def _has_return_statement(self, func_node: ast.FunctionDef) -> bool:
        """Verifica se una funzione ha return statement"""
        for node in ast.walk(func_node):
            if isinstance(node, ast.Return):
                return True
        return False
    
    def _calculate_validation_score(self, errors: List[ASTError], warnings: List[ASTError]) -> float:
        """Calcola score di validazione"""
        error_penalty = len([e for e in errors if e.severity == "error"]) * 20
        warning_penalty = len([e for e in warnings if e.severity == "warning"]) * 5
        
        score = max(0, 100 - error_penalty - warning_penalty)
        return score
    
    def _generate_suggestions(self, errors: List[ASTError], warnings: List[ASTError]) -> List[str]:
        """Genera suggerimenti per migliorare il codice"""
        suggestions = []
        
        for error in errors:
            if error.type == ErrorType.SYNTAX_ERROR:
                suggestions.append(f"Correggere errore di sintassi: {error.message}")
            elif error.type == ErrorType.LOGIC_ERROR:
                suggestions.append(f"Correggere errore logico: {error.message}")
        
        return suggestions


class ASTCorrector:
    """
    Correttore automatico di errori AST.
    """
    
    def __init__(self):
        """Inizializza il correttore"""
        self.validator = ASTValidator()
        self.correction_strategies = self._load_correction_strategies()
        self.stats = {
            "total_corrections": 0,
            "successful_corrections": 0,
            "failed_corrections": 0
        }
        
        logger.info("🔧 ASTCorrector inizializzato")
    
    def _load_correction_strategies(self) -> Dict[ErrorType, callable]:
        """Carica strategie di correzione per ogni tipo di errore"""
        return {
            ErrorType.SYNTAX_ERROR: self._correct_syntax_error,
            ErrorType.SEMANTIC_ERROR: self._correct_semantic_error,
            ErrorType.TYPE_ERROR: self._correct_type_error,
            ErrorType.SCOPE_ERROR: self._correct_scope_error,
            ErrorType.LOGIC_ERROR: self._correct_logic_error
        }
    
    def correct(self, code: Union[str, ast.AST]) -> Tuple[str, ValidationResult]:
        """
        Corregge automaticamente errori nel codice.
        
        Args:
            code: Codice sorgente o AST da correggere
            
        Returns:
            Tuple[codice_corretto, risultato_validazione]
        """
        logger.info("🔧 Inizio correzione automatica...")
        self.stats["total_corrections"] += 1
        
        try:
            # Converte a AST se necessario
            if isinstance(code, str):
                tree = ast.parse(code)
                original_code = code
            else:
                tree = code
                original_code = ast.unparse(tree)
            
            # Valida per identificare errori
            validation_result = self.validator.validate(tree)
            
            if validation_result.is_valid:
                logger.info("✅ Codice già valido, nessuna correzione necessaria")
                return original_code, validation_result
            
            # Applica correzioni
            corrected_tree = self._apply_corrections(tree, validation_result.errors)
            
            # Ri-valida
            final_validation = self.validator.validate(corrected_tree)
            
            # Converte a codice
            corrected_code = ast.unparse(corrected_tree)
            
            if final_validation.is_valid:
                self.stats["successful_corrections"] += 1
                logger.info("✅ Correzione completata con successo")
            else:
                self.stats["failed_corrections"] += 1
                logger.warning("⚠️ Correzione parziale, alcuni errori persistono")
            
            return corrected_code, final_validation
            
        except Exception as e:
            logger.error(f"❌ Errore durante correzione: {e}")
            self.stats["failed_corrections"] += 1
            return original_code if 'original_code' in locals() else str(code), validation_result
    
    def _apply_corrections(self, tree: ast.AST, errors: List[ASTError]) -> ast.AST:
        """Applica correzioni all'AST"""
        corrected_tree = tree
        
        for error in errors:
            strategy = self.correction_strategies.get(error.type)
            if strategy:
                try:
                    corrected_tree = strategy(corrected_tree, error)
                except Exception as e:
                    logger.warning(f"⚠️ Impossibile correggere errore {error.type}: {e}")
        
        return corrected_tree
    
    def _correct_syntax_error(self, tree: ast.AST, error: ASTError) -> ast.AST:
        """Corregge errori di sintassi"""
        # Implementazione semplificata
        if "Nodo richiesto mancante: FunctionDef" in error.message:
            # Aggiunge una funzione base se mancante
            if not any(isinstance(node, ast.FunctionDef) for node in tree.body):
                func_node = ast.FunctionDef(
                    name="main_function",
                    args=ast.arguments(
                        posonlyargs=[],
                        args=[],
                        vararg=None,
                        kwonlyargs=[],
                        kw_defaults=[],
                        kwarg=None,
                        defaults=[]
                    ),
                    body=[ast.Return(value=ast.Constant(value=None))],
                    decorator_list=[],
                    returns=None
                )
                tree.body.append(func_node)
        
        return tree
    
    def _correct_semantic_error(self, tree: ast.AST, error: ASTError) -> ast.AST:
        """Corregge errori semantici"""
        # Implementazione base - in futuro più sofisticata
        return tree
    
    def _correct_type_error(self, tree: ast.AST, error: ASTError) -> ast.AST:
        """Corregge errori di tipo"""
        return tree
    
    def _correct_scope_error(self, tree: ast.AST, error: ASTError) -> ast.AST:
        """Corregge errori di scope"""
        return tree
    
    def _correct_logic_error(self, tree: ast.AST, error: ASTError) -> ast.AST:
        """Corregge errori logici"""
        if "senza return statement" in error.message:
            # Aggiunge return statement alle funzioni che ne sono prive
            for node in ast.walk(tree):
                if isinstance(node, ast.FunctionDef) and not self._has_return(node):
                    node.body.append(ast.Return(value=ast.Constant(value=None)))
        
        return tree
    
    def _has_return(self, func_node: ast.FunctionDef) -> bool:
        """Verifica se una funzione ha return statement"""
        for node in ast.walk(func_node):
            if isinstance(node, ast.Return):
                return True
        return False


# Funzione demo
def demo_ast_corrector():
    """Demo dell'AST Corrector"""
    print("🔧 NEUROGLYPH LLM - Demo AST Corrector")
    print("=" * 50)
    
    # Codice con errori
    buggy_code = """
# Funzione senza return
def broken_function(x):
    result = x * 2
    # Manca return statement
"""
    
    print("❌ CODICE CON ERRORI:")
    print(buggy_code)
    
    # Corregge automaticamente
    corrector = ASTCorrector()
    corrected_code, validation = corrector.correct(buggy_code)
    
    print("\n✅ CODICE CORRETTO:")
    print(corrected_code)
    
    print(f"\n📊 Risultato validazione:")
    print(f"   - Valido: {validation.is_valid}")
    print(f"   - Score: {validation.score:.1f}/100")
    print(f"   - Errori: {len(validation.errors)}")
    print(f"   - Warning: {len(validation.warnings)}")


if __name__ == "__main__":
    demo_ast_corrector()
