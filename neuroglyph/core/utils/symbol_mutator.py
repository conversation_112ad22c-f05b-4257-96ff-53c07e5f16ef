#!/usr/bin/env python3
"""
NEUROGLYPH LLM - SYMBOL MUTATOR
===============================

Modulo per la mutazione e generazione autonoma di nuovi simboli neuroglifi.
Implementa algoritmi di crescita simbolica per l'espansione infinita dell'intelligenza.

Funzioni principali:
- Mutazione simbolica controllata
- Combinazione semantica di simboli esistenti
- Generazione di meta-simboli
- Validazione automatica delle mutazioni
- Registrazione evolutiva

Autore: NEUROGLYPH ULTRA Team
Data: 2025-05-26
Licenza: MIT
"""

import sys
import os
import json
import random
import hashlib
import logging
from datetime import datetime
from typing import Dict, List, Set, Any, Optional, Tuple, Union
from dataclasses import dataclass, field
from enum import Enum
from pathlib import Path

# Aggiungi path per import moduli NEUROGLYPH
current_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.append(current_dir)

# Import componenti NEUROGLYPH
from enhanced_symbol_loader import EnhancedSymbolLoader, LoadingStrategy

# Configurazione logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class MutationType(Enum):
    """Tipi di mutazione simbolica."""
    COMBINATION = "combination"      # Combina 2+ simboli esistenti
    DERIVATION = "derivation"        # Deriva da simbolo base
    ABSTRACTION = "abstraction"      # Crea meta-simbolo
    SEMANTIC_SHIFT = "semantic_shift" # Modifica semantica
    UNICODE_MORPH = "unicode_morph"   # Trasformazione Unicode
    DOMAIN_BRIDGE = "domain_bridge"   # Collega domini diversi

class MutationStrategy(Enum):
    """Strategie di mutazione."""
    CONSERVATIVE = "conservative"    # Mutazioni sicure e validate
    CREATIVE = "creative"           # Mutazioni innovative
    AGGRESSIVE = "aggressive"       # Mutazioni sperimentali
    SEMANTIC = "semantic"           # Focus su coerenza semantica
    UNICODE_SAFE = "unicode_safe"   # Solo Unicode sicuri

@dataclass
class MutationCandidate:
    """Candidato per mutazione simbolica."""
    original_symbols: List[str]
    mutated_symbol: str
    mutation_type: MutationType
    semantic_category: str
    confidence_score: float
    unicode_safe: bool
    tokenizer_cost: int
    collision_risk: float
    metadata: Dict[str, Any] = field(default_factory=dict)

@dataclass
class MutationResult:
    """Risultato di una mutazione simbolica."""
    success: bool
    candidate: Optional[MutationCandidate]
    validation_passed: bool
    registry_updated: bool
    error_message: Optional[str] = None
    generation_time: float = 0.0

class SymbolMutator:
    """
    Motore di mutazione simbolica per NEUROGLYPH.

    Genera nuovi simboli attraverso algoritmi evolutivi controllati,
    garantendo coerenza semantica e validazione rigorosa.
    """

    def __init__(self,
                 registry_path: str = "neuroglyph/core/locked_registry_godmode_v9.json",
                 strategy: MutationStrategy = MutationStrategy.CONSERVATIVE):
        """
        Inizializza il mutatore simbolico.

        Args:
            registry_path: Percorso al registry simbolico
            strategy: Strategia di mutazione
        """
        self.registry_path = registry_path
        self.strategy = strategy

        # Carica registry simbolico
        self.symbol_loader = EnhancedSymbolLoader(registry_path, LoadingStrategy.COGNITIVE)
        self.symbol_loader.load_symbols()

        # Stato interno
        self.mutation_history: List[MutationResult] = []
        self.generated_symbols: Set[str] = set()
        self.semantic_clusters: Dict[str, List[str]] = {}
        self.unicode_pool: Set[str] = set()

        # Configurazione strategia
        self._configure_strategy(strategy)

        # Inizializza pool Unicode sicuri
        self._initialize_unicode_pool()

        # Costruisce cluster semantici
        self._build_semantic_clusters()

        logger.info(f"🧬 SymbolMutator inizializzato con strategia: {strategy.value}")
        logger.info(f"📊 Registry caricato: {len(self.symbol_loader.symbol_profiles)} simboli")
        logger.info(f"🎯 Cluster semantici: {len(self.semantic_clusters)}")

    def _configure_strategy(self, strategy: MutationStrategy):
        """Configura parametri per la strategia di mutazione."""
        if strategy == MutationStrategy.CONSERVATIVE:
            self.min_confidence = 0.85
            self.max_collision_risk = 0.1
            self.allowed_mutations = [MutationType.COMBINATION, MutationType.DERIVATION]

        elif strategy == MutationStrategy.CREATIVE:
            self.min_confidence = 0.70
            self.max_collision_risk = 0.2
            self.allowed_mutations = [MutationType.COMBINATION, MutationType.ABSTRACTION,
                                    MutationType.SEMANTIC_SHIFT]

        elif strategy == MutationStrategy.AGGRESSIVE:
            self.min_confidence = 0.60
            self.max_collision_risk = 0.3
            self.allowed_mutations = list(MutationType)

        elif strategy == MutationStrategy.SEMANTIC:
            self.min_confidence = 0.80
            self.max_collision_risk = 0.15
            self.allowed_mutations = [MutationType.COMBINATION, MutationType.DOMAIN_BRIDGE]

        elif strategy == MutationStrategy.UNICODE_SAFE:
            self.min_confidence = 0.90
            self.max_collision_risk = 0.05
            self.allowed_mutations = [MutationType.COMBINATION, MutationType.DERIVATION]

    def _initialize_unicode_pool(self):
        """Inizializza pool di caratteri Unicode sicuri per mutazioni."""
        # Unicode blocks sicuri per simboli
        safe_ranges = [
            (0x2190, 0x21FF),  # Arrows
            (0x2200, 0x22FF),  # Mathematical Operators
            (0x2300, 0x23FF),  # Miscellaneous Technical
            (0x2500, 0x257F),  # Box Drawing
            (0x25A0, 0x25FF),  # Geometric Shapes
            (0x2600, 0x26FF),  # Miscellaneous Symbols
            (0x2700, 0x27BF),  # Dingbats
            (0x2980, 0x29FF),  # Miscellaneous Mathematical Symbols-B
            (0x2A00, 0x2AFF),  # Supplemental Mathematical Operators
        ]

        for start, end in safe_ranges:
            for code_point in range(start, end + 1):
                try:
                    char = chr(code_point)
                    # Verifica che sia renderizzabile
                    if char.isprintable() and not char.isspace():
                        self.unicode_pool.add(char)
                except ValueError:
                    continue

        logger.info(f"🔤 Pool Unicode inizializzato: {len(self.unicode_pool)} caratteri sicuri")

    def _build_semantic_clusters(self):
        """Costruisce cluster semantici dai simboli esistenti."""
        for symbol_id, profile in self.symbol_loader.symbol_profiles.items():
            # Accesso corretto agli attributi SymbolProfile
            category = getattr(profile, 'category', 'unknown')
            symbol = getattr(profile, 'symbol', '')

            # Estrae categoria principale (prima parte dopo ng:)
            if category.startswith('ng:'):
                main_category = category.split(':')[1] if ':' in category else category
            else:
                main_category = category

            if main_category not in self.semantic_clusters:
                self.semantic_clusters[main_category] = []

            if symbol:
                self.semantic_clusters[main_category].append(symbol)

        logger.info(f"🎯 Cluster costruiti: {list(self.semantic_clusters.keys())}")

    def generate_mutation(self,
                         source_symbols: Optional[List[str]] = None,
                         target_category: Optional[str] = None,
                         mutation_type: Optional[MutationType] = None) -> MutationResult:
        """
        Genera una nuova mutazione simbolica.

        Args:
            source_symbols: Simboli sorgente (None per selezione automatica)
            target_category: Categoria target (None per automatica)
            mutation_type: Tipo di mutazione (None per automatica)

        Returns:
            MutationResult con il risultato della mutazione
        """
        start_time = datetime.now()

        try:
            # Selezione automatica se non specificato
            if source_symbols is None:
                source_symbols = self._select_source_symbols(target_category)

            if mutation_type is None:
                mutation_type = random.choice(self.allowed_mutations)

            if target_category is None:
                target_category = self._infer_target_category(source_symbols)

            # Genera candidato mutazione
            candidate = self._generate_mutation_candidate(
                source_symbols, target_category, mutation_type
            )

            if candidate is None:
                return MutationResult(
                    success=False,
                    candidate=None,
                    validation_passed=False,
                    registry_updated=False,
                    error_message="Impossibile generare candidato valido"
                )

            # Validazione candidato
            validation_passed = self._validate_mutation_candidate(candidate)

            # Registrazione se validato
            registry_updated = False
            if validation_passed:
                registry_updated = self._register_mutation(candidate)

            generation_time = (datetime.now() - start_time).total_seconds()

            result = MutationResult(
                success=validation_passed and registry_updated,
                candidate=candidate,
                validation_passed=validation_passed,
                registry_updated=registry_updated,
                generation_time=generation_time
            )

            # Salva nella storia
            self.mutation_history.append(result)

            if result.success:
                self.generated_symbols.add(candidate.mutated_symbol)
                logger.info(f"✅ Mutazione riuscita: {candidate.mutated_symbol} "
                          f"({mutation_type.value}) in {generation_time:.3f}s")
            else:
                logger.warning(f"❌ Mutazione fallita: {result.error_message}")

            return result

        except Exception as e:
            generation_time = (datetime.now() - start_time).total_seconds()
            logger.error(f"💥 Errore durante mutazione: {e}")

            return MutationResult(
                success=False,
                candidate=None,
                validation_passed=False,
                registry_updated=False,
                error_message=str(e),
                generation_time=generation_time
            )

    def _select_source_symbols(self, target_category: Optional[str] = None) -> List[str]:
        """Seleziona simboli sorgente per la mutazione."""
        if target_category and target_category in self.semantic_clusters:
            # Seleziona dal cluster specifico
            cluster_symbols = self.semantic_clusters[target_category]
            return random.sample(cluster_symbols, min(2, len(cluster_symbols)))
        else:
            # Selezione casuale da tutti i simboli
            all_symbols = [getattr(profile, 'symbol', '') for profile in self.symbol_loader.symbol_profiles.values()]
            all_symbols = [s for s in all_symbols if s]  # Filtra simboli vuoti
            return random.sample(all_symbols, min(2, len(all_symbols)))

    def _infer_target_category(self, source_symbols: List[str]) -> str:
        """Inferisce categoria target dai simboli sorgente."""
        # Trova categorie più comuni nei simboli sorgente
        categories = []
        for symbol in source_symbols:
            for profile in self.symbol_loader.symbol_profiles.values():
                existing_symbol = getattr(profile, 'symbol', '')
                if existing_symbol == symbol:
                    category = getattr(profile, 'category', 'unknown')
                    if category.startswith('ng:'):
                        main_category = category.split(':')[1] if ':' in category else category
                        categories.append(main_category)
                    break

        # Ritorna categoria più comune o 'mixed' se diverse
        if categories:
            most_common = max(set(categories), key=categories.count)
            return most_common
        else:
            return 'mixed'

    def _generate_mutation_candidate(self,
                                   source_symbols: List[str],
                                   target_category: str,
                                   mutation_type: MutationType) -> Optional[MutationCandidate]:
        """Genera candidato per mutazione simbolica."""
        try:
            if mutation_type == MutationType.COMBINATION:
                return self._generate_combination_mutation(source_symbols, target_category)
            elif mutation_type == MutationType.DERIVATION:
                return self._generate_derivation_mutation(source_symbols, target_category)
            elif mutation_type == MutationType.ABSTRACTION:
                return self._generate_abstraction_mutation(source_symbols, target_category)
            elif mutation_type == MutationType.SEMANTIC_SHIFT:
                return self._generate_semantic_shift_mutation(source_symbols, target_category)
            elif mutation_type == MutationType.UNICODE_MORPH:
                return self._generate_unicode_morph_mutation(source_symbols, target_category)
            elif mutation_type == MutationType.DOMAIN_BRIDGE:
                return self._generate_domain_bridge_mutation(source_symbols, target_category)
            else:
                logger.warning(f"⚠️ Tipo mutazione non supportato: {mutation_type}")
                return None

        except Exception as e:
            logger.error(f"💥 Errore generazione candidato: {e}")
            return None

    def _generate_combination_mutation(self, source_symbols: List[str], target_category: str) -> MutationCandidate:
        """Genera mutazione per combinazione di simboli."""
        # Seleziona carattere Unicode dal pool
        new_char = random.choice(list(self.unicode_pool))

        # Calcola confidenza basata su similarità semantica
        confidence = self._calculate_semantic_confidence(source_symbols, target_category)

        # Stima costo tokenizer (approssimativo)
        tokenizer_cost = 1  # Carattere singolo Unicode

        # Calcola rischio collisione
        collision_risk = self._calculate_collision_risk(new_char)

        return MutationCandidate(
            original_symbols=source_symbols,
            mutated_symbol=new_char,
            mutation_type=MutationType.COMBINATION,
            semantic_category=f"ng:{target_category}:combined",
            confidence_score=confidence,
            unicode_safe=True,
            tokenizer_cost=tokenizer_cost,
            collision_risk=collision_risk,
            metadata={
                'generation_method': 'unicode_pool_selection',
                'source_count': len(source_symbols),
                'unicode_block': self._get_unicode_block(new_char)
            }
        )

    def _generate_derivation_mutation(self, source_symbols: List[str], target_category: str) -> MutationCandidate:
        """Genera mutazione per derivazione da simbolo base."""
        base_symbol = source_symbols[0] if source_symbols else random.choice(list(self.unicode_pool))

        # Assicurati che sia un carattere singolo
        if len(base_symbol) > 1:
            # Se è un ID simbolo, prendi un simbolo dal pool
            base_symbol = random.choice(list(self.unicode_pool))

        # Trova simbolo simile nel pool Unicode
        base_code = ord(base_symbol)

        # Cerca varianti vicine
        for offset in [1, -1, 2, -2, 16, -16]:  # Offset comuni per varianti
            try:
                variant_char = chr(base_code + offset)
                if variant_char in self.unicode_pool and variant_char not in self.generated_symbols:
                    confidence = self._calculate_semantic_confidence([base_symbol], target_category) * 0.9
                    collision_risk = self._calculate_collision_risk(variant_char)

                    return MutationCandidate(
                        original_symbols=source_symbols,
                        mutated_symbol=variant_char,
                        mutation_type=MutationType.DERIVATION,
                        semantic_category=f"ng:{target_category}:derived",
                        confidence_score=confidence,
                        unicode_safe=True,
                        tokenizer_cost=1,
                        collision_risk=collision_risk,
                        metadata={
                            'base_symbol': base_symbol,
                            'unicode_offset': offset,
                            'derivation_method': 'unicode_offset'
                        }
                    )
            except ValueError:
                continue

        # Fallback: selezione casuale
        return self._generate_combination_mutation(source_symbols, target_category)

    def _generate_abstraction_mutation(self, source_symbols: List[str], target_category: str) -> MutationCandidate:
        """Genera mutazione per astrazione (meta-simbolo)."""
        # Seleziona simbolo dal range Mathematical Operators per astrazioni
        abstract_pool = [char for char in self.unicode_pool if 0x2200 <= ord(char) <= 0x22FF]

        if not abstract_pool:
            return self._generate_combination_mutation(source_symbols, target_category)

        new_symbol = random.choice(abstract_pool)
        confidence = self._calculate_semantic_confidence(source_symbols, target_category) * 0.8
        collision_risk = self._calculate_collision_risk(new_symbol)

        return MutationCandidate(
            original_symbols=source_symbols,
            mutated_symbol=new_symbol,
            mutation_type=MutationType.ABSTRACTION,
            semantic_category=f"ng:{target_category}:abstract",
            confidence_score=confidence,
            unicode_safe=True,
            tokenizer_cost=1,
            collision_risk=collision_risk,
            metadata={
                'abstraction_level': 'meta',
                'unicode_range': 'mathematical_operators',
                'conceptual_depth': len(source_symbols)
            }
        )

    def _generate_semantic_shift_mutation(self, source_symbols: List[str], target_category: str) -> MutationCandidate:
        """Genera mutazione per shift semantico."""
        # Seleziona categoria correlata per shift semantico
        related_categories = self._find_related_categories(target_category)
        new_category = random.choice(related_categories) if related_categories else target_category

        # Seleziona simbolo dal cluster della nuova categoria
        if new_category in self.semantic_clusters:
            cluster_symbols = self.semantic_clusters[new_category]
            available_symbols = [s for s in cluster_symbols if s not in self.generated_symbols]

            if available_symbols:
                new_symbol = random.choice(available_symbols)
                confidence = self._calculate_semantic_confidence(source_symbols, new_category) * 0.7
                collision_risk = self._calculate_collision_risk(new_symbol)

                return MutationCandidate(
                    original_symbols=source_symbols,
                    mutated_symbol=new_symbol,
                    mutation_type=MutationType.SEMANTIC_SHIFT,
                    semantic_category=f"ng:{new_category}:shifted",
                    confidence_score=confidence,
                    unicode_safe=True,
                    tokenizer_cost=1,
                    collision_risk=collision_risk,
                    metadata={
                        'original_category': target_category,
                        'shifted_category': new_category,
                        'semantic_distance': self._calculate_semantic_distance(target_category, new_category)
                    }
                )

        # Fallback
        return self._generate_combination_mutation(source_symbols, target_category)

    def _generate_unicode_morph_mutation(self, source_symbols: List[str], target_category: str) -> MutationCandidate:
        """Genera mutazione per morfing Unicode."""
        if not source_symbols:
            return self._generate_combination_mutation(source_symbols, target_category)

        base_symbol = source_symbols[0]
        base_code = ord(base_symbol)

        # Applica trasformazioni Unicode comuni
        transformations = [
            ('bold', 0x1D400 - 0x41),      # Mathematical Bold
            ('italic', 0x1D434 - 0x41),    # Mathematical Italic
            ('script', 0x1D49C - 0x41),    # Mathematical Script
            ('fraktur', 0x1D504 - 0x41),   # Mathematical Fraktur
        ]

        for transform_name, offset in transformations:
            try:
                if 0x41 <= base_code <= 0x5A:  # A-Z
                    morphed_code = base_code + offset
                    morphed_char = chr(morphed_code)

                    if morphed_char not in self.generated_symbols:
                        confidence = self._calculate_semantic_confidence(source_symbols, target_category) * 0.6
                        collision_risk = self._calculate_collision_risk(morphed_char)

                        return MutationCandidate(
                            original_symbols=source_symbols,
                            mutated_symbol=morphed_char,
                            mutation_type=MutationType.UNICODE_MORPH,
                            semantic_category=f"ng:{target_category}:morphed",
                            confidence_score=confidence,
                            unicode_safe=True,
                            tokenizer_cost=1,
                            collision_risk=collision_risk,
                            metadata={
                                'base_symbol': base_symbol,
                                'transformation': transform_name,
                                'unicode_offset': offset
                            }
                        )
            except ValueError:
                continue

        # Fallback
        return self._generate_combination_mutation(source_symbols, target_category)

    def _generate_domain_bridge_mutation(self, source_symbols: List[str], target_category: str) -> MutationCandidate:
        """Genera mutazione per bridge tra domini."""
        # Trova domini diversi da quello target
        other_domains = [cat for cat in self.semantic_clusters.keys() if cat != target_category]

        if not other_domains:
            return self._generate_combination_mutation(source_symbols, target_category)

        bridge_domain = random.choice(other_domains)
        bridge_symbols = self.semantic_clusters[bridge_domain]

        # Seleziona simbolo bridge
        available_bridge = [s for s in bridge_symbols if s not in self.generated_symbols]
        if not available_bridge:
            return self._generate_combination_mutation(source_symbols, target_category)

        bridge_symbol = random.choice(available_bridge)
        confidence = self._calculate_semantic_confidence(source_symbols + [bridge_symbol], target_category) * 0.75
        collision_risk = self._calculate_collision_risk(bridge_symbol)

        return MutationCandidate(
            original_symbols=source_symbols,
            mutated_symbol=bridge_symbol,
            mutation_type=MutationType.DOMAIN_BRIDGE,
            semantic_category=f"ng:{target_category}:bridge:{bridge_domain}",
            confidence_score=confidence,
            unicode_safe=True,
            tokenizer_cost=1,
            collision_risk=collision_risk,
            metadata={
                'bridge_domain': bridge_domain,
                'target_domain': target_category,
                'bridge_strength': self._calculate_bridge_strength(target_category, bridge_domain)
            }
        )

    # Metodi di utilità e validazione
    def _calculate_semantic_confidence(self, symbols: List[str], category: str) -> float:
        """Calcola confidenza semantica per i simboli."""
        if not symbols:
            return 0.5

        # Calcola basandosi su presenza nel cluster target
        if category in self.semantic_clusters:
            cluster_symbols = set(self.semantic_clusters[category])
            matching_symbols = sum(1 for s in symbols if s in cluster_symbols)
            base_confidence = matching_symbols / len(symbols)
        else:
            base_confidence = 0.5

        # Aggiusta per strategia
        if self.strategy == MutationStrategy.CONSERVATIVE:
            return min(base_confidence * 1.1, 1.0)
        elif self.strategy == MutationStrategy.CREATIVE:
            return base_confidence * 0.9
        else:
            return base_confidence

    def _calculate_collision_risk(self, symbol: str) -> float:
        """Calcola rischio di collisione per un simbolo."""
        # Verifica se già esiste nel registry
        for profile in self.symbol_loader.symbol_profiles.values():
            existing_symbol = getattr(profile, 'symbol', '')
            if existing_symbol == symbol:
                return 1.0  # Collisione certa

        # Verifica se già generato
        if symbol in self.generated_symbols:
            return 0.9  # Quasi certa

        # Calcola basandosi su similarità Unicode
        similar_count = 0
        symbol_code = ord(symbol)

        for profile in self.symbol_loader.symbol_profiles.values():
            existing_symbol = getattr(profile, 'symbol', '')
            if existing_symbol:
                existing_code = ord(existing_symbol)
                if abs(symbol_code - existing_code) <= 5:  # Molto simili
                    similar_count += 1

        # Normalizza rischio
        total_symbols = len(self.symbol_loader.symbol_profiles)
        similarity_risk = similar_count / max(total_symbols, 1) if total_symbols > 0 else 0

        return min(similarity_risk * 2, 0.8)  # Max 80% rischio

    def _get_unicode_block(self, char: str) -> str:
        """Identifica il blocco Unicode di un carattere."""
        code = ord(char)

        if 0x2190 <= code <= 0x21FF:
            return "Arrows"
        elif 0x2200 <= code <= 0x22FF:
            return "Mathematical Operators"
        elif 0x2300 <= code <= 0x23FF:
            return "Miscellaneous Technical"
        elif 0x2500 <= code <= 0x257F:
            return "Box Drawing"
        elif 0x25A0 <= code <= 0x25FF:
            return "Geometric Shapes"
        elif 0x2600 <= code <= 0x26FF:
            return "Miscellaneous Symbols"
        elif 0x2700 <= code <= 0x27BF:
            return "Dingbats"
        elif 0x2980 <= code <= 0x29FF:
            return "Miscellaneous Mathematical Symbols-B"
        elif 0x2A00 <= code <= 0x2AFF:
            return "Supplemental Mathematical Operators"
        else:
            return "Other"

    def _find_related_categories(self, category: str) -> List[str]:
        """Trova categorie semanticamente correlate."""
        # Mapping di categorie correlate
        category_relations = {
            'operators': ['control', 'logic', 'math'],
            'control': ['operators', 'flow', 'logic'],
            'data': ['structures', 'types', 'memory'],
            'structures': ['data', 'algorithms', 'patterns'],
            'logic': ['operators', 'reasoning', 'control'],
            'reasoning': ['logic', 'cognitive', 'meta'],
            'cognitive': ['reasoning', 'meta', 'consciousness'],
            'meta': ['cognitive', 'reflection', 'abstraction'],
            'memory': ['data', 'storage', 'persistence'],
            'flow': ['control', 'execution', 'sequence']
        }

        return category_relations.get(category, [category])

    def _calculate_semantic_distance(self, cat1: str, cat2: str) -> float:
        """Calcola distanza semantica tra categorie."""
        if cat1 == cat2:
            return 0.0

        # Distanze predefinite (semplificato)
        distance_map = {
            ('operators', 'control'): 0.2,
            ('operators', 'logic'): 0.3,
            ('control', 'flow'): 0.1,
            ('data', 'structures'): 0.2,
            ('logic', 'reasoning'): 0.2,
            ('reasoning', 'cognitive'): 0.3,
            ('cognitive', 'meta'): 0.4,
        }

        # Cerca distanza diretta o inversa
        key = (cat1, cat2)
        reverse_key = (cat2, cat1)

        if key in distance_map:
            return distance_map[key]
        elif reverse_key in distance_map:
            return distance_map[reverse_key]
        else:
            return 0.8  # Distanza alta per categorie non correlate

    def _calculate_bridge_strength(self, domain1: str, domain2: str) -> float:
        """Calcola forza del bridge tra domini."""
        distance = self._calculate_semantic_distance(domain1, domain2)
        return 1.0 - distance  # Inverso della distanza

    def _validate_mutation_candidate(self, candidate: MutationCandidate) -> bool:
        """Valida un candidato per mutazione."""
        # Controlli base
        if not candidate.mutated_symbol:
            return False

        if candidate.confidence_score < self.min_confidence:
            logger.debug(f"❌ Confidenza troppo bassa: {candidate.confidence_score:.2f} < {self.min_confidence}")
            return False

        if candidate.collision_risk > self.max_collision_risk:
            logger.debug(f"❌ Rischio collisione troppo alto: {candidate.collision_risk:.2f} > {self.max_collision_risk}")
            return False

        # Controllo Unicode safety
        if not candidate.unicode_safe:
            logger.debug(f"❌ Simbolo non Unicode-safe: {candidate.mutated_symbol}")
            return False

        # Controllo costo tokenizer
        if candidate.tokenizer_cost > 2:  # Max 2 token
            logger.debug(f"❌ Costo tokenizer troppo alto: {candidate.tokenizer_cost}")
            return False

        # Controllo categoria valida
        if not candidate.semantic_category.startswith('ng:'):
            logger.debug(f"❌ Categoria non valida: {candidate.semantic_category}")
            return False

        logger.debug(f"✅ Candidato validato: {candidate.mutated_symbol}")
        return True

    def _register_mutation(self, candidate: MutationCandidate) -> bool:
        """Registra una mutazione validata nel meta-registry."""
        try:
            # Crea entry per meta-registry
            meta_entry = {
                'symbol': candidate.mutated_symbol,
                'id': f"ng_mut_{hashlib.md5(candidate.mutated_symbol.encode()).hexdigest()[:8]}",
                'category': candidate.semantic_category,
                'description': f"Generated by mutation ({candidate.mutation_type.value})",
                'fallback': f"MUT_{candidate.mutation_type.value.upper()}",
                'score': candidate.confidence_score * 100,
                'valid': True,
                'mutation_metadata': {
                    'original_symbols': candidate.original_symbols,
                    'mutation_type': candidate.mutation_type.value,
                    'generation_timestamp': datetime.now().isoformat(),
                    'confidence': candidate.confidence_score,
                    'collision_risk': candidate.collision_risk,
                    'tokenizer_cost': candidate.tokenizer_cost,
                    'strategy': self.strategy.value,
                    'metadata': candidate.metadata
                }
            }

            # Salva in meta-registry (per ora solo in memoria)
            # TODO: Implementare salvataggio persistente
            self.generated_symbols.add(candidate.mutated_symbol)

            logger.info(f"📝 Mutazione registrata: {candidate.mutated_symbol} -> {candidate.semantic_category}")
            return True

        except Exception as e:
            logger.error(f"💥 Errore registrazione mutazione: {e}")
            return False

    def get_mutation_stats(self) -> Dict[str, Any]:
        """Ritorna statistiche delle mutazioni."""
        successful_mutations = [r for r in self.mutation_history if r.success]

        return {
            'total_attempts': len(self.mutation_history),
            'successful_mutations': len(successful_mutations),
            'success_rate': len(successful_mutations) / max(len(self.mutation_history), 1),
            'generated_symbols_count': len(self.generated_symbols),
            'average_confidence': sum(r.candidate.confidence_score for r in successful_mutations if r.candidate) / max(len(successful_mutations), 1),
            'average_generation_time': sum(r.generation_time for r in self.mutation_history) / max(len(self.mutation_history), 1),
            'mutation_types_used': list(set(r.candidate.mutation_type.value for r in successful_mutations if r.candidate)),
            'semantic_categories': list(set(r.candidate.semantic_category for r in successful_mutations if r.candidate))
        }

    def generate_batch_mutations(self, count: int = 10, target_category: Optional[str] = None) -> List[MutationResult]:
        """Genera un batch di mutazioni."""
        results = []

        for i in range(count):
            logger.info(f"🧬 Generazione mutazione {i+1}/{count}")
            result = self.generate_mutation(target_category=target_category)
            results.append(result)

            # Pausa breve tra mutazioni
            if i < count - 1:
                import time
                time.sleep(0.1)

        successful = [r for r in results if r.success]
        logger.info(f"📊 Batch completato: {len(successful)}/{count} mutazioni riuscite")

        return results


# Funzioni di utilità per uso esterno
def create_symbol_mutator(
    registry_path: str = "neuroglyph/core/locked_registry_godmode_v9.json",
    strategy: MutationStrategy = MutationStrategy.CONSERVATIVE
) -> SymbolMutator:
    """Crea Symbol Mutator con configurazione ottimale."""
    return SymbolMutator(registry_path, strategy)


if __name__ == "__main__":
    # Test Symbol Mutator
    print("🧬 NEUROGLYPH Symbol Mutator - Test")
    print("=" * 60)

    # Crea mutatore
    mutator = create_symbol_mutator(strategy=MutationStrategy.CREATIVE)

    # Test mutazione
    print("\n🧪 Test mutazione simbolica...")
    result = mutator.generate_mutation()

    if result.success:
        print(f"✅ Mutazione riuscita!")
        print(f"🔣 Nuovo simbolo: {result.candidate.mutated_symbol}")
        print(f"📊 Confidenza: {result.candidate.confidence_score:.2f}")
        print(f"⏱️ Tempo: {result.generation_time:.3f}s")
    else:
        print(f"❌ Mutazione fallita: {result.error_message}")

    print(f"\n📈 Statistiche mutatore:")
    print(f"🎯 Simboli generati: {len(mutator.generated_symbols)}")
    print(f"📚 Cluster semantici: {len(mutator.semantic_clusters)}")
    print(f"🔤 Pool Unicode: {len(mutator.unicode_pool)}")
