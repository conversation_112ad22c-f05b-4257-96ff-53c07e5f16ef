"""
NEUROGLIPH Runtime Wrapper
=========================

Wrapper ad alte prestazioni per l'esecuzione di codice decodificato da neuroglifi.
Fornisce un'interfaccia unificata per l'esecuzione sicura e ottimizzata di codice
generato dal sistema NEUROGLIPH.
"""

import sys
import ast
import types
import inspect
import traceback
import subprocess
import tempfile
import os
import time
import resource
import signal
from typing import Any, Dict, List, Optional, Callable, Union, Tuple
from dataclasses import dataclass
from pathlib import Path
from contextlib import contextmanager
import logging
import json

# Configurazione logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


@dataclass
class ExecutionContext:
    """Contesto di esecuzione per il codice decodificato."""
    language: str
    timeout: float = 30.0
    memory_limit: int = 512 * 1024 * 1024  # 512MB
    allow_imports: bool = True
    allowed_modules: Optional[List[str]] = None
    sandbox_mode: bool = True
    capture_output: bool = True
    working_directory: Optional[str] = None


@dataclass
class ExecutionResult:
    """Risultato dell'esecuzione del codice."""
    success: bool
    output: str
    error: Optional[str] = None
    execution_time: float = 0.0
    memory_used: int = 0
    return_value: Any = None
    stdout: str = ""
    stderr: str = ""
    exit_code: int = 0


class SecurityError(Exception):
    """Eccezione per violazioni di sicurezza durante l'esecuzione."""
    pass


class TimeoutError(Exception):
    """Eccezione per timeout durante l'esecuzione."""
    pass


class MemoryError(Exception):
    """Eccezione per superamento limite memoria."""
    pass


class NeuroGlyphRuntime:
    """
    Runtime principale per l'esecuzione di codice decodificato da neuroglifi.
    
    Caratteristiche:
    - Esecuzione sicura in sandbox
    - Gestione timeout e limiti memoria
    - Supporto multi-linguaggio
    - Cattura output e errori
    - Monitoraggio performance
    """
    
    def __init__(self, default_context: Optional[ExecutionContext] = None):
        """Inizializza il runtime con contesto di default."""
        self.default_context = default_context or ExecutionContext(language="python")
        self.execution_stats = {
            "total_executions": 0,
            "successful_executions": 0,
            "failed_executions": 0,
            "avg_execution_time": 0.0,
            "total_execution_time": 0.0
        }
        self.security_violations = []
        
        # Moduli pericolosi da bloccare in sandbox
        self.dangerous_modules = {
            'os', 'sys', 'subprocess', 'shutil', 'glob', 'tempfile',
            'socket', 'urllib', 'http', 'ftplib', 'smtplib',
            'pickle', 'marshal', 'shelve', 'dbm',
            '__builtin__', '__builtins__', 'builtins'
        }
        
        # Funzioni pericolose da bloccare
        self.dangerous_functions = {
            'eval', 'exec', 'compile', 'open', 'file', 'input', 'raw_input',
            '__import__', 'reload', 'vars', 'locals', 'globals', 'dir',
            'hasattr', 'getattr', 'setattr', 'delattr'
        }
    
    def execute(self, code: str, context: Optional[ExecutionContext] = None) -> ExecutionResult:
        """
        Esegue codice decodificato da neuroglifi.
        
        Args:
            code: Codice sorgente da eseguire
            context: Contesto di esecuzione (opzionale)
            
        Returns:
            Risultato dell'esecuzione
        """
        ctx = context or self.default_context
        start_time = time.time()
        
        logger.info(f"Inizio esecuzione codice {ctx.language}")
        
        try:
            # Validazione sicurezza
            if ctx.sandbox_mode:
                self._validate_security(code, ctx)
            
            # Esecuzione basata sul linguaggio
            if ctx.language.lower() == "python":
                result = self._execute_python(code, ctx)
            elif ctx.language.lower() in ["javascript", "js"]:
                result = self._execute_javascript(code, ctx)
            elif ctx.language.lower() == "rust":
                result = self._execute_rust(code, ctx)
            else:
                result = self._execute_generic(code, ctx)
            
            # Aggiornamento statistiche
            execution_time = time.time() - start_time
            result.execution_time = execution_time
            
            self._update_stats(result, execution_time)
            
            return result
            
        except Exception as e:
            execution_time = time.time() - start_time
            error_result = ExecutionResult(
                success=False,
                output="",
                error=str(e),
                execution_time=execution_time
            )
            self._update_stats(error_result, execution_time)
            return error_result
    
    def _validate_security(self, code: str, context: ExecutionContext) -> None:
        """Valida il codice per potenziali rischi di sicurezza."""
        if context.language.lower() == "python":
            self._validate_python_security(code, context)
        else:
            self._validate_generic_security(code, context)
    
    def _validate_python_security(self, code: str, context: ExecutionContext) -> None:
        """Validazione sicurezza specifica per Python."""
        try:
            tree = ast.parse(code)
            
            for node in ast.walk(tree):
                # Controllo import pericolosi
                if isinstance(node, (ast.Import, ast.ImportFrom)):
                    module_names = []
                    if isinstance(node, ast.Import):
                        module_names = [alias.name for alias in node.names]
                    else:
                        module_names = [node.module] if node.module else []
                    
                    for module_name in module_names:
                        if module_name in self.dangerous_modules:
                            if not context.allow_imports or \
                               (context.allowed_modules and module_name not in context.allowed_modules):
                                raise SecurityError(f"Import pericoloso bloccato: {module_name}")
                
                # Controllo chiamate di funzioni pericolose
                if isinstance(node, ast.Call):
                    if isinstance(node.func, ast.Name):
                        if node.func.id in self.dangerous_functions:
                            raise SecurityError(f"Funzione pericolosa bloccata: {node.func.id}")
                
                # Controllo accesso attributi pericolosi
                if isinstance(node, ast.Attribute):
                    if node.attr.startswith('_'):
                        raise SecurityError(f"Accesso a attributo privato bloccato: {node.attr}")
                        
        except SyntaxError as e:
            raise SecurityError(f"Errore di sintassi nel codice: {e}")
    
    def _validate_generic_security(self, code: str, context: ExecutionContext) -> None:
        """Validazione sicurezza generica per altri linguaggi."""
        # Controlli base per pattern pericolosi
        dangerous_patterns = [
            'system(', 'exec(', 'eval(', 'shell_exec(',
            'file_get_contents(', 'fopen(', 'include(',
            'require(', 'import os', 'import sys'
        ]
        
        for pattern in dangerous_patterns:
            if pattern in code.lower():
                raise SecurityError(f"Pattern pericoloso rilevato: {pattern}")
    
    def _execute_python(self, code: str, context: ExecutionContext) -> ExecutionResult:
        """Esecuzione specifica per Python."""
        # Preparazione ambiente sandbox
        sandbox_globals = self._create_python_sandbox(context)
        
        # Cattura output
        from io import StringIO
        old_stdout = sys.stdout
        old_stderr = sys.stderr
        
        captured_stdout = StringIO()
        captured_stderr = StringIO()
        
        try:
            if context.capture_output:
                sys.stdout = captured_stdout
                sys.stderr = captured_stderr
            
            # Esecuzione con timeout
            with self._timeout_context(context.timeout):
                with self._memory_limit_context(context.memory_limit):
                    # Compilazione
                    compiled_code = compile(code, '<neurogliph>', 'exec')
                    
                    # Esecuzione
                    exec(compiled_code, sandbox_globals)
                    
                    # Estrazione risultato se presente
                    return_value = sandbox_globals.get('__result__', None)
            
            return ExecutionResult(
                success=True,
                output=captured_stdout.getvalue(),
                stdout=captured_stdout.getvalue(),
                stderr=captured_stderr.getvalue(),
                return_value=return_value
            )
            
        except Exception as e:
            return ExecutionResult(
                success=False,
                output=captured_stdout.getvalue(),
                error=str(e),
                stdout=captured_stdout.getvalue(),
                stderr=captured_stderr.getvalue() + str(e)
            )
        finally:
            sys.stdout = old_stdout
            sys.stderr = old_stderr
    
    def _create_python_sandbox(self, context: ExecutionContext) -> Dict[str, Any]:
        """Crea un ambiente sandbox sicuro per Python."""
        # Builtins sicuri
        safe_builtins = {
            'abs', 'all', 'any', 'bin', 'bool', 'chr', 'dict', 'divmod',
            'enumerate', 'filter', 'float', 'format', 'frozenset', 'hex',
            'int', 'isinstance', 'issubclass', 'iter', 'len', 'list',
            'map', 'max', 'min', 'oct', 'ord', 'pow', 'print', 'range',
            'repr', 'reversed', 'round', 'set', 'slice', 'sorted', 'str',
            'sum', 'tuple', 'type', 'zip'
        }
        
        sandbox_builtins = {}
        for name in safe_builtins:
            if hasattr(__builtins__, name):
                sandbox_builtins[name] = getattr(__builtins__, name)
        
        # Moduli sicuri consentiti
        safe_modules = {}
        if context.allow_imports and context.allowed_modules:
            for module_name in context.allowed_modules:
                try:
                    safe_modules[module_name] = __import__(module_name)
                except ImportError:
                    logger.warning(f"Modulo non trovato: {module_name}")
        
        return {
            '__builtins__': sandbox_builtins,
            **safe_modules
        }
    
    def _execute_javascript(self, code: str, context: ExecutionContext) -> ExecutionResult:
        """Esecuzione JavaScript tramite Node.js."""
        try:
            with tempfile.NamedTemporaryFile(mode='w', suffix='.js', delete=False) as f:
                f.write(code)
                temp_file = f.name
            
            # Esecuzione con subprocess
            result = subprocess.run(
                ['node', temp_file],
                capture_output=True,
                text=True,
                timeout=context.timeout,
                cwd=context.working_directory
            )
            
            return ExecutionResult(
                success=result.returncode == 0,
                output=result.stdout,
                error=result.stderr if result.returncode != 0 else None,
                stdout=result.stdout,
                stderr=result.stderr,
                exit_code=result.returncode
            )
            
        except subprocess.TimeoutExpired:
            return ExecutionResult(
                success=False,
                output="",
                error="Timeout durante l'esecuzione JavaScript"
            )
        except Exception as e:
            return ExecutionResult(
                success=False,
                output="",
                error=f"Errore esecuzione JavaScript: {str(e)}"
            )
        finally:
            if 'temp_file' in locals():
                os.unlink(temp_file)
    
    def _execute_rust(self, code: str, context: ExecutionContext) -> ExecutionResult:
        """Esecuzione Rust tramite compilazione temporanea."""
        try:
            with tempfile.TemporaryDirectory() as temp_dir:
                # Creazione progetto Rust temporaneo
                src_file = os.path.join(temp_dir, "main.rs")
                with open(src_file, 'w') as f:
                    f.write(code)
                
                # Compilazione
                compile_result = subprocess.run(
                    ['rustc', src_file, '-o', os.path.join(temp_dir, 'program')],
                    capture_output=True,
                    text=True,
                    timeout=context.timeout / 2  # Metà tempo per compilazione
                )
                
                if compile_result.returncode != 0:
                    return ExecutionResult(
                        success=False,
                        output="",
                        error=f"Errore compilazione Rust: {compile_result.stderr}"
                    )
                
                # Esecuzione
                exec_result = subprocess.run(
                    [os.path.join(temp_dir, 'program')],
                    capture_output=True,
                    text=True,
                    timeout=context.timeout / 2,
                    cwd=context.working_directory
                )
                
                return ExecutionResult(
                    success=exec_result.returncode == 0,
                    output=exec_result.stdout,
                    error=exec_result.stderr if exec_result.returncode != 0 else None,
                    stdout=exec_result.stdout,
                    stderr=exec_result.stderr,
                    exit_code=exec_result.returncode
                )
                
        except subprocess.TimeoutExpired:
            return ExecutionResult(
                success=False,
                output="",
                error="Timeout durante compilazione/esecuzione Rust"
            )
        except Exception as e:
            return ExecutionResult(
                success=False,
                output="",
                error=f"Errore esecuzione Rust: {str(e)}"
            )
    
    def _execute_generic(self, code: str, context: ExecutionContext) -> ExecutionResult:
        """Esecuzione generica per linguaggi non supportati nativamente."""
        return ExecutionResult(
            success=False,
            output="",
            error=f"Linguaggio {context.language} non supportato"
        )
    
    @contextmanager
    def _timeout_context(self, timeout: float):
        """Context manager per gestione timeout."""
        def timeout_handler(signum, frame):
            raise TimeoutError(f"Esecuzione interrotta dopo {timeout} secondi")
        
        old_handler = signal.signal(signal.SIGALRM, timeout_handler)
        signal.alarm(int(timeout))
        
        try:
            yield
        finally:
            signal.alarm(0)
            signal.signal(signal.SIGALRM, old_handler)
    
    @contextmanager
    def _memory_limit_context(self, memory_limit: int):
        """Context manager per gestione limite memoria."""
        try:
            # Imposta limite memoria (solo su Unix)
            if hasattr(resource, 'RLIMIT_AS'):
                old_limit = resource.getrlimit(resource.RLIMIT_AS)
                resource.setrlimit(resource.RLIMIT_AS, (memory_limit, memory_limit))
            
            yield
            
        except MemoryError:
            raise MemoryError(f"Superato limite memoria di {memory_limit} bytes")
        finally:
            # Ripristina limite precedente
            if hasattr(resource, 'RLIMIT_AS') and 'old_limit' in locals():
                resource.setrlimit(resource.RLIMIT_AS, old_limit)
    
    def _update_stats(self, result: ExecutionResult, execution_time: float) -> None:
        """Aggiorna statistiche di esecuzione."""
        self.execution_stats["total_executions"] += 1
        self.execution_stats["total_execution_time"] += execution_time
        
        if result.success:
            self.execution_stats["successful_executions"] += 1
        else:
            self.execution_stats["failed_executions"] += 1
        
        # Calcola media tempo esecuzione
        total_exec = self.execution_stats["total_executions"]
        total_time = self.execution_stats["total_execution_time"]
        self.execution_stats["avg_execution_time"] = total_time / total_exec
    
    def get_stats(self) -> Dict[str, Any]:
        """Ritorna statistiche di utilizzo del runtime."""
        return self.execution_stats.copy()
    
    def clear_stats(self) -> None:
        """Resetta le statistiche."""
        self.execution_stats = {
            "total_executions": 0,
            "successful_executions": 0,
            "failed_executions": 0,
            "avg_execution_time": 0.0,
            "total_execution_time": 0.0
        }


# Funzioni di utilità
def execute_neuroglyph_code(code: str, language: str = "python", 
                           timeout: float = 30.0, sandbox: bool = True) -> ExecutionResult:
    """Funzione di convenienza per esecuzione rapida."""
    runtime = NeuroGlyphRuntime()
    context = ExecutionContext(
        language=language,
        timeout=timeout,
        sandbox_mode=sandbox
    )
    return runtime.execute(code, context)


def batch_execute(code_list: List[str], language: str = "python") -> List[ExecutionResult]:
    """Esecuzione batch di più frammenti di codice."""
    runtime = NeuroGlyphRuntime()
    context = ExecutionContext(language=language)
    
    results = []
    for code in code_list:
        result = runtime.execute(code, context)
        results.append(result)
    
    return results


if __name__ == "__main__":
    # Test del runtime
    runtime = NeuroGlyphRuntime()
    
    # Test Python
    python_code = """
def fibonacci(n):
    if n <= 1:
        return n
    return fibonacci(n-1) + fibonacci(n-2)

result = fibonacci(10)
print(f"Fibonacci(10) = {result}")
__result__ = result
"""
    
    context = ExecutionContext(language="python", timeout=10.0)
    result = runtime.execute(python_code, context)
    
    print(f"Esecuzione Python:")
    print(f"Successo: {result.success}")
    print(f"Output: {result.output}")
    print(f"Tempo: {result.execution_time:.3f}s")
    print(f"Valore ritornato: {result.return_value}")
    
    # Statistiche
    print(f"\nStatistiche runtime:")
    stats = runtime.get_stats()
    for key, value in stats.items():
        print(f"{key}: {value}")
