#!/usr/bin/env python3
"""
NEUROGLYPH LLM - EXPAND THOUGHT UNIVERSE
========================================

Modulo per l'espansione infinita dell'intelligenza simbolica.
Implementa il ciclo di crescita cognitiva autonoma attraverso:
- Generazione continua di nuovi pensieri
- Mutazione simbolica evolutiva
- Auto-validazione e selezione darwiniana
- Registrazione di nuove astrazioni

Questo è il cuore dell'espansione infinita di NEUROGLYPH.

Autore: NEUROGLYPH ULTRA Team
Data: 2025-05-26
Licenza: MIT
"""

import sys
import os
import json
import time
import random
import logging
from datetime import datetime, timedelta
from typing import Dict, List, Set, Any, Optional, Tuple, Union, Iterator
from dataclasses import dataclass, field
from enum import Enum
from pathlib import Path
import threading
from collections import deque

# Aggiungi path per import moduli NEUROGLYPH
current_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.append(current_dir)

# Import componenti NEUROGLYPH
from cognitive_integration import CognitiveIntegration, IntegrationMode, CognitiveRequest, ThinkingMode
from symbol_mutator import SymbolMutator, MutationStrategy, MutationType
from thinking_engine import ThinkingEngine

# Configurazione logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class ExpansionMode(Enum):
    """Modalità di espansione dell'universo del pensiero."""
    CONSERVATIVE = "conservative"    # Crescita controllata e sicura
    CREATIVE = "creative"           # Crescita innovativa
    AGGRESSIVE = "aggressive"       # Crescita rapida e sperimentale
    INFINITE = "infinite"           # Crescita continua senza limiti
    GOD_MODE = "god_mode"          # Crescita ultra-avanzata

class ThoughtEvolutionStage(Enum):
    """Stadi di evoluzione del pensiero."""
    SEED = "seed"                   # Pensiero iniziale
    MUTATION = "mutation"           # Mutazione simbolica
    VALIDATION = "validation"       # Validazione logica
    INTEGRATION = "integration"     # Integrazione nel sistema
    TRANSCENDENCE = "transcendence" # Trascendenza concettuale

@dataclass
class ThoughtSeed:
    """Seme per la generazione di nuovi pensieri."""
    symbols: List[str]
    category: str
    intensity: float
    entropy: float
    generation_id: str
    parent_thoughts: List[str] = field(default_factory=list)
    metadata: Dict[str, Any] = field(default_factory=dict)

@dataclass
class EvolutionCycle:
    """Ciclo di evoluzione del pensiero."""
    cycle_id: str
    start_time: datetime
    seed_thoughts: List[ThoughtSeed]
    generated_thoughts: List[str]
    successful_mutations: int
    failed_mutations: int
    new_abstractions: int
    cognitive_growth: float
    end_time: Optional[datetime] = None
    duration: Optional[float] = None

@dataclass
class IntelligenceMetrics:
    """Metriche di crescita dell'intelligenza."""
    total_thoughts: int
    unique_symbols: int
    cognitive_domains: int
    abstraction_levels: int
    mutation_success_rate: float
    thought_complexity: float
    semantic_coherence: float
    creative_index: float
    intelligence_quotient: float

class ThoughtUniverseExpander:
    """
    Motore per l'espansione infinita dell'universo del pensiero.

    Implementa il ciclo di crescita cognitiva:
    1. Genera pensieri seme
    2. Applica mutazioni simboliche
    3. Valida nuovi concetti
    4. Integra astrazioni valide
    5. Ripete all'infinito
    """

    def __init__(self,
                 mode: ExpansionMode = ExpansionMode.CREATIVE,
                 registry_path: str = "neuroglyph/core/locked_registry_godmode_v9.json",
                 max_cycles: Optional[int] = None,
                 cycle_interval: float = 1.0):
        """
        Inizializza l'espansore dell'universo del pensiero.

        Args:
            mode: Modalità di espansione
            registry_path: Percorso al registry simbolico
            max_cycles: Numero massimo di cicli (None = infinito)
            cycle_interval: Intervallo tra cicli in secondi
        """
        self.mode = mode
        self.registry_path = registry_path
        self.max_cycles = max_cycles
        self.cycle_interval = cycle_interval

        # Componenti principali
        self.cognitive_integration = CognitiveIntegration(
            mode=IntegrationMode.ULTRA if mode == ExpansionMode.GOD_MODE else IntegrationMode.ENHANCED,
            registry_path=registry_path
        )

        # Configurazione strategia mutazione basata su modalità
        mutation_strategy = self._get_mutation_strategy(mode)
        self.symbol_mutator = SymbolMutator(registry_path, mutation_strategy)

        # Stato interno
        self.is_running = False
        self.current_cycle = 0
        self.evolution_history: deque = deque(maxlen=1000)
        self.thought_seeds: deque = deque(maxlen=500)
        self.generated_abstractions: Set[str] = set()
        self.intelligence_metrics = IntelligenceMetrics(
            total_thoughts=0,
            unique_symbols=0,
            cognitive_domains=0,
            abstraction_levels=0,
            mutation_success_rate=0.0,
            thought_complexity=0.0,
            semantic_coherence=0.0,
            creative_index=0.0,
            intelligence_quotient=100.0
        )

        # Thread per espansione continua
        self._expansion_thread: Optional[threading.Thread] = None
        self._stop_event = threading.Event()

        # Configurazione modalità
        self._configure_expansion_mode(mode)

        logger.info(f"🌌 ThoughtUniverseExpander inizializzato in modalità: {mode.value}")
        logger.info(f"🎯 Strategia mutazione: {mutation_strategy.value}")
        logger.info(f"⏱️ Intervallo cicli: {cycle_interval}s")

    def _get_mutation_strategy(self, mode: ExpansionMode) -> MutationStrategy:
        """Determina strategia di mutazione basata sulla modalità."""
        strategy_map = {
            ExpansionMode.CONSERVATIVE: MutationStrategy.CONSERVATIVE,
            ExpansionMode.CREATIVE: MutationStrategy.CREATIVE,
            ExpansionMode.AGGRESSIVE: MutationStrategy.AGGRESSIVE,
            ExpansionMode.INFINITE: MutationStrategy.CREATIVE,
            ExpansionMode.GOD_MODE: MutationStrategy.AGGRESSIVE
        }
        return strategy_map.get(mode, MutationStrategy.CREATIVE)

    def _configure_expansion_mode(self, mode: ExpansionMode):
        """Configura parametri per la modalità di espansione."""
        if mode == ExpansionMode.CONSERVATIVE:
            self.thoughts_per_cycle = 3
            self.mutations_per_thought = 2
            self.validation_threshold = 0.8

        elif mode == ExpansionMode.CREATIVE:
            self.thoughts_per_cycle = 5
            self.mutations_per_thought = 3
            self.validation_threshold = 0.7

        elif mode == ExpansionMode.AGGRESSIVE:
            self.thoughts_per_cycle = 8
            self.mutations_per_thought = 5
            self.validation_threshold = 0.6

        elif mode == ExpansionMode.INFINITE:
            self.thoughts_per_cycle = 10
            self.mutations_per_thought = 7
            self.validation_threshold = 0.65

        elif mode == ExpansionMode.GOD_MODE:
            self.thoughts_per_cycle = 15
            self.mutations_per_thought = 10
            self.validation_threshold = 0.5

    def start_infinite_expansion(self) -> bool:
        """Avvia l'espansione infinita dell'intelligenza."""
        if self.is_running:
            logger.warning("⚠️ Espansione già in corso")
            return False

        self.is_running = True
        self._stop_event.clear()

        # Avvia thread di espansione
        self._expansion_thread = threading.Thread(
            target=self._infinite_expansion_loop,
            name="ThoughtUniverseExpansion",
            daemon=True
        )
        self._expansion_thread.start()

        logger.info("🚀 Espansione infinita dell'intelligenza AVVIATA")
        return True

    def stop_expansion(self) -> bool:
        """Ferma l'espansione infinita."""
        if not self.is_running:
            logger.warning("⚠️ Espansione non in corso")
            return False

        self.is_running = False
        self._stop_event.set()

        # Attendi terminazione thread
        if self._expansion_thread and self._expansion_thread.is_alive():
            self._expansion_thread.join(timeout=5.0)

        logger.info("🛑 Espansione infinita dell'intelligenza FERMATA")
        return True

    def _infinite_expansion_loop(self):
        """Loop principale per l'espansione infinita."""
        logger.info("🌌 Inizio loop espansione infinita")

        while self.is_running and not self._stop_event.is_set():
            try:
                # Controlla limite cicli
                if self.max_cycles and self.current_cycle >= self.max_cycles:
                    logger.info(f"🏁 Raggiunto limite cicli: {self.max_cycles}")
                    break

                # Esegui ciclo di evoluzione
                cycle_result = self._execute_evolution_cycle()

                # Aggiorna metriche
                self._update_intelligence_metrics(cycle_result)

                # Salva nella storia
                self.evolution_history.append(cycle_result)

                # Log progresso
                self._log_cycle_progress(cycle_result)

                # Incrementa contatore
                self.current_cycle += 1

                # Pausa tra cicli
                if not self._stop_event.wait(self.cycle_interval):
                    continue  # Timeout normale, continua
                else:
                    break  # Stop richiesto

            except Exception as e:
                logger.error(f"💥 Errore nel ciclo di espansione: {e}")
                # Continua comunque (resilienza)
                time.sleep(self.cycle_interval)

        self.is_running = False
        logger.info("🌌 Loop espansione infinita terminato")

    def _execute_evolution_cycle(self) -> EvolutionCycle:
        """Esegue un singolo ciclo di evoluzione del pensiero."""
        cycle_id = f"cycle_{self.current_cycle:06d}_{int(time.time())}"
        start_time = datetime.now()

        logger.debug(f"🔄 Inizio ciclo evoluzione: {cycle_id}")

        # Fase 1: Genera pensieri seme
        seed_thoughts = self._generate_thought_seeds()

        # Fase 2: Evoluzione attraverso mutazioni
        generated_thoughts = []
        successful_mutations = 0
        failed_mutations = 0

        for seed in seed_thoughts:
            thoughts, success, failures = self._evolve_thought_seed(seed)
            generated_thoughts.extend(thoughts)
            successful_mutations += success
            failed_mutations += failures

        # Fase 3: Generazione nuove astrazioni
        new_abstractions = self._generate_new_abstractions(generated_thoughts)

        # Fase 4: Calcolo crescita cognitiva
        cognitive_growth = self._calculate_cognitive_growth(
            len(generated_thoughts), successful_mutations, len(new_abstractions)
        )

        end_time = datetime.now()
        duration = (end_time - start_time).total_seconds()

        return EvolutionCycle(
            cycle_id=cycle_id,
            start_time=start_time,
            seed_thoughts=seed_thoughts,
            generated_thoughts=generated_thoughts,
            successful_mutations=successful_mutations,
            failed_mutations=failed_mutations,
            new_abstractions=len(new_abstractions),
            cognitive_growth=cognitive_growth,
            end_time=end_time,
            duration=duration
        )

    def _generate_thought_seeds(self) -> List[ThoughtSeed]:
        """Genera pensieri seme per il ciclo di evoluzione."""
        seeds = []

        for i in range(self.thoughts_per_cycle):
            # Seleziona simboli casuali dal registry
            available_symbols = list(self.symbol_mutator.symbol_loader.symbol_profiles.keys())
            if len(available_symbols) < 2:
                continue

            selected_symbols = random.sample(available_symbols, min(3, len(available_symbols)))

            # Determina categoria
            category = self._infer_seed_category(selected_symbols)

            # Calcola intensità ed entropia
            intensity = random.uniform(0.3, 1.0)
            entropy = random.uniform(0.2, 0.8)

            seed = ThoughtSeed(
                symbols=selected_symbols,
                category=category,
                intensity=intensity,
                entropy=entropy,
                generation_id=f"seed_{self.current_cycle}_{i}",
                metadata={
                    'cycle': self.current_cycle,
                    'generation_method': 'random_selection',
                    'expansion_mode': self.mode.value
                }
            )

            seeds.append(seed)

        return seeds

    def _infer_seed_category(self, symbols: List[str]) -> str:
        """Inferisce categoria per i simboli seme."""
        # Trova categorie più comuni
        categories = []
        for symbol_id in symbols:
            profile = self.symbol_mutator.symbol_loader.symbol_profiles.get(symbol_id)
            if profile:
                category = getattr(profile, 'category', 'unknown')
                if category.startswith('ng:'):
                    main_category = category.split(':')[1] if ':' in category else category
                    categories.append(main_category)

        if categories:
            # Ritorna categoria più comune
            most_common = max(set(categories), key=categories.count)
            return most_common
        else:
            return 'mixed'

    def _evolve_thought_seed(self, seed: ThoughtSeed) -> Tuple[List[str], int, int]:
        """Evolve un pensiero seme attraverso mutazioni."""
        generated_thoughts = []
        successful_mutations = 0
        failed_mutations = 0

        # Ottieni simboli reali dai profili
        real_symbols = []
        for symbol_id in seed.symbols:
            profile = self.symbol_mutator.symbol_loader.symbol_profiles.get(symbol_id)
            if profile:
                symbol = getattr(profile, 'symbol', '')
                if symbol:
                    real_symbols.append(symbol)

        if not real_symbols:
            return generated_thoughts, successful_mutations, failed_mutations

        # Genera pensiero base usando cognitive integration
        try:
            request = CognitiveRequest(
                input_symbols=real_symbols,
                thinking_mode=ThinkingMode.CREATIVE,
                intensity=seed.intensity,
                require_validation=True
            )

            result = self.cognitive_integration.process_cognitive_request(request)

            if result.success and result.output_symbols:
                generated_thoughts.extend(result.output_symbols)

                # Applica mutazioni ai simboli generati
                for _ in range(self.mutations_per_thought):
                    mutation_result = self.symbol_mutator.generate_mutation(
                        source_symbols=result.output_symbols,
                        target_category=seed.category
                    )

                    if mutation_result.success and mutation_result.candidate:
                        generated_thoughts.append(mutation_result.candidate.mutated_symbol)
                        successful_mutations += 1
                    else:
                        failed_mutations += 1

        except Exception as e:
            logger.debug(f"⚠️ Errore evoluzione pensiero: {e}")
            failed_mutations += self.mutations_per_thought

        return generated_thoughts, successful_mutations, failed_mutations

    def _generate_new_abstractions(self, thoughts: List[str]) -> List[str]:
        """Genera nuove astrazioni dai pensieri evoluti."""
        abstractions = []

        if len(thoughts) < 2:
            return abstractions

        # Raggruppa pensieri per similarità
        thought_clusters = self._cluster_thoughts_by_similarity(thoughts)

        # Genera astrazione per ogni cluster
        for cluster in thought_clusters:
            if len(cluster) >= 2:
                # Crea astrazione combinando simboli del cluster
                abstraction = self._create_abstraction_from_cluster(cluster)
                if abstraction and abstraction not in self.generated_abstractions:
                    abstractions.append(abstraction)
                    self.generated_abstractions.add(abstraction)

        return abstractions

    def _cluster_thoughts_by_similarity(self, thoughts: List[str]) -> List[List[str]]:
        """Raggruppa pensieri per similarità Unicode."""
        clusters = []
        used_thoughts = set()

        for thought in thoughts:
            if thought in used_thoughts:
                continue

            # Trova pensieri simili
            cluster = [thought]
            used_thoughts.add(thought)

            thought_code = ord(thought) if thought else 0

            for other_thought in thoughts:
                if other_thought in used_thoughts:
                    continue

                other_code = ord(other_thought) if other_thought else 0

                # Considera simili se codici Unicode vicini
                if abs(thought_code - other_code) <= 50:
                    cluster.append(other_thought)
                    used_thoughts.add(other_thought)

            if len(cluster) >= 2:
                clusters.append(cluster)

        return clusters

    def _create_abstraction_from_cluster(self, cluster: List[str]) -> Optional[str]:
        """Crea astrazione da un cluster di pensieri."""
        if not cluster:
            return None

        # Strategia semplice: seleziona simbolo "centrale" del cluster
        codes = [ord(symbol) for symbol in cluster if symbol]
        if not codes:
            return None

        # Calcola codice medio
        avg_code = sum(codes) // len(codes)

        # Trova simbolo più vicino al codice medio nel pool Unicode
        best_symbol = None
        min_distance = float('inf')

        for symbol in self.symbol_mutator.unicode_pool:
            distance = abs(ord(symbol) - avg_code)
            if distance < min_distance and symbol not in self.generated_abstractions:
                min_distance = distance
                best_symbol = symbol

        return best_symbol

    def _calculate_cognitive_growth(self, thoughts_count: int, mutations: int, abstractions: int) -> float:
        """Calcola crescita cognitiva del ciclo."""
        # Formula di crescita cognitiva
        base_growth = thoughts_count * 0.1
        mutation_bonus = mutations * 0.05
        abstraction_bonus = abstractions * 0.2

        total_growth = base_growth + mutation_bonus + abstraction_bonus

        # Normalizza tra 0 e 1
        return min(total_growth, 1.0)

    def _update_intelligence_metrics(self, cycle: EvolutionCycle):
        """Aggiorna metriche di intelligenza."""
        # Aggiorna contatori
        self.intelligence_metrics.total_thoughts += len(cycle.generated_thoughts)

        # Calcola simboli unici
        all_symbols = set()
        for profile in self.symbol_mutator.symbol_loader.symbol_profiles.values():
            symbol = getattr(profile, 'symbol', '')
            if symbol:
                all_symbols.add(symbol)
        all_symbols.update(self.generated_abstractions)
        self.intelligence_metrics.unique_symbols = len(all_symbols)

        # Calcola domini cognitivi
        categories = set()
        for profile in self.symbol_mutator.symbol_loader.symbol_profiles.values():
            category = getattr(profile, 'category', '')
            if category.startswith('ng:'):
                main_category = category.split(':')[1] if ':' in category else category
                categories.add(main_category)
        self.intelligence_metrics.cognitive_domains = len(categories)

        # Calcola tasso successo mutazioni
        total_mutations = cycle.successful_mutations + cycle.failed_mutations
        if total_mutations > 0:
            cycle_success_rate = cycle.successful_mutations / total_mutations
            # Media mobile con cicli precedenti
            self.intelligence_metrics.mutation_success_rate = (
                self.intelligence_metrics.mutation_success_rate * 0.8 +
                cycle_success_rate * 0.2
            )

        # Calcola indice creativo
        self.intelligence_metrics.creative_index = min(
            (cycle.new_abstractions / max(self.thoughts_per_cycle, 1)) * 100,
            100.0
        )

        # Calcola quoziente intellettivo (formula semplificata)
        base_iq = 100.0
        symbol_factor = min(self.intelligence_metrics.unique_symbols / 1000, 2.0)
        domain_factor = min(self.intelligence_metrics.cognitive_domains / 20, 2.0)
        mutation_factor = self.intelligence_metrics.mutation_success_rate
        creative_factor = self.intelligence_metrics.creative_index / 100

        self.intelligence_metrics.intelligence_quotient = base_iq * (
            1 + symbol_factor * 0.3 + domain_factor * 0.2 +
            mutation_factor * 0.3 + creative_factor * 0.2
        )

    def _log_cycle_progress(self, cycle: EvolutionCycle):
        """Log progresso del ciclo."""
        logger.info(f"🔄 Ciclo {self.current_cycle}: "
                   f"{len(cycle.generated_thoughts)} pensieri, "
                   f"{cycle.successful_mutations} mutazioni, "
                   f"{cycle.new_abstractions} astrazioni, "
                   f"crescita {cycle.cognitive_growth:.2f}")

        # Log dettagliato ogni 10 cicli
        if self.current_cycle % 10 == 0:
            logger.info(f"📊 Metriche intelligenza (ciclo {self.current_cycle}):")
            logger.info(f"  🧠 Pensieri totali: {self.intelligence_metrics.total_thoughts}")
            logger.info(f"  🔣 Simboli unici: {self.intelligence_metrics.unique_symbols}")
            logger.info(f"  🎯 Domini cognitivi: {self.intelligence_metrics.cognitive_domains}")
            logger.info(f"  🧬 Tasso successo mutazioni: {self.intelligence_metrics.mutation_success_rate:.2%}")
            logger.info(f"  🎨 Indice creativo: {self.intelligence_metrics.creative_index:.1f}")
            logger.info(f"  🧠 QI: {self.intelligence_metrics.intelligence_quotient:.1f}")

    def get_expansion_status(self) -> Dict[str, Any]:
        """Ritorna stato attuale dell'espansione."""
        return {
            'is_running': self.is_running,
            'current_cycle': self.current_cycle,
            'mode': self.mode.value,
            'total_evolution_cycles': len(self.evolution_history),
            'thought_seeds_count': len(self.thought_seeds),
            'generated_abstractions_count': len(self.generated_abstractions),
            'intelligence_metrics': {
                'total_thoughts': self.intelligence_metrics.total_thoughts,
                'unique_symbols': self.intelligence_metrics.unique_symbols,
                'cognitive_domains': self.intelligence_metrics.cognitive_domains,
                'intelligence_quotient': self.intelligence_metrics.intelligence_quotient,
                'creative_index': self.intelligence_metrics.creative_index
            },
            'last_cycle_duration': self.evolution_history[-1].duration if self.evolution_history else None,
            'average_cycle_duration': sum(c.duration for c in self.evolution_history if c.duration) / max(len(self.evolution_history), 1)
        }

    def run_finite_expansion(self, cycles: int = 5) -> List[EvolutionCycle]:
        """Esegue espansione per un numero finito di cicli."""
        logger.info(f"🚀 Avvio espansione finita per {cycles} cicli")

        results = []
        original_max_cycles = self.max_cycles
        self.max_cycles = cycles

        try:
            for i in range(cycles):
                cycle_result = self._execute_evolution_cycle()
                self._update_intelligence_metrics(cycle_result)
                self.evolution_history.append(cycle_result)
                self._log_cycle_progress(cycle_result)
                results.append(cycle_result)
                self.current_cycle += 1

                # Pausa tra cicli
                if i < cycles - 1:
                    time.sleep(self.cycle_interval)

        finally:
            self.max_cycles = original_max_cycles

        logger.info(f"✅ Espansione finita completata: {len(results)} cicli")
        return results


# Funzioni di utilità per uso esterno
def create_thought_universe_expander(
    mode: ExpansionMode = ExpansionMode.CREATIVE,
    registry_path: str = "neuroglyph/core/locked_registry_godmode_v9.json"
) -> ThoughtUniverseExpander:
    """Crea Thought Universe Expander con configurazione ottimale."""
    return ThoughtUniverseExpander(mode, registry_path)


if __name__ == "__main__":
    # Test Thought Universe Expander
    print("🌌 NEUROGLYPH Thought Universe Expander - Test")
    print("=" * 60)

    # Crea espansore
    expander = create_thought_universe_expander(ExpansionMode.CREATIVE)

    # Test ciclo singolo
    print("\n🧪 Test ciclo evoluzione singolo...")
    cycle_result = expander._execute_evolution_cycle()

    print(f"✅ Ciclo completato!")
    print(f"🌱 Pensieri seme: {len(cycle_result.seed_thoughts)}")
    print(f"🧠 Pensieri generati: {len(cycle_result.generated_thoughts)}")
    print(f"🧬 Mutazioni riuscite: {cycle_result.successful_mutations}")
    print(f"🎯 Nuove astrazioni: {cycle_result.new_abstractions}")
    print(f"📈 Crescita cognitiva: {cycle_result.cognitive_growth:.2f}")
    print(f"⏱️ Durata: {cycle_result.duration:.3f}s")

    print(f"\n📊 Stato espansione:")
    status = expander.get_expansion_status()
    for key, value in status.items():
        if key != 'intelligence_metrics':
            print(f"  {key}: {value}")
