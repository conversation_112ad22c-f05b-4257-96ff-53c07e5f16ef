{"symbol": "⊕", "code": "ng:operator:add", "fallback": "[+]", "valid": false, "score": 35.0, "errors": [], "warnings": [], "usu_check": {"unicode_unique": false, "code_unique": false, "fallback_unique": false, "visually_distinct": true, "semantically_atomic": true, "errors": ["Simbolo '⊕' gi<PERSON> esistente", "Codice 'ng:operator:add' gi<PERSON> esistente"], "warnings": ["Fallback '[+]' gi<PERSON> esistente"]}, "ctu_check": {"format_valid": true, "category_valid": true, "function_valid": true, "errors": [], "warnings": []}, "lcl_check": {"utf8_compatible": true, "fallback_format_valid": true, "font_safe": true, "tokenizer_friendly": true, "token_cost_estimate": 3, "errors": [], "warnings": []}}