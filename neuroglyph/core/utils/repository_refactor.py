#!/usr/bin/env python3
"""
NEUROGLYPH LLM - Repository Refactoring
=======================================

Refactoring intelligente e completo del repository NEUROGLYPH.
Organizza files per scopo, funzionalità e fase del progetto.
"""

import os
import shutil
import json
from pathlib import Path
from datetime import datetime
from typing import Dict, List, Tuple

class RepositoryRefactor:
    """Refactoring intelligente repository NEUROGLYPH"""
    
    def __init__(self):
        self.root = Path(".")
        self.backup_dir = Path("archive/refactor_backup")
        self.new_structure = self._define_new_structure()
        
    def _define_new_structure(self) -> Dict[str, Dict]:
        """Definisce la nuova struttura organizzata del repository"""
        return {
            # CORE SYSTEM - Componenti principali NEUROGLYPH
            "neuroglyph/": {
                "description": "Core NEUROGLYPH LLM system",
                "subdirs": {
                    "core/": "Core components (validator, tokenizer, memory)",
                    "symbols/": "Symbol registry and management", 
                    "reasoning/": "SOCRATE reasoning engine",
                    "training/": "LLM training components"
                }
            },
            
            # DATASETS - Training e test data
            "data/": {
                "description": "Training datasets and test data",
                "subdirs": {
                    "training/": "Training datasets (JSONL)",
                    "testing/": "Test datasets and validation",
                    "benchmarks/": "Benchmark results and metrics"
                }
            },
            
            # MODELS - Model files e configurazioni
            "models/": {
                "description": "Model files and configurations",
                "subdirs": {
                    "base/": "Base models (Qwen, etc.)",
                    "trained/": "Trained NEUROGLYPH models",
                    "configs/": "Model configurations"
                }
            },
            
            # TOOLS - Scripts e utilities
            "tools/": {
                "description": "Development tools and utilities",
                "subdirs": {
                    "training/": "Training scripts",
                    "validation/": "Validation and testing tools",
                    "symbols/": "Symbol management tools",
                    "benchmarks/": "Benchmark and evaluation tools"
                }
            },
            
            # DOCS - Documentazione organizzata
            "docs/": {
                "description": "Documentation and guides",
                "subdirs": {
                    "architecture/": "System architecture docs",
                    "training/": "Training guides and reports",
                    "api/": "API documentation",
                    "research/": "Research papers and analysis"
                }
            },
            
            # TESTS - Test suite completa
            "tests/": {
                "description": "Test suite and validation",
                "subdirs": {
                    "unit/": "Unit tests",
                    "integration/": "Integration tests", 
                    "performance/": "Performance tests",
                    "validation/": "Symbol validation tests"
                }
            },
            
            # CONFIG - Configurazioni sistema
            "config/": {
                "description": "System configurations",
                "subdirs": {
                    "training/": "Training configurations",
                    "gpu/": "GPU and hardware configs",
                    "deployment/": "Deployment configurations"
                }
            },
            
            # ARCHIVE - Files archiviati
            "archive/": {
                "description": "Archived and backup files",
                "subdirs": {
                    "old_versions/": "Previous versions",
                    "experiments/": "Experimental code",
                    "deprecated/": "Deprecated components"
                }
            }
        }
    
    def analyze_current_files(self) -> Dict[str, List[Path]]:
        """Analizza tutti i file attuali e li categorizza"""
        print("🔍 ANALISI FILES ATTUALI")
        print("=" * 60)
        
        file_categories = {
            "core_components": [],
            "symbol_management": [],
            "training_scripts": [],
            "datasets": [],
            "models": [],
            "documentation": [],
            "tests": [],
            "configs": [],
            "benchmarks": [],
            "deprecated": [],
            "archive": []
        }
        
        # Analizza tutti i file
        for file_path in self.root.rglob("*"):
            if file_path.is_file() and not self._should_ignore(file_path):
                category = self._categorize_file(file_path)
                file_categories[category].append(file_path)
        
        # Mostra statistiche
        for category, files in file_categories.items():
            if files:
                print(f"📁 {category}: {len(files)} files")
        
        return file_categories
    
    def _should_ignore(self, file_path: Path) -> bool:
        """Determina se un file deve essere ignorato"""
        ignore_patterns = [
            "__pycache__", ".git", "venv_metal", ".DS_Store",
            ".pyc", ".pyo", ".pyd", "node_modules"
        ]
        
        return any(pattern in str(file_path) for pattern in ignore_patterns)
    
    def _categorize_file(self, file_path: Path) -> str:
        """Categorizza un file in base al nome e contenuto"""
        path_str = str(file_path).lower()
        name = file_path.name.lower()
        
        # Core components
        if any(x in path_str for x in ["core/", "symbolic_validator", "neuroglyph_tokenizer", "dag_memory"]):
            return "core_components"
        
        # Symbol management
        if any(x in path_str for x in ["symbol", "validate_symbol", "score_symbol", "submit_symbol"]):
            return "symbol_management"
        
        # Training
        if any(x in path_str for x in ["train", "training", "qlora", "unsloth", "setup_"]):
            return "training_scripts"
        
        # Datasets
        if any(x in path_str for x in ["dataset", ".jsonl", "data/"]) and "docs/" not in path_str:
            return "datasets"
        
        # Models
        if any(x in path_str for x in ["model/", "qwen", ".bin", "config.json", "tokenizer"]):
            return "models"
        
        # Documentation
        if any(x in path_str for x in ["docs/", ".md", "readme"]) and "model/" not in path_str:
            return "documentation"
        
        # Tests
        if any(x in path_str for x in ["test", "tests/"]):
            return "tests"
        
        # Configs
        if any(x in path_str for x in ["config", ".yaml", ".yml"]) and "model/" not in path_str:
            return "configs"
        
        # Benchmarks
        if any(x in path_str for x in ["benchmark", "logs/", "results"]):
            return "benchmarks"
        
        # Archive
        if any(x in path_str for x in ["archive/", "old", "backup"]):
            return "archive"
        
        # Deprecated (ultra docs, old scripts)
        if any(x in path_str for x in ["ultra/", "deprecated", "legacy"]):
            return "deprecated"
        
        return "core_components"  # Default
    
    def create_new_structure(self):
        """Crea la nuova struttura di directory"""
        print("\n🏗️ CREAZIONE NUOVA STRUTTURA")
        print("=" * 60)
        
        for main_dir, info in self.new_structure.items():
            main_path = Path(main_dir)
            main_path.mkdir(exist_ok=True)
            print(f"📁 Creata: {main_dir}")
            
            for subdir, desc in info["subdirs"].items():
                sub_path = main_path / subdir
                sub_path.mkdir(exist_ok=True)
                print(f"   📂 {subdir} - {desc}")
    
    def create_backup(self):
        """Crea backup completo prima del refactoring"""
        print("\n💾 CREAZIONE BACKUP")
        print("=" * 60)
        
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        backup_path = self.backup_dir / f"pre_refactor_{timestamp}"
        backup_path.mkdir(parents=True, exist_ok=True)
        
        # Copia file importanti
        important_dirs = ["core", "scripts", "docs", "datasets", "config"]
        
        for dir_name in important_dirs:
            src_dir = Path(dir_name)
            if src_dir.exists():
                dst_dir = backup_path / dir_name
                shutil.copytree(src_dir, dst_dir, ignore=shutil.ignore_patterns("__pycache__"))
                print(f"✅ Backup: {dir_name} → {dst_dir}")
        
        print(f"💾 Backup completo in: {backup_path}")
        return backup_path
    
    def move_files_to_new_structure(self, file_categories: Dict[str, List[Path]]):
        """Sposta i file nella nuova struttura"""
        print("\n🔄 SPOSTAMENTO FILES")
        print("=" * 60)
        
        # Mapping categorie → nuove posizioni
        category_mapping = {
            "core_components": "neuroglyph/core/",
            "symbol_management": "neuroglyph/symbols/",
            "training_scripts": "tools/training/",
            "datasets": "data/training/",
            "models": "models/base/",
            "documentation": "docs/",
            "tests": "tests/unit/",
            "configs": "config/",
            "benchmarks": "data/benchmarks/",
            "deprecated": "archive/deprecated/",
            "archive": "archive/old_versions/"
        }
        
        moved_count = 0
        
        for category, files in file_categories.items():
            if not files:
                continue
                
            target_dir = Path(category_mapping.get(category, "archive/misc/"))
            target_dir.mkdir(parents=True, exist_ok=True)
            
            print(f"\n📁 Categoria: {category} → {target_dir}")
            
            for file_path in files:
                try:
                    # Determina nome file di destinazione
                    if self._is_duplicate_name(file_path, target_dir):
                        # Aggiungi timestamp per evitare conflitti
                        stem = file_path.stem
                        suffix = file_path.suffix
                        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                        new_name = f"{stem}_{timestamp}{suffix}"
                    else:
                        new_name = file_path.name
                    
                    target_path = target_dir / new_name
                    
                    # Sposta file
                    shutil.move(str(file_path), str(target_path))
                    print(f"   ✅ {file_path} → {target_path}")
                    moved_count += 1
                    
                except Exception as e:
                    print(f"   ❌ Errore spostamento {file_path}: {e}")
        
        print(f"\n📊 Files spostati: {moved_count}")
    
    def _is_duplicate_name(self, file_path: Path, target_dir: Path) -> bool:
        """Controlla se esiste già un file con lo stesso nome"""
        return (target_dir / file_path.name).exists()
    
    def organize_documentation(self):
        """Organizza la documentazione in sottocartelle logiche"""
        print("\n📚 ORGANIZZAZIONE DOCUMENTAZIONE")
        print("=" * 60)
        
        docs_dir = Path("docs")
        if not docs_dir.exists():
            return
        
        # Mapping documenti → sottocartelle
        doc_mapping = {
            "architecture": ["NEUROGLYPH_LLM_COMPLETE", "ZERO_HALLUCINATION", "SOCRATE_ENGINE"],
            "training": ["TRAINING_ROADMAP", "UNSLOTH", "METAL_GPU", "MODEL_SELECTION"],
            "research": ["neuroglyph_paper", "ng_llm_manifesto", "CODING_SUPERIORITY"],
            "api": ["usage_guide", "README"]
        }
        
        for subdir, keywords in doc_mapping.items():
            subdir_path = docs_dir / subdir
            subdir_path.mkdir(exist_ok=True)
            
            for doc_file in docs_dir.glob("*.md"):
                if any(keyword.lower() in doc_file.name.lower() for keyword in keywords):
                    target_path = subdir_path / doc_file.name
                    if not target_path.exists():
                        shutil.move(str(doc_file), str(target_path))
                        print(f"   📄 {doc_file.name} → {subdir}/")
    
    def create_index_files(self):
        """Crea file index per ogni directory principale"""
        print("\n📋 CREAZIONE INDEX FILES")
        print("=" * 60)
        
        for main_dir, info in self.new_structure.items():
            index_path = Path(main_dir) / "README.md"
            
            content = f"""# {main_dir.rstrip('/')}

{info['description']}

## Struttura

"""
            for subdir, desc in info["subdirs"].items():
                content += f"- **{subdir}**: {desc}\n"
            
            content += f"""

## Utilizzo

Questa directory contiene {info['description'].lower()}.

---
*Generato automaticamente dal refactoring NEUROGLYPH - {datetime.now().strftime('%Y-%m-%d')}*
"""
            
            with open(index_path, 'w', encoding='utf-8') as f:
                f.write(content)
            
            print(f"📋 Creato: {index_path}")
    
    def update_import_paths(self):
        """Aggiorna i path di import nei file Python"""
        print("\n🔧 AGGIORNAMENTO IMPORT PATHS")
        print("=" * 60)
        
        # Mapping vecchi → nuovi path
        path_mapping = {
            "neuroglyph.core.": "neuroglyph.neuroglyph.core.",
            "from neuroglyph.core import": "from neuroglyph.core import",
            "tools.": "tools.",
            "from tools import": "from tools import"
        }
        
        # Trova tutti i file Python
        python_files = list(Path(".").rglob("*.py"))
        
        updated_count = 0
        for py_file in python_files:
            if self._should_ignore(py_file):
                continue
                
            try:
                with open(py_file, 'r', encoding='utf-8') as f:
                    content = f.read()
                
                original_content = content
                
                # Applica mapping
                for old_path, new_path in path_mapping.items():
                    content = content.replace(old_path, new_path)
                
                # Salva se modificato
                if content != original_content:
                    with open(py_file, 'w', encoding='utf-8') as f:
                        f.write(content)
                    print(f"   ✅ Aggiornato: {py_file}")
                    updated_count += 1
                    
            except Exception as e:
                print(f"   ❌ Errore aggiornamento {py_file}: {e}")
        
        print(f"📊 Files aggiornati: {updated_count}")
    
    def cleanup_empty_directories(self):
        """Rimuove directory vuote"""
        print("\n🧹 PULIZIA DIRECTORY VUOTE")
        print("=" * 60)
        
        removed_count = 0
        for dir_path in sorted(Path(".").rglob("*"), key=lambda x: len(str(x)), reverse=True):
            if dir_path.is_dir() and not self._should_ignore(dir_path):
                try:
                    if not any(dir_path.iterdir()):  # Directory vuota
                        dir_path.rmdir()
                        print(f"   🗑️ Rimossa: {dir_path}")
                        removed_count += 1
                except OSError:
                    pass  # Directory non vuota o errore
        
        print(f"📊 Directory rimosse: {removed_count}")
    
    def generate_refactor_report(self, backup_path: Path):
        """Genera report del refactoring"""
        print("\n📊 GENERAZIONE REPORT")
        print("=" * 60)
        
        report = {
            "refactor_date": datetime.now().isoformat(),
            "backup_location": str(backup_path),
            "new_structure": self.new_structure,
            "summary": {
                "total_directories": len(list(Path(".").rglob("*"))),
                "total_files": len([f for f in Path(".").rglob("*") if f.is_file()]),
                "backup_created": True,
                "structure_organized": True
            }
        }
        
        report_path = Path("docs/REFACTOR_REPORT.md")
        
        content = f"""# NEUROGLYPH Repository Refactoring Report

**Data**: {report['refactor_date']}
**Backup**: {report['backup_location']}

## Nuova Struttura

"""
        
        for main_dir, info in self.new_structure.items():
            content += f"### {main_dir}\n{info['description']}\n\n"
            for subdir, desc in info["subdirs"].items():
                content += f"- **{subdir}**: {desc}\n"
            content += "\n"
        
        content += f"""
## Statistiche

- Directory totali: {report['summary']['total_directories']}
- Files totali: {report['summary']['total_files']}
- Backup creato: ✅
- Struttura organizzata: ✅

## Benefici

1. **Organizzazione logica**: Files raggruppati per funzionalità
2. **Navigazione facile**: Struttura intuitiva e pulita
3. **Manutenzione semplificata**: Componenti ben separati
4. **Scalabilità**: Struttura pronta per crescita progetto

---
*Report generato automaticamente*
"""
        
        with open(report_path, 'w', encoding='utf-8') as f:
            f.write(content)
        
        print(f"📊 Report salvato: {report_path}")

def main():
    """Refactoring principale"""
    print("🔧 NEUROGLYPH LLM - Repository Refactoring")
    print("🎯 Organizzazione intelligente e pulizia completa")
    print("=" * 70)
    
    refactor = RepositoryRefactor()
    
    # 1. Analisi situazione attuale
    file_categories = refactor.analyze_current_files()
    
    # 2. Backup di sicurezza
    backup_path = refactor.create_backup()
    
    # 3. Creazione nuova struttura
    refactor.create_new_structure()
    
    # 4. Spostamento files
    refactor.move_files_to_new_structure(file_categories)
    
    # 5. Organizzazione documentazione
    refactor.organize_documentation()
    
    # 6. Creazione index files
    refactor.create_index_files()
    
    # 7. Aggiornamento import paths
    refactor.update_import_paths()
    
    # 8. Pulizia directory vuote
    refactor.cleanup_empty_directories()
    
    # 9. Report finale
    refactor.generate_refactor_report(backup_path)
    
    print("\n🎉 REFACTORING COMPLETATO!")
    print("✅ Repository NEUROGLYPH organizzato e pulito")
    print("📁 Struttura logica e scalabile implementata")
    
    return True

if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)
