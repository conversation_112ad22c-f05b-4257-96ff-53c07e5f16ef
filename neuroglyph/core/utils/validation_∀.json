{"symbol": "∀", "code": "ng:logic:forall", "fallback": "[FORALL]", "valid": true, "score": 100.0, "errors": [], "warnings": [], "usu_check": {"unicode_unique": true, "code_unique": true, "fallback_unique": true, "visually_distinct": true, "semantically_atomic": true, "errors": [], "warnings": []}, "ctu_check": {"format_valid": true, "category_valid": true, "function_valid": true, "errors": [], "warnings": []}, "lcl_check": {"utf8_compatible": true, "fallback_format_valid": true, "font_safe": true, "tokenizer_friendly": true, "token_cost_estimate": 3, "errors": [], "warnings": []}}