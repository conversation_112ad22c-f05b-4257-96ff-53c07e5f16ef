[{"symbol": "∀", "unicode_codepoint": "U+2200", "unicode_name": "FOR ALL", "block": "Mathematical Operators"}, {"symbol": "∁", "unicode_codepoint": "U+2201", "unicode_name": "COMPLEMENT", "block": "Mathematical Operators"}, {"symbol": "∂", "unicode_codepoint": "U+2202", "unicode_name": "PARTIAL DIFFERENTIAL", "block": "Mathematical Operators"}, {"symbol": "∃", "unicode_codepoint": "U+2203", "unicode_name": "THERE EXISTS", "block": "Mathematical Operators"}, {"symbol": "∄", "unicode_codepoint": "U+2204", "unicode_name": "THERE DOES NOT EXIST", "block": "Mathematical Operators"}, {"symbol": "∅", "unicode_codepoint": "U+2205", "unicode_name": "EMPTY SET", "block": "Mathematical Operators"}, {"symbol": "∆", "unicode_codepoint": "U+2206", "unicode_name": "INCREMENT", "block": "Mathematical Operators"}, {"symbol": "∇", "unicode_codepoint": "U+2207", "unicode_name": "NABLA", "block": "Mathematical Operators"}, {"symbol": "∈", "unicode_codepoint": "U+2208", "unicode_name": "ELEMENT OF", "block": "Mathematical Operators"}, {"symbol": "∉", "unicode_codepoint": "U+2209", "unicode_name": "NOT AN ELEMENT OF", "block": "Mathematical Operators"}, {"symbol": "∊", "unicode_codepoint": "U+220A", "unicode_name": "SMALL ELEMENT OF", "block": "Mathematical Operators"}, {"symbol": "∋", "unicode_codepoint": "U+220B", "unicode_name": "CONTAINS AS MEMBER", "block": "Mathematical Operators"}, {"symbol": "∌", "unicode_codepoint": "U+220C", "unicode_name": "DOES NOT CONTAIN AS MEMBER", "block": "Mathematical Operators"}, {"symbol": "∍", "unicode_codepoint": "U+220D", "unicode_name": "SMALL CONTAINS AS MEMBER", "block": "Mathematical Operators"}, {"symbol": "∎", "unicode_codepoint": "U+220E", "unicode_name": "END OF PROOF", "block": "Mathematical Operators"}, {"symbol": "∏", "unicode_codepoint": "U+220F", "unicode_name": "N-ARY PRODUCT", "block": "Mathematical Operators"}, {"symbol": "∐", "unicode_codepoint": "U+2210", "unicode_name": "N-ARY COPRODUCT", "block": "Mathematical Operators"}, {"symbol": "∑", "unicode_codepoint": "U+2211", "unicode_name": "N-ARY SUMMATION", "block": "Mathematical Operators"}, {"symbol": "−", "unicode_codepoint": "U+2212", "unicode_name": "MINUS SIGN", "block": "Mathematical Operators"}, {"symbol": "∓", "unicode_codepoint": "U+2213", "unicode_name": "MINUS-OR-PLUS SIGN", "block": "Mathematical Operators"}, {"symbol": "∔", "unicode_codepoint": "U+2214", "unicode_name": "DOT PLUS", "block": "Mathematical Operators"}, {"symbol": "∕", "unicode_codepoint": "U+2215", "unicode_name": "DIVISION SLASH", "block": "Mathematical Operators"}, {"symbol": "∖", "unicode_codepoint": "U+2216", "unicode_name": "SET MINUS", "block": "Mathematical Operators"}, {"symbol": "∗", "unicode_codepoint": "U+2217", "unicode_name": "ASTERISK OPERATOR", "block": "Mathematical Operators"}, {"symbol": "∘", "unicode_codepoint": "U+2218", "unicode_name": "RING OPERATOR", "block": "Mathematical Operators"}, {"symbol": "∙", "unicode_codepoint": "U+2219", "unicode_name": "BULLET OPERATOR", "block": "Mathematical Operators"}, {"symbol": "√", "unicode_codepoint": "U+221A", "unicode_name": "SQUARE ROOT", "block": "Mathematical Operators"}, {"symbol": "∛", "unicode_codepoint": "U+221B", "unicode_name": "CUBE ROOT", "block": "Mathematical Operators"}, {"symbol": "∜", "unicode_codepoint": "U+221C", "unicode_name": "FOURTH ROOT", "block": "Mathematical Operators"}, {"symbol": "∝", "unicode_codepoint": "U+221D", "unicode_name": "PROPORTIONAL TO", "block": "Mathematical Operators"}, {"symbol": "∞", "unicode_codepoint": "U+221E", "unicode_name": "INFINITY", "block": "Mathematical Operators"}, {"symbol": "∟", "unicode_codepoint": "U+221F", "unicode_name": "RIGHT ANGLE", "block": "Mathematical Operators"}, {"symbol": "∠", "unicode_codepoint": "U+2220", "unicode_name": "ANGLE", "block": "Mathematical Operators"}, {"symbol": "∡", "unicode_codepoint": "U+2221", "unicode_name": "MEASURED ANGLE", "block": "Mathematical Operators"}, {"symbol": "∢", "unicode_codepoint": "U+2222", "unicode_name": "SPHERICAL ANGLE", "block": "Mathematical Operators"}, {"symbol": "∣", "unicode_codepoint": "U+2223", "unicode_name": "DIVIDES", "block": "Mathematical Operators"}, {"symbol": "∤", "unicode_codepoint": "U+2224", "unicode_name": "DOES NOT DIVIDE", "block": "Mathematical Operators"}, {"symbol": "∥", "unicode_codepoint": "U+2225", "unicode_name": "PARALLEL TO", "block": "Mathematical Operators"}, {"symbol": "∦", "unicode_codepoint": "U+2226", "unicode_name": "NOT PARALLEL TO", "block": "Mathematical Operators"}, {"symbol": "∧", "unicode_codepoint": "U+2227", "unicode_name": "LOGICAL AND", "block": "Mathematical Operators"}, {"symbol": "∨", "unicode_codepoint": "U+2228", "unicode_name": "LOGICAL OR", "block": "Mathematical Operators"}, {"symbol": "∩", "unicode_codepoint": "U+2229", "unicode_name": "INTERSECTION", "block": "Mathematical Operators"}, {"symbol": "∪", "unicode_codepoint": "U+222A", "unicode_name": "UNION", "block": "Mathematical Operators"}, {"symbol": "∫", "unicode_codepoint": "U+222B", "unicode_name": "INTEGRAL", "block": "Mathematical Operators"}, {"symbol": "∬", "unicode_codepoint": "U+222C", "unicode_name": "DOUBLE INTEGRAL", "block": "Mathematical Operators"}, {"symbol": "∭", "unicode_codepoint": "U+222D", "unicode_name": "TRIPLE INTEGRAL", "block": "Mathematical Operators"}, {"symbol": "∮", "unicode_codepoint": "U+222E", "unicode_name": "CONTOUR INTEGRAL", "block": "Mathematical Operators"}, {"symbol": "∯", "unicode_codepoint": "U+222F", "unicode_name": "SURFACE INTEGRAL", "block": "Mathematical Operators"}, {"symbol": "∰", "unicode_codepoint": "U+2230", "unicode_name": "VOLUME INTEGRAL", "block": "Mathematical Operators"}, {"symbol": "∱", "unicode_codepoint": "U+2231", "unicode_name": "CLOCKWISE INTEGRAL", "block": "Mathematical Operators"}, {"symbol": "∲", "unicode_codepoint": "U+2232", "unicode_name": "CLOCKWISE CONTOUR INTEGRAL", "block": "Mathematical Operators"}, {"symbol": "∳", "unicode_codepoint": "U+2233", "unicode_name": "ANTICLOCKWISE CONTOUR INTEGRAL", "block": "Mathematical Operators"}, {"symbol": "∴", "unicode_codepoint": "U+2234", "unicode_name": "THEREFORE", "block": "Mathematical Operators"}, {"symbol": "∵", "unicode_codepoint": "U+2235", "unicode_name": "BECAUSE", "block": "Mathematical Operators"}, {"symbol": "∶", "unicode_codepoint": "U+2236", "unicode_name": "RATIO", "block": "Mathematical Operators"}, {"symbol": "∷", "unicode_codepoint": "U+2237", "unicode_name": "PROPORTION", "block": "Mathematical Operators"}, {"symbol": "∸", "unicode_codepoint": "U+2238", "unicode_name": "DOT MINUS", "block": "Mathematical Operators"}, {"symbol": "∹", "unicode_codepoint": "U+2239", "unicode_name": "EXCESS", "block": "Mathematical Operators"}, {"symbol": "∺", "unicode_codepoint": "U+223A", "unicode_name": "GEOMETRIC PROPORTION", "block": "Mathematical Operators"}, {"symbol": "∻", "unicode_codepoint": "U+223B", "unicode_name": "HOMOTHETIC", "block": "Mathematical Operators"}, {"symbol": "∼", "unicode_codepoint": "U+223C", "unicode_name": "TILDE OPERATOR", "block": "Mathematical Operators"}, {"symbol": "∽", "unicode_codepoint": "U+223D", "unicode_name": "REVERSED TILDE", "block": "Mathematical Operators"}, {"symbol": "∾", "unicode_codepoint": "U+223E", "unicode_name": "INVERTED LAZY S", "block": "Mathematical Operators"}, {"symbol": "∿", "unicode_codepoint": "U+223F", "unicode_name": "SINE WAVE", "block": "Mathematical Operators"}, {"symbol": "≀", "unicode_codepoint": "U+2240", "unicode_name": "WREATH PRODUCT", "block": "Mathematical Operators"}, {"symbol": "≁", "unicode_codepoint": "U+2241", "unicode_name": "NOT TILDE", "block": "Mathematical Operators"}, {"symbol": "≂", "unicode_codepoint": "U+2242", "unicode_name": "MINUS TILDE", "block": "Mathematical Operators"}, {"symbol": "≃", "unicode_codepoint": "U+2243", "unicode_name": "ASYMPTOTICALLY EQUAL TO", "block": "Mathematical Operators"}, {"symbol": "≄", "unicode_codepoint": "U+2244", "unicode_name": "NOT ASYMPTOTICALLY EQUAL TO", "block": "Mathematical Operators"}, {"symbol": "≅", "unicode_codepoint": "U+2245", "unicode_name": "APPROXIMATELY EQUAL TO", "block": "Mathematical Operators"}, {"symbol": "≆", "unicode_codepoint": "U+2246", "unicode_name": "APPROXIMATELY BUT NOT ACTUALLY EQUAL TO", "block": "Mathematical Operators"}, {"symbol": "≇", "unicode_codepoint": "U+2247", "unicode_name": "NEITHER APPROXIMATELY NOR ACTUALLY EQUAL TO", "block": "Mathematical Operators"}, {"symbol": "≈", "unicode_codepoint": "U+2248", "unicode_name": "ALMOST EQUAL TO", "block": "Mathematical Operators"}, {"symbol": "≉", "unicode_codepoint": "U+2249", "unicode_name": "NOT ALMOST EQUAL TO", "block": "Mathematical Operators"}, {"symbol": "≊", "unicode_codepoint": "U+224A", "unicode_name": "ALMOST EQUAL OR EQUAL TO", "block": "Mathematical Operators"}, {"symbol": "≋", "unicode_codepoint": "U+224B", "unicode_name": "TRIPLE TILDE", "block": "Mathematical Operators"}, {"symbol": "≌", "unicode_codepoint": "U+224C", "unicode_name": "ALL EQUAL TO", "block": "Mathematical Operators"}, {"symbol": "≍", "unicode_codepoint": "U+224D", "unicode_name": "EQUIVALENT TO", "block": "Mathematical Operators"}, {"symbol": "≎", "unicode_codepoint": "U+224E", "unicode_name": "GEOMETRICALLY EQUIVALENT TO", "block": "Mathematical Operators"}, {"symbol": "≏", "unicode_codepoint": "U+224F", "unicode_name": "DIFFERENCE BETWEEN", "block": "Mathematical Operators"}, {"symbol": "≐", "unicode_codepoint": "U+2250", "unicode_name": "APPROACHES THE LIMIT", "block": "Mathematical Operators"}, {"symbol": "≑", "unicode_codepoint": "U+2251", "unicode_name": "GEOMETRICALLY EQUAL TO", "block": "Mathematical Operators"}, {"symbol": "≒", "unicode_codepoint": "U+2252", "unicode_name": "APPROXIMATELY EQUAL TO OR THE IMAGE OF", "block": "Mathematical Operators"}, {"symbol": "≓", "unicode_codepoint": "U+2253", "unicode_name": "IMAGE OF OR APPROXIMATELY EQUAL TO", "block": "Mathematical Operators"}, {"symbol": "≔", "unicode_codepoint": "U+2254", "unicode_name": "COLON EQUALS", "block": "Mathematical Operators"}, {"symbol": "≕", "unicode_codepoint": "U+2255", "unicode_name": "EQUALS COLON", "block": "Mathematical Operators"}, {"symbol": "≖", "unicode_codepoint": "U+2256", "unicode_name": "RING IN EQUAL TO", "block": "Mathematical Operators"}, {"symbol": "≗", "unicode_codepoint": "U+2257", "unicode_name": "RING EQUAL TO", "block": "Mathematical Operators"}, {"symbol": "≘", "unicode_codepoint": "U+2258", "unicode_name": "CORRESPONDS TO", "block": "Mathematical Operators"}, {"symbol": "≙", "unicode_codepoint": "U+2259", "unicode_name": "ESTIMATES", "block": "Mathematical Operators"}, {"symbol": "≚", "unicode_codepoint": "U+225A", "unicode_name": "EQUIANGULAR TO", "block": "Mathematical Operators"}, {"symbol": "≛", "unicode_codepoint": "U+225B", "unicode_name": "STAR EQUALS", "block": "Mathematical Operators"}, {"symbol": "≜", "unicode_codepoint": "U+225C", "unicode_name": "DELTA EQUAL TO", "block": "Mathematical Operators"}, {"symbol": "≝", "unicode_codepoint": "U+225D", "unicode_name": "EQUAL TO BY DEFINITION", "block": "Mathematical Operators"}, {"symbol": "≞", "unicode_codepoint": "U+225E", "unicode_name": "MEASURED BY", "block": "Mathematical Operators"}, {"symbol": "≟", "unicode_codepoint": "U+225F", "unicode_name": "QUESTIONED EQUAL TO", "block": "Mathematical Operators"}, {"symbol": "≠", "unicode_codepoint": "U+2260", "unicode_name": "NOT EQUAL TO", "block": "Mathematical Operators"}, {"symbol": "≡", "unicode_codepoint": "U+2261", "unicode_name": "IDENTICAL TO", "block": "Mathematical Operators"}, {"symbol": "≢", "unicode_codepoint": "U+2262", "unicode_name": "NOT IDENTICAL TO", "block": "Mathematical Operators"}, {"symbol": "≣", "unicode_codepoint": "U+2263", "unicode_name": "STRICTLY EQUIVALENT TO", "block": "Mathematical Operators"}, {"symbol": "≤", "unicode_codepoint": "U+2264", "unicode_name": "LESS-THAN OR EQUAL TO", "block": "Mathematical Operators"}, {"symbol": "≥", "unicode_codepoint": "U+2265", "unicode_name": "GREATER-THAN OR EQUAL TO", "block": "Mathematical Operators"}, {"symbol": "≦", "unicode_codepoint": "U+2266", "unicode_name": "LESS-THAN OVER EQUAL TO", "block": "Mathematical Operators"}, {"symbol": "≧", "unicode_codepoint": "U+2267", "unicode_name": "GREATER-THAN OVER EQUAL TO", "block": "Mathematical Operators"}, {"symbol": "≨", "unicode_codepoint": "U+2268", "unicode_name": "LESS-THAN BUT NOT EQUAL TO", "block": "Mathematical Operators"}, {"symbol": "≩", "unicode_codepoint": "U+2269", "unicode_name": "GREATER-THAN BUT NOT EQUAL TO", "block": "Mathematical Operators"}, {"symbol": "≪", "unicode_codepoint": "U+226A", "unicode_name": "MUCH LESS-THAN", "block": "Mathematical Operators"}, {"symbol": "≫", "unicode_codepoint": "U+226B", "unicode_name": "MUCH GREATER-THAN", "block": "Mathematical Operators"}, {"symbol": "≬", "unicode_codepoint": "U+226C", "unicode_name": "BETWEEN", "block": "Mathematical Operators"}, {"symbol": "≭", "unicode_codepoint": "U+226D", "unicode_name": "NOT EQUIVALENT TO", "block": "Mathematical Operators"}, {"symbol": "≮", "unicode_codepoint": "U+226E", "unicode_name": "NOT LESS-THAN", "block": "Mathematical Operators"}, {"symbol": "≯", "unicode_codepoint": "U+226F", "unicode_name": "NOT GREATER-THAN", "block": "Mathematical Operators"}, {"symbol": "≰", "unicode_codepoint": "U+2270", "unicode_name": "NEITHER LESS-THAN NOR EQUAL TO", "block": "Mathematical Operators"}, {"symbol": "≱", "unicode_codepoint": "U+2271", "unicode_name": "NEITHER GREATER-THAN NOR EQUAL TO", "block": "Mathematical Operators"}, {"symbol": "≲", "unicode_codepoint": "U+2272", "unicode_name": "LESS-THAN OR EQUIVALENT TO", "block": "Mathematical Operators"}, {"symbol": "≳", "unicode_codepoint": "U+2273", "unicode_name": "GREATER-THAN OR EQUIVALENT TO", "block": "Mathematical Operators"}, {"symbol": "≴", "unicode_codepoint": "U+2274", "unicode_name": "NEITHER LESS-THAN NOR EQUIVALENT TO", "block": "Mathematical Operators"}, {"symbol": "≵", "unicode_codepoint": "U+2275", "unicode_name": "NEITHER GREATER-THAN NOR EQUIVALENT TO", "block": "Mathematical Operators"}, {"symbol": "≶", "unicode_codepoint": "U+2276", "unicode_name": "LESS-THAN OR GREATER-THAN", "block": "Mathematical Operators"}, {"symbol": "≷", "unicode_codepoint": "U+2277", "unicode_name": "GREATER-THAN OR LESS-THAN", "block": "Mathematical Operators"}, {"symbol": "≸", "unicode_codepoint": "U+2278", "unicode_name": "NEITHER LESS-THAN NOR GREATER-THAN", "block": "Mathematical Operators"}, {"symbol": "≹", "unicode_codepoint": "U+2279", "unicode_name": "NEITHER GREATER-THAN NOR LESS-THAN", "block": "Mathematical Operators"}, {"symbol": "≺", "unicode_codepoint": "U+227A", "unicode_name": "PRECEDES", "block": "Mathematical Operators"}, {"symbol": "≻", "unicode_codepoint": "U+227B", "unicode_name": "SUCCEEDS", "block": "Mathematical Operators"}, {"symbol": "≼", "unicode_codepoint": "U+227C", "unicode_name": "PRECEDES OR EQUAL TO", "block": "Mathematical Operators"}, {"symbol": "≽", "unicode_codepoint": "U+227D", "unicode_name": "SUCCEEDS OR EQUAL TO", "block": "Mathematical Operators"}, {"symbol": "≾", "unicode_codepoint": "U+227E", "unicode_name": "PRECEDES OR EQUIVALENT TO", "block": "Mathematical Operators"}, {"symbol": "≿", "unicode_codepoint": "U+227F", "unicode_name": "SUCCEEDS OR EQUIVALENT TO", "block": "Mathematical Operators"}, {"symbol": "⊀", "unicode_codepoint": "U+2280", "unicode_name": "DOES NOT PRECEDE", "block": "Mathematical Operators"}, {"symbol": "⊁", "unicode_codepoint": "U+2281", "unicode_name": "DOES NOT SUCCEED", "block": "Mathematical Operators"}, {"symbol": "⊂", "unicode_codepoint": "U+2282", "unicode_name": "SUBSET OF", "block": "Mathematical Operators"}, {"symbol": "⊃", "unicode_codepoint": "U+2283", "unicode_name": "SUPERSET OF", "block": "Mathematical Operators"}, {"symbol": "⊄", "unicode_codepoint": "U+2284", "unicode_name": "NOT A SUBSET OF", "block": "Mathematical Operators"}, {"symbol": "⊅", "unicode_codepoint": "U+2285", "unicode_name": "NOT A SUPERSET OF", "block": "Mathematical Operators"}, {"symbol": "⊆", "unicode_codepoint": "U+2286", "unicode_name": "SUBSET OF OR EQUAL TO", "block": "Mathematical Operators"}, {"symbol": "⊇", "unicode_codepoint": "U+2287", "unicode_name": "SUPERSET OF OR EQUAL TO", "block": "Mathematical Operators"}, {"symbol": "⊈", "unicode_codepoint": "U+2288", "unicode_name": "NEITHER A SUBSET OF NOR EQUAL TO", "block": "Mathematical Operators"}, {"symbol": "⊉", "unicode_codepoint": "U+2289", "unicode_name": "NEITHER A SUPERSET OF NOR EQUAL TO", "block": "Mathematical Operators"}, {"symbol": "⊊", "unicode_codepoint": "U+228A", "unicode_name": "SUBSET OF WITH NOT EQUAL TO", "block": "Mathematical Operators"}, {"symbol": "⊋", "unicode_codepoint": "U+228B", "unicode_name": "SUPERSET OF WITH NOT EQUAL TO", "block": "Mathematical Operators"}, {"symbol": "⊌", "unicode_codepoint": "U+228C", "unicode_name": "MULTISET", "block": "Mathematical Operators"}, {"symbol": "⊍", "unicode_codepoint": "U+228D", "unicode_name": "MULTISET MULTIPLICATION", "block": "Mathematical Operators"}, {"symbol": "⊎", "unicode_codepoint": "U+228E", "unicode_name": "MULTISET UNION", "block": "Mathematical Operators"}, {"symbol": "⊏", "unicode_codepoint": "U+228F", "unicode_name": "SQUARE IMAGE OF", "block": "Mathematical Operators"}, {"symbol": "⊐", "unicode_codepoint": "U+2290", "unicode_name": "SQUARE ORIGINAL OF", "block": "Mathematical Operators"}, {"symbol": "⊑", "unicode_codepoint": "U+2291", "unicode_name": "SQUARE IMAGE OF OR EQUAL TO", "block": "Mathematical Operators"}, {"symbol": "⊒", "unicode_codepoint": "U+2292", "unicode_name": "SQUARE ORIGINAL OF OR EQUAL TO", "block": "Mathematical Operators"}, {"symbol": "⊓", "unicode_codepoint": "U+2293", "unicode_name": "SQUARE CAP", "block": "Mathematical Operators"}, {"symbol": "⊔", "unicode_codepoint": "U+2294", "unicode_name": "SQUARE CUP", "block": "Mathematical Operators"}, {"symbol": "⊕", "unicode_codepoint": "U+2295", "unicode_name": "CIRCLED PLUS", "block": "Mathematical Operators"}, {"symbol": "⊖", "unicode_codepoint": "U+2296", "unicode_name": "CIRCLED MINUS", "block": "Mathematical Operators"}, {"symbol": "⊗", "unicode_codepoint": "U+2297", "unicode_name": "CIRCLED TIMES", "block": "Mathematical Operators"}, {"symbol": "⊘", "unicode_codepoint": "U+2298", "unicode_name": "CIRCLED DIVISION SLASH", "block": "Mathematical Operators"}, {"symbol": "⊙", "unicode_codepoint": "U+2299", "unicode_name": "CIRCLED DOT OPERATOR", "block": "Mathematical Operators"}, {"symbol": "⊚", "unicode_codepoint": "U+229A", "unicode_name": "CIRCLED RING OPERATOR", "block": "Mathematical Operators"}, {"symbol": "⊛", "unicode_codepoint": "U+229B", "unicode_name": "CIRCLED ASTERISK OPERATOR", "block": "Mathematical Operators"}, {"symbol": "⊜", "unicode_codepoint": "U+229C", "unicode_name": "CIRCLED EQUALS", "block": "Mathematical Operators"}, {"symbol": "⊝", "unicode_codepoint": "U+229D", "unicode_name": "CIRCLED DASH", "block": "Mathematical Operators"}, {"symbol": "⊞", "unicode_codepoint": "U+229E", "unicode_name": "SQUARED PLUS", "block": "Mathematical Operators"}, {"symbol": "⊟", "unicode_codepoint": "U+229F", "unicode_name": "SQUARED MINUS", "block": "Mathematical Operators"}, {"symbol": "⊠", "unicode_codepoint": "U+22A0", "unicode_name": "SQUARED TIMES", "block": "Mathematical Operators"}, {"symbol": "⊡", "unicode_codepoint": "U+22A1", "unicode_name": "SQUARED DOT OPERATOR", "block": "Mathematical Operators"}, {"symbol": "⊢", "unicode_codepoint": "U+22A2", "unicode_name": "RIGHT TACK", "block": "Mathematical Operators"}, {"symbol": "⊣", "unicode_codepoint": "U+22A3", "unicode_name": "LEFT TACK", "block": "Mathematical Operators"}, {"symbol": "⊤", "unicode_codepoint": "U+22A4", "unicode_name": "DOWN TACK", "block": "Mathematical Operators"}, {"symbol": "⊥", "unicode_codepoint": "U+22A5", "unicode_name": "UP TACK", "block": "Mathematical Operators"}, {"symbol": "⊦", "unicode_codepoint": "U+22A6", "unicode_name": "ASSERTION", "block": "Mathematical Operators"}, {"symbol": "⊧", "unicode_codepoint": "U+22A7", "unicode_name": "MODELS", "block": "Mathematical Operators"}, {"symbol": "⊨", "unicode_codepoint": "U+22A8", "unicode_name": "TRUE", "block": "Mathematical Operators"}, {"symbol": "⊩", "unicode_codepoint": "U+22A9", "unicode_name": "FORCES", "block": "Mathematical Operators"}, {"symbol": "⊪", "unicode_codepoint": "U+22AA", "unicode_name": "TRIPLE VERTICAL BAR RIGHT TURNSTILE", "block": "Mathematical Operators"}, {"symbol": "⊫", "unicode_codepoint": "U+22AB", "unicode_name": "DOUBLE VERTICAL BAR DOUBLE RIGHT TURNSTILE", "block": "Mathematical Operators"}, {"symbol": "⊬", "unicode_codepoint": "U+22AC", "unicode_name": "DOES NOT PROVE", "block": "Mathematical Operators"}, {"symbol": "⊭", "unicode_codepoint": "U+22AD", "unicode_name": "NOT TRUE", "block": "Mathematical Operators"}, {"symbol": "⊮", "unicode_codepoint": "U+22AE", "unicode_name": "DOES NOT FORCE", "block": "Mathematical Operators"}, {"symbol": "⊯", "unicode_codepoint": "U+22AF", "unicode_name": "NEGATED DOUBLE VERTICAL BAR DOUBLE RIGHT TURNSTILE", "block": "Mathematical Operators"}, {"symbol": "⊰", "unicode_codepoint": "U+22B0", "unicode_name": "PRECEDES UNDER RELATION", "block": "Mathematical Operators"}, {"symbol": "⊱", "unicode_codepoint": "U+22B1", "unicode_name": "SUCCEEDS UNDER RELATION", "block": "Mathematical Operators"}, {"symbol": "⊲", "unicode_codepoint": "U+22B2", "unicode_name": "NORMAL SUBGROUP OF", "block": "Mathematical Operators"}, {"symbol": "⊳", "unicode_codepoint": "U+22B3", "unicode_name": "CONTAINS AS NORMAL SUBGROUP", "block": "Mathematical Operators"}, {"symbol": "⊴", "unicode_codepoint": "U+22B4", "unicode_name": "NORMAL SUBGROUP OF OR EQUAL TO", "block": "Mathematical Operators"}, {"symbol": "⊵", "unicode_codepoint": "U+22B5", "unicode_name": "CONTAINS AS NORMAL SUBGROUP OR EQUAL TO", "block": "Mathematical Operators"}, {"symbol": "⊶", "unicode_codepoint": "U+22B6", "unicode_name": "ORIGINAL OF", "block": "Mathematical Operators"}, {"symbol": "⊷", "unicode_codepoint": "U+22B7", "unicode_name": "IMAGE OF", "block": "Mathematical Operators"}, {"symbol": "⊸", "unicode_codepoint": "U+22B8", "unicode_name": "MULTIMAP", "block": "Mathematical Operators"}, {"symbol": "⊹", "unicode_codepoint": "U+22B9", "unicode_name": "HERMITIAN CONJUGATE MATRIX", "block": "Mathematical Operators"}, {"symbol": "⊺", "unicode_codepoint": "U+22BA", "unicode_name": "INTERCALATE", "block": "Mathematical Operators"}, {"symbol": "⊻", "unicode_codepoint": "U+22BB", "unicode_name": "XOR", "block": "Mathematical Operators"}, {"symbol": "⊼", "unicode_codepoint": "U+22BC", "unicode_name": "NAND", "block": "Mathematical Operators"}, {"symbol": "⊽", "unicode_codepoint": "U+22BD", "unicode_name": "NOR", "block": "Mathematical Operators"}, {"symbol": "⊾", "unicode_codepoint": "U+22BE", "unicode_name": "RIGHT ANGLE WITH ARC", "block": "Mathematical Operators"}, {"symbol": "⊿", "unicode_codepoint": "U+22BF", "unicode_name": "RIGHT TRIANGLE", "block": "Mathematical Operators"}, {"symbol": "⋀", "unicode_codepoint": "U+22C0", "unicode_name": "N-ARY LOGICAL AND", "block": "Mathematical Operators"}, {"symbol": "⋁", "unicode_codepoint": "U+22C1", "unicode_name": "N-ARY LOGICAL OR", "block": "Mathematical Operators"}, {"symbol": "⋂", "unicode_codepoint": "U+22C2", "unicode_name": "N-ARY INTERSECTION", "block": "Mathematical Operators"}, {"symbol": "⋃", "unicode_codepoint": "U+22C3", "unicode_name": "N-ARY UNION", "block": "Mathematical Operators"}, {"symbol": "⋄", "unicode_codepoint": "U+22C4", "unicode_name": "DIAMOND OPERATOR", "block": "Mathematical Operators"}, {"symbol": "⋅", "unicode_codepoint": "U+22C5", "unicode_name": "DOT OPERATOR", "block": "Mathematical Operators"}, {"symbol": "⋆", "unicode_codepoint": "U+22C6", "unicode_name": "STAR OPERATOR", "block": "Mathematical Operators"}, {"symbol": "⋇", "unicode_codepoint": "U+22C7", "unicode_name": "DIVISION TIMES", "block": "Mathematical Operators"}, {"symbol": "⋈", "unicode_codepoint": "U+22C8", "unicode_name": "BOWTIE", "block": "Mathematical Operators"}, {"symbol": "⋉", "unicode_codepoint": "U+22C9", "unicode_name": "LEFT NORMAL FACTOR SEMIDIRECT PRODUCT", "block": "Mathematical Operators"}, {"symbol": "⋊", "unicode_codepoint": "U+22CA", "unicode_name": "RIGHT NORMAL FACTOR SEMIDIRECT PRODUCT", "block": "Mathematical Operators"}, {"symbol": "⋋", "unicode_codepoint": "U+22CB", "unicode_name": "LEFT SEMIDIRECT PRODUCT", "block": "Mathematical Operators"}, {"symbol": "⋌", "unicode_codepoint": "U+22CC", "unicode_name": "RIGHT SEMIDIRECT PRODUCT", "block": "Mathematical Operators"}, {"symbol": "⋍", "unicode_codepoint": "U+22CD", "unicode_name": "REVERSED TILDE EQUALS", "block": "Mathematical Operators"}, {"symbol": "⋎", "unicode_codepoint": "U+22CE", "unicode_name": "CURLY LOGICAL OR", "block": "Mathematical Operators"}, {"symbol": "⋏", "unicode_codepoint": "U+22CF", "unicode_name": "CURLY LOGICAL AND", "block": "Mathematical Operators"}, {"symbol": "⋐", "unicode_codepoint": "U+22D0", "unicode_name": "DOUBLE SUBSET", "block": "Mathematical Operators"}, {"symbol": "⋑", "unicode_codepoint": "U+22D1", "unicode_name": "DOUBLE SUPERSET", "block": "Mathematical Operators"}, {"symbol": "⋒", "unicode_codepoint": "U+22D2", "unicode_name": "DOUBLE INTERSECTION", "block": "Mathematical Operators"}, {"symbol": "⋓", "unicode_codepoint": "U+22D3", "unicode_name": "DOUBLE UNION", "block": "Mathematical Operators"}, {"symbol": "⋔", "unicode_codepoint": "U+22D4", "unicode_name": "PITCHFORK", "block": "Mathematical Operators"}, {"symbol": "⋕", "unicode_codepoint": "U+22D5", "unicode_name": "EQUAL AND PARALLEL TO", "block": "Mathematical Operators"}, {"symbol": "⋖", "unicode_codepoint": "U+22D6", "unicode_name": "LESS-THAN WITH DOT", "block": "Mathematical Operators"}, {"symbol": "⋗", "unicode_codepoint": "U+22D7", "unicode_name": "GREATER-THAN WITH DOT", "block": "Mathematical Operators"}, {"symbol": "⋘", "unicode_codepoint": "U+22D8", "unicode_name": "VERY MUCH LESS-THAN", "block": "Mathematical Operators"}, {"symbol": "⋙", "unicode_codepoint": "U+22D9", "unicode_name": "VERY MUCH GREATER-THAN", "block": "Mathematical Operators"}, {"symbol": "⋚", "unicode_codepoint": "U+22DA", "unicode_name": "LESS-<PERSON>HA<PERSON> EQUAL TO OR GREATER-THAN", "block": "Mathematical Operators"}, {"symbol": "⋛", "unicode_codepoint": "U+22DB", "unicode_name": "GREATER-<PERSON>HA<PERSON> EQUAL TO OR LESS-THAN", "block": "Mathematical Operators"}, {"symbol": "⋜", "unicode_codepoint": "U+22DC", "unicode_name": "EQUAL TO OR LESS-THAN", "block": "Mathematical Operators"}, {"symbol": "⋝", "unicode_codepoint": "U+22DD", "unicode_name": "EQUAL TO OR GREATER-THAN", "block": "Mathematical Operators"}, {"symbol": "⋞", "unicode_codepoint": "U+22DE", "unicode_name": "EQUAL TO OR PRECEDES", "block": "Mathematical Operators"}, {"symbol": "⋟", "unicode_codepoint": "U+22DF", "unicode_name": "EQUAL TO OR SUCCEEDS", "block": "Mathematical Operators"}, {"symbol": "⋠", "unicode_codepoint": "U+22E0", "unicode_name": "DOES NOT PRECEDE OR EQUAL", "block": "Mathematical Operators"}, {"symbol": "⋡", "unicode_codepoint": "U+22E1", "unicode_name": "DOES NOT SUCCEED OR EQUAL", "block": "Mathematical Operators"}, {"symbol": "⋢", "unicode_codepoint": "U+22E2", "unicode_name": "NOT SQUARE IMAGE OF OR EQUAL TO", "block": "Mathematical Operators"}, {"symbol": "⋣", "unicode_codepoint": "U+22E3", "unicode_name": "NOT SQUARE ORIGINAL OF OR EQUAL TO", "block": "Mathematical Operators"}, {"symbol": "⋤", "unicode_codepoint": "U+22E4", "unicode_name": "SQUARE IMAGE OF OR NOT EQUAL TO", "block": "Mathematical Operators"}, {"symbol": "⋥", "unicode_codepoint": "U+22E5", "unicode_name": "SQUARE ORIGINAL OF OR NOT EQUAL TO", "block": "Mathematical Operators"}, {"symbol": "⋦", "unicode_codepoint": "U+22E6", "unicode_name": "LESS-THAN BUT NOT EQUIVALENT TO", "block": "Mathematical Operators"}, {"symbol": "⋧", "unicode_codepoint": "U+22E7", "unicode_name": "GREATER-THAN BUT NOT EQUIVALENT TO", "block": "Mathematical Operators"}, {"symbol": "⋨", "unicode_codepoint": "U+22E8", "unicode_name": "PRECEDES BUT NOT EQUIVALENT TO", "block": "Mathematical Operators"}, {"symbol": "⋩", "unicode_codepoint": "U+22E9", "unicode_name": "SUCCEEDS BUT NOT EQUIVALENT TO", "block": "Mathematical Operators"}, {"symbol": "⋪", "unicode_codepoint": "U+22EA", "unicode_name": "NOT NORMAL SUBGROUP OF", "block": "Mathematical Operators"}, {"symbol": "⋫", "unicode_codepoint": "U+22EB", "unicode_name": "DOES NOT CONTAIN AS NORMAL SUBGROUP", "block": "Mathematical Operators"}, {"symbol": "⋬", "unicode_codepoint": "U+22EC", "unicode_name": "NOT NORMAL SUBGROUP OF OR EQUAL TO", "block": "Mathematical Operators"}, {"symbol": "⋭", "unicode_codepoint": "U+22ED", "unicode_name": "DOES NOT CONTAIN AS NORMAL SUBGROUP OR EQUAL", "block": "Mathematical Operators"}, {"symbol": "⋮", "unicode_codepoint": "U+22EE", "unicode_name": "VERTICAL ELLIPSIS", "block": "Mathematical Operators"}, {"symbol": "⋯", "unicode_codepoint": "U+22EF", "unicode_name": "MIDLINE HORIZONTAL ELLIPSIS", "block": "Mathematical Operators"}, {"symbol": "⋰", "unicode_codepoint": "U+22F0", "unicode_name": "UP RIGHT DIAGONAL ELLIPSIS", "block": "Mathematical Operators"}, {"symbol": "⋱", "unicode_codepoint": "U+22F1", "unicode_name": "DOWN RIGHT DIAGONAL ELLIPSIS", "block": "Mathematical Operators"}, {"symbol": "⋲", "unicode_codepoint": "U+22F2", "unicode_name": "ELEMENT OF WITH LONG HORIZONTAL STROKE", "block": "Mathematical Operators"}, {"symbol": "⋳", "unicode_codepoint": "U+22F3", "unicode_name": "ELEMENT OF WITH VERTICAL BAR AT END OF HORIZONTAL STROKE", "block": "Mathematical Operators"}, {"symbol": "⋴", "unicode_codepoint": "U+22F4", "unicode_name": "SMALL ELEMENT OF WITH VERTICAL BAR AT END OF HORIZONTAL STROKE", "block": "Mathematical Operators"}, {"symbol": "⋵", "unicode_codepoint": "U+22F5", "unicode_name": "ELEMENT OF WITH DOT ABOVE", "block": "Mathematical Operators"}, {"symbol": "⋶", "unicode_codepoint": "U+22F6", "unicode_name": "ELEMENT OF WITH OVERBAR", "block": "Mathematical Operators"}, {"symbol": "⋷", "unicode_codepoint": "U+22F7", "unicode_name": "SMALL ELEMENT OF WITH OVERBAR", "block": "Mathematical Operators"}, {"symbol": "⋸", "unicode_codepoint": "U+22F8", "unicode_name": "ELEMENT OF WITH UNDERBAR", "block": "Mathematical Operators"}, {"symbol": "⋹", "unicode_codepoint": "U+22F9", "unicode_name": "ELEMENT OF WITH TWO HORIZONTAL STROKES", "block": "Mathematical Operators"}, {"symbol": "⋺", "unicode_codepoint": "U+22FA", "unicode_name": "CONTAINS WITH LONG HORIZONTAL STROKE", "block": "Mathematical Operators"}, {"symbol": "⋻", "unicode_codepoint": "U+22FB", "unicode_name": "CONTAINS WITH VERTICAL BAR AT END OF HORIZONTAL STROKE", "block": "Mathematical Operators"}, {"symbol": "⋼", "unicode_codepoint": "U+22FC", "unicode_name": "SMALL CONTAINS WITH VERTICAL BAR AT END OF HORIZONTAL STROKE", "block": "Mathematical Operators"}, {"symbol": "⋽", "unicode_codepoint": "U+22FD", "unicode_name": "CONTAINS WITH OVERBAR", "block": "Mathematical Operators"}, {"symbol": "⋾", "unicode_codepoint": "U+22FE", "unicode_name": "SMALL CONTAINS WITH OVERBAR", "block": "Mathematical Operators"}, {"symbol": "⋿", "unicode_codepoint": "U+22FF", "unicode_name": "Z NOTATION BAG MEMBERSHIP", "block": "Mathematical Operators"}, {"symbol": "←", "unicode_codepoint": "U+2190", "unicode_name": "LEFTWARDS ARROW", "block": "Arrows"}, {"symbol": "↑", "unicode_codepoint": "U+2191", "unicode_name": "UPWARDS ARROW", "block": "Arrows"}, {"symbol": "→", "unicode_codepoint": "U+2192", "unicode_name": "RIGHTWARDS ARROW", "block": "Arrows"}, {"symbol": "↓", "unicode_codepoint": "U+2193", "unicode_name": "DOWNWARDS ARROW", "block": "Arrows"}, {"symbol": "↔", "unicode_codepoint": "U+2194", "unicode_name": "LEFT RIGHT ARROW", "block": "Arrows"}, {"symbol": "↕", "unicode_codepoint": "U+2195", "unicode_name": "UP DOWN ARROW", "block": "Arrows"}, {"symbol": "↖", "unicode_codepoint": "U+2196", "unicode_name": "NORTH WEST ARROW", "block": "Arrows"}, {"symbol": "↗", "unicode_codepoint": "U+2197", "unicode_name": "NORTH EAST ARROW", "block": "Arrows"}, {"symbol": "↘", "unicode_codepoint": "U+2198", "unicode_name": "SOUTH EAST ARROW", "block": "Arrows"}, {"symbol": "↙", "unicode_codepoint": "U+2199", "unicode_name": "SOUTH WEST ARROW", "block": "Arrows"}, {"symbol": "↚", "unicode_codepoint": "U+219A", "unicode_name": "LEFTWARDS ARROW WITH STROKE", "block": "Arrows"}, {"symbol": "↛", "unicode_codepoint": "U+219B", "unicode_name": "RIGHTWARDS ARROW WITH STROKE", "block": "Arrows"}, {"symbol": "↜", "unicode_codepoint": "U+219C", "unicode_name": "LEFTWARDS WAVE ARROW", "block": "Arrows"}, {"symbol": "↝", "unicode_codepoint": "U+219D", "unicode_name": "RIGHTWARDS WAVE ARROW", "block": "Arrows"}, {"symbol": "↞", "unicode_codepoint": "U+219E", "unicode_name": "LEFTWARDS TWO HEADED ARROW", "block": "Arrows"}, {"symbol": "↟", "unicode_codepoint": "U+219F", "unicode_name": "UPWARDS TWO HEADED ARROW", "block": "Arrows"}, {"symbol": "↠", "unicode_codepoint": "U+21A0", "unicode_name": "RIGHTWARDS TWO HEADED ARROW", "block": "Arrows"}, {"symbol": "↡", "unicode_codepoint": "U+21A1", "unicode_name": "DOWNWARDS TWO HEADED ARROW", "block": "Arrows"}, {"symbol": "↢", "unicode_codepoint": "U+21A2", "unicode_name": "LEFTWARDS ARROW WITH TAIL", "block": "Arrows"}, {"symbol": "↣", "unicode_codepoint": "U+21A3", "unicode_name": "RIGHTWARDS ARROW WITH TAIL", "block": "Arrows"}, {"symbol": "↤", "unicode_codepoint": "U+21A4", "unicode_name": "LEFTWARDS ARROW FROM BAR", "block": "Arrows"}, {"symbol": "↥", "unicode_codepoint": "U+21A5", "unicode_name": "UPWARDS ARROW FROM BAR", "block": "Arrows"}, {"symbol": "↦", "unicode_codepoint": "U+21A6", "unicode_name": "RIGHTWARDS ARROW FROM BAR", "block": "Arrows"}, {"symbol": "↧", "unicode_codepoint": "U+21A7", "unicode_name": "DOWNWARDS ARROW FROM BAR", "block": "Arrows"}, {"symbol": "↨", "unicode_codepoint": "U+21A8", "unicode_name": "UP DOWN ARROW WITH BASE", "block": "Arrows"}, {"symbol": "↩", "unicode_codepoint": "U+21A9", "unicode_name": "LEFTWARDS ARROW WITH HOOK", "block": "Arrows"}, {"symbol": "↪", "unicode_codepoint": "U+21AA", "unicode_name": "RIGHTWARDS ARROW WITH HOOK", "block": "Arrows"}, {"symbol": "↫", "unicode_codepoint": "U+21AB", "unicode_name": "LEFTWARDS ARROW WITH LOOP", "block": "Arrows"}, {"symbol": "↬", "unicode_codepoint": "U+21AC", "unicode_name": "RIGHTWARDS ARROW WITH LOOP", "block": "Arrows"}, {"symbol": "↭", "unicode_codepoint": "U+21AD", "unicode_name": "LEFT RIGHT WAVE ARROW", "block": "Arrows"}, {"symbol": "↮", "unicode_codepoint": "U+21AE", "unicode_name": "LEFT RIGHT ARROW WITH STROKE", "block": "Arrows"}, {"symbol": "↯", "unicode_codepoint": "U+21AF", "unicode_name": "DOWNWARDS ZIGZAG ARROW", "block": "Arrows"}, {"symbol": "↰", "unicode_codepoint": "U+21B0", "unicode_name": "UPWARDS ARROW WITH TIP LEFTWARDS", "block": "Arrows"}, {"symbol": "↱", "unicode_codepoint": "U+21B1", "unicode_name": "UPWARDS ARROW WITH TIP RIGHTWARDS", "block": "Arrows"}, {"symbol": "↲", "unicode_codepoint": "U+21B2", "unicode_name": "DOWNWARDS ARROW WITH TIP LEFTWARDS", "block": "Arrows"}, {"symbol": "↳", "unicode_codepoint": "U+21B3", "unicode_name": "DOWNWARDS ARROW WITH TIP RIGHTWARDS", "block": "Arrows"}, {"symbol": "↴", "unicode_codepoint": "U+21B4", "unicode_name": "RIGHTWARDS ARROW WITH CORNER DOWNWARDS", "block": "Arrows"}, {"symbol": "↵", "unicode_codepoint": "U+21B5", "unicode_name": "DOWNWARDS ARROW WITH CORNER LEFTWARDS", "block": "Arrows"}, {"symbol": "↶", "unicode_codepoint": "U+21B6", "unicode_name": "ANTICLOCKWISE TOP SEMICIRCLE ARROW", "block": "Arrows"}, {"symbol": "↷", "unicode_codepoint": "U+21B7", "unicode_name": "CLOCKWISE TOP SEMICIRCLE ARROW", "block": "Arrows"}, {"symbol": "↸", "unicode_codepoint": "U+21B8", "unicode_name": "NORTH WEST ARROW TO LONG BAR", "block": "Arrows"}, {"symbol": "↹", "unicode_codepoint": "U+21B9", "unicode_name": "LEFTWARDS ARROW TO BAR OVER RIGHTWARDS ARROW TO BAR", "block": "Arrows"}, {"symbol": "↺", "unicode_codepoint": "U+21BA", "unicode_name": "ANTICLOCKWISE OPEN CIRCLE ARROW", "block": "Arrows"}, {"symbol": "↻", "unicode_codepoint": "U+21BB", "unicode_name": "CLOCKWISE OPEN CIRCLE ARROW", "block": "Arrows"}, {"symbol": "↼", "unicode_codepoint": "U+21BC", "unicode_name": "LEFTWARDS HARPOON WITH BARB UPWARDS", "block": "Arrows"}, {"symbol": "↽", "unicode_codepoint": "U+21BD", "unicode_name": "LEFTWARDS HARPOON WITH BARB DOWNWARDS", "block": "Arrows"}, {"symbol": "↾", "unicode_codepoint": "U+21BE", "unicode_name": "UPWARDS HARPOON WITH BARB RIGHTWARDS", "block": "Arrows"}, {"symbol": "↿", "unicode_codepoint": "U+21BF", "unicode_name": "UPWARDS HARPOON WITH BARB LEFTWARDS", "block": "Arrows"}, {"symbol": "⇀", "unicode_codepoint": "U+21C0", "unicode_name": "RIGHTWARDS HARPOON WITH BARB UPWARDS", "block": "Arrows"}, {"symbol": "⇁", "unicode_codepoint": "U+21C1", "unicode_name": "RIGHTWARDS HARPOON WITH BARB DOWNWARDS", "block": "Arrows"}, {"symbol": "⇂", "unicode_codepoint": "U+21C2", "unicode_name": "DOWNWARDS HARPOON WITH BARB RIGHTWARDS", "block": "Arrows"}, {"symbol": "⇃", "unicode_codepoint": "U+21C3", "unicode_name": "DOWNWARDS HARPOON WITH BARB LEFTWARDS", "block": "Arrows"}, {"symbol": "⇄", "unicode_codepoint": "U+21C4", "unicode_name": "RIGHTWARDS ARROW OVER LEFTWARDS ARROW", "block": "Arrows"}, {"symbol": "⇅", "unicode_codepoint": "U+21C5", "unicode_name": "UPWARDS ARROW LEFTWARDS OF DOWNWARDS ARROW", "block": "Arrows"}, {"symbol": "⇆", "unicode_codepoint": "U+21C6", "unicode_name": "LEFTWARDS ARROW OVER RIGHTWARDS ARROW", "block": "Arrows"}, {"symbol": "⇇", "unicode_codepoint": "U+21C7", "unicode_name": "LEFTWARDS PAIRED ARROWS", "block": "Arrows"}, {"symbol": "⇈", "unicode_codepoint": "U+21C8", "unicode_name": "UPWARDS PAIRED ARROWS", "block": "Arrows"}, {"symbol": "⇉", "unicode_codepoint": "U+21C9", "unicode_name": "RIGHTWARDS PAIRED ARROWS", "block": "Arrows"}, {"symbol": "⇊", "unicode_codepoint": "U+21CA", "unicode_name": "DOWNWARDS PAIRED ARROWS", "block": "Arrows"}, {"symbol": "⇋", "unicode_codepoint": "U+21CB", "unicode_name": "LEFTWARDS HARPOON OVER RIGHTWARDS HARPOON", "block": "Arrows"}, {"symbol": "⇌", "unicode_codepoint": "U+21CC", "unicode_name": "RIGHTWARDS HARPOON OVER LEFTWARDS HARPOON", "block": "Arrows"}, {"symbol": "⇍", "unicode_codepoint": "U+21CD", "unicode_name": "LEFTWARDS DOUBLE ARROW WITH STROKE", "block": "Arrows"}, {"symbol": "⇎", "unicode_codepoint": "U+21CE", "unicode_name": "LEFT RIGHT DOUBLE ARROW WITH STROKE", "block": "Arrows"}, {"symbol": "⇏", "unicode_codepoint": "U+21CF", "unicode_name": "RIGHTWARDS DOUBLE ARROW WITH STROKE", "block": "Arrows"}, {"symbol": "⇐", "unicode_codepoint": "U+21D0", "unicode_name": "LEFTWARDS DOUBLE ARROW", "block": "Arrows"}, {"symbol": "⇑", "unicode_codepoint": "U+21D1", "unicode_name": "UPWARDS DOUBLE ARROW", "block": "Arrows"}, {"symbol": "⇒", "unicode_codepoint": "U+21D2", "unicode_name": "RIGHTWARDS DOUBLE ARROW", "block": "Arrows"}, {"symbol": "⇓", "unicode_codepoint": "U+21D3", "unicode_name": "DOWNWARDS DOUBLE ARROW", "block": "Arrows"}, {"symbol": "⇔", "unicode_codepoint": "U+21D4", "unicode_name": "LEFT RIGHT DOUBLE ARROW", "block": "Arrows"}, {"symbol": "⇕", "unicode_codepoint": "U+21D5", "unicode_name": "UP DOWN DOUBLE ARROW", "block": "Arrows"}, {"symbol": "⇖", "unicode_codepoint": "U+21D6", "unicode_name": "NORTH WEST DOUBLE ARROW", "block": "Arrows"}, {"symbol": "⇗", "unicode_codepoint": "U+21D7", "unicode_name": "NORTH EAST DOUBLE ARROW", "block": "Arrows"}, {"symbol": "⇘", "unicode_codepoint": "U+21D8", "unicode_name": "SOUTH EAST DOUBLE ARROW", "block": "Arrows"}, {"symbol": "⇙", "unicode_codepoint": "U+21D9", "unicode_name": "SOUTH WEST DOUBLE ARROW", "block": "Arrows"}, {"symbol": "⇚", "unicode_codepoint": "U+21DA", "unicode_name": "LEFTWARDS TRIPLE ARROW", "block": "Arrows"}, {"symbol": "⇛", "unicode_codepoint": "U+21DB", "unicode_name": "RIGHTWARDS TRIPLE ARROW", "block": "Arrows"}, {"symbol": "⇜", "unicode_codepoint": "U+21DC", "unicode_name": "LEFTWARDS SQUIGGLE ARROW", "block": "Arrows"}, {"symbol": "⇝", "unicode_codepoint": "U+21DD", "unicode_name": "RIGHTWARDS SQUIGGLE ARROW", "block": "Arrows"}, {"symbol": "⇞", "unicode_codepoint": "U+21DE", "unicode_name": "UPWARDS ARROW WITH DOUBLE STROKE", "block": "Arrows"}, {"symbol": "⇟", "unicode_codepoint": "U+21DF", "unicode_name": "DOWNWARDS ARROW WITH DOUBLE STROKE", "block": "Arrows"}, {"symbol": "⇠", "unicode_codepoint": "U+21E0", "unicode_name": "LEFTWARDS DASHED ARROW", "block": "Arrows"}, {"symbol": "⇡", "unicode_codepoint": "U+21E1", "unicode_name": "UPWARDS DASHED ARROW", "block": "Arrows"}, {"symbol": "⇢", "unicode_codepoint": "U+21E2", "unicode_name": "RIGHTWARDS DASHED ARROW", "block": "Arrows"}, {"symbol": "⇣", "unicode_codepoint": "U+21E3", "unicode_name": "DOWNWARDS DASHED ARROW", "block": "Arrows"}, {"symbol": "⇤", "unicode_codepoint": "U+21E4", "unicode_name": "LEFTWARDS ARROW TO BAR", "block": "Arrows"}, {"symbol": "⇥", "unicode_codepoint": "U+21E5", "unicode_name": "RIGHTWARDS ARROW TO BAR", "block": "Arrows"}, {"symbol": "⇦", "unicode_codepoint": "U+21E6", "unicode_name": "LEFTWARDS WHITE ARROW", "block": "Arrows"}, {"symbol": "⇧", "unicode_codepoint": "U+21E7", "unicode_name": "UPWARDS WHITE ARROW", "block": "Arrows"}, {"symbol": "⇨", "unicode_codepoint": "U+21E8", "unicode_name": "RIGHTWARDS WHITE ARROW", "block": "Arrows"}, {"symbol": "⇩", "unicode_codepoint": "U+21E9", "unicode_name": "DOWNWARDS WHITE ARROW", "block": "Arrows"}, {"symbol": "⇪", "unicode_codepoint": "U+21EA", "unicode_name": "UPWARDS WHITE ARROW FROM BAR", "block": "Arrows"}, {"symbol": "⇫", "unicode_codepoint": "U+21EB", "unicode_name": "UPWARDS WHITE ARROW ON PEDESTAL", "block": "Arrows"}, {"symbol": "⇬", "unicode_codepoint": "U+21EC", "unicode_name": "UPWARDS WHITE ARROW ON PEDESTAL WITH HORIZONTAL BAR", "block": "Arrows"}, {"symbol": "⇭", "unicode_codepoint": "U+21ED", "unicode_name": "UPWARDS WHITE ARROW ON PEDESTAL WITH VERTICAL BAR", "block": "Arrows"}, {"symbol": "⇮", "unicode_codepoint": "U+21EE", "unicode_name": "UPWARDS WHITE DOUBLE ARROW", "block": "Arrows"}, {"symbol": "⇯", "unicode_codepoint": "U+21EF", "unicode_name": "UPWARDS WHITE DOUBLE ARROW ON PEDESTAL", "block": "Arrows"}, {"symbol": "⇰", "unicode_codepoint": "U+21F0", "unicode_name": "RIGHTWARDS WHITE ARROW FROM WALL", "block": "Arrows"}, {"symbol": "⇱", "unicode_codepoint": "U+21F1", "unicode_name": "NORTH WEST ARROW TO CORNER", "block": "Arrows"}, {"symbol": "⇲", "unicode_codepoint": "U+21F2", "unicode_name": "SOUTH EAST ARROW TO CORNER", "block": "Arrows"}, {"symbol": "⇳", "unicode_codepoint": "U+21F3", "unicode_name": "UP DOWN WHITE ARROW", "block": "Arrows"}, {"symbol": "⇴", "unicode_codepoint": "U+21F4", "unicode_name": "RIGHT ARROW WITH SMALL CIRCLE", "block": "Arrows"}, {"symbol": "⇵", "unicode_codepoint": "U+21F5", "unicode_name": "DOWNWARDS ARROW LEFTWARDS OF UPWARDS ARROW", "block": "Arrows"}, {"symbol": "⇶", "unicode_codepoint": "U+21F6", "unicode_name": "THREE RIGHTWARDS ARROWS", "block": "Arrows"}, {"symbol": "⇷", "unicode_codepoint": "U+21F7", "unicode_name": "LEFTWARDS ARROW WITH VERTICAL STROKE", "block": "Arrows"}, {"symbol": "⇸", "unicode_codepoint": "U+21F8", "unicode_name": "RIGHTWARDS ARROW WITH VERTICAL STROKE", "block": "Arrows"}, {"symbol": "⇹", "unicode_codepoint": "U+21F9", "unicode_name": "LEFT RIGHT ARROW WITH VERTICAL STROKE", "block": "Arrows"}, {"symbol": "⇺", "unicode_codepoint": "U+21FA", "unicode_name": "LEFTWARDS ARROW WITH DOUBLE VERTICAL STROKE", "block": "Arrows"}, {"symbol": "⇻", "unicode_codepoint": "U+21FB", "unicode_name": "RIGHTWARDS ARROW WITH DOUBLE VERTICAL STROKE", "block": "Arrows"}, {"symbol": "⇼", "unicode_codepoint": "U+21FC", "unicode_name": "LEFT RIGHT ARROW WITH DOUBLE VERTICAL STROKE", "block": "Arrows"}, {"symbol": "⇽", "unicode_codepoint": "U+21FD", "unicode_name": "LEFTWARDS OPEN-HEADED ARROW", "block": "Arrows"}, {"symbol": "⇾", "unicode_codepoint": "U+21FE", "unicode_name": "RIGHTWARDS OPEN-HEADED ARROW", "block": "Arrows"}, {"symbol": "⇿", "unicode_codepoint": "U+21FF", "unicode_name": "LEFT RIGHT OPEN-HEADED ARROW", "block": "Arrows"}, {"symbol": "■", "unicode_codepoint": "U+25A0", "unicode_name": "BLACK SQUARE", "block": "Geometric Shapes"}, {"symbol": "□", "unicode_codepoint": "U+25A1", "unicode_name": "WHITE SQUARE", "block": "Geometric Shapes"}, {"symbol": "▢", "unicode_codepoint": "U+25A2", "unicode_name": "WHITE SQUARE WITH ROUNDED CORNERS", "block": "Geometric Shapes"}, {"symbol": "▣", "unicode_codepoint": "U+25A3", "unicode_name": "WHITE SQUARE CONTAINING BLACK SMALL SQUARE", "block": "Geometric Shapes"}, {"symbol": "▤", "unicode_codepoint": "U+25A4", "unicode_name": "SQUARE WITH HORIZONTAL FILL", "block": "Geometric Shapes"}, {"symbol": "▥", "unicode_codepoint": "U+25A5", "unicode_name": "SQUARE WITH VERTICAL FILL", "block": "Geometric Shapes"}, {"symbol": "▦", "unicode_codepoint": "U+25A6", "unicode_name": "SQUARE WITH ORTHOGONAL CROSSHATCH FILL", "block": "Geometric Shapes"}, {"symbol": "▧", "unicode_codepoint": "U+25A7", "unicode_name": "SQUARE WITH UPPER LEFT TO LOWER RIGHT FILL", "block": "Geometric Shapes"}, {"symbol": "▨", "unicode_codepoint": "U+25A8", "unicode_name": "SQUARE WITH UPPER RIGHT TO LOWER LEFT FILL", "block": "Geometric Shapes"}, {"symbol": "▩", "unicode_codepoint": "U+25A9", "unicode_name": "SQUARE WITH DIAGONAL CROSSHATCH FILL", "block": "Geometric Shapes"}, {"symbol": "▪", "unicode_codepoint": "U+25AA", "unicode_name": "BLACK SMALL SQUARE", "block": "Geometric Shapes"}, {"symbol": "▫", "unicode_codepoint": "U+25AB", "unicode_name": "WHITE SMALL SQUARE", "block": "Geometric Shapes"}, {"symbol": "▬", "unicode_codepoint": "U+25AC", "unicode_name": "BLACK RECTANGLE", "block": "Geometric Shapes"}, {"symbol": "▭", "unicode_codepoint": "U+25AD", "unicode_name": "WHITE RECTANGLE", "block": "Geometric Shapes"}, {"symbol": "▮", "unicode_codepoint": "U+25AE", "unicode_name": "BLACK VERTICAL RECTANGLE", "block": "Geometric Shapes"}, {"symbol": "▯", "unicode_codepoint": "U+25AF", "unicode_name": "WHITE VERTICAL RECTANGLE", "block": "Geometric Shapes"}, {"symbol": "▰", "unicode_codepoint": "U+25B0", "unicode_name": "BLACK PARALLELOGRAM", "block": "Geometric Shapes"}, {"symbol": "▱", "unicode_codepoint": "U+25B1", "unicode_name": "WHITE PARALLELOGRAM", "block": "Geometric Shapes"}, {"symbol": "▲", "unicode_codepoint": "U+25B2", "unicode_name": "BLACK UP-POINTING TRIANGLE", "block": "Geometric Shapes"}, {"symbol": "△", "unicode_codepoint": "U+25B3", "unicode_name": "WHITE UP-POINTING TRIANGLE", "block": "Geometric Shapes"}, {"symbol": "▴", "unicode_codepoint": "U+25B4", "unicode_name": "BLACK UP-POINTING SMALL TRIANGLE", "block": "Geometric Shapes"}, {"symbol": "▵", "unicode_codepoint": "U+25B5", "unicode_name": "WHITE UP-POINTING SMALL TRIANGLE", "block": "Geometric Shapes"}, {"symbol": "▶", "unicode_codepoint": "U+25B6", "unicode_name": "BLACK RIGHT-POINTING TRIANGLE", "block": "Geometric Shapes"}, {"symbol": "▷", "unicode_codepoint": "U+25B7", "unicode_name": "WHITE RIGHT-POINTING TRIANGLE", "block": "Geometric Shapes"}, {"symbol": "▸", "unicode_codepoint": "U+25B8", "unicode_name": "BLACK RIGHT-POINTING SMALL TRIANGLE", "block": "Geometric Shapes"}, {"symbol": "▹", "unicode_codepoint": "U+25B9", "unicode_name": "WHITE RIGHT-POINTING SMALL TRIANGLE", "block": "Geometric Shapes"}, {"symbol": "►", "unicode_codepoint": "U+25BA", "unicode_name": "BLACK RIGHT-POINTING POINTER", "block": "Geometric Shapes"}, {"symbol": "▻", "unicode_codepoint": "U+25BB", "unicode_name": "WHITE RIGHT-POINTING POINTER", "block": "Geometric Shapes"}, {"symbol": "▼", "unicode_codepoint": "U+25BC", "unicode_name": "BLACK DOWN-POINTING TRIANGLE", "block": "Geometric Shapes"}, {"symbol": "▽", "unicode_codepoint": "U+25BD", "unicode_name": "WHITE DOWN-POINTING TRIANGLE", "block": "Geometric Shapes"}, {"symbol": "▾", "unicode_codepoint": "U+25BE", "unicode_name": "BLACK DOWN-POINTING SMALL TRIANGLE", "block": "Geometric Shapes"}, {"symbol": "▿", "unicode_codepoint": "U+25BF", "unicode_name": "WHITE DOWN-POINTING SMALL TRIANGLE", "block": "Geometric Shapes"}, {"symbol": "◀", "unicode_codepoint": "U+25C0", "unicode_name": "BLACK LEFT-POINTING TRIANGLE", "block": "Geometric Shapes"}, {"symbol": "◁", "unicode_codepoint": "U+25C1", "unicode_name": "WHITE LEFT-POINTING TRIANGLE", "block": "Geometric Shapes"}, {"symbol": "◂", "unicode_codepoint": "U+25C2", "unicode_name": "BLACK LEFT-POINTING SMALL TRIANGLE", "block": "Geometric Shapes"}, {"symbol": "◃", "unicode_codepoint": "U+25C3", "unicode_name": "WHITE LEFT-POINTING SMALL TRIANGLE", "block": "Geometric Shapes"}, {"symbol": "◄", "unicode_codepoint": "U+25C4", "unicode_name": "BLACK LEFT-POINTING POINTER", "block": "Geometric Shapes"}, {"symbol": "◅", "unicode_codepoint": "U+25C5", "unicode_name": "WHITE LEFT-POINTING POINTER", "block": "Geometric Shapes"}, {"symbol": "◆", "unicode_codepoint": "U+25C6", "unicode_name": "BLACK DIAMOND", "block": "Geometric Shapes"}, {"symbol": "◇", "unicode_codepoint": "U+25C7", "unicode_name": "WHITE DIAMOND", "block": "Geometric Shapes"}, {"symbol": "◈", "unicode_codepoint": "U+25C8", "unicode_name": "WHITE DIAMOND CONTAINING BLACK SMALL DIAMOND", "block": "Geometric Shapes"}, {"symbol": "◉", "unicode_codepoint": "U+25C9", "unicode_name": "FISHEYE", "block": "Geometric Shapes"}, {"symbol": "◊", "unicode_codepoint": "U+25CA", "unicode_name": "LOZENGE", "block": "Geometric Shapes"}, {"symbol": "○", "unicode_codepoint": "U+25CB", "unicode_name": "WHITE CIRCLE", "block": "Geometric Shapes"}, {"symbol": "◌", "unicode_codepoint": "U+25CC", "unicode_name": "DOTTED CIRCLE", "block": "Geometric Shapes"}, {"symbol": "◍", "unicode_codepoint": "U+25CD", "unicode_name": "CIRCLE WITH VERTICAL FILL", "block": "Geometric Shapes"}, {"symbol": "◎", "unicode_codepoint": "U+25CE", "unicode_name": "BULLSEYE", "block": "Geometric Shapes"}, {"symbol": "●", "unicode_codepoint": "U+25CF", "unicode_name": "BLACK CIRCLE", "block": "Geometric Shapes"}, {"symbol": "◐", "unicode_codepoint": "U+25D0", "unicode_name": "CIRCLE WITH LEFT HALF BLACK", "block": "Geometric Shapes"}, {"symbol": "◑", "unicode_codepoint": "U+25D1", "unicode_name": "CIRCLE WITH RIGHT HALF BLACK", "block": "Geometric Shapes"}, {"symbol": "◒", "unicode_codepoint": "U+25D2", "unicode_name": "CIRCLE WITH LOWER HALF BLACK", "block": "Geometric Shapes"}, {"symbol": "◓", "unicode_codepoint": "U+25D3", "unicode_name": "CIRCLE WITH UPPER HALF BLACK", "block": "Geometric Shapes"}, {"symbol": "◔", "unicode_codepoint": "U+25D4", "unicode_name": "CIRCLE WITH UPPER RIGHT QUADRANT BLACK", "block": "Geometric Shapes"}, {"symbol": "◕", "unicode_codepoint": "U+25D5", "unicode_name": "CIRCLE WITH ALL BUT UPPER LEFT QUADRANT BLACK", "block": "Geometric Shapes"}, {"symbol": "◖", "unicode_codepoint": "U+25D6", "unicode_name": "LEFT HALF BLACK CIRCLE", "block": "Geometric Shapes"}, {"symbol": "◗", "unicode_codepoint": "U+25D7", "unicode_name": "RIGHT HALF BLACK CIRCLE", "block": "Geometric Shapes"}, {"symbol": "◘", "unicode_codepoint": "U+25D8", "unicode_name": "INVERSE BULLET", "block": "Geometric Shapes"}, {"symbol": "◙", "unicode_codepoint": "U+25D9", "unicode_name": "INVERSE WHITE CIRCLE", "block": "Geometric Shapes"}, {"symbol": "◚", "unicode_codepoint": "U+25DA", "unicode_name": "UPPER HALF INVERSE WHITE CIRCLE", "block": "Geometric Shapes"}, {"symbol": "◛", "unicode_codepoint": "U+25DB", "unicode_name": "LOWER HALF INVERSE WHITE CIRCLE", "block": "Geometric Shapes"}, {"symbol": "◜", "unicode_codepoint": "U+25DC", "unicode_name": "UPPER LEFT QUADRANT CIRCULAR ARC", "block": "Geometric Shapes"}, {"symbol": "◝", "unicode_codepoint": "U+25DD", "unicode_name": "UPPER RIGHT QUADRANT CIRCULAR ARC", "block": "Geometric Shapes"}, {"symbol": "◞", "unicode_codepoint": "U+25DE", "unicode_name": "LOWER RIGHT QUADRANT CIRCULAR ARC", "block": "Geometric Shapes"}, {"symbol": "◟", "unicode_codepoint": "U+25DF", "unicode_name": "LOWER LEFT QUADRANT CIRCULAR ARC", "block": "Geometric Shapes"}, {"symbol": "◠", "unicode_codepoint": "U+25E0", "unicode_name": "UPPER HALF CIRCLE", "block": "Geometric Shapes"}, {"symbol": "◡", "unicode_codepoint": "U+25E1", "unicode_name": "LOWER HALF CIRCLE", "block": "Geometric Shapes"}, {"symbol": "◢", "unicode_codepoint": "U+25E2", "unicode_name": "BLACK LOWER RIGHT TRIANGLE", "block": "Geometric Shapes"}, {"symbol": "◣", "unicode_codepoint": "U+25E3", "unicode_name": "BLACK LOWER LEFT TRIANGLE", "block": "Geometric Shapes"}, {"symbol": "◤", "unicode_codepoint": "U+25E4", "unicode_name": "BLACK UPPER LEFT TRIANGLE", "block": "Geometric Shapes"}, {"symbol": "◥", "unicode_codepoint": "U+25E5", "unicode_name": "BLACK UPPER RIGHT TRIANGLE", "block": "Geometric Shapes"}, {"symbol": "◦", "unicode_codepoint": "U+25E6", "unicode_name": "WHITE BULLET", "block": "Geometric Shapes"}, {"symbol": "◧", "unicode_codepoint": "U+25E7", "unicode_name": "SQUARE WITH LEFT HALF BLACK", "block": "Geometric Shapes"}, {"symbol": "◨", "unicode_codepoint": "U+25E8", "unicode_name": "SQUARE WITH RIGHT HALF BLACK", "block": "Geometric Shapes"}, {"symbol": "◩", "unicode_codepoint": "U+25E9", "unicode_name": "SQUARE WITH UPPER LEFT DIAGONAL HALF BLACK", "block": "Geometric Shapes"}, {"symbol": "◪", "unicode_codepoint": "U+25EA", "unicode_name": "SQUARE WITH LOWER RIGHT DIAGONAL HALF BLACK", "block": "Geometric Shapes"}, {"symbol": "◫", "unicode_codepoint": "U+25EB", "unicode_name": "WHITE SQUARE WITH VERTICAL BISECTING LINE", "block": "Geometric Shapes"}, {"symbol": "◬", "unicode_codepoint": "U+25EC", "unicode_name": "WHITE UP-POINTING TRIAN<PERSON>LE WITH DOT", "block": "Geometric Shapes"}, {"symbol": "◭", "unicode_codepoint": "U+25ED", "unicode_name": "UP-POINTING T<PERSON><PERSON><PERSON><PERSON> WITH LEFT HALF BLACK", "block": "Geometric Shapes"}, {"symbol": "◮", "unicode_codepoint": "U+25EE", "unicode_name": "UP-POINTING T<PERSON><PERSON><PERSON><PERSON> WITH RIGHT HALF BLACK", "block": "Geometric Shapes"}, {"symbol": "◯", "unicode_codepoint": "U+25EF", "unicode_name": "LARGE CIRCLE", "block": "Geometric Shapes"}, {"symbol": "◰", "unicode_codepoint": "U+25F0", "unicode_name": "WHITE SQUARE WITH UPPER LEFT QUADRANT", "block": "Geometric Shapes"}, {"symbol": "◱", "unicode_codepoint": "U+25F1", "unicode_name": "WHITE SQUARE WITH LOWER LEFT QUADRANT", "block": "Geometric Shapes"}, {"symbol": "◲", "unicode_codepoint": "U+25F2", "unicode_name": "WHITE SQUARE WITH LOWER RIGHT QUADRANT", "block": "Geometric Shapes"}, {"symbol": "◳", "unicode_codepoint": "U+25F3", "unicode_name": "WHITE SQUARE WITH UPPER RIGHT QUADRANT", "block": "Geometric Shapes"}, {"symbol": "◴", "unicode_codepoint": "U+25F4", "unicode_name": "WHITE CIRCLE WITH UPPER LEFT QUADRANT", "block": "Geometric Shapes"}, {"symbol": "◵", "unicode_codepoint": "U+25F5", "unicode_name": "WHITE CIRCLE WITH LOWER LEFT QUADRANT", "block": "Geometric Shapes"}, {"symbol": "◶", "unicode_codepoint": "U+25F6", "unicode_name": "WHITE CIRCLE WITH LOWER RIGHT QUADRANT", "block": "Geometric Shapes"}, {"symbol": "◷", "unicode_codepoint": "U+25F7", "unicode_name": "WHITE CIRCLE WITH UPPER RIGHT QUADRANT", "block": "Geometric Shapes"}, {"symbol": "◸", "unicode_codepoint": "U+25F8", "unicode_name": "UPPER LEFT TRIANGLE", "block": "Geometric Shapes"}, {"symbol": "◹", "unicode_codepoint": "U+25F9", "unicode_name": "UPPER RIGHT TRIANGLE", "block": "Geometric Shapes"}, {"symbol": "◺", "unicode_codepoint": "U+25FA", "unicode_name": "LOWER LEFT TRIANGLE", "block": "Geometric Shapes"}, {"symbol": "◻", "unicode_codepoint": "U+25FB", "unicode_name": "WHITE MEDIUM SQUARE", "block": "Geometric Shapes"}, {"symbol": "◼", "unicode_codepoint": "U+25FC", "unicode_name": "BLACK MEDIUM SQUARE", "block": "Geometric Shapes"}, {"symbol": "◽", "unicode_codepoint": "U+25FD", "unicode_name": "WHITE MEDIUM SMALL SQUARE", "block": "Geometric Shapes"}, {"symbol": "◾", "unicode_codepoint": "U+25FE", "unicode_name": "BLACK MEDIUM SMALL SQUARE", "block": "Geometric Shapes"}, {"symbol": "◿", "unicode_codepoint": "U+25FF", "unicode_name": "LOWER RIGHT TRIANGLE", "block": "Geometric Shapes"}, {"symbol": "℀", "unicode_codepoint": "U+2100", "unicode_name": "ACCOUNT OF", "block": "Letterlike Symbols"}, {"symbol": "℁", "unicode_codepoint": "U+2101", "unicode_name": "ADDRESSED TO THE SUBJECT", "block": "Letterlike Symbols"}, {"symbol": "ℂ", "unicode_codepoint": "U+2102", "unicode_name": "DOUBLE-STRUCK CAPITAL C", "block": "Letterlike Symbols"}, {"symbol": "℃", "unicode_codepoint": "U+2103", "unicode_name": "DEGREE CELSIUS", "block": "Letterlike Symbols"}, {"symbol": "℄", "unicode_codepoint": "U+2104", "unicode_name": "CENTRE LINE SYMBOL", "block": "Letterlike Symbols"}, {"symbol": "℅", "unicode_codepoint": "U+2105", "unicode_name": "CARE OF", "block": "Letterlike Symbols"}, {"symbol": "℆", "unicode_codepoint": "U+2106", "unicode_name": "CADA UNA", "block": "Letterlike Symbols"}, {"symbol": "ℇ", "unicode_codepoint": "U+2107", "unicode_name": "EULER CONSTANT", "block": "Letterlike Symbols"}, {"symbol": "℈", "unicode_codepoint": "U+2108", "unicode_name": "SCRUPLE", "block": "Letterlike Symbols"}, {"symbol": "℉", "unicode_codepoint": "U+2109", "unicode_name": "DEGREE FAHRENHEIT", "block": "Letterlike Symbols"}, {"symbol": "ℊ", "unicode_codepoint": "U+210A", "unicode_name": "SCRIPT SMALL G", "block": "Letterlike Symbols"}, {"symbol": "ℋ", "unicode_codepoint": "U+210B", "unicode_name": "SCRIPT CAPITAL H", "block": "Letterlike Symbols"}, {"symbol": "ℌ", "unicode_codepoint": "U+210C", "unicode_name": "BLACK-LETTER CAPITAL H", "block": "Letterlike Symbols"}, {"symbol": "ℍ", "unicode_codepoint": "U+210D", "unicode_name": "DOUBLE-STRUCK CAPITAL H", "block": "Letterlike Symbols"}, {"symbol": "ℎ", "unicode_codepoint": "U+210E", "unicode_name": "PLANCK CONSTANT", "block": "Letterlike Symbols"}, {"symbol": "ℏ", "unicode_codepoint": "U+210F", "unicode_name": "PLANCK CONSTANT OVER TWO PI", "block": "Letterlike Symbols"}, {"symbol": "ℐ", "unicode_codepoint": "U+2110", "unicode_name": "SCRIPT CAPITAL I", "block": "Letterlike Symbols"}, {"symbol": "ℑ", "unicode_codepoint": "U+2111", "unicode_name": "BLACK-LETTER CAPITAL I", "block": "Letterlike Symbols"}, {"symbol": "ℒ", "unicode_codepoint": "U+2112", "unicode_name": "SCRIPT CAPITAL L", "block": "Letterlike Symbols"}, {"symbol": "ℓ", "unicode_codepoint": "U+2113", "unicode_name": "SCRIPT SMALL L", "block": "Letterlike Symbols"}, {"symbol": "℔", "unicode_codepoint": "U+2114", "unicode_name": "L B BAR SYMBOL", "block": "Letterlike Symbols"}, {"symbol": "ℕ", "unicode_codepoint": "U+2115", "unicode_name": "DOUBLE-STRUCK CAPITAL N", "block": "Letterlike Symbols"}, {"symbol": "№", "unicode_codepoint": "U+2116", "unicode_name": "NUMERO SIGN", "block": "Letterlike Symbols"}, {"symbol": "℗", "unicode_codepoint": "U+2117", "unicode_name": "SOUND RECORDING COPYRIGHT", "block": "Letterlike Symbols"}, {"symbol": "℘", "unicode_codepoint": "U+2118", "unicode_name": "SCRIPT CAPITAL P", "block": "Letterlike Symbols"}, {"symbol": "ℙ", "unicode_codepoint": "U+2119", "unicode_name": "DOUBLE-STRUCK CAPITAL P", "block": "Letterlike Symbols"}, {"symbol": "ℚ", "unicode_codepoint": "U+211A", "unicode_name": "DOUBLE-STRUCK CAPITAL Q", "block": "Letterlike Symbols"}, {"symbol": "ℛ", "unicode_codepoint": "U+211B", "unicode_name": "SCRIPT CAPITAL R", "block": "Letterlike Symbols"}, {"symbol": "ℜ", "unicode_codepoint": "U+211C", "unicode_name": "BLACK-LETTER CAPITAL R", "block": "Letterlike Symbols"}, {"symbol": "ℝ", "unicode_codepoint": "U+211D", "unicode_name": "DOUBLE-STRUCK CAPITAL R", "block": "Letterlike Symbols"}, {"symbol": "℞", "unicode_codepoint": "U+211E", "unicode_name": "PRESCRIPTION TAKE", "block": "Letterlike Symbols"}, {"symbol": "℟", "unicode_codepoint": "U+211F", "unicode_name": "RESPONSE", "block": "Letterlike Symbols"}, {"symbol": "℠", "unicode_codepoint": "U+2120", "unicode_name": "SERVICE MARK", "block": "Letterlike Symbols"}, {"symbol": "℡", "unicode_codepoint": "U+2121", "unicode_name": "TELEPHONE SIGN", "block": "Letterlike Symbols"}, {"symbol": "™", "unicode_codepoint": "U+2122", "unicode_name": "TRADE MARK SIGN", "block": "Letterlike Symbols"}, {"symbol": "℣", "unicode_codepoint": "U+2123", "unicode_name": "VERSICLE", "block": "Letterlike Symbols"}, {"symbol": "ℤ", "unicode_codepoint": "U+2124", "unicode_name": "DOUBLE-STRUCK CAPITAL Z", "block": "Letterlike Symbols"}, {"symbol": "℥", "unicode_codepoint": "U+2125", "unicode_name": "OUNCE SIGN", "block": "Letterlike Symbols"}, {"symbol": "Ω", "unicode_codepoint": "U+2126", "unicode_name": "OHM SIGN", "block": "Letterlike Symbols"}, {"symbol": "℧", "unicode_codepoint": "U+2127", "unicode_name": "INVERTED OHM SIGN", "block": "Letterlike Symbols"}, {"symbol": "ℨ", "unicode_codepoint": "U+2128", "unicode_name": "BLACK-LETTER CAPITAL Z", "block": "Letterlike Symbols"}, {"symbol": "℩", "unicode_codepoint": "U+2129", "unicode_name": "TURNED GREEK SMALL LETTER IOTA", "block": "Letterlike Symbols"}, {"symbol": "K", "unicode_codepoint": "U+212A", "unicode_name": "KELVIN SIGN", "block": "Letterlike Symbols"}, {"symbol": "Å", "unicode_codepoint": "U+212B", "unicode_name": "ANGSTROM SIGN", "block": "Letterlike Symbols"}, {"symbol": "ℬ", "unicode_codepoint": "U+212C", "unicode_name": "SCRIPT CAPITAL B", "block": "Letterlike Symbols"}, {"symbol": "ℭ", "unicode_codepoint": "U+212D", "unicode_name": "BLACK-LETTER CAPITAL C", "block": "Letterlike Symbols"}, {"symbol": "℮", "unicode_codepoint": "U+212E", "unicode_name": "ESTIMATED SYMBOL", "block": "Letterlike Symbols"}, {"symbol": "ℯ", "unicode_codepoint": "U+212F", "unicode_name": "SCRIPT SMALL E", "block": "Letterlike Symbols"}, {"symbol": "ℰ", "unicode_codepoint": "U+2130", "unicode_name": "SCRIPT CAPITAL E", "block": "Letterlike Symbols"}, {"symbol": "ℱ", "unicode_codepoint": "U+2131", "unicode_name": "SCRIPT CAPITAL F", "block": "Letterlike Symbols"}, {"symbol": "Ⅎ", "unicode_codepoint": "U+2132", "unicode_name": "TURNED CAPITAL F", "block": "Letterlike Symbols"}, {"symbol": "ℳ", "unicode_codepoint": "U+2133", "unicode_name": "SCRIPT CAPITAL M", "block": "Letterlike Symbols"}, {"symbol": "ℴ", "unicode_codepoint": "U+2134", "unicode_name": "SCRIPT SMALL O", "block": "Letterlike Symbols"}, {"symbol": "ℵ", "unicode_codepoint": "U+2135", "unicode_name": "ALEF SYMBOL", "block": "Letterlike Symbols"}, {"symbol": "ℶ", "unicode_codepoint": "U+2136", "unicode_name": "BET SYMBOL", "block": "Letterlike Symbols"}, {"symbol": "ℷ", "unicode_codepoint": "U+2137", "unicode_name": "GIMEL SYMBOL", "block": "Letterlike Symbols"}, {"symbol": "ℸ", "unicode_codepoint": "U+2138", "unicode_name": "DALET SYMBOL", "block": "Letterlike Symbols"}, {"symbol": "ℹ", "unicode_codepoint": "U+2139", "unicode_name": "INFORMATION SOURCE", "block": "Letterlike Symbols"}, {"symbol": "℺", "unicode_codepoint": "U+213A", "unicode_name": "ROTATED CAPITAL Q", "block": "Letterlike Symbols"}, {"symbol": "℻", "unicode_codepoint": "U+213B", "unicode_name": "FACSIMILE SIGN", "block": "Letterlike Symbols"}, {"symbol": "ℼ", "unicode_codepoint": "U+213C", "unicode_name": "DOUBLE-STRUCK SMALL PI", "block": "Letterlike Symbols"}, {"symbol": "ℽ", "unicode_codepoint": "U+213D", "unicode_name": "DOUBLE-STRUCK SMALL GAMMA", "block": "Letterlike Symbols"}, {"symbol": "ℾ", "unicode_codepoint": "U+213E", "unicode_name": "DOUBLE-STRUCK CAPITAL GAMMA", "block": "Letterlike Symbols"}, {"symbol": "ℿ", "unicode_codepoint": "U+213F", "unicode_name": "DOUBLE-STRUCK CAPITAL PI", "block": "Letterlike Symbols"}, {"symbol": "⅀", "unicode_codepoint": "U+2140", "unicode_name": "DOUBLE-STRUCK N-ARY SUMMATION", "block": "Letterlike Symbols"}, {"symbol": "⅁", "unicode_codepoint": "U+2141", "unicode_name": "TURNED SANS-SERIF CAPITAL G", "block": "Letterlike Symbols"}, {"symbol": "⅂", "unicode_codepoint": "U+2142", "unicode_name": "TURNED SANS-SERIF CAPITAL L", "block": "Letterlike Symbols"}, {"symbol": "⅃", "unicode_codepoint": "U+2143", "unicode_name": "REVERSED SANS-SERIF CAPITAL L", "block": "Letterlike Symbols"}, {"symbol": "⅄", "unicode_codepoint": "U+2144", "unicode_name": "TURNED SANS-SERIF CAPITAL Y", "block": "Letterlike Symbols"}, {"symbol": "ⅅ", "unicode_codepoint": "U+2145", "unicode_name": "DOUBLE-STRUCK ITALIC CAPITAL D", "block": "Letterlike Symbols"}, {"symbol": "ⅆ", "unicode_codepoint": "U+2146", "unicode_name": "DOUBLE-STRUCK ITALIC SMALL D", "block": "Letterlike Symbols"}, {"symbol": "ⅇ", "unicode_codepoint": "U+2147", "unicode_name": "DOUBLE-STRUCK ITALIC SMALL E", "block": "Letterlike Symbols"}, {"symbol": "ⅈ", "unicode_codepoint": "U+2148", "unicode_name": "DOUBLE-STRUCK ITALIC SMALL I", "block": "Letterlike Symbols"}, {"symbol": "ⅉ", "unicode_codepoint": "U+2149", "unicode_name": "DOUBLE-STRUCK ITALIC SMALL J", "block": "Letterlike Symbols"}, {"symbol": "⅊", "unicode_codepoint": "U+214A", "unicode_name": "PROPERTY LINE", "block": "Letterlike Symbols"}, {"symbol": "⅋", "unicode_codepoint": "U+214B", "unicode_name": "TURNED AMPERSAND", "block": "Letterlike Symbols"}, {"symbol": "⅌", "unicode_codepoint": "U+214C", "unicode_name": "PER SIGN", "block": "Letterlike Symbols"}, {"symbol": "⅍", "unicode_codepoint": "U+214D", "unicode_name": "AKTIESELSKAB", "block": "Letterlike Symbols"}, {"symbol": "ⅎ", "unicode_codepoint": "U+214E", "unicode_name": "TURNED SMALL F", "block": "Letterlike Symbols"}, {"symbol": "⅏", "unicode_codepoint": "U+214F", "unicode_name": "SYMBOL FOR SAMARITAN SOURCE", "block": "Letterlike Symbols"}, {"symbol": "⌀", "unicode_codepoint": "U+2300", "unicode_name": "DIAMETER SIGN", "block": "Miscellaneous Technical"}, {"symbol": "⌁", "unicode_codepoint": "U+2301", "unicode_name": "ELECTRIC ARROW", "block": "Miscellaneous Technical"}, {"symbol": "⌂", "unicode_codepoint": "U+2302", "unicode_name": "HOUSE", "block": "Miscellaneous Technical"}, {"symbol": "⌃", "unicode_codepoint": "U+2303", "unicode_name": "UP ARROWHEAD", "block": "Miscellaneous Technical"}, {"symbol": "⌄", "unicode_codepoint": "U+2304", "unicode_name": "DOWN ARROWHEAD", "block": "Miscellaneous Technical"}, {"symbol": "⌅", "unicode_codepoint": "U+2305", "unicode_name": "PROJECTIVE", "block": "Miscellaneous Technical"}, {"symbol": "⌆", "unicode_codepoint": "U+2306", "unicode_name": "PERSPECTIVE", "block": "Miscellaneous Technical"}, {"symbol": "⌇", "unicode_codepoint": "U+2307", "unicode_name": "WAVY LINE", "block": "Miscellaneous Technical"}, {"symbol": "⌈", "unicode_codepoint": "U+2308", "unicode_name": "LEFT CEILING", "block": "Miscellaneous Technical"}, {"symbol": "⌉", "unicode_codepoint": "U+2309", "unicode_name": "RIGHT CEILING", "block": "Miscellaneous Technical"}, {"symbol": "⌊", "unicode_codepoint": "U+230A", "unicode_name": "LEFT FLOOR", "block": "Miscellaneous Technical"}, {"symbol": "⌋", "unicode_codepoint": "U+230B", "unicode_name": "RIGHT FLOOR", "block": "Miscellaneous Technical"}, {"symbol": "⌌", "unicode_codepoint": "U+230C", "unicode_name": "BOTTOM RIGHT CROP", "block": "Miscellaneous Technical"}, {"symbol": "⌍", "unicode_codepoint": "U+230D", "unicode_name": "BOTTOM LEFT CROP", "block": "Miscellaneous Technical"}, {"symbol": "⌎", "unicode_codepoint": "U+230E", "unicode_name": "TOP RIGHT CROP", "block": "Miscellaneous Technical"}, {"symbol": "⌏", "unicode_codepoint": "U+230F", "unicode_name": "TOP LEFT CROP", "block": "Miscellaneous Technical"}, {"symbol": "⌐", "unicode_codepoint": "U+2310", "unicode_name": "REVERSED NOT SIGN", "block": "Miscellaneous Technical"}, {"symbol": "⌑", "unicode_codepoint": "U+2311", "unicode_name": "SQUARE LOZENGE", "block": "Miscellaneous Technical"}, {"symbol": "⌒", "unicode_codepoint": "U+2312", "unicode_name": "ARC", "block": "Miscellaneous Technical"}, {"symbol": "⌓", "unicode_codepoint": "U+2313", "unicode_name": "SEGMENT", "block": "Miscellaneous Technical"}, {"symbol": "⌔", "unicode_codepoint": "U+2314", "unicode_name": "SECTOR", "block": "Miscellaneous Technical"}, {"symbol": "⌕", "unicode_codepoint": "U+2315", "unicode_name": "TELEPHONE RECORDER", "block": "Miscellaneous Technical"}, {"symbol": "⌖", "unicode_codepoint": "U+2316", "unicode_name": "POSITION INDICATOR", "block": "Miscellaneous Technical"}, {"symbol": "⌗", "unicode_codepoint": "U+2317", "unicode_name": "VIEWDATA SQUARE", "block": "Miscellaneous Technical"}, {"symbol": "⌘", "unicode_codepoint": "U+2318", "unicode_name": "PLACE OF INTEREST SIGN", "block": "Miscellaneous Technical"}, {"symbol": "⌙", "unicode_codepoint": "U+2319", "unicode_name": "TURNED NOT SIGN", "block": "Miscellaneous Technical"}, {"symbol": "⌚", "unicode_codepoint": "U+231A", "unicode_name": "WATCH", "block": "Miscellaneous Technical"}, {"symbol": "⌛", "unicode_codepoint": "U+231B", "unicode_name": "HOURGLASS", "block": "Miscellaneous Technical"}, {"symbol": "⌜", "unicode_codepoint": "U+231C", "unicode_name": "TOP LEFT CORNER", "block": "Miscellaneous Technical"}, {"symbol": "⌝", "unicode_codepoint": "U+231D", "unicode_name": "TOP RIGHT CORNER", "block": "Miscellaneous Technical"}, {"symbol": "⌞", "unicode_codepoint": "U+231E", "unicode_name": "BOTTOM LEFT CORNER", "block": "Miscellaneous Technical"}, {"symbol": "⌟", "unicode_codepoint": "U+231F", "unicode_name": "BOTTOM RIGHT CORNER", "block": "Miscellaneous Technical"}, {"symbol": "⌠", "unicode_codepoint": "U+2320", "unicode_name": "TOP HALF INTEGRAL", "block": "Miscellaneous Technical"}, {"symbol": "⌡", "unicode_codepoint": "U+2321", "unicode_name": "BOTTOM HALF INTEGRAL", "block": "Miscellaneous Technical"}, {"symbol": "⌢", "unicode_codepoint": "U+2322", "unicode_name": "FROWN", "block": "Miscellaneous Technical"}, {"symbol": "⌣", "unicode_codepoint": "U+2323", "unicode_name": "SMILE", "block": "Miscellaneous Technical"}, {"symbol": "⌤", "unicode_codepoint": "U+2324", "unicode_name": "UP ARROWHEAD BET<PERSON>EEN TWO HORIZONTAL BARS", "block": "Miscellaneous Technical"}, {"symbol": "⌥", "unicode_codepoint": "U+2325", "unicode_name": "OPTION KEY", "block": "Miscellaneous Technical"}, {"symbol": "⌦", "unicode_codepoint": "U+2326", "unicode_name": "ERASE TO THE RIGHT", "block": "Miscellaneous Technical"}, {"symbol": "⌧", "unicode_codepoint": "U+2327", "unicode_name": "X IN A RECTANGLE BOX", "block": "Miscellaneous Technical"}, {"symbol": "⌨", "unicode_codepoint": "U+2328", "unicode_name": "KEYBOARD", "block": "Miscellaneous Technical"}, {"symbol": "〈", "unicode_codepoint": "U+2329", "unicode_name": "LEFT-POINTING ANGLE BRACKET", "block": "Miscellaneous Technical"}, {"symbol": "〉", "unicode_codepoint": "U+232A", "unicode_name": "RIGHT-POINTING ANG<PERSON> BRACKET", "block": "Miscellaneous Technical"}, {"symbol": "⌫", "unicode_codepoint": "U+232B", "unicode_name": "ERASE TO THE LEFT", "block": "Miscellaneous Technical"}, {"symbol": "⌬", "unicode_codepoint": "U+232C", "unicode_name": "BENZENE RING", "block": "Miscellaneous Technical"}, {"symbol": "⌭", "unicode_codepoint": "U+232D", "unicode_name": "CYLINDRICITY", "block": "Miscellaneous Technical"}, {"symbol": "⌮", "unicode_codepoint": "U+232E", "unicode_name": "ALL AROUND-PROFILE", "block": "Miscellaneous Technical"}, {"symbol": "⌯", "unicode_codepoint": "U+232F", "unicode_name": "SYMMETRY", "block": "Miscellaneous Technical"}, {"symbol": "⌰", "unicode_codepoint": "U+2330", "unicode_name": "TOTAL RUNOUT", "block": "Miscellaneous Technical"}, {"symbol": "⌱", "unicode_codepoint": "U+2331", "unicode_name": "DIMENSION ORIGIN", "block": "Miscellaneous Technical"}, {"symbol": "⌲", "unicode_codepoint": "U+2332", "unicode_name": "CONICAL TAPER", "block": "Miscellaneous Technical"}, {"symbol": "⌳", "unicode_codepoint": "U+2333", "unicode_name": "SLOPE", "block": "Miscellaneous Technical"}, {"symbol": "⌴", "unicode_codepoint": "U+2334", "unicode_name": "COUNTERBORE", "block": "Miscellaneous Technical"}, {"symbol": "⌵", "unicode_codepoint": "U+2335", "unicode_name": "COUNTERSINK", "block": "Miscellaneous Technical"}, {"symbol": "⌶", "unicode_codepoint": "U+2336", "unicode_name": "APL FUNCTIONAL SYMBOL I-BEAM", "block": "Miscellaneous Technical"}, {"symbol": "⌷", "unicode_codepoint": "U+2337", "unicode_name": "APL FUNCTIONAL SYMBOL SQUISH QUAD", "block": "Miscellaneous Technical"}, {"symbol": "⌸", "unicode_codepoint": "U+2338", "unicode_name": "APL FUNCTIONAL SYMBOL QUAD EQUAL", "block": "Miscellaneous Technical"}, {"symbol": "⌹", "unicode_codepoint": "U+2339", "unicode_name": "APL FUNCTIONAL SYMBOL QUAD DIVIDE", "block": "Miscellaneous Technical"}, {"symbol": "⌺", "unicode_codepoint": "U+233A", "unicode_name": "APL FUNCTIONAL SYMBOL QUAD DIAMOND", "block": "Miscellaneous Technical"}, {"symbol": "⌻", "unicode_codepoint": "U+233B", "unicode_name": "APL FUNCTIONAL SYMBOL QUAD JOT", "block": "Miscellaneous Technical"}, {"symbol": "⌼", "unicode_codepoint": "U+233C", "unicode_name": "APL FUNCTIONAL SYMBOL QUAD CIRCLE", "block": "Miscellaneous Technical"}, {"symbol": "⌽", "unicode_codepoint": "U+233D", "unicode_name": "APL FUNCTIONAL SYMBOL CIRCLE STILE", "block": "Miscellaneous Technical"}, {"symbol": "⌾", "unicode_codepoint": "U+233E", "unicode_name": "APL FUNCTIONAL SYMBOL CIRCLE JOT", "block": "Miscellaneous Technical"}, {"symbol": "⌿", "unicode_codepoint": "U+233F", "unicode_name": "APL FUNCTIONAL SYMBOL SLASH BAR", "block": "Miscellaneous Technical"}, {"symbol": "⍀", "unicode_codepoint": "U+2340", "unicode_name": "APL FUNCTIONAL SYMBOL BACKSLASH BAR", "block": "Miscellaneous Technical"}, {"symbol": "⍁", "unicode_codepoint": "U+2341", "unicode_name": "APL FUNCTIONAL SYMBOL QUAD SLASH", "block": "Miscellaneous Technical"}, {"symbol": "⍂", "unicode_codepoint": "U+2342", "unicode_name": "APL FUNCTIONAL SYMBOL QUAD BACKSLASH", "block": "Miscellaneous Technical"}, {"symbol": "⍃", "unicode_codepoint": "U+2343", "unicode_name": "APL FUNCTIONAL SYMBOL QUAD LESS-THAN", "block": "Miscellaneous Technical"}, {"symbol": "⍄", "unicode_codepoint": "U+2344", "unicode_name": "APL FUNCTIONAL SYMBOL QUAD GREATER-THAN", "block": "Miscellaneous Technical"}, {"symbol": "⍅", "unicode_codepoint": "U+2345", "unicode_name": "APL FUNCTIONAL SYMBOL LEFTWARDS VANE", "block": "Miscellaneous Technical"}, {"symbol": "⍆", "unicode_codepoint": "U+2346", "unicode_name": "APL FUNCTIONAL SYMBOL RIGHTWARDS VANE", "block": "Miscellaneous Technical"}, {"symbol": "⍇", "unicode_codepoint": "U+2347", "unicode_name": "APL FUNCTIONAL SYMBOL QUAD LEFTWARDS ARROW", "block": "Miscellaneous Technical"}, {"symbol": "⍈", "unicode_codepoint": "U+2348", "unicode_name": "APL FUNCTIONAL SYMBOL QUAD RIGHTWARDS ARROW", "block": "Miscellaneous Technical"}, {"symbol": "⍉", "unicode_codepoint": "U+2349", "unicode_name": "APL FUNCTIONAL SYMBOL CIRCLE BACKSLASH", "block": "Miscellaneous Technical"}, {"symbol": "⍊", "unicode_codepoint": "U+234A", "unicode_name": "APL FUNCTIONAL SYMBOL DOWN TACK UNDERBAR", "block": "Miscellaneous Technical"}, {"symbol": "⍋", "unicode_codepoint": "U+234B", "unicode_name": "APL FUNCTIONAL SYMBOL DELTA STILE", "block": "Miscellaneous Technical"}, {"symbol": "⍌", "unicode_codepoint": "U+234C", "unicode_name": "APL FUNCTIONAL SYMBOL QUAD DOWN CARET", "block": "Miscellaneous Technical"}, {"symbol": "⍍", "unicode_codepoint": "U+234D", "unicode_name": "APL FUNCTIONAL SYMBOL QUAD DELTA", "block": "Miscellaneous Technical"}, {"symbol": "⍎", "unicode_codepoint": "U+234E", "unicode_name": "APL FUNCTIONAL SYMBOL DOWN TACK JOT", "block": "Miscellaneous Technical"}, {"symbol": "⍏", "unicode_codepoint": "U+234F", "unicode_name": "APL FUNCTIONAL SYMBOL UPWARDS VANE", "block": "Miscellaneous Technical"}, {"symbol": "⍐", "unicode_codepoint": "U+2350", "unicode_name": "APL FUNCTIONAL SYMBOL QUAD UPWARDS ARROW", "block": "Miscellaneous Technical"}, {"symbol": "⍑", "unicode_codepoint": "U+2351", "unicode_name": "APL FUNCTIONAL SYMBOL UP TACK OVERBAR", "block": "Miscellaneous Technical"}, {"symbol": "⍒", "unicode_codepoint": "U+2352", "unicode_name": "APL FUNCTIONAL SYMBOL DEL STILE", "block": "Miscellaneous Technical"}, {"symbol": "⍓", "unicode_codepoint": "U+2353", "unicode_name": "APL FUNCTIONAL SYMBOL QUAD UP CARET", "block": "Miscellaneous Technical"}, {"symbol": "⍔", "unicode_codepoint": "U+2354", "unicode_name": "APL FUNCTIONAL SYMBOL QUAD DEL", "block": "Miscellaneous Technical"}, {"symbol": "⍕", "unicode_codepoint": "U+2355", "unicode_name": "APL FUNCTIONAL SYMBOL UP TACK JOT", "block": "Miscellaneous Technical"}, {"symbol": "⍖", "unicode_codepoint": "U+2356", "unicode_name": "APL FUNCTIONAL SYMBOL DOWNWARDS VANE", "block": "Miscellaneous Technical"}, {"symbol": "⍗", "unicode_codepoint": "U+2357", "unicode_name": "APL FUNCTIONAL SYMBOL QUAD DOWNWARDS ARROW", "block": "Miscellaneous Technical"}, {"symbol": "⍘", "unicode_codepoint": "U+2358", "unicode_name": "APL FUNCTIONAL SYMBOL QUOTE UNDERBAR", "block": "Miscellaneous Technical"}, {"symbol": "⍙", "unicode_codepoint": "U+2359", "unicode_name": "APL FUNCTIONAL SYMBOL DELTA UNDERBAR", "block": "Miscellaneous Technical"}, {"symbol": "⍚", "unicode_codepoint": "U+235A", "unicode_name": "APL FUNCTIONAL SYMBOL DIAMOND UNDERBAR", "block": "Miscellaneous Technical"}, {"symbol": "⍛", "unicode_codepoint": "U+235B", "unicode_name": "APL FUNCTIONAL SYMBOL JOT UNDERBAR", "block": "Miscellaneous Technical"}, {"symbol": "⍜", "unicode_codepoint": "U+235C", "unicode_name": "APL FUNCTIONAL SYMBOL CIRCLE UNDERBAR", "block": "Miscellaneous Technical"}, {"symbol": "⍝", "unicode_codepoint": "U+235D", "unicode_name": "APL FUNCTIONAL SYMBOL UP SHOE JOT", "block": "Miscellaneous Technical"}, {"symbol": "⍞", "unicode_codepoint": "U+235E", "unicode_name": "APL FUNCTIONAL SYMBOL QUOTE QUAD", "block": "Miscellaneous Technical"}, {"symbol": "⍟", "unicode_codepoint": "U+235F", "unicode_name": "APL FUNCTIONAL SYMBOL CIRCLE STAR", "block": "Miscellaneous Technical"}, {"symbol": "⍠", "unicode_codepoint": "U+2360", "unicode_name": "APL FUNCTIONAL SYMBOL QUAD COLON", "block": "Miscellaneous Technical"}, {"symbol": "⍡", "unicode_codepoint": "U+2361", "unicode_name": "APL FUNCTIONAL SYMBOL UP TACK DIAERESIS", "block": "Miscellaneous Technical"}, {"symbol": "⍢", "unicode_codepoint": "U+2362", "unicode_name": "APL FUNCTIONAL SYMBOL DEL DIAERESIS", "block": "Miscellaneous Technical"}, {"symbol": "⍣", "unicode_codepoint": "U+2363", "unicode_name": "APL FUNCTIONAL SYMBOL STAR DIAERESIS", "block": "Miscellaneous Technical"}, {"symbol": "⍤", "unicode_codepoint": "U+2364", "unicode_name": "APL FUNCTIONAL SYMBOL JOT DIAERESIS", "block": "Miscellaneous Technical"}, {"symbol": "⍥", "unicode_codepoint": "U+2365", "unicode_name": "APL FUNCTIONAL SYMBOL CIRCLE DIAERESIS", "block": "Miscellaneous Technical"}, {"symbol": "⍦", "unicode_codepoint": "U+2366", "unicode_name": "APL FUNCTIONAL SYMBOL DOWN SHOE STILE", "block": "Miscellaneous Technical"}, {"symbol": "⍧", "unicode_codepoint": "U+2367", "unicode_name": "APL FUNCTIONAL SYMBOL LEFT SHOE STILE", "block": "Miscellaneous Technical"}, {"symbol": "⍨", "unicode_codepoint": "U+2368", "unicode_name": "APL FUNCTIONAL SYMBOL TILDE DIAERESIS", "block": "Miscellaneous Technical"}, {"symbol": "⍩", "unicode_codepoint": "U+2369", "unicode_name": "APL FUNCTIONAL SYMBOL GREATER-THAN DIAERESIS", "block": "Miscellaneous Technical"}, {"symbol": "⍪", "unicode_codepoint": "U+236A", "unicode_name": "APL FUNCTIONAL SYMBOL COMMA BAR", "block": "Miscellaneous Technical"}, {"symbol": "⍫", "unicode_codepoint": "U+236B", "unicode_name": "APL FUNCTIONAL SYMBOL DEL TILDE", "block": "Miscellaneous Technical"}, {"symbol": "⍬", "unicode_codepoint": "U+236C", "unicode_name": "APL FUNCTIONAL SYMBOL ZILDE", "block": "Miscellaneous Technical"}, {"symbol": "⍭", "unicode_codepoint": "U+236D", "unicode_name": "APL FUNCTIONAL SYMBOL STILE TILDE", "block": "Miscellaneous Technical"}, {"symbol": "⍮", "unicode_codepoint": "U+236E", "unicode_name": "APL FUNCTIONAL SYMBOL SEMICOLON UNDERBAR", "block": "Miscellaneous Technical"}, {"symbol": "⍯", "unicode_codepoint": "U+236F", "unicode_name": "APL FUNCTIONAL SYMBOL QUAD NOT EQUAL", "block": "Miscellaneous Technical"}, {"symbol": "⍰", "unicode_codepoint": "U+2370", "unicode_name": "APL FUNCTIONAL SYMBOL QUAD QUESTION", "block": "Miscellaneous Technical"}, {"symbol": "⍱", "unicode_codepoint": "U+2371", "unicode_name": "APL FUNCTIONAL SYMBOL DOWN CARET TILDE", "block": "Miscellaneous Technical"}, {"symbol": "⍲", "unicode_codepoint": "U+2372", "unicode_name": "APL FUNCTIONAL SYMBOL UP CARET TILDE", "block": "Miscellaneous Technical"}, {"symbol": "⍳", "unicode_codepoint": "U+2373", "unicode_name": "APL FUNCTIONAL SYMBOL IOTA", "block": "Miscellaneous Technical"}, {"symbol": "⍴", "unicode_codepoint": "U+2374", "unicode_name": "APL FUNCTIONAL SYMBOL RHO", "block": "Miscellaneous Technical"}, {"symbol": "⍵", "unicode_codepoint": "U+2375", "unicode_name": "APL FUNCTIONAL SYMBOL OMEGA", "block": "Miscellaneous Technical"}, {"symbol": "⍶", "unicode_codepoint": "U+2376", "unicode_name": "APL FUNCTIONAL SYMBOL ALPHA UNDERBAR", "block": "Miscellaneous Technical"}, {"symbol": "⍷", "unicode_codepoint": "U+2377", "unicode_name": "APL FUNCTIONAL SYMBOL EPSILON UNDERBAR", "block": "Miscellaneous Technical"}, {"symbol": "⍸", "unicode_codepoint": "U+2378", "unicode_name": "APL FUNCTIONAL SYMBOL IOTA UNDERBAR", "block": "Miscellaneous Technical"}, {"symbol": "⍹", "unicode_codepoint": "U+2379", "unicode_name": "APL FUNCTIONAL SYMBOL OMEGA UNDERBAR", "block": "Miscellaneous Technical"}, {"symbol": "⍺", "unicode_codepoint": "U+237A", "unicode_name": "APL FUNCTIONAL SYMBOL ALPHA", "block": "Miscellaneous Technical"}, {"symbol": "⍻", "unicode_codepoint": "U+237B", "unicode_name": "NOT CHECK MARK", "block": "Miscellaneous Technical"}, {"symbol": "⍼", "unicode_codepoint": "U+237C", "unicode_name": "RIGHT ANGLE WITH DOWNWARDS ZIGZAG ARROW", "block": "Miscellaneous Technical"}, {"symbol": "⍽", "unicode_codepoint": "U+237D", "unicode_name": "SHOULDERED OPEN BOX", "block": "Miscellaneous Technical"}, {"symbol": "⍾", "unicode_codepoint": "U+237E", "unicode_name": "BELL SYMBOL", "block": "Miscellaneous Technical"}, {"symbol": "⍿", "unicode_codepoint": "U+237F", "unicode_name": "VERTICAL LINE WITH MIDDLE DOT", "block": "Miscellaneous Technical"}, {"symbol": "⎀", "unicode_codepoint": "U+2380", "unicode_name": "INSERTION SYMBOL", "block": "Miscellaneous Technical"}, {"symbol": "⎁", "unicode_codepoint": "U+2381", "unicode_name": "CONTINUOUS UNDERLINE SYMBOL", "block": "Miscellaneous Technical"}, {"symbol": "⎂", "unicode_codepoint": "U+2382", "unicode_name": "DISCONTINUOUS UNDERLINE SYMBOL", "block": "Miscellaneous Technical"}, {"symbol": "⎃", "unicode_codepoint": "U+2383", "unicode_name": "EMPHASIS SYMBOL", "block": "Miscellaneous Technical"}, {"symbol": "⎄", "unicode_codepoint": "U+2384", "unicode_name": "COMPOSITION SYMBOL", "block": "Miscellaneous Technical"}, {"symbol": "⎅", "unicode_codepoint": "U+2385", "unicode_name": "WHITE SQUARE WITH CENTRE VERTICAL LINE", "block": "Miscellaneous Technical"}, {"symbol": "⎆", "unicode_codepoint": "U+2386", "unicode_name": "ENTER <PERSON>", "block": "Miscellaneous Technical"}, {"symbol": "⎇", "unicode_codepoint": "U+2387", "unicode_name": "ALTERNATIVE KEY SYMBOL", "block": "Miscellaneous Technical"}, {"symbol": "⎈", "unicode_codepoint": "U+2388", "unicode_name": "HELM SYMBOL", "block": "Miscellaneous Technical"}, {"symbol": "⎉", "unicode_codepoint": "U+2389", "unicode_name": "CIRCLED <PERSON>OR<PERSON><PERSON>ON<PERSON>L BAR WITH NOTCH", "block": "Miscellaneous Technical"}, {"symbol": "⎊", "unicode_codepoint": "U+238A", "unicode_name": "CIRCLED TRIANGLE DOWN", "block": "Miscellaneous Technical"}, {"symbol": "⎋", "unicode_codepoint": "U+238B", "unicode_name": "BROKEN CIRCLE WITH NORTHWEST ARROW", "block": "Miscellaneous Technical"}, {"symbol": "⎌", "unicode_codepoint": "U+238C", "unicode_name": "UNDO SYMBOL", "block": "Miscellaneous Technical"}, {"symbol": "⎍", "unicode_codepoint": "U+238D", "unicode_name": "MONOSTABLE SYMBOL", "block": "Miscellaneous Technical"}, {"symbol": "⎎", "unicode_codepoint": "U+238E", "unicode_name": "HYSTERESIS SYMBOL", "block": "Miscellaneous Technical"}, {"symbol": "⎏", "unicode_codepoint": "U+238F", "unicode_name": "OPEN-CIRCUIT-OUTPUT H-TYPE SYMBOL", "block": "Miscellaneous Technical"}, {"symbol": "⎐", "unicode_codepoint": "U+2390", "unicode_name": "OPEN-CIRCUIT-OUTPUT L-TYPE SYMBOL", "block": "Miscellaneous Technical"}, {"symbol": "⎑", "unicode_codepoint": "U+2391", "unicode_name": "PASSIVE-PULL-DOWN-OUTPUT SYMBOL", "block": "Miscellaneous Technical"}, {"symbol": "⎒", "unicode_codepoint": "U+2392", "unicode_name": "PASSIVE-PULL-UP-OUTPUT SYMBOL", "block": "Miscellaneous Technical"}, {"symbol": "⎓", "unicode_codepoint": "U+2393", "unicode_name": "DIRECT CURRENT SYMBOL FORM TWO", "block": "Miscellaneous Technical"}, {"symbol": "⎔", "unicode_codepoint": "U+2394", "unicode_name": "SOFTWARE-FUNCTION SYMBOL", "block": "Miscellaneous Technical"}, {"symbol": "⎕", "unicode_codepoint": "U+2395", "unicode_name": "APL FUNCTIONAL SYMBOL QUAD", "block": "Miscellaneous Technical"}, {"symbol": "⎖", "unicode_codepoint": "U+2396", "unicode_name": "DECIMAL SEPARATOR KEY SYMBOL", "block": "Miscellaneous Technical"}, {"symbol": "⎗", "unicode_codepoint": "U+2397", "unicode_name": "PREVIOUS PAGE", "block": "Miscellaneous Technical"}, {"symbol": "⎘", "unicode_codepoint": "U+2398", "unicode_name": "NEXT PAGE", "block": "Miscellaneous Technical"}, {"symbol": "⎙", "unicode_codepoint": "U+2399", "unicode_name": "PRINT SCREEN SYMBOL", "block": "Miscellaneous Technical"}, {"symbol": "⎚", "unicode_codepoint": "U+239A", "unicode_name": "CLEAR SCREEN SYMBOL", "block": "Miscellaneous Technical"}, {"symbol": "⎛", "unicode_codepoint": "U+239B", "unicode_name": "LEFT PARENTHESIS UPPER HOOK", "block": "Miscellaneous Technical"}, {"symbol": "⎜", "unicode_codepoint": "U+239C", "unicode_name": "LEFT PARENTHESIS EXTENSION", "block": "Miscellaneous Technical"}, {"symbol": "⎝", "unicode_codepoint": "U+239D", "unicode_name": "LEFT PARENTHESIS LOWER HOOK", "block": "Miscellaneous Technical"}, {"symbol": "⎞", "unicode_codepoint": "U+239E", "unicode_name": "RIGHT PARENTHESIS UPPER HOOK", "block": "Miscellaneous Technical"}, {"symbol": "⎟", "unicode_codepoint": "U+239F", "unicode_name": "RIGHT PARENTHESIS EXTENSION", "block": "Miscellaneous Technical"}, {"symbol": "⎠", "unicode_codepoint": "U+23A0", "unicode_name": "RIGHT PARENTHESIS LOWER HOOK", "block": "Miscellaneous Technical"}, {"symbol": "⎡", "unicode_codepoint": "U+23A1", "unicode_name": "LEFT SQUARE BRACKET UPPER CORNER", "block": "Miscellaneous Technical"}, {"symbol": "⎢", "unicode_codepoint": "U+23A2", "unicode_name": "LEFT SQUARE BRACKET EXTENSION", "block": "Miscellaneous Technical"}, {"symbol": "⎣", "unicode_codepoint": "U+23A3", "unicode_name": "LEFT SQUARE BRACKET LOWER CORNER", "block": "Miscellaneous Technical"}, {"symbol": "⎤", "unicode_codepoint": "U+23A4", "unicode_name": "RIGHT SQUARE BRACKET UPPER CORNER", "block": "Miscellaneous Technical"}, {"symbol": "⎥", "unicode_codepoint": "U+23A5", "unicode_name": "RIGHT SQUARE BRACKET EXTENSION", "block": "Miscellaneous Technical"}, {"symbol": "⎦", "unicode_codepoint": "U+23A6", "unicode_name": "RIGHT SQUARE BRACKET LOWER CORNER", "block": "Miscellaneous Technical"}, {"symbol": "⎧", "unicode_codepoint": "U+23A7", "unicode_name": "LEFT CURLY BRACKET UPPER HOOK", "block": "Miscellaneous Technical"}, {"symbol": "⎨", "unicode_codepoint": "U+23A8", "unicode_name": "LEFT CURLY BRACKET MIDDLE PIECE", "block": "Miscellaneous Technical"}, {"symbol": "⎩", "unicode_codepoint": "U+23A9", "unicode_name": "LEFT CURLY BRACKET LOWER HOOK", "block": "Miscellaneous Technical"}, {"symbol": "⎪", "unicode_codepoint": "U+23AA", "unicode_name": "CURLY BRACKET EXTENSION", "block": "Miscellaneous Technical"}, {"symbol": "⎫", "unicode_codepoint": "U+23AB", "unicode_name": "RIGHT CURLY BRACKET UPPER HOOK", "block": "Miscellaneous Technical"}, {"symbol": "⎬", "unicode_codepoint": "U+23AC", "unicode_name": "RIGHT CURLY BRACKET MIDDLE PIECE", "block": "Miscellaneous Technical"}, {"symbol": "⎭", "unicode_codepoint": "U+23AD", "unicode_name": "RIGHT CURLY BRACKET LOWER HOOK", "block": "Miscellaneous Technical"}, {"symbol": "⎮", "unicode_codepoint": "U+23AE", "unicode_name": "INTEGRAL EXTENSION", "block": "Miscellaneous Technical"}, {"symbol": "⎯", "unicode_codepoint": "U+23AF", "unicode_name": "HORIZONTAL LINE EXTENSION", "block": "Miscellaneous Technical"}, {"symbol": "⎰", "unicode_codepoint": "U+23B0", "unicode_name": "UPPER LEFT OR LOWER RIGHT CURLY BRACKET SECTION", "block": "Miscellaneous Technical"}, {"symbol": "⎱", "unicode_codepoint": "U+23B1", "unicode_name": "UPPER RIGHT OR LOWER LEFT CURLY BRACKET SECTION", "block": "Miscellaneous Technical"}, {"symbol": "⎲", "unicode_codepoint": "U+23B2", "unicode_name": "SUMMATION TOP", "block": "Miscellaneous Technical"}, {"symbol": "⎳", "unicode_codepoint": "U+23B3", "unicode_name": "SUMMATION BOTTOM", "block": "Miscellaneous Technical"}, {"symbol": "⎴", "unicode_codepoint": "U+23B4", "unicode_name": "TOP SQUARE BRACKET", "block": "Miscellaneous Technical"}, {"symbol": "⎵", "unicode_codepoint": "U+23B5", "unicode_name": "BOTTOM SQUARE BRACKET", "block": "Miscellaneous Technical"}, {"symbol": "⎶", "unicode_codepoint": "U+23B6", "unicode_name": "BOTTOM SQUARE BRACKET OVER TOP SQUARE BRACKET", "block": "Miscellaneous Technical"}, {"symbol": "⎷", "unicode_codepoint": "U+23B7", "unicode_name": "RADICAL SYMBOL BOTTOM", "block": "Miscellaneous Technical"}, {"symbol": "⎸", "unicode_codepoint": "U+23B8", "unicode_name": "LEFT VERTICAL BOX LINE", "block": "Miscellaneous Technical"}, {"symbol": "⎹", "unicode_codepoint": "U+23B9", "unicode_name": "RIGHT VERTICAL BOX LINE", "block": "Miscellaneous Technical"}, {"symbol": "⎺", "unicode_codepoint": "U+23BA", "unicode_name": "HORIZONTAL SCAN LINE-1", "block": "Miscellaneous Technical"}, {"symbol": "⎻", "unicode_codepoint": "U+23BB", "unicode_name": "HORIZONTAL SCAN LINE-3", "block": "Miscellaneous Technical"}, {"symbol": "⎼", "unicode_codepoint": "U+23BC", "unicode_name": "HORIZONTAL SCAN LINE-7", "block": "Miscellaneous Technical"}, {"symbol": "⎽", "unicode_codepoint": "U+23BD", "unicode_name": "HORIZONTAL SCAN LINE-9", "block": "Miscellaneous Technical"}, {"symbol": "⎾", "unicode_codepoint": "U+23BE", "unicode_name": "DENTISTRY SYMBOL LIGHT VERTICAL AND TOP RIGHT", "block": "Miscellaneous Technical"}, {"symbol": "⎿", "unicode_codepoint": "U+23BF", "unicode_name": "DENTISTRY SYMBOL LIGHT VERTICAL AND BOTTOM RIGHT", "block": "Miscellaneous Technical"}, {"symbol": "⏀", "unicode_codepoint": "U+23C0", "unicode_name": "DENTISTRY SYMBOL LIGHT VERTICAL WITH CIRCLE", "block": "Miscellaneous Technical"}, {"symbol": "⏁", "unicode_codepoint": "U+23C1", "unicode_name": "DENTISTRY SYMBOL LIGHT DOWN AND <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> WITH CIRCLE", "block": "Miscellaneous Technical"}, {"symbol": "⏂", "unicode_codepoint": "U+23C2", "unicode_name": "DENTISTRY SYMBOL LIGHT UP AND <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>L WITH CIRCLE", "block": "Miscellaneous Technical"}, {"symbol": "⏃", "unicode_codepoint": "U+23C3", "unicode_name": "DENTISTRY SYMBOL LIGHT VERTICAL WITH TRIANGLE", "block": "Miscellaneous Technical"}, {"symbol": "⏄", "unicode_codepoint": "U+23C4", "unicode_name": "DENTISTRY SYMBOL LIGHT DOWN AND <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> WITH TRIANGLE", "block": "Miscellaneous Technical"}, {"symbol": "⏅", "unicode_codepoint": "U+23C5", "unicode_name": "DENTISTRY SYMBOL LIGHT UP AND <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> WITH TRIANGLE", "block": "Miscellaneous Technical"}, {"symbol": "⏆", "unicode_codepoint": "U+23C6", "unicode_name": "DENTISTRY SYMBOL LIGHT VERTICAL AND WAVE", "block": "Miscellaneous Technical"}, {"symbol": "⏇", "unicode_codepoint": "U+23C7", "unicode_name": "DENTISTRY SYMBOL LIGHT DOWN AND <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>L WITH WAVE", "block": "Miscellaneous Technical"}, {"symbol": "⏈", "unicode_codepoint": "U+23C8", "unicode_name": "DENTISTRY SYMBOL LIGHT UP AND <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>L WITH WAVE", "block": "Miscellaneous Technical"}, {"symbol": "⏉", "unicode_codepoint": "U+23C9", "unicode_name": "DENTISTRY SYMBOL LIGHT DOWN AND HORIZONTAL", "block": "Miscellaneous Technical"}, {"symbol": "⏊", "unicode_codepoint": "U+23CA", "unicode_name": "DENTISTRY SYMBOL LIGHT UP AND HORIZONTAL", "block": "Miscellaneous Technical"}, {"symbol": "⏋", "unicode_codepoint": "U+23CB", "unicode_name": "DENTISTRY SYMBOL LIGHT VERTICAL AND TOP LEFT", "block": "Miscellaneous Technical"}, {"symbol": "⏌", "unicode_codepoint": "U+23CC", "unicode_name": "DENTISTRY SYMBOL LIGHT VERTICAL AND BOTTOM LEFT", "block": "Miscellaneous Technical"}, {"symbol": "⏍", "unicode_codepoint": "U+23CD", "unicode_name": "SQUARE FOOT", "block": "Miscellaneous Technical"}, {"symbol": "⏎", "unicode_codepoint": "U+23CE", "unicode_name": "RETURN SYMBOL", "block": "Miscellaneous Technical"}, {"symbol": "⏏", "unicode_codepoint": "U+23CF", "unicode_name": "EJECT SYMBOL", "block": "Miscellaneous Technical"}, {"symbol": "⏐", "unicode_codepoint": "U+23D0", "unicode_name": "VERTICAL LINE EXTENSION", "block": "Miscellaneous Technical"}, {"symbol": "⏑", "unicode_codepoint": "U+23D1", "unicode_name": "METRICAL BREVE", "block": "Miscellaneous Technical"}, {"symbol": "⏒", "unicode_codepoint": "U+23D2", "unicode_name": "METRICAL LONG OVER SHORT", "block": "Miscellaneous Technical"}, {"symbol": "⏓", "unicode_codepoint": "U+23D3", "unicode_name": "METRICAL SHORT OVER LONG", "block": "Miscellaneous Technical"}, {"symbol": "⏔", "unicode_codepoint": "U+23D4", "unicode_name": "METRICAL LONG OVER TWO SHORTS", "block": "Miscellaneous Technical"}, {"symbol": "⏕", "unicode_codepoint": "U+23D5", "unicode_name": "METRICAL TWO SHORTS OVER LONG", "block": "Miscellaneous Technical"}, {"symbol": "⏖", "unicode_codepoint": "U+23D6", "unicode_name": "METRICAL TWO SHORTS JOINED", "block": "Miscellaneous Technical"}, {"symbol": "⏗", "unicode_codepoint": "U+23D7", "unicode_name": "METRICAL TRISEME", "block": "Miscellaneous Technical"}, {"symbol": "⏘", "unicode_codepoint": "U+23D8", "unicode_name": "METRICAL TETRASEME", "block": "Miscellaneous Technical"}, {"symbol": "⏙", "unicode_codepoint": "U+23D9", "unicode_name": "METRICAL PENTASEME", "block": "Miscellaneous Technical"}, {"symbol": "⏚", "unicode_codepoint": "U+23DA", "unicode_name": "EARTH GROUND", "block": "Miscellaneous Technical"}, {"symbol": "⏛", "unicode_codepoint": "U+23DB", "unicode_name": "FUSE", "block": "Miscellaneous Technical"}, {"symbol": "⏜", "unicode_codepoint": "U+23DC", "unicode_name": "TOP PARENTHESIS", "block": "Miscellaneous Technical"}, {"symbol": "⏝", "unicode_codepoint": "U+23DD", "unicode_name": "BOTTOM PARENTHESIS", "block": "Miscellaneous Technical"}, {"symbol": "⏞", "unicode_codepoint": "U+23DE", "unicode_name": "TOP CURLY BRACKET", "block": "Miscellaneous Technical"}, {"symbol": "⏟", "unicode_codepoint": "U+23DF", "unicode_name": "BOTTOM CURLY BRACKET", "block": "Miscellaneous Technical"}, {"symbol": "⏠", "unicode_codepoint": "U+23E0", "unicode_name": "TOP TORTOISE SHELL BRACKET", "block": "Miscellaneous Technical"}, {"symbol": "⏡", "unicode_codepoint": "U+23E1", "unicode_name": "BOTTOM TORTOISE SHELL BRACKET", "block": "Miscellaneous Technical"}, {"symbol": "⏢", "unicode_codepoint": "U+23E2", "unicode_name": "WHITE TRAPEZIUM", "block": "Miscellaneous Technical"}, {"symbol": "⏣", "unicode_codepoint": "U+23E3", "unicode_name": "BENZENE RING WITH CIRCLE", "block": "Miscellaneous Technical"}, {"symbol": "⏤", "unicode_codepoint": "U+23E4", "unicode_name": "STRAIGHTNESS", "block": "Miscellaneous Technical"}, {"symbol": "⏥", "unicode_codepoint": "U+23E5", "unicode_name": "FLATNESS", "block": "Miscellaneous Technical"}, {"symbol": "⏦", "unicode_codepoint": "U+23E6", "unicode_name": "AC CURRENT", "block": "Miscellaneous Technical"}, {"symbol": "⏧", "unicode_codepoint": "U+23E7", "unicode_name": "ELECTRICAL INTERSECTION", "block": "Miscellaneous Technical"}, {"symbol": "⏨", "unicode_codepoint": "U+23E8", "unicode_name": "DECIMAL EXPONENT SYMBOL", "block": "Miscellaneous Technical"}, {"symbol": "⏩", "unicode_codepoint": "U+23E9", "unicode_name": "BLACK RIGHT-POINTING DOUBLE TRIANGLE", "block": "Miscellaneous Technical"}, {"symbol": "⏪", "unicode_codepoint": "U+23EA", "unicode_name": "BLACK LEFT-POINTING DOUBLE TRIANGLE", "block": "Miscellaneous Technical"}, {"symbol": "⏫", "unicode_codepoint": "U+23EB", "unicode_name": "BLACK UP-POINTING DOUBLE TRIANGLE", "block": "Miscellaneous Technical"}, {"symbol": "⏬", "unicode_codepoint": "U+23EC", "unicode_name": "BLACK DOWN-POINTING DOUBLE TRIANGLE", "block": "Miscellaneous Technical"}, {"symbol": "⏭", "unicode_codepoint": "U+23ED", "unicode_name": "BLACK RIGHT-POINTING DOUBLE TRIANGLE WITH VERTICAL BAR", "block": "Miscellaneous Technical"}, {"symbol": "⏮", "unicode_codepoint": "U+23EE", "unicode_name": "BLACK LEFT-POINTING DOUBLE TRIANGLE WITH VERTICAL BAR", "block": "Miscellaneous Technical"}, {"symbol": "⏯", "unicode_codepoint": "U+23EF", "unicode_name": "BLACK RIGHT-POINTING TRIANGLE WITH DOUBLE VERTICAL BAR", "block": "Miscellaneous Technical"}, {"symbol": "⏰", "unicode_codepoint": "U+23F0", "unicode_name": "ALARM CLOCK", "block": "Miscellaneous Technical"}, {"symbol": "⏱", "unicode_codepoint": "U+23F1", "unicode_name": "STOPWATCH", "block": "Miscellaneous Technical"}, {"symbol": "⏲", "unicode_codepoint": "U+23F2", "unicode_name": "TIMER CLOCK", "block": "Miscellaneous Technical"}, {"symbol": "⏳", "unicode_codepoint": "U+23F3", "unicode_name": "HOURGLASS WITH FLOWING SAND", "block": "Miscellaneous Technical"}, {"symbol": "⏴", "unicode_codepoint": "U+23F4", "unicode_name": "BLACK MEDIUM LEFT-POINTING TRIANGLE", "block": "Miscellaneous Technical"}, {"symbol": "⏵", "unicode_codepoint": "U+23F5", "unicode_name": "BLACK MEDIUM RIGHT-POINTING TRIANGLE", "block": "Miscellaneous Technical"}, {"symbol": "⏶", "unicode_codepoint": "U+23F6", "unicode_name": "BLACK MEDIUM UP-POINTING TRIANGLE", "block": "Miscellaneous Technical"}, {"symbol": "⏷", "unicode_codepoint": "U+23F7", "unicode_name": "BLACK MEDIUM DOWN-POINTING TRIANGLE", "block": "Miscellaneous Technical"}, {"symbol": "⏸", "unicode_codepoint": "U+23F8", "unicode_name": "DOUBLE VERTICAL BAR", "block": "Miscellaneous Technical"}, {"symbol": "⏹", "unicode_codepoint": "U+23F9", "unicode_name": "BLACK SQUARE FOR STOP", "block": "Miscellaneous Technical"}, {"symbol": "⏺", "unicode_codepoint": "U+23FA", "unicode_name": "BLACK CIRCLE FOR RECORD", "block": "Miscellaneous Technical"}, {"symbol": "⏻", "unicode_codepoint": "U+23FB", "unicode_name": "POWER SYMBOL", "block": "Miscellaneous Technical"}, {"symbol": "⏼", "unicode_codepoint": "U+23FC", "unicode_name": "POWER ON-OFF SYMBOL", "block": "Miscellaneous Technical"}, {"symbol": "⏽", "unicode_codepoint": "U+23FD", "unicode_name": "POWER ON SYMBOL", "block": "Miscellaneous Technical"}, {"symbol": "⏾", "unicode_codepoint": "U+23FE", "unicode_name": "POWER SLEEP SYMBOL", "block": "Miscellaneous Technical"}, {"symbol": "⏿", "unicode_codepoint": "U+23FF", "unicode_name": "OBSERVER EYE SYMBOL", "block": "Miscellaneous Technical"}]