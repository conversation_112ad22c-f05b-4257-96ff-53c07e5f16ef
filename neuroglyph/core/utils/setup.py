#!/usr/bin/env python3
"""
NEUROGLIPH Setup Script
======================

Script di configurazione e installazione per il progetto NEUROGLIPH.
Gestisce l'installazione delle dipendenze, la configurazione dell'ambiente
e l'inizializzazione dei componenti necessari.
"""

import os
import sys
import subprocess
import json
import argparse
from pathlib import Path
import logging

# Configurazione logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# Versioni richieste
PYTHON_MIN_VERSION = (3, 10)
REQUIRED_PACKAGES = [
    "torch>=2.1.0",
    "transformers>=4.35.0",
    "datasets>=2.14.0",
    "numpy>=1.24.0",
    "pandas>=2.0.0",
    "scikit-learn>=1.3.0",
    "matplotlib>=3.7.0",
    "seaborn>=0.12.0",
    "jupyter>=1.0.0",
    "tqdm>=4.65.0",
    "wandb>=0.15.0",
    "tensorboard>=2.14.0",
    "accelerate>=0.24.0",
    "peft>=0.6.0",
    "bitsandbytes>=0.41.0",
    "tokenizers>=0.14.0",
    "sentencepiece>=0.1.99",
    "protobuf>=4.24.0",
    "requests>=2.31.0",
    "aiohttp>=3.8.0",
    "fastapi>=0.104.0",
    "uvicorn>=0.24.0",
    "pydantic>=2.4.0",
    "click>=8.1.0",
    "rich>=13.6.0",
    "typer>=0.9.0"
]

OPTIONAL_PACKAGES = [
    "flash-attn>=2.3.0",  # Per ottimizzazioni attention
    "xformers>=0.0.22",   # Per ottimizzazioni memoria
    "deepspeed>=0.11.0",  # Per training distribuito
    "apex",               # Per mixed precision NVIDIA
    "triton>=2.1.0"       # Per kernel ottimizzati
]

DEV_PACKAGES = [
    "pytest>=7.4.0",
    "pytest-cov>=4.1.0",
    "black>=23.9.0",
    "isort>=5.12.0",
    "flake8>=6.1.0",
    "mypy>=1.6.0",
    "pre-commit>=3.5.0",
    "sphinx>=7.2.0",
    "sphinx-rtd-theme>=1.3.0"
]


def check_python_version():
    """Verifica che la versione di Python sia compatibile."""
    current_version = sys.version_info[:2]
    if current_version < PYTHON_MIN_VERSION:
        logger.error(f"Python {PYTHON_MIN_VERSION[0]}.{PYTHON_MIN_VERSION[1]}+ richiesto. "
                    f"Versione corrente: {current_version[0]}.{current_version[1]}")
        sys.exit(1)
    logger.info(f"✓ Python {current_version[0]}.{current_version[1]} compatibile")


def check_gpu_availability():
    """Verifica disponibilità GPU e CUDA."""
    try:
        import torch
        if torch.cuda.is_available():
            gpu_count = torch.cuda.device_count()
            gpu_name = torch.cuda.get_device_name(0)
            logger.info(f"✓ GPU disponibile: {gpu_count}x {gpu_name}")
            logger.info(f"✓ CUDA versione: {torch.version.cuda}")
            return True
        else:
            logger.warning("⚠ GPU non disponibile - utilizzo CPU")
            return False
    except ImportError:
        logger.warning("⚠ PyTorch non installato - impossibile verificare GPU")
        return False


def install_packages(packages, optional=False):
    """Installa pacchetti Python."""
    package_type = "opzionali" if optional else "richiesti"
    logger.info(f"Installazione pacchetti {package_type}...")
    
    for package in packages:
        try:
            logger.info(f"Installazione {package}...")
            subprocess.run([sys.executable, "-m", "pip", "install", package], 
                         check=True, capture_output=True)
            logger.info(f"✓ {package} installato")
        except subprocess.CalledProcessError as e:
            if optional:
                logger.warning(f"⚠ Pacchetto opzionale {package} non installato: {e}")
            else:
                logger.error(f"✗ Errore installazione {package}: {e}")
                if not optional:
                    sys.exit(1)


def create_directories():
    """Crea le directory necessarie per il progetto."""
    directories = [
        "data/raw",
        "data/processed", 
        "data/cache",
        "models/checkpoints",
        "models/exports",
        "logs/training",
        "logs/inference",
        "outputs/generated",
        "outputs/evaluations",
        "configs/experiments",
        "notebooks",
        "tests/unit",
        "tests/integration"
    ]
    
    logger.info("Creazione struttura directory...")
    for directory in directories:
        Path(directory).mkdir(parents=True, exist_ok=True)
        logger.info(f"✓ Directory creata: {directory}")


def create_config_files():
    """Crea file di configurazione di default."""
    configs = {
        "config.json": {
            "project_name": "NEUROGLIPH",
            "version": "1.0.0",
            "data_dir": "data",
            "models_dir": "models", 
            "logs_dir": "logs",
            "cache_dir": "data/cache",
            "max_workers": 4,
            "default_device": "auto"
        },
        
        "logging.json": {
            "version": 1,
            "disable_existing_loggers": False,
            "formatters": {
                "standard": {
                    "format": "%(asctime)s [%(levelname)s] %(name)s: %(message)s"
                }
            },
            "handlers": {
                "default": {
                    "level": "INFO",
                    "formatter": "standard",
                    "class": "logging.StreamHandler"
                },
                "file": {
                    "level": "DEBUG", 
                    "formatter": "standard",
                    "class": "logging.FileHandler",
                    "filename": "logs/neurogliph.log"
                }
            },
            "loggers": {
                "": {
                    "handlers": ["default", "file"],
                    "level": "INFO",
                    "propagate": False
                }
            }
        },
        
        ".env.example": """# NEUROGLIPH Environment Variables
NEUROGLIPH_DATA_DIR=data
NEUROGLIPH_MODELS_DIR=models
NEUROGLIPH_CACHE_DIR=data/cache
NEUROGLIPH_LOG_LEVEL=INFO

# Weights & Biases (optional)
WANDB_PROJECT=neurogliph
WANDB_ENTITY=your-entity

# Hugging Face (optional)
HF_TOKEN=your-hf-token

# OpenAI (optional)
OPENAI_API_KEY=your-openai-key

# CUDA Settings
CUDA_VISIBLE_DEVICES=0
PYTORCH_CUDA_ALLOC_CONF=max_split_size_mb:512
"""
    }
    
    logger.info("Creazione file di configurazione...")
    for filename, content in configs.items():
        filepath = Path(filename)
        if not filepath.exists():
            if filename.endswith('.json'):
                with open(filepath, 'w') as f:
                    json.dump(content, f, indent=2)
            else:
                with open(filepath, 'w') as f:
                    f.write(content)
            logger.info(f"✓ File creato: {filename}")
        else:
            logger.info(f"⚠ File già esistente: {filename}")


def setup_git_hooks():
    """Configura git hooks per il progetto."""
    if not Path('.git').exists():
        logger.warning("⚠ Repository git non inizializzato")
        return
    
    try:
        # Installa pre-commit hooks
        subprocess.run(["pre-commit", "install"], check=True, capture_output=True)
        logger.info("✓ Pre-commit hooks installati")
    except (subprocess.CalledProcessError, FileNotFoundError):
        logger.warning("⚠ Pre-commit non disponibile - hooks non installati")


def create_requirements_files():
    """Crea file requirements per diverse configurazioni."""
    requirements = {
        "requirements.txt": REQUIRED_PACKAGES,
        "requirements-dev.txt": REQUIRED_PACKAGES + DEV_PACKAGES,
        "requirements-optional.txt": OPTIONAL_PACKAGES
    }
    
    logger.info("Creazione file requirements...")
    for filename, packages in requirements.items():
        with open(filename, 'w') as f:
            for package in packages:
                f.write(f"{package}\n")
        logger.info(f"✓ File creato: {filename}")


def run_tests():
    """Esegue test di base per verificare l'installazione."""
    logger.info("Esecuzione test di verifica...")
    
    # Test import principali
    test_imports = [
        "torch",
        "transformers", 
        "datasets",
        "numpy",
        "pandas"
    ]
    
    for module in test_imports:
        try:
            __import__(module)
            logger.info(f"✓ Import {module} riuscito")
        except ImportError as e:
            logger.error(f"✗ Import {module} fallito: {e}")
    
    # Test GPU se disponibile
    check_gpu_availability()
    
    # Test core NEUROGLIPH
    try:
        from neuroglyph.core.encoder import NeuroGlyphEncoder
        encoder = NeuroGlyphEncoder()
        logger.info("✓ Core NEUROGLIPH funzionante")
    except Exception as e:
        logger.warning(f"⚠ Test core NEUROGLIPH fallito: {e}")


def main():
    """Funzione principale di setup."""
    parser = argparse.ArgumentParser(description="Setup NEUROGLIPH environment")
    parser.add_argument("--dev", action="store_true", help="Installa dipendenze development")
    parser.add_argument("--optional", action="store_true", help="Installa dipendenze opzionali")
    parser.add_argument("--no-test", action="store_true", help="Salta test di verifica")
    parser.add_argument("--force", action="store_true", help="Forza reinstallazione")
    
    args = parser.parse_args()
    
    logger.info("🚀 Inizio setup NEUROGLIPH")
    
    # Verifiche preliminari
    check_python_version()
    
    # Creazione struttura
    create_directories()
    create_config_files()
    create_requirements_files()
    
    # Installazione pacchetti
    packages_to_install = REQUIRED_PACKAGES.copy()
    if args.dev:
        packages_to_install.extend(DEV_PACKAGES)
        logger.info("📦 Modalità development attivata")
    
    install_packages(packages_to_install)
    
    if args.optional:
        install_packages(OPTIONAL_PACKAGES, optional=True)
    
    # Configurazione git
    setup_git_hooks()
    
    # Test finali
    if not args.no_test:
        run_tests()
    
    logger.info("✅ Setup NEUROGLIPH completato!")
    logger.info("📖 Consulta docs/README.md per iniziare")
    
    # Suggerimenti post-installazione
    print("\n" + "="*50)
    print("🎉 NEUROGLIPH Setup Completato!")
    print("="*50)
    print("\n📋 Prossimi passi:")
    print("1. Copia .env.example in .env e configura le variabili")
    print("2. Esegui 'python core/encoder.py' per testare il core")
    print("3. Consulta docs/ per guide dettagliate")
    print("4. Inizia con i notebook in notebooks/")
    print("\n🔧 Comandi utili:")
    print("- python scripts/train.py --help")
    print("- python scripts/evaluate.py --help") 
    print("- jupyter notebook notebooks/")
    print("\n📚 Documentazione: docs/README.md")
    print("🐛 Issues: https://github.com/JoyciAkira/NEUROGLIPH/issues")


if __name__ == "__main__":
    main()
