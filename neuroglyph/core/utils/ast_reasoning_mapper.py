#!/usr/bin/env python3
"""
🧠 NEUROGLYPH ULTRA - Fase 2: Mapping AST → Simboli Reasoning
================================================================

Collega simboli reasoning a pattern ast.*
Esempio: ast.Compare → ng:reasoning:comparison

Autore: NEUROGLYPH ULTRA Team
Data: 2025-05-23
"""

import ast
import json
import logging
from pathlib import Path
from typing import Dict, List, Set, Any, Optional, Tuple

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('logs/ast_reasoning_mapping.log'),
        logging.StreamHandler()
    ]
)

class ASTReasoningMapper:
    """Mapper per collegare pattern AST a simboli reasoning NEUROGLYPH."""
    
    def __init__(self):
        self.registry_path = Path("core/symbols_registry.json")
        self.mapping_path = Path("core/ast_reasoning_mapping.json")
        
        # Carica simboli reasoning
        self.reasoning_symbols = self._load_reasoning_symbols()
        
        # Mapping AST → Reasoning patterns
        self.ast_reasoning_mappings = {
            # Comparison operations → reasoning patterns
            "ast.Compare": ["ng:reasoning:similarity", "ng:reasoning:difference", "ng:reasoning:evaluation"],
            "ast.Eq": ["ng:reasoning:similarity", "ng:reasoning:consistency"],
            "ast.NotEq": ["ng:reasoning:difference", "ng:reasoning:contradiction"],
            "ast.Lt": ["ng:reasoning:evaluation", "ng:reasoning:assessment"],
            "ast.LtE": ["ng:reasoning:evaluation", "ng:reasoning:assessment"],
            "ast.Gt": ["ng:reasoning:evaluation", "ng:reasoning:assessment"],
            "ast.GtE": ["ng:reasoning:evaluation", "ng:reasoning:assessment"],
            "ast.Is": ["ng:reasoning:similarity", "ng:reasoning:classification"],
            "ast.IsNot": ["ng:reasoning:difference", "ng:reasoning:classification"],
            "ast.In": ["ng:reasoning:classification", "ng:reasoning:pattern"],
            "ast.NotIn": ["ng:reasoning:classification", "ng:reasoning:difference"],
            
            # Boolean operations → logical reasoning
            "ast.BoolOp": ["ng:reasoning:inference", "ng:reasoning:deduction"],
            "ast.And": ["ng:reasoning:synthesis", "ng:reasoning:consistency"],
            "ast.Or": ["ng:reasoning:generalization", "ng:reasoning:hypothesis"],
            "ast.Not": ["ng:reasoning:contradiction", "ng:reasoning:contrapositive"],
            
            # Control flow → reasoning processes
            "ast.If": ["ng:reasoning:decision", "ng:reasoning:judgment"],
            "ast.IfExp": ["ng:reasoning:decision", "ng:reasoning:evaluation"],
            "ast.For": ["ng:reasoning:pattern", "ng:reasoning:induction"],
            "ast.While": ["ng:reasoning:monitoring", "ng:reasoning:control"],
            "ast.Break": ["ng:reasoning:decision", "ng:reasoning:control"],
            "ast.Continue": ["ng:reasoning:strategy", "ng:reasoning:control"],
            
            # Function definitions → reasoning structures
            "ast.FunctionDef": ["ng:reasoning:abstraction", "ng:reasoning:planning"],
            "ast.AsyncFunctionDef": ["ng:reasoning:abstraction", "ng:reasoning:planning"],
            "ast.Lambda": ["ng:reasoning:abstraction", "ng:reasoning:synthesis"],
            "ast.Return": ["ng:reasoning:conclusion", "ng:reasoning:synthesis"],
            "ast.Yield": ["ng:reasoning:generalization", "ng:reasoning:pattern"],
            "ast.YieldFrom": ["ng:reasoning:generalization", "ng:reasoning:abstraction"],
            
            # Exception handling → error reasoning
            "ast.Try": ["ng:reasoning:hypothesis", "ng:reasoning:monitoring"],
            "ast.ExceptHandler": ["ng:reasoning:error_correction", "ng:reasoning:fallacy"],
            "ast.Raise": ["ng:reasoning:contradiction", "ng:reasoning:error_correction"],
            "ast.Assert": ["ng:reasoning:proof", "ng:reasoning:validity"],
            
            # Class definitions → classification reasoning
            "ast.ClassDef": ["ng:reasoning:classification", "ng:reasoning:abstraction"],
            "ast.Attribute": ["ng:reasoning:specialization", "ng:reasoning:analysis"],
            "ast.Subscript": ["ng:reasoning:specialization", "ng:reasoning:pattern"],
            
            # Assignments → reasoning updates
            "ast.Assign": ["ng:reasoning:premise", "ng:reasoning:hypothesis"],
            "ast.AnnAssign": ["ng:reasoning:premise", "ng:reasoning:classification"],
            "ast.AugAssign": ["ng:reasoning:synthesis", "ng:reasoning:generalization"],
            
            # Comprehensions → pattern reasoning
            "ast.ListComp": ["ng:reasoning:pattern", "ng:reasoning:generalization"],
            "ast.SetComp": ["ng:reasoning:pattern", "ng:reasoning:classification"],
            "ast.DictComp": ["ng:reasoning:pattern", "ng:reasoning:analogy"],
            "ast.GeneratorExp": ["ng:reasoning:pattern", "ng:reasoning:induction"],
            
            # Advanced reasoning patterns
            "ast.Call": ["ng:reasoning:inference", "ng:reasoning:deduction"],
            "ast.Starred": ["ng:reasoning:generalization", "ng:reasoning:pattern"],
            "ast.NamedExpr": ["ng:reasoning:premise", "ng:reasoning:hypothesis"],
            "ast.Match": ["ng:reasoning:pattern", "ng:reasoning:classification"],
            "ast.MatchValue": ["ng:reasoning:similarity", "ng:reasoning:pattern"],
            "ast.MatchSingleton": ["ng:reasoning:classification", "ng:reasoning:validity"],
            "ast.MatchSequence": ["ng:reasoning:pattern", "ng:reasoning:induction"],
            "ast.MatchMapping": ["ng:reasoning:analogy", "ng:reasoning:pattern"],
            "ast.MatchClass": ["ng:reasoning:classification", "ng:reasoning:specialization"],
            "ast.MatchStar": ["ng:reasoning:generalization", "ng:reasoning:pattern"],
            "ast.MatchAs": ["ng:reasoning:analogy", "ng:reasoning:abstraction"],
            "ast.MatchOr": ["ng:reasoning:generalization", "ng:reasoning:hypothesis"]
        }
        
        logging.info(f"🧠 Inizializzato AST Reasoning Mapper")
        logging.info(f"📊 Simboli reasoning caricati: {len(self.reasoning_symbols)}")
        logging.info(f"🔗 Pattern AST mappati: {len(self.ast_reasoning_mappings)}")
        
    def _load_reasoning_symbols(self) -> Dict[str, Dict[str, Any]]:
        """Carica simboli reasoning dal registry."""
        reasoning_symbols = {}
        
        try:
            with open(self.registry_path, 'r', encoding='utf-8') as f:
                registry = json.load(f)
                
            for symbol_data in registry.get("approved_symbols", []):
                code = symbol_data.get("code", "")
                if code.startswith("ng:reasoning:"):
                    reasoning_symbols[code] = symbol_data
                    
            logging.info(f"📊 Caricati {len(reasoning_symbols)} simboli reasoning")
            
        except Exception as e:
            logging.error(f"Errore caricamento simboli reasoning: {e}")
            
        return reasoning_symbols
        
    def analyze_ast_node(self, node: ast.AST) -> List[str]:
        """Analizza un nodo AST e restituisce simboli reasoning applicabili."""
        node_type = type(node).__name__
        full_node_type = f"ast.{node_type}"
        
        # Cerca mapping diretto
        if full_node_type in self.ast_reasoning_mappings:
            return self.ast_reasoning_mappings[full_node_type]
            
        # Mapping per categorie generiche
        generic_mappings = {
            "BinOp": ["ng:reasoning:synthesis", "ng:reasoning:evaluation"],
            "UnaryOp": ["ng:reasoning:contrapositive", "ng:reasoning:evaluation"],
            "Constant": ["ng:reasoning:premise", "ng:reasoning:axiom"],
            "Name": ["ng:reasoning:premise", "ng:reasoning:hypothesis"],
            "Load": ["ng:reasoning:premise", "ng:reasoning:analysis"],
            "Store": ["ng:reasoning:conclusion", "ng:reasoning:synthesis"],
            "Del": ["ng:reasoning:contradiction", "ng:reasoning:error_correction"]
        }
        
        if node_type in generic_mappings:
            return generic_mappings[node_type]
            
        # Default reasoning pattern
        return ["ng:reasoning:analysis", "ng:reasoning:pattern"]
        
    def analyze_python_code(self, code: str) -> Dict[str, Any]:
        """Analizza codice Python e mappa pattern AST a simboli reasoning."""
        try:
            tree = ast.parse(code)
            
            analysis = {
                "code": code,
                "ast_nodes": [],
                "reasoning_patterns": {},
                "symbol_mappings": {},
                "complexity_score": 0,
                "reasoning_depth": 0
            }
            
            # Analizza tutti i nodi AST
            for node in ast.walk(tree):
                node_type = f"ast.{type(node).__name__}"
                reasoning_codes = self.analyze_ast_node(node)
                
                analysis["ast_nodes"].append({
                    "type": node_type,
                    "line": getattr(node, 'lineno', 0),
                    "reasoning_codes": reasoning_codes
                })
                
                # Accumula pattern di ragionamento
                for code in reasoning_codes:
                    if code in analysis["reasoning_patterns"]:
                        analysis["reasoning_patterns"][code] += 1
                    else:
                        analysis["reasoning_patterns"][code] = 1
                        
                    # Mappa a simbolo se disponibile
                    if code in self.reasoning_symbols:
                        symbol_data = self.reasoning_symbols[code]
                        analysis["symbol_mappings"][code] = {
                            "symbol": symbol_data.get("symbol", ""),
                            "id": symbol_data.get("id", ""),
                            "fallback": symbol_data.get("fallback", "")
                        }
                        
            # Calcola metriche
            analysis["complexity_score"] = len(analysis["ast_nodes"])
            analysis["reasoning_depth"] = len(analysis["reasoning_patterns"])
            
            return analysis
            
        except SyntaxError as e:
            logging.error(f"Errore parsing codice: {e}")
            return {"error": str(e)}
        except Exception as e:
            logging.error(f"Errore analisi AST: {e}")
            return {"error": str(e)}
            
    def generate_neuroglyphs_representation(self, analysis: Dict[str, Any]) -> str:
        """Genera rappresentazione in neuroglifi del codice."""
        if "error" in analysis:
            return f"[ERROR: {analysis['error']}]"
            
        neuroglyphs = []
        
        # Ordina pattern per frequenza
        sorted_patterns = sorted(
            analysis["reasoning_patterns"].items(),
            key=lambda x: x[1],
            reverse=True
        )
        
        for code, frequency in sorted_patterns:
            if code in analysis["symbol_mappings"]:
                symbol = analysis["symbol_mappings"][code]["symbol"]
                neuroglyphs.append(f"{symbol}×{frequency}")
            else:
                fallback = code.split(":")[-1].upper()
                neuroglyphs.append(f"[{fallback}]×{frequency}")
                
        return " ".join(neuroglyphs)
        
    def save_mapping(self):
        """Salva mapping AST → Reasoning in file JSON."""
        mapping_data = {
            "version": "1.0",
            "description": "NEUROGLYPH ULTRA - AST to Reasoning Symbols Mapping",
            "created": "2025-05-23",
            "ast_reasoning_mappings": self.ast_reasoning_mappings,
            "reasoning_symbols_count": len(self.reasoning_symbols),
            "mapped_ast_patterns": len(self.ast_reasoning_mappings)
        }
        
        try:
            with open(self.mapping_path, 'w', encoding='utf-8') as f:
                json.dump(mapping_data, f, indent=2, ensure_ascii=False)
                
            logging.info(f"💾 Mapping salvato in: {self.mapping_path}")
            
        except Exception as e:
            logging.error(f"Errore salvataggio mapping: {e}")


def main():
    """Main function per mapping AST → Reasoning."""
    print("🧠 NEUROGLYPH ULTRA - Fase 2: Mapping AST → Simboli Reasoning")
    print("="*70)
    print("🔗 Collegamento simboli reasoning a pattern ast.*")
    print("📊 Esempio: ast.Compare → ng:reasoning:comparison")
    print("="*70)
    
    # Crea directory logs se non esiste
    Path("logs").mkdir(exist_ok=True)
    
    # Inizializza mapper
    mapper = ASTReasoningMapper()
    
    # Salva mapping
    mapper.save_mapping()
    
    # Test con codice di esempio
    test_code = '''
def fibonacci(n):
    if n <= 1:
        return n
    else:
        return fibonacci(n-1) + fibonacci(n-2)

result = fibonacci(10)
print(f"Fibonacci(10) = {result}")
'''
    
    print("\n🧪 TEST MAPPING CON CODICE FIBONACCI:")
    print("-" * 50)
    print(test_code)
    print("-" * 50)
    
    # Analizza codice
    analysis = mapper.analyze_python_code(test_code)
    
    if "error" not in analysis:
        print(f"📊 Nodi AST trovati: {len(analysis['ast_nodes'])}")
        print(f"🧠 Pattern reasoning: {analysis['reasoning_depth']}")
        print(f"⚡ Complessità: {analysis['complexity_score']}")
        print()
        
        print("🔗 PATTERN REASONING IDENTIFICATI:")
        for code, frequency in analysis["reasoning_patterns"].items():
            if code in analysis["symbol_mappings"]:
                symbol_info = analysis["symbol_mappings"][code]
                print(f"  • {symbol_info['symbol']} {code} (×{frequency})")
            else:
                print(f"  • [MISSING] {code} (×{frequency})")
        print()
        
        # Genera rappresentazione neuroglifi
        neuroglyphs = mapper.generate_neuroglyphs_representation(analysis)
        print("🧠 RAPPRESENTAZIONE NEUROGLIFI:")
        print(f"  {neuroglyphs}")
        print()
        
    else:
        print(f"❌ Errore analisi: {analysis['error']}")
        
    print("🎉 FASE 2 COMPLETATA - AST REASONING MAPPING READY!")
    return 0


if __name__ == "__main__":
    exit(main())
