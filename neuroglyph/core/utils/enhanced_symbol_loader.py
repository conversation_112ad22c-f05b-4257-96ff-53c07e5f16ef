#!/usr/bin/env python3
"""
NEUROGLYPH LLM - ENHANCED SYMBOL LOADER
=======================================

Caricatore simboli avanzato integrato con il Cognitive Runtime Engine.
Ottimizzato per performance ULTRA/GOD MODE con caricamento intelligente,
cache cognitiva e validazione in tempo reale.

Componenti:
- EnhancedSymbolLoader: Caricatore principale con cache intelligente
- CognitiveSymbolCache: <PERSON><PERSON> ottimizzata per domini cognitivi
- SymbolProfiler: Profilazione performance simboli
- DynamicSymbolValidator: Validazione dinamica e adattiva

Autore: NEUROGLYPH ULTRA Team
Data: 2025-05-26
Licenza: MIT
"""

import sys
import os
import json
import time
import logging
import hashlib
from datetime import datetime
from typing import Dict, List, Set, Any, Optional, Tuple
from dataclasses import dataclass, field
from enum import Enum
from pathlib import Path
from collections import defaultdict, deque

# Aggiungi path per import
current_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.append(current_dir)

# Import componenti NEUROGLYPH
try:
    from load_symbols import UltraSymbolLoader
    from cognitive_state import CognitiveDomainType
except ImportError:
    logger.warning("⚠️ Alcuni componenti non disponibili - funzionalità limitate")
    UltraSymbolLoader = None
    CognitiveDomainType = None

# Setup logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class LoadingStrategy(Enum):
    """Strategie di caricamento simboli."""
    LAZY = "lazy"              # Caricamento on-demand
    EAGER = "eager"            # Caricamento completo immediato
    COGNITIVE = "cognitive"    # Caricamento basato su domini cognitivi
    ADAPTIVE = "adaptive"      # Caricamento adattivo basato su uso

class SymbolPriority(Enum):
    """Priorità simboli per caricamento."""
    CRITICAL = 1    # Simboli critici (sempre caricati)
    HIGH = 2        # Alta priorità (caricati per primi)
    MEDIUM = 3      # Media priorità (caricati su richiesta)
    LOW = 4         # Bassa priorità (caricati se necessario)

@dataclass
class SymbolProfile:
    """Profilo di performance di un simbolo."""
    symbol: str
    usage_count: int = 0
    last_used: Optional[datetime] = None
    cognitive_domains: Set[str] = field(default_factory=set)
    encoding_efficiency: float = 1.0
    decoding_efficiency: float = 1.0
    priority: SymbolPriority = SymbolPriority.MEDIUM
    metadata: Dict[str, Any] = field(default_factory=dict)

@dataclass
class LoadingMetrics:
    """Metriche di caricamento simboli."""
    total_symbols: int = 0
    loaded_symbols: int = 0
    cached_symbols: int = 0
    loading_time: float = 0.0
    cache_hit_rate: float = 0.0
    memory_usage: int = 0
    cognitive_coverage: float = 0.0

class CognitiveSymbolCache:
    """Cache intelligente per simboli basata su domini cognitivi."""

    def __init__(self, max_size: int = 1000):
        """Inizializza cache cognitiva."""
        self.max_size = max_size
        self.cache: Dict[str, Any] = {}
        self.domain_cache: Dict[str, Set[str]] = defaultdict(set)
        self.access_history: deque = deque(maxlen=max_size)
        self.hit_count = 0
        self.miss_count = 0

    def get(self, symbol: str) -> Optional[Any]:
        """Recupera simbolo dalla cache."""
        if symbol in self.cache:
            self.hit_count += 1
            self.access_history.append(symbol)
            return self.cache[symbol]

        self.miss_count += 1
        return None

    def put(self, symbol: str, data: Any, domains: Optional[Set[str]] = None):
        """Inserisce simbolo nella cache."""
        # Gestione overflow cache
        if len(self.cache) >= self.max_size:
            self._evict_lru()

        self.cache[symbol] = data
        self.access_history.append(symbol)

        # Aggiorna cache domini
        if domains:
            for domain in domains:
                self.domain_cache[domain].add(symbol)

    def get_by_domain(self, domain: str) -> List[Any]:
        """Recupera tutti i simboli di un dominio."""
        symbols = self.domain_cache.get(domain, set())
        return [self.cache[s] for s in symbols if s in self.cache]

    def _evict_lru(self):
        """Rimuove simbolo meno recentemente usato."""
        if self.access_history:
            # Trova il simbolo più vecchio ancora in cache
            for symbol in self.access_history:
                if symbol in self.cache:
                    del self.cache[symbol]
                    # Rimuovi da cache domini
                    for domain_symbols in self.domain_cache.values():
                        domain_symbols.discard(symbol)
                    break

    @property
    def hit_rate(self) -> float:
        """Calcola tasso di hit della cache."""
        total = self.hit_count + self.miss_count
        return self.hit_count / total if total > 0 else 0.0

    def clear(self):
        """Pulisce la cache."""
        self.cache.clear()
        self.domain_cache.clear()
        self.access_history.clear()
        self.hit_count = 0
        self.miss_count = 0

class EnhancedSymbolLoader:
    """
    Caricatore simboli avanzato per NEUROGLYPH LLM.

    Caratteristiche ULTRA/GOD MODE:
    - Caricamento intelligente basato su domini cognitivi
    - Cache ottimizzata per performance
    - Profilazione simboli in tempo reale
    - Validazione dinamica e adattiva
    - Integrazione con Cognitive Runtime Engine
    """

    def __init__(self,
                 registry_path: str = "neuroglyph/core/locked_registry_godmode_v9.json",
                 strategy: LoadingStrategy = LoadingStrategy.COGNITIVE,
                 cache_size: int = 1000):
        """
        Inizializza Enhanced Symbol Loader.

        Args:
            registry_path: Percorso al registry simbolico
            strategy: Strategia di caricamento
            cache_size: Dimensione cache simboli
        """
        self.registry_path = registry_path
        self.strategy = strategy

        # Componenti principali
        self.cache = CognitiveSymbolCache(cache_size)
        self.base_loader = UltraSymbolLoader() if UltraSymbolLoader else None

        # Stato interno
        self.symbol_profiles: Dict[str, SymbolProfile] = {}
        self.domain_mappings: Dict[str, Set[str]] = defaultdict(set)
        self.loading_metrics = LoadingMetrics()
        self.is_loaded = False

        # Configurazione strategia
        self._configure_strategy(strategy)

        logger.info(f"🧠 Enhanced Symbol Loader inizializzato")
        logger.info(f"📊 Strategia: {strategy.value}, Cache: {cache_size}")

    def _configure_strategy(self, strategy: LoadingStrategy):
        """Configura parametri per strategia di caricamento."""
        if strategy == LoadingStrategy.LAZY:
            self.preload_critical = True
            self.preload_domains = False
            self.adaptive_loading = False

        elif strategy == LoadingStrategy.EAGER:
            self.preload_critical = True
            self.preload_domains = True
            self.adaptive_loading = False

        elif strategy == LoadingStrategy.COGNITIVE:
            self.preload_critical = True
            self.preload_domains = True
            self.adaptive_loading = True

        elif strategy == LoadingStrategy.ADAPTIVE:
            self.preload_critical = True
            self.preload_domains = False
            self.adaptive_loading = True

    def load_symbols(self, force_reload: bool = False) -> LoadingMetrics:
        """
        Carica simboli usando strategia configurata.

        Args:
            force_reload: Forza ricaricamento anche se già caricato

        Returns:
            Metriche di caricamento
        """
        if self.is_loaded and not force_reload:
            logger.info("📊 Simboli già caricati, uso cache esistente")
            return self.loading_metrics

        start_time = time.time()
        logger.info(f"🚀 Caricamento simboli: strategia {self.strategy.value}")

        try:
            # Carica registry base
            registry_data = self._load_registry()

            # Processa simboli secondo strategia
            if self.strategy == LoadingStrategy.LAZY:
                self._load_lazy(registry_data)
            elif self.strategy == LoadingStrategy.EAGER:
                self._load_eager(registry_data)
            elif self.strategy == LoadingStrategy.COGNITIVE:
                self._load_cognitive(registry_data)
            elif self.strategy == LoadingStrategy.ADAPTIVE:
                self._load_adaptive(registry_data)

            # Aggiorna metriche
            loading_time = time.time() - start_time
            self._update_loading_metrics(loading_time)

            self.is_loaded = True

            logger.info(f"✅ Caricamento completato in {loading_time:.2f}s")
            logger.info(f"📊 Simboli caricati: {self.loading_metrics.loaded_symbols}/{self.loading_metrics.total_symbols}")
            logger.info(f"🧠 Copertura cognitiva: {self.loading_metrics.cognitive_coverage:.1%}")

        except Exception as e:
            logger.error(f"❌ Errore caricamento simboli: {e}")
            raise

        return self.loading_metrics

    def _load_registry(self) -> Dict[str, Any]:
        """Carica registry simbolico."""
        try:
            with open(self.registry_path, 'r', encoding='utf-8') as f:
                registry_data = json.load(f)

            self.loading_metrics.total_symbols = len(registry_data.get('approved_symbols', []))
            logger.debug(f"📂 Registry caricato: {self.loading_metrics.total_symbols} simboli")

            return registry_data

        except Exception as e:
            logger.error(f"❌ Errore caricamento registry: {e}")
            raise

    def _load_lazy(self, registry_data: Dict[str, Any]):
        """Caricamento lazy: solo simboli critici."""
        symbols = registry_data.get('approved_symbols', [])

        for symbol_data in symbols:
            # Carica solo simboli critici
            if self._is_critical_symbol(symbol_data):
                self._load_symbol(symbol_data, SymbolPriority.CRITICAL)

    def _load_eager(self, registry_data: Dict[str, Any]):
        """Caricamento eager: tutti i simboli."""
        symbols = registry_data.get('approved_symbols', [])

        for symbol_data in symbols:
            priority = self._determine_symbol_priority(symbol_data)
            self._load_symbol(symbol_data, priority)

    def _load_cognitive(self, registry_data: Dict[str, Any]):
        """Caricamento cognitivo: basato su domini."""
        symbols = registry_data.get('approved_symbols', [])

        # Raggruppa per domini cognitivi
        domain_symbols = defaultdict(list)
        for symbol_data in symbols:
            if symbol_data.get('cognitive_domain', False):
                category = symbol_data.get('category', 'unknown')
                domain_symbols[category].append(symbol_data)

        # Carica per domini prioritari
        priority_domains = ['logic', 'reasoning', 'memory', 'structure', 'flow']

        for domain in priority_domains:
            if domain in domain_symbols:
                for symbol_data in domain_symbols[domain]:
                    self._load_symbol(symbol_data, SymbolPriority.HIGH)

        # Carica altri domini cognitivi
        for domain, symbols_list in domain_symbols.items():
            if domain not in priority_domains:
                for symbol_data in symbols_list:
                    self._load_symbol(symbol_data, SymbolPriority.MEDIUM)

    def _load_adaptive(self, registry_data: Dict[str, Any]):
        """Caricamento adattivo: basato su uso storico."""
        symbols = registry_data.get('approved_symbols', [])

        # Ordina per priorità adattiva
        sorted_symbols = sorted(symbols, key=self._calculate_adaptive_priority, reverse=True)

        # Carica top 50% immediatamente
        immediate_count = len(sorted_symbols) // 2

        for i, symbol_data in enumerate(sorted_symbols):
            if i < immediate_count:
                self._load_symbol(symbol_data, SymbolPriority.HIGH)
            else:
                # Registra per caricamento on-demand
                self._register_for_lazy_loading(symbol_data)

    def _load_symbol(self, symbol_data: Dict[str, Any], priority: SymbolPriority):
        """Carica un singolo simbolo."""
        symbol = symbol_data.get('symbol', '')
        category = symbol_data.get('category', '')

        # Crea profilo simbolo
        profile = SymbolProfile(
            symbol=symbol,
            cognitive_domains={category} if symbol_data.get('cognitive_domain', False) else set(),
            priority=priority,
            metadata=symbol_data
        )

        self.symbol_profiles[symbol] = profile

        # Aggiorna mappature domini
        if category:
            self.domain_mappings[category].add(symbol)

        # Inserisci in cache
        self.cache.put(symbol, symbol_data, profile.cognitive_domains)

        self.loading_metrics.loaded_symbols += 1

    def _is_critical_symbol(self, symbol_data: Dict[str, Any]) -> bool:
        """Verifica se un simbolo è critico."""
        # Simboli critici: alta frequenza, domini essenziali
        category = symbol_data.get('category', '')
        score = symbol_data.get('score', 0)

        critical_categories = {'logic', 'reasoning', 'memory', 'structure', 'operator'}
        return category in critical_categories or score >= 98.0

    def _determine_symbol_priority(self, symbol_data: Dict[str, Any]) -> SymbolPriority:
        """Determina priorità di un simbolo."""
        if self._is_critical_symbol(symbol_data):
            return SymbolPriority.CRITICAL

        score = symbol_data.get('score', 0)
        cognitive_domain = symbol_data.get('cognitive_domain', False)

        if score >= 95.0 and cognitive_domain:
            return SymbolPriority.HIGH
        elif score >= 90.0:
            return SymbolPriority.MEDIUM
        else:
            return SymbolPriority.LOW

    def _calculate_adaptive_priority(self, symbol_data: Dict[str, Any]) -> float:
        """Calcola priorità adattiva basata su metriche multiple."""
        score = symbol_data.get('score', 0)
        cognitive_domain = symbol_data.get('cognitive_domain', False)
        category = symbol_data.get('category', '')

        # Peso base dal score
        priority = score / 100.0

        # Bonus per domini cognitivi
        if cognitive_domain:
            priority += 0.2

        # Bonus per categorie prioritarie
        priority_categories = {'logic': 0.3, 'reasoning': 0.3, 'memory': 0.2, 'structure': 0.2}
        priority += priority_categories.get(category, 0.0)

        return priority

    def _register_for_lazy_loading(self, symbol_data: Dict[str, Any]):
        """Registra simbolo per caricamento lazy."""
        symbol = symbol_data.get('symbol', '')
        # Salva metadata per caricamento futuro
        self.symbol_profiles[symbol] = SymbolProfile(
            symbol=symbol,
            priority=SymbolPriority.LOW,
            metadata=symbol_data
        )

    def _update_loading_metrics(self, loading_time: float):
        """Aggiorna metriche di caricamento."""
        self.loading_metrics.loading_time = loading_time
        self.loading_metrics.cached_symbols = len(self.cache.cache)
        self.loading_metrics.cache_hit_rate = self.cache.hit_rate

        # Calcola copertura cognitiva
        cognitive_symbols = sum(1 for p in self.symbol_profiles.values()
                              if p.cognitive_domains)
        self.loading_metrics.cognitive_coverage = (
            cognitive_symbols / max(1, self.loading_metrics.total_symbols)
        )

    def get_symbol(self, symbol: str) -> Optional[Dict[str, Any]]:
        """Recupera simbolo con caricamento on-demand."""
        # Prova cache prima
        cached = self.cache.get(symbol)
        if cached:
            self._update_symbol_usage(symbol)
            return cached

        # Caricamento on-demand se registrato
        if symbol in self.symbol_profiles:
            profile = self.symbol_profiles[symbol]
            if profile.metadata:
                self._load_symbol(profile.metadata, profile.priority)
                return profile.metadata

        return None

    def get_symbols_by_domain(self, domain: str) -> List[Dict[str, Any]]:
        """Recupera tutti i simboli di un dominio cognitivo."""
        # Usa cache domini se disponibile
        cached_symbols = self.cache.get_by_domain(domain)
        if cached_symbols:
            return cached_symbols

        # Carica simboli del dominio se necessario
        domain_symbols = []
        for symbol in self.domain_mappings.get(domain, set()):
            symbol_data = self.get_symbol(symbol)
            if symbol_data:
                domain_symbols.append(symbol_data)

        return domain_symbols

    def get_symbols_by_priority(self, priority: SymbolPriority) -> List[Dict[str, Any]]:
        """Recupera simboli per priorità."""
        symbols = []
        for profile in self.symbol_profiles.values():
            if profile.priority == priority and profile.metadata:
                symbols.append(profile.metadata)

        return symbols

    def _update_symbol_usage(self, symbol: str):
        """Aggiorna statistiche uso simbolo."""
        if symbol in self.symbol_profiles:
            profile = self.symbol_profiles[symbol]
            profile.usage_count += 1
            profile.last_used = datetime.now()

    def get_loading_report(self) -> str:
        """Genera report dettagliato del caricamento."""
        metrics = self.loading_metrics

        report = [
            "🧠 NEUROGLYPH Enhanced Symbol Loader - Report",
            "=" * 50,
            f"📊 Strategia: {self.strategy.value}",
            f"📂 Registry: {self.registry_path}",
            f"⏱️  Tempo caricamento: {metrics.loading_time:.2f}s",
            "",
            "📈 Metriche Caricamento:",
            f"  • Simboli totali: {metrics.total_symbols}",
            f"  • Simboli caricati: {metrics.loaded_symbols}",
            f"  • Simboli in cache: {metrics.cached_symbols}",
            f"  • Tasso hit cache: {metrics.cache_hit_rate:.1%}",
            f"  • Copertura cognitiva: {metrics.cognitive_coverage:.1%}",
            "",
            "🧠 Domini Cognitivi:",
        ]

        # Aggiungi statistiche domini
        for domain, symbols in self.domain_mappings.items():
            report.append(f"  • {domain}: {len(symbols)} simboli")

        # Aggiungi statistiche priorità
        report.append("\n🎯 Distribuzione Priorità:")
        priority_counts = defaultdict(int)
        for profile in self.symbol_profiles.values():
            priority_counts[profile.priority] += 1

        for priority, count in priority_counts.items():
            report.append(f"  • {priority.name}: {count} simboli")

        # Simboli più usati
        report.append("\n🔥 Simboli Più Usati:")
        top_symbols = sorted(
            self.symbol_profiles.values(),
            key=lambda p: p.usage_count,
            reverse=True
        )[:5]

        for profile in top_symbols:
            if profile.usage_count > 0:
                report.append(f"  • {profile.symbol}: {profile.usage_count} usi")

        return "\n".join(report)

    def optimize_cache(self):
        """Ottimizza cache basata su pattern di uso."""
        # Promuovi simboli frequentemente usati
        for profile in self.symbol_profiles.values():
            if profile.usage_count > 10:  # Soglia uso frequente
                if profile.priority != SymbolPriority.CRITICAL:
                    profile.priority = SymbolPriority.HIGH

                # Assicurati che sia in cache
                if profile.metadata and profile.symbol not in self.cache.cache:
                    self.cache.put(profile.symbol, profile.metadata, profile.cognitive_domains)

    def clear_cache(self):
        """Pulisce cache mantenendo simboli critici."""
        critical_symbols = {}

        # Salva simboli critici
        for symbol, profile in self.symbol_profiles.items():
            if profile.priority == SymbolPriority.CRITICAL:
                cached_data = self.cache.get(symbol)
                if cached_data:
                    critical_symbols[symbol] = (cached_data, profile.cognitive_domains)

        # Pulisce cache
        self.cache.clear()

        # Ripristina simboli critici
        for symbol, (data, domains) in critical_symbols.items():
            self.cache.put(symbol, data, domains)

        logger.info(f"🧹 Cache pulita, mantenuti {len(critical_symbols)} simboli critici")

    def reload_symbols(self, strategy: Optional[LoadingStrategy] = None):
        """Ricarica simboli con nuova strategia opzionale."""
        if strategy:
            self.strategy = strategy
            self._configure_strategy(strategy)

        # Reset stato
        self.is_loaded = False
        self.symbol_profiles.clear()
        self.domain_mappings.clear()
        self.clear_cache()

        # Ricarica
        return self.load_symbols(force_reload=True)


# Funzioni di utilità per uso esterno
def create_enhanced_symbol_loader(
    registry_path: str = "neuroglyph/core/locked_registry_godmode_v9.json",
    strategy: LoadingStrategy = LoadingStrategy.COGNITIVE
) -> EnhancedSymbolLoader:
    """Crea Enhanced Symbol Loader con configurazione ottimale."""
    return EnhancedSymbolLoader(registry_path, strategy)


def load_symbols_for_cognitive_integration(
    registry_path: str = "neuroglyph/core/locked_registry_godmode_v9.json"
) -> Tuple[EnhancedSymbolLoader, LoadingMetrics]:
    """Carica simboli ottimizzati per integrazione cognitiva."""
    loader = create_enhanced_symbol_loader(registry_path, LoadingStrategy.COGNITIVE)
    metrics = loader.load_symbols()
    return loader, metrics


if __name__ == "__main__":
    # Test Enhanced Symbol Loader
    print("🧠 NEUROGLYPH Enhanced Symbol Loader - Test")
    print("=" * 60)

    try:
        # Test con registry ridotto per velocità
        test_registry = "test_registry.json"

        # Crea loader
        loader = create_enhanced_symbol_loader(test_registry, LoadingStrategy.COGNITIVE)

        # Carica simboli
        metrics = loader.load_symbols()

        # Report
        print(loader.get_loading_report())

        # Test recupero simboli
        print(f"\n🧪 Test recupero simboli:")
        test_symbol = "◯"
        symbol_data = loader.get_symbol(test_symbol)
        print(f"  • Simbolo {test_symbol}: {'✅ Trovato' if symbol_data else '❌ Non trovato'}")

        # Test domini
        logic_symbols = loader.get_symbols_by_domain("logic")
        print(f"  • Domini logic: {len(logic_symbols)} simboli")

        print("\n✅ Test completato con successo!")

    except Exception as e:
        print(f"❌ Errore durante test: {e}")
        import traceback
        traceback.print_exc()
