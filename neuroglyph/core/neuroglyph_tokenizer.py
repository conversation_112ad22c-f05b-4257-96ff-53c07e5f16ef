#!/usr/bin/env python3
"""
NEUROGLYPH LLM - Tokenizer Simbolico
===================================

Estensione del tokenizer Qwen per supporto nativo dei 2K simboli neuroglifi.
Questo è essenziale per "zero allucinazioni" - il modello deve conoscere 
perfettamente il significato di ogni simbolo.
"""

import json
import torch
from typing import Dict, List, Tuple, Optional, Any
from transformers import AutoTokenizer
from pathlib import Path

class NeuroglyphTokenizer:
    """
    Tokenizer esteso per simboli neuroglifi
    
    Combina il tokenizer base Qwen con vocabolario simbolico dedicato
    per garantire comprensione perfetta dei neuroglifi.
    """
    
    def __init__(self, 
                 base_model: str = "Qwen/Qwen2.5-Coder-1.5B-Instruct",
                 symbols_registry_path: str = "symbols_registry.json"):
        
        # Carica tokenizer base
        self.base_tokenizer = AutoTokenizer.from_pretrained(base_model)
        
        # Carica registry simboli
        self.symbols_registry = self._load_symbols_registry(symbols_registry_path)
        
        # Crea mappings simboli
        self.symbol_to_id = {}
        self.id_to_symbol = {}
        self.symbol_embeddings = {}
        
        self._build_symbol_mappings()
        
        # Tokens speciali neuroglifi
        self.special_tokens = {
            "<NG_START>": None,
            "<NG_END>": None,
            "<SCOPE>": None,
            "<SEM>": None,
            "<COMP>": None
        }
        
        self._add_special_tokens()
    
    def _load_symbols_registry(self, path: str) -> Dict[str, Any]:
        """Carica registry simboli neuroglifi"""
        try:
            with open(path, 'r', encoding='utf-8') as f:
                return json.load(f)
        except FileNotFoundError:
            print(f"⚠️ Registry simboli non trovato: {path}")
            return {"symbols": []}
    
    def _build_symbol_mappings(self):
        """Costruisce mappings simbolo ↔ ID"""
        base_vocab_size = len(self.base_tokenizer.vocab)
        
        for i, symbol_data in enumerate(self.symbols_registry.get('symbols', [])):
            symbol = symbol_data.get('symbol', '')
            if symbol:
                # Assegna ID univoco oltre il vocabolario base
                symbol_id = base_vocab_size + i
                self.symbol_to_id[symbol] = symbol_id
                self.id_to_symbol[symbol_id] = symbol
                
                # Prepara embedding semantico
                self.symbol_embeddings[symbol] = {
                    'category': symbol_data.get('category', ''),
                    'meaning': symbol_data.get('meaning', ''),
                    'semantic_vector': self._compute_semantic_vector(symbol_data)
                }
    
    def _compute_semantic_vector(self, symbol_data: Dict) -> List[float]:
        """Computa vettore semantico per il simbolo"""
        # Implementazione semplificata - in produzione useremo embeddings più sofisticati
        category = symbol_data.get('category', '')
        meaning = symbol_data.get('meaning', '')
        
        # Hash semantico basato su categoria e significato
        semantic_hash = hash(f"{category}:{meaning}") % 1000
        
        # Genera vettore pseudo-casuale ma deterministico
        import random
        random.seed(semantic_hash)
        vector = [random.random() for _ in range(768)]  # Dimensione embedding Qwen
        
        return vector
    
    def _add_special_tokens(self):
        """Aggiunge tokens speciali per neuroglifi"""
        special_tokens_list = list(self.special_tokens.keys())
        
        # Aggiunge al tokenizer base
        num_added = self.base_tokenizer.add_special_tokens({
            'additional_special_tokens': special_tokens_list
        })
        
        print(f"✅ Aggiunti {num_added} tokens speciali neuroglifi")
        
        # Aggiorna mappings
        for token in special_tokens_list:
            if token in self.base_tokenizer.vocab:
                self.special_tokens[token] = self.base_tokenizer.vocab[token]
    
    def encode_neuroglyphs(self, text: str) -> List[int]:
        """
        Encoding specializzato per testo con neuroglifi
        
        Args:
            text: Testo contenente simboli neuroglifi
            
        Returns:
            Lista di token IDs con simboli neuroglifi mappati correttamente
        """
        tokens = []
        i = 0
        
        while i < len(text):
            char = text[i]
            
            # Controlla se è un simbolo neuroglifo
            if char in self.symbol_to_id:
                tokens.append(self.symbol_to_id[char])
                i += 1
            else:
                # Trova la sequenza più lunga di caratteri non-neuroglifi
                j = i
                while j < len(text) and text[j] not in self.symbol_to_id:
                    j += 1
                
                # Tokenizza la sequenza normale
                normal_text = text[i:j]
                if normal_text:
                    normal_tokens = self.base_tokenizer.encode(
                        normal_text, add_special_tokens=False
                    )
                    tokens.extend(normal_tokens)
                
                i = j
        
        return tokens
    
    def decode_neuroglyphs(self, token_ids: List[int]) -> str:
        """
        Decoding specializzato per tokens con neuroglifi
        
        Args:
            token_ids: Lista di token IDs
            
        Returns:
            Testo decodificato con simboli neuroglifi
        """
        result = []
        normal_tokens = []
        
        for token_id in token_ids:
            if token_id in self.id_to_symbol:
                # È un simbolo neuroglifo
                if normal_tokens:
                    # Decodifica tokens normali accumulati
                    normal_text = self.base_tokenizer.decode(normal_tokens)
                    result.append(normal_text)
                    normal_tokens = []
                
                # Aggiungi simbolo neuroglifo
                result.append(self.id_to_symbol[token_id])
            else:
                # Token normale - accumula
                normal_tokens.append(token_id)
        
        # Decodifica tokens normali rimanenti
        if normal_tokens:
            normal_text = self.base_tokenizer.decode(normal_tokens)
            result.append(normal_text)
        
        return ''.join(result)
    
    def get_extended_vocab_size(self) -> int:
        """Dimensione vocabolario esteso (base + neuroglifi)"""
        return len(self.base_tokenizer.vocab) + len(self.symbol_to_id)
    
    def create_embedding_matrix(self, base_embeddings: torch.Tensor) -> torch.Tensor:
        """
        Crea matrice embedding estesa con simboli neuroglifi
        
        Args:
            base_embeddings: Embeddings del modello base
            
        Returns:
            Matrice embedding estesa
        """
        base_size, embed_dim = base_embeddings.shape
        extended_size = self.get_extended_vocab_size()
        
        # Crea matrice estesa
        extended_embeddings = torch.zeros(extended_size, embed_dim)
        
        # Copia embeddings base
        extended_embeddings[:base_size] = base_embeddings
        
        # Inizializza embeddings neuroglifi
        for symbol, symbol_id in self.symbol_to_id.items():
            if symbol in self.symbol_embeddings:
                semantic_vector = self.symbol_embeddings[symbol]['semantic_vector']
                # Converte a tensor e normalizza
                embedding = torch.tensor(semantic_vector[:embed_dim], dtype=torch.float32)
                embedding = torch.nn.functional.normalize(embedding, dim=0)
                extended_embeddings[symbol_id] = embedding
        
        return extended_embeddings
    
    def validate_round_trip(self, text: str) -> Tuple[bool, float, str]:
        """
        Valida round-trip encoding/decoding
        
        Args:
            text: Testo originale con neuroglifi
            
        Returns:
            (successo, similarità, testo_ricostruito)
        """
        try:
            # Encode
            tokens = self.encode_neuroglyphs(text)
            
            # Decode
            reconstructed = self.decode_neuroglyphs(tokens)
            
            # Calcola similarità
            similarity = self._compute_similarity(text, reconstructed)
            
            success = similarity > 0.99  # Soglia alta per round-trip
            
            return success, similarity, reconstructed
            
        except Exception as e:
            print(f"❌ Errore round-trip: {e}")
            return False, 0.0, ""
    
    def _compute_similarity(self, text1: str, text2: str) -> float:
        """Calcola similarità tra due testi"""
        if text1 == text2:
            return 1.0
        
        # Similarità basata su caratteri comuni
        if len(text1) == 0 and len(text2) == 0:
            return 1.0
        if len(text1) == 0 or len(text2) == 0:
            return 0.0
        
        common_chars = sum(1 for c1, c2 in zip(text1, text2) if c1 == c2)
        max_len = max(len(text1), len(text2))
        
        return common_chars / max_len

def demo_neuroglyph_tokenizer():
    """Demo del tokenizer neuroglifi"""
    print("🧩 NEUROGLYPH LLM - Tokenizer Simbolico Demo")
    print("=" * 60)
    
    # Inizializza tokenizer
    tokenizer = NeuroglyphTokenizer()
    
    print(f"📊 Vocabolario base: {len(tokenizer.base_tokenizer.vocab)}")
    print(f"📊 Simboli neuroglifi: {len(tokenizer.symbol_to_id)}")
    print(f"📊 Vocabolario esteso: {tokenizer.get_extended_vocab_size()}")
    
    # Test encoding/decoding
    test_texts = [
        "def add(a, b): return a + b",
        "⟨⟩α⊕β⤴α⊕β",
        "def fibonacci(n): ⟨⟩ƒ⟨n⟩⤴n≤1?n:ƒ⟨n-1⟩⊕ƒ⟨n-2⟩",
        "<NG_START>⟨⟩α⊕β⤴α⊕β<NG_END>"
    ]
    
    for i, text in enumerate(test_texts):
        print(f"\n🧪 Test {i+1}: {text[:50]}...")
        
        # Encode
        tokens = tokenizer.encode_neuroglyphs(text)
        print(f"   Tokens: {len(tokens)} - {tokens[:10]}...")
        
        # Decode
        reconstructed = tokenizer.decode_neuroglyphs(tokens)
        print(f"   Ricostruito: {reconstructed[:50]}...")
        
        # Round-trip validation
        success, similarity, _ = tokenizer.validate_round_trip(text)
        print(f"   Round-trip: {'✅' if success else '❌'} ({similarity:.3f})")
    
    print("\n✅ Demo tokenizer completata!")

if __name__ == "__main__":
    demo_neuroglyph_tokenizer()
