#!/usr/bin/env python3
"""
NEUROGLYPH QWEN INTEGRATION
===========================

Integrazione diretta con Qwen2.5-Coder-1.5B-Instruct-bnb-4bit locale.
Fornisce il ponte linguistico per NEUROGLYPH.

Funzioni principali:
- Caricamento modello Qwen locale
- Conversione simboli → prompt
- Generazione testo da simboli
- Ottimizzazione per Mac M2

Autore: NEUROGLYPH ULTRA Team
Data: 2025-05-26
Licenza: MIT
"""

import sys
import os
import json
import time
import logging
from typing import Dict, List, Any, Optional, Tuple
from dataclasses import dataclass
from pathlib import Path

# Aggiungi path per import moduli NEUROGLYPH
current_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.append(current_dir)

# Configurazione logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Import condizionali per evitare errori se non installati
try:
    from transformers import AutoTokenizer, AutoModelForCausalLM, pipeline
    import torch
    TRANSFORMERS_AVAILABLE = True
    logger.info("✅ Transformers disponibile")
except ImportError:
    TRANSFORMERS_AVAILABLE = False
    logger.warning("⚠️ Transformers non disponibile - usando fallback")

@dataclass
class QwenConfig:
    """Configurazione per Qwen2.5-Coder."""
    model_path: str
    max_length: int = 512
    temperature: float = 0.3
    top_p: float = 0.9
    do_sample: bool = True
    pad_token_id: Optional[int] = None
    eos_token_id: Optional[int] = None

@dataclass
class QwenResponse:
    """Risposta da Qwen."""
    text: str
    success: bool
    confidence: float
    tokens_generated: int
    processing_time: float
    error_message: Optional[str] = None

class QwenLocalLLM:
    """
    Wrapper per Qwen2.5-Coder-1.5B-Instruct-bnb-4bit locale.
    
    Ottimizzato per Mac M2 con 8GB RAM.
    """
    
    def __init__(self, model_path: str = "models/base"):
        self.model_path = model_path
        self.model = None
        self.tokenizer = None
        self.pipeline = None
        self.config = QwenConfig(model_path=model_path)
        
        # Stato caricamento
        self.is_loaded = False
        self.load_error = None
        
        logger.info(f"🤖 Inizializzazione Qwen: {model_path}")
        
        # Carica modello se Transformers disponibile
        if TRANSFORMERS_AVAILABLE:
            self._load_model()
        else:
            logger.warning("⚠️ Transformers non disponibile - modalità fallback")

    def _load_model(self):
        """Carica il modello Qwen locale."""
        try:
            start_time = time.time()
            
            # Verifica esistenza modello
            model_path = Path(self.model_path)
            if not model_path.exists():
                raise FileNotFoundError(f"Modello non trovato: {self.model_path}")
            
            logger.info("📥 Caricamento tokenizer...")
            
            # Carica tokenizer
            self.tokenizer = AutoTokenizer.from_pretrained(
                self.model_path,
                trust_remote_code=True,
                local_files_only=True
            )
            
            # Configura pad token se mancante
            if self.tokenizer.pad_token is None:
                self.tokenizer.pad_token = self.tokenizer.eos_token
                self.config.pad_token_id = self.tokenizer.eos_token_id
            
            logger.info("🧠 Caricamento modello...")
            
            # Carica modello con ottimizzazioni per Mac M2
            device_map = "auto" if torch.cuda.is_available() else "cpu"
            
            self.model = AutoModelForCausalLM.from_pretrained(
                self.model_path,
                trust_remote_code=True,
                local_files_only=True,
                device_map=device_map,
                torch_dtype=torch.float16 if torch.cuda.is_available() else torch.float32,
                low_cpu_mem_usage=True
            )
            
            # Crea pipeline per generazione
            self.pipeline = pipeline(
                "text-generation",
                model=self.model,
                tokenizer=self.tokenizer,
                device_map=device_map,
                return_full_text=False
            )
            
            load_time = time.time() - start_time
            self.is_loaded = True
            
            logger.info(f"✅ Qwen caricato in {load_time:.1f}s")
            logger.info(f"🎯 Device: {device_map}")
            logger.info(f"📊 Vocab size: {self.tokenizer.vocab_size}")
            
        except Exception as e:
            self.load_error = str(e)
            self.is_loaded = False
            logger.error(f"❌ Errore caricamento Qwen: {e}")

    def generate(self, prompt: str, **kwargs) -> QwenResponse:
        """
        Genera testo usando Qwen.
        
        Args:
            prompt: Prompt di input
            **kwargs: Parametri aggiuntivi per generazione
            
        Returns:
            QwenResponse con risultato
        """
        start_time = time.time()
        
        # Fallback se modello non caricato
        if not self.is_loaded:
            return self._fallback_response(prompt, start_time)
        
        try:
            # Parametri generazione
            generation_params = {
                "max_length": kwargs.get("max_length", self.config.max_length),
                "temperature": kwargs.get("temperature", self.config.temperature),
                "top_p": kwargs.get("top_p", self.config.top_p),
                "do_sample": kwargs.get("do_sample", self.config.do_sample),
                "pad_token_id": self.config.pad_token_id,
                "eos_token_id": self.config.eos_token_id,
                "num_return_sequences": 1
            }
            
            # Genera usando pipeline
            logger.debug(f"🤖 Generazione: '{prompt[:50]}...'")
            
            outputs = self.pipeline(
                prompt,
                **generation_params
            )
            
            # Estrai testo generato
            if outputs and len(outputs) > 0:
                generated_text = outputs[0]["generated_text"].strip()
                
                # Calcola metriche
                tokens_generated = len(self.tokenizer.encode(generated_text))
                processing_time = time.time() - start_time
                
                # Stima confidenza (semplificata)
                confidence = min(0.9, 0.5 + (len(generated_text) / 200))
                
                logger.debug(f"✅ Generato: '{generated_text[:50]}...' ({tokens_generated} token)")
                
                return QwenResponse(
                    text=generated_text,
                    success=True,
                    confidence=confidence,
                    tokens_generated=tokens_generated,
                    processing_time=processing_time
                )
            else:
                return QwenResponse(
                    text="",
                    success=False,
                    confidence=0.0,
                    tokens_generated=0,
                    processing_time=time.time() - start_time,
                    error_message="Nessun output generato"
                )
                
        except Exception as e:
            logger.error(f"❌ Errore generazione Qwen: {e}")
            return QwenResponse(
                text="",
                success=False,
                confidence=0.0,
                tokens_generated=0,
                processing_time=time.time() - start_time,
                error_message=str(e)
            )

    def _fallback_response(self, prompt: str, start_time: float) -> QwenResponse:
        """Risposta fallback quando modello non disponibile."""
        # Analizza prompt per generare risposta appropriata
        prompt_lower = prompt.lower()
        
        if "logic" in prompt_lower or "reasoning" in prompt_lower:
            fallback_text = "Based on logical reasoning principles, this follows a structured approach to problem-solving."
        elif "math" in prompt_lower or "calculate" in prompt_lower:
            fallback_text = "Using mathematical analysis, we can approach this systematically."
        elif "creative" in prompt_lower or "innovative" in prompt_lower:
            fallback_text = "Through creative thinking, we can explore innovative solutions and approaches."
        elif "symbols" in prompt_lower:
            fallback_text = "The symbolic representation suggests a formal structure with logical relationships."
        else:
            fallback_text = "Based on the given context, we can analyze this through structured reasoning."
        
        return QwenResponse(
            text=fallback_text,
            success=True,
            confidence=0.6,  # Confidenza media per fallback
            tokens_generated=len(fallback_text.split()),
            processing_time=time.time() - start_time,
            error_message=f"Fallback mode: {self.load_error}" if self.load_error else "Fallback mode"
        )

    def symbols_to_prompt(self, symbols: List[str], context: str = "", domain: str = "default") -> str:
        """
        Converte simboli NEUROGLYPH in prompt per Qwen.
        
        Args:
            symbols: Lista simboli neuroglifi
            context: Contesto aggiuntivo
            domain: Dominio semantico
            
        Returns:
            Prompt ottimizzato per Qwen
        """
        # Template specifici per dominio
        domain_templates = {
            "logic": "Given the logical symbols {symbols}, explain the reasoning: {context}",
            "math": "Using mathematical symbols {symbols}, solve: {context}",
            "reasoning": "With symbolic reasoning using {symbols}, analyze: {context}",
            "cognitive": "From a cognitive perspective with {symbols}, consider: {context}",
            "creative": "Using creative symbols {symbols}, explore: {context}",
            "default": "Interpret these symbols {symbols} in context: {context}"
        }
        
        # Converte simboli in descrizione
        symbol_descriptions = []
        for symbol in symbols[:5]:  # Limita a 5 simboli per prompt clarity
            if len(symbol) == 1:
                # Simbolo Unicode singolo
                symbol_descriptions.append(f"'{symbol}' (U+{ord(symbol):04X})")
            else:
                # Simbolo multi-carattere o ID
                symbol_descriptions.append(f"'{symbol}'")
        
        symbols_text = ", ".join(symbol_descriptions)
        
        # Seleziona template
        template = domain_templates.get(domain, domain_templates["default"])
        
        # Crea prompt finale
        prompt = template.format(symbols=symbols_text, context=context)
        
        # Aggiungi istruzioni specifiche per Qwen
        qwen_prompt = f"""You are a symbolic reasoning AI. {prompt}

Provide a clear, logical explanation in natural language.
Focus on the meaning and relationships of the symbols.
Keep the response concise and informative.

Response:"""
        
        return qwen_prompt

    def get_model_info(self) -> Dict[str, Any]:
        """Ritorna informazioni sul modello."""
        info = {
            "model_path": self.model_path,
            "is_loaded": self.is_loaded,
            "transformers_available": TRANSFORMERS_AVAILABLE,
            "config": {
                "max_length": self.config.max_length,
                "temperature": self.config.temperature,
                "top_p": self.config.top_p
            }
        }
        
        if self.is_loaded and self.tokenizer:
            info.update({
                "vocab_size": self.tokenizer.vocab_size,
                "model_type": getattr(self.model.config, 'model_type', 'unknown'),
                "device": str(next(self.model.parameters()).device) if self.model else "unknown"
            })
        
        if self.load_error:
            info["load_error"] = self.load_error
            
        return info

    def test_generation(self) -> bool:
        """Test rapido di generazione."""
        test_prompt = "Hello, I am a symbolic reasoning AI."
        
        try:
            response = self.generate(test_prompt, max_length=50)
            
            if response.success and response.text:
                logger.info(f"✅ Test generazione riuscito: '{response.text[:50]}...'")
                return True
            else:
                logger.warning(f"⚠️ Test generazione fallito: {response.error_message}")
                return False
                
        except Exception as e:
            logger.error(f"❌ Errore test generazione: {e}")
            return False


# Funzioni di utilità
def create_qwen_llm(model_path: str = "models/base") -> QwenLocalLLM:
    """Crea istanza Qwen LLM."""
    return QwenLocalLLM(model_path)


if __name__ == "__main__":
    # Test Qwen Integration
    print("🤖 NEUROGLYPH Qwen Integration - Test")
    print("=" * 60)
    
    # Crea Qwen LLM
    qwen = create_qwen_llm()
    
    # Info modello
    info = qwen.get_model_info()
    print(f"\n📊 Model Info:")
    for key, value in info.items():
        print(f"  {key}: {value}")
    
    # Test generazione
    print(f"\n🧪 Test generazione...")
    test_success = qwen.test_generation()
    
    # Test simboli → prompt
    test_symbols = ["⊃", "∧", "→"]
    test_context = "logical reasoning about transitivity"
    prompt = qwen.symbols_to_prompt(test_symbols, test_context, "logic")
    
    print(f"\n🔣 Test simboli → prompt:")
    print(f"Simboli: {test_symbols}")
    print(f"Prompt: {prompt[:100]}...")
    
    # Test generazione da simboli
    if test_success:
        print(f"\n🤖 Test generazione da simboli...")
        response = qwen.generate(prompt, max_length=100)
        
        if response.success:
            print(f"✅ Risposta: {response.text}")
            print(f"📊 Confidenza: {response.confidence:.2f}")
            print(f"⏱️ Tempo: {response.processing_time:.2f}s")
        else:
            print(f"❌ Errore: {response.error_message}")
    
    print(f"\n🏁 Test completato")
