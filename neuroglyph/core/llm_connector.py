#!/usr/bin/env python3
"""
NEUROGLYPH LLM CONNECTOR
========================

Ponte tra il ragionamento simbolico di NEUROGLYPH e LLM tradizionali.
Permette a NEUROGLYPH di "parlare" convertendo simboli in linguaggio naturale.

Funzioni principali:
- Conversione NL → simboli (input preprocessing)
- Conversione simboli → NL (output postprocessing)
- Integrazione con Qwen2.5-Coder locale
- Validazione coerenza simbolica

Autore: NEUROGLYPH ULTRA Team
Data: 2025-05-26
Licenza: MIT
"""

import sys
import os
import json
import logging
import re
from typing import Dict, List, Any, Optional, Tuple
from dataclasses import dataclass
from enum import Enum

# Aggiungi path per import moduli NEUROGLYPH
current_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.append(current_dir)

# Import Qwen Integration
try:
    from qwen_integration import QwenLocalLLM
    QWEN_AVAILABLE = True
except ImportError:
    QWEN_AVAILABLE = False

# Import Ollama Integration
try:
    from ollama_integration import OllamaLLM
    OLLAMA_AVAILABLE = True
except ImportError:
    OLLAMA_AVAILABLE = False

# Configurazione logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class LLMBackend(Enum):
    """Backend LLM supportati."""
    MOCK = "mock"                    # Simulazione per test
    QWEN_LOCAL = "qwen_local"        # Qwen2.5-Coder locale
    OLLAMA = "ollama"                # Ollama API
    TRANSFORMERS = "transformers"    # HuggingFace Transformers

@dataclass
class LLMRequest:
    """Richiesta al LLM."""
    prompt: str
    symbols_context: List[str]
    domain: str
    temperature: float = 0.3
    max_tokens: int = 200

@dataclass
class LLMResponse:
    """Risposta dal LLM."""
    text: str
    success: bool
    confidence: float
    processing_time: float
    error_message: Optional[str] = None

class SymbolToPromptConverter:
    """Converte simboli NEUROGLYPH in prompt comprensibili per LLM."""

    def __init__(self):
        # Mapping simboli → descrizioni testuali
        self.symbol_meanings = {
            # Logica
            "⊃": "implies",
            "∧": "and",
            "∨": "or",
            "¬": "not",
            "∀": "for all",
            "∃": "exists",
            "→": "then",
            "↔": "if and only if",
            "⊬": "does not prove",
            "⊢": "proves",
            "∴": "therefore",

            # Matematica
            ">": "greater than",
            "<": "less than",
            "=": "equals",
            "≠": "not equals",
            "≥": "greater than or equal",
            "≤": "less than or equal",
            "∈": "belongs to",
            "∉": "does not belong to",
            "⊆": "subset of",
            "⊇": "superset of",

            # Controllo
            "■": "block",
            "◯": "circle",
            "△": "triangle",
            "▽": "inverted triangle",
            "◆": "diamond",
            "★": "star",

            # Fallback per simboli sconosciuti
        }

        # Template di prompt per domini
        self.domain_templates = {
            "logic": "Given the logical symbols {symbols}, explain the logical relationship: {context}",
            "math": "Given the mathematical symbols {symbols}, solve or explain: {context}",
            "reasoning": "Using symbolic reasoning with {symbols}, analyze: {context}",
            "cognitive": "From a cognitive perspective using {symbols}, consider: {context}",
            "default": "Interpret these symbols {symbols} in the context of: {context}"
        }

    def symbols_to_text(self, symbols: List[str]) -> str:
        """Converte lista di simboli in descrizione testuale."""
        if not symbols:
            return "no symbols"

        descriptions = []
        for symbol in symbols:
            if symbol in self.symbol_meanings:
                descriptions.append(self.symbol_meanings[symbol])
            else:
                # Fallback per simboli non mappati
                descriptions.append(f"symbol_{ord(symbol) if len(symbol) == 1 else 'unknown'}")

        return ", ".join(descriptions)

    def create_prompt(self, symbols: List[str], context: str, domain: str = "default") -> str:
        """Crea prompt per LLM basato su simboli e contesto."""
        symbols_text = self.symbols_to_text(symbols)

        template = self.domain_templates.get(domain, self.domain_templates["default"])
        prompt = template.format(symbols=symbols_text, context=context)

        return prompt

class MockLLM:
    """LLM simulato per test senza dipendenze esterne."""

    def __init__(self):
        # Risposte predefinite per test
        self.mock_responses = {
            "logic": "Based on logical reasoning, this follows the principle of transitivity.",
            "math": "Using mathematical analysis, the relationship can be expressed algebraically.",
            "reasoning": "Through symbolic reasoning, we can deduce the logical conclusion.",
            "cognitive": "From a cognitive perspective, this involves pattern recognition.",
            "default": "The symbolic representation suggests a structured relationship."
        }

    def generate(self, request: LLMRequest) -> LLMResponse:
        """Genera risposta simulata."""
        import time
        import random

        start_time = time.time()

        # Simula processing time
        time.sleep(0.1)

        # Determina dominio dal prompt
        domain = "default"
        for key in self.mock_responses.keys():
            if key in request.prompt.lower():
                domain = key
                break

        # Genera risposta
        base_response = self.mock_responses[domain]

        # Aggiunge contesto simbolico se presente
        if request.symbols_context:
            symbols_text = ", ".join(request.symbols_context[:3])  # Primi 3 simboli
            response_text = f"{base_response} The symbols ({symbols_text}) indicate a formal structure."
        else:
            response_text = base_response

        processing_time = time.time() - start_time
        confidence = random.uniform(0.7, 0.95)  # Simula confidenza variabile

        return LLMResponse(
            text=response_text,
            success=True,
            confidence=confidence,
            processing_time=processing_time
        )

class NeuroglyphLLMConnector:
    """
    Connettore principale tra NEUROGLYPH e LLM.

    Fornisce interfaccia unificata per:
    - Preprocessing input NL → simboli
    - Postprocessing simboli → output NL
    - Gestione backend LLM multipli
    """

    def __init__(self, backend: LLMBackend = LLMBackend.MOCK):
        self.backend = backend
        self.converter = SymbolToPromptConverter()

        # Inizializza backend specifico
        if backend == LLMBackend.MOCK:
            self.llm = MockLLM()
        elif backend == LLMBackend.QWEN_LOCAL:
            self.llm = self._init_qwen_local()
        elif backend == LLMBackend.OLLAMA:
            self.llm = self._init_ollama()
        elif backend == LLMBackend.TRANSFORMERS:
            self.llm = self._init_transformers()
        else:
            raise ValueError(f"Backend non supportato: {backend}")

        logger.info(f"🔗 LLM Connector inizializzato con backend: {backend.value}")

    def _init_qwen_local(self):
        """Inizializza Qwen2.5-Coder locale."""
        if QWEN_AVAILABLE:
            try:
                qwen_llm = QwenLocalLLM("models/base")
                if qwen_llm.is_loaded:
                    logger.info("✅ Qwen2.5-Coder caricato con successo")
                    return qwen_llm
                else:
                    logger.warning(f"⚠️ Qwen non caricato: {qwen_llm.load_error}")
                    return MockLLM()
            except Exception as e:
                logger.error(f"❌ Errore inizializzazione Qwen: {e}")
                return MockLLM()
        else:
            logger.warning("⚠️ Qwen integration non disponibile, uso MockLLM")
            return MockLLM()

    def _init_ollama(self):
        """Inizializza Ollama API."""
        if OLLAMA_AVAILABLE:
            try:
                ollama_llm = OllamaLLM()
                if ollama_llm.is_available and ollama_llm.model_loaded:
                    logger.info("✅ Ollama caricato con successo")
                    return ollama_llm
                else:
                    logger.warning(f"⚠️ Ollama non disponibile")
                    return MockLLM()
            except Exception as e:
                logger.error(f"❌ Errore inizializzazione Ollama: {e}")
                return MockLLM()
        else:
            logger.warning("⚠️ Ollama integration non disponibile, uso MockLLM")
            return MockLLM()

    def _init_transformers(self):
        """Inizializza HuggingFace Transformers."""
        # TODO: Implementare quando disponibile
        logger.warning("⚠️ Transformers non ancora implementato, uso MockLLM")
        return MockLLM()

    def natural_language_to_symbols(self, text: str) -> List[str]:
        """
        Converte linguaggio naturale in simboli NEUROGLYPH.

        Args:
            text: Input in linguaggio naturale

        Returns:
            Lista di simboli estratti/inferiti
        """
        # Mapping pattern NL → simboli
        nl_to_symbols = {
            # Logica
            r"se.*allora|if.*then": "→",
            r"e |and ": "∧",
            r"o |or ": "∨",
            r"non |not ": "¬",
            r"tutti|all": "∀",
            r"esiste|exists": "∃",
            r"quindi|therefore": "∴",
            r"maggiore|greater": ">",
            r"minore|less": "<",
            r"uguale|equal": "=",
            r"diverso|different": "≠",

            # Concetti
            r"logica|logic": "⊢",
            r"matematica|math": "=",
            r"ragionamento|reasoning": "⊬",
            r"memoria|memory": "■",
            r"creatività|creativity": "★",
        }

        extracted_symbols = []
        text_lower = text.lower()

        for pattern, symbol in nl_to_symbols.items():
            if re.search(pattern, text_lower):
                extracted_symbols.append(symbol)

        # Se non trova simboli specifici, usa simboli generici
        if not extracted_symbols:
            extracted_symbols = ["◯", "■"]  # Simboli neutri

        logger.debug(f"🔤 NL→Simboli: '{text[:50]}...' → {extracted_symbols}")
        return extracted_symbols

    def symbols_to_natural_language(self, symbols: List[str], context: str = "", domain: str = "default") -> str:
        """
        Converte simboli NEUROGLYPH in linguaggio naturale.

        Args:
            symbols: Lista di simboli da convertire
            context: Contesto aggiuntivo
            domain: Dominio semantico

        Returns:
            Testo in linguaggio naturale
        """
        if not symbols:
            return "No symbolic reasoning available."

        # Usa metodo specifico per Qwen se disponibile
        if hasattr(self.llm, 'symbols_to_prompt'):
            # Qwen LLM con metodo specializzato
            prompt = self.llm.symbols_to_prompt(symbols, context, domain)
            response = self.llm.generate(prompt)

            # Converte QwenResponse in LLMResponse
            if hasattr(response, 'success'):
                llm_response = LLMResponse(
                    text=response.text,
                    success=response.success,
                    confidence=response.confidence,
                    processing_time=response.processing_time,
                    error_message=response.error_message
                )
            else:
                # Fallback per altri tipi di risposta
                llm_response = response
        else:
            # MockLLM o altri backend
            prompt = self.converter.create_prompt(symbols, context, domain)

            # Richiesta al LLM
            request = LLMRequest(
                prompt=prompt,
                symbols_context=symbols,
                domain=domain
            )

            # Genera risposta
            llm_response = self.llm.generate(request)

        if llm_response.success:
            logger.debug(f"🗣️ Simboli→NL: {symbols} → '{llm_response.text[:50]}...'")
            return llm_response.text
        else:
            logger.error(f"❌ Errore LLM: {llm_response.error_message}")
            return f"Error processing symbols: {', '.join(symbols)}"

    def process_full_cycle(self, input_text: str, reasoning_symbols: List[str], domain: str = "default") -> str:
        """
        Ciclo completo: NL input → symbolic reasoning → NL output.

        Args:
            input_text: Input originale in NL
            reasoning_symbols: Simboli prodotti dal ragionamento
            domain: Dominio semantico

        Returns:
            Output finale in linguaggio naturale
        """
        logger.info(f"🔄 Ciclo completo LLM: '{input_text[:30]}...' → {len(reasoning_symbols)} simboli")

        # Converte simboli in output NL
        output_text = self.symbols_to_natural_language(
            symbols=reasoning_symbols,
            context=input_text,
            domain=domain
        )

        return output_text

    def get_backend_info(self) -> Dict[str, Any]:
        """Ritorna informazioni sul backend LLM."""
        return {
            "backend": self.backend.value,
            "status": "active",
            "converter_symbols": len(self.converter.symbol_meanings),
            "domain_templates": len(self.converter.domain_templates)
        }


# Funzioni di utilità per uso esterno
def create_llm_connector(backend: LLMBackend = LLMBackend.MOCK) -> NeuroglyphLLMConnector:
    """Crea LLM Connector con backend specificato."""
    return NeuroglyphLLMConnector(backend)


if __name__ == "__main__":
    # Test LLM Connector
    print("🔗 NEUROGLYPH LLM Connector - Test")
    print("=" * 60)

    # Crea connector
    connector = create_llm_connector(LLMBackend.MOCK)

    # Test NL → simboli
    test_input = "Se A è maggiore di B e B è maggiore di C, cosa possiamo dire di A e C?"
    symbols = connector.natural_language_to_symbols(test_input)
    print(f"\n🔤 Input: {test_input}")
    print(f"🔣 Simboli estratti: {symbols}")

    # Test simboli → NL
    reasoning_symbols = ["⊃", "∧", "→", "∴"]
    output = connector.symbols_to_natural_language(reasoning_symbols, test_input, "logic")
    print(f"\n🧠 Simboli ragionamento: {reasoning_symbols}")
    print(f"🗣️ Output: {output}")

    # Test ciclo completo
    full_output = connector.process_full_cycle(test_input, reasoning_symbols, "logic")
    print(f"\n🔄 Ciclo completo: {full_output}")

    # Info backend
    info = connector.get_backend_info()
    print(f"\n📊 Backend info: {info}")
