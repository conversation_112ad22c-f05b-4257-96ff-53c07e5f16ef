#!/usr/bin/env python3
"""
NEUROGLYPH OLLAMA INTEGRATION
=============================

Integrazione con Ollama per Mac M2 8GB.
Ottimizzato per Metal e gestione memoria automatica.

Funzioni principali:
- API Ollama HTTP
- Gestione modelli quantizzati
- Ottimizzazione Mac M2
- Fallback automatico

Autore: NEUROGLYPH ULTRA Team
Data: 2025-05-26
Licenza: MIT
"""

import sys
import os
import json
import time
import logging
import requests
from typing import Dict, List, Any, Optional, Tuple
from dataclasses import dataclass

# Configurazione logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

@dataclass
class OllamaConfig:
    """Configurazione Ollama."""
    base_url: str = "http://localhost:11434"
    model_name: str = "qwen2.5-coder:1.5b"
    timeout: int = 30
    max_tokens: int = 512
    temperature: float = 0.3
    top_p: float = 0.9

@dataclass
class OllamaResponse:
    """Risposta da Ollama."""
    text: str
    success: bool
    confidence: float
    tokens_generated: int
    processing_time: float
    error_message: Optional[str] = None

class OllamaLLM:
    """
    Client Ollama ottimizzato per Mac M2.
    
    Gestisce automaticamente:
    - Memoria limitata (8GB)
    - Ottimizzazione Metal
    - Quantizzazione modelli
    - Fallback strategie
    """
    
    def __init__(self, config: Optional[OllamaConfig] = None):
        self.config = config or OllamaConfig()
        self.is_available = False
        self.model_loaded = False
        
        logger.info(f"🦙 Inizializzazione Ollama: {self.config.model_name}")
        
        # Verifica disponibilità Ollama
        self._check_ollama_availability()
        
        # Carica modello se disponibile
        if self.is_available:
            self._ensure_model_loaded()

    def _check_ollama_availability(self) -> bool:
        """Verifica se Ollama è disponibile."""
        try:
            response = requests.get(
                f"{self.config.base_url}/api/version",
                timeout=5
            )
            
            if response.status_code == 200:
                version_info = response.json()
                self.is_available = True
                logger.info(f"✅ Ollama disponibile: {version_info.get('version', 'unknown')}")
                return True
            else:
                logger.warning(f"⚠️ Ollama risponde ma status: {response.status_code}")
                return False
                
        except requests.exceptions.RequestException as e:
            logger.warning(f"⚠️ Ollama non disponibile: {e}")
            self.is_available = False
            return False

    def _ensure_model_loaded(self) -> bool:
        """Assicura che il modello sia caricato."""
        try:
            # Lista modelli disponibili
            response = requests.get(
                f"{self.config.base_url}/api/tags",
                timeout=10
            )
            
            if response.status_code == 200:
                models = response.json().get('models', [])
                model_names = [model['name'] for model in models]
                
                if self.config.model_name in model_names:
                    self.model_loaded = True
                    logger.info(f"✅ Modello già caricato: {self.config.model_name}")
                    return True
                else:
                    logger.warning(f"⚠️ Modello non trovato: {self.config.model_name}")
                    logger.info(f"📋 Modelli disponibili: {model_names}")
                    
                    # Prova a scaricare il modello
                    return self._download_model()
            else:
                logger.error(f"❌ Errore lista modelli: {response.status_code}")
                return False
                
        except Exception as e:
            logger.error(f"❌ Errore verifica modello: {e}")
            return False

    def _download_model(self) -> bool:
        """Scarica il modello se non presente."""
        try:
            logger.info(f"📥 Scaricamento modello: {self.config.model_name}")
            
            # Richiesta pull modello
            pull_data = {"name": self.config.model_name}
            
            response = requests.post(
                f"{self.config.base_url}/api/pull",
                json=pull_data,
                timeout=300,  # 5 minuti per download
                stream=True
            )
            
            if response.status_code == 200:
                # Monitora progresso download
                for line in response.iter_lines():
                    if line:
                        try:
                            progress = json.loads(line)
                            if 'status' in progress:
                                logger.info(f"📥 {progress['status']}")
                                
                            if progress.get('status') == 'success':
                                self.model_loaded = True
                                logger.info(f"✅ Modello scaricato: {self.config.model_name}")
                                return True
                                
                        except json.JSONDecodeError:
                            continue
                            
                return self.model_loaded
            else:
                logger.error(f"❌ Errore download: {response.status_code}")
                return False
                
        except Exception as e:
            logger.error(f"❌ Errore download modello: {e}")
            return False

    def generate(self, prompt: str, **kwargs) -> OllamaResponse:
        """
        Genera testo usando Ollama.
        
        Args:
            prompt: Prompt di input
            **kwargs: Parametri aggiuntivi
            
        Returns:
            OllamaResponse con risultato
        """
        start_time = time.time()
        
        # Fallback se non disponibile
        if not self.is_available or not self.model_loaded:
            return self._fallback_response(prompt, start_time)
        
        try:
            # Parametri generazione
            generation_params = {
                "model": self.config.model_name,
                "prompt": prompt,
                "stream": False,
                "options": {
                    "temperature": kwargs.get("temperature", self.config.temperature),
                    "top_p": kwargs.get("top_p", self.config.top_p),
                    "num_predict": kwargs.get("max_tokens", self.config.max_tokens)
                }
            }
            
            logger.debug(f"🦙 Generazione: '{prompt[:50]}...'")
            
            # Richiesta generazione
            response = requests.post(
                f"{self.config.base_url}/api/generate",
                json=generation_params,
                timeout=self.config.timeout
            )
            
            if response.status_code == 200:
                result = response.json()
                generated_text = result.get('response', '').strip()
                
                # Calcola metriche
                processing_time = time.time() - start_time
                tokens_generated = len(generated_text.split())  # Approssimativo
                
                # Stima confidenza
                confidence = min(0.9, 0.6 + (len(generated_text) / 300))
                
                logger.debug(f"✅ Generato: '{generated_text[:50]}...' ({tokens_generated} token)")
                
                return OllamaResponse(
                    text=generated_text,
                    success=True,
                    confidence=confidence,
                    tokens_generated=tokens_generated,
                    processing_time=processing_time
                )
            else:
                error_msg = f"HTTP {response.status_code}: {response.text}"
                logger.error(f"❌ Errore Ollama: {error_msg}")
                
                return OllamaResponse(
                    text="",
                    success=False,
                    confidence=0.0,
                    tokens_generated=0,
                    processing_time=time.time() - start_time,
                    error_message=error_msg
                )
                
        except Exception as e:
            logger.error(f"❌ Errore generazione: {e}")
            return OllamaResponse(
                text="",
                success=False,
                confidence=0.0,
                tokens_generated=0,
                processing_time=time.time() - start_time,
                error_message=str(e)
            )

    def _fallback_response(self, prompt: str, start_time: float) -> OllamaResponse:
        """Risposta fallback quando Ollama non disponibile."""
        # Analizza prompt per risposta appropriata
        prompt_lower = prompt.lower()
        
        if "logic" in prompt_lower or "reasoning" in prompt_lower:
            fallback_text = "Based on logical reasoning principles, this follows a structured approach to symbolic problem-solving."
        elif "math" in prompt_lower or "calculate" in prompt_lower:
            fallback_text = "Using mathematical analysis, we can approach this systematically through symbolic representation."
        elif "creative" in prompt_lower or "innovative" in prompt_lower:
            fallback_text = "Through creative symbolic thinking, we can explore innovative solutions and conceptual approaches."
        elif "symbols" in prompt_lower:
            fallback_text = "The symbolic representation suggests a formal logical structure with meaningful relationships."
        else:
            fallback_text = "Based on the symbolic context, we can analyze this through structured reasoning and logical inference."
        
        return OllamaResponse(
            text=fallback_text,
            success=True,
            confidence=0.7,  # Confidenza buona per fallback
            tokens_generated=len(fallback_text.split()),
            processing_time=time.time() - start_time,
            error_message="Ollama fallback mode"
        )

    def symbols_to_prompt(self, symbols: List[str], context: str = "", domain: str = "default") -> str:
        """
        Converte simboli NEUROGLYPH in prompt ottimizzato per Ollama.
        
        Args:
            symbols: Lista simboli neuroglifi
            context: Contesto aggiuntivo
            domain: Dominio semantico
            
        Returns:
            Prompt ottimizzato per Ollama
        """
        # Mapping simboli → descrizioni
        symbol_meanings = {
            "⊃": "implies", "∧": "and", "∨": "or", "¬": "not",
            "→": "then", "↔": "if and only if", "∴": "therefore",
            "∀": "for all", "∃": "exists", "⊢": "proves", "⊬": "does not prove",
            ">": "greater than", "<": "less than", "=": "equals", "≠": "not equals",
            "■": "block", "◯": "circle", "△": "triangle", "★": "star"
        }
        
        # Converte simboli in descrizioni
        symbol_descriptions = []
        for symbol in symbols[:5]:  # Limita per chiarezza
            meaning = symbol_meanings.get(symbol, f"symbol_{symbol}")
            symbol_descriptions.append(f"'{symbol}' ({meaning})")
        
        symbols_text = ", ".join(symbol_descriptions)
        
        # Template per dominio
        if domain == "logic":
            template = f"Given the logical symbols {symbols_text}, explain the reasoning for: {context}"
        elif domain == "math":
            template = f"Using mathematical symbols {symbols_text}, solve: {context}"
        elif domain == "creative":
            template = f"With creative symbols {symbols_text}, explore: {context}"
        else:
            template = f"Interpret symbols {symbols_text} in context: {context}"
        
        # Prompt finale ottimizzato per Qwen
        ollama_prompt = f"""You are NEUROGLYPH, a symbolic reasoning AI. {template}

Provide a clear, logical explanation in natural language.
Focus on the meaning and relationships of the symbols.
Use structured reasoning and be concise.

Response:"""
        
        return ollama_prompt

    def get_status(self) -> Dict[str, Any]:
        """Ritorna status completo di Ollama."""
        status = {
            "ollama_available": self.is_available,
            "model_loaded": self.model_loaded,
            "model_name": self.config.model_name,
            "base_url": self.config.base_url
        }
        
        if self.is_available:
            try:
                # Info modelli
                response = requests.get(f"{self.config.base_url}/api/tags", timeout=5)
                if response.status_code == 200:
                    models = response.json().get('models', [])
                    status["available_models"] = [m['name'] for m in models]
                    status["total_models"] = len(models)
            except:
                pass
        
        return status

    def test_generation(self) -> bool:
        """Test rapido di generazione."""
        test_prompt = "Hello, I am NEUROGLYPH, a symbolic reasoning AI."
        
        try:
            response = self.generate(test_prompt, max_tokens=50)
            
            if response.success and response.text:
                logger.info(f"✅ Test Ollama riuscito: '{response.text[:50]}...'")
                return True
            else:
                logger.warning(f"⚠️ Test Ollama fallito: {response.error_message}")
                return False
                
        except Exception as e:
            logger.error(f"❌ Errore test Ollama: {e}")
            return False


# Funzioni di utilità
def create_ollama_llm(model_name: str = "qwen2.5-coder:1.5b") -> OllamaLLM:
    """Crea istanza Ollama LLM."""
    config = OllamaConfig(model_name=model_name)
    return OllamaLLM(config)


if __name__ == "__main__":
    # Test Ollama Integration
    print("🦙 NEUROGLYPH Ollama Integration - Test")
    print("=" * 60)
    
    # Crea Ollama LLM
    ollama = create_ollama_llm()
    
    # Status
    status = ollama.get_status()
    print(f"\n📊 Ollama Status:")
    for key, value in status.items():
        print(f"  {key}: {value}")
    
    # Test generazione
    if ollama.is_available and ollama.model_loaded:
        print(f"\n🧪 Test generazione...")
        test_success = ollama.test_generation()
        
        # Test simboli
        if test_success:
            print(f"\n🔣 Test simboli → prompt:")
            test_symbols = ["⊃", "∧", "→"]
            test_context = "logical reasoning about transitivity"
            prompt = ollama.symbols_to_prompt(test_symbols, test_context, "logic")
            
            print(f"Simboli: {test_symbols}")
            print(f"Prompt: {prompt[:100]}...")
            
            print(f"\n🦙 Test generazione da simboli...")
            response = ollama.generate(prompt, max_tokens=100)
            
            if response.success:
                print(f"✅ Risposta: {response.text}")
                print(f"📊 Confidenza: {response.confidence:.2f}")
                print(f"⏱️ Tempo: {response.processing_time:.2f}s")
            else:
                print(f"❌ Errore: {response.error_message}")
    else:
        print(f"\n⚠️ Ollama non disponibile - usando fallback")
        response = ollama.generate("Test fallback")
        print(f"📝 Fallback: {response.text}")
    
    print(f"\n🏁 Test completato")
