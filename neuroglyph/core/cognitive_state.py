#!/usr/bin/env python3
"""
NEUROGLYPH LLM - COGNITIVE STATE MANAGER
========================================

Gestisce lo stato cognitivo dinamico di NEUROGLYPH LLM, trasformando
il modello da predittore statistico a entità pensante con memoria,
consapevolezza e capacità di auto-riflessione.

Componenti:
- CognitiveState: Stato mentale corrente del sistema
- CognitiveDomain: Domini di conoscenza attivi
- ThoughtTrace: Traccia dei processi di pensiero
- CognitiveMetrics: Metriche di performance cognitiva

Autore: NEUROGLYPH ULTRA Team
Data: 2025-05-26
Licenza: MIT
"""

import json
import logging
import hashlib
from datetime import datetime
from typing import Dict, List, Set, Any, Optional, Tuple, Union
from dataclasses import dataclass, field, asdict
from enum import Enum
from pathlib import Path
import threading
from collections import defaultdict, deque

# Setup logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class CognitiveDomainType(Enum):
    """Tipi di domini cognitivi supportati."""
    LOGIC = "logic"
    REASONING = "reasoning"
    MEMORY = "memory"
    STRUCTURE = "structure"
    FLOW = "flow"
    OPERATOR = "operator"
    META = "meta"
    TEMPORAL = "temporal"
    SPATIAL = "spatial"
    CAUSAL = "causal"
    ANALOGICAL = "analogical"
    CREATIVE = "creative"
    PROBLEM_SOLVING = "problem_solving"
    LEARNING = "learning"
    CONSCIOUSNESS = "consciousness"
    SELF_REFLECTION = "self_reflection"
    GOAL_ORIENTED = "goal_oriented"
    EMOTIONAL = "emotional"
    SOCIAL = "social"
    MULTIMODAL = "multimodal"
    LANGUAGE = "language"
    ATTENTION = "attention"
    DECISION_MAKING = "decision_making"
    ADAPTIVE = "adaptive"
    NEURAL_ARCHITECTURES = "neural_architectures"
    QUANTUM_COMPUTING = "quantum_computing"
    SYMBOLIC_AI = "symbolic_ai"
    META_PROGRAMMING = "meta_programming"

class ThoughtType(Enum):
    """Tipi di pensiero supportati."""
    DEDUCTIVE = "deductive"
    INDUCTIVE = "inductive"
    ABDUCTIVE = "abductive"
    ANALOGICAL = "analogical"
    CAUSAL = "causal"
    TEMPORAL = "temporal"
    SPATIAL = "spatial"
    MODAL = "modal"
    PROBABILISTIC = "probabilistic"
    METACOGNITIVE = "metacognitive"
    CREATIVE = "creative"
    CRITICAL = "critical"
    REFLECTIVE = "reflective"
    INTUITIVE = "intuitive"

class CognitiveIntensity(Enum):
    """Livelli di intensità cognitiva."""
    MINIMAL = 0.1
    LOW = 0.3
    MEDIUM = 0.5
    HIGH = 0.7
    ULTRA = 0.9
    GOD_MODE = 1.0

@dataclass
class CognitiveDomain:
    """Rappresenta un dominio cognitivo attivo."""
    domain_type: CognitiveDomainType
    activation_level: float  # 0.0 - 1.0
    active_symbols: Set[str] = field(default_factory=set)
    related_domains: Set[CognitiveDomainType] = field(default_factory=set)
    last_activation: Optional[datetime] = None
    activation_count: int = 0
    success_rate: float = 1.0
    metadata: Dict[str, Any] = field(default_factory=dict)

    def activate(self, intensity: float = 0.5, symbols: Optional[Set[str]] = None):
        """Attiva il dominio cognitivo."""
        self.activation_level = min(1.0, max(0.0, intensity))
        self.last_activation = datetime.now()
        self.activation_count += 1

        if symbols:
            self.active_symbols.update(symbols)

        logger.debug(f"🧠 Dominio {self.domain_type.value} attivato: {self.activation_level:.2f}")

    def deactivate(self):
        """Disattiva il dominio cognitivo."""
        self.activation_level = 0.0
        self.active_symbols.clear()
        logger.debug(f"🧠 Dominio {self.domain_type.value} disattivato")

    def is_active(self, threshold: float = 0.1) -> bool:
        """Verifica se il dominio è attivo."""
        return self.activation_level >= threshold

@dataclass
class ThoughtTrace:
    """Traccia di un processo di pensiero."""
    thought_id: str
    thought_type: ThoughtType
    input_symbols: List[str]
    active_domains: List[CognitiveDomainType]
    reasoning_steps: List[str]
    output_symbols: List[str]
    confidence: float
    processing_time: float
    timestamp: datetime
    success: bool
    error_message: Optional[str] = None
    metadata: Dict[str, Any] = field(default_factory=dict)

    def __post_init__(self):
        """Post-inizializzazione per generare ID se mancante."""
        if not self.thought_id:
            content = f"{self.input_symbols}{self.timestamp.isoformat()}"
            self.thought_id = hashlib.md5(content.encode()).hexdigest()[:12]

@dataclass
class CognitiveMetrics:
    """Metriche di performance cognitiva."""
    total_thoughts: int = 0
    successful_thoughts: int = 0
    average_confidence: float = 0.0
    average_processing_time: float = 0.0
    domain_activations: Dict[str, int] = field(default_factory=lambda: defaultdict(int))
    thought_type_distribution: Dict[str, int] = field(default_factory=lambda: defaultdict(int))
    symbol_usage_frequency: Dict[str, int] = field(default_factory=lambda: defaultdict(int))
    error_patterns: Dict[str, int] = field(default_factory=lambda: defaultdict(int))
    cognitive_entropy: float = 0.0
    learning_rate: float = 0.0
    creativity_index: float = 0.0

    def update_metrics(self, thought: ThoughtTrace):
        """Aggiorna le metriche con un nuovo pensiero."""
        self.total_thoughts += 1

        if thought.success:
            self.successful_thoughts += 1

        # Aggiorna confidenza media
        self.average_confidence = (
            (self.average_confidence * (self.total_thoughts - 1) + thought.confidence)
            / self.total_thoughts
        )

        # Aggiorna tempo medio
        self.average_processing_time = (
            (self.average_processing_time * (self.total_thoughts - 1) + thought.processing_time)
            / self.total_thoughts
        )

        # Aggiorna distribuzioni
        for domain in thought.active_domains:
            self.domain_activations[domain.value] += 1

        self.thought_type_distribution[thought.thought_type.value] += 1

        for symbol in thought.input_symbols + thought.output_symbols:
            self.symbol_usage_frequency[symbol] += 1

        if not thought.success and thought.error_message:
            self.error_patterns[thought.error_message] += 1

    @property
    def success_rate(self) -> float:
        """Calcola il tasso di successo."""
        if self.total_thoughts == 0:
            return 0.0
        return self.successful_thoughts / self.total_thoughts

    @property
    def cognitive_efficiency(self) -> float:
        """Calcola l'efficienza cognitiva."""
        if self.average_processing_time == 0:
            return 1.0
        return self.success_rate / max(0.001, self.average_processing_time)

class CognitiveState:
    """
    Gestisce lo stato cognitivo completo di NEUROGLYPH LLM.

    Questo è il cervello del sistema che mantiene:
    - Domini cognitivi attivi
    - Traccia dei pensieri
    - Metriche di performance
    - Stato di consapevolezza
    """

    def __init__(self, registry_path: str = "neuroglyph/core/locked_registry_godmode_v9.json"):
        """Inizializza lo stato cognitivo."""
        self.registry_path = registry_path
        self.domains: Dict[CognitiveDomainType, CognitiveDomain] = {}
        self.thought_history: deque = deque(maxlen=1000)  # Mantiene ultimi 1000 pensieri
        self.metrics = CognitiveMetrics()
        self.current_intensity = CognitiveIntensity.MEDIUM
        self.consciousness_level = 0.5
        self.self_awareness = 0.3
        self.creativity_mode = False
        self.learning_mode = True
        self.debug_mode = False

        # Thread safety
        self._lock = threading.RLock()

        # Stato interno
        self._symbol_registry = {}
        self._domain_mappings = {}
        self._active_thought_id = None
        self._last_update = datetime.now()

        # Inizializzazione
        self._initialize_domains()
        self._load_symbol_registry()
        self._build_domain_mappings()

        logger.info("🧠 NEUROGLYPH Cognitive State inizializzato")
        logger.info(f"📊 Domini cognitivi: {len(self.domains)}")
        logger.info(f"🎯 Simboli caricati: {len(self._symbol_registry)}")

    def _initialize_domains(self):
        """Inizializza tutti i domini cognitivi."""
        for domain_type in CognitiveDomainType:
            self.domains[domain_type] = CognitiveDomain(
                domain_type=domain_type,
                activation_level=0.0
            )

        logger.debug(f"🧠 Inizializzati {len(self.domains)} domini cognitivi")

    def _load_symbol_registry(self):
        """Carica il registry simbolico."""
        try:
            with open(self.registry_path, 'r', encoding='utf-8') as f:
                registry_data = json.load(f)

            self._symbol_registry = {}
            for symbol_data in registry_data.get('approved_symbols', []):
                symbol = symbol_data.get('symbol', '')
                category = symbol_data.get('category', '')
                code = symbol_data.get('code', '')

                self._symbol_registry[symbol] = {
                    'category': category,
                    'code': code,
                    'cognitive_domain': symbol_data.get('cognitive_domain', False),
                    'tier': symbol_data.get('tier', 'base'),
                    'score': symbol_data.get('score', 0.0)
                }

            logger.info(f"✅ Registry simbolico caricato: {len(self._symbol_registry)} simboli")

        except Exception as e:
            logger.error(f"❌ Errore caricamento registry: {e}")
            self._symbol_registry = {}

    def _build_domain_mappings(self):
        """Costruisce mappature tra simboli e domini cognitivi."""
        self._domain_mappings = defaultdict(set)

        # Mappa categorie simboli a domini cognitivi
        category_to_domain = {
            'logic': CognitiveDomainType.LOGIC,
            'reasoning': CognitiveDomainType.REASONING,
            'memory': CognitiveDomainType.MEMORY,
            'structure': CognitiveDomainType.STRUCTURE,
            'flow': CognitiveDomainType.FLOW,
            'operator': CognitiveDomainType.OPERATOR,
            'meta': CognitiveDomainType.META,
            'temporal': CognitiveDomainType.TEMPORAL,
            'spatial': CognitiveDomainType.SPATIAL,
            'causal': CognitiveDomainType.CAUSAL,
            'analogical': CognitiveDomainType.ANALOGICAL,
            'creative': CognitiveDomainType.CREATIVE,
            'problem_solving': CognitiveDomainType.PROBLEM_SOLVING,
            'learning': CognitiveDomainType.LEARNING,
            'consciousness': CognitiveDomainType.CONSCIOUSNESS,
            'self_reflection': CognitiveDomainType.SELF_REFLECTION,
            'goal_oriented': CognitiveDomainType.GOAL_ORIENTED,
            'emotional': CognitiveDomainType.EMOTIONAL,
            'social': CognitiveDomainType.SOCIAL,
            'multimodal': CognitiveDomainType.MULTIMODAL,
            'language': CognitiveDomainType.LANGUAGE,
            'attention': CognitiveDomainType.ATTENTION,
            'decision_making': CognitiveDomainType.DECISION_MAKING,
            'adaptive': CognitiveDomainType.ADAPTIVE,
            'neural_architectures': CognitiveDomainType.NEURAL_ARCHITECTURES,
            'quantum_computing': CognitiveDomainType.QUANTUM_COMPUTING,
            'symbolic_ai': CognitiveDomainType.SYMBOLIC_AI,
            'meta_programming': CognitiveDomainType.META_PROGRAMMING
        }

        for symbol, data in self._symbol_registry.items():
            category = data.get('category', '')
            if category in category_to_domain:
                domain_type = category_to_domain[category]
                self._domain_mappings[domain_type].add(symbol)

        logger.debug(f"🧠 Mappature domini costruite: {len(self._domain_mappings)} domini")

    def activate_domains_for_symbols(self, symbols: List[str], intensity: float = 0.7) -> Set[CognitiveDomainType]:
        """
        Attiva domini cognitivi basati sui simboli di input.

        Args:
            symbols: Lista di simboli neuroglifi
            intensity: Intensità di attivazione (0.0-1.0)

        Returns:
            Set di domini attivati
        """
        with self._lock:
            activated_domains = set()

            for symbol in symbols:
                if symbol in self._symbol_registry:
                    category = self._symbol_registry[symbol]['category']

                    # Trova domini corrispondenti
                    for domain_type, domain_symbols in self._domain_mappings.items():
                        if symbol in domain_symbols:
                            domain = self.domains[domain_type]
                            domain.activate(intensity, {symbol})
                            activated_domains.add(domain_type)

                            logger.debug(f"🧠 Attivato dominio {domain_type.value} per simbolo {symbol}")

            # Attiva domini correlati con intensità ridotta
            for domain_type in list(activated_domains):
                domain = self.domains[domain_type]
                for related_domain_type in domain.related_domains:
                    if related_domain_type not in activated_domains:
                        related_domain = self.domains[related_domain_type]
                        related_domain.activate(intensity * 0.5)
                        activated_domains.add(related_domain_type)

            logger.info(f"🧠 Attivati {len(activated_domains)} domini cognitivi")
            return activated_domains

    def get_active_domains(self, threshold: float = 0.1) -> Dict[CognitiveDomainType, float]:
        """
        Restituisce domini attualmente attivi.

        Args:
            threshold: Soglia minima di attivazione

        Returns:
            Dizionario dominio -> livello di attivazione
        """
        with self._lock:
            active = {}
            for domain_type, domain in self.domains.items():
                if domain.is_active(threshold):
                    active[domain_type] = domain.activation_level

            return active

    def record_thought(self, thought: ThoughtTrace):
        """
        Registra un processo di pensiero nella storia cognitiva.

        Args:
            thought: Traccia del pensiero da registrare
        """
        with self._lock:
            self.thought_history.append(thought)
            self.metrics.update_metrics(thought)
            self._active_thought_id = thought.thought_id
            self._last_update = datetime.now()

            # Aggiorna tassi di successo dei domini
            for domain_type in thought.active_domains:
                domain = self.domains[domain_type]
                if thought.success:
                    domain.success_rate = (domain.success_rate * 0.9 + 1.0 * 0.1)
                else:
                    domain.success_rate = (domain.success_rate * 0.9 + 0.0 * 0.1)

            logger.debug(f"🧠 Pensiero registrato: {thought.thought_id}")

    def calculate_cognitive_entropy(self) -> float:
        """
        Calcola l'entropia cognitiva corrente.

        L'entropia misura la varietà e complessità del pensiero.
        Valori bassi indicano pensiero ripetitivo, valori alti creatività.

        Returns:
            Valore di entropia (0.0-1.0)
        """
        with self._lock:
            if len(self.thought_history) < 2:
                return 0.5

            # Analizza varietà nei tipi di pensiero
            recent_thoughts = list(self.thought_history)[-10:]  # Ultimi 10 pensieri
            thought_types = [t.thought_type for t in recent_thoughts]
            type_variety = len(set(thought_types)) / len(ThoughtType)

            # Analizza varietà nei domini attivi
            all_domains = set()
            for thought in recent_thoughts:
                all_domains.update(thought.active_domains)
            domain_variety = len(all_domains) / len(CognitiveDomainType)

            # Analizza varietà simbolica
            all_symbols = set()
            for thought in recent_thoughts:
                all_symbols.update(thought.input_symbols + thought.output_symbols)
            symbol_variety = min(1.0, len(all_symbols) / 50)  # Normalizza a 50 simboli

            # Calcola entropia combinata
            entropy = (type_variety * 0.4 + domain_variety * 0.3 + symbol_variety * 0.3)

            self.metrics.cognitive_entropy = entropy
            return entropy

    def detect_cognitive_loops(self, window_size: int = 5) -> bool:
        """
        Rileva loop cognitivi (pensieri ripetitivi).

        Args:
            window_size: Dimensione finestra per rilevamento

        Returns:
            True se rilevato loop, False altrimenti
        """
        with self._lock:
            if len(self.thought_history) < window_size * 2:
                return False

            recent_thoughts = list(self.thought_history)[-window_size * 2:]

            # Confronta pattern di simboli
            first_half = recent_thoughts[:window_size]
            second_half = recent_thoughts[window_size:]

            similarity_count = 0
            for i in range(window_size):
                t1, t2 = first_half[i], second_half[i]

                # Calcola similarità simbolica
                symbols1 = set(t1.input_symbols + t1.output_symbols)
                symbols2 = set(t2.input_symbols + t2.output_symbols)

                if symbols1 and symbols2:
                    intersection = len(symbols1 & symbols2)
                    union = len(symbols1 | symbols2)
                    similarity = intersection / union

                    if similarity > 0.8:  # Soglia di similarità
                        similarity_count += 1

            loop_detected = similarity_count >= window_size * 0.7

            if loop_detected:
                logger.warning(f"🔄 Loop cognitivo rilevato: {similarity_count}/{window_size} pensieri simili")

            return loop_detected

    def stimulate_creativity(self, intensity: float = 0.8):
        """
        Stimola la creatività cognitiva per uscire da pattern ripetitivi.

        Args:
            intensity: Intensità della stimolazione (0.0-1.0)
        """
        with self._lock:
            self.creativity_mode = True

            # Attiva domini creativi
            creative_domains = [
                CognitiveDomainType.CREATIVE,
                CognitiveDomainType.ANALOGICAL,
                CognitiveDomainType.META,
                CognitiveDomainType.ADAPTIVE
            ]

            for domain_type in creative_domains:
                domain = self.domains[domain_type]
                domain.activate(intensity)

            # Aumenta consapevolezza e auto-riflessione
            self.consciousness_level = min(1.0, self.consciousness_level + 0.2)
            self.self_awareness = min(1.0, self.self_awareness + 0.3)

            logger.info(f"🎨 Creatività stimolata: intensità {intensity:.2f}")

    def get_cognitive_status(self) -> Dict[str, Any]:
        """
        Restituisce stato cognitivo completo per debugging e monitoraggio.

        Returns:
            Dizionario con stato cognitivo dettagliato
        """
        with self._lock:
            active_domains = self.get_active_domains()
            entropy = self.calculate_cognitive_entropy()

            status = {
                'timestamp': datetime.now().isoformat(),
                'consciousness_level': self.consciousness_level,
                'self_awareness': self.self_awareness,
                'current_intensity': self.current_intensity.value,
                'creativity_mode': self.creativity_mode,
                'learning_mode': self.learning_mode,
                'active_domains': {d.value: level for d, level in active_domains.items()},
                'cognitive_entropy': entropy,
                'metrics': {
                    'total_thoughts': self.metrics.total_thoughts,
                    'success_rate': self.metrics.success_rate,
                    'average_confidence': self.metrics.average_confidence,
                    'cognitive_efficiency': self.metrics.cognitive_efficiency
                },
                'recent_thoughts': len(self.thought_history),
                'active_thought_id': self._active_thought_id,
                'last_update': self._last_update.isoformat()
            }

            return status

    def reset_cognitive_state(self, preserve_learning: bool = True):
        """
        Reset dello stato cognitivo mantenendo opzionalmente l'apprendimento.

        Args:
            preserve_learning: Se mantenere metriche e storia per apprendimento
        """
        with self._lock:
            # Disattiva tutti i domini
            for domain in self.domains.values():
                domain.deactivate()

            # Reset stato
            self.consciousness_level = 0.5
            self.self_awareness = 0.3
            self.creativity_mode = False
            self.current_intensity = CognitiveIntensity.MEDIUM
            self._active_thought_id = None

            if not preserve_learning:
                self.thought_history.clear()
                self.metrics = CognitiveMetrics()

            self._last_update = datetime.now()

            logger.info(f"🔄 Stato cognitivo resettato (learning preserved: {preserve_learning})")

    def save_cognitive_state(self, filepath: str):
        """
        Salva lo stato cognitivo su file per persistenza.

        Args:
            filepath: Percorso file di salvataggio
        """
        with self._lock:
            state_data = {
                'timestamp': datetime.now().isoformat(),
                'consciousness_level': self.consciousness_level,
                'self_awareness': self.self_awareness,
                'current_intensity': self.current_intensity.value,
                'creativity_mode': self.creativity_mode,
                'learning_mode': self.learning_mode,
                'domains': {
                    domain_type.value: {
                        'activation_level': domain.activation_level,
                        'activation_count': domain.activation_count,
                        'success_rate': domain.success_rate,
                        'last_activation': domain.last_activation.isoformat() if domain.last_activation else None
                    }
                    for domain_type, domain in self.domains.items()
                },
                'metrics': asdict(self.metrics),
                'thought_history_count': len(self.thought_history)
            }

            try:
                with open(filepath, 'w', encoding='utf-8') as f:
                    json.dump(state_data, f, indent=2, ensure_ascii=False)

                logger.info(f"💾 Stato cognitivo salvato: {filepath}")

            except Exception as e:
                logger.error(f"❌ Errore salvataggio stato: {e}")

    def load_cognitive_state(self, filepath: str):
        """
        Carica lo stato cognitivo da file.

        Args:
            filepath: Percorso file da caricare
        """
        try:
            with open(filepath, 'r', encoding='utf-8') as f:
                state_data = json.load(f)

            with self._lock:
                self.consciousness_level = state_data.get('consciousness_level', 0.5)
                self.self_awareness = state_data.get('self_awareness', 0.3)
                self.creativity_mode = state_data.get('creativity_mode', False)
                self.learning_mode = state_data.get('learning_mode', True)

                # Carica intensità
                intensity_value = state_data.get('current_intensity', 0.5)
                for intensity in CognitiveIntensity:
                    if abs(intensity.value - intensity_value) < 0.1:
                        self.current_intensity = intensity
                        break

                # Carica domini
                domains_data = state_data.get('domains', {})
                for domain_type_str, domain_data in domains_data.items():
                    try:
                        domain_type = CognitiveDomainType(domain_type_str)
                        domain = self.domains[domain_type]

                        domain.activation_level = domain_data.get('activation_level', 0.0)
                        domain.activation_count = domain_data.get('activation_count', 0)
                        domain.success_rate = domain_data.get('success_rate', 1.0)

                        last_activation_str = domain_data.get('last_activation')
                        if last_activation_str:
                            domain.last_activation = datetime.fromisoformat(last_activation_str)

                    except (ValueError, KeyError) as e:
                        logger.warning(f"⚠️ Errore caricamento dominio {domain_type_str}: {e}")

                # Carica metriche
                metrics_data = state_data.get('metrics', {})
                for key, value in metrics_data.items():
                    if hasattr(self.metrics, key):
                        setattr(self.metrics, key, value)

                self._last_update = datetime.now()

            logger.info(f"📂 Stato cognitivo caricato: {filepath}")

        except Exception as e:
            logger.error(f"❌ Errore caricamento stato: {e}")


# Funzioni di utilità per uso esterno
def create_cognitive_state(registry_path: str = None) -> CognitiveState:
    """Crea una nuova istanza di stato cognitivo."""
    if registry_path:
        return CognitiveState(registry_path)
    return CognitiveState()


def analyze_symbols_cognitive_domains(symbols: List[str], cognitive_state: CognitiveState) -> Dict[str, Any]:
    """Analizza domini cognitivi per una lista di simboli."""
    activated_domains = cognitive_state.activate_domains_for_symbols(symbols)

    return {
        'input_symbols': symbols,
        'activated_domains': [d.value for d in activated_domains],
        'domain_count': len(activated_domains),
        'cognitive_coverage': len(activated_domains) / len(CognitiveDomainType),
        'timestamp': datetime.now().isoformat()
    }


if __name__ == "__main__":
    # Test del sistema cognitivo
    print("🧠 NEUROGLYPH Cognitive State - Test")
    print("=" * 50)

    # Crea stato cognitivo
    cognitive_state = create_cognitive_state()

    # Test attivazione domini
    test_symbols = ["◯", "■", "⫗", "≺", "⌀"]
    print(f"\n🧪 Test simboli: {test_symbols}")

    activated = cognitive_state.activate_domains_for_symbols(test_symbols)
    print(f"✅ Domini attivati: {[d.value for d in activated]}")

    # Test stato cognitivo
    status = cognitive_state.get_cognitive_status()
    print(f"\n📊 Stato cognitivo:")
    print(f"  • Coscienza: {status['consciousness_level']:.2f}")
    print(f"  • Auto-consapevolezza: {status['self_awareness']:.2f}")
    print(f"  • Domini attivi: {len(status['active_domains'])}")
    print(f"  • Entropia: {status['cognitive_entropy']:.2f}")

    print("\n✅ Test completato con successo!")
