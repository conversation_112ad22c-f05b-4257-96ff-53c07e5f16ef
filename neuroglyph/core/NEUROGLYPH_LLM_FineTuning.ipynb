{"nbformat": 4, "nbformat_minor": 0, "metadata": {"colab": {"provenance": [], "gpuType": "T4"}, "kernelspec": {"name": "python3", "display_name": "Python 3"}, "language_info": {"name": "python"}, "accelerator": "GPU"}, "cells": [{"cell_type": "markdown", "metadata": {"id": "header"}, "source": ["# 🧠 NEUROGLYPH LLM - Fine-Tuning Qwen2.5-Coder-1.5B\n", "\n", "> **Obiettivo**: Fine-tune Qwen2.5-Coder-1.5B con dataset neuroglifi per creare il primo LLM pensante\n", "\n", "## 🎯 **Setup**\n", "- **Modello**: Qwen2.5-Coder-1.5B-Instruct\n", "- **<PERSON><PERSON><PERSON>**: <PERSON><PERSON> Fine-tuning\n", "- **Dataset**: Neuroglifi → Codice\n", "- **Target**: Ragionamento simbolico + Coding excellence"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "setup"}, "outputs": [], "source": ["# 🔧 SETUP ENVIRONMENT\n", "print(\"🚀 NEUROGLYPH LLM - Fine-Tuning Setup\")\n", "print(\"=\" * 50)\n", "\n", "# Verifica GPU\n", "!nvidia-smi\n", "\n", "# Verifica memoria\n", "import psutil\n", "import torch\n", "\n", "print(f\"💾 RAM: {psutil.virtual_memory().total / (1024**3):.1f} GB\")\n", "print(f\"🚀 GPU: {torch.cuda.get_device_name() if torch.cuda.is_available() else 'No GPU'}\")\n", "print(f\"💻 VRAM: {torch.cuda.get_device_properties(0).total_memory / (1024**3):.1f} GB\" if torch.cuda.is_available() else \"\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "install"}, "outputs": [], "source": ["# 📦 INSTALLAZIONE DIPENDENZE\n", "print(\"📦 Installazione dipendenze per fine-tuning...\")\n", "\n", "!pip install -q transformers accelerate peft datasets bitsandbytes wandb\n", "!pip install -q torch torchvision torchaudio --index-url https://download.pytorch.org/whl/cu118\n", "!pip install -q huggingface_hub tokenizers\n", "\n", "print(\"✅ Dipendenze installate!\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "download_model"}, "outputs": [], "source": ["# 🔽 DOWNLOAD MODELLO QWEN2.5-CODER-1.5B\n", "print(\"🔽 Download Qwen2.5-Coder-1.5B-Instruct...\")\n", "\n", "from transformers import AutoTokenizer, AutoModelForCausalLM\n", "import torch\n", "\n", "model_name = \"Qwen/Qwen2.5-Coder-1.5B-Instruct\"\n", "\n", "# Download tokenizer\n", "print(\"📚 Caricamento tokenizer...\")\n", "tokenizer = AutoTokenizer.from_pretrained(model_name)\n", "tokenizer.pad_token = tokenizer.eos_token\n", "\n", "# Download model con quantizzazione\n", "print(\"🧠 Caricamento modello (4-bit quantized)...\")\n", "model = AutoModelForCausalLM.from_pretrained(\n", "    model_name,\n", "    torch_dtype=torch.float16,\n", "    device_map=\"auto\",\n", "    load_in_4bit=True,  # 4-bit quantization\n", "    bnb_4bit_compute_dtype=torch.float16,\n", "    bnb_4bit_use_double_quant=True,\n", "    bnb_4bit_quant_type=\"nf4\"\n", ")\n", "\n", "print(f\"✅ Modello caricato: {model.get_memory_footprint() / (1024**3):.2f} GB\")\n", "print(f\"📊 Parametri: {model.num_parameters() / 1e6:.1f}M\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "create_dataset"}, "outputs": [], "source": ["# 📚 CREAZIONE DATASET NEUROGLIFI\n", "print(\"📚 Creazione dataset NEUROGLYPH...\")\n", "\n", "# Dataset neuroglifi → codice per NEUROGLYPH LLM\n", "neuroglyph_dataset = [\n", "    {\n", "        \"instruction\": \"Converti questi neuroglifi in codice Python:\",\n", "        \"neuroglyphs\": \"⟨⟩α⟲ι⟦⟧◊ι≤ξ⤴ι\",\n", "        \"code\": \"def sort_list(arr):\\n    for i in range(len(arr)):\\n        for j in range(len(arr)-1-i):\\n            if arr[j] > arr[j+1]:\\n                arr[j], arr[j+1] = arr[j+1], arr[j]\\n    return arr\"\n", "    },\n", "    {\n", "        \"instruction\": \"Implementa questa logica simbolica:\",\n", "        \"neuroglyphs\": \"⟨⟩α∈ι⟦⟧◊ι≡ξ⤴ι\",\n", "        \"code\": \"def search_element(arr, target):\\n    for i, element in enumerate(arr):\\n        if element == target:\\n            return i\\n    return -1\"\n", "    },\n", "    {\n", "        \"instruction\": \"Genera codice da neuroglifi:\",\n", "        \"neuroglyphs\": \"⟨⟩α⊕ι⟦⟧⤴ι\",\n", "        \"code\": \"def calculate_sum(numbers):\\n    total = 0\\n    for num in numbers:\\n        total += num\\n    return total\"\n", "    },\n", "    {\n", "        \"instruction\": \"Traduci simboli di ragionamento:\",\n", "        \"neuroglyphs\": \"⟨⟩α⟲ι⊗ξ◊ι≤ξ⤴ι\",\n", "        \"code\": \"def factorial(n):\\n    if n <= 1:\\n        return 1\\n    return n * factorial(n - 1)\"\n", "    },\n", "    {\n", "        \"instruction\": \"Implementa logica condizionale:\",\n", "        \"neuroglyphs\": \"⟨⟩α◊ι≡ξ∧ι⤴ι\",\n", "        \"code\": \"def is_even(number):\\n    if number % 2 == 0:\\n        return True\\n    return False\"\n", "    },\n", "    {\n", "        \"instruction\": \"Crea funzione da neuroglifi:\",\n", "        \"neuroglyphs\": \"⟨⟩α⟦⟧⟲ι⊕ξ⤴ι\",\n", "        \"code\": \"def fi<PERSON><PERSON><PERSON>(n):\\n    if n <= 1:\\n        return n\\n    a, b = 0, 1\\n    for _ in range(2, n + 1):\\n        a, b = b, a + b\\n    return b\"\n", "    },\n", "    {\n", "        \"instruction\": \"Interpreta simboli di validazione:\",\n", "        \"neuroglyphs\": \"⟨⟩α∈ι⟦⟧∧ι≢ξ⤴ι\",\n", "        \"code\": \"def validate_input(data, valid_types):\\n    for item in data:\\n        if type(item) not in valid_types:\\n            return False\\n    return True\"\n", "    },\n", "    {\n", "        \"instruction\": \"Genera algoritmo da neuroglifi:\",\n", "        \"neuroglyphs\": \"⟨⟩α⟦⟧⟲ι≺ξ⊕ι⤴ι\",\n", "        \"code\": \"def merge_sort(arr):\\n    if len(arr) <= 1:\\n        return arr\\n    mid = len(arr) // 2\\n    left = merge_sort(arr[:mid])\\n    right = merge_sort(arr[mid:])\\n    return merge(left, right)\"\n", "    }\n", "]\n", "\n", "print(f\"✅ Dataset creato: {len(neuroglyph_dataset)} esempi\")\n", "\n", "# Formatta dataset per training\n", "def format_training_data(examples):\n", "    formatted = []\n", "    for ex in examples:\n", "        prompt = f\"\"\"### Instruction:\n", "{ex['instruction']}\n", "\n", "### Neuroglyphs:\n", "{ex['neuroglyphs']}\n", "\n", "### Response:\n", "{ex['code']}\"\"\"\n", "        formatted.append({\"text\": prompt})\n", "    return formatted\n", "\n", "training_data = format_training_data(neuroglyph_dataset)\n", "print(f\"📊 Dati formattati per training: {len(training_data)} esempi\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "setup_lora"}, "outputs": [], "source": ["# ⚙️ SETUP LORA CONFIGURATION\n", "print(\"⚙️ Configurazione LoRA per NEUROGLYPH...\")\n", "\n", "from peft import LoraConfig, get_peft_model, TaskType\n", "\n", "# Configurazione LoRA ottimizzata per Qwen2.5-Coder\n", "lora_config = LoraConfig(\n", "    task_type=TaskType.CAUSAL_LM,\n", "    r=16,                    # Rank LoRA\n", "    lora_alpha=32,           # Alpha scaling\n", "    lora_dropout=0.1,        # Dropout\n", "    target_modules=[         # Target modules per Qwen\n", "        \"q_proj\",\n", "        \"k_proj\", \n", "        \"v_proj\",\n", "        \"o_proj\",\n", "        \"gate_proj\",\n", "        \"up_proj\",\n", "        \"down_proj\"\n", "    ],\n", "    bias=\"none\",\n", "    inference_mode=False\n", ")\n", "\n", "# Applica LoRA al modello\n", "model = get_peft_model(model, lora_config)\n", "\n", "# Mostra parametri trainable\n", "trainable_params = sum(p.numel() for p in model.parameters() if p.requires_grad)\n", "total_params = sum(p.numel() for p in model.parameters())\n", "\n", "print(f\"📊 Parametri trainable: {trainable_params:,} ({trainable_params/total_params*100:.2f}%)\")\n", "print(f\"📊 Parametri totali: {total_params:,}\")\n", "print(\"✅ LoRA configurato per NEUROGLYPH!\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "training"}, "outputs": [], "source": ["# 🚀 FINE-TUNING NEUROGLYPH LLM\n", "print(\"🚀 Inizio fine-tuning NEUROGLYPH LLM...\")\n", "\n", "from transformers import TrainingArguments, Trainer, DataCollatorForLanguageModeling\n", "from datasets import Dataset\n", "\n", "# Prepara dataset\n", "def tokenize_function(examples):\n", "    return tokenizer(\n", "        examples[\"text\"],\n", "        truncation=True,\n", "        padding=True,\n", "        max_length=512,\n", "        return_tensors=\"pt\"\n", "    )\n", "\n", "# Crea dataset\n", "dataset = Dataset.from_list(training_data)\n", "tokenized_dataset = dataset.map(tokenize_function, batched=True)\n", "\n", "# Data collator\n", "data_collator = DataCollatorForLanguageModeling(\n", "    tokenizer=tokenizer,\n", "    mlm=False  # Causal LM\n", ")\n", "\n", "# Training arguments\n", "training_args = TrainingArguments(\n", "    output_dir=\"./neuroglyph-qwen-1.5b\",\n", "    num_train_epochs=3,\n", "    per_device_train_batch_size=2,\n", "    gradient_accumulation_steps=4,\n", "    warmup_steps=10,\n", "    learning_rate=2e-4,\n", "    fp16=True,\n", "    logging_steps=1,\n", "    save_strategy=\"epoch\",\n", "    evaluation_strategy=\"no\",\n", "    remove_unused_columns=False,\n", "    push_to_hub=False,\n", "    report_to=\"none\"\n", ")\n", "\n", "# Trainer\n", "trainer = Trainer(\n", "    model=model,\n", "    args=training_args,\n", "    train_dataset=tokenized_dataset,\n", "    data_collator=data_collator,\n", "    tokenizer=tokenizer\n", ")\n", "\n", "print(\"✅ Trainer configurato, inizio training...\")\n", "\n", "# Avvia training\n", "trainer.train()\n", "\n", "print(\"🎉 Fine-tuning NEUROGLYPH LLM completato!\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "test_model"}, "outputs": [], "source": ["# 🧪 TEST NEUROGLYPH LLM\n", "print(\"🧪 Test NEUROGLYPH LLM fine-tuned...\")\n", "\n", "def test_neuroglyph_generation(neuroglyphs, instruction):\n", "    prompt = f\"\"\"### Instruction:\n", "{instruction}\n", "\n", "### Neuroglyphs:\n", "{neuroglyphs}\n", "\n", "### Response:\n", "\"\"\"\n", "    \n", "    inputs = tokenizer(prompt, return_tensors=\"pt\").to(model.device)\n", "    \n", "    with torch.no_grad():\n", "        outputs = model.generate(\n", "            **inputs,\n", "            max_new_tokens=150,\n", "            temperature=0.7,\n", "            do_sample=True,\n", "            pad_token_id=tokenizer.eos_token_id\n", "        )\n", "    \n", "    response = tokenizer.decode(outputs[0], skip_special_tokens=True)\n", "    return response.split(\"### Response:\")[-1].strip()\n", "\n", "# Test cases\n", "test_cases = [\n", "    {\n", "        \"neuroglyphs\": \"⟨⟩α⟦⟧⟲ι⊗ξ⤴ι\",\n", "        \"instruction\": \"Genera una funzione da questi neuroglifi:\"\n", "    },\n", "    {\n", "        \"neuroglyphs\": \"⟨⟩α∈ι⟦⟧≡ξ⤴ι\",\n", "        \"instruction\": \"Implementa questa logica simbolica:\"\n", "    }\n", "]\n", "\n", "print(\"🎯 Test NEUROGLYPH LLM:\")\n", "print(\"=\" * 60)\n", "\n", "for i, test in enumerate(test_cases):\n", "    print(f\"\\n🧪 Test {i+1}:\")\n", "    print(f\"Neuroglifi: {test['neuroglyphs']}\")\n", "    print(f\"Istruzione: {test['instruction']}\")\n", "    \n", "    result = test_neuroglyph_generation(test['neuroglyphs'], test['instruction'])\n", "    print(f\"\\n🤖 NEUROGLYPH Output:\")\n", "    print(\"-\" * 40)\n", "    print(result)\n", "    print(\"-\" * 40)\n", "\n", "print(\"\\n🎉 Test NEUROGLYPH LLM completati!\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "save_model"}, "outputs": [], "source": ["# 💾 SALVATAGGIO MODELLO NEUROGLYPH\n", "print(\"💾 Salvataggio NEUROGLYPH LLM...\")\n", "\n", "# Salva modello LoRA\n", "model.save_pretrained(\"./neuroglyph-qwen-1.5b-lora\")\n", "tokenizer.save_pretrained(\"./neuroglyph-qwen-1.5b-lora\")\n", "\n", "print(\"✅ NEUROGLYPH LLM salvato in: ./neuroglyph-qwen-1.5b-lora\")\n", "\n", "# Crea archivio per download\n", "!zip -r neuroglyph_llm_1.5b.zip ./neuroglyph-qwen-1.5b-lora\n", "\n", "print(\"📦 Archivio creato: neuroglyph_llm_1.5b.zip\")\n", "print(\"🔽 Puoi scaricare il modello dal menu Files di Colab\")\n", "\n", "# Statistiche finali\n", "print(\"\\n📊 STATISTICHE NEUROGLYPH LLM:\")\n", "print(f\"🧠 Modello base: Qwen2.5-Coder-1.5B-Instruct\")\n", "print(f\"⚙️ Metodo: LoRA Fine-tuning\")\n", "print(f\"📚 Dataset: {len(neuroglyph_dataset)} esempi neuroglifi\")\n", "print(f\"🎯 Specializzazione: Ragionamento simbolico + Coding\")\n", "print(f\"💾 Dimensione: ~1.5GB base + ~50MB LoRA\")\n", "print(\"\\n🎉 NEUROGLYPH LLM - Primo LLM pensante creato!\")"]}]}