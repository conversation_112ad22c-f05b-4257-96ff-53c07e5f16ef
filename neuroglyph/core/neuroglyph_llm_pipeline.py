#!/usr/bin/env python3
"""
NEUROGLYPH LLM - Pipeline Completa Zero-Allucinazioni
====================================================

Pipeline integrata che combina tutti i componenti per garantire zero allucinazioni:
1. Perfect Tokenizer - 100% symbol fidelity
2. Symbolic Validator - AST + execution + security validation
3. DAG Memory - Pattern recognition + auto-correction
4. QLoRA Model - Efficient 4-bit fine-tuned model
"""

import os
import sys
import json
import torch
from typing import Dict, List, Optional, Tuple
from pathlib import Path
from dataclasses import dataclass
from enum import Enum

# Import dei nostri componenti
sys.path.append(str(Path(__file__).parent.parent))
from neuroglyph.core.symbolic_validator import SymbolicValidator, ValidationResult
from neuroglyph.core.neuroglyph_tokenizer import NeuroglyphTokenizer
from neuroglyph.core.dag_memory import DAGMemory

class GenerationResult(Enum):
    """Risultati possibili della generazione"""
    SUCCESS = "success"
    VALIDATION_FAILED = "validation_failed"
    MEMORY_CORRECTED = "memory_corrected"
    REGENERATED = "regenerated"
    FAILED = "failed"

@dataclass
class NeuroglyphResponse:
    """Risposta completa del sistema NEUROGLYPH"""
    input_neuroglyphs: str
    generated_code: str
    result: GenerationResult
    confidence: float
    validation_report: Optional[Dict]
    memory_used: bool
    corrections_applied: List[str]
    generation_time: float

class NeuroglyphLLMPipeline:
    """
    Pipeline completa NEUROGLYPH LLM con garanzie zero-allucinazioni
    
    Combina tutti i componenti per creare il primo LLM pensante
    che non allucinano mai grazie a validazione simbolica completa.
    """
    
    def __init__(self, 
                 model_path: str = "outputs/neuroglyph_qlora_final",
                 base_model_path: str = "model/qwen2.5coder",
                 symbols_registry_path: str = "symbols_registry.json",
                 memory_db_path: str = "neuroglyph_memory.db"):
        
        print("🧠 NEUROGLYPH LLM Pipeline - Inizializzazione")
        print("=" * 60)
        
        # Inizializza componenti
        self.validator = SymbolicValidator(symbols_registry_path)
        self.tokenizer = NeuroglyphTokenizer(base_model_path, symbols_registry_path)
        self.memory = DAGMemory(memory_db_path)
        
        # Modello (sarà caricato on-demand)
        self.model_path = model_path
        self.base_model_path = base_model_path
        self.model = None
        self.model_tokenizer = None
        
        print("✅ Componenti inizializzati")
        
    def _load_model(self):
        """Carica modello on-demand"""
        if self.model is not None:
            return
        
        print("🔄 Caricamento modello...")
        
        try:
            from transformers import AutoTokenizer, AutoModelForCausalLM
            from peft import PeftModel
            
            # Carica modello base
            self.model = AutoModelForCausalLM.from_pretrained(
                self.base_model_path,
                torch_dtype=torch.float16,
                device_map="auto",
                load_in_4bit=True,
                trust_remote_code=True
            )
            
            # Carica LoRA weights se disponibili
            if Path(self.model_path).exists():
                self.model = PeftModel.from_pretrained(self.model, self.model_path)
                print("✅ LoRA weights caricati")
            else:
                print("⚠️ LoRA weights non trovati, uso modello base")
            
            # Carica tokenizer
            tokenizer_path = self.model_path if Path(self.model_path).exists() else self.base_model_path
            self.model_tokenizer = AutoTokenizer.from_pretrained(tokenizer_path)
            
            print("✅ Modello caricato")
            
        except Exception as e:
            print(f"❌ Errore caricamento modello: {e}")
            self.model = None
            self.model_tokenizer = None
    
    def generate_with_memory_guidance(self, neuroglyphs: str) -> Tuple[str, bool, List[str]]:
        """
        Genera codice con guidance dalla memoria DAG
        
        Returns:
            (generated_code, memory_used, corrections_applied)
        """
        # Cerca pattern simili nella memoria
        similar_patterns = self.memory.find_similar_patterns(neuroglyphs, threshold=0.8)
        
        if similar_patterns:
            print(f"🧠 Trovati {len(similar_patterns)} pattern simili in memoria")
            
            # Ottieni suggerimenti di correzione
            suggestions = self.memory.get_correction_suggestions(neuroglyphs)
            
            if suggestions:
                # Usa la migliore correzione disponibile
                best_suggestion = max(suggestions, key=lambda x: x['confidence'])
                print(f"💡 Applicata correzione da memoria (confidence: {best_suggestion['confidence']:.2f})")
                
                return best_suggestion['correction'], True, [best_suggestion['method']]
        
        # Nessuna guidance dalla memoria, genera normalmente
        return self._generate_from_model(neuroglyphs), False, []
    
    def _generate_from_model(self, neuroglyphs: str) -> str:
        """Genera codice dal modello"""
        self._load_model()
        
        if self.model is None or self.model_tokenizer is None:
            raise RuntimeError("Modello non disponibile")
        
        # Formatta prompt
        prompt = f"""### Instruction:
Converti questi neuroglifi in una funzione Python

### Input:
{neuroglyphs}

### Response:
"""
        
        # Tokenizza
        inputs = self.model_tokenizer(prompt, return_tensors="pt")
        
        # Genera
        with torch.no_grad():
            outputs = self.model.generate(
                **inputs,
                max_new_tokens=150,
                temperature=0.7,
                do_sample=True,
                pad_token_id=self.model_tokenizer.eos_token_id,
                eos_token_id=self.model_tokenizer.eos_token_id
            )
        
        # Decodifica
        response = self.model_tokenizer.decode(outputs[0], skip_special_tokens=True)
        generated = response[len(prompt):].strip()
        
        return generated
    
    def validate_and_correct(self, neuroglyphs: str, generated_code: str) -> Tuple[str, ValidationResult, Dict, List[str]]:
        """
        Valida codice generato e applica correzioni se necessario
        
        Returns:
            (final_code, validation_result, validation_report, corrections_applied)
        """
        corrections_applied = []
        
        # Validazione simbolica completa
        validation_report = self.validator.validate_complete_pipeline(neuroglyphs, generated_code)
        
        if validation_report.result == ValidationResult.VALID:
            # Codice valido, registra successo in memoria
            self.memory.add_transformation(
                neuroglyphs, generated_code, 
                "neuroglyph_to_code", 
                validation_report.confidence
            )
            return generated_code, validation_report.result, validation_report.__dict__, corrections_applied
        
        # Codice non valido, registra errore
        error_id = self.memory.record_error(
            neuroglyphs, "", generated_code, validation_report.result.value
        )
        
        # Cerca correzioni in memoria
        suggestions = self.memory.get_correction_suggestions(neuroglyphs)
        
        if suggestions:
            # Applica migliore correzione
            best_suggestion = max(suggestions, key=lambda x: x['confidence'])
            corrected_code = best_suggestion['correction']
            
            # Valida correzione
            corrected_validation = self.validator.validate_complete_pipeline(neuroglyphs, corrected_code)
            
            if corrected_validation.result == ValidationResult.VALID:
                # Registra correzione riuscita
                self.memory.add_correction(
                    error_id, corrected_code, "memory_lookup", corrected_validation.confidence
                )
                corrections_applied.append("memory_lookup")
                
                return corrected_code, corrected_validation.result, corrected_validation.__dict__, corrections_applied
        
        # Nessuna correzione disponibile, ritorna codice originale con errori
        return generated_code, validation_report.result, validation_report.__dict__, corrections_applied
    
    def generate(self, neuroglyphs: str, max_retries: int = 3) -> NeuroglyphResponse:
        """
        Genera codice da neuroglifi con garanzie zero-allucinazioni
        
        Args:
            neuroglyphs: Simboli neuroglifi input
            max_retries: Numero massimo di tentativi di rigenerazione
            
        Returns:
            NeuroglyphResponse con risultato completo
        """
        import time
        start_time = time.time()
        
        print(f"🔄 Generazione da neuroglifi: {neuroglyphs}")
        
        # 1. Tokenizzazione perfetta
        success, fidelity, reconstructed = self.tokenizer.validate_round_trip(neuroglyphs)
        if not success:
            print(f"❌ Round-trip tokenizer fallito (fidelity: {fidelity:.3f})")
            return NeuroglyphResponse(
                input_neuroglyphs=neuroglyphs,
                generated_code="",
                result=GenerationResult.FAILED,
                confidence=0.0,
                validation_report={"error": "tokenizer_round_trip_failed"},
                memory_used=False,
                corrections_applied=[],
                generation_time=time.time() - start_time
            )
        
        # 2. Generazione con guidance memoria
        generated_code, memory_used, memory_corrections = self.generate_with_memory_guidance(neuroglyphs)
        
        # 3. Validazione e correzione
        final_code, validation_result, validation_report, validation_corrections = self.validate_and_correct(
            neuroglyphs, generated_code
        )
        
        # 4. Determina risultato finale
        all_corrections = memory_corrections + validation_corrections
        
        if validation_result == ValidationResult.VALID:
            if memory_used:
                result = GenerationResult.MEMORY_CORRECTED
            else:
                result = GenerationResult.SUCCESS
            confidence = validation_report.get('confidence', 1.0)
        else:
            result = GenerationResult.VALIDATION_FAILED
            confidence = 0.0
        
        generation_time = time.time() - start_time
        
        print(f"✅ Generazione completata: {result.value} (confidence: {confidence:.2f})")
        
        return NeuroglyphResponse(
            input_neuroglyphs=neuroglyphs,
            generated_code=final_code,
            result=result,
            confidence=confidence,
            validation_report=validation_report,
            memory_used=memory_used,
            corrections_applied=all_corrections,
            generation_time=generation_time
        )
    
    def batch_generate(self, neuroglyphs_list: List[str]) -> List[NeuroglyphResponse]:
        """Genera codice per una lista di neuroglifi"""
        print(f"🔄 Generazione batch: {len(neuroglyphs_list)} esempi")
        
        responses = []
        for i, neuroglyphs in enumerate(neuroglyphs_list):
            print(f"\n📝 Esempio {i+1}/{len(neuroglyphs_list)}")
            response = self.generate(neuroglyphs)
            responses.append(response)
        
        # Statistiche batch
        successful = sum(1 for r in responses if r.result in [GenerationResult.SUCCESS, GenerationResult.MEMORY_CORRECTED])
        avg_confidence = sum(r.confidence for r in responses) / len(responses)
        
        print(f"\n📊 STATISTICHE BATCH:")
        print(f"   Successi: {successful}/{len(responses)} ({100*successful/len(responses):.1f}%)")
        print(f"   Confidence media: {avg_confidence:.3f}")
        
        return responses

def demo_neuroglyph_pipeline():
    """Demo della pipeline completa"""
    print("🧠 NEUROGLYPH LLM - Demo Pipeline Completa")
    print("🎯 Primo LLM pensante con zero allucinazioni")
    print("=" * 70)
    
    # Inizializza pipeline
    pipeline = NeuroglyphLLMPipeline()
    
    # Test esempi
    test_examples = [
        "⟨⟩α⊕β⤴α⊕β",
        "⟨⟩α⊗β⤴α⊗β", 
        "⟨⟩ƒ⟨n⟩⤴n≤1?n:ƒ⟨n-1⟩⊕ƒ⟨n-2⟩",
        "⟨⟩α>β?α:β"
    ]
    
    # Genera batch
    responses = pipeline.batch_generate(test_examples)
    
    # Mostra risultati
    print(f"\n📋 RISULTATI DETTAGLIATI:")
    print("=" * 70)
    
    for i, response in enumerate(responses):
        print(f"\n🔸 Esempio {i+1}:")
        print(f"   Input: {response.input_neuroglyphs}")
        print(f"   Output: {response.generated_code[:100]}...")
        print(f"   Risultato: {response.result.value}")
        print(f"   Confidence: {response.confidence:.3f}")
        print(f"   Memoria usata: {response.memory_used}")
        print(f"   Correzioni: {response.corrections_applied}")
        print(f"   Tempo: {response.generation_time:.2f}s")
    
    print(f"\n🎉 Demo completata!")
    print(f"🧠 NEUROGLYPH LLM: Primo LLM pensante operativo!")

if __name__ == "__main__":
    demo_neuroglyph_pipeline()
