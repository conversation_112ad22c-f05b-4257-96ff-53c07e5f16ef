#!/usr/bin/env python3
"""
NEUROGLYPH LLM - THINKING ENGINE
================================

Il motore di pensiero centrale che trasforma NEUROGLYPH da LLM statistico
a sistema pensante con ragionamento simbolico, auto-riflessione e creatività.

Componenti:
- ThinkingEngine: Motore principale di pensiero
- ThoughtProcess: Processo di pensiero completo
- ReasoningChain: Catena di ragionamento simbolico
- CognitiveIntegration: Integrazione con componenti esistenti

Autore: NEUROGLYPH ULTRA Team
Data: 2025-05-26
Licenza: MIT
"""

import sys
import os
import json
import time
import logging
import hashlib
from datetime import datetime
from typing import Dict, List, Set, Any, Optional, Tuple, Union
from dataclasses import dataclass, field
from enum import Enum
from pathlib import Path

# Aggiungi path per import moduli NEUROGLYPH
current_dir = os.path.dirname(os.path.abspath(__file__))
parent_dir = os.path.dirname(current_dir)
root_dir = os.path.dirname(parent_dir)
sys.path.append(root_dir)

# Import componenti NEUROGLYPH esistenti
from cognitive_state import (
    CognitiveState, CognitiveDomainType, ThoughtType, ThoughtTrace,
    CognitiveIntensity, create_cognitive_state
)

# Setup logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Import componenti SOCRATE (temporaneamente disabilitato per test)
SOCRATEPlanner = None
SOCRATELogicSimulator = None
logger.warning("⚠️ Componenti SOCRATE temporaneamente disabilitati per test")

# Import DAG Memory (temporaneamente disabilitato per test)
DAGMemory = None
logger.warning("⚠️ DAG Memory temporaneamente disabilitato per test")

class ThinkingMode(Enum):
    """Modi di pensiero supportati."""
    ANALYTICAL = "analytical"      # Pensiero analitico step-by-step
    CREATIVE = "creative"          # Pensiero creativo e divergente
    CRITICAL = "critical"          # Pensiero critico e valutativo
    INTUITIVE = "intuitive"        # Pensiero intuitivo e rapido
    REFLECTIVE = "reflective"      # Auto-riflessione e metacognizione
    PROBLEM_SOLVING = "problem_solving"  # Risoluzione problemi
    LEARNING = "learning"          # Apprendimento e adattamento

class ThinkingStatus(Enum):
    """Stati del processo di pensiero."""
    IDLE = "idle"
    THINKING = "thinking"
    REASONING = "reasoning"
    VALIDATING = "validating"
    REFLECTING = "reflecting"
    COMPLETED = "completed"
    ERROR = "error"

@dataclass
class ReasoningStep:
    """Singolo step di ragionamento."""
    step_id: int
    step_type: str
    input_symbols: List[str]
    reasoning_rule: str
    output_symbols: List[str]
    confidence: float
    processing_time: float
    success: bool
    error_message: Optional[str] = None
    metadata: Dict[str, Any] = field(default_factory=dict)

@dataclass
class ThoughtProcess:
    """Processo di pensiero completo."""
    process_id: str
    thinking_mode: ThinkingMode
    input_symbols: List[str]
    active_domains: List[CognitiveDomainType]
    reasoning_steps: List[ReasoningStep]
    output_symbols: List[str]
    final_confidence: float
    total_processing_time: float
    status: ThinkingStatus
    success: bool
    start_time: datetime
    end_time: Optional[datetime] = None
    error_message: Optional[str] = None
    metadata: Dict[str, Any] = field(default_factory=dict)

    def __post_init__(self):
        """Post-inizializzazione per generare ID se mancante."""
        if not self.process_id:
            content = f"{self.input_symbols}{self.start_time.isoformat()}"
            self.process_id = hashlib.md5(content.encode()).hexdigest()[:16]

class ThinkingEngine:
    """
    Motore di pensiero centrale di NEUROGLYPH LLM.

    Integra tutti i componenti esistenti per creare un sistema
    di pensiero simbolico coerente e auto-riflessivo.
    """

    def __init__(self,
                 cognitive_state: Optional[CognitiveState] = None,
                 registry_path: str = "neuroglyph/core/locked_registry_godmode_v9.json"):
        """
        Inizializza il motore di pensiero.

        Args:
            cognitive_state: Stato cognitivo esistente o None per crearne uno nuovo
            registry_path: Percorso al registry simbolico
        """
        # Componenti principali
        self.cognitive_state = cognitive_state or create_cognitive_state(registry_path)

        # Inizializza componenti SOCRATE se disponibili
        self.socrate_planner = SOCRATEPlanner() if SOCRATEPlanner else None
        self.logic_simulator = SOCRATELogicSimulator() if SOCRATELogicSimulator else None

        # Inizializza DAG Memory se disponibile
        self.dag_memory = DAGMemory() if DAGMemory else None

        # Stato interno
        self.current_process: Optional[ThoughtProcess] = None
        self.thinking_history: List[ThoughtProcess] = []
        self.default_mode = ThinkingMode.ANALYTICAL
        self.max_reasoning_steps = 10
        self.confidence_threshold = 0.7
        self.creativity_boost = 0.0
        self.learning_rate = 0.1

        # Statistiche
        self.stats = {
            'total_thoughts': 0,
            'successful_thoughts': 0,
            'average_confidence': 0.0,
            'average_processing_time': 0.0,
            'mode_distribution': {mode.value: 0 for mode in ThinkingMode},
            'domain_usage': {},
            'error_patterns': {}
        }

        logger.info("🧠 NEUROGLYPH Thinking Engine inizializzato")
        logger.info(f"🎯 SOCRATE disponibile: {self.socrate_planner is not None}")
        logger.info(f"💾 DAG Memory disponibile: {self.dag_memory is not None}")

    def think(self,
              input_symbols: List[str],
              mode: ThinkingMode = None,
              context: Optional[Dict[str, Any]] = None) -> ThoughtProcess:
        """
        Processo di pensiero principale.

        Args:
            input_symbols: Simboli neuroglifi di input
            mode: Modalità di pensiero (default: ANALYTICAL)
            context: Contesto aggiuntivo per il pensiero

        Returns:
            Processo di pensiero completato
        """
        start_time = datetime.now()
        mode = mode or self.default_mode
        context = context or {}

        logger.info(f"🧠 Inizio pensiero: {len(input_symbols)} simboli, modo {mode.value}")

        # Crea processo di pensiero
        process = ThoughtProcess(
            process_id="",
            thinking_mode=mode,
            input_symbols=input_symbols.copy(),
            active_domains=[],
            reasoning_steps=[],
            output_symbols=[],
            final_confidence=0.0,
            total_processing_time=0.0,
            status=ThinkingStatus.THINKING,
            success=False,
            start_time=start_time,
            metadata=context
        )

        self.current_process = process

        try:
            # Fase 1: Attivazione domini cognitivi
            activated_domains = self._activate_cognitive_domains(input_symbols)
            process.active_domains = list(activated_domains)

            # Fase 2: Costruzione ragionamento
            if mode == ThinkingMode.CREATIVE:
                reasoning_chain = self._creative_reasoning(input_symbols, context)
            elif mode == ThinkingMode.CRITICAL:
                reasoning_chain = self._critical_reasoning(input_symbols, context)
            elif mode == ThinkingMode.REFLECTIVE:
                reasoning_chain = self._reflective_reasoning(input_symbols, context)
            else:
                reasoning_chain = self._analytical_reasoning(input_symbols, context)

            process.reasoning_steps = reasoning_chain
            process.status = ThinkingStatus.REASONING

            # Fase 3: Validazione logica
            validation_result = self._validate_reasoning(reasoning_chain)
            process.status = ThinkingStatus.VALIDATING

            # Fase 4: Generazione output
            output_symbols = self._generate_output_symbols(reasoning_chain, validation_result)
            process.output_symbols = output_symbols

            # Fase 5: Auto-riflessione
            if mode != ThinkingMode.REFLECTIVE:  # Evita ricorsione infinita
                reflection = self._self_reflect(process)
                if reflection:
                    process.metadata['reflection'] = reflection

            process.status = ThinkingStatus.REFLECTING

            # Calcola confidenza finale
            process.final_confidence = self._calculate_final_confidence(process)
            process.success = process.final_confidence >= self.confidence_threshold
            process.status = ThinkingStatus.COMPLETED

        except Exception as e:
            logger.error(f"❌ Errore durante pensiero: {e}")
            process.error_message = str(e)
            process.status = ThinkingStatus.ERROR
            process.success = False

        finally:
            # Finalizza processo
            process.end_time = datetime.now()
            process.total_processing_time = (process.end_time - process.start_time).total_seconds()

            # Registra nel cognitive state
            thought_trace = self._create_thought_trace(process)
            self.cognitive_state.record_thought(thought_trace)

            # Salva in memoria DAG se disponibile
            if self.dag_memory and process.success:
                self._save_to_dag_memory(process)

            # Aggiorna statistiche
            self._update_stats(process)

            # Aggiungi alla storia
            self.thinking_history.append(process)

            self.current_process = None

            logger.info(f"✅ Pensiero completato: {process.success}, confidenza {process.final_confidence:.2f}")

        return process

    def _activate_cognitive_domains(self, symbols: List[str]) -> Set[CognitiveDomainType]:
        """Attiva domini cognitivi basati sui simboli."""
        return self.cognitive_state.activate_domains_for_symbols(symbols)

    def _analytical_reasoning(self, symbols: List[str], context: Dict[str, Any]) -> List[ReasoningStep]:
        """Ragionamento analitico step-by-step."""
        steps = []

        if self.socrate_planner:
            try:
                # Usa SOCRATE per costruire DAG di ragionamento
                dag = self.socrate_planner.build_reasoning_dag(symbols, context)

                # Converte DAG in steps
                for i, node in enumerate(dag.nodes.values()):
                    step = ReasoningStep(
                        step_id=i + 1,
                        step_type="deductive",
                        input_symbols=symbols if i == 0 else steps[i-1].output_symbols,
                        reasoning_rule=node.reasoning_type,
                        output_symbols=[node.content],  # Semplificato
                        confidence=node.confidence,
                        processing_time=0.1,
                        success=True
                    )
                    steps.append(step)

            except Exception as e:
                logger.warning(f"⚠️ Errore SOCRATE reasoning: {e}")
                # Fallback a ragionamento semplice
                steps = self._simple_reasoning(symbols)
        else:
            steps = self._simple_reasoning(symbols)

        return steps

    def _creative_reasoning(self, symbols: List[str], context: Dict[str, Any]) -> List[ReasoningStep]:
        """Ragionamento creativo e divergente."""
        # Attiva modalità creatività
        self.cognitive_state.stimulate_creativity(0.9)

        steps = []

        # Genera combinazioni creative di simboli
        for i in range(min(3, len(symbols))):
            step = ReasoningStep(
                step_id=i + 1,
                step_type="creative",
                input_symbols=symbols[i:i+2] if i < len(symbols)-1 else [symbols[i]],
                reasoning_rule="creative_combination",
                output_symbols=[f"creative_{i}"],  # Placeholder
                confidence=0.6 + self.creativity_boost,
                processing_time=0.2,
                success=True
            )
            steps.append(step)

        return steps

    def _critical_reasoning(self, symbols: List[str], context: Dict[str, Any]) -> List[ReasoningStep]:
        """Ragionamento critico e valutativo."""
        steps = []

        # Analizza criticamente ogni simbolo
        for i, symbol in enumerate(symbols):
            step = ReasoningStep(
                step_id=i + 1,
                step_type="critical",
                input_symbols=[symbol],
                reasoning_rule="critical_analysis",
                output_symbols=[f"analyzed_{symbol}"],
                confidence=0.8,
                processing_time=0.15,
                success=True
            )
            steps.append(step)

        return steps

    def _reflective_reasoning(self, symbols: List[str], context: Dict[str, Any]) -> List[ReasoningStep]:
        """Auto-riflessione metacognitiva."""
        steps = []

        # Riflette sui propri processi di pensiero
        if self.thinking_history:
            last_process = self.thinking_history[-1]

            step = ReasoningStep(
                step_id=1,
                step_type="reflective",
                input_symbols=symbols,
                reasoning_rule="metacognitive_reflection",
                output_symbols=[f"reflection_on_{last_process.process_id}"],
                confidence=0.7,
                processing_time=0.3,
                success=True,
                metadata={'reflected_process': last_process.process_id}
            )
            steps.append(step)

        return steps

    def _simple_reasoning(self, symbols: List[str]) -> List[ReasoningStep]:
        """Ragionamento semplice di fallback."""
        steps = []

        step = ReasoningStep(
            step_id=1,
            step_type="simple",
            input_symbols=symbols,
            reasoning_rule="identity",
            output_symbols=symbols.copy(),
            confidence=0.5,
            processing_time=0.05,
            success=True
        )
        steps.append(step)

        return steps

    def _validate_reasoning(self, steps: List[ReasoningStep]) -> Dict[str, Any]:
        """
        Valida la catena di ragionamento usando SOCRATE Logic Simulator.

        Args:
            steps: Lista di step di ragionamento

        Returns:
            Risultato della validazione
        """
        validation_result = {
            'valid': True,
            'confidence': 1.0,
            'errors': [],
            'warnings': [],
            'simulation_trace': None
        }

        if self.logic_simulator and steps:
            try:
                # Costruisce DAG dai steps per simulazione
                # Questo è semplificato - in implementazione completa
                # convertirebbe steps in formato DAG appropriato

                # Per ora, valida logica base
                for i, step in enumerate(steps):
                    if not step.success:
                        validation_result['valid'] = False
                        validation_result['errors'].append(f"Step {i+1} fallito: {step.error_message}")

                    if step.confidence < 0.5:
                        validation_result['warnings'].append(f"Step {i+1} bassa confidenza: {step.confidence:.2f}")

                # Calcola confidenza media
                if steps:
                    avg_confidence = sum(s.confidence for s in steps) / len(steps)
                    validation_result['confidence'] = avg_confidence

            except Exception as e:
                logger.warning(f"⚠️ Errore validazione SOCRATE: {e}")
                validation_result['warnings'].append(f"Validazione SOCRATE fallita: {e}")

        return validation_result

    def _generate_output_symbols(self, steps: List[ReasoningStep], validation: Dict[str, Any]) -> List[str]:
        """
        Genera simboli di output dalla catena di ragionamento.

        Args:
            steps: Step di ragionamento
            validation: Risultato validazione

        Returns:
            Lista di simboli di output
        """
        output_symbols = []

        if not steps:
            return output_symbols

        # Raccoglie output da tutti gli step
        for step in steps:
            output_symbols.extend(step.output_symbols)

        # Rimuove duplicati mantenendo ordine
        seen = set()
        unique_output = []
        for symbol in output_symbols:
            if symbol not in seen:
                seen.add(symbol)
                unique_output.append(symbol)

        # Se validazione fallita, applica correzioni
        if not validation['valid']:
            # Strategia di correzione semplice
            unique_output = [f"corrected_{s}" for s in unique_output]

        return unique_output

    def _self_reflect(self, process: ThoughtProcess) -> Optional[Dict[str, Any]]:
        """
        Auto-riflessione sul processo di pensiero appena completato.

        Args:
            process: Processo di pensiero da riflettere

        Returns:
            Risultato della riflessione o None
        """
        if not process.reasoning_steps:
            return None

        reflection = {
            'process_quality': 'good' if process.final_confidence > 0.7 else 'poor',
            'reasoning_depth': len(process.reasoning_steps),
            'domain_coverage': len(process.active_domains),
            'improvement_suggestions': [],
            'learned_patterns': []
        }

        # Analizza qualità del ragionamento
        if process.final_confidence < 0.5:
            reflection['improvement_suggestions'].append("Aumentare profondità di ragionamento")

        if len(process.active_domains) < 2:
            reflection['improvement_suggestions'].append("Attivare più domini cognitivi")

        # Identifica pattern appresi
        if len(process.reasoning_steps) > 3:
            reflection['learned_patterns'].append("Ragionamento multi-step efficace")

        # Rileva loop cognitivi
        if self.cognitive_state.detect_cognitive_loops():
            reflection['improvement_suggestions'].append("Stimolare creatività per uscire da loop")
            self.cognitive_state.stimulate_creativity()

        return reflection

    def _calculate_final_confidence(self, process: ThoughtProcess) -> float:
        """
        Calcola la confidenza finale del processo di pensiero.

        Args:
            process: Processo di pensiero

        Returns:
            Confidenza finale (0.0-1.0)
        """
        if not process.reasoning_steps:
            return 0.0

        # Confidenza media degli step
        step_confidence = sum(s.confidence for s in process.reasoning_steps) / len(process.reasoning_steps)

        # Bonus per diversità domini
        domain_bonus = min(0.2, len(process.active_domains) * 0.05)

        # Bonus per profondità ragionamento
        depth_bonus = min(0.1, len(process.reasoning_steps) * 0.02)

        # Penalità per errori
        error_penalty = sum(0.1 for s in process.reasoning_steps if not s.success)

        # Bonus creatività se in modalità creativa
        creativity_bonus = 0.1 if process.thinking_mode == ThinkingMode.CREATIVE else 0.0

        final_confidence = step_confidence + domain_bonus + depth_bonus + creativity_bonus - error_penalty

        return max(0.0, min(1.0, final_confidence))

    def _create_thought_trace(self, process: ThoughtProcess) -> ThoughtTrace:
        """
        Crea ThoughtTrace dal processo per il cognitive state.

        Args:
            process: Processo di pensiero

        Returns:
            ThoughtTrace per registrazione
        """
        # Mappa thinking mode a thought type
        mode_to_type = {
            ThinkingMode.ANALYTICAL: ThoughtType.DEDUCTIVE,
            ThinkingMode.CREATIVE: ThoughtType.CREATIVE,
            ThinkingMode.CRITICAL: ThoughtType.CRITICAL,
            ThinkingMode.REFLECTIVE: ThoughtType.REFLECTIVE,
            ThinkingMode.INTUITIVE: ThoughtType.INTUITIVE,
            ThinkingMode.PROBLEM_SOLVING: ThoughtType.DEDUCTIVE,
            ThinkingMode.LEARNING: ThoughtType.INDUCTIVE
        }

        thought_type = mode_to_type.get(process.thinking_mode, ThoughtType.DEDUCTIVE)

        # Crea reasoning steps summary
        reasoning_summary = [
            f"Step {s.step_id}: {s.step_type} -> {s.confidence:.2f}"
            for s in process.reasoning_steps
        ]

        return ThoughtTrace(
            thought_id=process.process_id,
            thought_type=thought_type,
            input_symbols=process.input_symbols,
            active_domains=process.active_domains,
            reasoning_steps=reasoning_summary,
            output_symbols=process.output_symbols,
            confidence=process.final_confidence,
            processing_time=process.total_processing_time,
            timestamp=process.start_time,
            success=process.success,
            error_message=process.error_message,
            metadata=process.metadata
        )

    def _save_to_dag_memory(self, process: ThoughtProcess):
        """
        Salva il processo di pensiero nella DAG Memory.

        Args:
            process: Processo di pensiero da salvare
        """
        if not self.dag_memory:
            return

        try:
            input_text = " ".join(process.input_symbols)
            output_text = " ".join(process.output_symbols)

            self.dag_memory.add_transformation(
                input_text=input_text,
                output_text=output_text,
                transformation_type=f"thinking_{process.thinking_mode.value}",
                confidence=process.final_confidence,
                metadata={
                    'process_id': process.process_id,
                    'active_domains': [d.value for d in process.active_domains],
                    'reasoning_steps': len(process.reasoning_steps),
                    'processing_time': process.total_processing_time
                }
            )

            logger.debug(f"💾 Processo salvato in DAG Memory: {process.process_id}")

        except Exception as e:
            logger.warning(f"⚠️ Errore salvataggio DAG Memory: {e}")

    def _update_stats(self, process: ThoughtProcess):
        """
        Aggiorna statistiche del thinking engine.

        Args:
            process: Processo completato
        """
        self.stats['total_thoughts'] += 1

        if process.success:
            self.stats['successful_thoughts'] += 1

        # Aggiorna confidenza media
        total = self.stats['total_thoughts']
        self.stats['average_confidence'] = (
            (self.stats['average_confidence'] * (total - 1) + process.final_confidence) / total
        )

        # Aggiorna tempo medio
        self.stats['average_processing_time'] = (
            (self.stats['average_processing_time'] * (total - 1) + process.total_processing_time) / total
        )

        # Aggiorna distribuzione modalità
        self.stats['mode_distribution'][process.thinking_mode.value] += 1

        # Aggiorna uso domini
        for domain in process.active_domains:
            domain_name = domain.value
            if domain_name not in self.stats['domain_usage']:
                self.stats['domain_usage'][domain_name] = 0
            self.stats['domain_usage'][domain_name] += 1

        # Registra errori
        if not process.success and process.error_message:
            error_type = type(process.error_message).__name__
            if error_type not in self.stats['error_patterns']:
                self.stats['error_patterns'][error_type] = 0
            self.stats['error_patterns'][error_type] += 1

    def get_thinking_status(self) -> Dict[str, Any]:
        """
        Restituisce stato corrente del thinking engine.

        Returns:
            Dizionario con stato dettagliato
        """
        status = {
            'timestamp': datetime.now().isoformat(),
            'current_process': {
                'active': self.current_process is not None,
                'process_id': self.current_process.process_id if self.current_process else None,
                'mode': self.current_process.thinking_mode.value if self.current_process else None,
                'status': self.current_process.status.value if self.current_process else None
            },
            'cognitive_state': self.cognitive_state.get_cognitive_status(),
            'statistics': self.stats.copy(),
            'configuration': {
                'default_mode': self.default_mode.value,
                'max_reasoning_steps': self.max_reasoning_steps,
                'confidence_threshold': self.confidence_threshold,
                'creativity_boost': self.creativity_boost,
                'learning_rate': self.learning_rate
            },
            'components': {
                'socrate_available': self.socrate_planner is not None,
                'dag_memory_available': self.dag_memory is not None,
                'cognitive_state_active': True
            },
            'history': {
                'total_processes': len(self.thinking_history),
                'recent_success_rate': self._calculate_recent_success_rate()
            }
        }

        return status

    def _calculate_recent_success_rate(self, window: int = 10) -> float:
        """Calcola tasso di successo recente."""
        if not self.thinking_history:
            return 0.0

        recent = self.thinking_history[-window:]
        successful = sum(1 for p in recent if p.success)

        return successful / len(recent)

    def set_thinking_mode(self, mode: ThinkingMode):
        """Imposta modalità di pensiero predefinita."""
        self.default_mode = mode
        logger.info(f"🧠 Modalità pensiero impostata: {mode.value}")

    def boost_creativity(self, boost: float = 0.2):
        """Aumenta boost creatività."""
        self.creativity_boost = max(0.0, min(0.5, boost))
        logger.info(f"🎨 Boost creatività: {self.creativity_boost:.2f}")

    def reset_thinking_engine(self, preserve_history: bool = True):
        """
        Reset del thinking engine.

        Args:
            preserve_history: Se mantenere storia dei processi
        """
        self.current_process = None
        self.default_mode = ThinkingMode.ANALYTICAL
        self.creativity_boost = 0.0

        if not preserve_history:
            self.thinking_history.clear()
            self.stats = {
                'total_thoughts': 0,
                'successful_thoughts': 0,
                'average_confidence': 0.0,
                'average_processing_time': 0.0,
                'mode_distribution': {mode.value: 0 for mode in ThinkingMode},
                'domain_usage': {},
                'error_patterns': {}
            }

        # Reset cognitive state
        self.cognitive_state.reset_cognitive_state(preserve_learning=preserve_history)

        logger.info(f"🔄 Thinking Engine resettato (history preserved: {preserve_history})")


# Funzioni di utilità per uso esterno
def create_thinking_engine(registry_path: str = None) -> ThinkingEngine:
    """Crea una nuova istanza di thinking engine."""
    cognitive_state = create_cognitive_state(registry_path) if registry_path else None
    return ThinkingEngine(cognitive_state, registry_path)


def quick_think(symbols: List[str], mode: ThinkingMode = ThinkingMode.ANALYTICAL) -> ThoughtProcess:
    """Funzione di convenienza per pensiero rapido."""
    engine = create_thinking_engine()
    return engine.think(symbols, mode)


if __name__ == "__main__":
    # Test del thinking engine
    print("🧠 NEUROGLYPH Thinking Engine - Test")
    print("=" * 50)

    # Crea thinking engine
    engine = create_thinking_engine()

    # Test pensiero analitico
    test_symbols = ["◯", "■", "⫗", "≺", "⌀"]
    print(f"\n🧪 Test pensiero analitico: {test_symbols}")

    process = engine.think(test_symbols, ThinkingMode.ANALYTICAL)
    print(f"✅ Risultato: {process.success}, confidenza {process.final_confidence:.2f}")
    print(f"📊 Step: {len(process.reasoning_steps)}, domini: {len(process.active_domains)}")

    # Test pensiero creativo
    print(f"\n🎨 Test pensiero creativo: {test_symbols}")

    creative_process = engine.think(test_symbols, ThinkingMode.CREATIVE)
    print(f"✅ Risultato: {creative_process.success}, confidenza {creative_process.final_confidence:.2f}")

    # Stato del sistema
    status = engine.get_thinking_status()
    print(f"\n📊 Stato Thinking Engine:")
    print(f"  • Pensieri totali: {status['statistics']['total_thoughts']}")
    print(f"  • Tasso successo: {status['statistics']['successful_thoughts'] / max(1, status['statistics']['total_thoughts']):.2f}")
    print(f"  • Confidenza media: {status['statistics']['average_confidence']:.2f}")

    print("\n✅ Test completato con successo!")