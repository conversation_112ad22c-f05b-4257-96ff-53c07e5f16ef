"""
NEUROGLIPH Core Encoder
======================

Motore di codifica/decodifica avanzato per la trasformazione del codice in neuroglifi semantici.
Implementa algoritmi di compressione intelligente e mappatura simbolica ottimizzata per LLM.
"""

import json
import ast
import re
from typing import Dict, List, Tuple, Optional, Any, Union
from dataclasses import dataclass
from pathlib import Path
import logging

# Configurazione logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


@dataclass
class EncodingContext:
    """Contesto di codifica per mantenere stato e metadati durante la trasformazione."""
    language: str
    file_path: Optional[str] = None
    encoding_level: int = 1  # 1=base, 2=avanzato, 3=ultra
    preserve_comments: bool = True
    semantic_analysis: bool = True
    compression_ratio: float = 0.0


class NeuroGlyphEncoder:
    """
    Encoder principale per la trasformazione del codice in neuroglifi.

    Caratteristiche:
    - Analisi semantica avanzata del codice
    - Compressione intelligente basata su pattern
    - Mappatura simbolica ottimizzata per LLM
    - Supporto multi-linguaggio
    """

    def __init__(self, symbols_path: str = "core/symbols.json"):
        """Inizializza l'encoder con il dizionario dei simboli ULTRA."""
        # Carica simboli ULTRA
        self.symbol_loader = None
        self.symbol_data = self._load_ultra_symbols(symbols_path)

        # Mappe per accesso rapido
        self.symbol_map = self.symbol_data["symbol_map"]  # symbol -> entry
        self.name_map = self.symbol_data["name_map"]      # name -> entry
        self.id_map = self.symbol_data["id_map"]          # id -> entry

        # Cache e statistiche
        self.pattern_cache = {}
        self.encoding_stats = {
            "total_encodings": 0,
            "avg_compression": 0.0,
            "symbol_usage": {},
            "ultra_ready": True
        }

    def _load_ultra_symbols(self, symbols_path: str) -> Dict[str, Any]:
        """Carica simboli ULTRA evitando import circolari."""
        try:
            # Import locale per evitare circolarità
            import sys
            import os
            sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

            from load_symbols import UltraSymbolLoader

            loader = UltraSymbolLoader()
            self.symbol_loader = loader
            return loader.load_and_validate(symbols_path)

        except Exception as e:
            logger.error(f"Errore caricamento simboli ULTRA: {e}")
            # Fallback a simboli di base
            return self._get_fallback_symbols()

    def _get_fallback_symbols(self) -> Dict[str, Any]:
        """Simboli di fallback se il caricamento ULTRA fallisce."""
        basic_symbols = [
            {"id": "NG0001", "symbol": "⊕", "name": "fix", "category": "action"},
            {"id": "NG0021", "symbol": "⟨⟩", "name": "function", "category": "structure"},
            {"id": "NG0023", "symbol": "◊", "name": "if", "category": "flow"}
        ]

        symbol_map = {s["symbol"]: s for s in basic_symbols}
        name_map = {s["name"]: s for s in basic_symbols}
        id_map = {s["id"]: s for s in basic_symbols}

        return {
            "symbols": basic_symbols,
            "symbol_map": symbol_map,
            "name_map": name_map,
            "id_map": id_map,
            "stats": {"valid_symbols": len(basic_symbols)}
        }

    def get_symbol_by_name(self, name: str) -> Optional[Dict[str, Any]]:
        """Trova un simbolo per nome."""
        return self.name_map.get(name)

    def get_symbol_by_unicode(self, symbol: str) -> Optional[Dict[str, Any]]:
        """Trova un simbolo per carattere Unicode."""
        return self.symbol_map.get(symbol)

    def get_symbols_by_category(self, category: str) -> List[Dict[str, Any]]:
        """Ritorna tutti i simboli di una categoria."""
        return [s for s in self.symbol_data["symbols"] if s["category"] == category]

    def encode(self, code: str, context: EncodingContext) -> Tuple[str, Dict[str, Any]]:
        """
        Codifica il codice in neuroglifi.

        Args:
            code: Codice sorgente da codificare
            context: Contesto di codifica

        Returns:
            Tupla (codice_codificato, metadati)
        """
        logger.info(f"Inizio codifica per linguaggio: {context.language}")

        # Analisi semantica del codice
        semantic_tree = self._analyze_semantics(code, context)

        # Identificazione pattern comuni
        patterns = self._identify_patterns(semantic_tree)

        # Mappatura simbolica
        encoded_code = self._map_to_symbols(semantic_tree, patterns, context)

        # Compressione finale
        compressed_code = self._apply_compression(encoded_code, context)

        # Calcolo metriche
        metadata = self._calculate_metrics(code, compressed_code, context)

        self.encoding_stats["total_encodings"] += 1

        return compressed_code, metadata

    def decode(self, encoded_code: str, metadata: Dict[str, Any]) -> str:
        """
        Decodifica i neuroglifi in codice sorgente.

        Args:
            encoded_code: Codice codificato in neuroglifi
            metadata: Metadati di decodifica

        Returns:
            Codice sorgente originale
        """
        logger.info("Inizio decodifica neuroglifi")

        # Decompressione
        decompressed = self._decompress(encoded_code, metadata)

        # Mappatura inversa simboli -> codice
        decoded_tree = self._reverse_symbol_mapping(decompressed, metadata)

        # Ricostruzione codice sorgente
        source_code = self._reconstruct_source(decoded_tree, metadata)

        return source_code

    def _analyze_semantics(self, code: str, context: EncodingContext) -> Dict[str, Any]:
        """Analizza la semantica del codice per identificare strutture e pattern."""
        if context.language.lower() == "python":
            return self._analyze_python_semantics(code)
        else:
            return self._analyze_generic_semantics(code)

    def _analyze_python_semantics(self, code: str) -> Dict[str, Any]:
        """Analisi semantica specifica per Python usando AST."""
        try:
            tree = ast.parse(code)
            return {
                "ast": tree,
                "functions": [node for node in ast.walk(tree) if isinstance(node, ast.FunctionDef)],
                "classes": [node for node in ast.walk(tree) if isinstance(node, ast.ClassDef)],
                "imports": [node for node in ast.walk(tree) if isinstance(node, (ast.Import, ast.ImportFrom))],
                "control_flow": [node for node in ast.walk(tree) if isinstance(node, (ast.If, ast.For, ast.While))]
            }
        except SyntaxError as e:
            logger.warning(f"Errore parsing Python: {e}")
            return self._analyze_generic_semantics(code)

    def _analyze_generic_semantics(self, code: str) -> Dict[str, Any]:
        """Analisi semantica generica basata su regex e pattern."""
        return {
            "functions": re.findall(r'function\s+\w+|def\s+\w+', code),
            "classes": re.findall(r'class\s+\w+', code),
            "control_flow": re.findall(r'\b(if|for|while|switch)\b', code),
            "variables": re.findall(r'\b[a-zA-Z_]\w*\s*=', code)
        }

    def _identify_patterns(self, semantic_tree: Dict[str, Any]) -> List[Dict[str, Any]]:
        """Identifica pattern comuni nel codice per ottimizzare la compressione."""
        patterns = []

        # Pattern di controllo del flusso
        if semantic_tree.get("control_flow"):
            patterns.append({
                "type": "control_flow_chain",
                "frequency": len(semantic_tree["control_flow"]),
                "compression_potential": 0.3
            })

        # Pattern di definizione funzioni/classi
        if semantic_tree.get("functions") or semantic_tree.get("classes"):
            patterns.append({
                "type": "definition_block",
                "frequency": len(semantic_tree.get("functions", [])) + len(semantic_tree.get("classes", [])),
                "compression_potential": 0.4
            })

        return patterns

    def _map_to_symbols(self, semantic_tree: Dict[str, Any], patterns: List[Dict[str, Any]],
                       context: EncodingContext) -> str:
        """Mappa le strutture semantiche ai simboli neuroglifi ULTRA."""
        symbol_mapping = []

        # Mappatura funzioni usando simboli ULTRA
        for func in semantic_tree.get("functions", []):
            func_symbol = self.get_symbol_by_name("function")
            if func_symbol:
                symbol_mapping.append(func_symbol["symbol"])
                self._update_symbol_usage(func_symbol["id"])

        # Mappatura classi
        for cls in semantic_tree.get("classes", []):
            class_symbol = self.get_symbol_by_name("class")
            if class_symbol:
                symbol_mapping.append(class_symbol["symbol"])
                self._update_symbol_usage(class_symbol["id"])

        # Mappatura controllo flusso
        for cf in semantic_tree.get("control_flow", []):
            if "if" in str(cf).lower():
                if_symbol = self.get_symbol_by_name("if")
                if if_symbol:
                    symbol_mapping.append(if_symbol["symbol"])
                    self._update_symbol_usage(if_symbol["id"])
            elif "for" in str(cf).lower():
                for_symbol = self.get_symbol_by_name("for_loop")
                if for_symbol:
                    symbol_mapping.append(for_symbol["symbol"])
                    self._update_symbol_usage(for_symbol["id"])
            elif "while" in str(cf).lower():
                while_symbol = self.get_symbol_by_name("while_loop")
                if while_symbol:
                    symbol_mapping.append(while_symbol["symbol"])
                    self._update_symbol_usage(while_symbol["id"])

        return "".join(symbol_mapping)

    def _update_symbol_usage(self, symbol_id: str) -> None:
        """Aggiorna le statistiche di utilizzo dei simboli."""
        if symbol_id not in self.encoding_stats["symbol_usage"]:
            self.encoding_stats["symbol_usage"][symbol_id] = 0
        self.encoding_stats["symbol_usage"][symbol_id] += 1

    def _apply_compression(self, encoded_code: str, context: EncodingContext) -> str:
        """Applica algoritmi di compressione avanzata."""
        if context.encoding_level >= 2:
            # Compressione pattern ripetuti
            encoded_code = self._compress_repeated_patterns(encoded_code)

        if context.encoding_level >= 3:
            # Compressione semantica ultra
            encoded_code = self._ultra_semantic_compression(encoded_code)

        return encoded_code

    def _compress_repeated_patterns(self, code: str) -> str:
        """Comprime pattern ripetuti usando sostituzioni intelligenti."""
        # Implementazione semplificata - da espandere
        return code.replace("⟨⟩⟨⟩", "⟨⟩²").replace("◊◊", "◊²")

    def _ultra_semantic_compression(self, code: str) -> str:
        """Compressione semantica ultra per massima efficienza."""
        # Implementazione avanzata - da sviluppare
        return code

    def _calculate_metrics(self, original: str, encoded: str, context: EncodingContext) -> Dict[str, Any]:
        """Calcola metriche di compressione e qualità."""
        original_size = len(original.encode('utf-8'))
        encoded_size = len(encoded.encode('utf-8'))
        compression_ratio = 1 - (encoded_size / original_size) if original_size > 0 else 0

        context.compression_ratio = compression_ratio

        return {
            "original_size": original_size,
            "encoded_size": encoded_size,
            "compression_ratio": compression_ratio,
            "language": context.language,
            "encoding_level": context.encoding_level,
            "symbols_used": len(set(encoded)),
            "timestamp": "2024-01-01T00:00:00Z"
        }

    def _decompress(self, encoded_code: str, metadata: Dict[str, Any]) -> str:
        """Decomprime il codice codificato."""
        # Inversione delle compressioni applicate
        decompressed = encoded_code.replace("⟨⟩²", "⟨⟩⟨⟩").replace("◊²", "◊◊")
        return decompressed

    def _reverse_symbol_mapping(self, decompressed: str, metadata: Dict[str, Any]) -> Dict[str, Any]:
        """Applica la mappatura inversa dai simboli ULTRA alle strutture semantiche."""
        reconstructed_elements = []

        # Analizza ogni carattere come potenziale simbolo ULTRA
        for char in decompressed:
            symbol_entry = self.get_symbol_by_unicode(char)
            if symbol_entry:
                reconstructed_elements.append({
                    "symbol": char,
                    "name": symbol_entry["name"],
                    "category": symbol_entry["category"],
                    "description": symbol_entry["description"]
                })
            else:
                # Carattere non riconosciuto
                reconstructed_elements.append({
                    "symbol": char,
                    "name": "unknown",
                    "category": "unknown",
                    "description": f"Unknown symbol: {char}"
                })

        return {"reconstructed_elements": reconstructed_elements}

    def _reconstruct_source(self, decoded_tree: Dict[str, Any], metadata: Dict[str, Any]) -> str:
        """Ricostruisce il codice sorgente dalle strutture decodificate ULTRA."""
        elements = decoded_tree.get("reconstructed_elements", [])
        reconstructed = ""
        language = metadata.get("language", "python")

        for element in elements:
            symbol_name = element.get("name", "unknown")
            category = element.get("category", "unknown")

            # Ricostruzione basata su nome e categoria del simbolo
            if symbol_name == "function":
                if language == "python":
                    reconstructed += "def function():\n    pass\n"
                elif language == "javascript":
                    reconstructed += "function() {\n    // implementation\n}\n"
            elif symbol_name == "class":
                if language == "python":
                    reconstructed += "class Class:\n    pass\n"
                elif language == "javascript":
                    reconstructed += "class Class {\n    // implementation\n}\n"
            elif symbol_name == "if":
                if language == "python":
                    reconstructed += "if condition:\n    pass\n"
                elif language == "javascript":
                    reconstructed += "if (condition) {\n    // implementation\n}\n"
            elif symbol_name == "for_loop":
                if language == "python":
                    reconstructed += "for item in items:\n    pass\n"
                elif language == "javascript":
                    reconstructed += "for (let item of items) {\n    // implementation\n}\n"
            elif symbol_name == "while_loop":
                if language == "python":
                    reconstructed += "while condition:\n    pass\n"
                elif language == "javascript":
                    reconstructed += "while (condition) {\n    // implementation\n}\n"
            elif category == "action":
                reconstructed += f"// Action: {symbol_name}\n"
            elif category == "logic":
                reconstructed += f"// Logic: {symbol_name}\n"
            else:
                reconstructed += f"// {symbol_name}\n"

        return reconstructed

    def get_stats(self) -> Dict[str, Any]:
        """Ritorna statistiche di utilizzo dell'encoder."""
        return self.encoding_stats.copy()


# Funzioni di utilità
def encode_file(file_path: str, output_path: str, language: str = "python",
                encoding_level: int = 1) -> Dict[str, Any]:
    """Codifica un file sorgente in neuroglifi."""
    encoder = NeuroGlyphEncoder()

    with open(file_path, 'r', encoding='utf-8') as f:
        code = f.read()

    context = EncodingContext(
        language=language,
        file_path=file_path,
        encoding_level=encoding_level
    )

    encoded_code, metadata = encoder.encode(code, context)

    with open(output_path, 'w', encoding='utf-8') as f:
        f.write(encoded_code)

    return metadata


def decode_file(encoded_path: str, metadata_path: str, output_path: str) -> None:
    """Decodifica un file neuroglifi in codice sorgente."""
    encoder = NeuroGlyphEncoder()

    with open(encoded_path, 'r', encoding='utf-8') as f:
        encoded_code = f.read()

    with open(metadata_path, 'r', encoding='utf-8') as f:
        metadata = json.load(f)

    decoded_code = encoder.decode(encoded_code, metadata)

    with open(output_path, 'w', encoding='utf-8') as f:
        f.write(decoded_code)


if __name__ == "__main__":
    # Test di base
    encoder = NeuroGlyphEncoder()

    test_code = """
def hello_world():
    print("Hello, World!")

class TestClass:
    def __init__(self):
        self.value = 42

    def get_value(self):
        return self.value

if __name__ == "__main__":
    test = TestClass()
    hello_world()
    """

    context = EncodingContext(language="python", encoding_level=2)
    encoded, metadata = encoder.encode(test_code, context)

    print(f"Codice originale ({len(test_code)} caratteri):")
    print(test_code)
    print(f"\nCodice codificato ({len(encoded)} caratteri):")
    print(encoded)
    print(f"\nCompressione: {metadata['compression_ratio']:.2%}")

    # Test decodifica
    decoded = encoder.decode(encoded, metadata)
    print(f"\nCodice decodificato:")
    print(decoded)
