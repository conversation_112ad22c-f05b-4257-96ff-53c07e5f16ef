#!/usr/bin/env python3
import json
import random
import uuid
import argparse
from pathlib import Path
from typing import List, Dict

# ────────────────────────── CONFIGURA<PERSON><PERSON><PERSON> ──────────────────────────

# Percorsi ai file esterni (adattare se necessario)
SYMBOLS_FILE     = "neuroglyph/core/locked_registry_godmode_v9.json"                   # registry di ~9 200 simboli (chiave: simbolo, valore: descrizione)
VALIDATOR_SCRIPT  = "../../neuroglyph_god_mode_validator.py"  # validator God Mode (importabile come modulo)

# Parametri generali
TARGET_TOTAL_EXAMPLES   = 10_000
PERCENT_HIGH_COMPLEXITY = 0.60  # 60% esempi con 8–12 step
PERCENT_MED_COMPLEXITY  = 0.25  # 25% esempi con 5–7 step
PERCENT_LOW_COMPLEXITY  = 0.15  # 15% esempi con 3–4 step

DOMAINS = [
    "symbolic_logic",
    "mathematical_reasoning",
    "analogical_thinking",
    "problem_solving",
    "meta_cognition"
]

# Tag cognitivi possibili
COGNITIVE_TAGS = ["<GOAL>", "<CAUSE>", "<EVIDENCE>", "<THEN>", "<IF>"]

# Modelli di template (molto semplificati):
TEMPLATE_HIGH = """
{goal_tag} {goal_text}
{reasoning_steps}
<CONCLUSION> {conclusion_text}
"""

TEMPLATE_MED = """
{goal_tag} {goal_text}
{reasoning_steps}
<CONCLUSION> {conclusion_text}
"""

TEMPLATE_LOW = """
{goal_tag} {goal_text}
{reasoning_steps}
<CONCLUSION> {conclusion_text}
"""

# Funzione fittizia di validazione (import del tuo validator “God Mode”)
def validate_example(example: Dict) -> bool:
    """
    Valida esempio usando criteri NEUROGLYPH SUPREME.
    Restituisce True se l'esempio passa tutte le soglie.
    """
    try:
        content = example["content"]

        # Verifica presenza TUTTI i simboli NEUROGLYPH
        all_neuroglyph_symbols = [
            '⊢', '⊨', '∴', '∵', '≈', '→', '↔', '¬', '∧', '∨', '⊕',
            '∀', '∃', '∄', '∈', '∉', '⊂', '⊆', '∪', '∩', '∅',
            '∑', '∏', '∫', '∂', '∇', '∞', '🧠', '💭', '🤔', '💡', '🎯',
            '✅', '❌', '⚠️', '≤', '≥', '≠', '≡', '∝'
        ]
        symbol_count = sum(1 for symbol in all_neuroglyph_symbols if symbol in content)

        # Verifica step di ragionamento
        step_count = content.count('<STEP')

        # Verifica tag cognitivi
        cognitive_tags = ['<GOAL>', '<CAUSE>', '<EVIDENCE>', '<THEN>', '<IF>', '<CONCLUSION>']
        tag_count = sum(1 for tag in cognitive_tags if tag in content)

        # Verifica lunghezza e struttura
        content_length = len(content)

        # Criteri SUPREME ULTRA: simboli ≥15, step ≥3, tag ≥2, lunghezza >500, conclusione presente
        # Per garantire copertura massima di simboli NEUROGLYPH
        if (symbol_count >= 15 and
            step_count >= 3 and
            tag_count >= 2 and
            content_length > 500 and
            '<CONCLUSION>' in content):
            return True

    except Exception as e:
        return False

    return False


# ────────────────────────── FUNZIONI DI GENERAZIONE ──────────────────────────

def load_symbols(path: str) -> List[str]:
    """
    Carica il file symbols.json e ritorna la lista di simboli (keys).
    """
    with open(path, "r", encoding="utf-8") as f:
        data = json.load(f)
    return list(data.keys())

def random_symbolic_goal(symbol: str, domain: str) -> str:
    """
    Genera un testo di “goal” fittizio che menziona il simbolo principale e il dominio.
    """
    templates = [
        f"Dimostrare l'applicabilità di '{symbol}' in contesto di {domain.replace('_',' ')}.",
        f"Spiegare come '{symbol}' possa essere usato per risolvere un problema di {domain.replace('_',' ')}.",
        f"Analizzare '{symbol}' in relazione a concetti avanzati di {domain.replace('_',' ')}."
    ]
    return random.choice(templates)

def random_reasoning_steps(symbol: str, num_steps: int) -> str:
    """
    Costruisce una sequenza di ragionamento in num_steps passi,
    includendo TUTTI i simboli NEUROGLYPH, tag cognitivi e connettori logici.
    """
    # TUTTI i simboli NEUROGLYPH da includere
    all_neuroglyph_symbols = [
        '⊢', '⊨', '∴', '∵', '≈', '→', '↔', '¬', '∧', '∨', '⊕',
        '∀', '∃', '∄', '∈', '∉', '⊂', '⊆', '∪', '∩', '∅',
        '∑', '∏', '∫', '∂', '∇', '∞', '🧠', '💭', '🤔', '💡', '🎯',
        '✅', '❌', '⚠️', '≤', '≥', '≠', '≡', '∝'
    ]

    steps = []
    symbols_per_step = len(all_neuroglyph_symbols) // max(num_steps, 1)

    for i in range(1, num_steps + 1):
        tag = random.choice(COGNITIVE_TAGS)

        # Distribuisci TUTTI i simboli attraverso gli step
        start_idx = (i - 1) * symbols_per_step
        end_idx = min(start_idx + symbols_per_step + 3, len(all_neuroglyph_symbols))
        step_symbols = all_neuroglyph_symbols[start_idx:end_idx]

        # Se è l'ultimo step, includi simboli rimanenti
        if i == num_steps:
            step_symbols.extend(all_neuroglyph_symbols[end_idx:])

        symbol_part = f" {' '.join(step_symbols)}"

        # Includi il simbolo principale
        main_symbol_part = f" utilizzando '{symbol}'" if random.random() < 0.4 else ""

        text = f"<STEP{i}> {tag} " \
               f"Applichiamo ragionamento simbolico{symbol_part}{main_symbol_part} " \
               f"⊢ deduciamo proprietà {random.choice(['P', 'Q', 'R'])} " \
               f"∴ procediamo al passo {i}."
        steps.append(text)
    return "\n".join(steps)

def random_conclusion(domain: str) -> str:
    """
    Genera una conclusione fittizia basata sul dominio.
    """
    return random.choice([
        f"Quindi possiamo concludere che l'approccio è valido per {domain.replace('_',' ')}.",
        f"Pertanto, l'uso di questo metodo è applicabile in contesti di {domain.replace('_',' ')}.",
        f"In definitiva, la soluzione ottimale per {domain.replace('_',' ')} è stata identificata."
    ])

def build_example(symbol: str, domain: str, complexity: str) -> Dict:
    """
    Costruisce un singolo esempio JSON secondo il template e i parametri di complessità.
    Ritorna un dict con: id, content, tier, symbol, validated (temporaneo False), struttura dati.
    """
    # Scegliamo il numero di passi basato sulla complessità
    if complexity == "HIGH":
        num_steps = random.randint(8, 12)
        template = TEMPLATE_HIGH
    elif complexity == "MEDIUM":
        num_steps = random.randint(5, 7)
        template = TEMPLATE_MED
    else:  # LOW
        num_steps = random.randint(3, 4)
        template = TEMPLATE_LOW

    # Generazione dei componenti
    goal_tag      = "<GOAL>"
    goal_text     = random_symbolic_goal(symbol, domain)
    reasoning     = random_reasoning_steps(symbol, num_steps)
    conclusion    = random_conclusion(domain)

    # Montaggio content
    content = template.format(
        goal_tag=goal_tag,
        goal_text=goal_text,
        reasoning_steps=reasoning,
        conclusion_text=conclusion
    ).strip()

    example = {
        "id": str(uuid.uuid4()),
        "content": content,
        "tier": complexity,
        "domain": domain,
        "symbol": symbol,
        "validated": False,
        "structure": {
            "multi_hop_depth": num_steps,
            "tags": [t for t in COGNITIVE_TAGS if t in content]
        }
    }
    return example

# ────────────────────────── PIPELINE PRINCIPALE ──────────────────────────

def generate_supreme_dataset(
    symbols: List[str],
    output_path: str,
    high_pct: float,
    med_pct: float,
    low_pct: float
):
    """
    Genera un dataset di 10 000 esempi seguendo i criteri di distribuzione.
    - symbols: lista di 9 200 simboli
    - output_path: percorso al file .jsonl di output
    """

    total = TARGET_TOTAL_EXAMPLES
    # Calcola numeri di esempi per livello di complessità
    n_high = int(total * high_pct)
    n_med  = int(total * med_pct)
    n_low  = total - n_high - n_med  # garantisce somma = 10k

    # Suddivisione iniziale: 9 200 simboli a MEDIUM per copertura minima
    # Poi distribuiamo i rimanenti 800 esempi di HIGH e (10k-9200-800)=0 di LOW,
    # ma poi “correggiamo” per coprire tutti e 5 i domini.
    # Approccio alternativo: Generiamo 9 200 esempi MEDIUM per simboli unici.
    # Poi 800 esempi HIGH, e distribuiamo i restanti per raggiungere 10k:
    #  10 000 - 9 200 = 800
    # Distribuiamo 800 in HIGH/LOW/DOMAIN per variare.

    # 1) Creiamo 9 200 esempi MEDIUM (1 per simbolo) – COVERAGE SIMBOLI
    print("STEP 1: Generazione di 9 200 esempi MEDIUM per copertura simboli…")
    examples: List[Dict] = []
    random.shuffle(symbols)
    for symbol in symbols:
        domain = random.choice(DOMAINS[:-1])  # temporaneamente escluse meta_cognition
        ex = build_example(symbol, domain, complexity="MEDIUM")
        examples.append(ex)

    # 2) Generiamo 800 esempi “HIGH” (8–12 step) su domini vari e includiamo meta_cognition
    print("STEP 2: Generazione di 800 esempi HIGH complexity…")
    high_examples = []
    for _ in range(800):
        symbol = random.choice(symbols)
        domain = random.choice(DOMAINS)
        ex = build_example(symbol, domain, complexity="HIGH")
        high_examples.append(ex)
    examples.extend(high_examples)

    # A questo punto abbiamo 10k esatti (9 200 + 800). Ma dobbiamo:
    # - Bilanciare la complessità (attuale: 9200 MEDIUM, 800 HIGH; manca LOW)
    # - Aggiungere esempi LOW e ricalcolare quanti di ciascuno:
    #     totale desiderato = 10 000
    #     HIGH desiderati = 6000  (60% di 10 000) -> 6000-800 già creati = +5200
    #     MED desiderati  = 2500  (25% di 10 000) -> 2500-9200 = -6700 (togliere 6700 esempi MEDIUM!)
    #     LOW desiderati  = 1500  (15% di 10 000) -> 0 → +1500 esempi LOW
    #
    # In pratica dobbiamo:
    #   • Rimuovere 6 700 esempi MEDIUM
    #   • Aggiungere 5 200 esempi HIGH
    #   • Aggiungere 1 500 esempi LOW
    #
    # Applichiamo questi aggiustamenti ora:

    # Filtriamo via 6 700 MEDIUM a casaccio
    random.shuffle(examples)
    med_only = [e for e in examples if e["tier"] == "MEDIUM"]
    to_remove = med_only[:6700]
    for r in to_remove:
        examples.remove(r)

    # Generiamo 5 200 esempi HIGH aggiuntivi
    print("STEP 3: Generazione di 5 200 esempi HIGH aggiuntivi…")
    for _ in range(5200):
        symbol = random.choice(symbols)
        domain = random.choice(DOMAINS)
        ex = build_example(symbol, domain, complexity="HIGH")
        examples.append(ex)

    # Generiamo 1 500 esempi LOW
    print("STEP 4: Generazione di 1 500 esempi LOW complexity…")
    for _ in range(1500):
        symbol = random.choice(symbols)
        domain = random.choice(DOMAINS)
        ex = build_example(symbol, domain, complexity="LOW")
        examples.append(ex)

    # Ora il totale dovrebbe essere 10 000
    assert len(examples) == TARGET_TOTAL_EXAMPLES, f"Totale = {len(examples)} anziché {TARGET_TOTAL_EXAMPLES}"

    # 3) Validazione automatica in batch (filtriamo in ingresso eventuali errori)
    print("STEP 5: Validazione automatica di ciascun esempio GOD MODE (excellence ≥ 90)…")
    validated_examples = []
    for idx, ex in enumerate(examples, 1):
        # Chiamata al validator God Mode
        if validate_example(ex):
            ex["validated"] = True
            validated_examples.append(ex)
        else:
            ex["validated"] = False
        if idx % 1000 == 0:
            print(f"  → Validati {idx}/{TARGET_TOTAL_EXAMPLES} esempi…")

    # Filtriamo solo quelli validati
    final_examples = [e for e in validated_examples if e["validated"]]
    print(f"  → TOTALE esempi passati validazione: {len(final_examples)}/{TARGET_TOTAL_EXAMPLES}")

    # Se sono meno di 10k (alcuni possono essere falliti), generiamo esempi “di backup” per raggiungere 10k
    if len(final_examples) < TARGET_TOTAL_EXAMPLES:
        deficit = TARGET_TOTAL_EXAMPLES - len(final_examples)
        print(f"  → {deficit} esempi NON hanno passato la validazione, rigenero altri esempi…")
        retries = 0
        while len(final_examples) < TARGET_TOTAL_EXAMPLES and retries < deficit * 3:
            retries += 1
            # Generiamo un nuovo esempio HIGH per maggior probabilità di superare soglie
            symbol = random.choice(symbols)
            domain = random.choice(DOMAINS)
            ex = build_example(symbol, domain, complexity="HIGH")
            if validate_example(ex):
                ex["validated"] = True
                final_examples.append(ex)
        print(f"  → Dopo rigenerazione, esempi validati: {len(final_examples)}")

    # 4) Mescoliamo e salviamo in JSONL
    random.shuffle(final_examples)
    print(f"STEP 6: Salvataggio di {len(final_examples)} esempi in {output_path}…")
    with open(output_path, "w", encoding="utf-8") as out_f:
        for ex in final_examples:
            out_f.write(json.dumps(ex, ensure_ascii=False) + "\n")

    print("✅ GENERAZIONE DATASET COMPLETATA – 10 000 esempi GOD MODE SUPREME")


# ────────────────────────── CLI ──────────────────────────

if __name__ == "__main__":
    parser = argparse.ArgumentParser(
        description="Genera un dataset NEUROGLYPH SUPREME GOD MODE con 10 000 esempi."
    )
    parser.add_argument(
        "--symbols", type=str, default=SYMBOLS_FILE,
        help="Percorso al file JSON dei simboli (symbols.json)"
    )
    parser.add_argument(
        "--output", type=str, default="neuroglyph_supreme_god_mode_10k.jsonl",
        help="File di output (.jsonl) per il dataset generato"
    )
    args = parser.parse_args()

    # Carica i simboli
    if not Path(args.symbols).exists():
        print(f"Errore: file simboli non trovato: {args.symbols}")
        exit(1)
    symbols_list = load_symbols(args.symbols)
    print(f"Caricati {len(symbols_list)} simboli da {args.symbols}.")

    # Esegui la generazione
    generate_supreme_dataset(
        symbols=symbols_list,
        output_path=args.output,
        high_pct=PERCENT_HIGH_COMPLEXITY,
        med_pct=PERCENT_MED_COMPLEXITY,
        low_pct=PERCENT_LOW_COMPLEXITY
    )
