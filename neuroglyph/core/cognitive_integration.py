#!/usr/bin/env python3
"""
NEUROGLYPH LLM - COGNITIVE INTEGRATION LAYER
============================================

Layer di integrazione che collega tutti i componenti cognitivi di NEUROGLYPH
per creare un sistema di pensiero unificato e coerente.

Componenti integrati:
- CognitiveState: Gestione stato mentale
- ThinkingEngine: Motore di pensiero
- SOCRATE: Ragionamento simbolico
- DAGMemory: Memoria persistente
- Encoder/Decoder: Trasformazione simbolica

Autore: NEUROGLYPH ULTRA Team
Data: 2025-05-26
Licenza: MIT
"""

import sys
import os
import json
import time
import logging
from datetime import datetime
from typing import Dict, List, Set, Any, Optional, Tuple, Union
from dataclasses import dataclass, field
from enum import Enum
from pathlib import Path

# Aggiungi path per import moduli NEUROGLYPH
current_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.append(current_dir)

# Import componenti NEUROGLYPH
from cognitive_state import (
    CognitiveState, CognitiveDomainType, ThoughtType, ThoughtTrace,
    CognitiveIntensity, create_cognitive_state
)
from thinking_engine import (
    ThinkingEngine, ThinkingMode, ThoughtProcess, create_thinking_engine
)
from llm_connector import NeuroglyphLLMConnector, LLMBackend

# Setup logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Import componenti esistenti
try:
    from encoder import NeuroGlyphEncoder, EncodingContext
    NeuroglyphEncoder = NeuroGlyphEncoder
except ImportError:
    logger.warning("⚠️ NeuroGlyphEncoder non trovato - encoding limitato")
    NeuroglyphEncoder = None

try:
    from enhanced_symbol_loader import EnhancedSymbolLoader, LoadingStrategy
    SymbolLoader = EnhancedSymbolLoader
except ImportError:
    try:
        from load_symbols import UltraSymbolLoader
        SymbolLoader = UltraSymbolLoader
        LoadingStrategy = None
    except ImportError:
        logger.warning("⚠️ Symbol Loader non trovato - caricamento simboli limitato")
        SymbolLoader = None
        LoadingStrategy = None

try:
    from dag_memory import DAGMemory
except ImportError:
    logger.warning("⚠️ DAG Memory non trovato - memoria disabilitata")
    DAGMemory = None

class IntegrationMode(Enum):
    """Modi di integrazione cognitiva."""
    BASIC = "basic"              # Integrazione base
    ENHANCED = "enhanced"        # Integrazione avanzata
    ULTRA = "ultra"             # Modalità ULTRA con tutte le funzionalità
    GOD_MODE = "god_mode"       # Modalità GOD con capacità massime

class CognitiveResponse(Enum):
    """Tipi di risposta cognitiva."""
    SYMBOLIC = "symbolic"        # Risposta in simboli neuroglifi
    NATURAL = "natural"          # Risposta in linguaggio naturale
    CODE = "code"               # Risposta in codice
    MIXED = "mixed"             # Risposta mista

@dataclass
class CognitiveRequest:
    """Richiesta al sistema cognitivo."""
    input_text: str
    input_symbols: Optional[List[str]] = None
    thinking_mode: ThinkingMode = ThinkingMode.ANALYTICAL
    response_type: CognitiveResponse = CognitiveResponse.NATURAL
    context: Dict[str, Any] = field(default_factory=dict)
    intensity: CognitiveIntensity = CognitiveIntensity.MEDIUM
    require_validation: bool = True
    max_thinking_steps: int = 10

@dataclass
class CognitiveResult:
    """Risultato del processo cognitivo."""
    request_id: str
    input_symbols: List[str]
    thought_process: ThoughtProcess
    output_symbols: List[str]
    output_text: str
    confidence: float
    processing_time: float
    success: bool
    validation_passed: bool
    active_domains: List[CognitiveDomainType]
    reasoning_trace: List[str]
    metadata: Dict[str, Any] = field(default_factory=dict)
    error_message: Optional[str] = None

class CognitiveIntegration:
    """
    Layer di integrazione cognitiva principale.

    Coordina tutti i componenti per fornire un'interfaccia unificata
    al sistema di pensiero simbolico di NEUROGLYPH.
    """

    def __init__(self,
                 mode: IntegrationMode = IntegrationMode.ENHANCED,
                 registry_path: str = "neuroglyph/core/locked_registry_godmode_v9.json"):
        """
        Inizializza il layer di integrazione cognitiva.

        Args:
            mode: Modalità di integrazione
            registry_path: Percorso al registry simbolico
        """
        self.mode = mode
        self.registry_path = registry_path

        # Componenti principali
        self.cognitive_state = create_cognitive_state(registry_path)
        self.thinking_engine = ThinkingEngine(self.cognitive_state, registry_path)

        # Componenti opzionali
        self.encoder = NeuroglyphEncoder() if NeuroglyphEncoder else None
        self.dag_memory = DAGMemory() if DAGMemory else None
        # Usa Ollama (Qwen2.5-Coder ottimizzato per Mac M2)
        try:
            self.llm_connector = NeuroglyphLLMConnector(LLMBackend.OLLAMA)
            logger.info("� LLM Connector: Ollama + Qwen2.5-Coder attivato")
        except Exception as e:
            logger.warning(f"⚠️ Fallback a MockLLM: {e}")
            self.llm_connector = NeuroglyphLLMConnector(LLMBackend.MOCK)

        # Enhanced Symbol Loader con strategia cognitiva
        if SymbolLoader and LoadingStrategy:
            self.symbol_loader = SymbolLoader(registry_path, LoadingStrategy.COGNITIVE)
            # Carica simboli immediatamente
            try:
                loading_metrics = self.symbol_loader.load_symbols()
                logger.info(f"📊 Simboli caricati: {loading_metrics.loaded_symbols}/{loading_metrics.total_symbols}")
                logger.info(f"🧠 Copertura cognitiva: {loading_metrics.cognitive_coverage:.1%}")
            except Exception as e:
                logger.warning(f"⚠️ Errore caricamento simboli: {e}")
                self.symbol_loader = None
        else:
            self.symbol_loader = SymbolLoader() if SymbolLoader else None

        # Configurazione per modalità
        self._configure_for_mode(mode)

        # Stato interno
        self.request_history: List[CognitiveResult] = []
        self.active_request: Optional[CognitiveRequest] = None
        self.performance_metrics = {
            'total_requests': 0,
            'successful_requests': 0,
            'average_confidence': 0.0,
            'average_processing_time': 0.0,
            'mode_usage': {mode.value: 0 for mode in ThinkingMode},
            'domain_activation_frequency': {},
            'validation_success_rate': 0.0
        }

        logger.info(f"🧠 NEUROGLYPH Cognitive Integration inizializzato")
        logger.info(f"🎯 Modalità: {mode.value}")
        logger.info(f"🔧 Componenti attivi: {self._get_active_components()}")

    def _configure_for_mode(self, mode: IntegrationMode):
        """Configura il sistema per la modalità specificata."""
        if mode == IntegrationMode.BASIC:
            self.thinking_engine.confidence_threshold = 0.5
            self.thinking_engine.max_reasoning_steps = 5
            self.cognitive_state.current_intensity = CognitiveIntensity.LOW

        elif mode == IntegrationMode.ENHANCED:
            self.thinking_engine.confidence_threshold = 0.7
            self.thinking_engine.max_reasoning_steps = 8
            self.cognitive_state.current_intensity = CognitiveIntensity.MEDIUM

        elif mode == IntegrationMode.ULTRA:
            self.thinking_engine.confidence_threshold = 0.8
            self.thinking_engine.max_reasoning_steps = 12
            self.cognitive_state.current_intensity = CognitiveIntensity.ULTRA
            self.thinking_engine.boost_creativity(0.3)

        elif mode == IntegrationMode.GOD_MODE:
            self.thinking_engine.confidence_threshold = 0.9
            self.thinking_engine.max_reasoning_steps = 15
            self.cognitive_state.current_intensity = CognitiveIntensity.GOD_MODE
            self.thinking_engine.boost_creativity(0.5)
            self.cognitive_state.consciousness_level = 1.0
            self.cognitive_state.self_awareness = 1.0

    def _get_active_components(self) -> List[str]:
        """Restituisce lista dei componenti attivi."""
        components = ['cognitive_state', 'thinking_engine']

        if self.encoder:
            components.append('encoder')
        if self.dag_memory:
            components.append('dag_memory')
        if self.symbol_loader:
            components.append('symbol_loader')

        return components

    def process_cognitive_request(self, request: CognitiveRequest) -> CognitiveResult:
        """
        Processa una richiesta cognitiva completa.

        Args:
            request: Richiesta cognitiva da processare

        Returns:
            Risultato del processo cognitivo
        """
        start_time = time.time()
        request_id = f"req_{int(start_time)}_{len(self.request_history)}"

        logger.info(f"🧠 Processando richiesta cognitiva: {request_id}")
        logger.info(f"📝 Input: {request.input_text[:100]}...")
        logger.info(f"🎯 Modalità: {request.thinking_mode.value}")

        self.active_request = request

        try:
            # Fase 1: Preparazione input simbolico
            input_symbols = self._prepare_input_symbols(request)

            # Fase 2: Configurazione intensità cognitiva
            self._set_cognitive_intensity(request.intensity)

            # Fase 3: Processo di pensiero
            thought_process = self.thinking_engine.think(
                input_symbols=input_symbols,
                mode=request.thinking_mode,
                context=request.context
            )

            # Fase 4: Validazione (se richiesta)
            validation_passed = True
            if request.require_validation:
                validation_passed = self._validate_thought_process(thought_process)

            # Fase 5: Generazione output
            output_text = self._generate_output_text(
                thought_process,
                request.response_type
            )

            # Fase 6: Creazione risultato
            processing_time = time.time() - start_time

            result = CognitiveResult(
                request_id=request_id,
                input_symbols=input_symbols,
                thought_process=thought_process,
                output_symbols=thought_process.output_symbols,
                output_text=output_text,
                confidence=thought_process.final_confidence,
                processing_time=processing_time,
                success=thought_process.success and validation_passed,
                validation_passed=validation_passed,
                active_domains=thought_process.active_domains,
                reasoning_trace=[
                    f"Step {s.step_id}: {s.step_type} ({s.confidence:.2f})"
                    for s in thought_process.reasoning_steps
                ],
                metadata={
                    'mode': self.mode.value,
                    'thinking_mode': request.thinking_mode.value,
                    'response_type': request.response_type.value,
                    'intensity': request.intensity.value,
                    'components_used': self._get_active_components()
                }
            )

            # Fase 7: Post-processing
            self._post_process_result(result)

            logger.info(f"✅ Richiesta completata: {result.success}, confidenza {result.confidence:.2f}")

        except Exception as e:
            logger.error(f"❌ Errore processamento richiesta: {e}")

            processing_time = time.time() - start_time
            result = CognitiveResult(
                request_id=request_id,
                input_symbols=request.input_symbols or [],
                thought_process=None,
                output_symbols=[],
                output_text=f"Errore: {str(e)}",
                confidence=0.0,
                processing_time=processing_time,
                success=False,
                validation_passed=False,
                active_domains=[],
                reasoning_trace=[],
                error_message=str(e)
            )

        finally:
            self.active_request = None
            self.request_history.append(result)
            self._update_performance_metrics(result)

        return result

    def _prepare_input_symbols(self, request: CognitiveRequest) -> List[str]:
        """Prepara simboli di input dalla richiesta usando encoder avanzato."""
        if request.input_symbols:
            return request.input_symbols.copy()

        # Se disponibile, usa encoder NEUROGLYPH per conversione intelligente
        if self.encoder and request.input_text:
            try:
                # Preprocessa input NL per encoder
                preprocessed_input = self._preprocess_natural_language(request.input_text)

                # Crea contesto di encoding appropriato
                context = self._create_encoding_context(request)

                # Encoding completo con NEUROGLYPH
                encoded_code, metadata = self.encoder.encode(preprocessed_input, context)

                # Estrae simboli dall'encoding
                symbols = self._extract_symbols_from_encoded(encoded_code, metadata)

                logger.debug(f"🧠 Encoding: {len(request.input_text)} chars → {len(symbols)} simboli")
                logger.debug(f"📊 Compressione: {metadata.get('compression_ratio', 0):.2%}")

                return symbols[:15]  # Limita per performance cognitiva

            except Exception as e:
                logger.warning(f"⚠️ Errore encoding NEUROGLYPH: {e}")

        # Usa LLM Connector per convertire NL → simboli
        if request.input_text and self.llm_connector:
            try:
                symbols = self.llm_connector.natural_language_to_symbols(request.input_text)
                if symbols:
                    logger.debug(f"🔗 LLM Connector: '{request.input_text[:30]}...' → {symbols}")
                    return symbols[:10]  # Limita per performance
            except Exception as e:
                logger.warning(f"⚠️ Errore LLM Connector: {e}")

        # Fallback: usa simboli di default basati sul testo
        return self._extract_default_symbols(request.input_text)

    def _create_encoding_context(self, request: CognitiveRequest):
        """Crea contesto di encoding basato sulla richiesta cognitiva."""
        # Determina linguaggio dal testo
        language = self._detect_language(request.input_text)

        # Determina livello encoding dalla modalità
        encoding_level = 1  # Base
        if request.thinking_mode in [ThinkingMode.CREATIVE, ThinkingMode.PROBLEM_SOLVING]:
            encoding_level = 2  # Avanzato
        elif request.thinking_mode == ThinkingMode.REFLECTIVE:
            encoding_level = 3  # Ultra

        # Crea contesto con import dinamico per evitare errori
        try:
            from encoder import EncodingContext
            return EncodingContext(
                language=language,
                encoding_level=encoding_level,
                semantic_analysis=True,
                preserve_comments=True
            )
        except ImportError:
            # Fallback context semplice
            return type('EncodingContext', (), {
                'language': language,
                'encoding_level': encoding_level,
                'semantic_analysis': True,
                'preserve_comments': True
            })()

    def _detect_language(self, text: str) -> str:
        """Rileva il linguaggio del testo di input."""
        text_lower = text.lower()

        # Parole chiave Python
        python_keywords = ['def ', 'class ', 'import ', 'from ', 'if __name__', 'print(']
        if any(kw in text_lower for kw in python_keywords):
            return "python"

        # Parole chiave JavaScript
        js_keywords = ['function ', 'const ', 'let ', 'var ', '=>', 'console.log']
        if any(kw in text_lower for kw in js_keywords):
            return "javascript"

        # Parole chiave Rust
        rust_keywords = ['fn ', 'let mut', 'impl ', 'struct ', 'enum ']
        if any(kw in text_lower for kw in rust_keywords):
            return "rust"

        # Default: tratta come pseudocodice Python
        return "python"

    def _extract_symbols_from_encoded(self, encoded_code: str, metadata: Dict[str, Any]) -> List[str]:
        """Estrae simboli dall'output dell'encoder."""
        symbols = []

        # Analizza ogni carattere come potenziale simbolo
        for char in encoded_code:
            # Verifica se è un simbolo Unicode valido (non ASCII standard)
            if ord(char) > 127:  # Non ASCII
                symbols.append(char)
            # Oppure se è una sequenza speciale
            elif char in ['⊕', '⟨', '⟩', '◊', '∀', '∃', '→', '↔', '∧', '∨', '¬']:
                symbols.append(char)

        # Rimuovi duplicati mantenendo ordine
        seen = set()
        unique_symbols = []
        for symbol in symbols:
            if symbol not in seen:
                seen.add(symbol)
                unique_symbols.append(symbol)

        return unique_symbols

    def _extract_default_symbols(self, text: str) -> List[str]:
        """Estrae simboli di default dal testo usando Enhanced Symbol Loader."""
        if self.symbol_loader:
            try:
                # Usa Enhanced Symbol Loader per mappatura intelligente
                symbols = self._intelligent_symbol_mapping(text)
                if symbols:
                    return symbols[:10]  # Limita per performance
            except Exception as e:
                logger.warning(f"⚠️ Errore mappatura intelligente simboli: {e}")

        # Fallback: mapping semplificato parole chiave -> simboli
        keyword_to_symbol = {
            'function': '⊬',
            'memory': '■',
            'logic': '≺',
            'operator': '◯',
            'flow': '⤀',
            'structure': '⍍',
            'reasoning': '≺',
            'creative': '◊',
            'problem': '⟨',
            'solution': '⟩',
            'algorithm': '⊕',
            'data': '■',
            'class': '⍍',
            'method': '⊬',
            'variable': '◯',
            'loop': '⤀',
            'condition': '≺',
            'array': '■',
            'list': '■',
            'dict': '⍍'
        }

        symbols = []
        text_lower = text.lower()

        for keyword, symbol in keyword_to_symbol.items():
            if keyword in text_lower:
                symbols.append(symbol)

        # Se nessun simbolo trovato, usa simboli generici
        if not symbols:
            symbols = ['◯', '■', '≺']  # Simboli base

        return symbols[:8]  # Limita a 8 simboli

    def _intelligent_symbol_mapping(self, text: str) -> List[str]:
        """Mappatura intelligente testo → simboli usando Enhanced Symbol Loader."""
        if not self.symbol_loader:
            return []

        symbols = []
        text_lower = text.lower()

        # Analizza parole chiave e mappa a domini cognitivi
        domain_keywords = {
            'logic': ['logic', 'logical', 'reasoning', 'reason', 'if', 'then', 'else', 'condition'],
            'memory': ['memory', 'store', 'save', 'load', 'data', 'variable', 'array', 'list'],
            'structure': ['class', 'object', 'structure', 'organize', 'hierarchy', 'tree'],
            'flow': ['loop', 'iterate', 'repeat', 'while', 'for', 'sequence', 'flow'],
            'operator': ['add', 'subtract', 'multiply', 'divide', 'calculate', 'compute'],
            'reasoning': ['think', 'analyze', 'solve', 'problem', 'solution', 'algorithm'],
            'creative': ['create', 'generate', 'invent', 'design', 'innovative', 'new'],
            'meta': ['meta', 'self', 'reflect', 'monitor', 'control', 'manage']
        }

        # Trova domini rilevanti
        relevant_domains = set()
        for domain, keywords in domain_keywords.items():
            if any(keyword in text_lower for keyword in keywords):
                relevant_domains.add(domain)

        # Recupera simboli per domini rilevanti
        for domain in relevant_domains:
            try:
                domain_symbols = self.symbol_loader.get_symbols_by_domain(domain)
                # Prendi i primi 2-3 simboli per dominio
                for symbol_data in domain_symbols[:3]:
                    symbol = symbol_data.get('symbol', '')
                    if symbol and symbol not in symbols:
                        symbols.append(symbol)
            except Exception as e:
                logger.debug(f"⚠️ Errore recupero simboli dominio {domain}: {e}")

        return symbols

    def _set_cognitive_intensity(self, intensity: CognitiveIntensity):
        """Imposta l'intensità cognitiva del sistema."""
        self.cognitive_state.current_intensity = intensity

        # Adatta parametri thinking engine all'intensità
        if intensity == CognitiveIntensity.MINIMAL:
            self.thinking_engine.confidence_threshold = 0.3
        elif intensity == CognitiveIntensity.LOW:
            self.thinking_engine.confidence_threshold = 0.5
        elif intensity == CognitiveIntensity.MEDIUM:
            self.thinking_engine.confidence_threshold = 0.7
        elif intensity == CognitiveIntensity.HIGH:
            self.thinking_engine.confidence_threshold = 0.8
        elif intensity == CognitiveIntensity.ULTRA:
            self.thinking_engine.confidence_threshold = 0.85
        elif intensity == CognitiveIntensity.GOD_MODE:
            self.thinking_engine.confidence_threshold = 0.9

    def _validate_thought_process(self, thought_process: ThoughtProcess) -> bool:
        """Valida il processo di pensiero."""
        if not thought_process:
            return False

        # Validazione base
        if not thought_process.success:
            return False

        if thought_process.final_confidence < 0.3:
            return False

        if not thought_process.reasoning_steps:
            return False

        # Validazione avanzata per modalità superiori
        if self.mode in [IntegrationMode.ULTRA, IntegrationMode.GOD_MODE]:
            # Verifica diversità domini
            if len(thought_process.active_domains) < 2:
                logger.warning("⚠️ Validazione fallita: domini insufficienti")
                return False

            # Verifica profondità ragionamento
            if len(thought_process.reasoning_steps) < 3:
                logger.warning("⚠️ Validazione fallita: ragionamento superficiale")
                return False

        return True

    def _generate_output_text(self, thought_process: ThoughtProcess, response_type: CognitiveResponse) -> str:
        """Genera testo di output dal processo di pensiero usando decoder avanzato."""
        if not thought_process or not thought_process.success:
            return "Processo di pensiero fallito"

        if response_type == CognitiveResponse.SYMBOLIC:
            return " ".join(thought_process.output_symbols)

        elif response_type == CognitiveResponse.NATURAL:
            return self._decode_symbols_to_natural(thought_process.output_symbols, thought_process)

        elif response_type == CognitiveResponse.CODE:
            return self._decode_symbols_to_code(thought_process.output_symbols, thought_process)

        elif response_type == CognitiveResponse.MIXED:
            symbols_text = " ".join(thought_process.output_symbols)
            natural_text = self._decode_symbols_to_natural(thought_process.output_symbols, thought_process)
            code_text = self._decode_symbols_to_code(thought_process.output_symbols, thought_process)

            return f"""🧠 Pensiero Simbolico:
{symbols_text}

💬 Interpretazione Naturale:
{natural_text}

💻 Codice Generato:
{code_text}"""

        return "Output non disponibile"

    def _decode_symbols_to_natural(self, symbols: List[str], thought_process: ThoughtProcess) -> str:
        """Decodifica simboli in linguaggio naturale usando contesto cognitivo."""
        if not symbols:
            return "Nessun output simbolico generato"

        # Usa encoder per decodifica se disponibile
        if self.encoder:
            try:
                # Crea stringa simbolica
                symbolic_code = "".join(symbols)

                # Crea metadata di decodifica dal processo di pensiero
                decode_metadata = {
                    'language': 'python',  # Default
                    'thinking_mode': thought_process.thinking_mode.value,
                    'active_domains': [d.value for d in thought_process.active_domains],
                    'confidence': thought_process.final_confidence,
                    'reasoning_steps': len(thought_process.reasoning_steps)
                }

                # Decodifica usando NEUROGLYPH
                decoded_text = self.encoder.decode(symbolic_code, decode_metadata)

                # Arricchisci con contesto cognitivo
                enriched_text = self._enrich_with_cognitive_context(decoded_text, thought_process)

                return enriched_text

            except Exception as e:
                logger.warning(f"⚠️ Errore decodifica NEUROGLYPH: {e}")

        # Usa LLM Connector per decodifica simboli → NL
        if self.llm_connector:
            try:
                # Determina dominio dal processo di pensiero
                domain = "default"
                if thought_process.active_domains:
                    domain = thought_process.active_domains[0].value

                # Converte simboli in linguaggio naturale
                natural_text = self.llm_connector.symbols_to_natural_language(
                    symbols=symbols,
                    context=f"Pensiero {thought_process.thinking_mode.value}",
                    domain=domain
                )

                logger.debug(f"🔗 LLM Decoder: {symbols} → '{natural_text[:50]}...'")
                return natural_text

            except Exception as e:
                logger.warning(f"⚠️ Errore LLM Decoder: {e}")

        # Fallback: decodifica semplice
        return self._symbols_to_natural_language(symbols)

    def _decode_symbols_to_code(self, symbols: List[str], thought_process: ThoughtProcess) -> str:
        """Decodifica simboli in codice usando contesto cognitivo."""
        if not symbols:
            return "# Nessun codice generato"

        # Usa encoder per decodifica se disponibile
        if self.encoder:
            try:
                # Crea stringa simbolica
                symbolic_code = "".join(symbols)

                # Determina linguaggio dal contesto
                target_language = self._determine_target_language(thought_process)

                # Crea metadata di decodifica
                decode_metadata = {
                    'language': target_language,
                    'thinking_mode': thought_process.thinking_mode.value,
                    'active_domains': [d.value for d in thought_process.active_domains],
                    'confidence': thought_process.final_confidence,
                    'encoding_level': 2 if thought_process.thinking_mode == ThinkingMode.CREATIVE else 1
                }

                # Decodifica usando NEUROGLYPH
                decoded_code = self.encoder.decode(symbolic_code, decode_metadata)

                # Ottimizza codice basato su modalità pensiero
                optimized_code = self._optimize_code_by_thinking_mode(decoded_code, thought_process)

                return optimized_code

            except Exception as e:
                logger.warning(f"⚠️ Errore decodifica codice NEUROGLYPH: {e}")

        # Fallback: decodifica semplice
        return self._symbols_to_code(symbols)

    def _enrich_with_cognitive_context(self, text: str, thought_process: ThoughtProcess) -> str:
        """Arricchisce il testo con contesto cognitivo."""
        # Aggiungi informazioni sul processo di pensiero
        context_info = []

        # Modalità di pensiero
        mode_descriptions = {
            ThinkingMode.ANALYTICAL: "attraverso analisi logica step-by-step",
            ThinkingMode.CREATIVE: "attraverso pensiero creativo e divergente",
            ThinkingMode.CRITICAL: "attraverso valutazione critica",
            ThinkingMode.REFLECTIVE: "attraverso auto-riflessione metacognitiva",
            ThinkingMode.INTUITIVE: "attraverso intuizione rapida",
            ThinkingMode.PROBLEM_SOLVING: "attraverso risoluzione strutturata del problema",
            ThinkingMode.LEARNING: "attraverso apprendimento adattivo"
        }

        mode_desc = mode_descriptions.get(thought_process.thinking_mode, "attraverso ragionamento")
        context_info.append(f"Elaborato {mode_desc}")

        # Domini cognitivi attivi
        if thought_process.active_domains:
            domain_names = [d.value.replace('_', ' ') for d in thought_process.active_domains]
            context_info.append(f"Domini attivati: {', '.join(domain_names)}")

        # Confidenza
        confidence_level = "alta" if thought_process.final_confidence > 0.8 else "media" if thought_process.final_confidence > 0.5 else "bassa"
        context_info.append(f"Confidenza {confidence_level} ({thought_process.final_confidence:.2f})")

        # Combina testo originale con contesto
        if text.strip():
            return f"{text}\n\n[{' • '.join(context_info)}]"
        else:
            return f"Risultato del pensiero simbolico: {' • '.join(context_info)}"

    def _determine_target_language(self, thought_process: ThoughtProcess) -> str:
        """Determina il linguaggio target basato sul contesto cognitivo."""
        # Analizza domini attivi per determinare linguaggio appropriato
        domain_to_language = {
            'structure': 'python',
            'memory': 'c',
            'logic': 'python',
            'flow': 'python',
            'operator': 'python',
            'meta': 'python',
            'neural_architectures': 'python',
            'quantum_computing': 'python',
            'symbolic_ai': 'python'
        }

        # Trova linguaggio più appropriato basato su domini attivi
        language_votes = {}
        for domain in thought_process.active_domains:
            lang = domain_to_language.get(domain.value, 'python')
            language_votes[lang] = language_votes.get(lang, 0) + 1

        # Restituisce linguaggio con più voti, default Python
        if language_votes:
            return max(language_votes.items(), key=lambda x: x[1])[0]
        return 'python'

    def _optimize_code_by_thinking_mode(self, code: str, thought_process: ThoughtProcess) -> str:
        """Ottimizza il codice basato sulla modalità di pensiero."""
        if not code.strip():
            return self._generate_template_code(thought_process)

        # Ottimizzazioni specifiche per modalità
        if thought_process.thinking_mode == ThinkingMode.CREATIVE:
            # Aggiungi commenti creativi e struttura innovativa
            return self._add_creative_structure(code, thought_process)

        elif thought_process.thinking_mode == ThinkingMode.ANALYTICAL:
            # Aggiungi documentazione dettagliata e validazione
            return self._add_analytical_structure(code, thought_process)

        elif thought_process.thinking_mode == ThinkingMode.PROBLEM_SOLVING:
            # Struttura orientata alla soluzione con test
            return self._add_problem_solving_structure(code, thought_process)

        return code

    def _generate_template_code(self, thought_process: ThoughtProcess) -> str:
        """Genera codice template basato sul processo di pensiero."""
        language = self._determine_target_language(thought_process)
        mode = thought_process.thinking_mode.value
        domains = [d.value for d in thought_process.active_domains]

        if language == 'python':
            return f'''# Codice generato da NEUROGLYPH LLM
# Modalità: {mode}
# Domini: {", ".join(domains)}
# Confidenza: {thought_process.final_confidence:.2f}

def neuroglyph_solution():
    """
    Soluzione generata attraverso pensiero simbolico.
    Modalità: {mode}
    """
    # TODO: Implementare logica basata su simboli neuroglifi
    pass

if __name__ == "__main__":
    neuroglyph_solution()
'''

        return f"// Codice {language} generato da NEUROGLYPH LLM\n// Modalità: {mode}\n// TODO: Implementazione"

    def _add_creative_structure(self, code: str, thought_process: ThoughtProcess) -> str:
        """Aggiunge struttura creativa al codice."""
        creative_header = f'''# 🎨 NEUROGLYPH CREATIVE CODE
# Generato attraverso pensiero creativo divergente
# Confidenza: {thought_process.final_confidence:.2f}
# Domini attivi: {", ".join([d.value for d in thought_process.active_domains])}

"""
Questo codice è stato generato attraverso un processo di pensiero creativo
che ha esplorato soluzioni innovative e non convenzionali.
"""

'''

        # Aggiungi commenti creativi
        creative_footer = '''

# 🚀 Possibili estensioni creative:
# - Implementare pattern innovativi
# - Esplorare approcci non convenzionali
# - Integrare con altri domini cognitivi
'''

        return creative_header + code + creative_footer

    def _add_analytical_structure(self, code: str, thought_process: ThoughtProcess) -> str:
        """Aggiunge struttura analitica al codice."""
        analytical_header = f'''# 🔬 NEUROGLYPH ANALYTICAL CODE
# Generato attraverso analisi logica step-by-step
# Confidenza: {thought_process.final_confidence:.2f}
# Step di ragionamento: {len(thought_process.reasoning_steps)}

"""
Questo codice è stato generato attraverso un processo di analisi sistematica
che ha valutato ogni componente logico in modo rigoroso.

Processo di ragionamento:
{chr(10).join([f"  {i+1}. {step.step_type}" for i, step in enumerate(thought_process.reasoning_steps)])}
"""

import logging
from typing import Any, Optional

# Configurazione logging per tracciabilità
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

'''

        # Aggiungi validazione analitica
        analytical_footer = '''

def validate_solution() -> bool:
    """Valida la correttezza della soluzione."""
    try:
        # TODO: Implementare validazione specifica
        logger.info("✅ Validazione completata")
        return True
    except Exception as e:
        logger.error(f"❌ Validazione fallita: {e}")
        return False

if __name__ == "__main__":
    if validate_solution():
        # Esegui soluzione principale
        pass
'''

        return analytical_header + code + analytical_footer

    def _add_problem_solving_structure(self, code: str, thought_process: ThoughtProcess) -> str:
        """Aggiunge struttura orientata alla risoluzione problemi."""
        problem_solving_header = f'''# 🎯 NEUROGLYPH PROBLEM SOLVING CODE
# Generato attraverso approccio strutturato alla risoluzione
# Confidenza: {thought_process.final_confidence:.2f}
# Domini problem-solving attivi: {len([d for d in thought_process.active_domains if 'problem' in d.value.lower()])}

"""
Questo codice implementa una soluzione strutturata al problema,
seguendo un approccio metodico e verificabile.
"""

class ProblemSolver:
    """Classe per la risoluzione strutturata del problema."""

    def __init__(self):
        self.solution_steps = []
        self.validation_results = []

    def analyze_problem(self):
        """Analizza il problema da risolvere."""
        # TODO: Implementare analisi problema
        pass

    def generate_solution(self):
        """Genera la soluzione al problema."""
        # TODO: Implementare generazione soluzione
        pass

    def validate_solution(self):
        """Valida la soluzione generata."""
        # TODO: Implementare validazione
        pass

'''

        # Aggiungi test strutturati
        problem_solving_footer = '''

def test_solution():
    """Test della soluzione generata."""
    solver = ProblemSolver()

    # Test case 1: Analisi problema
    solver.analyze_problem()
    assert len(solver.solution_steps) >= 0

    # Test case 2: Generazione soluzione
    solver.generate_solution()

    # Test case 3: Validazione
    solver.validate_solution()

    print("✅ Tutti i test superati")

if __name__ == "__main__":
    test_solution()
'''

        return problem_solving_header + code + problem_solving_footer

    def _symbols_to_natural_language(self, symbols: List[str]) -> str:
        """Converte simboli in linguaggio naturale."""
        if not symbols:
            return "Nessun output simbolico generato"

        # Mapping semplificato simboli -> descrizioni
        symbol_descriptions = {
            '◯': 'operazione di sottrazione',
            '■': 'gestione memoria',
            '⫗': 'allocazione memoria',
            '≺': 'implicazione logica',
            '⌀': 'operazione OR',
            '⤀': 'allocazione memoria avanzata',
            '◟': 'liberazione memoria',
            '⊬': 'definizione funzione',
            '≯': 'moltiplicazione',
            '◺': 'operazione modulo'
        }

        descriptions = []
        for symbol in symbols:
            if symbol in symbol_descriptions:
                descriptions.append(symbol_descriptions[symbol])
            else:
                descriptions.append(f"simbolo {symbol}")

        if len(descriptions) == 1:
            return f"Il sistema ha identificato: {descriptions[0]}"
        else:
            return f"Il sistema ha elaborato: {', '.join(descriptions[:-1])} e {descriptions[-1]}"

    def _symbols_to_code(self, symbols: List[str]) -> str:
        """Converte simboli in codice."""
        if not symbols:
            return "# Nessun codice generato"

        # Mapping semplificato simboli -> codice
        symbol_to_code = {
            '◯': 'a - b',
            '■': 'memory_ptr',
            '⫗': 'malloc(size)',
            '≺': 'if a then b',
            '⌀': 'a or b',
            '⤀': 'calloc(n, size)',
            '◟': 'free(ptr)',
            '⊬': 'def function():',
            '≯': 'a * b',
            '◺': 'a % b'
        }

        code_lines = []
        for symbol in symbols:
            if symbol in symbol_to_code:
                code_lines.append(symbol_to_code[symbol])
            else:
                code_lines.append(f"# {symbol}")

        return '\n'.join(code_lines)

    def _post_process_result(self, result: CognitiveResult):
        """Post-processing del risultato."""
        # Salva in DAG Memory se disponibile e successo
        if self.dag_memory and result.success:
            try:
                self.dag_memory.add_transformation(
                    input_text=" ".join(result.input_symbols),
                    output_text=result.output_text,
                    transformation_type=f"cognitive_{result.thought_process.thinking_mode.value}",
                    confidence=result.confidence,
                    metadata=result.metadata
                )
            except Exception as e:
                logger.warning(f"⚠️ Errore salvataggio DAG Memory: {e}")

        # Aggiorna stato cognitivo con apprendimento
        if result.success and self.cognitive_state.learning_mode:
            # Rinforza domini utilizzati con successo
            for domain_type in result.active_domains:
                domain = self.cognitive_state.domains[domain_type]
                domain.success_rate = min(1.0, domain.success_rate + 0.01)

    def _update_performance_metrics(self, result: CognitiveResult):
        """Aggiorna metriche di performance."""
        self.performance_metrics['total_requests'] += 1

        if result.success:
            self.performance_metrics['successful_requests'] += 1

        # Aggiorna confidenza media
        total = self.performance_metrics['total_requests']
        self.performance_metrics['average_confidence'] = (
            (self.performance_metrics['average_confidence'] * (total - 1) + result.confidence) / total
        )

        # Aggiorna tempo medio
        self.performance_metrics['average_processing_time'] = (
            (self.performance_metrics['average_processing_time'] * (total - 1) + result.processing_time) / total
        )

        # Aggiorna uso modalità
        if result.thought_process:
            mode = result.thought_process.thinking_mode.value
            self.performance_metrics['mode_usage'][mode] += 1

        # Aggiorna frequenza attivazione domini
        for domain in result.active_domains:
            domain_name = domain.value
            if domain_name not in self.performance_metrics['domain_activation_frequency']:
                self.performance_metrics['domain_activation_frequency'][domain_name] = 0
            self.performance_metrics['domain_activation_frequency'][domain_name] += 1

        # Aggiorna tasso successo validazione
        validation_successes = sum(1 for r in self.request_history if r.validation_passed)
        self.performance_metrics['validation_success_rate'] = validation_successes / total

    def think(self, text: str, mode: ThinkingMode = ThinkingMode.ANALYTICAL) -> str:
        """
        Interfaccia semplificata per pensiero rapido.

        Args:
            text: Testo di input
            mode: Modalità di pensiero

        Returns:
            Risposta testuale
        """
        request = CognitiveRequest(
            input_text=text,
            thinking_mode=mode,
            response_type=CognitiveResponse.NATURAL
        )

        result = self.process_cognitive_request(request)
        return result.output_text

    def think_symbolic(self, symbols: List[str], mode: ThinkingMode = ThinkingMode.ANALYTICAL) -> List[str]:
        """
        Pensiero diretto con simboli.

        Args:
            symbols: Simboli di input
            mode: Modalità di pensiero

        Returns:
            Simboli di output
        """
        request = CognitiveRequest(
            input_text="",
            input_symbols=symbols,
            thinking_mode=mode,
            response_type=CognitiveResponse.SYMBOLIC
        )

        result = self.process_cognitive_request(request)
        return result.output_symbols

    def get_cognitive_status(self) -> Dict[str, Any]:
        """Restituisce stato completo del sistema cognitivo."""
        return {
            'timestamp': datetime.now().isoformat(),
            'mode': self.mode.value,
            'active_request': self.active_request is not None,
            'cognitive_state': self.cognitive_state.get_cognitive_status(),
            'thinking_engine': self.thinking_engine.get_thinking_status(),
            'performance_metrics': self.performance_metrics.copy(),
            'components': {
                'encoder_available': self.encoder is not None,
                'dag_memory_available': self.dag_memory is not None,
                'symbol_loader_available': self.symbol_loader is not None
            },
            'history': {
                'total_requests': len(self.request_history),
                'recent_success_rate': self._calculate_recent_success_rate()
            }
        }

    def _calculate_recent_success_rate(self, window: int = 10) -> float:
        """Calcola tasso di successo recente."""
        if not self.request_history:
            return 0.0

        recent = self.request_history[-window:]
        successful = sum(1 for r in recent if r.success)

        return successful / len(recent)

    def set_mode(self, mode: IntegrationMode):
        """Cambia modalità di integrazione."""
        self.mode = mode
        self._configure_for_mode(mode)
        logger.info(f"🔧 Modalità cambiata a: {mode.value}")

    def reset_system(self, preserve_learning: bool = True):
        """Reset completo del sistema cognitivo."""
        self.cognitive_state.reset_cognitive_state(preserve_learning)
        self.thinking_engine.reset_thinking_engine(preserve_learning)

        if not preserve_learning:
            self.request_history.clear()
            self.performance_metrics = {
                'total_requests': 0,
                'successful_requests': 0,
                'average_confidence': 0.0,
                'average_processing_time': 0.0,
                'mode_usage': {mode.value: 0 for mode in ThinkingMode},
                'domain_activation_frequency': {},
                'validation_success_rate': 0.0
            }

        logger.info(f"🔄 Sistema cognitivo resettato (learning preserved: {preserve_learning})")


# Funzioni di utilità per uso esterno
def create_cognitive_integration(mode: IntegrationMode = IntegrationMode.ENHANCED) -> CognitiveIntegration:
    """Crea una nuova istanza di integrazione cognitiva."""
    return CognitiveIntegration(mode)


def quick_cognitive_response(text: str, mode: ThinkingMode = ThinkingMode.ANALYTICAL) -> str:
    """Risposta cognitiva rapida."""
    integration = create_cognitive_integration()
    return integration.think(text, mode)


if __name__ == "__main__":
    # Test del sistema di integrazione cognitiva
    print("🧠 NEUROGLYPH Cognitive Integration - Test")
    print("=" * 60)

    # Crea sistema di integrazione
    integration = create_cognitive_integration(IntegrationMode.ENHANCED)

    # Test pensiero con testo
    test_text = "Crea una funzione per gestire la memoria"
    print(f"\n🧪 Test pensiero: {test_text}")

    response = integration.think(test_text, ThinkingMode.ANALYTICAL)
    print(f"✅ Risposta: {response}")

    # Test pensiero simbolico
    test_symbols = ["⊬", "■", "⫗"]
    print(f"\n🔣 Test simbolico: {test_symbols}")

    symbolic_response = integration.think_symbolic(test_symbols, ThinkingMode.CREATIVE)
    print(f"✅ Simboli output: {symbolic_response}")

    # Stato del sistema
    status = integration.get_cognitive_status()
    print(f"\n📊 Stato Sistema:")
    print(f"  • Modalità: {status['mode']}")
    print(f"  • Richieste totali: {status['performance_metrics']['total_requests']}")
    print(f"  • Tasso successo: {status['performance_metrics']['successful_requests'] / max(1, status['performance_metrics']['total_requests']):.2f}")
    print(f"  • Confidenza media: {status['performance_metrics']['average_confidence']:.2f}")

    print("\n✅ Test completato con successo!")
