#!/usr/bin/env python3
"""
NEUROGLYPH FASE 1A - REMEDIATION AUTOMATICA
Correzioni automatiche per simboli severity C (moderati)
"""

import json
import csv
import re
import sys
from pathlib import Path
from datetime import datetime
from typing import Dict, List, Any

class AutomaticRemediation:
    """Remediation automatica per simboli severity C."""
    
    def __init__(self, 
                 registry_path: str = "neuroglyph/core/symbols_registry.json",
                 audit_csv_path: str = None):
        self.registry_path = Path(registry_path)
        self.audit_csv_path = audit_csv_path
        self.registry = {}
        self.audit_data = {}
        self.remediation_timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        self.changes_log = []
        
    def load_registry(self) -> bool:
        """Carica il registry dei simboli."""
        try:
            with open(self.registry_path, 'r', encoding='utf-8') as f:
                self.registry = json.load(f)
            print(f"✅ Registry caricato: {len(self.registry.get('approved_symbols', []))} simboli")
            return True
        except Exception as e:
            print(f"❌ Errore caricamento registry: {e}")
            return False
    
    def load_audit_data(self) -> bool:
        """Carica dati audit CSV."""
        if not self.audit_csv_path:
            # Trova il file audit più recente
            audit_files = list(Path("neuroglyph/symbols").glob("surgical_audit_*.csv"))
            if not audit_files:
                print("❌ Nessun file audit trovato")
                return False
            self.audit_csv_path = max(audit_files, key=lambda x: x.stat().st_mtime)
        
        try:
            with open(self.audit_csv_path, 'r', encoding='utf-8') as f:
                reader = csv.DictReader(f)
                for row in reader:
                    self.audit_data[row['id']] = row
            print(f"✅ Audit data caricato: {len(self.audit_data)} simboli")
            return True
        except Exception as e:
            print(f"❌ Errore caricamento audit: {e}")
            return False
    
    def apply_fallback_abbreviations(self) -> int:
        """Applica abbreviazioni automatiche ai fallback lunghi."""
        changes_count = 0
        
        for symbol in self.registry.get("approved_symbols", []):
            symbol_id = symbol.get("id", "")
            audit_info = self.audit_data.get(symbol_id, {})
            
            # Solo severity C con long_fallback
            if (audit_info.get("severity") == "C" and 
                audit_info.get("long_fallback") == "True" and
                audit_info.get("suggested_fallback")):
                
                old_fallback = symbol.get("fallback", "")
                new_fallback = audit_info["suggested_fallback"]
                
                if old_fallback != new_fallback:
                    symbol["fallback"] = new_fallback
                    changes_count += 1
                    
                    self.changes_log.append({
                        "type": "fallback_abbreviation",
                        "symbol_id": symbol_id,
                        "symbol": symbol.get("symbol", ""),
                        "old_fallback": old_fallback,
                        "new_fallback": new_fallback,
                        "timestamp": datetime.now().isoformat()
                    })
        
        return changes_count
    
    def apply_semantic_renaming(self) -> int:
        """Applica rinominazione semantica ai nomi generici."""
        changes_count = 0
        
        for symbol in self.registry.get("approved_symbols", []):
            symbol_id = symbol.get("id", "")
            audit_info = self.audit_data.get(symbol_id, {})
            
            # Solo severity C con generic_name
            if (audit_info.get("severity") == "C" and 
                audit_info.get("generic_name") == "True" and
                audit_info.get("suggested_name")):
                
                old_name = symbol.get("name", "")
                new_name = audit_info["suggested_name"]
                
                if old_name != new_name and new_name != old_name:
                    symbol["name"] = new_name
                    
                    # Aggiorna anche il code se necessario
                    old_code = symbol.get("code", "")
                    if old_name in old_code:
                        new_code = old_code.replace(old_name, new_name)
                        symbol["code"] = new_code
                    
                    changes_count += 1
                    
                    self.changes_log.append({
                        "type": "semantic_renaming",
                        "symbol_id": symbol_id,
                        "symbol": symbol.get("symbol", ""),
                        "old_name": old_name,
                        "new_name": new_name,
                        "old_code": old_code,
                        "new_code": symbol.get("code", ""),
                        "timestamp": datetime.now().isoformat()
                    })
        
        return changes_count
    
    def update_validation_scores(self) -> int:
        """Aggiorna validation scores per simboli corretti."""
        changes_count = 0
        
        for symbol in self.registry.get("approved_symbols", []):
            symbol_id = symbol.get("id", "")
            audit_info = self.audit_data.get(symbol_id, {})
            
            # Solo severity C che sono stati corretti
            if audit_info.get("severity") == "C":
                old_score = symbol.get("validation_score", 100.0)
                
                # Calcola nuovo score basato su correzioni applicate
                new_score = old_score
                
                # Bonus per correzioni applicate
                if any(change["symbol_id"] == symbol_id for change in self.changes_log):
                    if old_score < 90.0:
                        new_score = min(95.0, old_score + 10.0)  # Boost significativo
                    else:
                        new_score = min(98.0, old_score + 3.0)   # Miglioramento moderato
                
                if new_score != old_score:
                    symbol["validation_score"] = new_score
                    changes_count += 1
                    
                    self.changes_log.append({
                        "type": "score_update",
                        "symbol_id": symbol_id,
                        "symbol": symbol.get("symbol", ""),
                        "old_score": old_score,
                        "new_score": new_score,
                        "timestamp": datetime.now().isoformat()
                    })
        
        return changes_count
    
    def save_registry_backup(self) -> str:
        """Salva backup del registry prima delle modifiche."""
        backup_path = f"neuroglyph/core/symbols_registry_backup_{self.remediation_timestamp}.json"
        
        with open(backup_path, 'w', encoding='utf-8') as f:
            json.dump(self.registry, f, indent=2, ensure_ascii=False)
        
        return backup_path
    
    def save_updated_registry(self) -> bool:
        """Salva registry aggiornato."""
        try:
            # Aggiorna metadati
            if "stats" not in self.registry:
                self.registry["stats"] = {}
            
            self.registry["stats"]["last_remediation"] = self.remediation_timestamp
            self.registry["stats"]["remediation_changes"] = len(self.changes_log)
            
            with open(self.registry_path, 'w', encoding='utf-8') as f:
                json.dump(self.registry, f, indent=2, ensure_ascii=False)
            
            return True
        except Exception as e:
            print(f"❌ Errore salvataggio registry: {e}")
            return False
    
    def save_changes_log(self) -> str:
        """Salva log delle modifiche."""
        log_path = f"neuroglyph/symbols/remediation_log_{self.remediation_timestamp}.json"
        
        log_data = {
            "remediation_timestamp": self.remediation_timestamp,
            "total_changes": len(self.changes_log),
            "changes_by_type": {},
            "changes": self.changes_log
        }
        
        # Conta per tipo
        for change in self.changes_log:
            change_type = change["type"]
            log_data["changes_by_type"][change_type] = log_data["changes_by_type"].get(change_type, 0) + 1
        
        with open(log_path, 'w', encoding='utf-8') as f:
            json.dump(log_data, f, indent=2, ensure_ascii=False)
        
        return log_path
    
    def run_automatic_remediation(self) -> Dict[str, Any]:
        """Esegue remediation automatica completa."""
        print("🔧 Avvio remediation automatica per severity C...")
        
        # Backup
        backup_path = self.save_registry_backup()
        print(f"💾 Backup salvato: {backup_path}")
        
        # Applica correzioni
        fallback_changes = self.apply_fallback_abbreviations()
        print(f"📝 Fallback abbreviati: {fallback_changes}")
        
        renaming_changes = self.apply_semantic_renaming()
        print(f"🏷️ Nomi rinominati: {renaming_changes}")
        
        score_changes = self.update_validation_scores()
        print(f"📊 Score aggiornati: {score_changes}")
        
        # Salva risultati
        if self.save_updated_registry():
            print("✅ Registry aggiornato salvato")
        
        log_path = self.save_changes_log()
        print(f"📋 Log modifiche salvato: {log_path}")
        
        return {
            "total_changes": len(self.changes_log),
            "fallback_changes": fallback_changes,
            "renaming_changes": renaming_changes,
            "score_changes": score_changes,
            "backup_path": backup_path,
            "log_path": log_path
        }

def main():
    """Esegue remediation automatica."""
    print("🧠 NEUROGLYPH LLM - FASE 1A: REMEDIATION AUTOMATICA")
    print("🎯 Correzioni automatiche per severity C")
    print("=" * 70)
    
    # Crea remediation engine
    remediation = AutomaticRemediation()
    
    # Carica dati
    if not remediation.load_registry():
        sys.exit(1)
    
    if not remediation.load_audit_data():
        sys.exit(1)
    
    # Esegui remediation
    results = remediation.run_automatic_remediation()
    
    print(f"\n🎉 REMEDIATION AUTOMATICA COMPLETATA!")
    print(f"📊 Modifiche totali: {results['total_changes']}")
    print(f"📝 Fallback abbreviati: {results['fallback_changes']}")
    print(f"🏷️ Nomi rinominati: {results['renaming_changes']}")
    print(f"📊 Score aggiornati: {results['score_changes']}")
    
    print(f"\n🚀 PROSSIMI PASSI:")
    print(f"  1. Review log: {results['log_path']}")
    print(f"  2. Eseguire cargo check per validazione")
    print(f"  3. Procedere con FASE 1B: Review manuale severity A/B")
    
    return True

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
