{"batch_info": {"batch_number": 3, "tier": "ultra", "theme": "data_structures", "target_size": 128, "actual_size": 60, "generated_date": "2025-05-25T12:34:02.361816"}, "symbols": [{"id": "NG0756", "symbol": "⟦", "code": "ng:collection:list", "fallback": "[list]", "description": "Lista", "category": "data_structures", "batch": 3, "tier": "ultra", "approved_date": "2025-05-25", "validation_score": 1.5000000000000002, "status": "pending_approval"}, {"id": "NG0757", "symbol": "⟧", "code": "ng:collection:list_end", "fallback": "[/list]", "description": "Fine lista", "category": "data_structures", "batch": 3, "tier": "ultra", "approved_date": "2025-05-25", "validation_score": 1.5000000000000002, "status": "pending_approval"}, {"id": "NG0758", "symbol": "⋅", "code": "ng:tree:child", "fallback": "[child]", "description": "Nodo figlio", "category": "data_structures", "batch": 3, "tier": "ultra", "approved_date": "2025-05-25", "validation_score": 1.3, "status": "pending_approval"}, {"id": "NG0759", "symbol": "⋖", "code": "ng:tree:bst", "fallback": "[bst]", "description": "Binary search tree", "category": "data_structures", "batch": 3, "tier": "ultra", "approved_date": "2025-05-25", "validation_score": 1.3, "status": "pending_approval"}, {"id": "NG0760", "symbol": "⋗", "code": "ng:tree:avl", "fallback": "[avl]", "description": "AVL tree", "category": "data_structures", "batch": 3, "tier": "ultra", "approved_date": "2025-05-25", "validation_score": 1.3, "status": "pending_approval"}, {"id": "NG0761", "symbol": "⌀", "code": "ng:algo:quick_sort", "fallback": "[quick]", "description": "Quick sort", "category": "data_structures", "batch": 3, "tier": "ultra", "approved_date": "2025-05-25", "validation_score": 1.3, "status": "pending_approval"}, {"id": "NG0762", "symbol": "⌁", "code": "ng:algo:merge_sort", "fallback": "[merge]", "description": "Merge sort", "category": "data_structures", "batch": 3, "tier": "ultra", "approved_date": "2025-05-25", "validation_score": 1.3, "status": "pending_approval"}, {"id": "NG0763", "symbol": "⌅", "code": "ng:algo:radix_sort", "fallback": "[radix]", "description": "<PERSON><PERSON><PERSON> sort", "category": "data_structures", "batch": 3, "tier": "ultra", "approved_date": "2025-05-25", "validation_score": 1.3, "status": "pending_approval"}, {"id": "NG0764", "symbol": "⌌", "code": "ng:algo:jump_search", "fallback": "[jump]", "description": "Jump search", "category": "data_structures", "batch": 3, "tier": "ultra", "approved_date": "2025-05-25", "validation_score": 1.3, "status": "pending_approval"}, {"id": "NG0765", "symbol": "⦈", "code": "ng:collection:map_end", "fallback": "[/map]", "description": "Fine mappa", "category": "data_structures", "batch": 3, "tier": "ultra", "approved_date": "2025-05-25", "validation_score": 1.2000000000000002, "status": "pending_approval"}, {"id": "NG0766", "symbol": "⦉", "code": "ng:collection:dict", "fallback": "[dict]", "description": "Dizionario", "category": "data_structures", "batch": 3, "tier": "ultra", "approved_date": "2025-05-25", "validation_score": 1.2000000000000002, "status": "pending_approval"}, {"id": "NG0767", "symbol": "⦊", "code": "ng:collection:dict_end", "fallback": "[/dict]", "description": "Fine dizionario", "category": "data_structures", "batch": 3, "tier": "ultra", "approved_date": "2025-05-25", "validation_score": 1.2000000000000002, "status": "pending_approval"}, {"id": "NG0768", "symbol": "⦋", "code": "ng:collection:queue", "fallback": "[queue]", "description": "Coda", "category": "data_structures", "batch": 3, "tier": "ultra", "approved_date": "2025-05-25", "validation_score": 1.2000000000000002, "status": "pending_approval"}, {"id": "NG0769", "symbol": "⦌", "code": "ng:collection:queue_end", "fallback": "[/queue]", "description": "Fine coda", "category": "data_structures", "batch": 3, "tier": "ultra", "approved_date": "2025-05-25", "validation_score": 1.2000000000000002, "status": "pending_approval"}, {"id": "NG0770", "symbol": "⌂", "code": "ng:algo:heap_sort", "fallback": "[heap_sort]", "description": "Heap sort", "category": "data_structures", "batch": 3, "tier": "ultra", "approved_date": "2025-05-25", "validation_score": 1.2, "status": "pending_approval"}, {"id": "NG0771", "symbol": "⌃", "code": "ng:algo:insertion_sort", "fallback": "[insertion]", "description": "Insertion sort", "category": "data_structures", "batch": 3, "tier": "ultra", "approved_date": "2025-05-25", "validation_score": 1.2, "status": "pending_approval"}, {"id": "NG0772", "symbol": "⌄", "code": "ng:algo:selection_sort", "fallback": "[selection]", "description": "Selection sort", "category": "data_structures", "batch": 3, "tier": "ultra", "approved_date": "2025-05-25", "validation_score": 1.2, "status": "pending_approval"}, {"id": "NG0773", "symbol": "⊩", "code": "ng:collection:map_op", "fallback": "[map]", "description": "Mappa operazione", "category": "data_structures", "batch": 3, "tier": "ultra", "approved_date": "2025-05-25", "validation_score": 1.1, "status": "pending_approval"}, {"id": "NG0774", "symbol": "⊫", "code": "ng:collection:fold", "fallback": "[fold]", "description": "Fold collezione", "category": "data_structures", "batch": 3, "tier": "ultra", "approved_date": "2025-05-25", "validation_score": 1.1, "status": "pending_approval"}, {"id": "NG0775", "symbol": "⊭", "code": "ng:collection:full", "fallback": "[full]", "description": "Collezione piena", "category": "data_structures", "batch": 3, "tier": "ultra", "approved_date": "2025-05-25", "validation_score": 1.1, "status": "pending_approval"}, {"id": "NG0776", "symbol": "⋌", "code": "ng:tree:search", "fallback": "[search]", "description": "Cerca in albero", "category": "data_structures", "batch": 3, "tier": "ultra", "approved_date": "2025-05-25", "validation_score": 1.1, "status": "pending_approval"}, {"id": "NG0777", "symbol": "⋍", "code": "ng:tree:find", "fallback": "[find]", "description": "<PERSON><PERSON><PERSON> nodo", "category": "data_structures", "batch": 3, "tier": "ultra", "approved_date": "2025-05-25", "validation_score": 1.1, "status": "pending_approval"}, {"id": "NG0778", "symbol": "⋐", "code": "ng:tree:left_child", "fallback": "[left]", "description": "Figlio sinistro", "category": "data_structures", "batch": 3, "tier": "ultra", "approved_date": "2025-05-25", "validation_score": 1.1, "status": "pending_approval"}, {"id": "NG0779", "symbol": "⋑", "code": "ng:tree:right_child", "fallback": "[right]", "description": "<PERSON><PERSON>o destro", "category": "data_structures", "batch": 3, "tier": "ultra", "approved_date": "2025-05-25", "validation_score": 1.1, "status": "pending_approval"}, {"id": "NG0780", "symbol": "⋒", "code": "ng:tree:height", "fallback": "[height]", "description": "Altezza albero", "category": "data_structures", "batch": 3, "tier": "ultra", "approved_date": "2025-05-25", "validation_score": 1.1, "status": "pending_approval"}, {"id": "NG0781", "symbol": "⋚", "code": "ng:tree:heap", "fallback": "[heap]", "description": "<PERSON><PERSON>", "category": "data_structures", "batch": 3, "tier": "ultra", "approved_date": "2025-05-25", "validation_score": 1.1, "status": "pending_approval"}, {"id": "NG0782", "symbol": "⋠", "code": "ng:graph:edge", "fallback": "[edge]", "description": "Arco", "category": "data_structures", "batch": 3, "tier": "ultra", "approved_date": "2025-05-25", "validation_score": 1.1, "status": "pending_approval"}, {"id": "NG0783", "symbol": "⋡", "code": "ng:graph:weight", "fallback": "[weight]", "description": "Peso arco", "category": "data_structures", "batch": 3, "tier": "ultra", "approved_date": "2025-05-25", "validation_score": 1.1, "status": "pending_approval"}, {"id": "NG0784", "symbol": "⋤", "code": "ng:graph:weighted", "fallback": "[weighted]", "description": "Grafo pesato", "category": "data_structures", "batch": 3, "tier": "ultra", "approved_date": "2025-05-25", "validation_score": 1.1, "status": "pending_approval"}, {"id": "NG0785", "symbol": "⋪", "code": "ng:graph:prim", "fallback": "[prim]", "description": "Prim MST", "category": "data_structures", "batch": 3, "tier": "ultra", "approved_date": "2025-05-25", "validation_score": 1.1, "status": "pending_approval"}, {"id": "NG0786", "symbol": "⋭", "code": "ng:graph:cycle_detection", "fallback": "[cycle]", "description": "Rile<PERSON><PERSON> cicli", "category": "data_structures", "batch": 3, "tier": "ultra", "approved_date": "2025-05-25", "validation_score": 1.1, "status": "pending_approval"}, {"id": "NG0787", "symbol": "⋶", "code": "ng:graph:degree", "fallback": "[degree]", "description": "Grado vertice", "category": "data_structures", "batch": 3, "tier": "ultra", "approved_date": "2025-05-25", "validation_score": 1.1, "status": "pending_approval"}, {"id": "NG0788", "symbol": "⋻", "code": "ng:graph:clique", "fallback": "[clique]", "description": "Clique", "category": "data_structures", "batch": 3, "tier": "ultra", "approved_date": "2025-05-25", "validation_score": 1.1, "status": "pending_approval"}, {"id": "NG0789", "symbol": "⋽", "code": "ng:graph:matching", "fallback": "[matching]", "description": "Matching", "category": "data_structures", "batch": 3, "tier": "ultra", "approved_date": "2025-05-25", "validation_score": 1.1, "status": "pending_approval"}, {"id": "NG0790", "symbol": "⌆", "code": "ng:algo:linear_search", "fallback": "[linear]", "description": "Ricerca lineare", "category": "data_structures", "batch": 3, "tier": "ultra", "approved_date": "2025-05-25", "validation_score": 1.1, "status": "pending_approval"}, {"id": "NG0791", "symbol": "⌇", "code": "ng:algo:binary_search", "fallback": "[binary]", "description": "Ricerca binaria", "category": "data_structures", "batch": 3, "tier": "ultra", "approved_date": "2025-05-25", "validation_score": 1.1, "status": "pending_approval"}, {"id": "NG0792", "symbol": "⌋", "code": "ng:algo:ternary_search", "fallback": "[ternary]", "description": "Ricerca ternaria", "category": "data_structures", "batch": 3, "tier": "ultra", "approved_date": "2025-05-25", "validation_score": 1.1, "status": "pending_approval"}, {"id": "NG0793", "symbol": "⌍", "code": "ng:algo:hash_search", "fallback": "[hash]", "description": "Ricerca hash", "category": "data_structures", "batch": 3, "tier": "ultra", "approved_date": "2025-05-25", "validation_score": 1.1, "status": "pending_approval"}, {"id": "NG0794", "symbol": "⌎", "code": "ng:algo:dp", "fallback": "[dp]", "description": "Programmazione dinamica", "category": "data_structures", "batch": 3, "tier": "ultra", "approved_date": "2025-05-25", "validation_score": 1.1, "status": "pending_approval"}, {"id": "NG0795", "symbol": "⌐", "code": "ng:algo:tabulation", "fallback": "[tab]", "description": "Tabulazione", "category": "data_structures", "batch": 3, "tier": "ultra", "approved_date": "2025-05-25", "validation_score": 1.1, "status": "pending_approval"}, {"id": "NG0796", "symbol": "⌑", "code": "ng:algo:optimal_substructure", "fallback": "[optimal]", "description": "Sottostruttura ottimale", "category": "data_structures", "batch": 3, "tier": "ultra", "approved_date": "2025-05-25", "validation_score": 1.1, "status": "pending_approval"}, {"id": "NG0797", "symbol": "⌒", "code": "ng:algo:overlapping_subproblems", "fallback": "[overlap]", "description": "Sottoproblemi sovrapposti", "category": "data_structures", "batch": 3, "tier": "ultra", "approved_date": "2025-05-25", "validation_score": 1.1, "status": "pending_approval"}, {"id": "NG0798", "symbol": "⌓", "code": "ng:algo:knapsack", "fallback": "[knapsack]", "description": "<PERSON><PERSON> zaino", "category": "data_structures", "batch": 3, "tier": "ultra", "approved_date": "2025-05-25", "validation_score": 1.1, "status": "pending_approval"}, {"id": "NG0799", "symbol": "⌕", "code": "ng:algo:edit_distance", "fallback": "[edit]", "description": "Distanza di edit", "category": "data_structures", "batch": 3, "tier": "ultra", "approved_date": "2025-05-25", "validation_score": 1.1, "status": "pending_approval"}, {"id": "NG0800", "symbol": "⌖", "code": "ng:algo:greedy", "fallback": "[greedy]", "description": "Algoritmo greedy", "category": "data_structures", "batch": 3, "tier": "ultra", "approved_date": "2025-05-25", "validation_score": 1.1, "status": "pending_approval"}, {"id": "NG0801", "symbol": "⌗", "code": "ng:algo:activity_selection", "fallback": "[activity]", "description": "Selezione attività", "category": "data_structures", "batch": 3, "tier": "ultra", "approved_date": "2025-05-25", "validation_score": 1.1, "status": "pending_approval"}, {"id": "NG0802", "symbol": "⌙", "code": "ng:algo:huffman", "fallback": "[huffman]", "description": "Codifica Huffman", "category": "data_structures", "batch": 3, "tier": "ultra", "approved_date": "2025-05-25", "validation_score": 1.1, "status": "pending_approval"}, {"id": "NG0803", "symbol": "⌚", "code": "ng:algo:job_scheduling", "fallback": "[job]", "description": "Scheduling lavori", "category": "data_structures", "batch": 3, "tier": "ultra", "approved_date": "2025-05-25", "validation_score": 1.1, "status": "pending_approval"}, {"id": "NG0804", "symbol": "⌜", "code": "ng:algo:minimum_spanning_tree", "fallback": "[mst]", "description": "Albero ricoprente minimo", "category": "data_structures", "batch": 3, "tier": "ultra", "approved_date": "2025-05-25", "validation_score": 1.1, "status": "pending_approval"}, {"id": "NG0805", "symbol": "⌝", "code": "ng:algo:single_source_shortest_path", "fallback": "[sssp]", "description": "Percorso minimo sorgente singola", "category": "data_structures", "batch": 3, "tier": "ultra", "approved_date": "2025-05-25", "validation_score": 1.1, "status": "pending_approval"}, {"id": "NG0806", "symbol": "⋕", "code": "ng:tree:rotate_right", "fallback": "[rot_right]", "description": "Rotazione destra", "category": "data_structures", "batch": 3, "tier": "ultra", "approved_date": "2025-05-25", "validation_score": 1.0, "status": "pending_approval"}, {"id": "NG0807", "symbol": "⋣", "code": "ng:graph:undirected", "fallback": "[undirected]", "description": "<PERSON><PERSON> non diretto", "category": "data_structures", "batch": 3, "tier": "ultra", "approved_date": "2025-05-25", "validation_score": 1.0, "status": "pending_approval"}, {"id": "NG0808", "symbol": "⋥", "code": "ng:graph:unweighted", "fallback": "[unweighted]", "description": "Grafo non pesato", "category": "data_structures", "batch": 3, "tier": "ultra", "approved_date": "2025-05-25", "validation_score": 1.0, "status": "pending_approval"}, {"id": "NG0809", "symbol": "⋷", "code": "ng:graph:in_degree", "fallback": "[in_degree]", "description": "<PERSON><PERSON> entrante", "category": "data_structures", "batch": 3, "tier": "ultra", "approved_date": "2025-05-25", "validation_score": 1.0, "status": "pending_approval"}, {"id": "NG0810", "symbol": "⋸", "code": "ng:graph:out_degree", "fallback": "[out_degree]", "description": "Grado uscente", "category": "data_structures", "batch": 3, "tier": "ultra", "approved_date": "2025-05-25", "validation_score": 1.0, "status": "pending_approval"}, {"id": "NG0811", "symbol": "⋼", "code": "ng:graph:independent_set", "fallback": "[independent]", "description": "Set indipendente", "category": "data_structures", "batch": 3, "tier": "ultra", "approved_date": "2025-05-25", "validation_score": 1.0, "status": "pending_approval"}, {"id": "NG0812", "symbol": "⌈", "code": "ng:algo:interpolation_search", "fallback": "[interpolation]", "description": "Ricerca interpolazione", "category": "data_structures", "batch": 3, "tier": "ultra", "approved_date": "2025-05-25", "validation_score": 1.0, "status": "pending_approval"}, {"id": "NG0813", "symbol": "⌉", "code": "ng:algo:exponential_search", "fallback": "[exponential]", "description": "Ricerca esponenziale", "category": "data_structures", "batch": 3, "tier": "ultra", "approved_date": "2025-05-25", "validation_score": 1.0, "status": "pending_approval"}, {"id": "NG0814", "symbol": "⌊", "code": "ng:algo:<PERSON><PERSON><PERSON><PERSON>_search", "fallback": "[<PERSON><PERSON><PERSON><PERSON>]", "description": "Ricerca Fibonacci", "category": "data_structures", "batch": 3, "tier": "ultra", "approved_date": "2025-05-25", "validation_score": 1.0, "status": "pending_approval"}, {"id": "NG0815", "symbol": "⌘", "code": "ng:algo:fractional_knapsack", "fallback": "[frac_knapsack]", "description": "Zaino frazionario", "category": "data_structures", "batch": 3, "tier": "ultra", "approved_date": "2025-05-25", "validation_score": 1.0, "status": "pending_approval"}], "summary": {"collection_patterns": 10, "tree_patterns": 11, "graph_patterns": 13, "algorithm_patterns": 27}}