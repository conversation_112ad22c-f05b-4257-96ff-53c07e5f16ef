#!/usr/bin/env python3
"""
NEUROGLYPH FASE 0 - AUDIT CHIRURGICO
Approccio revisionato: remediation mirata invece di pulizia drastica
"""

import json
import csv
import re
import sys
from pathlib import Path
from datetime import datetime
from typing import Dict, List, Any, Set

class SurgicalAuditor:
    """Auditor chirurgico per remediation mirata del registry NEUROGLYPH."""

    def __init__(self, registry_path: str = "neuroglyph/core/symbols_registry.json"):
        self.registry_path = Path(registry_path)
        self.registry = {}
        self.audit_timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")

        # Unicode whitelist per simboli grafici utili
        self.unicode_whitelist = {
            "⚙": "gear/settings",
            "⚡": "lightning/energy",
            "⚠": "warning",
            "⚖": "balance/justice",
            "⚗": "chemistry/transform",
            "⚛": "atom/science",
            "⚜": "fleur-de-lis/royal",
            "⚝": "outlined_star",
            "⚞": "angle_bracket",
            "⚟": "angle_bracket_end",
            "⚡": "lightning_bolt",
            "⚢": "female_symbol",
            "⚣": "male_symbol",
            "⚤": "transgender",
            "⚥": "hermaphrodite",
            "⚦": "male_with_stroke",
            "⚧": "transgender_symbol",
            "⚨": "vertical_male",
            "⚩": "horizontal_male",
            "⚪": "medium_white_circle",
            "⚫": "medium_black_circle",
            "⚬": "medium_small_white_circle",
            "⚭": "marriage_symbol",
            "⚮": "divorce_symbol",
            "⚯": "unmarried_partnership",
            "⚰": "coffin",
            "⚱": "funeral_urn",
            "⚲": "neuter",
            "⚳": "ceres",
            "⚴": "pallas",
            "⚵": "juno",
            "⚶": "vesta",
            "⚷": "chiron",
            "⚸": "lilith",
            "⚹": "sextile",
            "⚺": "semisextile",
            "⚻": "quincunx",
            "⚼": "sesquiquadrate"
        }

        # Abbreviazioni standard per fallback
        self.fallback_abbreviations = {
            "CORRELATION": "CORR",
            "SPECIALIZATION": "SPEC",
            "PROBLEMSOLVING": "SOLVE",
            "CONTRAPOSITIVE": "CONTRA",
            "SELFAWARENESS": "AWARE",
            "MODUSPONENS": "MPONENS",
            "CONSISTENCY": "CONSIST",
            "CONTRADICTION": "CONTRA",
            "METACOGNITION": "META",
            "COMPLETENESS": "COMPLETE",
            "GENERALIZATION": "GENERAL",
            "ERRORCORRECTION": "ERRCORR",
            "CLASSIFICATION": "CLASS",
            "MODUSTOLLENS": "MTOLLENS",
            "ABSTRACTION": "ABSTRACT",
            "DECIDABILITY": "DECIDE"
        }

    def load_registry(self) -> bool:
        """Carica il registry dei simboli."""
        try:
            with open(self.registry_path, 'r', encoding='utf-8') as f:
                self.registry = json.load(f)
            print(f"✅ Registry caricato: {len(self.registry.get('approved_symbols', []))} simboli")
            return True
        except Exception as e:
            print(f"❌ Errore caricamento registry: {e}")
            return False

    def analyze_unicode_range(self, unicode_point: str) -> Dict[str, Any]:
        """Analizza il range Unicode di un simbolo."""
        if not unicode_point:
            return {"risky": False, "range": "unknown", "whitelisted": False}

        try:
            code_point = int(unicode_point.replace("U+", ""), 16)

            # Range problematici
            emoji_ranges = [
                (0x1F000, 0x1F9FF, "emoji_extended"),
                (0x2600, 0x26FF, "miscellaneous_symbols"),
                (0x1F700, 0x1F77F, "alchemical_symbols")
            ]

            for start, end, range_name in emoji_ranges:
                if start <= code_point <= end:
                    symbol = chr(code_point)
                    whitelisted = symbol in self.unicode_whitelist
                    return {
                        "risky": not whitelisted,
                        "range": range_name,
                        "whitelisted": whitelisted,
                        "whitelist_reason": self.unicode_whitelist.get(symbol, "")
                    }

            return {"risky": False, "range": "safe", "whitelisted": False}

        except Exception:
            return {"risky": True, "range": "invalid", "whitelisted": False}

    def suggest_fallback_abbreviation(self, fallback: str) -> str:
        """Suggerisce abbreviazione per fallback lungo."""
        # Rimuovi brackets
        clean = fallback.strip("[]")

        # Usa abbreviazioni predefinite
        if clean in self.fallback_abbreviations:
            return f"[{self.fallback_abbreviations[clean]}]"

        # Abbreviazioni automatiche
        if len(clean) <= 8:
            return fallback

        # Strategia 1: Acronimo
        words = clean.split("_")
        if len(words) > 1:
            acronym = "".join(word[0].upper() for word in words if word)
            if len(acronym) <= 6:
                return f"[{acronym}]"

        # Strategia 2: Troncamento intelligente
        if len(clean) > 8:
            truncated = clean[:6] + ".."
            return f"[{truncated}]"

        return fallback

    def suggest_semantic_name(self, name: str, category: str) -> str:
        """Suggerisce nome semantico per nomi generici numerati."""
        if not re.search(r'_\d+$', name):
            return name

        base_name = re.sub(r'_\d+$', '', name)

        # Mapping semantico basato su categoria
        semantic_mappings = {
            "operator": {
                "add": ["scalar", "vector", "matrix", "complex", "modular"],
                "sub": ["scalar", "vector", "matrix", "complex", "modular"],
                "mul": ["scalar", "vector", "matrix", "cross", "dot"],
                "div": ["scalar", "vector", "matrix", "integer", "modular"],
                "mod": ["integer", "polynomial", "matrix", "group", "ring"]
            },
            "memory": {
                "alloc": ["heap", "stack", "pool", "aligned", "shared"],
                "free": ["heap", "stack", "pool", "batch", "deferred"],
                "deref": ["weak", "strong", "atomic", "volatile", "const"]
            },
            "logic": {
                "and": ["bitwise", "logical", "fuzzy", "quantum", "modal"],
                "or": ["bitwise", "logical", "fuzzy", "quantum", "modal"],
                "not": ["bitwise", "logical", "fuzzy", "quantum", "modal"],
                "implies": ["material", "strict", "relevant", "modal", "quantum"]
            },
            "flow": {
                "if": ["conditional", "ternary", "switch", "guard", "pattern"],
                "for": ["range", "iterator", "parallel", "vectorized", "unrolled"],
                "while": ["condition", "infinite", "bounded", "monitored", "async"]
            }
        }

        if category in semantic_mappings and base_name in semantic_mappings[category]:
            variants = semantic_mappings[category][base_name]
            # Estrai numero dal nome originale
            match = re.search(r'_(\d+)$', name)
            if match:
                index = int(match.group(1)) - 1
                if 0 <= index < len(variants):
                    return f"{base_name}_{variants[index]}"

        return name

    def audit_symbol(self, symbol: Dict[str, Any]) -> Dict[str, Any]:
        """Audit completo di un singolo simbolo."""
        audit_result = {
            "id": symbol.get("id", ""),
            "symbol": symbol.get("symbol", ""),
            "code": symbol.get("code", ""),
            "fallback": symbol.get("fallback", ""),
            "category": symbol.get("category", ""),
            "name": symbol.get("name", ""),
            "validation_score": symbol.get("validation_score", 100.0),
            "auto_generated": symbol.get("auto_generated", False),
            "unicode_point": symbol.get("unicode_point", ""),
            "token_cost": symbol.get("token_cost", 1),

            # Flags di severity
            "low_score": False,
            "unicode_risky": False,
            "long_fallback": False,
            "generic_name": False,

            # Severity level
            "severity": "OK",

            # Suggerimenti
            "suggested_fallback": "",
            "suggested_name": "",
            "unicode_analysis": {},

            # Issues dettagliate
            "issues": []
        }

        issues = []
        severity_points = 0

        # 1. Check validation score
        score = symbol.get("validation_score", 100.0)
        if score < 90.0:
            audit_result["low_score"] = True
            issues.append(f"Low validation score: {score}")
            severity_points += 3  # Critico

        # 2. Check Unicode range
        unicode_analysis = self.analyze_unicode_range(symbol.get("unicode_point", ""))
        audit_result["unicode_analysis"] = unicode_analysis
        if unicode_analysis["risky"]:
            audit_result["unicode_risky"] = True
            issues.append(f"Risky Unicode range: {unicode_analysis['range']}")
            severity_points += 2  # Alto

        # 3. Check fallback length
        fallback = symbol.get("fallback", "")
        if len(fallback) > 10:
            audit_result["long_fallback"] = True
            issues.append(f"Long fallback: {len(fallback)} chars")
            severity_points += 1  # Moderato
            audit_result["suggested_fallback"] = self.suggest_fallback_abbreviation(fallback)

        # 4. Check generic name
        name = symbol.get("name", "")
        if re.search(r'_\d+$', name):
            audit_result["generic_name"] = True
            issues.append("Generic numbered name")
            severity_points += 1  # Moderato
            audit_result["suggested_name"] = self.suggest_semantic_name(name, symbol.get("category", ""))

        # Determina severity
        if severity_points >= 3:
            audit_result["severity"] = "A"  # Critico
        elif severity_points >= 2:
            audit_result["severity"] = "B"  # Alto
        elif severity_points >= 1:
            audit_result["severity"] = "C"  # Moderato
        else:
            audit_result["severity"] = "OK"

        audit_result["issues"] = "; ".join(issues)
        return audit_result

    def audit_all_symbols(self) -> List[Dict[str, Any]]:
        """Audit completo di tutti i simboli."""
        symbols = self.registry.get("approved_symbols", [])
        audit_results = []

        print(f"🔍 Audit chirurgico di {len(symbols)} simboli...")

        for i, symbol in enumerate(symbols):
            if i % 100 == 0:
                print(f"  Processati {i}/{len(symbols)} simboli...")

            audit_result = self.audit_symbol(symbol)
            audit_results.append(audit_result)

        return audit_results

    def generate_surgical_csv(self, audit_results: List[Dict[str, Any]]) -> str:
        """Genera CSV per audit chirurgico."""
        output_path = f"neuroglyph/symbols/surgical_audit_{self.audit_timestamp}.csv"

        fieldnames = [
            "id", "symbol", "code", "fallback", "category", "name",
            "validation_score", "severity", "low_score", "unicode_risky",
            "long_fallback", "generic_name", "suggested_fallback",
            "suggested_name", "unicode_range", "whitelisted", "issues"
        ]

        with open(output_path, 'w', newline='', encoding='utf-8') as csvfile:
            writer = csv.DictWriter(csvfile, fieldnames=fieldnames)
            writer.writeheader()

            for result in audit_results:
                row = {
                    "id": result["id"],
                    "symbol": result["symbol"],
                    "code": result["code"],
                    "fallback": result["fallback"],
                    "category": result["category"],
                    "name": result["name"],
                    "validation_score": result["validation_score"],
                    "severity": result["severity"],
                    "low_score": result["low_score"],
                    "unicode_risky": result["unicode_risky"],
                    "long_fallback": result["long_fallback"],
                    "generic_name": result["generic_name"],
                    "suggested_fallback": result["suggested_fallback"],
                    "suggested_name": result["suggested_name"],
                    "unicode_range": result["unicode_analysis"].get("range", ""),
                    "whitelisted": result["unicode_analysis"].get("whitelisted", False),
                    "issues": result["issues"]
                }
                writer.writerow(row)

        return output_path

    def generate_surgical_summary(self, audit_results: List[Dict[str, Any]]) -> Dict[str, Any]:
        """Genera summary per audit chirurgico."""
        total_symbols = len(audit_results)

        severity_counts = {"A": 0, "B": 0, "C": 0, "OK": 0}
        flag_counts = {
            "low_score": 0,
            "unicode_risky": 0,
            "long_fallback": 0,
            "generic_name": 0
        }

        unicode_ranges = {}
        whitelisted_count = 0

        for result in audit_results:
            severity_counts[result["severity"]] += 1

            for flag in flag_counts:
                if result[flag]:
                    flag_counts[flag] += 1

            unicode_range = result["unicode_analysis"].get("range", "unknown")
            unicode_ranges[unicode_range] = unicode_ranges.get(unicode_range, 0) + 1

            if result["unicode_analysis"].get("whitelisted", False):
                whitelisted_count += 1

        # Calcola metriche precise
        precise_metrics = {
            "unicode_risky_total": flag_counts["unicode_risky"],
            "unicode_whitelisted": whitelisted_count,
            "unicode_to_replace": flag_counts["unicode_risky"] - whitelisted_count,
            "fallback_to_abbreviate": flag_counts["long_fallback"],
            "names_to_rename": flag_counts["generic_name"],
            "symbols_to_remove": flag_counts["low_score"]
        }

        return {
            "audit_timestamp": self.audit_timestamp,
            "total_symbols": total_symbols,
            "severity_distribution": severity_counts,
            "flag_counts": flag_counts,
            "unicode_ranges": unicode_ranges,
            "precise_metrics": precise_metrics,
            "remediation_plan": {
                "critical_A": severity_counts["A"],
                "high_B": severity_counts["B"],
                "moderate_C": severity_counts["C"],
                "healthy_OK": severity_counts["OK"]
            }
        }

    def print_surgical_summary(self, summary: Dict[str, Any]) -> None:
        """Stampa summary audit chirurgico."""
        print(f"\n🔬 NEUROGLYPH AUDIT CHIRURGICO - FASE 0")
        print(f"=" * 60)
        print(f"📊 Simboli totali: {summary['total_symbols']}")

        print(f"\n🎯 DISTRIBUZIONE SEVERITY:")
        severity = summary['severity_distribution']
        print(f"  🔴 A (Critico): {severity['A']} simboli")
        print(f"  🟠 B (Alto): {severity['B']} simboli")
        print(f"  🟡 C (Moderato): {severity['C']} simboli")
        print(f"  ✅ OK (Sano): {severity['OK']} simboli")

        print(f"\n📈 METRICHE PRECISE:")
        metrics = summary['precise_metrics']
        print(f"  • Score < 90: {metrics['symbols_to_remove']} simboli da rimuovere/rigenerare")
        print(f"  • Unicode rischiosi: {metrics['unicode_risky_total']} totali")
        print(f"    - Whitelisted: {metrics['unicode_whitelisted']} (da mantenere)")
        print(f"    - Da sostituire: {metrics['unicode_to_replace']} simboli")
        print(f"  • Fallback lunghi: {metrics['fallback_to_abbreviate']} da abbreviare")
        print(f"  • Nomi generici: {metrics['names_to_rename']} da rinominare")

        print(f"\n🏥 PIANO REMEDIATION:")
        plan = summary['remediation_plan']
        print(f"  🔴 Priorità 1: {plan['critical_A']} simboli critici")
        print(f"  🟠 Priorità 2: {plan['high_B']} simboli ad alto rischio")
        print(f"  🟡 Priorità 3: {plan['moderate_C']} simboli moderati")
        print(f"  ✅ Mantenere: {plan['healthy_OK']} simboli sani")

def main():
    """Esegue audit chirurgico completo."""
    print("🧠 NEUROGLYPH LLM - FASE 0: AUDIT CHIRURGICO")
    print("🎯 Approccio revisionato: remediation mirata")
    print("=" * 70)

    # Crea auditor
    auditor = SurgicalAuditor()

    # Carica registry
    if not auditor.load_registry():
        sys.exit(1)

    # Audit completo
    audit_results = auditor.audit_all_symbols()

    # Genera CSV
    csv_path = auditor.generate_surgical_csv(audit_results)
    print(f"📄 CSV audit generato: {csv_path}")

    # Genera e stampa summary
    summary = auditor.generate_surgical_summary(audit_results)
    auditor.print_surgical_summary(summary)

    # Salva summary JSON
    summary_path = f"neuroglyph/symbols/surgical_summary_{auditor.audit_timestamp}.json"
    with open(summary_path, 'w', encoding='utf-8') as f:
        json.dump(summary, f, indent=2, ensure_ascii=False)
    print(f"📊 Summary JSON salvato: {summary_path}")

    print(f"\n🚀 PROSSIMI PASSI CHIRURGICI:")
    print(f"  1. Aprire {csv_path} per review dettagliata")
    print(f"  2. Implementare remediation automatica per severity C")
    print(f"  3. Review manuale per severity A e B")
    print(f"  4. Procedere con FASE 1: Remediation mirata")

    return True

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
