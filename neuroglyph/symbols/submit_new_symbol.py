#!/usr/bin/env python3
"""
NEUROGLYPH ULTRA - Symbol Submission Tool
==========================================

Tool per proporre nuovi simboli con validazione automatica e merge condizionale.
Solo simboli con score >= 90% vengono approvati automaticamente.

Usage: python submit_new_symbol.py --symbol "∀" --code "ng:logic:forall" --fallback "[FORALL]" --category "logic" --name "forall" --description "Universal quantifier"
"""

import json
import sys
import argparse
from datetime import datetime
from typing import Dict, Any, List
import os

# Import del validatore (assumendo che sia nello stesso progetto)
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

class SymbolSubmissionSystem:
    """Sistema di submission per nuovi simboli NEUROGLYPH ULTRA."""
    
    def __init__(self):
        self.symbols_file = "core/symbols.json"
        self.registry_file = "core/symbols_registry.json"
        self.pending_file = "core/symbols_pending.json"
        
    def submit_symbol(self, symbol_data: Dict[str, Any]) -> Dict[str, Any]:
        """Sottomette un nuovo simbolo per validazione."""
        
        # Importa e usa il validatore
        try:
            from tools.validate_symbol import SymbolValidator
            validator = SymbolValidator(self.symbols_file)
        except ImportError:
            return {
                "success": False,
                "error": "Impossibile importare SymbolValidator"
            }
        
        # Valida il simbolo
        validation_result = validator.validate_symbol(
            symbol_data["symbol"],
            symbol_data["code"], 
            symbol_data["fallback"],
            symbol_data.get("category", ""),
            symbol_data.get("name", ""),
            symbol_data.get("description", "")
        )
        
        # Prepara risultato submission
        submission_result = {
            "symbol": symbol_data["symbol"],
            "code": symbol_data["code"],
            "submission_date": datetime.now().isoformat(),
            "validation_score": validation_result["score"],
            "validation_result": validation_result,
            "success": False,
            "status": "rejected",
            "message": ""
        }
        
        # Decisione automatica basata su score
        if validation_result["score"] >= 90.0 and validation_result["valid"]:
            # Auto-approvazione
            success = self._approve_symbol(symbol_data, validation_result)
            if success:
                submission_result.update({
                    "success": True,
                    "status": "approved",
                    "message": f"Simbolo approvato automaticamente (score: {validation_result['score']:.1f}%)"
                })
            else:
                submission_result.update({
                    "status": "error",
                    "message": "Errore durante l'approvazione automatica"
                })
        
        elif validation_result["score"] >= 75.0:
            # Pending review
            self._add_to_pending(symbol_data, validation_result)
            submission_result.update({
                "status": "pending",
                "message": f"Simbolo in attesa di review manuale (score: {validation_result['score']:.1f}%)"
            })
        
        else:
            # Rigetto automatico
            self._add_to_rejected(symbol_data, validation_result)
            submission_result.update({
                "status": "rejected", 
                "message": f"Simbolo rigettato automaticamente (score: {validation_result['score']:.1f}%)"
            })
        
        return submission_result
    
    def _approve_symbol(self, symbol_data: Dict[str, Any], validation_result: Dict[str, Any]) -> bool:
        """Approva un simbolo aggiungendolo ai file principali."""
        try:
            # 1. Carica simboli esistenti
            with open(self.symbols_file, 'r', encoding='utf-8') as f:
                symbols = json.load(f)
            
            # 2. Genera nuovo ID
            next_id = self._get_next_id()
            
            # 3. Crea entry simbolo completa
            new_symbol = {
                "id": next_id,
                "symbol": symbol_data["symbol"],
                "name": symbol_data.get("name", ""),
                "description": symbol_data.get("description", ""),
                "category": symbol_data.get("category", ""),
                "aliases": symbol_data.get("aliases", []),
                "status": "approved",
                "version": "1.0",
                "code": symbol_data["code"],
                "fallback": symbol_data["fallback"],
                "unicode_point": self._get_unicode_point(symbol_data["symbol"]),
                "token_cost": validation_result["lcl_check"].get("token_cost_estimate", 1),
                "token_density": 1.0,
                "llm_support": ["openai", "qwen", "deepseek", "llama"]
            }
            
            # 4. Aggiungi ai simboli
            symbols.append(new_symbol)
            
            # 5. Salva simboli aggiornati
            with open(self.symbols_file, 'w', encoding='utf-8') as f:
                json.dump(symbols, f, indent=2, ensure_ascii=False)
            
            # 6. Aggiorna registry
            self._update_registry(new_symbol, validation_result, "approved")
            
            return True
            
        except Exception as e:
            print(f"Errore durante approvazione: {e}")
            return False
    
    def _add_to_pending(self, symbol_data: Dict[str, Any], validation_result: Dict[str, Any]) -> None:
        """Aggiunge simbolo alla lista pending."""
        try:
            # Carica pending esistenti
            try:
                with open(self.pending_file, 'r', encoding='utf-8') as f:
                    pending = json.load(f)
            except FileNotFoundError:
                pending = {"pending_symbols": []}
            
            # Aggiungi nuovo pending
            pending_entry = {
                "symbol": symbol_data["symbol"],
                "code": symbol_data["code"],
                "fallback": symbol_data["fallback"],
                "category": symbol_data.get("category", ""),
                "name": symbol_data.get("name", ""),
                "description": symbol_data.get("description", ""),
                "submission_date": datetime.now().isoformat(),
                "validation_score": validation_result["score"],
                "validation_result": validation_result
            }
            
            pending["pending_symbols"].append(pending_entry)
            
            # Salva
            with open(self.pending_file, 'w', encoding='utf-8') as f:
                json.dump(pending, f, indent=2, ensure_ascii=False)
                
        except Exception as e:
            print(f"Errore aggiunta pending: {e}")
    
    def _add_to_rejected(self, symbol_data: Dict[str, Any], validation_result: Dict[str, Any]) -> None:
        """Aggiunge simbolo alla lista rejected nel registry."""
        self._update_registry(symbol_data, validation_result, "rejected")
    
    def _update_registry(self, symbol_data: Dict[str, Any], validation_result: Dict[str, Any], status: str) -> None:
        """Aggiorna il registry con nuovo simbolo."""
        try:
            # Carica registry
            with open(self.registry_file, 'r', encoding='utf-8') as f:
                registry = json.load(f)
            
            # Prepara entry
            registry_entry = {
                "symbol": symbol_data.get("symbol", symbol_data["symbol"]),
                "code": symbol_data.get("code", symbol_data["code"]),
                "fallback": symbol_data.get("fallback", symbol_data["fallback"]),
                "submission_date": datetime.now().isoformat(),
                "validation_score": validation_result["score"],
                "status": status
            }
            
            if status == "approved":
                registry_entry["id"] = symbol_data.get("id", self._get_next_id())
                registry_entry["approved_date"] = datetime.now().isoformat()
                registry["approved_symbols"].append(registry_entry)
                registry["stats"]["approved"] += 1
            else:
                registry["rejected_symbols"].append(registry_entry)
                registry["stats"]["rejected"] += 1
            
            registry["stats"]["total_submissions"] += 1
            
            # Salva registry aggiornato
            with open(self.registry_file, 'w', encoding='utf-8') as f:
                json.dump(registry, f, indent=2, ensure_ascii=False)
                
        except Exception as e:
            print(f"Errore aggiornamento registry: {e}")
    
    def _get_next_id(self) -> str:
        """Ottiene il prossimo ID disponibile."""
        try:
            with open(self.registry_file, 'r', encoding='utf-8') as f:
                registry = json.load(f)
            
            next_id = registry.get("next_id", "NG0021")
            
            # Incrementa per il prossimo
            num = int(next_id[2:]) + 1
            registry["next_id"] = f"NG{num:04d}"
            
            with open(self.registry_file, 'w', encoding='utf-8') as f:
                json.dump(registry, f, indent=2, ensure_ascii=False)
            
            return next_id
            
        except:
            return "NG0021"  # Fallback
    
    def _get_unicode_point(self, symbol: str) -> str:
        """Ottiene Unicode point del simbolo."""
        if not symbol:
            return "U+0000"
        
        try:
            if len(symbol) == 1:
                return f"U+{ord(symbol):04X}"
            else:
                # Multi-character symbol
                points = [f"U+{ord(c):04X}" for c in symbol]
                return "".join(points)
        except:
            return "U+0000"

def main():
    """Main function per command line usage."""
    parser = argparse.ArgumentParser(description="Submit new NEUROGLYPH ULTRA symbol")
    parser.add_argument("--symbol", required=True, help="Unicode symbol")
    parser.add_argument("--code", required=True, help="Symbol code (ng:category:function)")
    parser.add_argument("--fallback", required=True, help="ASCII fallback ([UPPERCASE])")
    parser.add_argument("--category", required=True, help="Symbol category")
    parser.add_argument("--name", required=True, help="Symbol name")
    parser.add_argument("--description", required=True, help="Symbol description")
    parser.add_argument("--aliases", help="Comma-separated aliases")
    
    args = parser.parse_args()
    
    # Prepara symbol data
    symbol_data = {
        "symbol": args.symbol,
        "code": args.code,
        "fallback": args.fallback,
        "category": args.category,
        "name": args.name,
        "description": args.description,
        "aliases": args.aliases.split(",") if args.aliases else []
    }
    
    # Submit
    submission_system = SymbolSubmissionSystem()
    result = submission_system.submit_symbol(symbol_data)
    
    # Output risultato
    print("🧠 NEUROGLYPH ULTRA - Symbol Submission Result")
    print("=" * 50)
    print(f"Symbol: {result['symbol']}")
    print(f"Code: {result['code']}")
    print(f"Status: {result['status'].upper()}")
    print(f"Score: {result['validation_score']:.1f}%")
    print(f"Message: {result['message']}")
    
    if result.get("validation_result"):
        validation = result["validation_result"]
        if validation.get("errors"):
            print("\n❌ Errors:")
            for error in validation["errors"]:
                print(f"  - {error}")
        
        if validation.get("warnings"):
            print("\n⚠️ Warnings:")
            for warning in validation["warnings"]:
                print(f"  - {warning}")
    
    sys.exit(0 if result["success"] or result["status"] == "pending" else 1)

if __name__ == "__main__":
    main()
