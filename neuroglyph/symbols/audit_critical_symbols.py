#!/usr/bin/env python3
"""
NEUROGLYPH FASE 1 - AUDIT CRITICO
Genera report CSV dettagliato di simboli problematici per validazione manuale
"""

import json
import csv
import sys
from pathlib import Path
from datetime import datetime
from typing import Dict, List, Any, Tuple

class CriticalSymbolAuditor:
    """Auditor per identificare simboli critici che necessitano validazione manuale."""
    
    def __init__(self, registry_path: str = "neuroglyph/core/symbols_registry.json"):
        self.registry_path = Path(registry_path)
        self.registry = {}
        self.critical_symbols = []
        self.audit_timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        
    def load_registry(self) -> bool:
        """Carica il registry dei simboli."""
        try:
            with open(self.registry_path, 'r', encoding='utf-8') as f:
                self.registry = json.load(f)
            print(f"✅ Registry caricato: {len(self.registry.get('approved_symbols', []))} simboli")
            return True
        except Exception as e:
            print(f"❌ Errore caricamento registry: {e}")
            return False
    
    def identify_critical_symbols(self) -> List[Dict[str, Any]]:
        """Identifica simboli critici che necessitano audit manuale."""
        symbols = self.registry.get("approved_symbols", [])
        critical_symbols = []
        
        print(f"🔍 Analizzando {len(symbols)} simboli per identificare criticità...")
        
        for symbol in symbols:
            issues = []
            priority = "LOW"
            
            # 1. Validation score < 90.0
            validation_score = symbol.get("validation_score", 100.0)
            if validation_score < 90.0:
                issues.append(f"Low validation score: {validation_score}")
                priority = "HIGH"
            
            # 2. Simboli auto-generati con score sospetti
            if symbol.get("auto_generated", False):
                if validation_score <= 1.5:  # Score tipico 1.4
                    issues.append(f"Auto-generated with suspicious score: {validation_score}")
                    priority = "HIGH"
                elif symbol.get("generator") == "simple":
                    issues.append("Auto-generated by simple generator")
                    if priority == "LOW":
                        priority = "MEDIUM"
            
            # 3. Simboli in reserved_expansion (audit semantico)
            category = symbol.get("category", "")
            if "reserved" in category.lower() or "expansion" in category.lower():
                issues.append("Reserved expansion category - semantic audit needed")
                if priority == "LOW":
                    priority = "MEDIUM"
            
            # 4. Simboli con nomi generici o numerati
            name = symbol.get("name", "")
            code = symbol.get("code", "")
            if "_1" in name or "_2" in name or "_3" in name:
                issues.append("Generic numbered name - semantic clarity check needed")
                if priority == "LOW":
                    priority = "MEDIUM"
            
            # 5. Simboli con fallback lunghi o problematici
            fallback = symbol.get("fallback", "")
            if len(fallback) > 10:
                issues.append(f"Long fallback: {len(fallback)} chars")
                if priority == "LOW":
                    priority = "MEDIUM"
            
            # 6. Simboli con unicode problematici (emoji range)
            unicode_point = symbol.get("unicode_point", "")
            if unicode_point:
                try:
                    code_point = int(unicode_point.replace("U+", ""), 16)
                    # Emoji ranges
                    if (0x1F000 <= code_point <= 0x1F9FF) or (0x2600 <= code_point <= 0x26FF):
                        issues.append(f"Potentially problematic unicode range: {unicode_point}")
                        priority = "HIGH"
                except:
                    pass
            
            # 7. Token cost alto
            token_cost = symbol.get("token_cost", 1)
            if token_cost > 2:
                issues.append(f"High token cost: {token_cost}")
                priority = "HIGH"
            
            # Se ci sono issues, aggiungi ai simboli critici
            if issues:
                critical_symbol = {
                    "id": symbol.get("id", ""),
                    "symbol": symbol.get("symbol", ""),
                    "code": symbol.get("code", ""),
                    "fallback": symbol.get("fallback", ""),
                    "category": symbol.get("category", ""),
                    "name": symbol.get("name", ""),
                    "validation_score": validation_score,
                    "auto_generated": symbol.get("auto_generated", False),
                    "generator": symbol.get("generator", ""),
                    "unicode_point": symbol.get("unicode_point", ""),
                    "token_cost": token_cost,
                    "priority": priority,
                    "issues": "; ".join(issues),
                    "approved_date": symbol.get("approved_date", ""),
                    "status": symbol.get("status", "")
                }
                critical_symbols.append(critical_symbol)
        
        # Ordina per priorità e score
        priority_order = {"HIGH": 0, "MEDIUM": 1, "LOW": 2}
        critical_symbols.sort(key=lambda x: (priority_order[x["priority"]], x["validation_score"]))
        
        self.critical_symbols = critical_symbols
        return critical_symbols
    
    def generate_csv_report(self, output_path: str = None) -> str:
        """Genera report CSV dettagliato."""
        if not output_path:
            output_path = f"neuroglyph/symbols/critical_symbols_audit_{self.audit_timestamp}.csv"
        
        fieldnames = [
            "id", "symbol", "code", "fallback", "category", "name",
            "validation_score", "auto_generated", "generator", "unicode_point",
            "token_cost", "priority", "issues", "approved_date", "status"
        ]
        
        with open(output_path, 'w', newline='', encoding='utf-8') as csvfile:
            writer = csv.DictWriter(csvfile, fieldnames=fieldnames)
            writer.writeheader()
            writer.writerows(self.critical_symbols)
        
        return output_path
    
    def generate_summary_report(self) -> Dict[str, Any]:
        """Genera report riassuntivo."""
        total_symbols = len(self.registry.get("approved_symbols", []))
        critical_count = len(self.critical_symbols)
        
        priority_counts = {"HIGH": 0, "MEDIUM": 0, "LOW": 0}
        issue_types = {}
        
        for symbol in self.critical_symbols:
            priority_counts[symbol["priority"]] += 1
            
            # Conta tipi di issues
            issues = symbol["issues"].split("; ")
            for issue in issues:
                issue_type = issue.split(":")[0].strip()
                issue_types[issue_type] = issue_types.get(issue_type, 0) + 1
        
        return {
            "audit_timestamp": self.audit_timestamp,
            "total_symbols": total_symbols,
            "critical_symbols": critical_count,
            "critical_percentage": (critical_count / total_symbols * 100) if total_symbols > 0 else 0,
            "priority_distribution": priority_counts,
            "most_common_issues": sorted(issue_types.items(), key=lambda x: x[1], reverse=True)[:10],
            "registry_health": "CRITICAL" if priority_counts["HIGH"] > 50 else "WARNING" if priority_counts["HIGH"] > 10 else "GOOD"
        }
    
    def print_summary(self, summary: Dict[str, Any]) -> None:
        """Stampa summary report."""
        print(f"\n🔍 NEUROGLYPH AUDIT CRITICO - FASE 1")
        print(f"=" * 60)
        print(f"📊 Simboli totali: {summary['total_symbols']}")
        print(f"⚠️ Simboli critici: {summary['critical_symbols']} ({summary['critical_percentage']:.1f}%)")
        print(f"🏥 Health status: {summary['registry_health']}")
        
        print(f"\n📈 DISTRIBUZIONE PRIORITÀ:")
        for priority, count in summary['priority_distribution'].items():
            emoji = "🔴" if priority == "HIGH" else "🟠" if priority == "MEDIUM" else "🟡"
            print(f"  {emoji} {priority}: {count} simboli")
        
        print(f"\n🔍 ISSUES PIÙ COMUNI:")
        for issue_type, count in summary['most_common_issues']:
            print(f"  • {issue_type}: {count} simboli")
        
        print(f"\n📅 Audit completato: {summary['audit_timestamp']}")

def main():
    """Esegue audit critico completo."""
    print("🧠 NEUROGLYPH LLM - FASE 1: AUDIT CRITICO")
    print("🎯 Identificazione simboli problematici per validazione manuale")
    print("=" * 70)
    
    # Crea auditor
    auditor = CriticalSymbolAuditor()
    
    # Carica registry
    if not auditor.load_registry():
        sys.exit(1)
    
    # Identifica simboli critici
    critical_symbols = auditor.identify_critical_symbols()
    
    # Genera report CSV
    csv_path = auditor.generate_csv_report()
    print(f"📄 Report CSV generato: {csv_path}")
    
    # Genera e stampa summary
    summary = auditor.generate_summary_report()
    auditor.print_summary(summary)
    
    # Salva summary JSON
    summary_path = f"neuroglyph/symbols/audit_summary_{auditor.audit_timestamp}.json"
    with open(summary_path, 'w', encoding='utf-8') as f:
        json.dump(summary, f, indent=2, ensure_ascii=False)
    print(f"📊 Summary JSON salvato: {summary_path}")
    
    # Raccomandazioni
    print(f"\n💡 RACCOMANDAZIONI IMMEDIATE:")
    if summary['priority_distribution']['HIGH'] > 0:
        print(f"  🔴 URGENTE: Validare manualmente {summary['priority_distribution']['HIGH']} simboli HIGH priority")
    if summary['priority_distribution']['MEDIUM'] > 0:
        print(f"  🟠 IMPORTANTE: Rivedere {summary['priority_distribution']['MEDIUM']} simboli MEDIUM priority")
    
    print(f"\n🚀 PROSSIMI PASSI:")
    print(f"  1. Aprire {csv_path} per review dettagliata")
    print(f"  2. Validare simboli HIGH priority con validate_symbol.py")
    print(f"  3. Eseguire score_symbol_quality.py sui simboli problematici")
    print(f"  4. Procedere con FASE 2: Completamento domini critici")
    
    return summary['registry_health'] in ["GOOD", "WARNING"]

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
