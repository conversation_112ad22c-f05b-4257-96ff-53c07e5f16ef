{"symbol_id": "NG0001", "symbol": "⊕", "audit_timestamp": "2025-05-25T11:55:57.699438", "audit_type": "full_validation", "unicode_valid": true, "tokenizer_cost": 1, "visual_complexity": 1, "collision_risk": 0.0, "semantic_clarity": 0.9, "usage_count": 73, "error_count": 3, "success_rate": 0.958904109589041, "overall_score": 0.979, "quality_grade": "A", "issues": [], "recommendations": [], "auditor_version": "1.0.0", "registry_version": "1.0"}
{"symbol_id": "NG0002", "symbol": "⊖", "audit_timestamp": "2025-05-25T11:55:57.701712", "audit_type": "full_validation", "unicode_valid": true, "tokenizer_cost": 1, "visual_complexity": 1, "collision_risk": 0.0, "semantic_clarity": 0.9, "usage_count": 26, "error_count": 6, "success_rate": 0.7692307692307693, "overall_score": 0.95, "quality_grade": "A", "issues": ["Low success rate: 0.77"], "recommendations": [], "auditor_version": "1.0.0", "registry_version": "1.0"}
{"symbol_id": "NG0003", "symbol": "⊗", "audit_timestamp": "2025-05-25T11:55:57.702437", "audit_type": "full_validation", "unicode_valid": true, "tokenizer_cost": 1, "visual_complexity": 1, "collision_risk": 0.0, "semantic_clarity": 0.9, "usage_count": 9, "error_count": 9, "success_rate": 0.0, "overall_score": 0.835, "quality_grade": "B", "issues": ["Low success rate: 0.00"], "recommendations": [], "auditor_version": "1.0.0", "registry_version": "1.0"}
{"symbol_id": "NG0004", "symbol": "⊘", "audit_timestamp": "2025-05-25T11:55:57.703075", "audit_type": "full_validation", "unicode_valid": true, "tokenizer_cost": 1, "visual_complexity": 1, "collision_risk": 0.0, "semantic_clarity": 0.9, "usage_count": 85, "error_count": 5, "success_rate": 0.9411764705882353, "overall_score": 0.976, "quality_grade": "A", "issues": [], "recommendations": [], "auditor_version": "1.0.0", "registry_version": "1.0"}
{"symbol_id": "NG0005", "symbol": "≡", "audit_timestamp": "2025-05-25T11:55:57.703683", "audit_type": "full_validation", "unicode_valid": true, "tokenizer_cost": 1, "visual_complexity": 1, "collision_risk": 0.1, "semantic_clarity": 0.9, "usage_count": 36, "error_count": 6, "success_rate": 0.8333333333333334, "overall_score": 0.94, "quality_grade": "A", "issues": [], "recommendations": [], "auditor_version": "1.0.0", "registry_version": "1.0"}
{"symbol_id": "NG0006", "symbol": "≢", "audit_timestamp": "2025-05-25T11:55:57.704373", "audit_type": "full_validation", "unicode_valid": true, "tokenizer_cost": 1, "visual_complexity": 1, "collision_risk": 0.4, "semantic_clarity": 0.9, "usage_count": 83, "error_count": 3, "success_rate": 0.963855421686747, "overall_score": 0.9, "quality_grade": "A", "issues": ["High collision risk: 0.40"], "recommendations": [], "auditor_version": "1.0.0", "registry_version": "1.0"}
{"symbol_id": "NG0007", "symbol": "∧", "audit_timestamp": "2025-05-25T11:55:57.705262", "audit_type": "full_validation", "unicode_valid": true, "tokenizer_cost": 1, "visual_complexity": 1, "collision_risk": 0.30000000000000004, "semantic_clarity": 0.9, "usage_count": 3, "error_count": 3, "success_rate": 0.0, "overall_score": 0.775, "quality_grade": "C", "issues": ["High collision risk: 0.30", "Low success rate: 0.00"], "recommendations": ["Improve symbol quality before expansion"], "auditor_version": "1.0.0", "registry_version": "1.0"}
{"symbol_id": "NG0008", "symbol": "∨", "audit_timestamp": "2025-05-25T11:55:57.705942", "audit_type": "full_validation", "unicode_valid": true, "tokenizer_cost": 1, "visual_complexity": 1, "collision_risk": 0.2, "semantic_clarity": 0.9, "usage_count": 37, "error_count": 7, "success_rate": 0.8108108108108109, "overall_score": 0.917, "quality_grade": "A", "issues": [], "recommendations": [], "auditor_version": "1.0.0", "registry_version": "1.0"}
{"symbol_id": "NG0009", "symbol": "¬", "audit_timestamp": "2025-05-25T11:55:57.706581", "audit_type": "full_validation", "unicode_valid": true, "tokenizer_cost": 1, "visual_complexity": 1, "collision_risk": 0.0, "semantic_clarity": 0.9, "usage_count": 93, "error_count": 3, "success_rate": 0.967741935483871, "overall_score": 0.98, "quality_grade": "A", "issues": [], "recommendations": [], "auditor_version": "1.0.0", "registry_version": "1.0"}
{"symbol_id": "NG0010", "symbol": "∈", "audit_timestamp": "2025-05-25T11:55:57.707208", "audit_type": "full_validation", "unicode_valid": true, "tokenizer_cost": 1, "visual_complexity": 1, "collision_risk": 0.1, "semantic_clarity": 0.9, "usage_count": 98, "error_count": 8, "success_rate": 0.9183673469387755, "overall_score": 0.953, "quality_grade": "A", "issues": [], "recommendations": [], "auditor_version": "1.0.0", "registry_version": "1.0"}
{"symbol_id": "NG0011", "symbol": "∉", "audit_timestamp": "2025-05-25T11:55:57.708009", "audit_type": "full_validation", "unicode_valid": true, "tokenizer_cost": 1, "visual_complexity": 2, "collision_risk": 0.0, "semantic_clarity": 0.9, "usage_count": 10, "error_count": 0, "success_rate": 1.0, "overall_score": 0.955, "quality_grade": "A", "issues": [], "recommendations": [], "auditor_version": "1.0.0", "registry_version": "1.0"}
{"symbol_id": "NG0012", "symbol": "⊨", "audit_timestamp": "2025-05-25T11:55:57.708615", "audit_type": "full_validation", "unicode_valid": true, "tokenizer_cost": 1, "visual_complexity": 1, "collision_risk": 0.0, "semantic_clarity": 0.9, "usage_count": 26, "error_count": 6, "success_rate": 0.7692307692307693, "overall_score": 0.95, "quality_grade": "A", "issues": ["Low success rate: 0.77"], "recommendations": [], "auditor_version": "1.0.0", "registry_version": "1.0"}
{"symbol_id": "NG0013", "symbol": "⇌", "audit_timestamp": "2025-05-25T11:55:57.709294", "audit_type": "full_validation", "unicode_valid": true, "tokenizer_cost": 1, "visual_complexity": 2, "collision_risk": 0.30000000000000004, "semantic_clarity": 0.9, "usage_count": 98, "error_count": 8, "success_rate": 0.9183673469387755, "overall_score": 0.883, "quality_grade": "B", "issues": ["High collision risk: 0.30"], "recommendations": [], "auditor_version": "1.0.0", "registry_version": "1.0"}
