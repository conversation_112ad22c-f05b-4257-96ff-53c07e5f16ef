#!/usr/bin/env python3
"""
NEUROGLYPH FASE 1B - REVIEW MANUALE
Review e remediation per simboli severity A (critico) e B (alto)
"""

import json
import csv
import sys
from pathlib import Path
from datetime import datetime
from typing import Dict, List, Any, Tuple

class ManualReviewEngine:
    """Engine per review manuale di simboli critici."""
    
    def __init__(self, 
                 registry_path: str = "neuroglyph/core/symbols_registry.json",
                 audit_csv_path: str = None):
        self.registry_path = Path(registry_path)
        self.audit_csv_path = audit_csv_path
        self.registry = {}
        self.audit_data = {}
        self.review_timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        
        # Azioni possibili per review
        self.review_actions = {
            "REMOVE": "Rimuovi simbolo (score troppo basso)",
            "REPLACE_UNICODE": "Sostituisci Unicode problematico",
            "REGENERATE": "Rigenera simbolo con nuovi criteri",
            "APPROVE": "Approva dopo correzioni minori",
            "DEFER": "Rimanda a review successiva"
        }
        
    def load_data(self) -> bool:
        """Carica registry e audit data."""
        # Carica registry
        try:
            with open(self.registry_path, 'r', encoding='utf-8') as f:
                self.registry = json.load(f)
            print(f"✅ Registry caricato: {len(self.registry.get('approved_symbols', []))} simboli")
        except Exception as e:
            print(f"❌ Errore caricamento registry: {e}")
            return False
        
        # Trova audit CSV più recente
        if not self.audit_csv_path:
            audit_files = list(Path("neuroglyph/symbols").glob("surgical_audit_*.csv"))
            if not audit_files:
                print("❌ Nessun file audit trovato")
                return False
            self.audit_csv_path = max(audit_files, key=lambda x: x.stat().st_mtime)
        
        # Carica audit data
        try:
            with open(self.audit_csv_path, 'r', encoding='utf-8') as f:
                reader = csv.DictReader(f)
                for row in reader:
                    self.audit_data[row['id']] = row
            print(f"✅ Audit data caricato: {len(self.audit_data)} simboli")
        except Exception as e:
            print(f"❌ Errore caricamento audit: {e}")
            return False
        
        return True
    
    def get_critical_symbols(self) -> Tuple[List[Dict], List[Dict]]:
        """Ottieni simboli severity A e B."""
        severity_a = []
        severity_b = []
        
        for symbol_id, audit_info in self.audit_data.items():
            if audit_info.get("severity") == "A":
                # Trova simbolo nel registry
                symbol = next((s for s in self.registry.get("approved_symbols", []) 
                             if s.get("id") == symbol_id), None)
                if symbol:
                    severity_a.append({
                        "symbol_data": symbol,
                        "audit_info": audit_info
                    })
            elif audit_info.get("severity") == "B":
                symbol = next((s for s in self.registry.get("approved_symbols", []) 
                             if s.get("id") == symbol_id), None)
                if symbol:
                    severity_b.append({
                        "symbol_data": symbol,
                        "audit_info": audit_info
                    })
        
        return severity_a, severity_b
    
    def analyze_critical_patterns(self, critical_symbols: List[Dict]) -> Dict[str, Any]:
        """Analizza pattern nei simboli critici."""
        patterns = {
            "low_score_count": 0,
            "unicode_risky_count": 0,
            "both_issues_count": 0,
            "score_distribution": {},
            "unicode_ranges": {},
            "categories": {},
            "auto_generated_count": 0
        }
        
        for item in critical_symbols:
            audit_info = item["audit_info"]
            symbol_data = item["symbol_data"]
            
            # Conta issues
            if audit_info.get("low_score") == "True":
                patterns["low_score_count"] += 1
            if audit_info.get("unicode_risky") == "True":
                patterns["unicode_risky_count"] += 1
            if (audit_info.get("low_score") == "True" and 
                audit_info.get("unicode_risky") == "True"):
                patterns["both_issues_count"] += 1
            
            # Score distribution
            score = float(audit_info.get("validation_score", 100.0))
            score_range = f"{int(score//10)*10}-{int(score//10)*10+9}"
            patterns["score_distribution"][score_range] = patterns["score_distribution"].get(score_range, 0) + 1
            
            # Unicode ranges
            unicode_range = audit_info.get("unicode_range", "unknown")
            patterns["unicode_ranges"][unicode_range] = patterns["unicode_ranges"].get(unicode_range, 0) + 1
            
            # Categories
            category = symbol_data.get("category", "unknown")
            patterns["categories"][category] = patterns["categories"].get(category, 0) + 1
            
            # Auto-generated
            if symbol_data.get("auto_generated", False):
                patterns["auto_generated_count"] += 1
        
        return patterns
    
    def generate_review_recommendations(self, severity_a: List[Dict], severity_b: List[Dict]) -> Dict[str, Any]:
        """Genera raccomandazioni per review manuale."""
        recommendations = {
            "severity_a_actions": {},
            "severity_b_actions": {},
            "priority_order": [],
            "batch_actions": {}
        }
        
        # Analizza severity A (critici)
        for item in severity_a:
            symbol_id = item["symbol_data"].get("id", "")
            audit_info = item["audit_info"]
            
            # Determina azione raccomandata
            action = self._determine_action(audit_info, item["symbol_data"])
            recommendations["severity_a_actions"][symbol_id] = {
                "action": action,
                "reason": self._get_action_reason(audit_info, item["symbol_data"]),
                "symbol": item["symbol_data"].get("symbol", ""),
                "score": audit_info.get("validation_score", ""),
                "issues": audit_info.get("issues", "")
            }
        
        # Analizza severity B (alto rischio)
        for item in severity_b:
            symbol_id = item["symbol_data"].get("id", "")
            audit_info = item["audit_info"]
            
            action = self._determine_action(audit_info, item["symbol_data"])
            recommendations["severity_b_actions"][symbol_id] = {
                "action": action,
                "reason": self._get_action_reason(audit_info, item["symbol_data"]),
                "symbol": item["symbol_data"].get("symbol", ""),
                "score": audit_info.get("validation_score", ""),
                "issues": audit_info.get("issues", "")
            }
        
        # Ordine di priorità
        recommendations["priority_order"] = self._calculate_priority_order(severity_a, severity_b)
        
        # Azioni batch
        recommendations["batch_actions"] = self._identify_batch_actions(severity_a, severity_b)
        
        return recommendations
    
    def _determine_action(self, audit_info: Dict, symbol_data: Dict) -> str:
        """Determina azione raccomandata per un simbolo."""
        score = float(audit_info.get("validation_score", 100.0))
        low_score = audit_info.get("low_score") == "True"
        unicode_risky = audit_info.get("unicode_risky") == "True"
        whitelisted = audit_info.get("whitelisted") == "True"
        
        # Score troppo basso -> rimuovi
        if score < 50.0:
            return "REMOVE"
        
        # Score basso + unicode rischioso -> rigenera
        if low_score and unicode_risky and not whitelisted:
            return "REGENERATE"
        
        # Solo unicode rischioso ma non whitelisted -> sostituisci
        if unicode_risky and not whitelisted and not low_score:
            return "REPLACE_UNICODE"
        
        # Score moderatamente basso -> approva con correzioni
        if low_score and score >= 70.0:
            return "APPROVE"
        
        # Altri casi -> rimanda
        return "DEFER"
    
    def _get_action_reason(self, audit_info: Dict, symbol_data: Dict) -> str:
        """Ottieni ragione per l'azione raccomandata."""
        issues = audit_info.get("issues", "").split("; ")
        score = audit_info.get("validation_score", "")
        
        reasons = []
        if "Low validation score" in audit_info.get("issues", ""):
            reasons.append(f"Score basso: {score}")
        if "Risky Unicode range" in audit_info.get("issues", ""):
            reasons.append(f"Unicode problematico: {audit_info.get('unicode_range', '')}")
        if symbol_data.get("auto_generated", False):
            reasons.append("Auto-generato")
        
        return "; ".join(reasons) if reasons else "Review generale"
    
    def _calculate_priority_order(self, severity_a: List[Dict], severity_b: List[Dict]) -> List[str]:
        """Calcola ordine di priorità per review."""
        all_symbols = severity_a + severity_b
        
        # Ordina per score (più basso = più priorità)
        sorted_symbols = sorted(all_symbols, 
                              key=lambda x: float(x["audit_info"].get("validation_score", 100.0)))
        
        return [item["symbol_data"].get("id", "") for item in sorted_symbols[:50]]  # Top 50
    
    def _identify_batch_actions(self, severity_a: List[Dict], severity_b: List[Dict]) -> Dict[str, List[str]]:
        """Identifica azioni che possono essere eseguite in batch."""
        batch_actions = {
            "REMOVE": [],
            "REPLACE_UNICODE": [],
            "REGENERATE": [],
            "APPROVE": [],
            "DEFER": []
        }
        
        all_symbols = severity_a + severity_b
        
        for item in all_symbols:
            symbol_id = item["symbol_data"].get("id", "")
            action = self._determine_action(item["audit_info"], item["symbol_data"])
            batch_actions[action].append(symbol_id)
        
        return batch_actions
    
    def save_review_report(self, severity_a: List[Dict], severity_b: List[Dict], 
                          recommendations: Dict[str, Any]) -> str:
        """Salva report di review."""
        report_path = f"neuroglyph/symbols/manual_review_report_{self.review_timestamp}.json"
        
        # Analizza pattern
        patterns_a = self.analyze_critical_patterns(severity_a)
        patterns_b = self.analyze_critical_patterns(severity_b)
        
        report = {
            "review_timestamp": self.review_timestamp,
            "summary": {
                "severity_a_count": len(severity_a),
                "severity_b_count": len(severity_b),
                "total_critical": len(severity_a) + len(severity_b)
            },
            "patterns": {
                "severity_a": patterns_a,
                "severity_b": patterns_b
            },
            "recommendations": recommendations,
            "next_steps": [
                "1. Eseguire azioni REMOVE in batch",
                "2. Sostituire Unicode problematici",
                "3. Rigenerare simboli con score < 70",
                "4. Review manuale simboli DEFER",
                "5. Procedere con FASE 2: Completamento domini"
            ]
        }
        
        with open(report_path, 'w', encoding='utf-8') as f:
            json.dump(report, f, indent=2, ensure_ascii=False)
        
        return report_path

def main():
    """Esegue review manuale."""
    print("🧠 NEUROGLYPH LLM - FASE 1B: REVIEW MANUALE")
    print("🎯 Review simboli severity A (critico) e B (alto)")
    print("=" * 70)
    
    # Crea review engine
    review_engine = ManualReviewEngine()
    
    # Carica dati
    if not review_engine.load_data():
        sys.exit(1)
    
    # Ottieni simboli critici
    severity_a, severity_b = review_engine.get_critical_symbols()
    print(f"🔴 Severity A (critici): {len(severity_a)} simboli")
    print(f"🟠 Severity B (alto rischio): {len(severity_b)} simboli")
    
    # Genera raccomandazioni
    recommendations = review_engine.generate_review_recommendations(severity_a, severity_b)
    
    # Salva report
    report_path = review_engine.save_review_report(severity_a, severity_b, recommendations)
    print(f"📄 Report review salvato: {report_path}")
    
    # Stampa summary
    print(f"\n📊 SUMMARY AZIONI RACCOMANDATE:")
    batch_actions = recommendations["batch_actions"]
    for action, symbol_ids in batch_actions.items():
        if symbol_ids:
            print(f"  {action}: {len(symbol_ids)} simboli")
    
    print(f"\n🚀 PROSSIMI PASSI:")
    print(f"  1. Review report: {report_path}")
    print(f"  2. Eseguire azioni batch automatiche")
    print(f"  3. Review manuale simboli DEFER")
    print(f"  4. Procedere con FASE 2: Completamento domini critici")
    
    return True

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
