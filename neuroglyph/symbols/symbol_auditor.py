#!/usr/bin/env python3
"""
NEUROGLYPH LLM - Symbol Auditor
==============================

Sistema di audit automatico per simboli NEUROGLYPH.
Monitora qualità, collisioni, usage e performance simboli.
"""

import json
import time
import hashlib
from pathlib import Path
from typing import Dict, List, Set, Tuple, Optional
from dataclasses import dataclass, asdict
from datetime import datetime
import unicodedata

@dataclass
class SymbolAuditEntry:
    symbol_id: str
    symbol: str
    audit_timestamp: str
    audit_type: str  # "validation", "collision_check", "usage_analysis"

    # Validation metrics
    unicode_valid: bool
    tokenizer_cost: int
    visual_complexity: int
    collision_risk: float
    semantic_clarity: float

    # Usage metrics
    usage_count: int
    error_count: int
    success_rate: float

    # Quality metrics
    overall_score: float
    quality_grade: str  # A, B, C, D, F

    # Issues found
    issues: List[str]
    recommendations: List[str]

    # Metadata
    auditor_version: str
    registry_version: str

class SymbolAuditor:
    """Auditor automatico per simboli NEUROGLYPH"""

    def __init__(self,
                 registry_path: str = "neuroglyph/core/symbols_registry.json",
                 audit_log_path: str = "neuroglyph/symbols/symbol_audit.jsonl"):
        self.registry_path = Path(registry_path)
        self.audit_log_path = Path(audit_log_path)
        self.auditor_version = "1.0.0"

        # Carica registry
        self.registry = self._load_registry()

        # Carica audit log esistente
        self.audit_history = self._load_audit_history()

        # Thresholds per quality grading
        self.quality_thresholds = {
            'A': 0.9,  # Excellent
            'B': 0.8,  # Good
            'C': 0.7,  # Acceptable
            'D': 0.6,  # Poor
            'F': 0.0   # Failing
        }

    def _load_registry(self) -> Dict:
        """Carica registry simboli"""
        if self.registry_path.exists():
            with open(self.registry_path, 'r', encoding='utf-8') as f:
                return json.load(f)
        return {"approved_symbols": []}

    def _load_audit_history(self) -> List[Dict]:
        """Carica storico audit"""
        history = []
        if self.audit_log_path.exists():
            with open(self.audit_log_path, 'r', encoding='utf-8') as f:
                for line in f:
                    if line.strip():
                        history.append(json.loads(line))
        return history

    def audit_all_symbols(self) -> List[SymbolAuditEntry]:
        """Audit completo di tutti i simboli"""
        print("🔍 AUDIT COMPLETO SIMBOLI NEUROGLYPH")
        print("=" * 60)

        symbols = self.registry.get("approved_symbols", [])
        audit_entries = []

        for i, symbol_data in enumerate(symbols):
            print(f"📊 Audit simbolo {i+1}/{len(symbols)}: {symbol_data.get('symbol', 'N/A')}")

            entry = self._audit_single_symbol(symbol_data)
            audit_entries.append(entry)

            # Salva entry nel log
            self._save_audit_entry(entry)

        # Genera report riassuntivo
        self._generate_audit_report(audit_entries)

        print(f"✅ Audit completato: {len(audit_entries)} simboli analizzati")
        return audit_entries

    def _audit_single_symbol(self, symbol_data: Dict) -> SymbolAuditEntry:
        """Audit di un singolo simbolo"""
        symbol = symbol_data.get("symbol", "")
        symbol_id = symbol_data.get("id", "")

        # Validation checks
        unicode_valid = self._check_unicode_validity(symbol)
        tokenizer_cost = self._estimate_tokenizer_cost(symbol)
        visual_complexity = self._calculate_visual_complexity(symbol)
        collision_risk = self._calculate_collision_risk(symbol)
        semantic_clarity = self._calculate_semantic_clarity(symbol_data)

        # Usage metrics (mock data - da integrare con sistema reale)
        usage_count = self._get_usage_count(symbol_id)
        error_count = self._get_error_count(symbol_id)
        success_rate = self._calculate_success_rate(usage_count, error_count)

        # Quality assessment
        overall_score = self._calculate_overall_score(
            unicode_valid, tokenizer_cost, visual_complexity,
            collision_risk, semantic_clarity, success_rate
        )
        quality_grade = self._assign_quality_grade(overall_score)

        # Issue detection
        issues = self._detect_issues(
            symbol, symbol_data, unicode_valid, tokenizer_cost,
            collision_risk, semantic_clarity, success_rate
        )

        # Recommendations
        recommendations = self._generate_recommendations(issues, overall_score)

        return SymbolAuditEntry(
            symbol_id=symbol_id,
            symbol=symbol,
            audit_timestamp=datetime.now().isoformat(),
            audit_type="full_validation",
            unicode_valid=unicode_valid,
            tokenizer_cost=tokenizer_cost,
            visual_complexity=visual_complexity,
            collision_risk=collision_risk,
            semantic_clarity=semantic_clarity,
            usage_count=usage_count,
            error_count=error_count,
            success_rate=success_rate,
            overall_score=overall_score,
            quality_grade=quality_grade,
            issues=issues,
            recommendations=recommendations,
            auditor_version=self.auditor_version,
            registry_version=self.registry.get("registry_version", "unknown")
        )

    def _check_unicode_validity(self, symbol: str) -> bool:
        """Verifica validità Unicode"""
        try:
            # Controlli base
            if len(symbol) == 0:
                return False

            # Codificabile UTF-8
            symbol.encode('utf-8')

            # Per simboli multi-carattere, controlla ogni carattere
            if len(symbol) > 1:
                for char in symbol:
                    try:
                        unicodedata.name(char)
                        category = unicodedata.category(char)
                        if category.startswith('C'):  # Control characters
                            return False
                    except ValueError:
                        return False
                return True

            # Simbolo singolo
            # Ha nome Unicode
            unicodedata.name(symbol)

            # Categoria valida
            category = unicodedata.category(symbol)
            if category.startswith('C'):  # Control characters
                return False

            return True

        except (UnicodeError, ValueError):
            return False

    def _estimate_tokenizer_cost(self, symbol: str) -> int:
        """Stima costo tokenizer"""
        # Euristica - da migliorare con tokenizer reale
        if len(symbol) == 0:
            return 0
        elif len(symbol) == 1:
            code_point = ord(symbol)
            if code_point < 0x1000:
                return 1
            elif code_point < 0x10000:
                return 1
            else:
                return 2
        else:
            # Multi-character symbol
            return len(symbol)

    def _calculate_visual_complexity(self, symbol: str) -> int:
        """Calcola complessità visiva (1-5)"""
        try:
            name = unicodedata.name(symbol, "").lower()

            complexity = 1

            # Fattori di complessità
            if "combining" in name:
                complexity += 2
            if "double" in name or "triple" in name:
                complexity += 1
            if "heavy" in name or "bold" in name:
                complexity += 1
            if len(name.split()) > 3:
                complexity += 1

            return min(complexity, 5)

        except:
            return 5  # Massima complessità se errore

    def _calculate_collision_risk(self, symbol: str) -> float:
        """Calcola rischio collisione (0.0-1.0)"""
        symbols = [s["symbol"] for s in self.registry.get("approved_symbols", [])]

        risk = 0.0

        # Controlla similarità con altri simboli
        for other_symbol in symbols:
            if other_symbol != symbol:
                if self._are_visually_similar(symbol, other_symbol):
                    risk += 0.1

        return min(risk, 1.0)

    def _are_visually_similar(self, symbol1: str, symbol2: str) -> bool:
        """Controlla similarità visiva"""
        try:
            name1 = unicodedata.name(symbol1, "").lower()
            name2 = unicodedata.name(symbol2, "").lower()

            words1 = set(name1.split())
            words2 = set(name2.split())

            if len(words1) == 0 or len(words2) == 0:
                return False

            common = words1.intersection(words2)
            similarity = len(common) / max(len(words1), len(words2))

            return similarity > 0.6

        except:
            return False

    def _calculate_semantic_clarity(self, symbol_data: Dict) -> float:
        """Calcola chiarezza semantica (0.0-1.0)"""
        # Fattori di chiarezza
        clarity = 1.0

        # Ha codice ng: ben formato?
        ng_code = symbol_data.get("code", "")
        if not ng_code.startswith("ng:"):
            clarity -= 0.3

        # Ha fallback ASCII?
        fallback = symbol_data.get("fallback", "")
        if not fallback:
            clarity -= 0.2

        # Ha descrizione?
        if "description" not in symbol_data:
            clarity -= 0.1

        return max(clarity, 0.0)

    def _get_usage_count(self, symbol_id: str) -> int:
        """Ottieni conteggio utilizzo (mock)"""
        # Mock data - da integrare con sistema tracking reale
        return hash(symbol_id) % 100

    def _get_error_count(self, symbol_id: str) -> int:
        """Ottieni conteggio errori (mock)"""
        # Mock data - da integrare con sistema tracking reale
        return hash(symbol_id) % 10

    def _calculate_success_rate(self, usage_count: int, error_count: int) -> float:
        """Calcola tasso di successo"""
        if usage_count == 0:
            return 1.0  # Nessun utilizzo = nessun errore

        return max(0.0, (usage_count - error_count) / usage_count)

    def _calculate_overall_score(self, unicode_valid: bool, tokenizer_cost: int,
                               visual_complexity: int, collision_risk: float,
                               semantic_clarity: float, success_rate: float) -> float:
        """Calcola score complessivo"""
        # Pesi per diverse metriche
        weights = {
            'unicode': 0.2,
            'tokenizer': 0.15,
            'complexity': 0.15,
            'collision': 0.2,
            'semantic': 0.15,
            'success': 0.15
        }

        # Normalizza metriche
        unicode_score = 1.0 if unicode_valid else 0.0
        tokenizer_score = 1.0 if tokenizer_cost == 1 else 0.5
        complexity_score = (6 - visual_complexity) / 5
        collision_score = 1.0 - collision_risk

        total_score = (
            unicode_score * weights['unicode'] +
            tokenizer_score * weights['tokenizer'] +
            complexity_score * weights['complexity'] +
            collision_score * weights['collision'] +
            semantic_clarity * weights['semantic'] +
            success_rate * weights['success']
        )

        return round(total_score, 3)

    def _assign_quality_grade(self, score: float) -> str:
        """Assegna grade qualità"""
        for grade, threshold in self.quality_thresholds.items():
            if score >= threshold:
                return grade
        return 'F'

    def _detect_issues(self, symbol: str, symbol_data: Dict, unicode_valid: bool,
                      tokenizer_cost: int, collision_risk: float,
                      semantic_clarity: float, success_rate: float) -> List[str]:
        """Rileva problemi nel simbolo"""
        issues = []

        if not unicode_valid:
            issues.append("Unicode validity failed")

        if tokenizer_cost > 1:
            issues.append(f"High tokenizer cost: {tokenizer_cost} tokens")

        if collision_risk > 0.3:
            issues.append(f"High collision risk: {collision_risk:.2f}")

        if semantic_clarity < 0.7:
            issues.append(f"Low semantic clarity: {semantic_clarity:.2f}")

        if success_rate < 0.8:
            issues.append(f"Low success rate: {success_rate:.2f}")

        # Controlli specifici
        ng_code = symbol_data.get("code", "")
        if not ng_code.startswith("ng:"):
            issues.append("Invalid ng: code format")

        if not symbol_data.get("fallback"):
            issues.append("Missing ASCII fallback")

        return issues

    def _generate_recommendations(self, issues: List[str], score: float) -> List[str]:
        """Genera raccomandazioni"""
        recommendations = []

        if "Unicode validity failed" in issues:
            recommendations.append("Replace with valid Unicode symbol")

        if "High tokenizer cost" in issues:
            recommendations.append("Consider symbol with lower tokenizer cost")

        if "High collision risk" in issues:
            recommendations.append("Review visual similarity with existing symbols")

        if "Low semantic clarity" in issues:
            recommendations.append("Improve ng: code and add description")

        if "Missing ASCII fallback" in issues:
            recommendations.append("Add ASCII fallback representation")

        if score < 0.6:
            recommendations.append("Consider removing or replacing this symbol")
        elif score < 0.8:
            recommendations.append("Improve symbol quality before expansion")

        return recommendations

    def _save_audit_entry(self, entry: SymbolAuditEntry):
        """Salva entry audit nel log"""
        self.audit_log_path.parent.mkdir(exist_ok=True)

        with open(self.audit_log_path, 'a', encoding='utf-8') as f:
            f.write(json.dumps(asdict(entry), ensure_ascii=False) + '\n')

    def _generate_audit_report(self, entries: List[SymbolAuditEntry]):
        """Genera report riassuntivo audit"""
        report_path = self.audit_log_path.parent / "audit_report.json"

        # Statistiche
        total_symbols = len(entries)
        grades = {}
        issues_count = {}

        for entry in entries:
            # Conteggio grades
            grade = entry.quality_grade
            grades[grade] = grades.get(grade, 0) + 1

            # Conteggio issues
            for issue in entry.issues:
                issues_count[issue] = issues_count.get(issue, 0) + 1

        # Calcola metriche aggregate
        avg_score = sum(e.overall_score for e in entries) / total_symbols if total_symbols > 0 else 0
        avg_collision_risk = sum(e.collision_risk for e in entries) / total_symbols if total_symbols > 0 else 0
        avg_success_rate = sum(e.success_rate for e in entries) / total_symbols if total_symbols > 0 else 0

        report = {
            "audit_timestamp": datetime.now().isoformat(),
            "auditor_version": self.auditor_version,
            "total_symbols": total_symbols,
            "quality_distribution": grades,
            "average_score": round(avg_score, 3),
            "average_collision_risk": round(avg_collision_risk, 3),
            "average_success_rate": round(avg_success_rate, 3),
            "common_issues": dict(sorted(issues_count.items(), key=lambda x: x[1], reverse=True)),
            "symbols_needing_attention": [
                {"symbol": e.symbol, "score": e.overall_score, "issues": e.issues}
                for e in entries if e.overall_score < 0.7
            ]
        }

        with open(report_path, 'w', encoding='utf-8') as f:
            json.dump(report, f, indent=2, ensure_ascii=False)

        print(f"📊 Report audit salvato: {report_path}")

        # Mostra statistiche
        print(f"\n📈 STATISTICHE AUDIT:")
        print(f"   Simboli totali: {total_symbols}")
        print(f"   Score medio: {avg_score:.3f}")
        print(f"   Distribuzione qualità: {grades}")
        print(f"   Simboli problematici: {len(report['symbols_needing_attention'])}")

def main():
    """Demo Symbol Auditor"""
    print("🔍 NEUROGLYPH LLM - Symbol Auditor")
    print("🎯 Audit automatico qualità simboli")
    print("=" * 70)

    auditor = SymbolAuditor()

    # Audit completo
    entries = auditor.audit_all_symbols()

    print(f"\n🎉 Audit completato!")
    print(f"📊 {len(entries)} simboli analizzati")

if __name__ == "__main__":
    main()
