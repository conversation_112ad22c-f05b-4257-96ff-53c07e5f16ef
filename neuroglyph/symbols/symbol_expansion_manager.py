#!/usr/bin/env python3
"""
NEUROGLYPH LLM - Symbol Expansion Manager
========================================

Gestione intelligente e sicura dell'espansione simboli NEUROGLYPH.
Implementa strategia tiered con validazione rigorosa per evitare caos.
"""

import json
import hashlib
from pathlib import Path
from typing import Dict, List, Set, Tuple, Optional
from dataclasses import dataclass
from enum import Enum
import unicodedata
import re

class SymbolTier(Enum):
    BASE = "base"           # 512 simboli - Logic, Math, Control
    ULTRA = "ultra"         # 1024 simboli - Async, Classes, Memory
    GOD = "god"             # 2048 simboli - Meta, AST, Latent
    ULTIMATE = "ultimate"   # 4096 simboli - Full capacity

@dataclass
class SymbolCandidate:
    symbol: str
    unicode_name: str
    unicode_code: str
    category: str
    tier: SymbolTier
    semantic_group: str
    visual_complexity: int  # 1-5 scale
    collision_risk: float   # 0.0-1.0
    tokenizer_cost: int     # Expected tokens
    fallback: str
    ng_code: str = ""
    validation_score: float = 0.0
    approved: bool = False

class SymbolExpansionManager:
    """Manager per espansione controllata simboli NEUROGLYPH"""

    def __init__(self, registry_path: str = "neuroglyph/core/symbols_registry.json"):
        self.registry_path = Path(registry_path)
        self.current_symbols = self._load_current_registry()
        self.tier_limits = {
            SymbolTier.BASE: 512,
            SymbolTier.ULTRA: 1024,
            SymbolTier.GOD: 2048,
            SymbolTier.ULTIMATE: 4096
        }

    def _load_current_registry(self) -> Dict:
        """Carica registry attuale"""
        if self.registry_path.exists():
            with open(self.registry_path, 'r', encoding='utf-8') as f:
                return json.load(f)
        return {"approved_symbols": []}

    def get_current_tier_status(self) -> Dict[SymbolTier, Dict]:
        """Analizza status attuale per tier"""
        current_count = len(self.current_symbols.get("approved_symbols", []))

        status = {}
        for tier in SymbolTier:
            limit = self.tier_limits[tier]
            if current_count >= limit:
                status[tier] = {
                    "status": "completed" if current_count == limit else "exceeded",
                    "current": current_count,
                    "limit": limit,
                    "remaining": 0
                }
            else:
                status[tier] = {
                    "status": "in_progress" if tier == self._get_current_tier(current_count) else "future",
                    "current": current_count,
                    "limit": limit,
                    "remaining": limit - current_count
                }

        return status

    def _get_current_tier(self, count: int) -> SymbolTier:
        """Determina tier attuale basato su count"""
        if count < 512:
            return SymbolTier.BASE
        elif count < 1024:
            return SymbolTier.ULTRA
        elif count < 2048:
            return SymbolTier.GOD
        else:
            return SymbolTier.ULTIMATE

    def generate_unicode_candidates(self, target_tier: SymbolTier) -> List[SymbolCandidate]:
        """Genera candidati Unicode per tier specifico"""
        print(f"🔍 Generazione candidati per TIER {target_tier.value.upper()}")

        # Unicode ranges per tier
        unicode_ranges = {
            SymbolTier.BASE: [
                (0x2200, 0x22FF),  # Mathematical Operators
                (0x2190, 0x21FF),  # Arrows
                (0x2300, 0x23FF),  # Miscellaneous Technical
            ],
            SymbolTier.ULTRA: [
                (0x25A0, 0x25FF),  # Geometric Shapes
                (0x2600, 0x26FF),  # Miscellaneous Symbols
                (0x27C0, 0x27EF),  # Miscellaneous Mathematical Symbols-A
            ],
            SymbolTier.GOD: [
                (0x2900, 0x297F),  # Supplemental Arrows-B
                (0x2980, 0x29FF),  # Miscellaneous Mathematical Symbols-B
                (0x2A00, 0x2AFF),  # Supplemental Mathematical Operators
            ],
            SymbolTier.ULTIMATE: [
                (0x1D400, 0x1D7FF),  # Mathematical Alphanumeric Symbols
                (0x1F300, 0x1F5FF),  # Miscellaneous Symbols and Pictographs
            ]
        }

        candidates = []
        ranges = unicode_ranges.get(target_tier, [])

        for start, end in ranges:
            for code_point in range(start, end + 1):
                try:
                    symbol = chr(code_point)

                    # Filtri di base
                    if not self._is_valid_candidate(symbol):
                        continue

                    # Crea candidato
                    candidate = self._create_candidate(symbol, target_tier)
                    if candidate:
                        candidates.append(candidate)

                except (ValueError, UnicodeError):
                    continue

        # Ordina per qualità
        candidates.sort(key=lambda x: x.validation_score, reverse=True)

        print(f"✅ Generati {len(candidates)} candidati per {target_tier.value}")
        return candidates

    def _is_valid_candidate(self, symbol: str) -> bool:
        """Verifica se simbolo è candidato valido"""
        # Controlli di base
        if len(symbol) != 1:
            return False

        # Categoria Unicode
        category = unicodedata.category(symbol)
        if category.startswith('C'):  # Control characters
            return False

        # Già presente nel registry
        current_symbols = [s["symbol"] for s in self.current_symbols.get("approved_symbols", [])]
        if symbol in current_symbols:
            return False

        # Renderizzabile
        try:
            symbol.encode('utf-8')
            unicodedata.name(symbol)
        except (UnicodeError, ValueError):
            return False

        return True

    def _create_candidate(self, symbol: str, tier: SymbolTier) -> Optional[SymbolCandidate]:
        """Crea candidato con metadati completi"""
        try:
            unicode_name = unicodedata.name(symbol)
            unicode_code = f"U+{ord(symbol):04X}"
            category = unicodedata.category(symbol)

            # Determina semantic group
            semantic_group = self._determine_semantic_group(symbol, unicode_name)

            # Calcola metriche
            visual_complexity = self._calculate_visual_complexity(symbol)
            collision_risk = self._calculate_collision_risk(symbol)
            tokenizer_cost = self._estimate_tokenizer_cost(symbol)

            # Genera fallback
            fallback = self._generate_fallback(symbol, unicode_name)

            # Genera ng_code
            ng_code = self._generate_ng_code(semantic_group, symbol)

            # Score di validazione
            validation_score = self._calculate_validation_score(
                visual_complexity, collision_risk, tokenizer_cost
            )

            return SymbolCandidate(
                symbol=symbol,
                unicode_name=unicode_name,
                unicode_code=unicode_code,
                category=category,
                tier=tier,
                semantic_group=semantic_group,
                visual_complexity=visual_complexity,
                collision_risk=collision_risk,
                tokenizer_cost=tokenizer_cost,
                fallback=fallback,
                ng_code=ng_code,
                validation_score=validation_score,
                approved=False
            )

        except Exception as e:
            return None

    def _determine_semantic_group(self, symbol: str, unicode_name: str) -> str:
        """Determina gruppo semantico del simbolo"""
        name_lower = unicode_name.lower()

        # Mapping keywords → semantic groups
        semantic_mapping = {
            "arrow": "flow",
            "operator": "math",
            "plus": "math",
            "minus": "math",
            "times": "math",
            "divide": "math",
            "equal": "logic",
            "greater": "logic",
            "less": "logic",
            "and": "logic",
            "or": "logic",
            "not": "logic",
            "circle": "shape",
            "square": "shape",
            "triangle": "shape",
            "diamond": "shape",
            "star": "shape",
            "function": "meta",
            "lambda": "meta",
            "integral": "calculus",
            "sum": "calculus",
            "product": "calculus",
            "partial": "calculus"
        }

        for keyword, group in semantic_mapping.items():
            if keyword in name_lower:
                return group

        # Default basato su categoria Unicode
        category = unicodedata.category(symbol)
        if category.startswith('Sm'):  # Math symbols
            return "math"
        elif category.startswith('So'):  # Other symbols
            return "misc"
        else:
            return "unknown"

    def _calculate_visual_complexity(self, symbol: str) -> int:
        """Calcola complessità visiva (1-5)"""
        # Euristica basata su caratteristiche Unicode
        name = unicodedata.name(symbol, "").lower()

        complexity = 1

        # Aumenta per caratteristiche complesse
        if "combining" in name:
            complexity += 2
        if "double" in name or "triple" in name:
            complexity += 1
        if "heavy" in name or "bold" in name:
            complexity += 1
        if len(name.split()) > 3:
            complexity += 1

        return min(complexity, 5)

    def _calculate_collision_risk(self, symbol: str) -> float:
        """Calcola rischio collisione visiva (0.0-1.0)"""
        # Simboli simili esistenti
        current_symbols = [s["symbol"] for s in self.current_symbols.get("approved_symbols", [])]

        risk = 0.0

        # Controlla similarità con simboli esistenti
        for existing in current_symbols:
            if self._are_visually_similar(symbol, existing):
                risk += 0.2

        return min(risk, 1.0)

    def _are_visually_similar(self, symbol1: str, symbol2: str) -> bool:
        """Controlla similarità visiva tra simboli"""
        # Euristica semplice - da migliorare con computer vision
        name1 = unicodedata.name(symbol1, "").lower()
        name2 = unicodedata.name(symbol2, "").lower()

        # Parole comuni nei nomi
        words1 = set(name1.split())
        words2 = set(name2.split())

        common_words = words1.intersection(words2)

        # Se condividono >50% delle parole, sono simili
        if len(common_words) > 0:
            similarity = len(common_words) / max(len(words1), len(words2))
            return similarity > 0.5

        return False

    def _estimate_tokenizer_cost(self, symbol: str) -> int:
        """Stima costo tokenizer (numero token attesi)"""
        # Euristica basata su caratteristiche Unicode
        code_point = ord(symbol)

        if code_point < 0x1000:  # Basic Multilingual Plane
            return 1
        elif code_point < 0x10000:  # Extended BMP
            return 1
        else:  # Supplementary planes
            return 2

    def _generate_fallback(self, symbol: str, unicode_name: str) -> str:
        """Genera fallback ASCII per simbolo"""
        name_lower = unicode_name.lower()

        # Mapping comuni
        fallback_mapping = {
            "plus": "[+]",
            "minus": "[-]",
            "times": "[*]",
            "divide": "[/]",
            "equal": "[=]",
            "greater": "[>]",
            "less": "[<]",
            "arrow": "[->]",
            "circle": "[O]",
            "square": "[#]",
            "triangle": "[^]",
            "star": "[*]"
        }

        for keyword, fallback in fallback_mapping.items():
            if keyword in name_lower:
                return fallback

        # Fallback generico
        return f"[{symbol}]"

    def _generate_ng_code(self, semantic_group: str, symbol: str) -> str:
        """Genera codice ng: per simbolo"""
        # Conta simboli esistenti nel gruppo
        existing_codes = [
            s["code"] for s in self.current_symbols.get("approved_symbols", [])
            if s["code"].startswith(f"ng:{semantic_group}:")
        ]

        next_id = len(existing_codes) + 1
        return f"ng:{semantic_group}:{next_id:03d}"

    def _calculate_validation_score(self, visual_complexity: int,
                                  collision_risk: float,
                                  tokenizer_cost: int) -> float:
        """Calcola score di validazione complessivo"""
        # Pesi per diverse metriche
        complexity_weight = 0.3
        collision_weight = 0.4
        tokenizer_weight = 0.3

        # Normalizza metriche (higher is better)
        complexity_score = (6 - visual_complexity) / 5  # Semplicità è meglio
        collision_score = 1.0 - collision_risk  # Meno collisioni è meglio
        tokenizer_score = 1.0 if tokenizer_cost == 1 else 0.5  # 1 token è meglio

        total_score = (
            complexity_score * complexity_weight +
            collision_score * collision_weight +
            tokenizer_score * tokenizer_weight
        )

        return round(total_score, 3)

    def validate_expansion_safety(self, target_tier: SymbolTier) -> Dict:
        """Valida sicurezza espansione a tier target"""
        current_status = self.get_current_tier_status()
        current_count = len(self.current_symbols.get("approved_symbols", []))
        target_limit = self.tier_limits[target_tier]

        safety_report = {
            "safe_to_expand": False,
            "current_tier": self._get_current_tier(current_count),
            "target_tier": target_tier,
            "current_count": current_count,
            "target_limit": target_limit,
            "expansion_size": target_limit - current_count,
            "risks": [],
            "recommendations": []
        }

        # Controlli di sicurezza
        if current_count >= target_limit:
            safety_report["risks"].append("Target tier già raggiunto o superato")
            return safety_report

        expansion_size = target_limit - current_count

        # Rischi basati su dimensione espansione
        if expansion_size > 512:
            safety_report["risks"].append("Espansione >512 simboli rischiosa")

        if expansion_size > 1024:
            safety_report["risks"].append("Espansione >1024 simboli molto rischiosa")

        # Raccomandazioni
        if expansion_size <= 256:
            safety_report["safe_to_expand"] = True
            safety_report["recommendations"].append("Espansione sicura - procedi")
        elif expansion_size <= 512:
            safety_report["safe_to_expand"] = True
            safety_report["recommendations"].append("Espansione moderata - usa validazione rigorosa")
        else:
            safety_report["recommendations"].append("Espansione in batch da 256 simboli")

        return safety_report

def main():
    """Demo Symbol Expansion Manager"""
    print("🧠 NEUROGLYPH LLM - Symbol Expansion Manager")
    print("🎯 Gestione intelligente espansione simboli")
    print("=" * 70)

    manager = SymbolExpansionManager()

    # Status attuale
    status = manager.get_current_tier_status()
    print("\n📊 STATUS TIER ATTUALI:")
    for tier, info in status.items():
        print(f"   {tier.value.upper()}: {info['current']}/{info['limit']} ({info['status']})")

    # Valida espansione a ULTRA
    safety = manager.validate_expansion_safety(SymbolTier.ULTRA)
    print(f"\n🔒 SICUREZZA ESPANSIONE A ULTRA:")
    print(f"   Sicura: {safety['safe_to_expand']}")
    print(f"   Espansione: +{safety['expansion_size']} simboli")
    if safety['risks']:
        print(f"   Rischi: {safety['risks']}")
    if safety['recommendations']:
        print(f"   Raccomandazioni: {safety['recommendations']}")

    # Genera candidati per ULTRA (sample)
    if safety['safe_to_expand']:
        candidates = manager.generate_unicode_candidates(SymbolTier.ULTRA)
        print(f"\n🎯 CANDIDATI ULTRA (top 10):")
        for i, candidate in enumerate(candidates[:10]):
            print(f"   {i+1}. {candidate.symbol} ({candidate.unicode_code}) - {candidate.semantic_group} - Score: {candidate.validation_score}")

if __name__ == "__main__":
    main()
