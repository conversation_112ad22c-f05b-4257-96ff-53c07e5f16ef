#!/usr/bin/env python3
"""
NEUROGLYPH FASE 2 - INTEGRATE GENERATED SYMBOLS
Integra simboli generati nel registry principale con validazione completa
"""

import json
import sys
import glob
from pathlib import Path
from datetime import datetime
from typing import Dict, List, Any, Set

class SymbolIntegrator:
    """Integrator per simboli generati nel registry principale."""
    
    def __init__(self, registry_path: str = "neuroglyph/core/symbols_registry.json"):
        self.registry_path = Path(registry_path)
        self.registry = {}
        self.integration_timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        self.integrated_symbols = []
        self.integration_log = []
        
    def load_registry(self) -> bool:
        """Carica il registry principale."""
        try:
            with open(self.registry_path, 'r', encoding='utf-8') as f:
                self.registry = json.load(f)
            print(f"✅ Registry caricato: {len(self.registry.get('approved_symbols', []))} simboli")
            return True
        except Exception as e:
            print(f"❌ Errore caricamento registry: {e}")
            return False
    
    def find_generated_symbol_files(self) -> List[Path]:
        """Trova tutti i file di simboli generati."""
        pattern = "neuroglyph/symbols/generated_ultra_symbols_*.json"
        files = [Path(f) for f in glob.glob(pattern)]
        files.sort(key=lambda x: x.stat().st_mtime)  # Ordina per data
        return files
    
    def load_generated_symbols(self, files: List[Path]) -> List[Dict[str, Any]]:
        """Carica tutti i simboli generati."""
        all_symbols = []
        
        for file_path in files:
            try:
                with open(file_path, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                    symbols = data.get("symbols", [])
                    all_symbols.extend(symbols)
                    print(f"📄 Caricati {len(symbols)} simboli da {file_path.name}")
            except Exception as e:
                print(f"❌ Errore caricamento {file_path}: {e}")
        
        return all_symbols
    
    def validate_symbol_for_integration(self, symbol: Dict[str, Any]) -> List[str]:
        """Valida simbolo per integrazione nel registry."""
        errors = []
        
        # Verifica campi obbligatori
        required_fields = ["id", "symbol", "unicode_point", "name", "code", "fallback", "category"]
        for field in required_fields:
            if not symbol.get(field):
                errors.append(f"Campo obbligatorio mancante: {field}")
        
        # Verifica unicità nel registry esistente
        existing_symbols = self.registry.get("approved_symbols", [])
        
        # Verifica unicità ID
        existing_ids = {s.get("id", "") for s in existing_symbols}
        if symbol.get("id") in existing_ids:
            errors.append(f"ID {symbol.get('id')} già esistente")
        
        # Verifica unicità Unicode
        existing_unicode = {s.get("unicode_point", "") for s in existing_symbols}
        if symbol.get("unicode_point") in existing_unicode:
            errors.append(f"Unicode {symbol.get('unicode_point')} già esistente")
        
        # Verifica unicità simbolo
        existing_symbol_chars = {s.get("symbol", "") for s in existing_symbols}
        if symbol.get("symbol") in existing_symbol_chars:
            errors.append(f"Simbolo {symbol.get('symbol')} già esistente")
        
        # Verifica unicità code
        existing_codes = {s.get("code", "") for s in existing_symbols}
        if symbol.get("code") in existing_codes:
            errors.append(f"Code {symbol.get('code')} già esistente")
        
        # Verifica validation score
        score = symbol.get("validation_score", 0)
        if score < 95.0:
            errors.append(f"Validation score troppo basso: {score}")
        
        # Verifica fallback length
        fallback = symbol.get("fallback", "")
        if len(fallback) > 10:
            errors.append(f"Fallback troppo lungo: {len(fallback)} chars")
        
        # Verifica token cost
        token_cost = symbol.get("token_cost", 999)
        if token_cost > 2:
            errors.append(f"Token cost troppo alto: {token_cost}")
        
        # Verifica token density
        token_density = symbol.get("token_density", 0)
        if token_density < 0.9:
            errors.append(f"Token density troppo bassa: {token_density}")
        
        return errors
    
    def integrate_symbols(self, generated_symbols: List[Dict[str, Any]]) -> Dict[str, Any]:
        """Integra simboli nel registry con validazione."""
        valid_symbols = []
        invalid_symbols = []
        
        print(f"🔍 Validando {len(generated_symbols)} simboli per integrazione...")
        
        # Traccia simboli già processati per evitare duplicati
        processed_ids = set()
        processed_unicode = set()
        processed_codes = set()
        
        for symbol in generated_symbols:
            symbol_id = symbol.get("id", "")
            
            # Salta duplicati nella stessa sessione
            if (symbol_id in processed_ids or 
                symbol.get("unicode_point") in processed_unicode or
                symbol.get("code") in processed_codes):
                continue
            
            # Valida simbolo
            errors = self.validate_symbol_for_integration(symbol)
            
            if errors:
                invalid_symbols.append({
                    "symbol": symbol,
                    "errors": errors
                })
                self.integration_log.append({
                    "action": "REJECTED",
                    "symbol_id": symbol_id,
                    "symbol_char": symbol.get("symbol", ""),
                    "errors": errors,
                    "timestamp": datetime.now().isoformat()
                })
            else:
                valid_symbols.append(symbol)
                processed_ids.add(symbol_id)
                processed_unicode.add(symbol.get("unicode_point", ""))
                processed_codes.add(symbol.get("code", ""))
                
                self.integration_log.append({
                    "action": "INTEGRATED",
                    "symbol_id": symbol_id,
                    "symbol_char": symbol.get("symbol", ""),
                    "domain": symbol.get("category", ""),
                    "score": symbol.get("validation_score", 0),
                    "timestamp": datetime.now().isoformat()
                })
        
        # Aggiungi simboli validi al registry
        if valid_symbols:
            self.registry["approved_symbols"].extend(valid_symbols)
            self.integrated_symbols = valid_symbols
        
        return {
            "total_processed": len(generated_symbols),
            "valid_integrated": len(valid_symbols),
            "invalid_rejected": len(invalid_symbols),
            "invalid_symbols": invalid_symbols
        }
    
    def update_registry_metadata(self, integration_results: Dict[str, Any]) -> None:
        """Aggiorna metadati del registry."""
        if "stats" not in self.registry:
            self.registry["stats"] = {}
        
        # Aggiorna statistiche
        self.registry["stats"]["last_integration"] = self.integration_timestamp
        self.registry["stats"]["symbols_integrated"] = integration_results["valid_integrated"]
        self.registry["stats"]["total_symbols"] = len(self.registry.get("approved_symbols", []))
        
        # Aggiorna versione
        current_version = self.registry.get("version", "1.0.0")
        version_parts = current_version.split(".")
        version_parts[1] = str(int(version_parts[1]) + 1)  # Incrementa minor version
        self.registry["version"] = ".".join(version_parts)
        
        # Aggiorna timestamp
        self.registry["last_updated"] = datetime.now().isoformat()
    
    def save_updated_registry(self) -> bool:
        """Salva registry aggiornato."""
        try:
            # Backup prima del salvataggio
            backup_path = f"neuroglyph/core/symbols_registry_backup_integration_{self.integration_timestamp}.json"
            with open(backup_path, 'w', encoding='utf-8') as f:
                json.dump(self.registry, f, indent=2, ensure_ascii=False)
            
            # Salva registry aggiornato
            with open(self.registry_path, 'w', encoding='utf-8') as f:
                json.dump(self.registry, f, indent=2, ensure_ascii=False)
            
            print(f"💾 Backup salvato: {backup_path}")
            return True
        except Exception as e:
            print(f"❌ Errore salvataggio registry: {e}")
            return False
    
    def save_integration_log(self) -> str:
        """Salva log dell'integrazione."""
        log_path = f"neuroglyph/symbols/integration_log_{self.integration_timestamp}.json"
        
        log_data = {
            "integration_timestamp": self.integration_timestamp,
            "total_actions": len(self.integration_log),
            "actions_by_type": {},
            "integration_summary": {
                "symbols_integrated": sum(1 for log in self.integration_log if log["action"] == "INTEGRATED"),
                "symbols_rejected": sum(1 for log in self.integration_log if log["action"] == "REJECTED")
            },
            "actions": self.integration_log
        }
        
        # Conta per tipo
        for log_entry in self.integration_log:
            action_type = log_entry["action"]
            log_data["actions_by_type"][action_type] = log_data["actions_by_type"].get(action_type, 0) + 1
        
        with open(log_path, 'w', encoding='utf-8') as f:
            json.dump(log_data, f, indent=2, ensure_ascii=False)
        
        return log_path
    
    def generate_integration_summary(self, integration_results: Dict[str, Any]) -> Dict[str, Any]:
        """Genera summary dell'integrazione."""
        # Analizza simboli per dominio
        domain_counts = {}
        score_stats = {"min": 100, "max": 0, "avg": 0, "total": 0}
        
        for symbol in self.integrated_symbols:
            domain = symbol.get("category", "unknown")
            domain_counts[domain] = domain_counts.get(domain, 0) + 1
            
            score = symbol.get("validation_score", 0)
            score_stats["min"] = min(score_stats["min"], score)
            score_stats["max"] = max(score_stats["max"], score)
            score_stats["total"] += score
        
        if self.integrated_symbols:
            score_stats["avg"] = score_stats["total"] / len(self.integrated_symbols)
        
        return {
            "integration_timestamp": self.integration_timestamp,
            "registry_before": len(self.registry.get("approved_symbols", [])) - len(self.integrated_symbols),
            "registry_after": len(self.registry.get("approved_symbols", [])),
            "symbols_added": len(self.integrated_symbols),
            "integration_results": integration_results,
            "domain_distribution": domain_counts,
            "quality_metrics": {
                "score_min": score_stats["min"],
                "score_max": score_stats["max"],
                "score_avg": round(score_stats["avg"], 1),
                "all_scores_above_95": all(s.get("validation_score", 0) >= 95.0 for s in self.integrated_symbols)
            }
        }

def main():
    """Esegue integrazione simboli."""
    print("🧠 NEUROGLYPH LLM - FASE 2: INTEGRATE GENERATED SYMBOLS")
    print("🎯 Integrazione simboli generati nel registry principale")
    print("=" * 70)
    
    # Crea integrator
    integrator = SymbolIntegrator()
    
    # Carica registry
    if not integrator.load_registry():
        sys.exit(1)
    
    # Trova file simboli generati
    generated_files = integrator.find_generated_symbol_files()
    if not generated_files:
        print("❌ Nessun file di simboli generati trovato")
        sys.exit(1)
    
    print(f"📄 Trovati {len(generated_files)} file di simboli generati")
    
    # Carica simboli generati
    generated_symbols = integrator.load_generated_symbols(generated_files)
    print(f"📊 Simboli totali da integrare: {len(generated_symbols)}")
    
    # Integra simboli
    integration_results = integrator.integrate_symbols(generated_symbols)
    
    # Aggiorna metadati
    integrator.update_registry_metadata(integration_results)
    
    # Salva registry aggiornato
    if integrator.save_updated_registry():
        print("✅ Registry aggiornato salvato")
    
    # Salva log
    log_path = integrator.save_integration_log()
    print(f"📋 Log integrazione salvato: {log_path}")
    
    # Genera e stampa summary
    summary = integrator.generate_integration_summary(integration_results)
    
    print(f"\n🎉 INTEGRAZIONE COMPLETATA!")
    print(f"📊 Registry prima: {summary['registry_before']} simboli")
    print(f"📈 Registry dopo: {summary['registry_after']} simboli")
    print(f"➕ Simboli aggiunti: {summary['symbols_added']}")
    print(f"❌ Simboli rigettati: {integration_results['invalid_rejected']}")
    
    print(f"\n📈 QUALITÀ INTEGRATA:")
    quality = summary['quality_metrics']
    print(f"  Score min: {quality['score_min']}")
    print(f"  Score max: {quality['score_max']}")
    print(f"  Score medio: {quality['score_avg']}")
    print(f"  Tutti score ≥ 95.0: {'✅' if quality['all_scores_above_95'] else '❌'}")
    
    print(f"\n📊 DISTRIBUZIONE DOMINI:")
    for domain, count in summary['domain_distribution'].items():
        print(f"  • {domain}: +{count} simboli")
    
    print(f"\n🚀 PROSSIMI PASSI:")
    print(f"  1. Validare registry integrato")
    print(f"  2. Eseguire test di qualità")
    print(f"  3. Lock GOD-tier registry")
    print(f"  4. Preparare per export LLM")
    
    return True

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
