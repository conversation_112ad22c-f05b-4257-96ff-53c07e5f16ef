{"generation_info": {"domain": "philosophical_concepts", "count_requested": 64, "count_generated": 64, "timestamp": "2025-05-25T18:21:27.223208", "generator": "god_tier_v1"}, "symbols": [{"symbol": "𝞉", "code": "ng:philosophical_concepts:knowledge_ctrl", "fallback": "[KNOWLEDGECTRL]", "category": "philosophical_concepts", "name": "knowledge_ctrl", "description": "Philosophical concept: knowledge_ctrl in epistemology", "subcategory": "epistemology", "unicode_point": "U+1D789", "tier": "god", "auto_generated": true, "generator": "god_tier_v1"}, {"symbol": "⤣", "code": "ng:philosophical_concepts:justification_sys", "fallback": "[JUSTIFICATIONSYS]", "category": "philosophical_concepts", "name": "justification_sys", "description": "Philosophical concept: justification_sys in epistemology", "subcategory": "epistemology", "unicode_point": "U+2923", "tier": "god", "auto_generated": true, "generator": "god_tier_v1"}, {"symbol": "⣡", "code": "ng:philosophical_concepts:justification_proc", "fallback": "[JUSTIFICATIONPROC]", "category": "philosophical_concepts", "name": "justification_proc", "description": "Philosophical concept: justification_proc in epistemology", "subcategory": "epistemology", "unicode_point": "U+28E1", "tier": "god", "auto_generated": true, "generator": "god_tier_v1"}, {"symbol": "😪", "code": "ng:philosophical_concepts:knowledge_ctrl_1", "fallback": "[KNOWLEDGECTRL1]", "category": "philosophical_concepts", "name": "knowledge_ctrl_1", "description": "Philosophical concept: knowledge_ctrl_1 in epistemology", "subcategory": "epistemology", "unicode_point": "U+1F62A", "tier": "god", "auto_generated": true, "generator": "god_tier_v1"}, {"symbol": "⢢", "code": "ng:philosophical_concepts:belief_core", "fallback": "[BELIEFCORE]", "category": "philosophical_concepts", "name": "belief_core", "description": "Philosophical concept: belief_core in epistemology", "subcategory": "epistemology", "unicode_point": "U+28A2", "tier": "god", "auto_generated": true, "generator": "god_tier_v1"}, {"symbol": "❈", "code": "ng:philosophical_concepts:belief_ctrl", "fallback": "[BELIEFCTRL]", "category": "philosophical_concepts", "name": "belief_ctrl", "description": "Philosophical concept: belief_ctrl in epistemology", "subcategory": "epistemology", "unicode_point": "U+2748", "tier": "god", "auto_generated": true, "generator": "god_tier_v1"}, {"symbol": "🞓", "code": "ng:philosophical_concepts:knowledge_proc", "fallback": "[KNOWLEDGEPROC]", "category": "philosophical_concepts", "name": "knowledge_proc", "description": "Philosophical concept: knowledge_proc in epistemology", "subcategory": "epistemology", "unicode_point": "U+1F793", "tier": "god", "auto_generated": true, "generator": "god_tier_v1"}, {"symbol": "⧘", "code": "ng:philosophical_concepts:belief_fn", "fallback": "[BELIEFFN]", "category": "philosophical_concepts", "name": "belief_fn", "description": "Philosophical concept: belief_fn in epistemology", "subcategory": "epistemology", "unicode_point": "U+29D8", "tier": "god", "auto_generated": true, "generator": "god_tier_v1"}, {"symbol": "⢞", "code": "ng:philosophical_concepts:justification_ctrl", "fallback": "[JUSTIFICATIONCTRL]", "category": "philosophical_concepts", "name": "justification_ctrl", "description": "Philosophical concept: justification_ctrl in epistemology", "subcategory": "epistemology", "unicode_point": "U+289E", "tier": "god", "auto_generated": true, "generator": "god_tier_v1"}, {"symbol": "🎊", "code": "ng:philosophical_concepts:belief_sys", "fallback": "[BELIEFSYS]", "category": "philosophical_concepts", "name": "belief_sys", "description": "Philosophical concept: belief_sys in epistemology", "subcategory": "epistemology", "unicode_point": "U+1F38A", "tier": "god", "auto_generated": true, "generator": "god_tier_v1"}, {"symbol": "🜪", "code": "ng:philosophical_concepts:ontology_op", "fallback": "[ONTOLOGYOP]", "category": "philosophical_concepts", "name": "ontology_op", "description": "Philosophical concept: ontology_op in ontology", "subcategory": "ontology", "unicode_point": "U+1F72A", "tier": "god", "auto_generated": true, "generator": "god_tier_v1"}, {"symbol": "🤵", "code": "ng:philosophical_concepts:ontology_sys", "fallback": "[ONTOLOGYSYS]", "category": "philosophical_concepts", "name": "ontology_sys", "description": "Philosophical concept: ontology_sys in ontology", "subcategory": "ontology", "unicode_point": "U+1F935", "tier": "god", "auto_generated": true, "generator": "god_tier_v1"}, {"symbol": "🔕", "code": "ng:philosophical_concepts:ontology_sys_1", "fallback": "[ONTOLOGYSYS1]", "category": "philosophical_concepts", "name": "ontology_sys_1", "description": "Philosophical concept: ontology_sys_1 in ontology", "subcategory": "ontology", "unicode_point": "U+1F515", "tier": "god", "auto_generated": true, "generator": "god_tier_v1"}, {"symbol": "🡄", "code": "ng:philosophical_concepts:ontology_op_1", "fallback": "[ONTOLOGYOP1]", "category": "philosophical_concepts", "name": "ontology_op_1", "description": "Philosophical concept: ontology_op_1 in ontology", "subcategory": "ontology", "unicode_point": "U+1F844", "tier": "god", "auto_generated": true, "generator": "god_tier_v1"}, {"symbol": "⤯", "code": "ng:philosophical_concepts:ontology_sys_2", "fallback": "[ONTOLOGYSYS2]", "category": "philosophical_concepts", "name": "ontology_sys_2", "description": "Philosophical concept: ontology_sys_2 in ontology", "subcategory": "ontology", "unicode_point": "U+292F", "tier": "god", "auto_generated": true, "generator": "god_tier_v1"}, {"symbol": "🔊", "code": "ng:philosophical_concepts:ontology_op_2", "fallback": "[ONTOLOGYOP2]", "category": "philosophical_concepts", "name": "ontology_op_2", "description": "Philosophical concept: ontology_op_2 in ontology", "subcategory": "ontology", "unicode_point": "U+1F50A", "tier": "god", "auto_generated": true, "generator": "god_tier_v1"}, {"symbol": "💔", "code": "ng:philosophical_concepts:ontology_meta", "fallback": "[ONTOLOGYMETA]", "category": "philosophical_concepts", "name": "ontology_meta", "description": "Philosophical concept: ontology_meta in ontology", "subcategory": "ontology", "unicode_point": "U+1F494", "tier": "god", "auto_generated": true, "generator": "god_tier_v1"}, {"symbol": "🞗", "code": "ng:philosophical_concepts:ontology_op_3", "fallback": "[ONTOLOGYOP3]", "category": "philosophical_concepts", "name": "ontology_op_3", "description": "Philosophical concept: ontology_op_3 in ontology", "subcategory": "ontology", "unicode_point": "U+1F797", "tier": "god", "auto_generated": true, "generator": "god_tier_v1"}, {"symbol": "🡇", "code": "ng:philosophical_concepts:ontology_op_4", "fallback": "[ONTOLOGYOP4]", "category": "philosophical_concepts", "name": "ontology_op_4", "description": "Philosophical concept: ontology_op_4 in ontology", "subcategory": "ontology", "unicode_point": "U+1F847", "tier": "god", "auto_generated": true, "generator": "god_tier_v1"}, {"symbol": "⢡", "code": "ng:philosophical_concepts:ontology_core", "fallback": "[ONTOLOGYCORE]", "category": "philosophical_concepts", "name": "ontology_core", "description": "Philosophical concept: ontology_core in ontology", "subcategory": "ontology", "unicode_point": "U+28A1", "tier": "god", "auto_generated": true, "generator": "god_tier_v1"}, {"symbol": "🐤", "code": "ng:philosophical_concepts:logicphilosophy_op", "fallback": "[LOGICPHILOSOPHYOP]", "category": "philosophical_concepts", "name": "logicphilosophy_op", "description": "Philosophical concept: logicphilosophy_op in logic_philosophy", "subcategory": "logic_philosophy", "unicode_point": "U+1F424", "tier": "god", "auto_generated": true, "generator": "god_tier_v1"}, {"symbol": "🞣", "code": "ng:philosophical_concepts:logicphilosophy", "fallback": "[LOGICPHILOSOPHY]", "category": "philosophical_concepts", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "description": "Philosophical concept: logicphilosophy in logic_philosophy", "subcategory": "logic_philosophy", "unicode_point": "U+1F7A3", "tier": "god", "auto_generated": true, "generator": "god_tier_v1"}, {"symbol": "📻", "code": "ng:philosophical_concepts:logicphilosophy_fn", "fallback": "[LOGICPHILOSOPHYFN]", "category": "philosophical_concepts", "name": "logicphilosophy_fn", "description": "Philosophical concept: logicphilosophy_fn in logic_philosophy", "subcategory": "logic_philosophy", "unicode_point": "U+1F4FB", "tier": "god", "auto_generated": true, "generator": "god_tier_v1"}, {"symbol": "✬", "code": "ng:philosophical_concepts:logicphilosophy_fn_1", "fallback": "[LOGICPHILOSOPHYFN1]", "category": "philosophical_concepts", "name": "logicphilosophy_fn_1", "description": "Philosophical concept: logicphilosophy_fn_1 in logic_philosophy", "subcategory": "logic_philosophy", "unicode_point": "U+272C", "tier": "god", "auto_generated": true, "generator": "god_tier_v1"}, {"symbol": "⢫", "code": "ng:philosophical_concepts:logicphilosophy_op_1", "fallback": "[LOGICPHILOSOPHYOP1]", "category": "philosophical_concepts", "name": "logicphilosophy_op_1", "description": "Philosophical concept: logicphilosophy_op_1 in logic_philosophy", "subcategory": "logic_philosophy", "unicode_point": "U+28AB", "tier": "god", "auto_generated": true, "generator": "god_tier_v1"}, {"symbol": "🥷", "code": "ng:philosophical_concepts:logicphilosophy_fn_2", "fallback": "[LOGICPHILOSOPHYFN2]", "category": "philosophical_concepts", "name": "logicphilosophy_fn_2", "description": "Philosophical concept: logicphilosophy_fn_2 in logic_philosophy", "subcategory": "logic_philosophy", "unicode_point": "U+1F977", "tier": "god", "auto_generated": true, "generator": "god_tier_v1"}, {"symbol": "⯡", "code": "ng:philosophical_concepts:logicphilosophy_sys", "fallback": "[LOGICPHILOSOPHYSYS]", "category": "philosophical_concepts", "name": "logicphilosophy_sys", "description": "Philosophical concept: logicphilosophy_sys in logic_philosophy", "subcategory": "logic_philosophy", "unicode_point": "U+2BE1", "tier": "god", "auto_generated": true, "generator": "god_tier_v1"}, {"symbol": "➱", "code": "ng:philosophical_concepts:logicphilosophy_op_2", "fallback": "[LOGICPHILOSOPHYOP2]", "category": "philosophical_concepts", "name": "logicphilosophy_op_2", "description": "Philosophical concept: logicphilosophy_op_2 in logic_philosophy", "subcategory": "logic_philosophy", "unicode_point": "U+27B1", "tier": "god", "auto_generated": true, "generator": "god_tier_v1"}, {"symbol": "⪙", "code": "ng:philosophical_concepts:logicphilosophy_fn_3", "fallback": "[LOGICPHILOSOPHYFN3]", "category": "philosophical_concepts", "name": "logicphilosophy_fn_3", "description": "Philosophical concept: logicphilosophy_fn_3 in logic_philosophy", "subcategory": "logic_philosophy", "unicode_point": "U+2A99", "tier": "god", "auto_generated": true, "generator": "god_tier_v1"}, {"symbol": "💽", "code": "ng:philosophical_concepts:logicphilosophy_op_3", "fallback": "[LOGICPHILOSOPHYOP3]", "category": "philosophical_concepts", "name": "logicphilosophy_op_3", "description": "Philosophical concept: logicphilosophy_op_3 in logic_philosophy", "subcategory": "logic_philosophy", "unicode_point": "U+1F4BD", "tier": "god", "auto_generated": true, "generator": "god_tier_v1"}, {"symbol": "🍊", "code": "ng:philosophical_concepts:philosophyofmind_fn", "fallback": "[PHILOSOPHYOFMINDFN]", "category": "philosophical_concepts", "name": "philosophyofmind_fn", "description": "Philosophical concept: philosophyofmind_fn in philosophy_of_mind", "subcategory": "philosophy_of_mind", "unicode_point": "U+1F34A", "tier": "god", "auto_generated": true, "generator": "god_tier_v1"}, {"symbol": "⨵", "code": "ng:philosophical_concepts:philosophyofmind", "fallback": "[PHILOSOPHYOFMIND]", "category": "philosophical_concepts", "name": "philosophyofmind", "description": "Philosophical concept: philosophyofmind in philosophy_of_mind", "subcategory": "philosophy_of_mind", "unicode_point": "U+2A35", "tier": "god", "auto_generated": true, "generator": "god_tier_v1"}, {"symbol": "🤂", "code": "ng:philosophical_concepts:philosophyofmind_1", "fallback": "[PHILOSOPHYOFMIND1]", "category": "philosophical_concepts", "name": "philosophyofmind_1", "description": "Philosophical concept: philosophyofmind_1 in philosophy_of_mind", "subcategory": "philosophy_of_mind", "unicode_point": "U+1F902", "tier": "god", "auto_generated": true, "generator": "god_tier_v1"}, {"symbol": "😐", "code": "ng:philosophical_concepts:philosophyofmind_op", "fallback": "[PHILOSOPHYOFMINDOP]", "category": "philosophical_concepts", "name": "philosophyofmind_op", "description": "Philosophical concept: philosophyofmind_op in philosophy_of_mind", "subcategory": "philosophy_of_mind", "unicode_point": "U+1F610", "tier": "god", "auto_generated": true, "generator": "god_tier_v1"}, {"symbol": "🖜", "code": "ng:philosophical_concepts:philosophyofmind_2", "fallback": "[PHILOSOPHYOFMIND2]", "category": "philosophical_concepts", "name": "philosophyofmind_2", "description": "Philosophical concept: philosophyofmind_2 in philosophy_of_mind", "subcategory": "philosophy_of_mind", "unicode_point": "U+1F59C", "tier": "god", "auto_generated": true, "generator": "god_tier_v1"}, {"symbol": "⧈", "code": "ng:philosophical_concepts:philosophyofmind_3", "fallback": "[PHILOSOPHYOFMIND3]", "category": "philosophical_concepts", "name": "philosophyofmind_3", "description": "Philosophical concept: philosophyofmind_3 in philosophy_of_mind", "subcategory": "philosophy_of_mind", "unicode_point": "U+29C8", "tier": "god", "auto_generated": true, "generator": "god_tier_v1"}, {"symbol": "⮉", "code": "ng:philosophical_concepts:philosophyofmind_4", "fallback": "[PHILOSOPHYOFMIND4]", "category": "philosophical_concepts", "name": "philosophyofmind_4", "description": "Philosophical concept: philosophyofmind_4 in philosophy_of_mind", "subcategory": "philosophy_of_mind", "unicode_point": "U+2B89", "tier": "god", "auto_generated": true, "generator": "god_tier_v1"}, {"symbol": "🢰", "code": "ng:philosophical_concepts:ethics_op", "fallback": "[ETHICSOP]", "category": "philosophical_concepts", "name": "ethics_op", "description": "Philosophical concept: ethics_op in ethics", "subcategory": "ethics", "unicode_point": "U+1F8B0", "tier": "god", "auto_generated": true, "generator": "god_tier_v1"}, {"symbol": "🤻", "code": "ng:philosophical_concepts:ethics_op_1", "fallback": "[ETHICSOP1]", "category": "philosophical_concepts", "name": "ethics_op_1", "description": "Philosophical concept: ethics_op_1 in ethics", "subcategory": "ethics", "unicode_point": "U+1F93B", "tier": "god", "auto_generated": true, "generator": "god_tier_v1"}, {"symbol": "🤽", "code": "ng:philosophical_concepts:ethics_meta", "fallback": "[ETHICSMETA]", "category": "philosophical_concepts", "name": "ethics_meta", "description": "Philosophical concept: ethics_meta in ethics", "subcategory": "ethics", "unicode_point": "U+1F93D", "tier": "god", "auto_generated": true, "generator": "god_tier_v1"}, {"symbol": "🜑", "code": "ng:philosophical_concepts:ethics_core", "fallback": "[ETHICSCORE]", "category": "philosophical_concepts", "name": "ethics_core", "description": "Philosophical concept: ethics_core in ethics", "subcategory": "ethics", "unicode_point": "U+1F711", "tier": "god", "auto_generated": true, "generator": "god_tier_v1"}, {"symbol": "⬑", "code": "ng:philosophical_concepts:ethics", "fallback": "[ETHICS]", "category": "philosophical_concepts", "name": "ethics", "description": "Philosophical concept: ethics in ethics", "subcategory": "ethics", "unicode_point": "U+2B11", "tier": "god", "auto_generated": true, "generator": "god_tier_v1"}, {"symbol": "🧰", "code": "ng:philosophical_concepts:ethics_1", "fallback": "[ETHICS1]", "category": "philosophical_concepts", "name": "ethics_1", "description": "Philosophical concept: ethics_1 in ethics", "subcategory": "ethics", "unicode_point": "U+1F9F0", "tier": "god", "auto_generated": true, "generator": "god_tier_v1"}, {"symbol": "🤡", "code": "ng:philosophical_concepts:ethics_core_1", "fallback": "[ETHICSCORE1]", "category": "philosophical_concepts", "name": "ethics_core_1", "description": "Philosophical concept: ethics_core_1 in ethics", "subcategory": "ethics", "unicode_point": "U+1F921", "tier": "god", "auto_generated": true, "generator": "god_tier_v1"}, {"symbol": "⯖", "code": "ng:philosophical_concepts:ethics_fn", "fallback": "[ETHICSFN]", "category": "philosophical_concepts", "name": "ethics_fn", "description": "Philosophical concept: ethics_fn in ethics", "subcategory": "ethics", "unicode_point": "U+2BD6", "tier": "god", "auto_generated": true, "generator": "god_tier_v1"}, {"symbol": "⪵", "code": "ng:philosophical_concepts:ethics_2", "fallback": "[ETHICS2]", "category": "philosophical_concepts", "name": "ethics_2", "description": "Philosophical concept: ethics_2 in ethics", "subcategory": "ethics", "unicode_point": "U+2AB5", "tier": "god", "auto_generated": true, "generator": "god_tier_v1"}, {"symbol": "🥔", "code": "ng:philosophical_concepts:ethics_3", "fallback": "[ETHICS3]", "category": "philosophical_concepts", "name": "ethics_3", "description": "Philosophical concept: ethics_3 in ethics", "subcategory": "ethics", "unicode_point": "U+1F954", "tier": "god", "auto_generated": true, "generator": "god_tier_v1"}, {"symbol": "⮞", "code": "ng:philosophical_concepts:metaphysics_meta", "fallback": "[METAPHYSICSMETA]", "category": "philosophical_concepts", "name": "metaphysics_meta", "description": "Philosophical concept: metaphysics_meta in metaphysics", "subcategory": "metaphysics", "unicode_point": "U+2B9E", "tier": "god", "auto_generated": true, "generator": "god_tier_v1"}, {"symbol": "⠕", "code": "ng:philosophical_concepts:metaphysics_ctrl", "fallback": "[METAPHYSICSCTRL]", "category": "philosophical_concepts", "name": "metaphysics_ctrl", "description": "Philosophical concept: metaphysics_ctrl in metaphysics", "subcategory": "metaphysics", "unicode_point": "U+2815", "tier": "god", "auto_generated": true, "generator": "god_tier_v1"}, {"symbol": "➰", "code": "ng:philosophical_concepts:metaphysics_ctrl_1", "fallback": "[METAPHYSICSCTRL1]", "category": "philosophical_concepts", "name": "metaphysics_ctrl_1", "description": "Philosophical concept: metaphysics_ctrl_1 in metaphysics", "subcategory": "metaphysics", "unicode_point": "U+27B0", "tier": "god", "auto_generated": true, "generator": "god_tier_v1"}, {"symbol": "🧇", "code": "ng:philosophical_concepts:metaphysics_ctrl_2", "fallback": "[METAPHYSICSCTRL2]", "category": "philosophical_concepts", "name": "metaphysics_ctrl_2", "description": "Philosophical concept: metaphysics_ctrl_2 in metaphysics", "subcategory": "metaphysics", "unicode_point": "U+1F9C7", "tier": "god", "auto_generated": true, "generator": "god_tier_v1"}, {"symbol": "✆", "code": "ng:philosophical_concepts:metaphysics_fn", "fallback": "[METAPHYSICSFN]", "category": "philosophical_concepts", "name": "metaphysics_fn", "description": "Philosophical concept: metaphysics_fn in metaphysics", "subcategory": "metaphysics", "unicode_point": "U+2706", "tier": "god", "auto_generated": true, "generator": "god_tier_v1"}, {"symbol": "⧻", "code": "ng:philosophical_concepts:metaphysics", "fallback": "[METAPHYSICS]", "category": "philosophical_concepts", "name": "metaphysics", "description": "Philosophical concept: metaphysics in metaphysics", "subcategory": "metaphysics", "unicode_point": "U+29FB", "tier": "god", "auto_generated": true, "generator": "god_tier_v1"}, {"symbol": "⦬", "code": "ng:philosophical_concepts:metaphysics_ctrl_3", "fallback": "[METAPHYSICSCTRL3]", "category": "philosophical_concepts", "name": "metaphysics_ctrl_3", "description": "Philosophical concept: metaphysics_ctrl_3 in metaphysics", "subcategory": "metaphysics", "unicode_point": "U+29AC", "tier": "god", "auto_generated": true, "generator": "god_tier_v1"}, {"symbol": "🦚", "code": "ng:philosophical_concepts:metaphysics_ctrl_4", "fallback": "[METAPHYSICSCTRL4]", "category": "philosophical_concepts", "name": "metaphysics_ctrl_4", "description": "Philosophical concept: metaphysics_ctrl_4 in metaphysics", "subcategory": "metaphysics", "unicode_point": "U+1F99A", "tier": "god", "auto_generated": true, "generator": "god_tier_v1"}, {"symbol": "💩", "code": "ng:philosophical_concepts:metaphysics_meta_1", "fallback": "[METAPHYSICSMETA1]", "category": "philosophical_concepts", "name": "metaphysics_meta_1", "description": "Philosophical concept: metaphysics_meta_1 in metaphysics", "subcategory": "metaphysics", "unicode_point": "U+1F4A9", "tier": "god", "auto_generated": true, "generator": "god_tier_v1"}, {"symbol": "🞩", "code": "ng:philosophical_concepts:metaphysics_core", "fallback": "[METAPHYSICSCORE]", "category": "philosophical_concepts", "name": "metaphysics_core", "description": "Philosophical concept: metaphysics_core in metaphysics", "subcategory": "metaphysics", "unicode_point": "U+1F7A9", "tier": "god", "auto_generated": true, "generator": "god_tier_v1"}, {"symbol": "🚞", "code": "ng:philosophical_concepts:metaphysics_1", "fallback": "[METAPHYSICS1]", "category": "philosophical_concepts", "name": "metaphysics_1", "description": "Philosophical concept: metaphysics_1 in metaphysics", "subcategory": "metaphysics", "unicode_point": "U+1F69E", "tier": "god", "auto_generated": true, "generator": "god_tier_v1"}, {"symbol": "🕯", "code": "ng:philosophical_concepts:metaphysics_fn_1", "fallback": "[METAPHYSICSFN1]", "category": "philosophical_concepts", "name": "metaphysics_fn_1", "description": "Philosophical concept: metaphysics_fn_1 in metaphysics", "subcategory": "metaphysics", "unicode_point": "U+1F56F", "tier": "god", "auto_generated": true, "generator": "god_tier_v1"}, {"symbol": "🝾", "code": "ng:philosophical_concepts:metaphysics_ctrl_5", "fallback": "[METAPHYSICSCTRL5]", "category": "philosophical_concepts", "name": "metaphysics_ctrl_5", "description": "Philosophical concept: metaphysics_ctrl_5 in metaphysics", "subcategory": "metaphysics", "unicode_point": "U+1F77E", "tier": "god", "auto_generated": true, "generator": "god_tier_v1"}, {"symbol": "🛣", "code": "ng:philosophical_concepts:metaphysics_fn_2", "fallback": "[METAPHYSICSFN2]", "category": "philosophical_concepts", "name": "metaphysics_fn_2", "description": "Philosophical concept: metaphysics_fn_2 in metaphysics", "subcategory": "metaphysics", "unicode_point": "U+1F6E3", "tier": "god", "auto_generated": true, "generator": "god_tier_v1"}, {"symbol": "❜", "code": "ng:philosophical_concepts:metaphysics_fn_3", "fallback": "[METAPHYSICSFN3]", "category": "philosophical_concepts", "name": "metaphysics_fn_3", "description": "Philosophical concept: metaphysics_fn_3 in metaphysics", "subcategory": "metaphysics", "unicode_point": "U+275C", "tier": "god", "auto_generated": true, "generator": "god_tier_v1"}, {"symbol": "🦞", "code": "ng:philosophical_concepts:metaphysics_proc", "fallback": "[METAPHYSICSPROC]", "category": "philosophical_concepts", "name": "metaphysics_proc", "description": "Philosophical concept: metaphysics_proc in metaphysics", "subcategory": "metaphysics", "unicode_point": "U+1F99E", "tier": "god", "auto_generated": true, "generator": "god_tier_v1"}, {"symbol": "🦔", "code": "ng:philosophical_concepts:metaphysics_ctrl_6", "fallback": "[METAPHYSICSCTRL6]", "category": "philosophical_concepts", "name": "metaphysics_ctrl_6", "description": "Philosophical concept: metaphysics_ctrl_6 in metaphysics", "subcategory": "metaphysics", "unicode_point": "U+1F994", "tier": "god", "auto_generated": true, "generator": "god_tier_v1"}]}