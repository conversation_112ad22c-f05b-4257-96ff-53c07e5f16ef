{"generation_info": {"domain": "category_theory", "count_requested": 64, "count_generated": 64, "timestamp": "2025-05-25T18:19:29.648399", "generator": "god_tier_v1"}, "symbols": [{"symbol": "🛵", "code": "ng:category_theory:map_meta", "fallback": "[MAPMETA]", "category": "category_theory", "name": "map_meta", "description": "Category theory concept: map_meta in functors", "subcategory": "functors", "unicode_point": "U+1F6F5", "tier": "god", "auto_generated": true, "generator": "god_tier_v1"}, {"symbol": "🤬", "code": "ng:category_theory:fmap_op", "fallback": "[FMAPOP]", "category": "category_theory", "name": "fmap_op", "description": "Category theory concept: fmap_op in functors", "subcategory": "functors", "unicode_point": "U+1F92C", "tier": "god", "auto_generated": true, "generator": "god_tier_v1"}, {"symbol": "⥣", "code": "ng:category_theory:functor_proc", "fallback": "[FUNCTORPROC]", "category": "category_theory", "name": "functor_proc", "description": "Category theory concept: functor_proc in functors", "subcategory": "functors", "unicode_point": "U+2963", "tier": "god", "auto_generated": true, "generator": "god_tier_v1"}, {"symbol": "😮", "code": "ng:category_theory:fmap_sys", "fallback": "[FMAPSYS]", "category": "category_theory", "name": "fmap_sys", "description": "Category theory concept: fmap_sys in functors", "subcategory": "functors", "unicode_point": "U+1F62E", "tier": "god", "auto_generated": true, "generator": "god_tier_v1"}, {"symbol": "🞄", "code": "ng:category_theory:functor", "fallback": "[FUNCTOR]", "category": "category_theory", "name": "functor", "description": "Category theory concept: functor in functors", "subcategory": "functors", "unicode_point": "U+1F784", "tier": "god", "auto_generated": true, "generator": "god_tier_v1"}, {"symbol": "🏉", "code": "ng:category_theory:functor_op", "fallback": "[FUNCTOROP]", "category": "category_theory", "name": "functor_op", "description": "Category theory concept: functor_op in functors", "subcategory": "functors", "unicode_point": "U+1F3C9", "tier": "god", "auto_generated": true, "generator": "god_tier_v1"}, {"symbol": "🝭", "code": "ng:category_theory:fmap_meta", "fallback": "[FMAPMETA]", "category": "category_theory", "name": "fmap_meta", "description": "Category theory concept: fmap_meta in functors", "subcategory": "functors", "unicode_point": "U+1F76D", "tier": "god", "auto_generated": true, "generator": "god_tier_v1"}, {"symbol": "⥏", "code": "ng:category_theory:monads_op", "fallback": "[MONADSOP]", "category": "category_theory", "name": "monads_op", "description": "Category theory concept: monads_op in monads", "subcategory": "monads", "unicode_point": "U+294F", "tier": "god", "auto_generated": true, "generator": "god_tier_v1"}, {"symbol": "🝘", "code": "ng:category_theory:monads_sys", "fallback": "[MONADSSYS]", "category": "category_theory", "name": "monads_sys", "description": "Category theory concept: monads_sys in monads", "subcategory": "monads", "unicode_point": "U+1F758", "tier": "god", "auto_generated": true, "generator": "god_tier_v1"}, {"symbol": "⦺", "code": "ng:category_theory:monads_meta", "fallback": "[MONADSMETA]", "category": "category_theory", "name": "monads_meta", "description": "Category theory concept: monads_meta in monads", "subcategory": "monads", "unicode_point": "U+29BA", "tier": "god", "auto_generated": true, "generator": "god_tier_v1"}, {"symbol": "🎁", "code": "ng:category_theory:monads_proc", "fallback": "[MONADSPROC]", "category": "category_theory", "name": "monads_proc", "description": "Category theory concept: monads_proc in monads", "subcategory": "monads", "unicode_point": "U+1F381", "tier": "god", "auto_generated": true, "generator": "god_tier_v1"}, {"symbol": "⫛", "code": "ng:category_theory:monads_core", "fallback": "[MONADSCORE]", "category": "category_theory", "name": "monads_core", "description": "Category theory concept: monads_core in monads", "subcategory": "monads", "unicode_point": "U+2ADB", "tier": "god", "auto_generated": true, "generator": "god_tier_v1"}, {"symbol": "⤖", "code": "ng:category_theory:monads_op_1", "fallback": "[MONADSOP1]", "category": "category_theory", "name": "monads_op_1", "description": "Category theory concept: monads_op_1 in monads", "subcategory": "monads", "unicode_point": "U+2916", "tier": "god", "auto_generated": true, "generator": "god_tier_v1"}, {"symbol": "⮃", "code": "ng:category_theory:monads_fn", "fallback": "[MONADSFN]", "category": "category_theory", "name": "monads_fn", "description": "Category theory concept: monads_fn in monads", "subcategory": "monads", "unicode_point": "U+2B83", "tier": "god", "auto_generated": true, "generator": "god_tier_v1"}, {"symbol": "🧨", "code": "ng:category_theory:comonads_core", "fallback": "[COMONADSCORE]", "category": "category_theory", "name": "comonads_core", "description": "Category theory concept: comonads_core in comonads", "subcategory": "comonads", "unicode_point": "U+1F9E8", "tier": "god", "auto_generated": true, "generator": "god_tier_v1"}, {"symbol": "⤙", "code": "ng:category_theory:comonads_meta", "fallback": "[COMONADSMETA]", "category": "category_theory", "name": "comonads_meta", "description": "Category theory concept: comonads_meta in comonads", "subcategory": "comonads", "unicode_point": "U+2919", "tier": "god", "auto_generated": true, "generator": "god_tier_v1"}, {"symbol": "🤼", "code": "ng:category_theory:comonads_core_1", "fallback": "[COMONADSCORE1]", "category": "category_theory", "name": "comonads_core_1", "description": "Category theory concept: comonads_core_1 in comonads", "subcategory": "comonads", "unicode_point": "U+1F93C", "tier": "god", "auto_generated": true, "generator": "god_tier_v1"}, {"symbol": "⧤", "code": "ng:category_theory:comonads_op", "fallback": "[COMONADSOP]", "category": "category_theory", "name": "comonads_op", "description": "Category theory concept: comonads_op in comonads", "subcategory": "comonads", "unicode_point": "U+29E4", "tier": "god", "auto_generated": true, "generator": "god_tier_v1"}, {"symbol": "🍗", "code": "ng:category_theory:comonads_sys", "fallback": "[COMONADSSYS]", "category": "category_theory", "name": "comonads_sys", "description": "Category theory concept: comonads_sys in comonads", "subcategory": "comonads", "unicode_point": "U+1F357", "tier": "god", "auto_generated": true, "generator": "god_tier_v1"}, {"symbol": "⦜", "code": "ng:category_theory:comonads_meta_1", "fallback": "[COMONADSMETA1]", "category": "category_theory", "name": "comonads_meta_1", "description": "Category theory concept: comonads_meta_1 in comonads", "subcategory": "comonads", "unicode_point": "U+299C", "tier": "god", "auto_generated": true, "generator": "god_tier_v1"}, {"symbol": "➚", "code": "ng:category_theory:comonads_core_2", "fallback": "[COMONADSCORE2]", "category": "category_theory", "name": "comonads_core_2", "description": "Category theory concept: comonads_core_2 in comonads", "subcategory": "comonads", "unicode_point": "U+279A", "tier": "god", "auto_generated": true, "generator": "god_tier_v1"}, {"symbol": "🝡", "code": "ng:category_theory:adjunctions_proc", "fallback": "[ADJUNCTIONSPROC]", "category": "category_theory", "name": "adjunctions_proc", "description": "Category theory concept: adjunctions_proc in adjunctions", "subcategory": "adjunctions", "unicode_point": "U+1F761", "tier": "god", "auto_generated": true, "generator": "god_tier_v1"}, {"symbol": "😍", "code": "ng:category_theory:adjunctions_proc_1", "fallback": "[ADJUNCTIONSPROC1]", "category": "category_theory", "name": "adjunctions_proc_1", "description": "Category theory concept: adjunctions_proc_1 in adjunctions", "subcategory": "adjunctions", "unicode_point": "U+1F60D", "tier": "god", "auto_generated": true, "generator": "god_tier_v1"}, {"symbol": "🚵", "code": "ng:category_theory:adjunctions", "fallback": "[ADJUNCTIONS]", "category": "category_theory", "name": "adjunctions", "description": "Category theory concept: adjunctions in adjunctions", "subcategory": "adjunctions", "unicode_point": "U+1F6B5", "tier": "god", "auto_generated": true, "generator": "god_tier_v1"}, {"symbol": "🟤", "code": "ng:category_theory:adjunctions_ctrl", "fallback": "[ADJUNCTIONSCTRL]", "category": "category_theory", "name": "adjunctions_ctrl", "description": "Category theory concept: adjunctions_ctrl in adjunctions", "subcategory": "adjunctions", "unicode_point": "U+1F7E4", "tier": "god", "auto_generated": true, "generator": "god_tier_v1"}, {"symbol": "🛂", "code": "ng:category_theory:adjunctions_1", "fallback": "[ADJUNCTIONS1]", "category": "category_theory", "name": "adjunctions_1", "description": "Category theory concept: adjunctions_1 in adjunctions", "subcategory": "adjunctions", "unicode_point": "U+1F6C2", "tier": "god", "auto_generated": true, "generator": "god_tier_v1"}, {"symbol": "⢉", "code": "ng:category_theory:adjunctions_2", "fallback": "[ADJUNCTIONS2]", "category": "category_theory", "name": "adjunctions_2", "description": "Category theory concept: adjunctions_2 in adjunctions", "subcategory": "adjunctions", "unicode_point": "U+2889", "tier": "god", "auto_generated": true, "generator": "god_tier_v1"}, {"symbol": "🠪", "code": "ng:category_theory:adjunctions_core", "fallback": "[ADJUNCTIONSCORE]", "category": "category_theory", "name": "adjunctions_core", "description": "Category theory concept: adjunctions_core in adjunctions", "subcategory": "adjunctions", "unicode_point": "U+1F82A", "tier": "god", "auto_generated": true, "generator": "god_tier_v1"}, {"symbol": "⫕", "code": "ng:category_theory:limits_ctrl", "fallback": "[LIMITSCTRL]", "category": "category_theory", "name": "limits_ctrl", "description": "Category theory concept: limits_ctrl in limits", "subcategory": "limits", "unicode_point": "U+2AD5", "tier": "god", "auto_generated": true, "generator": "god_tier_v1"}, {"symbol": "🞖", "code": "ng:category_theory:limits_meta", "fallback": "[LIMITSMETA]", "category": "category_theory", "name": "limits_meta", "description": "Category theory concept: limits_meta in limits", "subcategory": "limits", "unicode_point": "U+1F796", "tier": "god", "auto_generated": true, "generator": "god_tier_v1"}, {"symbol": "🍳", "code": "ng:category_theory:limits_fn", "fallback": "[LIMITSFN]", "category": "category_theory", "name": "limits_fn", "description": "Category theory concept: limits_fn in limits", "subcategory": "limits", "unicode_point": "U+1F373", "tier": "god", "auto_generated": true, "generator": "god_tier_v1"}, {"symbol": "😣", "code": "ng:category_theory:limits_sys", "fallback": "[LIMITSSYS]", "category": "category_theory", "name": "limits_sys", "description": "Category theory concept: limits_sys in limits", "subcategory": "limits", "unicode_point": "U+1F623", "tier": "god", "auto_generated": true, "generator": "god_tier_v1"}, {"symbol": "⣂", "code": "ng:category_theory:limits_op", "fallback": "[LIMITSOP]", "category": "category_theory", "name": "limits_op", "description": "Category theory concept: limits_op in limits", "subcategory": "limits", "unicode_point": "U+28C2", "tier": "god", "auto_generated": true, "generator": "god_tier_v1"}, {"symbol": "⫲", "code": "ng:category_theory:limits_meta_1", "fallback": "[LIMITSMETA1]", "category": "category_theory", "name": "limits_meta_1", "description": "Category theory concept: limits_meta_1 in limits", "subcategory": "limits", "unicode_point": "U+2AF2", "tier": "god", "auto_generated": true, "generator": "god_tier_v1"}, {"symbol": "🧅", "code": "ng:category_theory:limits_sys_1", "fallback": "[LIMITSSYS1]", "category": "category_theory", "name": "limits_sys_1", "description": "Category theory concept: limits_sys_1 in limits", "subcategory": "limits", "unicode_point": "U+1F9C5", "tier": "god", "auto_generated": true, "generator": "god_tier_v1"}, {"symbol": "😀", "code": "ng:category_theory:colimits_meta", "fallback": "[COLIMITSMETA]", "category": "category_theory", "name": "colimits_meta", "description": "Category theory concept: colimits_meta in colimits", "subcategory": "colimits", "unicode_point": "U+1F600", "tier": "god", "auto_generated": true, "generator": "god_tier_v1"}, {"symbol": "🝴", "code": "ng:category_theory:colimits_fn", "fallback": "[COLIMITSFN]", "category": "category_theory", "name": "colimits_fn", "description": "Category theory concept: colimits_fn in colimits", "subcategory": "colimits", "unicode_point": "U+1F774", "tier": "god", "auto_generated": true, "generator": "god_tier_v1"}, {"symbol": "😉", "code": "ng:category_theory:colimits_op", "fallback": "[COLIMITSOP]", "category": "category_theory", "name": "colimits_op", "description": "Category theory concept: colimits_op in colimits", "subcategory": "colimits", "unicode_point": "U+1F609", "tier": "god", "auto_generated": true, "generator": "god_tier_v1"}, {"symbol": "➿", "code": "ng:category_theory:colimits_meta_1", "fallback": "[COLIMITSMETA1]", "category": "category_theory", "name": "colimits_meta_1", "description": "Category theory concept: colimits_meta_1 in colimits", "subcategory": "colimits", "unicode_point": "U+27BF", "tier": "god", "auto_generated": true, "generator": "god_tier_v1"}, {"symbol": "🚅", "code": "ng:category_theory:colimits_fn_1", "fallback": "[COLIMITSFN1]", "category": "category_theory", "name": "colimits_fn_1", "description": "Category theory concept: colimits_fn_1 in colimits", "subcategory": "colimits", "unicode_point": "U+1F685", "tier": "god", "auto_generated": true, "generator": "god_tier_v1"}, {"symbol": "🝊", "code": "ng:category_theory:colimits_ctrl", "fallback": "[COLIMITSCTRL]", "category": "category_theory", "name": "colimits_ctrl", "description": "Category theory concept: colimits_ctrl in colimits", "subcategory": "colimits", "unicode_point": "U+1F74A", "tier": "god", "auto_generated": true, "generator": "god_tier_v1"}, {"symbol": "🜚", "code": "ng:category_theory:colimits_ctrl_1", "fallback": "[COLIMITSCTRL1]", "category": "category_theory", "name": "colimits_ctrl_1", "description": "Category theory concept: colimits_ctrl_1 in colimits", "subcategory": "colimits", "unicode_point": "U+1F71A", "tier": "god", "auto_generated": true, "generator": "god_tier_v1"}, {"symbol": "👏", "code": "ng:category_theory:topoi_core", "fallback": "[TOPOICORE]", "category": "category_theory", "name": "topoi_core", "description": "Category theory concept: topoi_core in topoi", "subcategory": "topoi", "unicode_point": "U+1F44F", "tier": "god", "auto_generated": true, "generator": "god_tier_v1"}, {"symbol": "🝫", "code": "ng:category_theory:topoi", "fallback": "[TOPOI]", "category": "category_theory", "name": "topoi", "description": "Category theory concept: topoi in topoi", "subcategory": "topoi", "unicode_point": "U+1F76B", "tier": "god", "auto_generated": true, "generator": "god_tier_v1"}, {"symbol": "🦌", "code": "ng:category_theory:topoi_op", "fallback": "[TOPOIOP]", "category": "category_theory", "name": "topoi_op", "description": "Category theory concept: topoi_op in topoi", "subcategory": "topoi", "unicode_point": "U+1F98C", "tier": "god", "auto_generated": true, "generator": "god_tier_v1"}, {"symbol": "🙆", "code": "ng:category_theory:topoi_fn", "fallback": "[TOPOIFN]", "category": "category_theory", "name": "topoi_fn", "description": "Category theory concept: topoi_fn in topoi", "subcategory": "topoi", "unicode_point": "U+1F646", "tier": "god", "auto_generated": true, "generator": "god_tier_v1"}, {"symbol": "🠸", "code": "ng:category_theory:topoi_fn_1", "fallback": "[TOPOIFN1]", "category": "category_theory", "name": "topoi_fn_1", "description": "Category theory concept: topoi_fn_1 in topoi", "subcategory": "topoi", "unicode_point": "U+1F838", "tier": "god", "auto_generated": true, "generator": "god_tier_v1"}, {"symbol": "🗰", "code": "ng:category_theory:topoi_core_1", "fallback": "[TOPOICORE1]", "category": "category_theory", "name": "topoi_core_1", "description": "Category theory concept: topoi_core_1 in topoi", "subcategory": "topoi", "unicode_point": "U+1F5F0", "tier": "god", "auto_generated": true, "generator": "god_tier_v1"}, {"symbol": "⤬", "code": "ng:category_theory:topoi_meta", "fallback": "[TOPOIMETA]", "category": "category_theory", "name": "topoi_meta", "description": "Category theory concept: topoi_meta in topoi", "subcategory": "topoi", "unicode_point": "U+292C", "tier": "god", "auto_generated": true, "generator": "god_tier_v1"}, {"symbol": "🥖", "code": "ng:category_theory:sheaves_op", "fallback": "[SHEAVESOP]", "category": "category_theory", "name": "sheaves_op", "description": "Category theory concept: sheaves_op in sheaves", "subcategory": "sheaves", "unicode_point": "U+1F956", "tier": "god", "auto_generated": true, "generator": "god_tier_v1"}, {"symbol": "😔", "code": "ng:category_theory:sheaves_op_1", "fallback": "[SHEAVESOP1]", "category": "category_theory", "name": "sheaves_op_1", "description": "Category theory concept: sheaves_op_1 in sheaves", "subcategory": "sheaves", "unicode_point": "U+1F614", "tier": "god", "auto_generated": true, "generator": "god_tier_v1"}, {"symbol": "🟋", "code": "ng:category_theory:sheaves_ctrl", "fallback": "[SHEAVESCTRL]", "category": "category_theory", "name": "sheaves_ctrl", "description": "Category theory concept: sheaves_ctrl in sheaves", "subcategory": "sheaves", "unicode_point": "U+1F7CB", "tier": "god", "auto_generated": true, "generator": "god_tier_v1"}, {"symbol": "🡭", "code": "ng:category_theory:sheaves_ctrl_1", "fallback": "[SHEAVESCTRL1]", "category": "category_theory", "name": "sheaves_ctrl_1", "description": "Category theory concept: sheaves_ctrl_1 in sheaves", "subcategory": "sheaves", "unicode_point": "U+1F86D", "tier": "god", "auto_generated": true, "generator": "god_tier_v1"}, {"symbol": "😓", "code": "ng:category_theory:sheaves_fn", "fallback": "[SHEAVESFN]", "category": "category_theory", "name": "sheaves_fn", "description": "Category theory concept: sheaves_fn in sheaves", "subcategory": "sheaves", "unicode_point": "U+1F613", "tier": "god", "auto_generated": true, "generator": "god_tier_v1"}, {"symbol": "😞", "code": "ng:category_theory:sheaves_sys", "fallback": "[SHEAVESSYS]", "category": "category_theory", "name": "sheaves_sys", "description": "Category theory concept: sheaves_sys in sheaves", "subcategory": "sheaves", "unicode_point": "U+1F61E", "tier": "god", "auto_generated": true, "generator": "god_tier_v1"}, {"symbol": "⣳", "code": "ng:category_theory:sheaves_ctrl_2", "fallback": "[SHEAVESCTRL2]", "category": "category_theory", "name": "sheaves_ctrl_2", "description": "Category theory concept: sheaves_ctrl_2 in sheaves", "subcategory": "sheaves", "unicode_point": "U+28F3", "tier": "god", "auto_generated": true, "generator": "god_tier_v1"}, {"symbol": "🟦", "code": "ng:category_theory:sheaves_op_2", "fallback": "[SHEAVESOP2]", "category": "category_theory", "name": "sheaves_op_2", "description": "Category theory concept: sheaves_op_2 in sheaves", "subcategory": "sheaves", "unicode_point": "U+1F7E6", "tier": "god", "auto_generated": true, "generator": "god_tier_v1"}, {"symbol": "⤡", "code": "ng:category_theory:sheaves", "fallback": "[SHEAVES]", "category": "category_theory", "name": "sheaves", "description": "Category theory concept: sheaves in sheaves", "subcategory": "sheaves", "unicode_point": "U+2921", "tier": "god", "auto_generated": true, "generator": "god_tier_v1"}, {"symbol": "✻", "code": "ng:category_theory:sheaves_fn_1", "fallback": "[SHEAVESFN1]", "category": "category_theory", "name": "sheaves_fn_1", "description": "Category theory concept: sheaves_fn_1 in sheaves", "subcategory": "sheaves", "unicode_point": "U+273B", "tier": "god", "auto_generated": true, "generator": "god_tier_v1"}, {"symbol": "🛫", "code": "ng:category_theory:sheaves_ctrl_3", "fallback": "[SHEAVESCTRL3]", "category": "category_theory", "name": "sheaves_ctrl_3", "description": "Category theory concept: sheaves_ctrl_3 in sheaves", "subcategory": "sheaves", "unicode_point": "U+1F6EB", "tier": "god", "auto_generated": true, "generator": "god_tier_v1"}, {"symbol": "⡦", "code": "ng:category_theory:sheaves_op_3", "fallback": "[SHEAVESOP3]", "category": "category_theory", "name": "sheaves_op_3", "description": "Category theory concept: sheaves_op_3 in sheaves", "subcategory": "sheaves", "unicode_point": "U+2866", "tier": "god", "auto_generated": true, "generator": "god_tier_v1"}, {"symbol": "⥇", "code": "ng:category_theory:sheaves_ctrl_4", "fallback": "[SHEAVESCTRL4]", "category": "category_theory", "name": "sheaves_ctrl_4", "description": "Category theory concept: sheaves_ctrl_4 in sheaves", "subcategory": "sheaves", "unicode_point": "U+2947", "tier": "god", "auto_generated": true, "generator": "god_tier_v1"}, {"symbol": "🛺", "code": "ng:category_theory:sheaves_proc", "fallback": "[SHEAVESPROC]", "category": "category_theory", "name": "sheaves_proc", "description": "Category theory concept: sheaves_proc in sheaves", "subcategory": "sheaves", "unicode_point": "U+1F6FA", "tier": "god", "auto_generated": true, "generator": "god_tier_v1"}, {"symbol": "😝", "code": "ng:category_theory:sheaves_op_4", "fallback": "[SHEAVESOP4]", "category": "category_theory", "name": "sheaves_op_4", "description": "Category theory concept: sheaves_op_4 in sheaves", "subcategory": "sheaves", "unicode_point": "U+1F61D", "tier": "god", "auto_generated": true, "generator": "god_tier_v1"}]}