{"generation_info": {"domain": "meta_programming", "count_requested": 128, "count_generated": 63, "timestamp": "2025-05-25T18:19:27.173456", "generator": "god_tier_v1"}, "symbols": [{"symbol": "⩶", "code": "ng:meta_programming:codeasdata_proc", "fallback": "[CODEASDATAPROC]", "category": "meta_programming", "name": "codeasdata_proc", "description": "Meta-programming primitive: codeasdata_proc for code_as_data", "subcategory": "code_as_data", "unicode_point": "U+2A76", "tier": "god", "auto_generated": true, "generator": "god_tier_v1"}, {"symbol": "⧿", "code": "ng:meta_programming:codeasdata", "fallback": "[CODEASDATA]", "category": "meta_programming", "name": "codeasdata", "description": "Meta-programming primitive: codeasdata for code_as_data", "subcategory": "code_as_data", "unicode_point": "U+29FF", "tier": "god", "auto_generated": true, "generator": "god_tier_v1"}, {"symbol": "⦼", "code": "ng:meta_programming:codeasdata_sys", "fallback": "[CODEASDATASYS]", "category": "meta_programming", "name": "codeasdata_sys", "description": "Meta-programming primitive: codeasdata_sys for code_as_data", "subcategory": "code_as_data", "unicode_point": "U+29BC", "tier": "god", "auto_generated": true, "generator": "god_tier_v1"}, {"symbol": "🗟", "code": "ng:meta_programming:codeasdata_core", "fallback": "[CODEASDATACORE]", "category": "meta_programming", "name": "codeasdata_core", "description": "Meta-programming primitive: codeasdata_core for code_as_data", "subcategory": "code_as_data", "unicode_point": "U+1F5DF", "tier": "god", "auto_generated": true, "generator": "god_tier_v1"}, {"symbol": "⥈", "code": "ng:meta_programming:codeasdata_fn", "fallback": "[CODEASDATAFN]", "category": "meta_programming", "name": "codeasdata_fn", "description": "Meta-programming primitive: codeasdata_fn for code_as_data", "subcategory": "code_as_data", "unicode_point": "U+2948", "tier": "god", "auto_generated": true, "generator": "god_tier_v1"}, {"symbol": "🙅", "code": "ng:meta_programming:codeasdata_op", "fallback": "[CODEASDATAOP]", "category": "meta_programming", "name": "codeasdata_op", "description": "Meta-programming primitive: codeasdata_op for code_as_data", "subcategory": "code_as_data", "unicode_point": "U+1F645", "tier": "god", "auto_generated": true, "generator": "god_tier_v1"}, {"symbol": "⯚", "code": "ng:meta_programming:codeasdata_1", "fallback": "[CODEASDATA1]", "category": "meta_programming", "name": "codeasdata_1", "description": "Meta-programming primitive: codeasdata_1 for code_as_data", "subcategory": "code_as_data", "unicode_point": "U+2BDA", "tier": "god", "auto_generated": true, "generator": "god_tier_v1"}, {"symbol": "⣹", "code": "ng:meta_programming:codeasdata_2", "fallback": "[CODEASDATA2]", "category": "meta_programming", "name": "codeasdata_2", "description": "Meta-programming primitive: codeasdata_2 for code_as_data", "subcategory": "code_as_data", "unicode_point": "U+28F9", "tier": "god", "auto_generated": true, "generator": "god_tier_v1"}, {"symbol": "🚂", "code": "ng:meta_programming:codeasdata_3", "fallback": "[CODEASDATA3]", "category": "meta_programming", "name": "codeasdata_3", "description": "Meta-programming primitive: codeasdata_3 for code_as_data", "subcategory": "code_as_data", "unicode_point": "U+1F682", "tier": "god", "auto_generated": true, "generator": "god_tier_v1"}, {"symbol": "⥯", "code": "ng:meta_programming:codeasdata_meta", "fallback": "[CODEASDATAMETA]", "category": "meta_programming", "name": "codeasdata_meta", "description": "Meta-programming primitive: codeasdata_meta for code_as_data", "subcategory": "code_as_data", "unicode_point": "U+296F", "tier": "god", "auto_generated": true, "generator": "god_tier_v1"}, {"symbol": "⥍", "code": "ng:meta_programming:codeasdata_core_1", "fallback": "[CODEASDATACORE1]", "category": "meta_programming", "name": "codeasdata_core_1", "description": "Meta-programming primitive: codeasdata_core_1 for code_as_data", "subcategory": "code_as_data", "unicode_point": "U+294D", "tier": "god", "auto_generated": true, "generator": "god_tier_v1"}, {"symbol": "🠟", "code": "ng:meta_programming:codeasdata_op_1", "fallback": "[CODEASDATAOP1]", "category": "meta_programming", "name": "codeasdata_op_1", "description": "Meta-programming primitive: codeasdata_op_1 for code_as_data", "subcategory": "code_as_data", "unicode_point": "U+1F81F", "tier": "god", "auto_generated": true, "generator": "god_tier_v1"}, {"symbol": "🦢", "code": "ng:meta_programming:codeasdata_4", "fallback": "[CODEASDATA4]", "category": "meta_programming", "name": "codeasdata_4", "description": "Meta-programming primitive: codeasdata_4 for code_as_data", "subcategory": "code_as_data", "unicode_point": "U+1F9A2", "tier": "god", "auto_generated": true, "generator": "god_tier_v1"}, {"symbol": "🥤", "code": "ng:meta_programming:codeasdata_sys_1", "fallback": "[CODEASDATASYS1]", "category": "meta_programming", "name": "codeasdata_sys_1", "description": "Meta-programming primitive: codeasdata_sys_1 for code_as_data", "subcategory": "code_as_data", "unicode_point": "U+1F964", "tier": "god", "auto_generated": true, "generator": "god_tier_v1"}, {"symbol": "🝻", "code": "ng:meta_programming:macrosystems_ctrl", "fallback": "[MACROSYSTEMSCTRL]", "category": "meta_programming", "name": "macrosystems_ctrl", "description": "Meta-programming primitive: macrosystems_ctrl for macro_systems", "subcategory": "macro_systems", "unicode_point": "U+1F77B", "tier": "god", "auto_generated": true, "generator": "god_tier_v1"}, {"symbol": "🥆", "code": "ng:meta_programming:macrosystems_ctrl_1", "fallback": "[MACROSYSTEMSCTRL1]", "category": "meta_programming", "name": "macrosystems_ctrl_1", "description": "Meta-programming primitive: macrosystems_ctrl_1 for macro_systems", "subcategory": "macro_systems", "unicode_point": "U+1F946", "tier": "god", "auto_generated": true, "generator": "god_tier_v1"}, {"symbol": "🢩", "code": "ng:meta_programming:macrosystems_proc", "fallback": "[MACROSYSTEMSPROC]", "category": "meta_programming", "name": "macrosystems_proc", "description": "Meta-programming primitive: macrosystems_proc for macro_systems", "subcategory": "macro_systems", "unicode_point": "U+1F8A9", "tier": "god", "auto_generated": true, "generator": "god_tier_v1"}, {"symbol": "🟘", "code": "ng:meta_programming:macrosystems_fn", "fallback": "[MACROSYSTEMSFN]", "category": "meta_programming", "name": "macrosystems_fn", "description": "Meta-programming primitive: macrosystems_fn for macro_systems", "subcategory": "macro_systems", "unicode_point": "U+1F7D8", "tier": "god", "auto_generated": true, "generator": "god_tier_v1"}, {"symbol": "✽", "code": "ng:meta_programming:macrosystems_ctrl_2", "fallback": "[MACROSYSTEMSCTRL2]", "category": "meta_programming", "name": "macrosystems_ctrl_2", "description": "Meta-programming primitive: macrosystems_ctrl_2 for macro_systems", "subcategory": "macro_systems", "unicode_point": "U+273D", "tier": "god", "auto_generated": true, "generator": "god_tier_v1"}, {"symbol": "🝔", "code": "ng:meta_programming:macrosystems", "fallback": "[MACROSYSTEMS]", "category": "meta_programming", "name": "macrosystems", "description": "Meta-programming primitive: macrosystems for macro_systems", "subcategory": "macro_systems", "unicode_point": "U+1F754", "tier": "god", "auto_generated": true, "generator": "god_tier_v1"}, {"symbol": "⢁", "code": "ng:meta_programming:macrosystems_sys", "fallback": "[MACROSYSTEMSSYS]", "category": "meta_programming", "name": "macrosystems_sys", "description": "Meta-programming primitive: macrosystems_sys for macro_systems", "subcategory": "macro_systems", "unicode_point": "U+2881", "tier": "god", "auto_generated": true, "generator": "god_tier_v1"}, {"symbol": "😣", "code": "ng:meta_programming:macrosystems_1", "fallback": "[MACROSYSTEMS1]", "category": "meta_programming", "name": "macrosystems_1", "description": "Meta-programming primitive: macrosystems_1 for macro_systems", "subcategory": "macro_systems", "unicode_point": "U+1F623", "tier": "god", "auto_generated": true, "generator": "god_tier_v1"}, {"symbol": "🜆", "code": "ng:meta_programming:macrosystems_2", "fallback": "[MACROSYSTEMS2]", "category": "meta_programming", "name": "macrosystems_2", "description": "Meta-programming primitive: macrosystems_2 for macro_systems", "subcategory": "macro_systems", "unicode_point": "U+1F706", "tier": "god", "auto_generated": true, "generator": "god_tier_v1"}, {"symbol": "⡶", "code": "ng:meta_programming:macrosystems_op", "fallback": "[MACROSYSTEMSOP]", "category": "meta_programming", "name": "macrosystems_op", "description": "Meta-programming primitive: macrosystems_op for macro_systems", "subcategory": "macro_systems", "unicode_point": "U+2876", "tier": "god", "auto_generated": true, "generator": "god_tier_v1"}, {"symbol": "⦭", "code": "ng:meta_programming:macrosystems_op_1", "fallback": "[MACROSYSTEMSOP1]", "category": "meta_programming", "name": "macrosystems_op_1", "description": "Meta-programming primitive: macrosystems_op_1 for macro_systems", "subcategory": "macro_systems", "unicode_point": "U+29AD", "tier": "god", "auto_generated": true, "generator": "god_tier_v1"}, {"symbol": "⥅", "code": "ng:meta_programming:macrosystems_3", "fallback": "[MACROSYSTEMS3]", "category": "meta_programming", "name": "macrosystems_3", "description": "Meta-programming primitive: macrosystems_3 for macro_systems", "subcategory": "macro_systems", "unicode_point": "U+2945", "tier": "god", "auto_generated": true, "generator": "god_tier_v1"}, {"symbol": "⢹", "code": "ng:meta_programming:macrosystems_op_2", "fallback": "[MACROSYSTEMSOP2]", "category": "meta_programming", "name": "macrosystems_op_2", "description": "Meta-programming primitive: macrosystems_op_2 for macro_systems", "subcategory": "macro_systems", "unicode_point": "U+28B9", "tier": "god", "auto_generated": true, "generator": "god_tier_v1"}, {"symbol": "⧛", "code": "ng:meta_programming:macrosystems_proc_1", "fallback": "[MACROSYSTEMSPROC1]", "category": "meta_programming", "name": "macrosystems_proc_1", "description": "Meta-programming primitive: macrosystems_proc_1 for macro_systems", "subcategory": "macro_systems", "unicode_point": "U+29DB", "tier": "god", "auto_generated": true, "generator": "god_tier_v1"}, {"symbol": "🞂", "code": "ng:meta_programming:stagedcomputation", "fallback": "[STAGEDCOMPUTATION]", "category": "meta_programming", "name": "stagedcomputation", "description": "Meta-programming primitive: stagedcomputation for staged_computation", "subcategory": "staged_computation", "unicode_point": "U+1F782", "tier": "god", "auto_generated": true, "generator": "god_tier_v1"}, {"symbol": "🥾", "code": "ng:meta_programming:stagedcomputation_1", "fallback": "[STAGEDCOMPUTATION1]", "category": "meta_programming", "name": "stagedcomputation_1", "description": "Meta-programming primitive: stagedcomputation_1 for staged_computation", "subcategory": "staged_computation", "unicode_point": "U+1F97E", "tier": "god", "auto_generated": true, "generator": "god_tier_v1"}, {"symbol": "⮜", "code": "ng:meta_programming:stagedcomputation_2", "fallback": "[STAGEDCOMPUTATION2]", "category": "meta_programming", "name": "stagedcomputation_2", "description": "Meta-programming primitive: stagedcomputation_2 for staged_computation", "subcategory": "staged_computation", "unicode_point": "U+2B9C", "tier": "god", "auto_generated": true, "generator": "god_tier_v1"}, {"symbol": "🠙", "code": "ng:meta_programming:stagedcomputation_3", "fallback": "[STAGEDCOMPUTATION3]", "category": "meta_programming", "name": "stagedcomputation_3", "description": "Meta-programming primitive: stagedcomputation_3 for staged_computation", "subcategory": "staged_computation", "unicode_point": "U+1F819", "tier": "god", "auto_generated": true, "generator": "god_tier_v1"}, {"symbol": "🟍", "code": "ng:meta_programming:stagedcomputation_4", "fallback": "[STAGEDCOMPUTATION4]", "category": "meta_programming", "name": "stagedcomputation_4", "description": "Meta-programming primitive: stagedcomputation_4 for staged_computation", "subcategory": "staged_computation", "unicode_point": "U+1F7CD", "tier": "god", "auto_generated": true, "generator": "god_tier_v1"}, {"symbol": "⬬", "code": "ng:meta_programming:stagedcomputation_5", "fallback": "[STAGEDCOMPUTATION5]", "category": "meta_programming", "name": "stagedcomputation_5", "description": "Meta-programming primitive: stagedcomputation_5 for staged_computation", "subcategory": "staged_computation", "unicode_point": "U+2B2C", "tier": "god", "auto_generated": true, "generator": "god_tier_v1"}, {"symbol": "⛰", "code": "ng:meta_programming:stagedcomputation_6", "fallback": "[STAGEDCOMPUTATION6]", "category": "meta_programming", "name": "stagedcomputation_6", "description": "Meta-programming primitive: stagedcomputation_6 for staged_computation", "subcategory": "staged_computation", "unicode_point": "U+26F0", "tier": "god", "auto_generated": true, "generator": "god_tier_v1"}, {"symbol": "😛", "code": "ng:meta_programming:stagedcomputation_7", "fallback": "[STAGEDCOMPUTATION7]", "category": "meta_programming", "name": "stagedcomputation_7", "description": "Meta-programming primitive: stagedcomputation_7 for staged_computation", "subcategory": "staged_computation", "unicode_point": "U+1F61B", "tier": "god", "auto_generated": true, "generator": "god_tier_v1"}, {"symbol": "⮘", "code": "ng:meta_programming:stagedcomputation_8", "fallback": "[STAGEDCOMPUTATION8]", "category": "meta_programming", "name": "stagedcomputation_8", "description": "Meta-programming primitive: stagedcomputation_8 for staged_computation", "subcategory": "staged_computation", "unicode_point": "U+2B98", "tier": "god", "auto_generated": true, "generator": "god_tier_v1"}, {"symbol": "🔤", "code": "ng:meta_programming:stagedcomputation_9", "fallback": "[STAGEDCOMPUTATION9]", "category": "meta_programming", "name": "stagedcomputation_9", "description": "Meta-programming primitive: stagedcomputation_9 for staged_computation", "subcategory": "staged_computation", "unicode_point": "U+1F524", "tier": "god", "auto_generated": true, "generator": "god_tier_v1"}, {"symbol": "⦛", "code": "ng:meta_programming:partialevaluation", "fallback": "[PARTIALEVALUATION]", "category": "meta_programming", "name": "partialevaluation", "description": "Meta-programming primitive: partialevaluation for partial_evaluation", "subcategory": "partial_evaluation", "unicode_point": "U+299B", "tier": "god", "auto_generated": true, "generator": "god_tier_v1"}, {"symbol": "⣘", "code": "ng:meta_programming:partialevaluation_1", "fallback": "[PARTIALEVALUATION1]", "category": "meta_programming", "name": "partialevaluation_1", "description": "Meta-programming primitive: partialevaluation_1 for partial_evaluation", "subcategory": "partial_evaluation", "unicode_point": "U+28D8", "tier": "god", "auto_generated": true, "generator": "god_tier_v1"}, {"symbol": "👢", "code": "ng:meta_programming:partialevaluation_2", "fallback": "[PARTIALEVALUATION2]", "category": "meta_programming", "name": "partialevaluation_2", "description": "Meta-programming primitive: partialevaluation_2 for partial_evaluation", "subcategory": "partial_evaluation", "unicode_point": "U+1F462", "tier": "god", "auto_generated": true, "generator": "god_tier_v1"}, {"symbol": "🛈", "code": "ng:meta_programming:partialevaluation_3", "fallback": "[PARTIALEVALUATION3]", "category": "meta_programming", "name": "partialevaluation_3", "description": "Meta-programming primitive: partialevaluation_3 for partial_evaluation", "subcategory": "partial_evaluation", "unicode_point": "U+1F6C8", "tier": "god", "auto_generated": true, "generator": "god_tier_v1"}, {"symbol": "🛰", "code": "ng:meta_programming:partialevaluation_4", "fallback": "[PARTIALEVALUATION4]", "category": "meta_programming", "name": "partialevaluation_4", "description": "Meta-programming primitive: partialevaluation_4 for partial_evaluation", "subcategory": "partial_evaluation", "unicode_point": "U+1F6F0", "tier": "god", "auto_generated": true, "generator": "god_tier_v1"}, {"symbol": "⛜", "code": "ng:meta_programming:partialevaluation_5", "fallback": "[PARTIALEVALUATION5]", "category": "meta_programming", "name": "partialevaluation_5", "description": "Meta-programming primitive: partialevaluation_5 for partial_evaluation", "subcategory": "partial_evaluation", "unicode_point": "U+26DC", "tier": "god", "auto_generated": true, "generator": "god_tier_v1"}, {"symbol": "🦅", "code": "ng:meta_programming:partialevaluation_6", "fallback": "[PARTIALEVALUATION6]", "category": "meta_programming", "name": "partialevaluation_6", "description": "Meta-programming primitive: partialevaluation_6 for partial_evaluation", "subcategory": "partial_evaluation", "unicode_point": "U+1F985", "tier": "god", "auto_generated": true, "generator": "god_tier_v1"}, {"symbol": "⣂", "code": "ng:meta_programming:partialevaluation_7", "fallback": "[PARTIALEVALUATION7]", "category": "meta_programming", "name": "partialevaluation_7", "description": "Meta-programming primitive: partialevaluation_7 for partial_evaluation", "subcategory": "partial_evaluation", "unicode_point": "U+28C2", "tier": "god", "auto_generated": true, "generator": "god_tier_v1"}, {"symbol": "⡆", "code": "ng:meta_programming:partialevaluation_8", "fallback": "[PARTIALEVALUATION8]", "category": "meta_programming", "name": "partialevaluation_8", "description": "Meta-programming primitive: partialevaluation_8 for partial_evaluation", "subcategory": "partial_evaluation", "unicode_point": "U+2846", "tier": "god", "auto_generated": true, "generator": "god_tier_v1"}, {"symbol": "🐽", "code": "ng:meta_programming:partialevaluation_9", "fallback": "[PARTIALEVALUATION9]", "category": "meta_programming", "name": "partialevaluation_9", "description": "Meta-programming primitive: partialevaluation_9 for partial_evaluation", "subcategory": "partial_evaluation", "unicode_point": "U+1F43D", "tier": "god", "auto_generated": true, "generator": "god_tier_v1"}, {"symbol": "⨹", "code": "ng:meta_programming:programsynthesis_fn", "fallback": "[PROGRAMSYNTHESISFN]", "category": "meta_programming", "name": "programsynthesis_fn", "description": "Meta-programming primitive: programsynthesis_fn for program_synthesis", "subcategory": "program_synthesis", "unicode_point": "U+2A39", "tier": "god", "auto_generated": true, "generator": "god_tier_v1"}, {"symbol": "⡭", "code": "ng:meta_programming:programsynthesis_op", "fallback": "[PROGRAMSYNTHESISOP]", "category": "meta_programming", "name": "programsynthesis_op", "description": "Meta-programming primitive: programsynthesis_op for program_synthesis", "subcategory": "program_synthesis", "unicode_point": "U+286D", "tier": "god", "auto_generated": true, "generator": "god_tier_v1"}, {"symbol": "⩟", "code": "ng:meta_programming:programsynthesis", "fallback": "[PROGRAMSYNTHESIS]", "category": "meta_programming", "name": "programsynthesis", "description": "Meta-programming primitive: programsynthesis for program_synthesis", "subcategory": "program_synthesis", "unicode_point": "U+2A5F", "tier": "god", "auto_generated": true, "generator": "god_tier_v1"}, {"symbol": "🟥", "code": "ng:meta_programming:programsynthesis_1", "fallback": "[PROGRAMSYNTHESIS1]", "category": "meta_programming", "name": "programsynthesis_1", "description": "Meta-programming primitive: programsynthesis_1 for program_synthesis", "subcategory": "program_synthesis", "unicode_point": "U+1F7E5", "tier": "god", "auto_generated": true, "generator": "god_tier_v1"}, {"symbol": "😢", "code": "ng:meta_programming:programsynthesis_2", "fallback": "[PROGRAMSYNTHESIS2]", "category": "meta_programming", "name": "programsynthesis_2", "description": "Meta-programming primitive: programsynthesis_2 for program_synthesis", "subcategory": "program_synthesis", "unicode_point": "U+1F622", "tier": "god", "auto_generated": true, "generator": "god_tier_v1"}, {"symbol": "🚁", "code": "ng:meta_programming:programsynthesis_3", "fallback": "[PROGRAMSYNTHESIS3]", "category": "meta_programming", "name": "programsynthesis_3", "description": "Meta-programming primitive: programsynthesis_3 for program_synthesis", "subcategory": "program_synthesis", "unicode_point": "U+1F681", "tier": "god", "auto_generated": true, "generator": "god_tier_v1"}, {"symbol": "⦴", "code": "ng:meta_programming:programsynthesis_4", "fallback": "[PROGRAMSYNTHESIS4]", "category": "meta_programming", "name": "programsynthesis_4", "description": "Meta-programming primitive: programsynthesis_4 for program_synthesis", "subcategory": "program_synthesis", "unicode_point": "U+29B4", "tier": "god", "auto_generated": true, "generator": "god_tier_v1"}, {"symbol": "🠭", "code": "ng:meta_programming:programsynthesis_5", "fallback": "[PROGRAMSYNTHESIS5]", "category": "meta_programming", "name": "programsynthesis_5", "description": "Meta-programming primitive: programsynthesis_5 for program_synthesis", "subcategory": "program_synthesis", "unicode_point": "U+1F82D", "tier": "god", "auto_generated": true, "generator": "god_tier_v1"}, {"symbol": "👿", "code": "ng:meta_programming:programsynthesis_6", "fallback": "[PROGRAMSYNTHESIS6]", "category": "meta_programming", "name": "programsynthesis_6", "description": "Meta-programming primitive: programsynthesis_6 for program_synthesis", "subcategory": "program_synthesis", "unicode_point": "U+1F47F", "tier": "god", "auto_generated": true, "generator": "god_tier_v1"}, {"symbol": "⣩", "code": "ng:meta_programming:programsynthesis_7", "fallback": "[PROGRAMSYNTHESIS7]", "category": "meta_programming", "name": "programsynthesis_7", "description": "Meta-programming primitive: programsynthesis_7 for program_synthesis", "subcategory": "program_synthesis", "unicode_point": "U+28E9", "tier": "god", "auto_generated": true, "generator": "god_tier_v1"}, {"symbol": "🤕", "code": "ng:meta_programming:programsynthesis_8", "fallback": "[PROGRAMSYNTHESIS8]", "category": "meta_programming", "name": "programsynthesis_8", "description": "Meta-programming primitive: programsynthesis_8 for program_synthesis", "subcategory": "program_synthesis", "unicode_point": "U+1F915", "tier": "god", "auto_generated": true, "generator": "god_tier_v1"}, {"symbol": "❥", "code": "ng:meta_programming:programsynthesis_9", "fallback": "[PROGRAMSYNTHESIS9]", "category": "meta_programming", "name": "programsynthesis_9", "description": "Meta-programming primitive: programsynthesis_9 for program_synthesis", "subcategory": "program_synthesis", "unicode_point": "U+2765", "tier": "god", "auto_generated": true, "generator": "god_tier_v1"}, {"symbol": "🍓", "code": "ng:meta_programming:programsynthesis_10", "fallback": "[PROGRAMSYNTHESIS10]", "category": "meta_programming", "name": "programsynthesis_10", "description": "Meta-programming primitive: programsynthesis_10 for program_synthesis", "subcategory": "program_synthesis", "unicode_point": "U+1F353", "tier": "god", "auto_generated": true, "generator": "god_tier_v1"}, {"symbol": "⧥", "code": "ng:meta_programming:programsynthesis_11", "fallback": "[PROGRAMSYNTHESIS11]", "category": "meta_programming", "name": "programsynthesis_11", "description": "Meta-programming primitive: programsynthesis_11 for program_synthesis", "subcategory": "program_synthesis", "unicode_point": "U+29E5", "tier": "god", "auto_generated": true, "generator": "god_tier_v1"}, {"symbol": "⭐", "code": "ng:meta_programming:codetransformation", "fallback": "[CODETRANSFORMATION]", "category": "meta_programming", "name": "codetransformation", "description": "Meta-programming primitive: codetransformation for code_transformation", "subcategory": "code_transformation", "unicode_point": "U+2B50", "tier": "god", "auto_generated": true, "generator": "god_tier_v1"}]}