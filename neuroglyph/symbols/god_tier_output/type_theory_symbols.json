{"generation_info": {"domain": "type_theory", "count_requested": 64, "count_generated": 64, "timestamp": "2025-05-25T18:19:29.721686", "generator": "god_tier_v1"}, "symbols": [{"symbol": "🡫", "code": "ng:type_theory:sigma_type_proc", "fallback": "[SIGMATYPEPROC]", "category": "type_theory", "name": "sigma_type_proc", "description": "Type theory construct: sigma_type_proc for dependent_types", "subcategory": "dependent_types", "unicode_point": "U+1F86B", "tier": "god", "auto_generated": true, "generator": "god_tier_v1"}, {"symbol": "⦩", "code": "ng:type_theory:indexed_ctrl", "fallback": "[INDEXEDCTRL]", "category": "type_theory", "name": "indexed_ctrl", "description": "Type theory construct: indexed_ctrl for dependent_types", "subcategory": "dependent_types", "unicode_point": "U+29A9", "tier": "god", "auto_generated": true, "generator": "god_tier_v1"}, {"symbol": "⯚", "code": "ng:type_theory:pi_type_fn", "fallback": "[PITYPEFN]", "category": "type_theory", "name": "pi_type_fn", "description": "Type theory construct: pi_type_fn for dependent_types", "subcategory": "dependent_types", "unicode_point": "U+2BDA", "tier": "god", "auto_generated": true, "generator": "god_tier_v1"}, {"symbol": "⥰", "code": "ng:type_theory:indexed", "fallback": "[INDEXED]", "category": "type_theory", "name": "indexed", "description": "Type theory construct: indexed for dependent_types", "subcategory": "dependent_types", "unicode_point": "U+2970", "tier": "god", "auto_generated": true, "generator": "god_tier_v1"}, {"symbol": "🠡", "code": "ng:type_theory:sigma_type_core", "fallback": "[SIGMATYPECORE]", "category": "type_theory", "name": "sigma_type_core", "description": "Type theory construct: sigma_type_core for dependent_types", "subcategory": "dependent_types", "unicode_point": "U+1F821", "tier": "god", "auto_generated": true, "generator": "god_tier_v1"}, {"symbol": "🍭", "code": "ng:type_theory:dep_pair_op", "fallback": "[DEPPAIROP]", "category": "type_theory", "name": "dep_pair_op", "description": "Type theory construct: dep_pair_op for dependent_types", "subcategory": "dependent_types", "unicode_point": "U+1F36D", "tier": "god", "auto_generated": true, "generator": "god_tier_v1"}, {"symbol": "⫌", "code": "ng:type_theory:pi_type_meta", "fallback": "[PITYPEMETA]", "category": "type_theory", "name": "pi_type_meta", "description": "Type theory construct: pi_type_meta for dependent_types", "subcategory": "dependent_types", "unicode_point": "U+2ACC", "tier": "god", "auto_generated": true, "generator": "god_tier_v1"}, {"symbol": "⦮", "code": "ng:type_theory:indexed_sys", "fallback": "[INDEXEDSYS]", "category": "type_theory", "name": "indexed_sys", "description": "Type theory construct: indexed_sys for dependent_types", "subcategory": "dependent_types", "unicode_point": "U+29AE", "tier": "god", "auto_generated": true, "generator": "god_tier_v1"}, {"symbol": "⤓", "code": "ng:type_theory:lineartypes_ctrl", "fallback": "[LINEARTYPESCTRL]", "category": "type_theory", "name": "lineartypes_ctrl", "description": "Type theory construct: lineartypes_ctrl for linear_types", "subcategory": "linear_types", "unicode_point": "U+2913", "tier": "god", "auto_generated": true, "generator": "god_tier_v1"}, {"symbol": "🔥", "code": "ng:type_theory:lineartypes_proc", "fallback": "[LINEARTYPESPROC]", "category": "type_theory", "name": "lineartypes_proc", "description": "Type theory construct: lineartypes_proc for linear_types", "subcategory": "linear_types", "unicode_point": "U+1F525", "tier": "god", "auto_generated": true, "generator": "god_tier_v1"}, {"symbol": "⧙", "code": "ng:type_theory:lineartypes_op", "fallback": "[LINEARTYPESOP]", "category": "type_theory", "name": "lineartypes_op", "description": "Type theory construct: lineartypes_op for linear_types", "subcategory": "linear_types", "unicode_point": "U+29D9", "tier": "god", "auto_generated": true, "generator": "god_tier_v1"}, {"symbol": "⨈", "code": "ng:type_theory:lineartypes", "fallback": "[LINEARTYPES]", "category": "type_theory", "name": "lineartypes", "description": "Type theory construct: lineartypes for linear_types", "subcategory": "linear_types", "unicode_point": "U+2A08", "tier": "god", "auto_generated": true, "generator": "god_tier_v1"}, {"symbol": "😘", "code": "ng:type_theory:lineartypes_proc_1", "fallback": "[LINEARTYPESPROC1]", "category": "type_theory", "name": "lineartypes_proc_1", "description": "Type theory construct: lineartypes_proc_1 for linear_types", "subcategory": "linear_types", "unicode_point": "U+1F618", "tier": "god", "auto_generated": true, "generator": "god_tier_v1"}, {"symbol": "🞃", "code": "ng:type_theory:lineartypes_meta", "fallback": "[LINEARTYPESMETA]", "category": "type_theory", "name": "lineartypes_meta", "description": "Type theory construct: lineartypes_meta for linear_types", "subcategory": "linear_types", "unicode_point": "U+1F783", "tier": "god", "auto_generated": true, "generator": "god_tier_v1"}, {"symbol": "⠴", "code": "ng:type_theory:lineartypes_fn", "fallback": "[LINEARTYPESFN]", "category": "type_theory", "name": "lineartypes_fn", "description": "Type theory construct: lineartypes_fn for linear_types", "subcategory": "linear_types", "unicode_point": "U+2834", "tier": "god", "auto_generated": true, "generator": "god_tier_v1"}, {"symbol": "⡍", "code": "ng:type_theory:lineartypes_meta_1", "fallback": "[LINEARTYPESMETA1]", "category": "type_theory", "name": "lineartypes_meta_1", "description": "Type theory construct: lineartypes_meta_1 for linear_types", "subcategory": "linear_types", "unicode_point": "U+284D", "tier": "god", "auto_generated": true, "generator": "god_tier_v1"}, {"symbol": "⛾", "code": "ng:type_theory:sessiontypes_op", "fallback": "[SESSIONTYPESOP]", "category": "type_theory", "name": "sessiontypes_op", "description": "Type theory construct: sessiontypes_op for session_types", "subcategory": "session_types", "unicode_point": "U+26FE", "tier": "god", "auto_generated": true, "generator": "god_tier_v1"}, {"symbol": "⠾", "code": "ng:type_theory:sessiontypes_fn", "fallback": "[SESSIONTYPESFN]", "category": "type_theory", "name": "sessiontypes_fn", "description": "Type theory construct: sessiontypes_fn for session_types", "subcategory": "session_types", "unicode_point": "U+283E", "tier": "god", "auto_generated": true, "generator": "god_tier_v1"}, {"symbol": "⬘", "code": "ng:type_theory:sessiontypes_core", "fallback": "[SESSIONTYPESCORE]", "category": "type_theory", "name": "sessiontypes_core", "description": "Type theory construct: sessiontypes_core for session_types", "subcategory": "session_types", "unicode_point": "U+2B18", "tier": "god", "auto_generated": true, "generator": "god_tier_v1"}, {"symbol": "⥩", "code": "ng:type_theory:sessiontypes_ctrl", "fallback": "[SESSIONTYPESCTRL]", "category": "type_theory", "name": "sessiontypes_ctrl", "description": "Type theory construct: sessiontypes_ctrl for session_types", "subcategory": "session_types", "unicode_point": "U+2969", "tier": "god", "auto_generated": true, "generator": "god_tier_v1"}, {"symbol": "⦾", "code": "ng:type_theory:sessiontypes_core_1", "fallback": "[SESSIONTYPESCORE1]", "category": "type_theory", "name": "sessiontypes_core_1", "description": "Type theory construct: sessiontypes_core_1 for session_types", "subcategory": "session_types", "unicode_point": "U+29BE", "tier": "god", "auto_generated": true, "generator": "god_tier_v1"}, {"symbol": "🜣", "code": "ng:type_theory:sessiontypes_op_1", "fallback": "[SESSIONTYPESOP1]", "category": "type_theory", "name": "sessiontypes_op_1", "description": "Type theory construct: sessiontypes_op_1 for session_types", "subcategory": "session_types", "unicode_point": "U+1F723", "tier": "god", "auto_generated": true, "generator": "god_tier_v1"}, {"symbol": "👲", "code": "ng:type_theory:sessiontypes", "fallback": "[SESSIONTYPES]", "category": "type_theory", "name": "sessiontypes", "description": "Type theory construct: sessiontypes for session_types", "subcategory": "session_types", "unicode_point": "U+1F472", "tier": "god", "auto_generated": true, "generator": "god_tier_v1"}, {"symbol": "🤗", "code": "ng:type_theory:sessiontypes_ctrl_1", "fallback": "[SESSIONTYPESCTRL1]", "category": "type_theory", "name": "sessiontypes_ctrl_1", "description": "Type theory construct: sessiontypes_ctrl_1 for session_types", "subcategory": "session_types", "unicode_point": "U+1F917", "tier": "god", "auto_generated": true, "generator": "god_tier_v1"}, {"symbol": "🝟", "code": "ng:type_theory:effecttypes_sys", "fallback": "[EFFECTTYPESSYS]", "category": "type_theory", "name": "effecttypes_sys", "description": "Type theory construct: effecttypes_sys for effect_types", "subcategory": "effect_types", "unicode_point": "U+1F75F", "tier": "god", "auto_generated": true, "generator": "god_tier_v1"}, {"symbol": "𝝯", "code": "ng:type_theory:effecttypes_sys_1", "fallback": "[EFFECTTYPESSYS1]", "category": "type_theory", "name": "effecttypes_sys_1", "description": "Type theory construct: effecttypes_sys_1 for effect_types", "subcategory": "effect_types", "unicode_point": "U+1D76F", "tier": "god", "auto_generated": true, "generator": "god_tier_v1"}, {"symbol": "⯓", "code": "ng:type_theory:effecttypes_sys_2", "fallback": "[EFFECTTYPESSYS2]", "category": "type_theory", "name": "effecttypes_sys_2", "description": "Type theory construct: effecttypes_sys_2 for effect_types", "subcategory": "effect_types", "unicode_point": "U+2BD3", "tier": "god", "auto_generated": true, "generator": "god_tier_v1"}, {"symbol": "🔖", "code": "ng:type_theory:effecttypes_ctrl", "fallback": "[EFFECTTYPESCTRL]", "category": "type_theory", "name": "effecttypes_ctrl", "description": "Type theory construct: effecttypes_ctrl for effect_types", "subcategory": "effect_types", "unicode_point": "U+1F516", "tier": "god", "auto_generated": true, "generator": "god_tier_v1"}, {"symbol": "🝦", "code": "ng:type_theory:effecttypes_proc", "fallback": "[EFFECTTYPESPROC]", "category": "type_theory", "name": "effecttypes_proc", "description": "Type theory construct: effecttypes_proc for effect_types", "subcategory": "effect_types", "unicode_point": "U+1F766", "tier": "god", "auto_generated": true, "generator": "god_tier_v1"}, {"symbol": "🜴", "code": "ng:type_theory:effecttypes_sys_3", "fallback": "[EFFECTTYPESSYS3]", "category": "type_theory", "name": "effecttypes_sys_3", "description": "Type theory construct: effecttypes_sys_3 for effect_types", "subcategory": "effect_types", "unicode_point": "U+1F734", "tier": "god", "auto_generated": true, "generator": "god_tier_v1"}, {"symbol": "✼", "code": "ng:type_theory:effecttypes_core", "fallback": "[EFFECTTYPESCORE]", "category": "type_theory", "name": "effecttypes_core", "description": "Type theory construct: effecttypes_core for effect_types", "subcategory": "effect_types", "unicode_point": "U+273C", "tier": "god", "auto_generated": true, "generator": "god_tier_v1"}, {"symbol": "⦣", "code": "ng:type_theory:effecttypes_sys_4", "fallback": "[EFFECTTYPESSYS4]", "category": "type_theory", "name": "effecttypes_sys_4", "description": "Type theory construct: effecttypes_sys_4 for effect_types", "subcategory": "effect_types", "unicode_point": "U+29A3", "tier": "god", "auto_generated": true, "generator": "god_tier_v1"}, {"symbol": "⥡", "code": "ng:type_theory:refinementtypes", "fallback": "[REFINEMENTTYPES]", "category": "type_theory", "name": "refinementtypes", "description": "Type theory construct: refinementtypes for refinement_types", "subcategory": "refinement_types", "unicode_point": "U+2961", "tier": "god", "auto_generated": true, "generator": "god_tier_v1"}, {"symbol": "🚔", "code": "ng:type_theory:refinementtypes_op", "fallback": "[REFINEMENTTYPESOP]", "category": "type_theory", "name": "refinementtypes_op", "description": "Type theory construct: refinementtypes_op for refinement_types", "subcategory": "refinement_types", "unicode_point": "U+1F694", "tier": "god", "auto_generated": true, "generator": "god_tier_v1"}, {"symbol": "🢣", "code": "ng:type_theory:refinementtypes_1", "fallback": "[REFINEMENTTYPES1]", "category": "type_theory", "name": "refinementtypes_1", "description": "Type theory construct: refinementtypes_1 for refinement_types", "subcategory": "refinement_types", "unicode_point": "U+1F8A3", "tier": "god", "auto_generated": true, "generator": "god_tier_v1"}, {"symbol": "⧎", "code": "ng:type_theory:refinementtypes_sys", "fallback": "[REFINEMENTTYPESSYS]", "category": "type_theory", "name": "refinementtypes_sys", "description": "Type theory construct: refinementtypes_sys for refinement_types", "subcategory": "refinement_types", "unicode_point": "U+29CE", "tier": "god", "auto_generated": true, "generator": "god_tier_v1"}, {"symbol": "❝", "code": "ng:type_theory:refinementtypes_2", "fallback": "[REFINEMENTTYPES2]", "category": "type_theory", "name": "refinementtypes_2", "description": "Type theory construct: refinementtypes_2 for refinement_types", "subcategory": "refinement_types", "unicode_point": "U+275D", "tier": "god", "auto_generated": true, "generator": "god_tier_v1"}, {"symbol": "⡟", "code": "ng:type_theory:refinementtypes_fn", "fallback": "[REFINEMENTTYPESFN]", "category": "type_theory", "name": "refinementtypes_fn", "description": "Type theory construct: refinementtypes_fn for refinement_types", "subcategory": "refinement_types", "unicode_point": "U+285F", "tier": "god", "auto_generated": true, "generator": "god_tier_v1"}, {"symbol": "⣉", "code": "ng:type_theory:refinementtypes_fn_1", "fallback": "[REFINEMENTTYPESFN1]", "category": "type_theory", "name": "refinementtypes_fn_1", "description": "Type theory construct: refinementtypes_fn_1 for refinement_types", "subcategory": "refinement_types", "unicode_point": "U+28C9", "tier": "god", "auto_generated": true, "generator": "god_tier_v1"}, {"symbol": "🠦", "code": "ng:type_theory:refinementtypes_fn_2", "fallback": "[REFINEMENTTYPESFN2]", "category": "type_theory", "name": "refinementtypes_fn_2", "description": "Type theory construct: refinementtypes_fn_2 for refinement_types", "subcategory": "refinement_types", "unicode_point": "U+1F826", "tier": "god", "auto_generated": true, "generator": "god_tier_v1"}, {"symbol": "⦺", "code": "ng:type_theory:intersectiontypes", "fallback": "[INTERSECTIONTYPES]", "category": "type_theory", "name": "intersectiontypes", "description": "Type theory construct: intersectiontypes for intersection_types", "subcategory": "intersection_types", "unicode_point": "U+29BA", "tier": "god", "auto_generated": true, "generator": "god_tier_v1"}, {"symbol": "🧉", "code": "ng:type_theory:intersectiontypes_1", "fallback": "[INTERSECTIONTYPES1]", "category": "type_theory", "name": "intersectiontypes_1", "description": "Type theory construct: intersectiontypes_1 for intersection_types", "subcategory": "intersection_types", "unicode_point": "U+1F9C9", "tier": "god", "auto_generated": true, "generator": "god_tier_v1"}, {"symbol": "⥟", "code": "ng:type_theory:intersectiontypes_2", "fallback": "[INTERSECTIONTYPES2]", "category": "type_theory", "name": "intersectiontypes_2", "description": "Type theory construct: intersectiontypes_2 for intersection_types", "subcategory": "intersection_types", "unicode_point": "U+295F", "tier": "god", "auto_generated": true, "generator": "god_tier_v1"}, {"symbol": "❴", "code": "ng:type_theory:intersectiontypes_3", "fallback": "[INTERSECTIONTYPES3]", "category": "type_theory", "name": "intersectiontypes_3", "description": "Type theory construct: intersectiontypes_3 for intersection_types", "subcategory": "intersection_types", "unicode_point": "U+2774", "tier": "god", "auto_generated": true, "generator": "god_tier_v1"}, {"symbol": "⣿", "code": "ng:type_theory:intersectiontypes_4", "fallback": "[INTERSECTIONTYPES4]", "category": "type_theory", "name": "intersectiontypes_4", "description": "Type theory construct: intersectiontypes_4 for intersection_types", "subcategory": "intersection_types", "unicode_point": "U+28FF", "tier": "god", "auto_generated": true, "generator": "god_tier_v1"}, {"symbol": "⦎", "code": "ng:type_theory:intersectiontypes_5", "fallback": "[INTERSECTIONTYPES5]", "category": "type_theory", "name": "intersectiontypes_5", "description": "Type theory construct: intersectiontypes_5 for intersection_types", "subcategory": "intersection_types", "unicode_point": "U+298E", "tier": "god", "auto_generated": true, "generator": "god_tier_v1"}, {"symbol": "➬", "code": "ng:type_theory:intersectiontypes_6", "fallback": "[INTERSECTIONTYPES6]", "category": "type_theory", "name": "intersectiontypes_6", "description": "Type theory construct: intersectiontypes_6 for intersection_types", "subcategory": "intersection_types", "unicode_point": "U+27AC", "tier": "god", "auto_generated": true, "generator": "god_tier_v1"}, {"symbol": "⧠", "code": "ng:type_theory:intersectiontypes_7", "fallback": "[INTERSECTIONTYPES7]", "category": "type_theory", "name": "intersectiontypes_7", "description": "Type theory construct: intersectiontypes_7 for intersection_types", "subcategory": "intersection_types", "unicode_point": "U+29E0", "tier": "god", "auto_generated": true, "generator": "god_tier_v1"}, {"symbol": "🛰", "code": "ng:type_theory:uniontypes", "fallback": "[UNIONTYPES]", "category": "type_theory", "name": "uniontypes", "description": "Type theory construct: uniontypes for union_types", "subcategory": "union_types", "unicode_point": "U+1F6F0", "tier": "god", "auto_generated": true, "generator": "god_tier_v1"}, {"symbol": "⮯", "code": "ng:type_theory:uniontypes_meta", "fallback": "[UNIONTYPESMETA]", "category": "type_theory", "name": "uniontypes_meta", "description": "Type theory construct: uniontypes_meta for union_types", "subcategory": "union_types", "unicode_point": "U+2BAF", "tier": "god", "auto_generated": true, "generator": "god_tier_v1"}, {"symbol": "✠", "code": "ng:type_theory:uniontypes_ctrl", "fallback": "[UNIONTYPESCTRL]", "category": "type_theory", "name": "uniontypes_ctrl", "description": "Type theory construct: uniontypes_ctrl for union_types", "subcategory": "union_types", "unicode_point": "U+2720", "tier": "god", "auto_generated": true, "generator": "god_tier_v1"}, {"symbol": "💕", "code": "ng:type_theory:uniontypes_1", "fallback": "[UNIONTYPES1]", "category": "type_theory", "name": "uniontypes_1", "description": "Type theory construct: uniontypes_1 for union_types", "subcategory": "union_types", "unicode_point": "U+1F495", "tier": "god", "auto_generated": true, "generator": "god_tier_v1"}, {"symbol": "🢕", "code": "ng:type_theory:uniontypes_meta_1", "fallback": "[UNIONTYPESMETA1]", "category": "type_theory", "name": "uniontypes_meta_1", "description": "Type theory construct: uniontypes_meta_1 for union_types", "subcategory": "union_types", "unicode_point": "U+1F895", "tier": "god", "auto_generated": true, "generator": "god_tier_v1"}, {"symbol": "🥉", "code": "ng:type_theory:uniontypes_fn", "fallback": "[UNIONTYPESFN]", "category": "type_theory", "name": "uniontypes_fn", "description": "Type theory construct: uniontypes_fn for union_types", "subcategory": "union_types", "unicode_point": "U+1F949", "tier": "god", "auto_generated": true, "generator": "god_tier_v1"}, {"symbol": "⦨", "code": "ng:type_theory:uniontypes_op", "fallback": "[UNIONTYPESOP]", "category": "type_theory", "name": "uniontypes_op", "description": "Type theory construct: uniontypes_op for union_types", "subcategory": "union_types", "unicode_point": "U+29A8", "tier": "god", "auto_generated": true, "generator": "god_tier_v1"}, {"symbol": "✏", "code": "ng:type_theory:uniontypes_core", "fallback": "[UNIONTYPESCORE]", "category": "type_theory", "name": "uniontypes_core", "description": "Type theory construct: uniontypes_core for union_types", "subcategory": "union_types", "unicode_point": "U+270F", "tier": "god", "auto_generated": true, "generator": "god_tier_v1"}, {"symbol": "😉", "code": "ng:type_theory:gradualtyping_core", "fallback": "[GRADUALTYPINGCORE]", "category": "type_theory", "name": "gradualtyping_core", "description": "Type theory construct: gradualtyping_core for gradual_typing", "subcategory": "gradual_typing", "unicode_point": "U+1F609", "tier": "god", "auto_generated": true, "generator": "god_tier_v1"}, {"symbol": "⩈", "code": "ng:type_theory:gradualtyping_core_1", "fallback": "[GRADUALTYPINGCORE1]", "category": "type_theory", "name": "gradualtyping_core_1", "description": "Type theory construct: gradualtyping_core_1 for gradual_typing", "subcategory": "gradual_typing", "unicode_point": "U+2A48", "tier": "god", "auto_generated": true, "generator": "god_tier_v1"}, {"symbol": "⢲", "code": "ng:type_theory:gradualtyping_proc", "fallback": "[GRADUALTYPINGPROC]", "category": "type_theory", "name": "gradualtyping_proc", "description": "Type theory construct: gradualtyping_proc for gradual_typing", "subcategory": "gradual_typing", "unicode_point": "U+28B2", "tier": "god", "auto_generated": true, "generator": "god_tier_v1"}, {"symbol": "⡲", "code": "ng:type_theory:gradualtyping_proc_1", "fallback": "[GRADUALTYPINGPROC1]", "category": "type_theory", "name": "gradualtyping_proc_1", "description": "Type theory construct: gradualtyping_proc_1 for gradual_typing", "subcategory": "gradual_typing", "unicode_point": "U+2872", "tier": "god", "auto_generated": true, "generator": "god_tier_v1"}, {"symbol": "⣱", "code": "ng:type_theory:gradualtyping", "fallback": "[GRADUALTYPING]", "category": "type_theory", "name": "gradualtyping", "description": "Type theory construct: gradualtyping for gradual_typing", "subcategory": "gradual_typing", "unicode_point": "U+28F1", "tier": "god", "auto_generated": true, "generator": "god_tier_v1"}, {"symbol": "⥢", "code": "ng:type_theory:gradualtyping_proc_2", "fallback": "[GRADUALTYPINGPROC2]", "category": "type_theory", "name": "gradualtyping_proc_2", "description": "Type theory construct: gradualtyping_proc_2 for gradual_typing", "subcategory": "gradual_typing", "unicode_point": "U+2962", "tier": "god", "auto_generated": true, "generator": "god_tier_v1"}, {"symbol": "🕦", "code": "ng:type_theory:gradualtyping_fn", "fallback": "[GRADUALTYPINGFN]", "category": "type_theory", "name": "gradualtyping_fn", "description": "Type theory construct: gradualtyping_fn for gradual_typing", "subcategory": "gradual_typing", "unicode_point": "U+1F566", "tier": "god", "auto_generated": true, "generator": "god_tier_v1"}, {"symbol": "😱", "code": "ng:type_theory:gradualtyping_meta", "fallback": "[GRADUALTYPINGMETA]", "category": "type_theory", "name": "gradualtyping_meta", "description": "Type theory construct: gradualtyping_meta for gradual_typing", "subcategory": "gradual_typing", "unicode_point": "U+1F631", "tier": "god", "auto_generated": true, "generator": "god_tier_v1"}]}