{"generation_info": {"domain": "formal_verification", "count_requested": 64, "count_generated": 64, "timestamp": "2025-05-25T18:19:29.574126", "generator": "god_tier_v1"}, "symbols": [{"symbol": "😾", "code": "ng:formal_verification:ctl_meta", "fallback": "[CTLMETA]", "category": "formal_verification", "name": "ctl_meta", "description": "Formal verification method: ctl_meta in model_checking", "subcategory": "model_checking", "unicode_point": "U+1F63E", "tier": "god", "auto_generated": true, "generator": "god_tier_v1"}, {"symbol": "🗙", "code": "ng:formal_verification:temporal", "fallback": "[TEMPORAL]", "category": "formal_verification", "name": "temporal", "description": "Formal verification method: temporal in model_checking", "subcategory": "model_checking", "unicode_point": "U+1F5D9", "tier": "god", "auto_generated": true, "generator": "god_tier_v1"}, {"symbol": "🡗", "code": "ng:formal_verification:temporal_1", "fallback": "[TEMPORAL1]", "category": "formal_verification", "name": "temporal_1", "description": "Formal verification method: temporal_1 in model_checking", "subcategory": "model_checking", "unicode_point": "U+1F857", "tier": "god", "auto_generated": true, "generator": "god_tier_v1"}, {"symbol": "🥰", "code": "ng:formal_verification:model_check_fn", "fallback": "[MODELCHECKFN]", "category": "formal_verification", "name": "model_check_fn", "description": "Formal verification method: model_check_fn in model_checking", "subcategory": "model_checking", "unicode_point": "U+1F970", "tier": "god", "auto_generated": true, "generator": "god_tier_v1"}, {"symbol": "➡", "code": "ng:formal_verification:model_check", "fallback": "[MODELCHECK]", "category": "formal_verification", "name": "model_check", "description": "Formal verification method: model_check in model_checking", "subcategory": "model_checking", "unicode_point": "U+27A1", "tier": "god", "auto_generated": true, "generator": "god_tier_v1"}, {"symbol": "⣫", "code": "ng:formal_verification:verify_ctrl", "fallback": "[VERIFYCTRL]", "category": "formal_verification", "name": "verify_ctrl", "description": "Formal verification method: verify_ctrl in model_checking", "subcategory": "model_checking", "unicode_point": "U+28EB", "tier": "god", "auto_generated": true, "generator": "god_tier_v1"}, {"symbol": "⫡", "code": "ng:formal_verification:temporal_sys", "fallback": "[TEMPORALSYS]", "category": "formal_verification", "name": "temporal_sys", "description": "Formal verification method: temporal_sys in model_checking", "subcategory": "model_checking", "unicode_point": "U+2AE1", "tier": "god", "auto_generated": true, "generator": "god_tier_v1"}, {"symbol": "⪩", "code": "ng:formal_verification:theoremproving_ctrl", "fallback": "[THEOREMPROVINGCTRL]", "category": "formal_verification", "name": "theoremproving_ctrl", "description": "Formal verification method: theoremproving_ctrl in theorem_proving", "subcategory": "theorem_proving", "unicode_point": "U+2AA9", "tier": "god", "auto_generated": true, "generator": "god_tier_v1"}, {"symbol": "🝧", "code": "ng:formal_verification:theoremproving_fn", "fallback": "[THEOREMPROVINGFN]", "category": "formal_verification", "name": "theoremproving_fn", "description": "Formal verification method: theoremproving_fn in theorem_proving", "subcategory": "theorem_proving", "unicode_point": "U+1F767", "tier": "god", "auto_generated": true, "generator": "god_tier_v1"}, {"symbol": "⥢", "code": "ng:formal_verification:theoremproving_core", "fallback": "[THEOREMPROVINGCORE]", "category": "formal_verification", "name": "theoremproving_core", "description": "Formal verification method: theoremproving_core in theorem_proving", "subcategory": "theorem_proving", "unicode_point": "U+2962", "tier": "god", "auto_generated": true, "generator": "god_tier_v1"}, {"symbol": "⬪", "code": "ng:formal_verification:theoremproving_meta", "fallback": "[THEOREMPROVINGMETA]", "category": "formal_verification", "name": "theoremproving_meta", "description": "Formal verification method: theoremproving_meta in theorem_proving", "subcategory": "theorem_proving", "unicode_point": "U+2B2A", "tier": "god", "auto_generated": true, "generator": "god_tier_v1"}, {"symbol": "🜈", "code": "ng:formal_verification:theoremproving", "fallback": "[THEOREMPROVING]", "category": "formal_verification", "name": "theoremproving", "description": "Formal verification method: theoremproving in theorem_proving", "subcategory": "theorem_proving", "unicode_point": "U+1F708", "tier": "god", "auto_generated": true, "generator": "god_tier_v1"}, {"symbol": "😡", "code": "ng:formal_verification:theoremproving_op", "fallback": "[THEOREMPROVINGOP]", "category": "formal_verification", "name": "theoremproving_op", "description": "Formal verification method: theoremproving_op in theorem_proving", "subcategory": "theorem_proving", "unicode_point": "U+1F621", "tier": "god", "auto_generated": true, "generator": "god_tier_v1"}, {"symbol": "⩞", "code": "ng:formal_verification:theoremproving_fn_1", "fallback": "[THEOREMPROVINGFN1]", "category": "formal_verification", "name": "theoremproving_fn_1", "description": "Formal verification method: theoremproving_fn_1 in theorem_proving", "subcategory": "theorem_proving", "unicode_point": "U+2A5E", "tier": "god", "auto_generated": true, "generator": "god_tier_v1"}, {"symbol": "🙉", "code": "ng:formal_verification:staticanalysis", "fallback": "[STATICANALYSIS]", "category": "formal_verification", "name": "staticanalysis", "description": "Formal verification method: staticanalysis in static_analysis", "subcategory": "static_analysis", "unicode_point": "U+1F649", "tier": "god", "auto_generated": true, "generator": "god_tier_v1"}, {"symbol": "➛", "code": "ng:formal_verification:staticanalysis_core", "fallback": "[STATICANALYSISCORE]", "category": "formal_verification", "name": "staticanalysis_core", "description": "Formal verification method: staticanalysis_core in static_analysis", "subcategory": "static_analysis", "unicode_point": "U+279B", "tier": "god", "auto_generated": true, "generator": "god_tier_v1"}, {"symbol": "⩖", "code": "ng:formal_verification:staticanalysis_ctrl", "fallback": "[STATICANALYSISCTRL]", "category": "formal_verification", "name": "staticanalysis_ctrl", "description": "Formal verification method: staticanalysis_ctrl in static_analysis", "subcategory": "static_analysis", "unicode_point": "U+2A56", "tier": "god", "auto_generated": true, "generator": "god_tier_v1"}, {"symbol": "⦱", "code": "ng:formal_verification:staticanalysis_fn", "fallback": "[STATICANALYSISFN]", "category": "formal_verification", "name": "staticanalysis_fn", "description": "Formal verification method: staticanalysis_fn in static_analysis", "subcategory": "static_analysis", "unicode_point": "U+29B1", "tier": "god", "auto_generated": true, "generator": "god_tier_v1"}, {"symbol": "🤧", "code": "ng:formal_verification:staticanalysis_1", "fallback": "[STATICANALYSIS1]", "category": "formal_verification", "name": "staticanalysis_1", "description": "Formal verification method: staticanalysis_1 in static_analysis", "subcategory": "static_analysis", "unicode_point": "U+1F927", "tier": "god", "auto_generated": true, "generator": "god_tier_v1"}, {"symbol": "🝚", "code": "ng:formal_verification:staticanalysis_op", "fallback": "[STATICANALYSISOP]", "category": "formal_verification", "name": "staticanalysis_op", "description": "Formal verification method: staticanalysis_op in static_analysis", "subcategory": "static_analysis", "unicode_point": "U+1F75A", "tier": "god", "auto_generated": true, "generator": "god_tier_v1"}, {"symbol": "⛷", "code": "ng:formal_verification:staticanalysis_meta", "fallback": "[STATICANALYSISMETA]", "category": "formal_verification", "name": "staticanalysis_meta", "description": "Formal verification method: staticanalysis_meta in static_analysis", "subcategory": "static_analysis", "unicode_point": "U+26F7", "tier": "god", "auto_generated": true, "generator": "god_tier_v1"}, {"symbol": "🛍", "code": "ng:formal_verification:symbolicexecution", "fallback": "[SYMBOLICEXECUTION]", "category": "formal_verification", "name": "symbolicexecution", "description": "Formal verification method: symbolicexecution in symbolic_execution", "subcategory": "symbolic_execution", "unicode_point": "U+1F6CD", "tier": "god", "auto_generated": true, "generator": "god_tier_v1"}, {"symbol": "⡧", "code": "ng:formal_verification:symbolicexecution_1", "fallback": "[SYMBOLICEXECUTION1]", "category": "formal_verification", "name": "symbolicexecution_1", "description": "Formal verification method: symbolicexecution_1 in symbolic_execution", "subcategory": "symbolic_execution", "unicode_point": "U+2867", "tier": "god", "auto_generated": true, "generator": "god_tier_v1"}, {"symbol": "⤉", "code": "ng:formal_verification:symbolicexecution_2", "fallback": "[SYMBOLICEXECUTION2]", "category": "formal_verification", "name": "symbolicexecution_2", "description": "Formal verification method: symbolicexecution_2 in symbolic_execution", "subcategory": "symbolic_execution", "unicode_point": "U+2909", "tier": "god", "auto_generated": true, "generator": "god_tier_v1"}, {"symbol": "⪁", "code": "ng:formal_verification:symbolicexecution_3", "fallback": "[SYMBOLICEXECUTION3]", "category": "formal_verification", "name": "symbolicexecution_3", "description": "Formal verification method: symbolicexecution_3 in symbolic_execution", "subcategory": "symbolic_execution", "unicode_point": "U+2A81", "tier": "god", "auto_generated": true, "generator": "god_tier_v1"}, {"symbol": "😯", "code": "ng:formal_verification:symbolicexecution_4", "fallback": "[SYMBOLICEXECUTION4]", "category": "formal_verification", "name": "symbolicexecution_4", "description": "Formal verification method: symbolicexecution_4 in symbolic_execution", "subcategory": "symbolic_execution", "unicode_point": "U+1F62F", "tier": "god", "auto_generated": true, "generator": "god_tier_v1"}, {"symbol": "➬", "code": "ng:formal_verification:symbolicexecution_5", "fallback": "[SYMBOLICEXECUTION5]", "category": "formal_verification", "name": "symbolicexecution_5", "description": "Formal verification method: symbolicexecution_5 in symbolic_execution", "subcategory": "symbolic_execution", "unicode_point": "U+27AC", "tier": "god", "auto_generated": true, "generator": "god_tier_v1"}, {"symbol": "🥁", "code": "ng:formal_verification:temporallogic_fn", "fallback": "[TEMPORALLOGICFN]", "category": "formal_verification", "name": "temporallogic_fn", "description": "Formal verification method: temporallogic_fn in temporal_logic", "subcategory": "temporal_logic", "unicode_point": "U+1F941", "tier": "god", "auto_generated": true, "generator": "god_tier_v1"}, {"symbol": "🌫", "code": "ng:formal_verification:temporallogic_proc", "fallback": "[TEMPORALLOGICPROC]", "category": "formal_verification", "name": "temporallogic_proc", "description": "Formal verification method: temporallogic_proc in temporal_logic", "subcategory": "temporal_logic", "unicode_point": "U+1F32B", "tier": "god", "auto_generated": true, "generator": "god_tier_v1"}, {"symbol": "⨤", "code": "ng:formal_verification:temporallogic_fn_1", "fallback": "[TEMPORALLOGICFN1]", "category": "formal_verification", "name": "temporallogic_fn_1", "description": "Formal verification method: temporallogic_fn_1 in temporal_logic", "subcategory": "temporal_logic", "unicode_point": "U+2A24", "tier": "god", "auto_generated": true, "generator": "god_tier_v1"}, {"symbol": "😈", "code": "ng:formal_verification:temporallogic_ctrl", "fallback": "[TEMPORALLOGICCTRL]", "category": "formal_verification", "name": "temporallogic_ctrl", "description": "Formal verification method: temporallogic_ctrl in temporal_logic", "subcategory": "temporal_logic", "unicode_point": "U+1F608", "tier": "god", "auto_generated": true, "generator": "god_tier_v1"}, {"symbol": "✔", "code": "ng:formal_verification:temporallogic_ctrl_1", "fallback": "[TEMPORALLOGICCTRL1]", "category": "formal_verification", "name": "temporallogic_ctrl_1", "description": "Formal verification method: temporallogic_ctrl_1 in temporal_logic", "subcategory": "temporal_logic", "unicode_point": "U+2714", "tier": "god", "auto_generated": true, "generator": "god_tier_v1"}, {"symbol": "🚘", "code": "ng:formal_verification:temporallogic_meta", "fallback": "[TEMPORALLOGICMETA]", "category": "formal_verification", "name": "temporallogic_meta", "description": "Formal verification method: temporallogic_meta in temporal_logic", "subcategory": "temporal_logic", "unicode_point": "U+1F698", "tier": "god", "auto_generated": true, "generator": "god_tier_v1"}, {"symbol": "🚆", "code": "ng:formal_verification:temporallogic_meta_1", "fallback": "[TEMPORALLOGICMETA1]", "category": "formal_verification", "name": "temporallogic_meta_1", "description": "Formal verification method: temporallogic_meta_1 in temporal_logic", "subcategory": "temporal_logic", "unicode_point": "U+1F686", "tier": "god", "auto_generated": true, "generator": "god_tier_v1"}, {"symbol": "🖒", "code": "ng:formal_verification:hoarelogic_meta", "fallback": "[HOARELOGICMETA]", "category": "formal_verification", "name": "hoarelogic_meta", "description": "Formal verification method: hoarelogic_meta in hoare_logic", "subcategory": "hoare_logic", "unicode_point": "U+1F592", "tier": "god", "auto_generated": true, "generator": "god_tier_v1"}, {"symbol": "⩗", "code": "ng:formal_verification:hoarelogic_proc", "fallback": "[HOARELOGICPROC]", "category": "formal_verification", "name": "hoarelogic_proc", "description": "Formal verification method: hoarelogic_proc in hoare_logic", "subcategory": "hoare_logic", "unicode_point": "U+2A57", "tier": "god", "auto_generated": true, "generator": "god_tier_v1"}, {"symbol": "✏", "code": "ng:formal_verification:hoarelogic", "fallback": "[HOARELOGIC]", "category": "formal_verification", "name": "hoarelogic", "description": "Formal verification method: hoarelogic in hoare_logic", "subcategory": "hoare_logic", "unicode_point": "U+270F", "tier": "god", "auto_generated": true, "generator": "god_tier_v1"}, {"symbol": "⤜", "code": "ng:formal_verification:hoarelogic_ctrl", "fallback": "[HOARELOGICCTRL]", "category": "formal_verification", "name": "hoarelogic_ctrl", "description": "Formal verification method: hoarelogic_ctrl in hoare_logic", "subcategory": "hoare_logic", "unicode_point": "U+291C", "tier": "god", "auto_generated": true, "generator": "god_tier_v1"}, {"symbol": "🠱", "code": "ng:formal_verification:hoarelogic_meta_1", "fallback": "[HOARELOGICMETA1]", "category": "formal_verification", "name": "hoarelogic_meta_1", "description": "Formal verification method: hoarelogic_meta_1 in hoare_logic", "subcategory": "hoare_logic", "unicode_point": "U+1F831", "tier": "god", "auto_generated": true, "generator": "god_tier_v1"}, {"symbol": "⡠", "code": "ng:formal_verification:hoarelogic_meta_2", "fallback": "[HOARELOGICMETA2]", "category": "formal_verification", "name": "hoarelogic_meta_2", "description": "Formal verification method: hoarelogic_meta_2 in hoare_logic", "subcategory": "hoare_logic", "unicode_point": "U+2860", "tier": "god", "auto_generated": true, "generator": "god_tier_v1"}, {"symbol": "🠑", "code": "ng:formal_verification:hoarelogic_sys", "fallback": "[HOARELOGICSYS]", "category": "formal_verification", "name": "hoarelogic_sys", "description": "Formal verification method: hoarelogic_sys in hoare_logic", "subcategory": "hoare_logic", "unicode_point": "U+1F811", "tier": "god", "auto_generated": true, "generator": "god_tier_v1"}, {"symbol": "🝐", "code": "ng:formal_verification:separationlogic_fn", "fallback": "[SEPARATIONLOGICFN]", "category": "formal_verification", "name": "separationlogic_fn", "description": "Formal verification method: separationlogic_fn in separation_logic", "subcategory": "separation_logic", "unicode_point": "U+1F750", "tier": "god", "auto_generated": true, "generator": "god_tier_v1"}, {"symbol": "🜒", "code": "ng:formal_verification:separationlogic_sys", "fallback": "[SEPARATIONLOGICSYS]", "category": "formal_verification", "name": "separationlogic_sys", "description": "Formal verification method: separationlogic_sys in separation_logic", "subcategory": "separation_logic", "unicode_point": "U+1F712", "tier": "god", "auto_generated": true, "generator": "god_tier_v1"}, {"symbol": "🚶", "code": "ng:formal_verification:separationlogic_fn_1", "fallback": "[SEPARATIONLOGICFN1]", "category": "formal_verification", "name": "separationlogic_fn_1", "description": "Formal verification method: separationlogic_fn_1 in separation_logic", "subcategory": "separation_logic", "unicode_point": "U+1F6B6", "tier": "god", "auto_generated": true, "generator": "god_tier_v1"}, {"symbol": "🙋", "code": "ng:formal_verification:separationlogic", "fallback": "[SEPARATIONLOGIC]", "category": "formal_verification", "name": "separationlogic", "description": "Formal verification method: separationlogic in separation_logic", "subcategory": "separation_logic", "unicode_point": "U+1F64B", "tier": "god", "auto_generated": true, "generator": "god_tier_v1"}, {"symbol": "📷", "code": "ng:formal_verification:separationlogic_1", "fallback": "[SEPARATIONLOGIC1]", "category": "formal_verification", "name": "separationlogic_1", "description": "Formal verification method: separationlogic_1 in separation_logic", "subcategory": "separation_logic", "unicode_point": "U+1F4F7", "tier": "god", "auto_generated": true, "generator": "god_tier_v1"}, {"symbol": "🟩", "code": "ng:formal_verification:separationlogic_2", "fallback": "[SEPARATIONLOGIC2]", "category": "formal_verification", "name": "separationlogic_2", "description": "Formal verification method: separationlogic_2 in separation_logic", "subcategory": "separation_logic", "unicode_point": "U+1F7E9", "tier": "god", "auto_generated": true, "generator": "god_tier_v1"}, {"symbol": "⦰", "code": "ng:formal_verification:separationlogic_3", "fallback": "[SEPARATIONLOGIC3]", "category": "formal_verification", "name": "separationlogic_3", "description": "Formal verification method: separationlogic_3 in separation_logic", "subcategory": "separation_logic", "unicode_point": "U+29B0", "tier": "god", "auto_generated": true, "generator": "god_tier_v1"}, {"symbol": "🞰", "code": "ng:formal_verification:separationlogic_4", "fallback": "[SEPARATIONLOGIC4]", "category": "formal_verification", "name": "separationlogic_4", "description": "Formal verification method: separationlogic_4 in separation_logic", "subcategory": "separation_logic", "unicode_point": "U+1F7B0", "tier": "god", "auto_generated": true, "generator": "god_tier_v1"}, {"symbol": "😿", "code": "ng:formal_verification:separationlogic_5", "fallback": "[SEPARATIONLOGIC5]", "category": "formal_verification", "name": "separationlogic_5", "description": "Formal verification method: separationlogic_5 in separation_logic", "subcategory": "separation_logic", "unicode_point": "U+1F63F", "tier": "god", "auto_generated": true, "generator": "god_tier_v1"}, {"symbol": "🐐", "code": "ng:formal_verification:separationlogic_fn_2", "fallback": "[SEPARATIONLOGICFN2]", "category": "formal_verification", "name": "separationlogic_fn_2", "description": "Formal verification method: separationlogic_fn_2 in separation_logic", "subcategory": "separation_logic", "unicode_point": "U+1F410", "tier": "god", "auto_generated": true, "generator": "god_tier_v1"}, {"symbol": "⨐", "code": "ng:formal_verification:separationlogic_6", "fallback": "[SEPARATIONLOGIC6]", "category": "formal_verification", "name": "separationlogic_6", "description": "Formal verification method: separationlogic_6 in separation_logic", "subcategory": "separation_logic", "unicode_point": "U+2A10", "tier": "god", "auto_generated": true, "generator": "god_tier_v1"}, {"symbol": "🟏", "code": "ng:formal_verification:separationlogic_op", "fallback": "[SEPARATIONLOGICOP]", "category": "formal_verification", "name": "separationlogic_op", "description": "Formal verification method: separationlogic_op in separation_logic", "subcategory": "separation_logic", "unicode_point": "U+1F7CF", "tier": "god", "auto_generated": true, "generator": "god_tier_v1"}, {"symbol": "⨲", "code": "ng:formal_verification:separationlogic_fn_3", "fallback": "[SEPARATIONLOGICFN3]", "category": "formal_verification", "name": "separationlogic_fn_3", "description": "Formal verification method: separationlogic_fn_3 in separation_logic", "subcategory": "separation_logic", "unicode_point": "U+2A32", "tier": "god", "auto_generated": true, "generator": "god_tier_v1"}, {"symbol": "🦈", "code": "ng:formal_verification:separationlogic_7", "fallback": "[SEPARATIONLOGIC7]", "category": "formal_verification", "name": "separationlogic_7", "description": "Formal verification method: separationlogic_7 in separation_logic", "subcategory": "separation_logic", "unicode_point": "U+1F988", "tier": "god", "auto_generated": true, "generator": "god_tier_v1"}, {"symbol": "🢂", "code": "ng:formal_verification:separationlogic_fn_4", "fallback": "[SEPARATIONLOGICFN4]", "category": "formal_verification", "name": "separationlogic_fn_4", "description": "Formal verification method: separationlogic_fn_4 in separation_logic", "subcategory": "separation_logic", "unicode_point": "U+1F882", "tier": "god", "auto_generated": true, "generator": "god_tier_v1"}, {"symbol": "🜠", "code": "ng:formal_verification:separationlogic_8", "fallback": "[SEPARATIONLOGIC8]", "category": "formal_verification", "name": "separationlogic_8", "description": "Formal verification method: separationlogic_8 in separation_logic", "subcategory": "separation_logic", "unicode_point": "U+1F720", "tier": "god", "auto_generated": true, "generator": "god_tier_v1"}, {"symbol": "✭", "code": "ng:formal_verification:separationlogic_op_1", "fallback": "[SEPARATIONLOGICOP1]", "category": "formal_verification", "name": "separationlogic_op_1", "description": "Formal verification method: separationlogic_op_1 in separation_logic", "subcategory": "separation_logic", "unicode_point": "U+272D", "tier": "god", "auto_generated": true, "generator": "god_tier_v1"}, {"symbol": "❝", "code": "ng:formal_verification:separationlogic_9", "fallback": "[SEPARATIONLOGIC9]", "category": "formal_verification", "name": "separationlogic_9", "description": "Formal verification method: separationlogic_9 in separation_logic", "subcategory": "separation_logic", "unicode_point": "U+275D", "tier": "god", "auto_generated": true, "generator": "god_tier_v1"}, {"symbol": "⥪", "code": "ng:formal_verification:separationlogic_fn_5", "fallback": "[SEPARATIONLOGICFN5]", "category": "formal_verification", "name": "separationlogic_fn_5", "description": "Formal verification method: separationlogic_fn_5 in separation_logic", "subcategory": "separation_logic", "unicode_point": "U+296A", "tier": "god", "auto_generated": true, "generator": "god_tier_v1"}, {"symbol": "🧮", "code": "ng:formal_verification:separationlogic_fn_6", "fallback": "[SEPARATIONLOGICFN6]", "category": "formal_verification", "name": "separationlogic_fn_6", "description": "Formal verification method: separationlogic_fn_6 in separation_logic", "subcategory": "separation_logic", "unicode_point": "U+1F9EE", "tier": "god", "auto_generated": true, "generator": "god_tier_v1"}, {"symbol": "🛕", "code": "ng:formal_verification:separationlogic_10", "fallback": "[SEPARATIONLOGIC10]", "category": "formal_verification", "name": "separationlogic_10", "description": "Formal verification method: separationlogic_10 in separation_logic", "subcategory": "separation_logic", "unicode_point": "U+1F6D5", "tier": "god", "auto_generated": true, "generator": "god_tier_v1"}, {"symbol": "🚱", "code": "ng:formal_verification:separationlogic_fn_7", "fallback": "[SEPARATIONLOGICFN7]", "category": "formal_verification", "name": "separationlogic_fn_7", "description": "Formal verification method: separationlogic_fn_7 in separation_logic", "subcategory": "separation_logic", "unicode_point": "U+1F6B1", "tier": "god", "auto_generated": true, "generator": "god_tier_v1"}, {"symbol": "✟", "code": "ng:formal_verification:separationlogic_11", "fallback": "[SEPARATIONLOGIC11]", "category": "formal_verification", "name": "separationlogic_11", "description": "Formal verification method: separationlogic_11 in separation_logic", "subcategory": "separation_logic", "unicode_point": "U+271F", "tier": "god", "auto_generated": true, "generator": "god_tier_v1"}]}