{"generation_info": {"domain": "symbolic_ai", "count_requested": 128, "count_generated": 66, "timestamp": "2025-05-25T18:19:27.405039", "generator": "god_tier_v1"}, "symbols": [{"symbol": "🜴", "code": "ng:symbolic_ai:logicalinference_fn", "fallback": "[LOGICALINFERENCEFN]", "category": "symbolic_ai", "name": "logicalinference_fn", "description": "Symbolic AI construct: logicalinference_fn in logical_inference", "subcategory": "logical_inference", "unicode_point": "U+1F734", "tier": "god", "auto_generated": true, "generator": "god_tier_v1"}, {"symbol": "😷", "code": "ng:symbolic_ai:logicalinference_op", "fallback": "[LOGICALINFERENCEOP]", "category": "symbolic_ai", "name": "logicalinference_op", "description": "Symbolic AI construct: logicalinference_op in logical_inference", "subcategory": "logical_inference", "unicode_point": "U+1F637", "tier": "god", "auto_generated": true, "generator": "god_tier_v1"}, {"symbol": "🤗", "code": "ng:symbolic_ai:logicalinference", "fallback": "[LOGICALINFERENCE]", "category": "symbolic_ai", "name": "logicalinference", "description": "Symbolic AI construct: logicalinference in logical_inference", "subcategory": "logical_inference", "unicode_point": "U+1F917", "tier": "god", "auto_generated": true, "generator": "god_tier_v1"}, {"symbol": "⨤", "code": "ng:symbolic_ai:logicalinference_1", "fallback": "[LOGICALINFERENCE1]", "category": "symbolic_ai", "name": "logicalinference_1", "description": "Symbolic AI construct: logicalinference_1 in logical_inference", "subcategory": "logical_inference", "unicode_point": "U+2A24", "tier": "god", "auto_generated": true, "generator": "god_tier_v1"}, {"symbol": "⥍", "code": "ng:symbolic_ai:logicalinference_2", "fallback": "[LOGICALINFERENCE2]", "category": "symbolic_ai", "name": "logicalinference_2", "description": "Symbolic AI construct: logicalinference_2 in logical_inference", "subcategory": "logical_inference", "unicode_point": "U+294D", "tier": "god", "auto_generated": true, "generator": "god_tier_v1"}, {"symbol": "😼", "code": "ng:symbolic_ai:logicalinference_3", "fallback": "[LOGICALINFERENCE3]", "category": "symbolic_ai", "name": "logicalinference_3", "description": "Symbolic AI construct: logicalinference_3 in logical_inference", "subcategory": "logical_inference", "unicode_point": "U+1F63C", "tier": "god", "auto_generated": true, "generator": "god_tier_v1"}, {"symbol": "✹", "code": "ng:symbolic_ai:logicalinference_4", "fallback": "[LOGICALINFERENCE4]", "category": "symbolic_ai", "name": "logicalinference_4", "description": "Symbolic AI construct: logicalinference_4 in logical_inference", "subcategory": "logical_inference", "unicode_point": "U+2739", "tier": "god", "auto_generated": true, "generator": "god_tier_v1"}, {"symbol": "⤓", "code": "ng:symbolic_ai:logicalinference_5", "fallback": "[LOGICALINFERENCE5]", "category": "symbolic_ai", "name": "logicalinference_5", "description": "Symbolic AI construct: logicalinference_5 in logical_inference", "subcategory": "logical_inference", "unicode_point": "U+2913", "tier": "god", "auto_generated": true, "generator": "god_tier_v1"}, {"symbol": "😵", "code": "ng:symbolic_ai:logicalinference_6", "fallback": "[LOGICALINFERENCE6]", "category": "symbolic_ai", "name": "logicalinference_6", "description": "Symbolic AI construct: logicalinference_6 in logical_inference", "subcategory": "logical_inference", "unicode_point": "U+1F635", "tier": "god", "auto_generated": true, "generator": "god_tier_v1"}, {"symbol": "😙", "code": "ng:symbolic_ai:logicalinference_7", "fallback": "[LOGICALINFERENCE7]", "category": "symbolic_ai", "name": "logicalinference_7", "description": "Symbolic AI construct: logicalinference_7 in logical_inference", "subcategory": "logical_inference", "unicode_point": "U+1F619", "tier": "god", "auto_generated": true, "generator": "god_tier_v1"}, {"symbol": "❴", "code": "ng:symbolic_ai:theoremproving", "fallback": "[THEOREMPROVING]", "category": "symbolic_ai", "name": "theoremproving", "description": "Symbolic AI construct: theoremproving in theorem_proving", "subcategory": "theorem_proving", "unicode_point": "U+2774", "tier": "god", "auto_generated": true, "generator": "god_tier_v1"}, {"symbol": "⧜", "code": "ng:symbolic_ai:theoremproving_sys", "fallback": "[THEOREMPROVINGSYS]", "category": "symbolic_ai", "name": "theoremproving_sys", "description": "Symbolic AI construct: theoremproving_sys in theorem_proving", "subcategory": "theorem_proving", "unicode_point": "U+29DC", "tier": "god", "auto_generated": true, "generator": "god_tier_v1"}, {"symbol": "⫴", "code": "ng:symbolic_ai:theoremproving_sys_1", "fallback": "[THEOREMPROVINGSYS1]", "category": "symbolic_ai", "name": "theoremproving_sys_1", "description": "Symbolic AI construct: theoremproving_sys_1 in theorem_proving", "subcategory": "theorem_proving", "unicode_point": "U+2AF4", "tier": "god", "auto_generated": true, "generator": "god_tier_v1"}, {"symbol": "⥳", "code": "ng:symbolic_ai:theoremproving_core", "fallback": "[THEOREMPROVINGCORE]", "category": "symbolic_ai", "name": "theoremproving_core", "description": "Symbolic AI construct: theoremproving_core in theorem_proving", "subcategory": "theorem_proving", "unicode_point": "U+2973", "tier": "god", "auto_generated": true, "generator": "god_tier_v1"}, {"symbol": "⣥", "code": "ng:symbolic_ai:theoremproving_op", "fallback": "[THEOREMPROVINGOP]", "category": "symbolic_ai", "name": "theoremproving_op", "description": "Symbolic AI construct: theoremproving_op in theorem_proving", "subcategory": "theorem_proving", "unicode_point": "U+28E5", "tier": "god", "auto_generated": true, "generator": "god_tier_v1"}, {"symbol": "😕", "code": "ng:symbolic_ai:theoremproving_ctrl", "fallback": "[THEOREMPROVINGCTRL]", "category": "symbolic_ai", "name": "theoremproving_ctrl", "description": "Symbolic AI construct: theoremproving_ctrl in theorem_proving", "subcategory": "theorem_proving", "unicode_point": "U+1F615", "tier": "god", "auto_generated": true, "generator": "god_tier_v1"}, {"symbol": "⩖", "code": "ng:symbolic_ai:theoremproving_sys_2", "fallback": "[THEOREMPROVINGSYS2]", "category": "symbolic_ai", "name": "theoremproving_sys_2", "description": "Symbolic AI construct: theoremproving_sys_2 in theorem_proving", "subcategory": "theorem_proving", "unicode_point": "U+2A56", "tier": "god", "auto_generated": true, "generator": "god_tier_v1"}, {"symbol": "⧥", "code": "ng:symbolic_ai:theoremproving_proc", "fallback": "[THEOREMPROVINGPROC]", "category": "symbolic_ai", "name": "theoremproving_proc", "description": "Symbolic AI construct: theoremproving_proc in theorem_proving", "subcategory": "theorem_proving", "unicode_point": "U+29E5", "tier": "god", "auto_generated": true, "generator": "god_tier_v1"}, {"symbol": "⥛", "code": "ng:symbolic_ai:theoremproving_1", "fallback": "[THEOREMPROVING1]", "category": "symbolic_ai", "name": "theoremproving_1", "description": "Symbolic AI construct: theoremproving_1 in theorem_proving", "subcategory": "theorem_proving", "unicode_point": "U+295B", "tier": "god", "auto_generated": true, "generator": "god_tier_v1"}, {"symbol": "😒", "code": "ng:symbolic_ai:theoremproving_meta", "fallback": "[THEOREMPROVINGMETA]", "category": "symbolic_ai", "name": "theoremproving_meta", "description": "Symbolic AI construct: theoremproving_meta in theorem_proving", "subcategory": "theorem_proving", "unicode_point": "U+1F612", "tier": "god", "auto_generated": true, "generator": "god_tier_v1"}, {"symbol": "🛏", "code": "ng:symbolic_ai:theoremproving_sys_3", "fallback": "[THEOREMPROVINGSYS3]", "category": "symbolic_ai", "name": "theoremproving_sys_3", "description": "Symbolic AI construct: theoremproving_sys_3 in theorem_proving", "subcategory": "theorem_proving", "unicode_point": "U+1F6CF", "tier": "god", "auto_generated": true, "generator": "god_tier_v1"}, {"symbol": "😺", "code": "ng:symbolic_ai:planningalgorithms", "fallback": "[PLANNINGALGORITHMS]", "category": "symbolic_ai", "name": "planningalgorithms", "description": "Symbolic AI construct: planningalgorithms in planning_algorithms", "subcategory": "planning_algorithms", "unicode_point": "U+1F63A", "tier": "god", "auto_generated": true, "generator": "god_tier_v1"}, {"symbol": "⪩", "code": "ng:symbolic_ai:expertsystems_meta", "fallback": "[EXPERTSYSTEMSMETA]", "category": "symbolic_ai", "name": "expertsystems_meta", "description": "Symbolic AI construct: expertsystems_meta in expert_systems", "subcategory": "expert_systems", "unicode_point": "U+2AA9", "tier": "god", "auto_generated": true, "generator": "god_tier_v1"}, {"symbol": "🜔", "code": "ng:symbolic_ai:expertsystems_core", "fallback": "[EXPERTSYSTEMSCORE]", "category": "symbolic_ai", "name": "expertsystems_core", "description": "Symbolic AI construct: expertsystems_core in expert_systems", "subcategory": "expert_systems", "unicode_point": "U+1F714", "tier": "god", "auto_generated": true, "generator": "god_tier_v1"}, {"symbol": "🎤", "code": "ng:symbolic_ai:expertsystems_proc", "fallback": "[EXPERTSYSTEMSPROC]", "category": "symbolic_ai", "name": "expertsystems_proc", "description": "Symbolic AI construct: expertsystems_proc in expert_systems", "subcategory": "expert_systems", "unicode_point": "U+1F3A4", "tier": "god", "auto_generated": true, "generator": "god_tier_v1"}, {"symbol": "✃", "code": "ng:symbolic_ai:expertsystems_proc_1", "fallback": "[EXPERTSYSTEMSPROC1]", "category": "symbolic_ai", "name": "expertsystems_proc_1", "description": "Symbolic AI construct: expertsystems_proc_1 in expert_systems", "subcategory": "expert_systems", "unicode_point": "U+2703", "tier": "god", "auto_generated": true, "generator": "god_tier_v1"}, {"symbol": "🛔", "code": "ng:symbolic_ai:expertsystems_ctrl", "fallback": "[EXPERTSYSTEMSCTRL]", "category": "symbolic_ai", "name": "expertsystems_ctrl", "description": "Symbolic AI construct: expertsystems_ctrl in expert_systems", "subcategory": "expert_systems", "unicode_point": "U+1F6D4", "tier": "god", "auto_generated": true, "generator": "god_tier_v1"}, {"symbol": "🧹", "code": "ng:symbolic_ai:expertsystems_core_1", "fallback": "[EXPERTSYSTEMSCORE1]", "category": "symbolic_ai", "name": "expertsystems_core_1", "description": "Symbolic AI construct: expertsystems_core_1 in expert_systems", "subcategory": "expert_systems", "unicode_point": "U+1F9F9", "tier": "god", "auto_generated": true, "generator": "god_tier_v1"}, {"symbol": "⥀", "code": "ng:symbolic_ai:expertsystems_meta_1", "fallback": "[EXPERTSYSTEMSMETA1]", "category": "symbolic_ai", "name": "expertsystems_meta_1", "description": "Symbolic AI construct: expertsystems_meta_1 in expert_systems", "subcategory": "expert_systems", "unicode_point": "U+2940", "tier": "god", "auto_generated": true, "generator": "god_tier_v1"}, {"symbol": "⧎", "code": "ng:symbolic_ai:expertsystems_proc_2", "fallback": "[EXPERTSYSTEMSPROC2]", "category": "symbolic_ai", "name": "expertsystems_proc_2", "description": "Symbolic AI construct: expertsystems_proc_2 in expert_systems", "subcategory": "expert_systems", "unicode_point": "U+29CE", "tier": "god", "auto_generated": true, "generator": "god_tier_v1"}, {"symbol": "🥋", "code": "ng:symbolic_ai:expertsystems_sys", "fallback": "[EXPERTSYSTEMSSYS]", "category": "symbolic_ai", "name": "expertsystems_sys", "description": "Symbolic AI construct: expertsystems_sys in expert_systems", "subcategory": "expert_systems", "unicode_point": "U+1F94B", "tier": "god", "auto_generated": true, "generator": "god_tier_v1"}, {"symbol": "😍", "code": "ng:symbolic_ai:expertsystems_op", "fallback": "[EXPERTSYSTEMSOP]", "category": "symbolic_ai", "name": "expertsystems_op", "description": "Symbolic AI construct: expertsystems_op in expert_systems", "subcategory": "expert_systems", "unicode_point": "U+1F60D", "tier": "god", "auto_generated": true, "generator": "god_tier_v1"}, {"symbol": "⥶", "code": "ng:symbolic_ai:expertsystems_meta_2", "fallback": "[EXPERTSYSTEMSMETA2]", "category": "symbolic_ai", "name": "expertsystems_meta_2", "description": "Symbolic AI construct: expertsystems_meta_2 in expert_systems", "subcategory": "expert_systems", "unicode_point": "U+2976", "tier": "god", "auto_generated": true, "generator": "god_tier_v1"}, {"symbol": "😢", "code": "ng:symbolic_ai:semanticnetworks_op", "fallback": "[SEMANTICNETWORKSOP]", "category": "symbolic_ai", "name": "semanticnetworks_op", "description": "Symbolic AI construct: semanticnetworks_op in semantic_networks", "subcategory": "semantic_networks", "unicode_point": "U+1F622", "tier": "god", "auto_generated": true, "generator": "god_tier_v1"}, {"symbol": "⪘", "code": "ng:symbolic_ai:semanticnetworks", "fallback": "[SEMANTICNETWORKS]", "category": "symbolic_ai", "name": "semanticnetworks", "description": "Symbolic AI construct: semanticnetworks in semantic_networks", "subcategory": "semantic_networks", "unicode_point": "U+2A98", "tier": "god", "auto_generated": true, "generator": "god_tier_v1"}, {"symbol": "🎽", "code": "ng:symbolic_ai:semanticnetworks_1", "fallback": "[SEMANTICNETWORKS1]", "category": "symbolic_ai", "name": "semanticnetworks_1", "description": "Symbolic AI construct: semanticnetworks_1 in semantic_networks", "subcategory": "semantic_networks", "unicode_point": "U+1F3BD", "tier": "god", "auto_generated": true, "generator": "god_tier_v1"}, {"symbol": "🡖", "code": "ng:symbolic_ai:semanticnetworks_fn", "fallback": "[SEMANTICNETWORKSFN]", "category": "symbolic_ai", "name": "semanticnetworks_fn", "description": "Symbolic AI construct: semanticnetworks_fn in semantic_networks", "subcategory": "semantic_networks", "unicode_point": "U+1F856", "tier": "god", "auto_generated": true, "generator": "god_tier_v1"}, {"symbol": "🛲", "code": "ng:symbolic_ai:semanticnetworks_2", "fallback": "[SEMANTICNETWORKS2]", "category": "symbolic_ai", "name": "semanticnetworks_2", "description": "Symbolic AI construct: semanticnetworks_2 in semantic_networks", "subcategory": "semantic_networks", "unicode_point": "U+1F6F2", "tier": "god", "auto_generated": true, "generator": "god_tier_v1"}, {"symbol": "🥺", "code": "ng:symbolic_ai:semanticnetworks_3", "fallback": "[SEMANTICNETWORKS3]", "category": "symbolic_ai", "name": "semanticnetworks_3", "description": "Symbolic AI construct: semanticnetworks_3 in semantic_networks", "subcategory": "semantic_networks", "unicode_point": "U+1F97A", "tier": "god", "auto_generated": true, "generator": "god_tier_v1"}, {"symbol": "🐆", "code": "ng:symbolic_ai:semanticnetworks_4", "fallback": "[SEMANTICNETWORKS4]", "category": "symbolic_ai", "name": "semanticnetworks_4", "description": "Symbolic AI construct: semanticnetworks_4 in semantic_networks", "subcategory": "semantic_networks", "unicode_point": "U+1F406", "tier": "god", "auto_generated": true, "generator": "god_tier_v1"}, {"symbol": "🠒", "code": "ng:symbolic_ai:semanticnetworks_5", "fallback": "[SEMANTICNETWORKS5]", "category": "symbolic_ai", "name": "semanticnetworks_5", "description": "Symbolic AI construct: semanticnetworks_5 in semantic_networks", "subcategory": "semantic_networks", "unicode_point": "U+1F812", "tier": "god", "auto_generated": true, "generator": "god_tier_v1"}, {"symbol": "✌", "code": "ng:symbolic_ai:semanticnetworks_6", "fallback": "[SEMANTICNETWORKS6]", "category": "symbolic_ai", "name": "semanticnetworks_6", "description": "Symbolic AI construct: semanticnetworks_6 in semantic_networks", "subcategory": "semantic_networks", "unicode_point": "U+270C", "tier": "god", "auto_generated": true, "generator": "god_tier_v1"}, {"symbol": "😔", "code": "ng:symbolic_ai:semanticnetworks_7", "fallback": "[SEMANTICNETWORKS7]", "category": "symbolic_ai", "name": "semanticnetworks_7", "description": "Symbolic AI construct: semanticnetworks_7 in semantic_networks", "subcategory": "semantic_networks", "unicode_point": "U+1F614", "tier": "god", "auto_generated": true, "generator": "god_tier_v1"}, {"symbol": "⥒", "code": "ng:symbolic_ai:semanticnetworks_8", "fallback": "[SEMANTICNETWORKS8]", "category": "symbolic_ai", "name": "semanticnetworks_8", "description": "Symbolic AI construct: semanticnetworks_8 in semantic_networks", "subcategory": "semantic_networks", "unicode_point": "U+2952", "tier": "god", "auto_generated": true, "generator": "god_tier_v1"}, {"symbol": "🛆", "code": "ng:symbolic_ai:ontologies_meta", "fallback": "[ONTOLOGIESMETA]", "category": "symbolic_ai", "name": "ontologies_meta", "description": "Symbolic AI construct: ontologies_meta in ontologies", "subcategory": "ontologies", "unicode_point": "U+1F6C6", "tier": "god", "auto_generated": true, "generator": "god_tier_v1"}, {"symbol": "⨶", "code": "ng:symbolic_ai:ontologies_sys", "fallback": "[ONTOLOGIESSYS]", "category": "symbolic_ai", "name": "ontologies_sys", "description": "Symbolic AI construct: ontologies_sys in ontologies", "subcategory": "ontologies", "unicode_point": "U+2A36", "tier": "god", "auto_generated": true, "generator": "god_tier_v1"}, {"symbol": "⬛", "code": "ng:symbolic_ai:ontologies_sys_1", "fallback": "[ONTOLOGIESSYS1]", "category": "symbolic_ai", "name": "ontologies_sys_1", "description": "Symbolic AI construct: ontologies_sys_1 in ontologies", "subcategory": "ontologies", "unicode_point": "U+2B1B", "tier": "god", "auto_generated": true, "generator": "god_tier_v1"}, {"symbol": "⮃", "code": "ng:symbolic_ai:ontologies_fn", "fallback": "[ONTOLOGIESFN]", "category": "symbolic_ai", "name": "ontologies_fn", "description": "Symbolic AI construct: ontologies_fn in ontologies", "subcategory": "ontologies", "unicode_point": "U+2B83", "tier": "god", "auto_generated": true, "generator": "god_tier_v1"}, {"symbol": "➪", "code": "ng:symbolic_ai:ontologies_sys_2", "fallback": "[ONTOLOGIESSYS2]", "category": "symbolic_ai", "name": "ontologies_sys_2", "description": "Symbolic AI construct: ontologies_sys_2 in ontologies", "subcategory": "ontologies", "unicode_point": "U+27AA", "tier": "god", "auto_generated": true, "generator": "god_tier_v1"}, {"symbol": "🞧", "code": "ng:symbolic_ai:ontologies_core", "fallback": "[ONTOLOGIESCORE]", "category": "symbolic_ai", "name": "ontologies_core", "description": "Symbolic AI construct: ontologies_core in ontologies", "subcategory": "ontologies", "unicode_point": "U+1F7A7", "tier": "god", "auto_generated": true, "generator": "god_tier_v1"}, {"symbol": "⢈", "code": "ng:symbolic_ai:ontologies_core_1", "fallback": "[ONTOLOGIESCORE1]", "category": "symbolic_ai", "name": "ontologies_core_1", "description": "Symbolic AI construct: ontologies_core_1 in ontologies", "subcategory": "ontologies", "unicode_point": "U+2888", "tier": "god", "auto_generated": true, "generator": "god_tier_v1"}, {"symbol": "🞻", "code": "ng:symbolic_ai:ontologies_sys_3", "fallback": "[ONTOLOGIESSYS3]", "category": "symbolic_ai", "name": "ontologies_sys_3", "description": "Symbolic AI construct: ontologies_sys_3 in ontologies", "subcategory": "ontologies", "unicode_point": "U+1F7BB", "tier": "god", "auto_generated": true, "generator": "god_tier_v1"}, {"symbol": "😊", "code": "ng:symbolic_ai:ontologies_fn_1", "fallback": "[ONTOLOGIESFN1]", "category": "symbolic_ai", "name": "ontologies_fn_1", "description": "Symbolic AI construct: ontologies_fn_1 in ontologies", "subcategory": "ontologies", "unicode_point": "U+1F60A", "tier": "god", "auto_generated": true, "generator": "god_tier_v1"}, {"symbol": "🗹", "code": "ng:symbolic_ai:ontologies_core_2", "fallback": "[ONTOLOGIESCORE2]", "category": "symbolic_ai", "name": "ontologies_core_2", "description": "Symbolic AI construct: ontologies_core_2 in ontologies", "subcategory": "ontologies", "unicode_point": "U+1F5F9", "tier": "god", "auto_generated": true, "generator": "god_tier_v1"}, {"symbol": "❄", "code": "ng:symbolic_ai:ontologies_sys_4", "fallback": "[ONTOLOGIESSYS4]", "category": "symbolic_ai", "name": "ontologies_sys_4", "description": "Symbolic AI construct: ontologies_sys_4 in ontologies", "subcategory": "ontologies", "unicode_point": "U+2744", "tier": "god", "auto_generated": true, "generator": "god_tier_v1"}, {"symbol": "🝠", "code": "ng:symbolic_ai:descriptionlogics", "fallback": "[DESCRIPTIONLOGICS]", "category": "symbolic_ai", "name": "descriptionlogics", "description": "Symbolic AI construct: descriptionlogics in description_logics", "subcategory": "description_logics", "unicode_point": "U+1F760", "tier": "god", "auto_generated": true, "generator": "god_tier_v1"}, {"symbol": "🞹", "code": "ng:symbolic_ai:descriptionlogics_1", "fallback": "[DESCRIPTIONLOGICS1]", "category": "symbolic_ai", "name": "descriptionlogics_1", "description": "Symbolic AI construct: descriptionlogics_1 in description_logics", "subcategory": "description_logics", "unicode_point": "U+1F7B9", "tier": "god", "auto_generated": true, "generator": "god_tier_v1"}, {"symbol": "📱", "code": "ng:symbolic_ai:descriptionlogics_2", "fallback": "[DESCRIPTIONLOGICS2]", "category": "symbolic_ai", "name": "descriptionlogics_2", "description": "Symbolic AI construct: descriptionlogics_2 in description_logics", "subcategory": "description_logics", "unicode_point": "U+1F4F1", "tier": "god", "auto_generated": true, "generator": "god_tier_v1"}, {"symbol": "🝵", "code": "ng:symbolic_ai:descriptionlogics_3", "fallback": "[DESCRIPTIONLOGICS3]", "category": "symbolic_ai", "name": "descriptionlogics_3", "description": "Symbolic AI construct: descriptionlogics_3 in description_logics", "subcategory": "description_logics", "unicode_point": "U+1F775", "tier": "god", "auto_generated": true, "generator": "god_tier_v1"}, {"symbol": "🍄", "code": "ng:symbolic_ai:descriptionlogics_4", "fallback": "[DESCRIPTIONLOGICS4]", "category": "symbolic_ai", "name": "descriptionlogics_4", "description": "Symbolic AI construct: descriptionlogics_4 in description_logics", "subcategory": "description_logics", "unicode_point": "U+1F344", "tier": "god", "auto_generated": true, "generator": "god_tier_v1"}, {"symbol": "🏗", "code": "ng:symbolic_ai:descriptionlogics_5", "fallback": "[DESCRIPTIONLOGICS5]", "category": "symbolic_ai", "name": "descriptionlogics_5", "description": "Symbolic AI construct: descriptionlogics_5 in description_logics", "subcategory": "description_logics", "unicode_point": "U+1F3D7", "tier": "god", "auto_generated": true, "generator": "god_tier_v1"}, {"symbol": "🜟", "code": "ng:symbolic_ai:descriptionlogics_6", "fallback": "[DESCRIPTIONLOGICS6]", "category": "symbolic_ai", "name": "descriptionlogics_6", "description": "Symbolic AI construct: descriptionlogics_6 in description_logics", "subcategory": "description_logics", "unicode_point": "U+1F71F", "tier": "god", "auto_generated": true, "generator": "god_tier_v1"}, {"symbol": "⥡", "code": "ng:symbolic_ai:descriptionlogics_7", "fallback": "[DESCRIPTIONLOGICS7]", "category": "symbolic_ai", "name": "descriptionlogics_7", "description": "Symbolic AI construct: descriptionlogics_7 in description_logics", "subcategory": "description_logics", "unicode_point": "U+2961", "tier": "god", "auto_generated": true, "generator": "god_tier_v1"}, {"symbol": "🦹", "code": "ng:symbolic_ai:descriptionlogics_8", "fallback": "[DESCRIPTIONLOGICS8]", "category": "symbolic_ai", "name": "descriptionlogics_8", "description": "Symbolic AI construct: descriptionlogics_8 in description_logics", "subcategory": "description_logics", "unicode_point": "U+1F9B9", "tier": "god", "auto_generated": true, "generator": "god_tier_v1"}, {"symbol": "⯯", "code": "ng:symbolic_ai:automatedreasoning", "fallback": "[AUTOMATEDREASONING]", "category": "symbolic_ai", "name": "automatedreasoning", "description": "Symbolic AI construct: automatedreasoning in automated_reasoning", "subcategory": "automated_reasoning", "unicode_point": "U+2BEF", "tier": "god", "auto_generated": true, "generator": "god_tier_v1"}, {"symbol": "🦬", "code": "ng:symbolic_ai:symbolicregression", "fallback": "[SYMBOLICREGRESSION]", "category": "symbolic_ai", "name": "symbolicregression", "description": "Symbolic AI construct: symbolicregression in symbolic_regression", "subcategory": "symbolic_regression", "unicode_point": "U+1F9AC", "tier": "god", "auto_generated": true, "generator": "god_tier_v1"}]}