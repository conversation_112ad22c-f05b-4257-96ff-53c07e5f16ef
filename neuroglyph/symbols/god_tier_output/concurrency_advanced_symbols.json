{"generation_info": {"domain": "concurrency_advanced", "count_requested": 128, "count_generated": 128, "timestamp": "2025-05-25T18:19:29.798086", "generator": "god_tier_v1"}, "symbols": [{"symbol": "⧣", "code": "ng:concurrency_advanced:spawn", "fallback": "[SPAWN]", "category": "concurrency_advanced", "name": "spawn", "description": "Advanced concurrency primitive: spawn in actor_model", "subcategory": "actor_model", "unicode_point": "U+29E3", "tier": "god", "auto_generated": true, "generator": "god_tier_v1"}, {"symbol": "🞼", "code": "ng:concurrency_advanced:message_ctrl", "fallback": "[MESSAGECTRL]", "category": "concurrency_advanced", "name": "message_ctrl", "description": "Advanced concurrency primitive: message_ctrl in actor_model", "subcategory": "actor_model", "unicode_point": "U+1F7BC", "tier": "god", "auto_generated": true, "generator": "god_tier_v1"}, {"symbol": "🛪", "code": "ng:concurrency_advanced:spawn_op", "fallback": "[SPAWNOP]", "category": "concurrency_advanced", "name": "spawn_op", "description": "Advanced concurrency primitive: spawn_op in actor_model", "subcategory": "actor_model", "unicode_point": "U+1F6EA", "tier": "god", "auto_generated": true, "generator": "god_tier_v1"}, {"symbol": "🞺", "code": "ng:concurrency_advanced:mailbox_core", "fallback": "[MAILBOXCORE]", "category": "concurrency_advanced", "name": "mailbox_core", "description": "Advanced concurrency primitive: mailbox_core in actor_model", "subcategory": "actor_model", "unicode_point": "U+1F7BA", "tier": "god", "auto_generated": true, "generator": "god_tier_v1"}, {"symbol": "🜛", "code": "ng:concurrency_advanced:actor_meta", "fallback": "[ACTORMETA]", "category": "concurrency_advanced", "name": "actor_meta", "description": "Advanced concurrency primitive: actor_meta in actor_model", "subcategory": "actor_model", "unicode_point": "U+1F71B", "tier": "god", "auto_generated": true, "generator": "god_tier_v1"}, {"symbol": "🤅", "code": "ng:concurrency_advanced:spawn_1", "fallback": "[SPAWN1]", "category": "concurrency_advanced", "name": "spawn_1", "description": "Advanced concurrency primitive: spawn_1 in actor_model", "subcategory": "actor_model", "unicode_point": "U+1F905", "tier": "god", "auto_generated": true, "generator": "god_tier_v1"}, {"symbol": "⭟", "code": "ng:concurrency_advanced:actor_meta_1", "fallback": "[ACTORMETA1]", "category": "concurrency_advanced", "name": "actor_meta_1", "description": "Advanced concurrency primitive: actor_meta_1 in actor_model", "subcategory": "actor_model", "unicode_point": "U+2B5F", "tier": "god", "auto_generated": true, "generator": "god_tier_v1"}, {"symbol": "🥵", "code": "ng:concurrency_advanced:message_sys", "fallback": "[MESSAGESYS]", "category": "concurrency_advanced", "name": "message_sys", "description": "Advanced concurrency primitive: message_sys in actor_model", "subcategory": "actor_model", "unicode_point": "U+1F975", "tier": "god", "auto_generated": true, "generator": "god_tier_v1"}, {"symbol": "🖝", "code": "ng:concurrency_advanced:message_op", "fallback": "[MESSAGEOP]", "category": "concurrency_advanced", "name": "message_op", "description": "Advanced concurrency primitive: message_op in actor_model", "subcategory": "actor_model", "unicode_point": "U+1F59D", "tier": "god", "auto_generated": true, "generator": "god_tier_v1"}, {"symbol": "⦖", "code": "ng:concurrency_advanced:spawn_ctrl", "fallback": "[SPAWNCTRL]", "category": "concurrency_advanced", "name": "spawn_ctrl", "description": "Advanced concurrency primitive: spawn_ctrl in actor_model", "subcategory": "actor_model", "unicode_point": "U+2996", "tier": "god", "auto_generated": true, "generator": "god_tier_v1"}, {"symbol": "⥣", "code": "ng:concurrency_advanced:mailbox_fn", "fallback": "[MAILBOXFN]", "category": "concurrency_advanced", "name": "mailbox_fn", "description": "Advanced concurrency primitive: mailbox_fn in actor_model", "subcategory": "actor_model", "unicode_point": "U+2963", "tier": "god", "auto_generated": true, "generator": "god_tier_v1"}, {"symbol": "⧷", "code": "ng:concurrency_advanced:message_op_1", "fallback": "[MESSAGEOP1]", "category": "concurrency_advanced", "name": "message_op_1", "description": "Advanced concurrency primitive: message_op_1 in actor_model", "subcategory": "actor_model", "unicode_point": "U+29F7", "tier": "god", "auto_generated": true, "generator": "god_tier_v1"}, {"symbol": "⥶", "code": "ng:concurrency_advanced:spawn_proc", "fallback": "[SPAWNPROC]", "category": "concurrency_advanced", "name": "spawn_proc", "description": "Advanced concurrency primitive: spawn_proc in actor_model", "subcategory": "actor_model", "unicode_point": "U+2976", "tier": "god", "auto_generated": true, "generator": "god_tier_v1"}, {"symbol": "🙋", "code": "ng:concurrency_advanced:actor_sys", "fallback": "[ACTORSYS]", "category": "concurrency_advanced", "name": "actor_sys", "description": "Advanced concurrency primitive: actor_sys in actor_model", "subcategory": "actor_model", "unicode_point": "U+1F64B", "tier": "god", "auto_generated": true, "generator": "god_tier_v1"}, {"symbol": "⠁", "code": "ng:concurrency_advanced:cspchannels", "fallback": "[CSPCHANNELS]", "category": "concurrency_advanced", "name": "cspchannels", "description": "Advanced concurrency primitive: cspchannels in csp_channels", "subcategory": "csp_channels", "unicode_point": "U+2801", "tier": "god", "auto_generated": true, "generator": "god_tier_v1"}, {"symbol": "⥢", "code": "ng:concurrency_advanced:cspchannels_proc", "fallback": "[CSPCHANNELSPROC]", "category": "concurrency_advanced", "name": "cspchannels_proc", "description": "Advanced concurrency primitive: cspchannels_proc in csp_channels", "subcategory": "csp_channels", "unicode_point": "U+2962", "tier": "god", "auto_generated": true, "generator": "god_tier_v1"}, {"symbol": "⭚", "code": "ng:concurrency_advanced:cspchannels_fn", "fallback": "[CSPCHANNELSFN]", "category": "concurrency_advanced", "name": "cspchannels_fn", "description": "Advanced concurrency primitive: cspchannels_fn in csp_channels", "subcategory": "csp_channels", "unicode_point": "U+2B5A", "tier": "god", "auto_generated": true, "generator": "god_tier_v1"}, {"symbol": "⦓", "code": "ng:concurrency_advanced:cspchannels_fn_1", "fallback": "[CSPCHANNELSFN1]", "category": "concurrency_advanced", "name": "cspchannels_fn_1", "description": "Advanced concurrency primitive: cspchannels_fn_1 in csp_channels", "subcategory": "csp_channels", "unicode_point": "U+2993", "tier": "god", "auto_generated": true, "generator": "god_tier_v1"}, {"symbol": "👾", "code": "ng:concurrency_advanced:cspchannels_meta", "fallback": "[CSPCHANNELSMETA]", "category": "concurrency_advanced", "name": "cspchannels_meta", "description": "Advanced concurrency primitive: cspchannels_meta in csp_channels", "subcategory": "csp_channels", "unicode_point": "U+1F47E", "tier": "god", "auto_generated": true, "generator": "god_tier_v1"}, {"symbol": "⧖", "code": "ng:concurrency_advanced:cspchannels_op", "fallback": "[CSPCHANNELSOP]", "category": "concurrency_advanced", "name": "cspchannels_op", "description": "Advanced concurrency primitive: cspchannels_op in csp_channels", "subcategory": "csp_channels", "unicode_point": "U+29D6", "tier": "god", "auto_generated": true, "generator": "god_tier_v1"}, {"symbol": "⧌", "code": "ng:concurrency_advanced:cspchannels_proc_1", "fallback": "[CSPCHANNELSPROC1]", "category": "concurrency_advanced", "name": "cspchannels_proc_1", "description": "Advanced concurrency primitive: cspchannels_proc_1 in csp_channels", "subcategory": "csp_channels", "unicode_point": "U+29CC", "tier": "god", "auto_generated": true, "generator": "god_tier_v1"}, {"symbol": "😟", "code": "ng:concurrency_advanced:cspchannels_fn_2", "fallback": "[CSPCHANNELSFN2]", "category": "concurrency_advanced", "name": "cspchannels_fn_2", "description": "Advanced concurrency primitive: cspchannels_fn_2 in csp_channels", "subcategory": "csp_channels", "unicode_point": "U+1F61F", "tier": "god", "auto_generated": true, "generator": "god_tier_v1"}, {"symbol": "🙏", "code": "ng:concurrency_advanced:cspchannels_ctrl", "fallback": "[CSPCHANNELSCTRL]", "category": "concurrency_advanced", "name": "cspchannels_ctrl", "description": "Advanced concurrency primitive: cspchannels_ctrl in csp_channels", "subcategory": "csp_channels", "unicode_point": "U+1F64F", "tier": "god", "auto_generated": true, "generator": "god_tier_v1"}, {"symbol": "🟓", "code": "ng:concurrency_advanced:cspchannels_op_1", "fallback": "[CSPCHANNELSOP1]", "category": "concurrency_advanced", "name": "cspchannels_op_1", "description": "Advanced concurrency primitive: cspchannels_op_1 in csp_channels", "subcategory": "csp_channels", "unicode_point": "U+1F7D3", "tier": "god", "auto_generated": true, "generator": "god_tier_v1"}, {"symbol": "⣋", "code": "ng:concurrency_advanced:cspchannels_proc_2", "fallback": "[CSPCHANNELSPROC2]", "category": "concurrency_advanced", "name": "cspchannels_proc_2", "description": "Advanced concurrency primitive: cspchannels_proc_2 in csp_channels", "subcategory": "csp_channels", "unicode_point": "U+28CB", "tier": "god", "auto_generated": true, "generator": "god_tier_v1"}, {"symbol": "🝮", "code": "ng:concurrency_advanced:cspchannels_sys", "fallback": "[CSPCHANNELSSYS]", "category": "concurrency_advanced", "name": "cspchannels_sys", "description": "Advanced concurrency primitive: cspchannels_sys in csp_channels", "subcategory": "csp_channels", "unicode_point": "U+1F76E", "tier": "god", "auto_generated": true, "generator": "god_tier_v1"}, {"symbol": "😢", "code": "ng:concurrency_advanced:cspchannels_ctrl_1", "fallback": "[CSPCHANNELSCTRL1]", "category": "concurrency_advanced", "name": "cspchannels_ctrl_1", "description": "Advanced concurrency primitive: cspchannels_ctrl_1 in csp_channels", "subcategory": "csp_channels", "unicode_point": "U+1F622", "tier": "god", "auto_generated": true, "generator": "god_tier_v1"}, {"symbol": "🏋", "code": "ng:concurrency_advanced:cspchannels_sys_1", "fallback": "[CSPCHANNELSSYS1]", "category": "concurrency_advanced", "name": "cspchannels_sys_1", "description": "Advanced concurrency primitive: cspchannels_sys_1 in csp_channels", "subcategory": "csp_channels", "unicode_point": "U+1F3CB", "tier": "god", "auto_generated": true, "generator": "god_tier_v1"}, {"symbol": "🥹", "code": "ng:concurrency_advanced:lockfreealgorithms", "fallback": "[LOCKFREEALGORITHMS]", "category": "concurrency_advanced", "name": "lockfreealgorithms", "description": "Advanced concurrency primitive: lockfreealgorithms in lock_free_algorithms", "subcategory": "lock_free_algorithms", "unicode_point": "U+1F979", "tier": "god", "auto_generated": true, "generator": "god_tier_v1"}, {"symbol": "🤦", "code": "ng:concurrency_advanced:waitfreealgorithms", "fallback": "[WAITFREEALGORITHMS]", "category": "concurrency_advanced", "name": "waitfreealgorithms", "description": "Advanced concurrency primitive: waitfreealgorithms in wait_free_algorithms", "subcategory": "wait_free_algorithms", "unicode_point": "U+1F926", "tier": "god", "auto_generated": true, "generator": "god_tier_v1"}, {"symbol": "⡬", "code": "ng:concurrency_advanced:memoryordering_proc", "fallback": "[MEMORYORDERINGPROC]", "category": "concurrency_advanced", "name": "memoryordering_proc", "description": "Advanced concurrency primitive: memoryordering_proc in memory_ordering", "subcategory": "memory_ordering", "unicode_point": "U+286C", "tier": "god", "auto_generated": true, "generator": "god_tier_v1"}, {"symbol": "🦥", "code": "ng:concurrency_advanced:memoryordering_ctrl", "fallback": "[MEMORYORDERINGCTRL]", "category": "concurrency_advanced", "name": "memoryordering_ctrl", "description": "Advanced concurrency primitive: memoryordering_ctrl in memory_ordering", "subcategory": "memory_ordering", "unicode_point": "U+1F9A5", "tier": "god", "auto_generated": true, "generator": "god_tier_v1"}, {"symbol": "🞵", "code": "ng:concurrency_advanced:memoryordering_op", "fallback": "[MEMORYORDERINGOP]", "category": "concurrency_advanced", "name": "memoryordering_op", "description": "Advanced concurrency primitive: memoryordering_op in memory_ordering", "subcategory": "memory_ordering", "unicode_point": "U+1F7B5", "tier": "god", "auto_generated": true, "generator": "god_tier_v1"}, {"symbol": "🝏", "code": "ng:concurrency_advanced:memoryordering_op_1", "fallback": "[MEMORYORDERINGOP1]", "category": "concurrency_advanced", "name": "memoryordering_op_1", "description": "Advanced concurrency primitive: memoryordering_op_1 in memory_ordering", "subcategory": "memory_ordering", "unicode_point": "U+1F74F", "tier": "god", "auto_generated": true, "generator": "god_tier_v1"}, {"symbol": "⦧", "code": "ng:concurrency_advanced:memoryordering_op_2", "fallback": "[MEMORYORDERINGOP2]", "category": "concurrency_advanced", "name": "memoryordering_op_2", "description": "Advanced concurrency primitive: memoryordering_op_2 in memory_ordering", "subcategory": "memory_ordering", "unicode_point": "U+29A7", "tier": "god", "auto_generated": true, "generator": "god_tier_v1"}, {"symbol": "⨷", "code": "ng:concurrency_advanced:memoryordering_fn", "fallback": "[MEMORYORDERINGFN]", "category": "concurrency_advanced", "name": "memoryordering_fn", "description": "Advanced concurrency primitive: memoryordering_fn in memory_ordering", "subcategory": "memory_ordering", "unicode_point": "U+2A37", "tier": "god", "auto_generated": true, "generator": "god_tier_v1"}, {"symbol": "⣿", "code": "ng:concurrency_advanced:memoryordering_meta", "fallback": "[MEMORYORDERINGMETA]", "category": "concurrency_advanced", "name": "memoryordering_meta", "description": "Advanced concurrency primitive: memoryordering_meta in memory_ordering", "subcategory": "memory_ordering", "unicode_point": "U+28FF", "tier": "god", "auto_generated": true, "generator": "god_tier_v1"}, {"symbol": "🡒", "code": "ng:concurrency_advanced:memoryordering_fn_1", "fallback": "[MEMORYORDERINGFN1]", "category": "concurrency_advanced", "name": "memoryordering_fn_1", "description": "Advanced concurrency primitive: memoryordering_fn_1 in memory_ordering", "subcategory": "memory_ordering", "unicode_point": "U+1F852", "tier": "god", "auto_generated": true, "generator": "god_tier_v1"}, {"symbol": "🚺", "code": "ng:concurrency_advanced:memoryordering", "fallback": "[MEMORYORDERING]", "category": "concurrency_advanced", "name": "memoryordering", "description": "Advanced concurrency primitive: memoryordering in memory_ordering", "subcategory": "memory_ordering", "unicode_point": "U+1F6BA", "tier": "god", "auto_generated": true, "generator": "god_tier_v1"}, {"symbol": "🕵", "code": "ng:concurrency_advanced:memoryordering_sys", "fallback": "[MEMORYORDERINGSYS]", "category": "concurrency_advanced", "name": "memoryordering_sys", "description": "Advanced concurrency primitive: memoryordering_sys in memory_ordering", "subcategory": "memory_ordering", "unicode_point": "U+1F575", "tier": "god", "auto_generated": true, "generator": "god_tier_v1"}, {"symbol": "😄", "code": "ng:concurrency_advanced:memoryordering_op_3", "fallback": "[MEMORYORDERINGOP3]", "category": "concurrency_advanced", "name": "memoryordering_op_3", "description": "Advanced concurrency primitive: memoryordering_op_3 in memory_ordering", "subcategory": "memory_ordering", "unicode_point": "U+1F604", "tier": "god", "auto_generated": true, "generator": "god_tier_v1"}, {"symbol": "✁", "code": "ng:concurrency_advanced:memoryordering_core", "fallback": "[MEMORYORDERINGCORE]", "category": "concurrency_advanced", "name": "memoryordering_core", "description": "Advanced concurrency primitive: memoryordering_core in memory_ordering", "subcategory": "memory_ordering", "unicode_point": "U+2701", "tier": "god", "auto_generated": true, "generator": "god_tier_v1"}, {"symbol": "⡃", "code": "ng:concurrency_advanced:memoryordering_op_4", "fallback": "[MEMORYORDERINGOP4]", "category": "concurrency_advanced", "name": "memoryordering_op_4", "description": "Advanced concurrency primitive: memoryordering_op_4 in memory_ordering", "subcategory": "memory_ordering", "unicode_point": "U+2843", "tier": "god", "auto_generated": true, "generator": "god_tier_v1"}, {"symbol": "⯎", "code": "ng:concurrency_advanced:memoryordering_sys_1", "fallback": "[MEMORYORDERINGSYS1]", "category": "concurrency_advanced", "name": "memoryordering_sys_1", "description": "Advanced concurrency primitive: memoryordering_sys_1 in memory_ordering", "subcategory": "memory_ordering", "unicode_point": "U+2BCE", "tier": "god", "auto_generated": true, "generator": "god_tier_v1"}, {"symbol": "🛓", "code": "ng:concurrency_advanced:atomicoperations_op", "fallback": "[ATOMICOPERATIONSOP]", "category": "concurrency_advanced", "name": "atomicoperations_op", "description": "Advanced concurrency primitive: atomicoperations_op in atomic_operations", "subcategory": "atomic_operations", "unicode_point": "U+1F6D3", "tier": "god", "auto_generated": true, "generator": "god_tier_v1"}, {"symbol": "⦔", "code": "ng:concurrency_advanced:atomicoperations_fn", "fallback": "[ATOMICOPERATIONSFN]", "category": "concurrency_advanced", "name": "atomicoperations_fn", "description": "Advanced concurrency primitive: atomicoperations_fn in atomic_operations", "subcategory": "atomic_operations", "unicode_point": "U+2994", "tier": "god", "auto_generated": true, "generator": "god_tier_v1"}, {"symbol": "⧜", "code": "ng:concurrency_advanced:atomicoperations", "fallback": "[ATOMICOPERATIONS]", "category": "concurrency_advanced", "name": "atomicoperations", "description": "Advanced concurrency primitive: atomicoperations in atomic_operations", "subcategory": "atomic_operations", "unicode_point": "U+29DC", "tier": "god", "auto_generated": true, "generator": "god_tier_v1"}, {"symbol": "⫨", "code": "ng:concurrency_advanced:atomicoperations_1", "fallback": "[ATOMICOPERATIONS1]", "category": "concurrency_advanced", "name": "atomicoperations_1", "description": "Advanced concurrency primitive: atomicoperations_1 in atomic_operations", "subcategory": "atomic_operations", "unicode_point": "U+2AE8", "tier": "god", "auto_generated": true, "generator": "god_tier_v1"}, {"symbol": "⨿", "code": "ng:concurrency_advanced:atomicoperations_2", "fallback": "[ATOMICOPERATIONS2]", "category": "concurrency_advanced", "name": "atomicoperations_2", "description": "Advanced concurrency primitive: atomicoperations_2 in atomic_operations", "subcategory": "atomic_operations", "unicode_point": "U+2A3F", "tier": "god", "auto_generated": true, "generator": "god_tier_v1"}, {"symbol": "🜽", "code": "ng:concurrency_advanced:atomicoperations_3", "fallback": "[ATOMICOPERATIONS3]", "category": "concurrency_advanced", "name": "atomicoperations_3", "description": "Advanced concurrency primitive: atomicoperations_3 in atomic_operations", "subcategory": "atomic_operations", "unicode_point": "U+1F73D", "tier": "god", "auto_generated": true, "generator": "god_tier_v1"}, {"symbol": "🥠", "code": "ng:concurrency_advanced:atomicoperations_4", "fallback": "[ATOMICOPERATIONS4]", "category": "concurrency_advanced", "name": "atomicoperations_4", "description": "Advanced concurrency primitive: atomicoperations_4 in atomic_operations", "subcategory": "atomic_operations", "unicode_point": "U+1F960", "tier": "god", "auto_generated": true, "generator": "god_tier_v1"}, {"symbol": "🞫", "code": "ng:concurrency_advanced:atomicoperations_5", "fallback": "[ATOMICOPERATIONS5]", "category": "concurrency_advanced", "name": "atomicoperations_5", "description": "Advanced concurrency primitive: atomicoperations_5 in atomic_operations", "subcategory": "atomic_operations", "unicode_point": "U+1F7AB", "tier": "god", "auto_generated": true, "generator": "god_tier_v1"}, {"symbol": "⦰", "code": "ng:concurrency_advanced:atomicoperations_6", "fallback": "[ATOMICOPERATIONS6]", "category": "concurrency_advanced", "name": "atomicoperations_6", "description": "Advanced concurrency primitive: atomicoperations_6 in atomic_operations", "subcategory": "atomic_operations", "unicode_point": "U+29B0", "tier": "god", "auto_generated": true, "generator": "god_tier_v1"}, {"symbol": "😊", "code": "ng:concurrency_advanced:atomicoperations_7", "fallback": "[ATOMICOPERATIONS7]", "category": "concurrency_advanced", "name": "atomicoperations_7", "description": "Advanced concurrency primitive: atomicoperations_7 in atomic_operations", "subcategory": "atomic_operations", "unicode_point": "U+1F60A", "tier": "god", "auto_generated": true, "generator": "god_tier_v1"}, {"symbol": "❙", "code": "ng:concurrency_advanced:atomicoperations_8", "fallback": "[ATOMICOPERATIONS8]", "category": "concurrency_advanced", "name": "atomicoperations_8", "description": "Advanced concurrency primitive: atomicoperations_8 in atomic_operations", "subcategory": "atomic_operations", "unicode_point": "U+2759", "tier": "god", "auto_generated": true, "generator": "god_tier_v1"}, {"symbol": "⯺", "code": "ng:concurrency_advanced:atomicoperations_9", "fallback": "[ATOMICOPERATIONS9]", "category": "concurrency_advanced", "name": "atomicoperations_9", "description": "Advanced concurrency primitive: atomicoperations_9 in atomic_operations", "subcategory": "atomic_operations", "unicode_point": "U+2BFA", "tier": "god", "auto_generated": true, "generator": "god_tier_v1"}, {"symbol": "🎷", "code": "ng:concurrency_advanced:atomicoperations_10", "fallback": "[ATOMICOPERATIONS10]", "category": "concurrency_advanced", "name": "atomicoperations_10", "description": "Advanced concurrency primitive: atomicoperations_10 in atomic_operations", "subcategory": "atomic_operations", "unicode_point": "U+1F3B7", "tier": "god", "auto_generated": true, "generator": "god_tier_v1"}, {"symbol": "😡", "code": "ng:concurrency_advanced:compareandswap_ctrl", "fallback": "[COMPAREANDSWAPCTRL]", "category": "concurrency_advanced", "name": "compareandswap_ctrl", "description": "Advanced concurrency primitive: compareandswap_ctrl in compare_and_swap", "subcategory": "compare_and_swap", "unicode_point": "U+1F621", "tier": "god", "auto_generated": true, "generator": "god_tier_v1"}, {"symbol": "😈", "code": "ng:concurrency_advanced:compareandswap", "fallback": "[COMPAREANDSWAP]", "category": "concurrency_advanced", "name": "compareandswap", "description": "Advanced concurrency primitive: compareandswap in compare_and_swap", "subcategory": "compare_and_swap", "unicode_point": "U+1F608", "tier": "god", "auto_generated": true, "generator": "god_tier_v1"}, {"symbol": "🞔", "code": "ng:concurrency_advanced:compareandswap_op", "fallback": "[COMPAREANDSWAPOP]", "category": "concurrency_advanced", "name": "compareandswap_op", "description": "Advanced concurrency primitive: compareandswap_op in compare_and_swap", "subcategory": "compare_and_swap", "unicode_point": "U+1F794", "tier": "god", "auto_generated": true, "generator": "god_tier_v1"}, {"symbol": "🞂", "code": "ng:concurrency_advanced:compareandswap_meta", "fallback": "[COMPAREANDSWAPMETA]", "category": "concurrency_advanced", "name": "compareandswap_meta", "description": "Advanced concurrency primitive: compareandswap_meta in compare_and_swap", "subcategory": "compare_and_swap", "unicode_point": "U+1F782", "tier": "god", "auto_generated": true, "generator": "god_tier_v1"}, {"symbol": "🝓", "code": "ng:concurrency_advanced:compareandswap_1", "fallback": "[COMPAREANDSWAP1]", "category": "concurrency_advanced", "name": "compareandswap_1", "description": "Advanced concurrency primitive: compareandswap_1 in compare_and_swap", "subcategory": "compare_and_swap", "unicode_point": "U+1F753", "tier": "god", "auto_generated": true, "generator": "god_tier_v1"}, {"symbol": "⡪", "code": "ng:concurrency_advanced:compareandswap_sys", "fallback": "[COMPAREANDSWAPSYS]", "category": "concurrency_advanced", "name": "compareandswap_sys", "description": "Advanced concurrency primitive: compareandswap_sys in compare_and_swap", "subcategory": "compare_and_swap", "unicode_point": "U+286A", "tier": "god", "auto_generated": true, "generator": "god_tier_v1"}, {"symbol": "🖡", "code": "ng:concurrency_advanced:compareandswap_core", "fallback": "[COMPAREANDSWAPCORE]", "category": "concurrency_advanced", "name": "compareandswap_core", "description": "Advanced concurrency primitive: compareandswap_core in compare_and_swap", "subcategory": "compare_and_swap", "unicode_point": "U+1F5A1", "tier": "god", "auto_generated": true, "generator": "god_tier_v1"}, {"symbol": "🧖", "code": "ng:concurrency_advanced:compareandswap_proc", "fallback": "[COMPAREANDSWAPPROC]", "category": "concurrency_advanced", "name": "compareandswap_proc", "description": "Advanced concurrency primitive: compareandswap_proc in compare_and_swap", "subcategory": "compare_and_swap", "unicode_point": "U+1F9D6", "tier": "god", "auto_generated": true, "generator": "god_tier_v1"}, {"symbol": "⩨", "code": "ng:concurrency_advanced:compareandswap_sys_1", "fallback": "[COMPAREANDSWAPSYS1]", "category": "concurrency_advanced", "name": "compareandswap_sys_1", "description": "Advanced concurrency primitive: compareandswap_sys_1 in compare_and_swap", "subcategory": "compare_and_swap", "unicode_point": "U+2A68", "tier": "god", "auto_generated": true, "generator": "god_tier_v1"}, {"symbol": "🜡", "code": "ng:concurrency_advanced:compareandswap_fn", "fallback": "[COMPAREANDSWAPFN]", "category": "concurrency_advanced", "name": "compareandswap_fn", "description": "Advanced concurrency primitive: compareandswap_fn in compare_and_swap", "subcategory": "compare_and_swap", "unicode_point": "U+1F721", "tier": "god", "auto_generated": true, "generator": "god_tier_v1"}, {"symbol": "🙃", "code": "ng:concurrency_advanced:compareandswap_fn_1", "fallback": "[COMPAREANDSWAPFN1]", "category": "concurrency_advanced", "name": "compareandswap_fn_1", "description": "Advanced concurrency primitive: compareandswap_fn_1 in compare_and_swap", "subcategory": "compare_and_swap", "unicode_point": "U+1F643", "tier": "god", "auto_generated": true, "generator": "god_tier_v1"}, {"symbol": "⫝", "code": "ng:concurrency_advanced:compareandswap_sys_2", "fallback": "[COMPAREANDSWAPSYS2]", "category": "concurrency_advanced", "name": "compareandswap_sys_2", "description": "Advanced concurrency primitive: compareandswap_sys_2 in compare_and_swap", "subcategory": "compare_and_swap", "unicode_point": "U+2ADD", "tier": "god", "auto_generated": true, "generator": "god_tier_v1"}, {"symbol": "⤗", "code": "ng:concurrency_advanced:compareandswap_sys_3", "fallback": "[COMPAREANDSWAPSYS3]", "category": "concurrency_advanced", "name": "compareandswap_sys_3", "description": "Advanced concurrency primitive: compareandswap_sys_3 in compare_and_swap", "subcategory": "compare_and_swap", "unicode_point": "U+2917", "tier": "god", "auto_generated": true, "generator": "god_tier_v1"}, {"symbol": "🧍", "code": "ng:concurrency_advanced:compareandswap_sys_4", "fallback": "[COMPAREANDSWAPSYS4]", "category": "concurrency_advanced", "name": "compareandswap_sys_4", "description": "Advanced concurrency primitive: compareandswap_sys_4 in compare_and_swap", "subcategory": "compare_and_swap", "unicode_point": "U+1F9CD", "tier": "god", "auto_generated": true, "generator": "god_tier_v1"}, {"symbol": "🟊", "code": "ng:concurrency_advanced:hazardpointers_proc", "fallback": "[HAZARDPOINTERSPROC]", "category": "concurrency_advanced", "name": "hazardpointers_proc", "description": "Advanced concurrency primitive: hazardpointers_proc in hazard_pointers", "subcategory": "hazard_pointers", "unicode_point": "U+1F7CA", "tier": "god", "auto_generated": true, "generator": "god_tier_v1"}, {"symbol": "🢚", "code": "ng:concurrency_advanced:hazardpointers_sys", "fallback": "[HAZARDPOINTERSSYS]", "category": "concurrency_advanced", "name": "hazardpointers_sys", "description": "Advanced concurrency primitive: hazardpointers_sys in hazard_pointers", "subcategory": "hazard_pointers", "unicode_point": "U+1F89A", "tier": "god", "auto_generated": true, "generator": "god_tier_v1"}, {"symbol": "⦐", "code": "ng:concurrency_advanced:hazardpointers", "fallback": "[HAZARDPOINTERS]", "category": "concurrency_advanced", "name": "hazardpointers", "description": "Advanced concurrency primitive: hazardpointers in hazard_pointers", "subcategory": "hazard_pointers", "unicode_point": "U+2990", "tier": "god", "auto_generated": true, "generator": "god_tier_v1"}, {"symbol": "⧲", "code": "ng:concurrency_advanced:hazardpointers_op", "fallback": "[HAZARDPOINTERSOP]", "category": "concurrency_advanced", "name": "hazardpointers_op", "description": "Advanced concurrency primitive: hazardpointers_op in hazard_pointers", "subcategory": "hazard_pointers", "unicode_point": "U+29F2", "tier": "god", "auto_generated": true, "generator": "god_tier_v1"}, {"symbol": "🚮", "code": "ng:concurrency_advanced:hazardpointers_ctrl", "fallback": "[HAZARDPOINTERSCTRL]", "category": "concurrency_advanced", "name": "hazardpointers_ctrl", "description": "Advanced concurrency primitive: hazardpointers_ctrl in hazard_pointers", "subcategory": "hazard_pointers", "unicode_point": "U+1F6AE", "tier": "god", "auto_generated": true, "generator": "god_tier_v1"}, {"symbol": "🠪", "code": "ng:concurrency_advanced:hazardpointers_fn", "fallback": "[HAZARDPOINTERSFN]", "category": "concurrency_advanced", "name": "hazardpointers_fn", "description": "Advanced concurrency primitive: hazardpointers_fn in hazard_pointers", "subcategory": "hazard_pointers", "unicode_point": "U+1F82A", "tier": "god", "auto_generated": true, "generator": "god_tier_v1"}, {"symbol": "⫯", "code": "ng:concurrency_advanced:hazardpointers_fn_1", "fallback": "[HAZARDPOINTERSFN1]", "category": "concurrency_advanced", "name": "hazardpointers_fn_1", "description": "Advanced concurrency primitive: hazardpointers_fn_1 in hazard_pointers", "subcategory": "hazard_pointers", "unicode_point": "U+2AEF", "tier": "god", "auto_generated": true, "generator": "god_tier_v1"}, {"symbol": "🞯", "code": "ng:concurrency_advanced:hazardpointers_op_1", "fallback": "[HAZARDPOINTERSOP1]", "category": "concurrency_advanced", "name": "hazardpointers_op_1", "description": "Advanced concurrency primitive: hazardpointers_op_1 in hazard_pointers", "subcategory": "hazard_pointers", "unicode_point": "U+1F7AF", "tier": "god", "auto_generated": true, "generator": "god_tier_v1"}, {"symbol": "🛨", "code": "ng:concurrency_advanced:hazardpointers_meta", "fallback": "[HAZARDPOINTERSMETA]", "category": "concurrency_advanced", "name": "hazardpointers_meta", "description": "Advanced concurrency primitive: hazardpointers_meta in hazard_pointers", "subcategory": "hazard_pointers", "unicode_point": "U+1F6E8", "tier": "god", "auto_generated": true, "generator": "god_tier_v1"}, {"symbol": "⠥", "code": "ng:concurrency_advanced:hazardpointers_op_2", "fallback": "[HAZARDPOINTERSOP2]", "category": "concurrency_advanced", "name": "hazardpointers_op_2", "description": "Advanced concurrency primitive: hazardpointers_op_2 in hazard_pointers", "subcategory": "hazard_pointers", "unicode_point": "U+2825", "tier": "god", "auto_generated": true, "generator": "god_tier_v1"}, {"symbol": "❬", "code": "ng:concurrency_advanced:hazardpointers_core", "fallback": "[HAZARDPOINTERSCORE]", "category": "concurrency_advanced", "name": "hazardpointers_core", "description": "Advanced concurrency primitive: hazardpointers_core in hazard_pointers", "subcategory": "hazard_pointers", "unicode_point": "U+276C", "tier": "god", "auto_generated": true, "generator": "god_tier_v1"}, {"symbol": "🥰", "code": "ng:concurrency_advanced:hazardpointers_fn_2", "fallback": "[HAZARDPOINTERSFN2]", "category": "concurrency_advanced", "name": "hazardpointers_fn_2", "description": "Advanced concurrency primitive: hazardpointers_fn_2 in hazard_pointers", "subcategory": "hazard_pointers", "unicode_point": "U+1F970", "tier": "god", "auto_generated": true, "generator": "god_tier_v1"}, {"symbol": "🍙", "code": "ng:concurrency_advanced:hazardpointers_op_3", "fallback": "[HAZARDPOINTERSOP3]", "category": "concurrency_advanced", "name": "hazardpointers_op_3", "description": "Advanced concurrency primitive: hazardpointers_op_3 in hazard_pointers", "subcategory": "hazard_pointers", "unicode_point": "U+1F359", "tier": "god", "auto_generated": true, "generator": "god_tier_v1"}, {"symbol": "💄", "code": "ng:concurrency_advanced:hazardpointers_sys_1", "fallback": "[HAZARDPOINTERSSYS1]", "category": "concurrency_advanced", "name": "hazardpointers_sys_1", "description": "Advanced concurrency primitive: hazardpointers_sys_1 in hazard_pointers", "subcategory": "hazard_pointers", "unicode_point": "U+1F484", "tier": "god", "auto_generated": true, "generator": "god_tier_v1"}, {"symbol": "⠆", "code": "ng:concurrency_advanced:hazardpointers_fn_3", "fallback": "[HAZARDPOINTERSFN3]", "category": "concurrency_advanced", "name": "hazardpointers_fn_3", "description": "Advanced concurrency primitive: hazardpointers_fn_3 in hazard_pointers", "subcategory": "hazard_pointers", "unicode_point": "U+2806", "tier": "god", "auto_generated": true, "generator": "god_tier_v1"}, {"symbol": "⨐", "code": "ng:concurrency_advanced:hazardpointers_fn_4", "fallback": "[HAZARDPOINTERSFN4]", "category": "concurrency_advanced", "name": "hazardpointers_fn_4", "description": "Advanced concurrency primitive: hazardpointers_fn_4 in hazard_pointers", "subcategory": "hazard_pointers", "unicode_point": "U+2A10", "tier": "god", "auto_generated": true, "generator": "god_tier_v1"}, {"symbol": "🚣", "code": "ng:concurrency_advanced:hazardpointers_sys_2", "fallback": "[HAZARDPOINTERSSYS2]", "category": "concurrency_advanced", "name": "hazardpointers_sys_2", "description": "Advanced concurrency primitive: hazardpointers_sys_2 in hazard_pointers", "subcategory": "hazard_pointers", "unicode_point": "U+1F6A3", "tier": "god", "auto_generated": true, "generator": "god_tier_v1"}, {"symbol": "⮡", "code": "ng:concurrency_advanced:hazardpointers_sys_3", "fallback": "[HAZARDPOINTERSSYS3]", "category": "concurrency_advanced", "name": "hazardpointers_sys_3", "description": "Advanced concurrency primitive: hazardpointers_sys_3 in hazard_pointers", "subcategory": "hazard_pointers", "unicode_point": "U+2BA1", "tier": "god", "auto_generated": true, "generator": "god_tier_v1"}, {"symbol": "⨏", "code": "ng:concurrency_advanced:hazardpointers_op_4", "fallback": "[HAZARDPOINTERSOP4]", "category": "concurrency_advanced", "name": "hazardpointers_op_4", "description": "Advanced concurrency primitive: hazardpointers_op_4 in hazard_pointers", "subcategory": "hazard_pointers", "unicode_point": "U+2A0F", "tier": "god", "auto_generated": true, "generator": "god_tier_v1"}, {"symbol": "🞃", "code": "ng:concurrency_advanced:hazardpointers_fn_5", "fallback": "[HAZARDPOINTERSFN5]", "category": "concurrency_advanced", "name": "hazardpointers_fn_5", "description": "Advanced concurrency primitive: hazardpointers_fn_5 in hazard_pointers", "subcategory": "hazard_pointers", "unicode_point": "U+1F783", "tier": "god", "auto_generated": true, "generator": "god_tier_v1"}, {"symbol": "✩", "code": "ng:concurrency_advanced:hazardpointers_sys_4", "fallback": "[HAZARDPOINTERSSYS4]", "category": "concurrency_advanced", "name": "hazardpointers_sys_4", "description": "Advanced concurrency primitive: hazardpointers_sys_4 in hazard_pointers", "subcategory": "hazard_pointers", "unicode_point": "U+2729", "tier": "god", "auto_generated": true, "generator": "god_tier_v1"}, {"symbol": "🜀", "code": "ng:concurrency_advanced:hazardpointers_sys_5", "fallback": "[HAZARDPOINTERSSYS5]", "category": "concurrency_advanced", "name": "hazardpointers_sys_5", "description": "Advanced concurrency primitive: hazardpointers_sys_5 in hazard_pointers", "subcategory": "hazard_pointers", "unicode_point": "U+1F700", "tier": "god", "auto_generated": true, "generator": "god_tier_v1"}, {"symbol": "⩎", "code": "ng:concurrency_advanced:hazardpointers_fn_6", "fallback": "[HAZARDPOINTERSFN6]", "category": "concurrency_advanced", "name": "hazardpointers_fn_6", "description": "Advanced concurrency primitive: hazardpointers_fn_6 in hazard_pointers", "subcategory": "hazard_pointers", "unicode_point": "U+2A4E", "tier": "god", "auto_generated": true, "generator": "god_tier_v1"}, {"symbol": "🠫", "code": "ng:concurrency_advanced:hazardpointers_sys_6", "fallback": "[HAZARDPOINTERSSYS6]", "category": "concurrency_advanced", "name": "hazardpointers_sys_6", "description": "Advanced concurrency primitive: hazardpointers_sys_6 in hazard_pointers", "subcategory": "hazard_pointers", "unicode_point": "U+1F82B", "tier": "god", "auto_generated": true, "generator": "god_tier_v1"}, {"symbol": "🜋", "code": "ng:concurrency_advanced:hazardpointers_op_5", "fallback": "[HAZARDPOINTERSOP5]", "category": "concurrency_advanced", "name": "hazardpointers_op_5", "description": "Advanced concurrency primitive: hazardpointers_op_5 in hazard_pointers", "subcategory": "hazard_pointers", "unicode_point": "U+1F70B", "tier": "god", "auto_generated": true, "generator": "god_tier_v1"}, {"symbol": "🜇", "code": "ng:concurrency_advanced:hazardpointers_sys_7", "fallback": "[HAZARDPOINTERSSYS7]", "category": "concurrency_advanced", "name": "hazardpointers_sys_7", "description": "Advanced concurrency primitive: hazardpointers_sys_7 in hazard_pointers", "subcategory": "hazard_pointers", "unicode_point": "U+1F707", "tier": "god", "auto_generated": true, "generator": "god_tier_v1"}, {"symbol": "🞢", "code": "ng:concurrency_advanced:hazardpointers_1", "fallback": "[HAZARDPOINTERS1]", "category": "concurrency_advanced", "name": "hazardpointers_1", "description": "Advanced concurrency primitive: hazardpointers_1 in hazard_pointers", "subcategory": "hazard_pointers", "unicode_point": "U+1F7A2", "tier": "god", "auto_generated": true, "generator": "god_tier_v1"}, {"symbol": "⦾", "code": "ng:concurrency_advanced:hazardpointers_sys_8", "fallback": "[HAZARDPOINTERSSYS8]", "category": "concurrency_advanced", "name": "hazardpointers_sys_8", "description": "Advanced concurrency primitive: hazardpointers_sys_8 in hazard_pointers", "subcategory": "hazard_pointers", "unicode_point": "U+29BE", "tier": "god", "auto_generated": true, "generator": "god_tier_v1"}, {"symbol": "⥔", "code": "ng:concurrency_advanced:hazardpointers_2", "fallback": "[HAZARDPOINTERS2]", "category": "concurrency_advanced", "name": "hazardpointers_2", "description": "Advanced concurrency primitive: hazardpointers_2 in hazard_pointers", "subcategory": "hazard_pointers", "unicode_point": "U+2954", "tier": "god", "auto_generated": true, "generator": "god_tier_v1"}, {"symbol": "🥒", "code": "ng:concurrency_advanced:hazardpointers_sys_9", "fallback": "[HAZARDPOINTERSSYS9]", "category": "concurrency_advanced", "name": "hazardpointers_sys_9", "description": "Advanced concurrency primitive: hazardpointers_sys_9 in hazard_pointers", "subcategory": "hazard_pointers", "unicode_point": "U+1F952", "tier": "god", "auto_generated": true, "generator": "god_tier_v1"}, {"symbol": "😗", "code": "ng:concurrency_advanced:hazardpointers_fn_7", "fallback": "[HAZARDPOINTERSFN7]", "category": "concurrency_advanced", "name": "hazardpointers_fn_7", "description": "Advanced concurrency primitive: hazardpointers_fn_7 in hazard_pointers", "subcategory": "hazard_pointers", "unicode_point": "U+1F617", "tier": "god", "auto_generated": true, "generator": "god_tier_v1"}, {"symbol": "👯", "code": "ng:concurrency_advanced:hazardpointers_3", "fallback": "[HAZARDPOINTERS3]", "category": "concurrency_advanced", "name": "hazardpointers_3", "description": "Advanced concurrency primitive: hazardpointers_3 in hazard_pointers", "subcategory": "hazard_pointers", "unicode_point": "U+1F46F", "tier": "god", "auto_generated": true, "generator": "god_tier_v1"}, {"symbol": "🡲", "code": "ng:concurrency_advanced:hazardpointers_op_6", "fallback": "[HAZARDPOINTERSOP6]", "category": "concurrency_advanced", "name": "hazardpointers_op_6", "description": "Advanced concurrency primitive: hazardpointers_op_6 in hazard_pointers", "subcategory": "hazard_pointers", "unicode_point": "U+1F872", "tier": "god", "auto_generated": true, "generator": "god_tier_v1"}, {"symbol": "😩", "code": "ng:concurrency_advanced:hazardpointers_4", "fallback": "[HAZARDPOINTERS4]", "category": "concurrency_advanced", "name": "hazardpointers_4", "description": "Advanced concurrency primitive: hazardpointers_4 in hazard_pointers", "subcategory": "hazard_pointers", "unicode_point": "U+1F629", "tier": "god", "auto_generated": true, "generator": "god_tier_v1"}, {"symbol": "⭷", "code": "ng:concurrency_advanced:hazardpointers_op_7", "fallback": "[HAZARDPOINTERSOP7]", "category": "concurrency_advanced", "name": "hazardpointers_op_7", "description": "Advanced concurrency primitive: hazardpointers_op_7 in hazard_pointers", "subcategory": "hazard_pointers", "unicode_point": "U+2B77", "tier": "god", "auto_generated": true, "generator": "god_tier_v1"}, {"symbol": "🛤", "code": "ng:concurrency_advanced:hazardpointers_op_8", "fallback": "[HAZARDPOINTERSOP8]", "category": "concurrency_advanced", "name": "hazardpointers_op_8", "description": "Advanced concurrency primitive: hazardpointers_op_8 in hazard_pointers", "subcategory": "hazard_pointers", "unicode_point": "U+1F6E4", "tier": "god", "auto_generated": true, "generator": "god_tier_v1"}, {"symbol": "⥪", "code": "ng:concurrency_advanced:hazardpointers_5", "fallback": "[HAZARDPOINTERS5]", "category": "concurrency_advanced", "name": "hazardpointers_5", "description": "Advanced concurrency primitive: hazardpointers_5 in hazard_pointers", "subcategory": "hazard_pointers", "unicode_point": "U+296A", "tier": "god", "auto_generated": true, "generator": "god_tier_v1"}, {"symbol": "🝃", "code": "ng:concurrency_advanced:hazardpointers_op_9", "fallback": "[HAZARDPOINTERSOP9]", "category": "concurrency_advanced", "name": "hazardpointers_op_9", "description": "Advanced concurrency primitive: hazardpointers_op_9 in hazard_pointers", "subcategory": "hazard_pointers", "unicode_point": "U+1F743", "tier": "god", "auto_generated": true, "generator": "god_tier_v1"}, {"symbol": "🤶", "code": "ng:concurrency_advanced:hazardpointers_op_10", "fallback": "[HAZARDPOINTERSOP10]", "category": "concurrency_advanced", "name": "hazardpointers_op_10", "description": "Advanced concurrency primitive: hazardpointers_op_10 in hazard_pointers", "subcategory": "hazard_pointers", "unicode_point": "U+1F936", "tier": "god", "auto_generated": true, "generator": "god_tier_v1"}, {"symbol": "⡢", "code": "ng:concurrency_advanced:hazardpointers_6", "fallback": "[HAZARDPOINTERS6]", "category": "concurrency_advanced", "name": "hazardpointers_6", "description": "Advanced concurrency primitive: hazardpointers_6 in hazard_pointers", "subcategory": "hazard_pointers", "unicode_point": "U+2862", "tier": "god", "auto_generated": true, "generator": "god_tier_v1"}, {"symbol": "🍗", "code": "ng:concurrency_advanced:hazardpointers_7", "fallback": "[HAZARDPOINTERS7]", "category": "concurrency_advanced", "name": "hazardpointers_7", "description": "Advanced concurrency primitive: hazardpointers_7 in hazard_pointers", "subcategory": "hazard_pointers", "unicode_point": "U+1F357", "tier": "god", "auto_generated": true, "generator": "god_tier_v1"}, {"symbol": "❉", "code": "ng:concurrency_advanced:hazardpointers_fn_8", "fallback": "[HAZARDPOINTERSFN8]", "category": "concurrency_advanced", "name": "hazardpointers_fn_8", "description": "Advanced concurrency primitive: hazardpointers_fn_8 in hazard_pointers", "subcategory": "hazard_pointers", "unicode_point": "U+2749", "tier": "god", "auto_generated": true, "generator": "god_tier_v1"}, {"symbol": "🐝", "code": "ng:concurrency_advanced:hazardpointers_op_11", "fallback": "[HAZARDPOINTERSOP11]", "category": "concurrency_advanced", "name": "hazardpointers_op_11", "description": "Advanced concurrency primitive: hazardpointers_op_11 in hazard_pointers", "subcategory": "hazard_pointers", "unicode_point": "U+1F41D", "tier": "god", "auto_generated": true, "generator": "god_tier_v1"}, {"symbol": "🜝", "code": "ng:concurrency_advanced:hazardpointers_8", "fallback": "[HAZARDPOINTERS8]", "category": "concurrency_advanced", "name": "hazardpointers_8", "description": "Advanced concurrency primitive: hazardpointers_8 in hazard_pointers", "subcategory": "hazard_pointers", "unicode_point": "U+1F71D", "tier": "god", "auto_generated": true, "generator": "god_tier_v1"}, {"symbol": "🠜", "code": "ng:concurrency_advanced:hazardpointers_9", "fallback": "[HAZARDPOINTERS9]", "category": "concurrency_advanced", "name": "hazardpointers_9", "description": "Advanced concurrency primitive: hazardpointers_9 in hazard_pointers", "subcategory": "hazard_pointers", "unicode_point": "U+1F81C", "tier": "god", "auto_generated": true, "generator": "god_tier_v1"}, {"symbol": "⬀", "code": "ng:concurrency_advanced:hazardpointers_fn_9", "fallback": "[HAZARDPOINTERSFN9]", "category": "concurrency_advanced", "name": "hazardpointers_fn_9", "description": "Advanced concurrency primitive: hazardpointers_fn_9 in hazard_pointers", "subcategory": "hazard_pointers", "unicode_point": "U+2B00", "tier": "god", "auto_generated": true, "generator": "god_tier_v1"}, {"symbol": "🞄", "code": "ng:concurrency_advanced:hazardpointers_10", "fallback": "[HAZARDPOINTERS10]", "category": "concurrency_advanced", "name": "hazardpointers_10", "description": "Advanced concurrency primitive: hazardpointers_10 in hazard_pointers", "subcategory": "hazard_pointers", "unicode_point": "U+1F784", "tier": "god", "auto_generated": true, "generator": "god_tier_v1"}, {"symbol": "🟨", "code": "ng:concurrency_advanced:hazardpointers_11", "fallback": "[HAZARDPOINTERS11]", "category": "concurrency_advanced", "name": "hazardpointers_11", "description": "Advanced concurrency primitive: hazardpointers_11 in hazard_pointers", "subcategory": "hazard_pointers", "unicode_point": "U+1F7E8", "tier": "god", "auto_generated": true, "generator": "god_tier_v1"}, {"symbol": "📬", "code": "ng:concurrency_advanced:hazardpointers_op_12", "fallback": "[HAZARDPOINTERSOP12]", "category": "concurrency_advanced", "name": "hazardpointers_op_12", "description": "Advanced concurrency primitive: hazardpointers_op_12 in hazard_pointers", "subcategory": "hazard_pointers", "unicode_point": "U+1F4EC", "tier": "god", "auto_generated": true, "generator": "god_tier_v1"}, {"symbol": "🖲", "code": "ng:concurrency_advanced:hazardpointers_op_13", "fallback": "[HAZARDPOINTERSOP13]", "category": "concurrency_advanced", "name": "hazardpointers_op_13", "description": "Advanced concurrency primitive: hazardpointers_op_13 in hazard_pointers", "subcategory": "hazard_pointers", "unicode_point": "U+1F5B2", "tier": "god", "auto_generated": true, "generator": "god_tier_v1"}, {"symbol": "⬒", "code": "ng:concurrency_advanced:hazardpointers_fn_10", "fallback": "[HAZARDPOINTERSFN10]", "category": "concurrency_advanced", "name": "hazardpointers_fn_10", "description": "Advanced concurrency primitive: hazardpointers_fn_10 in hazard_pointers", "subcategory": "hazard_pointers", "unicode_point": "U+2B12", "tier": "god", "auto_generated": true, "generator": "god_tier_v1"}, {"symbol": "⯥", "code": "ng:concurrency_advanced:hazardpointers_op_14", "fallback": "[HAZARDPOINTERSOP14]", "category": "concurrency_advanced", "name": "hazardpointers_op_14", "description": "Advanced concurrency primitive: hazardpointers_op_14 in hazard_pointers", "subcategory": "hazard_pointers", "unicode_point": "U+2BE5", "tier": "god", "auto_generated": true, "generator": "god_tier_v1"}, {"symbol": "🢂", "code": "ng:concurrency_advanced:hazardpointers_fn_11", "fallback": "[HAZARDPOINTERSFN11]", "category": "concurrency_advanced", "name": "hazardpointers_fn_11", "description": "Advanced concurrency primitive: hazardpointers_fn_11 in hazard_pointers", "subcategory": "hazard_pointers", "unicode_point": "U+1F882", "tier": "god", "auto_generated": true, "generator": "god_tier_v1"}, {"symbol": "❆", "code": "ng:concurrency_advanced:hazardpointers_12", "fallback": "[HAZARDPOINTERS12]", "category": "concurrency_advanced", "name": "hazardpointers_12", "description": "Advanced concurrency primitive: hazardpointers_12 in hazard_pointers", "subcategory": "hazard_pointers", "unicode_point": "U+2746", "tier": "god", "auto_generated": true, "generator": "god_tier_v1"}, {"symbol": "🤭", "code": "ng:concurrency_advanced:hazardpointers_op_15", "fallback": "[HAZARDPOINTERSOP15]", "category": "concurrency_advanced", "name": "hazardpointers_op_15", "description": "Advanced concurrency primitive: hazardpointers_op_15 in hazard_pointers", "subcategory": "hazard_pointers", "unicode_point": "U+1F92D", "tier": "god", "auto_generated": true, "generator": "god_tier_v1"}, {"symbol": "🡗", "code": "ng:concurrency_advanced:hazardpointers_op_16", "fallback": "[HAZARDPOINTERSOP16]", "category": "concurrency_advanced", "name": "hazardpointers_op_16", "description": "Advanced concurrency primitive: hazardpointers_op_16 in hazard_pointers", "subcategory": "hazard_pointers", "unicode_point": "U+1F857", "tier": "god", "auto_generated": true, "generator": "god_tier_v1"}, {"symbol": "🠻", "code": "ng:concurrency_advanced:hazardpointers_13", "fallback": "[HAZARDPOINTERS13]", "category": "concurrency_advanced", "name": "hazardpointers_13", "description": "Advanced concurrency primitive: hazardpointers_13 in hazard_pointers", "subcategory": "hazard_pointers", "unicode_point": "U+1F83B", "tier": "god", "auto_generated": true, "generator": "god_tier_v1"}]}