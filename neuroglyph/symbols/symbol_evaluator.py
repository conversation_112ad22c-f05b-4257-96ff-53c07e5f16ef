"""
symbol_evaluator.py — Evaluates usefulness of NEUROGLYPH symbols based on memory metrics
"""

import json

def evaluate_symbol(symbol_data, thresholds):
    score = 0
    reasons = []

    if symbol_data.get("frequency", 0) >= thresholds["min_frequency"]:
        score += 1
    else:
        reasons.append("frequency too low")

    if symbol_data.get("dag_pass_rate", 0) >= thresholds["min_dag_pass_rate"]:
        score += 1
    else:
        reasons.append("DAG pass rate too low")

    if symbol_data.get("roundtrip_pass_rate", 0) >= thresholds["min_roundtrip_pass_rate"]:
        score += 1
    else:
        reasons.append("Roundtrip pass rate too low")

    if symbol_data.get("tokens_saved", 0) >= thresholds["min_tokens_saved"]:
        score += 1
    else:
        reasons.append("tokens_saved too low")

    if symbol_data.get("entropy_gain", 0) >= thresholds["min_entropy_gain"]:
        score += 1
    else:
        reasons.append("entropy_gain too low")

    if score == 5:
        return "approved", []
    elif score >= 3:
        return "candidate", reasons
    else:
        return "deprecated", reasons

def load_thresholds(path="symbol_score_schema.json"):
    with open(path) as f:
        return json.load(f)["thresholds"]

if __name__ == "__main__":
    # Example usage
    thresholds = load_thresholds()
    symbol_data = {
        "frequency": 14,
        "dag_pass_rate": 0.96,
        "roundtrip_pass_rate": 0.99,
        "tokens_saved": 7,
        "entropy_gain": 0.18
    }
    status, explanation = evaluate_symbol(symbol_data, thresholds)
    print(f"Status: {status}")
    if explanation:
        print("Issues:", explanation)
