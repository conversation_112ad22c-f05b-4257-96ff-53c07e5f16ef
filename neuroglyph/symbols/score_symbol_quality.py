#!/usr/bin/env python3
"""
NEUROGLYPH ULTRA - Symbol Quality Scorer
=========================================

Assegna punteggio qualità a ogni simbolo analizzando:
- Density (informazione semantica per byte)
- Cost (costo computazionale)
- Overlap (sovrapposizione con simboli esistenti)
- Uniqueness (unicità visiva e semantica)
- LLM Efficiency (efficienza per LLM)

Usage: python score_symbol_quality.py "⊕" "ng:operator:add" "[+]"
"""

import sys
import json
import unicodedata
import math
from typing import Dict, Any, List, Optional
from collections import Counter

class SymbolQualityScorer:
    """Analizzatore di qualità per simboli NEUROGLYPH ULTRA."""
    
    def __init__(self, symbols_file: str = "core/symbols_registry.json"):
        self.symbols_file = symbols_file
        self.existing_symbols = self._load_existing_symbols()
        
    def _load_existing_symbols(self) -> List[Dict[str, Any]]:
        """Carica simboli esistenti."""
        try:
            with open(self.symbols_file, 'r', encoding='utf-8') as f:
                return json.load(f)
        except:
            return []
    
    def score_symbol(self, symbol: str, code: str, fallback: str, 
                    name: str = "", description: str = "") -> Dict[str, Any]:
        """Calcola score di qualità completo per un simbolo."""
        
        result = {
            "symbol": symbol,
            "code": code,
            "fallback": fallback,
            "name": name,
            "description": description,
            "scores": {},
            "overall_score": 0.0,
            "grade": "",
            "recommendations": []
        }
        
        # Calcola score per ogni dimensione
        result["scores"]["density"] = self._score_density(symbol, code, description)
        result["scores"]["cost"] = self._score_cost(symbol, fallback)
        result["scores"]["overlap"] = self._score_overlap(symbol, code, name)
        result["scores"]["uniqueness"] = self._score_uniqueness(symbol, code)
        result["scores"]["llm_efficiency"] = self._score_llm_efficiency(symbol, fallback)
        
        # Calcola score complessivo (media pesata)
        weights = {
            "density": 0.25,      # 25% - Densità informativa
            "cost": 0.20,         # 20% - Costo computazionale
            "overlap": 0.20,      # 20% - Sovrapposizione
            "uniqueness": 0.20,   # 20% - Unicità
            "llm_efficiency": 0.15 # 15% - Efficienza LLM
        }
        
        weighted_sum = sum(
            result["scores"][dimension] * weight 
            for dimension, weight in weights.items()
        )
        
        result["overall_score"] = weighted_sum
        result["grade"] = self._calculate_grade(weighted_sum)
        result["recommendations"] = self._generate_recommendations(result)
        
        return result
    
    def _score_density(self, symbol: str, code: str, description: str) -> float:
        """Score densità informativa (0-100)."""
        score = 50.0  # Base score
        
        # 1. Densità Unicode (informazione per byte)
        if symbol:
            unicode_bytes = len(symbol.encode('utf-8'))
            unicode_chars = len(symbol)
            
            # Simboli single-char sono più densi
            if unicode_chars == 1:
                score += 20
            elif unicode_chars == 2:
                score += 10
            else:
                score -= 10  # Simboli multi-char meno densi
            
            # Byte efficiency
            if unicode_bytes <= 3:
                score += 15
            elif unicode_bytes <= 6:
                score += 5
            else:
                score -= 10
        
        # 2. Densità semantica del codice
        if code:
            parts = code.split(":")
            if len(parts) == 3:
                _, category, function = parts
                
                # Funzioni concise sono migliori
                if len(function) <= 5:
                    score += 10
                elif len(function) <= 10:
                    score += 5
                else:
                    score -= 5
        
        # 3. Densità descrizione
        if description:
            words = description.split()
            if 3 <= len(words) <= 8:
                score += 10  # Descrizione concisa ma informativa
            elif len(words) > 15:
                score -= 5   # Troppo verbosa
        
        return max(0.0, min(100.0, score))
    
    def _score_cost(self, symbol: str, fallback: str) -> float:
        """Score costo computazionale (0-100, più alto = meno costoso)."""
        score = 50.0
        
        # 1. Costo Unicode
        if symbol:
            unicode_bytes = len(symbol.encode('utf-8'))
            
            # Costo basato su byte
            if unicode_bytes == 1:
                score += 30  # ASCII - costo minimo
            elif unicode_bytes <= 3:
                score += 20  # UTF-8 efficiente
            elif unicode_bytes <= 6:
                score += 10  # UTF-8 accettabile
            else:
                score -= 10  # UTF-8 costoso
            
            # Costo rendering (simboli complessi)
            try:
                unicode_category = unicodedata.category(symbol[0])
                if unicode_category.startswith('S'):  # Symbol
                    score += 10
                elif unicode_category.startswith('P'):  # Punctuation
                    score += 15
                elif unicode_category.startswith('L'):  # Letter
                    score += 5
                else:
                    score -= 5
            except:
                score -= 10
        
        # 2. Costo fallback
        if fallback:
            fallback_len = len(fallback)
            if fallback_len <= 5:
                score += 15
            elif fallback_len <= 10:
                score += 10
            else:
                score -= 5
        
        # 3. Costo parsing (simboli che richiedono escape)
        problematic_chars = set(['\\', '"', "'", '\n', '\t', '\r'])
        if symbol and any(c in problematic_chars for c in symbol):
            score -= 15
        
        return max(0.0, min(100.0, score))
    
    def _score_overlap(self, symbol: str, code: str, name: str) -> float:
        """Score sovrapposizione con simboli esistenti (0-100, più alto = meno overlap)."""
        score = 100.0
        
        # 1. Overlap visivo
        visual_similarity_count = 0
        for existing in self.existing_symbols:
            existing_symbol = existing.get("symbol", "")
            if self._are_visually_similar(symbol, existing_symbol):
                visual_similarity_count += 1
        
        # Penalty per similarità visiva
        score -= visual_similarity_count * 20
        
        # 2. Overlap semantico (codici simili)
        semantic_similarity_count = 0
        if code:
            parts = code.split(":")
            if len(parts) == 3:
                _, category, function = parts
                
                for existing in self.existing_symbols:
                    existing_code = existing.get("code", "")
                    if existing_code:
                        existing_parts = existing_code.split(":")
                        if len(existing_parts) == 3:
                            _, existing_category, existing_function = existing_parts
                            
                            # Stessa categoria + funzione simile
                            if (category == existing_category and 
                                self._are_semantically_similar(function, existing_function)):
                                semantic_similarity_count += 1
        
        # Penalty per similarità semantica
        score -= semantic_similarity_count * 15
        
        # 3. Overlap nomi
        name_similarity_count = 0
        if name:
            for existing in self.existing_symbols:
                existing_name = existing.get("name", "")
                if existing_name and self._are_semantically_similar(name, existing_name):
                    name_similarity_count += 1
        
        # Penalty per nomi simili
        score -= name_similarity_count * 10
        
        return max(0.0, min(100.0, score))
    
    def _score_uniqueness(self, symbol: str, code: str) -> float:
        """Score unicità (0-100)."""
        score = 50.0
        
        # 1. Unicità Unicode
        if symbol:
            # Bonus per simboli rari
            try:
                unicode_name = unicodedata.name(symbol[0], "")
                if "MATHEMATICAL" in unicode_name:
                    score += 20
                elif "LOGICAL" in unicode_name:
                    score += 15
                elif "GEOMETRIC" in unicode_name:
                    score += 10
                elif "ARROW" in unicode_name:
                    score += 5
                else:
                    score += 0
            except:
                score -= 5
            
            # Penalty per simboli comuni
            common_symbols = set(['*', '+', '-', '=', '<', '>', '!', '?', '@', '#', '$', '%'])
            if symbol in common_symbols:
                score -= 20
        
        # 2. Unicità codice
        if code:
            # Verifica che il codice non esista
            existing_codes = [s.get("code", "") for s in self.existing_symbols]
            if code not in existing_codes:
                score += 25
            else:
                score -= 50  # Grave: codice duplicato
        
        # 3. Unicità strutturale
        if symbol and len(symbol) == 1:
            # Single character è più unico
            score += 15
        
        return max(0.0, min(100.0, score))
    
    def _score_llm_efficiency(self, symbol: str, fallback: str) -> float:
        """Score efficienza per LLM (0-100)."""
        score = 50.0
        
        # 1. Tokenization efficiency (stima)
        if symbol:
            # Simboli matematici spesso sono single token
            try:
                unicode_name = unicodedata.name(symbol[0], "")
                if any(keyword in unicode_name for keyword in 
                      ["MATHEMATICAL", "LOGICAL", "OPERATOR", "SYMBOL"]):
                    score += 20
                else:
                    score += 5
            except:
                score -= 5
            
            # Lunghezza UTF-8 vs efficienza
            utf8_len = len(symbol.encode('utf-8'))
            if utf8_len <= 3:
                score += 15
            elif utf8_len <= 6:
                score += 5
            else:
                score -= 10
        
        # 2. Fallback efficiency
        if fallback:
            # Fallback brevi sono migliori
            if len(fallback) <= 6:
                score += 15
            elif len(fallback) <= 10:
                score += 10
            else:
                score -= 5
            
            # Fallback ASCII puri
            if fallback.isascii():
                score += 10
        
        # 3. Context efficiency
        # Simboli che non richiedono escape in JSON/string
        if symbol and '"' not in symbol and '\\' not in symbol:
            score += 10
        
        return max(0.0, min(100.0, score))
    
    def _are_visually_similar(self, symbol1: str, symbol2: str) -> bool:
        """Controlla similarità visiva tra simboli."""
        if not symbol1 or not symbol2 or symbol1 == symbol2:
            return False
        
        try:
            name1 = unicodedata.name(symbol1[0], "").lower()
            name2 = unicodedata.name(symbol2[0], "").lower()
            
            # Pattern di similarità
            similar_patterns = [
                ("circle", "ring"), ("square", "rectangle"),
                ("left", "right"), ("up", "down"),
                ("small", "large"), ("black", "white"),
                ("plus", "cross"), ("minus", "dash")
            ]
            
            for pattern1, pattern2 in similar_patterns:
                if ((pattern1 in name1 and pattern2 in name2) or 
                    (pattern2 in name1 and pattern1 in name2)):
                    return True
        except ValueError:
            pass
        
        return False
    
    def _are_semantically_similar(self, text1: str, text2: str) -> bool:
        """Controlla similarità semantica tra testi."""
        if not text1 or not text2:
            return False
        
        # Normalizza
        text1 = text1.lower().replace("_", "").replace("-", "")
        text2 = text2.lower().replace("_", "").replace("-", "")
        
        # Exact match
        if text1 == text2:
            return True
        
        # Substring match
        if text1 in text2 or text2 in text1:
            return True
        
        # Common prefixes/suffixes
        if len(text1) >= 3 and len(text2) >= 3:
            if text1[:3] == text2[:3] or text1[-3:] == text2[-3:]:
                return True
        
        return False
    
    def _calculate_grade(self, score: float) -> str:
        """Calcola grade letterale basato su sneuroglyph.core."""
        if score >= 90:
            return "A+"
        elif score >= 85:
            return "A"
        elif score >= 80:
            return "A-"
        elif score >= 75:
            return "B+"
        elif score >= 70:
            return "B"
        elif score >= 65:
            return "B-"
        elif score >= 60:
            return "C+"
        elif score >= 55:
            return "C"
        elif score >= 50:
            return "C-"
        elif score >= 40:
            return "D"
        else:
            return "F"
    
    def _generate_recommendations(self, result: Dict[str, Any]) -> List[str]:
        """Genera raccomandazioni per migliorare il simbolo."""
        recommendations = []
        scores = result["scores"]
        
        # Analisi per dimensione
        if scores["density"] < 70:
            recommendations.append("⚠️ Migliorare densità: considerare simbolo più conciso")
        
        if scores["cost"] < 70:
            recommendations.append("⚠️ Ridurre costo: simbolo troppo complesso per rendering")
        
        if scores["overlap"] < 70:
            recommendations.append("❌ Ridurre overlap: simbolo troppo simile a esistenti")
        
        if scores["uniqueness"] < 70:
            recommendations.append("⚠️ Aumentare unicità: scegliere simbolo più distintivo")
        
        if scores["llm_efficiency"] < 70:
            recommendations.append("⚠️ Migliorare efficienza LLM: ottimizzare per tokenizer")
        
        # Raccomandazione generale
        overall = result["overall_score"]
        if overall >= 85:
            recommendations.append("🚀 ECCELLENTE: Simbolo di alta qualità per NEUROGLYPH ULTRA")
        elif overall >= 75:
            recommendations.append("✅ BUONO: Simbolo accettabile con piccoli miglioramenti")
        elif overall >= 60:
            recommendations.append("⚠️ MEDIOCRE: Simbolo richiede miglioramenti significativi")
        else:
            recommendations.append("❌ SCADENTE: Simbolo non adatto per NEUROGLYPH ULTRA")
        
        return recommendations
    
    def generate_report(self, result: Dict[str, Any]) -> str:
        """Genera report dettagliato di qualità."""
        lines = []
        lines.append("🧠 NEUROGLYPH ULTRA - Symbol Quality Report")
        lines.append("=" * 50)
        lines.append(f"Symbol: {result['symbol']}")
        lines.append(f"Code: {result['code']}")
        lines.append(f"Name: {result['name']}")
        lines.append(f"Overall Score: {result['overall_score']:.1f}/100")
        lines.append(f"Grade: {result['grade']}")
        lines.append("")
        
        # Breakdown scores
        lines.append("📊 Quality Breakdown:")
        scores = result["scores"]
        lines.append(f"  Density (25%):      {scores['density']:.1f}/100")
        lines.append(f"  Cost (20%):         {scores['cost']:.1f}/100")
        lines.append(f"  Overlap (20%):      {scores['overlap']:.1f}/100")
        lines.append(f"  Uniqueness (20%):   {scores['uniqueness']:.1f}/100")
        lines.append(f"  LLM Efficiency (15%): {scores['llm_efficiency']:.1f}/100")
        lines.append("")
        
        # Recommendations
        lines.append("🎯 Recommendations:")
        for rec in result["recommendations"]:
            lines.append(f"  {rec}")
        
        return "\n".join(lines)

def main():
    """Main function per command line usage."""
    if len(sys.argv) < 4:
        print("Usage: python score_symbol_quality.py <symbol> <code> <fallback> [name] [description]")
        print("Example: python score_symbol_quality.py '⊕' 'ng:operator:add' '[+]' 'add' 'Addition operator'")
        sys.exit(1)
    
    symbol = sys.argv[1]
    code = sys.argv[2]
    fallback = sys.argv[3]
    name = sys.argv[4] if len(sys.argv) > 4 else ""
    description = sys.argv[5] if len(sys.argv) > 5 else ""
    
    scorer = SymbolQualityScorer()
    result = scorer.score_symbol(symbol, code, fallback, name, description)
    
    report = scorer.generate_report(result)
    print(report)
    
    # Salva risultati
    output_file = f"quality_score_{symbol.replace('/', '_')}.json"
    with open(output_file, "w", encoding="utf-8") as f:
        json.dump(result, f, indent=2, ensure_ascii=False)
    
    print(f"\n💾 Results saved to: {output_file}")
    
    # Exit code basato su grade
    exit_code = 0 if result["overall_score"] >= 70 else 1
    sys.exit(exit_code)

if __name__ == "__main__":
    main()
