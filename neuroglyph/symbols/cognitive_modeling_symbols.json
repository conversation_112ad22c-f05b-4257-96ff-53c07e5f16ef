{"generation_info": {"domain": "cognitive_modeling", "count_requested": 64, "count_generated": 54, "timestamp": "2025-05-25T18:21:40.620110", "generator": "god_tier_v1"}, "symbols": [{"symbol": "⬲", "code": "ng:cognitive_modeling:attention_proc", "fallback": "[ATTENTIONPROC]", "category": "cognitive_modeling", "name": "attention_proc", "description": "Cognitive model: attention_proc in cognitive_architectures", "subcategory": "cognitive_architectures", "unicode_point": "U+2B32", "tier": "god", "auto_generated": true, "generator": "god_tier_v1"}, {"symbol": "⨢", "code": "ng:cognitive_modeling:cognition_proc", "fallback": "[COGNITIONPROC]", "category": "cognitive_modeling", "name": "cognition_proc", "description": "Cognitive model: cognition_proc in cognitive_architectures", "subcategory": "cognitive_architectures", "unicode_point": "U+2A22", "tier": "god", "auto_generated": true, "generator": "god_tier_v1"}, {"symbol": "🤛", "code": "ng:cognitive_modeling:reasoning_fn", "fallback": "[REASONINGFN]", "category": "cognitive_modeling", "name": "reasoning_fn", "description": "Cognitive model: reasoning_fn in cognitive_architectures", "subcategory": "cognitive_architectures", "unicode_point": "U+1F91B", "tier": "god", "auto_generated": true, "generator": "god_tier_v1"}, {"symbol": "⨧", "code": "ng:cognitive_modeling:attention_proc_1", "fallback": "[ATTENTIONPROC1]", "category": "cognitive_modeling", "name": "attention_proc_1", "description": "Cognitive model: attention_proc_1 in cognitive_architectures", "subcategory": "cognitive_architectures", "unicode_point": "U+2A27", "tier": "god", "auto_generated": true, "generator": "god_tier_v1"}, {"symbol": "⧨", "code": "ng:cognitive_modeling:memory_core", "fallback": "[MEMORYCORE]", "category": "cognitive_modeling", "name": "memory_core", "description": "Cognitive model: memory_core in cognitive_architectures", "subcategory": "cognitive_architectures", "unicode_point": "U+29E8", "tier": "god", "auto_generated": true, "generator": "god_tier_v1"}, {"symbol": "➕", "code": "ng:cognitive_modeling:memory_op", "fallback": "[MEMORYOP]", "category": "cognitive_modeling", "name": "memory_op", "description": "Cognitive model: memory_op in cognitive_architectures", "subcategory": "cognitive_architectures", "unicode_point": "U+2795", "tier": "god", "auto_generated": true, "generator": "god_tier_v1"}, {"symbol": "🚍", "code": "ng:cognitive_modeling:cognition", "fallback": "[COGNITION]", "category": "cognitive_modeling", "name": "cognition", "description": "Cognitive model: cognition in cognitive_architectures", "subcategory": "cognitive_architectures", "unicode_point": "U+1F68D", "tier": "god", "auto_generated": true, "generator": "god_tier_v1"}, {"symbol": "🍉", "code": "ng:cognitive_modeling:memory_meta", "fallback": "[MEMORYMETA]", "category": "cognitive_modeling", "name": "memory_meta", "description": "Cognitive model: memory_meta in cognitive_architectures", "subcategory": "cognitive_architectures", "unicode_point": "U+1F349", "tier": "god", "auto_generated": true, "generator": "god_tier_v1"}, {"symbol": "😧", "code": "ng:cognitive_modeling:reasoning", "fallback": "[REASONING]", "category": "cognitive_modeling", "name": "reasoning", "description": "Cognitive model: reasoning in cognitive_architectures", "subcategory": "cognitive_architectures", "unicode_point": "U+1F627", "tier": "god", "auto_generated": true, "generator": "god_tier_v1"}, {"symbol": "⩇", "code": "ng:cognitive_modeling:attention_ctrl", "fallback": "[ATTENTIONCTRL]", "category": "cognitive_modeling", "name": "attention_ctrl", "description": "Cognitive model: attention_ctrl in cognitive_architectures", "subcategory": "cognitive_architectures", "unicode_point": "U+2A47", "tier": "god", "auto_generated": true, "generator": "god_tier_v1"}, {"symbol": "➜", "code": "ng:cognitive_modeling:memorymodels_op", "fallback": "[MEMORYMODELSOP]", "category": "cognitive_modeling", "name": "memorymodels_op", "description": "Cognitive model: memorymodels_op in memory_models", "subcategory": "memory_models", "unicode_point": "U+279C", "tier": "god", "auto_generated": true, "generator": "god_tier_v1"}, {"symbol": "🕾", "code": "ng:cognitive_modeling:memorymodels", "fallback": "[MEMORYMODELS]", "category": "cognitive_modeling", "name": "memorymodels", "description": "Cognitive model: memorymodels in memory_models", "subcategory": "memory_models", "unicode_point": "U+1F57E", "tier": "god", "auto_generated": true, "generator": "god_tier_v1"}, {"symbol": "⩧", "code": "ng:cognitive_modeling:memorymodels_proc", "fallback": "[MEMORYMODELSPROC]", "category": "cognitive_modeling", "name": "memorymodels_proc", "description": "Cognitive model: memorymodels_proc in memory_models", "subcategory": "memory_models", "unicode_point": "U+2A67", "tier": "god", "auto_generated": true, "generator": "god_tier_v1"}, {"symbol": "➫", "code": "ng:cognitive_modeling:memorymodels_1", "fallback": "[MEMORYMODELS1]", "category": "cognitive_modeling", "name": "memorymodels_1", "description": "Cognitive model: memorymodels_1 in memory_models", "subcategory": "memory_models", "unicode_point": "U+27AB", "tier": "god", "auto_generated": true, "generator": "god_tier_v1"}, {"symbol": "🧜", "code": "ng:cognitive_modeling:memorymodels_op_1", "fallback": "[MEMORYMODELSOP1]", "category": "cognitive_modeling", "name": "memorymodels_op_1", "description": "Cognitive model: memorymodels_op_1 in memory_models", "subcategory": "memory_models", "unicode_point": "U+1F9DC", "tier": "god", "auto_generated": true, "generator": "god_tier_v1"}, {"symbol": "🛌", "code": "ng:cognitive_modeling:memorymodels_ctrl", "fallback": "[MEMORYMODELSCTRL]", "category": "cognitive_modeling", "name": "memorymodels_ctrl", "description": "Cognitive model: memorymodels_ctrl in memory_models", "subcategory": "memory_models", "unicode_point": "U+1F6CC", "tier": "god", "auto_generated": true, "generator": "god_tier_v1"}, {"symbol": "📀", "code": "ng:cognitive_modeling:memorymodels_2", "fallback": "[MEMORYMODELS2]", "category": "cognitive_modeling", "name": "memorymodels_2", "description": "Cognitive model: memorymodels_2 in memory_models", "subcategory": "memory_models", "unicode_point": "U+1F4C0", "tier": "god", "auto_generated": true, "generator": "god_tier_v1"}, {"symbol": "✤", "code": "ng:cognitive_modeling:memorymodels_proc_1", "fallback": "[MEMORYMODELSPROC1]", "category": "cognitive_modeling", "name": "memorymodels_proc_1", "description": "Cognitive model: memorymodels_proc_1 in memory_models", "subcategory": "memory_models", "unicode_point": "U+2724", "tier": "god", "auto_generated": true, "generator": "god_tier_v1"}, {"symbol": "🐮", "code": "ng:cognitive_modeling:memorymodels_ctrl_1", "fallback": "[MEMORYMODELSCTRL1]", "category": "cognitive_modeling", "name": "memorymodels_ctrl_1", "description": "Cognitive model: memorymodels_ctrl_1 in memory_models", "subcategory": "memory_models", "unicode_point": "U+1F42E", "tier": "god", "auto_generated": true, "generator": "god_tier_v1"}, {"symbol": "🛢", "code": "ng:cognitive_modeling:memorymodels_sys", "fallback": "[MEMORYMODELSSYS]", "category": "cognitive_modeling", "name": "memorymodels_sys", "description": "Cognitive model: memorymodels_sys in memory_models", "subcategory": "memory_models", "unicode_point": "U+1F6E2", "tier": "god", "auto_generated": true, "generator": "god_tier_v1"}, {"symbol": "✂", "code": "ng:cognitive_modeling:attentionmodels", "fallback": "[ATTENTIONMODELS]", "category": "cognitive_modeling", "name": "attentionmodels", "description": "Cognitive model: attentionmodels in attention_models", "subcategory": "attention_models", "unicode_point": "U+2702", "tier": "god", "auto_generated": true, "generator": "god_tier_v1"}, {"symbol": "🔑", "code": "ng:cognitive_modeling:attentionmodels_1", "fallback": "[ATTENTIONMODELS1]", "category": "cognitive_modeling", "name": "attentionmodels_1", "description": "Cognitive model: attentionmodels_1 in attention_models", "subcategory": "attention_models", "unicode_point": "U+1F511", "tier": "god", "auto_generated": true, "generator": "god_tier_v1"}, {"symbol": "⠉", "code": "ng:cognitive_modeling:attentionmodels_op", "fallback": "[ATTENTIONMODELSOP]", "category": "cognitive_modeling", "name": "attentionmodels_op", "description": "Cognitive model: attentionmodels_op in attention_models", "subcategory": "attention_models", "unicode_point": "U+2809", "tier": "god", "auto_generated": true, "generator": "god_tier_v1"}, {"symbol": "🦫", "code": "ng:cognitive_modeling:attentionmodels_fn", "fallback": "[ATTENTIONMODELSFN]", "category": "cognitive_modeling", "name": "attentionmodels_fn", "description": "Cognitive model: attentionmodels_fn in attention_models", "subcategory": "attention_models", "unicode_point": "U+1F9AB", "tier": "god", "auto_generated": true, "generator": "god_tier_v1"}, {"symbol": "⧺", "code": "ng:cognitive_modeling:attentionmodels_sys", "fallback": "[ATTENTIONMODELSSYS]", "category": "cognitive_modeling", "name": "attentionmodels_sys", "description": "Cognitive model: attentionmodels_sys in attention_models", "subcategory": "attention_models", "unicode_point": "U+29FA", "tier": "god", "auto_generated": true, "generator": "god_tier_v1"}, {"symbol": "⢸", "code": "ng:cognitive_modeling:attentionmodels_op_1", "fallback": "[ATTENTIONMODELSOP1]", "category": "cognitive_modeling", "name": "attentionmodels_op_1", "description": "Cognitive model: attentionmodels_op_1 in attention_models", "subcategory": "attention_models", "unicode_point": "U+28B8", "tier": "god", "auto_generated": true, "generator": "god_tier_v1"}, {"symbol": "⨯", "code": "ng:cognitive_modeling:attentionmodels_fn_1", "fallback": "[ATTENTIONMODELSFN1]", "category": "cognitive_modeling", "name": "attentionmodels_fn_1", "description": "Cognitive model: attentionmodels_fn_1 in attention_models", "subcategory": "attention_models", "unicode_point": "U+2A2F", "tier": "god", "auto_generated": true, "generator": "god_tier_v1"}, {"symbol": "⢮", "code": "ng:cognitive_modeling:attentionmodels_2", "fallback": "[ATTENTIONMODELS2]", "category": "cognitive_modeling", "name": "attentionmodels_2", "description": "Cognitive model: attentionmodels_2 in attention_models", "subcategory": "attention_models", "unicode_point": "U+28AE", "tier": "god", "auto_generated": true, "generator": "god_tier_v1"}, {"symbol": "⮲", "code": "ng:cognitive_modeling:attentionmodels_op_2", "fallback": "[ATTENTIONMODELSOP2]", "category": "cognitive_modeling", "name": "attentionmodels_op_2", "description": "Cognitive model: attentionmodels_op_2 in attention_models", "subcategory": "attention_models", "unicode_point": "U+2BB2", "tier": "god", "auto_generated": true, "generator": "god_tier_v1"}, {"symbol": "🥴", "code": "ng:cognitive_modeling:attentionmodels_3", "fallback": "[ATTENTIONMODELS3]", "category": "cognitive_modeling", "name": "attentionmodels_3", "description": "Cognitive model: attentionmodels_3 in attention_models", "subcategory": "attention_models", "unicode_point": "U+1F974", "tier": "god", "auto_generated": true, "generator": "god_tier_v1"}, {"symbol": "🜫", "code": "ng:cognitive_modeling:decisionmaking_proc", "fallback": "[DECISIONMAKINGPROC]", "category": "cognitive_modeling", "name": "decisionmaking_proc", "description": "Cognitive model: decisionmaking_proc in decision_making", "subcategory": "decision_making", "unicode_point": "U+1F72B", "tier": "god", "auto_generated": true, "generator": "god_tier_v1"}, {"symbol": "⧰", "code": "ng:cognitive_modeling:decisionmaking_core", "fallback": "[DECISIONMAKINGCORE]", "category": "cognitive_modeling", "name": "decisionmaking_core", "description": "Cognitive model: decisionmaking_core in decision_making", "subcategory": "decision_making", "unicode_point": "U+29F0", "tier": "god", "auto_generated": true, "generator": "god_tier_v1"}, {"symbol": "🥭", "code": "ng:cognitive_modeling:decisionmaking_ctrl", "fallback": "[DECISIONMAKINGCTRL]", "category": "cognitive_modeling", "name": "decisionmaking_ctrl", "description": "Cognitive model: decisionmaking_ctrl in decision_making", "subcategory": "decision_making", "unicode_point": "U+1F96D", "tier": "god", "auto_generated": true, "generator": "god_tier_v1"}, {"symbol": "🠤", "code": "ng:cognitive_modeling:decisionmaking_op", "fallback": "[DECISIONMAKINGOP]", "category": "cognitive_modeling", "name": "decisionmaking_op", "description": "Cognitive model: decisionmaking_op in decision_making", "subcategory": "decision_making", "unicode_point": "U+1F824", "tier": "god", "auto_generated": true, "generator": "god_tier_v1"}, {"symbol": "⣽", "code": "ng:cognitive_modeling:decisionmaking_fn", "fallback": "[DECISIONMAKINGFN]", "category": "cognitive_modeling", "name": "decisionmaking_fn", "description": "Cognitive model: decisionmaking_fn in decision_making", "subcategory": "decision_making", "unicode_point": "U+28FD", "tier": "god", "auto_generated": true, "generator": "god_tier_v1"}, {"symbol": "⥗", "code": "ng:cognitive_modeling:decisionmaking_meta", "fallback": "[DECISIONMAKINGMETA]", "category": "cognitive_modeling", "name": "decisionmaking_meta", "description": "Cognitive model: decisionmaking_meta in decision_making", "subcategory": "decision_making", "unicode_point": "U+2957", "tier": "god", "auto_generated": true, "generator": "god_tier_v1"}, {"symbol": "🜁", "code": "ng:cognitive_modeling:decisionmaking_fn_1", "fallback": "[DECISIONMAKINGFN1]", "category": "cognitive_modeling", "name": "decisionmaking_fn_1", "description": "Cognitive model: decisionmaking_fn_1 in decision_making", "subcategory": "decision_making", "unicode_point": "U+1F701", "tier": "god", "auto_generated": true, "generator": "god_tier_v1"}, {"symbol": "❋", "code": "ng:cognitive_modeling:decisionmaking_op_1", "fallback": "[DECISIONMAKINGOP1]", "category": "cognitive_modeling", "name": "decisionmaking_op_1", "description": "Cognitive model: decisionmaking_op_1 in decision_making", "subcategory": "decision_making", "unicode_point": "U+274B", "tier": "god", "auto_generated": true, "generator": "god_tier_v1"}, {"symbol": "🡨", "code": "ng:cognitive_modeling:decisionmaking", "fallback": "[DECISIONMAKING]", "category": "cognitive_modeling", "name": "decisionmaking", "description": "Cognitive model: decisionmaking in decision_making", "subcategory": "decision_making", "unicode_point": "U+1F868", "tier": "god", "auto_generated": true, "generator": "god_tier_v1"}, {"symbol": "⠠", "code": "ng:cognitive_modeling:decisionmaking_1", "fallback": "[DECISIONMAKING1]", "category": "cognitive_modeling", "name": "decisionmaking_1", "description": "Cognitive model: decisionmaking_1 in decision_making", "subcategory": "decision_making", "unicode_point": "U+2820", "tier": "god", "auto_generated": true, "generator": "god_tier_v1"}, {"symbol": "🔎", "code": "ng:cognitive_modeling:learningmechanisms", "fallback": "[LEARNINGMECHANISMS]", "category": "cognitive_modeling", "name": "learningmechanisms", "description": "Cognitive model: learningmechanisms in learning_mechanisms", "subcategory": "learning_mechanisms", "unicode_point": "U+1F50E", "tier": "god", "auto_generated": true, "generator": "god_tier_v1"}, {"symbol": "✘", "code": "ng:cognitive_modeling:perceptionmodels", "fallback": "[PERCEPTIONMODELS]", "category": "cognitive_modeling", "name": "perceptionmodels", "description": "Cognitive model: perceptionmodels in perception_models", "subcategory": "perception_models", "unicode_point": "U+2718", "tier": "god", "auto_generated": true, "generator": "god_tier_v1"}, {"symbol": "🤞", "code": "ng:cognitive_modeling:perceptionmodels_op", "fallback": "[PERCEPTIONMODELSOP]", "category": "cognitive_modeling", "name": "perceptionmodels_op", "description": "Cognitive model: perceptionmodels_op in perception_models", "subcategory": "perception_models", "unicode_point": "U+1F91E", "tier": "god", "auto_generated": true, "generator": "god_tier_v1"}, {"symbol": "🤥", "code": "ng:cognitive_modeling:perceptionmodels_fn", "fallback": "[PERCEPTIONMODELSFN]", "category": "cognitive_modeling", "name": "perceptionmodels_fn", "description": "Cognitive model: perceptionmodels_fn in perception_models", "subcategory": "perception_models", "unicode_point": "U+1F925", "tier": "god", "auto_generated": true, "generator": "god_tier_v1"}, {"symbol": "⡹", "code": "ng:cognitive_modeling:perceptionmodels_1", "fallback": "[PERCEPTIONMODELS1]", "category": "cognitive_modeling", "name": "perceptionmodels_1", "description": "Cognitive model: perceptionmodels_1 in perception_models", "subcategory": "perception_models", "unicode_point": "U+2879", "tier": "god", "auto_generated": true, "generator": "god_tier_v1"}, {"symbol": "🚟", "code": "ng:cognitive_modeling:perceptionmodels_2", "fallback": "[PERCEPTIONMODELS2]", "category": "cognitive_modeling", "name": "perceptionmodels_2", "description": "Cognitive model: perceptionmodels_2 in perception_models", "subcategory": "perception_models", "unicode_point": "U+1F69F", "tier": "god", "auto_generated": true, "generator": "god_tier_v1"}, {"symbol": "💨", "code": "ng:cognitive_modeling:perceptionmodels_3", "fallback": "[PERCEPTIONMODELS3]", "category": "cognitive_modeling", "name": "perceptionmodels_3", "description": "Cognitive model: perceptionmodels_3 in perception_models", "subcategory": "perception_models", "unicode_point": "U+1F4A8", "tier": "god", "auto_generated": true, "generator": "god_tier_v1"}, {"symbol": "🚌", "code": "ng:cognitive_modeling:perceptionmodels_4", "fallback": "[PERCEPTIONMODELS4]", "category": "cognitive_modeling", "name": "perceptionmodels_4", "description": "Cognitive model: perceptionmodels_4 in perception_models", "subcategory": "perception_models", "unicode_point": "U+1F68C", "tier": "god", "auto_generated": true, "generator": "god_tier_v1"}, {"symbol": "⬺", "code": "ng:cognitive_modeling:perceptionmodels_5", "fallback": "[PERCEPTIONMODELS5]", "category": "cognitive_modeling", "name": "perceptionmodels_5", "description": "Cognitive model: perceptionmodels_5 in perception_models", "subcategory": "perception_models", "unicode_point": "U+2B3A", "tier": "god", "auto_generated": true, "generator": "god_tier_v1"}, {"symbol": "⠚", "code": "ng:cognitive_modeling:perceptionmodels_6", "fallback": "[PERCEPTIONMODELS6]", "category": "cognitive_modeling", "name": "perceptionmodels_6", "description": "Cognitive model: perceptionmodels_6 in perception_models", "subcategory": "perception_models", "unicode_point": "U+281A", "tier": "god", "auto_generated": true, "generator": "god_tier_v1"}, {"symbol": "⣭", "code": "ng:cognitive_modeling:perceptionmodels_7", "fallback": "[PERCEPTIONMODELS7]", "category": "cognitive_modeling", "name": "perceptionmodels_7", "description": "Cognitive model: perceptionmodels_7 in perception_models", "subcategory": "perception_models", "unicode_point": "U+28ED", "tier": "god", "auto_generated": true, "generator": "god_tier_v1"}, {"symbol": "🐗", "code": "ng:cognitive_modeling:perceptionmodels_8", "fallback": "[PERCEPTIONMODELS8]", "category": "cognitive_modeling", "name": "perceptionmodels_8", "description": "Cognitive model: perceptionmodels_8 in perception_models", "subcategory": "perception_models", "unicode_point": "U+1F417", "tier": "god", "auto_generated": true, "generator": "god_tier_v1"}, {"symbol": "🠁", "code": "ng:cognitive_modeling:perceptionmodels_9", "fallback": "[PERCEPTIONMODELS9]", "category": "cognitive_modeling", "name": "perceptionmodels_9", "description": "Cognitive model: perceptionmodels_9 in perception_models", "subcategory": "perception_models", "unicode_point": "U+1F801", "tier": "god", "auto_generated": true, "generator": "god_tier_v1"}, {"symbol": "❁", "code": "ng:cognitive_modeling:perceptionmodels_10", "fallback": "[PERCEPTIONMODELS10]", "category": "cognitive_modeling", "name": "perceptionmodels_10", "description": "Cognitive model: perceptionmodels_10 in perception_models", "subcategory": "perception_models", "unicode_point": "U+2741", "tier": "god", "auto_generated": true, "generator": "god_tier_v1"}]}