{"batch_info": {"batch_number": 2, "tier": "ultra", "theme": "classes_oop", "target_size": 128, "actual_size": 71, "generated_date": "2025-05-25T12:30:55.084845"}, "symbols": [{"id": "NG0685", "symbol": "⟴", "code": "ng:lifecycle:init", "fallback": "[init]", "description": "Inizializza", "category": "classes_oop", "batch": 2, "tier": "ultra", "approved_date": "2025-05-25", "validation_score": 1.4000000000000001, "status": "pending_approval"}, {"id": "NG0686", "symbol": "⟨", "code": "ng:generic:open", "fallback": "[<]", "description": "Apri generici", "category": "classes_oop", "batch": 2, "tier": "ultra", "approved_date": "2025-05-25", "validation_score": 1.4000000000000001, "status": "pending_approval"}, {"id": "NG0687", "symbol": "⟩", "code": "ng:generic:close", "fallback": "[>]", "description": "<PERSON><PERSON>i", "category": "classes_oop", "batch": 2, "tier": "ultra", "approved_date": "2025-05-25", "validation_score": 1.4000000000000001, "status": "pending_approval"}, {"id": "NG0688", "symbol": "⟪", "code": "ng:generic:nested_open", "fallback": "[<<]", "description": "Apri generici annidati", "category": "classes_oop", "batch": 2, "tier": "ultra", "approved_date": "2025-05-25", "validation_score": 1.4000000000000001, "status": "pending_approval"}, {"id": "NG0689", "symbol": "⟫", "code": "ng:generic:nested_close", "fallback": "[>>]", "description": "Chiudi generici annidati", "category": "classes_oop", "batch": 2, "tier": "ultra", "approved_date": "2025-05-25", "validation_score": 1.4000000000000001, "status": "pending_approval"}, {"id": "NG0690", "symbol": "⟬", "code": "ng:generic:constraint", "fallback": "[where]", "description": "Vin<PERSON>lo <PERSON>o", "category": "classes_oop", "batch": 2, "tier": "ultra", "approved_date": "2025-05-25", "validation_score": 1.4000000000000001, "status": "pending_approval"}, {"id": "NG0691", "symbol": "⟭", "code": "ng:generic:bound", "fallback": "[bound]", "description": "Limite generico", "category": "classes_oop", "batch": 2, "tier": "ultra", "approved_date": "2025-05-25", "validation_score": 1.4000000000000001, "status": "pending_approval"}, {"id": "NG0692", "symbol": "⟮", "code": "ng:generic:variance", "fallback": "[var]", "description": "<PERSON><PERSON><PERSON>", "category": "classes_oop", "batch": 2, "tier": "ultra", "approved_date": "2025-05-25", "validation_score": 1.4000000000000001, "status": "pending_approval"}, {"id": "NG0693", "symbol": "⟯", "code": "ng:generic:covariance", "fallback": "[covar]", "description": "<PERSON><PERSON><PERSON><PERSON>", "category": "classes_oop", "batch": 2, "tier": "ultra", "approved_date": "2025-05-25", "validation_score": 1.4000000000000001, "status": "pending_approval"}, {"id": "NG0694", "symbol": "⟰", "code": "ng:inherit:multi_inherit", "fallback": "[multi]", "description": "Ereditarietà multipla", "category": "classes_oop", "batch": 2, "tier": "ultra", "approved_date": "2025-05-25", "validation_score": 1.3, "status": "pending_approval"}, {"id": "NG0695", "symbol": "⟱", "code": "ng:inherit:diamond", "fallback": "[diamond]", "description": "Diamond problem", "category": "classes_oop", "batch": 2, "tier": "ultra", "approved_date": "2025-05-25", "validation_score": 1.3, "status": "pending_approval"}, {"id": "NG0696", "symbol": "⤊", "code": "ng:inherit:override", "fallback": "[override]", "description": "Override metodo", "category": "classes_oop", "batch": 2, "tier": "ultra", "approved_date": "2025-05-25", "validation_score": 1.3, "status": "pending_approval"}, {"id": "NG0697", "symbol": "⤋", "code": "ng:inherit:virtual", "fallback": "[virtual]", "description": "Metodo virtuale", "category": "classes_oop", "batch": 2, "tier": "ultra", "approved_date": "2025-05-25", "validation_score": 1.3, "status": "pending_approval"}, {"id": "NG0698", "symbol": "⤷", "code": "ng:inject:dependency", "fallback": "[inject]", "description": "Iniezione dipendenza", "category": "classes_oop", "batch": 2, "tier": "ultra", "approved_date": "2025-05-25", "validation_score": 1.3, "status": "pending_approval"}, {"id": "NG0699", "symbol": "⤶", "code": "ng:inject:provide", "fallback": "[provide]", "description": "Fornisce dipendenza", "category": "classes_oop", "batch": 2, "tier": "ultra", "approved_date": "2025-05-25", "validation_score": 1.3, "status": "pending_approval"}, {"id": "NG0700", "symbol": "⤸", "code": "ng:inject:wire", "fallback": "[wire]", "description": "<PERSON><PERSON><PERSON> dip<PERSON>", "category": "classes_oop", "batch": 2, "tier": "ultra", "approved_date": "2025-05-25", "validation_score": 1.3, "status": "pending_approval"}, {"id": "NG0701", "symbol": "⤹", "code": "ng:inject:resolve", "fallback": "[resolve]", "description": "<PERSON><PERSON><PERSON><PERSON>", "category": "classes_oop", "batch": 2, "tier": "ultra", "approved_date": "2025-05-25", "validation_score": 1.3, "status": "pending_approval"}, {"id": "NG0702", "symbol": "⤺", "code": "ng:inject:circular", "fallback": "[circular]", "description": "Dipendenza circolare", "category": "classes_oop", "batch": 2, "tier": "ultra", "approved_date": "2025-05-25", "validation_score": 1.3, "status": "pending_approval"}, {"id": "NG0703", "symbol": "⤼", "code": "ng:inject:factory", "fallback": "[factory]", "description": "Factory", "category": "classes_oop", "batch": 2, "tier": "ultra", "approved_date": "2025-05-25", "validation_score": 1.3, "status": "pending_approval"}, {"id": "NG0704", "symbol": "⤽", "code": "ng:inject:builder", "fallback": "[builder]", "description": "Builder", "category": "classes_oop", "batch": 2, "tier": "ultra", "approved_date": "2025-05-25", "validation_score": 1.3, "status": "pending_approval"}, {"id": "NG0705", "symbol": "⟵", "code": "ng:lifecycle:cleanup", "fallback": "[cleanup]", "description": "Pulizia", "category": "classes_oop", "batch": 2, "tier": "ultra", "approved_date": "2025-05-25", "validation_score": 1.3, "status": "pending_approval"}, {"id": "NG0706", "symbol": "⟶", "code": "ng:lifecycle:finalize", "fallback": "[finalize]", "description": "Finalizza", "category": "classes_oop", "batch": 2, "tier": "ultra", "approved_date": "2025-05-25", "validation_score": 1.3, "status": "pending_approval"}, {"id": "NG0707", "symbol": "⟷", "code": "ng:lifecycle:copy", "fallback": "[copy]", "description": "Copia og<PERSON>", "category": "classes_oop", "batch": 2, "tier": "ultra", "approved_date": "2025-05-25", "validation_score": 1.3, "status": "pending_approval"}, {"id": "NG0708", "symbol": "⟸", "code": "ng:lifecycle:move", "fallback": "[move]", "description": "Sposta oggetto", "category": "classes_oop", "batch": 2, "tier": "ultra", "approved_date": "2025-05-25", "validation_score": 1.3, "status": "pending_approval"}, {"id": "NG0709", "symbol": "⟹", "code": "ng:lifecycle:clone", "fallback": "[clone]", "description": "<PERSON><PERSON><PERSON> og<PERSON>", "category": "classes_oop", "batch": 2, "tier": "ultra", "approved_date": "2025-05-25", "validation_score": 1.3, "status": "pending_approval"}, {"id": "NG0710", "symbol": "⇔", "code": "ng:poly:polymorphic", "fallback": "[poly]", "description": "Polimorfico", "category": "classes_oop", "batch": 2, "tier": "ultra", "approved_date": "2025-05-25", "validation_score": 1.3, "status": "pending_approval"}, {"id": "NG0711", "symbol": "⟾", "code": "ng:method:virtual_call", "fallback": "[vcall]", "description": "Chiamata virtuale", "category": "classes_oop", "batch": 2, "tier": "ultra", "approved_date": "2025-05-25", "validation_score": 1.3, "status": "pending_approval"}, {"id": "NG0712", "symbol": "⟿", "code": "ng:method:direct_call", "fallback": "[dcall]", "description": "<PERSON><PERSON><PERSON>tta", "category": "classes_oop", "batch": 2, "tier": "ultra", "approved_date": "2025-05-25", "validation_score": 1.3, "status": "pending_approval"}, {"id": "NG0713", "symbol": "⤀", "code": "ng:method:override", "fallback": "[override]", "description": "Override", "category": "classes_oop", "batch": 2, "tier": "ultra", "approved_date": "2025-05-25", "validation_score": 1.3, "status": "pending_approval"}, {"id": "NG0714", "symbol": "⤁", "code": "ng:method:overload", "fallback": "[overload]", "description": "Overload", "category": "classes_oop", "batch": 2, "tier": "ultra", "approved_date": "2025-05-25", "validation_score": 1.3, "status": "pending_approval"}, {"id": "NG0715", "symbol": "⤂", "code": "ng:method:super_call", "fallback": "[super]", "description": "Chiamata super", "category": "classes_oop", "batch": 2, "tier": "ultra", "approved_date": "2025-05-25", "validation_score": 1.3, "status": "pending_approval"}, {"id": "NG0716", "symbol": "⤃", "code": "ng:method:this_call", "fallback": "[this]", "description": "<PERSON><PERSON><PERSON> this", "category": "classes_oop", "batch": 2, "tier": "ultra", "approved_date": "2025-05-25", "validation_score": 1.3, "status": "pending_approval"}, {"id": "NG0717", "symbol": "⤄", "code": "ng:method:delegate", "fallback": "[delegate]", "description": "Delega", "category": "classes_oop", "batch": 2, "tier": "ultra", "approved_date": "2025-05-25", "validation_score": 1.3, "status": "pending_approval"}, {"id": "NG0718", "symbol": "⤅", "code": "ng:method:forward", "fallback": "[forward]", "description": "Inoltra", "category": "classes_oop", "batch": 2, "tier": "ultra", "approved_date": "2025-05-25", "validation_score": 1.3, "status": "pending_approval"}, {"id": "NG0719", "symbol": "⚔", "code": "ng:pattern:builder", "fallback": "[builder]", "description": "Builder Pattern", "category": "classes_oop", "batch": 2, "tier": "ultra", "approved_date": "2025-05-25", "validation_score": 1.3, "status": "pending_approval"}, {"id": "NG0720", "symbol": "⚚", "code": "ng:pattern:adapter", "fallback": "[adapter]", "description": "<PERSON><PERSON><PERSON> Pattern", "category": "classes_oop", "batch": 2, "tier": "ultra", "approved_date": "2025-05-25", "validation_score": 1.3, "status": "pending_approval"}, {"id": "NG0721", "symbol": "⚞", "code": "ng:pattern:facade", "fallback": "[facade]", "description": "Facade Pattern", "category": "classes_oop", "batch": 2, "tier": "ultra", "approved_date": "2025-05-25", "validation_score": 1.3, "status": "pending_approval"}, {"id": "NG0722", "symbol": "⚨", "code": "ng:pattern:mediator", "fallback": "[mediator]", "description": "Mediator <PERSON>", "category": "classes_oop", "batch": 2, "tier": "ultra", "approved_date": "2025-05-25", "validation_score": 1.3, "status": "pending_approval"}, {"id": "NG0723", "symbol": "⚭", "code": "ng:pattern:repository", "fallback": "[repo]", "description": "Repository Pattern", "category": "classes_oop", "batch": 2, "tier": "ultra", "approved_date": "2025-05-25", "validation_score": 1.3, "status": "pending_approval"}, {"id": "NG0724", "symbol": "⚱", "code": "ng:pattern:memento", "fallback": "[memento]", "description": "<PERSON><PERSON>", "category": "classes_oop", "batch": 2, "tier": "ultra", "approved_date": "2025-05-25", "validation_score": 1.3, "status": "pending_approval"}, {"id": "NG0725", "symbol": "⊜", "code": "ng:visibility:final", "fallback": "[final]", "description": "Finale", "category": "classes_oop", "batch": 2, "tier": "ultra", "approved_date": "2025-05-25", "validation_score": 1.2000000000000002, "status": "pending_approval"}, {"id": "NG0726", "symbol": "⤻", "code": "ng:inject:singleton", "fallback": "[singleton]", "description": "<PERSON><PERSON>", "category": "classes_oop", "batch": 2, "tier": "ultra", "approved_date": "2025-05-25", "validation_score": 1.2, "status": "pending_approval"}, {"id": "NG0727", "symbol": "⚕", "code": "ng:pattern:prototype", "fallback": "[prototype]", "description": "Prototype Pattern", "category": "classes_oop", "batch": 2, "tier": "ultra", "approved_date": "2025-05-25", "validation_score": 1.2, "status": "pending_approval"}, {"id": "NG0728", "symbol": "⊂", "code": "ng:inherit:subclass", "fallback": "[sub]", "description": "Sottoclasse", "category": "classes_oop", "batch": 2, "tier": "ultra", "approved_date": "2025-05-25", "validation_score": 1.1, "status": "pending_approval"}, {"id": "NG0729", "symbol": "⊇", "code": "ng:inherit:extends", "fallback": "[extends]", "description": "Estende classe", "category": "classes_oop", "batch": 2, "tier": "ultra", "approved_date": "2025-05-25", "validation_score": 1.1, "status": "pending_approval"}, {"id": "NG0730", "symbol": "⬟", "code": "ng:interface:mixin", "fallback": "[mixin]", "description": "Mixin", "category": "classes_oop", "batch": 2, "tier": "ultra", "approved_date": "2025-05-25", "validation_score": 1.1, "status": "pending_approval"}, {"id": "NG0731", "symbol": "⬠", "code": "ng:interface:trait", "fallback": "[trait]", "description": "<PERSON><PERSON><PERSON>", "category": "classes_oop", "batch": 2, "tier": "ultra", "approved_date": "2025-05-25", "validation_score": 1.1, "status": "pending_approval"}, {"id": "NG0732", "symbol": "⬢", "code": "ng:interface:contract", "fallback": "[contract]", "description": "<PERSON><PERSON><PERSON>", "category": "classes_oop", "batch": 2, "tier": "ultra", "approved_date": "2025-05-25", "validation_score": 1.1, "status": "pending_approval"}, {"id": "NG0733", "symbol": "⟐", "code": "ng:compose:has_a", "fallback": "[has_a]", "description": "Ha una relazione", "category": "classes_oop", "batch": 2, "tier": "ultra", "approved_date": "2025-05-25", "validation_score": 1.1, "status": "pending_approval"}, {"id": "NG0734", "symbol": "⟑", "code": "ng:compose:part_of", "fallback": "[part_of]", "description": "Parte di", "category": "classes_oop", "batch": 2, "tier": "ultra", "approved_date": "2025-05-25", "validation_score": 1.1, "status": "pending_approval"}, {"id": "NG0735", "symbol": "⟒", "code": "ng:compose:contains", "fallback": "[contains]", "description": "<PERSON><PERSON><PERSON>", "category": "classes_oop", "batch": 2, "tier": "ultra", "approved_date": "2025-05-25", "validation_score": 1.1, "status": "pending_approval"}, {"id": "NG0736", "symbol": "⟕", "code": "ng:compose:owns", "fallback": "[owns]", "description": "<PERSON><PERSON><PERSON>", "category": "classes_oop", "batch": 2, "tier": "ultra", "approved_date": "2025-05-25", "validation_score": 1.1, "status": "pending_approval"}, {"id": "NG0737", "symbol": "⟖", "code": "ng:compose:owned_by", "fallback": "[owned_by]", "description": "<PERSON><PERSON><PERSON> da", "category": "classes_oop", "batch": 2, "tier": "ultra", "approved_date": "2025-05-25", "validation_score": 1.1, "status": "pending_approval"}, {"id": "NG0738", "symbol": "⊟", "code": "ng:memory:deallocate", "fallback": "[dealloc]", "description": "<PERSON><PERSON><PERSON> memoria", "category": "classes_oop", "batch": 2, "tier": "ultra", "approved_date": "2025-05-25", "validation_score": 1.1, "status": "pending_approval"}, {"id": "NG0739", "symbol": "⊡", "code": "ng:memory:weak_ref", "fallback": "[weak]", "description": "Riferimento debole", "category": "classes_oop", "batch": 2, "tier": "ultra", "approved_date": "2025-05-25", "validation_score": 1.1, "status": "pending_approval"}, {"id": "NG0740", "symbol": "⊣", "code": "ng:memory:shared_ref", "fallback": "[shared]", "description": "Riferimento condiviso", "category": "classes_oop", "batch": 2, "tier": "ultra", "approved_date": "2025-05-25", "validation_score": 1.1, "status": "pending_approval"}, {"id": "NG0741", "symbol": "⊥", "code": "ng:memory:null_ref", "fallback": "[null]", "description": "Riferimento nullo", "category": "classes_oop", "batch": 2, "tier": "ultra", "approved_date": "2025-05-25", "validation_score": 1.1, "status": "pending_approval"}, {"id": "NG0742", "symbol": "⦀", "code": "ng:type:parameter", "fallback": "[T]", "description": "Parametro tipo", "category": "classes_oop", "batch": 2, "tier": "ultra", "approved_date": "2025-05-25", "validation_score": 1.1, "status": "pending_approval"}, {"id": "NG0743", "symbol": "⦁", "code": "ng:type:wildcard", "fallback": "[?]", "description": "Wildcard", "category": "classes_oop", "batch": 2, "tier": "ultra", "approved_date": "2025-05-25", "validation_score": 1.1, "status": "pending_approval"}, {"id": "NG0744", "symbol": "⦂", "code": "ng:type:annotation", "fallback": "[:]", "description": "Annotazione tipo", "category": "classes_oop", "batch": 2, "tier": "ultra", "approved_date": "2025-05-25", "validation_score": 1.1, "status": "pending_approval"}, {"id": "NG0745", "symbol": "⦃", "code": "ng:type:union", "fallback": "[|]", "description": "Unione tipi", "category": "classes_oop", "batch": 2, "tier": "ultra", "approved_date": "2025-05-25", "validation_score": 1.1, "status": "pending_approval"}, {"id": "NG0746", "symbol": "⦄", "code": "ng:type:intersection", "fallback": "[&]", "description": "Intersezione tipi", "category": "classes_oop", "batch": 2, "tier": "ultra", "approved_date": "2025-05-25", "validation_score": 1.1, "status": "pending_approval"}, {"id": "NG0747", "symbol": "⦅", "code": "ng:type:optional", "fallback": "[?]", "description": "Tipo opzionale", "category": "classes_oop", "batch": 2, "tier": "ultra", "approved_date": "2025-05-25", "validation_score": 1.1, "status": "pending_approval"}, {"id": "NG0748", "symbol": "⦆", "code": "ng:type:nullable", "fallback": "[?]", "description": "Tipo nullable", "category": "classes_oop", "batch": 2, "tier": "ultra", "approved_date": "2025-05-25", "validation_score": 1.1, "status": "pending_approval"}, {"id": "NG0749", "symbol": "⦇", "code": "ng:type:array", "fallback": "[[]]", "description": "Tipo array", "category": "classes_oop", "batch": 2, "tier": "ultra", "approved_date": "2025-05-25", "validation_score": 1.1, "status": "pending_approval"}, {"id": "NG0750", "symbol": "⚦", "code": "ng:pattern:template", "fallback": "[template]", "description": "Template Method", "category": "classes_oop", "batch": 2, "tier": "ultra", "approved_date": "2025-05-25", "validation_score": 1.1, "status": "pending_approval"}, {"id": "NG0751", "symbol": "∌", "code": "ng:inherit:not_contains", "fallback": "[!contains]", "description": "Non contiene tipo", "category": "classes_oop", "batch": 2, "tier": "ultra", "approved_date": "2025-05-25", "validation_score": 1.0, "status": "pending_approval"}, {"id": "NG0752", "symbol": "⬡", "code": "ng:interface:signature", "fallback": "[signature]", "description": "Signature", "category": "classes_oop", "batch": 2, "tier": "ultra", "approved_date": "2025-05-25", "validation_score": 1.0, "status": "pending_approval"}, {"id": "NG0753", "symbol": "⟓", "code": "ng:compose:aggregates", "fallback": "[aggregates]", "description": "Aggrega", "category": "classes_oop", "batch": 2, "tier": "ultra", "approved_date": "2025-05-25", "validation_score": 1.0, "status": "pending_approval"}, {"id": "NG0754", "symbol": "⟔", "code": "ng:compose:composed_of", "fallback": "[composed_of]", "description": "Composto da", "category": "classes_oop", "batch": 2, "tier": "ultra", "approved_date": "2025-05-25", "validation_score": 1.0, "status": "pending_approval"}, {"id": "NG0755", "symbol": "⟗", "code": "ng:compose:delegates", "fallback": "[delegates]", "description": "Delega a", "category": "classes_oop", "batch": 2, "tier": "ultra", "approved_date": "2025-05-25", "validation_score": 1.0, "status": "pending_approval"}], "summary": {"inheritance_patterns": 7, "composition_patterns": 26, "polymorphism_patterns": 26, "design_patterns": 8}}