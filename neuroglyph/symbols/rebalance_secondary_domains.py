#!/usr/bin/env python3
"""
NEUROGLYPH FASE 2B - REBALANCE SECONDARY DOMAINS
Ribilanciamento domini secondari prima del completamento finale
"""

import json
import sys
from pathlib import Path
from datetime import datetime
from typing import Dict, List, Any, Tuple
from collections import defaultdict

class SecondaryDomainRebalancer:
    """Rebalancer per domini secondari NEUROGLYPH."""
    
    def __init__(self, registry_path: str = "neuroglyph/core/symbols_registry.json"):
        self.registry_path = Path(registry_path)
        self.registry = {}
        self.rebalance_timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        
        # Target finali per tutti i domini (2048 totali)
        self.final_targets = {
            # Domini primari (già in corso)
            "neural_architectures": 76,
            "quantum_computing": 76,
            "symbolic_ai": 76,
            "meta_programming": 128,
            
            # <PERSON>ini secondari da ribilanciare
            "cognitive_modeling": 64,
            "distributed_systems": 128,
            "type_theory": 64,
            "category_theory": 64,
            "formal_verification": 64,
            "machine_learning": 128,
            "concurrency_advanced": 64,
            "mathematical_structures": 64,
            "philosophical_concepts": 64,
            
            # Domini terziari
            "logical_dynamics": 32,
            "runtime_structures": 32,
            "abstract_operators": 32,
            "linguistic_mappings": 32,
            "cognitive_maps": 32,
            "semantic_networks": 32,
            "knowledge_representation": 32,
            "reasoning_patterns": 32,
            
            # Domini di supporto
            "advanced_coding": 256,  # Già ben rappresentato
            "operator": 64,
            "logic": 64,
            "reasoning": 64,
            "structure": 64,
            "flow": 64,
            "memory": 64,
            
            # Espansione finale
            "reserved_expansion": 64
        }
        
        # Domini critici per LLM simbolico
        self.critical_secondary_domains = {
            "cognitive_modeling": {
                "priority": "HIGH",
                "reason": "Essential for symbolic reasoning and self-awareness",
                "subdomains": [
                    "working_memory", "attention_control", "executive_functions",
                    "metacognitive_monitoring", "cognitive_load", "mental_models",
                    "schema_activation", "cognitive_flexibility", "inhibitory_control",
                    "cognitive_biases", "heuristic_processing", "dual_process_theory"
                ]
            },
            "cognitive_maps": {
                "priority": "HIGH", 
                "reason": "Critical for spatial and conceptual reasoning",
                "subdomains": [
                    "spatial_navigation", "conceptual_mapping", "semantic_spaces",
                    "topological_reasoning", "landmark_recognition", "path_planning",
                    "cognitive_landmarks", "spatial_memory", "mental_rotation",
                    "coordinate_systems", "reference_frames", "spatial_updating"
                ]
            },
            "semantic_networks": {
                "priority": "HIGH",
                "reason": "Foundation for knowledge representation and inference",
                "subdomains": [
                    "concept_nodes", "semantic_relations", "spreading_activation",
                    "semantic_priming", "conceptual_hierarchies", "associative_networks",
                    "semantic_similarity", "concept_formation", "semantic_memory",
                    "lexical_networks", "ontological_structures", "semantic_composition"
                ]
            },
            "knowledge_representation": {
                "priority": "MEDIUM",
                "reason": "Essential for symbolic AI and expert systems",
                "subdomains": [
                    "frames", "scripts", "schemas", "semantic_frames",
                    "conceptual_graphs", "description_logics", "rule_systems",
                    "production_rules", "declarative_knowledge", "procedural_knowledge",
                    "episodic_knowledge", "semantic_knowledge"
                ]
            },
            "reasoning_patterns": {
                "priority": "MEDIUM", 
                "reason": "Core patterns for logical inference and problem solving",
                "subdomains": [
                    "deductive_patterns", "inductive_patterns", "abductive_patterns",
                    "analogical_patterns", "causal_patterns", "temporal_patterns",
                    "spatial_patterns", "probabilistic_patterns", "fuzzy_patterns",
                    "modal_patterns", "counterfactual_patterns", "diagnostic_patterns"
                ]
            }
        }
        
    def load_registry(self) -> bool:
        """Carica il registry dei simboli."""
        try:
            with open(self.registry_path, 'r', encoding='utf-8') as f:
                self.registry = json.load(f)
            print(f"✅ Registry caricato: {len(self.registry.get('approved_symbols', []))} simboli")
            return True
        except Exception as e:
            print(f"❌ Errore caricamento registry: {e}")
            return False
    
    def analyze_current_distribution(self) -> Dict[str, Any]:
        """Analizza distribuzione attuale con focus sui domini secondari."""
        symbols = self.registry.get("approved_symbols", [])
        
        domain_counts = defaultdict(int)
        category_counts = defaultdict(int)
        
        for symbol in symbols:
            category = symbol.get("category", "unknown")
            category_counts[category] += 1
            
            # Estrai dominio dal code
            code = symbol.get("code", "")
            if ":" in code:
                parts = code.split(":")
                if len(parts) >= 2:
                    domain = parts[1]
                    domain_counts[domain] += 1
            else:
                domain_counts[category] += 1
        
        return {
            "total_symbols": len(symbols),
            "domain_counts": dict(domain_counts),
            "category_counts": dict(category_counts)
        }
    
    def calculate_rebalancing_needs(self, current_distribution: Dict[str, Any]) -> Dict[str, Any]:
        """Calcola necessità di ribilanciamento per domini secondari."""
        domain_counts = current_distribution["domain_counts"]
        rebalancing_plan = {}
        
        total_current = current_distribution["total_symbols"]
        total_target = sum(self.final_targets.values())
        remaining_budget = 2048 - total_current
        
        print(f"📊 Budget rimanente: {remaining_budget} simboli")
        
        # Analizza ogni dominio
        for domain, target in self.final_targets.items():
            current = domain_counts.get(domain, 0)
            gap = max(0, target - current)
            
            priority = "LOW"
            if domain in self.critical_secondary_domains:
                priority = self.critical_secondary_domains[domain]["priority"]
            elif gap > 30:
                priority = "MEDIUM"
            elif gap > 50:
                priority = "HIGH"
            
            rebalancing_plan[domain] = {
                "current": current,
                "target": target,
                "gap": gap,
                "priority": priority,
                "coverage_percent": (current / target * 100) if target > 0 else 100,
                "is_critical_secondary": domain in self.critical_secondary_domains
            }
        
        return {
            "total_current": total_current,
            "total_target": total_target,
            "remaining_budget": remaining_budget,
            "domain_plans": rebalancing_plan
        }
    
    def prioritize_domains_for_completion(self, rebalancing_needs: Dict[str, Any]) -> Dict[str, List[str]]:
        """Prioritizza domini per completamento finale."""
        domain_plans = rebalancing_needs["domain_plans"]
        
        priority_groups = {
            "CRITICAL_HIGH": [],      # Domini critici con gap alto
            "CRITICAL_MEDIUM": [],    # Domini critici con gap medio
            "SECONDARY_HIGH": [],     # Domini secondari con gap alto
            "SECONDARY_MEDIUM": [],   # Domini secondari con gap medio
            "MAINTENANCE": []         # Domini da mantenere/completare
        }
        
        for domain, plan in domain_plans.items():
            gap = plan["gap"]
            is_critical = plan["is_critical_secondary"]
            priority = plan["priority"]
            
            if gap == 0:
                continue
                
            if is_critical and gap > 30:
                priority_groups["CRITICAL_HIGH"].append(domain)
            elif is_critical and gap > 10:
                priority_groups["CRITICAL_MEDIUM"].append(domain)
            elif not is_critical and gap > 50:
                priority_groups["SECONDARY_HIGH"].append(domain)
            elif not is_critical and gap > 20:
                priority_groups["SECONDARY_MEDIUM"].append(domain)
            else:
                priority_groups["MAINTENANCE"].append(domain)
        
        # Ordina per gap decrescente in ogni gruppo
        for group in priority_groups:
            priority_groups[group].sort(
                key=lambda d: domain_plans[d]["gap"], 
                reverse=True
            )
        
        return priority_groups
    
    def generate_completion_strategy(self, rebalancing_needs: Dict[str, Any], 
                                   priority_groups: Dict[str, List[str]]) -> Dict[str, Any]:
        """Genera strategia di completamento ottimale."""
        remaining_budget = rebalancing_needs["remaining_budget"]
        domain_plans = rebalancing_needs["domain_plans"]
        
        # Calcola allocazione budget per priorità
        budget_allocation = {
            "CRITICAL_HIGH": int(remaining_budget * 0.40),    # 40% per domini critici
            "CRITICAL_MEDIUM": int(remaining_budget * 0.25),  # 25% per domini critici medi
            "SECONDARY_HIGH": int(remaining_budget * 0.20),   # 20% per secondari importanti
            "SECONDARY_MEDIUM": int(remaining_budget * 0.10), # 10% per secondari medi
            "MAINTENANCE": int(remaining_budget * 0.05)       # 5% per manutenzione
        }
        
        completion_plan = {}
        total_allocated = 0
        
        for priority, domains in priority_groups.items():
            if not domains:
                continue
                
            group_budget = budget_allocation[priority]
            symbols_per_domain = group_budget // len(domains) if domains else 0
            
            for domain in domains:
                plan = domain_plans[domain]
                gap = plan["gap"]
                
                # Alloca simboli considerando gap e budget
                allocated = min(gap, symbols_per_domain)
                if allocated > 0:
                    completion_plan[domain] = {
                        "current": plan["current"],
                        "target": plan["target"],
                        "gap": gap,
                        "allocated": allocated,
                        "priority_group": priority,
                        "completion_percent": (plan["current"] + allocated) / plan["target"] * 100,
                        "is_critical": plan["is_critical_secondary"]
                    }
                    total_allocated += allocated
        
        # Distribuisci budget rimanente ai domini con gap più alto
        remaining_budget_after = remaining_budget - total_allocated
        if remaining_budget_after > 0:
            # Trova domini con gap residuo
            domains_with_residual = []
            for domain, plan in completion_plan.items():
                residual_gap = domain_plans[domain]["gap"] - plan["allocated"]
                if residual_gap > 0:
                    domains_with_residual.append((domain, residual_gap))
            
            # Ordina per gap residuo decrescente
            domains_with_residual.sort(key=lambda x: x[1], reverse=True)
            
            # Distribuisci budget rimanente
            for domain, residual_gap in domains_with_residual:
                if remaining_budget_after <= 0:
                    break
                additional = min(residual_gap, remaining_budget_after)
                completion_plan[domain]["allocated"] += additional
                completion_plan[domain]["completion_percent"] = (
                    (completion_plan[domain]["current"] + completion_plan[domain]["allocated"]) / 
                    completion_plan[domain]["target"] * 100
                )
                remaining_budget_after -= additional
                total_allocated += additional
        
        return {
            "completion_plan": completion_plan,
            "total_allocated": total_allocated,
            "remaining_budget": remaining_budget - total_allocated,
            "budget_allocation": budget_allocation,
            "priority_groups": priority_groups
        }
    
    def save_rebalancing_analysis(self, current_distribution: Dict[str, Any],
                                rebalancing_needs: Dict[str, Any],
                                completion_strategy: Dict[str, Any]) -> str:
        """Salva analisi completa di ribilanciamento."""
        analysis_path = f"neuroglyph/symbols/rebalancing_analysis_{self.rebalance_timestamp}.json"
        
        analysis_data = {
            "rebalance_timestamp": self.rebalance_timestamp,
            "current_state": current_distribution,
            "rebalancing_needs": rebalancing_needs,
            "completion_strategy": completion_strategy,
            "critical_secondary_domains": self.critical_secondary_domains,
            "recommendations": {
                "immediate_actions": [
                    "Prioritizzare domini CRITICAL_HIGH",
                    "Generare simboli per cognitive_modeling, cognitive_maps, semantic_networks",
                    "Mantenere score ≥ 95.0 per tutti i nuovi simboli",
                    "Validare fallback consistency ≤ 8 caratteri"
                ],
                "completion_phases": [
                    "Fase 1: Domini CRITICAL_HIGH (40% budget)",
                    "Fase 2: Domini CRITICAL_MEDIUM (25% budget)", 
                    "Fase 3: Domini SECONDARY_HIGH (20% budget)",
                    "Fase 4: Completamento finale (15% budget)"
                ],
                "quality_assurance": [
                    "Validazione LLM-friendly su batch da 20 simboli",
                    "Test tokenizzazione con Qwen/DeepSeek",
                    "Verifica embedding stability",
                    "Misurazione compressione semantica"
                ]
            }
        }
        
        with open(analysis_path, 'w', encoding='utf-8') as f:
            json.dump(analysis_data, f, indent=2, ensure_ascii=False)
        
        return analysis_path

def main():
    """Esegue analisi di ribilanciamento domini secondari."""
    print("🧠 NEUROGLYPH LLM - FASE 2B: REBALANCE SECONDARY DOMAINS")
    print("🎯 Ribilanciamento domini secondari per completamento ottimale")
    print("=" * 70)
    
    # Crea rebalancer
    rebalancer = SecondaryDomainRebalancer()
    
    # Carica registry
    if not rebalancer.load_registry():
        sys.exit(1)
    
    # Analizza distribuzione attuale
    current_distribution = rebalancer.analyze_current_distribution()
    print(f"📊 Simboli attuali: {current_distribution['total_symbols']}")
    
    # Calcola necessità di ribilanciamento
    rebalancing_needs = rebalancer.calculate_rebalancing_needs(current_distribution)
    print(f"🎯 Budget rimanente: {rebalancing_needs['remaining_budget']} simboli")
    
    # Prioritizza domini
    priority_groups = rebalancer.prioritize_domains_for_completion(rebalancing_needs)
    
    # Genera strategia di completamento
    completion_strategy = rebalancer.generate_completion_strategy(rebalancing_needs, priority_groups)
    
    # Salva analisi
    analysis_path = rebalancer.save_rebalancing_analysis(
        current_distribution, rebalancing_needs, completion_strategy
    )
    print(f"📄 Analisi salvata: {analysis_path}")
    
    # Stampa summary
    print(f"\n📈 SUMMARY RIBILANCIAMENTO:")
    print(f"  Budget totale: {rebalancing_needs['remaining_budget']} simboli")
    print(f"  Budget allocato: {completion_strategy['total_allocated']} simboli")
    
    print(f"\n🔴 DOMINI CRITICI HIGH:")
    for domain in priority_groups["CRITICAL_HIGH"]:
        plan = completion_strategy["completion_plan"].get(domain, {})
        if plan:
            print(f"  • {domain}: +{plan['allocated']} simboli ({plan['completion_percent']:.1f}% target)")
    
    print(f"\n🟠 DOMINI CRITICI MEDIUM:")
    for domain in priority_groups["CRITICAL_MEDIUM"]:
        plan = completion_strategy["completion_plan"].get(domain, {})
        if plan:
            print(f"  • {domain}: +{plan['allocated']} simboli ({plan['completion_percent']:.1f}% target)")
    
    print(f"\n🚀 PROSSIMI PASSI:")
    print(f"  1. Generare simboli per domini CRITICAL_HIGH")
    print(f"  2. Validazione LLM-friendly batch da 20")
    print(f"  3. Test tokenizzazione Qwen/DeepSeek")
    print(f"  4. Procedere con fasi successive")
    
    return True

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
