{"generation_info": {"domain": "mathematical_structures", "count_requested": 64, "count_generated": 64, "timestamp": "2025-05-25T18:21:21.412314", "generator": "god_tier_v1"}, "symbols": [{"symbol": "⭽", "code": "ng:mathematical_structures:ring_op", "fallback": "[RINGOP]", "category": "mathematical_structures", "name": "ring_op", "description": "Mathematical structure: ring_op in algebraic_structures", "subcategory": "algebraic_structures", "unicode_point": "U+2B7D", "tier": "god", "auto_generated": true, "generator": "god_tier_v1"}, {"symbol": "⢂", "code": "ng:mathematical_structures:ring_op_1", "fallback": "[RINGOP1]", "category": "mathematical_structures", "name": "ring_op_1", "description": "Mathematical structure: ring_op_1 in algebraic_structures", "subcategory": "algebraic_structures", "unicode_point": "U+2882", "tier": "god", "auto_generated": true, "generator": "god_tier_v1"}, {"symbol": "🢐", "code": "ng:mathematical_structures:field_meta", "fallback": "[FIELDMETA]", "category": "mathematical_structures", "name": "field_meta", "description": "Mathematical structure: field_meta in algebraic_structures", "subcategory": "algebraic_structures", "unicode_point": "U+1F890", "tier": "god", "auto_generated": true, "generator": "god_tier_v1"}, {"symbol": "😽", "code": "ng:mathematical_structures:algebra_proc", "fallback": "[ALGEBRAPROC]", "category": "mathematical_structures", "name": "algebra_proc", "description": "Mathematical structure: algebra_proc in algebraic_structures", "subcategory": "algebraic_structures", "unicode_point": "U+1F63D", "tier": "god", "auto_generated": true, "generator": "god_tier_v1"}, {"symbol": "⢼", "code": "ng:mathematical_structures:algebra", "fallback": "[ALGEBRA]", "category": "mathematical_structures", "name": "algebra", "description": "Mathematical structure: algebra in algebraic_structures", "subcategory": "algebraic_structures", "unicode_point": "U+28BC", "tier": "god", "auto_generated": true, "generator": "god_tier_v1"}, {"symbol": "❩", "code": "ng:mathematical_structures:ring_meta", "fallback": "[RINGMETA]", "category": "mathematical_structures", "name": "ring_meta", "description": "Mathematical structure: ring_meta in algebraic_structures", "subcategory": "algebraic_structures", "unicode_point": "U+2769", "tier": "god", "auto_generated": true, "generator": "god_tier_v1"}, {"symbol": "🞉", "code": "ng:mathematical_structures:ring", "fallback": "[RING]", "category": "mathematical_structures", "name": "ring", "description": "Mathematical structure: ring in algebraic_structures", "subcategory": "algebraic_structures", "unicode_point": "U+1F789", "tier": "god", "auto_generated": true, "generator": "god_tier_v1"}, {"symbol": "⩂", "code": "ng:mathematical_structures:algebra_1", "fallback": "[ALGEBRA1]", "category": "mathematical_structures", "name": "algebra_1", "description": "Mathematical structure: algebra_1 in algebraic_structures", "subcategory": "algebraic_structures", "unicode_point": "U+2A42", "tier": "god", "auto_generated": true, "generator": "god_tier_v1"}, {"symbol": "👈", "code": "ng:mathematical_structures:group_ctrl", "fallback": "[GROUPCTRL]", "category": "mathematical_structures", "name": "group_ctrl", "description": "Mathematical structure: group_ctrl in algebraic_structures", "subcategory": "algebraic_structures", "unicode_point": "U+1F448", "tier": "god", "auto_generated": true, "generator": "god_tier_v1"}, {"symbol": "🜷", "code": "ng:mathematical_structures:group_ctrl_1", "fallback": "[GROUPCTRL1]", "category": "mathematical_structures", "name": "group_ctrl_1", "description": "Mathematical structure: group_ctrl_1 in algebraic_structures", "subcategory": "algebraic_structures", "unicode_point": "U+1F737", "tier": "god", "auto_generated": true, "generator": "god_tier_v1"}, {"symbol": "🧈", "code": "ng:mathematical_structures:topologicalspaces", "fallback": "[TOPOLOGICALSPACES]", "category": "mathematical_structures", "name": "topologicalspaces", "description": "Mathematical structure: topologicalspaces in topological_spaces", "subcategory": "topological_spaces", "unicode_point": "U+1F9C8", "tier": "god", "auto_generated": true, "generator": "god_tier_v1"}, {"symbol": "⩩", "code": "ng:mathematical_structures:topologicalspaces_1", "fallback": "[TOPOLOGICALSPACES1]", "category": "mathematical_structures", "name": "topologicalspaces_1", "description": "Mathematical structure: topologicalspaces_1 in topological_spaces", "subcategory": "topological_spaces", "unicode_point": "U+2A69", "tier": "god", "auto_generated": true, "generator": "god_tier_v1"}, {"symbol": "⧼", "code": "ng:mathematical_structures:topologicalspaces_2", "fallback": "[TOPOLOGICALSPACES2]", "category": "mathematical_structures", "name": "topologicalspaces_2", "description": "Mathematical structure: topologicalspaces_2 in topological_spaces", "subcategory": "topological_spaces", "unicode_point": "U+29FC", "tier": "god", "auto_generated": true, "generator": "god_tier_v1"}, {"symbol": "⢬", "code": "ng:mathematical_structures:topologicalspaces_3", "fallback": "[TOPOLOGICALSPACES3]", "category": "mathematical_structures", "name": "topologicalspaces_3", "description": "Mathematical structure: topologicalspaces_3 in topological_spaces", "subcategory": "topological_spaces", "unicode_point": "U+28AC", "tier": "god", "auto_generated": true, "generator": "god_tier_v1"}, {"symbol": "⧕", "code": "ng:mathematical_structures:topologicalspaces_4", "fallback": "[TOPOLOGICALSPACES4]", "category": "mathematical_structures", "name": "topologicalspaces_4", "description": "Mathematical structure: topologicalspaces_4 in topological_spaces", "subcategory": "topological_spaces", "unicode_point": "U+29D5", "tier": "god", "auto_generated": true, "generator": "god_tier_v1"}, {"symbol": "⮔", "code": "ng:mathematical_structures:measuretheory_sys", "fallback": "[MEASURETHEORYSYS]", "category": "mathematical_structures", "name": "measuretheory_sys", "description": "Mathematical structure: measuretheory_sys in measure_theory", "subcategory": "measure_theory", "unicode_point": "U+2B94", "tier": "god", "auto_generated": true, "generator": "god_tier_v1"}, {"symbol": "🠩", "code": "ng:mathematical_structures:measuretheory_fn", "fallback": "[MEASURETHEORYFN]", "category": "mathematical_structures", "name": "measuretheory_fn", "description": "Mathematical structure: measuretheory_fn in measure_theory", "subcategory": "measure_theory", "unicode_point": "U+1F829", "tier": "god", "auto_generated": true, "generator": "god_tier_v1"}, {"symbol": "🞨", "code": "ng:mathematical_structures:measuretheory_op", "fallback": "[MEASURETHEORYOP]", "category": "mathematical_structures", "name": "measuretheory_op", "description": "Mathematical structure: measuretheory_op in measure_theory", "subcategory": "measure_theory", "unicode_point": "U+1F7A8", "tier": "god", "auto_generated": true, "generator": "god_tier_v1"}, {"symbol": "🐦", "code": "ng:mathematical_structures:measuretheory_op_1", "fallback": "[MEASURETHEORYOP1]", "category": "mathematical_structures", "name": "measuretheory_op_1", "description": "Mathematical structure: measuretheory_op_1 in measure_theory", "subcategory": "measure_theory", "unicode_point": "U+1F426", "tier": "god", "auto_generated": true, "generator": "god_tier_v1"}, {"symbol": "🡩", "code": "ng:mathematical_structures:measuretheory_meta", "fallback": "[MEASURETHEORYMETA]", "category": "mathematical_structures", "name": "measuretheory_meta", "description": "Mathematical structure: measuretheory_meta in measure_theory", "subcategory": "measure_theory", "unicode_point": "U+1F869", "tier": "god", "auto_generated": true, "generator": "god_tier_v1"}, {"symbol": "🡸", "code": "ng:mathematical_structures:measuretheory_proc", "fallback": "[MEASURETHEORYPROC]", "category": "mathematical_structures", "name": "measuretheory_proc", "description": "Mathematical structure: measuretheory_proc in measure_theory", "subcategory": "measure_theory", "unicode_point": "U+1F878", "tier": "god", "auto_generated": true, "generator": "god_tier_v1"}, {"symbol": "😥", "code": "ng:mathematical_structures:measuretheory", "fallback": "[MEASURETHEORY]", "category": "mathematical_structures", "name": "measuretheory", "description": "Mathematical structure: measuretheory in measure_theory", "subcategory": "measure_theory", "unicode_point": "U+1F625", "tier": "god", "auto_generated": true, "generator": "god_tier_v1"}, {"symbol": "😫", "code": "ng:mathematical_structures:measuretheory_fn_1", "fallback": "[MEASURETHEORYFN1]", "category": "mathematical_structures", "name": "measuretheory_fn_1", "description": "Mathematical structure: measuretheory_fn_1 in measure_theory", "subcategory": "measure_theory", "unicode_point": "U+1F62B", "tier": "god", "auto_generated": true, "generator": "god_tier_v1"}, {"symbol": "❮", "code": "ng:mathematical_structures:measuretheory_proc_1", "fallback": "[MEASURETHEORYPROC1]", "category": "mathematical_structures", "name": "measuretheory_proc_1", "description": "Mathematical structure: measuretheory_proc_1 in measure_theory", "subcategory": "measure_theory", "unicode_point": "U+276E", "tier": "god", "auto_generated": true, "generator": "god_tier_v1"}, {"symbol": "✣", "code": "ng:mathematical_structures:measuretheory_ctrl", "fallback": "[MEASURETHEORYCTRL]", "category": "mathematical_structures", "name": "measuretheory_ctrl", "description": "Mathematical structure: measuretheory_ctrl in measure_theory", "subcategory": "measure_theory", "unicode_point": "U+2723", "tier": "god", "auto_generated": true, "generator": "god_tier_v1"}, {"symbol": "🠥", "code": "ng:mathematical_structures:numbertheory_meta", "fallback": "[NUMBERTHEORYMETA]", "category": "mathematical_structures", "name": "numbertheory_meta", "description": "Mathematical structure: numbertheory_meta in number_theory", "subcategory": "number_theory", "unicode_point": "U+1F825", "tier": "god", "auto_generated": true, "generator": "god_tier_v1"}, {"symbol": "➠", "code": "ng:mathematical_structures:numbertheory_ctrl", "fallback": "[NUMBERTHEORYCTRL]", "category": "mathematical_structures", "name": "numbertheory_ctrl", "description": "Mathematical structure: numbertheory_ctrl in number_theory", "subcategory": "number_theory", "unicode_point": "U+27A0", "tier": "god", "auto_generated": true, "generator": "god_tier_v1"}, {"symbol": "⣁", "code": "ng:mathematical_structures:numbertheory_ctrl_1", "fallback": "[NUMBERTHEORYCTRL1]", "category": "mathematical_structures", "name": "numbertheory_ctrl_1", "description": "Mathematical structure: numbertheory_ctrl_1 in number_theory", "subcategory": "number_theory", "unicode_point": "U+28C1", "tier": "god", "auto_generated": true, "generator": "god_tier_v1"}, {"symbol": "⤚", "code": "ng:mathematical_structures:numbertheory", "fallback": "[NUMBERTHEORY]", "category": "mathematical_structures", "name": "numbertheory", "description": "Mathematical structure: numbertheory in number_theory", "subcategory": "number_theory", "unicode_point": "U+291A", "tier": "god", "auto_generated": true, "generator": "god_tier_v1"}, {"symbol": "⤆", "code": "ng:mathematical_structures:numbertheory_op", "fallback": "[NUMBERTHEORYOP]", "category": "mathematical_structures", "name": "numbertheory_op", "description": "Mathematical structure: numbertheory_op in number_theory", "subcategory": "number_theory", "unicode_point": "U+2906", "tier": "god", "auto_generated": true, "generator": "god_tier_v1"}, {"symbol": "⛌", "code": "ng:mathematical_structures:numbertheory_proc", "fallback": "[NUMBERTHEORYPROC]", "category": "mathematical_structures", "name": "numbertheory_proc", "description": "Mathematical structure: numbertheory_proc in number_theory", "subcategory": "number_theory", "unicode_point": "U+26CC", "tier": "god", "auto_generated": true, "generator": "god_tier_v1"}, {"symbol": "🚲", "code": "ng:mathematical_structures:numbertheory_meta_1", "fallback": "[NUMBERTHEORYMETA1]", "category": "mathematical_structures", "name": "numbertheory_meta_1", "description": "Mathematical structure: numbertheory_meta_1 in number_theory", "subcategory": "number_theory", "unicode_point": "U+1F6B2", "tier": "god", "auto_generated": true, "generator": "god_tier_v1"}, {"symbol": "🠉", "code": "ng:mathematical_structures:numbertheory_core", "fallback": "[NUMBERTHEORYCORE]", "category": "mathematical_structures", "name": "numbertheory_core", "description": "Mathematical structure: numbertheory_core in number_theory", "subcategory": "number_theory", "unicode_point": "U+1F809", "tier": "god", "auto_generated": true, "generator": "god_tier_v1"}, {"symbol": "❰", "code": "ng:mathematical_structures:numbertheory_proc_1", "fallback": "[NUMBERTHEORYPROC1]", "category": "mathematical_structures", "name": "numbertheory_proc_1", "description": "Mathematical structure: numbertheory_proc_1 in number_theory", "subcategory": "number_theory", "unicode_point": "U+2770", "tier": "god", "auto_generated": true, "generator": "god_tier_v1"}, {"symbol": "⩐", "code": "ng:mathematical_structures:numbertheory_proc_2", "fallback": "[NUMBERTHEORYPROC2]", "category": "mathematical_structures", "name": "numbertheory_proc_2", "description": "Mathematical structure: numbertheory_proc_2 in number_theory", "subcategory": "number_theory", "unicode_point": "U+2A50", "tier": "god", "auto_generated": true, "generator": "god_tier_v1"}, {"symbol": "🚷", "code": "ng:mathematical_structures:combinatorics", "fallback": "[COMBINATORICS]", "category": "mathematical_structures", "name": "combinatorics", "description": "Mathematical structure: combinatorics in combinatorics", "subcategory": "combinatorics", "unicode_point": "U+1F6B7", "tier": "god", "auto_generated": true, "generator": "god_tier_v1"}, {"symbol": "⯤", "code": "ng:mathematical_structures:combinatorics_meta", "fallback": "[COMBINATORICSMETA]", "category": "mathematical_structures", "name": "combinatorics_meta", "description": "Mathematical structure: combinatorics_meta in combinatorics", "subcategory": "combinatorics", "unicode_point": "U+2BE4", "tier": "god", "auto_generated": true, "generator": "god_tier_v1"}, {"symbol": "⩦", "code": "ng:mathematical_structures:combinatorics_sys", "fallback": "[COMBINATORICSSYS]", "category": "mathematical_structures", "name": "combinatorics_sys", "description": "Mathematical structure: combinatorics_sys in combinatorics", "subcategory": "combinatorics", "unicode_point": "U+2A66", "tier": "god", "auto_generated": true, "generator": "god_tier_v1"}, {"symbol": "⭿", "code": "ng:mathematical_structures:combinatorics_op", "fallback": "[COMBINATORICSOP]", "category": "mathematical_structures", "name": "combinatorics_op", "description": "Mathematical structure: combinatorics_op in combinatorics", "subcategory": "combinatorics", "unicode_point": "U+2B7F", "tier": "god", "auto_generated": true, "generator": "god_tier_v1"}, {"symbol": "⨑", "code": "ng:mathematical_structures:combinatorics_sys_1", "fallback": "[COMBINATORICSSYS1]", "category": "mathematical_structures", "name": "combinatorics_sys_1", "description": "Mathematical structure: combinatorics_sys_1 in combinatorics", "subcategory": "combinatorics", "unicode_point": "U+2A11", "tier": "god", "auto_generated": true, "generator": "god_tier_v1"}, {"symbol": "👩", "code": "ng:mathematical_structures:combinatorics_proc", "fallback": "[COMBINATORICSPROC]", "category": "mathematical_structures", "name": "combinatorics_proc", "description": "Mathematical structure: combinatorics_proc in combinatorics", "subcategory": "combinatorics", "unicode_point": "U+1F469", "tier": "god", "auto_generated": true, "generator": "god_tier_v1"}, {"symbol": "⩏", "code": "ng:mathematical_structures:combinatorics_fn", "fallback": "[COMBINATORICSFN]", "category": "mathematical_structures", "name": "combinatorics_fn", "description": "Mathematical structure: combinatorics_fn in combinatorics", "subcategory": "combinatorics", "unicode_point": "U+2A4F", "tier": "god", "auto_generated": true, "generator": "god_tier_v1"}, {"symbol": "🞤", "code": "ng:mathematical_structures:combinatorics_core", "fallback": "[COMBINATORICSCORE]", "category": "mathematical_structures", "name": "combinatorics_core", "description": "Mathematical structure: combinatorics_core in combinatorics", "subcategory": "combinatorics", "unicode_point": "U+1F7A4", "tier": "god", "auto_generated": true, "generator": "god_tier_v1"}, {"symbol": "⭅", "code": "ng:mathematical_structures:combinatorics_proc_1", "fallback": "[COMBINATORICSPROC1]", "category": "mathematical_structures", "name": "combinatorics_proc_1", "description": "Mathematical structure: combinatorics_proc_1 in combinatorics", "subcategory": "combinatorics", "unicode_point": "U+2B45", "tier": "god", "auto_generated": true, "generator": "god_tier_v1"}, {"symbol": "⤇", "code": "ng:mathematical_structures:combinatorics_op_1", "fallback": "[COMBINATORICSOP1]", "category": "mathematical_structures", "name": "combinatorics_op_1", "description": "Mathematical structure: combinatorics_op_1 in combinatorics", "subcategory": "combinatorics", "unicode_point": "U+2907", "tier": "god", "auto_generated": true, "generator": "god_tier_v1"}, {"symbol": "🧙", "code": "ng:mathematical_structures:combinatorics_core_1", "fallback": "[COMBINATORICSCORE1]", "category": "mathematical_structures", "name": "combinatorics_core_1", "description": "Mathematical structure: combinatorics_core_1 in combinatorics", "subcategory": "combinatorics", "unicode_point": "U+1F9D9", "tier": "god", "auto_generated": true, "generator": "god_tier_v1"}, {"symbol": "🛴", "code": "ng:mathematical_structures:combinatorics_op_2", "fallback": "[COMBINATORICSOP2]", "category": "mathematical_structures", "name": "combinatorics_op_2", "description": "Mathematical structure: combinatorics_op_2 in combinatorics", "subcategory": "combinatorics", "unicode_point": "U+1F6F4", "tier": "god", "auto_generated": true, "generator": "god_tier_v1"}, {"symbol": "🡤", "code": "ng:mathematical_structures:combinatorics_core_2", "fallback": "[COMBINATORICSCORE2]", "category": "mathematical_structures", "name": "combinatorics_core_2", "description": "Mathematical structure: combinatorics_core_2 in combinatorics", "subcategory": "combinatorics", "unicode_point": "U+1F864", "tier": "god", "auto_generated": true, "generator": "god_tier_v1"}, {"symbol": "🗶", "code": "ng:mathematical_structures:combinatorics_meta_1", "fallback": "[COMBINATORICSMETA1]", "category": "mathematical_structures", "name": "combinatorics_meta_1", "description": "Mathematical structure: combinatorics_meta_1 in combinatorics", "subcategory": "combinatorics", "unicode_point": "U+1F5F6", "tier": "god", "auto_generated": true, "generator": "god_tier_v1"}, {"symbol": "⠲", "code": "ng:mathematical_structures:combinatorics_core_3", "fallback": "[COMBINATORICSCORE3]", "category": "mathematical_structures", "name": "combinatorics_core_3", "description": "Mathematical structure: combinatorics_core_3 in combinatorics", "subcategory": "combinatorics", "unicode_point": "U+2832", "tier": "god", "auto_generated": true, "generator": "god_tier_v1"}, {"symbol": "🟗", "code": "ng:mathematical_structures:combinatorics_ctrl", "fallback": "[COMBINATORICSCTRL]", "category": "mathematical_structures", "name": "combinatorics_ctrl", "description": "Mathematical structure: combinatorics_ctrl in combinatorics", "subcategory": "combinatorics", "unicode_point": "U+1F7D7", "tier": "god", "auto_generated": true, "generator": "god_tier_v1"}, {"symbol": "🥪", "code": "ng:mathematical_structures:combinatorics_fn_1", "fallback": "[COMBINATORICSFN1]", "category": "mathematical_structures", "name": "combinatorics_fn_1", "description": "Mathematical structure: combinatorics_fn_1 in combinatorics", "subcategory": "combinatorics", "unicode_point": "U+1F96A", "tier": "god", "auto_generated": true, "generator": "god_tier_v1"}, {"symbol": "⣕", "code": "ng:mathematical_structures:combinatorics_core_4", "fallback": "[COMBINATORICSCORE4]", "category": "mathematical_structures", "name": "combinatorics_core_4", "description": "Mathematical structure: combinatorics_core_4 in combinatorics", "subcategory": "combinatorics", "unicode_point": "U+28D5", "tier": "god", "auto_generated": true, "generator": "god_tier_v1"}, {"symbol": "⥲", "code": "ng:mathematical_structures:combinatorics_meta_2", "fallback": "[COMBINATORICSMETA2]", "category": "mathematical_structures", "name": "combinatorics_meta_2", "description": "Mathematical structure: combinatorics_meta_2 in combinatorics", "subcategory": "combinatorics", "unicode_point": "U+2972", "tier": "god", "auto_generated": true, "generator": "god_tier_v1"}, {"symbol": "⥉", "code": "ng:mathematical_structures:combinatorics_ctrl_1", "fallback": "[COMBINATORICSCTRL1]", "category": "mathematical_structures", "name": "combinatorics_ctrl_1", "description": "Mathematical structure: combinatorics_ctrl_1 in combinatorics", "subcategory": "combinatorics", "unicode_point": "U+2949", "tier": "god", "auto_generated": true, "generator": "god_tier_v1"}, {"symbol": "🚴", "code": "ng:mathematical_structures:combinatorics_fn_2", "fallback": "[COMBINATORICSFN2]", "category": "mathematical_structures", "name": "combinatorics_fn_2", "description": "Mathematical structure: combinatorics_fn_2 in combinatorics", "subcategory": "combinatorics", "unicode_point": "U+1F6B4", "tier": "god", "auto_generated": true, "generator": "god_tier_v1"}, {"symbol": "⮌", "code": "ng:mathematical_structures:combinatorics_meta_3", "fallback": "[COMBINATORICSMETA3]", "category": "mathematical_structures", "name": "combinatorics_meta_3", "description": "Mathematical structure: combinatorics_meta_3 in combinatorics", "subcategory": "combinatorics", "unicode_point": "U+2B8C", "tier": "god", "auto_generated": true, "generator": "god_tier_v1"}, {"symbol": "🝿", "code": "ng:mathematical_structures:combinatorics_op_3", "fallback": "[COMBINATORICSOP3]", "category": "mathematical_structures", "name": "combinatorics_op_3", "description": "Mathematical structure: combinatorics_op_3 in combinatorics", "subcategory": "combinatorics", "unicode_point": "U+1F77F", "tier": "god", "auto_generated": true, "generator": "god_tier_v1"}, {"symbol": "🜵", "code": "ng:mathematical_structures:combinatorics_core_5", "fallback": "[COMBINATORICSCORE5]", "category": "mathematical_structures", "name": "combinatorics_core_5", "description": "Mathematical structure: combinatorics_core_5 in combinatorics", "subcategory": "combinatorics", "unicode_point": "U+1F735", "tier": "god", "auto_generated": true, "generator": "god_tier_v1"}, {"symbol": "🥦", "code": "ng:mathematical_structures:combinatorics_op_4", "fallback": "[COMBINATORICSOP4]", "category": "mathematical_structures", "name": "combinatorics_op_4", "description": "Mathematical structure: combinatorics_op_4 in combinatorics", "subcategory": "combinatorics", "unicode_point": "U+1F966", "tier": "god", "auto_generated": true, "generator": "god_tier_v1"}, {"symbol": "⫶", "code": "ng:mathematical_structures:combinatorics_fn_3", "fallback": "[COMBINATORICSFN3]", "category": "mathematical_structures", "name": "combinatorics_fn_3", "description": "Mathematical structure: combinatorics_fn_3 in combinatorics", "subcategory": "combinatorics", "unicode_point": "U+2AF6", "tier": "god", "auto_generated": true, "generator": "god_tier_v1"}, {"symbol": "⤲", "code": "ng:mathematical_structures:combinatorics_fn_4", "fallback": "[COMBINATORICSFN4]", "category": "mathematical_structures", "name": "combinatorics_fn_4", "description": "Mathematical structure: combinatorics_fn_4 in combinatorics", "subcategory": "combinatorics", "unicode_point": "U+2932", "tier": "god", "auto_generated": true, "generator": "god_tier_v1"}, {"symbol": "🦩", "code": "ng:mathematical_structures:combinatorics_ctrl_2", "fallback": "[COMBINATORICSCTRL2]", "category": "mathematical_structures", "name": "combinatorics_ctrl_2", "description": "Mathematical structure: combinatorics_ctrl_2 in combinatorics", "subcategory": "combinatorics", "unicode_point": "U+1F9A9", "tier": "god", "auto_generated": true, "generator": "god_tier_v1"}, {"symbol": "⮰", "code": "ng:mathematical_structures:combinatorics_meta_4", "fallback": "[COMBINATORICSMETA4]", "category": "mathematical_structures", "name": "combinatorics_meta_4", "description": "Mathematical structure: combinatorics_meta_4 in combinatorics", "subcategory": "combinatorics", "unicode_point": "U+2BB0", "tier": "god", "auto_generated": true, "generator": "god_tier_v1"}]}