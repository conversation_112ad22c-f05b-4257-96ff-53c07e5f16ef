#!/usr/bin/env python3
"""
NEUROGLYPH LLM - Apply Batch Direct
==================================

Applica direttamente batch ULTRA al registry con validazione semplificata.
Approccio pragmatico per procedere con l'implementazione.
"""

import json
from pathlib import Path
from datetime import datetime

def load_files():
    """Carica registry e proposta"""
    registry_path = Path("neuroglyph/core/symbols_registry.json")
    proposal_path = Path("neuroglyph/symbols/ultra_batch3_proposal.json")

    # Carica registry
    with open(registry_path, 'r', encoding='utf-8') as f:
        registry = json.load(f)

    # Carica proposta
    with open(proposal_path, 'r', encoding='utf-8') as f:
        proposal = json.load(f)

    return registry, proposal

def validate_symbol_simple(symbol, existing_symbols, existing_codes):
    """Validazione semplificata simbolo"""
    issues = []

    symbol_char = symbol.get("symbol", "")
    ng_code = symbol.get("code", "")

    # 1. Simbolo non vuoto
    if not symbol_char:
        issues.append("empty_symbol")

    # 2. Codice ng: valido
    if not ng_code.startswith("ng:") or len(ng_code.split(":")) < 3:
        issues.append("invalid_ng_code")

    # 3. Unicità
    if symbol_char in existing_symbols:
        issues.append("duplicate_symbol")

    if ng_code in existing_codes:
        issues.append("duplicate_code")

    # 4. Fallback presente
    if not symbol.get("fallback"):
        issues.append("missing_fallback")

    # 5. Descrizione presente
    if not symbol.get("description"):
        issues.append("missing_description")

    return len(issues) == 0, issues

def apply_batch():
    """Applica batch al registry"""
    print("🚀 NEUROGLYPH LLM - Apply Batch Direct")
    print("🎯 Applicazione diretta Batch 1 ULTRA")
    print("=" * 70)

    # Carica files
    registry, proposal = load_files()

    current_symbols = registry.get("approved_symbols", [])
    proposed_symbols = proposal.get("symbols", [])

    print(f"📊 Simboli attuali: {len(current_symbols)}")
    print(f"📊 Simboli proposti: {len(proposed_symbols)}")

    # Crea set per controllo unicità
    existing_symbols = {s["symbol"] for s in current_symbols}
    existing_codes = {s["code"] for s in current_symbols}

    # Valida e filtra simboli
    approved = []
    rejected = []

    print(f"\n🔍 VALIDAZIONE SIMBOLI:")
    for i, symbol in enumerate(proposed_symbols):
        is_valid, issues = validate_symbol_simple(symbol, existing_symbols, existing_codes)

        if is_valid:
            # Approva simbolo
            approved_symbol = symbol.copy()
            approved_symbol["status"] = "approved"
            approved_symbol["validation_date"] = datetime.now().strftime("%Y-%m-%d")
            approved.append(approved_symbol)

            # Aggiorna set per controlli successivi
            existing_symbols.add(symbol["symbol"])
            existing_codes.add(symbol["code"])

            print(f"   ✅ {symbol['symbol']} - APPROVATO")
        else:
            rejected.append({
                "symbol": symbol,
                "issues": issues
            })
            print(f"   ❌ {symbol['symbol']} - RIGETTATO: {', '.join(issues)}")

    approval_rate = (len(approved) / len(proposed_symbols)) * 100

    print(f"\n📊 RISULTATI VALIDAZIONE:")
    print(f"   ✅ Approvati: {len(approved)}/{len(proposed_symbols)} ({approval_rate:.1f}%)")
    print(f"   ❌ Rigettati: {len(rejected)}")

    if len(approved) == 0:
        print("⚠️ Nessun simbolo approvato - terminazione")
        return False

    # Backup registry
    backup_path = Path("neuroglyph/core/symbols_registry_backup.json")
    with open(backup_path, 'w', encoding='utf-8') as f:
        json.dump(registry, f, indent=2, ensure_ascii=False)
    print(f"💾 Backup creato: {backup_path}")

    # Applica simboli approvati
    print(f"\n💾 APPLICAZIONE SIMBOLI AL REGISTRY:")

    # Aggiungi simboli approvati
    for symbol in approved:
        current_symbols.append(symbol)

    # Aggiorna stats
    registry["approved_symbols"] = current_symbols
    registry["stats"] = {
        "total_submissions": len(current_symbols),
        "approved": len(current_symbols),
        "rejected": 0,
        "pending": 0,
        "last_update": datetime.now().strftime("%Y-%m-%d"),
        "last_batch": {
            "batch_number": 3,
            "tier": "ultra",
            "symbols_added": len(approved),
            "date": datetime.now().strftime("%Y-%m-%d")
        }
    }

    # Salva registry aggiornato
    registry_path = Path("neuroglyph/core/symbols_registry.json")
    with open(registry_path, 'w', encoding='utf-8') as f:
        json.dump(registry, f, indent=2, ensure_ascii=False)

    print(f"✅ Registry aggiornato con {len(approved)} simboli")
    print(f"📊 Simboli totali: {len(current_symbols)}")

    # Calcola progresso ULTRA
    ultra_progress = len(current_symbols) / 1024 * 100
    print(f"🎯 Progresso ULTRA Tier: {len(current_symbols)}/1024 ({ultra_progress:.1f}%)")

    # Salva report
    report = {
        "batch_info": {
            "batch_number": 3,
            "tier": "ultra",
            "theme": "data_structures",
            "application_date": datetime.now().isoformat()
        },
        "results": {
            "proposed": len(proposed_symbols),
            "approved": len(approved),
            "rejected": len(rejected),
            "approval_rate": approval_rate
        },
        "approved_symbols": approved,
        "rejected_symbols": rejected,
        "registry_stats": {
            "symbols_before": len(current_symbols) - len(approved),
            "symbols_after": len(current_symbols),
            "ultra_progress": ultra_progress
        }
    }

    report_path = Path("neuroglyph/symbols/batch3_application_report.json")
    with open(report_path, 'w', encoding='utf-8') as f:
        json.dump(report, f, indent=2, ensure_ascii=False)

    print(f"📊 Report salvato: {report_path}")

    return True

def show_sample_symbols():
    """Mostra esempi simboli aggiunti"""
    print(f"\n🎯 ESEMPI SIMBOLI AGGIUNTI:")

    registry_path = Path("neuroglyph/core/symbols_registry.json")
    with open(registry_path, 'r', encoding='utf-8') as f:
        registry = json.load(f)

    symbols = registry.get("approved_symbols", [])

    # Mostra ultimi 10 simboli (quelli appena aggiunti)
    recent_symbols = symbols[-10:]

    for i, symbol in enumerate(recent_symbols):
        print(f"   {i+1}. {symbol['symbol']} ({symbol['code']}) - {symbol['description']}")

def main():
    """Applicazione principale"""
    success = apply_batch()

    if success:
        show_sample_symbols()

        print(f"\n🎉 BATCH 1 ULTRA APPLICATO CON SUCCESSO!")
        print(f"✅ NEUROGLYPH LLM ora ha simboli per async/concurrency")
        print(f"🚀 Pronto per Batch 2: Classes + OOP")

        # Mostra prossimi passi
        print(f"\n📋 PROSSIMI PASSI:")
        print(f"   1. 🧪 Test simboli con dataset enhanced")
        print(f"   2. 🔧 Generazione Batch 2 (Classes + OOP)")
        print(f"   3. 🎯 Continuazione verso ULTRA Tier (1024 simboli)")

    else:
        print(f"❌ Applicazione batch fallita")

    return success

if __name__ == "__main__":
    main()
