# 🔧 NEUROGLYPH COLAB - CELLA 1: SETUP E DIAGNOSTICA
# ================================================
# Setup completo e diagnostica del problema tokenizer

# Installa dipendenze
!pip install -q transformers torch unsloth datasets accelerate bitsandbytes

import json
import gzip
from pathlib import Path
from transformers import AutoTokenizer, AutoModelForCausalLM
import torch
from google.colab import drive
from rich.console import Console
from rich.table import Table
from rich.panel import Panel

# Setup console
console = Console()

# Mount Google Drive
drive.mount('/content/drive')

# Paths NEUROGLYPH - CORRETTI
NEUROGLYPH_BASE = "/content/drive/MyDrive/NEUROGLYPH"
MODEL_PATH = f"{NEUROGLYPH_BASE}/export/merged-final"  # Modello merged finale
DATASET_PATH = f"{NEUROGLYPH_BASE}/neuroglyph_training_unsloth_ULTRA.jsonl.gz"
REGISTRY_PATH = f"{NEUROGLYPH_BASE}/locked_registry_godmode_v9.json"  # Registry corretto

console.print(Panel.fit("🧠 NEUROGLYPH DIAGNOSTICA INIZIALE", style="bold blue"))

# 1. Verifica esistenza file
console.print("\n📁 [bold]Verifica File Esistenti[/bold]")

files_to_check = {
    "Modello Fine-tuned": MODEL_PATH,
    "Dataset Training": DATASET_PATH, 
    "Registry Simboli": REGISTRY_PATH
}

file_status = {}
for name, path in files_to_check.items():
    exists = Path(path).exists()
    file_status[name] = exists
    status = "✅" if exists else "❌"
    console.print(f"{status} {name}: {path}")

# 2. Carica Registry Simboli
console.print("\n🔣 [bold]Caricamento Registry Simboli[/bold]")

try:
    # Carica registry (JSON normale, non gzipped)
    with open(REGISTRY_PATH, 'r', encoding='utf-8') as f:
        registry_data = json.load(f)
    
    # Estrai simboli critici
    if 'approved_symbols' in registry_data:
        symbols = registry_data['approved_symbols']
        critical_symbols = [s['symbol'] for s in symbols if s.get('valid', False)]
    else:
        critical_symbols = []
    
    console.print(f"✅ Registry caricato: {len(critical_symbols)} simboli critici")
    
    # Mostra primi simboli
    sample_symbols = critical_symbols[:10]
    console.print(f"🔍 Primi simboli: {sample_symbols}")
    
except Exception as e:
    console.print(f"❌ Errore caricamento registry: {e}")
    critical_symbols = []

# 3. Carica Dataset Training
console.print("\n📊 [bold]Analisi Dataset Training[/bold]")

try:
    with gzip.open(DATASET_PATH, 'rt', encoding='utf-8') as f:
        dataset_samples = []
        for i, line in enumerate(f):
            if i >= 5:  # Solo primi 5 esempi
                break
            dataset_samples.append(json.loads(line))
    
    console.print(f"✅ Dataset caricato: {len(dataset_samples)} esempi (sample)")
    
    # Analizza simboli nel dataset
    symbols_in_dataset = set()
    for sample in dataset_samples:
        text = sample.get('output', '') + sample.get('instruction', '')
        for symbol in critical_symbols:
            if symbol in text:
                symbols_in_dataset.add(symbol)
    
    console.print(f"🔍 Simboli trovati nel dataset: {len(symbols_in_dataset)}")
    
except Exception as e:
    console.print(f"❌ Errore caricamento dataset: {e}")
    dataset_samples = []

# 4. Test Tokenizer Base (quello usato nel training)
console.print("\n🔤 [bold]Test Tokenizer Base Qwen[/bold]")

try:
    base_tokenizer = AutoTokenizer.from_pretrained("Qwen/Qwen2.5-Coder-1.5B-Instruct")
    console.print(f"✅ Tokenizer base caricato: {len(base_tokenizer.vocab)} tokens")
    
    # Test simboli critici con tokenizer base
    tokenization_table = Table(title="🔍 Tokenizzazione Simboli Critici (Tokenizer Base)")
    tokenization_table.add_column("Simbolo", style="cyan")
    tokenization_table.add_column("Tokens", style="yellow")
    tokenization_table.add_column("Count", style="red")
    tokenization_table.add_column("Status", style="green")
    
    single_token_count = 0
    multi_token_count = 0
    
    test_symbols = critical_symbols[:15] if critical_symbols else ["⚡", "🔄", "📊", "∑", "∫"]
    
    for symbol in test_symbols:
        tokens = base_tokenizer.encode(symbol, add_special_tokens=False)
        token_count = len(tokens)
        
        if token_count == 1:
            single_token_count += 1
            status = "✅ SINGLE"
        else:
            multi_token_count += 1
            status = f"❌ {token_count} TOKENS"
        
        tokenization_table.add_row(
            symbol,
            str(tokens[:3]) + "..." if len(tokens) > 3 else str(tokens),
            str(token_count),
            status
        )
    
    console.print(tokenization_table)
    
    # Statistiche tokenizzazione
    total_tested = len(test_symbols)
    single_rate = single_token_count / total_tested if total_tested > 0 else 0
    
    console.print(f"\n📊 [bold]Statistiche Tokenizzazione Base:[/bold]")
    console.print(f"✅ Single tokens: {single_token_count}/{total_tested} ({single_rate:.1%})")
    console.print(f"❌ Multi tokens: {multi_token_count}/{total_tested}")
    
    if single_rate < 0.5:
        console.print("🚨 [bold red]PROBLEMA CRITICO: Maggioranza simboli frammentati![/bold red]")
    elif single_rate < 0.8:
        console.print("⚠️ [bold yellow]ATTENZIONE: Molti simboli frammentati[/bold yellow]")
    else:
        console.print("✅ [bold green]BUONO: Maggioranza simboli single token[/bold green]")
    
except Exception as e:
    console.print(f"❌ Errore test tokenizer base: {e}")
    base_tokenizer = None

# 5. Verifica Modello Fine-tuned
console.print("\n🧠 [bold]Verifica Modello Fine-tuned[/bold]")

model_files = [
    "config.json",
    "model.safetensors", 
    "adapter_config.json",
    "adapter_model.safetensors",
    "tokenizer.json",
    "tokenizer_config.json"
]

model_status = {}
for file_name in model_files:
    file_path = Path(MODEL_PATH) / file_name
    exists = file_path.exists()
    model_status[file_name] = exists
    status = "✅" if exists else "❌"
    console.print(f"{status} {file_name}")

# 6. Diagnosi Problema
console.print("\n🔍 [bold]DIAGNOSI PROBLEMA[/bold]")

issues_found = []
recommendations = []

if single_rate < 0.5:
    issues_found.append("CRITICAL: Simboli frammentati in subtokens durante training")
    recommendations.append("🔧 Ricreare tokenizer con simboli come special tokens")
    recommendations.append("🔄 Re-training con tokenizer corretto")

if not model_status.get("tokenizer.json", False):
    issues_found.append("WARNING: Tokenizer del modello fine-tuned mancante")
    recommendations.append("📝 Estrarre/ricreare tokenizer dal modello")

if len(critical_symbols) == 0:
    issues_found.append("ERROR: Registry simboli non caricato")
    recommendations.append("🔣 Verificare path registry simboli")

# Mostra diagnosi
if issues_found:
    console.print("\n🚨 [bold red]PROBLEMI IDENTIFICATI:[/bold red]")
    for i, issue in enumerate(issues_found, 1):
        console.print(f"{i}. {issue}")
    
    console.print("\n🔧 [bold blue]RACCOMANDAZIONI:[/bold blue]")
    for i, rec in enumerate(recommendations, 1):
        console.print(f"{i}. {rec}")
else:
    console.print("✅ [bold green]Nessun problema critico identificato[/bold green]")

# 7. Prepara variabili per celle successive
console.print("\n📋 [bold]Setup Variabili Globali[/bold]")

# Salva variabili per celle successive
globals()['NEUROGLYPH_BASE'] = NEUROGLYPH_BASE
globals()['MODEL_PATH'] = MODEL_PATH
globals()['critical_symbols'] = critical_symbols
globals()['base_tokenizer'] = base_tokenizer
globals()['single_rate'] = single_rate if 'single_rate' in locals() else 0
globals()['model_status'] = model_status

console.print("✅ Variabili globali configurate per celle successive")

# Summary finale
console.print(Panel.fit(
    f"🎯 SETUP COMPLETATO\n"
    f"📊 Simboli critici: {len(critical_symbols)}\n"
    f"🔤 Single token rate: {single_rate:.1%}\n"
    f"🧠 Modello disponibile: {'✅' if model_status.get('config.json') else '❌'}\n"
    f"🔧 Azione richiesta: {'Re-training necessario' if single_rate < 0.5 else 'Test modello'}",
    style="bold green"
))

print("🚀 Pronto per CELLA 2: Test Tokenizer Fine-tuned")
