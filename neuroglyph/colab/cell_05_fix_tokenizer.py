# 🔧 NEUROGLYPH COLAB - CELLA 5: SOLUZIONE TOKENIZER (OPZIONALE)
# ================================================================
# Crea tokenizer corretto e prepara re-training se necessario

from transformers import AutoTokenizer
from rich.console import Console
from rich.panel import Panel
from rich.table import Table
import json
import gzip

console = Console()
console.print(Panel.fit("🔧 SOLUZIONE TOKENIZER NEUROGLYPH", style="bold blue"))

# Questa cella è opzionale - eseguire solo se il tokenizer ha problemi gravi
console.print("⚠️ [bold yellow]ATTENZIONE: Questa cella è opzionale[/bold yellow]")
console.print("🔄 Eseguire solo se il tokenizer score < 0.6")

# Verifica se è necessario
if 'tokenizer_score' in globals() and tokenizer_score >= 0.6:
    console.print("✅ [bold green]Tokenizer funziona bene - skip questa cella[/bold green]")
    print("🚀 Tokenizer OK - procedere con deployment")
else:
    console.print("🔧 [bold red]Tokenizer problematico - procedendo con fix[/bold red]")
    
    # 1. Carica Registry Simboli
    console.print("\n🔣 [bold]Caricamento Registry Simboli Completo[/bold]")
    
    try:
        # Carica registry (JSON normale, non gzipped)
        with open(REGISTRY_PATH, 'r', encoding='utf-8') as f:
            registry_data = json.load(f)
        
        # Estrai tutti i simboli approvati
        if 'approved_symbols' in registry_data:
            all_symbols = [s['symbol'] for s in registry_data['approved_symbols'] if s.get('valid', False)]
        else:
            all_symbols = critical_symbols
        
        console.print(f"✅ Caricati {len(all_symbols)} simboli dal registry")
        
        # Separa simboli per tipo
        unicode_symbols = [s for s in all_symbols if not s.startswith('ng:')]
        ng_symbols = [s for s in all_symbols if s.startswith('ng:')]
        
        console.print(f"🔣 Simboli Unicode: {len(unicode_symbols)}")
        console.print(f"🧠 Simboli ng:: {len(ng_symbols)}")
        
    except Exception as e:
        console.print(f"❌ Errore caricamento registry: {e}")
        all_symbols = critical_symbols if 'critical_symbols' in globals() else []
    
    # 2. Crea Tokenizer Corretto
    console.print("\n🔧 [bold]Creazione Tokenizer Corretto[/bold]")
    
    try:
        # Carica tokenizer base
        base_tokenizer = AutoTokenizer.from_pretrained("Qwen/Qwen2.5-Coder-1.5B-Instruct")
        original_vocab_size = len(base_tokenizer.vocab)
        
        console.print(f"📊 Tokenizer base: {original_vocab_size} tokens")
        
        # Aggiungi simboli come special tokens
        special_tokens_dict = {
            "additional_special_tokens": all_symbols
        }
        
        num_added = base_tokenizer.add_special_tokens(special_tokens_dict)
        new_vocab_size = len(base_tokenizer.vocab)
        
        console.print(f"✅ Aggiunti {num_added} special tokens")
        console.print(f"📈 Vocab size: {original_vocab_size} → {new_vocab_size}")
        
        # 3. Verifica Tokenizzazione Corretta
        console.print("\n🔍 [bold]Verifica Tokenizzazione Corretta[/bold]")
        
        verification_table = Table(title="🔍 Verifica Simboli come Single Tokens")
        verification_table.add_column("Simbolo", style="cyan")
        verification_table.add_column("Token ID", style="yellow")
        verification_table.add_column("Decoded", style="green")
        verification_table.add_column("Status", style="bold")
        
        perfect_count = 0
        test_symbols = all_symbols[:20]  # Test primi 20
        
        for symbol in test_symbols:
            try:
                tokens = base_tokenizer.encode(symbol, add_special_tokens=False)
                decoded = base_tokenizer.decode(tokens, skip_special_tokens=True)
                
                if len(tokens) == 1 and decoded.strip() == symbol:
                    perfect_count += 1
                    status = "✅ PERFECT"
                    token_id = tokens[0]
                elif len(tokens) == 1:
                    status = "⚠️ DECODE_ISSUE"
                    token_id = tokens[0]
                else:
                    status = f"❌ {len(tokens)} TOKENS"
                    token_id = "MULTI"
                
                verification_table.add_row(
                    symbol,
                    str(token_id),
                    decoded[:15] + "..." if len(decoded) > 15 else decoded,
                    status
                )
                
            except Exception as e:
                verification_table.add_row(
                    symbol,
                    "ERROR",
                    str(e)[:15],
                    "❌ ERROR"
                )
        
        console.print(verification_table)
        
        success_rate = perfect_count / len(test_symbols)
        console.print(f"📊 [bold]Success Rate: {perfect_count}/{len(test_symbols)} ({success_rate:.1%})[/bold]")
        
        # 4. Salva Tokenizer Corretto
        console.print("\n💾 [bold]Salvataggio Tokenizer Corretto[/bold]")
        
        corrected_tokenizer_path = "/content/drive/MyDrive/NEUROGLYPH/corrected_tokenizer"
        
        try:
            base_tokenizer.save_pretrained(corrected_tokenizer_path)
            console.print(f"✅ Tokenizer corretto salvato in: {corrected_tokenizer_path}")
            
            # Salva configurazione
            config = {
                "original_vocab_size": original_vocab_size,
                "new_vocab_size": new_vocab_size,
                "symbols_added": num_added,
                "success_rate": success_rate,
                "symbols_list": all_symbols,
                "creation_timestamp": datetime.now().isoformat()
            }
            
            config_path = f"{corrected_tokenizer_path}/neuroglyph_config.json"
            with open(config_path, 'w') as f:
                json.dump(config, f, indent=2)
            
            console.print(f"📋 Configurazione salvata: {config_path}")
            
        except Exception as e:
            console.print(f"❌ Errore salvataggio: {e}")
        
        # 5. Genera Script Re-training
        console.print("\n📝 [bold]Generazione Script Re-training[/bold]")
        
        retraining_script = f'''# 🔄 NEUROGLYPH RE-TRAINING SCRIPT
# ================================
# Script per re-training con tokenizer corretto

from transformers import AutoTokenizer, AutoModelForCausalLM
from unsloth import FastLanguageModel
import torch

def retrain_neuroglyph_with_correct_tokenizer():
    print("🔄 Re-training NEUROGLYPH con tokenizer corretto...")
    
    # 1. Carica tokenizer corretto
    tokenizer = AutoTokenizer.from_pretrained("{corrected_tokenizer_path}")
    print(f"✅ Tokenizer corretto caricato: {{len(tokenizer.vocab)}} tokens")
    
    # 2. Carica modello base
    model, _ = FastLanguageModel.from_pretrained(
        model_name="Qwen/Qwen2.5-Coder-1.5B-Instruct",
        max_seq_length=2048,
        dtype=None,
        load_in_4bit=True,
    )
    
    # 3. CRITICAL: Ridimensiona embedding layer
    model.resize_token_embeddings(len(tokenizer.vocab))
    print(f"🔧 Embedding layer ridimensionato a {{len(tokenizer.vocab)}}")
    
    # 4. Test simboli
    test_symbols = {all_symbols[:5]}
    for symbol in test_symbols:
        tokens = tokenizer.encode(symbol, add_special_tokens=False)
        print(f"🔍 {{symbol}} → {{tokens}} ({{len(tokens)}} tokens)")
    
    # 5. Configura LoRA
    model = FastLanguageModel.get_peft_model(
        model,
        r=16,
        target_modules=["q_proj", "k_proj", "v_proj", "o_proj",
                       "gate_proj", "up_proj", "down_proj"],
        lora_alpha=16,
        lora_dropout=0,
        bias="none",
        use_gradient_checkpointing="unsloth",
        random_state=3407,
    )
    
    print("🚀 Setup completato - pronto per re-training!")
    print("📊 Usa il dataset: {DATASET_PATH}")
    print("🔧 Tokenizer corretto: {corrected_tokenizer_path}")
    
    return model, tokenizer

# Esegui setup
if __name__ == "__main__":
    model, tokenizer = retrain_neuroglyph_with_correct_tokenizer()
'''
        
        script_path = "/content/drive/MyDrive/NEUROGLYPH/retrain_with_correct_tokenizer.py"
        with open(script_path, 'w') as f:
            f.write(retraining_script)
        
        console.print(f"📝 Script re-training salvato: {script_path}")
        
        # 6. Raccomandazioni
        console.print("\n🔧 [bold]Raccomandazioni per Re-training[/bold]")
        
        recommendations = [
            "🔄 Eseguire re-training con tokenizer corretto",
            "📊 Usare stesso dataset ma con tokenizer aggiornato",
            "🧪 Validare che simboli siano single tokens",
            "⚡ Monitorare miglioramento retention simboli",
            "🎯 Target: >90% single token rate per simboli"
        ]
        
        for i, rec in enumerate(recommendations, 1):
            console.print(f"{i}. {rec}")
        
        # 7. Verdetto
        if success_rate >= 0.9:
            console.print("\n🏆 [bold green]TOKENIZER CORRETTO CREATO CON SUCCESSO![/bold green]")
            console.print("🚀 Pronto per re-training di alta qualità")
        elif success_rate >= 0.7:
            console.print("\n✅ [bold yellow]TOKENIZER MIGLIORATO SIGNIFICATIVAMENTE[/bold yellow]")
            console.print("📈 Re-training dovrebbe migliorare performance")
        else:
            console.print("\n⚠️ [bold red]TOKENIZER ANCORA PROBLEMATICO[/bold red]")
            console.print("🔍 Investigare ulteriormente problemi simboli")
        
        console.print(Panel.fit(
            f"🔧 TOKENIZER CORRETTO CREATO\n"
            f"📊 Success Rate: {success_rate:.1%}\n"
            f"🔣 Simboli Aggiunti: {num_added}\n"
            f"📁 Path: {corrected_tokenizer_path}\n"
            f"📝 Script: {script_path}",
            style="bold green" if success_rate >= 0.8 else "bold yellow"
        ))
        
    except Exception as e:
        console.print(f"❌ [bold red]Errore creazione tokenizer corretto: {e}[/bold red]")

print("🎉 Soluzione tokenizer completata!")
print("🔄 Se necessario, eseguire re-training con tokenizer corretto")
