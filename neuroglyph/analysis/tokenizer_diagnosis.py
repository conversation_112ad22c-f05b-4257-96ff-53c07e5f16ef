#!/usr/bin/env python3
"""
🔍 NEUROGLYPH Tokenizer Diagnosis
================================

Diagnosi approfondita del problema del tokenizer NEUROGLYPH.
Analizza perché i simboli NEUROGLYPH non sono presenti nel vocabolario.

Findings:
- Il vocabolario contiene 8000 entries ma nessun simbolo NEUROGLYPH
- I simboli "trovati" sono principalmente caratteri Unicode generici
- Manca l'integrazione con il registry simbolico locked
"""

import json
from pathlib import Path
from typing import Dict, List, Any
import logging

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class TokenizerDiagnostic:
    """Diagnostica problemi del tokenizer NEUROGLYPH."""
    
    def __init__(self):
        self.project_root = Path("/Volumes/DANIELE/NEUROGLYPH")
        self.tokenizer_dir = self.project_root / "neuroglyph/training/colab_package"
        self.vocab_file = self.tokenizer_dir / "neuroglyph_tokenizer.vocab"
        self.locked_state_file = self.project_root / "neuroglyph/training/safety/tokenizer_locked_state.json"
        
        logger.info("🔍 NEUROGLYPH Tokenizer Diagnostic inizializzato")

    def analyze_vocab_content(self) -> Dict[str, Any]:
        """Analizza il contenuto effettivo del vocabolario."""
        
        logger.info("📊 Analizzando contenuto vocabolario...")
        
        if not self.vocab_file.exists():
            return {"error": "vocab_file_not_found"}
        
        # Leggi tutto il vocabolario
        vocab_entries = []
        unicode_symbols = []
        emoji_symbols = []
        mathematical_symbols = []
        
        with open(self.vocab_file, 'r', encoding='utf-8') as f:
            for line_num, line in enumerate(f, 1):
                line = line.strip()
                if not line:
                    continue
                
                parts = line.split('\t')
                if len(parts) >= 2:
                    token = parts[0]
                    score = float(parts[1]) if parts[1] != '0' else 0.0
                    
                    vocab_entries.append({
                        "token": token,
                        "score": score,
                        "line": line_num
                    })
                    
                    # Classifica simboli
                    if self._is_emoji(token):
                        emoji_symbols.append(token)
                    elif self._is_mathematical_symbol(token):
                        mathematical_symbols.append(token)
                    elif self._is_unicode_symbol(token):
                        unicode_symbols.append(token)
        
        return {
            "total_entries": len(vocab_entries),
            "emoji_count": len(emoji_symbols),
            "mathematical_count": len(mathematical_symbols),
            "unicode_count": len(unicode_symbols),
            "emoji_symbols": emoji_symbols[:20],  # Primi 20
            "mathematical_symbols": mathematical_symbols[:20],
            "unicode_symbols": unicode_symbols[:20],
            "sample_entries": vocab_entries[:10]
        }

    def _is_emoji(self, token: str) -> bool:
        """Verifica se è un emoji."""
        return any(0x1F000 <= ord(c) <= 0x1F9FF for c in token if len(token) <= 4)

    def _is_mathematical_symbol(self, token: str) -> bool:
        """Verifica se è un simbolo matematico."""
        math_ranges = [
            (0x2200, 0x22FF),  # Mathematical Operators
            (0x2190, 0x21FF),  # Arrows
            (0x2100, 0x214F),  # Letterlike Symbols
        ]
        return any(
            any(start <= ord(c) <= end for start, end in math_ranges)
            for c in token if len(token) <= 4
        )

    def _is_unicode_symbol(self, token: str) -> bool:
        """Verifica se è un simbolo Unicode generico."""
        return any(ord(c) > 127 for c in token) and len(token) <= 4

    def compare_with_expected_symbols(self) -> Dict[str, Any]:
        """Confronta con i simboli attesi da NEUROGLYPH."""
        
        logger.info("🔍 Confrontando con simboli attesi...")
        
        # Simboli che dovrebbero essere presenti
        expected_symbols = [
            "⚡", "🔄", "📊", "🧠", "📋", "🔢", "📝", "❓",
            "∑", "∫", "∂", "≈", "π", "ƒ", "∧", "∨", "→", "⊃",
            "⟲", "⋟", "⦀", "⟬", "⪕", "◿", "★", "⊵"
        ]
        
        # Simboli ng: che dovrebbero essere presenti
        expected_ng_symbols = [
            "ng:operator:add", "ng:operator:sub", "ng:operator:mul",
            "ng:logic:and", "ng:logic:or", "ng:logic:implies",
            "ng:memory:alloc", "ng:memory:free", "ng:memory:pointer",
            "ng:flow:if", "ng:flow:for", "ng:flow:return",
            "ng:structure:function", "ng:structure:class"
        ]
        
        # Leggi vocabolario attuale
        vocab_analysis = self.analyze_vocab_content()
        
        if "error" in vocab_analysis:
            return vocab_analysis
        
        # Cerca simboli nel vocabolario
        vocab_tokens = set()
        with open(self.vocab_file, 'r', encoding='utf-8') as f:
            for line in f:
                parts = line.strip().split('\t')
                if len(parts) >= 2:
                    vocab_tokens.add(parts[0])
        
        # Verifica presenza
        missing_symbols = []
        present_symbols = []
        
        for symbol in expected_symbols:
            if symbol in vocab_tokens:
                present_symbols.append(symbol)
            else:
                missing_symbols.append(symbol)
        
        missing_ng_symbols = []
        present_ng_symbols = []
        
        for ng_symbol in expected_ng_symbols:
            if ng_symbol in vocab_tokens:
                present_ng_symbols.append(ng_symbol)
            else:
                missing_ng_symbols.append(ng_symbol)
        
        return {
            "expected_symbols_total": len(expected_symbols),
            "present_symbols": present_symbols,
            "missing_symbols": missing_symbols,
            "symbol_coverage": len(present_symbols) / len(expected_symbols),
            
            "expected_ng_symbols_total": len(expected_ng_symbols),
            "present_ng_symbols": present_ng_symbols,
            "missing_ng_symbols": missing_ng_symbols,
            "ng_symbol_coverage": len(present_ng_symbols) / len(expected_ng_symbols),
            
            "vocab_analysis": vocab_analysis
        }

    def diagnose_tokenizer_issue(self) -> Dict[str, Any]:
        """Diagnosi completa del problema del tokenizer."""
        
        logger.info("🔧 Eseguendo diagnosi completa...")
        
        # 1. Verifica esistenza file
        files_status = {
            "vocab_exists": self.vocab_file.exists(),
            "model_exists": (self.tokenizer_dir / "neuroglyph_tokenizer.model").exists(),
            "locked_state_exists": self.locked_state_file.exists()
        }
        
        # 2. Analizza contenuto
        symbol_comparison = self.compare_with_expected_symbols()
        
        # 3. Verifica locked state
        locked_state_analysis = {}
        if files_status["locked_state_exists"]:
            with open(self.locked_state_file, 'r') as f:
                locked_state = json.load(f)
                locked_state_analysis = {
                    "locked_symbols_count": len(locked_state.get("locked_symbol_ids", {})),
                    "critical_symbols_count": len(locked_state.get("critical_symbols", [])),
                    "vocab_size_expected": locked_state.get("vocab_size", 0)
                }
        
        # 4. Identifica problema principale
        main_issue = self._identify_main_issue(symbol_comparison, locked_state_analysis)
        
        # 5. Genera raccomandazioni
        recommendations = self._generate_recommendations(main_issue, symbol_comparison)
        
        return {
            "files_status": files_status,
            "symbol_comparison": symbol_comparison,
            "locked_state_analysis": locked_state_analysis,
            "main_issue": main_issue,
            "recommendations": recommendations,
            "severity": "CRITICAL" if symbol_comparison.get("symbol_coverage", 0) == 0 else "WARNING"
        }

    def _identify_main_issue(self, symbol_comparison: Dict, locked_state: Dict) -> str:
        """Identifica il problema principale."""
        
        symbol_coverage = symbol_comparison.get("symbol_coverage", 0)
        ng_coverage = symbol_comparison.get("ng_symbol_coverage", 0)
        
        if symbol_coverage == 0 and ng_coverage == 0:
            return "COMPLETE_SYMBOL_ABSENCE"
        elif symbol_coverage < 0.5:
            return "PARTIAL_SYMBOL_INTEGRATION"
        elif ng_coverage == 0:
            return "MISSING_NG_SYMBOLS"
        else:
            return "MINOR_SYMBOL_GAPS"

    def _generate_recommendations(self, main_issue: str, symbol_comparison: Dict) -> List[str]:
        """Genera raccomandazioni per risolvere il problema."""
        
        recommendations = []
        
        if main_issue == "COMPLETE_SYMBOL_ABSENCE":
            recommendations.extend([
                "🚨 CRITICO: Il tokenizer non contiene simboli NEUROGLYPH",
                "🔧 Ricreare il tokenizer con simboli integrati",
                "📊 Utilizzare il registry simbolico locked per la creazione",
                "🧪 Testare tokenization/detokenization dopo la ricreazione"
            ])
        
        elif main_issue == "PARTIAL_SYMBOL_INTEGRATION":
            recommendations.extend([
                "⚠️ Integrazione simbolica incompleta",
                "🔄 Aggiornare il vocabolario con simboli mancanti",
                "🔍 Verificare mapping simboli nel locked state"
            ])
        
        elif main_issue == "MISSING_NG_SYMBOLS":
            recommendations.extend([
                "📝 Simboli ng: mancanti dal vocabolario",
                "🔧 Integrare simboli ng: dal registry",
                "🧪 Testare funzionalità simboliche avanzate"
            ])
        
        # Raccomandazioni generali
        recommendations.extend([
            "📋 Verificare compatibilità con Qwen2.5 tokenizer",
            "🔒 Sincronizzare con tokenizer_locked_state.json",
            "🧪 Eseguire test simbolici completi dopo le modifiche"
        ])
        
        return recommendations

    def generate_fix_script(self) -> str:
        """Genera script per risolvere il problema del tokenizer."""
        
        script = """#!/usr/bin/env python3
# 🔧 NEUROGLYPH Tokenizer Fix Script

import json
from transformers import AutoTokenizer

def fix_neuroglyph_tokenizer():
    print("🔧 Fixing NEUROGLYPH tokenizer...")
    
    # 1. Carica tokenizer base Qwen
    base_tokenizer = AutoTokenizer.from_pretrained("Qwen/Qwen2.5-Coder-1.5B-Instruct")
    
    # 2. Carica simboli NEUROGLYPH
    with open("neuroglyph/training/safety/tokenizer_locked_state.json", 'r') as f:
        locked_state = json.load(f)
    
    critical_symbols = locked_state["critical_symbols"]
    
    # 3. Aggiungi simboli al vocabolario
    new_tokens = []
    for symbol in critical_symbols:
        if symbol not in base_tokenizer.vocab:
            new_tokens.append(symbol)
    
    if new_tokens:
        print(f"📝 Aggiungendo {len(new_tokens)} nuovi simboli...")
        base_tokenizer.add_tokens(new_tokens)
    
    # 4. Salva tokenizer aggiornato
    base_tokenizer.save_pretrained("neuroglyph/training/colab_package/")
    print("✅ Tokenizer NEUROGLYPH aggiornato!")

if __name__ == "__main__":
    fix_neuroglyph_tokenizer()
"""
        
        return script

def main():
    """Main function per diagnosi tokenizer."""
    
    print("🔍 NEUROGLYPH TOKENIZER DIAGNOSIS")
    print("🎯 Comprehensive tokenizer problem analysis")
    print("=" * 60)
    
    # Inizializza diagnostic
    diagnostic = TokenizerDiagnostic()
    
    # Esegui diagnosi completa
    diagnosis = diagnostic.diagnose_tokenizer_issue()
    
    # Report risultati
    print(f"\n📊 DIAGNOSIS RESULTS:")
    print(f"🚨 Severity: {diagnosis['severity']}")
    print(f"🔧 Main Issue: {diagnosis['main_issue']}")
    
    # File status
    files = diagnosis['files_status']
    print(f"\n📁 FILES STATUS:")
    print(f"📄 Vocab file: {'✅' if files['vocab_exists'] else '❌'}")
    print(f"🔧 Model file: {'✅' if files['model_exists'] else '❌'}")
    print(f"🔒 Locked state: {'✅' if files['locked_state_exists'] else '❌'}")
    
    # Symbol coverage
    symbol_comp = diagnosis['symbol_comparison']
    print(f"\n🔣 SYMBOL COVERAGE:")
    print(f"⚡ Unicode symbols: {symbol_comp.get('symbol_coverage', 0):.1%}")
    print(f"🧠 NG symbols: {symbol_comp.get('ng_symbol_coverage', 0):.1%}")
    print(f"❌ Missing symbols: {len(symbol_comp.get('missing_symbols', []))}")
    print(f"❌ Missing NG symbols: {len(symbol_comp.get('missing_ng_symbols', []))}")
    
    # Recommendations
    print(f"\n🔧 RECOMMENDATIONS:")
    for i, rec in enumerate(diagnosis['recommendations'], 1):
        print(f"{i}. {rec}")
    
    # Generate fix script
    fix_script = diagnostic.generate_fix_script()
    with open("fix_neuroglyph_tokenizer.py", 'w') as f:
        f.write(fix_script)
    
    print(f"\n💾 Fix script generated: fix_neuroglyph_tokenizer.py")
    print(f"🚀 Run: python fix_neuroglyph_tokenizer.py")
    
    print(f"\n🎉 Diagnosis completed!")

if __name__ == "__main__":
    main()
