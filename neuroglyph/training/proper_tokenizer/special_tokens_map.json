{"additional_special_tokens": [{"content": "⊃", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false}, {"content": "∧", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false}, {"content": "∨", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false}, {"content": "→", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false}, {"content": "∑", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false}, {"content": "∫", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false}, {"content": "∂", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false}, {"content": "≈", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false}, {"content": "π", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false}, {"content": "ƒ", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false}, {"content": "🔄", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false}, {"content": "❓", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false}, {"content": "📋", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false}, {"content": "🔢", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false}, {"content": "📝", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false}, {"content": "⟲", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false}, {"content": "ng:operator:sub", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false}, {"content": "ng:memory:pointer", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false}, {"content": "ng:memory:alloc", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false}, {"content": "ng:logic:implies", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false}, {"content": "ng:logic:or_1", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false}, {"content": "ng:memory:alloc_1", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false}, {"content": "ng:memory:free", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false}, {"content": "ng:structure:function_1", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false}, {"content": "ng:operator:mul", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false}, {"content": "ng:operator:mod", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false}, {"content": "ng:operator:add_scalar", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false}, {"content": "ng:memory:deref", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false}, {"content": "ng:operator:add_vector", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false}, {"content": "ng:structure:property", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false}, {"content": "ng:flow:return_1", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false}, {"content": "ng:memory:alloc_stack", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false}, {"content": "ng:flow:for_range", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false}, {"content": "ng:operator:add_matrix", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false}, {"content": "ng:flow:if_conditional", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false}, {"content": "ng:operator:mul_scalar", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false}, {"content": "ng:logic:and_1", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false}, {"content": "ng:structure:function_2", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false}, {"content": "ng:structure:property_1", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false}, {"content": "ng:memory:pointer_1", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false}, {"content": "ng:flow:break", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false}, {"content": "ng:logic:not_bitwise", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false}, {"content": "ng:logic:implies_1", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false}, {"content": "ng:flow:break_1", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false}, {"content": "ng:operator:div", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false}, {"content": "ng:operator:add_complex", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false}, {"content": "ng:flow:if_ternary", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false}, {"content": "ng:logic:or_logical", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false}, {"content": "ng:memory:deref_1", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false}, {"content": "ng:structure:class_1", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false}, {"content": "ng:operator:div_scalar", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false}, {"content": "ng:memory:pointer_2", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false}, {"content": "ng:flow:break_2", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false}, {"content": "ng:memory:deref_2", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false}, {"content": "ng:operator:mod_integer", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false}, {"content": "ng:logic:implies_strict", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false}, {"content": "ng:flow:for_2", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false}, {"content": "ng:logic:and_logical", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false}, {"content": "ng:logic:implies_relevant", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false}, {"content": "ng:structure:property_2", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false}, {"content": "ng:operator:mod_polynomial", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false}, {"content": "ng:logic:and_fuzzy", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false}, {"content": "ng:operator:pow", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false}, {"content": "ng:structure:method", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false}, {"content": "ng:memory:pointer_3", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false}, {"content": "ng:logic:not_logical", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false}], "eos_token": {"content": "<|im_end|>", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false}, "pad_token": {"content": "<|endoftext|>", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false}}