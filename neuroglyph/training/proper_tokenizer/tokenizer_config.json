{"add_bos_token": false, "add_prefix_space": false, "added_tokens_decoder": {"151643": {"content": "<|endoftext|>", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "151644": {"content": "<|im_start|>", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "151645": {"content": "<|im_end|>", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "151646": {"content": "<|object_ref_start|>", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "151647": {"content": "<|object_ref_end|>", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "151648": {"content": "<|box_start|>", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "151649": {"content": "<|box_end|>", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "151650": {"content": "<|quad_start|>", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "151651": {"content": "<|quad_end|>", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "151652": {"content": "<|vision_start|>", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "151653": {"content": "<|vision_end|>", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "151654": {"content": "<|vision_pad|>", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "151655": {"content": "<|image_pad|>", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "151656": {"content": "<|video_pad|>", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "151657": {"content": "<tool_call>", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": false}, "151658": {"content": "</tool_call>", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": false}, "151659": {"content": "<|fim_prefix|>", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": false}, "151660": {"content": "<|fim_middle|>", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": false}, "151661": {"content": "<|fim_suffix|>", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": false}, "151662": {"content": "<|fim_pad|>", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": false}, "151663": {"content": "<|repo_name|>", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": false}, "151664": {"content": "<|file_sep|>", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": false}, "151665": {"content": "⊃", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "151666": {"content": "∧", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "151667": {"content": "∨", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "151668": {"content": "→", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "151669": {"content": "∑", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "151670": {"content": "∫", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "151671": {"content": "∂", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "151672": {"content": "≈", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "151673": {"content": "π", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "151674": {"content": "ƒ", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "151675": {"content": "🔄", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "151676": {"content": "❓", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "151677": {"content": "📋", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "151678": {"content": "🔢", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "151679": {"content": "📝", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "151680": {"content": "⟲", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "151681": {"content": "ng:operator:sub", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "151682": {"content": "ng:memory:pointer", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "151683": {"content": "ng:memory:alloc", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "151684": {"content": "ng:logic:implies", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "151685": {"content": "ng:logic:or_1", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "151686": {"content": "ng:memory:alloc_1", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "151687": {"content": "ng:memory:free", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "151688": {"content": "ng:structure:function_1", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "151689": {"content": "ng:operator:mul", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "151690": {"content": "ng:operator:mod", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "151691": {"content": "ng:operator:add_scalar", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "151692": {"content": "ng:memory:deref", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "151693": {"content": "ng:operator:add_vector", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "151694": {"content": "ng:structure:property", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "151695": {"content": "ng:flow:return_1", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "151696": {"content": "ng:memory:alloc_stack", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "151697": {"content": "ng:flow:for_range", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "151698": {"content": "ng:operator:add_matrix", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "151699": {"content": "ng:flow:if_conditional", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "151700": {"content": "ng:operator:mul_scalar", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "151701": {"content": "ng:logic:and_1", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "151702": {"content": "ng:structure:function_2", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "151703": {"content": "ng:structure:property_1", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "151704": {"content": "ng:memory:pointer_1", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "151705": {"content": "ng:flow:break", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "151706": {"content": "ng:logic:not_bitwise", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "151707": {"content": "ng:logic:implies_1", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "151708": {"content": "ng:flow:break_1", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "151709": {"content": "ng:operator:div", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "151710": {"content": "ng:operator:add_complex", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "151711": {"content": "ng:flow:if_ternary", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "151712": {"content": "ng:logic:or_logical", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "151713": {"content": "ng:memory:deref_1", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "151714": {"content": "ng:structure:class_1", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "151715": {"content": "ng:operator:div_scalar", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "151716": {"content": "ng:memory:pointer_2", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "151717": {"content": "ng:flow:break_2", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "151718": {"content": "ng:memory:deref_2", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "151719": {"content": "ng:operator:mod_integer", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "151720": {"content": "ng:logic:implies_strict", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "151721": {"content": "ng:flow:for_2", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "151722": {"content": "ng:logic:and_logical", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "151723": {"content": "ng:logic:implies_relevant", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "151724": {"content": "ng:structure:property_2", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "151725": {"content": "ng:operator:mod_polynomial", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "151726": {"content": "ng:logic:and_fuzzy", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "151727": {"content": "ng:operator:pow", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "151728": {"content": "ng:structure:method", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "151729": {"content": "ng:memory:pointer_3", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "151730": {"content": "ng:logic:not_logical", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}}, "additional_special_tokens": ["⊃", "∧", "∨", "→", "∑", "∫", "∂", "≈", "π", "ƒ", "🔄", "❓", "📋", "🔢", "📝", "⟲", "ng:operator:sub", "ng:memory:pointer", "ng:memory:alloc", "ng:logic:implies", "ng:logic:or_1", "ng:memory:alloc_1", "ng:memory:free", "ng:structure:function_1", "ng:operator:mul", "ng:operator:mod", "ng:operator:add_scalar", "ng:memory:deref", "ng:operator:add_vector", "ng:structure:property", "ng:flow:return_1", "ng:memory:alloc_stack", "ng:flow:for_range", "ng:operator:add_matrix", "ng:flow:if_conditional", "ng:operator:mul_scalar", "ng:logic:and_1", "ng:structure:function_2", "ng:structure:property_1", "ng:memory:pointer_1", "ng:flow:break", "ng:logic:not_bitwise", "ng:logic:implies_1", "ng:flow:break_1", "ng:operator:div", "ng:operator:add_complex", "ng:flow:if_ternary", "ng:logic:or_logical", "ng:memory:deref_1", "ng:structure:class_1", "ng:operator:div_scalar", "ng:memory:pointer_2", "ng:flow:break_2", "ng:memory:deref_2", "ng:operator:mod_integer", "ng:logic:implies_strict", "ng:flow:for_2", "ng:logic:and_logical", "ng:logic:implies_relevant", "ng:structure:property_2", "ng:operator:mod_polynomial", "ng:logic:and_fuzzy", "ng:operator:pow", "ng:structure:method", "ng:memory:pointer_3", "ng:logic:not_logical"], "bos_token": null, "clean_up_tokenization_spaces": false, "eos_token": "<|im_end|>", "errors": "replace", "extra_special_tokens": {}, "model_max_length": 32768, "pad_token": "<|endoftext|>", "split_special_tokens": false, "tokenizer_class": "Qwen2Tokenizer", "unk_token": null}