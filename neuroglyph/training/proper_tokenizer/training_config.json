{"tokenizer_path": "neuroglyph/training/proper_tokenizer", "vocab_size": 151731, "special_tokens_count": 66, "model_config_updates": {"vocab_size": 151731, "pad_token_id": 151643, "eos_token_id": 151645, "bos_token_id": null}, "training_notes": ["CRITICAL: Use this tokenizer for training", "Model vocab_size must be updated to match tokenizer", "Embedding layer must be resized before training", "All symbols should tokenize as single tokens"]}