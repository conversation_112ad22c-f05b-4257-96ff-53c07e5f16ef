#!/usr/bin/env python3
"""
NEUROGLYPH ADVANCED DATASET GENERATOR
Generatore di dataset avanzato per training simbolico con 10,000+ esempi
"""

import json
import random
import gzip
from pathlib import Path
from typing import Dict, List, Any, Tu<PERSON>
from dataclasses import dataclass
from datetime import datetime
import itertools

@dataclass
class SymbolicExample:
    """Esempio di training simbolico avanzato."""
    question: str
    symbols_used: str
    domain: str
    difficulty: str
    curriculum_level: int
    symbol_cluster: str
    thought_chain: List[str]
    answer_with_symbols: str
    explanation: str
    validation_symbols: List[str]
    quality_score: float
    metadata: Dict[str, Any]

class AdvancedDatasetGenerator:
    """Generatore dataset avanzato per NEUROGLYPH ULTRA."""

    def __init__(self, symbols_registry_path: str = "neuroglyph/core/locked_registry_godmode_v9.json"):
        self.symbols_registry = self._load_symbols_registry(symbols_registry_path)
        self.domains = self._define_cognitive_domains()
        self.symbol_clusters = self._create_symbol_clusters()
        self.templates = self._create_advanced_templates()

    def _load_symbols_registry(self, path: str) -> Dict[str, Any]:
        """Carica registry simboli validati."""
        try:
            with open(path, 'r', encoding='utf-8') as f:
                registry = json.load(f)

            # Prova diversi formati di registry
            symbols = {}
            if 'symbols' in registry:
                symbols = registry['symbols']
            elif 'approved_symbols' in registry:
                # Converti da lista a dict
                approved = registry['approved_symbols']
                if isinstance(approved, list):
                    symbols = {sym.get('symbol', ''): sym for sym in approved if 'symbol' in sym}
                else:
                    symbols = approved
            else:
                # Fallback: usa tutto il registry come simboli
                symbols = registry

            print(f"✅ Caricati {len(symbols)} simboli dal registry")

            # Se ancora vuoto, usa simboli base
            if not symbols:
                symbols = self._get_base_symbols()
                print(f"⚠️ Usando simboli base: {len(symbols)} simboli")

            return symbols
        except Exception as e:
            print(f"❌ Errore caricamento registry: {e}")
            print("🔄 Usando simboli base di fallback")
            return self._get_base_symbols()

    def _get_base_symbols(self) -> Dict[str, Any]:
        """Simboli base NEUROGLYPH per fallback."""
        return {
            "⊃": {"code": "ng:reasoning:logical", "fallback": "[IMPLIES]"},
            "∧": {"code": "ng:logic:and", "fallback": "[AND]"},
            "∨": {"code": "ng:logic:or", "fallback": "[OR]"},
            "¬": {"code": "ng:logic:not", "fallback": "[NOT]"},
            "ƒ": {"code": "ng:function:def", "fallback": "[FUNC]"},
            "🔄": {"code": "ng:control:loop", "fallback": "[LOOP]"},
            "❓": {"code": "ng:control:if", "fallback": "[IF]"},
            "→": {"code": "ng:flow:arrow", "fallback": "[ARROW]"},
            "⤴": {"code": "ng:control:return", "fallback": "[RETURN]"},
            "⊗": {"code": "ng:math:multiply", "fallback": "[MUL]"},
            "⊕": {"code": "ng:math:add", "fallback": "[ADD]"},
            "◊": {"code": "ng:control:else", "fallback": "[ELSE]"},
            "∀": {"code": "ng:logic:forall", "fallback": "[FORALL]"},
            "∃": {"code": "ng:logic:exists", "fallback": "[EXISTS]"},
            "∈": {"code": "ng:set:member", "fallback": "[IN]"},
            "∉": {"code": "ng:set:notmember", "fallback": "[NOTIN]"}
        }

    def _define_cognitive_domains(self) -> Dict[str, Dict[str, Any]]:
        """Definisce domini cognitivi per dataset diversificato."""
        return {
            "logic": {
                "description": "Logical reasoning and propositions",
                "symbols": ["⊃", "∧", "∨", "¬", "∀", "∃", "⊢", "⊨"],
                "complexity_levels": 5,
                "examples_target": 1500
            },
            "mathematics": {
                "description": "Mathematical operations and structures",
                "symbols": ["∑", "∏", "∫", "∂", "∇", "∞", "≈", "≡"],
                "complexity_levels": 5,
                "examples_target": 1500
            },
            "programming": {
                "description": "Programming constructs and algorithms",
                "symbols": ["ƒ", "🔄", "❓", "→", "⤴", "⊗", "⊕", "◊"],
                "complexity_levels": 5,
                "examples_target": 2000
            },
            "data_structures": {
                "description": "Data structures and operations",
                "symbols": ["⟨", "⟩", "[", "]", "∈", "∉", "⊆", "⊇"],
                "complexity_levels": 4,
                "examples_target": 1200
            },
            "algorithms": {
                "description": "Algorithmic thinking and optimization",
                "symbols": ["⟲", "⟳", "⇄", "⇅", "⟰", "⟱", "⤸", "⤹"],
                "complexity_levels": 5,
                "examples_target": 1500
            },
            "control_flow": {
                "description": "Program control and flow",
                "symbols": ["❓", "🔄", "⤴", "⤵", "⟲", "⟳", "◊", "⋄"],
                "complexity_levels": 4,
                "examples_target": 1000
            },
            "memory": {
                "description": "Memory management and references",
                "symbols": ["⟦", "⟧", "⟪", "⟫", "⇀", "⇁", "⇂", "⇃"],
                "complexity_levels": 4,
                "examples_target": 800
            },
            "concurrency": {
                "description": "Parallel and concurrent programming",
                "symbols": ["⫸", "⫷", "⫺", "⫻", "⟐", "⟑", "⟒", "⟓"],
                "complexity_levels": 5,
                "examples_target": 500
            }
        }

    def _create_symbol_clusters(self) -> Dict[str, List[str]]:
        """Crea cluster di simboli correlati."""
        return {
            "basic_logic": ["∧", "∨", "¬", "⊃"],
            "quantifiers": ["∀", "∃", "∈", "∉"],
            "functions": ["ƒ", "→", "⤴", "⟨", "⟩"],
            "control_basic": ["❓", "🔄", "◊"],
            "control_advanced": ["⟲", "⟳", "⇄", "⇅"],
            "math_basic": ["⊕", "⊗", "≈", "≡"],
            "math_advanced": ["∑", "∏", "∫", "∂"],
            "data_basic": ["[", "]", "⟨", "⟩"],
            "data_advanced": ["⟦", "⟧", "⟪", "⟫"],
            "memory_ops": ["⇀", "⇁", "⇂", "⇃"],
            "parallel": ["⫸", "⫷", "⫺", "⫻"]
        }

    def _create_advanced_templates(self) -> Dict[str, List[Dict[str, Any]]]:
        """Crea template avanzati per generazione esempi."""
        return {
            "programming": [
                {
                    "pattern": "function_definition",
                    "template": "Define a function {name} that {action}",
                    "symbols": ["ƒ", "→", "⤴"],
                    "answer_pattern": "ƒ {name}({params}) {return_type} : {body}"
                },
                {
                    "pattern": "conditional_logic",
                    "template": "Implement conditional logic for {condition}",
                    "symbols": ["❓", "◊", "→"],
                    "answer_pattern": "❓ {condition} → {then_action}; ◊ → {else_action}"
                },
                {
                    "pattern": "loop_construct",
                    "template": "Create a loop to {action}",
                    "symbols": ["🔄", "❓", "⤴"],
                    "answer_pattern": "🔄 {condition} : {body}"
                }
            ],
            "logic": [
                {
                    "pattern": "logical_implication",
                    "template": "Express the logical relationship: if {premise} then {conclusion}",
                    "symbols": ["⊃", "∧", "∨"],
                    "answer_pattern": "{premise} ⊃ {conclusion}"
                },
                {
                    "pattern": "quantified_statement",
                    "template": "Formalize: {statement} for all/some {domain}",
                    "symbols": ["∀", "∃", "∈"],
                    "answer_pattern": "{quantifier} x ∈ {domain}: {predicate}(x)"
                }
            ],
            "mathematics": [
                {
                    "pattern": "summation",
                    "template": "Express the sum of {expression} from {start} to {end}",
                    "symbols": ["∑", "≈", "∞"],
                    "answer_pattern": "∑ from {start} to {end} of {expression}"
                },
                {
                    "pattern": "integration",
                    "template": "Integrate {function} with respect to {variable}",
                    "symbols": ["∫", "∂", "→"],
                    "answer_pattern": "∫ {function} ∂{variable}"
                }
            ]
        }

    def generate_curriculum_example(self, domain: str, level: int) -> SymbolicExample:
        """Genera esempio per curriculum specifico."""
        domain_info = self.domains[domain]
        available_symbols = domain_info["symbols"]

        # Seleziona simboli basati sul livello
        num_symbols = min(level + 1, len(available_symbols))
        selected_symbols = random.sample(available_symbols, num_symbols)

        # Genera esempio basato su template
        templates = self.templates.get(domain, [])
        if templates:
            template = random.choice(templates)
            example = self._generate_from_template(template, selected_symbols, domain, level)
        else:
            example = self._generate_generic_example(selected_symbols, domain, level)

        return example

    def _generate_from_template(self, template: Dict[str, Any], symbols: List[str],
                               domain: str, level: int) -> SymbolicExample:
        """Genera esempio da template specifico."""
        # Genera valori per template
        template_values = {
            "name": "processData",
            "action": "process input data",
            "condition": "x > 0",
            "domain": "ℕ",
            "premise": "x is positive",
            "conclusion": "x > 0",
            "statement": "all numbers are positive",
            "expression": "i²",
            "start": "1",
            "end": "n",
            "function": "f(x)",
            "variable": "x"
        }

        # Formatta template con valori sicuri
        try:
            question = template["template"].format(**template_values)
        except KeyError as e:
            # Fallback per template mancanti
            question = f"Implement a {domain} solution using symbolic reasoning with level {level}"

        symbols_used = " ".join(symbols)

        # Genera thought chain basata sul livello
        thought_chain = self._generate_thought_chain(level, symbols)

        # Genera risposta simbolica
        answer = self._generate_symbolic_answer(template, symbols)

        # Calcola quality score
        quality_score = self._calculate_quality_score(symbols, thought_chain, answer)

        return SymbolicExample(
            question=question,
            symbols_used=symbols_used,
            domain=f"ng:{domain}:{template['pattern']}",
            difficulty=self._get_difficulty_name(level),
            curriculum_level=level,
            symbol_cluster=self._get_symbol_cluster(symbols),
            thought_chain=thought_chain,
            answer_with_symbols=answer,
            explanation=self._generate_explanation(symbols),
            validation_symbols=symbols,
            quality_score=quality_score,
            metadata={
                "template": template["pattern"],
                "generation_time": datetime.now().isoformat(),
                "symbol_count": len(symbols)
            }
        )

    def _generate_generic_example(self, symbols: List[str], domain: str, level: int) -> SymbolicExample:
        """Genera esempio generico quando non ci sono template."""
        question = f"Implement a {domain} solution using symbolic reasoning"
        symbols_used = " ".join(symbols)

        thought_chain = [
            f"Analyze {domain} requirements",
            f"Apply symbolic reasoning with {len(symbols)} symbols",
            "Validate symbolic consistency"
        ]

        answer = f"Solution using symbols: {symbols_used}"

        return SymbolicExample(
            question=question,
            symbols_used=symbols_used,
            domain=f"ng:{domain}:generic",
            difficulty=self._get_difficulty_name(level),
            curriculum_level=level,
            symbol_cluster="mixed",
            thought_chain=thought_chain,
            answer_with_symbols=answer,
            explanation=f"Generic {domain} example with symbolic reasoning",
            validation_symbols=symbols,
            quality_score=0.7,  # Lower for generic
            metadata={
                "type": "generic",
                "generation_time": datetime.now().isoformat(),
                "symbol_count": len(symbols)
            }
        )

    def _generate_thought_chain(self, level: int, symbols: List[str]) -> List[str]:
        """Genera catena di ragionamento basata sul livello."""
        base_steps = [
            "Analyze problem requirements",
            "Identify applicable symbols",
            "Design symbolic solution"
        ]

        if level >= 2:
            base_steps.extend([
                "Validate symbol combinations",
                "Optimize symbolic representation"
            ])

        if level >= 4:
            base_steps.extend([
                "Consider edge cases",
                "Verify semantic consistency",
                "Test reversibility"
            ])

        return base_steps[:level + 2]

    def _generate_symbolic_answer(self, template: Dict[str, Any], symbols: List[str]) -> str:
        """Genera risposta simbolica realistica."""
        if "answer_pattern" in template:
            # Valori completi per tutti i template
            answer_values = {
                "name": "solution",
                "params": "x, y",
                "return_type": "ℕ",
                "body": " ".join(symbols),
                "condition": "x > 0",
                "then_action": "⤴ x",
                "else_action": "⤴ 0",
                "premise": "x > 0",
                "conclusion": "result",
                "quantifier": "∀",
                "domain": "ℕ",
                "predicate": "positive",
                "expression": "i²",
                "start": "1",
                "end": "n",
                "function": "f(x)",
                "variable": "x"
            }

            try:
                return template["answer_pattern"].format(**answer_values)
            except KeyError as e:
                # Fallback se mancano ancora valori
                return f"Symbolic solution using {' '.join(symbols)}"
        else:
            return f"Symbolic solution: {' '.join(symbols)}"

    def _generate_explanation(self, symbols: List[str]) -> str:
        """Genera spiegazione simboli utilizzati."""
        explanations = []
        for symbol in symbols:
            if symbol in self.symbols_registry:
                code = self.symbols_registry[symbol].get('code', f'ng:unknown:{symbol}')
                explanations.append(f"{symbol} ({code})")

        return f"Symbols used: {', '.join(explanations)}"

    def _calculate_quality_score(self, symbols: List[str], thought_chain: List[str], answer: str) -> float:
        """Calcola score di qualità dell'esempio."""
        score = 0.5  # Base score

        # Bonus per simboli validati
        valid_symbols = sum(1 for s in symbols if s in self.symbols_registry)
        score += (valid_symbols / len(symbols)) * 0.3

        # Bonus per thought chain complessa
        score += min(len(thought_chain) / 8, 0.15)

        # Bonus per risposta dettagliata
        score += min(len(answer) / 100, 0.05)

        return min(score, 1.0)

    def _get_difficulty_name(self, level: int) -> str:
        """Converte livello numerico in nome difficoltà."""
        names = ["basic", "intermediate", "advanced", "expert", "god"]
        return names[min(level, len(names) - 1)]

    def _get_symbol_cluster(self, symbols: List[str]) -> str:
        """Determina cluster simbolico."""
        for cluster_name, cluster_symbols in self.symbol_clusters.items():
            if any(s in cluster_symbols for s in symbols):
                return cluster_name
        return "mixed"

    def generate_complete_dataset(self, target_size: int = 10000) -> List[SymbolicExample]:
        """Genera dataset completo con curriculum learning."""
        examples = []

        print(f"🚀 Generazione dataset NEUROGLYPH ULTRA: {target_size} esempi")

        # Distribuzione per dominio
        for domain, domain_info in self.domains.items():
            domain_target = domain_info["examples_target"]
            domain_examples = []

            # Distribuzione per livello curriculum
            levels = domain_info["complexity_levels"]
            examples_per_level = domain_target // levels

            for level in range(levels):
                for _ in range(examples_per_level):
                    example = self.generate_curriculum_example(domain, level)
                    domain_examples.append(example)

            examples.extend(domain_examples)
            print(f"✅ {domain}: {len(domain_examples)} esempi generati")

        # Shuffle per curriculum learning
        random.shuffle(examples)

        # Tronca al target size
        examples = examples[:target_size]

        print(f"🎉 Dataset completo: {len(examples)} esempi")
        return examples

    def save_dataset_unsloth_format(self, examples: List[SymbolicExample],
                                   output_path: str, compress: bool = True) -> str:
        """Salva dataset in formato Unsloth compatibile."""

        # Converti in formato Unsloth
        unsloth_examples = []
        for example in examples:
            unsloth_example = {
                "text": f"""<|im_start|>system
You are NEUROGLYPH, an advanced AI that thinks symbolically. Use the provided symbols to enhance your reasoning and provide precise, logical responses. Always maintain symbolic consistency and explain your reasoning process.
<|im_end|>
<|im_start|>user
{example.question}

Available Symbols: {example.symbols_used}
<|im_end|>
<|im_start|>assistant
{example.answer_with_symbols}<|im_end|>""",
                "symbols_count": len(example.validation_symbols),
                "quality_score": example.quality_score,
                "domain": example.domain,
                "curriculum_level": example.curriculum_level
            }
            unsloth_examples.append(unsloth_example)

        # Salva con compressione opzionale
        if compress:
            with gzip.open(output_path, 'wt', encoding='utf-8') as f:
                for example in unsloth_examples:
                    f.write(json.dumps(example, ensure_ascii=False) + '\n')
        else:
            with open(output_path, 'w', encoding='utf-8') as f:
                for example in unsloth_examples:
                    f.write(json.dumps(example, ensure_ascii=False) + '\n')

        print(f"💾 Dataset salvato: {output_path}")
        return output_path

def main():
    """Genera dataset NEUROGLYPH ULTRA avanzato."""
    print("🧠 NEUROGLYPH ULTRA - Advanced Dataset Generator")
    print("=" * 60)

    generator = AdvancedDatasetGenerator()

    # Genera dataset completo
    examples = generator.generate_complete_dataset(target_size=10000)

    # Split train/validation/test
    random.shuffle(examples)

    train_size = int(len(examples) * 0.8)  # 80%
    val_size = int(len(examples) * 0.15)   # 15%
    test_size = len(examples) - train_size - val_size  # 5%

    train_examples = examples[:train_size]
    val_examples = examples[train_size:train_size + val_size]
    test_examples = examples[train_size + val_size:]

    # Salva dataset
    output_dir = Path("neuroglyph/training/colab_package")
    output_dir.mkdir(parents=True, exist_ok=True)

    train_path = generator.save_dataset_unsloth_format(
        train_examples,
        str(output_dir / "neuroglyph_training_unsloth_ULTRA_v2.jsonl.gz")
    )

    val_path = generator.save_dataset_unsloth_format(
        val_examples,
        str(output_dir / "neuroglyph_validation_unsloth_ULTRA_v2.jsonl.gz")
    )

    test_path = generator.save_dataset_unsloth_format(
        test_examples,
        str(output_dir / "neuroglyph_test_unsloth_ULTRA_v2.jsonl.gz")
    )

    # Validazione qualità dataset
    validator = DatasetQualityValidator(generator.symbols_registry)
    validation_results = validator.validate_dataset(examples)

    # Salva report validazione
    validation_report_path = output_dir / "dataset_validation_report.json"
    with open(validation_report_path, 'w', encoding='utf-8') as f:
        json.dump(validation_results, f, indent=2, ensure_ascii=False)

    # Statistiche finali
    print(f"\n📊 Dataset Statistics:")
    print(f"   • Training: {len(train_examples)} esempi")
    print(f"   • Validation: {len(val_examples)} esempi")
    print(f"   • Test: {len(test_examples)} esempi")
    print(f"   • Total: {len(examples)} esempi")

    avg_quality = sum(ex.quality_score for ex in examples) / len(examples)
    print(f"   • Average Quality: {avg_quality:.3f}")
    print(f"   • Validation Report: {validation_report_path}")

    if validation_results['validation_passed']:
        print(f"\n🎉 NEUROGLYPH ULTRA Dataset v2.0 Ready! ✅")
        print("Dataset meets all quality criteria for production training.")
    else:
        print(f"\n⚠️ NEUROGLYPH ULTRA Dataset v2.0 Generated with Issues")
        print("Review validation report for improvement recommendations.")

class DatasetQualityValidator:
    """Validatore qualità dataset NEUROGLYPH."""

    def __init__(self, symbols_registry: Dict[str, Any]):
        self.symbols_registry = symbols_registry

    def validate_dataset(self, examples: List[SymbolicExample]) -> Dict[str, Any]:
        """Valida qualità completa del dataset."""
        print("🔍 Validazione qualità dataset NEUROGLYPH...")

        metrics = {
            "total_examples": len(examples),
            "symbol_coverage": self._check_symbol_coverage(examples),
            "domain_distribution": self._check_domain_distribution(examples),
            "curriculum_balance": self._check_curriculum_balance(examples),
            "quality_scores": self._analyze_quality_scores(examples),
            "symbol_consistency": self._check_symbol_consistency(examples),
            "validation_passed": False
        }

        # Criteri di validazione
        validation_criteria = {
            "min_examples": metrics["total_examples"] >= 8000,
            "symbol_coverage": metrics["symbol_coverage"]["coverage_rate"] >= 0.7,
            "avg_quality": metrics["quality_scores"]["average"] >= 0.8,
            "curriculum_balance": metrics["curriculum_balance"]["balanced"],
            "symbol_consistency": metrics["symbol_consistency"]["consistency_rate"] >= 0.95
        }

        metrics["validation_criteria"] = validation_criteria
        metrics["validation_passed"] = all(validation_criteria.values())

        self._print_validation_report(metrics)
        return metrics

    def _check_symbol_coverage(self, examples: List[SymbolicExample]) -> Dict[str, Any]:
        """Verifica copertura simboli nel dataset."""
        used_symbols = set()
        for example in examples:
            used_symbols.update(example.validation_symbols)

        total_symbols = len(self.symbols_registry)
        covered_symbols = len(used_symbols)
        coverage_rate = covered_symbols / total_symbols if total_symbols > 0 else 0

        return {
            "total_symbols": total_symbols,
            "covered_symbols": covered_symbols,
            "coverage_rate": coverage_rate,
            "missing_symbols": list(set(self.symbols_registry.keys()) - used_symbols)[:10]  # Prime 10
        }

    def _check_domain_distribution(self, examples: List[SymbolicExample]) -> Dict[str, Any]:
        """Verifica distribuzione domini."""
        domain_counts = {}
        for example in examples:
            domain = example.domain.split(':')[1] if ':' in example.domain else example.domain
            domain_counts[domain] = domain_counts.get(domain, 0) + 1

        total = len(examples)
        domain_percentages = {domain: count/total*100 for domain, count in domain_counts.items()}

        return {
            "domain_counts": domain_counts,
            "domain_percentages": domain_percentages,
            "num_domains": len(domain_counts),
            "balanced": max(domain_percentages.values()) - min(domain_percentages.values()) < 30
        }

    def _check_curriculum_balance(self, examples: List[SymbolicExample]) -> Dict[str, Any]:
        """Verifica bilanciamento curriculum."""
        level_counts = {}
        for example in examples:
            level = example.curriculum_level
            level_counts[level] = level_counts.get(level, 0) + 1

        total = len(examples)
        level_percentages = {level: count/total*100 for level, count in level_counts.items()}

        # Curriculum dovrebbe essere progressivo: più esempi nei livelli base
        expected_distribution = [35, 25, 20, 15, 5]  # % per livelli 0-4
        actual_distribution = [level_percentages.get(i, 0) for i in range(5)]

        balance_score = 1 - sum(abs(exp - act) for exp, act in zip(expected_distribution, actual_distribution)) / 200

        return {
            "level_counts": level_counts,
            "level_percentages": level_percentages,
            "balance_score": balance_score,
            "balanced": balance_score >= 0.8
        }

    def _analyze_quality_scores(self, examples: List[SymbolicExample]) -> Dict[str, Any]:
        """Analizza score di qualità."""
        scores = [example.quality_score for example in examples]

        return {
            "average": sum(scores) / len(scores),
            "minimum": min(scores),
            "maximum": max(scores),
            "below_threshold": sum(1 for s in scores if s < 0.7),
            "high_quality": sum(1 for s in scores if s >= 0.9)
        }

    def _check_symbol_consistency(self, examples: List[SymbolicExample]) -> Dict[str, Any]:
        """Verifica consistenza simbolica."""
        consistent_examples = 0
        total_symbols_used = 0
        valid_symbols_used = 0

        for example in examples:
            # Verifica che tutti i simboli nell'answer siano in validation_symbols
            answer_symbols = set()
            for char in example.answer_with_symbols:
                if ord(char) > 127 and char in self.symbols_registry:
                    answer_symbols.add(char)

            validation_symbols = set(example.validation_symbols)

            if answer_symbols.issubset(validation_symbols):
                consistent_examples += 1

            total_symbols_used += len(answer_symbols)
            valid_symbols_used += len(answer_symbols & set(self.symbols_registry.keys()))

        consistency_rate = consistent_examples / len(examples) if examples else 0
        validity_rate = valid_symbols_used / total_symbols_used if total_symbols_used > 0 else 0

        return {
            "consistent_examples": consistent_examples,
            "total_examples": len(examples),
            "consistency_rate": consistency_rate,
            "validity_rate": validity_rate
        }

    def _print_validation_report(self, metrics: Dict[str, Any]):
        """Stampa report di validazione."""
        print("\n" + "="*60)
        print("📊 NEUROGLYPH Dataset Validation Report")
        print("="*60)

        print(f"📈 Total Examples: {metrics['total_examples']}")
        print(f"🎯 Symbol Coverage: {metrics['symbol_coverage']['coverage_rate']:.1%}")
        print(f"🏆 Average Quality: {metrics['quality_scores']['average']:.3f}")
        print(f"⚖️ Curriculum Balance: {'✅' if metrics['curriculum_balance']['balanced'] else '❌'}")
        print(f"🔍 Symbol Consistency: {metrics['symbol_consistency']['consistency_rate']:.1%}")

        print(f"\n🎯 Validation Criteria:")
        for criterion, passed in metrics['validation_criteria'].items():
            status = "✅" if passed else "❌"
            print(f"   {status} {criterion}")

        overall_status = "✅ PASSED" if metrics['validation_passed'] else "❌ FAILED"
        print(f"\n🏁 Overall Validation: {overall_status}")

        if not metrics['validation_passed']:
            print("\n⚠️ Recommendations:")
            if not metrics['validation_criteria']['min_examples']:
                print("   • Increase dataset size to at least 8,000 examples")
            if not metrics['validation_criteria']['symbol_coverage']:
                print("   • Improve symbol coverage to at least 70%")
            if not metrics['validation_criteria']['avg_quality']:
                print("   • Enhance example quality to average 0.8+")

if __name__ == "__main__":
    main()
