#!/usr/bin/env python3
"""
NEUROGLYPH SAFE TRAINING CONFIGURATION
======================================

Configurazione completa per training sicuro con tutti i sistemi di safety attivati.
Implementa il Risk Mitigation Master Plan.

Autore: NEUROGLYPH ULTRA Team
Data: 2025-05-26
Licenza: MIT
"""

import json
import logging
import torch
from pathlib import Path
from typing import Dict, Any, Optional
from dataclasses import dataclass, asdict

# Setup logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

@dataclass
class SafeQLoRAConfig:
    """Configurazione QLoRA ultra-sicura."""
    # LoRA parameters (conservativi)
    r: int = 8                          # Rank basso = meno interferenza
    lora_alpha: int = 16                # Alpha moderato
    lora_dropout: float = 0.1           # Dropout per generalizzazione

    # Target modules (SOLO attention, MAI embeddings)
    target_modules: list = None

    # 4-bit quantization (efficienza ottimale)
    load_in_4bit: bool = True
    bnb_4bit_compute_dtype: str = "float16"
    bnb_4bit_quant_type: str = "nf4"
    bnb_4bit_use_double_quant: bool = True

    # Safety settings
    preserve_base_model: bool = True
    enable_gradient_checkpointing: bool = True
    torch_dtype: str = "float16"

    def __post_init__(self):
        if self.target_modules is None:
            # SOLO query e value projection (più sicuro)
            self.target_modules = ["q_proj", "v_proj"]

@dataclass
class SafeTrainingConfig:
    """Configurazione training ultra-conservativa."""
    # Learning rate ultra-conservativo
    learning_rate: float = 1e-4         # Più basso del normale (2e-4)
    lr_scheduler_type: str = "cosine_with_restarts"
    warmup_steps: int = 200             # Warmup lungo per stabilità
    warmup_ratio: float = 0.1

    # Batch configuration
    per_device_train_batch_size: int = 2    # Piccolo per stabilità
    per_device_eval_batch_size: int = 4
    gradient_accumulation_steps: int = 8    # Effective batch size = 16
    dataloader_num_workers: int = 2

    # Training duration (conservativo)
    num_train_epochs: int = 2           # Inizia con 2 epochs
    max_steps: int = 1000               # Hard limit per sicurezza
    save_steps: int = 50                # Checkpoint frequenti
    eval_steps: int = 100               # Validation frequente

    # Regularization aggressiva
    weight_decay: float = 0.01          # L2 regularization
    max_grad_norm: float = 1.0          # Gradient clipping
    adam_epsilon: float = 1e-8
    adam_beta1: float = 0.9
    adam_beta2: float = 0.999

    # Early stopping rigoroso
    early_stopping_patience: int = 3    # Stop dopo 3 eval senza miglioramento
    early_stopping_threshold: float = 0.001  # Threshold miglioramento minimo
    load_best_model_at_end: bool = True

    # Logging e monitoring
    logging_steps: int = 10             # Log frequente
    report_to: list = None
    run_name: str = None

    def __post_init__(self):
        if self.report_to is None:
            self.report_to = ["tensorboard"]
        if self.run_name is None:
            import time
            self.run_name = f"neuroglyph_safe_training_{int(time.time())}"

@dataclass
class SafetyThresholds:
    """Soglie di sicurezza per monitoring."""
    # Loss monitoring
    loss_explosion_multiplier: float = 2.0     # Loss > 2x baseline
    loss_stagnation_steps: int = 200           # No improvement per 200 steps
    loss_oscillation_variance: float = 0.5     # Varianza loss > 0.5

    # Symbol accuracy
    symbol_accuracy_critical: float = 0.6      # < 60% = critico
    symbol_accuracy_warning: float = 0.8       # < 80% = warning

    # Baseline performance
    baseline_critical: float = 0.90            # < 90% baseline = critico
    baseline_warning: float = 0.95             # < 95% baseline = warning
    baseline_test_frequency: int = 100         # Test ogni 100 steps

    # Gradient health
    gradient_explosion: float = 10.0           # Grad norm > 10
    gradient_vanishing: float = 1e-6           # Grad norm < 1e-6

    # Memory usage
    memory_critical: float = 0.95              # > 95% memory usage
    memory_warning: float = 0.85               # > 85% memory usage

@dataclass
class CheckpointConfig:
    """Configurazione checkpoint sicuri."""
    checkpoint_frequency: int = 50             # Every 50 steps
    validation_frequency: int = 100            # Every 100 steps
    max_safe_checkpoints: int = 10             # Mantieni ultimi 10
    integrity_threshold: float = 0.8           # Soglia minima integrità
    auto_rollback: bool = True                 # Rollback automatico

    # Paths
    checkpoint_dir: str = "neuroglyph/training/safety/checkpoints"
    backup_dir: str = "neuroglyph/training/safety/backups"

class SafeTrainingManager:
    """
    Manager principale per training sicuro NEUROGLYPH.

    Coordina tutti i sistemi di safety:
    - Tokenizer safety
    - Real-time monitoring
    - Emergency protocols
    - Safe checkpointing
    """

    def __init__(self,
                 model_name: str = "Qwen/Qwen2.5-Coder-1.5B-Instruct",
                 output_dir: str = "neuroglyph/training/output"):

        self.model_name = model_name
        self.output_dir = Path(output_dir)
        self.output_dir.mkdir(parents=True, exist_ok=True)

        # Configurazioni
        self.qlora_config = SafeQLoRAConfig()
        self.training_config = SafeTrainingConfig()
        self.safety_thresholds = SafetyThresholds()
        self.checkpoint_config = CheckpointConfig()

        # Sistemi di safety
        self.tokenizer_safety = None
        self.realtime_monitor = None
        self.emergency_protocols = None

        # Stato training
        self.baseline_metrics = {}
        self.training_active = False

        logger.info(f"🛡️ Safe Training Manager initialized for {model_name}")

    def initialize_safety_systems(self) -> bool:
        """
        Inizializza tutti i sistemi di safety.

        Returns:
            True se inizializzazione riuscita
        """
        try:
            logger.info("🔧 Initializing safety systems...")

            # 1. Tokenizer Safety System
            try:
                from .tokenizer_safety import TokenizerSafetySystem
            except ImportError:
                from tokenizer_safety import TokenizerSafetySystem
            self.tokenizer_safety = TokenizerSafetySystem(self.model_name)

            if not self.tokenizer_safety.initialize_tokenizer_lock():
                logger.error("❌ Tokenizer safety initialization failed")
                return False

            # 2. Establish baseline metrics
            self.baseline_metrics = self.establish_baseline_metrics()

            # 3. Real-time Monitor
            try:
                from .realtime_monitor import NeuroglyphRealTimeMonitor
            except ImportError:
                from realtime_monitor import NeuroglyphRealTimeMonitor
            self.realtime_monitor = NeuroglyphRealTimeMonitor(self.baseline_metrics)

            # 4. Emergency Protocols
            try:
                from .emergency_protocols import NeuroglyphEmergencyProtocols
            except ImportError:
                from emergency_protocols import NeuroglyphEmergencyProtocols
            self.emergency_protocols = NeuroglyphEmergencyProtocols()

            logger.info("✅ All safety systems initialized")
            return True

        except Exception as e:
            logger.error(f"❌ Safety systems initialization failed: {e}")
            return False

    def establish_baseline_metrics(self) -> Dict[str, float]:
        """
        Stabilisce metriche baseline prima del training.

        Returns:
            Dizionario metriche baseline
        """
        logger.info("📊 Establishing baseline metrics...")

        # Placeholder per metriche baseline reali
        # In produzione, qui si farebbero test reali del modello base
        baseline_metrics = {
            "initial_loss": 1.0,
            "baseline_score": 0.95,
            "symbol_recognition": 0.0,  # Modello base non conosce simboli
            "code_quality": 0.8,
            "general_reasoning": 0.85
        }

        # Salva baseline
        baseline_path = self.output_dir / "baseline_metrics.json"
        with open(baseline_path, 'w', encoding='utf-8') as f:
            json.dump(baseline_metrics, f, indent=2, ensure_ascii=False)

        logger.info(f"💾 Baseline metrics saved: {baseline_path}")
        return baseline_metrics

    def get_safe_training_arguments(self) -> Dict[str, Any]:
        """
        Ottieni argomenti training sicuri per Unsloth/Transformers.

        Returns:
            Dizionario argomenti training
        """
        # Converti dataclass a dict
        training_args = asdict(self.training_config)

        # Aggiungi paths
        training_args.update({
            "output_dir": str(self.output_dir),
            "logging_dir": str(self.output_dir / "logs"),
            "save_total_limit": self.checkpoint_config.max_safe_checkpoints,
            "save_strategy": "steps",
            "evaluation_strategy": "steps",
            "metric_for_best_model": "eval_loss",
            "greater_is_better": False,
            "remove_unused_columns": False,
            "dataloader_pin_memory": False  # Più sicuro per memoria
        })

        return training_args

    def get_safe_qlora_config(self) -> Dict[str, Any]:
        """
        Ottieni configurazione QLoRA sicura.

        Returns:
            Dizionario configurazione QLoRA
        """
        return asdict(self.qlora_config)

    def validate_training_setup(self) -> bool:
        """
        Valida setup training prima di iniziare.

        Returns:
            True se setup valido
        """
        logger.info("🔍 Validating training setup...")

        checks = []

        # Check 1: Tokenizer integrity
        if self.tokenizer_safety:
            tokenizer_ok = self.tokenizer_safety.validate_tokenizer_integrity()
            checks.append(("Tokenizer integrity", tokenizer_ok))

        # Check 2: Safety systems
        safety_systems_ok = all([
            self.tokenizer_safety is not None,
            self.realtime_monitor is not None,
            self.emergency_protocols is not None
        ])
        checks.append(("Safety systems", safety_systems_ok))

        # Check 3: Output directory
        output_ok = self.output_dir.exists() and self.output_dir.is_dir()
        checks.append(("Output directory", output_ok))

        # Check 4: Baseline metrics
        baseline_ok = bool(self.baseline_metrics)
        checks.append(("Baseline metrics", baseline_ok))

        # Check 5: GPU memory
        if torch.cuda.is_available():
            torch.cuda.empty_cache()
            memory_ok = torch.cuda.memory_allocated() / torch.cuda.max_memory_allocated() < 0.5
            checks.append(("GPU memory", memory_ok))

        # Log risultati
        all_passed = True
        for check_name, passed in checks:
            status = "✅ PASS" if passed else "❌ FAIL"
            logger.info(f"  {status} {check_name}")
            if not passed:
                all_passed = False

        if all_passed:
            logger.info("✅ Training setup validation passed")
        else:
            logger.error("❌ Training setup validation failed")

        return all_passed

    def start_safe_training(self) -> bool:
        """
        Avvia training sicuro.

        Returns:
            True se training avviato con successo
        """
        if not self.validate_training_setup():
            logger.error("❌ Cannot start training: setup validation failed")
            return False

        logger.info("🚀 Starting safe training...")

        # Attiva monitoring
        if self.realtime_monitor:
            self.realtime_monitor.start_monitoring()

        self.training_active = True

        logger.info("✅ Safe training started")
        return True

    def stop_safe_training(self):
        """Ferma training sicuro."""
        logger.info("🛑 Stopping safe training...")

        # Disattiva monitoring
        if self.realtime_monitor:
            self.realtime_monitor.stop_monitoring()

        self.training_active = False

        logger.info("✅ Safe training stopped")

    def get_safety_status(self) -> Dict[str, Any]:
        """Ottieni status completo sistemi di safety."""
        status = {
            "training_active": self.training_active,
            "safety_systems_initialized": all([
                self.tokenizer_safety is not None,
                self.realtime_monitor is not None,
                self.emergency_protocols is not None
            ]),
            "baseline_metrics_established": bool(self.baseline_metrics)
        }

        # Aggiungi status specifici se disponibili
        if self.tokenizer_safety:
            status["tokenizer_integrity"] = self.tokenizer_safety.get_integrity_report()

        if self.realtime_monitor:
            status["monitoring"] = self.realtime_monitor.get_monitoring_report()

        if self.emergency_protocols:
            status["emergency_protocols"] = self.emergency_protocols.get_emergency_status()

        return status

    def save_configuration(self):
        """Salva configurazione completa."""
        config_data = {
            "model_name": self.model_name,
            "output_dir": str(self.output_dir),
            "qlora_config": asdict(self.qlora_config),
            "training_config": asdict(self.training_config),
            "safety_thresholds": asdict(self.safety_thresholds),
            "checkpoint_config": asdict(self.checkpoint_config),
            "baseline_metrics": self.baseline_metrics
        }

        config_path = self.output_dir / "safe_training_config.json"
        with open(config_path, 'w', encoding='utf-8') as f:
            json.dump(config_data, f, indent=2, ensure_ascii=False)

        logger.info(f"💾 Configuration saved: {config_path}")


def main():
    """Test del Safe Training Manager."""
    print("🛡️ NEUROGLYPH SAFE TRAINING MANAGER")
    print("=" * 50)

    # Inizializza manager
    manager = SafeTrainingManager()

    # Test inizializzazione safety systems
    print("🔧 Testing safety systems initialization...")
    if manager.initialize_safety_systems():
        print("✅ Safety systems initialized")
    else:
        print("❌ Safety systems initialization failed")
        return

    # Test validazione setup
    print("\n🔍 Testing training setup validation...")
    if manager.validate_training_setup():
        print("✅ Training setup validated")
    else:
        print("❌ Training setup validation failed")

    # Test configurazioni
    print("\n⚙️ Testing configurations...")
    training_args = manager.get_safe_training_arguments()
    qlora_config = manager.get_safe_qlora_config()

    print(f"Training args keys: {len(training_args)}")
    print(f"QLoRA config keys: {len(qlora_config)}")

    # Salva configurazione
    print("\n💾 Saving configuration...")
    manager.save_configuration()

    # Status finale
    print("\n📊 Safety Status:")
    status = manager.get_safety_status()
    for key, value in status.items():
        if isinstance(value, dict):
            print(f"  {key}: {len(value)} items")
        else:
            print(f"  {key}: {value}")

    print("\n🎯 Safe Training Manager test completed")


if __name__ == "__main__":
    main()
