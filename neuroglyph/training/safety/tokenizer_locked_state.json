{"timestamp": 1748393416.3555899, "tokenizer_path": "Qwen/Qwen2.5-Coder-1.5B-Instruct", "locked_symbol_ids": {"⊃": 151665, "∧": 151666, "∨": 151667, "→": 151668, "∑": 151669, "∫": 151670, "∂": 151671, "≈": 151672, "π": 151673, "ƒ": 151674, "🔄": 151675, "❓": 151676, "📋": 151677, "🔢": 151678, "📝": 151679, "⟲": 151680, "ng:operator:sub": 151681, "ng:memory:pointer": 151682, "ng:memory:alloc": 151683, "ng:logic:implies": 151684, "ng:logic:or_1": 151685, "ng:memory:alloc_1": 151686, "ng:memory:free": 151687, "ng:structure:function_1": 151688, "ng:operator:mul": 151689, "ng:operator:mod": 151690, "ng:operator:add_scalar": 151691, "ng:memory:deref": 151692, "ng:operator:add_vector": 151693, "ng:structure:property": 151694, "ng:flow:return_1": 151695, "ng:memory:alloc_stack": 151696, "ng:flow:for_range": 151697, "ng:operator:add_matrix": 151698, "ng:flow:if_conditional": 151699, "ng:operator:mul_scalar": 151700, "ng:logic:and_1": 151701, "ng:structure:function_2": 151702, "ng:structure:property_1": 151703, "ng:memory:pointer_1": 151704, "ng:flow:break": 151705, "ng:logic:not_bitwise": 151706, "ng:logic:implies_1": 151707, "ng:flow:break_1": 151708, "ng:operator:div": 151709, "ng:operator:add_complex": 151710, "ng:flow:if_ternary": 151711, "ng:logic:or_logical": 151712, "ng:memory:deref_1": 151713, "ng:structure:class_1": 151714, "ng:operator:div_scalar": 151715, "ng:memory:pointer_2": 151716, "ng:flow:break_2": 151717, "ng:memory:deref_2": 151718, "ng:operator:mod_integer": 151719, "ng:logic:implies_strict": 151720, "ng:flow:for_2": 151721, "ng:logic:and_logical": 151722, "ng:logic:implies_relevant": 151723, "ng:structure:property_2": 151724, "ng:operator:mod_polynomial": 151725, "ng:logic:and_fuzzy": 151726, "ng:operator:pow": 151727, "ng:structure:method": 151728, "ng:memory:pointer_3": 151729, "ng:logic:not_logical": 151730}, "critical_symbols": ["⊃", "∧", "∨", "→", "∑", "∫", "∂", "≈", "π", "ƒ", "🔄", "❓", "📋", "🔢", "📝", "⟲", "ng:operator:sub", "ng:memory:pointer", "ng:memory:alloc", "ng:logic:implies", "ng:logic:or_1", "ng:memory:alloc_1", "ng:memory:free", "ng:structure:function_1", "ng:operator:mul", "ng:operator:mod", "ng:operator:add_scalar", "ng:memory:deref", "ng:operator:add_vector", "ng:structure:property", "ng:flow:return_1", "ng:memory:alloc_stack", "ng:flow:for_range", "ng:operator:add_matrix", "ng:flow:if_conditional", "ng:operator:mul_scalar", "ng:logic:and_1", "ng:structure:function_2", "ng:structure:property_1", "ng:memory:pointer_1", "ng:flow:break", "ng:logic:not_bitwise", "ng:logic:implies_1", "ng:flow:break_1", "ng:operator:div", "ng:operator:add_complex", "ng:flow:if_ternary", "ng:logic:or_logical", "ng:memory:deref_1", "ng:structure:class_1", "ng:operator:div_scalar", "ng:memory:pointer_2", "ng:flow:break_2", "ng:memory:deref_2", "ng:operator:mod_integer", "ng:logic:implies_strict", "ng:flow:for_2", "ng:logic:and_logical", "ng:logic:implies_relevant", "ng:structure:property_2", "ng:operator:mod_polynomial", "ng:logic:and_fuzzy", "ng:operator:pow", "ng:structure:method", "ng:memory:pointer_3", "ng:logic:not_logical"], "vocab_size": 151731}