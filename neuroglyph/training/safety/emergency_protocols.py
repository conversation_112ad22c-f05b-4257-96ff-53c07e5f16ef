#!/usr/bin/env python3
"""
NEUROGLYPH EMERGENCY PROTOCOLS SYSTEM
=====================================

Sistema di protocolli di emergenza per recovery automatico da situazioni critiche.
Implementa rollback automatico, recovery procedures e contingency plans.

Autore: NEUROGLYPH ULTRA Team
Data: 2025-05-26
Licenza: MIT
"""

import json
import logging
import time
import torch
from pathlib import Path
from typing import Dict, List, Any, Optional, Callable
from dataclasses import dataclass
from enum import Enum

# Setup logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class EmergencyLevel(Enum):
    """Livelli di emergenza."""
    IMMEDIATE = "immediate"      # Stop immediato + rollback
    GRACEFUL = "graceful"       # Stop graduale + analisi
    ROLLBACK_RETRY = "rollback_retry"  # Rollback + retry con modifiche

class RecoveryStatus(Enum):
    """Status recovery procedure."""
    SUCCESS = "success"
    PARTIAL = "partial"
    FAILED = "failed"
    IN_PROGRESS = "in_progress"

@dataclass
class SafeCheckpoint:
    """Checkpoint sicuro per rollback."""
    step: int
    timestamp: float
    model_state: Dict[str, Any]
    optimizer_state: Dict[str, Any]
    tokenizer_state: Dict[str, Any]
    metrics: Dict[str, Any]
    validation_passed: bool
    integrity_score: float

@dataclass
class EmergencyAction:
    """Azione di emergenza."""
    trigger: str
    level: EmergencyLevel
    description: str
    recovery_function: str
    max_retries: int
    timeout_seconds: int

class NeuroglyphEmergencyProtocols:
    """
    Sistema di protocolli di emergenza per training NEUROGLYPH.
    
    Gestisce:
    - Rollback automatico a checkpoint sicuri
    - Recovery da catastrophic forgetting
    - Recovery da symbol drift
    - Recovery da overfitting
    - Recovery da performance degradation
    """
    
    def __init__(self):
        self.safe_checkpoints = []
        self.max_safe_checkpoints = 10
        self.emergency_actions = self.setup_emergency_actions()
        self.recovery_history = []
        self.active_recovery = None
        
        logger.info("🚨 Emergency protocols system initialized")

    def setup_emergency_actions(self) -> Dict[str, EmergencyAction]:
        """Setup azioni di emergenza disponibili."""
        return {
            "tokenizer_corruption": EmergencyAction(
                trigger="tokenizer_corruption",
                level=EmergencyLevel.IMMEDIATE,
                description="Tokenizer integrity compromised",
                recovery_function="recover_tokenizer_corruption",
                max_retries=2,
                timeout_seconds=300
            ),
            "model_nan_weights": EmergencyAction(
                trigger="model_nan_weights",
                level=EmergencyLevel.IMMEDIATE,
                description="NaN weights detected in model",
                recovery_function="recover_nan_weights",
                max_retries=1,
                timeout_seconds=180
            ),
            "baseline_loss_50_percent": EmergencyAction(
                trigger="baseline_loss_50_percent",
                level=EmergencyLevel.IMMEDIATE,
                description="Baseline performance lost >50%",
                recovery_function="recover_catastrophic_forgetting",
                max_retries=3,
                timeout_seconds=600
            ),
            "symbol_drift": EmergencyAction(
                trigger="symbol_drift",
                level=EmergencyLevel.ROLLBACK_RETRY,
                description="Symbol mapping corrupted",
                recovery_function="recover_symbol_drift",
                max_retries=2,
                timeout_seconds=300
            ),
            "overfitting_severe": EmergencyAction(
                trigger="overfitting_severe",
                level=EmergencyLevel.GRACEFUL,
                description="Severe overfitting detected",
                recovery_function="recover_overfitting",
                max_retries=3,
                timeout_seconds=400
            ),
            "loss_explosion": EmergencyAction(
                trigger="loss_explosion",
                level=EmergencyLevel.ROLLBACK_RETRY,
                description="Training loss exploded",
                recovery_function="recover_loss_explosion",
                max_retries=2,
                timeout_seconds=200
            )
        }

    def save_safe_checkpoint(self, step: int, model: Any, optimizer: Any, 
                           tokenizer: Any, metrics: Dict[str, Any]) -> bool:
        """
        Salva checkpoint sicuro dopo validazione completa.
        
        Args:
            step: Step corrente
            model: Modello da salvare
            optimizer: Optimizer state
            tokenizer: Tokenizer state
            metrics: Metriche correnti
            
        Returns:
            True se checkpoint salvato con successo
        """
        try:
            # Validazione checkpoint
            integrity_score = self.validate_checkpoint_integrity(model, tokenizer, metrics)
            
            if integrity_score < 0.8:  # Soglia minima integrità
                logger.warning(f"Checkpoint at step {step} failed integrity check: {integrity_score:.2f}")
                return False
            
            # Crea checkpoint
            checkpoint = SafeCheckpoint(
                step=step,
                timestamp=time.time(),
                model_state=model.state_dict().copy() if hasattr(model, 'state_dict') else {},
                optimizer_state=optimizer.state_dict().copy() if hasattr(optimizer, 'state_dict') else {},
                tokenizer_state=tokenizer.get_vocab().copy() if hasattr(tokenizer, 'get_vocab') else {},
                metrics=metrics.copy(),
                validation_passed=True,
                integrity_score=integrity_score
            )
            
            # Aggiungi a lista checkpoint sicuri
            self.safe_checkpoints.append(checkpoint)
            
            # Mantieni solo ultimi N checkpoint
            if len(self.safe_checkpoints) > self.max_safe_checkpoints:
                self.safe_checkpoints = self.safe_checkpoints[-self.max_safe_checkpoints:]
            
            # Salva su disco
            self.save_checkpoint_to_disk(checkpoint)
            
            logger.info(f"✅ Safe checkpoint saved at step {step} (integrity: {integrity_score:.2f})")
            return True
            
        except Exception as e:
            logger.error(f"❌ Failed to save safe checkpoint: {e}")
            return False

    def validate_checkpoint_integrity(self, model: Any, tokenizer: Any, 
                                    metrics: Dict[str, Any]) -> float:
        """
        Valida integrità di un checkpoint.
        
        Returns:
            Score integrità 0.0-1.0
        """
        integrity_score = 0.0
        checks = 0
        
        # Check 1: Model weights sanity
        try:
            if hasattr(model, 'parameters'):
                for param in model.parameters():
                    if torch.isnan(param).any() or torch.isinf(param).any():
                        break
                else:
                    integrity_score += 0.3
            checks += 1
        except:
            pass
        
        # Check 2: Tokenizer integrity
        try:
            from .tokenizer_safety import TokenizerSafetySystem
            safety_system = TokenizerSafetySystem("current")
            if safety_system.validate_tokenizer_integrity():
                integrity_score += 0.3
            checks += 1
        except:
            pass
        
        # Check 3: Metrics sanity
        try:
            loss = metrics.get("train_loss", float('inf'))
            if 0 < loss < 10:  # Reasonable loss range
                integrity_score += 0.2
            checks += 1
        except:
            pass
        
        # Check 4: Performance preservation
        try:
            # Placeholder per test performance
            integrity_score += 0.2
            checks += 1
        except:
            pass
        
        return integrity_score if checks > 0 else 0.0

    def execute_emergency_protocol(self, trigger: str, context: Dict[str, Any]) -> RecoveryStatus:
        """
        Esegue protocollo di emergenza per trigger specifico.
        
        Args:
            trigger: Tipo di emergenza
            context: Contesto emergenza (model, metrics, etc.)
            
        Returns:
            Status del recovery
        """
        if trigger not in self.emergency_actions:
            logger.error(f"❌ Unknown emergency trigger: {trigger}")
            return RecoveryStatus.FAILED
        
        action = self.emergency_actions[trigger]
        
        logger.critical(f"🚨 EXECUTING EMERGENCY PROTOCOL: {trigger}")
        logger.critical(f"🚨 Level: {action.level.value}")
        logger.critical(f"🚨 Description: {action.description}")
        
        # Record recovery attempt
        recovery_attempt = {
            "trigger": trigger,
            "timestamp": time.time(),
            "context": context.copy(),
            "action": action,
            "status": RecoveryStatus.IN_PROGRESS
        }
        
        self.active_recovery = recovery_attempt
        
        try:
            # Esegui recovery function
            recovery_function = getattr(self, action.recovery_function)
            result = recovery_function(context)
            
            recovery_attempt["status"] = result
            recovery_attempt["completion_time"] = time.time()
            
            self.recovery_history.append(recovery_attempt)
            self.active_recovery = None
            
            if result == RecoveryStatus.SUCCESS:
                logger.info(f"✅ Emergency recovery successful: {trigger}")
            else:
                logger.warning(f"⚠️ Emergency recovery partial/failed: {trigger} -> {result}")
            
            return result
            
        except Exception as e:
            logger.error(f"❌ Emergency recovery error: {e}")
            recovery_attempt["status"] = RecoveryStatus.FAILED
            recovery_attempt["error"] = str(e)
            recovery_attempt["completion_time"] = time.time()
            
            self.recovery_history.append(recovery_attempt)
            self.active_recovery = None
            
            return RecoveryStatus.FAILED

    def emergency_rollback(self, reason: str) -> Optional[SafeCheckpoint]:
        """
        Rollback di emergenza all'ultimo checkpoint sicuro.
        
        Args:
            reason: Motivo del rollback
            
        Returns:
            Checkpoint ripristinato o None se fallimento
        """
        logger.critical(f"🚨 EMERGENCY ROLLBACK: {reason}")
        
        if not self.safe_checkpoints:
            logger.error("❌ No safe checkpoints available for rollback!")
            return None
        
        # Trova ultimo checkpoint sicuro
        latest_safe = max(self.safe_checkpoints, key=lambda c: c.integrity_score)
        
        logger.info(f"Rolling back to step {latest_safe.step} (integrity: {latest_safe.integrity_score:.2f})")
        
        try:
            # Salva stato corrente per analisi
            self.backup_current_state_for_analysis(reason)
            
            # Log rollback
            rollback_record = {
                "timestamp": time.time(),
                "reason": reason,
                "rollback_to_step": latest_safe.step,
                "rollback_to_timestamp": latest_safe.timestamp,
                "integrity_score": latest_safe.integrity_score
            }
            
            self.save_rollback_record(rollback_record)
            
            logger.info(f"✅ Emergency rollback completed to step {latest_safe.step}")
            return latest_safe
            
        except Exception as e:
            logger.error(f"❌ Emergency rollback failed: {e}")
            return None

    def recover_tokenizer_corruption(self, context: Dict[str, Any]) -> RecoveryStatus:
        """Recovery da corruzione tokenizer."""
        logger.info("🔧 Recovering from tokenizer corruption...")
        
        try:
            # Step 1: Emergency rollback
            safe_checkpoint = self.emergency_rollback("tokenizer_corruption")
            if not safe_checkpoint:
                return RecoveryStatus.FAILED
            
            # Step 2: Reset tokenizer
            from .tokenizer_safety import TokenizerSafetySystem
            safety_system = TokenizerSafetySystem(context.get("tokenizer_path", ""))
            
            if not safety_system.emergency_tokenizer_reset():
                return RecoveryStatus.FAILED
            
            # Step 3: Validate recovery
            if safety_system.validate_tokenizer_integrity():
                logger.info("✅ Tokenizer corruption recovery successful")
                return RecoveryStatus.SUCCESS
            else:
                logger.warning("⚠️ Tokenizer corruption recovery partial")
                return RecoveryStatus.PARTIAL
                
        except Exception as e:
            logger.error(f"❌ Tokenizer corruption recovery failed: {e}")
            return RecoveryStatus.FAILED

    def recover_catastrophic_forgetting(self, context: Dict[str, Any]) -> RecoveryStatus:
        """Recovery da catastrophic forgetting."""
        logger.info("🔧 Recovering from catastrophic forgetting...")
        
        try:
            # Step 1: Rollback to safe checkpoint
            safe_checkpoint = self.emergency_rollback("catastrophic_forgetting")
            if not safe_checkpoint:
                return RecoveryStatus.FAILED
            
            # Step 2: Reduce learning rate drastically
            new_lr = context.get("learning_rate", 2e-4) * 0.1
            context["recovery_learning_rate"] = new_lr
            logger.info(f"Reduced learning rate to {new_lr}")
            
            # Step 3: Increase regularization
            context["recovery_weight_decay"] = context.get("weight_decay", 0.01) * 2
            context["recovery_dropout"] = min(context.get("dropout", 0.1) + 0.1, 0.3)
            
            # Step 4: Enable curriculum restart
            context["curriculum_restart"] = True
            context["start_with_basic_examples"] = True
            
            logger.info("✅ Catastrophic forgetting recovery configured")
            return RecoveryStatus.SUCCESS
            
        except Exception as e:
            logger.error(f"❌ Catastrophic forgetting recovery failed: {e}")
            return RecoveryStatus.FAILED

    def recover_symbol_drift(self, context: Dict[str, Any]) -> RecoveryStatus:
        """Recovery da symbol drift."""
        logger.info("🔧 Recovering from symbol drift...")
        
        try:
            # Step 1: Freeze embeddings completely
            model = context.get("model")
            if model and hasattr(model, 'get_input_embeddings'):
                for param in model.get_input_embeddings().parameters():
                    param.requires_grad = False
                logger.info("Froze input embeddings")
            
            # Step 2: Reset tokenizer to locked state
            from .tokenizer_safety import TokenizerSafetySystem
            safety_system = TokenizerSafetySystem(context.get("tokenizer_path", ""))
            
            if not safety_system.load_locked_state():
                return RecoveryStatus.FAILED
            
            # Step 3: Retrain only attention layers
            context["recovery_target_modules"] = ["q_proj", "v_proj"]
            context["recovery_freeze_embeddings"] = True
            
            # Step 4: Validate symbol integrity
            if safety_system.validate_tokenizer_integrity():
                logger.info("✅ Symbol drift recovery successful")
                return RecoveryStatus.SUCCESS
            else:
                return RecoveryStatus.PARTIAL
                
        except Exception as e:
            logger.error(f"❌ Symbol drift recovery failed: {e}")
            return RecoveryStatus.FAILED

    def recover_overfitting(self, context: Dict[str, Any]) -> RecoveryStatus:
        """Recovery da overfitting."""
        logger.info("🔧 Recovering from overfitting...")
        
        try:
            # Step 1: Rollback alcuni step
            if self.safe_checkpoints:
                # Rollback a checkpoint precedente (non ultimo)
                if len(self.safe_checkpoints) >= 2:
                    rollback_checkpoint = self.safe_checkpoints[-2]
                    logger.info(f"Rolling back to step {rollback_checkpoint.step}")
            
            # Step 2: Increase dropout
            context["recovery_dropout"] = min(context.get("dropout", 0.1) + 0.2, 0.5)
            
            # Step 3: Reduce epochs
            context["recovery_max_epochs"] = max(context.get("num_epochs", 3) - 1, 1)
            
            # Step 4: Add data augmentation
            context["recovery_data_augmentation"] = True
            
            # Step 5: Early stopping più aggressivo
            context["recovery_early_stopping_patience"] = 2
            
            logger.info("✅ Overfitting recovery configured")
            return RecoveryStatus.SUCCESS
            
        except Exception as e:
            logger.error(f"❌ Overfitting recovery failed: {e}")
            return RecoveryStatus.FAILED

    def recover_loss_explosion(self, context: Dict[str, Any]) -> RecoveryStatus:
        """Recovery da loss explosion."""
        logger.info("🔧 Recovering from loss explosion...")
        
        try:
            # Step 1: Emergency rollback
            safe_checkpoint = self.emergency_rollback("loss_explosion")
            if not safe_checkpoint:
                return RecoveryStatus.FAILED
            
            # Step 2: Reduce learning rate drastically
            new_lr = context.get("learning_rate", 2e-4) * 0.2
            context["recovery_learning_rate"] = new_lr
            
            # Step 3: Increase gradient clipping
            context["recovery_max_grad_norm"] = 0.5
            
            # Step 4: Reduce batch size
            current_batch = context.get("batch_size", 4)
            context["recovery_batch_size"] = max(current_batch // 2, 1)
            
            logger.info("✅ Loss explosion recovery configured")
            return RecoveryStatus.SUCCESS
            
        except Exception as e:
            logger.error(f"❌ Loss explosion recovery failed: {e}")
            return RecoveryStatus.FAILED

    def recover_nan_weights(self, context: Dict[str, Any]) -> RecoveryStatus:
        """Recovery da NaN weights."""
        logger.info("🔧 Recovering from NaN weights...")
        
        try:
            # Step 1: Emergency rollback obbligatorio
            safe_checkpoint = self.emergency_rollback("nan_weights")
            if not safe_checkpoint:
                return RecoveryStatus.FAILED
            
            # Step 2: Reduce learning rate drasticamente
            context["recovery_learning_rate"] = 1e-5
            
            # Step 3: Gradient clipping molto aggressivo
            context["recovery_max_grad_norm"] = 0.1
            
            # Step 4: Check model initialization
            context["recovery_reinit_problematic_layers"] = True
            
            logger.info("✅ NaN weights recovery configured")
            return RecoveryStatus.SUCCESS
            
        except Exception as e:
            logger.error(f"❌ NaN weights recovery failed: {e}")
            return RecoveryStatus.FAILED

    def save_checkpoint_to_disk(self, checkpoint: SafeCheckpoint):
        """Salva checkpoint su disco."""
        try:
            checkpoint_dir = Path("neuroglyph/training/safety/checkpoints")
            checkpoint_dir.mkdir(parents=True, exist_ok=True)
            
            checkpoint_path = checkpoint_dir / f"safe_checkpoint_step_{checkpoint.step}.json"
            
            # Salva metadati (non model weights per dimensioni)
            checkpoint_metadata = {
                "step": checkpoint.step,
                "timestamp": checkpoint.timestamp,
                "metrics": checkpoint.metrics,
                "validation_passed": checkpoint.validation_passed,
                "integrity_score": checkpoint.integrity_score
            }
            
            with open(checkpoint_path, 'w', encoding='utf-8') as f:
                json.dump(checkpoint_metadata, f, indent=2, ensure_ascii=False)
            
        except Exception as e:
            logger.error(f"❌ Failed to save checkpoint to disk: {e}")

    def backup_current_state_for_analysis(self, reason: str):
        """Backup stato corrente per analisi post-mortem."""
        try:
            backup_dir = Path("neuroglyph/training/safety/analysis_backups")
            backup_dir.mkdir(parents=True, exist_ok=True)
            
            backup_data = {
                "timestamp": time.time(),
                "reason": reason,
                "recovery_history": self.recovery_history[-5:],  # Ultimi 5
                "safe_checkpoints_count": len(self.safe_checkpoints)
            }
            
            backup_path = backup_dir / f"analysis_backup_{int(time.time())}.json"
            
            with open(backup_path, 'w', encoding='utf-8') as f:
                json.dump(backup_data, f, indent=2, ensure_ascii=False)
            
            logger.info(f"💾 Analysis backup saved: {backup_path}")
            
        except Exception as e:
            logger.error(f"❌ Failed to save analysis backup: {e}")

    def save_rollback_record(self, rollback_record: Dict[str, Any]):
        """Salva record di rollback."""
        try:
            rollback_dir = Path("neuroglyph/training/safety/rollback_records")
            rollback_dir.mkdir(parents=True, exist_ok=True)
            
            rollback_path = rollback_dir / f"rollback_{int(time.time())}.json"
            
            with open(rollback_path, 'w', encoding='utf-8') as f:
                json.dump(rollback_record, f, indent=2, ensure_ascii=False)
            
        except Exception as e:
            logger.error(f"❌ Failed to save rollback record: {e}")

    def get_emergency_status(self) -> Dict[str, Any]:
        """Ottieni status sistema di emergenza."""
        return {
            "safe_checkpoints": len(self.safe_checkpoints),
            "recovery_attempts": len(self.recovery_history),
            "active_recovery": self.active_recovery is not None,
            "last_checkpoint_step": self.safe_checkpoints[-1].step if self.safe_checkpoints else None,
            "emergency_actions_available": len(self.emergency_actions)
        }


def main():
    """Test del sistema di emergency protocols."""
    print("🚨 NEUROGLYPH EMERGENCY PROTOCOLS SYSTEM")
    print("=" * 50)
    
    # Inizializza sistema
    emergency_system = NeuroglyphEmergencyProtocols()
    
    # Test salvataggio checkpoint
    print("💾 Testing safe checkpoint...")
    mock_metrics = {"train_loss": 0.5, "accuracy": 0.85}
    success = emergency_system.save_safe_checkpoint(100, None, None, None, mock_metrics)
    print(f"Checkpoint saved: {success}")
    
    # Test emergency protocol
    print("\n🚨 Testing emergency protocol...")
    context = {"learning_rate": 2e-4, "batch_size": 4}
    status = emergency_system.execute_emergency_protocol("loss_explosion", context)
    print(f"Recovery status: {status}")
    
    # Status finale
    print("\n📊 Emergency System Status:")
    status = emergency_system.get_emergency_status()
    for key, value in status.items():
        print(f"  {key}: {value}")
    
    print("\n🎯 Emergency protocols test completed")


if __name__ == "__main__":
    main()
