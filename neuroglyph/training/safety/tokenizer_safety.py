#!/usr/bin/env python3
"""
NEUROGLYPH TOKENIZER SAFETY SYSTEM
==================================

Sistema di sicurezza per preservare integrità tokenizer durante fine-tuning.
Implementa validazione rigorosa mapping simboli 1:1.

Autore: NEUROGLYPH ULTRA Team
Data: 2025-05-26
Licenza: MIT
"""

import json
import logging
import time
from pathlib import Path
from typing import Dict, List, Set, Optional
from transformers import AutoTokenizer

# Setup logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class TokenizerSafetySystem:
    """
    Sistema di sicurezza per tokenizer NEUROGLYPH.

    Garantisce:
    - Mapping simboli 1:1 inviolabile
    - Reversibilità perfetta
    - Stabilità ID token
    - Recovery automatico da corruzioni
    """

    def __init__(self, tokenizer_path: str):
        self.tokenizer_path = tokenizer_path
        self.tokenizer = None
        self.locked_symbol_ids = {}
        self.critical_symbols = self.load_critical_symbols()
        self.validation_history = []

        logger.info("🔒 Tokenizer Safety System initialized")

    def load_critical_symbols(self) -> List[str]:
        """Carica simboli critici che devono essere protetti."""
        # Simboli Unicode critici (rimosso ¬ problematico)
        unicode_symbols = ["⊃", "∧", "∨", "→", "∑", "∫", "∂", "≈", "π", "ƒ", "🔄", "❓", "📋", "🔢", "📝", "⟲"]

        # Simboli ng: dal registry
        ng_symbols = []
        try:
            registry_path = Path("neuroglyph/core/locked_registry_godmode_v9.json")
            if registry_path.exists():
                with open(registry_path, 'r', encoding='utf-8') as f:
                    registry = json.load(f)

                for symbol_data in registry.get("approved_symbols", []):
                    if isinstance(symbol_data, dict) and "code" in symbol_data:
                        code = symbol_data["code"]
                        if code.startswith("ng:"):
                            ng_symbols.append(code)
        except Exception as e:
            logger.warning(f"Could not load ng: symbols from registry: {e}")

        all_symbols = unicode_symbols + ng_symbols[:50]  # Limit per performance
        logger.info(f"Loaded {len(all_symbols)} critical symbols for protection")
        return all_symbols

    def initialize_tokenizer_lock(self) -> bool:
        """
        Inizializza tokenizer con lock sui simboli critici.

        Returns:
            True se inizializzazione riuscita
        """
        try:
            logger.info("🔧 Initializing tokenizer lock...")

            # Carica tokenizer base
            self.tokenizer = AutoTokenizer.from_pretrained(self.tokenizer_path)

            # Aggiungi simboli come special tokens
            special_tokens = {"additional_special_tokens": self.critical_symbols}
            num_added = self.tokenizer.add_special_tokens(special_tokens)

            logger.info(f"Added {num_added} special tokens")

            # Crea mapping locked
            self.locked_symbol_ids = {}
            for symbol in self.critical_symbols:
                tokens = self.tokenizer.encode(symbol, add_special_tokens=False)
                if len(tokens) == 1:
                    self.locked_symbol_ids[symbol] = tokens[0]
                else:
                    logger.warning(f"Symbol {symbol} maps to {len(tokens)} tokens")

            # Salva stato locked
            self.save_locked_state()

            logger.info(f"✅ Tokenizer lock initialized: {len(self.locked_symbol_ids)} symbols locked")
            return True

        except Exception as e:
            logger.error(f"❌ Failed to initialize tokenizer lock: {e}")
            return False

    def validate_tokenizer_integrity(self) -> bool:
        """
        Validazione critica integrità tokenizer.

        CRITICAL: Questo test deve passare al 100% sempre.

        Returns:
            True se integrità preservata
        """
        if not self.tokenizer:
            logger.error("❌ CRITICAL: Tokenizer not initialized")
            return False

        validation_start = time.time()
        failures = []

        logger.info("🔍 Validating tokenizer integrity...")

        for symbol in self.critical_symbols:
            try:
                # Test 1: Mapping 1:1 obbligatorio
                tokens = self.tokenizer.encode(symbol, add_special_tokens=False)

                if len(tokens) != 1:
                    failure = f"CRITICAL FAILURE: {symbol} → {len(tokens)} tokens: {tokens}"
                    failures.append(failure)
                    logger.error(failure)
                    continue

                # Test 2: Reversibilità perfetta
                decoded = self.tokenizer.decode(tokens)
                if decoded.strip() != symbol.strip():
                    failure = f"CRITICAL FAILURE: {symbol} → {decoded} (reversibility lost)"
                    failures.append(failure)
                    logger.error(failure)
                    continue

                # Test 3: Stabilità ID token (se disponibile)
                if symbol in self.locked_symbol_ids:
                    expected_id = self.locked_symbol_ids[symbol]
                    actual_id = tokens[0]
                    if actual_id != expected_id:
                        failure = f"CRITICAL FAILURE: {symbol} ID drift: {expected_id} → {actual_id}"
                        failures.append(failure)
                        logger.error(failure)
                        continue

            except Exception as e:
                failure = f"CRITICAL ERROR: {symbol} validation failed: {e}"
                failures.append(failure)
                logger.error(failure)

        # Calcola risultati
        validation_duration = time.time() - validation_start
        total_symbols = len(self.critical_symbols)
        failed_symbols = len(failures)
        success_rate = (total_symbols - failed_symbols) / total_symbols * 100

        # Log risultati
        validation_result = {
            "timestamp": time.time(),
            "duration": validation_duration,
            "total_symbols": total_symbols,
            "failed_symbols": failed_symbols,
            "success_rate": success_rate,
            "failures": failures
        }

        self.validation_history.append(validation_result)

        if failures:
            logger.error(f"❌ TOKENIZER INTEGRITY COMPROMISED: {failed_symbols}/{total_symbols} symbols failed")
            logger.error(f"❌ Success rate: {success_rate:.1f}%")
            for failure in failures[:5]:  # Log primi 5 fallimenti
                logger.error(f"   {failure}")
            return False
        else:
            logger.info(f"✅ Tokenizer integrity validated: {success_rate:.1f}% success ({validation_duration:.2f}s)")
            return True

    def save_locked_state(self):
        """Salva stato locked del tokenizer."""
        try:
            locked_state = {
                "timestamp": time.time(),
                "tokenizer_path": self.tokenizer_path,
                "locked_symbol_ids": self.locked_symbol_ids,
                "critical_symbols": self.critical_symbols,
                "vocab_size": len(self.tokenizer.get_vocab())
            }

            output_path = Path("neuroglyph/training/safety/tokenizer_locked_state.json")
            output_path.parent.mkdir(parents=True, exist_ok=True)

            with open(output_path, 'w', encoding='utf-8') as f:
                json.dump(locked_state, f, indent=2, ensure_ascii=False)

            logger.info(f"💾 Locked state saved: {output_path}")

        except Exception as e:
            logger.error(f"❌ Failed to save locked state: {e}")

    def load_locked_state(self) -> bool:
        """Carica stato locked del tokenizer."""
        try:
            state_path = Path("neuroglyph/training/safety/tokenizer_locked_state.json")

            if not state_path.exists():
                logger.warning("No locked state found")
                return False

            with open(state_path, 'r', encoding='utf-8') as f:
                locked_state = json.load(f)

            self.locked_symbol_ids = locked_state.get("locked_symbol_ids", {})

            logger.info(f"✅ Locked state loaded: {len(self.locked_symbol_ids)} symbols")
            return True

        except Exception as e:
            logger.error(f"❌ Failed to load locked state: {e}")
            return False

    def emergency_tokenizer_reset(self) -> bool:
        """
        Reset di emergenza del tokenizer.

        Usato quando integrità è compromessa oltre recovery.

        Returns:
            True se reset riuscito
        """
        logger.critical("🚨 EMERGENCY TOKENIZER RESET")

        try:
            # Backup stato corrente
            self.backup_current_state()

            # Reinizializza tokenizer da zero
            self.tokenizer = None
            self.locked_symbol_ids = {}

            # Ricarica e rilockka
            success = self.initialize_tokenizer_lock()

            if success:
                logger.info("✅ Emergency reset successful")
                return True
            else:
                logger.error("❌ Emergency reset failed")
                return False

        except Exception as e:
            logger.error(f"❌ Emergency reset error: {e}")
            return False

    def backup_current_state(self):
        """Backup stato corrente per recovery."""
        try:
            backup_data = {
                "timestamp": time.time(),
                "tokenizer_vocab": self.tokenizer.get_vocab() if self.tokenizer else {},
                "locked_symbol_ids": self.locked_symbol_ids,
                "validation_history": self.validation_history[-10:]  # Ultimi 10
            }

            backup_path = Path(f"neuroglyph/training/safety/tokenizer_backup_{int(time.time())}.json")
            backup_path.parent.mkdir(parents=True, exist_ok=True)

            with open(backup_path, 'w', encoding='utf-8') as f:
                json.dump(backup_data, f, indent=2, ensure_ascii=False)

            logger.info(f"💾 Tokenizer state backed up: {backup_path}")

        except Exception as e:
            logger.error(f"❌ Backup failed: {e}")

    def get_integrity_report(self) -> Dict:
        """Genera report integrità completo."""
        if not self.validation_history:
            return {"status": "no_validation_performed"}

        latest = self.validation_history[-1]

        return {
            "status": "healthy" if latest["success_rate"] == 100 else "compromised",
            "latest_validation": latest,
            "total_validations": len(self.validation_history),
            "locked_symbols": len(self.locked_symbol_ids),
            "critical_symbols": len(self.critical_symbols)
        }


def main():
    """Test del sistema di sicurezza tokenizer."""
    print("🔒 NEUROGLYPH TOKENIZER SAFETY SYSTEM")
    print("=" * 50)

    # Inizializza sistema
    safety_system = TokenizerSafetySystem("Qwen/Qwen2.5-Coder-1.5B-Instruct")

    # Test inizializzazione
    print("🔧 Testing tokenizer lock initialization...")
    if safety_system.initialize_tokenizer_lock():
        print("✅ Tokenizer lock initialized successfully")
    else:
        print("❌ Tokenizer lock initialization failed")
        return

    # Test validazione
    print("\n🔍 Testing tokenizer integrity validation...")
    if safety_system.validate_tokenizer_integrity():
        print("✅ Tokenizer integrity validated")
    else:
        print("❌ Tokenizer integrity compromised")

    # Report finale
    print("\n📊 Integrity Report:")
    report = safety_system.get_integrity_report()
    for key, value in report.items():
        print(f"  {key}: {value}")

    print("\n🎯 Tokenizer safety system test completed")


if __name__ == "__main__":
    main()
