#!/usr/bin/env python3
"""
NEUROGLYPH REAL-TIME MONITORING SYSTEM
======================================

Sistema di monitoring in tempo reale per training sicuro.
Rileva problemi immediatamente e attiva protocolli di emergenza.

Autore: NEUROGLYPH ULTRA Team
Data: 2025-05-26
Licenza: MIT
"""

import json
import logging
import time
import torch
from pathlib import Path
from typing import Dict, List, Any, Optional
from dataclasses import dataclass
from enum import Enum

# Setup logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class AlertLevel(Enum):
    """Livelli di alert del sistema."""
    INFO = "info"
    WARNING = "warning"
    CRITICAL = "critical"
    EMERGENCY = "emergency"

@dataclass
class Alert:
    """Struttura alert del sistema."""
    level: AlertLevel
    type: str
    message: str
    step: int
    timestamp: float
    metrics: Dict[str, Any]
    action_required: str

class NeuroglyphRealTimeMonitor:
    """
    Sistema di monitoring in tempo reale per training NEUROGLYPH.
    
    Monitora:
    - Loss convergence/explosion
    - Symbol accuracy degradation
    - Baseline performance preservation
    - Tokenizer integrity
    - Gradient health
    - Memory usage
    """
    
    def __init__(self, baseline_metrics: Dict[str, float]):
        self.baseline_metrics = baseline_metrics
        self.alert_thresholds = self.setup_alert_thresholds()
        self.alerts_history = []
        self.emergency_stops = 0
        self.max_emergency_stops = 3
        self.monitoring_active = False
        
        # Metriche tracking
        self.metrics_history = []
        self.performance_trend = []
        
        logger.info("📊 Real-time monitor initialized")

    def setup_alert_thresholds(self) -> Dict[str, Dict[str, float]]:
        """Setup soglie alert per monitoring."""
        return {
            "loss": {
                "explosion_multiplier": 2.0,    # Loss > 2x baseline
                "stagnation_steps": 200,        # No improvement per 200 steps
                "oscillation_variance": 0.5     # Varianza loss > 0.5
            },
            "symbol_accuracy": {
                "critical_threshold": 0.6,      # < 60% = critico
                "warning_threshold": 0.8,       # < 80% = warning
                "degradation_trend": 0.05       # -5% trend = alert
            },
            "baseline_performance": {
                "critical_threshold": 0.90,     # < 90% baseline = critico
                "warning_threshold": 0.95,      # < 95% baseline = warning
                "test_frequency": 100           # Test ogni 100 steps
            },
            "gradient": {
                "explosion_threshold": 10.0,    # Grad norm > 10
                "vanishing_threshold": 1e-6,    # Grad norm < 1e-6
                "nan_tolerance": 0              # Zero NaN tolerance
            },
            "memory": {
                "critical_usage": 0.95,         # > 95% memory usage
                "warning_usage": 0.85           # > 85% memory usage
            }
        }

    def start_monitoring(self):
        """Avvia monitoring in tempo reale."""
        self.monitoring_active = True
        logger.info("🟢 Real-time monitoring STARTED")

    def stop_monitoring(self):
        """Ferma monitoring."""
        self.monitoring_active = False
        logger.info("🔴 Real-time monitoring STOPPED")

    def monitor_training_step(self, step: int, metrics: Dict[str, Any], model: Any) -> List[Alert]:
        """
        Monitoring completo di un training step.
        
        Args:
            step: Numero step corrente
            metrics: Metriche training step
            model: Modello in training
            
        Returns:
            Lista alert generati
        """
        if not self.monitoring_active:
            return []
        
        alerts = []
        
        # Salva metriche per trend analysis
        self.metrics_history.append({
            "step": step,
            "timestamp": time.time(),
            "metrics": metrics.copy()
        })
        
        # Keep only last 1000 steps
        if len(self.metrics_history) > 1000:
            self.metrics_history = self.metrics_history[-1000:]
        
        # Monitor 1: Loss health
        loss_alerts = self.monitor_loss_health(step, metrics)
        alerts.extend(loss_alerts)
        
        # Monitor 2: Gradient health
        gradient_alerts = self.monitor_gradient_health(step, metrics)
        alerts.extend(gradient_alerts)
        
        # Monitor 3: Memory usage
        memory_alerts = self.monitor_memory_usage(step)
        alerts.extend(memory_alerts)
        
        # Monitor 4: Symbol accuracy (ogni 50 steps)
        if step % 50 == 0:
            symbol_alerts = self.monitor_symbol_accuracy(step, model)
            alerts.extend(symbol_alerts)
        
        # Monitor 5: Baseline performance (ogni 100 steps)
        if step % 100 == 0:
            baseline_alerts = self.monitor_baseline_performance(step, model)
            alerts.extend(baseline_alerts)
        
        # Monitor 6: Tokenizer integrity (ogni 200 steps)
        if step % 200 == 0:
            tokenizer_alerts = self.monitor_tokenizer_integrity(step)
            alerts.extend(tokenizer_alerts)
        
        # Gestisci alerts
        if alerts:
            self.handle_alerts(step, alerts, model)
        
        return alerts

    def monitor_loss_health(self, step: int, metrics: Dict[str, Any]) -> List[Alert]:
        """Monitora salute della loss function."""
        alerts = []
        current_loss = metrics.get("train_loss", 0)
        
        # Check 1: Loss explosion
        baseline_loss = self.baseline_metrics.get("initial_loss", 1.0)
        explosion_threshold = baseline_loss * self.alert_thresholds["loss"]["explosion_multiplier"]
        
        if current_loss > explosion_threshold:
            alerts.append(Alert(
                level=AlertLevel.EMERGENCY,
                type="LOSS_EXPLOSION",
                message=f"Loss exploded: {current_loss:.4f} > {explosion_threshold:.4f}",
                step=step,
                timestamp=time.time(),
                metrics=metrics,
                action_required="IMMEDIATE_ROLLBACK"
            ))
        
        # Check 2: Loss stagnation
        if len(self.metrics_history) >= 200:
            recent_losses = [m["metrics"].get("train_loss", 0) for m in self.metrics_history[-200:]]
            loss_improvement = recent_losses[0] - recent_losses[-1]
            
            if loss_improvement < 0.001:  # No improvement
                alerts.append(Alert(
                    level=AlertLevel.WARNING,
                    type="LOSS_STAGNATION",
                    message=f"Loss stagnant for 200 steps: improvement {loss_improvement:.6f}",
                    step=step,
                    timestamp=time.time(),
                    metrics=metrics,
                    action_required="ADJUST_HYPERPARAMETERS"
                ))
        
        # Check 3: Loss oscillation
        if len(self.metrics_history) >= 50:
            recent_losses = [m["metrics"].get("train_loss", 0) for m in self.metrics_history[-50:]]
            loss_variance = torch.var(torch.tensor(recent_losses)).item()
            
            if loss_variance > self.alert_thresholds["loss"]["oscillation_variance"]:
                alerts.append(Alert(
                    level=AlertLevel.WARNING,
                    type="LOSS_OSCILLATION",
                    message=f"Loss oscillating: variance {loss_variance:.4f}",
                    step=step,
                    timestamp=time.time(),
                    metrics=metrics,
                    action_required="REDUCE_LEARNING_RATE"
                ))
        
        return alerts

    def monitor_gradient_health(self, step: int, metrics: Dict[str, Any]) -> List[Alert]:
        """Monitora salute dei gradienti."""
        alerts = []
        grad_norm = metrics.get("grad_norm", 0)
        
        # Check 1: Gradient explosion
        if grad_norm > self.alert_thresholds["gradient"]["explosion_threshold"]:
            alerts.append(Alert(
                level=AlertLevel.CRITICAL,
                type="GRADIENT_EXPLOSION",
                message=f"Gradient explosion: norm {grad_norm:.4f}",
                step=step,
                timestamp=time.time(),
                metrics=metrics,
                action_required="GRADIENT_CLIPPING"
            ))
        
        # Check 2: Gradient vanishing
        if grad_norm < self.alert_thresholds["gradient"]["vanishing_threshold"]:
            alerts.append(Alert(
                level=AlertLevel.WARNING,
                type="GRADIENT_VANISHING",
                message=f"Gradient vanishing: norm {grad_norm:.8f}",
                step=step,
                timestamp=time.time(),
                metrics=metrics,
                action_required="INCREASE_LEARNING_RATE"
            ))
        
        # Check 3: NaN gradients
        if torch.isnan(torch.tensor(grad_norm)):
            alerts.append(Alert(
                level=AlertLevel.EMERGENCY,
                type="GRADIENT_NAN",
                message="NaN gradients detected",
                step=step,
                timestamp=time.time(),
                metrics=metrics,
                action_required="IMMEDIATE_ROLLBACK"
            ))
        
        return alerts

    def monitor_memory_usage(self, step: int) -> List[Alert]:
        """Monitora utilizzo memoria GPU."""
        alerts = []
        
        if torch.cuda.is_available():
            memory_allocated = torch.cuda.memory_allocated() / torch.cuda.max_memory_allocated()
            
            if memory_allocated > self.alert_thresholds["memory"]["critical_usage"]:
                alerts.append(Alert(
                    level=AlertLevel.CRITICAL,
                    type="MEMORY_CRITICAL",
                    message=f"Critical memory usage: {memory_allocated:.1%}",
                    step=step,
                    timestamp=time.time(),
                    metrics={"memory_usage": memory_allocated},
                    action_required="REDUCE_BATCH_SIZE"
                ))
            elif memory_allocated > self.alert_thresholds["memory"]["warning_usage"]:
                alerts.append(Alert(
                    level=AlertLevel.WARNING,
                    type="MEMORY_WARNING",
                    message=f"High memory usage: {memory_allocated:.1%}",
                    step=step,
                    timestamp=time.time(),
                    metrics={"memory_usage": memory_allocated},
                    action_required="MONITOR_CLOSELY"
                ))
        
        return alerts

    def monitor_symbol_accuracy(self, step: int, model: Any) -> List[Alert]:
        """Monitora accuratezza comprensione simboli."""
        alerts = []
        
        try:
            # Test simboli critici
            symbol_accuracy = self.test_symbol_comprehension(model)
            
            if symbol_accuracy < self.alert_thresholds["symbol_accuracy"]["critical_threshold"]:
                alerts.append(Alert(
                    level=AlertLevel.CRITICAL,
                    type="SYMBOL_ACCURACY_CRITICAL",
                    message=f"Critical symbol accuracy: {symbol_accuracy:.1%}",
                    step=step,
                    timestamp=time.time(),
                    metrics={"symbol_accuracy": symbol_accuracy},
                    action_required="ROLLBACK_AND_ADJUST"
                ))
            elif symbol_accuracy < self.alert_thresholds["symbol_accuracy"]["warning_threshold"]:
                alerts.append(Alert(
                    level=AlertLevel.WARNING,
                    type="SYMBOL_ACCURACY_WARNING",
                    message=f"Low symbol accuracy: {symbol_accuracy:.1%}",
                    step=step,
                    timestamp=time.time(),
                    metrics={"symbol_accuracy": symbol_accuracy},
                    action_required="INCREASE_SYMBOL_FOCUS"
                ))
        
        except Exception as e:
            alerts.append(Alert(
                level=AlertLevel.WARNING,
                type="SYMBOL_TEST_ERROR",
                message=f"Symbol accuracy test failed: {e}",
                step=step,
                timestamp=time.time(),
                metrics={},
                action_required="CHECK_MODEL_STATE"
            ))
        
        return alerts

    def monitor_baseline_performance(self, step: int, model: Any) -> List[Alert]:
        """Monitora preservazione performance baseline."""
        alerts = []
        
        try:
            # Test performance baseline
            current_performance = self.test_baseline_performance(model)
            baseline_ratio = current_performance / self.baseline_metrics.get("baseline_score", 1.0)
            
            if baseline_ratio < self.alert_thresholds["baseline_performance"]["critical_threshold"]:
                alerts.append(Alert(
                    level=AlertLevel.EMERGENCY,
                    type="BASELINE_DEGRADATION_CRITICAL",
                    message=f"Critical baseline degradation: {baseline_ratio:.1%}",
                    step=step,
                    timestamp=time.time(),
                    metrics={"baseline_ratio": baseline_ratio},
                    action_required="IMMEDIATE_ROLLBACK"
                ))
            elif baseline_ratio < self.alert_thresholds["baseline_performance"]["warning_threshold"]:
                alerts.append(Alert(
                    level=AlertLevel.WARNING,
                    type="BASELINE_DEGRADATION_WARNING",
                    message=f"Baseline performance declining: {baseline_ratio:.1%}",
                    step=step,
                    timestamp=time.time(),
                    metrics={"baseline_ratio": baseline_ratio},
                    action_required="MONITOR_CLOSELY"
                ))
        
        except Exception as e:
            alerts.append(Alert(
                level=AlertLevel.WARNING,
                type="BASELINE_TEST_ERROR",
                message=f"Baseline test failed: {e}",
                step=step,
                timestamp=time.time(),
                metrics={},
                action_required="CHECK_MODEL_STATE"
            ))
        
        return alerts

    def monitor_tokenizer_integrity(self, step: int) -> List[Alert]:
        """Monitora integrità tokenizer."""
        alerts = []
        
        try:
            # Import tokenizer safety system
            from .tokenizer_safety import TokenizerSafetySystem
            
            # Test integrità tokenizer
            safety_system = TokenizerSafetySystem("current_tokenizer")
            integrity_ok = safety_system.validate_tokenizer_integrity()
            
            if not integrity_ok:
                alerts.append(Alert(
                    level=AlertLevel.EMERGENCY,
                    type="TOKENIZER_CORRUPTION",
                    message="Tokenizer integrity compromised",
                    step=step,
                    timestamp=time.time(),
                    metrics={},
                    action_required="EMERGENCY_TOKENIZER_RESET"
                ))
        
        except Exception as e:
            alerts.append(Alert(
                level=AlertLevel.WARNING,
                type="TOKENIZER_TEST_ERROR",
                message=f"Tokenizer integrity test failed: {e}",
                step=step,
                timestamp=time.time(),
                metrics={},
                action_required="CHECK_TOKENIZER_STATE"
            ))
        
        return alerts

    def handle_alerts(self, step: int, alerts: List[Alert], model: Any):
        """Gestisce alerts generati."""
        # Salva alerts
        self.alerts_history.extend(alerts)
        
        # Categorizza per livello
        emergency_alerts = [a for a in alerts if a.level == AlertLevel.EMERGENCY]
        critical_alerts = [a for a in alerts if a.level == AlertLevel.CRITICAL]
        warning_alerts = [a for a in alerts if a.level == AlertLevel.WARNING]
        
        # Log alerts
        for alert in alerts:
            if alert.level == AlertLevel.EMERGENCY:
                logger.critical(f"🚨 EMERGENCY: {alert.message}")
            elif alert.level == AlertLevel.CRITICAL:
                logger.error(f"🔴 CRITICAL: {alert.message}")
            elif alert.level == AlertLevel.WARNING:
                logger.warning(f"🟡 WARNING: {alert.message}")
        
        # Azioni automatiche
        if emergency_alerts:
            self.trigger_emergency_stop(step, emergency_alerts, model)
        elif critical_alerts:
            self.trigger_critical_response(step, critical_alerts, model)

    def trigger_emergency_stop(self, step: int, alerts: List[Alert], model: Any):
        """Trigger emergency stop con rollback."""
        self.emergency_stops += 1
        
        logger.critical(f"🚨 EMERGENCY STOP #{self.emergency_stops} at step {step}")
        
        if self.emergency_stops >= self.max_emergency_stops:
            raise Exception("Maximum emergency stops reached - training aborted")
        
        # Salva stato per analisi
        self.save_emergency_state(step, alerts)
        
        # Trigger rollback (implementato dal training loop)
        raise EmergencyStopException(f"Emergency stop triggered: {[a.type for a in alerts]}")

    def trigger_critical_response(self, step: int, alerts: List[Alert], model: Any):
        """Trigger risposta critica."""
        logger.error(f"🔴 CRITICAL RESPONSE at step {step}")
        
        # Salva stato critico
        self.save_critical_state(step, alerts)
        
        # Notifica training loop per azioni correttive
        # (implementazione dipende dal training framework)

    def test_symbol_comprehension(self, model: Any) -> float:
        """Test comprensione simboli (placeholder)."""
        # Implementazione semplificata per test
        # In produzione userebbe il modello reale
        return 0.85  # 85% accuracy simulata

    def test_baseline_performance(self, model: Any) -> float:
        """Test performance baseline (placeholder)."""
        # Implementazione semplificata per test
        # In produzione userebbe benchmark reali
        return 0.92  # 92% baseline performance simulata

    def save_emergency_state(self, step: int, alerts: List[Alert]):
        """Salva stato di emergenza per analisi."""
        emergency_data = {
            "step": step,
            "timestamp": time.time(),
            "alerts": [
                {
                    "level": alert.level.value,
                    "type": alert.type,
                    "message": alert.message,
                    "metrics": alert.metrics
                }
                for alert in alerts
            ],
            "recent_metrics": self.metrics_history[-50:] if self.metrics_history else []
        }
        
        emergency_path = Path(f"neuroglyph/training/safety/emergency_state_{int(time.time())}.json")
        emergency_path.parent.mkdir(parents=True, exist_ok=True)
        
        with open(emergency_path, 'w', encoding='utf-8') as f:
            json.dump(emergency_data, f, indent=2, ensure_ascii=False)
        
        logger.info(f"💾 Emergency state saved: {emergency_path}")

    def save_critical_state(self, step: int, alerts: List[Alert]):
        """Salva stato critico per analisi."""
        # Simile a save_emergency_state ma per situazioni critiche
        pass

    def get_monitoring_report(self) -> Dict[str, Any]:
        """Genera report monitoring completo."""
        return {
            "monitoring_active": self.monitoring_active,
            "total_alerts": len(self.alerts_history),
            "emergency_stops": self.emergency_stops,
            "recent_alerts": self.alerts_history[-10:] if self.alerts_history else [],
            "metrics_tracked": len(self.metrics_history)
        }


class EmergencyStopException(Exception):
    """Eccezione per emergency stop."""
    pass


def main():
    """Test del sistema di monitoring."""
    print("📊 NEUROGLYPH REAL-TIME MONITORING SYSTEM")
    print("=" * 50)
    
    # Setup baseline metrics
    baseline_metrics = {
        "initial_loss": 1.0,
        "baseline_score": 0.95
    }
    
    # Inizializza monitor
    monitor = NeuroglyphRealTimeMonitor(baseline_metrics)
    monitor.start_monitoring()
    
    # Simula training steps
    print("🔄 Simulating training steps...")
    
    for step in range(1, 11):
        # Simula metriche
        metrics = {
            "train_loss": 1.0 - (step * 0.05),  # Loss decreasing
            "grad_norm": 1.0 + (step * 0.1)     # Grad norm increasing
        }
        
        try:
            alerts = monitor.monitor_training_step(step, metrics, None)
            print(f"Step {step}: {len(alerts)} alerts generated")
            
        except EmergencyStopException as e:
            print(f"🚨 Emergency stop at step {step}: {e}")
            break
    
    # Report finale
    print("\n📊 Monitoring Report:")
    report = monitor.get_monitoring_report()
    for key, value in report.items():
        print(f"  {key}: {value}")
    
    monitor.stop_monitoring()
    print("\n🎯 Monitoring system test completed")


if __name__ == "__main__":
    main()
