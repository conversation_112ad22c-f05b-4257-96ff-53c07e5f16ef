{"add_bos_token": false, "add_prefix_space": false, "added_tokens_decoder": {"151643": {"content": "<|endoftext|>", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "151644": {"content": "<|im_start|>", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "151645": {"content": "<|im_end|>", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "151646": {"content": "<|object_ref_start|>", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "151647": {"content": "<|object_ref_end|>", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "151648": {"content": "<|box_start|>", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "151649": {"content": "<|box_end|>", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "151650": {"content": "<|quad_start|>", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "151651": {"content": "<|quad_end|>", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "151652": {"content": "<|vision_start|>", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "151653": {"content": "<|vision_end|>", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "151654": {"content": "<|vision_pad|>", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "151655": {"content": "<|image_pad|>", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "151656": {"content": "<|video_pad|>", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "151657": {"content": "<tool_call>", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": false}, "151658": {"content": "</tool_call>", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": false}, "151659": {"content": "<|fim_prefix|>", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": false}, "151660": {"content": "<|fim_middle|>", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": false}, "151661": {"content": "<|fim_suffix|>", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": false}, "151662": {"content": "<|fim_pad|>", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": false}, "151663": {"content": "<|repo_name|>", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": false}, "151664": {"content": "<|file_sep|>", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": false}, "151665": {"content": "⋟", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "151666": {"content": "ng:structure:method_13", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "151667": {"content": "ng:social_cognition:social_understanding_4", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "151668": {"content": "🚈", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "151669": {"content": "ng:advanced_coding:introspection_proc", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "151670": {"content": "ng:distributed_systems:gossipprotocols_fn_2", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "151671": {"content": "ng:multimodal_reasoning:cross_modal_integration_4", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "151672": {"content": "ng:quantum_computing:quantum_optimization_4", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "151673": {"content": "␜", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "151674": {"content": "ng:emotional_intelligence:emotional_awareness", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "151675": {"content": "ng:language_understanding:lexical_access", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "151676": {"content": "⦀147", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "151677": {"content": "ng:mathematical_structures:combinatorics_fn_2", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "151678": {"content": "≷", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "151679": {"content": "ng:symbolic_ai:logicalinference", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "151680": {"content": "⦀815", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "151681": {"content": "🌏", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "151682": {"content": "☜", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "151683": {"content": "⑎", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "151684": {"content": "ng:neural_architectures:dense_layers_1", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "151685": {"content": "ng:memory_architectures:content_addressable_memory_1", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "151686": {"content": "⌆", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "151687": {"content": "ng:advanced_coding:metaclasses_meta", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "151688": {"content": "🠖", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "151689": {"content": "ng:logic:implies_strict", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "151690": {"content": "⒎", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "151691": {"content": "⟬3682", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "151692": {"content": "🚉", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "151693": {"content": "⪕", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "151694": {"content": "◿", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "151695": {"content": "❤", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "151696": {"content": "★", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "151697": {"content": "⟬8631", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "151698": {"content": "Ⓐ", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "151699": {"content": "⊵", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "151700": {"content": "ng:advanced_coding:memorypools_proc_1", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "151701": {"content": "⧳", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "151702": {"content": "❆", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "151703": {"content": "ng:advanced_coding:metaobjects_sys_1", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "151704": {"content": "⦀837", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "151705": {"content": "ng:machine_learning:ensemblemethods_34", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "151706": {"content": "ng:contextual_reasoning:presuppositions_1", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "151707": {"content": "ng:contextual_reasoning:cultural_context_1", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "151708": {"content": "ng:operator:mul", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "151709": {"content": "⟬1028", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "151710": {"content": "⒉", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "151711": {"content": "ng:multimodal_reasoning:modal_specific_processing_4", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "151712": {"content": "⅍", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "151713": {"content": "ng:reserved_expansion:extensionpoints", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "151714": {"content": "ng:reasoning_patterns:abductive_reasoning_1", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "151715": {"content": "ng:distributed_systems:distributedlocks_4", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "151716": {"content": "ng:decision_making:value_based_decisions_2", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "151717": {"content": "⦀496", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "151718": {"content": "⦀741", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "151719": {"content": "ng:reasoning_patterns:logical_equivalence_1", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "151720": {"content": "⦦", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "151721": {"content": "ng:memory_architectures:memory_schemas_1", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "151722": {"content": "⦀207", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "151723": {"content": "⑸", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "151724": {"content": "ng:self_reflection:metacognitive_awareness_5", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "151725": {"content": "ng:problem_solving:algorithmic_approaches_4", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "151726": {"content": "⟬1953", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "151727": {"content": "ng:advanced_coding:contextmanagers_2", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "151728": {"content": "ng:self_reflection:metacognition_6", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "151729": {"content": "⎴", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "151730": {"content": "ng:reasoning_patterns:syllogistic_reasoning", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "151731": {"content": "ng:problem_solving:means_ends_analysis", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "151732": {"content": "ng:category_theory:comonads_core_1", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "151733": {"content": "⨧", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "151734": {"content": "ng:social_cognition:social_understanding_5", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "151735": {"content": "🞙", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "151736": {"content": "ng:emotional_intelligence:emotion_regulation_3", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "151737": {"content": "ng:memory:pointer_4", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "151738": {"content": "⥋", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "151739": {"content": "ng:emotional_intelligence:emotional_expression_7", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "151740": {"content": "ng:operator:add_vector", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "151741": {"content": "⑧", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "151742": {"content": "ng:symbolic_ai:constraint_satisfaction", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "151743": {"content": "ng:operator:pow_1", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "151744": {"content": "ng:learning_algorithms:multi_task_learning", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "151745": {"content": "⨵", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "151746": {"content": "ng:creative_thinking:elaboration", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "151747": {"content": "ng:consciousness_models:global_workspace_1", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "151748": {"content": "┮", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "151749": {"content": "⩈", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "151750": {"content": "ng:calculus:rightwards_double_arrow_w", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "151751": {"content": "✈", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "151752": {"content": "⯪", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "151753": {"content": "ng:extension:sample_complexity", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "151754": {"content": "ng:goal_oriented_behavior:goal_conflict_resolution_2", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "151755": {"content": "⒜", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "151756": {"content": "ng:advanced_coding:metaclasses_meta_3", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "151757": {"content": "⏯", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "151758": {"content": "⧎", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "151759": {"content": "ng:reasoning_patterns:truth_tables_9", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "151760": {"content": "➳", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "151761": {"content": "🍛", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "151762": {"content": "🠠", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "151763": {"content": "ng:neural_architectures:positional_encoding_8", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "151764": {"content": "➮", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "151765": {"content": "⟬3124", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "151766": {"content": "↼", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "151767": {"content": "⏪", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "151768": {"content": "⌈", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "151769": {"content": "⭝", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "151770": {"content": "ng:self_reflection:self_regulation_7", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "151771": {"content": "␕", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "151772": {"content": "⦀574", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "151773": {"content": "⟬2536", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "151774": {"content": "𝜱", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "151775": {"content": "◝", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "151776": {"content": "ng:extension:bennett_inequality", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "151777": {"content": "≧", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "151778": {"content": "⟬7262", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "151779": {"content": "𝞚", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "151780": {"content": "ng:analogical_reasoning:structural_alignment_1", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "151781": {"content": "ng:emotional_intelligence:mood_regulation_5", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "151782": {"content": "ng:goal_oriented_behavior:persistence_4", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "151783": {"content": "ng:cognitive_architectures:cognitive_load_3", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "151784": {"content": "ng:agents:agent_perception", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "151785": {"content": "🠙", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "151786": {"content": "⬑", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "151787": {"content": "ng:creative_thinking:originality_3", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "151788": {"content": "ng:contextual_reasoning:implicatures_3", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "151789": {"content": "✞", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "151790": {"content": "ng:adaptive_intelligence:expertise_development_3", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "151791": {"content": "ℼ", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "151792": {"content": "ng:self:self_evolution", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "151793": {"content": "✰", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "151794": {"content": "⏼", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "151795": {"content": "ng:self_reflection:self_evaluation_4", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "151796": {"content": "⯨", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "151797": {"content": "⭽", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "151798": {"content": "⇌", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "151799": {"content": "ng:attention_systems:sustained_attention_5", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "151800": {"content": "ng:operator:sub_11", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "151801": {"content": "⭫", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "151802": {"content": "ng:consciousness_models:subjective_experience_5", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "151803": {"content": "🌧", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "151804": {"content": "ng:cognition:cognitive_state", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "151805": {"content": "ng:cognitive_architectures:declarative_memory_3", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "151806": {"content": "◶", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "151807": {"content": "ng:philosophical_concepts:metaphysics_ctrl_6", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "151808": {"content": "⯠", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "151809": {"content": "⫩", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "151810": {"content": "⟉", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "151811": {"content": "ng:extension:question_answering", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "151812": {"content": "ng:symbolic_ai:search_algorithms", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "151813": {"content": "ng:topology:left_right_wave_arrow", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "151814": {"content": "ng:logic:iff_9", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "151815": {"content": "ng:meta_programming:macrosystems_1", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "151816": {"content": "ng:category_theory:topoi_op", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "151817": {"content": "ng:operator:sub_12", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "151818": {"content": "⩒", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "151819": {"content": "ng:consciousness_models:higher_order_thought_1", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "151820": {"content": "⩁", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "151821": {"content": "ng:extension:congestion_control", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "151822": {"content": "⦀433", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "151823": {"content": "ng:attention_systems:attention_resources", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "151824": {"content": "ng:advanced_coding:emit_ctrl", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "151825": {"content": "ng:arrows:upwards_white_arrow_on_pe", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "151826": {"content": "∃", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "151827": {"content": "ng:decision_making:choice_architecture_1", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "151828": {"content": "ng:distributed_systems:leaderelection_core", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "151829": {"content": "∊", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "151830": {"content": "ng:operator:div_13", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "151831": {"content": "🞹", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "151832": {"content": "ng:self_reflection:self_evaluation_6", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "151833": {"content": "⟬4854", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "151834": {"content": "ng:operator:sub_13", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "151835": {"content": "🚇", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "151836": {"content": "ng:concurrency_advanced:compareandswap_op", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "151837": {"content": "⟬4002", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "151838": {"content": "⊕", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "151839": {"content": "⇑", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "151840": {"content": "⟬1915", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "151841": {"content": "ng:flow:if_switch", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "151842": {"content": "🍞", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "151843": {"content": "⦀993", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "151844": {"content": "🌶", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "151845": {"content": "ng:distributed_systems:loadbalancing_fn", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "151846": {"content": "𝝚", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "151847": {"content": "ng:consciousness_models:global_workspace_7", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "151848": {"content": "🌟", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "151849": {"content": "▾", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "151850": {"content": "≋", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "151851": {"content": "ng:algebra:upwards_harpoon_with_barb", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "151852": {"content": "ng:attention_systems:spatial_attention_4", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "151853": {"content": "ng:self_reflection:learning_strategies_2", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "151854": {"content": "🜋", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "151855": {"content": "ng:problem_solving:problem_identification_2", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "151856": {"content": "ng:goal_oriented_behavior:planning_5", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "151857": {"content": "∥", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "151858": {"content": "🞶", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "151859": {"content": "⮓", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "151860": {"content": "ng:advanced_coding:decorators_proc_5", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "151861": {"content": "ng:distributed_systems:replication_ctrl", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "151862": {"content": "⟬7876", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "151863": {"content": "😶", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "151864": {"content": "ng:memory:free_15", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "151865": {"content": "𝔙", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "151866": {"content": "ng:reserved_expansion:future_op", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "151867": {"content": "⊟", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "151868": {"content": "ng:decision_making:decision_confidence_3", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "151869": {"content": "🟚", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "151870": {"content": "ng:meta_programming:dynamic_compilation", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "151871": {"content": "ng:neural_architectures:positional_encoding_1", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "151872": {"content": "ng:contextual_reasoning:context_switching", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "151873": {"content": "ng:physics_notation:downwards_arrow_with_tip_", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "151874": {"content": "ng:flow:break_14", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "151875": {"content": "𝗇", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "151876": {"content": "⩢", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "151877": {"content": "ng:extension:image_processing", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "151878": {"content": "ng:quantum_computing:quantum_gates_1", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "151879": {"content": "ng:consciousness_models:phenomenal_consciousness_1", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "151880": {"content": "ng:extension:temporal_reasoning", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "151881": {"content": "⫨", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "151882": {"content": "⦀413", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "151883": {"content": "ng:logical_operators:downwards_arrow_with_doub", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "151884": {"content": "ng:multimodal_reasoning:tactile_understanding", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "151885": {"content": "⟬9981", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "151886": {"content": "🚓", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "151887": {"content": "ng:structure:function_6", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "151888": {"content": "ng:semantic_understanding:metaphorical_meaning_1", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "151889": {"content": "ng:operator:pow_2", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "151890": {"content": "🚷", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "151891": {"content": "↛", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "151892": {"content": "ꭐ", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "151893": {"content": "⩬", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "151894": {"content": "ng:advanced_coding:descriptors_op", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "151895": {"content": "🌍", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "151896": {"content": "╓", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "151897": {"content": "ng:memory_architectures:memory_retrieval", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "151898": {"content": "ng:mathematical_structures:combinatorics_meta_1", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "151899": {"content": "⋊", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "151900": {"content": "ng:extension:sparql_queries", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "151901": {"content": "⟬9940", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "151902": {"content": "⭠", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "151903": {"content": "⫧", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "151904": {"content": "⅌", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "151905": {"content": "ng:quantum_computing:entanglement_1", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "151906": {"content": "↙", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "151907": {"content": "ng:analogical_reasoning:exemplar_reasoning_4", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "151908": {"content": "⋀", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "151909": {"content": "⭇", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "151910": {"content": "ng:decision_making:choice_architecture_2", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "151911": {"content": "⋳", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "151912": {"content": "⟬3067", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "151913": {"content": "ng:distributed_systems:circuitbreakers_op_4", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "151914": {"content": "ng:arrows:leftwards_paired_arrows", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "151915": {"content": "ng:self_reflection:self_knowledge_7", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "151916": {"content": "😹", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "151917": {"content": "⟬5056", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "151918": {"content": "⦀992", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "151919": {"content": "ng:self_reflection:self_regulation_2", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "151920": {"content": "⦭", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "151921": {"content": "ng:structure:method_7", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "151922": {"content": "ng:reasoning_patterns:truth_tables_2", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "151923": {"content": "ng:emotional_intelligence:affective_computing_3", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "151924": {"content": "ng:problem_solving:problem_decomposition_1", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "151925": {"content": "ng:social_cognition:intention_recognition_6", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "151926": {"content": "ng:operator:pow_9", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "151927": {"content": "🞵", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "151928": {"content": "⟬9803", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "151929": {"content": "☲", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "151930": {"content": "ng:calculus:upwards_dashed_arrow", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "151931": {"content": "♚", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "151932": {"content": "ng:emotional_intelligence:mood_regulation_2", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "151933": {"content": "ng:multimodal_reasoning:auditory_processing_4", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "151934": {"content": "➹", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "151935": {"content": "ng:memory:alloc_6", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "151936": {"content": "ng:emotional_intelligence:emotional_contagion_4", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "151937": {"content": "ng:symbolic_ai:expert_systems", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "151938": {"content": "⦀871", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "151939": {"content": "ng:reasoning_patterns:pattern_matching", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "151940": {"content": "ng:meta_programming:codeasdata_sys_1", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "151941": {"content": "┴", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "151942": {"content": "ng:operator:div_7", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "151943": {"content": "ng:neural_architectures:multi_head_meta", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "151944": {"content": "ng:goal_oriented_behavior:subgoal_decomposition_1", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "151945": {"content": "➏", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "151946": {"content": "⇞", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "151947": {"content": "ng:emotional_intelligence:emotional_contagion", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "151948": {"content": "⟾", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "151949": {"content": "🌡", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "151950": {"content": "ng:knowledge_representation:inheritance_relations", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "151951": {"content": "⦀768", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "151952": {"content": "🠨", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "151953": {"content": "◕", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "151954": {"content": "⨂", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "151955": {"content": "ng:meta_programming:programsynthesis_op", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "151956": {"content": "⦀379", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "151957": {"content": "ng:adaptive_intelligence:strategy_selection", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "151958": {"content": "≞", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "151959": {"content": "ng:meta_programming:generic_programming_2", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "151960": {"content": "⥁", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "151961": {"content": "ng:logic:xor_5", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "151962": {"content": "ng:symbolic_ai:expertsystems_proc", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "151963": {"content": "⦀772", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "151964": {"content": "🛖", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "151965": {"content": "⨔", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "151966": {"content": "≽", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "151967": {"content": "ng:advanced_coding:generators_ctrl_3", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "151968": {"content": "ng:creative_thinking:creative_constraints_9", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "151969": {"content": "ng:creative_thinking:originality_1", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "151970": {"content": "⎂", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "151971": {"content": "ng:cognitive_architectures:interference_resolution_2", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "151972": {"content": "ng:semantic_understanding:compositional_semantics_4", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "151973": {"content": "ng:memory:ref_9", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "151974": {"content": "ng:consciousness_models:integrated_information", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "151975": {"content": "ng:contextual_reasoning:background_knowledge", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "151976": {"content": "ng:meta_programming:code_transformation_1", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "151977": {"content": "ng:concurrency_advanced:hazardpointers_op_8", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "151978": {"content": "ng:adaptive_intelligence:performance_optimization", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "151979": {"content": "🜏", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "151980": {"content": "⪏", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "151981": {"content": "⦀718", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "151982": {"content": "ng:extension:hidden_markov", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "151983": {"content": "ng:problem_solving:hill_climbing_2", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "151984": {"content": "✩", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "151985": {"content": "⏲", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "151986": {"content": "ng:advanced_coding:metaobjects_op", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "151987": {"content": "➌", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "151988": {"content": "ng:advanced_coding:coroutines_meta_1", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "151989": {"content": "ng:problem_solving:algorithmic_approaches_3", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "151990": {"content": "ng:number_forms:double_struck_n_ary_summa", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "151991": {"content": "ng:goal_oriented_behavior:goal_setting_2", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "151992": {"content": "ng:quantum_computing:quantumcircuits_op", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "151993": {"content": "🞃", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "151994": {"content": "ng:self:introspection", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "151995": {"content": "⩪", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "151996": {"content": "♢", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "151997": {"content": "ng:quantum_computing:quantum_measurement_1", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "151998": {"content": "⥀", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "151999": {"content": "ng:reasoning_patterns:proof_strategies_3", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "152000": {"content": "␛", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "152001": {"content": "☆", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "152002": {"content": "∬", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "152003": {"content": "ng:neural_architectures:attention_backpropagation", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "152004": {"content": "⤉", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "152005": {"content": "⤫", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "152006": {"content": "ng:reserved_expansion:extensionpoints_4", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "152007": {"content": "ng:contextual_reasoning:contextual_adaptation", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "152008": {"content": "ng:contextual_reasoning:contextual_adaptation_2", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "152009": {"content": "⦀607", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "152010": {"content": "ng:social_cognition:collaboration_10", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "152011": {"content": "⇙", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "152012": {"content": "ng:decision_making:preference_formation_1", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "152013": {"content": "🚅", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "152014": {"content": "⇮", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "152015": {"content": "ng:adaptive_intelligence:expertise_development", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "152016": {"content": "␎", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "152017": {"content": "⟬9001", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "152018": {"content": "♓", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "152019": {"content": "ng:emotional_intelligence:mood_regulation_3", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "152020": {"content": "ng:advanced_coding:iterators_ctrl", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "152021": {"content": "⍴", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "152022": {"content": "ng:problem_solving:heuristic_methods", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "152023": {"content": "ng:meta_programming:stagedcomputation_3", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "152024": {"content": "ng:memory_architectures:memory_interference_3", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "152025": {"content": "⦠", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "152026": {"content": "ng:operator:div_8", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "152027": {"content": "ng:analysis:rightwards_double_arrow", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "152028": {"content": "ng:philosophical_concepts:metaphysics_ctrl_2", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "152029": {"content": "🠴", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "152030": {"content": "ng:self:identity_core", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "152031": {"content": "⤮", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "152032": {"content": "⫈", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "152033": {"content": "⦀991", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "152034": {"content": "ng:contextual_reasoning:conversational_context", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "152035": {"content": "ng:logic:implies_10", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "152036": {"content": "┑", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "152037": {"content": "ng:mathematical_structures:combinatorics_core_5", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "152038": {"content": "ng:social_cognition:social_understanding_1", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "152039": {"content": "⧖", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "152040": {"content": "⪹", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "152041": {"content": "⊠", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "152042": {"content": "ng:category_theory:limits_meta", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "152043": {"content": "ng:supplemental_math:account_of", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "152044": {"content": "🠻", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "152045": {"content": "ng:neural_architectures:activation_lstm", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "152046": {"content": "⩠", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "152047": {"content": "☕", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "152048": {"content": "ng:advanced_coding:metaobjects_fn_3", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "152049": {"content": "⥛", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "152050": {"content": "◅", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "152051": {"content": "ng:distributed_systems:replication_op", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "152052": {"content": "↜", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "152053": {"content": "⦎", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "152054": {"content": "⮇", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "152055": {"content": "ng:adaptive_intelligence:learning_transfer_2", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "152056": {"content": "⭭", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "152057": {"content": "╊", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "152058": {"content": "ng:meta:meaning_space", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "152059": {"content": "🠽", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "152060": {"content": "⮏", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "152061": {"content": "⑖", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "152062": {"content": "ng:multimodal_reasoning:visual_reasoning_3", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "152063": {"content": "⦀934", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "152064": {"content": "⏗", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "152065": {"content": "ng:problem_solving:solution_evaluation_3", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "152066": {"content": "⨩", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "152067": {"content": "⥂", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "152068": {"content": "ng:meta_programming:codeasdata_core_1", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "152069": {"content": "ng:analogical_reasoning:analogical_mapping_1", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "152070": {"content": "ng:causal_reasoning:correlation_causation_3", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "152071": {"content": "𝞽", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "152072": {"content": "ng:meta_programming:dynamic_analysis_5", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "152073": {"content": "⋺", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "152074": {"content": "ng:goal_oriented_behavior:planning", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "152075": {"content": "ng:adaptive_intelligence:strategy_selection_5", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "152076": {"content": "ng:memory:pointer_17", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "152077": {"content": "⦀795", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "152078": {"content": "ng:memory:ref_10", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "152079": {"content": "ng:memory_architectures:associative_memory_1", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "152080": {"content": "⩌", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "152081": {"content": "ng:goal_oriented_behavior:goal_pursuit_1", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "152082": {"content": "ng:type_theory:uniontypes_1", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "152083": {"content": "ng:reasoning:reflection", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "152084": {"content": "⦀663", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "152085": {"content": "ng:formal_verification:symbolicexecution_3", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "152086": {"content": "ng:symbolic_ai:logicalinference_3", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "152087": {"content": "⌄", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "152088": {"content": "ng:causal_reasoning:causal_prediction", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "152089": {"content": "ng:physics_notation:north_west_double_arrow", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "152090": {"content": "ng:flow:if_17", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "152091": {"content": "ng:advanced_coding:introspection_sys", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "152092": {"content": "ng:category_theory:adjunctions_proc", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "152093": {"content": "ng:concurrency_advanced:memoryordering", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "152094": {"content": "ng:operator:mul_6", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "152095": {"content": "ng:language_understanding:phonological_processing_2", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "152096": {"content": "⩼", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "152097": {"content": "⟼", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "152098": {"content": "ng:agents:intention_driven", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "152099": {"content": "ng:reasoning_patterns:logical_equivalence_3", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "152100": {"content": "🟫", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "152101": {"content": "🟳", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "152102": {"content": "ng:physics_notation:script_capital_h", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "152103": {"content": "ng:causal_reasoning:causal_prediction_3", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "152104": {"content": "ng:creative_thinking:creative_constraints_6", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "152105": {"content": "☻", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "152106": {"content": "ng:flow:while_14", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "152107": {"content": "ng:decision_making:multi_criteria_decisions_4", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "152108": {"content": "ng:memory_architectures:memory_encoding_3", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "152109": {"content": "ng:analysis:leftwards_wave_arrow", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "152110": {"content": "⦀342", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "152111": {"content": "⟬5000", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "152112": {"content": "ng:social_cognition:belief_attribution_6", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "152113": {"content": "⦀732", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "152114": {"content": "⦀459", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "152115": {"content": "ng:multimodal_reasoning:sensory_substitution_4", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "152116": {"content": "ng:reasoning_patterns:inference_rules_1", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "152117": {"content": "🝵", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "152118": {"content": "☒", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "152119": {"content": "😃", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "152120": {"content": "⭜", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "152121": {"content": "ng:structure:function_14", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "152122": {"content": "ng:flow:while_async", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "152123": {"content": "ng:quantum_computing:quantum_simulation_1", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "152124": {"content": "➁", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "152125": {"content": "ng:multimodal_reasoning:multimodal_attention_3", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "152126": {"content": "ng:quantum_computing:entanglement_op", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "152127": {"content": "ng:self_reflection:self_knowledge_6", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "152128": {"content": "ng:social_cognition:theory_of_mind_6", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "152129": {"content": "ng:cognitive_modeling:decisionmaking_core", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "152130": {"content": "𝚶", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "152131": {"content": "ng:symbolic_ai:theoremproving_sys_3", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "152132": {"content": "Ⓒ", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "152133": {"content": "ng:distributed_systems:sharding_op_2", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "152134": {"content": "⌃", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "152135": {"content": "🢔", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "152136": {"content": "⥧", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "152137": {"content": "⟬4737", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "152138": {"content": "ng:symbolic_ai:higher_order_logic_9", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "152139": {"content": "⪾", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "152140": {"content": "ng:reasoning:modus_ponens", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "152141": {"content": "⟬6953", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "152142": {"content": "⩣", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "152143": {"content": "℅", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "152144": {"content": "ng:memory:alloc_19", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "152145": {"content": "ng:machine_learning:modelevaluation_fn_1", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "152146": {"content": "🠑", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "152147": {"content": "ng:advanced_coding:memorypools_sys_2", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "152148": {"content": "ng:advanced_coding:descriptors_fn_1", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "152149": {"content": "🠤", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "152150": {"content": "ng:extension:earth_mover", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "152151": {"content": "ng:analogical_reasoning:structural_alignment_3", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "152152": {"content": "◣", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "152153": {"content": "┽", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "152154": {"content": "ng:operator:div_9", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "152155": {"content": "ng:social_cognition:perspective_taking_4", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "152156": {"content": "ng:reasoning_patterns:modus_tollens_3", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "152157": {"content": "ng:distributed_systems:circuitbreakers_op_7", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "152158": {"content": "ng:contextual_reasoning:cultural_context_3", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "152159": {"content": "⊛", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "152160": {"content": "↺", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "152161": {"content": "⊚", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "152162": {"content": "⍛", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "152163": {"content": "ng:meta_programming:programsynthesis_11", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "152164": {"content": "ng:consciousness_models:embodied_cognition_7", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "152165": {"content": "𝝭", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "152166": {"content": "ng:self_reflection:executive_monitoring_5", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "152167": {"content": "ng:logic:or_15", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "152168": {"content": "ng:philosophical_concepts:ontology_sys", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "152169": {"content": "⬩", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "152170": {"content": "△", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "152171": {"content": "🠞", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "152172": {"content": "↭", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "152173": {"content": "⨁", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "152174": {"content": "ng:cognitive_architectures:semantic_memory_1", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "152175": {"content": "ng:analogical_reasoning:systematic_analogies_1", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "152176": {"content": "⟬9759", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "152177": {"content": "ng:language_understanding:lexical_access_5", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "152178": {"content": "ng:emotional_intelligence:affective_computing_6", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "152179": {"content": "🞰", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "152180": {"content": "⊆", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "152181": {"content": "ng:meta_programming:jit_compilation_1", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "152182": {"content": "⮩", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "152183": {"content": "⯎", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "152184": {"content": "ng:emotional_intelligence:emotion_understanding_1", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "152185": {"content": "⭨", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "152186": {"content": "ng:flow:while_17", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "152187": {"content": "ng:structure:property_3", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "152188": {"content": "ng:reasoning:conclusion", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "152189": {"content": "🜚", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "152190": {"content": "ng:advanced_coding:generators_ctrl_2", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "152191": {"content": "ng:goal_oriented_behavior:goal_pursuit_4", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "152192": {"content": "ng:memory:deref_8", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "152193": {"content": "ng:memory_architectures:memory_interference", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "152194": {"content": "ng:social_cognition:theory_of_mind", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "152195": {"content": "ng:advanced_coding:contextmanagers_op", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "152196": {"content": "ng:analogical_reasoning:analogical_mapping_3", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "152197": {"content": "🡀", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "152198": {"content": "𝘓", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "152199": {"content": "ng:self_reflection:introspection_3", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "152200": {"content": "ng:advanced_coding:stackframes_proc", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "152201": {"content": "ng:consciousness_models:access_consciousness_6", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "152202": {"content": "ng:operator:div_19", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "152203": {"content": "➝", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "152204": {"content": "ng:machine_learning:deeplearning_ctrl_1", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "152205": {"content": "⦳", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "152206": {"content": "ng:extension:formal_methods", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "152207": {"content": "⦀781", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "152208": {"content": "⌤", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "152209": {"content": "ng:learning_algorithms:meta_learning_4", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "152210": {"content": "ng:social_cognition:cultural_understanding_6", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "152211": {"content": "ng:neural_architectures:gate_connections", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "152212": {"content": "ng:multimodal_reasoning:perceptual_binding_4", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "152213": {"content": "ng:contextual_reasoning:common_sense_reasoning_1", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "152214": {"content": "ng:meta_programming:macrosystems_3", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "152215": {"content": "⭧", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "152216": {"content": "ng:concurrency_advanced:hazardpointers_sys_7", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "152217": {"content": "ng:reasoning_patterns:modus_tollens_2", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "152218": {"content": "ng:adaptive_intelligence:behavioral_adaptation_4", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "152219": {"content": "⤱", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "152220": {"content": "⇎", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "152221": {"content": "ng:analogical_reasoning:exemplar_reasoning", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "152222": {"content": "ng:architecture:distributed_system", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "152223": {"content": "ng:attention_systems:divided_attention", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "152224": {"content": "⦊", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "152225": {"content": "ng:advanced_coding:decorators_sys_1", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "152226": {"content": "⋕", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "152227": {"content": "ng:advanced_coding:metaclasses_op", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "152228": {"content": "🚿", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "152229": {"content": "ng:cognitive_architectures:attention_control_1", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "152230": {"content": "ng:advanced_coding:descriptors_sys", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "152231": {"content": "ng:extension:poisson_process", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "152232": {"content": "ng:problem_solving:hill_climbing_1", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "152233": {"content": "ng:reserved_expansion:extensionpoints_fn_4", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "152234": {"content": "⅀", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "152235": {"content": "┰", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "152236": {"content": "⩀", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "152237": {"content": "⮊", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "152238": {"content": "⬆", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "152239": {"content": "🞩", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "152240": {"content": "⦀700", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "152241": {"content": "⫾", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "152242": {"content": "ng:social_cognition:cultural_understanding_7", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "152243": {"content": "ng:multimodal_reasoning:cross_modal_plasticity", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "152244": {"content": "⧀", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "152245": {"content": "ng:flow:if_13", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "152246": {"content": "⇘", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "152247": {"content": "ng:self_reflection:learning_strategies_4", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "152248": {"content": "ng:flow:for_12", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "152249": {"content": "⮣", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "152250": {"content": "🝪", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "152251": {"content": "🛁", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "152252": {"content": "ng:machine_learning:ensemblemethods_42", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "152253": {"content": "☼", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "152254": {"content": "🞗", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "152255": {"content": "ng:operator:div_integer", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "152256": {"content": "ng:temporal_cognition:duration_estimation_3", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "152257": {"content": "ng:structure:property_6", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "152258": {"content": "ng:memory:alloc_12", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "152259": {"content": "🠗", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "152260": {"content": "∨", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "152261": {"content": "ng:advanced_coding:metaclasses_3", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "152262": {"content": "ng:analogical_reasoning:similarity_assessment", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "152263": {"content": "🚔", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "152264": {"content": "ng:self_reflection:metacognitive_awareness_7", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "152265": {"content": "🙊", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "152266": {"content": "ng:logical_operators:clockwise_open_circle_arr", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "152267": {"content": "ng:self_reflection:metacognitive_awareness_2", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "152268": {"content": "ng:analogical_reasoning:similarity_assessment_2", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "152269": {"content": "⨦", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "152270": {"content": "ng:contextual_reasoning:pragmatic_inference_1", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "152271": {"content": "ng:memory:alloc_20", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "152272": {"content": "ng:temporal_cognition:event_timing_2", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "152273": {"content": "ng:cognitive_architectures:updating_memory_1", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "152274": {"content": "𝘥", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "152275": {"content": "⦀381", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "152276": {"content": "ng:temporal:temporal_pattern", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "152277": {"content": "ng:reasoning_patterns:inductive_reasoning_3", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "152278": {"content": "🠅", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "152279": {"content": "ng:goal_oriented_behavior:goal_monitoring_7", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "152280": {"content": "ng:distributed_systems:loadbalancing_proc", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "152281": {"content": "ng:consciousness_models:integrated_information_1", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "152282": {"content": "ng:neural_architectures:weight_initialization_2", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "152283": {"content": "ng:adaptive_intelligence:cognitive_plasticity_2", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "152284": {"content": "⌍", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "152285": {"content": "ng:flow:break_8", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "152286": {"content": "ng:attention_systems:spatial_attention_5", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "152287": {"content": "⦀887", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "152288": {"content": "ng:operator:add_16", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "152289": {"content": "ng:symbolic_ai:higher_order_logic_11", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "152290": {"content": "⦙", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "152291": {"content": "⍹", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "152292": {"content": "ng:extension:transfer_learning", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "152293": {"content": "ng:learning_algorithms:online_learning_1", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "152294": {"content": "≓", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "152295": {"content": "⦀677", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "152296": {"content": "ng:extension:secure_multiparty", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "152297": {"content": "🠹", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "152298": {"content": "ng:symbolic_ai:descriptionlogics_5", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "152299": {"content": "🟯", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "152300": {"content": "🠛", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "152301": {"content": "╈", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "152302": {"content": "⥚", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "152303": {"content": "⦀598", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "152304": {"content": "ng:advanced_coding:reflect_op", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "152305": {"content": "ng:contextual_reasoning:social_context_1", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "152306": {"content": "⮉", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "152307": {"content": "ng:cognitive_architectures:long_term_memory_3", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "152308": {"content": "⦀827", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "152309": {"content": "ng:memory:ref_13", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "152310": {"content": "⪌", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "152311": {"content": "ng:operator:sub", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "152312": {"content": "ng:goal_oriented_behavior:goal_conflict_resolution_1", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "152313": {"content": "ng:meta_programming:macrosystems_fn", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "152314": {"content": "𝛺", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "152315": {"content": "⫀", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "152316": {"content": "⩔", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "152317": {"content": "⟬1941", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "152318": {"content": "⦀309", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "152319": {"content": "ng:meta_programming:macrosystems_op_1", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "152320": {"content": "ng:causal_reasoning:causal_prediction_1", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "152321": {"content": "ng:decision_making:preference_formation_4", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "152322": {"content": "ng:advanced_coding:metaclasses_sys_1", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "152323": {"content": "⟬8748", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "152324": {"content": "ng:social_cognition:theory_of_mind_5", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "152325": {"content": "ng:extension:differential_privacy", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "152326": {"content": "ng:emotional_intelligence:emotional_contagion_7", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "152327": {"content": "⫵", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "152328": {"content": "ng:advanced_coding:metaclasses_proc_2", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "152329": {"content": "ng:technical_symbols:telephone_sign", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "152330": {"content": "⍧", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "152331": {"content": "ng:symbolic_ai:semanticnetworks_3", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "152332": {"content": "ng:temporal_cognition:duration_estimation_1", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "152333": {"content": "❾", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "152334": {"content": "ng:memory:deref_6", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "152335": {"content": "ng:causal_reasoning:causal_mechanisms", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "152336": {"content": "ng:self_reflection:executive_monitoring_3", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "152337": {"content": "⌹", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "152338": {"content": "ng:reserved_expansion:extensionpoints_fn_8", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "152339": {"content": "⦀782", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "152340": {"content": "⊓", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "152341": {"content": "ng:structure:property_22", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "152342": {"content": "ng:memory:alloc_10", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "152343": {"content": "ng:neural_architectures:lossfunctions_proc", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "152344": {"content": "ng:social_cognition:social_norms_7", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "152345": {"content": "ng:distributed_systems:raft_op", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "152346": {"content": "ng:cognitive_modeling:memorymodels_op_1", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "152347": {"content": "⦀271", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "152348": {"content": "⦀883", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "152349": {"content": "🙌", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "152350": {"content": "⏳", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "152351": {"content": "🟌", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "152352": {"content": "ng:temporal_cognition:time_perception_4", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "152353": {"content": "ng:analogical_reasoning:analogical_problem_solving_4", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "152354": {"content": "ng:problem_solving:means_ends_analysis_4", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "152355": {"content": "ng:philosophical_concepts:logicphilosophy_fn", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "152356": {"content": "⦀793", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "152357": {"content": "ng:cognitive_architectures:long_term_memory_1", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "152358": {"content": "ng:neural_architectures:cross_attn_proc", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "152359": {"content": "⫃", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "152360": {"content": "ng:goal_oriented_behavior:goal_monitoring_1", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "152361": {"content": "⨑", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "152362": {"content": "ng:algebra:rightwards_dashed_arrow", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "152363": {"content": "♜", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "152364": {"content": "🜤", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "152365": {"content": "ng:reasoning:specialization", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "152366": {"content": "ng:analogical_reasoning:analogical_transfer_2", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "152367": {"content": "ng:logic:implies_8", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "152368": {"content": "ng:machine_learning:prediction_fn", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "152369": {"content": "ng:semantic_understanding:contextual_meaning_3", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "152370": {"content": "ng:multimodal_reasoning:modal_specific_processing", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "152371": {"content": "ng:supplemental_math:leftwards_harpoon_over_ri", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "152372": {"content": "␔", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "152373": {"content": "↧", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "152374": {"content": "ng:decision_making:rational_choice", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "152375": {"content": "ng:symbolic_ai:ontologies_sys_3", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "152376": {"content": "🝛", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "152377": {"content": "ng:cognition:mental_effort", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "152378": {"content": "ng:memory_architectures:false_memories_1", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "152379": {"content": "🚨", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "152380": {"content": "⟻", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "152381": {"content": "⏧", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "152382": {"content": "ng:decision_making:decision_biases_2", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "152383": {"content": "⦀145", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "152384": {"content": "ng:agents:collective_behavior", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "152385": {"content": "ng:extension:chatbot_design", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "152386": {"content": "ng:cognitive_modeling:decisionmaking_proc", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "152387": {"content": "ng:self_reflection:self_monitoring_2", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "152388": {"content": "ng:geometry:double_struck_capital_gam", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "152389": {"content": "℈", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "152390": {"content": "🟻", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "152391": {"content": "ng:flow:break_13", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "152392": {"content": "ng:consciousness_models:attention_schema_6", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "152393": {"content": "ng:reserved_expansion:researchareas_fn_2", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "152394": {"content": "ng:language_understanding:grammatical_knowledge_1", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "152395": {"content": "℔", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "152396": {"content": "⋂", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "152397": {"content": "ng:cognitive_architectures:executive_control", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "152398": {"content": "ng:creative_thinking:originality_2", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "152399": {"content": "ng:logic:not_21", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "152400": {"content": "ng:distributed_systems:loadbalancing", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "152401": {"content": "ng:knowledge_representation:knowledge_graphs_1", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "152402": {"content": "⦀452", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "152403": {"content": "⪚", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "152404": {"content": "⒢", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "152405": {"content": "🚵", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "152406": {"content": "ng:quantum_computing:quantum_machine_learning_1", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "152407": {"content": "ng:problem_solving:constraint_satisfaction", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "152408": {"content": "⭪", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "152409": {"content": "⊷", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "152410": {"content": "ng:meta_programming:automated_refactoring", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "152411": {"content": "ng:structure:property_12", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "152412": {"content": "ng:cognitive_architectures:cognitive_flexibility", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "152413": {"content": "⨠", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "152414": {"content": "🛄", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "152415": {"content": "∯", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "152416": {"content": "ng:learning_algorithms:supervised_learning_4", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "152417": {"content": "≃", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "152418": {"content": "⦔", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "152419": {"content": "●", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "152420": {"content": "ng:machine_learning:ensemblemethods", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "152421": {"content": "⥍", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "152422": {"content": "⫔", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "152423": {"content": "␻", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "152424": {"content": "⟬2163", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "152425": {"content": "🛞", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "152426": {"content": "⇋", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "152427": {"content": "ng:knowledge_representation:part_whole_relations_2", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "152428": {"content": "Ⓚ", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "152429": {"content": "ng:reserved_expansion:novelabstractions_8", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "152430": {"content": "ng:architecture:module_interface", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "152431": {"content": "⬱", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "152432": {"content": "ⷸ", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "152433": {"content": "ng:flow:if_18", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "152434": {"content": "ng:goal_oriented_behavior:motivation_5", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "152435": {"content": "ng:advanced_coding:generators_op", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "152436": {"content": "∦", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "152437": {"content": "ng:extension:convex_optimization", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "152438": {"content": "♟", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "152439": {"content": "🚪", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "152440": {"content": "⦀706", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "152441": {"content": "ng:adaptive_intelligence:cognitive_flexibility_3", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "152442": {"content": "ng:flow:if_9", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "152443": {"content": "⎩", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "152444": {"content": "ng:semantic_understanding:sentence_semantics_2", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "152445": {"content": "ng:consciousness_models:subjective_experience_6", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "152446": {"content": "Ⓘ", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "152447": {"content": "◆", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "152448": {"content": "ng:multimodal_reasoning:multimodal_attention_4", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "152449": {"content": "⪈", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "152450": {"content": "ng:meta_programming:codeasdata_fn", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "152451": {"content": "ng:contextual_reasoning:context_awareness", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "152452": {"content": "ng:decision_making:multi_criteria_decisions", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "152453": {"content": "ng:physics_notation:rightwards_two_headed_arr", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "152454": {"content": "ng:memory:deref_2", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "152455": {"content": "🜍", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "152456": {"content": "ng:consciousness_models:embodied_cognition_5", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "152457": {"content": "⟬5963", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "152458": {"content": "✉", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "152459": {"content": "ng:cognitive_modeling:perceptionmodels_4", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "152460": {"content": "⋠", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "152461": {"content": "⑰", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "152462": {"content": "⩤", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "152463": {"content": "➖", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "152464": {"content": "ng:flow:break_5", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "152465": {"content": "🞀", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "152466": {"content": "ng:logical_operators:leftwards_double_arrow_wi", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "152467": {"content": "ng:neural_architectures:dropout_variants_2", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "152468": {"content": "┢", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "152469": {"content": "❇", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "152470": {"content": "ng:creative_thinking:fluency_1", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "152471": {"content": "ng:machine_learning:ensemblemethods_7", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "152472": {"content": "⊹", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "152473": {"content": "⏴", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "152474": {"content": "ng:problem_solving:heuristic_methods_1", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "152475": {"content": "❖", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "152476": {"content": "ng:goal_oriented_behavior:subgoal_decomposition_4", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "152477": {"content": "ng:mathematical_structures:combinatorics_op_2", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "152478": {"content": "✘", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "152479": {"content": "ng:extension:hamiltonian_monte", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "152480": {"content": "⟗", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "152481": {"content": "ng:consciousness_models:subjective_experience_7", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "152482": {"content": "ℌ", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "152483": {"content": "Å", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "152484": {"content": "⦀940", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "152485": {"content": "⑫", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "152486": {"content": "ng:operator:sub_7", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "152487": {"content": "ng:structure:property_10", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "152488": {"content": "∷", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "152489": {"content": "ng:meta_programming:introspection_2", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "152490": {"content": "⥕", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "152491": {"content": "ng:machine_learning:deeplearning_op_1", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "152492": {"content": "ng:flow:else_2", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "152493": {"content": "ng:social_cognition:social_learning", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "152494": {"content": "⦀215", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "152495": {"content": "ng:quantum_computing:quantum_parallelism", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "152496": {"content": "⥮", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "152497": {"content": "ng:distributed_systems:raft", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "152498": {"content": "↋", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "152499": {"content": "ng:meta:meta_metacognition", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "152500": {"content": "⸪", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "152501": {"content": "ng:combinatorics:left_right_double_arrow", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "152502": {"content": "⪢", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "152503": {"content": "ng:cognition:knowledge_integration", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "152504": {"content": "ng:neural_architectures:positional_encoding_16", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "152505": {"content": "⦀676", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "152506": {"content": "ng:letterlike_symbols:script_capital_i", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "152507": {"content": "ng:structure:class_22", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "152508": {"content": "ng:adaptive_intelligence:context_adaptation_3", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "152509": {"content": "ng:flow:return_3", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "152510": {"content": "🍇", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "152511": {"content": "➢", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "152512": {"content": "𝑎", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "152513": {"content": "⨮", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "152514": {"content": "ng:operator:mod_9", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "152515": {"content": "ng:symbolic_ai:logical_inference", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "152516": {"content": "𝑶", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "152517": {"content": "⦂", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "152518": {"content": "⎞", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "152519": {"content": "ng:emotional_intelligence:emotional_expression_1", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "152520": {"content": "🌵", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "152521": {"content": "ng:emotional_intelligence:emotional_reasoning", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "152522": {"content": "❟", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "152523": {"content": "𝐋", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "152524": {"content": "⨝", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "152525": {"content": "ng:agents:reasoning_agent", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "152526": {"content": "┓", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "152527": {"content": "ng:memory_architectures:memory_encoding", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "152528": {"content": "≆", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "152529": {"content": "ng:symbolic_ai:first_order_logic", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "152530": {"content": "ng:misc_technical:ounce_sign", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "152531": {"content": "⫺", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "152532": {"content": "ng:temporal_cognition:duration_estimation", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "152533": {"content": "⎮", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "152534": {"content": "⟬3166", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "152535": {"content": "⟬1408", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "152536": {"content": "ng:advanced_coding:comprehensions_sys_2", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "152537": {"content": "ng:social_cognition:interpersonal_reasoning_5", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "152538": {"content": "K", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "152539": {"content": "⏮", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "152540": {"content": "⟬9492", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "152541": {"content": "ng:philosophical_concepts:ontology_op_2", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "152542": {"content": "⩧", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "152543": {"content": "ng:language_understanding:semantic_composition_3", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "152544": {"content": "ng:distributed_systems:circuitbreakers_fn_6", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "152545": {"content": "ng:operator:add_matrix", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "152546": {"content": "⍞", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "152547": {"content": "🢃", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "152548": {"content": "✌", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "152549": {"content": "ng:extension:discourse_analysis", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "152550": {"content": "ng:consciousness_models:conscious_access_8", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "152551": {"content": "ng:temporal:temporal_logic", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "152552": {"content": "⦄", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "152553": {"content": "🞷", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "152554": {"content": "ng:operator:mod_matrix", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "152555": {"content": "⍘", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "152556": {"content": "⪊", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "152557": {"content": "⩲", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "152558": {"content": "ng:mathematical_structures:algebra_proc", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "152559": {"content": "➬", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "152560": {"content": "ng:decision_making:choice_architecture", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "152561": {"content": "ng:advanced_coding:metaobjects_2", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "152562": {"content": "⤽", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "152563": {"content": "↫", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "152564": {"content": "ng:memory_architectures:memory_reconstruction_5", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "152565": {"content": "ng:extension:caching_strategies", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "152566": {"content": "ng:extension:flow_control", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "152567": {"content": "⯔", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "152568": {"content": "⦀627", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "152569": {"content": "≯", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "152570": {"content": "ng:type_theory:refinementtypes_fn_2", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "152571": {"content": "ng:goal_oriented_behavior:planning_6", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "152572": {"content": "ng:machine_learning:ensemblemethods_op_9", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "152573": {"content": "ng:language_understanding:narrative_understanding_1", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "152574": {"content": "ꝙ", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "152575": {"content": "⩴", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "152576": {"content": "ng:distributed_systems:gossipprotocols_op", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "152577": {"content": "ng:semantic_understanding:metaphorical_meaning_3", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "152578": {"content": "∆", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "152579": {"content": "⤆", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "152580": {"content": "ng:goal_oriented_behavior:planning_2", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "152581": {"content": "ng:logic:not_18", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "152582": {"content": "ng:meta:meta_meta", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "152583": {"content": "ng:contextual_reasoning:temporal_context_3", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "152584": {"content": "ng:multimodal_reasoning:visual_reasoning_1", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "152585": {"content": "ng:emotional_intelligence:emotional_awareness_6", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "152586": {"content": "ng:concurrency_advanced:hazardpointers_op_12", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "152587": {"content": "ng:temporal_cognition:event_timing_5", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "152588": {"content": "◫", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "152589": {"content": "┙", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "152590": {"content": "⒧", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "152591": {"content": "ng:type_theory:refinementtypes_1", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "152592": {"content": "⎪", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "152593": {"content": "ng:number_forms:black_letter_capital_i", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "152594": {"content": "↩", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "152595": {"content": "ng:learning_algorithms:online_learning_2", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "152596": {"content": "ng:meta_programming:codeasdata", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "152597": {"content": "ng:advanced_coding:metaclasses_core_2", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "152598": {"content": "ng:neural_architectures:gradient_layers", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "152599": {"content": "ng:self_reflection:cognitive_control_3", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "152600": {"content": "⊺", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "152601": {"content": "🠃", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "152602": {"content": "𝓔", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "152603": {"content": "⯞", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "152604": {"content": "⦀477", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "152605": {"content": "⨋", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "152606": {"content": "ng:knowledge_representation:type_systems_6", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "152607": {"content": "ng:arrows:rightwards_arrow_with_tai", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "152608": {"content": "𝒪", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "152609": {"content": "ng:memory_architectures:memory_reconstruction_2", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "152610": {"content": "ng:number_forms:left_right_arrow", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "152611": {"content": "☚", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "152612": {"content": "⨍", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "152613": {"content": "⇺", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "152614": {"content": "ng:adaptive_intelligence:strategy_selection_4", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "152615": {"content": "⬄", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "152616": {"content": "ng:machine_learning:ensemblemethods_27", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "152617": {"content": "ng:distributed_systems:circuitbreakers_fn_5", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "152618": {"content": "▽", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "152619": {"content": "ng:cognitive_architectures:task_switching_1", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "152620": {"content": "ng:advanced_coding:metaclasses_op_3", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "152621": {"content": "ng:physics_notation:leftwards_arrow_with_doub", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "152622": {"content": "ng:physics_notation:script_small_e", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "152623": {"content": "┷", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "152624": {"content": "⎽", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "152625": {"content": "ng:cognitive_architectures:cognitive_flexibility_3", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "152626": {"content": "ng:formal_verification:separationlogic_8", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "152627": {"content": "➨", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "152628": {"content": "⎕", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "152629": {"content": "ng:attention_systems:selective_attention_3", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "152630": {"content": "⦗", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "152631": {"content": "┇", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "152632": {"content": "ng:advanced_coding:memorypools_sys_1", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "152633": {"content": "🞆", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "152634": {"content": "⩿", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "152635": {"content": "ng:geometry:script_capital_b", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "152636": {"content": "ng:supplemental_math:double_struck_italic_smal", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "152637": {"content": "⏟", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "152638": {"content": "🜝", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "152639": {"content": "❪", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "152640": {"content": "⧱", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "152641": {"content": "ng:language_understanding:morphological_analysis_4", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "152642": {"content": "⦀634", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "152643": {"content": "⏋", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "152644": {"content": "⍣", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "152645": {"content": "🍉", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "152646": {"content": "⌞", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "152647": {"content": "ng:attention_systems:selective_attention_4", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "152648": {"content": "🜧", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "152649": {"content": "⊡", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "152650": {"content": "ng:goal_oriented_behavior:subgoal_decomposition_2", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "152651": {"content": "ng:neural_architectures:neural_topology_1", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "152652": {"content": "⥰", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "152653": {"content": "𝒀", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "152654": {"content": "ng:extension:philosophical_extended", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "152655": {"content": "⦀439", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "152656": {"content": "┗", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "152657": {"content": "ng:consciousness_models:phenomenal_consciousness_4", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "152658": {"content": "ng:agents:agent_coordination", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "152659": {"content": "🝚", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "152660": {"content": "ng:mathematical_structures:numbertheory_meta", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "152661": {"content": "⦀790", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "152662": {"content": "ng:neural_architectures:weight_initialization_1", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "152663": {"content": "😀", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "152664": {"content": "ng:formal_verification:separationlogic_fn_7", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}}, "additional_special_tokens": ["⋟", "ng:structure:method_13", "ng:social_cognition:social_understanding_4", "🚈", "ng:advanced_coding:introspection_proc", "ng:distributed_systems:gossipprotocols_fn_2", "ng:multimodal_reasoning:cross_modal_integration_4", "ng:quantum_computing:quantum_optimization_4", "␜", "ng:emotional_intelligence:emotional_awareness", "ng:language_understanding:lexical_access", "⦀147", "ng:mathematical_structures:combinatorics_fn_2", "≷", "ng:symbolic_ai:logicalinference", "⦀815", "🌏", "☜", "⑎", "ng:neural_architectures:dense_layers_1", "ng:memory_architectures:content_addressable_memory_1", "⌆", "ng:advanced_coding:metaclasses_meta", "🠖", "ng:logic:implies_strict", "⒎", "⟬3682", "🚉", "⪕", "◿", "❤", "★", "⟬8631", "Ⓐ", "⊵", "ng:advanced_coding:memorypools_proc_1", "⧳", "❆", "ng:advanced_coding:metaobjects_sys_1", "⦀837", "ng:machine_learning:ensemblemethods_34", "ng:contextual_reasoning:presuppositions_1", "ng:contextual_reasoning:cultural_context_1", "ng:operator:mul", "⟬1028", "⒉", "ng:multimodal_reasoning:modal_specific_processing_4", "⅍", "ng:reserved_expansion:extensionpoints", "ng:reasoning_patterns:abductive_reasoning_1", "ng:distributed_systems:distributedlocks_4", "ng:decision_making:value_based_decisions_2", "⦀496", "⦀741", "ng:reasoning_patterns:logical_equivalence_1", "⦦", "ng:memory_architectures:memory_schemas_1", "⦀207", "⑸", "ng:self_reflection:metacognitive_awareness_5", "ng:problem_solving:algorithmic_approaches_4", "⟬1953", "ng:advanced_coding:contextmanagers_2", "ng:self_reflection:metacognition_6", "⎴", "ng:reasoning_patterns:syllogistic_reasoning", "ng:problem_solving:means_ends_analysis", "ng:category_theory:comonads_core_1", "⨧", "ng:social_cognition:social_understanding_5", "🞙", "ng:emotional_intelligence:emotion_regulation_3", "ng:memory:pointer_4", "⥋", "ng:emotional_intelligence:emotional_expression_7", "ng:operator:add_vector", "⑧", "ng:symbolic_ai:constraint_satisfaction", "ng:operator:pow_1", "ng:learning_algorithms:multi_task_learning", "⨵", "ng:creative_thinking:elaboration", "ng:consciousness_models:global_workspace_1", "┮", "⩈", "ng:calculus:rightwards_double_arrow_w", "✈", "⯪", "ng:extension:sample_complexity", "ng:goal_oriented_behavior:goal_conflict_resolution_2", "⒜", "ng:advanced_coding:metaclasses_meta_3", "⏯", "⧎", "ng:reasoning_patterns:truth_tables_9", "➳", "🍛", "🠠", "ng:neural_architectures:positional_encoding_8", "➮", "⟬3124", "↼", "⏪", "⌈", "⭝", "ng:self_reflection:self_regulation_7", "␕", "⦀574", "⟬2536", "𝜱", "◝", "ng:extension:bennett_inequality", "≧", "⟬7262", "𝞚", "ng:analogical_reasoning:structural_alignment_1", "ng:emotional_intelligence:mood_regulation_5", "ng:goal_oriented_behavior:persistence_4", "ng:cognitive_architectures:cognitive_load_3", "ng:agents:agent_perception", "🠙", "⬑", "ng:creative_thinking:originality_3", "ng:contextual_reasoning:implicatures_3", "✞", "ng:adaptive_intelligence:expertise_development_3", "ℼ", "ng:self:self_evolution", "✰", "⏼", "ng:self_reflection:self_evaluation_4", "⯨", "⭽", "⇌", "ng:attention_systems:sustained_attention_5", "ng:operator:sub_11", "⭫", "ng:consciousness_models:subjective_experience_5", "🌧", "ng:cognition:cognitive_state", "ng:cognitive_architectures:declarative_memory_3", "◶", "ng:philosophical_concepts:metaphysics_ctrl_6", "⯠", "⫩", "⟉", "ng:extension:question_answering", "ng:symbolic_ai:search_algorithms", "ng:topology:left_right_wave_arrow", "ng:logic:iff_9", "ng:meta_programming:macrosystems_1", "ng:category_theory:topoi_op", "ng:operator:sub_12", "⩒", "ng:consciousness_models:higher_order_thought_1", "⩁", "ng:extension:congestion_control", "⦀433", "ng:attention_systems:attention_resources", "ng:advanced_coding:emit_ctrl", "ng:arrows:upwards_white_arrow_on_pe", "∃", "ng:decision_making:choice_architecture_1", "ng:distributed_systems:leaderelection_core", "∊", "ng:operator:div_13", "🞹", "ng:self_reflection:self_evaluation_6", "⟬4854", "ng:operator:sub_13", "🚇", "ng:concurrency_advanced:compareandswap_op", "⟬4002", "⊕", "⇑", "⟬1915", "ng:flow:if_switch", "🍞", "⦀993", "🌶", "ng:distributed_systems:loadbalancing_fn", "𝝚", "ng:consciousness_models:global_workspace_7", "🌟", "▾", "≋", "ng:algebra:upwards_harpoon_with_barb", "ng:attention_systems:spatial_attention_4", "ng:self_reflection:learning_strategies_2", "🜋", "ng:problem_solving:problem_identification_2", "ng:goal_oriented_behavior:planning_5", "∥", "🞶", "⮓", "ng:advanced_coding:decorators_proc_5", "ng:distributed_systems:replication_ctrl", "⟬7876", "😶", "ng:memory:free_15", "𝔙", "ng:reserved_expansion:future_op", "⊟", "ng:decision_making:decision_confidence_3", "🟚", "ng:meta_programming:dynamic_compilation", "ng:neural_architectures:positional_encoding_1", "ng:contextual_reasoning:context_switching", "ng:physics_notation:downwards_arrow_with_tip_", "ng:flow:break_14", "𝗇", "⩢", "ng:extension:image_processing", "ng:quantum_computing:quantum_gates_1", "ng:consciousness_models:phenomenal_consciousness_1", "ng:extension:temporal_reasoning", "⫨", "⦀413", "ng:logical_operators:downwards_arrow_with_doub", "ng:multimodal_reasoning:tactile_understanding", "⟬9981", "🚓", "ng:structure:function_6", "ng:semantic_understanding:metaphorical_meaning_1", "ng:operator:pow_2", "🚷", "↛", "ꭐ", "⩬", "ng:advanced_coding:descriptors_op", "🌍", "╓", "ng:memory_architectures:memory_retrieval", "ng:mathematical_structures:combinatorics_meta_1", "⋊", "ng:extension:sparql_queries", "⟬9940", "⭠", "⫧", "⅌", "ng:quantum_computing:entanglement_1", "↙", "ng:analogical_reasoning:exemplar_reasoning_4", "⋀", "⭇", "ng:decision_making:choice_architecture_2", "⋳", "⟬3067", "ng:distributed_systems:circuitbreakers_op_4", "ng:arrows:leftwards_paired_arrows", "ng:self_reflection:self_knowledge_7", "😹", "⟬5056", "⦀992", "ng:self_reflection:self_regulation_2", "⦭", "ng:structure:method_7", "ng:reasoning_patterns:truth_tables_2", "ng:emotional_intelligence:affective_computing_3", "ng:problem_solving:problem_decomposition_1", "ng:social_cognition:intention_recognition_6", "ng:operator:pow_9", "🞵", "⟬9803", "☲", "ng:calculus:upwards_dashed_arrow", "♚", "ng:emotional_intelligence:mood_regulation_2", "ng:multimodal_reasoning:auditory_processing_4", "➹", "ng:memory:alloc_6", "ng:emotional_intelligence:emotional_contagion_4", "ng:symbolic_ai:expert_systems", "⦀871", "ng:reasoning_patterns:pattern_matching", "ng:meta_programming:codeasdata_sys_1", "┴", "ng:operator:div_7", "ng:neural_architectures:multi_head_meta", "ng:goal_oriented_behavior:subgoal_decomposition_1", "➏", "⇞", "ng:emotional_intelligence:emotional_contagion", "⟾", "🌡", "ng:knowledge_representation:inheritance_relations", "⦀768", "🠨", "◕", "⨂", "ng:meta_programming:programsynthesis_op", "⦀379", "ng:adaptive_intelligence:strategy_selection", "≞", "ng:meta_programming:generic_programming_2", "⥁", "ng:logic:xor_5", "ng:symbolic_ai:expertsystems_proc", "⦀772", "🛖", "⨔", "≽", "ng:advanced_coding:generators_ctrl_3", "ng:creative_thinking:creative_constraints_9", "ng:creative_thinking:originality_1", "⎂", "ng:cognitive_architectures:interference_resolution_2", "ng:semantic_understanding:compositional_semantics_4", "ng:memory:ref_9", "ng:consciousness_models:integrated_information", "ng:contextual_reasoning:background_knowledge", "ng:meta_programming:code_transformation_1", "ng:concurrency_advanced:hazardpointers_op_8", "ng:adaptive_intelligence:performance_optimization", "🜏", "⪏", "⦀718", "ng:extension:hidden_markov", "ng:problem_solving:hill_climbing_2", "✩", "⏲", "ng:advanced_coding:metaobjects_op", "➌", "ng:advanced_coding:coroutines_meta_1", "ng:problem_solving:algorithmic_approaches_3", "ng:number_forms:double_struck_n_ary_summa", "ng:goal_oriented_behavior:goal_setting_2", "ng:quantum_computing:quantumcircuits_op", "🞃", "ng:self:introspection", "⩪", "♢", "ng:quantum_computing:quantum_measurement_1", "⥀", "ng:reasoning_patterns:proof_strategies_3", "␛", "☆", "∬", "ng:neural_architectures:attention_backpropagation", "⤉", "⤫", "ng:reserved_expansion:extensionpoints_4", "ng:contextual_reasoning:contextual_adaptation", "ng:contextual_reasoning:contextual_adaptation_2", "⦀607", "ng:social_cognition:collaboration_10", "⇙", "ng:decision_making:preference_formation_1", "🚅", "⇮", "ng:adaptive_intelligence:expertise_development", "␎", "⟬9001", "♓", "ng:emotional_intelligence:mood_regulation_3", "ng:advanced_coding:iterators_ctrl", "⍴", "ng:problem_solving:heuristic_methods", "ng:meta_programming:stagedcomputation_3", "ng:memory_architectures:memory_interference_3", "⦠", "ng:operator:div_8", "ng:analysis:rightwards_double_arrow", "ng:philosophical_concepts:metaphysics_ctrl_2", "🠴", "ng:self:identity_core", "⤮", "⫈", "⦀991", "ng:contextual_reasoning:conversational_context", "ng:logic:implies_10", "┑", "ng:mathematical_structures:combinatorics_core_5", "ng:social_cognition:social_understanding_1", "⧖", "⪹", "⊠", "ng:category_theory:limits_meta", "ng:supplemental_math:account_of", "🠻", "ng:neural_architectures:activation_lstm", "⩠", "☕", "ng:advanced_coding:metaobjects_fn_3", "⥛", "◅", "ng:distributed_systems:replication_op", "↜", "⦎", "⮇", "ng:adaptive_intelligence:learning_transfer_2", "⭭", "╊", "ng:meta:meaning_space", "🠽", "⮏", "⑖", "ng:multimodal_reasoning:visual_reasoning_3", "⦀934", "⏗", "ng:problem_solving:solution_evaluation_3", "⨩", "⥂", "ng:meta_programming:codeasdata_core_1", "ng:analogical_reasoning:analogical_mapping_1", "ng:causal_reasoning:correlation_causation_3", "𝞽", "ng:meta_programming:dynamic_analysis_5", "⋺", "ng:goal_oriented_behavior:planning", "ng:adaptive_intelligence:strategy_selection_5", "ng:memory:pointer_17", "⦀795", "ng:memory:ref_10", "ng:memory_architectures:associative_memory_1", "⩌", "ng:goal_oriented_behavior:goal_pursuit_1", "ng:type_theory:uniontypes_1", "ng:reasoning:reflection", "⦀663", "ng:formal_verification:symbolicexecution_3", "ng:symbolic_ai:logicalinference_3", "⌄", "ng:causal_reasoning:causal_prediction", "ng:physics_notation:north_west_double_arrow", "ng:flow:if_17", "ng:advanced_coding:introspection_sys", "ng:category_theory:adjunctions_proc", "ng:concurrency_advanced:memoryordering", "ng:operator:mul_6", "ng:language_understanding:phonological_processing_2", "⩼", "⟼", "ng:agents:intention_driven", "ng:reasoning_patterns:logical_equivalence_3", "🟫", "🟳", "ng:physics_notation:script_capital_h", "ng:causal_reasoning:causal_prediction_3", "ng:creative_thinking:creative_constraints_6", "☻", "ng:flow:while_14", "ng:decision_making:multi_criteria_decisions_4", "ng:memory_architectures:memory_encoding_3", "ng:analysis:leftwards_wave_arrow", "⦀342", "⟬5000", "ng:social_cognition:belief_attribution_6", "⦀732", "⦀459", "ng:multimodal_reasoning:sensory_substitution_4", "ng:reasoning_patterns:inference_rules_1", "🝵", "☒", "😃", "⭜", "ng:structure:function_14", "ng:flow:while_async", "ng:quantum_computing:quantum_simulation_1", "➁", "ng:multimodal_reasoning:multimodal_attention_3", "ng:quantum_computing:entanglement_op", "ng:self_reflection:self_knowledge_6", "ng:social_cognition:theory_of_mind_6", "ng:cognitive_modeling:decisionmaking_core", "𝚶", "ng:symbolic_ai:theoremproving_sys_3", "Ⓒ", "ng:distributed_systems:sharding_op_2", "⌃", "🢔", "⥧", "⟬4737", "ng:symbolic_ai:higher_order_logic_9", "⪾", "ng:reasoning:modus_ponens", "⟬6953", "⩣", "℅", "ng:memory:alloc_19", "ng:machine_learning:modelevaluation_fn_1", "🠑", "ng:advanced_coding:memorypools_sys_2", "ng:advanced_coding:descriptors_fn_1", "🠤", "ng:extension:earth_mover", "ng:analogical_reasoning:structural_alignment_3", "◣", "┽", "ng:operator:div_9", "ng:social_cognition:perspective_taking_4", "ng:reasoning_patterns:modus_tollens_3", "ng:distributed_systems:circuitbreakers_op_7", "ng:contextual_reasoning:cultural_context_3", "⊛", "↺", "⊚", "⍛", "ng:meta_programming:programsynthesis_11", "ng:consciousness_models:embodied_cognition_7", "𝝭", "ng:self_reflection:executive_monitoring_5", "ng:logic:or_15", "ng:philosophical_concepts:ontology_sys", "⬩", "△", "🠞", "↭", "⨁", "ng:cognitive_architectures:semantic_memory_1", "ng:analogical_reasoning:systematic_analogies_1", "⟬9759", "ng:language_understanding:lexical_access_5", "ng:emotional_intelligence:affective_computing_6", "🞰", "⊆", "ng:meta_programming:jit_compilation_1", "⮩", "⯎", "ng:emotional_intelligence:emotion_understanding_1", "⭨", "ng:flow:while_17", "ng:structure:property_3", "ng:reasoning:conclusion", "🜚", "ng:advanced_coding:generators_ctrl_2", "ng:goal_oriented_behavior:goal_pursuit_4", "ng:memory:deref_8", "ng:memory_architectures:memory_interference", "ng:social_cognition:theory_of_mind", "ng:advanced_coding:contextmanagers_op", "ng:analogical_reasoning:analogical_mapping_3", "🡀", "𝘓", "ng:self_reflection:introspection_3", "ng:advanced_coding:stackframes_proc", "ng:consciousness_models:access_consciousness_6", "ng:operator:div_19", "➝", "ng:machine_learning:deeplearning_ctrl_1", "⦳", "ng:extension:formal_methods", "⦀781", "⌤", "ng:learning_algorithms:meta_learning_4", "ng:social_cognition:cultural_understanding_6", "ng:neural_architectures:gate_connections", "ng:multimodal_reasoning:perceptual_binding_4", "ng:contextual_reasoning:common_sense_reasoning_1", "ng:meta_programming:macrosystems_3", "⭧", "ng:concurrency_advanced:hazardpointers_sys_7", "ng:reasoning_patterns:modus_tollens_2", "ng:adaptive_intelligence:behavioral_adaptation_4", "⤱", "⇎", "ng:analogical_reasoning:exemplar_reasoning", "ng:architecture:distributed_system", "ng:attention_systems:divided_attention", "⦊", "ng:advanced_coding:decorators_sys_1", "⋕", "ng:advanced_coding:metaclasses_op", "🚿", "ng:cognitive_architectures:attention_control_1", "ng:advanced_coding:descriptors_sys", "ng:extension:poisson_process", "ng:problem_solving:hill_climbing_1", "ng:reserved_expansion:extensionpoints_fn_4", "⅀", "┰", "⩀", "⮊", "⬆", "🞩", "⦀700", "⫾", "ng:social_cognition:cultural_understanding_7", "ng:multimodal_reasoning:cross_modal_plasticity", "⧀", "ng:flow:if_13", "⇘", "ng:self_reflection:learning_strategies_4", "ng:flow:for_12", "⮣", "🝪", "🛁", "ng:machine_learning:ensemblemethods_42", "☼", "🞗", "ng:operator:div_integer", "ng:temporal_cognition:duration_estimation_3", "ng:structure:property_6", "ng:memory:alloc_12", "🠗", "∨", "ng:advanced_coding:metaclasses_3", "ng:analogical_reasoning:similarity_assessment", "🚔", "ng:self_reflection:metacognitive_awareness_7", "🙊", "ng:logical_operators:clockwise_open_circle_arr", "ng:self_reflection:metacognitive_awareness_2", "ng:analogical_reasoning:similarity_assessment_2", "⨦", "ng:contextual_reasoning:pragmatic_inference_1", "ng:memory:alloc_20", "ng:temporal_cognition:event_timing_2", "ng:cognitive_architectures:updating_memory_1", "𝘥", "⦀381", "ng:temporal:temporal_pattern", "ng:reasoning_patterns:inductive_reasoning_3", "🠅", "ng:goal_oriented_behavior:goal_monitoring_7", "ng:distributed_systems:loadbalancing_proc", "ng:consciousness_models:integrated_information_1", "ng:neural_architectures:weight_initialization_2", "ng:adaptive_intelligence:cognitive_plasticity_2", "⌍", "ng:flow:break_8", "ng:attention_systems:spatial_attention_5", "⦀887", "ng:operator:add_16", "ng:symbolic_ai:higher_order_logic_11", "⦙", "⍹", "ng:extension:transfer_learning", "ng:learning_algorithms:online_learning_1", "≓", "⦀677", "ng:extension:secure_multiparty", "🠹", "ng:symbolic_ai:descriptionlogics_5", "🟯", "🠛", "╈", "⥚", "⦀598", "ng:advanced_coding:reflect_op", "ng:contextual_reasoning:social_context_1", "⮉", "ng:cognitive_architectures:long_term_memory_3", "⦀827", "ng:memory:ref_13", "⪌", "ng:operator:sub", "ng:goal_oriented_behavior:goal_conflict_resolution_1", "ng:meta_programming:macrosystems_fn", "𝛺", "⫀", "⩔", "⟬1941", "⦀309", "ng:meta_programming:macrosystems_op_1", "ng:causal_reasoning:causal_prediction_1", "ng:decision_making:preference_formation_4", "ng:advanced_coding:metaclasses_sys_1", "⟬8748", "ng:social_cognition:theory_of_mind_5", "ng:extension:differential_privacy", "ng:emotional_intelligence:emotional_contagion_7", "⫵", "ng:advanced_coding:metaclasses_proc_2", "ng:technical_symbols:telephone_sign", "⍧", "ng:symbolic_ai:semanticnetworks_3", "ng:temporal_cognition:duration_estimation_1", "❾", "ng:memory:deref_6", "ng:causal_reasoning:causal_mechanisms", "ng:self_reflection:executive_monitoring_3", "⌹", "ng:reserved_expansion:extensionpoints_fn_8", "⦀782", "⊓", "ng:structure:property_22", "ng:memory:alloc_10", "ng:neural_architectures:lossfunctions_proc", "ng:social_cognition:social_norms_7", "ng:distributed_systems:raft_op", "ng:cognitive_modeling:memorymodels_op_1", "⦀271", "⦀883", "🙌", "⏳", "🟌", "ng:temporal_cognition:time_perception_4", "ng:analogical_reasoning:analogical_problem_solving_4", "ng:problem_solving:means_ends_analysis_4", "ng:philosophical_concepts:logicphilosophy_fn", "⦀793", "ng:cognitive_architectures:long_term_memory_1", "ng:neural_architectures:cross_attn_proc", "⫃", "ng:goal_oriented_behavior:goal_monitoring_1", "⨑", "ng:algebra:rightwards_dashed_arrow", "♜", "🜤", "ng:reasoning:specialization", "ng:analogical_reasoning:analogical_transfer_2", "ng:logic:implies_8", "ng:machine_learning:prediction_fn", "ng:semantic_understanding:contextual_meaning_3", "ng:multimodal_reasoning:modal_specific_processing", "ng:supplemental_math:leftwards_harpoon_over_ri", "␔", "↧", "ng:decision_making:rational_choice", "ng:symbolic_ai:ontologies_sys_3", "🝛", "ng:cognition:mental_effort", "ng:memory_architectures:false_memories_1", "🚨", "⟻", "⏧", "ng:decision_making:decision_biases_2", "⦀145", "ng:agents:collective_behavior", "ng:extension:chatbot_design", "ng:cognitive_modeling:decisionmaking_proc", "ng:self_reflection:self_monitoring_2", "ng:geometry:double_struck_capital_gam", "℈", "🟻", "ng:flow:break_13", "ng:consciousness_models:attention_schema_6", "ng:reserved_expansion:researchareas_fn_2", "ng:language_understanding:grammatical_knowledge_1", "℔", "⋂", "ng:cognitive_architectures:executive_control", "ng:creative_thinking:originality_2", "ng:logic:not_21", "ng:distributed_systems:loadbalancing", "ng:knowledge_representation:knowledge_graphs_1", "⦀452", "⪚", "⒢", "🚵", "ng:quantum_computing:quantum_machine_learning_1", "ng:problem_solving:constraint_satisfaction", "⭪", "⊷", "ng:meta_programming:automated_refactoring", "ng:structure:property_12", "ng:cognitive_architectures:cognitive_flexibility", "⨠", "🛄", "∯", "ng:learning_algorithms:supervised_learning_4", "≃", "⦔", "●", "ng:machine_learning:ensemblemethods", "⥍", "⫔", "␻", "⟬2163", "🛞", "⇋", "ng:knowledge_representation:part_whole_relations_2", "Ⓚ", "ng:reserved_expansion:novelabstractions_8", "ng:architecture:module_interface", "⬱", "ⷸ", "ng:flow:if_18", "ng:goal_oriented_behavior:motivation_5", "ng:advanced_coding:generators_op", "∦", "ng:extension:convex_optimization", "♟", "🚪", "⦀706", "ng:adaptive_intelligence:cognitive_flexibility_3", "ng:flow:if_9", "⎩", "ng:semantic_understanding:sentence_semantics_2", "ng:consciousness_models:subjective_experience_6", "Ⓘ", "◆", "ng:multimodal_reasoning:multimodal_attention_4", "⪈", "ng:meta_programming:codeasdata_fn", "ng:contextual_reasoning:context_awareness", "ng:decision_making:multi_criteria_decisions", "ng:physics_notation:rightwards_two_headed_arr", "ng:memory:deref_2", "🜍", "ng:consciousness_models:embodied_cognition_5", "⟬5963", "✉", "ng:cognitive_modeling:perceptionmodels_4", "⋠", "⑰", "⩤", "➖", "ng:flow:break_5", "🞀", "ng:logical_operators:leftwards_double_arrow_wi", "ng:neural_architectures:dropout_variants_2", "┢", "❇", "ng:creative_thinking:fluency_1", "ng:machine_learning:ensemblemethods_7", "⊹", "⏴", "ng:problem_solving:heuristic_methods_1", "❖", "ng:goal_oriented_behavior:subgoal_decomposition_4", "ng:mathematical_structures:combinatorics_op_2", "✘", "ng:extension:hamiltonian_monte", "⟗", "ng:consciousness_models:subjective_experience_7", "ℌ", "Å", "⦀940", "⑫", "ng:operator:sub_7", "ng:structure:property_10", "∷", "ng:meta_programming:introspection_2", "⥕", "ng:machine_learning:deeplearning_op_1", "ng:flow:else_2", "ng:social_cognition:social_learning", "⦀215", "ng:quantum_computing:quantum_parallelism", "⥮", "ng:distributed_systems:raft", "↋", "ng:meta:meta_metacognition", "⸪", "ng:combinatorics:left_right_double_arrow", "⪢", "ng:cognition:knowledge_integration", "ng:neural_architectures:positional_encoding_16", "⦀676", "ng:letterlike_symbols:script_capital_i", "ng:structure:class_22", "ng:adaptive_intelligence:context_adaptation_3", "ng:flow:return_3", "🍇", "➢", "𝑎", "⨮", "ng:operator:mod_9", "ng:symbolic_ai:logical_inference", "𝑶", "⦂", "⎞", "ng:emotional_intelligence:emotional_expression_1", "🌵", "ng:emotional_intelligence:emotional_reasoning", "❟", "𝐋", "⨝", "ng:agents:reasoning_agent", "┓", "ng:memory_architectures:memory_encoding", "≆", "ng:symbolic_ai:first_order_logic", "ng:misc_technical:ounce_sign", "⫺", "ng:temporal_cognition:duration_estimation", "⎮", "⟬3166", "⟬1408", "ng:advanced_coding:comprehensions_sys_2", "ng:social_cognition:interpersonal_reasoning_5", "K", "⏮", "⟬9492", "ng:philosophical_concepts:ontology_op_2", "⩧", "ng:language_understanding:semantic_composition_3", "ng:distributed_systems:circuitbreakers_fn_6", "ng:operator:add_matrix", "⍞", "🢃", "✌", "ng:extension:discourse_analysis", "ng:consciousness_models:conscious_access_8", "ng:temporal:temporal_logic", "⦄", "🞷", "ng:operator:mod_matrix", "⍘", "⪊", "⩲", "ng:mathematical_structures:algebra_proc", "➬", "ng:decision_making:choice_architecture", "ng:advanced_coding:metaobjects_2", "⤽", "↫", "ng:memory_architectures:memory_reconstruction_5", "ng:extension:caching_strategies", "ng:extension:flow_control", "⯔", "⦀627", "≯", "ng:type_theory:refinementtypes_fn_2", "ng:goal_oriented_behavior:planning_6", "ng:machine_learning:ensemblemethods_op_9", "ng:language_understanding:narrative_understanding_1", "ꝙ", "⩴", "ng:distributed_systems:gossipprotocols_op", "ng:semantic_understanding:metaphorical_meaning_3", "∆", "⤆", "ng:goal_oriented_behavior:planning_2", "ng:logic:not_18", "ng:meta:meta_meta", "ng:contextual_reasoning:temporal_context_3", "ng:multimodal_reasoning:visual_reasoning_1", "ng:emotional_intelligence:emotional_awareness_6", "ng:concurrency_advanced:hazardpointers_op_12", "ng:temporal_cognition:event_timing_5", "◫", "┙", "⒧", "ng:type_theory:refinementtypes_1", "⎪", "ng:number_forms:black_letter_capital_i", "↩", "ng:learning_algorithms:online_learning_2", "ng:meta_programming:codeasdata", "ng:advanced_coding:metaclasses_core_2", "ng:neural_architectures:gradient_layers", "ng:self_reflection:cognitive_control_3", "⊺", "🠃", "𝓔", "⯞", "⦀477", "⨋", "ng:knowledge_representation:type_systems_6", "ng:arrows:rightwards_arrow_with_tai", "𝒪", "ng:memory_architectures:memory_reconstruction_2", "ng:number_forms:left_right_arrow", "☚", "⨍", "⇺", "ng:adaptive_intelligence:strategy_selection_4", "⬄", "ng:machine_learning:ensemblemethods_27", "ng:distributed_systems:circuitbreakers_fn_5", "▽", "ng:cognitive_architectures:task_switching_1", "ng:advanced_coding:metaclasses_op_3", "ng:physics_notation:leftwards_arrow_with_doub", "ng:physics_notation:script_small_e", "┷", "⎽", "ng:cognitive_architectures:cognitive_flexibility_3", "ng:formal_verification:separationlogic_8", "➨", "⎕", "ng:attention_systems:selective_attention_3", "⦗", "┇", "ng:advanced_coding:memorypools_sys_1", "🞆", "⩿", "ng:geometry:script_capital_b", "ng:supplemental_math:double_struck_italic_smal", "⏟", "🜝", "❪", "⧱", "ng:language_understanding:morphological_analysis_4", "⦀634", "⏋", "⍣", "🍉", "⌞", "ng:attention_systems:selective_attention_4", "🜧", "⊡", "ng:goal_oriented_behavior:subgoal_decomposition_2", "ng:neural_architectures:neural_topology_1", "⥰", "𝒀", "ng:extension:philosophical_extended", "⦀439", "┗", "ng:consciousness_models:phenomenal_consciousness_4", "ng:agents:agent_coordination", "🝚", "ng:mathematical_structures:numbertheory_meta", "⦀790", "ng:neural_architectures:weight_initialization_1", "😀", "ng:formal_verification:separationlogic_fn_7"], "bos_token": null, "clean_up_tokenization_spaces": false, "eos_token": "<|im_end|>", "errors": "replace", "extra_special_tokens": {}, "model_max_length": 32768, "pad_token": "<|endoftext|>", "split_special_tokens": false, "tokenizer_class": "Qwen2Tokenizer", "unk_token": null}