{"</tool_call>": 151658, "<tool_call>": 151657, "<|box_end|>": 151649, "<|box_start|>": 151648, "<|endoftext|>": 151643, "<|file_sep|>": 151664, "<|fim_middle|>": 151660, "<|fim_pad|>": 151662, "<|fim_prefix|>": 151659, "<|fim_suffix|>": 151661, "<|im_end|>": 151645, "<|im_start|>": 151644, "<|image_pad|>": 151655, "<|object_ref_end|>": 151647, "<|object_ref_start|>": 151646, "<|quad_end|>": 151651, "<|quad_start|>": 151650, "<|repo_name|>": 151663, "<|video_pad|>": 151656, "<|vision_end|>": 151653, "<|vision_pad|>": 151654, "<|vision_start|>": 151652, "ng:adaptive_intelligence:behavioral_adaptation_4": 152218, "ng:adaptive_intelligence:cognitive_flexibility_3": 152441, "ng:adaptive_intelligence:cognitive_plasticity_2": 152283, "ng:adaptive_intelligence:context_adaptation_3": 152508, "ng:adaptive_intelligence:expertise_development": 152015, "ng:adaptive_intelligence:expertise_development_3": 151790, "ng:adaptive_intelligence:learning_transfer_2": 152055, "ng:adaptive_intelligence:performance_optimization": 151978, "ng:adaptive_intelligence:strategy_selection": 151957, "ng:adaptive_intelligence:strategy_selection_4": 152614, "ng:adaptive_intelligence:strategy_selection_5": 152075, "ng:advanced_coding:comprehensions_sys_2": 152536, "ng:advanced_coding:contextmanagers_2": 151727, "ng:advanced_coding:contextmanagers_op": 152195, "ng:advanced_coding:coroutines_meta_1": 151988, "ng:advanced_coding:decorators_proc_5": 151860, "ng:advanced_coding:decorators_sys_1": 152225, "ng:advanced_coding:descriptors_fn_1": 152148, "ng:advanced_coding:descriptors_op": 151894, "ng:advanced_coding:descriptors_sys": 152230, "ng:advanced_coding:emit_ctrl": 151824, "ng:advanced_coding:generators_ctrl_2": 152190, "ng:advanced_coding:generators_ctrl_3": 151967, "ng:advanced_coding:generators_op": 152435, "ng:advanced_coding:introspection_proc": 151669, "ng:advanced_coding:introspection_sys": 152091, "ng:advanced_coding:iterators_ctrl": 152020, "ng:advanced_coding:memorypools_proc_1": 151700, "ng:advanced_coding:memorypools_sys_1": 152632, "ng:advanced_coding:memorypools_sys_2": 152147, "ng:advanced_coding:metaclasses_3": 152261, "ng:advanced_coding:metaclasses_core_2": 152597, "ng:advanced_coding:metaclasses_meta": 151687, "ng:advanced_coding:metaclasses_meta_3": 151756, "ng:advanced_coding:metaclasses_op": 152227, "ng:advanced_coding:metaclasses_op_3": 152620, "ng:advanced_coding:metaclasses_proc_2": 152328, "ng:advanced_coding:metaclasses_sys_1": 152322, "ng:advanced_coding:metaobjects_2": 152561, "ng:advanced_coding:metaobjects_fn_3": 152048, "ng:advanced_coding:metaobjects_op": 151986, "ng:advanced_coding:metaobjects_sys_1": 151703, "ng:advanced_coding:reflect_op": 152304, "ng:advanced_coding:stackframes_proc": 152200, "ng:agents:agent_coordination": 152658, "ng:agents:agent_perception": 151784, "ng:agents:collective_behavior": 152384, "ng:agents:intention_driven": 152098, "ng:agents:reasoning_agent": 152525, "ng:algebra:rightwards_dashed_arrow": 152362, "ng:algebra:upwards_harpoon_with_barb": 151851, "ng:analogical_reasoning:analogical_mapping_1": 152069, "ng:analogical_reasoning:analogical_mapping_3": 152196, "ng:analogical_reasoning:analogical_problem_solving_4": 152353, "ng:analogical_reasoning:analogical_transfer_2": 152366, "ng:analogical_reasoning:exemplar_reasoning": 152221, "ng:analogical_reasoning:exemplar_reasoning_4": 151907, "ng:analogical_reasoning:similarity_assessment": 152262, "ng:analogical_reasoning:similarity_assessment_2": 152268, "ng:analogical_reasoning:structural_alignment_1": 151780, "ng:analogical_reasoning:structural_alignment_3": 152151, "ng:analogical_reasoning:systematic_analogies_1": 152175, "ng:analysis:leftwards_wave_arrow": 152109, "ng:analysis:rightwards_double_arrow": 152027, "ng:architecture:distributed_system": 152222, "ng:architecture:module_interface": 152430, "ng:arrows:leftwards_paired_arrows": 151914, "ng:arrows:rightwards_arrow_with_tai": 152607, "ng:arrows:upwards_white_arrow_on_pe": 151825, "ng:attention_systems:attention_resources": 151823, "ng:attention_systems:divided_attention": 152223, "ng:attention_systems:selective_attention_3": 152629, "ng:attention_systems:selective_attention_4": 152647, "ng:attention_systems:spatial_attention_4": 151852, "ng:attention_systems:spatial_attention_5": 152286, "ng:attention_systems:sustained_attention_5": 151799, "ng:calculus:rightwards_double_arrow_w": 151750, "ng:calculus:upwards_dashed_arrow": 151930, "ng:category_theory:adjunctions_proc": 152092, "ng:category_theory:comonads_core_1": 151732, "ng:category_theory:limits_meta": 152042, "ng:category_theory:topoi_op": 151816, "ng:causal_reasoning:causal_mechanisms": 152335, "ng:causal_reasoning:causal_prediction": 152088, "ng:causal_reasoning:causal_prediction_1": 152320, "ng:causal_reasoning:causal_prediction_3": 152103, "ng:causal_reasoning:correlation_causation_3": 152070, "ng:cognition:cognitive_state": 151804, "ng:cognition:knowledge_integration": 152503, "ng:cognition:mental_effort": 152377, "ng:cognitive_architectures:attention_control_1": 152229, "ng:cognitive_architectures:cognitive_flexibility": 152412, "ng:cognitive_architectures:cognitive_flexibility_3": 152625, "ng:cognitive_architectures:cognitive_load_3": 151783, "ng:cognitive_architectures:declarative_memory_3": 151805, "ng:cognitive_architectures:executive_control": 152397, "ng:cognitive_architectures:interference_resolution_2": 151971, "ng:cognitive_architectures:long_term_memory_1": 152357, "ng:cognitive_architectures:long_term_memory_3": 152307, "ng:cognitive_architectures:semantic_memory_1": 152174, "ng:cognitive_architectures:task_switching_1": 152619, "ng:cognitive_architectures:updating_memory_1": 152273, "ng:cognitive_modeling:decisionmaking_core": 152129, "ng:cognitive_modeling:decisionmaking_proc": 152386, "ng:cognitive_modeling:memorymodels_op_1": 152346, "ng:cognitive_modeling:perceptionmodels_4": 152459, "ng:combinatorics:left_right_double_arrow": 152501, "ng:concurrency_advanced:compareandswap_op": 151836, "ng:concurrency_advanced:hazardpointers_op_12": 152586, "ng:concurrency_advanced:hazardpointers_op_8": 151977, "ng:concurrency_advanced:hazardpointers_sys_7": 152216, "ng:concurrency_advanced:memoryordering": 152093, "ng:consciousness_models:access_consciousness_6": 152201, "ng:consciousness_models:attention_schema_6": 152392, "ng:consciousness_models:conscious_access_8": 152550, "ng:consciousness_models:embodied_cognition_5": 152456, "ng:consciousness_models:embodied_cognition_7": 152164, "ng:consciousness_models:global_workspace_1": 151747, "ng:consciousness_models:global_workspace_7": 151847, "ng:consciousness_models:higher_order_thought_1": 151819, "ng:consciousness_models:integrated_information": 151974, "ng:consciousness_models:integrated_information_1": 152281, "ng:consciousness_models:phenomenal_consciousness_1": 151879, "ng:consciousness_models:phenomenal_consciousness_4": 152657, "ng:consciousness_models:subjective_experience_5": 151802, "ng:consciousness_models:subjective_experience_6": 152445, "ng:consciousness_models:subjective_experience_7": 152481, "ng:contextual_reasoning:background_knowledge": 151975, "ng:contextual_reasoning:common_sense_reasoning_1": 152213, "ng:contextual_reasoning:context_awareness": 152451, "ng:contextual_reasoning:context_switching": 151872, "ng:contextual_reasoning:contextual_adaptation": 152007, "ng:contextual_reasoning:contextual_adaptation_2": 152008, "ng:contextual_reasoning:conversational_context": 152034, "ng:contextual_reasoning:cultural_context_1": 151707, "ng:contextual_reasoning:cultural_context_3": 152158, "ng:contextual_reasoning:implicatures_3": 151788, "ng:contextual_reasoning:pragmatic_inference_1": 152270, "ng:contextual_reasoning:presuppositions_1": 151706, "ng:contextual_reasoning:social_context_1": 152305, "ng:contextual_reasoning:temporal_context_3": 152583, "ng:creative_thinking:creative_constraints_6": 152104, "ng:creative_thinking:creative_constraints_9": 151968, "ng:creative_thinking:elaboration": 151746, "ng:creative_thinking:fluency_1": 152470, "ng:creative_thinking:originality_1": 151969, "ng:creative_thinking:originality_2": 152398, "ng:creative_thinking:originality_3": 151787, "ng:decision_making:choice_architecture": 152560, "ng:decision_making:choice_architecture_1": 151827, "ng:decision_making:choice_architecture_2": 151910, "ng:decision_making:decision_biases_2": 152382, "ng:decision_making:decision_confidence_3": 151868, "ng:decision_making:multi_criteria_decisions": 152452, "ng:decision_making:multi_criteria_decisions_4": 152107, "ng:decision_making:preference_formation_1": 152012, "ng:decision_making:preference_formation_4": 152321, "ng:decision_making:rational_choice": 152374, "ng:decision_making:value_based_decisions_2": 151716, "ng:distributed_systems:circuitbreakers_fn_5": 152617, "ng:distributed_systems:circuitbreakers_fn_6": 152544, "ng:distributed_systems:circuitbreakers_op_4": 151913, "ng:distributed_systems:circuitbreakers_op_7": 152157, "ng:distributed_systems:distributedlocks_4": 151715, "ng:distributed_systems:gossipprotocols_fn_2": 151670, "ng:distributed_systems:gossipprotocols_op": 152576, "ng:distributed_systems:leaderelection_core": 151828, "ng:distributed_systems:loadbalancing": 152400, "ng:distributed_systems:loadbalancing_fn": 151845, "ng:distributed_systems:loadbalancing_proc": 152280, "ng:distributed_systems:raft": 152497, "ng:distributed_systems:raft_op": 152345, "ng:distributed_systems:replication_ctrl": 151861, "ng:distributed_systems:replication_op": 152051, "ng:distributed_systems:sharding_op_2": 152133, "ng:emotional_intelligence:affective_computing_3": 151923, "ng:emotional_intelligence:affective_computing_6": 152178, "ng:emotional_intelligence:emotion_regulation_3": 151736, "ng:emotional_intelligence:emotion_understanding_1": 152184, "ng:emotional_intelligence:emotional_awareness": 151674, "ng:emotional_intelligence:emotional_awareness_6": 152585, "ng:emotional_intelligence:emotional_contagion": 151947, "ng:emotional_intelligence:emotional_contagion_4": 151936, "ng:emotional_intelligence:emotional_contagion_7": 152326, "ng:emotional_intelligence:emotional_expression_1": 152519, "ng:emotional_intelligence:emotional_expression_7": 151739, "ng:emotional_intelligence:emotional_reasoning": 152521, "ng:emotional_intelligence:mood_regulation_2": 151932, "ng:emotional_intelligence:mood_regulation_3": 152019, "ng:emotional_intelligence:mood_regulation_5": 151781, "ng:extension:bennett_inequality": 151776, "ng:extension:caching_strategies": 152565, "ng:extension:chatbot_design": 152385, "ng:extension:congestion_control": 151821, "ng:extension:convex_optimization": 152437, "ng:extension:differential_privacy": 152325, "ng:extension:discourse_analysis": 152549, "ng:extension:earth_mover": 152150, "ng:extension:flow_control": 152566, "ng:extension:formal_methods": 152206, "ng:extension:hamiltonian_monte": 152479, "ng:extension:hidden_markov": 151982, "ng:extension:image_processing": 151877, "ng:extension:philosophical_extended": 152654, "ng:extension:poisson_process": 152231, "ng:extension:question_answering": 151811, "ng:extension:sample_complexity": 151753, "ng:extension:secure_multiparty": 152296, "ng:extension:sparql_queries": 151900, "ng:extension:temporal_reasoning": 151880, "ng:extension:transfer_learning": 152292, "ng:flow:break_13": 152391, "ng:flow:break_14": 151874, "ng:flow:break_5": 152464, "ng:flow:break_8": 152285, "ng:flow:else_2": 152492, "ng:flow:for_12": 152248, "ng:flow:if_13": 152245, "ng:flow:if_17": 152090, "ng:flow:if_18": 152433, "ng:flow:if_9": 152442, "ng:flow:if_switch": 151841, "ng:flow:return_3": 152509, "ng:flow:while_14": 152106, "ng:flow:while_17": 152186, "ng:flow:while_async": 152122, "ng:formal_verification:separationlogic_8": 152626, "ng:formal_verification:separationlogic_fn_7": 152664, "ng:formal_verification:symbolicexecution_3": 152085, "ng:geometry:double_struck_capital_gam": 152388, "ng:geometry:script_capital_b": 152635, "ng:goal_oriented_behavior:goal_conflict_resolution_1": 152312, "ng:goal_oriented_behavior:goal_conflict_resolution_2": 151754, "ng:goal_oriented_behavior:goal_monitoring_1": 152360, "ng:goal_oriented_behavior:goal_monitoring_7": 152279, "ng:goal_oriented_behavior:goal_pursuit_1": 152081, "ng:goal_oriented_behavior:goal_pursuit_4": 152191, "ng:goal_oriented_behavior:goal_setting_2": 151991, "ng:goal_oriented_behavior:motivation_5": 152434, "ng:goal_oriented_behavior:persistence_4": 151782, "ng:goal_oriented_behavior:planning": 152074, "ng:goal_oriented_behavior:planning_2": 152580, "ng:goal_oriented_behavior:planning_5": 151856, "ng:goal_oriented_behavior:planning_6": 152571, "ng:goal_oriented_behavior:subgoal_decomposition_1": 151944, "ng:goal_oriented_behavior:subgoal_decomposition_2": 152650, "ng:goal_oriented_behavior:subgoal_decomposition_4": 152476, "ng:knowledge_representation:inheritance_relations": 151950, "ng:knowledge_representation:knowledge_graphs_1": 152401, "ng:knowledge_representation:part_whole_relations_2": 152427, "ng:knowledge_representation:type_systems_6": 152606, "ng:language_understanding:grammatical_knowledge_1": 152394, "ng:language_understanding:lexical_access": 151675, "ng:language_understanding:lexical_access_5": 152177, "ng:language_understanding:morphological_analysis_4": 152641, "ng:language_understanding:narrative_understanding_1": 152573, "ng:language_understanding:phonological_processing_2": 152095, "ng:language_understanding:semantic_composition_3": 152543, "ng:learning_algorithms:meta_learning_4": 152209, "ng:learning_algorithms:multi_task_learning": 151744, "ng:learning_algorithms:online_learning_1": 152293, "ng:learning_algorithms:online_learning_2": 152595, "ng:learning_algorithms:supervised_learning_4": 152416, "ng:letterlike_symbols:script_capital_i": 152506, "ng:logic:iff_9": 151814, "ng:logic:implies_10": 152035, "ng:logic:implies_8": 152367, "ng:logic:implies_strict": 151689, "ng:logic:not_18": 152581, "ng:logic:not_21": 152399, "ng:logic:or_15": 152167, "ng:logic:xor_5": 151961, "ng:logical_operators:clockwise_open_circle_arr": 152266, "ng:logical_operators:downwards_arrow_with_doub": 151883, "ng:logical_operators:leftwards_double_arrow_wi": 152466, "ng:machine_learning:deeplearning_ctrl_1": 152204, "ng:machine_learning:deeplearning_op_1": 152491, "ng:machine_learning:ensemblemethods": 152420, "ng:machine_learning:ensemblemethods_27": 152616, "ng:machine_learning:ensemblemethods_34": 151705, "ng:machine_learning:ensemblemethods_42": 152252, "ng:machine_learning:ensemblemethods_7": 152471, "ng:machine_learning:ensemblemethods_op_9": 152572, "ng:machine_learning:modelevaluation_fn_1": 152145, "ng:machine_learning:prediction_fn": 152368, "ng:mathematical_structures:algebra_proc": 152558, "ng:mathematical_structures:combinatorics_core_5": 152037, "ng:mathematical_structures:combinatorics_fn_2": 151677, "ng:mathematical_structures:combinatorics_meta_1": 151898, "ng:mathematical_structures:combinatorics_op_2": 152477, "ng:mathematical_structures:numbertheory_meta": 152660, "ng:memory:alloc_10": 152342, "ng:memory:alloc_12": 152258, "ng:memory:alloc_19": 152144, "ng:memory:alloc_20": 152271, "ng:memory:alloc_6": 151935, "ng:memory:deref_2": 152454, "ng:memory:deref_6": 152334, "ng:memory:deref_8": 152192, "ng:memory:free_15": 151864, "ng:memory:pointer_17": 152076, "ng:memory:pointer_4": 151737, "ng:memory:ref_10": 152078, "ng:memory:ref_13": 152309, "ng:memory:ref_9": 151973, "ng:memory_architectures:associative_memory_1": 152079, "ng:memory_architectures:content_addressable_memory_1": 151685, "ng:memory_architectures:false_memories_1": 152378, "ng:memory_architectures:memory_encoding": 152527, "ng:memory_architectures:memory_encoding_3": 152108, "ng:memory_architectures:memory_interference": 152193, "ng:memory_architectures:memory_interference_3": 152024, "ng:memory_architectures:memory_reconstruction_2": 152609, "ng:memory_architectures:memory_reconstruction_5": 152564, "ng:memory_architectures:memory_retrieval": 151897, "ng:memory_architectures:memory_schemas_1": 151721, "ng:meta:meaning_space": 152058, "ng:meta:meta_meta": 152582, "ng:meta:meta_metacognition": 152499, "ng:meta_programming:automated_refactoring": 152410, "ng:meta_programming:code_transformation_1": 151976, "ng:meta_programming:codeasdata": 152596, "ng:meta_programming:codeasdata_core_1": 152068, "ng:meta_programming:codeasdata_fn": 152450, "ng:meta_programming:codeasdata_sys_1": 151940, "ng:meta_programming:dynamic_analysis_5": 152072, "ng:meta_programming:dynamic_compilation": 151870, "ng:meta_programming:generic_programming_2": 151959, "ng:meta_programming:introspection_2": 152489, "ng:meta_programming:jit_compilation_1": 152181, "ng:meta_programming:macrosystems_1": 151815, "ng:meta_programming:macrosystems_3": 152214, "ng:meta_programming:macrosystems_fn": 152313, "ng:meta_programming:macrosystems_op_1": 152319, "ng:meta_programming:programsynthesis_11": 152163, "ng:meta_programming:programsynthesis_op": 151955, "ng:meta_programming:stagedcomputation_3": 152023, "ng:misc_technical:ounce_sign": 152530, "ng:multimodal_reasoning:auditory_processing_4": 151933, "ng:multimodal_reasoning:cross_modal_integration_4": 151671, "ng:multimodal_reasoning:cross_modal_plasticity": 152243, "ng:multimodal_reasoning:modal_specific_processing": 152370, "ng:multimodal_reasoning:modal_specific_processing_4": 151711, "ng:multimodal_reasoning:multimodal_attention_3": 152125, "ng:multimodal_reasoning:multimodal_attention_4": 152448, "ng:multimodal_reasoning:perceptual_binding_4": 152212, "ng:multimodal_reasoning:sensory_substitution_4": 152115, "ng:multimodal_reasoning:tactile_understanding": 151884, "ng:multimodal_reasoning:visual_reasoning_1": 152584, "ng:multimodal_reasoning:visual_reasoning_3": 152062, "ng:neural_architectures:activation_lstm": 152045, "ng:neural_architectures:attention_backpropagation": 152003, "ng:neural_architectures:cross_attn_proc": 152358, "ng:neural_architectures:dense_layers_1": 151684, "ng:neural_architectures:dropout_variants_2": 152467, "ng:neural_architectures:gate_connections": 152211, "ng:neural_architectures:gradient_layers": 152598, "ng:neural_architectures:lossfunctions_proc": 152343, "ng:neural_architectures:multi_head_meta": 151943, "ng:neural_architectures:neural_topology_1": 152651, "ng:neural_architectures:positional_encoding_1": 151871, "ng:neural_architectures:positional_encoding_16": 152504, "ng:neural_architectures:positional_encoding_8": 151763, "ng:neural_architectures:weight_initialization_1": 152662, "ng:neural_architectures:weight_initialization_2": 152282, "ng:number_forms:black_letter_capital_i": 152593, "ng:number_forms:double_struck_n_ary_summa": 151990, "ng:number_forms:left_right_arrow": 152610, "ng:operator:add_16": 152288, "ng:operator:add_matrix": 152545, "ng:operator:add_vector": 151740, "ng:operator:div_13": 151830, "ng:operator:div_19": 152202, "ng:operator:div_7": 151942, "ng:operator:div_8": 152026, "ng:operator:div_9": 152154, "ng:operator:div_integer": 152255, "ng:operator:mod_9": 152514, "ng:operator:mod_matrix": 152554, "ng:operator:mul": 151708, "ng:operator:mul_6": 152094, "ng:operator:pow_1": 151743, "ng:operator:pow_2": 151889, "ng:operator:pow_9": 151926, "ng:operator:sub": 152311, "ng:operator:sub_11": 151800, "ng:operator:sub_12": 151817, "ng:operator:sub_13": 151834, "ng:operator:sub_7": 152486, "ng:philosophical_concepts:logicphilosophy_fn": 152355, "ng:philosophical_concepts:metaphysics_ctrl_2": 152028, "ng:philosophical_concepts:metaphysics_ctrl_6": 151807, "ng:philosophical_concepts:ontology_op_2": 152541, "ng:philosophical_concepts:ontology_sys": 152168, "ng:physics_notation:downwards_arrow_with_tip_": 151873, "ng:physics_notation:leftwards_arrow_with_doub": 152621, "ng:physics_notation:north_west_double_arrow": 152089, "ng:physics_notation:rightwards_two_headed_arr": 152453, "ng:physics_notation:script_capital_h": 152102, "ng:physics_notation:script_small_e": 152622, "ng:problem_solving:algorithmic_approaches_3": 151989, "ng:problem_solving:algorithmic_approaches_4": 151725, "ng:problem_solving:constraint_satisfaction": 152407, "ng:problem_solving:heuristic_methods": 152022, "ng:problem_solving:heuristic_methods_1": 152474, "ng:problem_solving:hill_climbing_1": 152232, "ng:problem_solving:hill_climbing_2": 151983, "ng:problem_solving:means_ends_analysis": 151731, "ng:problem_solving:means_ends_analysis_4": 152354, "ng:problem_solving:problem_decomposition_1": 151924, "ng:problem_solving:problem_identification_2": 151855, "ng:problem_solving:solution_evaluation_3": 152065, "ng:quantum_computing:entanglement_1": 151905, "ng:quantum_computing:entanglement_op": 152126, "ng:quantum_computing:quantum_gates_1": 151878, "ng:quantum_computing:quantum_machine_learning_1": 152406, "ng:quantum_computing:quantum_measurement_1": 151997, "ng:quantum_computing:quantum_optimization_4": 151672, "ng:quantum_computing:quantum_parallelism": 152495, "ng:quantum_computing:quantum_simulation_1": 152123, "ng:quantum_computing:quantumcircuits_op": 151992, "ng:reasoning:conclusion": 152188, "ng:reasoning:modus_ponens": 152140, "ng:reasoning:reflection": 152083, "ng:reasoning:specialization": 152365, "ng:reasoning_patterns:abductive_reasoning_1": 151714, "ng:reasoning_patterns:inductive_reasoning_3": 152277, "ng:reasoning_patterns:inference_rules_1": 152116, "ng:reasoning_patterns:logical_equivalence_1": 151719, "ng:reasoning_patterns:logical_equivalence_3": 152099, "ng:reasoning_patterns:modus_tollens_2": 152217, "ng:reasoning_patterns:modus_tollens_3": 152156, "ng:reasoning_patterns:pattern_matching": 151939, "ng:reasoning_patterns:proof_strategies_3": 151999, "ng:reasoning_patterns:syllogistic_reasoning": 151730, "ng:reasoning_patterns:truth_tables_2": 151922, "ng:reasoning_patterns:truth_tables_9": 151759, "ng:reserved_expansion:extensionpoints": 151713, "ng:reserved_expansion:extensionpoints_4": 152006, "ng:reserved_expansion:extensionpoints_fn_4": 152233, "ng:reserved_expansion:extensionpoints_fn_8": 152338, "ng:reserved_expansion:future_op": 151866, "ng:reserved_expansion:novelabstractions_8": 152429, "ng:reserved_expansion:researchareas_fn_2": 152393, "ng:self:identity_core": 152030, "ng:self:introspection": 151994, "ng:self:self_evolution": 151792, "ng:self_reflection:cognitive_control_3": 152599, "ng:self_reflection:executive_monitoring_3": 152336, "ng:self_reflection:executive_monitoring_5": 152166, "ng:self_reflection:introspection_3": 152199, "ng:self_reflection:learning_strategies_2": 151853, "ng:self_reflection:learning_strategies_4": 152247, "ng:self_reflection:metacognition_6": 151728, "ng:self_reflection:metacognitive_awareness_2": 152267, "ng:self_reflection:metacognitive_awareness_5": 151724, "ng:self_reflection:metacognitive_awareness_7": 152264, "ng:self_reflection:self_evaluation_4": 151795, "ng:self_reflection:self_evaluation_6": 151832, "ng:self_reflection:self_knowledge_6": 152127, "ng:self_reflection:self_knowledge_7": 151915, "ng:self_reflection:self_monitoring_2": 152387, "ng:self_reflection:self_regulation_2": 151919, "ng:self_reflection:self_regulation_7": 151770, "ng:semantic_understanding:compositional_semantics_4": 151972, "ng:semantic_understanding:contextual_meaning_3": 152369, "ng:semantic_understanding:metaphorical_meaning_1": 151888, "ng:semantic_understanding:metaphorical_meaning_3": 152577, "ng:semantic_understanding:sentence_semantics_2": 152444, "ng:social_cognition:belief_attribution_6": 152112, "ng:social_cognition:collaboration_10": 152010, "ng:social_cognition:cultural_understanding_6": 152210, "ng:social_cognition:cultural_understanding_7": 152242, "ng:social_cognition:intention_recognition_6": 151925, "ng:social_cognition:interpersonal_reasoning_5": 152537, "ng:social_cognition:perspective_taking_4": 152155, "ng:social_cognition:social_learning": 152493, "ng:social_cognition:social_norms_7": 152344, "ng:social_cognition:social_understanding_1": 152038, "ng:social_cognition:social_understanding_4": 151667, "ng:social_cognition:social_understanding_5": 151734, "ng:social_cognition:theory_of_mind": 152194, "ng:social_cognition:theory_of_mind_5": 152324, "ng:social_cognition:theory_of_mind_6": 152128, "ng:structure:class_22": 152507, "ng:structure:function_14": 152121, "ng:structure:function_6": 151887, "ng:structure:method_13": 151666, "ng:structure:method_7": 151921, "ng:structure:property_10": 152487, "ng:structure:property_12": 152411, "ng:structure:property_22": 152341, "ng:structure:property_3": 152187, "ng:structure:property_6": 152257, "ng:supplemental_math:account_of": 152043, "ng:supplemental_math:double_struck_italic_smal": 152636, "ng:supplemental_math:leftwards_harpoon_over_ri": 152371, "ng:symbolic_ai:constraint_satisfaction": 151742, "ng:symbolic_ai:descriptionlogics_5": 152298, "ng:symbolic_ai:expert_systems": 151937, "ng:symbolic_ai:expertsystems_proc": 151962, "ng:symbolic_ai:first_order_logic": 152529, "ng:symbolic_ai:higher_order_logic_11": 152289, "ng:symbolic_ai:higher_order_logic_9": 152138, "ng:symbolic_ai:logical_inference": 152515, "ng:symbolic_ai:logicalinference": 151679, "ng:symbolic_ai:logicalinference_3": 152086, "ng:symbolic_ai:ontologies_sys_3": 152375, "ng:symbolic_ai:search_algorithms": 151812, "ng:symbolic_ai:semanticnetworks_3": 152331, "ng:symbolic_ai:theoremproving_sys_3": 152131, "ng:technical_symbols:telephone_sign": 152329, "ng:temporal:temporal_logic": 152551, "ng:temporal:temporal_pattern": 152276, "ng:temporal_cognition:duration_estimation": 152532, "ng:temporal_cognition:duration_estimation_1": 152332, "ng:temporal_cognition:duration_estimation_3": 152256, "ng:temporal_cognition:event_timing_2": 152272, "ng:temporal_cognition:event_timing_5": 152587, "ng:temporal_cognition:time_perception_4": 152352, "ng:topology:left_right_wave_arrow": 151813, "ng:type_theory:refinementtypes_1": 152591, "ng:type_theory:refinementtypes_fn_2": 152570, "ng:type_theory:uniontypes_1": 152082, "℅": 152143, "℈": 152389, "ℌ": 152482, "℔": 152395, "K": 152538, "Å": 152483, "ℼ": 151791, "⅀": 152234, "⅌": 151904, "⅍": 151712, "↋": 152498, "↙": 151906, "↛": 151891, "↜": 152052, "↧": 152373, "↩": 152594, "↫": 152563, "↭": 152172, "↺": 152160, "↼": 151766, "⇋": 152426, "⇌": 151798, "⇎": 152220, "⇑": 151839, "⇘": 152246, "⇙": 152011, "⇞": 151946, "⇮": 152014, "⇺": 152613, "∃": 151826, "∆": 152578, "∊": 151829, "∥": 151857, "∦": 152436, "∨": 152260, "∬": 152002, "∯": 152415, "∷": 152488, "≃": 152417, "≆": 152528, "≋": 151850, "≓": 152294, "≞": 151958, "≧": 151777, "≯": 152569, "≷": 151678, "≽": 151966, "⊆": 152180, "⊓": 152340, "⊕": 151838, "⊚": 152161, "⊛": 152159, "⊟": 151867, "⊠": 152041, "⊡": 152649, "⊵": 151699, "⊷": 152409, "⊹": 152472, "⊺": 152600, "⋀": 151908, "⋂": 152396, "⋊": 151899, "⋕": 152226, "⋟": 151665, "⋠": 152460, "⋳": 151911, "⋺": 152073, "⌃": 152134, "⌄": 152087, "⌆": 151686, "⌈": 151768, "⌍": 152284, "⌞": 152646, "⌤": 152208, "⌹": 152337, "⍘": 152555, "⍛": 152162, "⍞": 152546, "⍣": 152644, "⍧": 152330, "⍴": 152021, "⍹": 152291, "⎂": 151970, "⎕": 152628, "⎞": 152518, "⎩": 152443, "⎪": 152592, "⎮": 152533, "⎴": 151729, "⎽": 152624, "⏋": 152643, "⏗": 152064, "⏟": 152637, "⏧": 152381, "⏪": 151767, "⏮": 152539, "⏯": 151757, "⏲": 151985, "⏳": 152350, "⏴": 152473, "⏼": 151794, "␎": 152016, "␔": 152372, "␕": 151771, "␛": 152000, "␜": 151673, "␻": 152423, "⑎": 151683, "⑖": 152061, "⑧": 151741, "⑫": 152485, "⑰": 152461, "⑸": 151723, "⒉": 151710, "⒎": 151690, "⒜": 151755, "⒢": 152404, "⒧": 152590, "Ⓐ": 151698, "Ⓒ": 152132, "Ⓘ": 152446, "Ⓚ": 152428, "┇": 152631, "┑": 152036, "┓": 152526, "┗": 152656, "┙": 152589, "┢": 152468, "┮": 151748, "┰": 152235, "┴": 151941, "┷": 152623, "┽": 152153, "╈": 152301, "╊": 152057, "╓": 151896, "△": 152170, "▽": 152618, "▾": 151849, "◅": 152050, "◆": 152447, "●": 152419, "◕": 151953, "◝": 151775, "◣": 152152, "◫": 152588, "◶": 151806, "◿": 151694, "★": 151696, "☆": 152001, "☒": 152118, "☕": 152047, "☚": 152611, "☜": 151682, "☲": 151929, "☻": 152105, "☼": 152253, "♓": 152018, "♚": 151931, "♜": 152363, "♟": 152438, "♢": 151996, "✈": 151751, "✉": 152458, "✌": 152548, "✘": 152478, "✞": 151789, "✩": 151984, "✰": 151793, "❆": 151702, "❇": 152469, "❖": 152475, "❟": 152522, "❤": 151695, "❪": 152639, "❾": 152333, "➁": 152124, "➌": 151987, "➏": 151945, "➖": 152463, "➝": 152203, "➢": 152511, "➨": 152627, "➬": 152559, "➮": 151764, "➳": 151760, "➹": 151934, "⟉": 151810, "⟗": 152480, "⟬1028": 151709, "⟬1408": 152535, "⟬1915": 151840, "⟬1941": 152317, "⟬1953": 151726, "⟬2163": 152424, "⟬2536": 151773, "⟬3067": 151912, "⟬3124": 151765, "⟬3166": 152534, "⟬3682": 151691, "⟬4002": 151837, "⟬4737": 152137, "⟬4854": 151833, "⟬5000": 152111, "⟬5056": 151917, "⟬5963": 152457, "⟬6953": 152141, "⟬7262": 151778, "⟬7876": 151862, "⟬8631": 151697, "⟬8748": 152323, "⟬9001": 152017, "⟬9492": 152540, "⟬9759": 152176, "⟬9803": 151928, "⟬9940": 151901, "⟬9981": 151885, "⟻": 152380, "⟼": 152097, "⟾": 151948, "⤆": 152579, "⤉": 152004, "⤫": 152005, "⤮": 152031, "⤱": 152219, "⤽": 152562, "⥀": 151998, "⥁": 151960, "⥂": 152067, "⥋": 151738, "⥍": 152421, "⥕": 152490, "⥚": 152302, "⥛": 152049, "⥧": 152136, "⥮": 152496, "⥰": 152652, "⦀145": 152383, "⦀147": 151676, "⦀207": 151722, "⦀215": 152494, "⦀271": 152347, "⦀309": 152318, "⦀342": 152110, "⦀379": 151956, "⦀381": 152275, "⦀413": 151882, "⦀433": 151822, "⦀439": 152655, "⦀452": 152402, "⦀459": 152114, "⦀477": 152604, "⦀496": 151717, "⦀574": 151772, "⦀598": 152303, "⦀607": 152009, "⦀627": 152568, "⦀634": 152642, "⦀663": 152084, "⦀676": 152505, "⦀677": 152295, "⦀700": 152240, "⦀706": 152440, "⦀718": 151981, "⦀732": 152113, "⦀741": 151718, "⦀768": 151951, "⦀772": 151963, "⦀781": 152207, "⦀782": 152339, "⦀790": 152661, "⦀793": 152356, "⦀795": 152077, "⦀815": 151680, "⦀827": 152308, "⦀837": 151704, "⦀871": 151938, "⦀883": 152348, "⦀887": 152287, "⦀934": 152063, "⦀940": 152484, "⦀991": 152033, "⦀992": 151918, "⦀993": 151843, "⦂": 152517, "⦄": 152552, "⦊": 152224, "⦎": 152053, "⦔": 152418, "⦗": 152630, "⦙": 152290, "⦠": 152025, "⦦": 151720, "⦭": 151920, "⦳": 152205, "⧀": 152244, "⧎": 151758, "⧖": 152039, "⧱": 152640, "⧳": 151701, "⨁": 152173, "⨂": 151954, "⨋": 152605, "⨍": 152612, "⨑": 152361, "⨔": 151965, "⨝": 152524, "⨠": 152413, "⨦": 152269, "⨧": 151733, "⨩": 152066, "⨮": 152513, "⨵": 151745, "⩀": 152236, "⩁": 151820, "⩈": 151749, "⩌": 152080, "⩒": 151818, "⩔": 152316, "⩠": 152046, "⩢": 151876, "⩣": 152142, "⩤": 152462, "⩧": 152542, "⩪": 151995, "⩬": 151893, "⩲": 152557, "⩴": 152575, "⩼": 152096, "⩿": 152634, "⪈": 152449, "⪊": 152556, "⪌": 152310, "⪏": 151980, "⪕": 151693, "⪚": 152403, "⪢": 152502, "⪹": 152040, "⪾": 152139, "⫀": 152315, "⫃": 152359, "⫈": 152032, "⫔": 152422, "⫧": 151903, "⫨": 151881, "⫩": 151809, "⫵": 152327, "⫺": 152531, "⫾": 152241, "⬄": 152615, "⬆": 152238, "⬑": 151786, "⬩": 152169, "⬱": 152431, "⭇": 151909, "⭜": 152120, "⭝": 151769, "⭠": 151902, "⭧": 152215, "⭨": 152185, "⭪": 152408, "⭫": 151801, "⭭": 152056, "⭽": 151797, "⮇": 152054, "⮉": 152306, "⮊": 152237, "⮏": 152060, "⮓": 151859, "⮣": 152249, "⮩": 152182, "⯎": 152183, "⯔": 152567, "⯞": 152603, "⯠": 151808, "⯨": 151796, "⯪": 151752, "ⷸ": 152432, "⸪": 152500, "ꝙ": 152574, "ꭐ": 151892, "𝐋": 152523, "𝑎": 152512, "𝑶": 152516, "𝒀": 152653, "𝒪": 152608, "𝓔": 152602, "𝔙": 151865, "𝗇": 151875, "𝘓": 152198, "𝘥": 152274, "𝚶": 152130, "𝛺": 152314, "𝜱": 151774, "𝝚": 151846, "𝝭": 152165, "𝞚": 151779, "𝞽": 152071, "🌍": 151895, "🌏": 151681, "🌟": 151848, "🌡": 151949, "🌧": 151803, "🌵": 152520, "🌶": 151844, "🍇": 152510, "🍉": 152645, "🍛": 151761, "🍞": 151842, "😀": 152663, "😃": 152119, "😶": 151863, "😹": 151916, "🙊": 152265, "🙌": 152349, "🚅": 152013, "🚇": 151835, "🚈": 151668, "🚉": 151692, "🚓": 151886, "🚔": 152263, "🚨": 152379, "🚪": 152439, "🚵": 152405, "🚷": 151890, "🚿": 152228, "🛁": 152251, "🛄": 152414, "🛖": 151964, "🛞": 152425, "🜋": 151854, "🜍": 152455, "🜏": 151979, "🜚": 152189, "🜝": 152638, "🜤": 152364, "🜧": 152648, "🝚": 152659, "🝛": 152376, "🝪": 152250, "🝵": 152117, "🞀": 152465, "🞃": 151993, "🞆": 152633, "🞗": 152254, "🞙": 151735, "🞩": 152239, "🞰": 152179, "🞵": 151927, "🞶": 151858, "🞷": 152553, "🞹": 151831, "🟌": 152351, "🟚": 151869, "🟫": 152100, "🟯": 152299, "🟳": 152101, "🟻": 152390, "🠃": 152601, "🠅": 152278, "🠑": 152146, "🠖": 151688, "🠗": 152259, "🠙": 151785, "🠛": 152300, "🠞": 152171, "🠠": 151762, "🠤": 152149, "🠨": 151952, "🠴": 152029, "🠹": 152297, "🠻": 152044, "🠽": 152059, "🡀": 152197, "🢃": 152547, "🢔": 152135}