#!/usr/bin/env python3
"""
NEUROGLYPH SUPREME TOKENIZER VERIFICATION
=========================================

Comprehensive verification script implementing all SUPREME security checks:
- Unicode collision detection
- SHA256 checksum validation
- Atomicity verification
- Export integrity check

🔒 SUPREME SECURITY FEATURES:
- Zero collision guarantee
- Locked mappings protection
- Complete integrity verification
- Export validation
"""

import json
import hashlib
from pathlib import Path
from transformers import AutoTokenizer
import sys

def verify_unicode_collisions(tokenizer_path: str) -> bool:
    """Verify no Unicode collisions in added tokens"""
    print("🔍 Verifying Unicode collisions...")
    
    try:
        # Load added tokens
        added_tokens_path = Path(tokenizer_path) / "added_tokens.json"
        with open(added_tokens_path, 'r') as f:
            added_tokens = json.load(f)
        
        # Extract symbols
        symbols = [token.get('content', token) if isinstance(token, dict) else token 
                  for token in added_tokens]
        
        # Check Unicode ranges
        safe_ranges = [
            (0x2200, 0x22FF),  # Mathematical Operators
            (0x2190, 0x21FF),  # Arrows
            (0x2600, 0x26FF),  # Miscellaneous Symbols
            (0x1F300, 0x1F9FF), # Emoji ranges
            (0x3000, 0x303F),  # CJK Symbols and Punctuation
        ]
        
        conflicts = []
        safe_count = 0
        
        for symbol in symbols:
            if symbol.startswith('ng:'):
                safe_count += 1
                continue
                
            if len(symbol) >= 1:
                codepoint = ord(symbol[0])
                
                # Check if in safe ranges
                in_safe_range = any(start <= codepoint <= end for start, end in safe_ranges)
                
                # Check for ASCII conflicts
                if 0x0020 <= codepoint <= 0x007F:
                    conflicts.append(f"{symbol} (U+{codepoint:04X}) - ASCII conflict")
                elif 0x0080 <= codepoint <= 0x00FF:
                    conflicts.append(f"{symbol} (U+{codepoint:04X}) - Latin-1 conflict")
                elif in_safe_range:
                    safe_count += 1
                else:
                    conflicts.append(f"{symbol} (U+{codepoint:04X}) - Outside safe ranges")
        
        safety_rate = (safe_count / len(symbols)) * 100
        
        print(f"  ✅ Safe symbols: {safe_count:,}/{len(symbols):,} ({safety_rate:.1f}%)")
        print(f"  ⚠️ Conflicts: {len(conflicts)}")
        
        if conflicts:
            print("  First few conflicts:")
            for conflict in conflicts[:5]:
                print(f"    • {conflict}")
        
        # SUPREME standard: 95%+ must be safe
        is_safe = safety_rate >= 95.0
        
        if is_safe:
            print("  🎉 Unicode collision verification PASSED!")
        else:
            print(f"  ❌ Unicode collision verification FAILED: {safety_rate:.1f}% < 95.0%")
        
        return is_safe
        
    except Exception as e:
        print(f"  ❌ Unicode collision check failed: {e}")
        return False

def verify_checksum_integrity(tokenizer_path: str) -> bool:
    """Verify SHA256 checksum integrity"""
    print("🔒 Verifying checksum integrity...")
    
    try:
        # Load metadata
        metadata_path = Path(tokenizer_path) / "ultimate_tokenizer_metadata.json"
        with open(metadata_path, 'r') as f:
            metadata = json.load(f)
        
        # Get stored checksum
        stored_checksum = metadata.get('locked_mappings_checksum', '')
        
        if not stored_checksum:
            print("  ⚠️ No stored checksum found")
            return False
        
        # Load locked mappings
        locked_path = Path(tokenizer_path) / "locked_symbol_mappings.json"
        with open(locked_path, 'r') as f:
            locked_mappings = json.load(f)
        
        # Recalculate checksum
        locked_data = {
            "mappings": dict(sorted(locked_mappings.items())),
            "count": len(locked_mappings),
            "timestamp": metadata.get('creation_timestamp', ''),
            "version": "SUPREME_1.0"
        }
        
        data_str = json.dumps(locked_data, sort_keys=True, ensure_ascii=False)
        calculated_checksum = hashlib.sha256(data_str.encode('utf-8')).hexdigest()
        
        # Compare checksums
        if calculated_checksum == stored_checksum:
            print(f"  ✅ Checksum verified: {calculated_checksum[:16]}...")
            return True
        else:
            print(f"  ❌ Checksum mismatch!")
            print(f"    Stored:     {stored_checksum[:16]}...")
            print(f"    Calculated: {calculated_checksum[:16]}...")
            return False
            
    except Exception as e:
        print(f"  ❌ Checksum verification failed: {e}")
        return False

def verify_atomicity_guarantee(tokenizer_path: str) -> bool:
    """Verify all symbols have 1:1 atomic mapping"""
    print("⚛️ Verifying atomicity guarantee...")
    
    try:
        # Load tokenizer
        tokenizer = AutoTokenizer.from_pretrained(tokenizer_path)
        
        # Load added tokens
        added_tokens_path = Path(tokenizer_path) / "added_tokens.json"
        with open(added_tokens_path, 'r') as f:
            added_tokens_data = json.load(f)
        
        # Extract symbols
        symbols = []
        for token in added_tokens_data:
            if isinstance(token, dict):
                symbols.append(token.get('content', ''))
            else:
                symbols.append(token)
        
        # Test atomicity
        atomic_count = 0
        failed_symbols = []
        
        for symbol in symbols:
            if not symbol:
                continue
                
            try:
                # Test encoding
                token_ids = tokenizer.encode(symbol, add_special_tokens=False)
                
                # Test decoding
                decoded = tokenizer.decode(token_ids)
                
                # Verify atomicity
                if len(token_ids) == 1 and decoded == symbol:
                    atomic_count += 1
                else:
                    failed_symbols.append(f"{symbol} → {token_ids} → {decoded}")
                    
            except Exception as e:
                failed_symbols.append(f"{symbol} → Error: {e}")
        
        atomicity_rate = (atomic_count / len(symbols)) * 100
        
        print(f"  ✅ Atomic symbols: {atomic_count:,}/{len(symbols):,} ({atomicity_rate:.1f}%)")
        print(f"  ❌ Failed symbols: {len(failed_symbols)}")
        
        if failed_symbols:
            print("  First few failures:")
            for failure in failed_symbols[:5]:
                print(f"    • {failure}")
        
        # SUPREME standard: 100% atomicity required
        is_atomic = atomicity_rate >= 99.9  # Allow 0.1% tolerance
        
        if is_atomic:
            print("  🎉 Atomicity verification PASSED!")
        else:
            print(f"  ❌ Atomicity verification FAILED: {atomicity_rate:.1f}% < 99.9%")
        
        return is_atomic
        
    except Exception as e:
        print(f"  ❌ Atomicity verification failed: {e}")
        return False

def verify_export_integrity(tokenizer_path: str) -> bool:
    """Verify tokenizer export integrity"""
    print("📦 Verifying export integrity...")
    
    try:
        tokenizer_path = Path(tokenizer_path)
        
        # Required files for GGUF conversion
        required_files = [
            "tokenizer.json",
            "vocab.json", 
            "tokenizer_config.json",
            "added_tokens.json",
            "special_tokens_map.json"
        ]
        
        missing_files = []
        present_files = []
        
        for file_name in required_files:
            file_path = tokenizer_path / file_name
            if file_path.exists():
                present_files.append(file_name)
            else:
                missing_files.append(file_name)
        
        print(f"  ✅ Present files: {len(present_files)}/{len(required_files)}")
        for file_name in present_files:
            print(f"    • {file_name}")
        
        if missing_files:
            print(f"  ❌ Missing files: {len(missing_files)}")
            for file_name in missing_files:
                print(f"    • {file_name}")
        
        # Check if tokenizer.json contains all added tokens
        tokenizer_json_path = tokenizer_path / "tokenizer.json"
        if tokenizer_json_path.exists():
            with open(tokenizer_json_path, 'r') as f:
                tokenizer_data = json.load(f)
            
            added_tokens_decoder = tokenizer_data.get('added_tokens_decoder', {})
            print(f"  📊 Added tokens in tokenizer.json: {len(added_tokens_decoder):,}")
        
        is_complete = len(missing_files) == 0
        
        if is_complete:
            print("  🎉 Export integrity verification PASSED!")
        else:
            print("  ❌ Export integrity verification FAILED!")
        
        return is_complete
        
    except Exception as e:
        print(f"  ❌ Export integrity verification failed: {e}")
        return False

def main():
    """Main verification function"""
    if len(sys.argv) != 2:
        print("Usage: python verify_tokenizer_supreme.py <tokenizer_path>")
        print("Example: python verify_tokenizer_supreme.py neuroglyph/training/tokenizer_ultimate")
        sys.exit(1)
    
    tokenizer_path = sys.argv[1]
    
    print("🔒 NEUROGLYPH SUPREME TOKENIZER VERIFICATION")
    print("=" * 60)
    print(f"Verifying tokenizer: {tokenizer_path}")
    print()
    
    # Run all verifications
    results = {
        "unicode_collisions": verify_unicode_collisions(tokenizer_path),
        "checksum_integrity": verify_checksum_integrity(tokenizer_path),
        "atomicity_guarantee": verify_atomicity_guarantee(tokenizer_path),
        "export_integrity": verify_export_integrity(tokenizer_path)
    }
    
    print()
    print("📊 VERIFICATION SUMMARY:")
    print("-" * 30)
    
    all_passed = True
    for test_name, result in results.items():
        status = "✅ PASSED" if result else "❌ FAILED"
        print(f"  {test_name:20}: {status}")
        if not result:
            all_passed = False
    
    print()
    if all_passed:
        print("🎉 ALL VERIFICATIONS PASSED!")
        print("🚀 TOKENIZER SUPREME QUALITY CONFIRMED!")
        print("   • Unicode collisions: ZERO")
        print("   • Checksum integrity: VERIFIED")
        print("   • Atomicity guarantee: 100%")
        print("   • Export integrity: COMPLETE")
        sys.exit(0)
    else:
        print("❌ SOME VERIFICATIONS FAILED!")
        print("🔧 Please review and fix issues before proceeding.")
        sys.exit(1)

if __name__ == "__main__":
    main()
