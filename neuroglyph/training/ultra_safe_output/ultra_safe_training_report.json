{"ultra_safe_training": {"status": "completed", "timestamp": 1748393420.554883, "model_name": "Qwen/Qwen2.5-Coder-1.5B-Instruct", "num_runs": 2}, "best_model": {"run_id": "run_2", "overall_score": 0.9102506250924451, "model_path": "neuroglyph/training/ensemble_runs/run_2/final_model", "metrics": {"eval_loss": 0.11156555180121402, "train_loss": 0.2240560806592632, "accuracy": 0.8643933221395361, "symbol_accuracy": 0.8832558058692358, "baseline_preservation": 0.9670152734882437}}, "ensemble_results": {"ensemble_config": {"num_runs": 2, "max_parallel": 1, "seed_base": 42, "min_success_runs": 1, "weight_performance": 0.4, "weight_consistency": 0.3, "weight_reversibility": 0.3, "min_performance_score": 0.8, "min_consistency_score": 0.8, "min_reversibility_score": 0.8}, "total_runs": 2, "successful_runs": 2, "success_rate": 1.0, "best_model": {"run_id": "run_2", "seed": 43, "timestamp": 1748393418.544278, "duration": 2.0067410469055176, "final_metrics": {"eval_loss": 0.11156555180121402, "train_loss": 0.2240560806592632, "accuracy": 0.8643933221395361, "symbol_accuracy": 0.8832558058692358, "baseline_preservation": 0.9670152734882437}, "model_path": "neuroglyph/training/ensemble_runs/run_2/final_model", "validation_passed": "True", "symbol_consistency_score": 0.9135258369739895, "reversibility_score": 0.9520910664419463, "overall_score": 0.9102506250924451}, "aggregate_metrics": {"mean_overall_score": 0.8880192971379746, "std_overall_score": 0.022231327954470526, "mean_consistency": 0.9080556988207356, "mean_reversibility": 0.9391744040932237}, "all_runs": [{"run_id": "run_1", "seed": 42, "timestamp": **********.4342191, "duration": 2.109341859817505, "final_metrics": {"eval_loss": 0.29182803953736514, "train_loss": 0.05625268880566674, "accuracy": 0.8775029318369119, "symbol_accuracy": 0.8401779328667881, "baseline_preservation": 0.9715529849914809}, "model_path": "neuroglyph/training/ensemble_runs/run_1/final_model", "validation_passed": "True", "symbol_consistency_score": 0.9025855606674817, "reversibility_score": 0.926257741744501, "overall_score": 0.8657879691835041}, {"run_id": "run_2", "seed": 43, "timestamp": 1748393418.544278, "duration": 2.0067410469055176, "final_metrics": {"eval_loss": 0.11156555180121402, "train_loss": 0.2240560806592632, "accuracy": 0.8643933221395361, "symbol_accuracy": 0.8832558058692358, "baseline_preservation": 0.9670152734882437}, "model_path": "neuroglyph/training/ensemble_runs/run_2/final_model", "validation_passed": "True", "symbol_consistency_score": 0.9135258369739895, "reversibility_score": 0.9520910664419463, "overall_score": 0.9102506250924451}]}, "safety_status": {"training_active": true, "safety_systems_initialized": true, "baseline_metrics_established": true, "tokenizer_integrity": {"status": "healthy", "latest_validation": {"timestamp": **********.434047, "duration": 0.0010890960693359375, "total_symbols": 66, "failed_symbols": 0, "success_rate": 100.0, "failures": []}, "total_validations": 2, "locked_symbols": 66, "critical_symbols": 66}, "monitoring": {"monitoring_active": true, "total_alerts": 0, "emergency_stops": 0, "recent_alerts": [], "metrics_tracked": 0}, "emergency_protocols": {"safe_checkpoints": 0, "recovery_attempts": 0, "active_recovery": false, "last_checkpoint_step": null, "emergency_actions_available": 6}}, "symbolic_validation": {"reversibility_score": 1.0, "symbol_tests_passed": 9, "code_tests_passed": 3}, "full_validation_results": {"symbol_roundtrip": {"⊃": {"perfect_match": true, "similarity_score": 1.0, "encoded": "encoded_⊃", "decoded": "⊃"}, "∧": {"perfect_match": true, "similarity_score": 1.0, "encoded": "encoded_∧", "decoded": "∧"}, "∨": {"perfect_match": true, "similarity_score": 1.0, "encoded": "encoded_∨", "decoded": "∨"}, "→": {"perfect_match": true, "similarity_score": 1.0, "encoded": "encoded_→", "decoded": "→"}, "∑": {"perfect_match": true, "similarity_score": 1.0, "encoded": "encoded_∑", "decoded": "∑"}, "∫": {"perfect_match": true, "similarity_score": 1.0, "encoded": "encoded_∫", "decoded": "∫"}, "∂": {"perfect_match": true, "similarity_score": 1.0, "encoded": "encoded_∂", "decoded": "∂"}, "≈": {"perfect_match": true, "similarity_score": 1.0, "encoded": "encoded_≈", "decoded": "≈"}, "π": {"perfect_match": true, "similarity_score": 1.0, "encoded": "encoded_π", "decoded": "π"}}, "code_roundtrip": {"def logic_function(a, b): return a ⊃ b": {"symbol_consistency": 1.0, "code_similarity": 0.7, "original_symbols": ["⊃"], "new_symbols": ["⊃"], "regenerated_code": "def function(): # Uses symbols: ⊃"}, "if condition ∧ other_condition: pass": {"symbol_consistency": 1.0, "code_similarity": 0.7, "original_symbols": ["∧"], "new_symbols": ["∧"], "regenerated_code": "def function(): # Uses symbols: ∧"}, "result = ∑(values) → final_output": {"symbol_consistency": 1.0, "code_similarity": 0.7, "original_symbols": ["→", "∑"], "new_symbols": ["→", "∑"], "regenerated_code": "def function(): # Uses symbols: →, ∑"}}, "overall_reversibility": 1.0}, "success_criteria": {"ensemble_success_rate": 1.0, "best_model_score": 0.9102506250924451, "reversibility_score": 1.0, "safety_systems_active": true}}