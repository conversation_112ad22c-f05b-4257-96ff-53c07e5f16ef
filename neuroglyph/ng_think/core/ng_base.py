#!/usr/bin/env python3
"""
NG-THINK Base Classes and Abstract Interfaces
=============================================

Classi base e interfacce astratte per l'architettura cognitiva
simbolica NG-THINK 3 ULTRA.

Autore: NEUROGLYPH NG-THINK Team
Data: 2025-01-27
Licenza: MIT
"""

from abc import ABC, abstractmethod
from typing import Dict, List, Optional, Any, Union
from datetime import datetime
import time
import logging

from .ng_types import (
    NGMessage, NGTraceUnit, NGModuleType, NGSubModuleType,
    NGProcessingStage, NGSubModuleResult, NGThinkConfig
)

logger = logging.getLogger(__name__)

# ============================================================================
# CLASSI BASE ASTRATTE
# ============================================================================

class NGBaseModule(ABC):
    """Classe base astratta per tutti i moduli NG-THINK"""

    def __init__(self, module_type: NGModuleType, config: Optional[Dict[str, Any]] = None):
        self.module_type = module_type
        self.config = config or {}
        self.trace_units: List[NGTraceUnit] = []
        self.is_enabled = self.config.get('enabled', True)
        self.timeout_seconds = self.config.get('timeout_seconds', 30.0)
        self.max_retries = self.config.get('max_retries', 3)
        self.confidence_threshold = self.config.get('confidence_threshold', 0.7)

        # Metriche di performance
        self.performance_metrics: Dict[str, float] = {}
        self.error_count = 0
        self.success_count = 0
        self.total_processing_time = 0.0

        logger.info(f"🧠 Inizializzato modulo {module_type.value}")

    @abstractmethod
    def process(self, message: NGMessage) -> NGMessage:
        """Elabora un messaggio e restituisce il risultato"""
        pass

    def validate_input(self, message: NGMessage) -> bool:
        """Valida l'input del messaggio"""
        if not message:
            return False
        if not hasattr(message, 'content'):
            return False
        return True

    def get_trace_units(self) -> List[NGTraceUnit]:
        """Restituisce le unità di tracciamento"""
        return self.trace_units.copy()

    def reset_state(self) -> None:
        """Reset dello stato interno del modulo"""
        self.trace_units.clear()
        self.error_count = 0
        self.success_count = 0
        self.total_processing_time = 0.0
        self.performance_metrics.clear()
        logger.debug(f"🔄 Reset stato modulo {self.module_type.value}")

    def update_metrics(self, processing_time: float, success: bool):
        """Aggiorna metriche di performance"""
        self.total_processing_time += processing_time

        if success:
            self.success_count += 1
        else:
            self.error_count += 1

        total_operations = self.success_count + self.error_count
        self.performance_metrics.update({
            'success_rate': self.success_count / total_operations if total_operations > 0 else 0,
            'error_rate': self.error_count / total_operations if total_operations > 0 else 0,
            'avg_processing_time': self.total_processing_time / total_operations if total_operations > 0 else 0,
            'total_operations': total_operations
        })

    def get_performance_metrics(self) -> Dict[str, float]:
        """Restituisce metriche di performance"""
        return self.performance_metrics.copy()

    def create_trace_unit(self,
                         operation: str,
                         input_data: Any,
                         output_data: Any,
                         processing_time: float,
                         success: bool,
                         submodule: Optional[NGSubModuleType] = None,
                         error_message: Optional[str] = None,
                         pitfall_detected: Optional[str] = None,
                         fix_applied: Optional[str] = None) -> NGTraceUnit:
        """Crea unità di tracciamento per l'operazione"""

        trace_unit = NGTraceUnit(
            module=self.module_type,
            operation=operation,
            input_data=input_data,
            output_data=output_data,
            processing_time=processing_time,
            success=success,
            submodule=submodule,
            error_message=error_message,
            pitfall_detected=pitfall_detected,
            fix_applied=fix_applied
        )

        self.trace_units.append(trace_unit)
        return trace_unit

    def _add_trace_unit(self, operation: str, input_data: Any, output_data: Any,
                       processing_time: float, success: bool,
                       error_message: Optional[str] = None,
                       submodule: Optional[str] = None) -> None:
        """Aggiunge una unità di tracciamento"""
        trace_unit = NGTraceUnit(
            module=self.module_type,
            submodule=submodule,
            operation=operation,
            input_data=input_data,
            output_data=output_data,
            processing_time=processing_time,
            success=success,
            error_message=error_message
        )
        self.trace_units.append(trace_unit)

    def _execute_with_timeout(self, func, *args, **kwargs):
        """Esegue una funzione con timeout"""
        start_time = time.time()
        try:
            result = func(*args, **kwargs)
            processing_time = time.time() - start_time
            return result, processing_time, None
        except Exception as e:
            processing_time = time.time() - start_time
            return None, processing_time, str(e)

class NGBaseSubModule(ABC):
    """Classe base astratta per sotto-moduli NG-THINK 3 ULTRA"""

    def __init__(self, submodule_type: NGSubModuleType,
                 parent_module: NGModuleType,
                 config: Optional[Dict[str, Any]] = None):
        self.submodule_type = submodule_type
        self.parent_module = parent_module
        self.config = config or {}
        self.is_enabled = self.config.get('enabled', True)

        logger.debug(f"🔧 Inizializzato sotto-modulo {submodule_type.value}")

    @abstractmethod
    def execute(self, input_data: Any) -> NGSubModuleResult:
        """Esegue l'operazione del sotto-modulo"""
        pass

    def validate_input(self, input_data: Any) -> bool:
        """Valida l'input del sotto-modulo"""
        return input_data is not None

    def _create_result(self, success: bool, output_data: Any, confidence: float,
                      processing_time: float, error_message: Optional[str] = None,
                      metadata: Optional[Dict[str, Any]] = None) -> NGSubModuleResult:
        """Crea un risultato standardizzato"""
        return NGSubModuleResult(
            submodule=self.submodule_type,
            success=success,
            output_data=output_data,
            confidence=confidence,
            processing_time=processing_time,
            error_message=error_message,
            metadata=metadata or {}
        )

# ============================================================================
# CONNETTORI E PIPELINE
# ============================================================================

class NGModuleConnector:
    """Connettore per collegare moduli NG-THINK"""

    def __init__(self):
        self.connections: Dict[NGModuleType, List[NGModuleType]] = {}
        self.modules: Dict[NGModuleType, NGBaseModule] = {}

    def register_module(self, module: NGBaseModule) -> None:
        """Registra un modulo nel connettore"""
        self.modules[module.module_type] = module
        if module.module_type not in self.connections:
            self.connections[module.module_type] = []
        logger.info(f"📡 Registrato modulo {module.module_type.value}")

    def connect(self, source: NGModuleType, target: NGModuleType) -> None:
        """Connette due moduli"""
        if source not in self.connections:
            self.connections[source] = []
        self.connections[source].append(target)
        logger.info(f"🔗 Connesso {source.value} → {target.value}")

    def send_message(self, message: NGMessage) -> bool:
        """Invia un messaggio attraverso il connettore"""
        try:
            if message.target_module and message.target_module in self.modules:
                target_module = self.modules[message.target_module]
                if target_module.is_enabled:
                    result = target_module.process(message)
                    return result is not None
            return False
        except Exception as e:
            logger.error(f"❌ Errore invio messaggio: {e}")
            return False

    def get_connection_status(self) -> Dict[str, Any]:
        """Stato delle connessioni"""
        return {
            'total_modules': len(self.modules),
            'total_connections': sum(len(targets) for targets in self.connections.values()),
            'enabled_modules': sum(1 for module in self.modules.values() if module.is_enabled),
            'connections': {source.value: [target.value for target in targets]
                          for source, targets in self.connections.items()}
        }

class NGPipeline:
    """Pipeline di esecuzione per moduli NG-THINK"""

    def __init__(self, config: NGThinkConfig):
        self.config = config
        self.connector = NGModuleConnector()
        self.execution_trace: List[NGTraceUnit] = []
        self.pipeline_stages = [
            NGModuleType.PARSER,
            NGModuleType.CONTEXT_PRIORITIZER,
            NGModuleType.GOAL_PLANNER,
            NGModuleType.MEMORY,
            NGModuleType.REASONER,
            NGModuleType.ENCODER,
            NGModuleType.SELF_CHECK,
            NGModuleType.DECODER
        ]

    def add_module(self, module: NGBaseModule) -> None:
        """Aggiunge un modulo alla pipeline"""
        self.connector.register_module(module)

    def execute_pipeline(self, input_message: NGMessage) -> NGMessage:
        """Esegue l'intera pipeline"""
        start_time = time.time()
        current_message = input_message

        logger.info(f"🚀 Avvio pipeline NG-THINK {self.config.version.value}")

        try:
            for stage in self.pipeline_stages:
                if stage in self.connector.modules:
                    module = self.connector.modules[stage]
                    if module.is_enabled:
                        logger.debug(f"⚙️ Esecuzione stage {stage.value}")
                        current_message.target_module = stage
                        current_message = module.process(current_message)

                        # Aggiungi trace units alla pipeline
                        self.execution_trace.extend(module.get_trace_units())

                        if not current_message:
                            raise Exception(f"Modulo {stage.value} ha restituito None")

            processing_time = time.time() - start_time
            current_message.stage = NGProcessingStage.COMPLETED

            logger.info(f"✅ Pipeline completata in {processing_time:.2f}s")
            return current_message

        except Exception as e:
            processing_time = time.time() - start_time
            logger.error(f"❌ Errore pipeline: {e}")

            # Crea messaggio di errore
            error_message = NGMessage(
                source_module=NGModuleType.PARSER,  # Placeholder
                stage=NGProcessingStage.FAILED,
                content={'error': str(e), 'processing_time': processing_time}
            )
            return error_message

    def get_execution_trace(self) -> List[NGTraceUnit]:
        """Ottiene la traccia di esecuzione completa"""
        return self.execution_trace.copy()

    def reset_pipeline(self) -> None:
        """Reset della pipeline"""
        self.execution_trace.clear()
        for module in self.connector.modules.values():
            module.reset_state()
        logger.info("🔄 Pipeline resettata")
