#!/usr/bin/env python3
"""
NG-THINK Core Types and Data Structures
=======================================

Definisce tutti i tipi, strutture dati e interfacce per l'architettura
cognitiva simbolica NG-THINK.

Autore: NEUROGLYPH NG-THINK Team
Data: 2025-01-27
Licenza: MIT
"""

from typing import Dict, List, Optional, Any, Union, Callable, Protocol, Tuple
from dataclasses import dataclass, field
from enum import Enum
from datetime import datetime
from abc import ABC, abstractmethod
import uuid

# ============================================================================
# ENUMERAZIONI CORE
# ============================================================================

class NGThinkVersion(Enum):
    """Versioni architettura NG-THINK"""
    V1_BASE = "v1_base"
    V2_ANTIFRAGILE = "v2_antifragile"
    V3_ULTRA = "v3_ultra"

class NGModuleType(Enum):
    """Tipi di moduli NG-THINK principali"""
    PARSER = "ng_parser"
    CONTEXT_PRIORITIZER = "ng_context_prioritizer"
    GOAL_PLANNER = "ng_goal_planner"
    MEMORY = "ng_memory"
    MEMORY_DISAMBIGUATOR = "ng_memory_disambiguator"
    REASONER = "ng_reasoner"
    REASONING_KERNEL = "ng_reasoning_kernel"
    ENCODER = "ng_encoder"
    SELF_CHECK = "ng_self_check"
    SANDBOX = "ng_sandbox"
    ADAPTIVE_PATCHER = "ng_adaptive_patcher"
    REINFORCER = "ng_reinforcer"
    CONSISTENCY_ENGINE = "ng_consistency_engine"
    LEARNER = "ng_learner"
    DECODER = "ng_decoder"

class NGSubModuleType(Enum):
    """Sotto-moduli NG-THINK 3 ULTRA micro-modulari"""
    # NG_PARSER sub-modules
    ULTRA_TOKENIZER = "ultra_tokenizer"
    INTENT_SEGMENTER = "intent_segmenter"
    AMBIGUITY_DETECTOR = "ambiguity_detector"

    # NG_CONTEXT_PRIORITIZER sub-modules
    URGENCY_CLASSIFIER = "urgency_classifier"
    RISK_SCORER = "risk_scorer"
    PRIORITY_VECTORIZER = "priority_vectorizer"

    # NG_GOAL_PLANNER sub-modules
    OBJECTIVE_EXTRACTOR = "objective_extractor"
    SUCCESS_METRIC_SETTER = "success_metric_setter"
    PLAN_GENERATOR = "plan_generator"

    # NG_MEMORY sub-modules
    SYMBOL_STORE = "symbol_store"
    EPISODE_CACHE = "episode_cache"
    ERROR_LOG = "error_log"

    # NG_MEMORY_DISAMBIGUATOR sub-modules
    CONFLICT_DETECTOR = "conflict_detector"
    RESOLUTION_ENGINE = "resolution_engine"
    SANITY_COMMITTER = "sanity_committer"

    # NG_REASONER sub-modules
    CHAINER = "chainer"
    ANALOGIZER = "analogizer"
    CONTRADICTION_CHECKER = "contradiction_checker"
    PROB_VERIFIER = "prob_verifier"

    # NG_ENCODER sub-modules
    SYMBOL_MAPPER = "symbol_mapper"
    PATTERN_COMPRESSOR = "pattern_compressor"
    CHECKSUMMER = "checksummer"

    # NG_SELF_CHECK sub-modules
    SEMANTIC_TESTER = "semantic_tester"
    POLICY_FILTER = "policy_filter"
    SCORE_AGGREGATOR = "score_aggregator"

    # NG_SANDBOX sub-modules
    EXEC_VM = "exec_vm"
    SIDE_EFFECT_TRACER = "side_effect_tracer"
    TIMEOUT_GUARD = "timeout_guard"

    # NG_ADAPTIVE_PATCHER sub-modules
    ROOT_CAUSE_MINER = "root_cause_miner"
    PATCH_SYNTHESIZER = "patch_synthesizer"
    ROLLBACK_MANAGER = "rollback_manager"

    # NG_LEARNER sub-modules
    CONFIDENCE_UPDATER = "confidence_updater"
    CURRICULUM_BUILDER = "curriculum_builder"
    WEIGHT_ADAPTER = "weight_adapter"

    # NG_CONSISTENCY_ENGINE sub-modules
    HISTORICAL_QUERY = "historical_query"
    DELTA_DIFF = "delta_diff"
    ALERT_EMITTER = "alert_emitter"

    # NG_DECODER sub-modules
    GLYPH_EXPANDER = "glyph_expander"
    SURFACE_REALIZER = "surface_realizer"
    EXPLANATION_GENERATOR = "explanation_generator"

    # NG_REINFORCER sub-modules
    REWARD_MODEL = "reward_model"
    POLICY_REFINER = "policy_refiner"
    SAFETY_REGULARIZER = "safety_regularizer"

class NGProcessingStage(Enum):
    """Stadi di elaborazione NG-THINK"""
    PARSING = "parsing"
    CONTEXT_ANALYSIS = "context_analysis"
    PRIORITIZATION = "prioritization"
    GOAL_PLANNING = "goal_planning"
    MEMORY_ACCESS = "memory_access"
    REASONING = "reasoning"
    ENCODING = "encoding"
    SELF_CHECK = "self_check"
    SANDBOX = "sandbox"
    PATCHING = "patching"
    LEARNING = "learning"
    DECODING = "decoding"
    COMPLETED = "completed"
    FAILED = "failed"

class NGUrgencyLevel(Enum):
    """Livelli di urgenza"""
    LOW = 0.2
    MEDIUM = 0.5
    HIGH = 0.8
    CRITICAL = 1.0

class NGToneType(Enum):
    """Tipi di tono emotivo"""
    NEUTRAL = "neutral"
    ANALYTICAL = "analytical"
    CREATIVE = "creative"
    URGENT = "urgent"
    EXPLORATORY = "exploratory"
    CRITICAL = "critical"

class NGRiskLevel(Enum):
    """Livelli di rischio per policy compliance"""
    SAFE = 0.1
    LOW_RISK = 0.3
    MEDIUM_RISK = 0.6
    HIGH_RISK = 0.8
    DANGEROUS = 1.0

class NGReasoningOperator(Enum):
    """Operatori di ragionamento simbolico micro-modulari"""
    # Core operators NG-THINK 3 ULTRA
    DEDUCE = "⊢"             # Forward chaining (Horn)
    ABDUCE = "∴"             # Best-explanation search
    NEGATE = "¬"             # Classical negation
    ANALOGIZE = "≈"          # Embedding similarity
    COUNTER_EXAMPLE = "↯"    # Generate failing case

    # Extended operators
    APPROXIMATION = "≃"      # Approssimazione semantica
    CONTRADICTION = "≠"      # Contraddizione rilevata
    ABSTRACTION = "↝"        # Astrazione
    CAUSATION = "→"          # Causalità
    EQUIVALENCE = "≡"        # Equivalenza
    IMPLICATION = "⇒"        # Implicazione logica
    BICONDITIONAL = "⇔"      # Bicondizionale

class NGExecutionStatus(Enum):
    """Stati di esecuzione per sandbox"""
    PENDING = "pending"
    RUNNING = "running"
    COMPLETED = "completed"
    TIMEOUT = "timeout"
    ERROR = "error"
    KILLED = "killed"

class NGPatchType(Enum):
    """Tipi di patch per adaptive patcher"""
    SYNTAX_FIX = "syntax_fix"
    LOGIC_FIX = "logic_fix"
    PERFORMANCE_FIX = "performance_fix"
    SAFETY_FIX = "safety_fix"
    ROLLBACK = "rollback"

# ============================================================================
# STRUTTURE DATI CORE
# ============================================================================

@dataclass
class NGMessage:
    """Messaggio standard tra moduli NG-THINK"""

    source_module: Union[NGModuleType, NGSubModuleType]

    stage: NGProcessingStage

    content: Any

    id: str = field(default_factory=lambda: str(uuid.uuid4()))

    timestamp: datetime = field(default_factory=datetime.now)

    target_module: Optional[Union[NGModuleType, NGSubModuleType]] = None

    metadata: Dict[str, Any] = field(default_factory=dict)

    trace_id: str = field(default_factory=lambda: str(uuid.uuid4()))

    confidence: float = 1.0

    processing_time: float = 0.0

@dataclass
class NGTraceUnit:
    """Unità di tracciamento per debugging e analisi"""

    module: NGModuleType

    operation: str

    input_data: Any

    output_data: Any

    processing_time: float

    success: bool

    id: str = field(default_factory=lambda: str(uuid.uuid4()))

    timestamp: datetime = field(default_factory=datetime.now)

    submodule: Optional[NGSubModuleType] = None

    confidence: float = 1.0

    error_message: Optional[str] = None

    pitfall_detected: Optional[str] = None

    fix_applied: Optional[str] = None

    metadata: Dict[str, Any] = field(default_factory=dict)

@dataclass
class NGReasonNode:
    """Nodo di ragionamento nel grafo simbolico"""

    operator: NGReasoningOperator

    input_symbols: List[str]

    output_symbols: List[str]

    reasoning_rule: str

    confidence: float

    id: str = field(default_factory=lambda: str(uuid.uuid4()))

    dependencies: List[str] = field(default_factory=list)

    metadata: Dict[str, Any] = field(default_factory=dict)

@dataclass
class NGSymbolicChain:
    """Catena di ragionamento simbolico"""

    id: str = field(default_factory=lambda: str(uuid.uuid4()))

    nodes: List[NGReasonNode] = field(default_factory=list)

    start_symbols: List[str] = field(default_factory=list)

    end_symbols: List[str] = field(default_factory=list)

    overall_confidence: float = 0.0

    validation_status: bool = False

    trace_units: List[NGTraceUnit] = field(default_factory=list)

@dataclass
class NGContextFlags:
    """Flag di contesto semantico"""

    urgency: NGUrgencyLevel

    tone: NGToneType

    requires_inference: bool = False

    requires_creativity: bool = False

    requires_safety_check: bool = False

    domain_hints: List[str] = field(default_factory=list)

    complexity_score: float = 0.5

@dataclass
class NGGoalVector:
    """Rappresentazione vettoriale dell'obiettivo"""

    intent_type: str

    semantic_vector: List[float]

    priority_score: float

    expected_output_type: str

    constraints: List[str] = field(default_factory=list)

    success_criteria: List[str] = field(default_factory=list)

@dataclass
class NGMemoryEntry:
    """Voce di memoria semantica"""

    symbols: List[str]

    context: str

    solution: str

    success_rate: float

    id: str = field(default_factory=lambda: str(uuid.uuid4()))

    usage_count: int = 0

    last_accessed: datetime = field(default_factory=datetime.now)

    tags: List[str] = field(default_factory=list)

    relevance_score: float = 0.0


class NGModule(Protocol):
    """Protocollo base per tutti i moduli NG-THINK"""

    def process(self, message: NGMessage) -> NGMessage:
        """Elabora un messaggio e restituisce il risultato"""
        ...

    def validate_input(self, message: NGMessage) -> bool:
        """Valida l'input del messaggio"""
        ...

    def get_trace_units(self) -> List[NGTraceUnit]:
        """Restituisce le unità di tracciamento"""
        ...

    def reset_state(self) -> None:
        """Reset dello stato interno del modulo"""
        ...

class NGConnector(Protocol):
    """Protocollo per connettori tra moduli"""

    def connect(self, source: NGModule, target: NGModule) -> None:
        """Connette due moduli"""
        ...

    def send_message(self, message: NGMessage) -> bool:
        """Invia un messaggio attraverso il connettore"""
        ...

    def get_connection_status(self) -> Dict[str, Any]:
        """Stato della connessione"""
        ...

class NGGraph(Protocol):
    """Protocollo per il grafo di elaborazione"""

    def add_module(self, module: NGModule, module_type: NGModuleType) -> None:
        """Aggiunge un modulo al grafo"""
        ...

    def connect_modules(self, source: NGModuleType, target: NGModuleType) -> None:
        """Connette due moduli nel grafo"""
        ...

    def execute_pipeline(self, input_message: NGMessage) -> NGMessage:
        """Esegue l'intera pipeline"""
        ...

    def get_execution_trace(self) -> List[NGTraceUnit]:
        """Ottiene la traccia di esecuzione completa"""
        ...

# ============================================================================
# RISULTATI E REPORT
# ============================================================================

@dataclass
class NGThinkResult:
    """Risultato completo di un processo NG-THINK"""

    input_prompt: str

    symbolic_chain: NGSymbolicChain

    generated_output: str

    confidence_score: float

    validation_passed: bool

    processing_time: float

    id: str = field(default_factory=lambda: str(uuid.uuid4()))

    trace_log: List[NGTraceUnit] = field(default_factory=list)

    error_corrections: List[str] = field(default_factory=list)

    learned_patterns: List[str] = field(default_factory=list)

    metadata: Dict[str, Any] = field(default_factory=dict)

@dataclass
class NGValidationReport:
    """Report di validazione dettagliato"""

    syntax_valid: bool

    semantic_valid: bool

    causal_valid: bool

    history_consistent: bool

    overall_valid: bool

    error_details: List[str] = field(default_factory=list)

    suggestions: List[str] = field(default_factory=list)

    confidence_breakdown: Dict[str, float] = field(default_factory=dict)

@dataclass
class NGPerformanceMetrics:
    """Metriche di performance del sistema"""

    total_requests: int = 0

    successful_requests: int = 0

    average_processing_time: float = 0.0

    average_confidence: float = 0.0

    error_rate: float = 0.0

    module_performance: Dict[NGModuleType, Dict[str, float]] = field(default_factory=dict)

    memory_usage: Dict[str, float] = field(default_factory=dict)

@dataclass
class NGModuleConfig:
    """Configurazione per un modulo specifico"""

    enabled: bool = True

    timeout_seconds: float = 30.0

    max_retries: int = 3

    confidence_threshold: float = 0.7

    custom_parameters: Dict[str, Any] = field(default_factory=dict)

@dataclass
class NGThinkConfig:
    """Configurazione completa NG-THINK"""

    version: NGThinkVersion = NGThinkVersion.V1_BASE

    module_configs: Dict[NGModuleType, NGModuleConfig] = field(default_factory=dict)

    global_timeout: float = 300.0

    enable_tracing: bool = True

    enable_learning: bool = True

    enable_sandbox: bool = True

    memory_limit_mb: int = 1024

    log_level: str = "INFO"

@dataclass
class NGSubModuleResult:
    """Risultato di un sotto-modulo"""

    submodule: NGSubModuleType

    success: bool

    output_data: Any

    confidence: float

    processing_time: float

    error_message: Optional[str] = None

    metadata: Dict[str, Any] = field(default_factory=dict)

@dataclass
class NGTokenizationResult:
    """Risultato tokenizzazione ultra"""

    tokens: List[str]

    token_types: List[str]  # 'symbol', 'code', 'text', 'emoji'

    semantic_preserved: bool

    oov_tokens: List[str] = field(default_factory=list)

    fallback_used: bool = False

@dataclass
class NGIntentSegment:
    """Segmento di intenzione del prompt"""

    segment_type: str  # 'context', 'question', 'constraint'

    content: str

    confidence: float

    start_pos: int

    end_pos: int

@dataclass
class NGAmbiguityReport:
    """Report di ambiguità rilevate"""

    ambiguous_spans: List[Tuple[int, int]]  # (start, end) positions

    entropy_scores: List[float]

    suggestions: List[str] = field(default_factory=list)

    overall_clarity: float = 0.0

@dataclass
class NGUrgencyAnalysis:
    """Analisi urgenza del prompt"""

    urgency_level: NGUrgencyLevel

    urgency_indicators: List[str]

    confidence: float

    emotional_tone: NGToneType

@dataclass
class NGRiskAssessment:
    """Valutazione rischio contenuti"""

    risk_level: NGRiskLevel

    risk_factors: List[str]

    policy_violations: List[str] = field(default_factory=list)

    mitigation_suggestions: List[str] = field(default_factory=list)

@dataclass
class NGPriorityVector:
    """Vettore di priorità normalizzato"""

    urgency_score: float

    risk_score: float

    domain_scores: Dict[str, float]

    overall_priority: float

@dataclass
class NGObjective:
    """Obiettivo formalizzato in DSL"""

    objective_type: str

    formal_spec: str

    sub_objectives: List[str] = field(default_factory=list)

    constraints: List[str] = field(default_factory=list)

@dataclass
class NGSuccessMetrics:
    """Metriche di successo per obiettivo"""

    accuracy_threshold: float

    time_limit: float

    quality_criteria: List[str]

    kpi_weights: Dict[str, float] = field(default_factory=dict)

@dataclass
class NGExecutionPlan:
    """Piano di esecuzione DAG"""

    plan_id: str = field(default_factory=lambda: str(uuid.uuid4()))

    sub_tasks: List[Dict[str, Any]] = field(default_factory=list)

    dependencies: Dict[str, List[str]] = field(default_factory=dict)

    estimated_time: float = 0.0

    complexity_score: float = 0.0


# ============================================================================
# CONTRATTI INTERFACCE NG-THINK v3.0 ULTRA - MODULO PER MODULO
# ============================================================================

@dataclass
class ParsedPrompt:
    """Risultato parsing completo del prompt (NG_PARSER output)"""
    tokens: List[str]
    token_types: List[str]
    segments: List[NGIntentSegment]
    intents: List[str]
    ambiguity_report: NGAmbiguityReport
    semantic_preserved: bool
    confidence: float
    metadata: Dict[str, Any] = field(default_factory=dict)

@dataclass
class PriorityVector:
    """Vettore priorità contesto (NG_CONTEXT_PRIORITIZER output)"""
    urgency: float  # 0.0-1.0
    risk: float     # 0.0-1.0
    domain: str     # "general", "technical", "creative", etc.
    complexity: float
    estimated_time: float
    flags: NGContextFlags
    confidence: float
    metadata: Dict[str, Any] = field(default_factory=dict)

@dataclass
class MemoryContext:
    """Contesto memoria recuperato (NG_MEMORY output)"""
    examples: List[NGMemoryEntry]
    errors: List[str]
    symbols: List[str]
    relevant_cases: List[Dict[str, Any]]
    confidence: float
    retrieval_time: float
    metadata: Dict[str, Any] = field(default_factory=dict)

@dataclass
class ReasoningGraph:
    """Grafo di ragionamento simbolico (NG_REASONER output)"""
    nodes: List[NGReasonNode]
    edges: List[Tuple[str, str]]  # (source_id, target_id)
    root_nodes: List[str]
    leaf_nodes: List[str]
    reasoning_chain: NGSymbolicChain
    confidence: float
    depth: int
    metadata: Dict[str, Any] = field(default_factory=dict)

@dataclass
class CheckResult:
    """Risultato verifica (NG_SELF_CHECK output)"""
    score: float  # 0.0-1.0
    issues: List[str]
    passed: bool
    syntax_valid: bool
    semantic_valid: bool
    causal_valid: bool
    suggestions: List[str]
    confidence: float
    metadata: Dict[str, Any] = field(default_factory=dict)

@dataclass
class NGOutput:
    """Output finale (NG_DECODER output)"""
    text: str
    glyphs: List[str]
    trace_id: str
    reasoning_trace: List[str]
    confidence: float
    generation_time: float
    validation_passed: bool
    metadata: Dict[str, Any] = field(default_factory=dict)

# ============================================================================
# INTERFACCE ASTRATTE PER OGNI MODULO
# ============================================================================

class NGParserInterface(Protocol):
    """Interfaccia contratto NG_PARSER"""

    def parse(self, prompt: str) -> ParsedPrompt:
        """
        Parse del prompt in struttura simbolica

        Args:
            prompt: Testo input da parsare

        Returns:
            ParsedPrompt con tokens, segments, intents
        """
        ...

class NGContextPrioritizerInterface(Protocol):
    """Interfaccia contratto NG_CONTEXT_PRIORITIZER"""

    def prioritize(self, parsed: ParsedPrompt) -> PriorityVector:
        """
        Prioritizzazione contesto e urgenza

        Args:
            parsed: Prompt già parsato

        Returns:
            PriorityVector con urgency, risk, domain
        """
        ...

class NGMemoryInterface(Protocol):
    """Interfaccia contratto NG_MEMORY"""

    def retrieve(self, priority: PriorityVector, parsed: ParsedPrompt) -> MemoryContext:
        """
        Retrieval memoria simbolica

        Args:
            priority: Vettore priorità
            parsed: Prompt parsato

        Returns:
            MemoryContext con examples, errors, symbols
        """
        ...

class NGReasonerInterface(Protocol):
    """Interfaccia contratto NG_REASONER"""

    def reason(self, memory: MemoryContext, parsed: ParsedPrompt) -> ReasoningGraph:
        """
        Ragionamento simbolico multi-hop

        Args:
            memory: Contesto memoria
            parsed: Prompt parsato

        Returns:
            ReasoningGraph con nodi e catene simboliche
        """
        ...

class NGSelfCheckInterface(Protocol):
    """Interfaccia contratto NG_SELF_CHECK"""

    def self_check(self, graph: ReasoningGraph) -> CheckResult:
        """
        Verifica coerenza ragionamento

        Args:
            graph: Grafo di ragionamento

        Returns:
            CheckResult con score e issues
        """
        ...

class NGDecoderInterface(Protocol):
    """Interfaccia contratto NG_DECODER"""

    def decode(self, graph: ReasoningGraph) -> NGOutput:
        """
        Decodifica grafo in output finale

        Args:
            graph: Grafo di ragionamento validato

        Returns:
            NGOutput con text, glyphs, trace_id
        """
        ...
