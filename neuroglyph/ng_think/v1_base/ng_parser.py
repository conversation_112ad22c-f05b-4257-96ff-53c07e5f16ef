#!/usr/bin/env python3
"""
NG-THINK v3.0 ULTRA - NG_PARSER Module
=====================================

Modulo di parsing simbolico avanzato con decomposizione micro-modulare:
- UltraTokenizer: Tokenizzazione preservando semantica
- IntentSegmenter: Separazione contesto/domanda/vincoli
- AmbiguityDetector: Rilevamento ambiguità

Autore: NEUROGLYPH NG-THINK Team
Data: 2025-01-27
Licenza: MIT
"""

import re
import time
import logging
from typing import Dict, List, Optional, Any, Tuple
from dataclasses import dataclass

from ..core.ng_base import NGBaseModule, NGBaseSubModule
from ..core.ng_types import (
    NGMessage, NGModuleType, NGSubModuleType, NGProcessingStage,
    NGTokenizationResult, NGIntentSegment, NGAmbiguityReport,
    NGSubModuleResult, ParsedPrompt, NGParserInterface
)

logger = logging.getLogger(__name__)

# ============================================================================
# SOTTOMODULI NG_PARSER
# ============================================================================

class UltraTokenizer(NGBaseSubModule):
    """
    1.1 UltraTokenizer - Tokenizzazione preservando semantica

    Goal: Tokenizzare preservando semantica (emoji, codice, unità)
    How: SentencePiece + tavola simboli NG
    Pitfall: Token OOD, segmentazione ambigua
    Fix: Finetune con vocab misto ≈ 90k + fallback char
    """

    def __init__(self, config: Optional[Dict[str, Any]] = None):
        super().__init__(
            NGSubModuleType.ULTRA_TOKENIZER,
            NGModuleType.PARSER,
            config
        )

        # Configurazione tokenizer
        self.vocab_size = self.config.get('vocab_size', 90000)
        self.preserve_symbols = self.config.get('preserve_symbols', True)
        self.fallback_char_level = self.config.get('fallback_char_level', True)

        # Pattern per preservare semantica
        self.semantic_patterns = {
            'neuroglyph_symbol': r'[⊢∴¬≈↯→≡⇒⇔≃≠↝⊕⊗⊙⊚⊛⊜⊝⊞⊟⊠⊡]',
            'emoji': r'[\U0001F600-\U0001F64F\U0001F300-\U0001F5FF\U0001F680-\U0001F6FF\U0001F1E0-\U0001F1FF]',
            'code_block': r'```[\s\S]*?```|`[^`]+`',
            'math_expr': r'\$[^$]+\$|\\\([^)]+\\\)|\\\[[^\]]+\\\]',
            'url': r'https?://[^\s]+',
            'email': r'[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}',
            'number': r'\b\d+\.?\d*\b',
            'special_units': r'\b\d+\s*(kg|m|s|Hz|°C|%)\b'
        }

        logger.info("🔤 UltraTokenizer inizializzato")

    def detect_pitfall(self, input_data: Any) -> Optional[str]:
        """Rileva pitfall di tokenizzazione"""
        if not isinstance(input_data, str):
            return "Input non è stringa"

        text = input_data

        # Pitfall 1: Token OOD (Out of Domain)
        oov_ratio = self._calculate_oov_ratio(text)
        if oov_ratio > 0.3:
            return f"Alto ratio OOV: {oov_ratio:.2f}"

        # Pitfall 2: Segmentazione ambigua
        if self._has_ambiguous_segmentation(text):
            return "Segmentazione ambigua rilevata"

        # Pitfall 3: Perdita semantica
        if self._semantic_loss_risk(text):
            return "Rischio perdita semantica"

        return None

    def apply_fix(self, input_data: Any, pitfall: str) -> Tuple[Any, str]:
        """Applica fix per pitfall rilevati"""
        text = input_data

        if "Alto ratio OOV" in pitfall:
            # Fix: Fallback char-level per token OOV
            text = self._apply_char_fallback(text)
            return text, "Applicato fallback char-level"

        elif "Segmentazione ambigua" in pitfall:
            # Fix: Pre-processing con delimitatori
            text = self._add_segmentation_hints(text)
            return text, "Aggiunti hint di segmentazione"

        elif "Rischio perdita semantica" in pitfall:
            # Fix: Protezione elementi semantici
            text = self._protect_semantic_elements(text)
            return text, "Protetti elementi semantici"

        return text, "Nessun fix applicato"

    def execute(self, input_data: Any, **kwargs) -> NGSubModuleResult:
        """Esegue tokenizzazione ultra"""
        start_time = time.time()

        try:
            text = str(input_data)

            # 1. Pre-processing semantico
            protected_text, protection_map = self._protect_semantic_elements(text)

            # 2. Tokenizzazione base
            tokens = self._tokenize_base(protected_text)

            # 3. Post-processing: ripristina elementi protetti
            final_tokens = self._restore_protected_elements(tokens, protection_map)

            # 4. Classifica tipi di token
            token_types = self._classify_token_types(final_tokens)

            # 5. Verifica preservazione semantica
            semantic_preserved = self._verify_semantic_preservation(text, final_tokens)

            # 6. Identifica token OOV
            oov_tokens = self._identify_oov_tokens(final_tokens)

            result = NGTokenizationResult(
                tokens=final_tokens,
                token_types=token_types,
                semantic_preserved=semantic_preserved,
                oov_tokens=oov_tokens,
                fallback_used=len(oov_tokens) > 0
            )

            processing_time = time.time() - start_time
            confidence = 1.0 - (len(oov_tokens) / len(final_tokens)) if final_tokens else 0.0

            return NGSubModuleResult(
                submodule=self.submodule_type,
                success=True,
                output_data=result,
                confidence=confidence,
                processing_time=processing_time,
                metadata={
                    'total_tokens': len(final_tokens),
                    'oov_count': len(oov_tokens),
                    'semantic_preserved': semantic_preserved
                }
            )

        except Exception as e:
            processing_time = time.time() - start_time
            return NGSubModuleResult(
                submodule=self.submodule_type,
                success=False,
                output_data=None,
                confidence=0.0,
                processing_time=processing_time,
                error_message=str(e)
            )

    def _calculate_oov_ratio(self, text: str) -> float:
        """Calcola ratio di token Out-of-Vocabulary"""
        # Simulazione: in implementazione reale userebbe vocab del modello
        words = text.split()
        if not words:
            return 0.0

        # Stima euristica: parole molto lunghe o con caratteri speciali
        oov_count = sum(1 for word in words if len(word) > 15 or
                       any(ord(c) > 127 for c in word))

        return oov_count / len(words)

    def _has_ambiguous_segmentation(self, text: str) -> bool:
        """Rileva segmentazione ambigua"""
        # Pattern che possono causare ambiguità
        ambiguous_patterns = [
            r'\w+\.\w+',  # parole.con.punti
            r'\w+/\w+',   # parole/con/slash
            r'\w+-\w+',   # parole-con-trattini
            r'\w+_\w+',   # parole_con_underscore
        ]

        for pattern in ambiguous_patterns:
            if re.search(pattern, text):
                return True

        return False

    def _semantic_loss_risk(self, text: str) -> bool:
        """Rileva rischio perdita semantica"""
        # Verifica presenza elementi semantici critici
        for pattern_name, pattern in self.semantic_patterns.items():
            if re.search(pattern, text):
                return True

        return False

    def _apply_char_fallback(self, text: str) -> str:
        """Applica fallback char-level per token problematici"""
        # Identifica token problematici e li separa carattere per carattere
        words = text.split()
        processed_words = []

        for word in words:
            if len(word) > 15 or any(ord(c) > 127 for c in word):
                # Separa carattere per carattere con marker speciale
                char_tokens = [f"<CHAR>{c}</CHAR>" for c in word]
                processed_words.extend(char_tokens)
            else:
                processed_words.append(word)

        return ' '.join(processed_words)

    def _add_segmentation_hints(self, text: str) -> str:
        """Aggiunge hint per migliorare segmentazione"""
        # Aggiunge spazi attorno a delimitatori problematici
        text = re.sub(r'([./_-])', r' \1 ', text)
        # Rimuove spazi multipli
        text = re.sub(r'\s+', ' ', text)
        return text.strip()

    def _protect_semantic_elements(self, text: str) -> Tuple[str, Dict[str, str]]:
        """Protegge elementi semantici durante tokenizzazione"""
        protection_map = {}
        protected_text = text

        for pattern_name, pattern in self.semantic_patterns.items():
            matches = re.finditer(pattern, protected_text)
            for i, match in enumerate(matches):
                placeholder = f"<PROTECTED_{pattern_name.upper()}_{i}>"
                protection_map[placeholder] = match.group()
                protected_text = protected_text.replace(match.group(), placeholder, 1)

        return protected_text, protection_map

    def _tokenize_base(self, text: str) -> List[str]:
        """Tokenizzazione base (simulata)"""
        # In implementazione reale userebbe SentencePiece o tokenizer del modello
        # Per ora: split semplice con gestione punteggiatura

        # Separa punteggiatura
        text = re.sub(r'([.!?,:;])', r' \1 ', text)
        # Split su whitespace
        tokens = text.split()
        # Rimuove token vuoti
        tokens = [t for t in tokens if t.strip()]

        return tokens

    def _restore_protected_elements(self, tokens: List[str],
                                  protection_map: Dict[str, str]) -> List[str]:
        """Ripristina elementi protetti"""
        restored_tokens = []

        for token in tokens:
            if token in protection_map:
                restored_tokens.append(protection_map[token])
            else:
                restored_tokens.append(token)

        return restored_tokens

    def _classify_token_types(self, tokens: List[str]) -> List[str]:
        """Classifica tipi di token"""
        types = []

        for token in tokens:
            if re.match(self.semantic_patterns['neuroglyph_symbol'], token):
                types.append('symbol')
            elif re.match(self.semantic_patterns['emoji'], token):
                types.append('emoji')
            elif re.match(self.semantic_patterns['code_block'], token):
                types.append('code')
            elif re.match(self.semantic_patterns['number'], token):
                types.append('number')
            else:
                types.append('text')

        return types

    def _verify_semantic_preservation(self, original: str, tokens: List[str]) -> bool:
        """Verifica preservazione semantica"""
        # Verifica che elementi critici siano preservati
        reconstructed = ' '.join(tokens)

        for pattern_name, pattern in self.semantic_patterns.items():
            original_matches = len(re.findall(pattern, original))
            reconstructed_matches = len(re.findall(pattern, reconstructed))

            if original_matches != reconstructed_matches:
                return False

        return True

    def _identify_oov_tokens(self, tokens: List[str]) -> List[str]:
        """Identifica token Out-of-Vocabulary"""
        # Simulazione: in implementazione reale verificherebbe contro vocab
        oov_tokens = []

        for token in tokens:
            # Euristica: token molto lunghi o con caratteri speciali
            if (len(token) > 20 or
                any(ord(c) > 127 for c in token) or
                token.startswith('<CHAR>')):
                oov_tokens.append(token)

        return oov_tokens


class IntentSegmenter(NGBaseSubModule):
    """
    1.2 IntentSegmenter - Separazione parti del prompt

    Goal: Separare parti: contesto, domanda, vincoli
    How: Parsing rule-based (regex) + CRF
    Pitfall: Prompt "run-on"
    Fix: Sliding-window + punteggiatura predittiva
    """

    def __init__(self, config: Optional[Dict[str, Any]] = None):
        super().__init__(
            NGSubModuleType.INTENT_SEGMENTER,
            NGModuleType.PARSER,
            config
        )

        # Pattern per identificare segmenti
        self.segment_patterns = {
            'context': [
                r'^(dato|considerando|nel contesto|premesso che)',
                r'(background|context|given that)',
                r'(sapendo che|tenendo conto)'
            ],
            'question': [
                r'(come|cosa|quando|dove|perché|chi)',
                r'(how|what|when|where|why|who)',
                r'\?',
                r'(puoi|potresti|riesci a)',
                r'(can you|could you|would you)'
            ],
            'constraint': [
                r'(deve|dovrebbe|è necessario)',
                r'(must|should|need to)',
                r'(vincolo|limitazione|requisito)',
                r'(constraint|limitation|requirement)',
                r'(senza|evitando|non)',
                r'(without|avoiding|not)'
            ]
        }

        # Delimitatori di segmento
        self.segment_delimiters = ['.', '!', '?', ';', '\n', ':', '-']

        logger.info("✂️ IntentSegmenter inizializzato")

    def detect_pitfall(self, input_data: Any) -> Optional[str]:
        """Rileva pitfall di segmentazione"""
        if not isinstance(input_data, str):
            return "Input non è stringa"

        text = input_data.strip()

        # Pitfall 1: Prompt "run-on" (troppo lungo senza delimitatori)
        if self._is_run_on_prompt(text):
            return "Prompt run-on rilevato"

        # Pitfall 2: Mancanza di delimitatori chiari
        if self._lacks_clear_delimiters(text):
            return "Mancanza delimitatori chiari"

        # Pitfall 3: Segmenti sovrapposti
        if self._has_overlapping_segments(text):
            return "Segmenti sovrapposti rilevati"

        return None

    def apply_fix(self, input_data: Any, pitfall: str) -> Tuple[Any, str]:
        """Applica fix per pitfall rilevati"""
        text = input_data

        if "Prompt run-on" in pitfall:
            # Fix: Sliding-window + punteggiatura predittiva
            text = self._apply_sliding_window_segmentation(text)
            return text, "Applicata segmentazione sliding-window"

        elif "Mancanza delimitatori" in pitfall:
            # Fix: Inserimento delimitatori predittivi
            text = self._add_predictive_punctuation(text)
            return text, "Aggiunti delimitatori predittivi"

        elif "Segmenti sovrapposti" in pitfall:
            # Fix: Disambiguazione segmenti
            text = self._disambiguate_segments(text)
            return text, "Disambiguati segmenti sovrapposti"

        return text, "Nessun fix applicato"

    def execute(self, input_data: Any, **kwargs) -> NGSubModuleResult:
        """Esegue segmentazione intent"""
        start_time = time.time()

        try:
            text = str(input_data)

            # 1. Pre-processing: normalizzazione
            normalized_text = self._normalize_text(text)

            # 2. Identificazione candidati segmenti
            candidate_segments = self._identify_candidate_segments(normalized_text)

            # 3. Classificazione segmenti
            classified_segments = self._classify_segments(candidate_segments)

            # 4. Post-processing: validazione e merge
            final_segments = self._validate_and_merge_segments(classified_segments)

            # 5. Calcola confidence
            confidence = self._calculate_segmentation_confidence(final_segments)

            processing_time = time.time() - start_time

            return NGSubModuleResult(
                submodule=self.submodule_type,
                success=True,
                output_data=final_segments,
                confidence=confidence,
                processing_time=processing_time,
                metadata={
                    'total_segments': len(final_segments),
                    'segment_types': [seg.segment_type for seg in final_segments],
                    'avg_segment_length': sum(len(seg.content) for seg in final_segments) / len(final_segments) if final_segments else 0
                }
            )

        except Exception as e:
            processing_time = time.time() - start_time
            return NGSubModuleResult(
                submodule=self.submodule_type,
                success=False,
                output_data=None,
                confidence=0.0,
                processing_time=processing_time,
                error_message=str(e)
            )

    def _is_run_on_prompt(self, text: str) -> bool:
        """Rileva prompt run-on"""
        # Prompt troppo lungo senza delimitatori
        if len(text) > 200:
            delimiter_count = sum(text.count(d) for d in self.segment_delimiters)
            if delimiter_count < len(text) / 100:  # Meno di 1 delimitatore ogni 100 caratteri
                return True
        return False

    def _lacks_clear_delimiters(self, text: str) -> bool:
        """Rileva mancanza delimitatori chiari"""
        delimiter_count = sum(text.count(d) for d in self.segment_delimiters)
        return delimiter_count == 0 and len(text) > 50

    def _has_overlapping_segments(self, text: str) -> bool:
        """Rileva segmenti sovrapposti"""
        # Verifica se ci sono pattern di tipi diversi nella stessa frase
        sentences = re.split(r'[.!?]', text)

        for sentence in sentences:
            if len(sentence.strip()) < 10:
                continue

            types_found = []
            for segment_type, patterns in self.segment_patterns.items():
                for pattern in patterns:
                    if re.search(pattern, sentence, re.IGNORECASE):
                        types_found.append(segment_type)
                        break

            if len(set(types_found)) > 1:
                return True

        return False

    def _apply_sliding_window_segmentation(self, text: str) -> str:
        """Applica segmentazione sliding-window"""
        # Divide il testo in finestre sovrapposte e cerca punti di segmentazione
        window_size = 100
        overlap = 20

        segments = []
        for i in range(0, len(text), window_size - overlap):
            window = text[i:i + window_size]

            # Cerca punto di segmentazione naturale nella finestra
            best_split = self._find_best_split_point(window)
            if best_split > 0:
                segments.append(window[:best_split])
                text = text[i + best_split:]
                break
            else:
                segments.append(window)

        return '. '.join(segments)

    def _add_predictive_punctuation(self, text: str) -> str:
        """Aggiunge punteggiatura predittiva"""
        # Identifica punti dove aggiungere punteggiatura

        # Dopo congiunzioni
        text = re.sub(r'(quindi|pertanto|inoltre|tuttavia)(\s+)', r'\1.\2', text)

        # Prima di domande
        for pattern in self.segment_patterns['question']:
            text = re.sub(f'(\\w)\\s+({pattern})', r'\1. \2', text, flags=re.IGNORECASE)

        # Prima di vincoli
        for pattern in self.segment_patterns['constraint']:
            text = re.sub(f'(\\w)\\s+({pattern})', r'\1. \2', text, flags=re.IGNORECASE)

        return text

    def _disambiguate_segments(self, text: str) -> str:
        """Disambigua segmenti sovrapposti"""
        # Separa segmenti sovrapposti con delimitatori espliciti
        sentences = re.split(r'([.!?])', text)

        disambiguated = []
        for i, sentence in enumerate(sentences):
            if i % 2 == 1:  # È un delimitatore
                disambiguated.append(sentence)
                continue

            # Verifica sovrapposizione
            types_found = []
            for segment_type, patterns in self.segment_patterns.items():
                for pattern in patterns:
                    if re.search(pattern, sentence, re.IGNORECASE):
                        types_found.append(segment_type)
                        break

            if len(set(types_found)) > 1:
                # Separa in base al primo pattern trovato
                first_type = types_found[0]
                sentence = f"[{first_type.upper()}] {sentence}"

            disambiguated.append(sentence)

        return ''.join(disambiguated)

    def _normalize_text(self, text: str) -> str:
        """Normalizza il testo"""
        # Rimuove spazi multipli
        text = re.sub(r'\s+', ' ', text)
        # Normalizza punteggiatura
        text = re.sub(r'([.!?])\s*', r'\1 ', text)
        return text.strip()

    def _identify_candidate_segments(self, text: str) -> List[str]:
        """Identifica segmenti candidati"""
        # Split su delimitatori principali
        segments = []

        # Prima prova con delimitatori forti
        parts = re.split(r'([.!?])', text)

        current_segment = ""
        for i, part in enumerate(parts):
            if i % 2 == 0:  # Testo
                current_segment += part
            else:  # Delimitatore
                current_segment += part
                if current_segment.strip():
                    segments.append(current_segment.strip())
                current_segment = ""

        # Aggiungi ultimo segmento se presente
        if current_segment.strip():
            segments.append(current_segment.strip())

        # Se non ci sono segmenti, usa delimitatori deboli
        if len(segments) <= 1:
            segments = re.split(r'[;:\n]', text)
            segments = [s.strip() for s in segments if s.strip()]

        return segments

    def _classify_segments(self, segments: List[str]) -> List[NGIntentSegment]:
        """Classifica i segmenti"""
        classified = []

        for i, segment in enumerate(segments):
            segment_type = self._classify_single_segment(segment)
            confidence = self._calculate_classification_confidence(segment, segment_type)

            intent_segment = NGIntentSegment(
                segment_type=segment_type,
                content=segment,
                confidence=confidence,
                start_pos=0,  # Semplificato per ora
                end_pos=len(segment)
            )

            classified.append(intent_segment)

        return classified

    def _classify_single_segment(self, segment: str) -> str:
        """Classifica un singolo segmento"""
        scores = {'context': 0, 'question': 0, 'constraint': 0}

        for segment_type, patterns in self.segment_patterns.items():
            for pattern in patterns:
                matches = len(re.findall(pattern, segment, re.IGNORECASE))
                scores[segment_type] += matches

        # Restituisce il tipo con score più alto
        if max(scores.values()) == 0:
            return 'context'  # Default

        return max(scores, key=scores.get)

    def _calculate_classification_confidence(self, segment: str, segment_type: str) -> float:
        """Calcola confidence della classificazione"""
        total_matches = 0
        type_matches = 0

        for stype, patterns in self.segment_patterns.items():
            for pattern in patterns:
                matches = len(re.findall(pattern, segment, re.IGNORECASE))
                total_matches += matches
                if stype == segment_type:
                    type_matches += matches

        if total_matches == 0:
            return 0.5  # Confidence neutrale

        return type_matches / total_matches

    def _validate_and_merge_segments(self, segments: List[NGIntentSegment]) -> List[NGIntentSegment]:
        """Valida e merge segmenti se necessario"""
        if not segments:
            return segments

        # Merge segmenti consecutivi dello stesso tipo se troppo corti
        merged = []
        current_segment = segments[0]

        for next_segment in segments[1:]:
            if (current_segment.segment_type == next_segment.segment_type and
                len(current_segment.content) < 30):
                # Merge
                current_segment = NGIntentSegment(
                    segment_type=current_segment.segment_type,
                    content=current_segment.content + " " + next_segment.content,
                    confidence=(current_segment.confidence + next_segment.confidence) / 2,
                    start_pos=current_segment.start_pos,
                    end_pos=next_segment.end_pos
                )
            else:
                merged.append(current_segment)
                current_segment = next_segment

        merged.append(current_segment)
        return merged

    def _calculate_segmentation_confidence(self, segments: List[NGIntentSegment]) -> float:
        """Calcola confidence complessiva della segmentazione"""
        if not segments:
            return 0.0

        # Media pesata delle confidence individuali
        total_length = sum(len(seg.content) for seg in segments)
        if total_length == 0:
            return 0.0

        weighted_confidence = sum(
            seg.confidence * len(seg.content) for seg in segments
        ) / total_length

        return weighted_confidence

    def _find_best_split_point(self, window: str) -> int:
        """Trova il miglior punto di split in una finestra"""
        # Cerca delimitatori naturali
        for delimiter in self.segment_delimiters:
            pos = window.rfind(delimiter)
            if pos > len(window) * 0.3:  # Almeno al 30% della finestra
                return pos + 1

        # Se non trova delimitatori, cerca spazi dopo parole complete
        words = window.split()
        if len(words) > 3:
            # Prende i primi 2/3 delle parole
            split_words = words[:len(words) * 2 // 3]
            return len(' '.join(split_words))

        return 0


class AmbiguityDetector(NGBaseSubModule):
    """
    1.3 AmbiguityDetector - Rilevamento ambiguità

    Goal: Segnalare parti poco chiare
    How: Entropia di sequenza > τ
    Pitfall: Falsi positivi su testo tecnico
    Fix: White-list domini tecnici
    """

    def __init__(self, config: Optional[Dict[str, Any]] = None):
        super().__init__(
            NGSubModuleType.AMBIGUITY_DETECTOR,
            NGModuleType.PARSER,
            config
        )

        # Configurazione rilevamento ambiguità
        self.entropy_threshold = self.config.get('entropy_threshold', 0.7)
        self.min_span_length = self.config.get('min_span_length', 5)
        self.max_span_length = self.config.get('max_span_length', 50)

        # White-list domini tecnici per ridurre falsi positivi
        self.technical_domains = {
            'programming': [
                'function', 'class', 'method', 'variable', 'array', 'object',
                'algorithm', 'data structure', 'API', 'framework', 'library'
            ],
            'mathematics': [
                'equation', 'formula', 'theorem', 'proof', 'derivative', 'integral',
                'matrix', 'vector', 'polynomial', 'logarithm', 'exponential'
            ],
            'science': [
                'hypothesis', 'experiment', 'analysis', 'measurement', 'observation',
                'theory', 'model', 'simulation', 'correlation', 'causation'
            ],
            'neuroglyph': [
                'symbol', 'glyph', 'tokenizer', 'reasoning', 'validation',
                'semantic', 'symbolic', 'cognitive', 'inference', 'abstraction'
            ]
        }

        # Pattern di ambiguità comuni
        self.ambiguity_patterns = {
            'pronoun_ambiguity': r'\b(it|this|that|they|them|which)\b',
            'vague_quantifiers': r'\b(some|many|few|several|various|multiple)\b',
            'modal_uncertainty': r'\b(might|could|may|possibly|perhaps|maybe)\b',
            'relative_terms': r'\b(better|worse|faster|slower|more|less)\s+(?!than)',
            'undefined_scope': r'\b(everything|anything|something|nothing)\b'
        }

        logger.info("🔍 AmbiguityDetector inizializzato")

    def detect_pitfall(self, input_data: Any) -> Optional[str]:
        """Rileva pitfall di rilevamento ambiguità"""
        if not isinstance(input_data, str):
            return "Input non è stringa"

        text = input_data

        # Pitfall 1: Falsi positivi su testo tecnico
        if self._is_technical_text(text):
            technical_ratio = self._calculate_technical_ratio(text)
            if technical_ratio > 0.4:
                return f"Testo tecnico ad alta densità: {technical_ratio:.2f}"

        # Pitfall 2: Testo troppo corto per analisi entropia
        if len(text.split()) < 5:
            return "Testo troppo corto per analisi entropia"

        # Pitfall 3: Linguaggio formale/matematico
        if self._is_formal_language(text):
            return "Linguaggio formale rilevato"

        return None

    def apply_fix(self, input_data: Any, pitfall: str) -> Tuple[Any, str]:
        """Applica fix per pitfall rilevati"""
        text = input_data

        if "Testo tecnico" in pitfall:
            # Fix: Applica white-list domini tecnici
            # Marca termini tecnici per escluderli dall'analisi
            text = self._mark_technical_terms(text)
            return text, "Applicata white-list domini tecnici"

        elif "Testo troppo corto" in pitfall:
            # Fix: Usa analisi pattern invece di entropia
            return text, "Switched to pattern-based analysis"

        elif "Linguaggio formale" in pitfall:
            # Fix: Riduce soglia entropia per linguaggio formale
            return text, "Ridotta soglia entropia per linguaggio formale"

        return text, "Nessun fix applicato"

    def execute(self, input_data: Any, **kwargs) -> NGSubModuleResult:
        """Esegue rilevamento ambiguità"""
        start_time = time.time()

        try:
            text = str(input_data)

            # 1. Pre-processing: normalizzazione
            normalized_text = self._normalize_for_analysis(text)

            # 2. Analisi entropia per span di testo
            entropy_spans = self._analyze_entropy_spans(normalized_text)

            # 3. Rilevamento pattern di ambiguità
            pattern_ambiguities = self._detect_pattern_ambiguities(normalized_text)

            # 4. Combinazione risultati
            all_ambiguities = self._combine_ambiguity_results(
                entropy_spans, pattern_ambiguities, normalized_text
            )

            # 5. Filtraggio falsi positivi
            filtered_ambiguities = self._filter_false_positives(
                all_ambiguities, normalized_text
            )

            # 6. Generazione suggerimenti
            suggestions = self._generate_clarification_suggestions(
                filtered_ambiguities, normalized_text
            )

            # 7. Calcolo clarity score
            overall_clarity = self._calculate_overall_clarity(
                filtered_ambiguities, normalized_text
            )

            result = NGAmbiguityReport(
                ambiguous_spans=[(amb['start'], amb['end']) for amb in filtered_ambiguities],
                entropy_scores=[amb['entropy'] for amb in filtered_ambiguities],
                suggestions=suggestions,
                overall_clarity=overall_clarity
            )

            processing_time = time.time() - start_time
            confidence = 1.0 - (len(filtered_ambiguities) / max(len(text.split()), 1))

            return NGSubModuleResult(
                submodule=self.submodule_type,
                success=True,
                output_data=result,
                confidence=confidence,
                processing_time=processing_time,
                metadata={
                    'total_ambiguities': len(filtered_ambiguities),
                    'entropy_ambiguities': len(entropy_spans),
                    'pattern_ambiguities': len(pattern_ambiguities),
                    'overall_clarity': overall_clarity
                }
            )

        except Exception as e:
            processing_time = time.time() - start_time
            return NGSubModuleResult(
                submodule=self.submodule_type,
                success=False,
                output_data=None,
                confidence=0.0,
                processing_time=processing_time,
                error_message=str(e)
            )

    def _is_technical_text(self, text: str) -> bool:
        """Verifica se il testo è tecnico"""
        words = text.lower().split()
        technical_count = 0

        for domain, terms in self.technical_domains.items():
            for term in terms:
                technical_count += words.count(term.lower())

        return technical_count > len(words) * 0.1  # >10% parole tecniche

    def _calculate_technical_ratio(self, text: str) -> float:
        """Calcola ratio di termini tecnici"""
        words = text.lower().split()
        if not words:
            return 0.0

        technical_count = 0
        for domain, terms in self.technical_domains.items():
            for term in terms:
                technical_count += words.count(term.lower())

        return technical_count / len(words)

    def _is_formal_language(self, text: str) -> bool:
        """Rileva linguaggio formale/matematico"""
        formal_indicators = [
            r'\b(theorem|lemma|proof|corollary|proposition)\b',
            r'\b(define|definition|let|given|assume)\b',
            r'\b(therefore|thus|hence|consequently)\b',
            r'[∀∃∈∉⊂⊃∩∪∧∨¬→↔]',  # Simboli logici
            r'\$[^$]+\$',  # Formule LaTeX
        ]

        for pattern in formal_indicators:
            if re.search(pattern, text, re.IGNORECASE):
                return True

        return False

    def _mark_technical_terms(self, text: str) -> str:
        """Marca termini tecnici per escluderli dall'analisi"""
        marked_text = text

        for domain, terms in self.technical_domains.items():
            for term in terms:
                pattern = r'\b' + re.escape(term) + r'\b'
                replacement = f"<TECHNICAL_{domain.upper()}>{term}</TECHNICAL_{domain.upper()}>"
                marked_text = re.sub(pattern, replacement, marked_text, flags=re.IGNORECASE)

        return marked_text

    def _normalize_for_analysis(self, text: str) -> str:
        """Normalizza testo per analisi"""
        # Rimuove markup tecnico se presente
        text = re.sub(r'<TECHNICAL_[^>]+>([^<]+)</TECHNICAL_[^>]+>', r'\1', text)

        # Normalizza spazi
        text = re.sub(r'\s+', ' ', text)

        return text.strip()

    def _analyze_entropy_spans(self, text: str) -> List[Dict[str, Any]]:
        """Analizza entropia per span di testo"""
        words = text.split()
        entropy_spans = []

        # Analizza finestre scorrevoli
        for i in range(len(words) - self.min_span_length + 1):
            for span_length in range(self.min_span_length,
                                   min(self.max_span_length, len(words) - i) + 1):
                span_words = words[i:i + span_length]
                span_text = ' '.join(span_words)

                # Calcola entropia della span
                entropy = self._calculate_text_entropy(span_text)

                if entropy > self.entropy_threshold:
                    entropy_spans.append({
                        'start': i,
                        'end': i + span_length,
                        'text': span_text,
                        'entropy': entropy,
                        'type': 'entropy'
                    })

        return entropy_spans

    def _calculate_text_entropy(self, text: str) -> float:
        """Calcola entropia di Shannon del testo"""
        import math
        from collections import Counter

        # Conta caratteri
        char_counts = Counter(text.lower())
        total_chars = len(text)

        if total_chars == 0:
            return 0.0

        # Calcola entropia
        entropy = 0.0
        for count in char_counts.values():
            probability = count / total_chars
            if probability > 0:
                entropy -= probability * math.log2(probability)

        # Normalizza per lunghezza massima teorica
        max_entropy = math.log2(len(char_counts)) if len(char_counts) > 1 else 1

        return entropy / max_entropy if max_entropy > 0 else 0.0

    def _detect_pattern_ambiguities(self, text: str) -> List[Dict[str, Any]]:
        """Rileva ambiguità basate su pattern"""
        pattern_ambiguities = []

        for pattern_name, pattern in self.ambiguity_patterns.items():
            matches = re.finditer(pattern, text, re.IGNORECASE)

            for match in matches:
                pattern_ambiguities.append({
                    'start': match.start(),
                    'end': match.end(),
                    'text': match.group(),
                    'entropy': 0.8,  # Score fisso per pattern
                    'type': pattern_name
                })

        return pattern_ambiguities

    def _combine_ambiguity_results(self, entropy_spans: List[Dict],
                                 pattern_ambiguities: List[Dict],
                                 text: str) -> List[Dict[str, Any]]:
        """Combina risultati di diversi tipi di analisi"""
        all_ambiguities = entropy_spans + pattern_ambiguities

        # Rimuove sovrapposizioni
        merged = []
        all_ambiguities.sort(key=lambda x: x['start'])

        for ambiguity in all_ambiguities:
            # Verifica sovrapposizione con ambiguità già aggiunte
            overlaps = False
            for existing in merged:
                if (ambiguity['start'] < existing['end'] and
                    ambiguity['end'] > existing['start']):
                    # Sovrapposizione: mantieni quella con entropia più alta
                    if ambiguity['entropy'] > existing['entropy']:
                        merged.remove(existing)
                        merged.append(ambiguity)
                    overlaps = True
                    break

            if not overlaps:
                merged.append(ambiguity)

        return merged

    def _filter_false_positives(self, ambiguities: List[Dict],
                               text: str) -> List[Dict[str, Any]]:
        """Filtra falsi positivi"""
        filtered = []

        for ambiguity in ambiguities:
            # Skip se è un termine tecnico noto
            if self._is_known_technical_term(ambiguity['text']):
                continue

            # Skip se è in un contesto chiaramente definito
            if self._has_clear_context(ambiguity, text):
                continue

            # Skip se l'entropia è dovuta a punteggiatura
            if self._is_punctuation_entropy(ambiguity['text']):
                continue

            filtered.append(ambiguity)

        return filtered

    def _is_known_technical_term(self, text: str) -> bool:
        """Verifica se è un termine tecnico noto"""
        text_lower = text.lower()

        for domain, terms in self.technical_domains.items():
            if text_lower in [term.lower() for term in terms]:
                return True

        return False

    def _has_clear_context(self, ambiguity: Dict, full_text: str) -> bool:
        """Verifica se l'ambiguità ha contesto chiaro"""
        # Estrae contesto attorno all'ambiguità
        words = full_text.split()
        start_word = ambiguity['start']
        end_word = ambiguity['end']

        # Contesto: 3 parole prima e dopo
        context_start = max(0, start_word - 3)
        context_end = min(len(words), end_word + 3)
        context = ' '.join(words[context_start:context_end])

        # Verifica presenza di definizioni o chiarimenti
        clarification_patterns = [
            r'(cioè|ovvero|vale a dire|in altre parole)',
            r'(that is|i\.e\.|namely|specifically)',
            r'(definito come|chiamato|noto come)',
            r'(defined as|called|known as)'
        ]

        for pattern in clarification_patterns:
            if re.search(pattern, context, re.IGNORECASE):
                return True

        return False

    def _is_punctuation_entropy(self, text: str) -> bool:
        """Verifica se l'alta entropia è dovuta solo a punteggiatura"""
        # Rimuove punteggiatura e ricalcola entropia
        clean_text = re.sub(r'[^\w\s]', '', text)

        if len(clean_text.strip()) < 3:
            return True

        clean_entropy = self._calculate_text_entropy(clean_text)
        original_entropy = self._calculate_text_entropy(text)

        # Se l'entropia si riduce significativamente, era dovuta a punteggiatura
        return (original_entropy - clean_entropy) > 0.3

    def _generate_clarification_suggestions(self, ambiguities: List[Dict],
                                          text: str) -> List[str]:
        """Genera suggerimenti per chiarire ambiguità"""
        suggestions = []

        for ambiguity in ambiguities:
            ambiguity_type = ambiguity.get('type', 'unknown')
            ambiguous_text = ambiguity['text']

            if ambiguity_type == 'pronoun_ambiguity':
                suggestions.append(f"Sostituire '{ambiguous_text}' con il sostantivo specifico")

            elif ambiguity_type == 'vague_quantifiers':
                suggestions.append(f"Specificare la quantità esatta invece di '{ambiguous_text}'")

            elif ambiguity_type == 'modal_uncertainty':
                suggestions.append(f"Chiarire il grado di certezza di '{ambiguous_text}'")

            elif ambiguity_type == 'relative_terms':
                suggestions.append(f"Aggiungere termine di confronto per '{ambiguous_text}'")

            elif ambiguity_type == 'undefined_scope':
                suggestions.append(f"Definire l'ambito di '{ambiguous_text}'")

            elif ambiguity_type == 'entropy':
                suggestions.append(f"Semplificare o ristrutturare: '{ambiguous_text[:30]}...'")

        return list(set(suggestions))  # Rimuove duplicati

    def _calculate_overall_clarity(self, ambiguities: List[Dict],
                                 text: str) -> float:
        """Calcola score di chiarezza complessiva"""
        if not text:
            return 0.0

        total_words = len(text.split())
        ambiguous_words = sum(
            len(amb['text'].split()) for amb in ambiguities
        )

        # Score basato su ratio di parole ambigue
        clarity_ratio = 1.0 - (ambiguous_words / total_words) if total_words > 0 else 1.0

        # Penalizza per alta entropia media
        if ambiguities:
            avg_entropy = sum(amb['entropy'] for amb in ambiguities) / len(ambiguities)
            entropy_penalty = min(avg_entropy * 0.3, 0.3)
            clarity_ratio -= entropy_penalty

        return max(0.0, min(1.0, clarity_ratio))


# ============================================================================
# MODULO PRINCIPALE NG_PARSER
# ============================================================================

class NGParser(NGBaseModule):
    """
    Modulo principale NG_PARSER v3.0 ULTRA

    Coordina i tre sottomoduli:
    1. UltraTokenizer - Tokenizzazione preservando semantica
    2. IntentSegmenter - Separazione contesto/domanda/vincoli
    3. AmbiguityDetector - Rilevamento ambiguità

    Pipeline: Input → Tokenization → Segmentation → Ambiguity Detection → Output
    """

    def __init__(self, config: Optional[Dict[str, Any]] = None):
        super().__init__(NGModuleType.PARSER, config)

        # Inizializza sottomoduli
        self.ultra_tokenizer = UltraTokenizer(
            self.config.get('ultra_tokenizer', {})
        )
        self.intent_segmenter = IntentSegmenter(
            self.config.get('intent_segmenter', {})
        )
        self.ambiguity_detector = AmbiguityDetector(
            self.config.get('ambiguity_detector', {})
        )

        # Configurazione pipeline
        self.enable_tokenization = self.config.get('enable_tokenization', True)
        self.enable_segmentation = self.config.get('enable_segmentation', True)
        self.enable_ambiguity_detection = self.config.get('enable_ambiguity_detection', True)

        logger.info("🧠 NGParser v3.0 ULTRA inizializzato")

    def parse(self, prompt: str) -> ParsedPrompt:
        """
        Implementa interfaccia NGParserInterface.parse()

        Args:
            prompt: Testo input da parsare

        Returns:
            ParsedPrompt con tokens, segments, intents
        """
        # Crea messaggio per il processo interno
        message = NGMessage(
            source_module=NGModuleType.PARSER,
            stage=NGProcessingStage.PARSING,
            content=prompt
        )

        # Esegue processo interno
        result_message = self.process(message)

        if not result_message.content:
            # Fallback se processo fallisce
            return ParsedPrompt(
                tokens=[],
                token_types=[],
                segments=[],
                intents=[],
                ambiguity_report=NGAmbiguityReport(
                    ambiguous_spans=[],
                    entropy_scores=[],
                    suggestions=[],
                    overall_clarity=0.0
                ),
                semantic_preserved=False,
                confidence=0.0,
                metadata={"error": "parsing_failed"}
            )

        # Estrae risultati dai sottomoduli
        parsing_results = result_message.content

        # Tokenizzazione
        tokens = []
        token_types = []
        semantic_preserved = True
        if 'tokenization' in parsing_results:
            tok_result = parsing_results['tokenization']
            if tok_result.success and tok_result.output_data:
                tokens = tok_result.output_data.tokens
                token_types = tok_result.output_data.token_types
                semantic_preserved = tok_result.output_data.semantic_preserved

        # Segmentazione
        segments = []
        intents = []
        if 'segmentation' in parsing_results:
            seg_result = parsing_results['segmentation']
            if seg_result.success and seg_result.output_data:
                segments = seg_result.output_data
                intents = [seg.segment_type for seg in segments]

        # Ambiguità
        ambiguity_report = NGAmbiguityReport(
            ambiguous_spans=[],
            entropy_scores=[],
            suggestions=[],
            overall_clarity=1.0
        )
        if 'ambiguity' in parsing_results:
            amb_result = parsing_results['ambiguity']
            if amb_result.success and amb_result.output_data:
                ambiguity_report = amb_result.output_data

        # Crea ParsedPrompt finale
        return ParsedPrompt(
            tokens=tokens,
            token_types=token_types,
            segments=segments,
            intents=intents,
            ambiguity_report=ambiguity_report,
            semantic_preserved=semantic_preserved,
            confidence=result_message.confidence,
            metadata={
                "processing_time": result_message.processing_time,
                "submodule_results": {
                    name: result.success for name, result in parsing_results.items()
                }
            }
        )

    def process(self, message: NGMessage) -> NGMessage:
        """
        Elabora il messaggio attraverso la pipeline di parsing.

        Args:
            message: Messaggio contenente il testo da parsare

        Returns:
            Messaggio con risultati di parsing
        """
        start_time = time.time()

        try:
            # Validazione input
            if not self.validate_input(message):
                raise ValueError("Input non valido")

            input_text = str(message.content)
            parsing_results = {}

            # 1. Tokenizzazione Ultra
            if self.enable_tokenization:
                tokenization_result = self.ultra_tokenizer.execute(input_text)
                parsing_results['tokenization'] = tokenization_result

                self.create_trace_unit(
                    operation="ultra_tokenization",
                    input_data=input_text,
                    output_data=tokenization_result.output_data,
                    processing_time=tokenization_result.processing_time,
                    success=tokenization_result.success,
                    submodule=NGSubModuleType.ULTRA_TOKENIZER,
                    error_message=tokenization_result.error_message
                )

            # 2. Segmentazione Intent
            if self.enable_segmentation:
                segmentation_result = self.intent_segmenter.execute(input_text)
                parsing_results['segmentation'] = segmentation_result

                self.create_trace_unit(
                    operation="intent_segmentation",
                    input_data=input_text,
                    output_data=segmentation_result.output_data,
                    processing_time=segmentation_result.processing_time,
                    success=segmentation_result.success,
                    submodule=NGSubModuleType.INTENT_SEGMENTER,
                    error_message=segmentation_result.error_message
                )

            # 3. Rilevamento Ambiguità
            if self.enable_ambiguity_detection:
                ambiguity_result = self.ambiguity_detector.execute(input_text)
                parsing_results['ambiguity'] = ambiguity_result

                self.create_trace_unit(
                    operation="ambiguity_detection",
                    input_data=input_text,
                    output_data=ambiguity_result.output_data,
                    processing_time=ambiguity_result.processing_time,
                    success=ambiguity_result.success,
                    submodule=NGSubModuleType.AMBIGUITY_DETECTOR,
                    error_message=ambiguity_result.error_message
                )

            # 4. Calcola metriche aggregate
            overall_success = all(
                result.success for result in parsing_results.values()
            )

            overall_confidence = sum(
                result.confidence for result in parsing_results.values()
            ) / len(parsing_results) if parsing_results else 0.0

            processing_time = time.time() - start_time

            # 5. Aggiorna metriche modulo
            self.update_metrics(processing_time, overall_success)

            # 6. Crea messaggio di output
            output_message = NGMessage(
                source_module=self.module_type,
                stage=NGProcessingStage.PARSING,
                content=parsing_results,
                metadata={
                    'input_length': len(input_text),
                    'overall_success': overall_success,
                    'overall_confidence': overall_confidence,
                    'submodule_results': {
                        name: {
                            'success': result.success,
                            'confidence': result.confidence,
                            'processing_time': result.processing_time
                        }
                        for name, result in parsing_results.items()
                    }
                },
                confidence=overall_confidence,
                processing_time=processing_time
            )

            logger.info(f"✅ NGParser completato: confidence={overall_confidence:.3f}, time={processing_time:.3f}s")

            return output_message

        except Exception as e:
            processing_time = time.time() - start_time
            self.update_metrics(processing_time, False)

            error_message = NGMessage(
                source_module=self.module_type,
                stage=NGProcessingStage.FAILED,
                content=None,
                metadata={'error': str(e)},
                confidence=0.0,
                processing_time=processing_time
            )

            logger.error(f"❌ NGParser fallito: {e}")

            return error_message

    def get_parsing_summary(self) -> Dict[str, Any]:
        """Restituisce riassunto delle capacità di parsing"""
        return {
            'module_type': self.module_type.value,
            'version': 'v3.0_ULTRA',
            'submodules': {
                'ultra_tokenizer': {
                    'enabled': self.enable_tokenization,
                    'capabilities': [
                        'Preservazione semantica simboli NEUROGLYPH',
                        'Gestione emoji e codice',
                        'Fallback char-level per OOV',
                        'Classificazione tipi token'
                    ]
                },
                'intent_segmenter': {
                    'enabled': self.enable_segmentation,
                    'capabilities': [
                        'Separazione contesto/domanda/vincoli',
                        'Sliding-window per prompt run-on',
                        'Punteggiatura predittiva',
                        'Disambiguazione segmenti'
                    ]
                },
                'ambiguity_detector': {
                    'enabled': self.enable_ambiguity_detection,
                    'capabilities': [
                        'Analisi entropia Shannon',
                        'Pattern recognition ambiguità',
                        'White-list domini tecnici',
                        'Suggerimenti chiarificazione'
                    ]
                }
            },
            'performance_metrics': self.get_performance_metrics(),
            'pitfall_handling': {
                'tokenization': [
                    'Token OOD → Fallback char-level',
                    'Segmentazione ambigua → Hint delimitatori',
                    'Perdita semantica → Protezione elementi'
                ],
                'segmentation': [
                    'Prompt run-on → Sliding-window',
                    'Mancanza delimitatori → Punteggiatura predittiva',
                    'Segmenti sovrapposti → Disambiguazione'
                ],
                'ambiguity': [
                    'Falsi positivi tecnici → White-list domini',
                    'Testo corto → Analisi pattern',
                    'Linguaggio formale → Soglia adattiva'
                ]
            }
        }

    def validate_parsing_quality(self, input_text: str,
                                parsing_results: Dict[str, Any]) -> Dict[str, float]:
        """
        Valida la qualità del parsing effettuato.

        Args:
            input_text: Testo originale
            parsing_results: Risultati del parsing

        Returns:
            Metriche di qualità
        """
        quality_metrics = {}

        # Qualità tokenizzazione
        if 'tokenization' in parsing_results:
            tokenization = parsing_results['tokenization']
            if tokenization.success and tokenization.output_data:
                tokens = tokenization.output_data.tokens
                quality_metrics['tokenization_coverage'] = (
                    len(tokens) / len(input_text.split()) if input_text.split() else 0
                )
                quality_metrics['semantic_preservation'] = (
                    1.0 if tokenization.output_data.semantic_preserved else 0.5
                )
                quality_metrics['oov_ratio'] = (
                    len(tokenization.output_data.oov_tokens) / len(tokens) if tokens else 0
                )

        # Qualità segmentazione
        if 'segmentation' in parsing_results:
            segmentation = parsing_results['segmentation']
            if segmentation.success and segmentation.output_data:
                segments = segmentation.output_data
                quality_metrics['segmentation_completeness'] = (
                    len(segments) / max(input_text.count('.') + input_text.count('?') + 1, 1)
                )
                quality_metrics['segment_confidence'] = (
                    sum(seg.confidence for seg in segments) / len(segments) if segments else 0
                )

        # Qualità rilevamento ambiguità
        if 'ambiguity' in parsing_results:
            ambiguity = parsing_results['ambiguity']
            if ambiguity.success and ambiguity.output_data:
                quality_metrics['clarity_score'] = ambiguity.output_data.overall_clarity
                quality_metrics['ambiguity_density'] = (
                    len(ambiguity.output_data.ambiguous_spans) / len(input_text.split())
                    if input_text.split() else 0
                )

        # Score complessivo
        if quality_metrics:
            quality_metrics['overall_quality'] = sum(quality_metrics.values()) / len(quality_metrics)

        return quality_metrics
