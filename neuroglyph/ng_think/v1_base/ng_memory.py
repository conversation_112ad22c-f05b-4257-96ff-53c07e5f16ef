#!/usr/bin/env python3
"""
NG-THINK v3.0 ULTRA - Memory System
==================================

Modulo NG_MEMORY per gestione memoria simbolica persistente.
Implementa storage LMDB, similarity search FAISS e error logging.

Autore: NEUROGLYPH NG-THINK Team
Data: 2025-01-27
Licenza: MIT
"""

import os
import json
import time
import pickle
import logging
import hashlib
from typing import Dict, List, Optional, Any, Tuple
from dataclasses import dataclass, asdict
from datetime import datetime

import numpy as np

# Conditional imports per dependencies opzionali
try:
    import lmdb
    LMDB_AVAILABLE = True
except ImportError:
    LMDB_AVAILABLE = False
    lmdb = None

try:
    import faiss
    FAISS_AVAILABLE = True
except ImportError:
    FAISS_AVAILABLE = False
    faiss = None

from ..core.ng_base import NGBaseModule, NGBaseSubModule
from ..core.ng_types import (
    NGMessage, NGModuleType, NGSubModuleType, NGProcessingStage,
    NGSubModuleResult, ParsedPrompt, PriorityVector, MemoryContext,
    NGMemoryEntry, NGMemoryInterface
)

logger = logging.getLogger(__name__)

# ============================================================================
# SOTTO-MODULO: SYMBOL STORE (LMDB)
# ============================================================================

class SymbolStore(NGBaseSubModule):
    """
    Storage persistente per simboli NEUROGLYPH usando LMDB.
    
    Gestisce:
    - Mapping simbolo → significato semantico
    - Contesti d'uso per ogni simbolo
    - Statistiche di utilizzo
    - Relazioni tra simboli
    """
    
    def __init__(self, config: Optional[Dict[str, Any]] = None):
        super().__init__(
            NGSubModuleType.SYMBOL_STORE,
            NGModuleType.MEMORY,
            config
        )
        
        # Configurazione LMDB
        self.db_path = self.config.get('db_path', 'data/ng_memory/symbols.lmdb')
        self.map_size = self.config.get('map_size', 1024 * 1024 * 1024)  # 1GB
        self.max_dbs = self.config.get('max_dbs', 10)
        
        # Inizializza database
        self.env = None
        self.symbol_db = None
        self.context_db = None
        self.stats_db = None
        
        self._initialize_lmdb()
        
        logger.debug("🔧 SymbolStore inizializzato")
    
    def _initialize_lmdb(self):
        """Inizializza database LMDB"""
        if not LMDB_AVAILABLE:
            logger.warning("⚠️ LMDB non disponibile, usando fallback in-memory")
            self.symbol_cache = {}
            self.context_cache = {}
            self.stats_cache = {}
            return
        
        try:
            # Crea directory se non esiste
            os.makedirs(os.path.dirname(self.db_path), exist_ok=True)
            
            # Apri environment LMDB
            self.env = lmdb.open(
                self.db_path,
                map_size=self.map_size,
                max_dbs=self.max_dbs,
                sync=True
            )
            
            # Crea database separati
            with self.env.begin(write=True) as txn:
                self.symbol_db = self.env.open_db(b'symbols', txn=txn)
                self.context_db = self.env.open_db(b'contexts', txn=txn)
                self.stats_db = self.env.open_db(b'stats', txn=txn)
            
            logger.info(f"✅ LMDB inizializzato: {self.db_path}")
            
        except Exception as e:
            logger.error(f"❌ Errore inizializzazione LMDB: {e}")
            # Fallback a cache in-memory
            self.env = None
            self.symbol_cache = {}
            self.context_cache = {}
            self.stats_cache = {}
    
    def execute(self, input_data: Any, **kwargs) -> NGSubModuleResult:
        """Esegue operazione su symbol store"""
        start_time = time.time()
        
        try:
            operation = kwargs.get('operation', 'retrieve')
            
            if operation == 'store':
                result = self._store_symbol(input_data)
            elif operation == 'retrieve':
                result = self._retrieve_symbols(input_data)
            elif operation == 'update_stats':
                result = self._update_stats(input_data)
            else:
                raise ValueError(f"Operazione non supportata: {operation}")
            
            processing_time = time.time() - start_time
            
            return self._create_result(
                success=True,
                output_data=result,
                confidence=1.0,
                processing_time=processing_time,
                metadata={'operation': operation}
            )
            
        except Exception as e:
            processing_time = time.time() - start_time
            logger.error(f"❌ Errore SymbolStore: {e}")
            
            return self._create_result(
                success=False,
                output_data=None,
                confidence=0.0,
                processing_time=processing_time,
                error_message=str(e)
            )
    
    def _store_symbol(self, symbol_data: Dict) -> Dict:
        """Memorizza simbolo nel database"""
        symbol = symbol_data.get('symbol')
        context = symbol_data.get('context', '')
        meaning = symbol_data.get('meaning', '')
        
        if not symbol:
            raise ValueError("Simbolo richiesto per storage")
        
        # Crea entry simbolo
        symbol_entry = {
            'symbol': symbol,
            'meaning': meaning,
            'contexts': [context] if context else [],
            'created_at': datetime.now().isoformat(),
            'usage_count': 1
        }
        
        if self.env:
            # Storage LMDB
            with self.env.begin(write=True) as txn:
                # Recupera entry esistente se presente
                existing = txn.get(symbol.encode(), db=self.symbol_db)
                if existing:
                    existing_data = json.loads(existing.decode())
                    existing_data['contexts'].append(context)
                    existing_data['usage_count'] += 1
                    symbol_entry = existing_data
                
                # Salva entry aggiornata
                txn.put(
                    symbol.encode(),
                    json.dumps(symbol_entry).encode(),
                    db=self.symbol_db
                )
        else:
            # Fallback cache
            if symbol in self.symbol_cache:
                self.symbol_cache[symbol]['contexts'].append(context)
                self.symbol_cache[symbol]['usage_count'] += 1
            else:
                self.symbol_cache[symbol] = symbol_entry
        
        return {'stored': True, 'symbol': symbol}
    
    def _retrieve_symbols(self, query_data: Dict) -> Dict:
        """Recupera simboli dal database"""
        symbols = query_data.get('symbols', [])
        domain = query_data.get('domain', '')
        
        results = []
        
        for symbol in symbols:
            if self.env:
                # Retrieval LMDB
                with self.env.begin() as txn:
                    data = txn.get(symbol.encode(), db=self.symbol_db)
                    if data:
                        symbol_data = json.loads(data.decode())
                        results.append(symbol_data)
            else:
                # Fallback cache
                if symbol in self.symbol_cache:
                    results.append(self.symbol_cache[symbol])
        
        return {
            'symbols_found': len(results),
            'symbols_data': results,
            'domain': domain
        }
    
    def _update_stats(self, stats_data: Dict) -> Dict:
        """Aggiorna statistiche utilizzo"""
        symbol = stats_data.get('symbol')
        operation = stats_data.get('operation', 'usage')
        
        if not symbol:
            return {'updated': False}
        
        stats_key = f"{symbol}:{operation}"
        
        if self.env:
            with self.env.begin(write=True) as txn:
                current = txn.get(stats_key.encode(), db=self.stats_db)
                count = 1 if not current else int(current.decode()) + 1
                txn.put(stats_key.encode(), str(count).encode(), db=self.stats_db)
        else:
            current = self.stats_cache.get(stats_key, 0)
            self.stats_cache[stats_key] = current + 1
        
        return {'updated': True, 'symbol': symbol, 'operation': operation}
    
    def close(self):
        """Chiude connessione LMDB"""
        if self.env:
            self.env.close()
            logger.debug("🔒 LMDB connection closed")

# ============================================================================
# SOTTO-MODULO: EPISODE CACHE (FAISS)
# ============================================================================

class EpisodeCache(NGBaseSubModule):
    """
    Cache episodi con similarity search usando FAISS.
    
    Gestisce:
    - Embedding di episodi di ragionamento
    - Similarity search per retrieval
    - Clustering di episodi simili
    - Performance optimization
    """
    
    def __init__(self, config: Optional[Dict[str, Any]] = None):
        super().__init__(
            NGSubModuleType.EPISODE_CACHE,
            NGModuleType.MEMORY,
            config
        )
        
        # Configurazione FAISS
        self.embedding_dim = self.config.get('embedding_dim', 384)
        self.index_type = self.config.get('index_type', 'flat')  # 'flat', 'ivf', 'hnsw'
        self.cache_path = self.config.get('cache_path', 'data/ng_memory/episodes.faiss')
        
        # Inizializza FAISS index
        self.index = None
        self.episode_metadata = []
        self.id_counter = 0
        
        self._initialize_faiss()
        
        logger.debug("🔧 EpisodeCache inizializzato")
    
    def _initialize_faiss(self):
        """Inizializza FAISS index"""
        if not FAISS_AVAILABLE:
            logger.warning("⚠️ FAISS non disponibile, usando fallback lineare")
            self.episodes_cache = []
            return
        
        try:
            # Crea directory se non esiste
            os.makedirs(os.path.dirname(self.cache_path), exist_ok=True)
            
            # Inizializza index FAISS
            if self.index_type == 'flat':
                self.index = faiss.IndexFlatL2(self.embedding_dim)
            elif self.index_type == 'ivf':
                quantizer = faiss.IndexFlatL2(self.embedding_dim)
                self.index = faiss.IndexIVFFlat(quantizer, self.embedding_dim, 100)
            elif self.index_type == 'hnsw':
                self.index = faiss.IndexHNSWFlat(self.embedding_dim, 32)
            
            # Carica index esistente se presente
            if os.path.exists(self.cache_path):
                self.index = faiss.read_index(self.cache_path)
                # Carica metadata
                metadata_path = self.cache_path.replace('.faiss', '_metadata.json')
                if os.path.exists(metadata_path):
                    with open(metadata_path, 'r') as f:
                        self.episode_metadata = json.load(f)
                    self.id_counter = len(self.episode_metadata)
            
            logger.info(f"✅ FAISS inizializzato: {self.index.ntotal} episodi")
            
        except Exception as e:
            logger.error(f"❌ Errore inizializzazione FAISS: {e}")
            self.index = None
            self.episodes_cache = []
    
    def execute(self, input_data: Any, **kwargs) -> NGSubModuleResult:
        """Esegue operazione su episode cache"""
        start_time = time.time()
        
        try:
            operation = kwargs.get('operation', 'search')
            
            if operation == 'add':
                result = self._add_episode(input_data)
            elif operation == 'search':
                result = self._search_episodes(input_data)
            elif operation == 'cluster':
                result = self._cluster_episodes(input_data)
            else:
                raise ValueError(f"Operazione non supportata: {operation}")
            
            processing_time = time.time() - start_time
            
            return self._create_result(
                success=True,
                output_data=result,
                confidence=1.0,
                processing_time=processing_time,
                metadata={'operation': operation}
            )
            
        except Exception as e:
            processing_time = time.time() - start_time
            logger.error(f"❌ Errore EpisodeCache: {e}")
            
            return self._create_result(
                success=False,
                output_data=None,
                confidence=0.0,
                processing_time=processing_time,
                error_message=str(e)
            )
    
    def _add_episode(self, episode_data: Dict) -> Dict:
        """Aggiunge episodio al cache"""
        embedding = episode_data.get('embedding')
        metadata = episode_data.get('metadata', {})
        
        if embedding is None:
            # Genera embedding dummy se non fornito
            embedding = np.random.random(self.embedding_dim).astype('float32')
        
        if isinstance(embedding, list):
            embedding = np.array(embedding, dtype='float32')
        
        if self.index:
            # Aggiungi a FAISS
            self.index.add(embedding.reshape(1, -1))
            
            # Salva metadata
            episode_meta = {
                'id': self.id_counter,
                'timestamp': datetime.now().isoformat(),
                **metadata
            }
            self.episode_metadata.append(episode_meta)
            self.id_counter += 1
            
            # Salva index periodicamente
            if self.id_counter % 100 == 0:
                self._save_index()
        else:
            # Fallback cache
            episode = {
                'id': self.id_counter,
                'embedding': embedding.tolist(),
                'metadata': metadata,
                'timestamp': datetime.now().isoformat()
            }
            self.episodes_cache.append(episode)
            self.id_counter += 1
        
        return {'added': True, 'episode_id': self.id_counter - 1}
    
    def _search_episodes(self, query_data: Dict) -> Dict:
        """Cerca episodi simili"""
        query_embedding = query_data.get('embedding')
        k = query_data.get('k', 5)
        threshold = query_data.get('threshold', 0.8)
        
        if query_embedding is None:
            return {'episodes': [], 'distances': []}
        
        if isinstance(query_embedding, list):
            query_embedding = np.array(query_embedding, dtype='float32')
        
        if self.index and self.index.ntotal > 0:
            # Search FAISS
            distances, indices = self.index.search(query_embedding.reshape(1, -1), k)
            
            results = []
            for i, (dist, idx) in enumerate(zip(distances[0], indices[0])):
                if idx < len(self.episode_metadata):
                    similarity = 1.0 / (1.0 + dist)  # Convert distance to similarity
                    if similarity >= threshold:
                        episode = self.episode_metadata[idx].copy()
                        episode['similarity'] = similarity
                        episode['distance'] = float(dist)
                        results.append(episode)
            
            return {
                'episodes': results,
                'total_found': len(results),
                'search_time': time.time()
            }
        else:
            # Fallback linear search
            results = []
            for episode in self.episodes_cache:
                # Calcola similarity coseno semplificata
                ep_emb = np.array(episode['embedding'])
                similarity = np.dot(query_embedding, ep_emb) / (
                    np.linalg.norm(query_embedding) * np.linalg.norm(ep_emb)
                )
                
                if similarity >= threshold:
                    episode_copy = episode.copy()
                    episode_copy['similarity'] = float(similarity)
                    results.append(episode_copy)
            
            # Ordina per similarity
            results.sort(key=lambda x: x['similarity'], reverse=True)
            
            return {
                'episodes': results[:k],
                'total_found': len(results)
            }
    
    def _cluster_episodes(self, cluster_data: Dict) -> Dict:
        """Raggruppa episodi simili"""
        n_clusters = cluster_data.get('n_clusters', 10)
        
        if not self.index or self.index.ntotal < n_clusters:
            return {'clusters': [], 'cluster_count': 0}
        
        # Implementazione clustering semplificata
        # In produzione usare K-means o clustering più sofisticato
        return {
            'clusters': [],
            'cluster_count': 0,
            'message': 'Clustering non implementato in questa versione'
        }
    
    def _save_index(self):
        """Salva index FAISS su disco"""
        if self.index:
            try:
                faiss.write_index(self.index, self.cache_path)
                
                # Salva metadata
                metadata_path = self.cache_path.replace('.faiss', '_metadata.json')
                with open(metadata_path, 'w') as f:
                    json.dump(self.episode_metadata, f)
                
                logger.debug(f"💾 FAISS index salvato: {self.index.ntotal} episodi")
            except Exception as e:
                logger.error(f"❌ Errore salvataggio FAISS: {e}")
    
    def close(self):
        """Chiude e salva index"""
        self._save_index()
        logger.debug("🔒 EpisodeCache closed")

# ============================================================================
# SOTTO-MODULO: ERROR LOG
# ============================================================================

class ErrorLog(NGBaseSubModule):
    """
    Sistema di logging errori per apprendimento adattivo.

    Gestisce:
    - Serializzazione errori e correzioni
    - Pattern di errori ricorrenti
    - Suggerimenti per miglioramenti
    - Statistiche di apprendimento
    """

    def __init__(self, config: Optional[Dict[str, Any]] = None):
        super().__init__(
            NGSubModuleType.ERROR_LOG,
            NGModuleType.MEMORY,
            config
        )

        # Configurazione error log
        self.log_path = self.config.get('log_path', 'data/ng_memory/errors.jsonl')
        self.max_entries = self.config.get('max_entries', 10000)
        self.enable_pattern_detection = self.config.get('enable_pattern_detection', True)

        # Cache errori in memoria
        self.error_cache = []
        self.error_patterns = {}

        # Carica errori esistenti
        self._load_existing_errors()

        logger.debug("🔧 ErrorLog inizializzato")

    def _load_existing_errors(self):
        """Carica errori esistenti dal file"""
        if os.path.exists(self.log_path):
            try:
                with open(self.log_path, 'r') as f:
                    for line in f:
                        if line.strip():
                            error_entry = json.loads(line.strip())
                            self.error_cache.append(error_entry)

                logger.info(f"📚 Caricati {len(self.error_cache)} errori esistenti")

                # Analizza pattern se abilitato
                if self.enable_pattern_detection:
                    self._analyze_error_patterns()

            except Exception as e:
                logger.error(f"❌ Errore caricamento error log: {e}")

    def execute(self, input_data: Any, **kwargs) -> NGSubModuleResult:
        """Esegue operazione su error log"""
        start_time = time.time()

        try:
            operation = kwargs.get('operation', 'log')

            if operation == 'log':
                result = self._log_error(input_data)
            elif operation == 'retrieve':
                result = self._retrieve_errors(input_data)
            elif operation == 'analyze':
                result = self._analyze_patterns(input_data)
            else:
                raise ValueError(f"Operazione non supportata: {operation}")

            processing_time = time.time() - start_time

            return self._create_result(
                success=True,
                output_data=result,
                confidence=1.0,
                processing_time=processing_time,
                metadata={'operation': operation}
            )

        except Exception as e:
            processing_time = time.time() - start_time
            logger.error(f"❌ Errore ErrorLog: {e}")

            return self._create_result(
                success=False,
                output_data=None,
                confidence=0.0,
                processing_time=processing_time,
                error_message=str(e)
            )

    def _log_error(self, error_data: Dict) -> Dict:
        """Registra nuovo errore"""
        error_entry = {
            'id': hashlib.md5(str(error_data).encode()).hexdigest()[:8],
            'timestamp': datetime.now().isoformat(),
            'error_type': error_data.get('error_type', 'unknown'),
            'error_message': error_data.get('error_message', ''),
            'context': error_data.get('context', {}),
            'correction': error_data.get('correction', ''),
            'severity': error_data.get('severity', 'medium'),
            'module': error_data.get('module', 'unknown')
        }

        # Aggiungi a cache
        self.error_cache.append(error_entry)

        # Mantieni limite cache
        if len(self.error_cache) > self.max_entries:
            self.error_cache = self.error_cache[-self.max_entries:]

        # Salva su file
        self._save_error_to_file(error_entry)

        # Aggiorna pattern se abilitato
        if self.enable_pattern_detection:
            self._update_error_patterns(error_entry)

        return {
            'logged': True,
            'error_id': error_entry['id'],
            'total_errors': len(self.error_cache)
        }

    def _retrieve_errors(self, query_data: Dict) -> Dict:
        """Recupera errori basati su query"""
        error_type = query_data.get('error_type')
        module = query_data.get('module')
        severity = query_data.get('severity')
        limit = query_data.get('limit', 10)

        filtered_errors = []

        for error in self.error_cache:
            match = True

            if error_type and error.get('error_type') != error_type:
                match = False
            if module and error.get('module') != module:
                match = False
            if severity and error.get('severity') != severity:
                match = False

            if match:
                filtered_errors.append(error)

        # Ordina per timestamp (più recenti prima)
        filtered_errors.sort(key=lambda x: x['timestamp'], reverse=True)

        return {
            'errors': filtered_errors[:limit],
            'total_found': len(filtered_errors),
            'patterns': self.error_patterns
        }

    def _analyze_patterns(self, analysis_data: Dict) -> Dict:
        """Analizza pattern di errori"""
        self._analyze_error_patterns()

        return {
            'patterns_found': len(self.error_patterns),
            'patterns': self.error_patterns,
            'recommendations': self._generate_recommendations()
        }

    def _analyze_error_patterns(self):
        """Analizza pattern ricorrenti negli errori"""
        pattern_counts = {}

        for error in self.error_cache:
            error_type = error.get('error_type', 'unknown')
            module = error.get('module', 'unknown')

            # Pattern per tipo di errore
            type_key = f"type:{error_type}"
            pattern_counts[type_key] = pattern_counts.get(type_key, 0) + 1

            # Pattern per modulo
            module_key = f"module:{module}"
            pattern_counts[module_key] = pattern_counts.get(module_key, 0) + 1

            # Pattern combinato
            combo_key = f"combo:{module}:{error_type}"
            pattern_counts[combo_key] = pattern_counts.get(combo_key, 0) + 1

        # Filtra pattern significativi (>= 3 occorrenze)
        self.error_patterns = {
            pattern: count for pattern, count in pattern_counts.items()
            if count >= 3
        }

    def _update_error_patterns(self, error_entry: Dict):
        """Aggiorna pattern con nuovo errore"""
        error_type = error_entry.get('error_type', 'unknown')
        module = error_entry.get('module', 'unknown')

        # Aggiorna contatori pattern
        type_key = f"type:{error_type}"
        module_key = f"module:{module}"
        combo_key = f"combo:{module}:{error_type}"

        self.error_patterns[type_key] = self.error_patterns.get(type_key, 0) + 1
        self.error_patterns[module_key] = self.error_patterns.get(module_key, 0) + 1
        self.error_patterns[combo_key] = self.error_patterns.get(combo_key, 0) + 1

    def _generate_recommendations(self) -> List[str]:
        """Genera raccomandazioni basate sui pattern"""
        recommendations = []

        # Analizza pattern più frequenti
        sorted_patterns = sorted(
            self.error_patterns.items(),
            key=lambda x: x[1],
            reverse=True
        )

        for pattern, count in sorted_patterns[:5]:  # Top 5 pattern
            if pattern.startswith('type:'):
                error_type = pattern.split(':', 1)[1]
                recommendations.append(
                    f"Errore ricorrente '{error_type}' ({count} volte): "
                    f"considera implementazione di controllo preventivo"
                )
            elif pattern.startswith('module:'):
                module = pattern.split(':', 1)[1]
                recommendations.append(
                    f"Modulo '{module}' genera molti errori ({count}): "
                    f"rivedi implementazione e test"
                )

        return recommendations

    def _save_error_to_file(self, error_entry: Dict):
        """Salva errore su file JSONL"""
        try:
            # Crea directory se non esiste
            os.makedirs(os.path.dirname(self.log_path), exist_ok=True)

            # Append al file JSONL
            with open(self.log_path, 'a') as f:
                f.write(json.dumps(error_entry) + '\n')

        except Exception as e:
            logger.error(f"❌ Errore salvataggio error log: {e}")

    def get_statistics(self) -> Dict:
        """Restituisce statistiche error log"""
        if not self.error_cache:
            return {'total_errors': 0}

        # Conta per tipo
        type_counts = {}
        module_counts = {}
        severity_counts = {}

        for error in self.error_cache:
            error_type = error.get('error_type', 'unknown')
            module = error.get('module', 'unknown')
            severity = error.get('severity', 'medium')

            type_counts[error_type] = type_counts.get(error_type, 0) + 1
            module_counts[module] = module_counts.get(module, 0) + 1
            severity_counts[severity] = severity_counts.get(severity, 0) + 1

        return {
            'total_errors': len(self.error_cache),
            'by_type': type_counts,
            'by_module': module_counts,
            'by_severity': severity_counts,
            'patterns_detected': len(self.error_patterns)
        }

# ============================================================================
# MODULO PRINCIPALE: NG_MEMORY
# ============================================================================

class NGMemory(NGBaseModule):
    """
    Modulo principale NG_MEMORY v3.0 ULTRA

    Coordina storage simbolico, episode cache e error logging.
    Implementa interfaccia NGMemoryInterface.

    Pipeline:
    1. SymbolStore: Storage persistente LMDB
    2. EpisodeCache: Similarity search FAISS
    3. ErrorLog: Apprendimento da errori
    """

    def __init__(self, config: Optional[Dict[str, Any]] = None):
        super().__init__(NGModuleType.MEMORY, config)

        # Inizializza sotto-moduli
        self.symbol_store = SymbolStore(self.config.get('symbol_store', {}))
        self.episode_cache = EpisodeCache(self.config.get('episode_cache', {}))
        self.error_log = ErrorLog(self.config.get('error_log', {}))

        # Configurazione modulo
        self.enable_symbol_storage = self.config.get('enable_symbol_storage', True)
        self.enable_episode_cache = self.config.get('enable_episode_cache', True)
        self.enable_error_logging = self.config.get('enable_error_logging', True)

        # Parametri retrieval
        self.max_symbols = self.config.get('max_symbols', 50)
        self.max_episodes = self.config.get('max_episodes', 10)
        self.similarity_threshold = self.config.get('similarity_threshold', 0.7)

        logger.info("🧠 NGMemory v3.0 ULTRA inizializzato")

    def process(self, message: NGMessage) -> NGMessage:
        """
        Processa messaggio attraverso pipeline di memoria

        Args:
            message: NGMessage con (PriorityVector, ParsedPrompt) nel content

        Returns:
            NGMessage con MemoryContext nel content
        """
        start_time = time.time()

        try:
            # Verifica input
            if not isinstance(message.content, tuple) or len(message.content) != 2:
                raise ValueError("Expected tuple (PriorityVector, ParsedPrompt)")

            priority_vector, parsed_prompt = message.content
            memory_results = {}

            # 1. Symbol Storage/Retrieval
            if self.enable_symbol_storage:
                symbol_result = self._process_symbols(parsed_prompt, priority_vector)
                memory_results['symbols'] = symbol_result

                # Crea trace unit
                self.create_trace_unit(
                    operation="symbol_retrieval",
                    input_data=len(parsed_prompt.tokens),
                    output_data=symbol_result.output_data.get('symbols_found') if symbol_result.success else 0,
                    processing_time=symbol_result.processing_time,
                    success=symbol_result.success,
                    submodule=NGSubModuleType.SYMBOL_STORE,
                    error_message=symbol_result.error_message
                )

            # 2. Episode Cache Search
            if self.enable_episode_cache:
                episode_result = self._process_episodes(parsed_prompt, priority_vector)
                memory_results['episodes'] = episode_result

                # Crea trace unit
                self.create_trace_unit(
                    operation="episode_search",
                    input_data=f"domain:{priority_vector.domain}",
                    output_data=episode_result.output_data.get('total_found') if episode_result.success else 0,
                    processing_time=episode_result.processing_time,
                    success=episode_result.success,
                    submodule=NGSubModuleType.EPISODE_CACHE,
                    error_message=episode_result.error_message
                )

            # 3. Error Log Analysis
            if self.enable_error_logging:
                error_result = self._process_errors(parsed_prompt, priority_vector)
                memory_results['errors'] = error_result

                # Crea trace unit
                self.create_trace_unit(
                    operation="error_analysis",
                    input_data=priority_vector.domain,
                    output_data=error_result.output_data.get('total_found') if error_result.success else 0,
                    processing_time=error_result.processing_time,
                    success=error_result.success,
                    submodule=NGSubModuleType.ERROR_LOG,
                    error_message=error_result.error_message
                )

            # Crea MemoryContext finale
            memory_context = self._create_memory_context(memory_results, priority_vector, parsed_prompt)

            # Calcola confidence e tempo totale
            processing_time = time.time() - start_time
            overall_confidence = self._calculate_overall_confidence(memory_results)

            # Aggiorna metriche
            success = memory_context is not None
            self.update_metrics(processing_time, success)

            # Crea messaggio di risposta
            response_message = NGMessage(
                source_module=NGModuleType.MEMORY,
                stage=NGProcessingStage.MEMORY_ACCESS,
                content=memory_context,
                confidence=overall_confidence,
                processing_time=processing_time,
                metadata={
                    'submodule_results': {
                        name: result.success for name, result in memory_results.items()
                    },
                    'input_tokens': len(parsed_prompt.tokens),
                    'priority_domain': priority_vector.domain,
                    'priority_urgency': priority_vector.urgency
                }
            )

            logger.debug(f"✅ Memory retrieval completato: confidence={overall_confidence:.3f}")
            return response_message

        except Exception as e:
            processing_time = time.time() - start_time
            logger.error(f"❌ Errore NGMemory: {e}")

            # Aggiorna metriche con errore
            self.update_metrics(processing_time, False)

            # Crea messaggio di errore
            error_message = NGMessage(
                source_module=NGModuleType.MEMORY,
                stage=NGProcessingStage.MEMORY_ACCESS,
                content=None,
                confidence=0.0,
                processing_time=processing_time,
                metadata={'error': str(e)}
            )

            return error_message

    def retrieve(self, priority: PriorityVector, parsed: ParsedPrompt) -> MemoryContext:
        """
        Implementa interfaccia NGMemoryInterface.retrieve()

        Args:
            priority: Vettore priorità
            parsed: Prompt parsato

        Returns:
            MemoryContext con examples, errors, symbols
        """
        # Crea messaggio per il processo interno
        message = NGMessage(
            source_module=NGModuleType.MEMORY,
            stage=NGProcessingStage.MEMORY_ACCESS,
            content=(priority, parsed)
        )

        # Esegue processo interno
        result_message = self.process(message)

        if not result_message.content:
            # Fallback se processo fallisce
            return MemoryContext(
                examples=[],
                errors=[],
                symbols=[],
                relevant_cases=[],
                confidence=0.0,
                retrieval_time=0.0,
                metadata={"error": "memory_retrieval_failed"}
            )

        return result_message.content

    def _process_symbols(self, parsed_prompt: ParsedPrompt, priority_vector: PriorityVector) -> NGSubModuleResult:
        """Processa retrieval simboli"""
        # Estrai simboli dai token
        symbols_to_search = []
        for token in parsed_prompt.tokens:
            # Cerca simboli NEUROGLYPH (caratteri non ASCII)
            if any(ord(char) > 127 for char in token):
                symbols_to_search.append(token)

        # Aggiungi simboli dal dominio
        domain_symbols = self._get_domain_symbols(priority_vector.domain)
        symbols_to_search.extend(domain_symbols)

        # Rimuovi duplicati
        symbols_to_search = list(set(symbols_to_search))

        # Esegui retrieval
        query_data = {
            'symbols': symbols_to_search,
            'domain': priority_vector.domain
        }

        return self.symbol_store.execute(query_data, operation='retrieve')

    def _process_episodes(self, parsed_prompt: ParsedPrompt, priority_vector: PriorityVector) -> NGSubModuleResult:
        """Processa search episodi"""
        # Genera embedding semplificato dal prompt
        # In produzione usare modello embedding reale
        prompt_text = " ".join(parsed_prompt.tokens)
        embedding = self._generate_simple_embedding(prompt_text)

        query_data = {
            'embedding': embedding,
            'k': self.max_episodes,
            'threshold': self.similarity_threshold,
            'domain': priority_vector.domain
        }

        return self.episode_cache.execute(query_data, operation='search')

    def _process_errors(self, parsed_prompt: ParsedPrompt, priority_vector: PriorityVector) -> NGSubModuleResult:
        """Processa analisi errori"""
        query_data = {
            'module': 'memory',
            'limit': 5
        }

        return self.error_log.execute(query_data, operation='retrieve')

    def _create_memory_context(self, memory_results: Dict,
                             priority_vector: PriorityVector,
                             parsed_prompt: ParsedPrompt) -> Optional[MemoryContext]:
        """Crea MemoryContext finale dai risultati dei sotto-moduli"""
        try:
            # Estrai risultati
            symbol_result = memory_results.get('symbols')
            episode_result = memory_results.get('episodes')
            error_result = memory_results.get('errors')

            # Valori di default
            examples = []
            errors = []
            symbols = []
            relevant_cases = []

            # Processa risultati simboli
            if symbol_result and symbol_result.success:
                symbol_data = symbol_result.output_data
                symbols_data = symbol_data.get('symbols_data', [])

                for symbol_info in symbols_data:
                    symbols.append(symbol_info.get('symbol', ''))

                    # Crea memory entry da simbolo
                    memory_entry = NGMemoryEntry(
                        symbols=[symbol_info.get('symbol', '')],
                        context=symbol_info.get('meaning', ''),
                        solution=f"Simbolo: {symbol_info.get('symbol', '')}",
                        success_rate=0.9,
                        usage_count=symbol_info.get('usage_count', 1),
                        tags=[priority_vector.domain],
                        relevance_score=0.8
                    )
                    examples.append(memory_entry)

            # Processa risultati episodi
            if episode_result and episode_result.success:
                episode_data = episode_result.output_data
                episodes = episode_data.get('episodes', [])

                for episode in episodes:
                    relevant_case = {
                        'type': 'episode',
                        'similarity': episode.get('similarity', 0.0),
                        'metadata': episode.get('metadata', {}),
                        'confidence': episode.get('similarity', 0.0)
                    }
                    relevant_cases.append(relevant_case)

            # Processa risultati errori
            if error_result and error_result.success:
                error_data = error_result.output_data
                error_list = error_data.get('errors', [])

                for error in error_list:
                    errors.append(error.get('error_message', ''))

            # Calcola confidence finale
            confidences = []
            if symbol_result:
                confidences.append(symbol_result.confidence)
            if episode_result:
                confidences.append(episode_result.confidence)
            if error_result:
                confidences.append(error_result.confidence)

            overall_confidence = sum(confidences) / len(confidences) if confidences else 0.5

            # Calcola tempo retrieval
            retrieval_times = []
            if symbol_result:
                retrieval_times.append(symbol_result.processing_time)
            if episode_result:
                retrieval_times.append(episode_result.processing_time)
            if error_result:
                retrieval_times.append(error_result.processing_time)

            total_retrieval_time = sum(retrieval_times)

            return MemoryContext(
                examples=examples,
                errors=errors,
                symbols=symbols,
                relevant_cases=relevant_cases,
                confidence=overall_confidence,
                retrieval_time=total_retrieval_time,
                metadata={
                    'submodule_successes': {
                        'symbols': symbol_result.success if symbol_result else False,
                        'episodes': episode_result.success if episode_result else False,
                        'errors': error_result.success if error_result else False
                    },
                    'domain': priority_vector.domain,
                    'urgency': priority_vector.urgency,
                    'input_tokens': len(parsed_prompt.tokens)
                }
            )

        except Exception as e:
            logger.error(f"❌ Errore creazione MemoryContext: {e}")
            return None

    def _calculate_overall_confidence(self, memory_results: Dict) -> float:
        """Calcola confidence complessiva dai risultati dei sotto-moduli"""
        confidences = []

        for result in memory_results.values():
            if result and hasattr(result, 'confidence'):
                confidences.append(result.confidence)

        if not confidences:
            return 0.5

        # Media pesata: più sotto-moduli hanno successo, più alta la confidence
        success_count = sum(1 for result in memory_results.values()
                          if result and result.success)
        total_count = len(memory_results)

        avg_confidence = sum(confidences) / len(confidences)
        success_ratio = success_count / total_count if total_count > 0 else 0

        # Combina confidence media con ratio di successo
        overall_confidence = (avg_confidence * 0.7) + (success_ratio * 0.3)

        return max(0.0, min(overall_confidence, 1.0))

    def _get_domain_symbols(self, domain: str) -> List[str]:
        """Restituisce simboli rilevanti per il dominio"""
        domain_symbols = {
            'programming': ['⊢', '→', '≈', '∴'],
            'mathematics': ['∑', '∫', '∂', '∞', '≤', '≥'],
            'logic': ['⊢', '¬', '∧', '∨', '→', '↔'],
            'general': ['⊢', '→', '≈']
        }

        return domain_symbols.get(domain, domain_symbols['general'])

    def _generate_simple_embedding(self, text: str) -> List[float]:
        """Genera embedding semplificato per il testo"""
        # Implementazione semplificata - in produzione usare modello reale
        import hashlib

        # Usa hash per generare embedding deterministico
        text_hash = hashlib.md5(text.encode()).hexdigest()

        # Converte hash in vettore numerico
        embedding = []
        for i in range(0, len(text_hash), 2):
            hex_pair = text_hash[i:i+2]
            value = int(hex_pair, 16) / 255.0  # Normalizza 0-1
            embedding.append(value)

        # Pad o tronca a dimensione fissa
        target_dim = self.episode_cache.embedding_dim
        while len(embedding) < target_dim:
            embedding.extend(embedding[:target_dim - len(embedding)])

        return embedding[:target_dim]

    def close(self):
        """Chiude tutti i sotto-moduli"""
        self.symbol_store.close()
        self.episode_cache.close()
        logger.info("🔒 NGMemory closed")
