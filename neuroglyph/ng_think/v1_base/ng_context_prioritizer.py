#!/usr/bin/env python3
"""
NG-THINK v3.0 ULTRA - Context Prioritizer
=========================================

Modulo NG_CONTEXT_PRIORITIZER per analisi e prioritizzazione del contesto.
Implementa classificazione urgenza, risk assessment e domain mapping.

Autore: NEUROGLYPH NG-THINK Team
Data: 2025-01-27
Licenza: MIT
"""

import re
import time
import logging
from typing import Dict, List, Optional, Any, Tuple
from dataclasses import dataclass

from ..core.ng_base import NGBaseModule, NGBaseSubModule
from ..core.ng_types import (
    NGMessage, NGModuleType, NGSubModuleType, NGProcessingStage,
    NGSubModuleResult, ParsedPrompt, PriorityVector, NGContextFlags,
    NGUrgencyLevel, NGToneType, NGRiskLevel, NGContextPrioritizerInterface
)

logger = logging.getLogger(__name__)

# ============================================================================
# SOTTO-MODULO: URGENCY CLASSIFIER
# ============================================================================

class UrgencyClassifier(NGBaseSubModule):
    """
    Classificatore di urgenza basato su pattern linguistici e RoBERTa.

    Analizza il testo per identificare indicatori di urgenza:
    - Parole chiave temporali (ASAP, urgente, subito)
    - Tono emotivo (esclamazioni, maiuscole)
    - Contesto temporale (deadline, scadenza)
    """

    def __init__(self, config: Optional[Dict[str, Any]] = None):
        super().__init__(
            NGSubModuleType.URGENCY_CLASSIFIER,
            NGModuleType.CONTEXT_PRIORITIZER,
            config
        )

        # Pattern di urgenza (ordinati per priorità)
        self.urgency_patterns = {
            NGUrgencyLevel.CRITICAL: [
                r'\b(ASAP|asap|URGENTE|EMERGENCY|CRITICO)\b',
                r'\b(subito|immediatamente|ora|adesso)\b',
                r'\b(emergenza|critico|grave)\b',
                r'!!+',  # Multiple exclamation marks
            ],
            NGUrgencyLevel.HIGH: [
                r'\b(urgente|importante|priorità alta)\b',
                r'\b(veloce|rapido|presto)\b',
                r'\b(deadline|scadenza|entro)\b',
                r'\b(help|aiuto|problema)\b',
            ],
            NGUrgencyLevel.MEDIUM: [
                r'\b(quando possibile|appena puoi)\b',
                r'\b(preferibilmente|meglio se)\b',
                r'\b(dovrebbe|sarebbe bene)\b',
            ],
            NGUrgencyLevel.LOW: [
                r'\b(no fretta|senza fretta|quando hai tempo)\b',
                r'\b(eventualmente|magari|forse)\b',
                r'\b(non urgente|non importante)\b',
            ]
        }

        # Tone indicators
        self.tone_patterns = {
            NGToneType.URGENT: [r'[!]{2,}', r'[A-Z]{3,}', r'\b(HELP|SOS)\b'],
            NGToneType.ANALYTICAL: [r'\b(analizza|studia|esamina|considera)\b'],
            NGToneType.CREATIVE: [r'\b(crea|inventa|immagina|design)\b'],
            NGToneType.CRITICAL: [r'\b(errore|bug|problema|fallito)\b'],
            NGToneType.EXPLORATORY: [r'\b(esplora|scopri|prova|sperimenta)\b']
        }

        logger.debug("🔧 UrgencyClassifier inizializzato")

    def execute(self, input_data: Any, **kwargs) -> NGSubModuleResult:
        """Classifica urgenza del prompt"""
        start_time = time.time()

        try:
            if isinstance(input_data, ParsedPrompt):
                text = " ".join(input_data.tokens)
                segments_text = " ".join([seg.content for seg in input_data.segments])
                full_text = f"{text} {segments_text}"
            else:
                full_text = str(input_data)

            # Analisi urgenza
            urgency_level, urgency_score, urgency_indicators = self._analyze_urgency(full_text)

            # Analisi tono
            tone_type, tone_confidence = self._analyze_tone(full_text)

            # Crea risultato
            result = {
                'urgency_level': urgency_level,
                'urgency_score': urgency_score,
                'urgency_indicators': urgency_indicators,
                'tone_type': tone_type,
                'tone_confidence': tone_confidence,
                'text_length': len(full_text),
                'caps_ratio': self._calculate_caps_ratio(full_text),
                'exclamation_count': full_text.count('!')
            }

            processing_time = time.time() - start_time
            confidence = min(urgency_score + tone_confidence, 1.0) / 2.0

            return self._create_result(
                success=True,
                output_data=result,
                confidence=confidence,
                processing_time=processing_time,
                metadata={'patterns_matched': len(urgency_indicators)}
            )

        except Exception as e:
            processing_time = time.time() - start_time
            logger.error(f"❌ Errore UrgencyClassifier: {e}")

            return self._create_result(
                success=False,
                output_data=None,
                confidence=0.0,
                processing_time=processing_time,
                error_message=str(e)
            )

    def _analyze_urgency(self, text: str) -> Tuple[NGUrgencyLevel, float, List[str]]:
        """Analizza urgenza del testo"""
        text_lower = text.lower()
        urgency_scores = {level: 0 for level in NGUrgencyLevel}
        indicators = []

        # Cerca pattern per ogni livello di urgenza
        for level, patterns in self.urgency_patterns.items():
            for pattern in patterns:
                matches = re.findall(pattern, text, re.IGNORECASE)
                if matches:
                    urgency_scores[level] += len(matches)
                    indicators.extend([f"{level.name}:{match}" for match in matches])

        # Determina livello finale
        max_score = max(urgency_scores.values())
        if max_score == 0:
            return NGUrgencyLevel.MEDIUM, 0.5, []

        # Trova livello con score massimo
        for level, score in urgency_scores.items():
            if score == max_score:
                urgency_level = level
                break

        # Normalizza score
        urgency_score = min(max_score / 3.0, 1.0)  # Max 3 pattern per livello massimo

        return urgency_level, urgency_score, indicators

    def _analyze_tone(self, text: str) -> Tuple[NGToneType, float]:
        """Analizza tono emotivo del testo"""
        tone_scores = {tone: 0 for tone in NGToneType}

        # Cerca pattern per ogni tono
        for tone, patterns in self.tone_patterns.items():
            for pattern in patterns:
                matches = len(re.findall(pattern, text, re.IGNORECASE))
                tone_scores[tone] += matches

        # Determina tono dominante
        max_score = max(tone_scores.values())
        if max_score == 0:
            return NGToneType.NEUTRAL, 0.5

        for tone, score in tone_scores.items():
            if score == max_score:
                tone_type = tone
                break

        confidence = min(max_score / 2.0, 1.0)
        return tone_type, confidence

    def _calculate_caps_ratio(self, text: str) -> float:
        """Calcola rapporto di lettere maiuscole"""
        if not text:
            return 0.0

        caps_count = sum(1 for c in text if c.isupper())
        total_letters = sum(1 for c in text if c.isalpha())

        return caps_count / total_letters if total_letters > 0 else 0.0

# ============================================================================
# SOTTO-MODULO: RISK SCORER
# ============================================================================

class RiskScorer(NGBaseSubModule):
    """
    Valutatore di rischio per policy compliance e safety.

    Analizza contenuti per:
    - Richieste potenzialmente dannose
    - Violazioni policy
    - Contenuti sensibili
    - Risk mitigation
    """

    def __init__(self, config: Optional[Dict[str, Any]] = None):
        super().__init__(
            NGSubModuleType.RISK_SCORER,
            NGModuleType.CONTEXT_PRIORITIZER,
            config
        )

        # Pattern di rischio
        self.risk_patterns = {
            NGRiskLevel.DANGEROUS: [
                r'\b(hack|crack|exploit|malware)\b',
                r'\b(password|credential|token)\s+\w+',
                r'\b(delete|remove|destroy)\s+(database|system|file)',
            ],
            NGRiskLevel.HIGH_RISK: [
                r'\b(bypass|circumvent|override)\b',
                r'\b(admin|root|sudo)\s+(access|permission)',
                r'\b(sensitive|confidential|private)\s+(data|info)',
            ],
            NGRiskLevel.MEDIUM_RISK: [
                r'\b(debug|test|experiment)\s+(production|live)',
                r'\b(modify|change|alter)\s+(config|setting)',
            ],
            NGRiskLevel.LOW_RISK: [
                r'\b(read|view|display)\s+(log|status)',
                r'\b(help|tutorial|guide|example)\b',
            ],
            NGRiskLevel.SAFE: [
                r'\b(learn|study|understand|explain)\b',
                r'\b(create|build|develop|design)\b',
                r'\b(analyze|review|check|validate)\b',
            ]
        }

        logger.debug("🔧 RiskScorer inizializzato")

    def execute(self, input_data: Any, **kwargs) -> NGSubModuleResult:
        """Valuta rischio del prompt"""
        start_time = time.time()

        try:
            if isinstance(input_data, ParsedPrompt):
                text = " ".join(input_data.tokens)
                segments_text = " ".join([seg.content for seg in input_data.segments])
                full_text = f"{text} {segments_text}"
            else:
                full_text = str(input_data)

            # Analisi rischio
            risk_level, risk_score, risk_factors = self._analyze_risk(full_text)

            # Policy violations
            policy_violations = self._check_policy_violations(full_text)

            # Mitigation suggestions
            mitigation_suggestions = self._generate_mitigation_suggestions(risk_level, risk_factors)

            result = {
                'risk_level': risk_level,
                'risk_score': risk_score,
                'risk_factors': risk_factors,
                'policy_violations': policy_violations,
                'mitigation_suggestions': mitigation_suggestions,
                'requires_review': risk_level.value >= NGRiskLevel.HIGH_RISK.value
            }

            processing_time = time.time() - start_time
            confidence = 1.0 - risk_score  # Higher risk = lower confidence

            return self._create_result(
                success=True,
                output_data=result,
                confidence=confidence,
                processing_time=processing_time,
                metadata={'risk_factors_count': len(risk_factors)}
            )

        except Exception as e:
            processing_time = time.time() - start_time
            logger.error(f"❌ Errore RiskScorer: {e}")

            return self._create_result(
                success=False,
                output_data=None,
                confidence=0.0,
                processing_time=processing_time,
                error_message=str(e)
            )

    def _analyze_risk(self, text: str) -> Tuple[NGRiskLevel, float, List[str]]:
        """Analizza livello di rischio"""
        risk_scores = {level: 0 for level in NGRiskLevel}
        risk_factors = []

        # Cerca pattern per ogni livello di rischio
        for level, patterns in self.risk_patterns.items():
            for pattern in patterns:
                matches = re.findall(pattern, text, re.IGNORECASE)
                if matches:
                    risk_scores[level] += len(matches)
                    risk_factors.extend([f"{level.name}:{match}" for match in matches])

        # Determina livello finale (prende il più alto)
        max_score = max(risk_scores.values())
        if max_score == 0:
            return NGRiskLevel.SAFE, 0.1, []

        # Trova livello con score massimo (priorità ai più rischiosi)
        for level in [NGRiskLevel.DANGEROUS, NGRiskLevel.HIGH_RISK,
                     NGRiskLevel.MEDIUM_RISK, NGRiskLevel.LOW_RISK, NGRiskLevel.SAFE]:
            if risk_scores[level] > 0:
                risk_level = level
                break

        # Normalizza score
        risk_score = min(max_score / 2.0, 1.0)

        return risk_level, risk_score, risk_factors

    def _check_policy_violations(self, text: str) -> List[str]:
        """Verifica violazioni policy"""
        violations = []

        # Policy patterns
        policy_checks = {
            'PII_EXPOSURE': r'\b\d{3}-\d{2}-\d{4}\b|\b\d{16}\b',  # SSN, Credit card
            'CREDENTIAL_LEAK': r'(password|token|key)\s*[:=]\s*\w+',
            'HARMFUL_CONTENT': r'\b(violence|harm|illegal)\b',
        }

        for policy, pattern in policy_checks.items():
            if re.search(pattern, text, re.IGNORECASE):
                violations.append(policy)

        return violations

    def _generate_mitigation_suggestions(self, risk_level: NGRiskLevel,
                                       risk_factors: List[str]) -> List[str]:
        """Genera suggerimenti per mitigazione rischio"""
        suggestions = []

        if risk_level == NGRiskLevel.DANGEROUS:
            suggestions.extend([
                "Richiesta bloccata per motivi di sicurezza",
                "Contattare amministratore per autorizzazione",
                "Utilizzare ambiente sandbox isolato"
            ])
        elif risk_level == NGRiskLevel.HIGH_RISK:
            suggestions.extend([
                "Richiedere approvazione supervisore",
                "Implementare logging dettagliato",
                "Utilizzare credenziali temporanee"
            ])
        elif risk_level == NGRiskLevel.MEDIUM_RISK:
            suggestions.extend([
                "Verificare permessi utente",
                "Implementare backup preventivo",
                "Monitorare attività"
            ])

        return suggestions

# ============================================================================
# SOTTO-MODULO: PRIORITY VECTORIZER
# ============================================================================

class PriorityVectorizer(NGBaseSubModule):
    """
    Generatore di vettori di priorità normalizzati.

    Combina risultati di urgenza e rischio in un vettore di priorità
    unificato con domain mapping e complexity estimation.
    """

    def __init__(self, config: Optional[Dict[str, Any]] = None):
        super().__init__(
            NGSubModuleType.PRIORITY_VECTORIZER,
            NGModuleType.CONTEXT_PRIORITIZER,
            config
        )

        # Mappa domini semantici
        self.domain_patterns = {
            'programming': [
                r'\b(code|function|algorithm|debug|compile)\b',
                r'\b(python|javascript|java|rust|c\+\+)\b',
                r'\b(class|method|variable|loop|condition)\b'
            ],
            'mathematics': [
                r'\b(equation|formula|calculate|solve|proof)\b',
                r'\b(algebra|geometry|calculus|statistics)\b',
                r'\b(matrix|vector|derivative|integral)\b'
            ],
            'data_science': [
                r'\b(dataset|model|training|prediction|analysis)\b',
                r'\b(machine learning|neural network|regression)\b',
                r'\b(pandas|numpy|sklearn|tensorflow)\b'
            ],
            'system_admin': [
                r'\b(server|database|network|security|backup)\b',
                r'\b(linux|windows|docker|kubernetes)\b',
                r'\b(configuration|deployment|monitoring)\b'
            ],
            'business': [
                r'\b(strategy|market|customer|revenue|profit)\b',
                r'\b(analysis|report|presentation|meeting)\b',
                r'\b(project|timeline|budget|resource)\b'
            ],
            'research': [
                r'\b(study|research|experiment|hypothesis|paper)\b',
                r'\b(literature|methodology|results|conclusion)\b',
                r'\b(academic|scientific|peer review)\b'
            ],
            'creative': [
                r'\b(design|art|creative|innovative|brainstorm)\b',
                r'\b(story|narrative|visual|aesthetic)\b',
                r'\b(inspiration|concept|prototype)\b'
            ],
            'general': [
                r'\b(help|question|explain|understand|learn)\b',
                r'\b(how|what|why|when|where)\b'
            ]
        }

        logger.debug("🔧 PriorityVectorizer inizializzato")

    def execute(self, input_data: Any, **kwargs) -> NGSubModuleResult:
        """Genera vettore di priorità"""
        start_time = time.time()

        try:
            # Input dovrebbe essere tuple (urgency_result, risk_result, parsed_prompt)
            urgency_result, risk_result, parsed_prompt = input_data

            # Estrai dati da risultati precedenti
            urgency_data = urgency_result.output_data if urgency_result.success else {}
            risk_data = risk_result.output_data if risk_result.success else {}

            # Analisi dominio
            domain, domain_confidence = self._analyze_domain(parsed_prompt)

            # Stima complessità
            complexity = self._estimate_complexity(parsed_prompt, urgency_data, risk_data)

            # Stima tempo
            estimated_time = self._estimate_time(complexity, domain, urgency_data.get('urgency_level'))

            # Crea flags di contesto
            context_flags = self._create_context_flags(urgency_data, risk_data, domain)

            # Calcola priorità finale
            urgency_score = urgency_data.get('urgency_score', 0.5)
            risk_score = risk_data.get('risk_score', 0.1)

            result = {
                'urgency': urgency_score,
                'risk': risk_score,
                'domain': domain,
                'domain_confidence': domain_confidence,
                'complexity': complexity,
                'estimated_time': estimated_time,
                'flags': context_flags,
                'overall_priority': self._calculate_overall_priority(urgency_score, risk_score, complexity)
            }

            processing_time = time.time() - start_time
            confidence = (urgency_result.confidence + risk_result.confidence + domain_confidence) / 3.0

            return self._create_result(
                success=True,
                output_data=result,
                confidence=confidence,
                processing_time=processing_time,
                metadata={'domain': domain, 'complexity': complexity}
            )

        except Exception as e:
            processing_time = time.time() - start_time
            logger.error(f"❌ Errore PriorityVectorizer: {e}")

            return self._create_result(
                success=False,
                output_data=None,
                confidence=0.0,
                processing_time=processing_time,
                error_message=str(e)
            )

    def _analyze_domain(self, parsed_prompt: ParsedPrompt) -> Tuple[str, float]:
        """Analizza dominio semantico del prompt"""
        text = " ".join(parsed_prompt.tokens)
        segments_text = " ".join([seg.content for seg in parsed_prompt.segments])
        full_text = f"{text} {segments_text}".lower()

        domain_scores = {}

        # Calcola score per ogni dominio
        for domain, patterns in self.domain_patterns.items():
            score = 0
            for pattern in patterns:
                matches = len(re.findall(pattern, full_text, re.IGNORECASE))
                score += matches
            domain_scores[domain] = score

        # Trova dominio dominante
        max_score = max(domain_scores.values())
        if max_score == 0:
            return "general", 0.5

        best_domain = max(domain_scores, key=domain_scores.get)
        confidence = min(max_score / 3.0, 1.0)  # Normalizza

        return best_domain, confidence

    def _estimate_complexity(self, parsed_prompt: ParsedPrompt,
                           urgency_data: Dict, risk_data: Dict) -> float:
        """Stima complessità del task"""
        complexity = 0.0

        # Fattori di complessità
        token_count = len(parsed_prompt.tokens)
        segment_count = len(parsed_prompt.segments)

        # Base complexity da lunghezza
        complexity += min(token_count / 50.0, 0.4)  # Max 0.4 da token count

        # Complexity da segmentazione
        complexity += min(segment_count / 5.0, 0.3)  # Max 0.3 da segments

        # Complexity da ambiguità
        if parsed_prompt.ambiguity_report.overall_clarity < 0.5:
            complexity += 0.2

        # Complexity da urgenza (più urgente = più complesso gestire)
        urgency_level = urgency_data.get('urgency_level')
        if urgency_level == NGUrgencyLevel.CRITICAL:
            complexity += 0.1

        return min(complexity, 1.0)

    def _estimate_time(self, complexity: float, domain: str, urgency_level) -> float:
        """Stima tempo di elaborazione in secondi"""
        base_time = 30.0  # 30 secondi base

        # Fattore complessità
        time_multiplier = 1.0 + complexity

        # Fattore dominio
        domain_multipliers = {
            'programming': 2.0,
            'mathematics': 1.8,
            'data_science': 2.2,
            'system_admin': 1.5,
            'research': 2.5,
            'general': 1.0
        }

        domain_multiplier = domain_multipliers.get(domain, 1.0)

        # Fattore urgenza (più urgente = meno tempo disponibile)
        urgency_multipliers = {
            NGUrgencyLevel.CRITICAL: 0.5,
            NGUrgencyLevel.HIGH: 0.7,
            NGUrgencyLevel.MEDIUM: 1.0,
            NGUrgencyLevel.LOW: 1.5
        }

        urgency_multiplier = urgency_multipliers.get(urgency_level, 1.0)

        estimated_time = base_time * time_multiplier * domain_multiplier * urgency_multiplier

        return max(5.0, min(estimated_time, 300.0))  # Tra 5 secondi e 5 minuti

    def _create_context_flags(self, urgency_data: Dict, risk_data: Dict, domain: str) -> NGContextFlags:
        """Crea flags di contesto"""
        urgency_level = urgency_data.get('urgency_level', NGUrgencyLevel.MEDIUM)
        tone_type = urgency_data.get('tone_type', NGToneType.NEUTRAL)
        risk_level = risk_data.get('risk_level', NGRiskLevel.SAFE)

        return NGContextFlags(
            urgency=urgency_level,
            tone=tone_type,
            requires_inference=domain in ['mathematics', 'programming', 'research'],
            requires_creativity=domain in ['creative', 'design'] or tone_type == NGToneType.CREATIVE,
            requires_safety_check=risk_level.value >= NGRiskLevel.MEDIUM_RISK.value,
            domain_hints=[domain],
            complexity_score=urgency_data.get('urgency_score', 0.5)
        )

    def _calculate_overall_priority(self, urgency: float, risk: float, complexity: float) -> float:
        """Calcola priorità complessiva"""
        # Formula pesata: urgenza ha peso maggiore, rischio riduce priorità
        priority = (urgency * 0.6) + (complexity * 0.3) - (risk * 0.1)
        return max(0.0, min(priority, 1.0))

# ============================================================================
# MODULO PRINCIPALE: NG_CONTEXT_PRIORITIZER
# ============================================================================

class NGContextPrioritizer(NGBaseModule):
    """
    Modulo principale NG_CONTEXT_PRIORITIZER v3.0 ULTRA

    Coordina analisi di contesto, urgenza, rischio e prioritizzazione.
    Implementa interfaccia NGContextPrioritizerInterface.

    Pipeline:
    1. UrgencyClassifier: Analizza urgenza e tono
    2. RiskScorer: Valuta rischi e policy compliance
    3. PriorityVectorizer: Genera vettore priorità finale
    """

    def __init__(self, config: Optional[Dict[str, Any]] = None):
        super().__init__(NGModuleType.CONTEXT_PRIORITIZER, config)

        # Inizializza sotto-moduli
        self.urgency_classifier = UrgencyClassifier(self.config.get('urgency_classifier', {}))
        self.risk_scorer = RiskScorer(self.config.get('risk_scorer', {}))
        self.priority_vectorizer = PriorityVectorizer(self.config.get('priority_vectorizer', {}))

        # Configurazione modulo
        self.enable_urgency_analysis = self.config.get('enable_urgency_analysis', True)
        self.enable_risk_analysis = self.config.get('enable_risk_analysis', True)
        self.enable_priority_vectorization = self.config.get('enable_priority_vectorization', True)

        # Thresholds
        self.urgency_threshold = self.config.get('urgency_threshold', 0.7)
        self.risk_threshold = self.config.get('risk_threshold', 0.5)

        logger.info("🧠 NGContextPrioritizer v3.0 ULTRA inizializzato")

    def process(self, message: NGMessage) -> NGMessage:
        """
        Processa messaggio attraverso pipeline di prioritizzazione

        Args:
            message: NGMessage con ParsedPrompt nel content

        Returns:
            NGMessage con PriorityVector nel content
        """
        start_time = time.time()

        try:
            # Verifica input
            if not isinstance(message.content, ParsedPrompt):
                raise ValueError(f"Expected ParsedPrompt, got {type(message.content)}")

            parsed_prompt = message.content
            prioritization_results = {}

            # 1. Analisi Urgenza
            if self.enable_urgency_analysis:
                urgency_result = self.urgency_classifier.execute(parsed_prompt)
                prioritization_results['urgency'] = urgency_result

                # Crea trace unit
                self.create_trace_unit(
                    operation="urgency_classification",
                    input_data=len(parsed_prompt.tokens),
                    output_data=urgency_result.output_data.get('urgency_level') if urgency_result.success else None,
                    processing_time=urgency_result.processing_time,
                    success=urgency_result.success,
                    submodule=NGSubModuleType.URGENCY_CLASSIFIER,
                    error_message=urgency_result.error_message
                )

            # 2. Analisi Rischio
            if self.enable_risk_analysis:
                risk_result = self.risk_scorer.execute(parsed_prompt)
                prioritization_results['risk'] = risk_result

                # Crea trace unit
                self.create_trace_unit(
                    operation="risk_scoring",
                    input_data=len(parsed_prompt.tokens),
                    output_data=risk_result.output_data.get('risk_level') if risk_result.success else None,
                    processing_time=risk_result.processing_time,
                    success=risk_result.success,
                    submodule=NGSubModuleType.RISK_SCORER,
                    error_message=risk_result.error_message
                )

            # 3. Generazione Vettore Priorità
            if self.enable_priority_vectorization:
                urgency_result = prioritization_results.get('urgency')
                risk_result = prioritization_results.get('risk')

                if urgency_result and risk_result:
                    vectorizer_input = (urgency_result, risk_result, parsed_prompt)
                    priority_result = self.priority_vectorizer.execute(vectorizer_input)
                    prioritization_results['priority'] = priority_result

                    # Crea trace unit
                    self.create_trace_unit(
                        operation="priority_vectorization",
                        input_data=f"urgency:{urgency_result.success}, risk:{risk_result.success}",
                        output_data=priority_result.output_data.get('domain') if priority_result.success else None,
                        processing_time=priority_result.processing_time,
                        success=priority_result.success,
                        submodule=NGSubModuleType.PRIORITY_VECTORIZER,
                        error_message=priority_result.error_message
                    )

            # Crea PriorityVector finale
            priority_vector = self._create_priority_vector(prioritization_results, parsed_prompt)

            # Calcola confidence e tempo totale
            processing_time = time.time() - start_time
            overall_confidence = self._calculate_overall_confidence(prioritization_results)

            # Aggiorna metriche
            success = priority_vector is not None
            self.update_metrics(processing_time, success)

            # Crea messaggio di risposta
            response_message = NGMessage(
                source_module=NGModuleType.CONTEXT_PRIORITIZER,
                stage=NGProcessingStage.CONTEXT_ANALYSIS,
                content=priority_vector,
                confidence=overall_confidence,
                processing_time=processing_time,
                metadata={
                    'submodule_results': {
                        name: result.success for name, result in prioritization_results.items()
                    },
                    'input_tokens': len(parsed_prompt.tokens),
                    'input_segments': len(parsed_prompt.segments)
                }
            )

            logger.debug(f"✅ Context prioritization completata: confidence={overall_confidence:.3f}")
            return response_message

        except Exception as e:
            processing_time = time.time() - start_time
            logger.error(f"❌ Errore NGContextPrioritizer: {e}")

            # Aggiorna metriche con errore
            self.update_metrics(processing_time, False)

            # Crea messaggio di errore
            error_message = NGMessage(
                source_module=NGModuleType.CONTEXT_PRIORITIZER,
                stage=NGProcessingStage.CONTEXT_ANALYSIS,
                content=None,
                confidence=0.0,
                processing_time=processing_time,
                metadata={'error': str(e)}
            )

            return error_message

    def prioritize(self, parsed: ParsedPrompt) -> PriorityVector:
        """
        Implementa interfaccia NGContextPrioritizerInterface.prioritize()

        Args:
            parsed: Prompt già parsato

        Returns:
            PriorityVector con urgency, risk, domain
        """
        # Crea messaggio per il processo interno
        message = NGMessage(
            source_module=NGModuleType.CONTEXT_PRIORITIZER,
            stage=NGProcessingStage.CONTEXT_ANALYSIS,
            content=parsed
        )

        # Esegue processo interno
        result_message = self.process(message)

        if not result_message.content:
            # Fallback se processo fallisce
            return PriorityVector(
                urgency=0.5,
                risk=0.1,
                domain="general",
                complexity=0.5,
                estimated_time=30.0,
                flags=NGContextFlags(
                    urgency=NGUrgencyLevel.MEDIUM,
                    tone=NGToneType.NEUTRAL,
                    requires_inference=False,
                    requires_creativity=False,
                    requires_safety_check=False,
                    domain_hints=["general"],
                    complexity_score=0.5
                ),
                confidence=0.0,
                metadata={"error": "prioritization_failed"}
            )

        return result_message.content

    def _create_priority_vector(self, prioritization_results: Dict,
                              parsed_prompt: ParsedPrompt) -> Optional[PriorityVector]:
        """Crea PriorityVector finale dai risultati dei sotto-moduli"""
        try:
            # Estrai risultati
            urgency_result = prioritization_results.get('urgency')
            risk_result = prioritization_results.get('risk')
            priority_result = prioritization_results.get('priority')

            # Valori di default
            urgency = 0.5
            risk = 0.1
            domain = "general"
            complexity = 0.5
            estimated_time = 30.0
            flags = NGContextFlags(
                urgency=NGUrgencyLevel.MEDIUM,
                tone=NGToneType.NEUTRAL,
                requires_inference=False,
                requires_creativity=False,
                requires_safety_check=False,
                domain_hints=["general"],
                complexity_score=0.5
            )

            # Estrai dati da priority_result se disponibile
            if priority_result and priority_result.success:
                priority_data = priority_result.output_data
                urgency = priority_data.get('urgency', urgency)
                risk = priority_data.get('risk', risk)
                domain = priority_data.get('domain', domain)
                complexity = priority_data.get('complexity', complexity)
                estimated_time = priority_data.get('estimated_time', estimated_time)
                flags = priority_data.get('flags', flags)

            # Altrimenti usa risultati individuali
            else:
                if urgency_result and urgency_result.success:
                    urgency_data = urgency_result.output_data
                    urgency = urgency_data.get('urgency_score', urgency)
                    flags.urgency = urgency_data.get('urgency_level', NGUrgencyLevel.MEDIUM)
                    flags.tone = urgency_data.get('tone_type', NGToneType.NEUTRAL)

                if risk_result and risk_result.success:
                    risk_data = risk_result.output_data
                    risk = risk_data.get('risk_score', risk)
                    flags.requires_safety_check = risk_data.get('requires_review', False)

            # Calcola confidence finale
            confidences = []
            if urgency_result:
                confidences.append(urgency_result.confidence)
            if risk_result:
                confidences.append(risk_result.confidence)
            if priority_result:
                confidences.append(priority_result.confidence)

            overall_confidence = sum(confidences) / len(confidences) if confidences else 0.5

            return PriorityVector(
                urgency=urgency,
                risk=risk,
                domain=domain,
                complexity=complexity,
                estimated_time=estimated_time,
                flags=flags,
                confidence=overall_confidence,
                metadata={
                    'submodule_successes': {
                        'urgency': urgency_result.success if urgency_result else False,
                        'risk': risk_result.success if risk_result else False,
                        'priority': priority_result.success if priority_result else False
                    },
                    'input_tokens': len(parsed_prompt.tokens),
                    'input_segments': len(parsed_prompt.segments)
                }
            )

        except Exception as e:
            logger.error(f"❌ Errore creazione PriorityVector: {e}")
            return None

    def _calculate_overall_confidence(self, prioritization_results: Dict) -> float:
        """Calcola confidence complessiva dai risultati dei sotto-moduli"""
        confidences = []

        for result in prioritization_results.values():
            if result and hasattr(result, 'confidence'):
                confidences.append(result.confidence)

        if not confidences:
            return 0.5

        # Media pesata: più sotto-moduli hanno successo, più alta la confidence
        success_count = sum(1 for result in prioritization_results.values()
                          if result and result.success)
        total_count = len(prioritization_results)

        avg_confidence = sum(confidences) / len(confidences)
        success_ratio = success_count / total_count if total_count > 0 else 0

        # Combina confidence media con ratio di successo
        overall_confidence = (avg_confidence * 0.7) + (success_ratio * 0.3)

        return max(0.0, min(overall_confidence, 1.0))
