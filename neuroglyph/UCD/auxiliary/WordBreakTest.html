<!DOCTYPE HTML PUBLIC '-//W3C//DTD HTML 4.01 Transitional//EN' 'http://www.w3.org/TR/html4/loose.dtd'>
<html><head><meta http-equiv='Content-Type' content='text/html; charset=utf-8'>
<title>Word Break Chart</title>
<style type='text/css'>
td, th { vertical-align: top }
</style></head>
<body bgcolor='#FFFFFF'>
<h2>Word_Break Chart</h2>
<p><b>Unicode Version:</b> 16.0.0</p>
<p><b>Date:</b> 2023-09-19, 18:50:43 GMT</p>
<p>This page illustrates the application of the Word_Break specification. The material here is informative, not normative.</p> <p>The first chart shows where breaks would appear between different sample characters or strings. The sample characters are chosen mechanically to represent the different properties used by the specification.</p><p>Each cell shows the break-status for the position between the character(s) in its row header and the character(s) in its column header. The × symbol indicates no break, while the ÷ symbol indicated a break. The cells with × are also shaded to make it easier to scan the table. For example, in the cell at the intersection of the row headed by “CR” and the column headed by “LF”, there is a × symbol, indicating that there is no break between CR and LF.</p>
<p>After the heavy blue line in the table are additional rows, either with different sample characters or for sequences, such as “ALetter MidLetter”. Some column headers may be composed, reflecting “treat as” or “ignore” rules.</p>
<p>If your browser handles titles (tooltips), then hovering the mouse over the row header will show a sample character of that type. Hovering over a column header will show the sample character, plus its abbreviated general category and script. Hovering over the intersected cells shows the rule number that produces the break-status. For example, hovering over the cell at the intersection of ExtendNumLet and ALetter shows ×, with the rule 13.2. Checking below the table, rule 13.2 is “ExtendNumLet × (AHLetter | Numeric | Katakana)”, which is the one that applies to that case. Note that a rule is invoked only when no lower-numbered rules have applied.</p>
<h3><a href='#table' name='table'>Table</a></h3>
<table border='1' cellspacing='0' width='100%'><tr><th width='4%'></th><th width='4%' class='lbclass' title='U+0001 <START OF HEADING>, gc=Cc, sc=Zyyy'>Other</th><th width='4%' class='lbclass' title='U+000D <CARRIAGE RETURN (CR)>, gc=Cc, sc=Zyyy'>CR</th><th width='4%' class='lbclass' title='U+000A <LINE FEED (LF)>, gc=Cc, sc=Zyyy'>LF</th><th width='4%' class='lbclass' title='U+000B <LINE TABULATION>, gc=Cc, sc=Zyyy'>Newline</th><th width='4%' class='lbclass' title='U+3031 VERTICAL KANA REPEAT MARK, gc=Lm, sc=Zyyy'>Katakana</th><th width='4%' class='lbclass' title='U+0041 LATIN CAPITAL LETTER A, gc=Lu, sc=Latn'>ALetter</th><th width='4%' class='lbclass' title='U+003A COLON, gc=Po, sc=Zyyy'>MidLetter</th><th width='4%' class='lbclass' title='U+002C COMMA, gc=Po, sc=Zyyy'>MidNum</th><th width='4%' class='lbclass' title='U+002E FULL STOP, gc=Po, sc=Zyyy'>MidNumLet</th><th width='4%' class='lbclass' title='U+0030 DIGIT ZERO, gc=Nd, sc=Zyyy'>Numeric</th><th width='4%' class='lbclass' title='U+005F LOW LINE, gc=Pc, sc=Zyyy'>ExtendNumLet</th><th width='4%' class='lbclass' title='U+1F1E6 REGIONAL INDICATOR SYMBOL LETTER A, gc=So, sc=Zyyy'>RI</th><th width='4%' class='lbclass' title='U+05D0 HEBREW LETTER ALEF, gc=Lo, sc=Hebr'>Hebrew_Letter</th><th width='4%' class='lbclass' title='U+0022 QUOTATION MARK, gc=Po, sc=Zyyy'>Double_Quote</th><th width='4%' class='lbclass' title='U+0027 APOSTROPHE, gc=Po, sc=Zyyy'>Single_Quote</th><th width='4%' class='lbclass' title='U+231A WATCH, gc=So, sc=Zyyy'>ExtPict</th><th width='4%' class='lbclass' title='U+0020 SPACE, gc=Zs, sc=Zyyy'>WSegSpace</th><th width='4%' class='lbclass' title='U+00AD SOFT HYPHEN, gc=Cf, sc=Zyyy'>Format_FE</th><th width='4%' class='lbclass' title='U+0300 COMBINING GRAVE ACCENT, gc=Mn, sc=Zinh'>Extend_FE</th><th width='4%' class='lbclass' title='U+200D ZERO WIDTH JOINER, gc=Cf, sc=Zinh'>ZWJ_FE</th></tr>
<tr><th class='lbclass' title='U+0001 <START OF HEADING>'>Other</th><th title='999.0' class='pairItem'>÷</th><th title='3.2' class='pairItem'>÷</th><th title='3.2' class='pairItem'>÷</th><th title='3.2' class='pairItem'>÷</th><th title='999.0' class='pairItem'>÷</th><th title='999.0' class='pairItem'>÷</th><th title='999.0' class='pairItem'>÷</th><th title='999.0' class='pairItem'>÷</th><th title='999.0' class='pairItem'>÷</th><th title='999.0' class='pairItem'>÷</th><th title='999.0' class='pairItem'>÷</th><th title='999.0' class='pairItem'>÷</th><th title='999.0' class='pairItem'>÷</th><th title='999.0' class='pairItem'>÷</th><th title='999.0' class='pairItem'>÷</th><th title='999.0' class='pairItem'>÷</th><th title='999.0' class='pairItem'>÷</th><th title='4.0' bgcolor='#CCCCFF' class='pairItem'>×</th><th title='4.0' bgcolor='#CCCCFF' class='pairItem'>×</th><th title='4.0' bgcolor='#CCCCFF' class='pairItem'>×</th></tr>
<tr><th class='lbclass' title='U+000D <CARRIAGE RETURN (CR)>'>CR</th><th title='3.1' class='pairItem'>÷</th><th title='3.1' class='pairItem'>÷</th><th title='3.0' bgcolor='#CCCCFF' class='pairItem'>×</th><th title='3.1' class='pairItem'>÷</th><th title='3.1' class='pairItem'>÷</th><th title='3.1' class='pairItem'>÷</th><th title='3.1' class='pairItem'>÷</th><th title='3.1' class='pairItem'>÷</th><th title='3.1' class='pairItem'>÷</th><th title='3.1' class='pairItem'>÷</th><th title='3.1' class='pairItem'>÷</th><th title='3.1' class='pairItem'>÷</th><th title='3.1' class='pairItem'>÷</th><th title='3.1' class='pairItem'>÷</th><th title='3.1' class='pairItem'>÷</th><th title='3.1' class='pairItem'>÷</th><th title='3.1' class='pairItem'>÷</th><th title='3.1' class='pairItem'>÷</th><th title='3.1' class='pairItem'>÷</th><th title='3.1' class='pairItem'>÷</th></tr>
<tr><th class='lbclass' title='U+000A <LINE FEED (LF)>'>LF</th><th title='3.1' class='pairItem'>÷</th><th title='3.1' class='pairItem'>÷</th><th title='3.1' class='pairItem'>÷</th><th title='3.1' class='pairItem'>÷</th><th title='3.1' class='pairItem'>÷</th><th title='3.1' class='pairItem'>÷</th><th title='3.1' class='pairItem'>÷</th><th title='3.1' class='pairItem'>÷</th><th title='3.1' class='pairItem'>÷</th><th title='3.1' class='pairItem'>÷</th><th title='3.1' class='pairItem'>÷</th><th title='3.1' class='pairItem'>÷</th><th title='3.1' class='pairItem'>÷</th><th title='3.1' class='pairItem'>÷</th><th title='3.1' class='pairItem'>÷</th><th title='3.1' class='pairItem'>÷</th><th title='3.1' class='pairItem'>÷</th><th title='3.1' class='pairItem'>÷</th><th title='3.1' class='pairItem'>÷</th><th title='3.1' class='pairItem'>÷</th></tr>
<tr><th class='lbclass' title='U+000B <LINE TABULATION>'>Newline</th><th title='3.1' class='pairItem'>÷</th><th title='3.1' class='pairItem'>÷</th><th title='3.1' class='pairItem'>÷</th><th title='3.1' class='pairItem'>÷</th><th title='3.1' class='pairItem'>÷</th><th title='3.1' class='pairItem'>÷</th><th title='3.1' class='pairItem'>÷</th><th title='3.1' class='pairItem'>÷</th><th title='3.1' class='pairItem'>÷</th><th title='3.1' class='pairItem'>÷</th><th title='3.1' class='pairItem'>÷</th><th title='3.1' class='pairItem'>÷</th><th title='3.1' class='pairItem'>÷</th><th title='3.1' class='pairItem'>÷</th><th title='3.1' class='pairItem'>÷</th><th title='3.1' class='pairItem'>÷</th><th title='3.1' class='pairItem'>÷</th><th title='3.1' class='pairItem'>÷</th><th title='3.1' class='pairItem'>÷</th><th title='3.1' class='pairItem'>÷</th></tr>
<tr><th class='lbclass' title='U+3031 VERTICAL KANA REPEAT MARK'>Katakana</th><th title='999.0' class='pairItem'>÷</th><th title='3.2' class='pairItem'>÷</th><th title='3.2' class='pairItem'>÷</th><th title='3.2' class='pairItem'>÷</th><th title='13.0' bgcolor='#CCCCFF' class='pairItem'>×</th><th title='999.0' class='pairItem'>÷</th><th title='999.0' class='pairItem'>÷</th><th title='999.0' class='pairItem'>÷</th><th title='999.0' class='pairItem'>÷</th><th title='999.0' class='pairItem'>÷</th><th title='13.1' bgcolor='#CCCCFF' class='pairItem'>×</th><th title='999.0' class='pairItem'>÷</th><th title='999.0' class='pairItem'>÷</th><th title='999.0' class='pairItem'>÷</th><th title='999.0' class='pairItem'>÷</th><th title='999.0' class='pairItem'>÷</th><th title='999.0' class='pairItem'>÷</th><th title='4.0' bgcolor='#CCCCFF' class='pairItem'>×</th><th title='4.0' bgcolor='#CCCCFF' class='pairItem'>×</th><th title='4.0' bgcolor='#CCCCFF' class='pairItem'>×</th></tr>
<tr><th class='lbclass' title='U+0041 LATIN CAPITAL LETTER A'>ALetter</th><th title='999.0' class='pairItem'>÷</th><th title='3.2' class='pairItem'>÷</th><th title='3.2' class='pairItem'>÷</th><th title='3.2' class='pairItem'>÷</th><th title='999.0' class='pairItem'>÷</th><th title='5.0' bgcolor='#CCCCFF' class='pairItem'>×</th><th title='999.0' class='pairItem'>÷</th><th title='999.0' class='pairItem'>÷</th><th title='999.0' class='pairItem'>÷</th><th title='9.0' bgcolor='#CCCCFF' class='pairItem'>×</th><th title='13.1' bgcolor='#CCCCFF' class='pairItem'>×</th><th title='999.0' class='pairItem'>÷</th><th title='5.0' bgcolor='#CCCCFF' class='pairItem'>×</th><th title='999.0' class='pairItem'>÷</th><th title='999.0' class='pairItem'>÷</th><th title='999.0' class='pairItem'>÷</th><th title='999.0' class='pairItem'>÷</th><th title='4.0' bgcolor='#CCCCFF' class='pairItem'>×</th><th title='4.0' bgcolor='#CCCCFF' class='pairItem'>×</th><th title='4.0' bgcolor='#CCCCFF' class='pairItem'>×</th></tr>
<tr><th class='lbclass' title='U+003A COLON'>MidLetter</th><th title='999.0' class='pairItem'>÷</th><th title='3.2' class='pairItem'>÷</th><th title='3.2' class='pairItem'>÷</th><th title='3.2' class='pairItem'>÷</th><th title='999.0' class='pairItem'>÷</th><th title='999.0' class='pairItem'>÷</th><th title='999.0' class='pairItem'>÷</th><th title='999.0' class='pairItem'>÷</th><th title='999.0' class='pairItem'>÷</th><th title='999.0' class='pairItem'>÷</th><th title='999.0' class='pairItem'>÷</th><th title='999.0' class='pairItem'>÷</th><th title='999.0' class='pairItem'>÷</th><th title='999.0' class='pairItem'>÷</th><th title='999.0' class='pairItem'>÷</th><th title='999.0' class='pairItem'>÷</th><th title='999.0' class='pairItem'>÷</th><th title='4.0' bgcolor='#CCCCFF' class='pairItem'>×</th><th title='4.0' bgcolor='#CCCCFF' class='pairItem'>×</th><th title='4.0' bgcolor='#CCCCFF' class='pairItem'>×</th></tr>
<tr><th class='lbclass' title='U+002C COMMA'>MidNum</th><th title='999.0' class='pairItem'>÷</th><th title='3.2' class='pairItem'>÷</th><th title='3.2' class='pairItem'>÷</th><th title='3.2' class='pairItem'>÷</th><th title='999.0' class='pairItem'>÷</th><th title='999.0' class='pairItem'>÷</th><th title='999.0' class='pairItem'>÷</th><th title='999.0' class='pairItem'>÷</th><th title='999.0' class='pairItem'>÷</th><th title='999.0' class='pairItem'>÷</th><th title='999.0' class='pairItem'>÷</th><th title='999.0' class='pairItem'>÷</th><th title='999.0' class='pairItem'>÷</th><th title='999.0' class='pairItem'>÷</th><th title='999.0' class='pairItem'>÷</th><th title='999.0' class='pairItem'>÷</th><th title='999.0' class='pairItem'>÷</th><th title='4.0' bgcolor='#CCCCFF' class='pairItem'>×</th><th title='4.0' bgcolor='#CCCCFF' class='pairItem'>×</th><th title='4.0' bgcolor='#CCCCFF' class='pairItem'>×</th></tr>
<tr><th class='lbclass' title='U+002E FULL STOP'>MidNumLet</th><th title='999.0' class='pairItem'>÷</th><th title='3.2' class='pairItem'>÷</th><th title='3.2' class='pairItem'>÷</th><th title='3.2' class='pairItem'>÷</th><th title='999.0' class='pairItem'>÷</th><th title='999.0' class='pairItem'>÷</th><th title='999.0' class='pairItem'>÷</th><th title='999.0' class='pairItem'>÷</th><th title='999.0' class='pairItem'>÷</th><th title='999.0' class='pairItem'>÷</th><th title='999.0' class='pairItem'>÷</th><th title='999.0' class='pairItem'>÷</th><th title='999.0' class='pairItem'>÷</th><th title='999.0' class='pairItem'>÷</th><th title='999.0' class='pairItem'>÷</th><th title='999.0' class='pairItem'>÷</th><th title='999.0' class='pairItem'>÷</th><th title='4.0' bgcolor='#CCCCFF' class='pairItem'>×</th><th title='4.0' bgcolor='#CCCCFF' class='pairItem'>×</th><th title='4.0' bgcolor='#CCCCFF' class='pairItem'>×</th></tr>
<tr><th class='lbclass' title='U+0030 DIGIT ZERO'>Numeric</th><th title='999.0' class='pairItem'>÷</th><th title='3.2' class='pairItem'>÷</th><th title='3.2' class='pairItem'>÷</th><th title='3.2' class='pairItem'>÷</th><th title='999.0' class='pairItem'>÷</th><th title='10.0' bgcolor='#CCCCFF' class='pairItem'>×</th><th title='999.0' class='pairItem'>÷</th><th title='999.0' class='pairItem'>÷</th><th title='999.0' class='pairItem'>÷</th><th title='8.0' bgcolor='#CCCCFF' class='pairItem'>×</th><th title='13.1' bgcolor='#CCCCFF' class='pairItem'>×</th><th title='999.0' class='pairItem'>÷</th><th title='10.0' bgcolor='#CCCCFF' class='pairItem'>×</th><th title='999.0' class='pairItem'>÷</th><th title='999.0' class='pairItem'>÷</th><th title='999.0' class='pairItem'>÷</th><th title='999.0' class='pairItem'>÷</th><th title='4.0' bgcolor='#CCCCFF' class='pairItem'>×</th><th title='4.0' bgcolor='#CCCCFF' class='pairItem'>×</th><th title='4.0' bgcolor='#CCCCFF' class='pairItem'>×</th></tr>
<tr><th class='lbclass' title='U+005F LOW LINE'>ExtendNumLet</th><th title='999.0' class='pairItem'>÷</th><th title='3.2' class='pairItem'>÷</th><th title='3.2' class='pairItem'>÷</th><th title='3.2' class='pairItem'>÷</th><th title='13.2' bgcolor='#CCCCFF' class='pairItem'>×</th><th title='13.2' bgcolor='#CCCCFF' class='pairItem'>×</th><th title='999.0' class='pairItem'>÷</th><th title='999.0' class='pairItem'>÷</th><th title='999.0' class='pairItem'>÷</th><th title='13.2' bgcolor='#CCCCFF' class='pairItem'>×</th><th title='13.1' bgcolor='#CCCCFF' class='pairItem'>×</th><th title='999.0' class='pairItem'>÷</th><th title='13.2' bgcolor='#CCCCFF' class='pairItem'>×</th><th title='999.0' class='pairItem'>÷</th><th title='999.0' class='pairItem'>÷</th><th title='999.0' class='pairItem'>÷</th><th title='999.0' class='pairItem'>÷</th><th title='4.0' bgcolor='#CCCCFF' class='pairItem'>×</th><th title='4.0' bgcolor='#CCCCFF' class='pairItem'>×</th><th title='4.0' bgcolor='#CCCCFF' class='pairItem'>×</th></tr>
<tr><th class='lbclass' title='U+1F1E6 REGIONAL INDICATOR SYMBOL LETTER A'>RI</th><th title='999.0' class='pairItem'>÷</th><th title='3.2' class='pairItem'>÷</th><th title='3.2' class='pairItem'>÷</th><th title='3.2' class='pairItem'>÷</th><th title='999.0' class='pairItem'>÷</th><th title='999.0' class='pairItem'>÷</th><th title='999.0' class='pairItem'>÷</th><th title='999.0' class='pairItem'>÷</th><th title='999.0' class='pairItem'>÷</th><th title='999.0' class='pairItem'>÷</th><th title='999.0' class='pairItem'>÷</th><th title='15.0' bgcolor='#CCCCFF' class='pairItem'>×</th><th title='999.0' class='pairItem'>÷</th><th title='999.0' class='pairItem'>÷</th><th title='999.0' class='pairItem'>÷</th><th title='999.0' class='pairItem'>÷</th><th title='999.0' class='pairItem'>÷</th><th title='4.0' bgcolor='#CCCCFF' class='pairItem'>×</th><th title='4.0' bgcolor='#CCCCFF' class='pairItem'>×</th><th title='4.0' bgcolor='#CCCCFF' class='pairItem'>×</th></tr>
<tr><th class='lbclass' title='U+05D0 HEBREW LETTER ALEF'>Hebrew_Letter</th><th title='999.0' class='pairItem'>÷</th><th title='3.2' class='pairItem'>÷</th><th title='3.2' class='pairItem'>÷</th><th title='3.2' class='pairItem'>÷</th><th title='999.0' class='pairItem'>÷</th><th title='5.0' bgcolor='#CCCCFF' class='pairItem'>×</th><th title='999.0' class='pairItem'>÷</th><th title='999.0' class='pairItem'>÷</th><th title='999.0' class='pairItem'>÷</th><th title='9.0' bgcolor='#CCCCFF' class='pairItem'>×</th><th title='13.1' bgcolor='#CCCCFF' class='pairItem'>×</th><th title='999.0' class='pairItem'>÷</th><th title='5.0' bgcolor='#CCCCFF' class='pairItem'>×</th><th title='999.0' class='pairItem'>÷</th><th title='7.1' bgcolor='#CCCCFF' class='pairItem'>×</th><th title='999.0' class='pairItem'>÷</th><th title='999.0' class='pairItem'>÷</th><th title='4.0' bgcolor='#CCCCFF' class='pairItem'>×</th><th title='4.0' bgcolor='#CCCCFF' class='pairItem'>×</th><th title='4.0' bgcolor='#CCCCFF' class='pairItem'>×</th></tr>
<tr><th class='lbclass' title='U+0022 QUOTATION MARK'>Double_Quote</th><th title='999.0' class='pairItem'>÷</th><th title='3.2' class='pairItem'>÷</th><th title='3.2' class='pairItem'>÷</th><th title='3.2' class='pairItem'>÷</th><th title='999.0' class='pairItem'>÷</th><th title='999.0' class='pairItem'>÷</th><th title='999.0' class='pairItem'>÷</th><th title='999.0' class='pairItem'>÷</th><th title='999.0' class='pairItem'>÷</th><th title='999.0' class='pairItem'>÷</th><th title='999.0' class='pairItem'>÷</th><th title='999.0' class='pairItem'>÷</th><th title='999.0' class='pairItem'>÷</th><th title='999.0' class='pairItem'>÷</th><th title='999.0' class='pairItem'>÷</th><th title='999.0' class='pairItem'>÷</th><th title='999.0' class='pairItem'>÷</th><th title='4.0' bgcolor='#CCCCFF' class='pairItem'>×</th><th title='4.0' bgcolor='#CCCCFF' class='pairItem'>×</th><th title='4.0' bgcolor='#CCCCFF' class='pairItem'>×</th></tr>
<tr><th class='lbclass' title='U+0027 APOSTROPHE'>Single_Quote</th><th title='999.0' class='pairItem'>÷</th><th title='3.2' class='pairItem'>÷</th><th title='3.2' class='pairItem'>÷</th><th title='3.2' class='pairItem'>÷</th><th title='999.0' class='pairItem'>÷</th><th title='999.0' class='pairItem'>÷</th><th title='999.0' class='pairItem'>÷</th><th title='999.0' class='pairItem'>÷</th><th title='999.0' class='pairItem'>÷</th><th title='999.0' class='pairItem'>÷</th><th title='999.0' class='pairItem'>÷</th><th title='999.0' class='pairItem'>÷</th><th title='999.0' class='pairItem'>÷</th><th title='999.0' class='pairItem'>÷</th><th title='999.0' class='pairItem'>÷</th><th title='999.0' class='pairItem'>÷</th><th title='999.0' class='pairItem'>÷</th><th title='4.0' bgcolor='#CCCCFF' class='pairItem'>×</th><th title='4.0' bgcolor='#CCCCFF' class='pairItem'>×</th><th title='4.0' bgcolor='#CCCCFF' class='pairItem'>×</th></tr>
<tr><th class='lbclass' title='U+231A WATCH'>ExtPict</th><th title='999.0' class='pairItem'>÷</th><th title='3.2' class='pairItem'>÷</th><th title='3.2' class='pairItem'>÷</th><th title='3.2' class='pairItem'>÷</th><th title='999.0' class='pairItem'>÷</th><th title='999.0' class='pairItem'>÷</th><th title='999.0' class='pairItem'>÷</th><th title='999.0' class='pairItem'>÷</th><th title='999.0' class='pairItem'>÷</th><th title='999.0' class='pairItem'>÷</th><th title='999.0' class='pairItem'>÷</th><th title='999.0' class='pairItem'>÷</th><th title='999.0' class='pairItem'>÷</th><th title='999.0' class='pairItem'>÷</th><th title='999.0' class='pairItem'>÷</th><th title='999.0' class='pairItem'>÷</th><th title='999.0' class='pairItem'>÷</th><th title='4.0' bgcolor='#CCCCFF' class='pairItem'>×</th><th title='4.0' bgcolor='#CCCCFF' class='pairItem'>×</th><th title='4.0' bgcolor='#CCCCFF' class='pairItem'>×</th></tr>
<tr><th class='lbclass' title='U+0020 SPACE'>WSegSpace</th><th title='999.0' class='pairItem'>÷</th><th title='3.2' class='pairItem'>÷</th><th title='3.2' class='pairItem'>÷</th><th title='3.2' class='pairItem'>÷</th><th title='999.0' class='pairItem'>÷</th><th title='999.0' class='pairItem'>÷</th><th title='999.0' class='pairItem'>÷</th><th title='999.0' class='pairItem'>÷</th><th title='999.0' class='pairItem'>÷</th><th title='999.0' class='pairItem'>÷</th><th title='999.0' class='pairItem'>÷</th><th title='999.0' class='pairItem'>÷</th><th title='999.0' class='pairItem'>÷</th><th title='999.0' class='pairItem'>÷</th><th title='999.0' class='pairItem'>÷</th><th title='999.0' class='pairItem'>÷</th><th title='3.4' bgcolor='#CCCCFF' class='pairItem'>×</th><th title='4.0' bgcolor='#CCCCFF' class='pairItem'>×</th><th title='4.0' bgcolor='#CCCCFF' class='pairItem'>×</th><th title='4.0' bgcolor='#CCCCFF' class='pairItem'>×</th></tr>
<tr><th class='lbclass' title='U+00AD SOFT HYPHEN'>Format_FE</th><th title='999.0' class='pairItem'>÷</th><th title='3.2' class='pairItem'>÷</th><th title='3.2' class='pairItem'>÷</th><th title='3.2' class='pairItem'>÷</th><th title='999.0' class='pairItem'>÷</th><th title='999.0' class='pairItem'>÷</th><th title='999.0' class='pairItem'>÷</th><th title='999.0' class='pairItem'>÷</th><th title='999.0' class='pairItem'>÷</th><th title='999.0' class='pairItem'>÷</th><th title='999.0' class='pairItem'>÷</th><th title='999.0' class='pairItem'>÷</th><th title='999.0' class='pairItem'>÷</th><th title='999.0' class='pairItem'>÷</th><th title='999.0' class='pairItem'>÷</th><th title='999.0' class='pairItem'>÷</th><th title='999.0' class='pairItem'>÷</th><th title='4.0' bgcolor='#CCCCFF' class='pairItem'>×</th><th title='4.0' bgcolor='#CCCCFF' class='pairItem'>×</th><th title='4.0' bgcolor='#CCCCFF' class='pairItem'>×</th></tr>
<tr><th class='lbclass' title='U+0300 COMBINING GRAVE ACCENT'>Extend_FE</th><th title='999.0' class='pairItem'>÷</th><th title='3.2' class='pairItem'>÷</th><th title='3.2' class='pairItem'>÷</th><th title='3.2' class='pairItem'>÷</th><th title='999.0' class='pairItem'>÷</th><th title='999.0' class='pairItem'>÷</th><th title='999.0' class='pairItem'>÷</th><th title='999.0' class='pairItem'>÷</th><th title='999.0' class='pairItem'>÷</th><th title='999.0' class='pairItem'>÷</th><th title='999.0' class='pairItem'>÷</th><th title='999.0' class='pairItem'>÷</th><th title='999.0' class='pairItem'>÷</th><th title='999.0' class='pairItem'>÷</th><th title='999.0' class='pairItem'>÷</th><th title='999.0' class='pairItem'>÷</th><th title='999.0' class='pairItem'>÷</th><th title='4.0' bgcolor='#CCCCFF' class='pairItem'>×</th><th title='4.0' bgcolor='#CCCCFF' class='pairItem'>×</th><th title='4.0' bgcolor='#CCCCFF' class='pairItem'>×</th></tr>
<tr><th class='lbclass' title='U+200D ZERO WIDTH JOINER'>ZWJ_FE</th><th title='999.0' class='pairItem'>÷</th><th title='3.2' class='pairItem'>÷</th><th title='3.2' class='pairItem'>÷</th><th title='3.2' class='pairItem'>÷</th><th title='999.0' class='pairItem'>÷</th><th title='999.0' class='pairItem'>÷</th><th title='999.0' class='pairItem'>÷</th><th title='999.0' class='pairItem'>÷</th><th title='999.0' class='pairItem'>÷</th><th title='999.0' class='pairItem'>÷</th><th title='999.0' class='pairItem'>÷</th><th title='999.0' class='pairItem'>÷</th><th title='999.0' class='pairItem'>÷</th><th title='999.0' class='pairItem'>÷</th><th title='999.0' class='pairItem'>÷</th><th title='3.3' bgcolor='#CCCCFF' class='pairItem'>×</th><th title='999.0' class='pairItem'>÷</th><th title='4.0' bgcolor='#CCCCFF' class='pairItem'>×</th><th title='4.0' bgcolor='#CCCCFF' class='pairItem'>×</th><th title='4.0' bgcolor='#CCCCFF' class='pairItem'>×</th></tr>
<tr><td bgcolor='#0000FF' colSpan='21' style='font-size: 1px'>&nbsp;</td></tr>
<tr><th class='lbclass' title='U+0061 LATIN SMALL LETTER A, U+2060 WORD JOINER'>ALetter Format_FE</th><th title='999.0' class='pairItem'>÷</th><th title='3.2' class='pairItem'>÷</th><th title='3.2' class='pairItem'>÷</th><th title='3.2' class='pairItem'>÷</th><th title='999.0' class='pairItem'>÷</th><th title='5.0' bgcolor='#CCCCFF' class='pairItem'>×</th><th title='999.0' class='pairItem'>÷</th><th title='999.0' class='pairItem'>÷</th><th title='999.0' class='pairItem'>÷</th><th title='9.0' bgcolor='#CCCCFF' class='pairItem'>×</th><th title='13.1' bgcolor='#CCCCFF' class='pairItem'>×</th><th title='999.0' class='pairItem'>÷</th><th title='5.0' bgcolor='#CCCCFF' class='pairItem'>×</th><th title='999.0' class='pairItem'>÷</th><th title='999.0' class='pairItem'>÷</th><th title='999.0' class='pairItem'>÷</th><th title='999.0' class='pairItem'>÷</th><th title='4.0' bgcolor='#CCCCFF' class='pairItem'>×</th><th title='4.0' bgcolor='#CCCCFF' class='pairItem'>×</th><th title='4.0' bgcolor='#CCCCFF' class='pairItem'>×</th></tr>
<tr><th class='lbclass' title='U+0061 LATIN SMALL LETTER A, U+003A COLON'>ALetter MidLetter</th><th title='999.0' class='pairItem'>÷</th><th title='3.2' class='pairItem'>÷</th><th title='3.2' class='pairItem'>÷</th><th title='3.2' class='pairItem'>÷</th><th title='999.0' class='pairItem'>÷</th><th title='7.0' bgcolor='#CCCCFF' class='pairItem'>×</th><th title='999.0' class='pairItem'>÷</th><th title='999.0' class='pairItem'>÷</th><th title='999.0' class='pairItem'>÷</th><th title='999.0' class='pairItem'>÷</th><th title='999.0' class='pairItem'>÷</th><th title='999.0' class='pairItem'>÷</th><th title='7.0' bgcolor='#CCCCFF' class='pairItem'>×</th><th title='999.0' class='pairItem'>÷</th><th title='999.0' class='pairItem'>÷</th><th title='999.0' class='pairItem'>÷</th><th title='999.0' class='pairItem'>÷</th><th title='4.0' bgcolor='#CCCCFF' class='pairItem'>×</th><th title='4.0' bgcolor='#CCCCFF' class='pairItem'>×</th><th title='4.0' bgcolor='#CCCCFF' class='pairItem'>×</th></tr>
<tr><th class='lbclass' title='U+0061 LATIN SMALL LETTER A, U+0027 APOSTROPHE'>ALetter Single_Quote</th><th title='999.0' class='pairItem'>÷</th><th title='3.2' class='pairItem'>÷</th><th title='3.2' class='pairItem'>÷</th><th title='3.2' class='pairItem'>÷</th><th title='999.0' class='pairItem'>÷</th><th title='7.0' bgcolor='#CCCCFF' class='pairItem'>×</th><th title='999.0' class='pairItem'>÷</th><th title='999.0' class='pairItem'>÷</th><th title='999.0' class='pairItem'>÷</th><th title='999.0' class='pairItem'>÷</th><th title='999.0' class='pairItem'>÷</th><th title='999.0' class='pairItem'>÷</th><th title='7.0' bgcolor='#CCCCFF' class='pairItem'>×</th><th title='999.0' class='pairItem'>÷</th><th title='999.0' class='pairItem'>÷</th><th title='999.0' class='pairItem'>÷</th><th title='999.0' class='pairItem'>÷</th><th title='4.0' bgcolor='#CCCCFF' class='pairItem'>×</th><th title='4.0' bgcolor='#CCCCFF' class='pairItem'>×</th><th title='4.0' bgcolor='#CCCCFF' class='pairItem'>×</th></tr>
<tr><th class='lbclass' title='U+0061 LATIN SMALL LETTER A, U+0027 APOSTROPHE, U+2060 WORD JOINER'>ALetter Single_Quote Format_FE</th><th title='999.0' class='pairItem'>÷</th><th title='3.2' class='pairItem'>÷</th><th title='3.2' class='pairItem'>÷</th><th title='3.2' class='pairItem'>÷</th><th title='999.0' class='pairItem'>÷</th><th title='7.0' bgcolor='#CCCCFF' class='pairItem'>×</th><th title='999.0' class='pairItem'>÷</th><th title='999.0' class='pairItem'>÷</th><th title='999.0' class='pairItem'>÷</th><th title='999.0' class='pairItem'>÷</th><th title='999.0' class='pairItem'>÷</th><th title='999.0' class='pairItem'>÷</th><th title='7.0' bgcolor='#CCCCFF' class='pairItem'>×</th><th title='999.0' class='pairItem'>÷</th><th title='999.0' class='pairItem'>÷</th><th title='999.0' class='pairItem'>÷</th><th title='999.0' class='pairItem'>÷</th><th title='4.0' bgcolor='#CCCCFF' class='pairItem'>×</th><th title='4.0' bgcolor='#CCCCFF' class='pairItem'>×</th><th title='4.0' bgcolor='#CCCCFF' class='pairItem'>×</th></tr>
<tr><th class='lbclass' title='U+0061 LATIN SMALL LETTER A, U+002C COMMA'>ALetter MidNum</th><th title='999.0' class='pairItem'>÷</th><th title='3.2' class='pairItem'>÷</th><th title='3.2' class='pairItem'>÷</th><th title='3.2' class='pairItem'>÷</th><th title='999.0' class='pairItem'>÷</th><th title='999.0' class='pairItem'>÷</th><th title='999.0' class='pairItem'>÷</th><th title='999.0' class='pairItem'>÷</th><th title='999.0' class='pairItem'>÷</th><th title='999.0' class='pairItem'>÷</th><th title='999.0' class='pairItem'>÷</th><th title='999.0' class='pairItem'>÷</th><th title='999.0' class='pairItem'>÷</th><th title='999.0' class='pairItem'>÷</th><th title='999.0' class='pairItem'>÷</th><th title='999.0' class='pairItem'>÷</th><th title='999.0' class='pairItem'>÷</th><th title='4.0' bgcolor='#CCCCFF' class='pairItem'>×</th><th title='4.0' bgcolor='#CCCCFF' class='pairItem'>×</th><th title='4.0' bgcolor='#CCCCFF' class='pairItem'>×</th></tr>
<tr><th class='lbclass' title='U+0031 DIGIT ONE, U+003A COLON'>Numeric MidLetter</th><th title='999.0' class='pairItem'>÷</th><th title='3.2' class='pairItem'>÷</th><th title='3.2' class='pairItem'>÷</th><th title='3.2' class='pairItem'>÷</th><th title='999.0' class='pairItem'>÷</th><th title='999.0' class='pairItem'>÷</th><th title='999.0' class='pairItem'>÷</th><th title='999.0' class='pairItem'>÷</th><th title='999.0' class='pairItem'>÷</th><th title='999.0' class='pairItem'>÷</th><th title='999.0' class='pairItem'>÷</th><th title='999.0' class='pairItem'>÷</th><th title='999.0' class='pairItem'>÷</th><th title='999.0' class='pairItem'>÷</th><th title='999.0' class='pairItem'>÷</th><th title='999.0' class='pairItem'>÷</th><th title='999.0' class='pairItem'>÷</th><th title='4.0' bgcolor='#CCCCFF' class='pairItem'>×</th><th title='4.0' bgcolor='#CCCCFF' class='pairItem'>×</th><th title='4.0' bgcolor='#CCCCFF' class='pairItem'>×</th></tr>
<tr><th class='lbclass' title='U+0031 DIGIT ONE, U+0027 APOSTROPHE'>Numeric Single_Quote</th><th title='999.0' class='pairItem'>÷</th><th title='3.2' class='pairItem'>÷</th><th title='3.2' class='pairItem'>÷</th><th title='3.2' class='pairItem'>÷</th><th title='999.0' class='pairItem'>÷</th><th title='999.0' class='pairItem'>÷</th><th title='999.0' class='pairItem'>÷</th><th title='999.0' class='pairItem'>÷</th><th title='999.0' class='pairItem'>÷</th><th title='11.0' bgcolor='#CCCCFF' class='pairItem'>×</th><th title='999.0' class='pairItem'>÷</th><th title='999.0' class='pairItem'>÷</th><th title='999.0' class='pairItem'>÷</th><th title='999.0' class='pairItem'>÷</th><th title='999.0' class='pairItem'>÷</th><th title='999.0' class='pairItem'>÷</th><th title='999.0' class='pairItem'>÷</th><th title='4.0' bgcolor='#CCCCFF' class='pairItem'>×</th><th title='4.0' bgcolor='#CCCCFF' class='pairItem'>×</th><th title='4.0' bgcolor='#CCCCFF' class='pairItem'>×</th></tr>
<tr><th class='lbclass' title='U+0031 DIGIT ONE, U+002C COMMA'>Numeric MidNum</th><th title='999.0' class='pairItem'>÷</th><th title='3.2' class='pairItem'>÷</th><th title='3.2' class='pairItem'>÷</th><th title='3.2' class='pairItem'>÷</th><th title='999.0' class='pairItem'>÷</th><th title='999.0' class='pairItem'>÷</th><th title='999.0' class='pairItem'>÷</th><th title='999.0' class='pairItem'>÷</th><th title='999.0' class='pairItem'>÷</th><th title='11.0' bgcolor='#CCCCFF' class='pairItem'>×</th><th title='999.0' class='pairItem'>÷</th><th title='999.0' class='pairItem'>÷</th><th title='999.0' class='pairItem'>÷</th><th title='999.0' class='pairItem'>÷</th><th title='999.0' class='pairItem'>÷</th><th title='999.0' class='pairItem'>÷</th><th title='999.0' class='pairItem'>÷</th><th title='4.0' bgcolor='#CCCCFF' class='pairItem'>×</th><th title='4.0' bgcolor='#CCCCFF' class='pairItem'>×</th><th title='4.0' bgcolor='#CCCCFF' class='pairItem'>×</th></tr>
<tr><th class='lbclass' title='U+0031 DIGIT ONE, U+002E FULL STOP, U+2060 WORD JOINER'>Numeric MidNumLet Format_FE</th><th title='999.0' class='pairItem'>÷</th><th title='3.2' class='pairItem'>÷</th><th title='3.2' class='pairItem'>÷</th><th title='3.2' class='pairItem'>÷</th><th title='999.0' class='pairItem'>÷</th><th title='999.0' class='pairItem'>÷</th><th title='999.0' class='pairItem'>÷</th><th title='999.0' class='pairItem'>÷</th><th title='999.0' class='pairItem'>÷</th><th title='11.0' bgcolor='#CCCCFF' class='pairItem'>×</th><th title='999.0' class='pairItem'>÷</th><th title='999.0' class='pairItem'>÷</th><th title='999.0' class='pairItem'>÷</th><th title='999.0' class='pairItem'>÷</th><th title='999.0' class='pairItem'>÷</th><th title='999.0' class='pairItem'>÷</th><th title='999.0' class='pairItem'>÷</th><th title='4.0' bgcolor='#CCCCFF' class='pairItem'>×</th><th title='4.0' bgcolor='#CCCCFF' class='pairItem'>×</th><th title='4.0' bgcolor='#CCCCFF' class='pairItem'>×</th></tr>
</table>
<h3><a href='#rules' name='rules'>Rules</a></h3>
<p>This section shows the rules. They are mechanically modified for programmatic generation of the tables and test code, and thus do not match the UAX rules precisely. In particular:</p><ol><li>The rules are cast into a form that is more like regular expressions.</li><li>The rules “sot ÷”, “÷ eot”, and “÷ Any” are added mechanically, and have artificial numbers.</li><li>The rules are given decimal numbers using tenths, and are written without prefix. For example, rule WB13a is given the number 13.1.</li><li>Any “treat as” or “ignore” rules are handled as discussed in UAX #29, and thus reflected in a transformation of the rules usually not visible here. In addition, final rules like “Any ÷ Any” may be recast as the equivalent expression “÷ Any”.</li><li>In some cases, the numbering and form of a rule is changed due to “treat as” rules.</li></ol><p>For the original rules and the macro values they use, see UAX #29.</p>
<table>
<tr><th style='text-align:right'><a href='#r0.2' name='r0.2'>0.2</a></th><td style='text-align:right'>sot </td><td>÷</td><td></td></tr>
<tr><th style='text-align:right'><a href='#r0.3' name='r0.3'>0.3</a></th><td style='text-align:right'></td><td>÷</td><td> eot</td></tr>
<tr><th style='text-align:right'><a href='#r3.0' name='r3.0'>3.0</a></th><td style='text-align:right'>CR </td><td>×</td><td> LF</td></tr>
<tr><th style='text-align:right'><a href='#r3.1' name='r3.1'>3.1</a></th><td style='text-align:right'>(Newline | CR | LF) </td><td>÷</td><td></td></tr>
<tr><th style='text-align:right'><a href='#r3.2' name='r3.2'>3.2</a></th><td style='text-align:right'></td><td>÷</td><td> (Newline | CR | LF)</td></tr>
<tr><th style='text-align:right'><a href='#r3.3' name='r3.3'>3.3</a></th><td style='text-align:right'>ZWJ </td><td>×</td><td> ExtPict</td></tr>
<tr><th style='text-align:right'><a href='#r3.4' name='r3.4'>3.4</a></th><td style='text-align:right'>WSegSpace </td><td>×</td><td> WSegSpace</td></tr>
<tr><th style='text-align:right'><a href='#r4.0' name='r4.0'>4.0</a></th><td style='text-align:right'>[^ Newline CR LF ] </td><td>×</td><td> [Format Extend ZWJ]</td></tr>
<tr><th style='text-align:right'><a href='#r5.0' name='r5.0'>5.0</a></th><td style='text-align:right'>AHLetter </td><td>×</td><td> AHLetter</td></tr>
<tr><th style='text-align:right'><a href='#r6.0' name='r6.0'>6.0</a></th><td style='text-align:right'>AHLetter </td><td>×</td><td> (MidLetter | MidNumLetQ) AHLetter</td></tr>
<tr><th style='text-align:right'><a href='#r7.0' name='r7.0'>7.0</a></th><td style='text-align:right'>AHLetter (MidLetter | MidNumLetQ) </td><td>×</td><td> AHLetter</td></tr>
<tr><th style='text-align:right'><a href='#r7.1' name='r7.1'>7.1</a></th><td style='text-align:right'>Hebrew_Letter </td><td>×</td><td> Single_Quote</td></tr>
<tr><th style='text-align:right'><a href='#r7.2' name='r7.2'>7.2</a></th><td style='text-align:right'>Hebrew_Letter </td><td>×</td><td> Double_Quote Hebrew_Letter</td></tr>
<tr><th style='text-align:right'><a href='#r7.3' name='r7.3'>7.3</a></th><td style='text-align:right'>Hebrew_Letter Double_Quote </td><td>×</td><td> Hebrew_Letter</td></tr>
<tr><th style='text-align:right'><a href='#r8.0' name='r8.0'>8.0</a></th><td style='text-align:right'>Numeric </td><td>×</td><td> Numeric</td></tr>
<tr><th style='text-align:right'><a href='#r9.0' name='r9.0'>9.0</a></th><td style='text-align:right'>AHLetter </td><td>×</td><td> Numeric</td></tr>
<tr><th style='text-align:right'><a href='#r10.0' name='r10.0'>10.0</a></th><td style='text-align:right'>Numeric </td><td>×</td><td> AHLetter</td></tr>
<tr><th style='text-align:right'><a href='#r11.0' name='r11.0'>11.0</a></th><td style='text-align:right'>Numeric (MidNum | MidNumLetQ) </td><td>×</td><td> Numeric</td></tr>
<tr><th style='text-align:right'><a href='#r12.0' name='r12.0'>12.0</a></th><td style='text-align:right'>Numeric </td><td>×</td><td> (MidNum | MidNumLetQ) Numeric</td></tr>
<tr><th style='text-align:right'><a href='#r13.0' name='r13.0'>13.0</a></th><td style='text-align:right'>Katakana </td><td>×</td><td> Katakana</td></tr>
<tr><th style='text-align:right'><a href='#r13.1' name='r13.1'>13.1</a></th><td style='text-align:right'>(AHLetter | Numeric | Katakana | ExtendNumLet) </td><td>×</td><td> ExtendNumLet</td></tr>
<tr><th style='text-align:right'><a href='#r13.2' name='r13.2'>13.2</a></th><td style='text-align:right'>ExtendNumLet </td><td>×</td><td> (AHLetter | Numeric | Katakana)</td></tr>
<tr><th style='text-align:right'><a href='#r15.0' name='r15.0'>15.0</a></th><td style='text-align:right'>^ (RI RI)* RI </td><td>×</td><td> RI</td></tr>
<tr><th style='text-align:right'><a href='#r16.0' name='r16.0'>16.0</a></th><td style='text-align:right'>[^RI] (RI RI)* RI </td><td>×</td><td> RI</td></tr>
<tr><th style='text-align:right'><a href='#r999.0' name='r999.0'>999.0</a></th><td style='text-align:right'></td><td>÷</td><td> Any</td></tr>
</table>
<h3><a href='#samples' name='samples'>Sample Strings</a></h3>
<p>The following samples illustrate the application of the rules. The blue lines indicate possible break points. If your browser supports titles (tooltips), then positioning the mouse over each character will show its name, while positioning between characters shows the number of the rule responsible for the break-status.</p>
<table>
<tr><th style='text-align:right'><a href='#s1' name='s1'>1</a></th><td><font size='5'>
<span title='0.2'><span style='border-right: 1px solid blue'>&nbsp;</span>&nbsp;</span><span title='U+000D &lt;CARRIAGE RETURN (CR)&gt; (CR)'>&#x25A1;</span><span title='3.0'><span>&nbsp;</span>&nbsp;</span><span title='U+000A &lt;LINE FEED (LF)&gt; (LF)'>&#x25A1;</span><span title='3.1'><span style='border-right: 1px solid blue'>&nbsp;</span>&nbsp;</span><span title='U+0061 LATIN SMALL LETTER A (ALetter)'>a</span><span title='3.2'><span style='border-right: 1px solid blue'>&nbsp;</span>&nbsp;</span><span title='U+000A &lt;LINE FEED (LF)&gt; (LF)'>&#x25A1;</span><span title='3.1'><span style='border-right: 1px solid blue'>&nbsp;</span>&nbsp;</span><span title='U+0308 COMBINING DIAERESIS (Extend_FE)'>&#x25CC;&#x308;</span><span title='0.3'><span style='border-right: 1px solid blue'>&nbsp;</span>&nbsp;</span>
</font></td></tr>
<tr><th style='text-align:right'><a href='#s2' name='s2'>2</a></th><td><font size='5'>
<span title='0.2'><span style='border-right: 1px solid blue'>&nbsp;</span>&nbsp;</span><span title='U+0061 LATIN SMALL LETTER A (ALetter)'>a</span><span title='4.0'><span>&nbsp;</span>&nbsp;</span><span title='U+0308 COMBINING DIAERESIS (Extend_FE)'>&#x25CC;&#x308;</span><span title='0.3'><span style='border-right: 1px solid blue'>&nbsp;</span>&nbsp;</span>
</font></td></tr>
<tr><th style='text-align:right'><a href='#s3' name='s3'>3</a></th><td><font size='5'>
<span title='0.2'><span style='border-right: 1px solid blue'>&nbsp;</span>&nbsp;</span><span title='U+0020 SPACE (WSegSpace)'> </span><span title='4.0'><span>&nbsp;</span>&nbsp;</span><span title='U+200D ZERO WIDTH JOINER (ZWJ_FE)'>&#x25A1;</span><span title='999.0'><span style='border-right: 1px solid blue'>&nbsp;</span>&nbsp;</span><span title='U+0646 ARABIC LETTER NOON (ALetter)'>&#x646;</span><span title='0.3'><span style='border-right: 1px solid blue'>&nbsp;</span>&nbsp;</span>
</font></td></tr>
<tr><th style='text-align:right'><a href='#s4' name='s4'>4</a></th><td><font size='5'>
<span title='0.2'><span style='border-right: 1px solid blue'>&nbsp;</span>&nbsp;</span><span title='U+0646 ARABIC LETTER NOON (ALetter)'>&#x646;</span><span title='4.0'><span>&nbsp;</span>&nbsp;</span><span title='U+200D ZERO WIDTH JOINER (ZWJ_FE)'>&#x25A1;</span><span title='999.0'><span style='border-right: 1px solid blue'>&nbsp;</span>&nbsp;</span><span title='U+0020 SPACE (WSegSpace)'> </span><span title='0.3'><span style='border-right: 1px solid blue'>&nbsp;</span>&nbsp;</span>
</font></td></tr>
<tr><th style='text-align:right'><a href='#s5' name='s5'>5</a></th><td><font size='5'>
<span title='0.2'><span style='border-right: 1px solid blue'>&nbsp;</span>&nbsp;</span><span title='U+0671 ARABIC LETTER ALEF WASLA (ALetter)'>&#x671;</span><span title='5.0'><span>&nbsp;</span>&nbsp;</span><span title='U+0644 ARABIC LETTER LAM (ALetter)'>&#x644;</span><span title='5.0'><span>&nbsp;</span>&nbsp;</span><span title='U+0631 ARABIC LETTER REH (ALetter)'>&#x631;</span><span title='4.0'><span>&nbsp;</span>&nbsp;</span><span title='U+064E ARABIC FATHA (Extend_FE)'>&#x25CC;&#x64E;</span><span title='4.0'><span>&nbsp;</span>&nbsp;</span><span title='U+0651 ARABIC SHADDA (Extend_FE)'>&#x25CC;&#x651;</span><span title='5.0'><span>&nbsp;</span>&nbsp;</span><span title='U+062D ARABIC LETTER HAH (ALetter)'>&#x62D;</span><span title='4.0'><span>&nbsp;</span>&nbsp;</span><span title='U+0650 ARABIC KASRA (Extend_FE)'>&#x25CC;&#x650;</span><span title='5.0'><span>&nbsp;</span>&nbsp;</span><span title='U+064A ARABIC LETTER YEH (ALetter)'>&#x64A;</span><span title='5.0'><span>&nbsp;</span>&nbsp;</span><span title='U+0645 ARABIC LETTER MEEM (ALetter)'>&#x645;</span><span title='4.0'><span>&nbsp;</span>&nbsp;</span><span title='U+0650 ARABIC KASRA (Extend_FE)'>&#x25CC;&#x650;</span><span title='999.0'><span style='border-right: 1px solid blue'>&nbsp;</span>&nbsp;</span><span title='U+0020 SPACE (WSegSpace)'> </span><span title='999.0'><span style='border-right: 1px solid blue'>&nbsp;</span>&nbsp;</span><span title='U+06DD ARABIC END OF AYAH (Numeric)'>&#x25A1;</span><span title='8.0'><span>&nbsp;</span>&nbsp;</span><span title='U+0661 ARABIC-INDIC DIGIT ONE (Numeric)'>&#x661;</span><span title='0.3'><span style='border-right: 1px solid blue'>&nbsp;</span>&nbsp;</span>
</font></td></tr>
<tr><th style='text-align:right'><a href='#s6' name='s6'>6</a></th><td><font size='5'>
<span title='0.2'><span style='border-right: 1px solid blue'>&nbsp;</span>&nbsp;</span><span title='U+0721 SYRIAC LETTER MIM (ALetter)'>&#x721;</span><span title='5.0'><span>&nbsp;</span>&nbsp;</span><span title='U+0719 SYRIAC LETTER ZAIN (ALetter)'>&#x719;</span><span title='5.0'><span>&nbsp;</span>&nbsp;</span><span title='U+0721 SYRIAC LETTER MIM (ALetter)'>&#x721;</span><span title='5.0'><span>&nbsp;</span>&nbsp;</span><span title='U+0718 SYRIAC LETTER WAW (ALetter)'>&#x718;</span><span title='5.0'><span>&nbsp;</span>&nbsp;</span><span title='U+072A SYRIAC LETTER RISH (ALetter)'>&#x72A;</span><span title='5.0'><span>&nbsp;</span>&nbsp;</span><span title='U+0710 SYRIAC LETTER ALAPH (ALetter)'>&#x710;</span><span title='999.0'><span style='border-right: 1px solid blue'>&nbsp;</span>&nbsp;</span><span title='U+0020 SPACE (WSegSpace)'> </span><span title='999.0'><span style='border-right: 1px solid blue'>&nbsp;</span>&nbsp;</span><span title='U+070F SYRIAC ABBREVIATION MARK (ALetter)'>&#x25A1;</span><span title='5.0'><span>&nbsp;</span>&nbsp;</span><span title='U+071D SYRIAC LETTER YUDH (ALetter)'>&#x71D;</span><span title='5.0'><span>&nbsp;</span>&nbsp;</span><span title='U+0717 SYRIAC LETTER HE (ALetter)'>&#x717;</span><span title='0.3'><span style='border-right: 1px solid blue'>&nbsp;</span>&nbsp;</span>
</font></td></tr>
<tr><th style='text-align:right'><a href='#s7' name='s7'>7</a></th><td><font size='5'>
<span title='0.2'><span style='border-right: 1px solid blue'>&nbsp;</span>&nbsp;</span><span title='U+072C SYRIAC LETTER TAW (ALetter)'>&#x72C;</span><span title='5.0'><span>&nbsp;</span>&nbsp;</span><span title='U+070F SYRIAC ABBREVIATION MARK (ALetter)'>&#x25A1;</span><span title='5.0'><span>&nbsp;</span>&nbsp;</span><span title='U+072B SYRIAC LETTER SHIN (ALetter)'>&#x72B;</span><span title='5.0'><span>&nbsp;</span>&nbsp;</span><span title='U+0712 SYRIAC LETTER BETH (ALetter)'>&#x712;</span><span title='5.0'><span>&nbsp;</span>&nbsp;</span><span title='U+0718 SYRIAC LETTER WAW (ALetter)'>&#x718;</span><span title='0.3'><span style='border-right: 1px solid blue'>&nbsp;</span>&nbsp;</span>
</font></td></tr>
<tr><th style='text-align:right'><a href='#s8' name='s8'>8</a></th><td><font size='5'>
<span title='0.2'><span style='border-right: 1px solid blue'>&nbsp;</span>&nbsp;</span><span title='U+0041 LATIN CAPITAL LETTER A (ALetter)'>A</span><span title='5.0'><span>&nbsp;</span>&nbsp;</span><span title='U+0041 LATIN CAPITAL LETTER A (ALetter)'>A</span><span title='5.0'><span>&nbsp;</span>&nbsp;</span><span title='U+0041 LATIN CAPITAL LETTER A (ALetter)'>A</span><span title='0.3'><span style='border-right: 1px solid blue'>&nbsp;</span>&nbsp;</span>
</font></td></tr>
<tr><th style='text-align:right'><a href='#s9' name='s9'>9</a></th><td><font size='5'>
<span title='0.2'><span style='border-right: 1px solid blue'>&nbsp;</span>&nbsp;</span><span title='U+0041 LATIN CAPITAL LETTER A (ALetter)'>A</span><span title='6.0'><span>&nbsp;</span>&nbsp;</span><span title='U+003A COLON (MidLetter)'>:</span><span title='7.0'><span>&nbsp;</span>&nbsp;</span><span title='U+0041 LATIN CAPITAL LETTER A (ALetter)'>A</span><span title='0.3'><span style='border-right: 1px solid blue'>&nbsp;</span>&nbsp;</span>
</font></td></tr>
<tr><th style='text-align:right'><a href='#s10' name='s10'>10</a></th><td><font size='5'>
<span title='0.2'><span style='border-right: 1px solid blue'>&nbsp;</span>&nbsp;</span><span title='U+0041 LATIN CAPITAL LETTER A (ALetter)'>A</span><span title='999.0'><span style='border-right: 1px solid blue'>&nbsp;</span>&nbsp;</span><span title='U+003A COLON (MidLetter)'>:</span><span title='999.0'><span style='border-right: 1px solid blue'>&nbsp;</span>&nbsp;</span><span title='U+003A COLON (MidLetter)'>:</span><span title='999.0'><span style='border-right: 1px solid blue'>&nbsp;</span>&nbsp;</span><span title='U+0041 LATIN CAPITAL LETTER A (ALetter)'>A</span><span title='0.3'><span style='border-right: 1px solid blue'>&nbsp;</span>&nbsp;</span>
</font></td></tr>
<tr><th style='text-align:right'><a href='#s11' name='s11'>11</a></th><td><font size='5'>
<span title='0.2'><span style='border-right: 1px solid blue'>&nbsp;</span>&nbsp;</span><span title='U+05D0 HEBREW LETTER ALEF (Hebrew_Letter)'>&#x5D0;</span><span title='7.1'><span>&nbsp;</span>&nbsp;</span><span title='U+0027 APOSTROPHE (Single_Quote)'>'</span><span title='0.3'><span style='border-right: 1px solid blue'>&nbsp;</span>&nbsp;</span>
</font></td></tr>
<tr><th style='text-align:right'><a href='#s12' name='s12'>12</a></th><td><font size='5'>
<span title='0.2'><span style='border-right: 1px solid blue'>&nbsp;</span>&nbsp;</span><span title='U+05D0 HEBREW LETTER ALEF (Hebrew_Letter)'>&#x5D0;</span><span title='7.2'><span>&nbsp;</span>&nbsp;</span><span title='U+0022 QUOTATION MARK (Double_Quote)'>&quot;</span><span title='7.3'><span>&nbsp;</span>&nbsp;</span><span title='U+05D0 HEBREW LETTER ALEF (Hebrew_Letter)'>&#x5D0;</span><span title='0.3'><span style='border-right: 1px solid blue'>&nbsp;</span>&nbsp;</span>
</font></td></tr>
<tr><th style='text-align:right'><a href='#s13' name='s13'>13</a></th><td><font size='5'>
<span title='0.2'><span style='border-right: 1px solid blue'>&nbsp;</span>&nbsp;</span><span title='U+0041 LATIN CAPITAL LETTER A (ALetter)'>A</span><span title='9.0'><span>&nbsp;</span>&nbsp;</span><span title='U+0030 DIGIT ZERO (Numeric)'>0</span><span title='8.0'><span>&nbsp;</span>&nbsp;</span><span title='U+0030 DIGIT ZERO (Numeric)'>0</span><span title='10.0'><span>&nbsp;</span>&nbsp;</span><span title='U+0041 LATIN CAPITAL LETTER A (ALetter)'>A</span><span title='0.3'><span style='border-right: 1px solid blue'>&nbsp;</span>&nbsp;</span>
</font></td></tr>
<tr><th style='text-align:right'><a href='#s14' name='s14'>14</a></th><td><font size='5'>
<span title='0.2'><span style='border-right: 1px solid blue'>&nbsp;</span>&nbsp;</span><span title='U+0030 DIGIT ZERO (Numeric)'>0</span><span title='12.0'><span>&nbsp;</span>&nbsp;</span><span title='U+002C COMMA (MidNum)'>,</span><span title='11.0'><span>&nbsp;</span>&nbsp;</span><span title='U+0030 DIGIT ZERO (Numeric)'>0</span><span title='0.3'><span style='border-right: 1px solid blue'>&nbsp;</span>&nbsp;</span>
</font></td></tr>
<tr><th style='text-align:right'><a href='#s15' name='s15'>15</a></th><td><font size='5'>
<span title='0.2'><span style='border-right: 1px solid blue'>&nbsp;</span>&nbsp;</span><span title='U+0030 DIGIT ZERO (Numeric)'>0</span><span title='999.0'><span style='border-right: 1px solid blue'>&nbsp;</span>&nbsp;</span><span title='U+002C COMMA (MidNum)'>,</span><span title='999.0'><span style='border-right: 1px solid blue'>&nbsp;</span>&nbsp;</span><span title='U+002C COMMA (MidNum)'>,</span><span title='999.0'><span style='border-right: 1px solid blue'>&nbsp;</span>&nbsp;</span><span title='U+0030 DIGIT ZERO (Numeric)'>0</span><span title='0.3'><span style='border-right: 1px solid blue'>&nbsp;</span>&nbsp;</span>
</font></td></tr>
<tr><th style='text-align:right'><a href='#s16' name='s16'>16</a></th><td><font size='5'>
<span title='0.2'><span style='border-right: 1px solid blue'>&nbsp;</span>&nbsp;</span><span title='U+3031 VERTICAL KANA REPEAT MARK (Katakana)'>&#x3031;</span><span title='13.0'><span>&nbsp;</span>&nbsp;</span><span title='U+3031 VERTICAL KANA REPEAT MARK (Katakana)'>&#x3031;</span><span title='0.3'><span style='border-right: 1px solid blue'>&nbsp;</span>&nbsp;</span>
</font></td></tr>
<tr><th style='text-align:right'><a href='#s17' name='s17'>17</a></th><td><font size='5'>
<span title='0.2'><span style='border-right: 1px solid blue'>&nbsp;</span>&nbsp;</span><span title='U+0041 LATIN CAPITAL LETTER A (ALetter)'>A</span><span title='13.1'><span>&nbsp;</span>&nbsp;</span><span title='U+005F LOW LINE (ExtendNumLet)'>_</span><span title='13.2'><span>&nbsp;</span>&nbsp;</span><span title='U+0030 DIGIT ZERO (Numeric)'>0</span><span title='13.1'><span>&nbsp;</span>&nbsp;</span><span title='U+005F LOW LINE (ExtendNumLet)'>_</span><span title='13.2'><span>&nbsp;</span>&nbsp;</span><span title='U+3031 VERTICAL KANA REPEAT MARK (Katakana)'>&#x3031;</span><span title='13.1'><span>&nbsp;</span>&nbsp;</span><span title='U+005F LOW LINE (ExtendNumLet)'>_</span><span title='0.3'><span style='border-right: 1px solid blue'>&nbsp;</span>&nbsp;</span>
</font></td></tr>
<tr><th style='text-align:right'><a href='#s18' name='s18'>18</a></th><td><font size='5'>
<span title='0.2'><span style='border-right: 1px solid blue'>&nbsp;</span>&nbsp;</span><span title='U+0041 LATIN CAPITAL LETTER A (ALetter)'>A</span><span title='13.1'><span>&nbsp;</span>&nbsp;</span><span title='U+005F LOW LINE (ExtendNumLet)'>_</span><span title='13.1'><span>&nbsp;</span>&nbsp;</span><span title='U+005F LOW LINE (ExtendNumLet)'>_</span><span title='13.2'><span>&nbsp;</span>&nbsp;</span><span title='U+0041 LATIN CAPITAL LETTER A (ALetter)'>A</span><span title='0.3'><span style='border-right: 1px solid blue'>&nbsp;</span>&nbsp;</span>
</font></td></tr>
<tr><th style='text-align:right'><a href='#s19' name='s19'>19</a></th><td><font size='5'>
<span title='0.2'><span style='border-right: 1px solid blue'>&nbsp;</span>&nbsp;</span><span title='U+1F1E6 REGIONAL INDICATOR SYMBOL LETTER A (RI)'>&#x1F1E6;</span><span title='15.0'><span>&nbsp;</span>&nbsp;</span><span title='U+1F1E7 REGIONAL INDICATOR SYMBOL LETTER B (RI)'>&#x1F1E7;</span><span title='999.0'><span style='border-right: 1px solid blue'>&nbsp;</span>&nbsp;</span><span title='U+1F1E8 REGIONAL INDICATOR SYMBOL LETTER C (RI)'>&#x1F1E8;</span><span title='999.0'><span style='border-right: 1px solid blue'>&nbsp;</span>&nbsp;</span><span title='U+0062 LATIN SMALL LETTER B (ALetter)'>b</span><span title='0.3'><span style='border-right: 1px solid blue'>&nbsp;</span>&nbsp;</span>
</font></td></tr>
<tr><th style='text-align:right'><a href='#s20' name='s20'>20</a></th><td><font size='5'>
<span title='0.2'><span style='border-right: 1px solid blue'>&nbsp;</span>&nbsp;</span><span title='U+0061 LATIN SMALL LETTER A (ALetter)'>a</span><span title='999.0'><span style='border-right: 1px solid blue'>&nbsp;</span>&nbsp;</span><span title='U+1F1E6 REGIONAL INDICATOR SYMBOL LETTER A (RI)'>&#x1F1E6;</span><span title='16.0'><span>&nbsp;</span>&nbsp;</span><span title='U+1F1E7 REGIONAL INDICATOR SYMBOL LETTER B (RI)'>&#x1F1E7;</span><span title='999.0'><span style='border-right: 1px solid blue'>&nbsp;</span>&nbsp;</span><span title='U+1F1E8 REGIONAL INDICATOR SYMBOL LETTER C (RI)'>&#x1F1E8;</span><span title='999.0'><span style='border-right: 1px solid blue'>&nbsp;</span>&nbsp;</span><span title='U+0062 LATIN SMALL LETTER B (ALetter)'>b</span><span title='0.3'><span style='border-right: 1px solid blue'>&nbsp;</span>&nbsp;</span>
</font></td></tr>
<tr><th style='text-align:right'><a href='#s21' name='s21'>21</a></th><td><font size='5'>
<span title='0.2'><span style='border-right: 1px solid blue'>&nbsp;</span>&nbsp;</span><span title='U+0061 LATIN SMALL LETTER A (ALetter)'>a</span><span title='999.0'><span style='border-right: 1px solid blue'>&nbsp;</span>&nbsp;</span><span title='U+1F1E6 REGIONAL INDICATOR SYMBOL LETTER A (RI)'>&#x1F1E6;</span><span title='16.0'><span>&nbsp;</span>&nbsp;</span><span title='U+1F1E7 REGIONAL INDICATOR SYMBOL LETTER B (RI)'>&#x1F1E7;</span><span title='4.0'><span>&nbsp;</span>&nbsp;</span><span title='U+200D ZERO WIDTH JOINER (ZWJ_FE)'>&#x25A1;</span><span title='999.0'><span style='border-right: 1px solid blue'>&nbsp;</span>&nbsp;</span><span title='U+1F1E8 REGIONAL INDICATOR SYMBOL LETTER C (RI)'>&#x1F1E8;</span><span title='999.0'><span style='border-right: 1px solid blue'>&nbsp;</span>&nbsp;</span><span title='U+0062 LATIN SMALL LETTER B (ALetter)'>b</span><span title='0.3'><span style='border-right: 1px solid blue'>&nbsp;</span>&nbsp;</span>
</font></td></tr>
<tr><th style='text-align:right'><a href='#s22' name='s22'>22</a></th><td><font size='5'>
<span title='0.2'><span style='border-right: 1px solid blue'>&nbsp;</span>&nbsp;</span><span title='U+0061 LATIN SMALL LETTER A (ALetter)'>a</span><span title='999.0'><span style='border-right: 1px solid blue'>&nbsp;</span>&nbsp;</span><span title='U+1F1E6 REGIONAL INDICATOR SYMBOL LETTER A (RI)'>&#x1F1E6;</span><span title='4.0'><span>&nbsp;</span>&nbsp;</span><span title='U+200D ZERO WIDTH JOINER (ZWJ_FE)'>&#x25A1;</span><span title='16.0'><span>&nbsp;</span>&nbsp;</span><span title='U+1F1E7 REGIONAL INDICATOR SYMBOL LETTER B (RI)'>&#x1F1E7;</span><span title='999.0'><span style='border-right: 1px solid blue'>&nbsp;</span>&nbsp;</span><span title='U+1F1E8 REGIONAL INDICATOR SYMBOL LETTER C (RI)'>&#x1F1E8;</span><span title='999.0'><span style='border-right: 1px solid blue'>&nbsp;</span>&nbsp;</span><span title='U+0062 LATIN SMALL LETTER B (ALetter)'>b</span><span title='0.3'><span style='border-right: 1px solid blue'>&nbsp;</span>&nbsp;</span>
</font></td></tr>
<tr><th style='text-align:right'><a href='#s23' name='s23'>23</a></th><td><font size='5'>
<span title='0.2'><span style='border-right: 1px solid blue'>&nbsp;</span>&nbsp;</span><span title='U+0061 LATIN SMALL LETTER A (ALetter)'>a</span><span title='999.0'><span style='border-right: 1px solid blue'>&nbsp;</span>&nbsp;</span><span title='U+1F1E6 REGIONAL INDICATOR SYMBOL LETTER A (RI)'>&#x1F1E6;</span><span title='16.0'><span>&nbsp;</span>&nbsp;</span><span title='U+1F1E7 REGIONAL INDICATOR SYMBOL LETTER B (RI)'>&#x1F1E7;</span><span title='999.0'><span style='border-right: 1px solid blue'>&nbsp;</span>&nbsp;</span><span title='U+1F1E8 REGIONAL INDICATOR SYMBOL LETTER C (RI)'>&#x1F1E8;</span><span title='16.0'><span>&nbsp;</span>&nbsp;</span><span title='U+1F1E9 REGIONAL INDICATOR SYMBOL LETTER D (RI)'>&#x1F1E9;</span><span title='999.0'><span style='border-right: 1px solid blue'>&nbsp;</span>&nbsp;</span><span title='U+0062 LATIN SMALL LETTER B (ALetter)'>b</span><span title='0.3'><span style='border-right: 1px solid blue'>&nbsp;</span>&nbsp;</span>
</font></td></tr>
<tr><th style='text-align:right'><a href='#s24' name='s24'>24</a></th><td><font size='5'>
<span title='0.2'><span style='border-right: 1px solid blue'>&nbsp;</span>&nbsp;</span><span title='U+1F476 BABY (ExtPict)'>&#x1F476;</span><span title='4.0'><span>&nbsp;</span>&nbsp;</span><span title='U+1F3FF EMOJI MODIFIER FITZPATRICK TYPE-6 (Extend_FE)'>&#x1F3FF;</span><span title='999.0'><span style='border-right: 1px solid blue'>&nbsp;</span>&nbsp;</span><span title='U+1F476 BABY (ExtPict)'>&#x1F476;</span><span title='0.3'><span style='border-right: 1px solid blue'>&nbsp;</span>&nbsp;</span>
</font></td></tr>
<tr><th style='text-align:right'><a href='#s25' name='s25'>25</a></th><td><font size='5'>
<span title='0.2'><span style='border-right: 1px solid blue'>&nbsp;</span>&nbsp;</span><span title='U+1F6D1 OCTAGONAL SIGN (ExtPict)'>&#x1F6D1;</span><span title='4.0'><span>&nbsp;</span>&nbsp;</span><span title='U+200D ZERO WIDTH JOINER (ZWJ_FE)'>&#x25A1;</span><span title='3.3'><span>&nbsp;</span>&nbsp;</span><span title='U+1F6D1 OCTAGONAL SIGN (ExtPict)'>&#x1F6D1;</span><span title='0.3'><span style='border-right: 1px solid blue'>&nbsp;</span>&nbsp;</span>
</font></td></tr>
<tr><th style='text-align:right'><a href='#s26' name='s26'>26</a></th><td><font size='5'>
<span title='0.2'><span style='border-right: 1px solid blue'>&nbsp;</span>&nbsp;</span><span title='U+0061 LATIN SMALL LETTER A (ALetter)'>a</span><span title='4.0'><span>&nbsp;</span>&nbsp;</span><span title='U+200D ZERO WIDTH JOINER (ZWJ_FE)'>&#x25A1;</span><span title='3.3'><span>&nbsp;</span>&nbsp;</span><span title='U+1F6D1 OCTAGONAL SIGN (ExtPict)'>&#x1F6D1;</span><span title='0.3'><span style='border-right: 1px solid blue'>&nbsp;</span>&nbsp;</span>
</font></td></tr>
<tr><th style='text-align:right'><a href='#s27' name='s27'>27</a></th><td><font size='5'>
<span title='0.2'><span style='border-right: 1px solid blue'>&nbsp;</span>&nbsp;</span><span title='U+2701 UPPER BLADE SCISSORS (Other)'>&#x2701;</span><span title='4.0'><span>&nbsp;</span>&nbsp;</span><span title='U+200D ZERO WIDTH JOINER (ZWJ_FE)'>&#x25A1;</span><span title='3.3'><span>&nbsp;</span>&nbsp;</span><span title='U+2701 UPPER BLADE SCISSORS (Other)'>&#x2701;</span><span title='0.3'><span style='border-right: 1px solid blue'>&nbsp;</span>&nbsp;</span>
</font></td></tr>
<tr><th style='text-align:right'><a href='#s28' name='s28'>28</a></th><td><font size='5'>
<span title='0.2'><span style='border-right: 1px solid blue'>&nbsp;</span>&nbsp;</span><span title='U+0061 LATIN SMALL LETTER A (ALetter)'>a</span><span title='4.0'><span>&nbsp;</span>&nbsp;</span><span title='U+200D ZERO WIDTH JOINER (ZWJ_FE)'>&#x25A1;</span><span title='3.3'><span>&nbsp;</span>&nbsp;</span><span title='U+2701 UPPER BLADE SCISSORS (Other)'>&#x2701;</span><span title='0.3'><span style='border-right: 1px solid blue'>&nbsp;</span>&nbsp;</span>
</font></td></tr>
<tr><th style='text-align:right'><a href='#s29' name='s29'>29</a></th><td><font size='5'>
<span title='0.2'><span style='border-right: 1px solid blue'>&nbsp;</span>&nbsp;</span><span title='U+1F476 BABY (ExtPict)'>&#x1F476;</span><span title='4.0'><span>&nbsp;</span>&nbsp;</span><span title='U+1F3FF EMOJI MODIFIER FITZPATRICK TYPE-6 (Extend_FE)'>&#x1F3FF;</span><span title='4.0'><span>&nbsp;</span>&nbsp;</span><span title='U+0308 COMBINING DIAERESIS (Extend_FE)'>&#x25CC;&#x308;</span><span title='4.0'><span>&nbsp;</span>&nbsp;</span><span title='U+200D ZERO WIDTH JOINER (ZWJ_FE)'>&#x25A1;</span><span title='3.3'><span>&nbsp;</span>&nbsp;</span><span title='U+1F476 BABY (ExtPict)'>&#x1F476;</span><span title='4.0'><span>&nbsp;</span>&nbsp;</span><span title='U+1F3FF EMOJI MODIFIER FITZPATRICK TYPE-6 (Extend_FE)'>&#x1F3FF;</span><span title='0.3'><span style='border-right: 1px solid blue'>&nbsp;</span>&nbsp;</span>
</font></td></tr>
<tr><th style='text-align:right'><a href='#s30' name='s30'>30</a></th><td><font size='5'>
<span title='0.2'><span style='border-right: 1px solid blue'>&nbsp;</span>&nbsp;</span><span title='U+1F6D1 OCTAGONAL SIGN (ExtPict)'>&#x1F6D1;</span><span title='4.0'><span>&nbsp;</span>&nbsp;</span><span title='U+1F3FF EMOJI MODIFIER FITZPATRICK TYPE-6 (Extend_FE)'>&#x1F3FF;</span><span title='0.3'><span style='border-right: 1px solid blue'>&nbsp;</span>&nbsp;</span>
</font></td></tr>
<tr><th style='text-align:right'><a href='#s31' name='s31'>31</a></th><td><font size='5'>
<span title='0.2'><span style='border-right: 1px solid blue'>&nbsp;</span>&nbsp;</span><span title='U+200D ZERO WIDTH JOINER (ZWJ_FE)'>&#x25A1;</span><span title='3.3'><span>&nbsp;</span>&nbsp;</span><span title='U+1F6D1 OCTAGONAL SIGN (ExtPict)'>&#x1F6D1;</span><span title='4.0'><span>&nbsp;</span>&nbsp;</span><span title='U+1F3FF EMOJI MODIFIER FITZPATRICK TYPE-6 (Extend_FE)'>&#x1F3FF;</span><span title='0.3'><span style='border-right: 1px solid blue'>&nbsp;</span>&nbsp;</span>
</font></td></tr>
<tr><th style='text-align:right'><a href='#s32' name='s32'>32</a></th><td><font size='5'>
<span title='0.2'><span style='border-right: 1px solid blue'>&nbsp;</span>&nbsp;</span><span title='U+200D ZERO WIDTH JOINER (ZWJ_FE)'>&#x25A1;</span><span title='3.3'><span>&nbsp;</span>&nbsp;</span><span title='U+1F6D1 OCTAGONAL SIGN (ExtPict)'>&#x1F6D1;</span><span title='0.3'><span style='border-right: 1px solid blue'>&nbsp;</span>&nbsp;</span>
</font></td></tr>
<tr><th style='text-align:right'><a href='#s33' name='s33'>33</a></th><td><font size='5'>
<span title='0.2'><span style='border-right: 1px solid blue'>&nbsp;</span>&nbsp;</span><span title='U+200D ZERO WIDTH JOINER (ZWJ_FE)'>&#x25A1;</span><span title='3.3'><span>&nbsp;</span>&nbsp;</span><span title='U+1F6D1 OCTAGONAL SIGN (ExtPict)'>&#x1F6D1;</span><span title='0.3'><span style='border-right: 1px solid blue'>&nbsp;</span>&nbsp;</span>
</font></td></tr>
<tr><th style='text-align:right'><a href='#s34' name='s34'>34</a></th><td><font size='5'>
<span title='0.2'><span style='border-right: 1px solid blue'>&nbsp;</span>&nbsp;</span><span title='U+1F6D1 OCTAGONAL SIGN (ExtPict)'>&#x1F6D1;</span><span title='999.0'><span style='border-right: 1px solid blue'>&nbsp;</span>&nbsp;</span><span title='U+1F6D1 OCTAGONAL SIGN (ExtPict)'>&#x1F6D1;</span><span title='0.3'><span style='border-right: 1px solid blue'>&nbsp;</span>&nbsp;</span>
</font></td></tr>
<tr><th style='text-align:right'><a href='#s35' name='s35'>35</a></th><td><font size='5'>
<span title='0.2'><span style='border-right: 1px solid blue'>&nbsp;</span>&nbsp;</span><span title='U+0061 LATIN SMALL LETTER A (ALetter)'>a</span><span title='4.0'><span>&nbsp;</span>&nbsp;</span><span title='U+0308 COMBINING DIAERESIS (Extend_FE)'>&#x25CC;&#x308;</span><span title='4.0'><span>&nbsp;</span>&nbsp;</span><span title='U+200D ZERO WIDTH JOINER (ZWJ_FE)'>&#x25A1;</span><span title='4.0'><span>&nbsp;</span>&nbsp;</span><span title='U+0308 COMBINING DIAERESIS (Extend_FE)'>&#x25CC;&#x308;</span><span title='5.0'><span>&nbsp;</span>&nbsp;</span><span title='U+0062 LATIN SMALL LETTER B (ALetter)'>b</span><span title='0.3'><span style='border-right: 1px solid blue'>&nbsp;</span>&nbsp;</span>
</font></td></tr>
<tr><th style='text-align:right'><a href='#s36' name='s36'>36</a></th><td><font size='5'>
<span title='0.2'><span style='border-right: 1px solid blue'>&nbsp;</span>&nbsp;</span><span title='U+0061 LATIN SMALL LETTER A (ALetter)'>a</span><span title='999.0'><span style='border-right: 1px solid blue'>&nbsp;</span>&nbsp;</span><span title='U+0020 SPACE (WSegSpace)'> </span><span title='3.4'><span>&nbsp;</span>&nbsp;</span><span title='U+0020 SPACE (WSegSpace)'> </span><span title='999.0'><span style='border-right: 1px solid blue'>&nbsp;</span>&nbsp;</span><span title='U+0062 LATIN SMALL LETTER B (ALetter)'>b</span><span title='0.3'><span style='border-right: 1px solid blue'>&nbsp;</span>&nbsp;</span>
</font></td></tr>
</table>
<hr width='50%'>
<div align='center'>
<center>
<table cellspacing='0' cellpadding='0' border='0'>
<tr>
<td><a href='https://www.unicode.org/copyright.html'>
<img src='https://www.unicode.org/img/hb_notice.gif' border='0' alt='Access to Copyright and terms of use' width='216' height='50'></a></td>
</tr>
</table>
</center>
</div>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
