# NamedSequencesProv-16.0.0.txt
# Date: 2024-02-02
# © 2024 Unicode®, Inc.
# Unicode and the Unicode Logo are registered trademarks of Unicode, Inc. in the U.S. and other countries.
# For terms of use and license, see https://www.unicode.org/terms_of_use.html
#
# Unicode Character Database
# For documentation, see https://www.unicode.org/reports/tr44/
#
# Provisional Unicode Named Character Sequences
#
# Note: This data file contains those named character
#   sequences which have been designated to be provisional,
#   rather than fully approved.
#
# Format:
# Name of Sequence; Code Point Sequence for USI
#
# Code point sequences in the Unicode Character Database
# use spaces as delimiters. The corresponding format for a
# UCS Sequence Identifier (USI) in ISO/IEC 10646 uses
# comma delimitation and angle brackets. Thus, a Unicode
# named character sequence of the form:
#
# EXAMPLE NAME;1000 1001 1002
#
# in this data file, would correspond to an ISO/IEC 10646 USI
# as follows:
#
# <1000, 1001, 1002>
#
# For more information, see UAX #34: Unicode Named Character
# Sequences, at https://www.unicode.org/reports/tr34/
#
# Note: The order of entries in this file is not significant.
# However, entries are generally in script order corresponding
# to block order in the Unicode Standard, to make it easier
# to find entries currently in the list.

# ================================================

# Provisional entries for NamedSequences.txt.

# No provisional entries are currently defined.

# ================================================

# Entries from Unicode 4.1.0 version of NamedSequences.txt,
# subsequently disapproved because of potential errors in
# representation.

# GURMUKHI HALF YA;0A2F 0A4D
# GURMUKHI PARI YA;0A4D 0A2F

# Entry removed 2006-05-18:
#
# LATIN SMALL LETTER A WITH ACUTE AND OGONEK;00E1 0328
#
# This entry was removed because the sequence was not in NFC,
# as required. It was replaced with the NFC version of
# the sequence, based on the Lithuanian additions accepted
# for Unicode 5.0.

# EOF
