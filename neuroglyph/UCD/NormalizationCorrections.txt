# NormalizationCorrections-16.0.0.txt
# Date: 2024-02-02
# © 2024 Unicode®, Inc.
# Unicode and the Unicode Logo are registered trademarks of Unicode, Inc. in the U.S. and other countries.
# For terms of use and license, see https://www.unicode.org/terms_of_use.html
#
# Unicode Character Database
# For documentation, see https://www.unicode.org/reports/tr44/
#
# This file is a normative contributory data file in the
# Unicode Character Database.
#
# The normalization stability policy of the Unicode Consortium
# ordinarily precludes any change to the decomposition
# for any character, once established in a relevant version
# of the UnicodeData.txt data file. However, under certain
# exceptional (and rare) conditions, an error in a decomposition
# mapping may be discovered that is truly just an unintended
# typo in the data, and not a matter of dubious interpretation.
#
# Whenever such an error may be found, and if it meets the
# requirements for possible exceptions to normalization
# stability, the correction is entered in this data file,
# so that any implementation depending on absolute stability
# of normalization, *including* any errors in the data, can
# safely reconstruct the exact state of the data tables at
# any given version of Unicode.
#
# Currently this list has exactly six entries in it, one for the
# typo found and corrected in Corrigendum #3, and five for
# the typos and misidentifications found and corrected in
# Corrigendum #4. All efforts
# will be made to keep the entries limited to just those fixes.
#
# Interpretation of the fields:
#   Field 0: Unicode code point
#   Field 1: Original (erroneous) decomposition
#   Field 2: Corrected decomposition
#   Field 3: Version of Unicode for which the correction was
#            entered into UnicodeData.txt, in n.n.n format.
#   Comment: Indicates the Unicode Corrigendum which documents
#            the correction
#
# For more information, see UAX #15, Unicode Normalization Forms.
#
F951;96FB;964B;3.2.0 # Corrigendum 3
2F868;2136A;36FC;4.0.0 # Corrigendum 4
2F874;5F33;5F53;4.0.0 # Corrigendum 4
2F91F;43AB;243AB;4.0.0 # Corrigendum 4
2F95F;7AAE;7AEE;4.0.0 # Corrigendum 4
2F9BF;4D57;45D7;4.0.0 # Corrigendum 4

# EOF
