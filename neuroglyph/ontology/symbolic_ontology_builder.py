#!/usr/bin/env python3
"""
NEUROGLYPH SYMBOLIC ONTOLOGY BUILDER
Costruisce la rete ontologica per ragionamento simbolico multi-hop
"""

import json
import sys
from pathlib import Path
from datetime import datetime
from typing import Dict, List, Any, Set, Tuple
from collections import defaultdict, Counter

def load_registry(registry_path: str = "neuroglyph/core/locked_registry_godmode_v9.json") -> Dict[str, Any]:
    """Carica registry GOD MODE."""
    try:
        with open(registry_path, 'r', encoding='utf-8') as f:
            registry = json.load(f)
        print(f"✅ Registry GOD MODE caricato: {len(registry.get('approved_symbols', []))} simboli")
        return registry
    except Exception as e:
        print(f"❌ Errore caricamento registry: {e}")
        return {}

def analyze_semantic_clusters(symbols: List[Dict[str, Any]]) -> Dict[str, List[str]]:
    """Analizza cluster semantici per identificare relazioni."""
    print("🔍 Analizzando cluster semantici...")
    
    # Raggruppa per categoria semantica
    semantic_clusters = defaultdict(list)
    
    for symbol in symbols:
        category = symbol.get("category", "unknown")
        semantic_domain = symbol.get("semantic_domain", category)
        name = symbol.get("name", "")
        
        # Cluster principali
        if "meta" in name or "meta" in category:
            semantic_clusters["meta_cognition"].append(symbol["id"])
        elif "self" in name or "ego" in name or "identity" in name:
            semantic_clusters["self_awareness"].append(symbol["id"])
        elif "agent" in name or "autonomous" in name:
            semantic_clusters["autonomous_agents"].append(symbol["id"])
        elif "temporal" in name or "time" in name:
            semantic_clusters["temporal_reasoning"].append(symbol["id"])
        elif "cognitive" in name or "cognition" in name:
            semantic_clusters["cognitive_processes"].append(symbol["id"])
        elif "architecture" in name or "structure" in name:
            semantic_clusters["system_architecture"].append(symbol["id"])
        elif "memory" in name or "recall" in name:
            semantic_clusters["memory_systems"].append(symbol["id"])
        elif "attention" in name or "focus" in name:
            semantic_clusters["attention_mechanisms"].append(symbol["id"])
        elif "learning" in name or "adaptation" in name:
            semantic_clusters["learning_systems"].append(symbol["id"])
        elif "reasoning" in name or "logic" in name:
            semantic_clusters["reasoning_engines"].append(symbol["id"])
        else:
            semantic_clusters["general"].append(symbol["id"])
    
    print(f"  📊 Cluster identificati: {len(semantic_clusters)}")
    for cluster, symbols_ids in semantic_clusters.items():
        print(f"    • {cluster}: {len(symbols_ids)} simboli")
    
    return dict(semantic_clusters)

def identify_symbolic_relationships(symbols: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
    """Identifica relazioni tra simboli."""
    print("🔗 Identificando relazioni simboliche...")
    
    # Crea mapping per accesso rapido
    symbol_map = {s["id"]: s for s in symbols}
    relationships = []
    
    # Relazioni semantiche basate su pattern
    for symbol in symbols:
        symbol_id = symbol["id"]
        name = symbol.get("name", "")
        category = symbol.get("category", "")
        
        # Trova simboli correlati
        for other_symbol in symbols:
            if other_symbol["id"] == symbol_id:
                continue
                
            other_name = other_symbol.get("name", "")
            other_category = other_symbol.get("category", "")
            
            # Relazione di specializzazione
            if name in other_name and len(name) < len(other_name):
                relationships.append({
                    "type": "specializes",
                    "source": symbol_id,
                    "target": other_symbol["id"],
                    "strength": 0.8,
                    "description": f"{other_name} specializes {name}"
                })
            
            # Relazione di categoria
            if category == other_category and symbol_id != other_symbol["id"]:
                relationships.append({
                    "type": "same_category",
                    "source": symbol_id,
                    "target": other_symbol["id"],
                    "strength": 0.6,
                    "description": f"Both belong to {category}"
                })
            
            # Relazioni meta
            if "meta" in name and name.replace("meta_", "") in other_name:
                relationships.append({
                    "type": "meta_of",
                    "source": symbol_id,
                    "target": other_symbol["id"],
                    "strength": 0.9,
                    "description": f"{name} is meta-level of {other_name}"
                })
            
            # Relazioni composizionali
            if "_" in name and "_" in other_name:
                name_parts = set(name.split("_"))
                other_parts = set(other_name.split("_"))
                overlap = name_parts.intersection(other_parts)
                
                if len(overlap) >= 2:
                    relationships.append({
                        "type": "compositional",
                        "source": symbol_id,
                        "target": other_symbol["id"],
                        "strength": len(overlap) / max(len(name_parts), len(other_parts)),
                        "description": f"Share components: {', '.join(overlap)}"
                    })
    
    # Rimuovi duplicati e relazioni deboli
    unique_relationships = []
    seen = set()
    
    for rel in relationships:
        key = (rel["source"], rel["target"], rel["type"])
        if key not in seen and rel["strength"] >= 0.5:
            seen.add(key)
            unique_relationships.append(rel)
    
    print(f"  ✅ Relazioni identificate: {len(unique_relationships)}")
    return unique_relationships

def build_ontology_graph(symbols: List[Dict[str, Any]], 
                        clusters: Dict[str, List[str]],
                        relationships: List[Dict[str, Any]]) -> Dict[str, Any]:
    """Costruisce grafo ontologico completo."""
    print("🧠 Costruendo grafo ontologico...")
    
    # Nodi del grafo
    nodes = []
    for symbol in symbols:
        node = {
            "id": symbol["id"],
            "label": symbol.get("name", ""),
            "type": "symbol",
            "category": symbol.get("category", ""),
            "semantic_domain": symbol.get("semantic_domain", ""),
            "description": symbol.get("description", ""),
            "fallback": symbol.get("fallback", ""),
            "unicode_symbol": symbol.get("symbol", ""),
            "validation_score": symbol.get("validation_score", 0),
            "tier": symbol.get("tier", "god"),
            "properties": {
                "abstraction_level": determine_abstraction_level(symbol),
                "cognitive_function": determine_cognitive_function(symbol),
                "operational_scope": determine_operational_scope(symbol)
            }
        }
        nodes.append(node)
    
    # Aggiungi nodi cluster
    for cluster_name, symbol_ids in clusters.items():
        cluster_node = {
            "id": f"CLUSTER_{cluster_name.upper()}",
            "label": cluster_name.replace("_", " ").title(),
            "type": "cluster",
            "category": "semantic_cluster",
            "members": symbol_ids,
            "size": len(symbol_ids),
            "properties": {
                "abstraction_level": "cluster",
                "cognitive_function": "grouping",
                "operational_scope": "semantic"
            }
        }
        nodes.append(cluster_node)
    
    # Edges del grafo
    edges = []
    
    # Relazioni simboliche
    for rel in relationships:
        edge = {
            "source": rel["source"],
            "target": rel["target"],
            "type": rel["type"],
            "weight": rel["strength"],
            "label": rel["description"],
            "properties": {
                "bidirectional": rel["type"] in ["same_category", "compositional"],
                "inference_enabled": True,
                "reasoning_path": True
            }
        }
        edges.append(edge)
    
    # Relazioni cluster-simbolo
    for cluster_name, symbol_ids in clusters.items():
        cluster_id = f"CLUSTER_{cluster_name.upper()}"
        for symbol_id in symbol_ids:
            edge = {
                "source": cluster_id,
                "target": symbol_id,
                "type": "contains",
                "weight": 1.0,
                "label": "cluster membership",
                "properties": {
                    "bidirectional": False,
                    "inference_enabled": True,
                    "reasoning_path": True
                }
            }
            edges.append(edge)
    
    # Costruisci grafo finale
    ontology_graph = {
        "metadata": {
            "name": "NEUROGLYPH Symbolic Ontology",
            "version": "1.0.0",
            "created": datetime.now().isoformat(),
            "total_nodes": len(nodes),
            "total_edges": len(edges),
            "total_symbols": len(symbols),
            "total_clusters": len(clusters),
            "description": "Ontological graph for NEUROGLYPH symbolic reasoning"
        },
        "nodes": nodes,
        "edges": edges,
        "clusters": clusters,
        "statistics": {
            "node_types": Counter(n["type"] for n in nodes),
            "edge_types": Counter(e["type"] for e in edges),
            "categories": Counter(n["category"] for n in nodes),
            "abstraction_levels": Counter(n["properties"]["abstraction_level"] for n in nodes),
            "cognitive_functions": Counter(n["properties"]["cognitive_function"] for n in nodes)
        }
    }
    
    print(f"  ✅ Grafo ontologico costruito:")
    print(f"    • Nodi: {len(nodes)} (simboli: {len(symbols)}, cluster: {len(clusters)})")
    print(f"    • Edges: {len(edges)}")
    print(f"    • Densità: {len(edges) / (len(nodes) * (len(nodes) - 1)) * 100:.2f}%")
    
    return ontology_graph

def determine_abstraction_level(symbol: Dict[str, Any]) -> str:
    """Determina livello di astrazione del simbolo."""
    name = symbol.get("name", "").lower()
    description = symbol.get("description", "").lower()
    
    if any(keyword in name for keyword in ["meta", "abstract", "conceptual", "philosophical"]):
        return "meta"
    elif any(keyword in name for keyword in ["system", "architecture", "structure", "framework"]):
        return "architectural"
    elif any(keyword in name for keyword in ["process", "function", "operation", "execution"]):
        return "operational"
    elif any(keyword in name for keyword in ["data", "input", "output", "interface"]):
        return "data"
    else:
        return "conceptual"

def determine_cognitive_function(symbol: Dict[str, Any]) -> str:
    """Determina funzione cognitiva del simbolo."""
    name = symbol.get("name", "").lower()
    
    if any(keyword in name for keyword in ["memory", "recall", "store", "retrieve"]):
        return "memory"
    elif any(keyword in name for keyword in ["attention", "focus", "select", "filter"]):
        return "attention"
    elif any(keyword in name for keyword in ["reasoning", "logic", "inference", "deduce"]):
        return "reasoning"
    elif any(keyword in name for keyword in ["learning", "adapt", "train", "improve"]):
        return "learning"
    elif any(keyword in name for keyword in ["perception", "sense", "detect", "recognize"]):
        return "perception"
    elif any(keyword in name for keyword in ["action", "execute", "perform", "control"]):
        return "action"
    elif any(keyword in name for keyword in ["communication", "language", "message", "signal"]):
        return "communication"
    else:
        return "general"

def determine_operational_scope(symbol: Dict[str, Any]) -> str:
    """Determina scope operativo del simbolo."""
    name = symbol.get("name", "").lower()
    
    if any(keyword in name for keyword in ["global", "system", "universal", "general"]):
        return "global"
    elif any(keyword in name for keyword in ["local", "specific", "particular", "instance"]):
        return "local"
    elif any(keyword in name for keyword in ["module", "component", "unit", "element"]):
        return "modular"
    elif any(keyword in name for keyword in ["network", "distributed", "collective", "multi"]):
        return "distributed"
    else:
        return "contextual"

def save_ontology(ontology_graph: Dict[str, Any]) -> bool:
    """Salva ontologia simbolica."""
    try:
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        
        # Salva ontologia completa
        ontology_path = f"neuroglyph/ontology/symbolic_ontology_v1.json"
        with open(ontology_path, 'w', encoding='utf-8') as f:
            json.dump(ontology_graph, f, indent=2, ensure_ascii=False)
        
        # Salva versione compatta per inferenza
        compact_ontology = {
            "metadata": ontology_graph["metadata"],
            "symbol_map": {n["id"]: n["label"] for n in ontology_graph["nodes"] if n["type"] == "symbol"},
            "relationships": [(e["source"], e["target"], e["type"], e["weight"]) for e in ontology_graph["edges"]],
            "clusters": ontology_graph["clusters"]
        }
        
        compact_path = f"neuroglyph/ontology/symbolic_ontology_compact.json"
        with open(compact_path, 'w', encoding='utf-8') as f:
            json.dump(compact_ontology, f, indent=2, ensure_ascii=False)
        
        # Salva grafo in formato DOT per visualizzazione
        dot_path = f"neuroglyph/ontology/symbolic_ontology.dot"
        with open(dot_path, 'w', encoding='utf-8') as f:
            f.write("digraph NEUROGLYPH_Ontology {\n")
            f.write("  rankdir=TB;\n")
            f.write("  node [shape=ellipse, style=filled];\n")
            
            # Nodi
            for node in ontology_graph["nodes"]:
                if node["type"] == "symbol":
                    color = "lightblue"
                elif node["type"] == "cluster":
                    color = "lightgreen"
                else:
                    color = "lightgray"
                
                f.write(f'  "{node["id"]}" [label="{node["label"]}", fillcolor={color}];\n')
            
            # Edges
            for edge in ontology_graph["edges"]:
                style = "solid" if edge["properties"]["reasoning_path"] else "dashed"
                f.write(f'  "{edge["source"]}" -> "{edge["target"]}" [label="{edge["type"]}", style={style}];\n')
            
            f.write("}\n")
        
        print(f"✅ Ontologia salvata:")
        print(f"  • Completa: {ontology_path}")
        print(f"  • Compatta: {compact_path}")
        print(f"  • Grafo DOT: {dot_path}")
        
        return True
        
    except Exception as e:
        print(f"❌ Errore salvataggio ontologia: {e}")
        return False

def main():
    """Costruisce ontologia simbolica NEUROGLYPH."""
    print("🧠 NEUROGLYPH SYMBOLIC ONTOLOGY BUILDER")
    print("🎯 Costruzione rete ontologica per ragionamento multi-hop")
    print("=" * 70)
    
    # Carica registry GOD MODE
    registry = load_registry()
    if not registry:
        sys.exit(1)
    
    symbols = registry.get("approved_symbols", [])
    print(f"📊 Simboli da processare: {len(symbols)}")
    
    # Analizza cluster semantici
    clusters = analyze_semantic_clusters(symbols)
    
    # Identifica relazioni simboliche
    relationships = identify_symbolic_relationships(symbols)
    
    # Costruisci grafo ontologico
    ontology_graph = build_ontology_graph(symbols, clusters, relationships)
    
    # Salva ontologia
    if save_ontology(ontology_graph):
        print(f"\n🎉 ONTOLOGIA SIMBOLICA COMPLETATA!")
        print(f"📊 Statistiche finali:")
        print(f"  • Simboli: {ontology_graph['metadata']['total_symbols']}")
        print(f"  • Cluster: {ontology_graph['metadata']['total_clusters']}")
        print(f"  • Relazioni: {len(relationships)}")
        print(f"  • Nodi totali: {ontology_graph['metadata']['total_nodes']}")
        print(f"  • Edges totali: {ontology_graph['metadata']['total_edges']}")
        
        print(f"\n🧠 CAPACITÀ COGNITIVE ABILITATE:")
        print(f"  ✅ Ragionamento multi-hop tra simboli")
        print(f"  ✅ Inferenza ontologica strutturata")
        print(f"  ✅ Navigazione semantica guidata")
        print(f"  ✅ Composizione concettuale dinamica")
        print(f"  ✅ Validazione logica delle inferenze")
        
        print(f"\n🚀 NEUROGLYPH LLM PRONTO PER INTELLIGENZA SIMBOLICA!")
        
        return True
    else:
        print(f"\n❌ Errore durante la costruzione ontologia")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
