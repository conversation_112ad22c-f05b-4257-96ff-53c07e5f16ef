{"id": "logic_transitivity_01", "prompt": "If A is greater than B and B is greater than C, what can we say about A and C?", "expected": "A is greater than C", "type": "logic", "context": "mathematical logic", "difficulty": "basic"}
{"id": "logic_syllogism_01", "prompt": "All humans are mortal. Socrates is human. What can we conclude about Socrates?", "expected": "Socrates is mortal", "type": "logic", "context": "classical logic", "difficulty": "basic"}
{"id": "math_algebra_01", "prompt": "Solve for x: 2x + 5 = 13", "expected": "x = 4", "type": "math", "context": "linear algebra", "difficulty": "basic"}
{"id": "math_calculus_01", "prompt": "What is the derivative of x^2 + 3x + 1?", "expected": "2x + 3", "type": "math", "context": "differential calculus", "difficulty": "intermediate"}
{"id": "coding_function_01", "prompt": "Write a Python function that returns the factorial of a number", "expected": "def factorial(n):\n    if n <= 1:\n        return 1\n    return n * factorial(n-1)", "type": "coding", "context": "recursion", "difficulty": "basic"}
{"id": "coding_loop_01", "prompt": "Write a Python loop that prints numbers from 1 to 10", "expected": "for i in range(1, 11):\n    print(i)", "type": "coding", "context": "iteration", "difficulty": "basic"}
{"id": "reasoning_analogy_01", "prompt": "Bird is to sky as fish is to what?", "expected": "water", "type": "reasoning", "context": "analogical reasoning", "difficulty": "basic"}
{"id": "reasoning_pattern_01", "prompt": "What comes next in the sequence: 2, 4, 8, 16, ?", "expected": "32", "type": "reasoning", "context": "pattern recognition", "difficulty": "basic"}
{"id": "creative_problem_01", "prompt": "How would you use a paperclip to solve the problem of a stuck zipper?", "expected": "Straighten the paperclip and use it as a small tool to gently work the zipper mechanism", "type": "creative", "context": "problem solving", "difficulty": "intermediate"}
{"id": "logic_conditional_01", "prompt": "If it rains, then the ground gets wet. The ground is wet. Can we conclude it rained?", "expected": "No, we cannot conclude it rained (affirming the consequent fallacy)", "type": "logic", "context": "conditional logic", "difficulty": "intermediate"}
{"id": "math_geometry_01", "prompt": "What is the area of a circle with radius 5?", "expected": "25π or approximately 78.54", "type": "math", "context": "geometry", "difficulty": "basic"}
{"id": "coding_debug_01", "prompt": "Find the bug in this code: def sum_list(lst): total = 0; for i in lst: total += i; return total", "expected": "No bug - this code correctly sums a list", "type": "coding", "context": "debugging", "difficulty": "intermediate"}
{"id": "reasoning_inference_01", "prompt": "All cats are animals. Some animals are pets. Can we conclude that some cats are pets?", "expected": "No, we cannot make this conclusion from the given premises", "type": "reasoning", "context": "logical inference", "difficulty": "intermediate"}
{"id": "creative_innovation_01", "prompt": "Propose an innovative way to reduce plastic waste in oceans", "expected": "Deploy autonomous underwater drones that collect plastic debris and convert it to fuel", "type": "creative", "context": "environmental innovation", "difficulty": "advanced"}
{"id": "math_statistics_01", "prompt": "What is the mean of the numbers: 2, 4, 6, 8, 10?", "expected": "6", "type": "math", "context": "statistics", "difficulty": "basic"}
{"id": "coding_optimization_01", "prompt": "Optimize this bubble sort algorithm for better performance", "expected": "Use quicksort or mergesort instead, or add early termination to bubble sort", "type": "coding", "context": "algorithm optimization", "difficulty": "advanced"}
{"id": "logic_contradiction_01", "prompt": "Can something be both completely red and completely blue at the same time?", "expected": "No, this is a logical contradiction", "type": "logic", "context": "contradiction", "difficulty": "basic"}
{"id": "reasoning_causation_01", "prompt": "Ice cream sales increase when drowning incidents increase. Does ice cream cause drowning?", "expected": "No, both are correlated with hot weather (confounding variable)", "type": "reasoning", "context": "causation vs correlation", "difficulty": "intermediate"}
{"id": "math_probability_01", "prompt": "What is the probability of rolling a 6 on a fair six-sided die?", "expected": "1/6 or approximately 0.167", "type": "math", "context": "probability", "difficulty": "basic"}
{"id": "coding_data_structure_01", "prompt": "Implement a stack data structure with push and pop operations", "expected": "class Stack:\n    def __init__(self):\n        self.items = []\n    def push(self, item):\n        self.items.append(item)\n    def pop(self):\n        return self.items.pop()", "type": "coding", "context": "data structures", "difficulty": "intermediate"}
