#!/usr/bin/env python3
"""
NEUROGLYPH DATASET DOWNLOADER
=============================

Scarica e prepara dataset ufficiali per benchmark:
- HumanEval (OpenAI)
- MBPP (Google)
- CodeContests
- APPS

Autore: NEUROGLYPH ULTRA Team
Data: 2025-05-26
Licenza: MIT
"""

import json
import gzip
import requests
import logging
from pathlib import Path
from typing import Dict, List, Any, Optional
from dataclasses import dataclass

# Setup logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

@dataclass
class DatasetInfo:
    """Informazioni dataset."""
    name: str
    url: str
    format: str  # 'jsonl', 'json', 'csv'
    description: str
    num_samples: int = 0

class DatasetDownloader:
    """
    Downloader per dataset ufficiali di coding benchmark.

    Supporta:
    - HumanEval: 164 problemi Python
    - MBPP: 1000+ problemi Python entry-level
    - CodeContests: Problemi competitive programming
    - APPS: Problemi algoritmici avanzati
    """

    def __init__(self, data_dir: str = "neuroglyph/benchmark/datasets"):
        self.data_dir = Path(data_dir)
        self.data_dir.mkdir(parents=True, exist_ok=True)

        # Dataset ufficiali
        self.datasets = {
            "humaneval": DatasetInfo(
                name="HumanEval",
                url="https://raw.githubusercontent.com/openai/human-eval/master/data/HumanEval.jsonl.gz",
                format="jsonl.gz",
                description="OpenAI HumanEval - 164 Python programming problems"
            ),
            "mbpp": DatasetInfo(
                name="MBPP",
                url="https://raw.githubusercontent.com/google-research/google-research/master/mbpp/mbpp.jsonl",
                format="jsonl",
                description="Google MBPP - 1000+ Python programming problems"
            ),
            "humaneval_plus": DatasetInfo(
                name="HumanEval+",
                url="https://raw.githubusercontent.com/evalplus/evalplus/master/evalplus/data/humaneval_plus.jsonl",
                format="jsonl",
                description="HumanEval+ with additional test cases"
            )
        }

        logger.info(f"📁 Dataset directory: {self.data_dir}")

    def download_dataset(self, dataset_name: str, force_refresh: bool = False) -> Optional[Path]:
        """
        Scarica dataset specifico.

        Args:
            dataset_name: Nome dataset da scaricare
            force_refresh: Forza ri-download anche se esiste

        Returns:
            Path file scaricato o None se errore
        """
        if dataset_name not in self.datasets:
            logger.error(f"❌ Dataset sconosciuto: {dataset_name}")
            logger.info(f"📋 Dataset disponibili: {list(self.datasets.keys())}")
            return None

        dataset = self.datasets[dataset_name]

        # Determina file output (sempre .jsonl non compresso)
        output_file = self.data_dir / f"{dataset_name}.jsonl"

        # Controlla se esiste già
        if output_file.exists() and not force_refresh:
            logger.info(f"✅ Dataset già presente: {output_file}")
            return output_file

        logger.info(f"📥 Scaricamento {dataset.name}...")
        logger.info(f"🔗 URL: {dataset.url}")

        try:
            # Download con timeout
            response = requests.get(dataset.url, timeout=60)
            response.raise_for_status()

            # Gestisci file compressi
            if dataset.format.endswith('.gz'):
                # Decomprimi e salva
                content = gzip.decompress(response.content).decode('utf-8')
                with open(output_file, 'w', encoding='utf-8') as f:
                    f.write(content)
            else:
                # Salva direttamente
                with open(output_file, 'w', encoding='utf-8') as f:
                    f.write(response.text)

            # Conta samples
            num_samples = self.count_samples(output_file, "jsonl")
            dataset.num_samples = num_samples

            logger.info(f"✅ {dataset.name} scaricato: {num_samples} samples")
            logger.info(f"💾 Salvato in: {output_file}")

            return output_file

        except requests.exceptions.RequestException as e:
            logger.error(f"❌ Errore download {dataset.name}: {e}")
            return None
        except Exception as e:
            logger.error(f"❌ Errore salvataggio {dataset.name}: {e}")
            return None

    def count_samples(self, file_path: Path, format: str) -> int:
        """
        Conta numero samples in dataset.

        Args:
            file_path: Path file dataset
            format: Formato file

        Returns:
            Numero samples
        """
        try:
            if format == "jsonl":
                with open(file_path, 'r', encoding='utf-8') as f:
                    return sum(1 for line in f if line.strip())
            elif format == "json":
                with open(file_path, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                    if isinstance(data, list):
                        return len(data)
                    elif isinstance(data, dict):
                        return len(data.get('data', []))
            return 0
        except Exception as e:
            logger.warning(f"⚠️ Errore conteggio samples: {e}")
            return 0

    def load_dataset(self, dataset_name: str, max_samples: Optional[int] = None) -> List[Dict[str, Any]]:
        """
        Carica dataset in memoria.

        Args:
            dataset_name: Nome dataset
            max_samples: Limite numero samples

        Returns:
            Lista samples dataset
        """
        dataset_file = self.data_dir / f"{dataset_name}.jsonl"

        if not dataset_file.exists():
            logger.warning(f"⚠️ Dataset non trovato: {dataset_file}")
            # Prova a scaricarlo
            downloaded = self.download_dataset(dataset_name)
            if not downloaded:
                return []
            dataset_file = downloaded

        try:
            samples = []
            with open(dataset_file, 'r', encoding='utf-8') as f:
                for i, line in enumerate(f):
                    if max_samples and i >= max_samples:
                        break

                    line = line.strip()
                    if line:
                        sample = json.loads(line)
                        samples.append(sample)

            logger.info(f"📊 Caricato {len(samples)} samples da {dataset_name}")
            return samples

        except Exception as e:
            logger.error(f"❌ Errore caricamento {dataset_name}: {e}")
            return []

    def download_all_datasets(self, force_refresh: bool = False) -> Dict[str, Optional[Path]]:
        """
        Scarica tutti i dataset disponibili.

        Args:
            force_refresh: Forza ri-download

        Returns:
            Dizionario nome_dataset -> path_file
        """
        logger.info("📥 Scaricamento tutti i dataset...")

        results = {}
        for dataset_name in self.datasets.keys():
            logger.info(f"\n📦 Processando {dataset_name}...")
            result = self.download_dataset(dataset_name, force_refresh)
            results[dataset_name] = result

        # Summary
        logger.info("\n" + "=" * 50)
        logger.info("📊 SUMMARY DOWNLOAD:")
        logger.info("=" * 50)

        total_samples = 0
        for name, path in results.items():
            if path:
                dataset = self.datasets[name]
                logger.info(f"✅ {dataset.name}: {dataset.num_samples} samples")
                total_samples += dataset.num_samples
            else:
                logger.info(f"❌ {name}: Download fallito")

        logger.info(f"\n🎯 Totale samples disponibili: {total_samples}")

        return results

    def create_benchmark_subset(self, dataset_name: str, num_samples: int,
                              difficulty: Optional[str] = None) -> List[Dict[str, Any]]:
        """
        Crea subset per benchmark.

        Args:
            dataset_name: Nome dataset
            num_samples: Numero samples desiderato
            difficulty: Filtro difficoltà (se supportato)

        Returns:
            Subset samples per benchmark
        """
        logger.info(f"🎯 Creazione subset {dataset_name}: {num_samples} samples")

        # Carica dataset completo
        all_samples = self.load_dataset(dataset_name)

        if not all_samples:
            logger.error(f"❌ Nessun sample disponibile per {dataset_name}")
            return []

        # Filtra per difficoltà se specificata
        if difficulty and dataset_name == "mbpp":
            # MBPP ha campo difficulty
            filtered = [s for s in all_samples if s.get('difficulty', '').lower() == difficulty.lower()]
            if filtered:
                all_samples = filtered
                logger.info(f"🔍 Filtrato per difficoltà '{difficulty}': {len(all_samples)} samples")

        # Seleziona subset
        if len(all_samples) <= num_samples:
            subset = all_samples
        else:
            # Prendi samples distribuiti uniformemente
            step = len(all_samples) // num_samples
            subset = [all_samples[i * step] for i in range(num_samples)]

        logger.info(f"✅ Subset creato: {len(subset)} samples")

        return subset

    def validate_dataset_format(self, dataset_name: str) -> bool:
        """
        Valida formato dataset.

        Args:
            dataset_name: Nome dataset da validare

        Returns:
            True se formato valido
        """
        samples = self.load_dataset(dataset_name, max_samples=5)

        if not samples:
            return False

        # Controlla campi richiesti per HumanEval
        if dataset_name == "humaneval":
            required_fields = ["task_id", "prompt", "canonical_solution", "test"]
            for sample in samples:
                if not all(field in sample for field in required_fields):
                    logger.error(f"❌ Campo mancante in {dataset_name}: {required_fields}")
                    return False

        # Controlla campi richiesti per MBPP
        elif dataset_name == "mbpp":
            required_fields = ["task_id", "text", "code", "test_list"]
            for sample in samples:
                if not all(field in sample for field in required_fields):
                    logger.error(f"❌ Campo mancante in {dataset_name}: {required_fields}")
                    return False

        logger.info(f"✅ Formato {dataset_name} valido")
        return True

    def get_dataset_stats(self) -> Dict[str, Any]:
        """
        Ottieni statistiche dataset.

        Returns:
            Statistiche complete
        """
        stats = {
            "available_datasets": len(self.datasets),
            "downloaded_datasets": 0,
            "total_samples": 0,
            "datasets": {}
        }

        for name, info in self.datasets.items():
            dataset_file = self.data_dir / f"{name}.jsonl"

            if dataset_file.exists():
                stats["downloaded_datasets"] += 1
                samples = self.count_samples(dataset_file, info.format)
                stats["total_samples"] += samples

                stats["datasets"][name] = {
                    "description": info.description,
                    "samples": samples,
                    "file_size_mb": round(dataset_file.stat().st_size / 1024 / 1024, 2),
                    "downloaded": True
                }
            else:
                stats["datasets"][name] = {
                    "description": info.description,
                    "samples": 0,
                    "downloaded": False
                }

        return stats


def main():
    """Test dataset downloader."""
    print("📥 NEUROGLYPH DATASET DOWNLOADER - Test")
    print("=" * 50)

    downloader = DatasetDownloader()

    # Mostra dataset disponibili
    print("📋 Dataset disponibili:")
    for name, info in downloader.datasets.items():
        print(f"  • {info.name}: {info.description}")

    # Scarica HumanEval per test
    print(f"\n📥 Test download HumanEval...")
    humaneval_path = downloader.download_dataset("humaneval")

    if humaneval_path:
        print(f"✅ Download riuscito: {humaneval_path}")

        # Valida formato
        if downloader.validate_dataset_format("humaneval"):
            print("✅ Formato valido")

            # Crea subset per test
            subset = downloader.create_benchmark_subset("humaneval", 5)
            print(f"✅ Subset creato: {len(subset)} samples")

            # Mostra primo sample
            if subset:
                first_sample = subset[0]
                print(f"\n📄 Primo sample:")
                print(f"  Task ID: {first_sample.get('task_id', 'N/A')}")
                print(f"  Prompt: {first_sample.get('prompt', 'N/A')[:100]}...")
        else:
            print("❌ Formato non valido")
    else:
        print("❌ Download fallito")

    # Statistiche
    stats = downloader.get_dataset_stats()
    print(f"\n📊 Statistiche:")
    print(f"  Dataset disponibili: {stats['available_datasets']}")
    print(f"  Dataset scaricati: {stats['downloaded_datasets']}")
    print(f"  Samples totali: {stats['total_samples']}")

    print("✅ Test completato")


if __name__ == "__main__":
    main()
