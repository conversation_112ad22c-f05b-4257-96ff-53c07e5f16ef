#!/usr/bin/env python3
"""
NEUROGLYPH REAL BENCHMARKS - MAIN RUNNER
========================================

Script principale per eseguire benchmark reali con:
- Dataset ufficiali (HumanEval, MBPP)
- <PERSON><PERSON> via Ollama (NO MOCK)
- Metriche scientifiche (CodeBLEU, Pass@K)
- Comparazione Qwen vs NEUROGLYPH+Qwen

Autore: NEUROGLYPH ULTRA Team
Data: 2025-05-26
Licenza: MIT
"""

import sys
import json
import time
import logging
from pathlib import Path
from datetime import datetime
from typing import Dict, List, Any, Optional

# Aggiungi path per importare moduli
sys.path.append(str(Path(__file__).parent))

from real_benchmark_runner import RealBenchmarkRunner, BenchmarkConfig
from benchmark_evaluator import BenchmarkEvaluator
from dataset_downloader import DatasetDownloader

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class NeuroglyphRealBenchmarks:
    """
    Orchestratore principale per benchmark reali NEUROGLYPH.
    
    Coordina:
    1. Download dataset ufficiali
    2. Setup Ollama + Qwen
    3. Esecuzione test comparativi
    4. Valutazione con metriche scientifiche
    5. Report finale con verdetto
    """
    
    def __init__(self, config: Optional[Dict[str, Any]] = None):
        self.config = config or self.get_default_config()
        self.timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        
        # Inizializza componenti
        self.downloader = DatasetDownloader()
        self.evaluator = BenchmarkEvaluator()
        
        # Configurazione benchmark
        benchmark_config = BenchmarkConfig(
            ollama_model=self.config["model"],
            num_samples=self.config["num_samples"],
            temperature=self.config["temperature"],
            max_tokens=self.config["max_tokens"]
        )
        self.runner = RealBenchmarkRunner(benchmark_config)
        
        logger.info("🚀 NEUROGLYPH Real Benchmarks inizializzato")
        logger.info(f"🎯 Configurazione: {self.config}")

    def get_default_config(self) -> Dict[str, Any]:
        """Configurazione default."""
        return {
            "model": "qwen2.5-coder:1.5b",
            "datasets": ["humaneval"],  # Inizia con HumanEval
            "num_samples": 10,  # Numero test per dataset
            "temperature": 0.1,  # Bassa per consistenza
            "max_tokens": 512,
            "use_advanced_evaluation": True,
            "save_detailed_results": True
        }

    def setup_datasets(self) -> bool:
        """
        Setup e download dataset necessari.
        
        Returns:
            True se setup riuscito
        """
        logger.info("📥 Setup dataset...")
        
        success = True
        for dataset_name in self.config["datasets"]:
            logger.info(f"📦 Preparazione {dataset_name}...")
            
            # Download dataset
            dataset_path = self.downloader.download_dataset(dataset_name)
            if not dataset_path:
                logger.error(f"❌ Fallito download {dataset_name}")
                success = False
                continue
            
            # Valida formato
            if not self.downloader.validate_dataset_format(dataset_name):
                logger.error(f"❌ Formato non valido {dataset_name}")
                success = False
                continue
            
            logger.info(f"✅ {dataset_name} pronto")
        
        if success:
            logger.info("✅ Tutti i dataset pronti")
        else:
            logger.error("❌ Errori nel setup dataset")
        
        return success

    def run_humaneval_benchmark(self) -> Dict[str, Any]:
        """
        Esegue benchmark HumanEval completo.
        
        Returns:
            Risultati benchmark HumanEval
        """
        logger.info("🧪 Avvio HumanEval Benchmark...")
        
        # Carica subset HumanEval
        humaneval_samples = self.downloader.create_benchmark_subset(
            "humaneval", 
            self.config["num_samples"]
        )
        
        if not humaneval_samples:
            logger.error("❌ Nessun sample HumanEval disponibile")
            return {"error": "No HumanEval samples"}
        
        logger.info(f"📊 Testing {len(humaneval_samples)} HumanEval problems...")
        
        # Esegui test comparativi
        results = []
        for i, sample in enumerate(humaneval_samples):
            task_id = sample.get("task_id", f"unknown_{i}")
            prompt = sample.get("prompt", "")
            canonical_solution = sample.get("canonical_solution", "")
            test_cases = sample.get("test", "").split('\n') if sample.get("test") else []
            
            logger.info(f"🧪 Test {i+1}/{len(humaneval_samples)}: {task_id}")
            
            try:
                # Test con runner
                benchmark_result = self.runner.run_single_test(sample)
                
                # Valutazione avanzata se abilitata
                if self.config["use_advanced_evaluation"]:
                    # Valuta Qwen standalone
                    qwen_eval = self.evaluator.evaluate_single_test(
                        f"{task_id}_qwen",
                        canonical_solution,
                        benchmark_result.qwen_response,
                        test_cases
                    )
                    
                    # Valuta NEUROGLYPH+Qwen
                    neuroglyph_eval = self.evaluator.evaluate_single_test(
                        f"{task_id}_neuroglyph",
                        canonical_solution,
                        benchmark_result.neuroglyph_response,
                        test_cases
                    )
                    
                    # Combina risultati
                    combined_result = {
                        "task_id": task_id,
                        "prompt_preview": prompt[:100] + "..." if len(prompt) > 100 else prompt,
                        "benchmark_metrics": {
                            "qwen_time": benchmark_result.qwen_time,
                            "neuroglyph_time": benchmark_result.neuroglyph_time,
                            "qwen_success": benchmark_result.qwen_success,
                            "neuroglyph_success": benchmark_result.neuroglyph_success,
                            "semantic_similarity": benchmark_result.semantic_score
                        },
                        "advanced_evaluation": {
                            "qwen": {
                                "code_bleu": qwen_eval.code_bleu_score,
                                "ast_similarity": qwen_eval.ast_similarity,
                                "execution_success": qwen_eval.execution_success,
                                "overall_score": qwen_eval.overall_score
                            },
                            "neuroglyph": {
                                "code_bleu": neuroglyph_eval.code_bleu_score,
                                "ast_similarity": neuroglyph_eval.ast_similarity,
                                "execution_success": neuroglyph_eval.execution_success,
                                "overall_score": neuroglyph_eval.overall_score
                            }
                        }
                    }
                else:
                    # Solo metriche base
                    combined_result = {
                        "task_id": task_id,
                        "qwen_time": benchmark_result.qwen_time,
                        "neuroglyph_time": benchmark_result.neuroglyph_time,
                        "qwen_success": benchmark_result.qwen_success,
                        "neuroglyph_success": benchmark_result.neuroglyph_success,
                        "semantic_score": benchmark_result.semantic_score,
                        "correctness_score": benchmark_result.correctness_score
                    }
                
                results.append(combined_result)
                
                # Log progresso
                if self.config["use_advanced_evaluation"]:
                    qwen_score = combined_result["advanced_evaluation"]["qwen"]["overall_score"]
                    neuroglyph_score = combined_result["advanced_evaluation"]["neuroglyph"]["overall_score"]
                    logger.info(f"  ✅ Qwen: {qwen_score:.2f}, NEUROGLYPH: {neuroglyph_score:.2f}")
                else:
                    logger.info(f"  ✅ Semantic: {combined_result['semantic_score']:.2f}")
                
            except Exception as e:
                logger.error(f"  ❌ Errore test {task_id}: {e}")
                continue
        
        # Calcola statistiche aggregate
        aggregate_stats = self.calculate_humaneval_statistics(results)
        
        return {
            "dataset": "HumanEval",
            "num_tests": len(results),
            "timestamp": self.timestamp,
            "config": self.config,
            "aggregate_statistics": aggregate_stats,
            "detailed_results": results
        }

    def calculate_humaneval_statistics(self, results: List[Dict[str, Any]]) -> Dict[str, Any]:
        """
        Calcola statistiche aggregate per HumanEval.
        
        Args:
            results: Lista risultati test
            
        Returns:
            Statistiche aggregate
        """
        if not results:
            return {"error": "No results to analyze"}
        
        stats = {
            "total_tests": len(results),
            "performance_comparison": {},
            "quality_metrics": {},
            "time_analysis": {},
            "success_rates": {}
        }
        
        if self.config["use_advanced_evaluation"]:
            # Statistiche avanzate
            qwen_scores = [r["advanced_evaluation"]["qwen"]["overall_score"] for r in results]
            neuroglyph_scores = [r["advanced_evaluation"]["neuroglyph"]["overall_score"] for r in results]
            
            qwen_executions = [r["advanced_evaluation"]["qwen"]["execution_success"] for r in results]
            neuroglyph_executions = [r["advanced_evaluation"]["neuroglyph"]["execution_success"] for r in results]
            
            stats["quality_metrics"] = {
                "qwen_avg_score": round(sum(qwen_scores) / len(qwen_scores), 3),
                "neuroglyph_avg_score": round(sum(neuroglyph_scores) / len(neuroglyph_scores), 3),
                "neuroglyph_improvement": round(
                    (sum(neuroglyph_scores) - sum(qwen_scores)) / len(results), 3
                )
            }
            
            stats["success_rates"] = {
                "qwen_execution_success": round(sum(qwen_executions) / len(qwen_executions) * 100, 1),
                "neuroglyph_execution_success": round(sum(neuroglyph_executions) / len(neuroglyph_executions) * 100, 1)
            }
        else:
            # Statistiche base
            semantic_scores = [r["semantic_score"] for r in results]
            correctness_scores = [r["correctness_score"] for r in results]
            
            stats["quality_metrics"] = {
                "avg_semantic_score": round(sum(semantic_scores) / len(semantic_scores), 3),
                "avg_correctness_score": round(sum(correctness_scores) / len(correctness_scores), 3)
            }
        
        # Analisi tempi
        qwen_times = [r["benchmark_metrics"]["qwen_time"] if "benchmark_metrics" in r else r["qwen_time"] for r in results]
        neuroglyph_times = [r["benchmark_metrics"]["neuroglyph_time"] if "benchmark_metrics" in r else r["neuroglyph_time"] for r in results]
        
        avg_qwen_time = sum(qwen_times) / len(qwen_times)
        avg_neuroglyph_time = sum(neuroglyph_times) / len(neuroglyph_times)
        
        stats["time_analysis"] = {
            "qwen_avg_time": round(avg_qwen_time, 3),
            "neuroglyph_avg_time": round(avg_neuroglyph_time, 3),
            "time_improvement_percent": round(
                (avg_qwen_time - avg_neuroglyph_time) / avg_qwen_time * 100, 1
            ) if avg_qwen_time > 0 else 0
        }
        
        # Verdetto performance
        if self.config["use_advanced_evaluation"]:
            neuroglyph_better = stats["quality_metrics"]["neuroglyph_improvement"] > 0
            faster_response = stats["time_analysis"]["time_improvement_percent"] > 0
            quality_threshold = stats["quality_metrics"]["neuroglyph_avg_score"] > 0.7
        else:
            neuroglyph_better = stats["quality_metrics"]["avg_correctness_score"] > 0.6
            faster_response = stats["time_analysis"]["time_improvement_percent"] > 0
            quality_threshold = stats["quality_metrics"]["avg_semantic_score"] > 0.5
        
        stats["performance_comparison"] = {
            "neuroglyph_outperforms_qwen": neuroglyph_better,
            "neuroglyph_faster": faster_response,
            "quality_threshold_met": quality_threshold,
            "overall_success": neuroglyph_better and quality_threshold
        }
        
        return stats

    def generate_final_report(self, benchmark_results: Dict[str, Any]) -> str:
        """
        Genera report finale con verdetto.
        
        Args:
            benchmark_results: Risultati benchmark
            
        Returns:
            Path file report
        """
        report_file = Path(f"neuroglyph/benchmark/results/final_report_{self.timestamp}.md")
        report_file.parent.mkdir(parents=True, exist_ok=True)
        
        stats = benchmark_results.get("aggregate_statistics", {})
        
        # Genera report Markdown
        report_content = f"""# NEUROGLYPH REAL BENCHMARK REPORT

**Timestamp:** {self.timestamp}  
**Dataset:** {benchmark_results.get('dataset', 'Unknown')}  
**Tests Executed:** {stats.get('total_tests', 0)}  
**Model:** {self.config['model']}

## 🎯 EXECUTIVE SUMMARY

"""
        
        if "performance_comparison" in stats:
            comparison = stats["performance_comparison"]
            
            if comparison.get("overall_success", False):
                report_content += "✅ **NEUROGLYPH OUTPERFORMS BASELINE**\n\n"
                report_content += "NEUROGLYPH dimostra miglioramenti misurabili rispetto a Qwen standalone.\n\n"
            else:
                report_content += "⚠️ **RISULTATI MISTI**\n\n"
                report_content += "NEUROGLYPH mostra alcuni miglioramenti ma necessita ottimizzazioni.\n\n"
        
        # Metriche dettagliate
        if "quality_metrics" in stats:
            quality = stats["quality_metrics"]
            report_content += "## 📊 QUALITY METRICS\n\n"
            
            for metric, value in quality.items():
                report_content += f"- **{metric.replace('_', ' ').title()}:** {value}\n"
            
            report_content += "\n"
        
        if "time_analysis" in stats:
            time_analysis = stats["time_analysis"]
            report_content += "## ⚡ PERFORMANCE ANALYSIS\n\n"
            
            for metric, value in time_analysis.items():
                report_content += f"- **{metric.replace('_', ' ').title()}:** {value}\n"
            
            report_content += "\n"
        
        # Raccomandazioni
        report_content += "## 🎯 RECOMMENDATIONS\n\n"
        
        if stats.get("performance_comparison", {}).get("overall_success", False):
            report_content += "1. ✅ Procedere con integrazione LLM\n"
            report_content += "2. 🚀 Espandere test su dataset più ampi\n"
            report_content += "3. 🔧 Ottimizzare simboli per performance\n"
        else:
            report_content += "1. 🔧 Migliorare mapping simboli\n"
            report_content += "2. 📊 Analizzare casi fallimento\n"
            report_content += "3. 🧪 Iterare su design simbolico\n"
        
        report_content += f"\n---\n*Report generato automaticamente da NEUROGLYPH Real Benchmarks*\n"
        
        # Salva report
        with open(report_file, 'w', encoding='utf-8') as f:
            f.write(report_content)
        
        logger.info(f"📄 Report finale salvato: {report_file}")
        return str(report_file)

    def run_full_benchmark_suite(self) -> bool:
        """
        Esegue suite completa di benchmark reali.
        
        Returns:
            True se benchmark superato
        """
        logger.info("🚀 AVVIO NEUROGLYPH REAL BENCHMARK SUITE")
        logger.info("=" * 60)
        
        start_time = time.time()
        
        # 1. Setup dataset
        if not self.setup_datasets():
            logger.error("❌ Setup dataset fallito")
            return False
        
        # 2. Verifica Ollama
        if not self.runner.ollama.is_available:
            logger.error("❌ Ollama non disponibile")
            return False
        
        logger.info("✅ Setup completato - avvio test...")
        
        # 3. Esegui benchmark per ogni dataset
        all_results = {}
        overall_success = True
        
        for dataset_name in self.config["datasets"]:
            logger.info(f"\n🧪 Benchmark {dataset_name.upper()}...")
            
            if dataset_name == "humaneval":
                results = self.run_humaneval_benchmark()
            else:
                logger.warning(f"⚠️ Dataset {dataset_name} non ancora supportato")
                continue
            
            if "error" in results:
                logger.error(f"❌ Benchmark {dataset_name} fallito: {results['error']}")
                overall_success = False
                continue
            
            all_results[dataset_name] = results
            
            # Verifica successo dataset
            stats = results.get("aggregate_statistics", {})
            dataset_success = stats.get("performance_comparison", {}).get("overall_success", False)
            
            if dataset_success:
                logger.info(f"✅ {dataset_name} benchmark SUPERATO")
            else:
                logger.warning(f"⚠️ {dataset_name} benchmark non superato")
                overall_success = False
        
        execution_time = time.time() - start_time
        
        # 4. Genera report finale
        if all_results:
            # Usa primo dataset per report (estendibile per multi-dataset)
            first_dataset = list(all_results.values())[0]
            report_path = self.generate_final_report(first_dataset)
            
            # Salva risultati completi
            results_file = Path(f"neuroglyph/benchmark/results/complete_results_{self.timestamp}.json")
            with open(results_file, 'w', encoding='utf-8') as f:
                json.dump(all_results, f, indent=2, ensure_ascii=False)
            
            logger.info(f"💾 Risultati completi: {results_file}")
        
        # 5. Summary finale
        logger.info("\n" + "=" * 60)
        logger.info("🎉 BENCHMARK SUITE COMPLETATO")
        logger.info("=" * 60)
        logger.info(f"⏱️  Tempo totale: {execution_time:.1f}s")
        logger.info(f"📊 Dataset testati: {len(all_results)}")
        
        if overall_success:
            logger.info("🏆 VERDETTO: NEUROGLYPH SUPERA BASELINE!")
            logger.info("🚀 Pronto per integrazione LLM avanzata")
        else:
            logger.info("⚠️ VERDETTO: Miglioramenti necessari")
            logger.info("🔧 Iterare su design simbolico")
        
        # Cleanup
        self.evaluator.cleanup()
        
        return overall_success


def main():
    """Main function."""
    print("🧠 NEUROGLYPH REAL BENCHMARKS")
    print("🎯 Benchmark scientifici con dataset ufficiali")
    print("=" * 60)
    
    # Configurazione per test iniziale
    config = {
        "model": "qwen2.5-coder:1.5b",
        "datasets": ["humaneval"],
        "num_samples": 5,  # Inizia con pochi test
        "temperature": 0.1,
        "max_tokens": 512,
        "use_advanced_evaluation": True,
        "save_detailed_results": True
    }
    
    # Esegui benchmark
    benchmarks = NeuroglyphRealBenchmarks(config)
    success = benchmarks.run_full_benchmark_suite()
    
    return success


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
