#!/usr/bin/env python3
"""
NEUROGLYPH BENCHMARK EVALUATOR
==============================

Evaluator avanzato per metriche precise:
- CodeBLEU score
- AST similarity
- Execution testing
- Pass@K metrics
- Semantic embeddings

Autore: NEUROGLYPH ULTRA Team
Data: 2025-05-26
Licenza: MIT
"""

import ast
import sys
import json
import time
import logging
import subprocess
import tempfile
from pathlib import Path
from typing import Dict, List, Any, Optional, Tuple
from dataclasses import dataclass
import difflib

# Setup logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

@dataclass
class EvaluationResult:
    """Risultato valutazione singolo test."""
    test_id: str
    code_bleu_score: float
    ast_similarity: float
    execution_success: bool
    pass_at_k: float
    semantic_score: float
    overall_score: float
    error_message: Optional[str] = None

class BenchmarkEvaluator:
    """
    Evaluator avanzato per benchmark NEUROGLYPH.
    
    Implementa metriche standard per valutazione LLM coding:
    - CodeBLEU: Similarità sintattica e semantica
    - AST Similarity: Confronto Abstract Syntax Tree
    - Execution Testing: Test funzionalità codice
    - Pass@K: Percentuale successo su K tentativi
    - Semantic Embeddings: Similarità semantica avanzata
    """
    
    def __init__(self):
        self.temp_dir = Path(tempfile.mkdtemp(prefix="neuroglyph_eval_"))
        logger.info(f"🔍 Evaluator inizializzato - temp dir: {self.temp_dir}")

    def calculate_code_bleu(self, reference: str, candidate: str) -> float:
        """
        Calcola CodeBLEU score tra codice di riferimento e candidato.
        
        Args:
            reference: Codice di riferimento
            candidate: Codice candidato
            
        Returns:
            Score CodeBLEU (0-1)
        """
        try:
            # Implementazione semplificata di CodeBLEU
            # In produzione useresti la libreria ufficiale codebleu
            
            # Tokenizza codice
            ref_tokens = self.tokenize_code(reference)
            cand_tokens = self.tokenize_code(candidate)
            
            if not ref_tokens or not cand_tokens:
                return 0.0
            
            # Calcola BLEU-like score
            bleu_score = self.calculate_bleu_score(ref_tokens, cand_tokens)
            
            # Aggiungi peso per similarità AST
            ast_weight = self.calculate_ast_similarity(reference, candidate)
            
            # CodeBLEU = weighted average di BLEU + AST + dataflow + syntax
            code_bleu = 0.25 * bleu_score + 0.25 * ast_weight + 0.25 * bleu_score + 0.25 * ast_weight
            
            return min(code_bleu, 1.0)
            
        except Exception as e:
            logger.warning(f"⚠️ Errore calcolo CodeBLEU: {e}")
            return 0.0

    def tokenize_code(self, code: str) -> List[str]:
        """
        Tokenizza codice Python.
        
        Args:
            code: Codice da tokenizzare
            
        Returns:
            Lista token
        """
        try:
            # Rimuovi commenti e whitespace extra
            lines = [line.strip() for line in code.split('\n') if line.strip() and not line.strip().startswith('#')]
            clean_code = ' '.join(lines)
            
            # Tokenizza base
            tokens = []
            for char in clean_code:
                if char.isalnum() or char == '_':
                    if tokens and tokens[-1].isalnum():
                        tokens[-1] += char
                    else:
                        tokens.append(char)
                elif char in '()[]{}:,=+-*/<>!':
                    tokens.append(char)
                elif char == ' ':
                    continue
                else:
                    tokens.append(char)
            
            return [t for t in tokens if t.strip()]
            
        except Exception as e:
            logger.warning(f"⚠️ Errore tokenizzazione: {e}")
            return []

    def calculate_bleu_score(self, reference: List[str], candidate: List[str]) -> float:
        """
        Calcola BLEU score semplificato.
        
        Args:
            reference: Token di riferimento
            candidate: Token candidato
            
        Returns:
            Score BLEU (0-1)
        """
        if not reference or not candidate:
            return 0.0
        
        # N-gram precision per n=1,2,3,4
        precisions = []
        
        for n in range(1, 5):
            ref_ngrams = self.get_ngrams(reference, n)
            cand_ngrams = self.get_ngrams(candidate, n)
            
            if not cand_ngrams:
                precisions.append(0.0)
                continue
            
            # Conta matches
            matches = 0
            for ngram in cand_ngrams:
                if ngram in ref_ngrams:
                    matches += min(cand_ngrams[ngram], ref_ngrams[ngram])
            
            precision = matches / sum(cand_ngrams.values())
            precisions.append(precision)
        
        # Geometric mean delle precision
        if all(p > 0 for p in precisions):
            bleu = (precisions[0] * precisions[1] * precisions[2] * precisions[3]) ** 0.25
        else:
            bleu = 0.0
        
        # Brevity penalty
        bp = min(1.0, len(candidate) / len(reference)) if reference else 0.0
        
        return bleu * bp

    def get_ngrams(self, tokens: List[str], n: int) -> Dict[tuple, int]:
        """
        Estrae n-grammi da lista token.
        
        Args:
            tokens: Lista token
            n: Dimensione n-grammi
            
        Returns:
            Dizionario n-grammi con conteggi
        """
        ngrams = {}
        for i in range(len(tokens) - n + 1):
            ngram = tuple(tokens[i:i+n])
            ngrams[ngram] = ngrams.get(ngram, 0) + 1
        return ngrams

    def calculate_ast_similarity(self, code1: str, code2: str) -> float:
        """
        Calcola similarità tra AST di due codici.
        
        Args:
            code1: Primo codice
            code2: Secondo codice
            
        Returns:
            Score similarità AST (0-1)
        """
        try:
            # Parse AST
            ast1 = ast.parse(code1)
            ast2 = ast.parse(code2)
            
            # Estrai nodi AST
            nodes1 = self.extract_ast_nodes(ast1)
            nodes2 = self.extract_ast_nodes(ast2)
            
            if not nodes1 and not nodes2:
                return 1.0
            if not nodes1 or not nodes2:
                return 0.0
            
            # Calcola Jaccard similarity
            intersection = len(nodes1.intersection(nodes2))
            union = len(nodes1.union(nodes2))
            
            return intersection / union if union > 0 else 0.0
            
        except (SyntaxError, ValueError) as e:
            logger.warning(f"⚠️ Errore parsing AST: {e}")
            return 0.0

    def extract_ast_nodes(self, tree: ast.AST) -> set:
        """
        Estrae tipi nodi da AST.
        
        Args:
            tree: AST tree
            
        Returns:
            Set tipi nodi
        """
        nodes = set()
        for node in ast.walk(tree):
            nodes.add(type(node).__name__)
        return nodes

    def test_code_execution(self, code: str, test_cases: List[str]) -> Tuple[bool, str]:
        """
        Testa esecuzione codice con test cases.
        
        Args:
            code: Codice da testare
            test_cases: Lista test cases
            
        Returns:
            (successo, messaggio_errore)
        """
        try:
            # Crea file temporaneo
            test_file = self.temp_dir / f"test_{int(time.time())}.py"
            
            # Scrivi codice + test
            full_code = code + "\n\n" + "\n".join(test_cases)
            
            with open(test_file, 'w', encoding='utf-8') as f:
                f.write(full_code)
            
            # Esegui test
            result = subprocess.run(
                [sys.executable, str(test_file)],
                capture_output=True,
                text=True,
                timeout=10
            )
            
            # Cleanup
            test_file.unlink(missing_ok=True)
            
            if result.returncode == 0:
                return True, ""
            else:
                return False, result.stderr or result.stdout
                
        except subprocess.TimeoutExpired:
            return False, "Timeout execution"
        except Exception as e:
            return False, str(e)

    def calculate_pass_at_k(self, results: List[bool], k: int = 1) -> float:
        """
        Calcola Pass@K metric.
        
        Args:
            results: Lista risultati booleani
            k: Numero tentativi
            
        Returns:
            Pass@K score (0-1)
        """
        if not results:
            return 0.0
        
        # Per semplicità, usiamo pass rate diretto
        # In implementazione completa, useresti sampling
        successes = sum(results)
        total = len(results)
        
        return min(successes / total, 1.0) if total > 0 else 0.0

    def calculate_semantic_similarity(self, text1: str, text2: str) -> float:
        """
        Calcola similarità semantica avanzata.
        
        Args:
            text1: Primo testo
            text2: Secondo testo
            
        Returns:
            Score similarità semantica (0-1)
        """
        # Implementazione semplificata
        # In produzione useresti embeddings (BERT, CodeBERT, etc.)
        
        if not text1 or not text2:
            return 0.0
        
        # Usa difflib per similarità sequenza
        similarity = difflib.SequenceMatcher(None, text1.lower(), text2.lower()).ratio()
        
        return similarity

    def evaluate_single_test(self, test_id: str, reference_code: str, 
                           candidate_code: str, test_cases: List[str] = None) -> EvaluationResult:
        """
        Valuta singolo test con tutte le metriche.
        
        Args:
            test_id: ID test
            reference_code: Codice di riferimento
            candidate_code: Codice candidato
            test_cases: Test cases opzionali
            
        Returns:
            Risultato valutazione completo
        """
        logger.debug(f"🔍 Evaluating test: {test_id}")
        
        try:
            # 1. CodeBLEU
            code_bleu = self.calculate_code_bleu(reference_code, candidate_code)
            
            # 2. AST Similarity
            ast_sim = self.calculate_ast_similarity(reference_code, candidate_code)
            
            # 3. Execution Testing
            execution_success = True
            if test_cases:
                exec_result, error_msg = self.test_code_execution(candidate_code, test_cases)
                execution_success = exec_result
            else:
                error_msg = None
            
            # 4. Pass@K (simulato)
            pass_at_k = 1.0 if execution_success else 0.0
            
            # 5. Semantic Similarity
            semantic_score = self.calculate_semantic_similarity(reference_code, candidate_code)
            
            # 6. Overall Score (weighted average)
            weights = {
                'code_bleu': 0.3,
                'ast_similarity': 0.2,
                'execution': 0.3,
                'semantic': 0.2
            }
            
            overall_score = (
                code_bleu * weights['code_bleu'] +
                ast_sim * weights['ast_similarity'] +
                (1.0 if execution_success else 0.0) * weights['execution'] +
                semantic_score * weights['semantic']
            )
            
            return EvaluationResult(
                test_id=test_id,
                code_bleu_score=round(code_bleu, 3),
                ast_similarity=round(ast_sim, 3),
                execution_success=execution_success,
                pass_at_k=round(pass_at_k, 3),
                semantic_score=round(semantic_score, 3),
                overall_score=round(overall_score, 3),
                error_message=error_msg
            )
            
        except Exception as e:
            logger.error(f"❌ Errore valutazione {test_id}: {e}")
            return EvaluationResult(
                test_id=test_id,
                code_bleu_score=0.0,
                ast_similarity=0.0,
                execution_success=False,
                pass_at_k=0.0,
                semantic_score=0.0,
                overall_score=0.0,
                error_message=str(e)
            )

    def cleanup(self):
        """Cleanup risorse temporanee."""
        try:
            import shutil
            shutil.rmtree(self.temp_dir, ignore_errors=True)
            logger.debug(f"🧹 Cleanup completato: {self.temp_dir}")
        except Exception as e:
            logger.warning(f"⚠️ Errore cleanup: {e}")


def main():
    """Test evaluator."""
    print("🔍 NEUROGLYPH BENCHMARK EVALUATOR - Test")
    print("=" * 50)
    
    evaluator = BenchmarkEvaluator()
    
    # Test case esempio
    reference = """
def factorial(n):
    if n <= 1:
        return 1
    return n * factorial(n-1)
"""
    
    candidate = """
def factorial(n):
    if n <= 1:
        return 1
    else:
        return n * factorial(n-1)
"""
    
    test_cases = [
        "assert factorial(0) == 1",
        "assert factorial(1) == 1", 
        "assert factorial(5) == 120"
    ]
    
    # Valuta
    result = evaluator.evaluate_single_test("test_factorial", reference, candidate, test_cases)
    
    print(f"📊 Risultati valutazione:")
    print(f"  CodeBLEU: {result.code_bleu_score}")
    print(f"  AST Similarity: {result.ast_similarity}")
    print(f"  Execution Success: {result.execution_success}")
    print(f"  Pass@K: {result.pass_at_k}")
    print(f"  Semantic Score: {result.semantic_score}")
    print(f"  Overall Score: {result.overall_score}")
    
    if result.error_message:
        print(f"  Error: {result.error_message}")
    
    # Cleanup
    evaluator.cleanup()
    
    print("✅ Test evaluator completato")


if __name__ == "__main__":
    main()
