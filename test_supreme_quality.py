#!/usr/bin/env python3
"""
Test rapido della qualità SUPREME
=================================
"""

import sys
sys.path.append('.')

from neuroglyph_supreme_god_mode_dataset_generator import NeuroglyphSupremeGodModeGenerator

def test_supreme_quality():
    """Test rapido della qualità SUPREME."""
    
    print("🔍 Test NEUROGLYPH SUPREME GOD MODE Quality")
    print("=" * 50)
    
    generator = NeuroglyphSupremeGodModeGenerator()
    
    # Test esempio per ogni dominio
    domains = ['symbolic_logic', 'mathematical_reasoning', 'analogical_thinking']
    
    for domain in domains:
        print(f"\n🧠 Testing {domain.replace('_', ' ').title()}...")
        
        try:
            example = generator.generate_supreme_god_mode_example(domain, 'god_mode')
            
            print(f"✅ Qualità: {example['metadata']['quality_score']}/10")
            print(f"🔣 Simboli: {example['metadata']['symbols_used']}")
            print(f"🔗 Step: {example['metadata']['reasoning_steps']}")
            print(f"📝 Instruction: {example['instruction'][:120]}...")
            print(f"📄 Output preview: {example['output'][:200]}...")
            
            if example['metadata']['quality_score'] >= 9.0:
                print("🎊 QUALITÀ SUPREME CONFERMATA!")
            else:
                print("⚠️ Qualità sotto soglia SUPREME")
                
        except Exception as e:
            print(f"❌ Errore: {e}")
    
    print("\n🎯 Test completato!")

if __name__ == "__main__":
    test_supreme_quality()
