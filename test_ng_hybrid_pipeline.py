#!/usr/bin/env python3
"""
Test Pipeline Ibrida NG-THINK v3.0 ULTRA
========================================

Test della pipeline ibrida con NG_PARSER reale e altri moduli stub.
Verifica integrazione modulo-per-modulo secondo principio di sviluppo.

Autore: NEUROGLYPH NG-THINK Team
Data: 2025-01-27
"""

import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), 'neuroglyph'))

from neuroglyph.ng_think.core.ng_hybrid_pipeline import NGThinkHybridPipeline

def test_hybrid_pipeline_basic():
    """Test base della pipeline ibrida"""
    print("🧪 TEST PIPELINE IBRIDA - NG_PARSER REALE")
    print("=" * 60)
    
    pipeline = NGThinkHybridPipeline()
    
    # Test case con prompt complesso
    test_prompt = "Crea una funzione Python per ordinare una lista usando quicksort. Deve essere efficiente e gestire casi edge."
    
    print(f"\n📝 Input: {test_prompt}")
    
    # Esegui pipeline ibrida
    result = pipeline.process_end_to_end(test_prompt)
    
    print(f"\n📊 Risultati Pipeline Ibrida:")
    print(f"   ✅ Output: {result.text[:100]}...")
    print(f"   🎯 Confidence: {result.confidence:.3f}")
    print(f"   ⏱️ Time: {result.generation_time:.3f}s")
    print(f"   ✓ Validated: {result.validation_passed}")
    
    # Analizza metadati parser reale
    if 'parser_tokens' in result.metadata:
        print(f"\n🔤 Parser Reale - Dettagli:")
        print(f"   Tokens: {result.metadata['parser_tokens']}")
        print(f"   Segments: {result.metadata['parser_segments']}")
        print(f"   Confidence: {result.metadata['parser_confidence']:.3f}")
        print(f"   Semantic preserved: {result.metadata['parser_semantic_preserved']}")

def test_parser_integration():
    """Test integrazione parser reale con stub"""
    print("\n🔗 TEST INTEGRAZIONE PARSER REALE")
    print("=" * 60)
    
    pipeline = NGThinkHybridPipeline()
    
    test_cases = [
        "Semplice test",
        "Test con simboli NEUROGLYPH ⊢ e ≈ per reasoning",
        "Test ambiguo: cosa significa 'quello' in questo contesto?",
        "Test tecnico: implementa algoritmo di machine learning per classificazione"
    ]
    
    for i, prompt in enumerate(test_cases, 1):
        print(f"\n📝 Test Case {i}: {prompt[:50]}...")
        
        validation = pipeline.validate_parser_integration(prompt)
        
        print(f"   ✅ Integration success: {validation['integration_success']}")
        
        if validation['error_message']:
            print(f"   ❌ Error: {validation['error_message']}")
        
        parser_output = validation['parser_output']
        print(f"   🔤 Tokens: {parser_output['tokens_count']}")
        print(f"   ✂️ Segments: {parser_output['segments_count']}")
        print(f"   🎯 Intents: {parser_output['intents']}")
        print(f"   📊 Confidence: {parser_output['confidence']:.3f}")
        print(f"   🔍 Clarity: {parser_output['ambiguity_clarity']:.3f}")
        
        # Verifica contratti
        contracts = validation['contract_validation']
        contract_checks = [
            ("Tokens", contracts['has_tokens']),
            ("Segments", contracts['has_segments']),
            ("Intents", contracts['has_intents']),
            ("Ambiguity", contracts['has_ambiguity_report']),
            ("Confidence", contracts['confidence_valid'])
        ]
        
        print(f"   📋 Contratti: {', '.join([f'{name}:✅' if check else f'{name}:❌' for name, check in contract_checks])}")

def test_hybrid_vs_stub_comparison():
    """Test confronto pipeline ibrida vs stub completa"""
    print("\n⚖️ TEST CONFRONTO IBRIDA VS STUB")
    print("=" * 60)
    
    pipeline = NGThinkHybridPipeline()
    
    test_prompts = [
        "Crea funzione fibonacci",
        "Analizza questo codice: def func(): pass",
        "Spiega il machine learning",
        "Debug errore: IndexError in lista"
    ]
    
    for prompt in test_prompts:
        print(f"\n📝 Prompt: {prompt}")
        
        comparison = pipeline.compare_with_stub_pipeline(prompt)
        
        hybrid = comparison['hybrid_pipeline']
        stub = comparison['stub_pipeline']
        diff = comparison['differences']
        
        print(f"   🔄 Ibrida: conf={hybrid['confidence']:.3f}, time={hybrid['generation_time']:.3f}s")
        print(f"   📦 Stub:   conf={stub['confidence']:.3f}, time={stub['generation_time']:.3f}s")
        print(f"   📊 Delta:  conf={diff['confidence_delta']:+.3f}, time={diff['time_delta']:+.3f}s")
        
        if diff['parser_improvement']:
            print(f"   ✅ Parser reale migliora tokenizzazione")
        
        # Dettagli parser reale
        if hybrid['parser_tokens'] > 0:
            print(f"   🔤 Parser: {hybrid['parser_tokens']} tokens, {hybrid['parser_segments']} segments")

def test_pipeline_status():
    """Test stato e roadmap pipeline"""
    print("\n📊 TEST STATO PIPELINE")
    print("=" * 60)
    
    pipeline = NGThinkHybridPipeline()
    
    # Stato attuale
    status = pipeline.get_pipeline_status()
    
    print(f"   Pipeline: {status['pipeline_type']} {status['version']}")
    print(f"   Ready: {status['ready']}")
    
    progress = status['implementation_progress']
    print(f"\n   📈 Progresso Implementazione:")
    print(f"      Totale moduli: {progress['total_modules']}")
    print(f"      Implementati: {progress['implemented']}")
    print(f"      Rimanenti: {progress['remaining']}")
    print(f"      Progresso: {progress['progress_percentage']:.1f}%")
    
    print(f"\n   ✅ Moduli Implementati:")
    for module, status_type in status['implemented_modules'].items():
        print(f"      🔧 {module}: {status_type}")
    
    print(f"\n   📦 Moduli Stub:")
    for module, status_type in status['stub_modules'].items():
        print(f"      📦 {module}: {status_type}")
    
    if status['next_to_implement']:
        print(f"\n   🎯 Prossimo da implementare: {status['next_to_implement']}")

def test_implementation_roadmap():
    """Test roadmap implementazione"""
    print("\n🗺️ TEST ROADMAP IMPLEMENTAZIONE")
    print("=" * 60)
    
    pipeline = NGThinkHybridPipeline()
    roadmap = pipeline.get_implementation_roadmap()
    
    print(f"   Fase attuale: {roadmap['current_phase']}")
    
    print(f"\n   ✅ Completato:")
    for completed in roadmap['completed']:
        print(f"      🔧 {completed['module']}: {completed['status']}")
        for feature in completed['features'][:3]:  # Prime 3 features
            print(f"         - {feature}")
    
    print(f"\n   📋 Prossime Fasi:")
    for phase in roadmap['next_phases'][:3]:  # Prime 3 fasi
        print(f"      {phase['phase']}")
        print(f"         Modulo: {phase['module']}")
        print(f"         Requisiti: {len(phase['requirements'])} items")

def test_parser_real_capabilities():
    """Test capacità specifiche del parser reale"""
    print("\n🔬 TEST CAPACITÀ PARSER REALE")
    print("=" * 60)
    
    pipeline = NGThinkHybridPipeline()
    
    # Test casi specifici per parser
    test_cases = [
        {
            "name": "Simboli NEUROGLYPH",
            "prompt": "Usa ⊢ per deduzione e ≈ per analogia nel reasoning",
            "expected": "preservazione simboli"
        },
        {
            "name": "Codice embedded",
            "prompt": "Crea `def fibonacci(n): return n if n <= 1 else fibonacci(n-1) + fibonacci(n-2)`",
            "expected": "riconoscimento codice"
        },
        {
            "name": "Prompt multi-intent",
            "prompt": "Dato il contesto di ML, crea un classificatore. Deve essere accurato ma non troppo complesso.",
            "expected": "segmentazione corretta"
        },
        {
            "name": "Ambiguità intenzionale",
            "prompt": "Quello che hai fatto prima non funziona. Puoi sistemarlo?",
            "expected": "rilevamento ambiguità"
        }
    ]
    
    for test_case in test_cases:
        print(f"\n📝 Test: {test_case['name']}")
        print(f"   Input: {test_case['prompt']}")
        
        # Usa direttamente il parser per analisi dettagliata
        parsed = pipeline.parser.parse(test_case['prompt'])
        
        print(f"   🔤 Tokens: {len(parsed.tokens)} ({parsed.tokens[:5]}...)")
        print(f"   ✂️ Segments: {len(parsed.segments)}")
        
        if parsed.segments:
            for i, seg in enumerate(parsed.segments):
                print(f"      {i+1}. [{seg.segment_type}] {seg.content[:30]}...")
        
        print(f"   🎯 Intents: {parsed.intents}")
        print(f"   🔍 Clarity: {parsed.ambiguity_report.overall_clarity:.3f}")
        print(f"   📊 Confidence: {parsed.confidence:.3f}")
        print(f"   ✓ Semantic preserved: {parsed.semantic_preserved}")
        
        # Verifica aspettativa
        if test_case['expected'] == "preservazione simboli":
            symbols_found = any('⊢' in token or '≈' in token for token in parsed.tokens)
            print(f"   ✅ Simboli preservati: {symbols_found}")
        
        elif test_case['expected'] == "segmentazione corretta":
            has_context = any(seg.segment_type == 'context' for seg in parsed.segments)
            has_constraint = any(seg.segment_type == 'constraint' for seg in parsed.segments)
            print(f"   ✅ Segmentazione: context={has_context}, constraint={has_constraint}")
        
        elif test_case['expected'] == "rilevamento ambiguità":
            ambiguities = len(parsed.ambiguity_report.ambiguous_spans)
            print(f"   ✅ Ambiguità rilevate: {ambiguities}")

def main():
    """Esegue tutti i test della pipeline ibrida"""
    try:
        print("🧠 NG-THINK v3.0 ULTRA - HYBRID PIPELINE TESTS")
        print("🎯 Parser Reale + Stub Integration")
        print("=" * 80)
        
        test_hybrid_pipeline_basic()
        test_parser_integration()
        test_hybrid_vs_stub_comparison()
        test_pipeline_status()
        test_implementation_roadmap()
        test_parser_real_capabilities()
        
        print("\n" + "=" * 80)
        print("✅ TUTTI I TEST IBRIDI COMPLETATI CON SUCCESSO!")
        print("🏗️ NG_PARSER reale integrato correttamente")
        print("📋 Contratti tra moduli validati")
        print("🚀 Pronto per implementazione NG_CONTEXT_PRIORITIZER")
        print("=" * 80)
        
    except Exception as e:
        print(f"\n❌ ERRORE NEI TEST: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
