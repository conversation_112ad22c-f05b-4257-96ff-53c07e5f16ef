#!/usr/bin/env python3
"""
NEUROGLYPH Cognitive Tags Expander
==================================

Strumento per espandere cognitive tags presence da 0.787 a >0.9
aggiungendo tag espliciti (<THEN>, <EVIDENCE>, <GOAL>, etc.) 
in esempi dove sono impliciti.

Obiettivo: Potenziare meta-cognizione esplicita per NEUROGLYPH GOD MODE.

Autore: NEUROGLYPH Perfection Team
Data: 2025-06-01
"""

import json
import re
from typing import Dict, List, Set, Tuple, Any
from datetime import datetime

class NeuroglyphCognitiveExpander:
    """
    Espansore cognitive tags per meta-cognizione esplicita.
    
    Aggiunge marcatori cognitivi espliciti dove sono impliciti.
    """
    
    def __init__(self):
        # Cognitive tags da espandere
        self.cognitive_tags = {
            'CAUSE': {
                'markers': ['<CAUSE>', 'CAUSA:', 'PERCHÉ:'],
                'implicit_patterns': [
                    r'perché\s+',
                    r'poiché\s+',
                    r'dato\s+che\s+',
                    r'a\s+causa\s+di\s+',
                    r'dovuto\s+a\s+',
                    r'∵\s+'
                ],
                'template': '<CAUSE> {content} </CAUSE>'
            },
            'GOAL': {
                'markers': ['<GOAL>', 'OBIETTIVO:', 'SCOPO:'],
                'implicit_patterns': [
                    r'obiettivo\s+è\s+',
                    r'scopo\s+è\s+',
                    r'finalità\s+è\s+',
                    r'target\s+è\s+',
                    r'🎯\s+'
                ],
                'template': '<GOAL> {content} </GOAL>'
            },
            'EVIDENCE': {
                'markers': ['<EVIDENCE>', 'EVIDENZA:', 'PROVA:'],
                'implicit_patterns': [
                    r'evidenza\s+mostra\s+',
                    r'prova\s+che\s+',
                    r'dimostrazione\s+',
                    r'verifica\s+che\s+',
                    r'✅\s+'
                ],
                'template': '<EVIDENCE> {content} </EVIDENCE>'
            },
            'REASONING': {
                'markers': ['<REASONING>', 'RAGIONAMENTO:', 'DEDUZIONE:'],
                'implicit_patterns': [
                    r'ragionamento\s+',
                    r'deduzione\s+',
                    r'inferenza\s+',
                    r'logica\s+',
                    r'⊢\s+'
                ],
                'template': '<REASONING> {content} </REASONING>'
            },
            'ANALOGY': {
                'markers': ['<ANALOGY>', 'ANALOGIA:', 'SIMILE A:'],
                'implicit_patterns': [
                    r'analogia\s+con\s+',
                    r'simile\s+a\s+',
                    r'come\s+nel\s+caso\s+',
                    r'pattern\s+simile\s+',
                    r'≈\s+'
                ],
                'template': '<ANALOGY> {content} </ANALOGY>'
            },
            'META': {
                'markers': ['<META>', 'META-COGNIZIONE:', 'PENSIERO SU:'],
                'implicit_patterns': [
                    r'meta-cognizione\s+',
                    r'pensiero\s+sul\s+pensiero\s+',
                    r'riflessione\s+su\s+',
                    r'processo\s+cognitivo\s+',
                    r'🧠\s+'
                ],
                'template': '<META> {content} </META>'
            },
            'VALIDATION': {
                'markers': ['<VALIDATION>', 'VALIDAZIONE:', 'CONTROLLO:'],
                'implicit_patterns': [
                    r'validazione\s+',
                    r'controllo\s+',
                    r'verifica\s+',
                    r'test\s+',
                    r'check\s+'
                ],
                'template': '<VALIDATION> {content} </VALIDATION>'
            },
            'CONCLUSION': {
                'markers': ['<CONCLUSION>', 'CONCLUSIONE:', 'QUINDI:'],
                'implicit_patterns': [
                    r'conclusione\s+',
                    r'quindi\s+',
                    r'risultato\s+finale\s+',
                    r'ne\s+segue\s+che\s+',
                    r'∴\s+'
                ],
                'template': '<CONCLUSION> {content} </CONCLUSION>'
            }
        }
        
    def expand_cognitive_tags(self, dataset_path: str, output_path: str) -> Dict[str, Any]:
        """Espande cognitive tags nel dataset."""
        
        print("🧠 NEUROGLYPH Cognitive Tags Expander")
        print("=" * 45)
        
        # Carica dataset
        with open(dataset_path, 'r', encoding='utf-8') as f:
            dataset = json.load(f)
        
        examples = dataset['examples']
        print(f"📊 Dataset loaded: {len(examples)} examples")
        
        # Analizza presenza attuale
        current_presence = self._analyze_current_presence(examples)
        print(f"🔍 Current cognitive tags presence: {current_presence:.3f}")
        
        # Espandi tags
        expanded_examples = []
        expansion_stats = {
            'examples_modified': 0,
            'tags_added': 0,
            'implicit_made_explicit': 0
        }
        
        for i, example in enumerate(examples):
            expanded_example, stats = self._expand_example_tags(example)
            expanded_examples.append(expanded_example)
            
            # Aggiorna statistiche
            if stats['modified']:
                expansion_stats['examples_modified'] += 1
            expansion_stats['tags_added'] += stats['tags_added']
            expansion_stats['implicit_made_explicit'] += stats['implicit_made_explicit']
            
            if (i + 1) % 100 == 0:
                print(f"   ✅ Expanded {i+1}/{len(examples)} examples")
        
        # Aggiorna dataset
        dataset['examples'] = expanded_examples
        dataset['statistics']['cognitive_expansion'] = expansion_stats
        dataset['statistics']['expansion_timestamp'] = datetime.now().isoformat()
        
        # Salva dataset espanso
        with open(output_path, 'w', encoding='utf-8') as f:
            json.dump(dataset, f, indent=2, ensure_ascii=False)
        
        # Valida risultati
        post_presence = self._analyze_current_presence(expanded_examples)
        
        # Report finale
        expansion_report = {
            "original_examples": len(examples),
            "expanded_examples": len(expanded_examples),
            "pre_presence": current_presence,
            "post_presence": post_presence,
            "improvement": post_presence - current_presence,
            "expansion_stats": expansion_stats,
            "output_path": output_path
        }
        
        self._print_expansion_summary(expansion_report)
        
        return expansion_report
    
    def _analyze_current_presence(self, examples: List[Dict[str, Any]]) -> float:
        """Analizza presenza attuale di cognitive tags."""
        
        total_categories = len(self.cognitive_tags)
        total_presence = 0
        
        for example in examples:
            output = example.get('output', '').lower()
            found_categories = 0
            
            for tag_name, tag_info in self.cognitive_tags.items():
                # Cerca marcatori espliciti
                explicit_found = any(marker.lower() in output for marker in tag_info['markers'])
                
                # Cerca pattern impliciti
                implicit_found = any(re.search(pattern, output) for pattern in tag_info['implicit_patterns'])
                
                if explicit_found or implicit_found:
                    found_categories += 1
            
            total_presence += found_categories / total_categories
        
        return total_presence / len(examples) if examples else 0
    
    def _expand_example_tags(self, example: Dict[str, Any]) -> Tuple[Dict[str, Any], Dict[str, Any]]:
        """Espande cognitive tags in un singolo esempio."""
        
        expanded_example = example.copy()
        output = example.get('output', '')
        
        stats = {
            'modified': False,
            'tags_added': 0,
            'implicit_made_explicit': 0
        }
        
        # Espandi ogni tipo di tag
        for tag_name, tag_info in self.cognitive_tags.items():
            # Verifica se tag è già presente esplicitamente
            has_explicit = any(marker in output for marker in tag_info['markers'])
            
            if not has_explicit:
                # Cerca pattern impliciti
                for pattern in tag_info['implicit_patterns']:
                    matches = list(re.finditer(pattern, output, re.IGNORECASE))
                    
                    if matches:
                        # Aggiungi tag esplicito
                        expanded_output = self._add_explicit_tag(output, pattern, tag_info['template'], tag_name)
                        
                        if expanded_output != output:
                            output = expanded_output
                            stats['modified'] = True
                            stats['tags_added'] += 1
                            stats['implicit_made_explicit'] += len(matches)
                            break  # Un tag per tipo per esempio
        
        expanded_example['output'] = output
        
        if stats['modified']:
            expanded_example['metadata']['cognitive_expanded'] = True
            expanded_example['metadata']['expansion_timestamp'] = datetime.now().isoformat()
        
        return expanded_example, stats
    
    def _add_explicit_tag(self, text: str, pattern: str, template: str, tag_name: str) -> str:
        """Aggiunge tag esplicito per pattern implicito."""
        
        lines = text.split('\n')
        modified_lines = []
        tag_added = False
        
        for line in lines:
            if not tag_added and re.search(pattern, line, re.IGNORECASE):
                # Trova la frase che contiene il pattern
                match = re.search(pattern, line, re.IGNORECASE)
                if match:
                    # Estrai contenuto rilevante
                    start_pos = match.start()
                    
                    # Trova fine della frase
                    sentence_end = line.find('.', start_pos)
                    if sentence_end == -1:
                        sentence_end = len(line)
                    
                    # Estrai contenuto
                    content = line[start_pos:sentence_end].strip()
                    
                    # Crea tag esplicito
                    explicit_tag = template.format(content=content)
                    
                    # Sostituisci nella linea
                    modified_line = line[:start_pos] + explicit_tag + line[sentence_end:]
                    modified_lines.append(modified_line)
                    tag_added = True
                else:
                    modified_lines.append(line)
            else:
                modified_lines.append(line)
        
        return '\n'.join(modified_lines)
    
    def _print_expansion_summary(self, report: Dict[str, Any]):
        """Stampa summary dell'espansione."""
        
        print("\n" + "="*45)
        print("📊 COGNITIVE TAGS EXPANSION SUMMARY")
        print("="*45)
        
        print(f"\n🎯 EXPANSION RESULTS:")
        print(f"   Examples processed: {report['expanded_examples']}")
        print(f"   Examples modified: {report['expansion_stats']['examples_modified']}")
        print(f"   Tags added: {report['expansion_stats']['tags_added']}")
        print(f"   Implicit made explicit: {report['expansion_stats']['implicit_made_explicit']}")
        
        print(f"\n📈 COGNITIVE PRESENCE IMPROVEMENT:")
        print(f"   Pre-expansion: {report['pre_presence']:.3f}")
        print(f"   Post-expansion: {report['post_presence']:.3f}")
        print(f"   Improvement: +{report['improvement']:.3f}")
        
        improvement_pct = (report['improvement'] / report['pre_presence'] * 100) if report['pre_presence'] > 0 else 0
        print(f"   Relative improvement: +{improvement_pct:.1f}%")
        
        target_reached = report['post_presence'] >= 0.9
        status_emoji = "🎊" if target_reached else "📈"
        status_text = "TARGET REACHED" if target_reached else "PROGRESS MADE"
        
        print(f"\n{status_emoji} COGNITIVE TAGS EXPANSION COMPLETED!")
        print(f"   Status: {status_text} (target: ≥0.9)")
        print(f"   Dataset saved: {report['output_path']}")
        
        print("="*45)

def main():
    """Esegue espansione cognitive tags."""
    
    input_path = "neuroglyph_supreme_god_mode_normalized.json"
    output_path = "neuroglyph_supreme_god_mode_perfected.json"
    
    print("🧠 NEUROGLYPH Cognitive Tags Expander")
    print("=" * 40)
    print("🎯 Expanding cognitive tags to >0.9 presence")
    
    expander = NeuroglyphCognitiveExpander()
    
    # Esegui espansione
    report = expander.expand_cognitive_tags(input_path, output_path)
    
    print(f"\n💾 Perfected dataset saved: {output_path}")
    print(f"🎯 Cognitive meta-cognition optimized for NEUROGLYPH!")

if __name__ == "__main__":
    main()
