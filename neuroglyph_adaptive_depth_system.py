#!/usr/bin/env python3
"""
NEUROGLYPH Adaptive Depth System
================================

Sistema intelligente per profondità ragionamento adattiva basata su complessità prompt.
Risolve problema overthinking con depth dinamico 3-12 step.

OBIETTIVI:
- Prompt semplici: 3-4 step (efficienza)
- Prompt medi: 5-7 step (bilanciamento)  
- Prompt complessi: 8-12 step (profondità)
- Zero overthinking su compiti banali
- Massima intelligenza su problemi complessi

Autore: NEUROGLYPH Adaptive Intelligence Team
Data: 2025-06-01
"""

import json
import re
import random
from typing import List, Dict, Any, Tuple
from datetime import datetime
import statistics

class NeuroglyphAdaptiveDepthSystem:
    """
    Sistema adattivo per profondità ragionamento intelligente.
    
    Classifica complessità prompt e adatta depth di conseguenza.
    """
    
    def __init__(self):
        # Classificatore complessità prompt
        self.complexity_patterns = {
            'simple': {
                'keywords': [
                    'cos\'è', 'definisci', 'spiega brevemente', 'in breve',
                    'cosa significa', 'che cosa è', 'definizione di',
                    'semplice spiegazione', 'riassumi', 'basic'
                ],
                'depth_range': (3, 4),
                'weight': 0.1
            },
            'medium': {
                'keywords': [
                    'analizza', 'confronta', 'spiega come', 'perché',
                    'quali sono', 'descrivi il processo', 'come funziona',
                    'vantaggi e svantaggi', 'esempi di', 'applica'
                ],
                'depth_range': (5, 7),
                'weight': 0.5
            },
            'complex': {
                'keywords': [
                    'dimostra', 'prova che', 'costruisci una dimostrazione',
                    'sviluppa una teoria', 'analogia avanzata', 'ragionamento formale',
                    'catena deduttiva', 'sistema di inferenza', 'meta-cognizione',
                    'ragionamento simbolico', 'dimostrazione rigorosa'
                ],
                'depth_range': (8, 12),
                'weight': 0.9
            }
        }
        
        # Distribuzione target adattiva
        self.adaptive_distribution = {
            'simple_3-4': 0.30,    # 30% esempi semplici
            'medium_5-7': 0.50,    # 50% esempi medi
            'complex_8-12': 0.20   # 20% esempi complessi
        }
        
        # Metriche qualità per depth
        self.quality_targets = {
            'simple': {'min_excellence': 90.0, 'max_verbosity': 300},
            'medium': {'min_excellence': 92.0, 'max_verbosity': 600},
            'complex': {'min_excellence': 95.0, 'max_verbosity': 1200}
        }
    
    def classify_prompt_complexity(self, prompt: str) -> Dict[str, Any]:
        """Classifica complessità prompt usando pattern matching avanzato."""
        
        prompt_lower = prompt.lower()
        complexity_scores = {}
        
        # Analizza pattern per ogni categoria
        for category, config in self.complexity_patterns.items():
            score = 0
            matched_keywords = []
            
            for keyword in config['keywords']:
                if keyword in prompt_lower:
                    score += 1
                    matched_keywords.append(keyword)
            
            # Normalizza score
            normalized_score = min(score / len(config['keywords']), 1.0)
            complexity_scores[category] = {
                'score': normalized_score,
                'weight': config['weight'],
                'matched_keywords': matched_keywords,
                'depth_range': config['depth_range']
            }
        
        # Calcola score finale pesato
        total_weighted_score = sum(
            scores['score'] * scores['weight'] 
            for scores in complexity_scores.values()
        )
        
        # Determina categoria dominante
        dominant_category = max(
            complexity_scores.keys(),
            key=lambda k: complexity_scores[k]['score']
        )
        
        # Analisi aggiuntiva lunghezza e struttura
        length_factor = min(len(prompt) / 200, 1.0)  # Normalizza a 200 char
        
        # Conta simboli NEUROGLYPH nel prompt
        neuroglyph_symbols = ['⊢', '∴', '→', '∧', '∨', '≈', '∀', '∃', '🧠', '💭']
        symbol_count = sum(1 for symbol in neuroglyph_symbols if symbol in prompt)
        symbol_factor = min(symbol_count / 5, 1.0)  # Normalizza a 5 simboli
        
        # Score finale adattivo
        final_score = (total_weighted_score * 0.6 + 
                      length_factor * 0.2 + 
                      symbol_factor * 0.2)
        
        return {
            'final_score': final_score,
            'dominant_category': dominant_category,
            'category_scores': complexity_scores,
            'length_factor': length_factor,
            'symbol_factor': symbol_factor,
            'recommended_depth': self._get_adaptive_depth(final_score, dominant_category)
        }
    
    def _get_adaptive_depth(self, score: float, category: str) -> int:
        """Determina profondità adattiva basata su score e categoria."""
        
        if score < 0.3 or category == 'simple':
            return random.randint(3, 4)  # Shallow reasoning
        elif score < 0.7 or category == 'medium':
            return random.randint(5, 7)  # Medium reasoning
        else:
            return random.randint(8, 12)  # Deep reasoning
    
    def adapt_dataset_depth(self, dataset_path: str, output_path: str) -> Dict[str, Any]:
        """Adatta profondità ragionamento nel dataset basata su complessità."""
        
        print("🧠 NEUROGLYPH Adaptive Depth System")
        print("=" * 45)
        print("🎯 Adattando profondità ragionamento per efficienza intelligente")
        
        # Carica dataset
        with open(dataset_path, 'r', encoding='utf-8') as f:
            dataset = json.load(f)
        
        examples = dataset['examples']
        print(f"📊 Dataset loaded: {len(examples)} examples")
        
        # Analizza e adatta ogni esempio
        adapted_examples = []
        adaptation_stats = {
            'simple_count': 0,
            'medium_count': 0,
            'complex_count': 0,
            'total_adapted': 0,
            'depth_distribution': {},
            'quality_preserved': 0
        }
        
        for i, example in enumerate(examples):
            # Classifica complessità
            instruction = example.get('instruction', '')
            complexity_analysis = self.classify_prompt_complexity(instruction)
            
            # Adatta profondità
            adapted_example = self._adapt_example_depth(example, complexity_analysis)
            adapted_examples.append(adapted_example)
            
            # Aggiorna statistiche
            category = complexity_analysis['dominant_category']
            adaptation_stats[f'{category}_count'] += 1
            adaptation_stats['total_adapted'] += 1
            
            depth = complexity_analysis['recommended_depth']
            adaptation_stats['depth_distribution'][depth] = \
                adaptation_stats['depth_distribution'].get(depth, 0) + 1
            
            # Progress tracking
            if (i + 1) % 1000 == 0:
                print(f"   ✅ Adapted {i+1}/{len(examples)} examples")
        
        # Aggiorna dataset
        adapted_dataset = dataset.copy()
        adapted_dataset['examples'] = adapted_examples
        adapted_dataset['statistics']['adaptive_depth'] = adaptation_stats
        adapted_dataset['statistics']['adaptation_timestamp'] = datetime.now().isoformat()
        adapted_dataset['version'] = 'NEUROGLYPH_ADAPTIVE_DEPTH'
        
        # Salva dataset adattato
        with open(output_path, 'w', encoding='utf-8') as f:
            json.dump(adapted_dataset, f, indent=2, ensure_ascii=False)
        
        # Report finale
        adaptation_report = {
            "original_examples": len(examples),
            "adapted_examples": len(adapted_examples),
            "adaptation_stats": adaptation_stats,
            "depth_distribution": adaptation_stats['depth_distribution'],
            "efficiency_gain": self._calculate_efficiency_gain(adaptation_stats),
            "output_path": output_path
        }
        
        self._print_adaptation_summary(adaptation_report)
        
        return adaptation_report
    
    def _adapt_example_depth(self, example: Dict[str, Any], 
                           complexity_analysis: Dict[str, Any]) -> Dict[str, Any]:
        """Adatta singolo esempio alla profondità ottimale."""
        
        adapted_example = example.copy()
        recommended_depth = complexity_analysis['recommended_depth']
        category = complexity_analysis['dominant_category']
        
        # Adatta output per nuova profondità
        original_output = example.get('output', '')
        adapted_output = self._adapt_output_depth(original_output, recommended_depth, category)
        
        # Aggiorna esempio
        adapted_example['output'] = adapted_output
        adapted_example['metadata'] = example.get('metadata', {}).copy()
        adapted_example['metadata'].update({
            'adaptive_depth': recommended_depth,
            'complexity_category': category,
            'complexity_score': complexity_analysis['final_score'],
            'original_depth': example.get('metadata', {}).get('reasoning_steps', 12),
            'adaptation_timestamp': datetime.now().isoformat(),
            'efficiency_optimized': True
        })
        
        return adapted_example
    
    def _adapt_output_depth(self, output: str, target_depth: int, category: str) -> str:
        """Adatta output alla profondità target mantenendo qualità."""
        
        lines = output.split('\n')
        
        # Identifica sezioni
        header_lines = []
        step_lines = []
        conclusion_lines = []
        
        current_section = 'header'
        
        for line in lines:
            if '🔗 CATENA DI RAGIONAMENTO' in line or 'STEP' in line:
                current_section = 'steps'
                if '🔗 CATENA DI RAGIONAMENTO' in line:
                    header_lines.append(line)
                else:
                    step_lines.append(line)
            elif ('CONCLUSIONE' in line or '✅ GARANZIE' in line or 
                  '∴ CONCLUSIONE' in line):
                current_section = 'conclusion'
                conclusion_lines.append(line)
            elif current_section == 'header':
                header_lines.append(line)
            elif current_section == 'steps':
                step_lines.append(line)
            elif current_section == 'conclusion':
                conclusion_lines.append(line)
        
        # Adatta step alla profondità target
        if len(step_lines) > target_depth:
            # Seleziona step più importanti
            adapted_steps = self._select_key_steps(step_lines, target_depth, category)
        else:
            adapted_steps = step_lines
        
        # Ricostruisci output adattato
        adapted_lines = header_lines + adapted_steps + conclusion_lines
        
        # Aggiorna header con nuova profondità
        for i, line in enumerate(adapted_lines):
            if '🔗 CATENA DI RAGIONAMENTO' in line:
                adapted_lines[i] = f"🔗 CATENA DI RAGIONAMENTO DETERMINISTICO ({target_depth} step):"
                break
        
        return '\n'.join(adapted_lines)
    
    def _select_key_steps(self, steps: List[str], target_count: int, category: str) -> List[str]:
        """Seleziona step chiave mantenendo logica."""
        
        if len(steps) <= target_count:
            return steps
        
        # Identifica step numerati
        numbered_steps = []
        for step in steps:
            if re.search(r'\d+\.', step):
                numbered_steps.append(step)
        
        if len(numbered_steps) <= target_count:
            return numbered_steps
        
        # Strategia selezione basata su categoria
        if category == 'simple':
            # Per semplici: primo, ultimo, e uno intermedio
            if target_count >= 3:
                selected = [numbered_steps[0], numbered_steps[-1]]
                if target_count > 2:
                    mid_idx = len(numbered_steps) // 2
                    selected.insert(1, numbered_steps[mid_idx])
                if target_count > 3:
                    # Aggiungi step aggiuntivi distribuiti
                    for i in range(target_count - 3):
                        idx = (i + 1) * len(numbered_steps) // (target_count - 2)
                        if idx < len(numbered_steps) and numbered_steps[idx] not in selected:
                            selected.append(numbered_steps[idx])
            else:
                selected = numbered_steps[:target_count]
        else:
            # Per medi/complessi: distribuzione uniforme
            indices = []
            for i in range(target_count):
                idx = int(i * (len(numbered_steps) - 1) / (target_count - 1))
                indices.append(idx)
            selected = [numbered_steps[i] for i in indices]
        
        # Rinumera step
        renumbered = []
        for i, step in enumerate(selected, 1):
            renumbered_step = re.sub(r'^\s*\d+\.', f'{i}.', step)
            renumbered.append(renumbered_step)
        
        return renumbered
    
    def _calculate_efficiency_gain(self, stats: Dict[str, Any]) -> Dict[str, float]:
        """Calcola guadagno efficienza da adattamento."""
        
        total_examples = stats['total_adapted']
        simple_count = stats['simple_count']
        medium_count = stats['medium_count']
        complex_count = stats['complex_count']
        
        # Calcola step medi prima (tutti a 12)
        original_avg_steps = 12.0
        
        # Calcola step medi dopo adattamento
        adapted_avg_steps = (
            simple_count * 3.5 +    # Media 3-4
            medium_count * 6.0 +    # Media 5-7  
            complex_count * 10.0    # Media 8-12
        ) / total_examples
        
        # Calcola guadagni
        step_reduction = (original_avg_steps - adapted_avg_steps) / original_avg_steps
        latency_improvement = step_reduction  # Approssimazione lineare
        computational_saving = step_reduction * 0.8  # Considerando overhead
        
        return {
            'original_avg_steps': original_avg_steps,
            'adapted_avg_steps': adapted_avg_steps,
            'step_reduction_pct': step_reduction * 100,
            'latency_improvement_pct': latency_improvement * 100,
            'computational_saving_pct': computational_saving * 100
        }
    
    def _print_adaptation_summary(self, report: Dict[str, Any]):
        """Stampa summary adattamento."""
        
        print("\n" + "="*45)
        print("📊 ADAPTIVE DEPTH ADAPTATION SUMMARY")
        print("="*45)
        
        stats = report['adaptation_stats']
        efficiency = report['efficiency_gain']
        
        print(f"\n🎯 ADAPTATION RESULTS:")
        print(f"   Examples processed: {report['adapted_examples']}")
        print(f"   Simple (3-4 step): {stats['simple_count']} ({stats['simple_count']/report['adapted_examples']*100:.1f}%)")
        print(f"   Medium (5-7 step): {stats['medium_count']} ({stats['medium_count']/report['adapted_examples']*100:.1f}%)")
        print(f"   Complex (8-12 step): {stats['complex_count']} ({stats['complex_count']/report['adapted_examples']*100:.1f}%)")
        
        print(f"\n📈 EFFICIENCY GAINS:")
        print(f"   Original avg steps: {efficiency['original_avg_steps']:.1f}")
        print(f"   Adapted avg steps: {efficiency['adapted_avg_steps']:.1f}")
        print(f"   Step reduction: {efficiency['step_reduction_pct']:.1f}%")
        print(f"   Latency improvement: {efficiency['latency_improvement_pct']:.1f}%")
        print(f"   Computational saving: {efficiency['computational_saving_pct']:.1f}%")
        
        print(f"\n🎊 ADAPTIVE DEPTH OPTIMIZATION COMPLETED!")
        print(f"   Zero overthinking su prompt semplici")
        print(f"   Massima profondità su problemi complessi")
        print(f"   Dataset saved: {report['output_path']}")
        
        print("="*45)

def main():
    """Esegue adattamento profondità dataset."""
    
    input_dataset = "neuroglyph_supreme_god_mode_20k.json"
    output_dataset = "neuroglyph_adaptive_depth_20k.json"
    
    print("🧠 NEUROGLYPH Adaptive Depth System")
    print("=" * 40)
    print("🎯 Solving overthinking with intelligent depth")
    
    system = NeuroglyphAdaptiveDepthSystem()
    
    # Esegui adattamento
    report = system.adapt_dataset_depth(input_dataset, output_dataset)
    
    print(f"\n💾 Adaptive dataset saved: {output_dataset}")
    print(f"🎯 Ready for efficient NEUROGLYPH training!")

if __name__ == "__main__":
    main()
